<?php
/**
 * Template for the UM User Photos, the "Edit Album" modal content
 *
 * Page: "Profile", tab "Photos"
 * Caller: User_Photos_Ajax->load_edit_album_modal() method
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/modal/edit-album.php
 * @var object $album
 * @var bool   $privacy
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

$photos        = get_post_meta( $album->ID, '_photos', true );
$disable_title = UM()->options()->get( 'um_user_photos_disable_title' );
$disable_cover = UM()->options()->get( 'um_user_photos_disable_cover' );
if ( ! $disable_cover ) {
	$cover       = get_post_meta( $album->ID, '_thumbnail_id', true ) ? get_post_meta( $album->ID, '_thumbnail_id', true ) : 0;
	$cover_index = 0;
}
$user_id           = get_current_user_id();
$count_user_photos = UM()->User_Photos()->common()->user()->photos_count( $user_id );
$limit_user_photos = UM()->User_Photos()->common()->user()->photos_limit( $user_id );

$limit_per_albums   = UM()->options()->get( 'um_user_photos_album_limit' );
$limit_per_albums   = ! empty( $limit_per_albums ) ? $limit_per_albums : '';
$album_photos_array = get_post_meta( $album->ID, '_photos', true );
$album_photos_array = ! empty( $album_photos_array ) && is_array( $album_photos_array ) ? $album_photos_array : array();
$count_per_album    = count( $album_photos_array );

$enable_upload = true;
if ( false !== $limit_user_photos && (int) $count_user_photos >= (int) $limit_user_photos ) {
	$enable_upload = false;
}
if ( ! empty( $limit_per_albums ) && (int) $count_per_album >= (int) $limit_per_albums ) {
	$enable_upload = false;
}
?>

<div class="um-form">
	<form id="um-user-photos-form-edit-album" class="um-user-photos-modal-form" action="<?php echo esc_url( admin_url( 'admin-ajax.php?action=update_um_user_photos_album' ) ); ?>" method="post" enctype="multipart/form-data"  data-max_size_error="<?php esc_attr_e( ' is too large. File should be less than ', 'um-user-photos' ); ?>" data-max_size="<?php echo esc_attr( wp_max_upload_size() ); ?>" data-limit="<?php echo esc_attr( $limit_user_photos ); ?>" data-count="<?php echo esc_attr( $count_user_photos ); ?>" data-album_limit="<?php echo esc_attr( $limit_per_albums ); ?>" data-count_album="<?php echo esc_attr( $count_per_album ); ?>" data-max_upload="<?php echo esc_attr( ini_get( 'max_file_uploads' ) ); ?>">

		<div class="um-galley-form-response"></div>

		<?php if ( ! $disable_title ) { ?>
		<div class="um-field">
			<input type="text" placeholder="<?php esc_attr_e( 'Album title', 'um-user-photos' ); ?>" name="album_title" value="<?php echo esc_attr( $album->post_title ); ?>" required="required" />
		</div>
		<?php } ?>

		<div class="um-field">
			<?php if ( ! $disable_cover ) { ?>
				<input id="um-user-photos-cover-image-id" name="album_cover_id" type="hidden" value="<?php echo absint( $cover ); ?>">
			<?php } ?>

			<?php if ( ! empty( $photos ) && is_array( $photos ) ) { ?>
				<div class="um-user-photos-album-photos" id="um-user-photos-sortable">
					<?php
					for ( $i = 0; $i < count( $photos ); $i++ ) {
						$image = wp_get_attachment_image_src( $photos[ $i ], 'thumbnail' );
						if ( ! $image ) {
							continue;
						}
						$cover_class = '';
						if ( ! $disable_cover && absint( $photos[ $i ] ) === absint( $cover ) ) {
							$cover_class = 'um-user-photos-cover';
							$cover_index = $i;
						}
						?>
						<div class="um-user-photos-photo ui-state-default <?php echo esc_attr( $cover_class ); ?>" id="album-photo-<?php echo esc_attr( $photos[ $i ] ); ?>" data-covertext="<?php echo esc_html__( 'Cover photo', 'um-user-photos' ); ?>">
							<img src="<?php echo esc_attr( $image[0] ); ?>"/>
							<input type="hidden" name="photos[]" value="<?php echo esc_attr( $photos[ $i ] ); ?>" />
							<a class="um-user-photos-delete-photo-album um-tip-n"
								data-id="<?php echo esc_attr( $photos[ $i ] ); ?>"
								data-wpnonce="<?php echo esc_attr( wp_create_nonce( 'um_delete_photo' ) ); ?>"
								title="<?php esc_attr_e( 'Delete photo', 'um-user-photos' ); ?>"
								data-confirmation="<?php esc_attr_e( 'Sure to delete photo?', 'um-user-photos' ); ?>"
								data-delete_photo="#album-photo-<?php echo esc_attr( $photos[ $i ] ); ?>"
								><i class="um-faicon-times"></i></a>
						</div>
						<?php
					}
					?>
				</div>
				<div class="um-clear"></div>
			<?php } ?>

			<?php if ( ! $disable_cover ) { ?>
				<input id="um-user-photos-cover-image" name="album_cover" type="hidden" value="<?php echo absint( $cover_index ); ?>">
			<?php } ?>
			<h6><?php echo esc_html__( 'Uploaded photos', 'um-user-photos' ); ?></h6>
			<div id="um-user-photos-images-uploaded" data-covertext="<?php echo esc_html__( 'Cover photo', 'um-user-photos' ); ?>"></div>
			<div class="um-clear"></div>
		</div>
		<div class="um-user-photos-error" <?php echo ( false === $enable_upload ) ? '' : 'style="display:none;"'; ?>><?php esc_html_e( 'You cannot upload more photos, you have reached the limit of uploads. Delete old photos to add new ones.', 'um-user-photos' ); ?></div>
		<div class="um-user-photos-max-upload-error">
			<?php
			// translators: %s: max_file_uploads parameter.
			echo esc_html( sprintf( __( 'You cannot upload more than %s photos at one time. Please upload less photos or increase the max_file_uploads parameter in your PHP' ), ini_get( 'max_file_uploads' ) ) );
			?>
		</div>
		<div class="um-user-photos-max-size-upload-error">
			<?php
			// translators: %1$s: files size.
			echo esc_html( sprintf( __( 'You cannot upload more than %s photos size at one time. Please upload less photos or increase the post_max_size parameter in your PHP' ), ini_get( 'post_max_size' ) ) );
			?>
		</div>
		<div class="um-field um-user-photos-modal-footer text-right">
			<?php
			$privacy_options = apply_filters(
				'um_user_photos_privacy_options_dropdown',
				array(
					'everyone' => __( 'Everyone', 'um-user-photos' ),
					'only_me'  => __( 'Only me', 'um-user-photos' ),
				)
			);
			?>

			<select class="um-form-field um-select2" name="user_photos_privacy">
				<?php foreach ( $privacy_options as $key => $label ) { ?>
					<option value="<?php echo esc_attr( $key ); ?>" <?php selected( $privacy, $key ); ?>><?php echo esc_html( $label ); ?></option>
				<?php } ?>
			</select>
			<button class="um-modal-btn um-galley-modal-update"><?php esc_html_e( 'Update', 'um-user-photos' ); ?></button>
			<label class="um-modal-btn alt" <?php echo ( true === $enable_upload ) ? '' : 'style="display:none;"'; ?>>
				<i class="um-icon-plus"></i> <?php esc_html_e( 'Select photos', 'um-user-photos' ); ?>
				<input id="um-user-photos-input-album-images" style="display:none;" type="file" name="album_images[]" accept="image/*" multiple <?php echo ( true === $enable_upload ) ? '' : 'disabled'; ?> />
			</label>
			<a href="javascript:void(0);" class="um-modal-btn alt um-user-photos-modal-close-link"><?php esc_html_e( 'Cancel', 'um-user-photos' ); ?></a>
		</div>

		<input type="hidden" name="album_id" value="<?php echo esc_attr( $album->ID ); ?>"/>
		<?php wp_nonce_field( 'um_edit_album' ); ?>
	</form>
</div>
