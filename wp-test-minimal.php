<?php
// Minimal WordPress test - bypass plugins and themes
echo "<h1>WordPress Minimal Test</h1>";

// Define WordPress constants to prevent loading plugins
define('WP_USE_THEMES', false);
define('SHORTINIT', true);

echo "<p>Loading WordPress core...</p>";

try {
    // Load WordPress bootstrap
    require_once('./wp-load.php');
    echo "✓ WordPress core loaded successfully<br>";
    
    // Test database connection
    global $wpdb;
    if ($wpdb) {
        echo "✓ WordPress database connection established<br>";
        
        // Test a simple query
        $result = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_status = 'publish'");
        echo "✓ Found $result published posts<br>";
        
        // Check site URL
        $home_url = get_option('home');
        $site_url = get_option('siteurl');
        echo "✓ Home URL: $home_url<br>";
        echo "✓ Site URL: $site_url<br>";
        
        // Check if we can load a post
        $posts = $wpdb->get_results("SELECT ID, post_title FROM {$wpdb->posts} WHERE post_status = 'publish' AND post_type = 'post' LIMIT 5");
        if ($posts) {
            echo "✓ Recent posts:<br>";
            foreach ($posts as $post) {
                echo "- {$post->post_title} (ID: {$post->ID})<br>";
            }
        } else {
            echo "- No published posts found<br>";
        }
        
    } else {
        echo "✗ WordPress database connection failed<br>";
    }
    
} catch (Exception $e) {
    echo "✗ Error loading WordPress: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "✗ Fatal error loading WordPress: " . $e->getMessage() . "<br>";
}

echo "<h2>WordPress Constants Check</h2>";
echo "ABSPATH: " . (defined('ABSPATH') ? ABSPATH : 'Not defined') . "<br>";
echo "WP_CONTENT_DIR: " . (defined('WP_CONTENT_DIR') ? WP_CONTENT_DIR : 'Not defined') . "<br>";
echo "WP_PLUGIN_DIR: " . (defined('WP_PLUGIN_DIR') ? WP_PLUGIN_DIR : 'Not defined') . "<br>";

echo "<h2>Active Theme Check</h2>";
if (function_exists('get_option')) {
    $theme = get_option('stylesheet');
    $template = get_option('template');
    echo "Active theme: $theme<br>";
    echo "Template: $template<br>";
} else {
    echo "WordPress functions not available<br>";
}

echo "<h2>Plugin Check</h2>";
if (function_exists('get_option')) {
    $active_plugins = get_option('active_plugins');
    if ($active_plugins && is_array($active_plugins)) {
        echo "Active plugins (" . count($active_plugins) . "):<br>";
        foreach ($active_plugins as $plugin) {
            echo "- $plugin<br>";
        }
    } else {
        echo "No active plugins found<br>";
    }
} else {
    echo "WordPress functions not available<br>";
}

echo "<h2>Memory Usage</h2>";
echo "Memory used: " . memory_get_usage(true) / 1024 / 1024 . " MB<br>";
echo "Memory limit: " . ini_get('memory_limit') . "<br>";
echo "Max execution time: " . ini_get('max_execution_time') . " seconds<br>";

echo "<h2>Test Complete</h2>";
echo "If you can see this, WordPress core is loading but something else is causing the timeout.";
?>
