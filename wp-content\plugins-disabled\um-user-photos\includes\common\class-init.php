<?php
namespace um_ext\um_user_photos\common;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Init
 *
 * @package um_ext\um_user_photos\common
 */
class Init {

	/**
	 * Create classes' instances where __construct isn't empty for hooks init
	 */
	public function includes() {
		$this->activity();
		$this->album()->hooks();
		$this->cpt();
		$this->gallery()->hooks();
		$this->email_notifications();
		$this->media();
		$this->profile();
		$this->query()->hooks();
		$this->settings();
		if ( defined( 'UM_DEV_MODE' ) && UM_DEV_MODE && UM()->options()->get( 'enable_new_ui' ) ) {
			$this->shortcodes();
		} else {
			$this->shortcodes_v2();
		}
		$this->uploader();
		$this->user()->hooks();
	}

	/**
	 * @return Activity|false
	 */
	public function activity() {
		if ( ! class_exists( 'UM_Activity_API' ) ) {
			return false;
		}

		if ( empty( UM()->classes['um_ext\um_user_photos\common\activity'] ) ) {
			UM()->classes['um_ext\um_user_photos\common\activity'] = new Activity();
		}
		return UM()->classes['um_ext\um_user_photos\common\activity'];
	}

	/**
	 * @return Album
	 */
	public function album() {
		if ( empty( UM()->classes['um_ext\um_user_photos\common\album'] ) ) {
			UM()->classes['um_ext\um_user_photos\common\album'] = new Album();
		}
		return UM()->classes['um_ext\um_user_photos\common\album'];
	}

	/**
	 * @return CPT
	 */
	public function cpt() {
		if ( empty( UM()->classes['um_ext\um_user_photos\common\cpt'] ) ) {
			UM()->classes['um_ext\um_user_photos\common\cpt'] = new CPT();
		}
		return UM()->classes['um_ext\um_user_photos\common\cpt'];
	}

	/**
	 * @return Gallery
	 */
	public function gallery() {
		if ( empty( UM()->classes['um_ext\um_user_photos\common\gallery'] ) ) {
			UM()->classes['um_ext\um_user_photos\common\gallery'] = new Gallery();
		}
		return UM()->classes['um_ext\um_user_photos\common\gallery'];
	}

	/**
	 * @return Email_Notifications
	 */
	public function email_notifications() {
		if ( empty( UM()->classes['um_ext\um_user_photos\common\email_notifications'] ) ) {
			UM()->classes['um_ext\um_user_photos\common\email_notifications'] = new Email_Notifications();
		}
		return UM()->classes['um_ext\um_user_photos\common\email_notifications'];
	}

	/**
	 * @return Media
	 */
	public function media() {
		if ( empty( UM()->classes['um_ext\um_user_photos\common\media'] ) ) {
			UM()->classes['um_ext\um_user_photos\common\media'] = new Media();
		}
		return UM()->classes['um_ext\um_user_photos\common\media'];
	}

	/**
	 * @return Photo
	 */
	public function photo() {
		if ( empty( UM()->classes['um_ext\um_user_photos\common\photo'] ) ) {
			UM()->classes['um_ext\um_user_photos\common\photo'] = new Photo();
		}
		return UM()->classes['um_ext\um_user_photos\common\photo'];
	}

	/**
	 * @return Profile
	 */
	public function profile() {
		if ( empty( UM()->classes['um_ext\um_user_photos\common\profile'] ) ) {
			UM()->classes['um_ext\um_user_photos\common\profile'] = new Profile();
		}
		return UM()->classes['um_ext\um_user_photos\common\profile'];
	}

	/**
	 * @return Query
	 */
	public function query() {
		if ( empty( UM()->classes['um_ext\um_user_photos\common\query'] ) ) {
			UM()->classes['um_ext\um_user_photos\common\query'] = new Query();
		}
		return UM()->classes['um_ext\um_user_photos\common\query'];
	}

	/**
	 * @return Settings
	 */
	public function settings() {
		if ( empty( UM()->classes['um_ext\um_user_photos\common\settings'] ) ) {
			UM()->classes['um_ext\um_user_photos\common\settings'] = new Settings();
		}
		return UM()->classes['um_ext\um_user_photos\common\settings'];
	}

	/**
	 * @return Shortcodes
	 */
	public function shortcodes() {
		if ( empty( UM()->classes['um_ext\um_user_photos\common\shortcodes'] ) ) {
			UM()->classes['um_ext\um_user_photos\common\shortcodes'] = new Shortcodes();
		}
		return UM()->classes['um_ext\um_user_photos\common\shortcodes'];
	}

	/**
	 * Note: It will be deprecated since new UI is live.
	 * @todo deprecate since new UI is live.
	 * @return Shortcodes_V2
	 */
	public function shortcodes_v2() {
		if ( empty( UM()->classes['um_ext\um_user_photos\common\shortcodes_v2'] ) ) {
			UM()->classes['um_ext\um_user_photos\common\shortcodes_v2'] = new Shortcodes_V2();
		}
		return UM()->classes['um_ext\um_user_photos\common\shortcodes_v2'];
	}

	/**
	 * @return Uploader
	 */
	public function uploader() {
		if ( empty( UM()->classes['um_ext\um_user_photos\common\uploader'] ) ) {
			UM()->classes['um_ext\um_user_photos\common\uploader'] = new Uploader();
		}
		return UM()->classes['um_ext\um_user_photos\common\uploader'];
	}

	/**
	 * @return User
	 */
	public function user() {
		if ( empty( UM()->classes['um_ext\um_user_photos\common\user'] ) ) {
			UM()->classes['um_ext\um_user_photos\common\user'] = new User();
		}
		return UM()->classes['um_ext\um_user_photos\common\user'];
	}
}
