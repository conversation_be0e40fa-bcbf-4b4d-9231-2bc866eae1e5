<?php
namespace um_ext\um_user_photos\ajax;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Comments
 *
 * @package um_ext\um_user_photos\ajax
 */
class Comments {

	public $per_page;

	/**
	 * Comments constructor.
	 */
	public function __construct() {
	}

	/**
	 *
	 */
	public function prepare_variables() {
		$this->per_page = apply_filters( 'um_user_photos_comments_per_page', 10 );
	}

	public function hooks() {
		add_action( 'init', array( $this, 'prepare_variables' ) );

		// delete comment
		add_action( 'wp_ajax_um_user_photos_comment_delete', array( $this, 'um_user_photos_comment_delete' ) );
		// show photo likes modal
		add_action( 'wp_ajax_get_um_user_photos_comment_likes', array( $this, 'get_um_user_photos_comment_likes' ) );
		add_action( 'wp_ajax_nopriv_get_um_user_photos_comment_likes', array( $this, 'get_um_user_photos_comment_likes' ) );

		// load more comments
		add_action( 'wp_ajax_um_user_photos_load_more_comments', array( $this, 'load_more_comments' ) );
		add_action( 'wp_ajax_nopriv_um_user_photos_load_more_comments', array( $this, 'load_more_comments' ) );

		// update comment
		add_action( 'wp_ajax_um_user_photos_comment_update', array( $this, 'um_user_photos_comment_update' ) );

		// post comment
		add_action( 'wp_ajax_um_user_photos_post_comment', array( $this, 'um_user_photos_post_comment' ) );

		// like comment
		add_action( 'wp_ajax_um_user_photos_like_comment', array( $this, 'um_user_photos_like_comment' ) );

		// unlike comment
		add_action( 'wp_ajax_um_user_photos_unlike_comment', array( $this, 'um_user_photos_unlike_comment' ) );
	}

	/**
	 * Delete comment
	 *
	 */
	public function um_user_photos_comment_delete() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_comment_delete' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$comment_id = absint( $_POST['comment_id'] ); // phpcs:ignore WordPress.Security.NonceVerification
		if ( empty( $comment_id ) ) {
			wp_send_json_error( __( 'Invalid comment ID', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_delete_comment( $comment_id ) ) {
			wp_send_json_error( __( 'You are not authorized for this', 'um-user-photos' ) );
		}

		$comment = get_comment( $comment_id );
		$post_id = absint( $comment->comment_post_ID );
		$deleted = wp_delete_comment( $comment_id, true );

		$comment_count = UM()->User_Photos()->common()->photo()->get_comment_count( $post_id );

		if ( $deleted ) {
			wp_send_json_success( array( 'count' => $comment_count ) );
		} else {
			wp_send_json_error( __( 'Could not be deleted.', 'um-user-photos' ) );
		}
	}

	/**
	 * Show photo's comments likes.
	 */
	public function get_um_user_photos_comment_likes() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_get_comment_likes' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$comment_id = absint( $_POST['comment_id'] );

		if ( empty( $comment_id ) ) {
			wp_send_json_error( __( 'Invalid comment ID', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_view_comment_likes( $comment_id ) ) {
			wp_send_json_error( __( 'You are not authorized for this', 'um-user-photos' ) );
		}

		$likes = UM()->User_Photos()->common()->photo()->get_comment_likes( $comment_id );

		$content = UM()->ajax()->esc_html_spaces(
			UM()->get_template(
				'v3/modal/likes.php',
				UM_USER_PHOTOS_PLUGIN,
				array(
					'likes'   => $likes,
					'context' => 'comment',
				)
			)
		);

		wp_send_json_success( array( 'content' => $content ) );
	}

	/**
	 * Load more comments
	 *
	 */
	public function load_more_comments() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_load_more_comments' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		if ( UM()->options()->get( 'um_user_photos_disable_comments' ) ) {
			wp_send_json_error( __( 'Comments are disabled.', 'um-user-photos' ) );
		}

		$image_id = absint( $_POST['image_id'] );
		if ( ! UM()->User_Photos()->common()->user()->can_view_photo( $image_id ) ) {
			wp_send_json_error( __( 'You are not authorized for this', 'um-user-photos' ) );
		}

		$disable_comments = get_post_meta( $image_id, '_disable_comment', true );
		if ( ! empty( $disable_comments ) ) {
			wp_send_json_error( __( 'Comments are disabled for this photo.', 'um-user-photos' ) );
		}

		$last_id  = absint( $_POST['last_id'] );
		$comments = UM()->User_Photos()->common()->photo()->get_comments( $image_id, array( 'last_id' => $last_id ) );

		ob_start();
		if ( ! empty( $comments ) ) {
			foreach ( $comments as $photo_comment ) {
				$is_url  = filter_var( $photo_comment->comment_content, FILTER_VALIDATE_URL );
				$content = $is_url ? '<a href="' . esc_url( $photo_comment->comment_content ) . '" target="_blank">' . esc_html( $photo_comment->comment_content ) . '</a>' : esc_html( $photo_comment->comment_content );

				UM()->get_template(
					'v3/comment.php',
					UM_USER_PHOTOS_PLUGIN,
					array(
						'user_id'       => $photo_comment->user_id,
						'content'       => $content,
						'date'          => $photo_comment->comment_date,
						'id'            => $photo_comment->comment_ID,
						'image_id'      => $image_id,
						'photo_comment' => $photo_comment,
					),
					true
				);
			}
		}
		$output = ob_get_clean();
		$output = UM()->ajax()->esc_html_spaces( $output );

		wp_send_json_success( $output );
	}

	/**
	 * Update comment
	 *
	 */
	public function um_user_photos_comment_update() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_comment_update' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$comment_id = absint( $_POST['comment_id'] );

		if ( empty( $comment_id ) ) {
			wp_send_json_error( __( 'Invalid comment ID', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_edit_comment( $comment_id ) ) {
			wp_send_json_error( __( 'You are not authorized for this', 'um-user-photos' ) );
		}

		$comment_content = sanitize_textarea_field( $_POST['comment_content'] );

		$updated = wp_update_comment(
			array(
				'comment_ID'      => $comment_id,
				'comment_content' => $comment_content,
			)
		);

		if ( $updated ) {

			wp_send_json_success(
				array(
					'message'    => '<p style="background: green;color: #fff;text-align: center; line-height: 40px;border-radius: 5px;">' . __( 'Comment updated', 'um-user-photos' ) . '</p>',
					'status'     => __( 'Saved', 'um-user-photos' ),
					'comment'    => $comment_content,
					'comment_id' => $comment_id,
				)
			);

		} else {
			wp_send_json_error(
				array(
					'status' => __( 'Error', 'um-user-photos' ),
				)
			);
		}
	}

	/**
	 * Post comment
	 */
	public function um_user_photos_post_comment() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_comment_post' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$post_id = absint( $_POST['image_id'] );

		if ( ! UM()->User_Photos()->common()->user()->can_comment( $post_id ) ) {
			wp_send_json_error( __( 'You are not authorized to comment this photo.', 'um-user-photos' ) );
		}

		if ( ! isset( $_POST['comment'] ) || '' === sanitize_text_field( $_POST['comment'] ) ) {
			$message = esc_html__( 'Enter a comment first', 'um-user-photos' );
			wp_send_json_error( $message );
		}

		um_fetch_user( get_current_user_id() );

		$orig_content    = sanitize_text_field( $_POST['comment'] );
		$comment_content = wp_kses(
			$orig_content,
			array(
				'br' => array(),
			)
		);

		$time = current_time( 'mysql' );
		$data = array(
			'comment_post_ID'      => $post_id,
			'comment_author'       => um_user( 'display_name' ),
			'comment_author_email' => um_user( 'user_email' ),
			'comment_author_url'   => um_user_profile_url(),
			'comment_content'      => trim( $comment_content ),
			'user_id'              => get_current_user_id(),
			'comment_approved'     => 1,
			'comment_author_IP'    => um_user_ip(),
			'comment_type'         => 'um-user-photos',
			'comment_date'         => $time,
		);

		$commentid = wp_insert_comment( $data );

		wp_update_comment_count_now( $post_id );

		$is_url  = filter_var( $comment_content, FILTER_VALIDATE_URL );
		$content = $is_url ? '<a href="' . esc_url( $comment_content ) . '" target="_blank">' . esc_html( $comment_content ) . '</a>' : esc_html( $comment_content );

		ob_start();

		UM()->get_template(
			'v3/comment.php',
			UM_USER_PHOTOS_PLUGIN,
			array(
				'user_id'  => um_user( 'ID' ),
				'content'  => $content,
				'date'     => $time,
				'id'       => $commentid,
				'image_id' => $post_id,
			),
			true
		);

		$content = ob_get_clean();

		$comment_count = UM()->User_Photos()->common()->photo()->get_comment_count( $post_id );

		wp_send_json_success(
			array(
				'content' => $content,
				'count'   => $comment_count,
			)
		);
	}

	/**
	 * Like comment
	 */
	public function um_user_photos_like_comment() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_comment_like' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$comment_id = absint( $_POST['commentid'] );

		if ( empty( $comment_id ) ) {
			wp_send_json_error( __( 'Invalid comment ID', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_like_comment( $comment_id ) ) {
			wp_send_json_error( __( 'You are not authorized to like this comment', 'um-user-photos' ) );
		}

		$likes = UM()->User_Photos()->common()->photo()->like_comment( $comment_id );
		if ( false === $likes ) {
			wp_send_json_error( __( 'Cannot like this comment.', 'um-user-photos' ) );
		}

		$content = UM()->frontend()::layouts()::avatars_list(
			$likes,
			array(
				'wrapper' => 'span',
				'size'    => 'xs',
				'count'   => 5,
			)
		);

		wp_send_json_success(
			array(
				'count'   => count( $likes ),
				'user_id' => get_current_user_id(),
				'content' => $content,
			)
		);
	}

	/**
	 * Unlike comment
	 */
	public function um_user_photos_unlike_comment() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_comment_unlike' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$comment_id = absint( $_POST['commentid'] );

		if ( empty( $comment_id ) ) {
			wp_send_json_error( __( 'Invalid comment ID', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_unlike_comment( $comment_id ) ) {
			wp_send_json_error( __( 'You are not authorized to like this comment', 'um-user-photos' ) );
		}

		$likes = UM()->User_Photos()->common()->photo()->unlike_comment( $comment_id );
		if ( false === $likes ) {
			wp_send_json_error( __( 'Cannot unlike this comment.', 'um-user-photos' ) );
		}

		$content = '';
		if ( ! empty( $likes ) ) {
			$content = UM()->frontend()::layouts()::avatars_list(
				$likes,
				array(
					'wrapper' => 'span',
					'size'    => 'xs',
					'count'   => 5,
				)
			);
		}

		wp_send_json_success(
			array(
				'count'   => count( $likes ),
				'user_id' => get_current_user_id(),
				'content' => $content,
			)
		);
	}
}
