(()=>{"use strict";var e={889:(e,t,r)=>{r.d(t,{$5:()=>s,Nt:()=>o,S_:()=>i,cq:()=>a});const{__:__}=wp.i18n,n={
/* translators: text read by a screen reader when a warning icon is displayed in front of an error message. */
warning:__("Warning.","jetpack-forms")},o=(e,t,r,n=null)=>{const o=`${e.name?e.name:e.getAttribute("name")}-error`;let i=t.querySelector(`#${o}`);if(!i){i=a(o);const t=e.closest(r.hasInsetLabel?".contact-form__inset-label-wrap":".grunion-field-wrap");t&&t.appendChild(i)}n||(n=e.validationMessage),i.replaceChildren(s(n)),e.setAttribute("aria-invalid","true"),e.setAttribute("aria-describedby",o)},i=(e,t)=>{e.removeAttribute("aria-invalid"),e.removeAttribute("aria-describedby");const r=e.closest(t.hasInsetLabel?".contact-form__inset-label-wrap":".grunion-field-wrap");if(!r)return;const n=r.querySelector(".contact-form__input-error");n&&n.remove();const o=e.closest("form"),i=o.querySelectorAll(".contact-form__input-error"),a=o.querySelector(".contact-form__error");a&&0===i.length&&a.remove()},a=e=>{const t=document.createElement("div");return t.id=e,t.classList.add("contact-form__input-error"),t},s=e=>{const t=document.createDocumentFragment();return t.appendChild(c()),t.appendChild(l(e)),t},c=()=>{const e=document.createElement("span"),t=document.createElement("span"),r=document.createElement("i");return t.textContent=n.warning,t.classList.add("visually-hidden"),r.setAttribute("aria-hidden",!0),e.classList.add("contact-form__warning-icon"),e.appendChild(t),e.appendChild(r),e},l=e=>{const t=document.createElement("span");return t.textContent=e,t}},990:(e,t,r)=>{r.d(t,{Z:()=>n});const n=(e,t)=>{let r,n,o;if(!e)return!1;switch(t){case"mm/dd/yy":[n,o,r]=e.split("/").map(Number);break;case"dd/mm/yy":[o,n,r]=e.split("/").map(Number);break;case"yy-mm-dd":[r,n,o]=e.split("-").map(Number);break;default:return!1}if(isNaN(r)||isNaN(n)||isNaN(o))return!1;const i=new Date(r,n-1,o);return i.getFullYear()===r&&i.getMonth()===n-1&&i.getDate()===o}}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,r),i.exports}r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var n=r(889),o=r(990);document.addEventListener("DOMContentLoaded",(()=>{a()}));const{__:__,_n:_n}=wp.i18n,i={
/* translators: text read by a screen reader when a warning icon is displayed in front of an error message. */
warning:__("Warning.","jetpack-forms"),
/* translators: error message shown when one or more fields of the form are invalid. */
invalidForm:__("Please make sure all fields are valid.","jetpack-forms"),
/* translators: error message shown when a multiple choice field requires at least one option to be selected. */
checkboxMissingValue:__("Please select at least one option.","jetpack-forms"),
/* translators: error message shown when a user enters an invalid date */
invalidDate:__("The date is not valid.","jetpack-forms"),
/* translators: text read by a screen reader when a form is being submitted */
submittingForm:__("Submitting form","jetpack-forms"),
/* translators: generic error message */
genericError:__("Please correct this field","jetpack-forms"),fileRequired:__("A file is required.","jetpack-forms"),
/* translators: error message shown when no field has been filled out */
emptyForm:__("The form you are trying to submit is empty.","jetpack-forms"),errorCount:e=>/* translators: message displayed when errors need to be fixed. %d is the number of errors. */
_n("You need to fix %d error.","You need to fix %d errors.",e,"jetpack-forms")},a=()=>{document.querySelectorAll(".wp-block-jetpack-contact-form-container form.contact-form").forEach(s)};const s=e=>{e.hasAttribute("novalidate")||e.setAttribute("novalidate",!0);const t={hasInsetLabel:y(e)};let r={};const n=o=>{if(o.preventDefault(),l(e))return;w(e,r,t);const i=c(e);(function(e){const t=e.cloneNode(!0);Array.from(t.querySelectorAll("select")).forEach((t=>{t.value=e.querySelector(`select[id="${t.id}"`)?.value})),Array.from(t.querySelectorAll('input[type="hidden"]:not(.include-hidden)')).forEach((e=>e.remove()));const r=new FormData(t);return!Array.from(r.values()).some((e=>e instanceof File?!!e.size:!!e?.trim?.()))})(e)&&i?Y(e,[],{disableLiveRegion:!0,type:"emptyForm"}):i?(r={},e.removeEventListener("submit",n),F(e)):r=D(e,t)};e.addEventListener("submit",n)},c=e=>{if(!e.checkValidity())return!1;const t=g(e);for(const e of t)if(m(e)&&!h(e))return!1;const r=_(e);for(const e of r)if(!b(e))return!1;const n=A(e);for(const e of n){if(q(e))return!1;if(C(e))return!1}return!0},l=e=>!0===e.getAttribute("data-submitting"),u=e=>"fieldset"===e.tagName.toLowerCase()&&e.classList.contains("grunion-checkbox-multiple-options"),d=e=>"fieldset"===e.tagName.toLowerCase()&&e.classList.contains("grunion-radio-options"),m=e=>e.hasAttribute("data-required"),p=e=>{return"input"===(t=e).tagName.toLowerCase()&&t.classList.contains("jp-contact-form-date")&&e.value?b(e):e.validity.valid;var t},f=e=>{const t=Array.from(e.querySelectorAll('input[type="radio"]'));return t.length>0&&t.every((e=>e.validity.valid))},h=e=>{if(!m(e))return!0;const t=Array.from(e.querySelectorAll('input[type="checkbox"]'));return t.length>0&&t.some((e=>e.checked))},b=e=>{const t=e.getAttribute("data-format"),r=e.value;if(r&&t){if(!(0,o.Z)(r,t))return e.setCustomValidity(i.invalidDate),!1;e.setCustomValidity("")}return!0},y=e=>{const t=e.querySelector(".wp-block-jetpack-contact-form");if(!t)return!1;const r=t.classList;return r.contains("is-style-outlined")||r.contains("is-style-animated")},v=e=>e.querySelector('[type="submit"]')||e.querySelector('button:not([type="reset"])'),g=e=>Array.from(e.querySelectorAll(".grunion-checkbox-multiple-options")),A=e=>Array.from(e.querySelectorAll(".jetpack-form-file-field__container")),q=e=>!(0===Array.from(e.querySelectorAll(".is-error")).length),C=e=>"1"===e.dataset.isRequired&&0===Array.from(e.querySelectorAll(".jetpack-form-file-field__hidden")).length,_=e=>Array.from(e.querySelectorAll("input.jp-contact-form-date")),k=e=>{const t=E((e=>Array.from(e.elements).filter((e=>!["hidden","submit"].includes(e.type)&&null!==e.offsetParent)))(e)),r={simple:t.default,singleChoice:[],multipleChoice:[]},n=t.radios.reduce(((e,t)=>e.includes(t.name)?e:[...e,t.name]),[]);for(const t of n){const n=e.querySelector(`input[type="radio"][name="${t}"]`);if(n){const e=n.closest("fieldset");e&&r.singleChoice.push(e)}}const o=t.checkboxes.reduce(((e,t)=>e.includes(t.name)?e:[...e,t.name]),[]);for(const t of o){const n=e.querySelector(`input[type="checkbox"][name="${t}"]`);if(n){const e=n.closest("fieldset");e&&r.multipleChoice.push(e)}}return r.fileFields=A(e),r},S=e=>e.querySelector(".contact-form__error"),L=e=>e.querySelectorAll("[aria-invalid]"),E=e=>e.reduce(((e,t)=>{switch(t.type){case"radio":e.radios.push(t);break;case"checkbox":t.name.indexOf("[]")===t.name.length-2?e.checkboxes.push(t):e.default.push(t);break;default:e.default.push(t)}return e}),{default:[],radios:[],checkboxes:[]}),w=(e,t,r)=>{x(e,r);for(const r in t)e.querySelectorAll(`[name="${r}"]`).forEach((e=>e.removeEventListener(t[r][0],t[r][1])))},x=(e,t)=>{N(e),j(e,t)},N=e=>{const t=S(e);t&&t.replaceChildren()},j=(e,t)=>{for(const r of L(e))d(r)||u(r)?$(r):(0,n.S_)(r,t)},$=e=>{e.removeAttribute("aria-invalid"),e.removeAttribute("aria-describedby");const t=e.querySelector(".contact-form__input-error");t&&t.replaceChildren()},F=e=>{M(e),e.setAttribute("data-submitting",!0),e.submit()},M=e=>{const t=v(e);t&&(t.setAttribute("aria-disabled",!0),t.appendChild((()=>{const e=document.createElement("span"),t=document.createElement("span"),r=document.createElement("span");return t.setAttribute("aria-hidden",!0),t.innerHTML='<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12,1A11,11,0,1,0,23,12,11,11,0,0,0,12,1Zm0,19a8,8,0,1,1,8-8A8,8,0,0,1,12,20Z" opacity=".25"/><path d="M10.14,1.16a11,11,0,0,0-9,8.92A1.59,1.59,0,0,0,2.46,12,1.52,1.52,0,0,0,4.11,10.7a8,8,0,0,1,6.66-6.61A1.42,1.42,0,0,0,12,2.69h0A1.57,1.57,0,0,0,10.14,1.16Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path></svg>',r.classList.add("visually-hidden"),r.textContent=i.submittingForm,e.classList.add("contact-form__spinner"),e.appendChild(t),e.appendChild(r),e})()))},D=(e,t)=>(I(e,t),R(e,t)),O=e=>e.classList.contains("jetpack-form-file-field__container"),P=(e,t)=>{new MutationObserver((e=>{for(const r of e)"attributes"===r.type&&"aria-invalid"===r.attributeName&&t()})).observe(e,{attributes:!0})},R=(e,t)=>{let r={};const n=()=>B(e);for(const i of L(e)){let a;a=d(i)&&(o=i,Array.from(o.querySelectorAll('input[type="radio"]')).some((e=>e.hasAttribute("required")||e.hasAttribute("aria-required"))))?T(i,n,e,t):u(i)&&m(i)?V(i,n,e,t):O(i)?P(i,n):Z(i,n,e,t),r={...r,...a}}var o;return r},T=(e,t,r,n)=>{const o={},i=()=>{f(e)?$(e):z(e,r,n),t()},a=e.querySelectorAll('input[type="radio"]');for(const e of a)e.addEventListener("blur",i),e.addEventListener("change",i),o[e.name]=["blur",i],o[e.name]=["change",i];return o},V=(e,t,r,n)=>{const o={},i=()=>{h(e)?$(e):H(e,r,n),t()},a=e.querySelectorAll('input[type="checkbox"]');for(const e of a)e.addEventListener("blur",i),e.addEventListener("change",i),o[e.name]=["blur",i],o[e.name]=["change",i];return o},Z=(e,t,r,o)=>{const i=e.validity.valueMissing,a={},s=()=>{p(e)?(0,n.S_)(e,o):(0,n.Nt)(e,r,o),t()},c=()=>{e.validity.valueMissing?(0,n.Nt)(e,r,o):(0,n.S_)(e,o),t()};return e.addEventListener("blur",s),a[e.name]=["blur",s],i&&(e.addEventListener("input",c),a[e.name]=["input",c]),a},I=(e,t)=>{const r=W(e,t);Y(e,r)},Y=(e,t,r={})=>{let o=S(e);if(!o){o=(()=>{const e=document.createElement("div");return e.classList.add("contact-form__error"),e})();const t=v(e);t?t.parentNode.parentNode.insertBefore(o,t.parentNode):e.appendChild(o)}const{disableLiveRegion:a}=r;a?(o.removeAttribute("aria-live"),o.removeAttribute("role")):(o.setAttribute("aria-live","assertive"),o.setAttribute("role","alert"));const s=t.length;if(!s&&i[r.type])return void o.appendChild((0,n.$5)(i[r.type]));const c=[i.invalidForm];s>0&&c.push(i.errorCount(s).replace("%d",s)),o.appendChild((0,n.$5)(c.join(" "))),s>0&&o.appendChild(((e,t)=>{const r=document.createElement("ul");for(const n of t){const t=n.id;if(!t)continue;let o;if(o=u(n)||d(n)?n.querySelector("legend"):e.querySelector(`label[for="${t}"]`),!o)continue;const i=document.createElement("li"),a=document.createElement("a");a.textContent=o.innerText,a.setAttribute("href",`#${t}`),i.appendChild(a),r.appendChild(i)}return r})(e,t))},B=e=>{N(e),c(e)||Y(e,L(e),{disableLiveRegion:!0})},W=(e,t)=>{const r=[],{simple:o,singleChoice:a,multipleChoice:s,fileFields:c}=k(e);for(const i of o)p(i)||((0,n.Nt)(i,e,t),r.push(i));for(const n of a)f(n)||(z(n,e,t),r.push(n));for(const n of s)h(n)||(H(n,e,t),r.push(n));for(const o of c)C(o)?((0,n.Nt)(o,e,t,i.fileRequired),r.push(o)):q(o)&&((0,n.Nt)(o,e,t,i.genericError),r.push(o));return r},z=(e,t,r)=>{G(e,t,r)},H=(e,t,r)=>{G(e,t,{...r,message:i.checkboxMissingValue})},G=(e,t,r)=>{const o=e.querySelector("input");if(!o)return;const a=`${o.name.replace("[]","")}-error`;let s=t.querySelector(`#${a}`);s||(s=(0,n.cq)(a)),s.replaceChildren((0,n.$5)(o.validationMessage||r.message||i.genericError)),e.appendChild(s),e.setAttribute("aria-invalid","true"),e.setAttribute("aria-describedby",a)}})();