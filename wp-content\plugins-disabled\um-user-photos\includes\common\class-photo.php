<?php
namespace um_ext\um_user_photos\common;

use WP_Comment;
use WP_Comment_Query;
use WP_Post;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Photo
 *
 * @package um_ext\um_user_photos\common
 */
class Photo {

	/**
	 * Photo constructor.
	 */
	public function __construct() {
	}

	/**
	 *
	 * @param int|WP_Post $photo Photo ID or Photo WP_Post object.
	 *
	 * @return bool
	 */
	public function exists( $photo ) {
		$status = get_post_status( $photo );
		return false !== $status;
	}

	/**
	 *
	 * @param int|WP_Post $photo Photo ID or Photo WP_Post object.
	 *
	 * @return bool
	 */
	public function is_published( $photo ) {
		$status = get_post_status( $photo );
		return 'publish' === $status;
	}

	/**
	 *
	 * @param int|WP_Comment $comment Comment ID or WP_Comment object.
	 *
	 * @return bool
	 */
	public function comment_exists( $comment ) {
		$status = wp_get_comment_status( $comment );
		return false !== $status;
	}

	/**
	 *
	 * @param int|WP_Comment $comment Comment ID or WP_Comment object.
	 *
	 * @return bool
	 */
	public function is_comment_approved( $comment ) {
		$status = wp_get_comment_status( $comment );
		return 'approved' === $status;
	}

	public function get_comments( $post_id, $args = array() ) {
		if ( ! $this->is_published( $post_id ) ) {
			return false;
		}

		$disable_comments = get_post_meta( $post_id, '_disable_comment', true );
		if ( ! empty( $disable_comments ) ) {
			return false;
		}

		$args = wp_parse_args(
			$args,
			array(
				'last_id'  => false,
				'per_page' => UM()->User_Photos()->ajax()->comments()->per_page,
			)
		);

		$query_args = array(
			'post_id' => $post_id,
			'type'    => 'um-user-photos',
			'status'  => 'approve',
		);

		if ( false !== $args['per_page'] ) {
			$query_args['number'] = $args['per_page'];
		}

		$blocked_users = UM()->User_Photos()->common()->user()->get_blocked_users();
		if ( ! empty( $blocked_users ) ) {
			$query_args['author__not_in'] = $blocked_users;
		}

		if ( false !== $args['last_id'] ) {
			$last_id   = absint( $args['last_id'] );
			$last_time = get_comment_time( 'Y-m-d H:i:s', false, true, $last_id );

			if ( ! empty( $last_time ) ) {
				$query_args['date_query'] = array(
					'before' => $last_time,
				);
			}
		}

		$comment_query = new WP_Comment_Query();

		return $comment_query->query( $query_args );
	}

	public function get_comment_count( $post_id ) {
		if ( ! $this->is_published( $post_id ) ) {
			return false;
		}

		$disable_comments = get_post_meta( $post_id, '_disable_comment', true );
		if ( ! empty( $disable_comments ) ) {
			return false;
		}

		$comments = $this->get_comments( $post_id, array( 'per_page' => false ) );
		return count( $comments );
	}

	/**
	 * @param int      $post_id
	 * @param null|int $user_id
	 * @param bool     $exclude_blocked
	 *
	 * @return array|false
	 */
	public function like( $post_id, $user_id = null, $exclude_blocked = true ) {
		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		if ( empty( $user_id ) ) {
			return false;
		}

		if ( ! $this->is_published( $post_id ) ) {
			return false;
		}

		$liked = get_post_meta( $post_id, '_liked', true );

		if ( $liked && ! in_array( $user_id, $liked, true ) ) {
			$liked[] = $user_id;
		} else {
			$liked = array( $user_id );
		}

		$result = update_post_meta( $post_id, '_liked', $liked );
		if ( false === $result ) {
			return false;
		}

		return $exclude_blocked ? $this->get_likes( $post_id, $liked ) : $liked;
	}

	/**
	 * @param int      $post_id
	 * @param null|int $user_id
	 * @param bool     $exclude_blocked
	 *
	 * @return array|false
	 */
	public function unlike( $post_id, $user_id = null, $exclude_blocked = true ) {
		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		if ( empty( $user_id ) ) {
			return false;
		}

		if ( ! $this->is_published( $post_id ) ) {
			return false;
		}

		$liked = get_post_meta( $post_id, '_liked', true );

		if ( ! empty( $liked ) ) {
			$key = array_search( $user_id, $liked, true );
			if ( false !== $key ) {
				unset( $liked[ $key ] );
			}
		} else {
			$liked = array();
		}

		$result = update_post_meta( $post_id, '_liked', $liked );
		if ( false === $result ) {
			return false;
		}

		return $exclude_blocked ? $this->get_likes( $post_id, $liked ) : $liked;
	}

	/**
	 * @param int        $post_id
	 * @param null|array $pre_result
	 *
	 * @return array|false
	 */
	public function get_likes( $post_id, $pre_result = null ) {
		if ( ! $this->is_published( $post_id ) ) {
			return false;
		}

		// Maybe use predefined result of meta.
		if ( is_null( $pre_result ) ) {
			$likes = get_post_meta( $post_id, '_liked', true );
			if ( empty( $likes ) ) {
				$likes = array();
			}
		} else {
			$likes = $pre_result;
		}

		if ( ! empty( $likes ) ) {
			$blocked_users = UM()->User_Photos()->common()->user()->get_blocked_users();
			if ( ! empty( $blocked_users ) ) {
				$likes = array_diff( $likes, $blocked_users );
			}
		}

		return $likes;
	}

	/**
	 * @param int      $comment_id
	 * @param null|int $user_id
	 * @param bool     $exclude_blocked
	 *
	 * @return array|false
	 */
	public function like_comment( $comment_id, $user_id = null, $exclude_blocked = true ) {
		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		if ( empty( $user_id ) ) {
			return false;
		}

		if ( ! $this->is_comment_approved( $comment_id ) ) {
			return false;
		}

		$likes = get_comment_meta( $comment_id, '_likes', true );

		if ( $likes && ! in_array( $user_id, $likes, true ) ) {
			$likes[] = $user_id;
		} else {
			$likes = array( $user_id );
		}

		$result = update_comment_meta( $comment_id, '_likes', $likes );
		if ( false === $result ) {
			return false;
		}

		return $exclude_blocked ? $this->get_comment_likes( $comment_id, $likes ) : $likes;
	}

	/**
	 * @param int      $comment_id
	 * @param null|int $user_id
	 * @param bool     $exclude_blocked
	 *
	 * @return array|false
	 */
	public function unlike_comment( $comment_id, $user_id = null, $exclude_blocked = true ) {
		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		if ( empty( $user_id ) ) {
			return false;
		}

		if ( ! $this->is_comment_approved( $comment_id ) ) {
			return false;
		}

		$likes = get_comment_meta( $comment_id, '_likes', true );

		if ( $likes && in_array( $user_id, $likes, true ) ) {
			$key = array_search( $user_id, $likes, true );
			if ( false !== $key ) {
				unset( $likes[ $key ] );
			}
		} else {
			$likes = array();
		}

		$result = update_comment_meta( $comment_id, '_likes', $likes );
		if ( false === $result ) {
			return false;
		}

		return $exclude_blocked ? $this->get_comment_likes( $comment_id, $likes ) : $likes;
	}

	/**
	 * @param int        $comment_id
	 * @param null|array $pre_result
	 *
	 * @return array|false
	 */
	public function get_comment_likes( $comment_id, $pre_result = null ) {
		if ( ! $this->is_comment_approved( $comment_id ) ) {
			return false;
		}

		// Maybe use predefined result of meta.
		if ( is_null( $pre_result ) ) {
			$likes = get_comment_meta( $comment_id, '_likes', true );
			if ( empty( $likes ) ) {
				$likes = array();
			}
		} else {
			$likes = $pre_result;
		}

		if ( ! empty( $likes ) ) {
			$blocked_users = UM()->User_Photos()->common()->user()->get_blocked_users();
			if ( ! empty( $blocked_users ) ) {
				$likes = array_diff( $likes, $blocked_users );
			}
		}

		return $likes;
	}
}
