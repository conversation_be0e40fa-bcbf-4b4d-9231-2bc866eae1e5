<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Composer\\Installers\\AglInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AglInstaller.php',
    'Composer\\Installers\\AkauntingInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AkauntingInstaller.php',
    'Composer\\Installers\\AnnotateCmsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AnnotateCmsInstaller.php',
    'Composer\\Installers\\AsgardInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AsgardInstaller.php',
    'Composer\\Installers\\AttogramInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AttogramInstaller.php',
    'Composer\\Installers\\BaseInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/BaseInstaller.php',
    'Composer\\Installers\\BitrixInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/BitrixInstaller.php',
    'Composer\\Installers\\BonefishInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/BonefishInstaller.php',
    'Composer\\Installers\\BotbleInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/BotbleInstaller.php',
    'Composer\\Installers\\CakePHPInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CakePHPInstaller.php',
    'Composer\\Installers\\ChefInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ChefInstaller.php',
    'Composer\\Installers\\CiviCrmInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CiviCrmInstaller.php',
    'Composer\\Installers\\ClanCatsFrameworkInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ClanCatsFrameworkInstaller.php',
    'Composer\\Installers\\CockpitInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CockpitInstaller.php',
    'Composer\\Installers\\CodeIgniterInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CodeIgniterInstaller.php',
    'Composer\\Installers\\Concrete5Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Concrete5Installer.php',
    'Composer\\Installers\\ConcreteCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ConcreteCMSInstaller.php',
    'Composer\\Installers\\CroogoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CroogoInstaller.php',
    'Composer\\Installers\\DecibelInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DecibelInstaller.php',
    'Composer\\Installers\\DframeInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DframeInstaller.php',
    'Composer\\Installers\\DokuWikiInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DokuWikiInstaller.php',
    'Composer\\Installers\\DolibarrInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DolibarrInstaller.php',
    'Composer\\Installers\\DrupalInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DrupalInstaller.php',
    'Composer\\Installers\\ElggInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ElggInstaller.php',
    'Composer\\Installers\\EliasisInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/EliasisInstaller.php',
    'Composer\\Installers\\ExpressionEngineInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ExpressionEngineInstaller.php',
    'Composer\\Installers\\EzPlatformInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/EzPlatformInstaller.php',
    'Composer\\Installers\\ForkCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ForkCMSInstaller.php',
    'Composer\\Installers\\FuelInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/FuelInstaller.php',
    'Composer\\Installers\\FuelphpInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/FuelphpInstaller.php',
    'Composer\\Installers\\GravInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/GravInstaller.php',
    'Composer\\Installers\\HuradInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/HuradInstaller.php',
    'Composer\\Installers\\ImageCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ImageCMSInstaller.php',
    'Composer\\Installers\\Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Installer.php',
    'Composer\\Installers\\ItopInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ItopInstaller.php',
    'Composer\\Installers\\KanboardInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KanboardInstaller.php',
    'Composer\\Installers\\KnownInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KnownInstaller.php',
    'Composer\\Installers\\KodiCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KodiCMSInstaller.php',
    'Composer\\Installers\\KohanaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KohanaInstaller.php',
    'Composer\\Installers\\LanManagementSystemInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LanManagementSystemInstaller.php',
    'Composer\\Installers\\LaravelInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LaravelInstaller.php',
    'Composer\\Installers\\LavaLiteInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LavaLiteInstaller.php',
    'Composer\\Installers\\LithiumInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LithiumInstaller.php',
    'Composer\\Installers\\MODULEWorkInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MODULEWorkInstaller.php',
    'Composer\\Installers\\MODXEvoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MODXEvoInstaller.php',
    'Composer\\Installers\\MagentoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MagentoInstaller.php',
    'Composer\\Installers\\MajimaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MajimaInstaller.php',
    'Composer\\Installers\\MakoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MakoInstaller.php',
    'Composer\\Installers\\MantisBTInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MantisBTInstaller.php',
    'Composer\\Installers\\MatomoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MatomoInstaller.php',
    'Composer\\Installers\\MauticInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MauticInstaller.php',
    'Composer\\Installers\\MayaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MayaInstaller.php',
    'Composer\\Installers\\MediaWikiInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MediaWikiInstaller.php',
    'Composer\\Installers\\MiaoxingInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MiaoxingInstaller.php',
    'Composer\\Installers\\MicroweberInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MicroweberInstaller.php',
    'Composer\\Installers\\ModxInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ModxInstaller.php',
    'Composer\\Installers\\MoodleInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MoodleInstaller.php',
    'Composer\\Installers\\OctoberInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OctoberInstaller.php',
    'Composer\\Installers\\OntoWikiInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OntoWikiInstaller.php',
    'Composer\\Installers\\OsclassInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OsclassInstaller.php',
    'Composer\\Installers\\OxidInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OxidInstaller.php',
    'Composer\\Installers\\PPIInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PPIInstaller.php',
    'Composer\\Installers\\PantheonInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PantheonInstaller.php',
    'Composer\\Installers\\PhiftyInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PhiftyInstaller.php',
    'Composer\\Installers\\PhpBBInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PhpBBInstaller.php',
    'Composer\\Installers\\PiwikInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PiwikInstaller.php',
    'Composer\\Installers\\PlentymarketsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PlentymarketsInstaller.php',
    'Composer\\Installers\\Plugin' => $vendorDir . '/composer/installers/src/Composer/Installers/Plugin.php',
    'Composer\\Installers\\PortoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PortoInstaller.php',
    'Composer\\Installers\\PrestashopInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PrestashopInstaller.php',
    'Composer\\Installers\\ProcessWireInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ProcessWireInstaller.php',
    'Composer\\Installers\\PuppetInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PuppetInstaller.php',
    'Composer\\Installers\\PxcmsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PxcmsInstaller.php',
    'Composer\\Installers\\RadPHPInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/RadPHPInstaller.php',
    'Composer\\Installers\\ReIndexInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ReIndexInstaller.php',
    'Composer\\Installers\\Redaxo5Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Redaxo5Installer.php',
    'Composer\\Installers\\RedaxoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/RedaxoInstaller.php',
    'Composer\\Installers\\RoundcubeInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/RoundcubeInstaller.php',
    'Composer\\Installers\\SMFInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SMFInstaller.php',
    'Composer\\Installers\\ShopwareInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ShopwareInstaller.php',
    'Composer\\Installers\\SilverStripeInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SilverStripeInstaller.php',
    'Composer\\Installers\\SiteDirectInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SiteDirectInstaller.php',
    'Composer\\Installers\\StarbugInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/StarbugInstaller.php',
    'Composer\\Installers\\SyDESInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SyDESInstaller.php',
    'Composer\\Installers\\SyliusInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SyliusInstaller.php',
    'Composer\\Installers\\TaoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TaoInstaller.php',
    'Composer\\Installers\\TastyIgniterInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TastyIgniterInstaller.php',
    'Composer\\Installers\\TheliaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TheliaInstaller.php',
    'Composer\\Installers\\TuskInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TuskInstaller.php',
    'Composer\\Installers\\UserFrostingInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/UserFrostingInstaller.php',
    'Composer\\Installers\\VanillaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/VanillaInstaller.php',
    'Composer\\Installers\\VgmcpInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/VgmcpInstaller.php',
    'Composer\\Installers\\WHMCSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WHMCSInstaller.php',
    'Composer\\Installers\\WinterInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WinterInstaller.php',
    'Composer\\Installers\\WolfCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WolfCMSInstaller.php',
    'Composer\\Installers\\WordPressInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WordPressInstaller.php',
    'Composer\\Installers\\YawikInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/YawikInstaller.php',
    'Composer\\Installers\\ZendInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ZendInstaller.php',
    'Composer\\Installers\\ZikulaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ZikulaInstaller.php',
    'WPMailSMTP\\AbstractConnection' => $baseDir . '/src/AbstractConnection.php',
    'WPMailSMTP\\Admin\\AdminBarMenu' => $baseDir . '/src/Admin/AdminBarMenu.php',
    'WPMailSMTP\\Admin\\Area' => $baseDir . '/src/Admin/Area.php',
    'WPMailSMTP\\Admin\\ConnectionSettings' => $baseDir . '/src/Admin/ConnectionSettings.php',
    'WPMailSMTP\\Admin\\DashboardWidget' => $baseDir . '/src/Admin/DashboardWidget.php',
    'WPMailSMTP\\Admin\\DebugEvents\\DebugEvents' => $baseDir . '/src/Admin/DebugEvents/DebugEvents.php',
    'WPMailSMTP\\Admin\\DebugEvents\\Event' => $baseDir . '/src/Admin/DebugEvents/Event.php',
    'WPMailSMTP\\Admin\\DebugEvents\\EventsCollection' => $baseDir . '/src/Admin/DebugEvents/EventsCollection.php',
    'WPMailSMTP\\Admin\\DebugEvents\\Migration' => $baseDir . '/src/Admin/DebugEvents/Migration.php',
    'WPMailSMTP\\Admin\\DebugEvents\\Table' => $baseDir . '/src/Admin/DebugEvents/Table.php',
    'WPMailSMTP\\Admin\\DomainChecker' => $baseDir . '/src/Admin/DomainChecker.php',
    'WPMailSMTP\\Admin\\Education' => $baseDir . '/src/Admin/Education.php',
    'WPMailSMTP\\Admin\\FlyoutMenu' => $baseDir . '/src/Admin/FlyoutMenu.php',
    'WPMailSMTP\\Admin\\Notifications' => $baseDir . '/src/Admin/Notifications.php',
    'WPMailSMTP\\Admin\\PageAbstract' => $baseDir . '/src/Admin/PageAbstract.php',
    'WPMailSMTP\\Admin\\PageInterface' => $baseDir . '/src/Admin/PageInterface.php',
    'WPMailSMTP\\Admin\\Pages\\About' => $baseDir . '/src/Admin/Pages/About.php',
    'WPMailSMTP\\Admin\\Pages\\AboutTab' => $baseDir . '/src/Admin/Pages/AboutTab.php',
    'WPMailSMTP\\Admin\\Pages\\ActionSchedulerTab' => $baseDir . '/src/Admin/Pages/ActionSchedulerTab.php',
    'WPMailSMTP\\Admin\\Pages\\AdditionalConnectionsTab' => $baseDir . '/src/Admin/Pages/AdditionalConnectionsTab.php',
    'WPMailSMTP\\Admin\\Pages\\AlertsTab' => $baseDir . '/src/Admin/Pages/AlertsTab.php',
    'WPMailSMTP\\Admin\\Pages\\AuthTab' => $baseDir . '/src/Admin/Pages/AuthTab.php',
    'WPMailSMTP\\Admin\\Pages\\ControlTab' => $baseDir . '/src/Admin/Pages/ControlTab.php',
    'WPMailSMTP\\Admin\\Pages\\DebugEventsTab' => $baseDir . '/src/Admin/Pages/DebugEventsTab.php',
    'WPMailSMTP\\Admin\\Pages\\EmailReports' => $baseDir . '/src/Admin/Pages/EmailReports.php',
    'WPMailSMTP\\Admin\\Pages\\EmailReportsTab' => $baseDir . '/src/Admin/Pages/EmailReportsTab.php',
    'WPMailSMTP\\Admin\\Pages\\ExportTab' => $baseDir . '/src/Admin/Pages/ExportTab.php',
    'WPMailSMTP\\Admin\\Pages\\Logs' => $baseDir . '/src/Admin/Pages/Logs.php',
    'WPMailSMTP\\Admin\\Pages\\LogsTab' => $baseDir . '/src/Admin/Pages/LogsTab.php',
    'WPMailSMTP\\Admin\\Pages\\MiscTab' => $baseDir . '/src/Admin/Pages/MiscTab.php',
    'WPMailSMTP\\Admin\\Pages\\SettingsTab' => $baseDir . '/src/Admin/Pages/SettingsTab.php',
    'WPMailSMTP\\Admin\\Pages\\SmartRoutingTab' => $baseDir . '/src/Admin/Pages/SmartRoutingTab.php',
    'WPMailSMTP\\Admin\\Pages\\TestTab' => $baseDir . '/src/Admin/Pages/TestTab.php',
    'WPMailSMTP\\Admin\\Pages\\Tools' => $baseDir . '/src/Admin/Pages/Tools.php',
    'WPMailSMTP\\Admin\\Pages\\VersusTab' => $baseDir . '/src/Admin/Pages/VersusTab.php',
    'WPMailSMTP\\Admin\\ParentPageAbstract' => $baseDir . '/src/Admin/ParentPageAbstract.php',
    'WPMailSMTP\\Admin\\PluginsInstallSkin' => $baseDir . '/src/Admin/PluginsInstallSkin.php',
    'WPMailSMTP\\Admin\\Review' => $baseDir . '/src/Admin/Review.php',
    'WPMailSMTP\\Admin\\SetupWizard' => $baseDir . '/src/Admin/SetupWizard.php',
    'WPMailSMTP\\Compatibility\\Compatibility' => $baseDir . '/src/Compatibility/Compatibility.php',
    'WPMailSMTP\\Compatibility\\Plugin\\Admin2020' => $baseDir . '/src/Compatibility/Plugin/Admin2020.php',
    'WPMailSMTP\\Compatibility\\Plugin\\PluginAbstract' => $baseDir . '/src/Compatibility/Plugin/PluginAbstract.php',
    'WPMailSMTP\\Compatibility\\Plugin\\PluginInterface' => $baseDir . '/src/Compatibility/Plugin/PluginInterface.php',
    'WPMailSMTP\\Compatibility\\Plugin\\WPForms' => $baseDir . '/src/Compatibility/Plugin/WPForms.php',
    'WPMailSMTP\\Compatibility\\Plugin\\WPFormsLite' => $baseDir . '/src/Compatibility/Plugin/WPFormsLite.php',
    'WPMailSMTP\\Compatibility\\Plugin\\WooCommerce' => $baseDir . '/src/Compatibility/Plugin/WooCommerce.php',
    'WPMailSMTP\\Conflicts' => $baseDir . '/src/Conflicts.php',
    'WPMailSMTP\\Connect' => $baseDir . '/src/Connect.php',
    'WPMailSMTP\\Connection' => $baseDir . '/src/Connection.php',
    'WPMailSMTP\\ConnectionInterface' => $baseDir . '/src/ConnectionInterface.php',
    'WPMailSMTP\\ConnectionsManager' => $baseDir . '/src/ConnectionsManager.php',
    'WPMailSMTP\\Core' => $baseDir . '/src/Core.php',
    'WPMailSMTP\\DBRepair' => $baseDir . '/src/DBRepair.php',
    'WPMailSMTP\\Debug' => $baseDir . '/src/Debug.php',
    'WPMailSMTP\\Geo' => $baseDir . '/src/Geo.php',
    'WPMailSMTP\\Helpers\\Crypto' => $baseDir . '/src/Helpers/Crypto.php',
    'WPMailSMTP\\Helpers\\DB' => $baseDir . '/src/Helpers/DB.php',
    'WPMailSMTP\\Helpers\\Helpers' => $baseDir . '/src/Helpers/Helpers.php',
    'WPMailSMTP\\Helpers\\PluginImportDataRetriever' => $baseDir . '/src/Helpers/PluginImportDataRetriever.php',
    'WPMailSMTP\\Helpers\\UI' => $baseDir . '/src/Helpers/UI.php',
    'WPMailSMTP\\MailCatcher' => $baseDir . '/src/MailCatcher.php',
    'WPMailSMTP\\MailCatcherInterface' => $baseDir . '/src/MailCatcherInterface.php',
    'WPMailSMTP\\MailCatcherTrait' => $baseDir . '/src/MailCatcherTrait.php',
    'WPMailSMTP\\MailCatcherV6' => $baseDir . '/src/MailCatcherV6.php',
    'WPMailSMTP\\Migration' => $baseDir . '/src/Migration.php',
    'WPMailSMTP\\MigrationAbstract' => $baseDir . '/src/MigrationAbstract.php',
    'WPMailSMTP\\Migrations' => $baseDir . '/src/Migrations.php',
    'WPMailSMTP\\OptimizedEmailSending' => $baseDir . '/src/OptimizedEmailSending.php',
    'WPMailSMTP\\Options' => $baseDir . '/src/Options.php',
    'WPMailSMTP\\Pro\\AdditionalConnections\\AdditionalConnections' => $baseDir . '/src/Pro/AdditionalConnections/AdditionalConnections.php',
    'WPMailSMTP\\Pro\\AdditionalConnections\\Admin\\SettingsTab' => $baseDir . '/src/Pro/AdditionalConnections/Admin/SettingsTab.php',
    'WPMailSMTP\\Pro\\AdditionalConnections\\Admin\\TestTab' => $baseDir . '/src/Pro/AdditionalConnections/Admin/TestTab.php',
    'WPMailSMTP\\Pro\\AdditionalConnections\\Connection' => $baseDir . '/src/Pro/AdditionalConnections/Connection.php',
    'WPMailSMTP\\Pro\\AdditionalConnections\\ConnectionOptions' => $baseDir . '/src/Pro/AdditionalConnections/ConnectionOptions.php',
    'WPMailSMTP\\Pro\\Admin\\Area' => $baseDir . '/src/Pro/Admin/Area.php',
    'WPMailSMTP\\Pro\\Admin\\DashboardWidget' => $baseDir . '/src/Pro/Admin/DashboardWidget.php',
    'WPMailSMTP\\Pro\\Admin\\Pages\\MiscTab' => $baseDir . '/src/Pro/Admin/Pages/MiscTab.php',
    'WPMailSMTP\\Pro\\Admin\\PluginsList' => $baseDir . '/src/Pro/Admin/PluginsList.php',
    'WPMailSMTP\\Pro\\Alerts\\AbstractOptions' => $baseDir . '/src/Pro/Alerts/AbstractOptions.php',
    'WPMailSMTP\\Pro\\Alerts\\Admin\\SettingsTab' => $baseDir . '/src/Pro/Alerts/Admin/SettingsTab.php',
    'WPMailSMTP\\Pro\\Alerts\\Alert' => $baseDir . '/src/Pro/Alerts/Alert.php',
    'WPMailSMTP\\Pro\\Alerts\\Alerts' => $baseDir . '/src/Pro/Alerts/Alerts.php',
    'WPMailSMTP\\Pro\\Alerts\\Handlers\\HandlerInterface' => $baseDir . '/src/Pro/Alerts/Handlers/HandlerInterface.php',
    'WPMailSMTP\\Pro\\Alerts\\Loader' => $baseDir . '/src/Pro/Alerts/Loader.php',
    'WPMailSMTP\\Pro\\Alerts\\Notifier' => $baseDir . '/src/Pro/Alerts/Notifier.php',
    'WPMailSMTP\\Pro\\Alerts\\OptionsInterface' => $baseDir . '/src/Pro/Alerts/OptionsInterface.php',
    'WPMailSMTP\\Pro\\Alerts\\Providers\\CustomWebhook\\Handler' => $baseDir . '/src/Pro/Alerts/Providers/CustomWebhook/Handler.php',
    'WPMailSMTP\\Pro\\Alerts\\Providers\\CustomWebhook\\Options' => $baseDir . '/src/Pro/Alerts/Providers/CustomWebhook/Options.php',
    'WPMailSMTP\\Pro\\Alerts\\Providers\\DiscordWebhook\\Handler' => $baseDir . '/src/Pro/Alerts/Providers/DiscordWebhook/Handler.php',
    'WPMailSMTP\\Pro\\Alerts\\Providers\\DiscordWebhook\\Options' => $baseDir . '/src/Pro/Alerts/Providers/DiscordWebhook/Options.php',
    'WPMailSMTP\\Pro\\Alerts\\Providers\\Email\\Handler' => $baseDir . '/src/Pro/Alerts/Providers/Email/Handler.php',
    'WPMailSMTP\\Pro\\Alerts\\Providers\\Email\\Options' => $baseDir . '/src/Pro/Alerts/Providers/Email/Options.php',
    'WPMailSMTP\\Pro\\Alerts\\Providers\\Push\\Handler' => $baseDir . '/src/Pro/Alerts/Providers/Push/Handler.php',
    'WPMailSMTP\\Pro\\Alerts\\Providers\\Push\\Options' => $baseDir . '/src/Pro/Alerts/Providers/Push/Options.php',
    'WPMailSMTP\\Pro\\Alerts\\Providers\\Push\\Provider' => $baseDir . '/src/Pro/Alerts/Providers/Push/Provider.php',
    'WPMailSMTP\\Pro\\Alerts\\Providers\\SlackWebhook\\Handler' => $baseDir . '/src/Pro/Alerts/Providers/SlackWebhook/Handler.php',
    'WPMailSMTP\\Pro\\Alerts\\Providers\\SlackWebhook\\Options' => $baseDir . '/src/Pro/Alerts/Providers/SlackWebhook/Options.php',
    'WPMailSMTP\\Pro\\Alerts\\Providers\\TeamsWebhook\\Handler' => $baseDir . '/src/Pro/Alerts/Providers/TeamsWebhook/Handler.php',
    'WPMailSMTP\\Pro\\Alerts\\Providers\\TeamsWebhook\\Options' => $baseDir . '/src/Pro/Alerts/Providers/TeamsWebhook/Options.php',
    'WPMailSMTP\\Pro\\Alerts\\Providers\\TwilioSMS\\Handler' => $baseDir . '/src/Pro/Alerts/Providers/TwilioSMS/Handler.php',
    'WPMailSMTP\\Pro\\Alerts\\Providers\\TwilioSMS\\Options' => $baseDir . '/src/Pro/Alerts/Providers/TwilioSMS/Options.php',
    'WPMailSMTP\\Pro\\BackupConnections\\Admin\\SettingsTab' => $baseDir . '/src/Pro/BackupConnections/Admin/SettingsTab.php',
    'WPMailSMTP\\Pro\\BackupConnections\\BackupConnections' => $baseDir . '/src/Pro/BackupConnections/BackupConnections.php',
    'WPMailSMTP\\Pro\\ConditionalLogic\\CanProcessConditionalLogicTrait' => $baseDir . '/src/Pro/ConditionalLogic/CanProcessConditionalLogicTrait.php',
    'WPMailSMTP\\Pro\\ConditionalLogic\\ConditionalLogicSettings' => $baseDir . '/src/Pro/ConditionalLogic/ConditionalLogicSettings.php',
    'WPMailSMTP\\Pro\\ConnectionsManager' => $baseDir . '/src/Pro/ConnectionsManager.php',
    'WPMailSMTP\\Pro\\DBRepair' => $baseDir . '/src/Pro/DBRepair.php',
    'WPMailSMTP\\Pro\\Emails\\Control\\Admin\\SettingsTab' => $baseDir . '/src/Pro/Emails/Control/Admin/SettingsTab.php',
    'WPMailSMTP\\Pro\\Emails\\Control\\Control' => $baseDir . '/src/Pro/Emails/Control/Control.php',
    'WPMailSMTP\\Pro\\Emails\\Control\\Reload' => $baseDir . '/src/Pro/Emails/Control/Reload.php',
    'WPMailSMTP\\Pro\\Emails\\Control\\Switcher' => $baseDir . '/src/Pro/Emails/Control/Switcher.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Admin\\ArchivePage' => $baseDir . '/src/Pro/Emails/Logs/Admin/ArchivePage.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Admin\\PageAbstract' => $baseDir . '/src/Pro/Emails/Logs/Admin/PageAbstract.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Admin\\PrintPreview' => $baseDir . '/src/Pro/Emails/Logs/Admin/PrintPreview.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Admin\\SettingsTab' => $baseDir . '/src/Pro/Emails/Logs/Admin/SettingsTab.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Admin\\SinglePage' => $baseDir . '/src/Pro/Emails/Logs/Admin/SinglePage.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Admin\\Table' => $baseDir . '/src/Pro/Emails/Logs/Admin/Table.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Attachments\\Attachment' => $baseDir . '/src/Pro/Emails/Logs/Attachments/Attachment.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Attachments\\Attachments' => $baseDir . '/src/Pro/Emails/Logs/Attachments/Attachments.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Attachments\\Cleanup' => $baseDir . '/src/Pro/Emails/Logs/Attachments/Cleanup.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Attachments\\Migration' => $baseDir . '/src/Pro/Emails/Logs/Attachments/Migration.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\CanResendEmailTrait' => $baseDir . '/src/Pro/Emails/Logs/CanResendEmailTrait.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\AbstractDeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/AbstractDeliveryVerifier.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\DeliveryStatus' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/DeliveryStatus.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\DeliveryVerification' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/DeliveryVerification.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\ElasticEmail\\DeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/ElasticEmail/DeliveryVerifier.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\Mailgun\\DeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/Mailgun/DeliveryVerifier.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\Mailjet\\DeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/Mailjet/DeliveryVerifier.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\Postmark\\DeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/Postmark/DeliveryVerifier.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\SMTP2GO\\DeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/SMTP2GO/DeliveryVerifier.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\SMTPcom\\DeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/SMTPcom/DeliveryVerifier.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\Sendinblue\\DeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/Sendinblue/DeliveryVerifier.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\Sendlayer\\DeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/Sendlayer/DeliveryVerifier.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\SparkPost\\DeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/SparkPost/DeliveryVerifier.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Email' => $baseDir . '/src/Pro/Emails/Logs/Email.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\EmailsCollection' => $baseDir . '/src/Pro/Emails/Logs/EmailsCollection.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Export\\AbstractData' => $baseDir . '/src/Pro/Emails/Logs/Export/AbstractData.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Export\\Admin' => $baseDir . '/src/Pro/Emails/Logs/Export/Admin.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Export\\CanRemoveExportFileTrait' => $baseDir . '/src/Pro/Emails/Logs/Export/CanRemoveExportFileTrait.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Export\\EMLData' => $baseDir . '/src/Pro/Emails/Logs/Export/EMLData.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Export\\Export' => $baseDir . '/src/Pro/Emails/Logs/Export/Export.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Export\\File' => $baseDir . '/src/Pro/Emails/Logs/Export/File.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Export\\Handler' => $baseDir . '/src/Pro/Emails/Logs/Export/Handler.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Export\\Request' => $baseDir . '/src/Pro/Emails/Logs/Export/Request.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Export\\TableData' => $baseDir . '/src/Pro/Emails/Logs/Export/TableData.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Importers\\ImporterAbstract' => $baseDir . '/src/Pro/Emails/Logs/Importers/ImporterAbstract.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Importers\\ImporterInterface' => $baseDir . '/src/Pro/Emails/Logs/Importers/ImporterInterface.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Importers\\ImporterTabAbstract' => $baseDir . '/src/Pro/Emails/Logs/Importers/ImporterTabAbstract.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Importers\\ImporterTabAbstractInterface' => $baseDir . '/src/Pro/Emails/Logs/Importers/ImporterTabAbstractInterface.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Importers\\Importers' => $baseDir . '/src/Pro/Emails/Logs/Importers/Importers.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Importers\\WPMailLogging\\Importer' => $baseDir . '/src/Pro/Emails/Logs/Importers/WPMailLogging/Importer.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Importers\\WPMailLogging\\Tab' => $baseDir . '/src/Pro/Emails/Logs/Importers/WPMailLogging/Tab.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Logs' => $baseDir . '/src/Pro/Emails/Logs/Logs.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Migration' => $baseDir . '/src/Pro/Emails/Logs/Migration.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Providers\\Common' => $baseDir . '/src/Pro/Emails/Logs/Providers/Common.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Providers\\SMTP' => $baseDir . '/src/Pro/Emails/Logs/Providers/SMTP.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\RecheckDeliveryStatus' => $baseDir . '/src/Pro/Emails/Logs/RecheckDeliveryStatus.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Reports\\Admin' => $baseDir . '/src/Pro/Emails/Logs/Reports/Admin.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Reports\\Emails\\Summary' => $baseDir . '/src/Pro/Emails/Logs/Reports/Emails/Summary.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Reports\\Report' => $baseDir . '/src/Pro/Emails/Logs/Reports/Report.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Reports\\Reports' => $baseDir . '/src/Pro/Emails/Logs/Reports/Reports.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Reports\\Table' => $baseDir . '/src/Pro/Emails/Logs/Reports/Table.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Resend' => $baseDir . '/src/Pro/Emails/Logs/Resend.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Tracking\\Cleanup' => $baseDir . '/src/Pro/Emails/Logs/Tracking/Cleanup.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\AbstractEvent' => $baseDir . '/src/Pro/Emails/Logs/Tracking/Events/AbstractEvent.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\EventFactory' => $baseDir . '/src/Pro/Emails/Logs/Tracking/Events/EventFactory.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\EventInterface' => $baseDir . '/src/Pro/Emails/Logs/Tracking/Events/EventInterface.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\Events' => $baseDir . '/src/Pro/Emails/Logs/Tracking/Events/Events.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\Injectable\\AbstractInjectableEvent' => $baseDir . '/src/Pro/Emails/Logs/Tracking/Events/Injectable/AbstractInjectableEvent.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\Injectable\\ClickLinkEvent' => $baseDir . '/src/Pro/Emails/Logs/Tracking/Events/Injectable/ClickLinkEvent.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\Injectable\\OpenEmailEvent' => $baseDir . '/src/Pro/Emails/Logs/Tracking/Events/Injectable/OpenEmailEvent.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Tracking\\Migration' => $baseDir . '/src/Pro/Emails/Logs/Tracking/Migration.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Tracking\\Tracking' => $baseDir . '/src/Pro/Emails/Logs/Tracking/Tracking.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\AbstractProcessor' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/AbstractProcessor.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\AbstractProvider' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/AbstractProvider.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\AbstractSubscriber' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/AbstractSubscriber.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Events\\Delivered' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Events/Delivered.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Events\\EventInterface' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Events/EventInterface.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Events\\Failed' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Events/Failed.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\ProcessorInterface' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/ProcessorInterface.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\ProviderInterface' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/ProviderInterface.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailgun\\Events\\Failed' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailgun/Events/Failed.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailgun\\Processor' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailgun/Processor.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailgun\\Provider' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailgun/Provider.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailgun\\Subscriber' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailgun/Subscriber.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailjet\\Events\\Failed' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailjet/Events/Failed.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailjet\\Processor' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailjet/Processor.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailjet\\Provider' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailjet/Provider.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailjet\\Subscriber' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailjet/Subscriber.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Postmark\\Events\\Failed' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Postmark/Events/Failed.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Postmark\\Processor' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Postmark/Processor.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Postmark\\Provider' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Postmark/Provider.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Postmark\\Subscriber' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Postmark/Subscriber.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTP2GO\\Events\\Failed' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTP2GO/Events/Failed.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTP2GO\\Processor' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTP2GO/Processor.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTP2GO\\Provider' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTP2GO/Provider.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTP2GO\\Subscriber' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTP2GO/Subscriber.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTPcom\\Events\\Failed' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTPcom/Events/Failed.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTPcom\\Processor' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTPcom/Processor.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTPcom\\Provider' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTPcom/Provider.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTPcom\\Subscriber' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTPcom/Subscriber.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendinblue\\Events\\Failed' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendinblue/Events/Failed.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendinblue\\Processor' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendinblue/Processor.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendinblue\\Provider' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendinblue/Provider.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendinblue\\Subscriber' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendinblue/Subscriber.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendlayer\\Events\\Failed' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendlayer/Events/Failed.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendlayer\\Processor' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendlayer/Processor.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendlayer\\Provider' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendlayer/Provider.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendlayer\\Subscriber' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendlayer/Subscriber.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SparkPost\\Events\\Failed' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SparkPost/Events/Failed.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SparkPost\\Processor' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SparkPost/Processor.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SparkPost\\Provider' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SparkPost/Provider.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SparkPost\\Subscriber' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SparkPost/Subscriber.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\SubscriberInterface' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/SubscriberInterface.php',
    'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Webhooks' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Webhooks.php',
    'WPMailSMTP\\Pro\\Emails\\RateLimiting\\RateLimiting' => $baseDir . '/src/Pro/Emails/RateLimiting/RateLimiting.php',
    'WPMailSMTP\\Pro\\Emails\\TestEmail' => $baseDir . '/src/Pro/Emails/TestEmail.php',
    'WPMailSMTP\\Pro\\License\\License' => $baseDir . '/src/Pro/License/License.php',
    'WPMailSMTP\\Pro\\License\\Updater' => $baseDir . '/src/Pro/License/Updater.php',
    'WPMailSMTP\\Pro\\MailCatcher' => $baseDir . '/src/Pro/MailCatcher.php',
    'WPMailSMTP\\Pro\\MailCatcherTrait' => $baseDir . '/src/Pro/MailCatcherTrait.php',
    'WPMailSMTP\\Pro\\MailCatcherV6' => $baseDir . '/src/Pro/MailCatcherV6.php',
    'WPMailSMTP\\Pro\\Migration' => $baseDir . '/src/Pro/Migration.php',
    'WPMailSMTP\\Pro\\Multisite' => $baseDir . '/src/Pro/Multisite.php',
    'WPMailSMTP\\Pro\\Pro' => $baseDir . '/src/Pro/Pro.php',
    'WPMailSMTP\\Pro\\ProductApi\\Client' => $baseDir . '/src/Pro/ProductApi/Client.php',
    'WPMailSMTP\\Pro\\ProductApi\\Credentials' => $baseDir . '/src/Pro/ProductApi/Credentials.php',
    'WPMailSMTP\\Pro\\ProductApi\\CredentialsGenerationNonce' => $baseDir . '/src/Pro/ProductApi/CredentialsGenerationNonce.php',
    'WPMailSMTP\\Pro\\ProductApi\\CredentialsGenerator' => $baseDir . '/src/Pro/ProductApi/CredentialsGenerator.php',
    'WPMailSMTP\\Pro\\ProductApi\\CredentialsRepository' => $baseDir . '/src/Pro/ProductApi/CredentialsRepository.php',
    'WPMailSMTP\\Pro\\ProductApi\\ProductApi' => $baseDir . '/src/Pro/ProductApi/ProductApi.php',
    'WPMailSMTP\\Pro\\ProductApi\\Response' => $baseDir . '/src/Pro/ProductApi/Response.php',
    'WPMailSMTP\\Pro\\Providers\\AmazonSES\\Auth' => $baseDir . '/src/Pro/Providers/AmazonSES/Auth.php',
    'WPMailSMTP\\Pro\\Providers\\AmazonSES\\IdentitiesTable' => $baseDir . '/src/Pro/Providers/AmazonSES/IdentitiesTable.php',
    'WPMailSMTP\\Pro\\Providers\\AmazonSES\\Identity' => $baseDir . '/src/Pro/Providers/AmazonSES/Identity.php',
    'WPMailSMTP\\Pro\\Providers\\AmazonSES\\Mailer' => $baseDir . '/src/Pro/Providers/AmazonSES/Mailer.php',
    'WPMailSMTP\\Pro\\Providers\\AmazonSES\\Options' => $baseDir . '/src/Pro/Providers/AmazonSES/Options.php',
    'WPMailSMTP\\Pro\\Providers\\Gmail\\Api\\Client' => $baseDir . '/src/Pro/Providers/Gmail/Api/Client.php',
    'WPMailSMTP\\Pro\\Providers\\Gmail\\Api\\OneTimeToken' => $baseDir . '/src/Pro/Providers/Gmail/Api/OneTimeToken.php',
    'WPMailSMTP\\Pro\\Providers\\Gmail\\Api\\Response' => $baseDir . '/src/Pro/Providers/Gmail/Api/Response.php',
    'WPMailSMTP\\Pro\\Providers\\Gmail\\Api\\SiteId' => $baseDir . '/src/Pro/Providers/Gmail/Api/SiteId.php',
    'WPMailSMTP\\Pro\\Providers\\Gmail\\Auth' => $baseDir . '/src/Pro/Providers/Gmail/Auth.php',
    'WPMailSMTP\\Pro\\Providers\\Gmail\\Mailer' => $baseDir . '/src/Pro/Providers/Gmail/Mailer.php',
    'WPMailSMTP\\Pro\\Providers\\Gmail\\Options' => $baseDir . '/src/Pro/Providers/Gmail/Options.php',
    'WPMailSMTP\\Pro\\Providers\\Gmail\\Provider' => $baseDir . '/src/Pro/Providers/Gmail/Provider.php',
    'WPMailSMTP\\Pro\\Providers\\Outlook\\AttachmentsUploader' => $baseDir . '/src/Pro/Providers/Outlook/AttachmentsUploader.php',
    'WPMailSMTP\\Pro\\Providers\\Outlook\\Auth' => $baseDir . '/src/Pro/Providers/Outlook/Auth.php',
    'WPMailSMTP\\Pro\\Providers\\Outlook\\Mailer' => $baseDir . '/src/Pro/Providers/Outlook/Mailer.php',
    'WPMailSMTP\\Pro\\Providers\\Outlook\\OneClick\\Auth' => $baseDir . '/src/Pro/Providers/Outlook/OneClick/Auth.php',
    'WPMailSMTP\\Pro\\Providers\\Outlook\\OneClick\\Auth\\Client' => $baseDir . '/src/Pro/Providers/Outlook/OneClick/Auth/Client.php',
    'WPMailSMTP\\Pro\\Providers\\Outlook\\OneClick\\Auth\\Response' => $baseDir . '/src/Pro/Providers/Outlook/OneClick/Auth/Response.php',
    'WPMailSMTP\\Pro\\Providers\\Outlook\\OneClick\\Options' => $baseDir . '/src/Pro/Providers/Outlook/OneClick/Options.php',
    'WPMailSMTP\\Pro\\Providers\\Outlook\\Options' => $baseDir . '/src/Pro/Providers/Outlook/Options.php',
    'WPMailSMTP\\Pro\\Providers\\Outlook\\Provider' => $baseDir . '/src/Pro/Providers/Outlook/Provider.php',
    'WPMailSMTP\\Pro\\Providers\\Providers' => $baseDir . '/src/Pro/Providers/Providers.php',
    'WPMailSMTP\\Pro\\Providers\\Zoho\\Auth' => $baseDir . '/src/Pro/Providers/Zoho/Auth.php',
    'WPMailSMTP\\Pro\\Providers\\Zoho\\Auth\\Zoho' => $baseDir . '/src/Pro/Providers/Zoho/Auth/Zoho.php',
    'WPMailSMTP\\Pro\\Providers\\Zoho\\Auth\\ZohoUser' => $baseDir . '/src/Pro/Providers/Zoho/Auth/ZohoUser.php',
    'WPMailSMTP\\Pro\\Providers\\Zoho\\Mailer' => $baseDir . '/src/Pro/Providers/Zoho/Mailer.php',
    'WPMailSMTP\\Pro\\Providers\\Zoho\\Options' => $baseDir . '/src/Pro/Providers/Zoho/Options.php',
    'WPMailSMTP\\Pro\\SiteHealth' => $baseDir . '/src/Pro/SiteHealth.php',
    'WPMailSMTP\\Pro\\SmartRouting\\Admin\\SettingsTab' => $baseDir . '/src/Pro/SmartRouting/Admin/SettingsTab.php',
    'WPMailSMTP\\Pro\\SmartRouting\\ConditionalLogic' => $baseDir . '/src/Pro/SmartRouting/ConditionalLogic.php',
    'WPMailSMTP\\Pro\\SmartRouting\\SmartRouting' => $baseDir . '/src/Pro/SmartRouting/SmartRouting.php',
    'WPMailSMTP\\Pro\\Tasks\\EmailLogCleanupTask' => $baseDir . '/src/Pro/Tasks/EmailLogCleanupTask.php',
    'WPMailSMTP\\Pro\\Tasks\\LicenseCheckTask' => $baseDir . '/src/Pro/Tasks/LicenseCheckTask.php',
    'WPMailSMTP\\Pro\\Tasks\\Logs\\BulkVerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/BulkVerifySentStatusTask.php',
    'WPMailSMTP\\Pro\\Tasks\\Logs\\ElasticEmail\\VerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/ElasticEmail/VerifySentStatusTask.php',
    'WPMailSMTP\\Pro\\Tasks\\Logs\\ExportCleanupTask' => $baseDir . '/src/Pro/Tasks/Logs/ExportCleanupTask.php',
    'WPMailSMTP\\Pro\\Tasks\\Logs\\Mailgun\\VerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/Mailgun/VerifySentStatusTask.php',
    'WPMailSMTP\\Pro\\Tasks\\Logs\\Mailjet\\VerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/Mailjet/VerifySentStatusTask.php',
    'WPMailSMTP\\Pro\\Tasks\\Logs\\Postmark\\VerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/Postmark/VerifySentStatusTask.php',
    'WPMailSMTP\\Pro\\Tasks\\Logs\\ResendTask' => $baseDir . '/src/Pro/Tasks/Logs/ResendTask.php',
    'WPMailSMTP\\Pro\\Tasks\\Logs\\SMTP2GO\\VerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/SMTP2GO/VerifySentStatusTask.php',
    'WPMailSMTP\\Pro\\Tasks\\Logs\\SMTPcom\\VerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/SMTPcom/VerifySentStatusTask.php',
    'WPMailSMTP\\Pro\\Tasks\\Logs\\Sendinblue\\VerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/Sendinblue/VerifySentStatusTask.php',
    'WPMailSMTP\\Pro\\Tasks\\Logs\\Sendlayer\\VerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/Sendlayer/VerifySentStatusTask.php',
    'WPMailSMTP\\Pro\\Tasks\\Logs\\SparkPost\\VerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/SparkPost/VerifySentStatusTask.php',
    'WPMailSMTP\\Pro\\Tasks\\Logs\\VerifySentStatusTaskAbstract' => $baseDir . '/src/Pro/Tasks/Logs/VerifySentStatusTaskAbstract.php',
    'WPMailSMTP\\Pro\\Tasks\\Migrations\\EmailLogMigration11' => $baseDir . '/src/Pro/Tasks/Migrations/EmailLogMigration11.php',
    'WPMailSMTP\\Pro\\Tasks\\Migrations\\EmailLogMigration4' => $baseDir . '/src/Pro/Tasks/Migrations/EmailLogMigration4.php',
    'WPMailSMTP\\Pro\\Tasks\\Migrations\\EmailLogMigration5' => $baseDir . '/src/Pro/Tasks/Migrations/EmailLogMigration5.php',
    'WPMailSMTP\\Pro\\Tasks\\NotifierTask' => $baseDir . '/src/Pro/Tasks/NotifierTask.php',
    'WPMailSMTP\\Pro\\Translations' => $baseDir . '/src/Pro/Translations.php',
    'WPMailSMTP\\Pro\\Upgrade' => $baseDir . '/src/Pro/Upgrade.php',
    'WPMailSMTP\\Processor' => $baseDir . '/src/Processor.php',
    'WPMailSMTP\\Providers\\AmazonSES\\Options' => $baseDir . '/src/Providers/AmazonSES/Options.php',
    'WPMailSMTP\\Providers\\AuthAbstract' => $baseDir . '/src/Providers/AuthAbstract.php',
    'WPMailSMTP\\Providers\\AuthInterface' => $baseDir . '/src/Providers/AuthInterface.php',
    'WPMailSMTP\\Providers\\ElasticEmail\\Mailer' => $baseDir . '/src/Providers/ElasticEmail/Mailer.php',
    'WPMailSMTP\\Providers\\ElasticEmail\\Options' => $baseDir . '/src/Providers/ElasticEmail/Options.php',
    'WPMailSMTP\\Providers\\Gmail\\Auth' => $baseDir . '/src/Providers/Gmail/Auth.php',
    'WPMailSMTP\\Providers\\Gmail\\Mailer' => $baseDir . '/src/Providers/Gmail/Mailer.php',
    'WPMailSMTP\\Providers\\Gmail\\Options' => $baseDir . '/src/Providers/Gmail/Options.php',
    'WPMailSMTP\\Providers\\Loader' => $baseDir . '/src/Providers/Loader.php',
    'WPMailSMTP\\Providers\\Mail\\Mailer' => $baseDir . '/src/Providers/Mail/Mailer.php',
    'WPMailSMTP\\Providers\\Mail\\Options' => $baseDir . '/src/Providers/Mail/Options.php',
    'WPMailSMTP\\Providers\\MailerAbstract' => $baseDir . '/src/Providers/MailerAbstract.php',
    'WPMailSMTP\\Providers\\MailerInterface' => $baseDir . '/src/Providers/MailerInterface.php',
    'WPMailSMTP\\Providers\\Mailgun\\Mailer' => $baseDir . '/src/Providers/Mailgun/Mailer.php',
    'WPMailSMTP\\Providers\\Mailgun\\Options' => $baseDir . '/src/Providers/Mailgun/Options.php',
    'WPMailSMTP\\Providers\\Mailjet\\Mailer' => $baseDir . '/src/Providers/Mailjet/Mailer.php',
    'WPMailSMTP\\Providers\\Mailjet\\Options' => $baseDir . '/src/Providers/Mailjet/Options.php',
    'WPMailSMTP\\Providers\\OptionsAbstract' => $baseDir . '/src/Providers/OptionsAbstract.php',
    'WPMailSMTP\\Providers\\OptionsInterface' => $baseDir . '/src/Providers/OptionsInterface.php',
    'WPMailSMTP\\Providers\\Outlook\\Options' => $baseDir . '/src/Providers/Outlook/Options.php',
    'WPMailSMTP\\Providers\\Outlook\\Provider' => $baseDir . '/src/Providers/Outlook/Provider.php',
    'WPMailSMTP\\Providers\\PepipostAPI\\Mailer' => $baseDir . '/src/Providers/PepipostAPI/Mailer.php',
    'WPMailSMTP\\Providers\\PepipostAPI\\Options' => $baseDir . '/src/Providers/PepipostAPI/Options.php',
    'WPMailSMTP\\Providers\\Pepipost\\Mailer' => $baseDir . '/src/Providers/Pepipost/Mailer.php',
    'WPMailSMTP\\Providers\\Pepipost\\Options' => $baseDir . '/src/Providers/Pepipost/Options.php',
    'WPMailSMTP\\Providers\\Postmark\\Mailer' => $baseDir . '/src/Providers/Postmark/Mailer.php',
    'WPMailSMTP\\Providers\\Postmark\\Options' => $baseDir . '/src/Providers/Postmark/Options.php',
    'WPMailSMTP\\Providers\\SMTP2GO\\Mailer' => $baseDir . '/src/Providers/SMTP2GO/Mailer.php',
    'WPMailSMTP\\Providers\\SMTP2GO\\Options' => $baseDir . '/src/Providers/SMTP2GO/Options.php',
    'WPMailSMTP\\Providers\\SMTP\\Mailer' => $baseDir . '/src/Providers/SMTP/Mailer.php',
    'WPMailSMTP\\Providers\\SMTP\\Options' => $baseDir . '/src/Providers/SMTP/Options.php',
    'WPMailSMTP\\Providers\\SMTPcom\\Mailer' => $baseDir . '/src/Providers/SMTPcom/Mailer.php',
    'WPMailSMTP\\Providers\\SMTPcom\\Options' => $baseDir . '/src/Providers/SMTPcom/Options.php',
    'WPMailSMTP\\Providers\\Sendgrid\\Mailer' => $baseDir . '/src/Providers/Sendgrid/Mailer.php',
    'WPMailSMTP\\Providers\\Sendgrid\\Options' => $baseDir . '/src/Providers/Sendgrid/Options.php',
    'WPMailSMTP\\Providers\\Sendinblue\\Api' => $baseDir . '/src/Providers/Sendinblue/Api.php',
    'WPMailSMTP\\Providers\\Sendinblue\\Mailer' => $baseDir . '/src/Providers/Sendinblue/Mailer.php',
    'WPMailSMTP\\Providers\\Sendinblue\\Options' => $baseDir . '/src/Providers/Sendinblue/Options.php',
    'WPMailSMTP\\Providers\\Sendlayer\\Mailer' => $baseDir . '/src/Providers/Sendlayer/Mailer.php',
    'WPMailSMTP\\Providers\\Sendlayer\\Options' => $baseDir . '/src/Providers/Sendlayer/Options.php',
    'WPMailSMTP\\Providers\\SparkPost\\Mailer' => $baseDir . '/src/Providers/SparkPost/Mailer.php',
    'WPMailSMTP\\Providers\\SparkPost\\Options' => $baseDir . '/src/Providers/SparkPost/Options.php',
    'WPMailSMTP\\Providers\\Zoho\\Options' => $baseDir . '/src/Providers/Zoho/Options.php',
    'WPMailSMTP\\Queue\\Attachments' => $baseDir . '/src/Queue/Attachments.php',
    'WPMailSMTP\\Queue\\Email' => $baseDir . '/src/Queue/Email.php',
    'WPMailSMTP\\Queue\\Migration' => $baseDir . '/src/Queue/Migration.php',
    'WPMailSMTP\\Queue\\Queue' => $baseDir . '/src/Queue/Queue.php',
    'WPMailSMTP\\Reports\\Emails\\Summary' => $baseDir . '/src/Reports/Emails/Summary.php',
    'WPMailSMTP\\Reports\\Reports' => $baseDir . '/src/Reports/Reports.php',
    'WPMailSMTP\\SiteHealth' => $baseDir . '/src/SiteHealth.php',
    'WPMailSMTP\\Tasks\\DebugEventsCleanupTask' => $baseDir . '/src/Tasks/DebugEventsCleanupTask.php',
    'WPMailSMTP\\Tasks\\Meta' => $baseDir . '/src/Tasks/Meta.php',
    'WPMailSMTP\\Tasks\\NotificationsUpdateTask' => $baseDir . '/src/Tasks/NotificationsUpdateTask.php',
    'WPMailSMTP\\Tasks\\Queue\\CleanupQueueTask' => $baseDir . '/src/Tasks/Queue/CleanupQueueTask.php',
    'WPMailSMTP\\Tasks\\Queue\\ProcessQueueTask' => $baseDir . '/src/Tasks/Queue/ProcessQueueTask.php',
    'WPMailSMTP\\Tasks\\Queue\\SendEnqueuedEmailTask' => $baseDir . '/src/Tasks/Queue/SendEnqueuedEmailTask.php',
    'WPMailSMTP\\Tasks\\Reports\\SummaryEmailTask' => $baseDir . '/src/Tasks/Reports/SummaryEmailTask.php',
    'WPMailSMTP\\Tasks\\Task' => $baseDir . '/src/Tasks/Task.php',
    'WPMailSMTP\\Tasks\\Tasks' => $baseDir . '/src/Tasks/Tasks.php',
    'WPMailSMTP\\Upgrade' => $baseDir . '/src/Upgrade.php',
    'WPMailSMTP\\Uploads' => $baseDir . '/src/Uploads.php',
    'WPMailSMTP\\UsageTracking\\SendUsageTask' => $baseDir . '/src/UsageTracking/SendUsageTask.php',
    'WPMailSMTP\\UsageTracking\\UsageTracking' => $baseDir . '/src/UsageTracking/UsageTracking.php',
    'WPMailSMTP\\Vendor\\Google\\AccessToken\\Revoke' => $baseDir . '/vendor_prefixed/google/apiclient/src/AccessToken/Revoke.php',
    'WPMailSMTP\\Vendor\\Google\\AccessToken\\Verify' => $baseDir . '/vendor_prefixed/google/apiclient/src/AccessToken/Verify.php',
    'WPMailSMTP\\Vendor\\Google\\AuthHandler\\AuthHandlerFactory' => $baseDir . '/vendor_prefixed/google/apiclient/src/AuthHandler/AuthHandlerFactory.php',
    'WPMailSMTP\\Vendor\\Google\\AuthHandler\\Guzzle5AuthHandler' => $baseDir . '/vendor_prefixed/google/apiclient/src/AuthHandler/Guzzle5AuthHandler.php',
    'WPMailSMTP\\Vendor\\Google\\AuthHandler\\Guzzle6AuthHandler' => $baseDir . '/vendor_prefixed/google/apiclient/src/AuthHandler/Guzzle6AuthHandler.php',
    'WPMailSMTP\\Vendor\\Google\\AuthHandler\\Guzzle7AuthHandler' => $baseDir . '/vendor_prefixed/google/apiclient/src/AuthHandler/Guzzle7AuthHandler.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\AccessToken' => $baseDir . '/vendor_prefixed/google/auth/src/AccessToken.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\ApplicationDefaultCredentials' => $baseDir . '/vendor_prefixed/google/auth/src/ApplicationDefaultCredentials.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\CacheTrait' => $baseDir . '/vendor_prefixed/google/auth/src/CacheTrait.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\Cache\\InvalidArgumentException' => $baseDir . '/vendor_prefixed/google/auth/src/Cache/InvalidArgumentException.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\Cache\\Item' => $baseDir . '/vendor_prefixed/google/auth/src/Cache/Item.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\Cache\\MemoryCacheItemPool' => $baseDir . '/vendor_prefixed/google/auth/src/Cache/MemoryCacheItemPool.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\Cache\\SysVCacheItemPool' => $baseDir . '/vendor_prefixed/google/auth/src/Cache/SysVCacheItemPool.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\Cache\\TypedItem' => $baseDir . '/vendor_prefixed/google/auth/src/Cache/TypedItem.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\CredentialsLoader' => $baseDir . '/vendor_prefixed/google/auth/src/CredentialsLoader.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\Credentials\\AppIdentityCredentials' => $baseDir . '/vendor_prefixed/google/auth/src/Credentials/AppIdentityCredentials.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\Credentials\\GCECredentials' => $baseDir . '/vendor_prefixed/google/auth/src/Credentials/GCECredentials.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\Credentials\\IAMCredentials' => $baseDir . '/vendor_prefixed/google/auth/src/Credentials/IAMCredentials.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\Credentials\\ImpersonatedServiceAccountCredentials' => $baseDir . '/vendor_prefixed/google/auth/src/Credentials/ImpersonatedServiceAccountCredentials.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\Credentials\\InsecureCredentials' => $baseDir . '/vendor_prefixed/google/auth/src/Credentials/InsecureCredentials.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\Credentials\\ServiceAccountCredentials' => $baseDir . '/vendor_prefixed/google/auth/src/Credentials/ServiceAccountCredentials.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\Credentials\\ServiceAccountJwtAccessCredentials' => $baseDir . '/vendor_prefixed/google/auth/src/Credentials/ServiceAccountJwtAccessCredentials.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\Credentials\\UserRefreshCredentials' => $baseDir . '/vendor_prefixed/google/auth/src/Credentials/UserRefreshCredentials.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\FetchAuthTokenCache' => $baseDir . '/vendor_prefixed/google/auth/src/FetchAuthTokenCache.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\FetchAuthTokenInterface' => $baseDir . '/vendor_prefixed/google/auth/src/FetchAuthTokenInterface.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\GCECache' => $baseDir . '/vendor_prefixed/google/auth/src/GCECache.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\GetQuotaProjectInterface' => $baseDir . '/vendor_prefixed/google/auth/src/GetQuotaProjectInterface.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\HttpHandler\\Guzzle5HttpHandler' => $baseDir . '/vendor_prefixed/google/auth/src/HttpHandler/Guzzle5HttpHandler.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\HttpHandler\\Guzzle6HttpHandler' => $baseDir . '/vendor_prefixed/google/auth/src/HttpHandler/Guzzle6HttpHandler.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\HttpHandler\\Guzzle7HttpHandler' => $baseDir . '/vendor_prefixed/google/auth/src/HttpHandler/Guzzle7HttpHandler.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\HttpHandler\\HttpClientCache' => $baseDir . '/vendor_prefixed/google/auth/src/HttpHandler/HttpClientCache.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\HttpHandler\\HttpHandlerFactory' => $baseDir . '/vendor_prefixed/google/auth/src/HttpHandler/HttpHandlerFactory.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\Iam' => $baseDir . '/vendor_prefixed/google/auth/src/Iam.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\IamSignerTrait' => $baseDir . '/vendor_prefixed/google/auth/src/IamSignerTrait.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\Middleware\\AuthTokenMiddleware' => $baseDir . '/vendor_prefixed/google/auth/src/Middleware/AuthTokenMiddleware.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\Middleware\\ProxyAuthTokenMiddleware' => $baseDir . '/vendor_prefixed/google/auth/src/Middleware/ProxyAuthTokenMiddleware.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\Middleware\\ScopedAccessTokenMiddleware' => $baseDir . '/vendor_prefixed/google/auth/src/Middleware/ScopedAccessTokenMiddleware.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\Middleware\\SimpleMiddleware' => $baseDir . '/vendor_prefixed/google/auth/src/Middleware/SimpleMiddleware.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\OAuth2' => $baseDir . '/vendor_prefixed/google/auth/src/OAuth2.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\ProjectIdProviderInterface' => $baseDir . '/vendor_prefixed/google/auth/src/ProjectIdProviderInterface.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\ServiceAccountSignerTrait' => $baseDir . '/vendor_prefixed/google/auth/src/ServiceAccountSignerTrait.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\SignBlobInterface' => $baseDir . '/vendor_prefixed/google/auth/src/SignBlobInterface.php',
    'WPMailSMTP\\Vendor\\Google\\Auth\\UpdateMetadataInterface' => $baseDir . '/vendor_prefixed/google/auth/src/UpdateMetadataInterface.php',
    'WPMailSMTP\\Vendor\\Google\\Client' => $baseDir . '/vendor_prefixed/google/apiclient/src/Client.php',
    'WPMailSMTP\\Vendor\\Google\\Collection' => $baseDir . '/vendor_prefixed/google/apiclient/src/Collection.php',
    'WPMailSMTP\\Vendor\\Google\\Exception' => $baseDir . '/vendor_prefixed/google/apiclient/src/Exception.php',
    'WPMailSMTP\\Vendor\\Google\\Http\\Batch' => $baseDir . '/vendor_prefixed/google/apiclient/src/Http/Batch.php',
    'WPMailSMTP\\Vendor\\Google\\Http\\MediaFileUpload' => $baseDir . '/vendor_prefixed/google/apiclient/src/Http/MediaFileUpload.php',
    'WPMailSMTP\\Vendor\\Google\\Http\\REST' => $baseDir . '/vendor_prefixed/google/apiclient/src/Http/REST.php',
    'WPMailSMTP\\Vendor\\Google\\Model' => $baseDir . '/vendor_prefixed/google/apiclient/src/Model.php',
    'WPMailSMTP\\Vendor\\Google\\Service' => $baseDir . '/vendor_prefixed/google/apiclient/src/Service.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Exception' => $baseDir . '/vendor_prefixed/google/apiclient/src/Service/Exception.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\AutoForwarding' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/AutoForwarding.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\BatchDeleteMessagesRequest' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/BatchDeleteMessagesRequest.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\BatchModifyMessagesRequest' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/BatchModifyMessagesRequest.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\CseIdentity' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/CseIdentity.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\CseKeyPair' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/CseKeyPair.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\CsePrivateKeyMetadata' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/CsePrivateKeyMetadata.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Delegate' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Delegate.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\DisableCseKeyPairRequest' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/DisableCseKeyPairRequest.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Draft' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Draft.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\EnableCseKeyPairRequest' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/EnableCseKeyPairRequest.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Filter' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Filter.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\FilterAction' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/FilterAction.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\FilterCriteria' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/FilterCriteria.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ForwardingAddress' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/ForwardingAddress.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\History' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/History.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\HistoryLabelAdded' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/HistoryLabelAdded.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\HistoryLabelRemoved' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/HistoryLabelRemoved.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\HistoryMessageAdded' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/HistoryMessageAdded.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\HistoryMessageDeleted' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/HistoryMessageDeleted.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ImapSettings' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/ImapSettings.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\KaclsKeyMetadata' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/KaclsKeyMetadata.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Label' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Label.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\LabelColor' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/LabelColor.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\LanguageSettings' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/LanguageSettings.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListCseIdentitiesResponse' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListCseIdentitiesResponse.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListCseKeyPairsResponse' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListCseKeyPairsResponse.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListDelegatesResponse' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListDelegatesResponse.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListDraftsResponse' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListDraftsResponse.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListFiltersResponse' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListFiltersResponse.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListForwardingAddressesResponse' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListForwardingAddressesResponse.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListHistoryResponse' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListHistoryResponse.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListLabelsResponse' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListLabelsResponse.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListMessagesResponse' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListMessagesResponse.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListSendAsResponse' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListSendAsResponse.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListSmimeInfoResponse' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListSmimeInfoResponse.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListThreadsResponse' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListThreadsResponse.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Message' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Message.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\MessagePart' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/MessagePart.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\MessagePartBody' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/MessagePartBody.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\MessagePartHeader' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/MessagePartHeader.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ModifyMessageRequest' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/ModifyMessageRequest.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ModifyThreadRequest' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/ModifyThreadRequest.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ObliterateCseKeyPairRequest' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/ObliterateCseKeyPairRequest.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\PopSettings' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/PopSettings.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Profile' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Profile.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\Users' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/Users.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersDrafts' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersDrafts.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersHistory' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersHistory.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersLabels' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersLabels.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersMessages' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersMessages.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersMessagesAttachments' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersMessagesAttachments.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersSettings' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersSettings.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersSettingsCse' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersSettingsCse.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersSettingsCseIdentities' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersSettingsCseIdentities.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersSettingsCseKeypairs' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersSettingsCseKeypairs.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersSettingsDelegates' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersSettingsDelegates.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersSettingsFilters' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersSettingsFilters.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersSettingsForwardingAddresses' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersSettingsForwardingAddresses.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersSettingsSendAs' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersSettingsSendAs.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersSettingsSendAsSmimeInfo' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersSettingsSendAsSmimeInfo.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersThreads' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersThreads.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\SendAs' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/SendAs.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\SmimeInfo' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/SmimeInfo.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\SmtpMsa' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/SmtpMsa.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Thread' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/Thread.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\VacationSettings' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/VacationSettings.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\WatchRequest' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/WatchRequest.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\WatchResponse' => $baseDir . '/vendor_prefixed/google/apiclient-services/src/Gmail/WatchResponse.php',
    'WPMailSMTP\\Vendor\\Google\\Service\\Resource' => $baseDir . '/vendor_prefixed/google/apiclient/src/Service/Resource.php',
    'WPMailSMTP\\Vendor\\Google\\Task\\Composer' => $baseDir . '/vendor_prefixed/google/apiclient/src/Task/Composer.php',
    'WPMailSMTP\\Vendor\\Google\\Task\\Exception' => $baseDir . '/vendor_prefixed/google/apiclient/src/Task/Exception.php',
    'WPMailSMTP\\Vendor\\Google\\Task\\Retryable' => $baseDir . '/vendor_prefixed/google/apiclient/src/Task/Retryable.php',
    'WPMailSMTP\\Vendor\\Google\\Task\\Runner' => $baseDir . '/vendor_prefixed/google/apiclient/src/Task/Runner.php',
    'WPMailSMTP\\Vendor\\Google\\Utils\\UriTemplate' => $baseDir . '/vendor_prefixed/google/apiclient/src/Utils/UriTemplate.php',
    'WPMailSMTP\\Vendor\\Google_AccessToken_Revoke' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\Google_AccessToken_Verify' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\Google_AuthHandler_AuthHandlerFactory' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\Google_AuthHandler_Guzzle5AuthHandler' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\Google_AuthHandler_Guzzle6AuthHandler' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\Google_AuthHandler_Guzzle7AuthHandler' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\Google_Client' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\Google_Collection' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\Google_Exception' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\Google_Http_Batch' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\Google_Http_MediaFileUpload' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\Google_Http_REST' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\Google_Model' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\Google_Service' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\Google_Service_Exception' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\Google_Service_Resource' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\Google_Task_Composer' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\Google_Task_Exception' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\Google_Task_Retryable' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\Google_Task_Runner' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\Google_Utils_UriTemplate' => $baseDir . '/vendor_prefixed/google/apiclient/src/aliases.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\BodySummarizer' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/BodySummarizer.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\BodySummarizerInterface' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/BodySummarizerInterface.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Client' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Client.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\ClientInterface' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/ClientInterface.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\ClientTrait' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/ClientTrait.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Cookie\\CookieJar' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Cookie/CookieJar.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Cookie\\CookieJarInterface' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Cookie/CookieJarInterface.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Cookie\\FileCookieJar' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Cookie/FileCookieJar.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Cookie\\SessionCookieJar' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Cookie/SessionCookieJar.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Cookie\\SetCookie' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Cookie/SetCookie.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Exception\\BadResponseException' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/BadResponseException.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Exception\\ClientException' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/ClientException.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Exception\\ConnectException' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/ConnectException.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Exception\\GuzzleException' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/GuzzleException.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Exception\\InvalidArgumentException' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/InvalidArgumentException.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Exception\\RequestException' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/RequestException.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Exception\\ServerException' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/ServerException.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Exception\\TooManyRedirectsException' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/TooManyRedirectsException.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Exception\\TransferException' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/TransferException.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\HandlerStack' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/HandlerStack.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Handler\\CurlFactory' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/CurlFactory.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Handler\\CurlFactoryInterface' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/CurlFactoryInterface.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Handler\\CurlHandler' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/CurlHandler.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Handler\\CurlMultiHandler' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/CurlMultiHandler.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Handler\\EasyHandle' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/EasyHandle.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Handler\\HeaderProcessor' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/HeaderProcessor.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Handler\\MockHandler' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/MockHandler.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Handler\\Proxy' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/Proxy.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Handler\\StreamHandler' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/StreamHandler.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\MessageFormatter' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/MessageFormatter.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\MessageFormatterInterface' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/MessageFormatterInterface.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Middleware' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Middleware.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Pool' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Pool.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\PrepareBodyMiddleware' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/PrepareBodyMiddleware.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\AggregateException' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/AggregateException.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\CancellationException' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/CancellationException.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\Coroutine' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/Coroutine.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\Create' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/Create.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\Each' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/Each.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\EachPromise' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/EachPromise.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\FulfilledPromise' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/FulfilledPromise.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\Is' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/Is.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\Promise' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/Promise.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\PromiseInterface' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/PromiseInterface.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\PromisorInterface' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/PromisorInterface.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\RejectedPromise' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/RejectedPromise.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\RejectionException' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/RejectionException.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\TaskQueue' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/TaskQueue.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\TaskQueueInterface' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/TaskQueueInterface.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\Utils' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/Utils.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\AppendStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/AppendStream.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\BufferStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/BufferStream.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\CachingStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/CachingStream.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\DroppingStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/DroppingStream.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\Exception\\MalformedUriException' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/Exception/MalformedUriException.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\FnStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/FnStream.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\Header' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/Header.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\HttpFactory' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/HttpFactory.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\InflateStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/InflateStream.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\LazyOpenStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/LazyOpenStream.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\LimitStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/LimitStream.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\Message' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/Message.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\MessageTrait' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/MessageTrait.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\MimeType' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/MimeType.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\MultipartStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/MultipartStream.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\NoSeekStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/NoSeekStream.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\PumpStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/PumpStream.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\Query' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/Query.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\Request' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/Request.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\Response' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/Response.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\Rfc7230' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/Rfc7230.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\ServerRequest' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/ServerRequest.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\Stream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/Stream.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\StreamDecoratorTrait' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/StreamDecoratorTrait.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\StreamWrapper' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/StreamWrapper.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\UploadedFile' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/UploadedFile.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\Uri' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/Uri.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\UriComparator' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/UriComparator.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\UriNormalizer' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/UriNormalizer.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\UriResolver' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/UriResolver.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\Utils' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/Utils.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\RedirectMiddleware' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/RedirectMiddleware.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\RequestOptions' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/RequestOptions.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\RetryMiddleware' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/RetryMiddleware.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\TransferStats' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/TransferStats.php',
    'WPMailSMTP\\Vendor\\GuzzleHttp\\Utils' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Utils.php',
    'WPMailSMTP\\Vendor\\Monolog\\Attribute\\AsMonologProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Attribute/AsMonologProcessor.php',
    'WPMailSMTP\\Vendor\\Monolog\\DateTimeImmutable' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/DateTimeImmutable.php',
    'WPMailSMTP\\Vendor\\Monolog\\ErrorHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/ErrorHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Formatter\\ChromePHPFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/ChromePHPFormatter.php',
    'WPMailSMTP\\Vendor\\Monolog\\Formatter\\ElasticaFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/ElasticaFormatter.php',
    'WPMailSMTP\\Vendor\\Monolog\\Formatter\\ElasticsearchFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/ElasticsearchFormatter.php',
    'WPMailSMTP\\Vendor\\Monolog\\Formatter\\FlowdockFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/FlowdockFormatter.php',
    'WPMailSMTP\\Vendor\\Monolog\\Formatter\\FluentdFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/FluentdFormatter.php',
    'WPMailSMTP\\Vendor\\Monolog\\Formatter\\FormatterInterface' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/FormatterInterface.php',
    'WPMailSMTP\\Vendor\\Monolog\\Formatter\\GelfMessageFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/GelfMessageFormatter.php',
    'WPMailSMTP\\Vendor\\Monolog\\Formatter\\GoogleCloudLoggingFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/GoogleCloudLoggingFormatter.php',
    'WPMailSMTP\\Vendor\\Monolog\\Formatter\\HtmlFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/HtmlFormatter.php',
    'WPMailSMTP\\Vendor\\Monolog\\Formatter\\JsonFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/JsonFormatter.php',
    'WPMailSMTP\\Vendor\\Monolog\\Formatter\\LineFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/LineFormatter.php',
    'WPMailSMTP\\Vendor\\Monolog\\Formatter\\LogglyFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/LogglyFormatter.php',
    'WPMailSMTP\\Vendor\\Monolog\\Formatter\\LogmaticFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/LogmaticFormatter.php',
    'WPMailSMTP\\Vendor\\Monolog\\Formatter\\LogstashFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/LogstashFormatter.php',
    'WPMailSMTP\\Vendor\\Monolog\\Formatter\\MongoDBFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/MongoDBFormatter.php',
    'WPMailSMTP\\Vendor\\Monolog\\Formatter\\NormalizerFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/NormalizerFormatter.php',
    'WPMailSMTP\\Vendor\\Monolog\\Formatter\\ScalarFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/ScalarFormatter.php',
    'WPMailSMTP\\Vendor\\Monolog\\Formatter\\WildfireFormatter' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/WildfireFormatter.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\AbstractHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/AbstractHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\AbstractProcessingHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\AbstractSyslogHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/AbstractSyslogHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\AmqpHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/AmqpHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\BrowserConsoleHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/BrowserConsoleHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\BufferHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/BufferHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\ChromePHPHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ChromePHPHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\CouchDBHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/CouchDBHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\CubeHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/CubeHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\Curl\\Util' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/Curl/Util.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\DeduplicationHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/DeduplicationHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\DoctrineCouchDBHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/DoctrineCouchDBHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\DynamoDbHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/DynamoDbHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\ElasticaHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ElasticaHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\ElasticsearchHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ElasticsearchHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\ErrorLogHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ErrorLogHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\FallbackGroupHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FallbackGroupHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\FilterHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FilterHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\FingersCrossedHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FingersCrossedHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\FingersCrossed\\ActivationStrategyInterface' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FingersCrossed/ActivationStrategyInterface.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\FingersCrossed\\ChannelLevelActivationStrategy' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FingersCrossed/ChannelLevelActivationStrategy.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\FingersCrossed\\ErrorLevelActivationStrategy' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FingersCrossed/ErrorLevelActivationStrategy.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\FirePHPHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FirePHPHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\FleepHookHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FleepHookHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\FlowdockHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FlowdockHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\FormattableHandlerInterface' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FormattableHandlerInterface.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\FormattableHandlerTrait' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FormattableHandlerTrait.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\GelfHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/GelfHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\GroupHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/GroupHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\Handler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/Handler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\HandlerInterface' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/HandlerInterface.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\HandlerWrapper' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/HandlerWrapper.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\IFTTTHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/IFTTTHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\InsightOpsHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/InsightOpsHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\LogEntriesHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/LogEntriesHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\LogglyHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/LogglyHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\LogmaticHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/LogmaticHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\MailHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/MailHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\MandrillHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/MandrillHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\MissingExtensionException' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/MissingExtensionException.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\MongoDBHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/MongoDBHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\NativeMailerHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/NativeMailerHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\NewRelicHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/NewRelicHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\NoopHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/NoopHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\NullHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/NullHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\OverflowHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/OverflowHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\PHPConsoleHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/PHPConsoleHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\ProcessHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ProcessHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\ProcessableHandlerInterface' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ProcessableHandlerInterface.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\ProcessableHandlerTrait' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ProcessableHandlerTrait.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\PsrHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/PsrHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\PushoverHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/PushoverHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\RedisHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/RedisHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\RedisPubSubHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/RedisPubSubHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\RollbarHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/RollbarHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\RotatingFileHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/RotatingFileHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\SamplingHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SamplingHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\SendGridHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SendGridHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\SlackHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SlackHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\SlackWebhookHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SlackWebhookHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\Slack\\SlackRecord' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/Slack/SlackRecord.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\SocketHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SocketHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\SqsHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SqsHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\StreamHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/StreamHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\SwiftMailerHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SwiftMailerHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\SymfonyMailerHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SymfonyMailerHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\SyslogHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SyslogHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\SyslogUdpHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SyslogUdpHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\SyslogUdp\\UdpSocket' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SyslogUdp/UdpSocket.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\TelegramBotHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/TelegramBotHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\TestHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/TestHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\WebRequestRecognizerTrait' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/WebRequestRecognizerTrait.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\WhatFailureGroupHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/WhatFailureGroupHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Handler\\ZendMonitorHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ZendMonitorHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\LogRecord' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/LogRecord.php',
    'WPMailSMTP\\Vendor\\Monolog\\Logger' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Logger.php',
    'WPMailSMTP\\Vendor\\Monolog\\Processor\\GitProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/GitProcessor.php',
    'WPMailSMTP\\Vendor\\Monolog\\Processor\\HostnameProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/HostnameProcessor.php',
    'WPMailSMTP\\Vendor\\Monolog\\Processor\\IntrospectionProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/IntrospectionProcessor.php',
    'WPMailSMTP\\Vendor\\Monolog\\Processor\\MemoryPeakUsageProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/MemoryPeakUsageProcessor.php',
    'WPMailSMTP\\Vendor\\Monolog\\Processor\\MemoryProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/MemoryProcessor.php',
    'WPMailSMTP\\Vendor\\Monolog\\Processor\\MemoryUsageProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/MemoryUsageProcessor.php',
    'WPMailSMTP\\Vendor\\Monolog\\Processor\\MercurialProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/MercurialProcessor.php',
    'WPMailSMTP\\Vendor\\Monolog\\Processor\\ProcessIdProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/ProcessIdProcessor.php',
    'WPMailSMTP\\Vendor\\Monolog\\Processor\\ProcessorInterface' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/ProcessorInterface.php',
    'WPMailSMTP\\Vendor\\Monolog\\Processor\\PsrLogMessageProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/PsrLogMessageProcessor.php',
    'WPMailSMTP\\Vendor\\Monolog\\Processor\\TagProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/TagProcessor.php',
    'WPMailSMTP\\Vendor\\Monolog\\Processor\\UidProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/UidProcessor.php',
    'WPMailSMTP\\Vendor\\Monolog\\Processor\\WebProcessor' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/WebProcessor.php',
    'WPMailSMTP\\Vendor\\Monolog\\Registry' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Registry.php',
    'WPMailSMTP\\Vendor\\Monolog\\ResettableInterface' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/ResettableInterface.php',
    'WPMailSMTP\\Vendor\\Monolog\\SignalHandler' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/SignalHandler.php',
    'WPMailSMTP\\Vendor\\Monolog\\Test\\TestCase' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Test/TestCase.php',
    'WPMailSMTP\\Vendor\\Monolog\\Utils' => $baseDir . '/vendor_prefixed/monolog/monolog/src/Monolog/Utils.php',
    'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\Base32' => $baseDir . '/vendor_prefixed/paragonie/constant_time_encoding/src/Base32.php',
    'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\Base32Hex' => $baseDir . '/vendor_prefixed/paragonie/constant_time_encoding/src/Base32Hex.php',
    'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\Base64' => $baseDir . '/vendor_prefixed/paragonie/constant_time_encoding/src/Base64.php',
    'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\Base64DotSlash' => $baseDir . '/vendor_prefixed/paragonie/constant_time_encoding/src/Base64DotSlash.php',
    'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\Base64DotSlashOrdered' => $baseDir . '/vendor_prefixed/paragonie/constant_time_encoding/src/Base64DotSlashOrdered.php',
    'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\Base64UrlSafe' => $baseDir . '/vendor_prefixed/paragonie/constant_time_encoding/src/Base64UrlSafe.php',
    'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\Binary' => $baseDir . '/vendor_prefixed/paragonie/constant_time_encoding/src/Binary.php',
    'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\EncoderInterface' => $baseDir . '/vendor_prefixed/paragonie/constant_time_encoding/src/EncoderInterface.php',
    'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\Encoding' => $baseDir . '/vendor_prefixed/paragonie/constant_time_encoding/src/Encoding.php',
    'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\Hex' => $baseDir . '/vendor_prefixed/paragonie/constant_time_encoding/src/Hex.php',
    'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\RFC4648' => $baseDir . '/vendor_prefixed/paragonie/constant_time_encoding/src/RFC4648.php',
    'WPMailSMTP\\Vendor\\Psr\\Cache\\CacheException' => $baseDir . '/vendor_prefixed/psr/cache/src/CacheException.php',
    'WPMailSMTP\\Vendor\\Psr\\Cache\\CacheItemInterface' => $baseDir . '/vendor_prefixed/psr/cache/src/CacheItemInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Cache\\CacheItemPoolInterface' => $baseDir . '/vendor_prefixed/psr/cache/src/CacheItemPoolInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Cache\\InvalidArgumentException' => $baseDir . '/vendor_prefixed/psr/cache/src/InvalidArgumentException.php',
    'WPMailSMTP\\Vendor\\Psr\\Http\\Client\\ClientExceptionInterface' => $baseDir . '/vendor_prefixed/psr/http-client/src/ClientExceptionInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Http\\Client\\ClientInterface' => $baseDir . '/vendor_prefixed/psr/http-client/src/ClientInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Http\\Client\\NetworkExceptionInterface' => $baseDir . '/vendor_prefixed/psr/http-client/src/NetworkExceptionInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Http\\Client\\RequestExceptionInterface' => $baseDir . '/vendor_prefixed/psr/http-client/src/RequestExceptionInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\MessageInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/MessageInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\RequestFactoryInterface' => $baseDir . '/vendor_prefixed/psr/http-factory/src/RequestFactoryInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\RequestInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/RequestInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\ResponseFactoryInterface' => $baseDir . '/vendor_prefixed/psr/http-factory/src/ResponseFactoryInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\ResponseInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/ResponseInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\ServerRequestFactoryInterface' => $baseDir . '/vendor_prefixed/psr/http-factory/src/ServerRequestFactoryInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\ServerRequestInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/ServerRequestInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\StreamFactoryInterface' => $baseDir . '/vendor_prefixed/psr/http-factory/src/StreamFactoryInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\StreamInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/StreamInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\UploadedFileFactoryInterface' => $baseDir . '/vendor_prefixed/psr/http-factory/src/UploadedFileFactoryInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\UploadedFileInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/UploadedFileInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\UriFactoryInterface' => $baseDir . '/vendor_prefixed/psr/http-factory/src/UriFactoryInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\UriInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/UriInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Log\\AbstractLogger' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/AbstractLogger.php',
    'WPMailSMTP\\Vendor\\Psr\\Log\\InvalidArgumentException' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/InvalidArgumentException.php',
    'WPMailSMTP\\Vendor\\Psr\\Log\\LogLevel' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/LogLevel.php',
    'WPMailSMTP\\Vendor\\Psr\\Log\\LoggerAwareInterface' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/LoggerAwareInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Log\\LoggerAwareTrait' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/LoggerAwareTrait.php',
    'WPMailSMTP\\Vendor\\Psr\\Log\\LoggerInterface' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/LoggerInterface.php',
    'WPMailSMTP\\Vendor\\Psr\\Log\\LoggerTrait' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/LoggerTrait.php',
    'WPMailSMTP\\Vendor\\Psr\\Log\\NullLogger' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/NullLogger.php',
    'WPMailSMTP\\Vendor\\Psr\\Log\\Test\\DummyTest' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/Test/DummyTest.php',
    'WPMailSMTP\\Vendor\\Psr\\Log\\Test\\LoggerInterfaceTest' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/Test/LoggerInterfaceTest.php',
    'WPMailSMTP\\Vendor\\Psr\\Log\\Test\\TestLogger' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/Test/TestLogger.php',
    'WPMailSMTP\\Vendor\\Symfony\\Polyfill\\Intl\\Idn\\Idn' => $baseDir . '/vendor_prefixed/symfony/polyfill-intl-idn/Idn.php',
    'WPMailSMTP\\Vendor\\Symfony\\Polyfill\\Intl\\Idn\\Info' => $baseDir . '/vendor_prefixed/symfony/polyfill-intl-idn/Info.php',
    'WPMailSMTP\\Vendor\\Symfony\\Polyfill\\Intl\\Idn\\Resources\\unidata\\DisallowedRanges' => $baseDir . '/vendor_prefixed/symfony/polyfill-intl-idn/Resources/unidata/DisallowedRanges.php',
    'WPMailSMTP\\Vendor\\Symfony\\Polyfill\\Intl\\Idn\\Resources\\unidata\\Regex' => $baseDir . '/vendor_prefixed/symfony/polyfill-intl-idn/Resources/unidata/Regex.php',
    'WPMailSMTP\\Vendor\\Symfony\\Polyfill\\Mbstring\\Mbstring' => $baseDir . '/vendor_prefixed/symfony/polyfill-mbstring/Mbstring.php',
    'WPMailSMTP\\WP' => $baseDir . '/src/WP.php',
    'WPMailSMTP\\WPMailArgs' => $baseDir . '/src/WPMailArgs.php',
    'WPMailSMTP\\WPMailInitiator' => $baseDir . '/src/WPMailInitiator.php',
);
