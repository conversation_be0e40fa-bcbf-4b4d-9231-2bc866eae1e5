/**
 * Common styles.
 */
.wpforms-edit-post-education-notice-body {
  padding-left: 52px;
  min-height: 40px;
  margin: 0;
  position: relative;
}

.wpforms-edit-post-education-notice-body p {
  margin: 0;
}

.wpforms-edit-post-education-notice-body:before {
  content: '';
  display: block;
  width: 40px;
  height: 40px;
  background-image: url("../../images/sullie-edit-post-education.svg");
  background-size: 100%;
  position: absolute;
  left: 0;
  top: 0;
}

/**
 * Classic editor styles.
 */
.wpforms-hidden {
  display: none !important;
}

.wpforms-edit-post-education-notice {
  position: relative;
  background: #FFFFFF;
  border: 1px solid #C3C4C7;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  padding: 10px;
  font-size: 14px;
  line-height: 21px;
  display: flex;
  align-items: flex-start;
  margin: 23px 0;
}

.wpforms-edit-post-education-notice .notice-dismiss {
  position: static;
  margin-left: auto;
}

.wpforms-edit-post-education-notice:not(.wpforms-hidden) + #postdivrich {
  margin-top: -20px;
}

/**
 * Gutenberg editor styles.
 */
.is-distraction-free .interface-interface-skeleton__header:focus-within .wpforms-edit-post-education-notice .components-notice__actions,
.is-distraction-free .interface-interface-skeleton__header:focus-within .wpforms-edit-post-education-notice .components-notice__content {
  transform: none !important;
}

.components-notice-list .wpforms-edit-post-education-notice {
  background-color: #f0f6fc;
  border-top: 0;
  border-left: 4px solid #007cba;
  border-bottom: 0;
  border-right: 0;
  margin: 0;
}

.components-notice-list .wpforms-edit-post-education-notice-body {
  padding-right: 150px;
  float: left;
}

@media (max-width: 767px) {
  .components-notice-list .wpforms-edit-post-education-notice-body {
    float: none;
    padding-right: 0;
  }
  .components-notice-list .components-notice__action.components-button.wpforms-edit-post-education-notice-guide-button {
    margin-left: 52px;
  }
}

@media (min-width: 768px) {
  .components-notice-list .components-notice__action.components-button.wpforms-edit-post-education-notice-guide-button {
    position: absolute;
    right: 60px;
    top: 14px;
    margin: 0;
  }
  .components-notice-list .wpforms-edit-post-education-notice.components-notice .components-notice__dismiss {
    margin-top: 14px;
  }
}
