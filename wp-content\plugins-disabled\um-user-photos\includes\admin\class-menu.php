<?php
namespace um_ext\um_user_photos\admin;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Menu
 * @package um_ext\um_user_photos\admin
 */
class Menu {

	/**
	 * Menu constructor.
	 */
	public function __construct() {
		add_action( 'admin_menu', array( $this, 'extend_admin_menu' ) );
	}

	/**
	 * Additional menu item in Media Library for User Photos.
	 */
	public function extend_admin_menu() {
		if ( UM()->options()->get( 'um_user_photos_media_library' ) ) {
			add_media_page(
				__( 'User Photos', 'um-user-photos' ),
				__( 'User Photos', 'um-user-photos' ),
				'edit_users',
				'upload.php?mode=list&um_user_album_photos=1'
			);

			global $submenu_file;
			// phpcs:ignore WordPress.Security.NonceVerification -- No submitted input here.
			if ( ! empty( $_GET['um_user_album_photos'] ) ) {
				// phpcs:ignore WordPress.WP.GlobalVariablesOverride.Prohibited -- required for proper submenu displaying
				$submenu_file = 'upload.php?mode=list&um_user_album_photos=1';
			}

			add_filter( 'views_upload', array( $this, 'media_views' ) );
		}
	}

	/**
	 * Extends Media Library filters.
	 *
	 * @param array $views Views to use the filter bar display.
	 *
	 * @return array
	 */
	public function media_views( $views ) {
		// phpcs:ignore WordPress.Security.NonceVerification -- No submitted input here.
		if ( ! empty( $_GET['um_user_album_photos'] ) && UM()->options()->get( 'um_user_photos_media_library' ) ) {
			?>
			<input type="hidden" name="um_user_album_photos" value="1">
			<?php
		}
		return $views;
	}
}
