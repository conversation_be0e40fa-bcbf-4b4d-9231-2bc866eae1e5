<?php
/**
 * Template for the modal
 *
 * Used:  Profile page, the [um_user_notes_view] shortcode.
 * Call:  UM()->Notes()->profile()->add_modal();
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-notes/profile/modal.php
 *
 * @see      https://docs.ultimatemember.com/article/1516-templates-map
 * @package  um_ext\um_user_notes\templates
 * @version  1.1.1
 *
 * @var string $url Redirect URL on modal close.
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
?>

<div class="um um-notes-modal">
	<a href="#" data-close="<?php echo esc_url( $url ); ?>" id="um_notes_modal_close">&times; <?php esc_html_e( 'Close', 'um-user-notes' ); ?></a>
	<div class="um_notes_modal_content">
		<h1 class="um_notes_modal_default">
			<i class="um-user-notes-ajax-loading"></i>
		</h1>
	</div>
</div>
