(()=>{var e={941:(e,t,n)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const n="color: "+this.color;t.splice(1,0,n,"color: inherit");let o=0,r=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(o++,"%c"===e&&(r=o))})),t.splice(r,0,n)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=n(212)(t);const{formatters:o}=e.exports;o.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},212:(e,t,n)=>{e.exports=function(e){function t(e){let n,r,s,i=null;function a(...e){if(!a.enabled)return;const o=a,r=Number(new Date),s=r-(n||r);o.diff=s,o.prev=n,o.curr=r,n=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((n,r)=>{if("%%"===n)return"%";i++;const s=t.formatters[r];if("function"==typeof s){const t=e[i];n=s.call(o,t),e.splice(i,1),i--}return n})),t.formatArgs.call(o,e);(o.log||t.log).apply(o,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=o,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==i?i:(r!==t.namespaces&&(r=t.namespaces,s=t.enabled(e)),s),set:e=>{i=e}}),"function"==typeof t.init&&t.init(a),a}function o(e,n){const o=t(this.namespace+(void 0===n?":":n)+e);return o.log=this.log,o}function r(e,t){let n=0,o=0,r=-1,s=0;for(;n<e.length;)if(o<t.length&&(t[o]===e[n]||"*"===t[o]))"*"===t[o]?(r=o,s=n,o++):(n++,o++);else{if(-1===r)return!1;o=r+1,s++,n=s}for(;o<t.length&&"*"===t[o];)o++;return o===t.length}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names,...t.skips.map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const n=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of n)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const n of t.skips)if(r(e,n))return!1;for(const n of t.names)if(r(e,n))return!0;return!1},t.humanize=n(997),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((n=>{t[n]=e[n]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let n=0;for(let t=0;t<e.length;t++)n=(n<<5)-n+e.charCodeAt(t),n|=0;return t.colors[Math.abs(n)%t.colors.length]},t.enable(t.load()),t}},997:e=>{var t=1e3,n=60*t,o=60*n,r=24*o,s=7*r,i=365.25*r;function a(e,t,n,o){var r=t>=1.5*n;return Math.round(e/n)+" "+o+(r?"s":"")}e.exports=function(e,c){c=c||{};var d=typeof e;if("string"===d&&e.length>0)return function(e){if((e=String(e)).length>100)return;var a=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!a)return;var c=parseFloat(a[1]);switch((a[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return c*i;case"weeks":case"week":case"w":return c*s;case"days":case"day":case"d":return c*r;case"hours":case"hour":case"hrs":case"hr":case"h":return c*o;case"minutes":case"minute":case"mins":case"min":case"m":return c*n;case"seconds":case"second":case"secs":case"sec":case"s":return c*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}(e);if("number"===d&&isFinite(e))return c.long?function(e){var s=Math.abs(e);if(s>=r)return a(e,s,r,"day");if(s>=o)return a(e,s,o,"hour");if(s>=n)return a(e,s,n,"minute");if(s>=t)return a(e,s,t,"second");return e+" ms"}(e):function(e){var s=Math.abs(e);if(s>=r)return Math.round(e/r)+"d";if(s>=o)return Math.round(e/o)+"h";if(s>=n)return Math.round(e/n)+"m";if(s>=t)return Math.round(e/t)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},372:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var o=n(941);const r=n.n(o)()("dops:analytics");let s,i;window._tkq=window._tkq||[],window.ga=window.ga||function(){(window.ga.q=window.ga.q||[]).push(arguments)},window.ga.l=+new Date;const a={initialize:function(e,t,n){a.setUser(e,t),a.setSuperProps(n),a.identifyUser()},setGoogleAnalyticsEnabled:function(e,t=null){this.googleAnalyticsEnabled=e,this.googleAnalyticsKey=t},setMcAnalyticsEnabled:function(e){this.mcAnalyticsEnabled=e},setUser:function(e,t){i={ID:e,username:t}},setSuperProps:function(e){s=e},assignSuperProps:function(e){s=Object.assign(s||{},e)},mc:{bumpStat:function(e,t){const n=function(e,t){let n="";if("object"==typeof e){for(const t in e)n+="&x_"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);r("Bumping stats %o",e)}else n="&x_"+encodeURIComponent(e)+"="+encodeURIComponent(t),r('Bumping stat "%s" in group "%s"',t,e);return n}(e,t);a.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom-no-pv"+n+"&t="+Math.random())},bumpStatWithPageView:function(e,t){const n=function(e,t){let n="";if("object"==typeof e){for(const t in e)n+="&"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);r("Built stats %o",e)}else n="&"+encodeURIComponent(e)+"="+encodeURIComponent(t),r('Built stat "%s" in group "%s"',t,e);return n}(e,t);a.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom"+n+"&t="+Math.random())}},pageView:{record:function(e,t){a.tracks.recordPageView(e),a.ga.recordPageView(e,t)}},purchase:{record:function(e,t,n,o,r,s,i){a.ga.recordPurchase(e,t,n,o,r,s,i)}},tracks:{recordEvent:function(e,t){t=t||{},0===e.indexOf("akismet_")||0===e.indexOf("jetpack_")?(s&&(r("- Super Props: %o",s),t=Object.assign(t,s)),r('Record event "%s" called with props %s',e,JSON.stringify(t)),window._tkq.push(["recordEvent",e,t])):r('- Event name must be prefixed by "akismet_" or "jetpack_"')},recordJetpackClick:function(e){const t="object"==typeof e?e:{target:e};a.tracks.recordEvent("jetpack_wpa_click",t)},recordPageView:function(e){a.tracks.recordEvent("akismet_page_view",{path:e})},setOptOut:function(e){r("Pushing setOptOut: %o",e),window._tkq.push(["setOptOut",e])}},ga:{initialized:!1,initialize:function(){let e={};a.ga.initialized||(i&&(e={userId:"u-"+i.ID}),window.ga("create",this.googleAnalyticsKey,"auto",e),a.ga.initialized=!0)},recordPageView:function(e,t){a.ga.initialize(),r("Recording Page View ~ [URL: "+e+"] [Title: "+t+"]"),this.googleAnalyticsEnabled&&(window.ga("set","page",e),window.ga("send",{hitType:"pageview",page:e,title:t}))},recordEvent:function(e,t,n,o){a.ga.initialize();let s="Recording Event ~ [Category: "+e+"] [Action: "+t+"]";void 0!==n&&(s+=" [Option Label: "+n+"]"),void 0!==o&&(s+=" [Option Value: "+o+"]"),r(s),this.googleAnalyticsEnabled&&window.ga("send","event",e,t,n,o)},recordPurchase:function(e,t,n,o,r,s,i){window.ga("require","ecommerce"),window.ga("ecommerce:addTransaction",{id:e,revenue:o,currency:i}),window.ga("ecommerce:addItem",{id:e,name:t,sku:n,price:r,quantity:s}),window.ga("ecommerce:send")}},identifyUser:function(){i&&window._tkq.push(["identifyUser",i.ID,i.username])},setProperties:function(e){window._tkq.push(["setProperties",e])},clearedIdentity:function(){window._tkq.push(["clearIdentity"])}},c=a},428:e=>{"use strict";e.exports=window.jQuery},455:e=>{"use strict";e.exports=window.wp.apiFetch},723:e=>{"use strict";e.exports=window.wp.i18n}},t={};function n(o){var r=t[o];if(void 0!==r)return r.exports;var s=t[o]={exports:{}};return e[o](s,s.exports,n),s.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=n(372),t=n(455),o=n.n(t),r=n(723),s=n(428),i=n.n(s);const _n=r._n,__=r.__,{connectionsUrl:a,isEnhancedPublishingEnabled:c,resharePath:d,refreshConnections:u,isReshareSupported:l,siteType:p}=window.jetpackSocialClassicEditorOptions,f=["instagram-business"],{recordEvent:g}=e.A.tracks,m=(e,t)=>{const n=window.wp.media.featuredImage.get(),o=n&&-1!==n,r=e("#pub-connection-needs-media"),s=!!r.html();if(o!==s)return;if(t.forEach((t=>{e(".wpas-submit-"+t).each(((t,n)=>{const r=e(n);r.prop("disabled")||o||r.data("checkedVal",r.prop("checked")),r.prop("checked",o&&r.data("checkedVal")),r.prop("disabled",!o)}))})),o)return void r.removeClass().html("");const i=c?/* translators: %s is the link to the media upload best practices. */__('You need a featured image to share to Instagram. Use the block editor for more advanced media features! <a href="%s" rel="noopener noreferrer" target="_blank">Learn more</a>.',"jetpack-publicize-pkg"):/* translators: %s is the link to the media upload best practices. */__('You need a featured image to share to Instagram. <a href="%s" rel="noopener noreferrer" target="_blank">Learn more</a>.',"jetpack-publicize-pkg",0);r.addClass("notice-warning publicize__notice-media-warning publicize__notice-warning").append(i.replace("%s","https://jetpack.com/support/jetpack-social/sharing-to-instagram-with-jetpack-social"))};i()((function(e){const t=f.filter((t=>e(".wpas-submit-"+t).length));if(t.length>0){m(e,t);const n=window.wp.media.featuredImage.set;window.wp.media.featuredImage.set=function(o){n(o),m(e,t)}}let n=!1;const r=function(){n||(n=!0,o()({path:u,method:"GET"}).then(i).finally((()=>{n=!1})))};let s;window.addEventListener("focus",(()=>{s&&clearTimeout(s),s=setTimeout(r,2e3)}));const i=function(t){const n=e("#pub-connection-tests");n.removeClass("test-in-progress").removeClass("below-h2").removeClass("error").removeClass("publicize-token-refresh-message").html("");let o=0,r=0;for(const n of t){let t=!1;"twitter"===n.service_name?(r++,t=!0):["broken","must_reauth"].includes(n.status)&&(o++,t="broken"===n.status),t&&e("#wpas-submit-"+n.connection_id).prop("checked",!1).prop("disabled",!0)}if(o){
/* translators: %s is the link to the connections page */
const e=_n('One of your social connections needs attention. You can fix it on the <a href="%s" rel="noopener noreferrer" target="_blank">connection management</a> page.','Some of your social connections need attention. You can fix them on the <a href="%s" rel="noopener noreferrer" target="_blank">connection management</a> page.',o,"jetpack-publicize-pkg");n.addClass("below-h2").addClass("error").addClass("publicize-token-refresh-message").append(e.replace("%s",a))}if(r){
/* translators: %s is the link to the connections page */
const e=__('Twitter is not supported anymore. <a href="%s" rel="noopener noreferrer" target="_blank">Learn more</a>.',"jetpack-publicize-pkg");o?n.append("<hr />"):n.addClass("below-h2").addClass("error").addClass("publicize-token-refresh-message"),n.append(e.replace("%s",a))}};e("#pub-connection-tests").length&&r();const c=e("#publicize-share-now"),C=e("#publicize-share-now-notice"),h=e("#publicize-form").find('li input[type="checkbox"]'),w=(e,t="warning")=>{C.removeClass("notice-warning notice-success hidden").addClass("publicize__notice-warning notice-"+t).text(e)};c.on("click",(function(t){if(t.preventDefault(),!l)return;if(C.removeClass("publicize__notice-warning").addClass("hidden").text(""),!h.filter(((t,n)=>e(n).prop("checked"))).length)return void w(__("Please select at least one connection to share with.","jetpack-publicize-pkg"));const n=e('input[name="post_ID"]').val(),r=d.replace("{postId}",n),s=h.filter(((t,n)=>!e(n).prop("checked"))).map(((t,n)=>e(n).data("id"))).toArray(),i=e('textarea[name="wpas_title"]').val();c.prop("disabled",!0).text(__("Sharing…","jetpack-publicize-pkg")),g("jetpack_social_reshare_clicked",{location:"classic-editor",environment:p}),o()({path:r,method:"POST",data:{message:i,skipped_connections:s,async:!0}}).then((()=>{w(__("Request submitted successfully.","jetpack-publicize-pkg"),"success")})).catch((()=>{w(__("An error occurred while sharing your post.","jetpack-publicize-pkg"))})).finally((()=>{c.prop("disabled",!1).text(__("Share now","jetpack-publicize-pkg"))}))}))}))})()})();