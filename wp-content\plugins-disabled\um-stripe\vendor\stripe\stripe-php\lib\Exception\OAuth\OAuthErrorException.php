<?php

namespace UM_Stripe\Vendor\Stripe\Exception\OAuth;

/**
 * Implements properties and methods common to all (non-SPL) Stripe OAuth
 * exceptions.
 */
abstract class OAuthErrorException extends \UM_Stripe\Vendor\Stripe\Exception\ApiErrorException
{
    protected function constructErrorObject()
    {
        if (null === $this->jsonBody) {
            return null;
        }
        return \UM_Stripe\Vendor\Stripe\OAuthErrorObject::constructFrom($this->jsonBody);
    }
}
