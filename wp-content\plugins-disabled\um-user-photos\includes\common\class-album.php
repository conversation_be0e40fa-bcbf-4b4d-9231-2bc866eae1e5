<?php
namespace um_ext\um_user_photos\common;

use WP_Post;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Album
 *
 * @package um_ext\um_user_photos\common
 */
class Album {

	/**
	 * @var array
	 */
	public $privacy_options = array();

	/**
	 * Album constructor.
	 */
	public function __construct() {
	}

	/**
	 *
	 */
	public function hooks() {
		add_action( 'init', array( $this, 'prepare_variables' ) );
	}

	/**
	 *
	 */
	public function prepare_variables() {
		$this->privacy_options = array(
			'everyone' => __( 'Everyone', 'um-user-photos' ),
			'only_me'  => __( 'Only me', 'um-user-photos' ),
		);
		$this->privacy_options = apply_filters( 'um_user_photos_privacy_options_dropdown', $this->privacy_options );
	}

	/**
	 *
	 * @param int|WP_Post $album Optional. Album ID or Album WP_Post object.
	 *
	 * @return bool
	 */
	public function exists( $album ) {
		$status = get_post_status( $album );
		return false !== $status;
	}

	/**
	 *
	 * @param int|WP_Post $album Optional. Album ID or Album WP_Post object.
	 *
	 * @return bool
	 */
	public function is_published( $album ) {
		$status = get_post_status( $album );
		return 'publish' === $status;
	}

	public function get_cover( $album_id ) {
		// default album cover
		$img = UM_USER_PHOTOS_URL . 'assets/images/dummy_album_cover.png';

		if ( has_post_thumbnail( $album_id ) ) {
			$image = wp_get_attachment_image_src( get_post_thumbnail_id( $album_id ), 'album_cover' );

			if ( ! empty( $image[0] ) ) {
				$img = $image[0];
			}
		} else {
			$photos = get_post_meta( $album_id, '_photos', true );

			if ( ! empty( $photos ) && is_array( $photos ) ) {
				if ( isset( $photos[0] ) ) {
					$photo_id = $photos[0];
					$image    = wp_get_attachment_image_src( $photo_id, 'album_cover' );
					if ( ! empty( $image[0] ) ) {
						$img = $image[0];
					}
				}
			} else {
				if ( defined( 'UM_DEV_MODE' ) && UM_DEV_MODE && UM()->options()->get( 'enable_new_ui' ) ) {
					$img = '';
				} else {
					$img = $this->generate_album_cover_placeholder();
				}
			}
		}

		return $img;
	}

	/**
	 * @param int $width
	 * @param int $height
	 * @param string $background
	 *
	 * @return string
	 */
	private function generate_album_cover_placeholder( $width = 340, $height = 240, $background = '0, 0, 0, 0.8' ) {
		$width  = absint( $width );
		$height = absint( $height );

		$cover_size = UM()->options()->get( 'um_user_photos_cover_size' );
		if ( $cover_size && '' !== trim( $cover_size ) ) {
			$cover_size = strtolower( $cover_size );
			$size       = explode( 'x', $cover_size );
			if ( is_array( $size ) && 2 === count( $size ) ) {
				$width  = absint( $size[0] );
				$height = absint( $size[1] );
			}
		}

		return 'https://dummyimage.com/' . $width . 'x' . $height . '/efefef/efefef.png&text=.';
		//ob_start();

		$image = imagecreatetruecolor( $width, $height );
		imagesavealpha( $image, true );
		$color = imagecolorallocatealpha( $image,243,243,243,1 );
		imagefill( $image, 0, 0, $color );

		imagepng( $image );

		$imagedata = ob_get_clean();
		return 'data:image/png;base64,' . base64_encode( $imagedata );
	}
}
