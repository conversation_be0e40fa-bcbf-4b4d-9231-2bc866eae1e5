<?php
/**
 * Template for the User Photos
 *
 * Page: "Account", tab "My Photos"
 * Caller: UM()->User_Photos()->account()->um_account_content_hook_um_user_photos()
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/account.php
 * @var int  $user_id
 * @var int  $count_user_photos
 * @var bool $download_my_photos_notice
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
?>

<div class="um_user_photos_account">
	<?php
	$count_user_photos = UM()->User_Photos()->common()->user()->photos_count( $user_id );

	$albums = new WP_Query(
		array(
			'post_type'      => 'um_user_photos',
			'author__in'     => array( $user_id ),
			'posts_per_page' => -1,
		)
	);

	if ( $albums->found_posts > 0 ) {
		?>
		<p>
			<?php esc_html_e( 'Once photos and albums are deleted, they are deleted permanently and cannot be recovered.', 'um-user-photos' ); ?>
		</p>
		<?php
	}

	if ( 0 !== absint( $count_user_photos ) ) {
		$nonce_download = esc_attr( wp_create_nonce( 'um_user_photos_download_all' ) );
		?>
		<a id="um_user_photos_download_all"
			class="um-button"
			data-profile="<?php echo esc_attr( $user_id ); ?>"
			href="<?php echo esc_url( admin_url( "admin-ajax.php?action=download_my_photos&profile_id=$user_id&_wpnonce=$nonce_download" ) ); ?>"><?php esc_html_e( 'Download my photos', 'um-user-photos' ); ?></a>

		<?php if ( $download_my_photos_notice ) { ?>
			<div class="um-field-error">
				<span class="um-field-arrow"><i class="um-faicon-caret-up"></i></span><?php echo esc_html( $download_my_photos_notice ); ?>
			</div>
		<?php } ?>
	<?php } ?>

	<?php
	if ( $albums->found_posts > 0 ) {
		$nonce_delete = esc_attr( wp_create_nonce( 'um_user_photos_delete_all' ) );
		?>
		<p>
			<a id="um_user_photos_delete_all"
				class="um-button danger"
				data-profile="<?php echo esc_attr( $user_id ); ?>"
				data-nonce="<?php echo esc_attr( $nonce_delete ); ?>"
				data-alert_message="<?php esc_attr_e( 'Are you sure to delete all your albums & photos?', 'um-user-photos' ); ?>">
				<?php esc_html_e( 'Delete all my albums & photos', 'um-user-photos' ); ?>
			</a>
		</p>
		<?php
	} else {
		echo '<p>' . esc_html__( 'There are no photos and albums in your account.', 'um-user-photos' ) . '</p>';
	}
	?>
</div>
<div class="um-clear"></div>
