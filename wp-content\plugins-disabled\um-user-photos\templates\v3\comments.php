<?php
/**
 * Template for the UM User Photos. The comments block
 *
 * Page: "Profile", tab "Photos", the image popup
 * Parent template: caption.php
 * @version 2.1.9
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/comments.php
 * @var int   $image_id
 * @var array $comments
 * @var int   $comment_count
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
?>

<div class="um-user-photos-comments-loop">
	<?php
	$last_id = null;
	if ( ! empty( $comments ) ) {
		foreach ( $comments as $photo_comment ) {
			$is_url  = filter_var( $photo_comment->comment_content, FILTER_VALIDATE_URL );
			$content = $is_url ? '<a href="' . esc_url( $photo_comment->comment_content ) . '" target="_blank">' . esc_html( $photo_comment->comment_content ) . '</a>' : esc_html( $photo_comment->comment_content );

			$last_id = $photo_comment->comment_ID;

			UM()->get_template(
				'v3/comment.php',
				UM_USER_PHOTOS_PLUGIN,
				array(
					'user_id'       => $photo_comment->user_id,
					'content'       => $content,
					'date'          => $photo_comment->comment_date,
					'id'            => $photo_comment->comment_ID,
					'image_id'      => $image_id,
					'photo_comment' => $photo_comment,
				),
				true
			);
		}
	}
	?>
</div>
<?php
$loader = UM()->frontend()::layouts()::ajax_loader( 's', array( 'classes' => array( 'um-user-photos-loader', 'um-display-none' ) ) );

$button_classes = array();
if ( empty( $comments ) || $comment_count <= count( $comments ) ) {
	$button_classes[] = 'um-display-none';
}
$button = UM()->frontend()::layouts()::button(
	__( 'Load more', 'um-user-photos' ),
	array(
		'id'      => 'um-user-photos-comments-load-more',
		'type'    => 'button',
		'size'    => 's',
		'design'  => 'tertiary-gray',
		'data'    => array(
			'wpnonce'  => wp_create_nonce( 'um_user_photos_load_more_comments' ),
			'image_id' => $image_id,
			'all'      => $comment_count,
			'last_id'  => $last_id,
		),
		'classes' => $button_classes,
	)
);

echo wp_kses( $loader . $button, UM()->get_allowed_html( 'templates' ) );
