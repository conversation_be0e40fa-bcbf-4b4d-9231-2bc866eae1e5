jQuery(document).ready(function(r){r(document.body).on("click",".um-user-photos-new-album",function(t){t.preventDefault();var e=r("body").find('[data-scope="um-user-photos-modal"]'),o=r(this).data("modal_title"),t=r(this).data("nonce"),a=e.find(".um-user-photos-modal-content");a.html('<div class="text-center"><div class="um-user-photos-ajax-loading"></div></div>'),e.show(),wp.ajax.send("um_user_photos_new_album_modal",{data:{image_id:r(this).data("photo-id"),_wpnonce:t},success:function(t){a.html(t),e.find(".um-user-photos-modal-title").text(o)},error:function(t){console.log(t)}})}),r(document.body).on("click",".um-user-photos-edit-album",function(t){t.preventDefault();let e=r("body").find('[data-scope="um-user-photos-modal"]'),o=r(this).data("modal_title");var t=r(this).data("id"),a=r(this).data("nonce");let s=e.find(".um-user-photos-modal-content");s.html('<div class="text-center"><div class="um-user-photos-ajax-loading"></div></div>'),e.show(),wp.ajax.send("um_user_photos_edit_album_modal",{data:{album_id:t,_wpnonce:a},success:function(t){s.html(t),e.find(".um-user-photos-modal-title").text(o),r("#um-user-photos-sortable").sortable()},error:function(t){console.log(t)}})}),r(document.body).on("click",".um-user-photos-delete-album",function(t){t.preventDefault();let o=r(this);r.um_confirm({title:o.attr("title"),message:o.data("confirm"),onYes:function(){var t=o.data("id"),e=o.data("nonce");wp.ajax.send("um_user_photos_delete_album",{data:{id:t,_wpnonce:e},success:function(){var t=new URL(window.location.href);t.searchParams.delete("album_id"),window.location.assign(t)},error:function(t){console.log(t)}})},object:this})}),r(document.body).on("click",".um-user-photos-edit-image",function(t){t.preventDefault();var e=r("body").find('[data-scope="um-user-photos-modal"]'),o=r(this).data("modal_title"),t=r(this).data("nonce"),a=e.find(".um-user-photos-modal-content");a.html('<div class="text-center"><div class="um-user-photos-ajax-loading"></div></div>'),e.show(),wp.ajax.send("um_user_photos_edit_photo_modal",{data:{image_id:r(this).data("photo-id"),_wpnonce:t},success:function(t){a.html(t),e.find(".um-user-photos-modal-title").text(o)},error:function(t){console.log(t)}})}),r(document.body).on("click",".um-user-photos-back-to-gallery",function(t){t.preventDefault();let e=r(this).parents(".um-user-photos-albums");var t=r(this).data("profile"),o=r(this).data("nonce");e.html('<div class="text-center"><div class="um-user-photos-ajax-loading"></div></div>'),wp.ajax.send("um_user_photos_get_gallery",{data:{user_id:t,_wpnonce:o},success:function(t){e.html(t);t=new URL(window.location.href);t.searchParams.delete("album_id"),history.pushState(null,"",t)},error:function(t){console.log(t)}})})}),function(c){function e(){var t=c(".um-user-photos-modal");t.hide(),t.find(".um-user-photos-modal-title").text(""),t.find(".um-user-photos-modal-content").html("")}function o(t,e){let o=t.parents(".um-user-photos-albums");o.html('<div class="text-center"><div class="um-user-photos-ajax-loading"></div></div>');t=t.data("nonce");wp.ajax.send("um_user_photos_get_single_album_view",{data:{id:e,_wpnonce:t},success:function(t){o.html(t)},error:function(t){console.log(t)}}),o.siblings(".um-user-photos-add").remove(),window.album_load=!1}function m(){c(".um-user-photos-error").show(),c(".um-galley-modal-update").attr("disabled","disabled")}function l(){c(".um-user-photos-error").hide(),c(".um-galley-modal-update").removeAttr("disabled","disabled")}c(document.body).on("click",".um-user-photos-modal-close-link",function(t){t.preventDefault(),e()}),c(document.body).on("click",".um-user-photos-modal",function(t){c(t.target).closest(".um-user-photos-modal-body").length||e()}),c(document).on("click",".um-galley-modal-update",function(t){t.preventDefault();var o,a=c(this),s=a.text(),t=a.parents("form"),e=t.attr("data-limit"),r=t.attr("data-count"),u=c(".um-user-photos-modal-form").attr("data-album_limit"),n=c(".um-user-photos-modal-form").attr("data-count_album"),i=c(".um-user-photos-modal-form").attr("data-max_upload"),d=c(".um-user-photos-modal-form").attr("data-max_size"),m=c("#um-user-photos-input-album-images").attr("data-imgs_size"),l=c("#um-user-photos-images-uploaded img").length,p=0;0<c("#um-user-photos-input-album-cover.uploaded").length&&(p=c("#um-user-photos-input-album-cover.uploaded").attr("data-size")),parseInt(l)>parseInt(i)||0<c("#um-user-photos-input-album-cover.uploaded").length&&parseInt(l)>parseInt(i)-1?c(".um-user-photos-max-upload-error").show():parseInt(p)+parseInt(m)>parseInt(d)?c(".um-user-photos-max-size-upload-error").show():""!==e&&parseInt(l)+parseInt(r)>parseInt(e)||""!==u&&parseInt(l)+parseInt(n)>parseInt(u)?c(".um-user-photos-error").show():(c(".um-user-photos-error").hide(),i=t.attr("action"),p=t[0],m=new FormData(p),a.html('<i class="um-user-photos-ajax-loading"></i>'),a.attr("disabled",!0),(o=t.find(".um-galley-form-response")).removeClass("success").removeClass("error"),o.html(""),c.ajax({type:"POST",url:i,data:m,cache:!1,contentType:!1,processData:!1,success:function(e){if(e.success)location.reload();else{a.html(s),a.removeAttr("disabled");try{o.addClass("error");var t="<ul><li>"+e.data+"</li></ul>";o.html(t)}catch(t){e.data&&o.html("<span>Error:"+e.data+"</span>").addClass("error")}}},error:function(t){a.html(s),a.attr("disabled",!1),console.log("error"),console.log(t)}}))}),window.album_load=!1,c(window).on("load",function(){var t=new URLSearchParams(window.location.search);t.has("album_id")&&!t.has("photo_id")&&(window.album_load=!0,t=t.get("album_id"),o(c(".um-user-photos-album-block[data-id="+t+"]"),t))}),c(window).on("popstate",function(t){var e=new URLSearchParams(window.location.search);!e.has("album_id")&&0<c(".um-user-photos-single-album").length?c(".um-user-photos-back-to-gallery").trigger("click"):e.has("album_id")&&0===c(".um-user-photos-single-album").length&&c(".um-user-photos-album-block[data-id="+e.get("album_id")+"]").trigger("click")}),c(document.body).on("click",".um-user-photos-album-block",function(t){t.preventDefault();var e,t=c(this).data("id");o(c(this),t),new URLSearchParams(window.location.search).has("album_id")||((e=new URL(window.location.href)).searchParams.set("album_id",t),history.pushState({albumId:t},"",e))}),c(document.body).on("click","#um-user-photos-image-update-btn",function(t){t.preventDefault();var e=c(this),o=e.text(),t=e.parents("form"),a=(e.html('<i class="um-user-photos-ajax-loading"></i>'),e.attr("disabled",!0),t.find(".um-galley-form-response")),t=(a.removeClass("success").removeClass("error"),a.html(""),UM.common.form.vanillaSerialize("um_user_photos_edit_image"));wp.ajax.send("um_user_photos_image_update",{data:t,success:function(t){e.html(o),e.attr("disabled",!1),a.addClass("success"),a.html("<ul><li>"+t+"</li></ul>"),setTimeout(function(){a.parents(".um-user-photos-modal").hide()},1e3)},error:function(t){e.html(o),e.attr("disabled",!1),a.addClass("error"),a.html("<ul><li>"+t+"</li></ul>"),setTimeout(function(){a.parents(".um-user-photos-modal").hide()},2e3)}})}),c(document).on("click",".um-user-photos-delete-photo-album",function(t){t.preventDefault(),t.stopPropagation(),c(this).hide();let s=c("#um-user-photos-form-edit-album"),e=c(this).data("confirmation");var r=c(this).data("delete_photo"),t=c(this).data("id"),o=c(this).data("wpnonce"),u=s.attr("data-limit"),n=s.attr("data-count"),i=s.attr("data-album_limit"),d=s.attr("data-count_album"),a=c(this).parents("#um-user-photos-form-edit-album").find(".um-galley-form-response");confirm(e)?wp.ajax.send("um_delete_album_photo",{data:{image_id:t,_wpnonce:o},success:function(t){var e,o,a;c(r).remove(),""!==u&&(e=n-1,o=d-1,a=c("#um-user-photos-images-uploaded img").length,s.data("count",e).data("count_album",o),parseInt(o)>parseInt(e)?parseInt(a)+parseInt(e)<parseInt(u)&&(c("#um-user-photos-form-edit-album .um-modal-btn").show(),c("#um-user-photos-form-edit-album .um-user-photos-error").hide(),c("#um-user-photos-input-album-images, .um-galley-modal-update").removeAttr("disabled")):""!==i&&parseInt(a)+parseInt(o)<parseInt(i)&&(c("#um-user-photos-form-edit-album .um-modal-btn").show(),c("#um-user-photos-form-edit-album .um-user-photos-error").hide(),c("#um-user-photos-input-album-images, .um-galley-modal-update").removeAttr("disabled")))},error:function(t){console.log(t),a.html("<span>Error:"+t+"</span>").addClass("error")}}):c(this).show()}),c(document).on("click","#um-user-photos-toggle-view-photos-load-more",function(t){t.preventDefault();var e=c(this),o=e.parents(".um-load-more"),a=e.text(),t=(e.attr("data-href"),e.attr("data-profile")),s=e.attr("data-per_page"),r=e.attr("data-current_page"),r=parseInt(r)+1,u=e.attr("data-count"),n=e.parents(".um-user-photos-albums"),i=n.find(".photos-container .um-user-photos-single-album"),d=jQuery(this).attr("data-wpnonce");e.attr("data-current_page",r),e.text(wp.i18n.__("Loading","um-user-photos")),e.attr("disabled",!0),n.css("opacity","0.5"),wp.ajax.send("um_user_photos_load_more",{data:{profile:t,per_page:s,page:r,_wpnonce:d},success:function(t){e.text(a),e.attr("disabled",!1),n.css("opacity","1"),""===t?o.remove():(i.append(t),t=i.find(".um-user-photos-image-block").length,parseInt(u)<=parseInt(t)&&o.remove(),n.attr("data-count",t))},error:function(t){console.log(t)}})}),c(document).on("change","#um-user-photos-input-album-cover",function(t){var e=c(this).parents("h1.album-poster-holder"),o=new FileReader;o.onload=function(t){t=t.target.result;e.css("background-image",'url("'+t+'")'),e.css("background-size","contain")},o.readAsDataURL(this.files[0]),c("#um-user-photos-input-album-cover").addClass("uploaded").attr("data-size",this.files[0].size)}),c(document).on("change","#um-user-photos-input-album-images",function(t){c(".um-user-photos-max-upload-error").hide(),c(".um-user-photos-max-size-upload-error").hide();for(var e=c(this).parents("form"),o=c(this).parents("form").find("#um-user-photos-images-uploaded"),a=t.target.files,s=0,r=(o.html(""),0);r<a.length;r++){var u,n=a[r];n.type.match("image")&&((u=new FileReader).addEventListener("load",function(t){t=t.target;o.append('<span><img src="'+t.result+'" data-index=""/></span>')}),u.readAsDataURL(n),s+=n.size)}var t=e.attr("data-count"),i=e.attr("data-limit"),d=e.attr("data-album_limit"),e=e.attr("data-count_album");""!==i&&""!==d?(r+parseInt(t)>parseInt(i)||r+parseInt(e)>parseInt(d)?m:l)():(""!==i&&(r+parseInt(t)>parseInt(i)?m:l)(),""!==d&&(r+parseInt(e)>parseInt(d)?m:l)()),u.addEventListener("loadend",function(t){var e;0===c(".um-user-photos-album-photos").length&&0<c("#um-user-photos-cover-image").length&&(e=o.attr("data-covertext"),o.find("span").first().addClass("um-user-photos-cover").attr("data-covertext",e))}),c(".um-user-photos-modal-form h6").show(),c(this).attr("data-imgs_size",s)}),c(document.body).on("click","#um-user-photos-images-uploaded span",function(t){var e;t.preventDefault(),0<c("#um-user-photos-cover-image").length&&(t=c(this).index(),e=c("#um-user-photos-images-uploaded").attr("data-covertext"),c(".um-user-photos-album-photos .um-user-photos-photo").removeClass("um-user-photos-cover"),c("#um-user-photos-images-uploaded span").removeClass("um-user-photos-cover"),c(this).addClass("um-user-photos-cover").attr("data-covertext",e),c("#um-user-photos-cover-image").val(t),c("#um-user-photos-cover-image-id").val(""))}),c(document.body).on("click",".um-user-photos-album-photos .um-user-photos-photo",function(t){t.preventDefault(),0<c("#um-user-photos-cover-image").length&&(c(".um-user-photos-album-photos .um-user-photos-photo").removeClass("um-user-photos-cover"),c("#um-user-photos-images-uploaded span").removeClass("um-user-photos-cover"),c(this).addClass("um-user-photos-cover"),c("#um-user-photos-cover-image").val(""),t=c(this).find("input").val(),c("#um-user-photos-cover-image-id").val(t))}),c(document).on("change","#um-user-photos-input-album-cover",function(t){t.preventDefault();var t=c(this),e=t.parents("form"),o=e.attr("data-max_size"),a=e.attr("data-max_size_error"),s=e.find(".um-user-photos-modal-footer"),e=e.find(".um-galley-form-response"),r=(e.html("").removeClass("error"),s.show(),o/1e6);if(t[0].files&&o<=t[0].files[0].size)return o=t[0].files[0].name+" "+a+" "+r+" MB",t.trigger("reset"),e.html("<span>"+o+"</span>").addClass("error"),s.hide(),!1}),c(document).on("change","#um-user-photos-input-album-images",function(t){t.preventDefault();var e=c(this),t=e.parents("form"),o=t.attr("data-max_size"),a=t.attr("data-max_size_error"),s=t.find(".um-user-photos-modal-footer"),t=t.find(".um-galley-form-response"),r=(t.html("").removeClass("error"),s.show(),o/1e6);if(e[0].files){for(var u,n,i=!1,d=e[0].files,m="",l=0;d[l];l++)u=e[0].files[l].size,n=e[0].files[l].name,o<=u&&(i=!0,m=n+" "+a+" "+r+" MB");if(i)return t.html("<span>"+m+"</span>").addClass("error"),s.hide(),!1}}),c(document).on("click",".um-user-photos-album-options",function(t){t.preventDefault();t=c(this).next(".um-dropdown");return t.length&&UM.dropdown.show(t)}),c(document).on("click",".um-dropdown-hide",function(t){t.preventDefault(),UM.dropdown.hideAll()}),c(document).on("click","[data-um-pagi-action] [data-page]",function(t){t.preventDefault();var e=jQuery(t.currentTarget).closest("[data-um-pagi-action]"),o=(e.data("um-pagi-action"),e.find(".um-pagi").data("wpnonce"));wp.ajax.send("um_user_photos_get_albums_content",{data:{page:jQuery(t.currentTarget).data("page"),per_page:e.data("um-pagi-per_page"),column:e.data("um-pagi-column"),_wpnonce:o},success:function(t){e.html(jQuery(t)),e.find(".um.ultimatemember_albums").unwrap(),c(document).trigger("resize")},error:function(t){console.log(t)}})})}(jQuery);