{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "videopress/video", "version": "0.1.0", "title": "VideoPress", "category": "media", "icon": "<svg viewBox='0 0 29 21' width='24' height='24' xmlns='http://www.w3.org/2000/svg'><path fill-rule='evenodd' clip-rule='evenodd' d='M2.79037 0.59375C4.0363 0.59375 5.13102 1.41658 5.47215 2.60947L8.8452 14.4044C8.8486 14.4164 8.85411 14.4273 8.86124 14.4368L12.8572 0.59375H15.0927H21.2721C25.6033 0.59375 28.5066 3.39892 28.5066 7.64565C28.5066 11.9411 25.5272 14.6196 21.0818 14.6196H18.1499H14.3719L13.6379 16.8813C12.9796 18.9095 11.0827 20.2839 8.94152 20.2839C6.80035 20.2839 4.90341 18.9095 4.24517 16.8813L0.137069 4.22276C-0.444671 2.43022 0.898038 0.59375 2.79037 0.59375ZM15.7374 10.4119H20.0156C21.8718 10.4119 22.9856 9.35018 22.9856 7.64565C22.9856 5.93137 21.8718 4.91839 20.0156 4.91839H17.5202L15.7374 10.4119Z'></path></svg>", "description": "Embed a video from your media library or upload a new one with VideoPress.", "supports": {"html": false, "align": true, "anchor": true, "spacing": {"margin": true, "padding": true}}, "attributes": {"autoplay": {"type": "boolean"}, "anchor": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "caption": {"type": "string"}, "controls": {"type": "boolean", "default": true}, "loop": {"type": "boolean"}, "maxWidth": {"type": "string", "default": "100%"}, "muted": {"type": "boolean"}, "playsinline": {"type": "boolean"}, "preload": {"type": "string", "default": "metadata"}, "seekbarPlayedColor": {"type": "string", "default": ""}, "seekbarLoadingColor": {"type": "string", "default": ""}, "seekbarColor": {"type": "string", "default": ""}, "useAverageColor": {"type": "boolean", "default": true}, "id": {"type": "number"}, "guid": {"type": "string"}, "src": {"type": "string"}, "cacheHtml": {"type": "string", "default": ""}, "poster": {"type": "string"}, "posterData": {"type": "object", "default": {}}, "videoRatio": {"type": "number"}, "tracks": {"type": "array", "items": {"type": "object"}, "default": []}, "privacySetting": {"type": "number", "default": 1}, "allowDownload": {"type": "boolean", "default": true}, "displayEmbed": {"type": "boolean", "default": true}, "rating": {"type": "string"}, "isPrivate": {"type": "boolean"}, "isExample": {"type": "boolean", "default": false}, "duration": {"type": "number"}}, "textdomain": "jetpack-videopress-pkg", "editorScript": "file:./index.js", "editorStyle": "file:./index.css", "style": "file:./style.css", "viewScript": "file:./view.js", "viewStyle": "file:./view.css"}