"use strict";var WPFormsConditionals=window.WPFormsConditionals||function(n,s){var i={arraySplitIntoChunks:function(e,o){return e.length?[e.slice(0,o)].concat(i.arraySplitIntoChunks(e.slice(o),o)):[]}},r={allFields:{},$ruleRows:{},conditionalFields:{},fieldsListTemplate:"",fieldValuesListTemplates:{},cacheAllFields:function(e){r.allFields=e},cacheRuleRows:function(e){r.$ruleRows=e||s(".wpforms-conditional-row")},setConditionalFields:function(){r.conditionalFields=r.removeUnsupportedFields()},removeUnsupportedFields(){const o=wpforms_builder.cl_fields_supported;let i={...r.allFields};return i=wp.hooks.applyFilters("wpforms.ConditionalLogicCore.BeforeRemoveUnsupportedFields",i),Object.keys(i).forEach(e=>{o.includes(i[e].type)&&!i[e].dynamic_choices||delete i[e]}),i},setTemplates:function(){r.setFieldsListTemplate(),r.fieldValuesListTemplates={}},setFieldsListTemplate(){var o=s("<select>").append(s("<option>",{value:"",text:wpforms_builder.select_field}));for(const t in r.conditionalFields){var i=r.conditionalFields[t];let e;e=void 0!==i.label&&""!==i.label.toString().trim()?wpf.sanitizeHTML(i.label.toString().trim()):wpforms_builder.field+" #"+i.id,o.append(s("<option>",{value:i.id,text:e,id:"option-"+i.id}))}r.fieldsListTemplate=o.html()},getFieldValuesListTemplate(e,o){if(r.fieldValuesListTemplates[o])return r.fieldValuesListTemplates[o];var i=wpf.orders.choices["field_"+o],t=s("<select>"),n=Object.values(wpf.getFields()).find(e=>e.id.toString()===o.toString());for(const a in i){var l=i[a],d=void 0!==n.choices[l]&&""!==n.choices[l].label.toString().trim()?wpf.sanitizeHTML(n.choices[l].label.toString().trim()):wpforms_builder.choice_empty_label_tpl.replace("{number}",l);t.append(s("<option>",{value:l,text:d,id:"choice-"+l}))}return r.fieldValuesListTemplates[o]=t.html()},updateConditionalRuleRows:function(){r.$ruleRows.length;i.arraySplitIntoChunks(r.$ruleRows,20).map(function(o){return setTimeout(function(){for(var e=0;e<o.length;++e)r.updateConditionalRuleRow(o[e]),0},0),o})},updateConditionalRuleRow:function(e){var e=s(e),o=e.attr("data-field-id"),i=e.find(".wpforms-conditional-field"),t=i.val(),n=e.find(".wpforms-conditional-value");i[0].innerHTML=r.fieldsListTemplate,i.find("#option-"+o).remove(),(t?(i.find("#option-"+t).length?r.restorePreviousRuleRowSelection(e,i,t,n):r.removeRuleRow(e),i.find("option").removeAttr("id"),n):i).find("option").removeAttr("id")},fieldDeleteConfirmAlert:function(e){var o,i=wpforms_builder.conditionals_change+"<br>";s(".wpforms-conditional-field").each(function(){e.id!==Number(s(this).val())||e.choiceId&&e.choiceId!==Number(s(this).closest(".wpforms-conditional-row").find(".wpforms-conditional-value").val())||(i+=r.getChangedFieldNameForAlert(s(this).closest(".wpforms-conditional-group").data("reference")),o=!0,e.trigger=!0)}),o&&(e.message="<strong>"+e.message+"</strong><br><br>"+i)},restorePreviousRuleRowSelection:function(e,o,i,t){o.find("#option-"+i).prop("selected",!0),t.length&&t.is("select")&&(o=t.val(),t[0].innerHTML=r.getFieldValuesListTemplate(r.conditionalFields,i),t.find("#choice-"+o).length)&&t.find("#choice-"+o).prop("selected",!0)},removeRuleRow:function(e){var o=e.closest(".wpforms-conditional-group");1===o.find("table >tbody >tr").length?1<e.closest(".wpforms-conditional-groups").find(".wpforms-conditional-group").length?o.remove():(e.find(".wpforms-conditional-value").remove(),e.find(".value").append("<select>")):e.remove()},getChangedFieldNameForAlert(e){var o;return wpf.isNumber(e)?(((o=wpf.formObject("#wpforms-field-options")).fields[e]||{}).label||"").length?"<br/>"+wpf.sanitizeHTML(o.fields[e].label)+" ("+wpforms_builder.field+" #"+e+")":"<br>"+wpforms_builder.field+" #"+e:"<br>"+e}},l={init:function(){s(WPFormsConditionals.ready)},ready:function(){WPFormsConditionals.bindUIActions()},getLayoutFieldsToExclude(e){e=e.parents(".wpforms-field-option").find(".wpforms-field-option-hidden-id").val();const o=wpf.formObject("#wpforms-field-options");e=o?.fields[e]??[];const i={};return Object.values(e["columns-json"]??{}).forEach(e=>{Object.values(e?.fields??[]).forEach(e=>{o.fields[e]&&(i[e]=o.fields[e])})}),i},bindUIActions:function(){var e=s("#wpforms-builder");e.on("change",".wpforms-conditionals-enable-toggle input[type=checkbox]",function(e){WPFormsConditionals.conditionalToggle(this,e)}),e.on("click",".wpforms-field-option-group-conditionals",function(){var e,o=s(this);"layout"===o.parents(".wpforms-field-option").find(".wpforms-field-option-hidden-type").val()&&(e=o.find(".wpforms-conditional-block"),o=wpf.getFields(!1,!0,!1,l.getLayoutFieldsToExclude(o)),WPFormsConditionals.conditionalUpdateOptions(!1,o,e.find(".wpforms-conditional-row")))}),e.on("change",".wpforms-conditional-field",function(e){WPFormsConditionals.conditionalField(this,e)}),e.on("change",".wpforms-conditional-operator",function(e){WPFormsConditionals.conditionalOperator(this,e)}),e.on("click",".wpforms-conditional-rule-add",function(e){WPFormsConditionals.conditionalRuleAdd(this,e)}),e.on("click",".wpforms-conditional-rule-delete",function(e){WPFormsConditionals.conditionalRuleDelete(this,e)}),e.on("click",".wpforms-conditional-groups-add",function(e){WPFormsConditionals.conditionalGroupAdd(this,e)}),s(n).on("wpformsFieldUpdate",WPFormsConditionals.conditionalUpdateOptions),e.on("wpformsBeforeFieldDeleteAlert",function(e,o){r.fieldDeleteConfirmAlert(o)})},conditionalUpdateOptions:function(e,o,i){wpf.empty(o)||(r.cacheAllFields(o),r.cacheRuleRows(i),r.setConditionalFields(),r.setTemplates(),r.updateConditionalRuleRows())},conditionalToggle:function(e,o){o.preventDefault();var i=s(e),t=i.closest(".wpforms-conditional-block"),o=wp.template("wpforms-conditional-block"),e={fieldID:i.closest(".wpforms-field-option-row").data("field-id"),fieldName:i.data("name"),actions:i.data("actions"),actionDesc:i.data("action-desc"),reference:i.data("reference")};i.is(":checked")?(t.append(o(e)),o=wpf.getFields(!1,!0,!1,l.getLayoutFieldsToExclude(i)),WPFormsConditionals.conditionalUpdateOptions(!1,o,t.find(".wpforms-conditional-row"))):s.confirm({title:!1,content:wpforms_builder.conditionals_disable,backgroundDismiss:!1,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action:function(){t.find(".wpforms-conditional-groups").remove(),s(n).trigger("wpformsRemoveConditionalLogicRules",[i])}},cancel:{text:wpforms_builder.cancel,action:function(){i.prop("checked",!0)}}}})},conditionalField:function(e,o){o.preventDefault();var i,o=s(e),e=o.parent().parent(),t=e.find(".wpforms-conditional-operator"),n=t.find("option:selected").val(),l=WPFormsConditionals.conditionalData(o),o=l.inputName+"["+l.groupID+"]["+l.ruleID+"][value]";if(l.field)if("select"===l.field.type||"radio"===l.field.type||"checkbox"===l.field.type||"payment-multiple"===l.field.type||"payment-checkbox"===l.field.type||"payment-select"===l.field.type){if((i=s("<select>").attr({name:o,class:"wpforms-conditional-value"})).append(s("<option>",{value:"",text:wpforms_builder.select_choice})),l.field.choices)for(var d in wpf.orders.choices["field_"+l.field.id]){var d=wpf.orders.choices["field_"+l.field.id][d],a=void 0!==l.field.choices[d].label&&""!==l.field.choices[d].label.toString().trim()?wpf.sanitizeHTML(l.field.choices[d].label.toString().trim()):wpforms_builder.choice_empty_label_tpl.replace("{number}",d);i.append(s("<option>",{value:d,text:wpf.sanitizeHTML(a)}))}t.find("option:not([value='=='],[value='!='],[value='e'],[value='!e'])").prop("disabled",!0).prop("selected",!1)}else{var r="text";"rating"!==l.field.type&&"net_promoter_score"!==l.field.type&&"number-slider"!==l.field.type||(r="number"),i=s("<input>").attr({type:r,name:o,class:"wpforms-conditional-value"}),t.find("option").prop("disabled",!1)}else i=s("<select>");"e"!==n&&"!e"!==n||i.prop("disabled",!0),e.find(".value").empty().append(i)},conditionalOperator:function(e,o){o.preventDefault();o=s(e),e=o.parent().parent().find(".wpforms-conditional-value"),o=o.find("option:selected").val();"e"===o||"!e"===o?(e.prop("disabled",!0),e.is("select")?e.find("option:selected").prop("selected",!1):e.val("")):e.prop("disabled",!1)},conditionalRuleAdd:function(e,o){o.preventDefault();var o=s(e).closest(".wpforms-conditional-group").find("tr").last(),e=o.clone(),i=e.find(".wpforms-conditional-field"),t=e.find(".wpforms-conditional-operator"),n=WPFormsConditionals.conditionalData(i),l=Number(n.ruleID)+1,n=n.inputName+"["+n.groupID+"]["+l+"]";e.find("option:selected").prop("selected",!1),e.find(".value").empty().append(s("<select>")),i.attr("name",n+"[field]").attr("data-ruleid",l),t.attr("name",n+"[operator]"),o.after(e)},conditionalRuleDelete:function(e,o){o.preventDefault();var o=s(e),e=o.closest(".wpforms-conditional-group"),i=e.find("table >tbody >tr");i&&1===i.length?1<o.closest(".wpforms-conditional-groups").find(".wpforms-conditional-group").length?e.remove():(i.find(".wpforms-conditional-operator").val("==").trigger("change"),i.find(".wpforms-conditional-value").val("").trigger("change"),i.find(".wpforms-conditional-field").val("").trigger("change")):o.parent().parent().remove()},conditionalGroupAdd:function(e,o){o.preventDefault();var o=s(e),e=o.parent().find(".wpforms-conditional-group").last().clone(),i=(e.find("tr").slice(1).remove(),e.find(".wpforms-conditional-field")),t=e.find(".wpforms-conditional-operator"),n=WPFormsConditionals.conditionalData(i),l=Number(n.groupID)+1,n=n.inputName+"["+l+"][0]";e.find("option:selected").prop("selected",!1),e.find(".value").empty().append(s("<select>")),i.attr("name",n+"[field]").attr("data-ruleid",0).attr("data-groupid",l),t.attr("name",n+"[operator]"),o.before(e)},conditionalData:function(e){e=s(e),e={fields:wpf.getFields(!1,!0),inputBase:e.closest(".wpforms-conditional-row").attr("data-input-name"),fieldID:e.closest(".wpforms-conditional-row").attr("data-field-id"),ruleID:e.attr("data-ruleid"),groupID:e.attr("data-groupid"),selectedID:e.find(":selected").val()};return e.inputName=e.inputBase+"[conditionals]",e.selectedID.length?e.field=wpf.getField(e.selectedID):e.field=!1,e}};return l}(document,(window,jQuery));WPFormsConditionals.init();