/**
 * A nested map of all of our z-index values.
 *
 * Please add new values relative to their parent stacking context. For
 * example the values of 'root' are elements with a stacking context that have no
 * parents with a stacking context, other than the default html root.
 *
 * A Stacking Context is created when:
 * 1. It's the root element (HTML)
 * 2. Has a position other than static, with a z-index value
 * 3. position:fixed
 * 4. Has one of the following css properties: (transform, opacity<1, mix-blend-mode, filter)
 * 5. isolation:isolate
 * 6: -webkit-overflow-scrolling: touch
 *
 * So before adding a new z-index:
 * 1. You'll want to make sure the element actually creates a stacking context
 * 2. Look up what its parent stacking context is
 * You can run this handy gist: https://gist.github.com/gwwar/2f661deec7b99a1a418b in the console to find both.
 *
 * For readability please sort values from lowest to highest.
 *
 * Usage:
 * .environment-badge {
 *     z-index: z-index( 'root' '.environment-badge' );
 * }
 *
 * For a refresher on stacking contexts see:
 * https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Positioning/Understanding_z_index/The_stacking_context
 */
 @use 'sass:map';

$z-layers: (
	'root': (
		'.translator-invitation:before': -1,
		'.NuxWelcome:before': -1,
		'.infinite-scroll-end:before': -1,
		'.is-group-editor::before': -1,
		'.site-icon.is-blank .gridicon': 0,
		'.dops-chart__bar-section.is-spacer': 0,
		'.gear-dropdown:after': 0,
		'.plan-compare-card__ribbon': 1,
		'.reader__featured-post-title': 1,
		'.reader-list-gap__button': 1,
		'.post-trends__title': 1,
		'.plugin-item__label': 1,
		'.wp-editor-tools': 1,
		'.plan .gridicons-checkmark-circle': 1,
		'.plan-discount-message': 1,
		'.editor__switch-mode': 1,
		'.auth__form .form-fieldset input': 1,
		'.dops-chart__empty': 1,
		'.dops-chart__bar-marker': 1,
		'.dops-chart__bar-section.is-ghost::after': 1,
		'.billing-history-page .filter-popover-content': 1,
		'.module-content-table::after': 1,
		'.stats-popular__empty': 1,
		'.menus__menu-item.is-lander': 1,
		'.people-list-item__label': 1,
		'.is-actionable .theme__active-focus': 1,
		'.dops-accessible-focus .current-theme__button:focus': 1,
		'.signup-processing-screen__processing-step.is-processing:before': 1,
		'.dops-accessible-focus .theme__more-button button:focus': 1,
		'.reader-update-notice': 2,
		'.people-list-item .card__link-indicator': 2,
		'.updated-confirmation': 2,
		'.auth__form .form-fieldset input:focus': 2,
		'.toolbar-bulk': 2,
		'.menus__menu-item': 2,
		'.menus__menu-item-open:before': 2,
		'.dops-chart__bar-section': 2,
		'.module-content-table tbody th:first-child': 2,
		'ul.module-header-actions .module-header-action-link': 2,
		'.stats-module.is-loading .module-header-title::after': 2,
		'ul.module-content-list-item-action-submenu': 2,
		'ul.module-content-list-item-actions': 2,
		'.site-indicator__button': 3,
		'ul.module-content-list-item-actions.collapsed': 3,
		'.auth__input-wrapper .gridicon': 3,
		'.auth__self-hosted-instructions': 4,
		'.auth__form .form-password-input__toggle-visibility': 4,
		'.site-selector': 10,
		'.editor-featured-image__preview.is-transient::after': 10,
		'.wp-secondary .site-selector': 10,
		'.range__label': 10,
		'.sticky-panel.is-sticky .sticky-panel__content': 20,
		'.editor-featured-image .editor-drawer-well__remove': 20,
		'.main': 20, //TODO: this doesn't always have a stacking context
		//'tinymce-toolbar': 20, client/components/tinymce/index.jsx
		'.dops-search': 22,
		'#translator-launcher': 99,
		'.author-selector__popover.popover': 100,
		'.dops-search.is-pinned': 170,
		'.dops-select-dropdown.is-open .dops-select-dropdown__container': 170,
		'.dops-accessible-focus .dops-select-dropdown.is-open .dops-select-dropdown__container': 170,
		'.sites-dropdown.is-open .sites-dropdown__wrapper' : 170,
		'.popover.editor-visibility__popover': 179,
		'.feature-example__gradient': 179,
		'.global-notices': 179,
		'.notices-list.is-pinned': 180,
		'.notices-list.is-pinned .notice': 180,
		'.masterbar': 180,
		'.detail-page__backdrop': 190,
		'.layout__loader': 200,
		'.offline-status': 200,
		'.reader-post-images__full-list': 200,
		'.environment-badge': 999,
		'.customizer-loading-panel__muse-status': 999,
		'.customizer-loading-panel__placeholder-change-theme': 999,
		'.module-overlay': 1000,
		'.drop-zone': 1000,
		'.dops-popover': 1000,
		'.sharing-buttons-preview-buttons__more': 1000,
		'.sortable-list__item.is-draggable.is-active': 1000,
		'.dops-chart__tooltip': 1000,
		'.drop-zone__content': 1010,
		'.wp-overlay': 9999,
		'.dropdown-menu': 9999,
		'.main.customize.is-iframe': 9999,
		'.fullscreen-overlay': 100005,
		'#wp_editbtns': 100020,
		'#wp-fullscreen-body': 100010,
		'.wp-fullscreen-wrap': 100015,
		'#wp-fullscreen-statusbar': 100020,
		'#fullscreen-topbar': 100020,
		'.wp-fullscreen-active #TB_overlay': 100050,
		'.wp-fullscreen-active #TB_window': 100051,
		'div.mce-inline-toolbar-grp': 100100,
		'.dialog__backdrop': 100200,
		'.wplink__dialog.dialog.card': 100200,
		'.web-preview': 100200,
		'.category-selector__add-category-info': 100201,
		'.dops-popover.is-dialog-visible': 100300,
		'body .webui-popover': 100300,
		'.fullscreen-fader': 200000,
		'.guided-tours__overlay': 200050,
		'.guided-tours__step': 201000,
		'#habla_window_div.habla_window_div_base': 99999999 //olark
	),
	'.plan-compare-card__ribbon': (
		'.plan-compare-card__ribbon-title::before': -1,
		'.plan-compare-card__ribbon-title::after': -1
	),
	'.environment-badge': (
		'.environment-badge .environment::before': -1,
		'.environment-badge .bug-report': 1000
	),
	'.masterbar': (
		'.masterbar__notifications-bubble': 99999
	),
	'.detail-page__backdrop': (
		'.detail-page__action-buttons': 200
	),
	'.toolbar-bulk': (
		'.toolbar-bulk__check-all': 1,
		'.toolbar-bulk__selection-options': 1,
		'.toolbar-bulk__more-actions': 1,
		'.plugins .toolbar-bulk__toggle': 21,
		'.toolbar-bulk__toggle': 30
	),
	'.dops-popover': (
		'.input-chrono__container .gridicons-calendar': 0,
		'input.input-chrono': 1,
		'.dops-popover .dops-popover__arrow': 1,
		'.post-schedule__header': 1
	),
	'.dops-search': (
		'.dops-search__input': 10,
		'.dops-search.is-searching .dops-spinner': 20,
		'.dops-search .dops-search__open-icon': 20,
		'.dops-search .dops-search__close-icon': 20
	),
	'.profile-gravatar__edit-label-wrap': (
		'.profile-gravatar__edit-label-wrap:after': 0,
		'.profile-gravatar__edit-label': 1000
	),
	'.media-library__list-item': (
		'.media-library__list-item.is-selected::after': 10,
		'.media-library__list-item.is-transient .media-library__list-item-figure::after': 10,
		'.media-library__list-item-selected-icon .gridicon': 20,
		'.media-library__list-item-spinner': 20,
		'.media-library__list-item-edit': 20
	),
	'.dialog__backdrop': (
		'.editor-media-modal .section-nav': 10,
		'.editor-media-modal .notice': 10,
		'.editor-media-modal-gallery__preview-toggle': 100,
		'.editor-contact-form-modal .section-nav': 10
	),
	'.following-edit': ( //aka 'main'
		'.following-edit__subscribe-form .gridicons-add-outline': 23,
		'.following-edit__subscribe-form .card.is-search-result': 35
	),

	// The following may be inserted into different areas.
	// The parent stacking context may be root, or something else depending on where it is inserted.
	'icon-parent': (
		'.sidebar__menu .gridicon.gridicons-external': 1,
		'.sidebar__menu .noticon-external': 1
	),
	'screen-reader-text-parent': (
		'.screen-reader-text:focus': 100000
	),
	'button-group-parent': (
		'.button-group .button:focus': 1
	),
	'progress-indicator-parent': (
		'.progress-indicator__cancel.noticon:before': 1,
		'.progress-indicator .is-success': 2
	),
	'dops-section-nav-tabs__dropdown-parent': (
		'.dops-section-nav-tabs__dropdown': 3,
		'.dops-section-nav-tabs__dropdown.is-open': 4
	),
	'reader-card-follow-button-parent': (
		'.reader__card.card .follow-button': 1
	)
);

// allows us to do a nested fetch
@function map-deep-get( $map, $keys... ) {

	@each $key in $keys {

		@if not map.has-key( $map, $key) {

			@warn "No layer found for `#{$key}` of `[#{ $keys }]` in $z-layers map. Property omitted.";
			@return map.get( $map, $key );
		}
		$map: map.get( $map, $key );
	}

	@return $map;
}

@function z-index( $keys... ) {

	@return map-deep-get( $z-layers, $keys... );
}
