<?php
/**
 * Plugin Name: Ultimate Member - Stripe
 * Plugin URI: https://www.ultimatemember.com/extensions/stripe
 * Description: Integrates the popular payment processing platform Stripe with Ultimate Member
 * Version: 1.4.11
 * Author: Ultimate Member Group Ltd.
 * Author URI: https://www.ultimatemember.com
 * Text Domain: um-stripe
 * Domain Path: /languages
 * Requires at least: 5.5
 * Requires PHP: 7.4
 * UM version: 2.10.3
 * Requires Plugins: ultimate-member
 *
 * @package UM_Stripe
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

require_once ABSPATH . 'wp-admin/includes/plugin.php';
$plugin_data = get_plugin_data( __FILE__, true, false );

define( 'UM_STRIPE_URL', plugin_dir_url( __FILE__ ) );
define( 'UM_STRIPE_PATH', plugin_dir_path( __FILE__ ) );
define( 'UM_STRIPE_PLUGIN', plugin_basename( __FILE__ ) );
define( 'UM_STRIPE_EXTENSION', $plugin_data['Name'] );
define( 'UM_STRIPE_VERSION', $plugin_data['Version'] );
define( 'UM_STRIPE_TEXTDOMAIN', 'um-stripe' );
define( 'UM_STRIPE_REQUIRES', '2.10.3' );

/**
 * Load plugin languages.
 *
 * Loads the language files.
 *
 * @since 1.0.0
 */
function um_stripe_plugins_loaded() {
	$locale = ( get_locale() !== '' ) ? get_locale() : 'en_US';
	load_textdomain( UM_STRIPE_TEXTDOMAIN, WP_LANG_DIR . '/plugins/' . UM_STRIPE_TEXTDOMAIN . '-' . $locale . '.mo' );
	load_plugin_textdomain( UM_STRIPE_TEXTDOMAIN, false, dirname( plugin_basename( __FILE__ ) ) . '/languages/' );
}
add_action( 'init', 'um_stripe_plugins_loaded', 0 );

add_action( 'plugins_loaded', 'um_stripe_check_dependencies', -20 );

if ( ! function_exists( 'um_stripe_check_dependencies' ) ) {
	/**
	 * Check Core dependencies
	 *
	 * This handles the Ultimate Member core dependencies and validation of plugin requirements.
	 *
	 * @since 1.0.0
	 */
	function um_stripe_check_dependencies() {
		if ( ! defined( 'UM_PATH' ) || ! file_exists( UM_PATH . 'includes/class-dependencies.php' ) ) {
			/**
			 * Show error notice for missing core plugin
			 *
			 * @since 1.0.0
			 */
			function um_stripe_dependencies() {
				echo '<div class="error"><p>' .
				wp_kses(
					/* translators: 1: The %s extension requires the Ultimate Member plugin to be activated to work properly */
					sprintf( __( 'The <strong>%s</strong> extension requires the Ultimate Member plugin to be activated to work properly. You can download it <a href="https://wordpress.org/plugins/ultimate-member">here</a>', 'um-stripe' ), UM_STRIPE_EXTENSION ),
					array(
						'strong',
						'a' => array( 'href' ),
					)
				)
				. '</p></div>';
			}

			add_action( 'admin_notices', 'um_stripe_dependencies' );
		} else {

			if ( ! function_exists( 'UM' ) ) {
				require_once UM_PATH . 'includes/class-dependencies.php';
				$is_um_active = um\is_um_active();
			} else {
				$is_um_active = UM()->dependencies()->ultimatemember_active_check();
			}

			if ( ! $is_um_active ) {
				/**
				 * Show error notice for missing core plugin
				 *
				 * Displays the required dependencies in the admin screen.
				 *
				 * @since 1.0.0
				 */
				function um_stripe_dependencies() {
					echo '<div class="error"><p>' . wp_kses(
						sprintf(
						/* translators: 1: The %s extension requires the Ultimate Member plugin to be activated to work properly */
							__( 'The <strong>%s</strong> extension requires the Ultimate Member plugin to be activated to work properly. You can download it <a href="https://wordpress.org/plugins/ultimate-member">here</a>', 'um-stripe' ),
							UM_STRIPE_EXTENSION
						),
						array(
							'strong',
							'a' => array( 'href' ),
						)
					) . '</p></div>';
				}

				add_action( 'admin_notices', 'um_stripe_dependencies' );

			} elseif ( true !== UM()->dependencies()->compare_versions( UM_STRIPE_REQUIRES, UM_STRIPE_VERSION, 'stripe', UM_STRIPE_EXTENSION, true ) ) {
				/**
				 * Show error notice for older Plugin versions
				 *
				 * Displays the required version dependencies in the admin screen.
				 */
				function um_stripe_dependencies() {
					echo '<div class="error"><p>' .
					wp_kses(
						UM()->dependencies()->compare_versions( UM_STRIPE_REQUIRES, UM_STRIPE_VERSION, 'stripe', UM_STRIPE_EXTENSION ),
						array(
							'strong',
							'a' => array( 'href' ),
						)
					) . '</p></div>';
				}

				add_action( 'admin_notices', 'um_stripe_dependencies' );

				/**
				 * Extend license settings
				 *
				 * @param array $settings core and extension settings.
				 *
				 * @since 1.0.0
				 * @return array
				 */
				function um_stripe_extend_license_settings( $settings ) {
					$settings['licenses']['fields'][] = array(
						'id'        => 'um_stripe_license_key',
						'label'     => __( 'Stripe License Key', 'um-stripe' ),
						'item_name' => 'Stripe',
						'author'    => 'Ultimate Member',
						'version'   => UM_STRIPE_VERSION,
					);

					return $settings;
				}

				add_filter( 'um_settings_structure', 'um_stripe_extend_license_settings' );

			} elseif ( ! UM()->dependencies()->php_version_check( '7.4' ) ) {
				/**
				 * Show error notice for older PHP versions
				 *
				 * Displays the required PHP version in the admin screen.
				 *
				 * @since 1.0.0
				 */
				function um_stripe_dependencies() {
					echo '<div class="error"><p>' . wp_kses(
						sprintf(
								/* translators: 1: The %s extension requires 7.4 or better */
							__( 'The <strong>%s</strong> extension requires <strong>PHP 7.4 or better</strong> installed on your server.', 'um-stripe' ),
							UM_STRIPE_EXTENSION
						),
						array(
							'strong',
							'a' => array( 'href' ),
						)
					) . '</p></div>';
				}

				add_action( 'admin_notices', 'um_stripe_dependencies' );

			} else {

				/**
				 * Autoloader. We need it being separate and not using Composer autoloader because of the Stripe libs,
				 * which are huge and not needed for most users.
				 * Inspired by PSR-4 examples: https://github.com/php-fig/fig-standards/blob/master/accepted/PSR-4-autoloader-examples.md
				 *
				 * @since 1.0.0
				 *
				 * @param string $class The fully-qualified class name.
				 */
				$psr4_namespaces = require_once UM_STRIPE_PATH . '/vendor/composer/autoload_psr4.php';

				$psr4_keys = array_keys( $psr4_namespaces );

				spl_autoload_register(
					/**
					 * Closure of the autoloader.
					 *
					 * @param string $class_name The fully-qualified class name.
					 * @return void
					 */
					static function ( $class_name = '' ) use ( $psr4_keys, $psr4_namespaces ) {
						// Project namespace & length.
						$root_namespace    = 'UM_Stripe\\Vendor\\';
						$project_namespace = 'UM_Stripe\\Vendor\\';
						$length            = strlen( $project_namespace );
						$vendor_dir        = '';
						// Bail if class is not in this namespace.
						if ( 0 !== strncmp( $project_namespace, $class_name, $length ) ) {
							return;
						}
						$found_key = '';

						foreach ( $psr4_keys as $i => $ns ) {
							if ( empty( $class_name ) || empty( $ns ) ) {
								continue;
							}
							$class_name = str_replace( 'Symfony\\Component\\Intl\\', 'Symfony\\Intl\\', $class_name );
							$ns         = str_replace( 'Symfony\\Component\\Intl\\', 'Symfony\\Intl\\', $ns );

							if ( strpos( strtolower( $class_name ), strtolower( $ns ) ) > -1 && ! empty( $psr4_namespaces[ $psr4_keys[ $i ] ][0] ) ) {

								$found_key  = $ns;
								$vendor_dir = str_replace( $found_key, '', $psr4_namespaces[ $psr4_keys[ $i ] ][0] );

								$class_name = str_replace( 'Symfony\\Intl\\', '', $class_name );

								$name = str_replace( str_replace( '\\', '', $found_key ) . '/', '\\', str_replace( '\\', \DIRECTORY_SEPARATOR, str_replace( $root_namespace, '', $class_name ) ) );
								// Setup file parts.
								$format = $vendor_dir . '\\%1$s.php';

								// Parse class and namespace to file.
								$file = wp_normalize_path( sprintf( $format, $name ) );
								$file = str_replace( '/', DIRECTORY_SEPARATOR, $file );
								$file = str_replace( $found_key, '', $file );
								// Bail if file does not exist.
								if ( file_exists( $file ) ) {
									require_once $file;
									break;
								}
							}
						}
					}
				);

				require_once UM_STRIPE_PATH . 'class-functions.php';
				require_once UM_STRIPE_PATH . 'class-um-stripe-api.php';
			}
		}
	}
}

/**
 * Plugin Extras
 *
 * @since 1.0.0
*/
require_once UM_STRIPE_PATH . 'includes/admin/class-um-stripe-plugin.php';
new UM_Stripe_Plugin();

/**
 * Activation hook
 *
 * Handles the processes on plugin activation.
 *
 * @since 1.0.0
 */
function um_stripe_activation_hook() {
	// run setup.
	if ( ! class_exists( 'um_ext\um_stripe\Install' ) ) {
		require_once UM_STRIPE_PATH . 'class-install.php';
	}

	$um_stripe_setup = new um_ext\um_stripe\Install();
	$um_stripe_setup->start();

	// first install.
	$version = get_option( 'um_stripe_version' );
	if ( ! $version ) {
		update_option( 'um_stripe_last_version_upgrade', UM_STRIPE_VERSION );
	}

	if ( UM_STRIPE_VERSION !== $version ) {
		update_option( 'um_stripe_version', UM_STRIPE_VERSION );
	}
}
register_activation_hook( UM_STRIPE_PLUGIN, 'um_stripe_activation_hook' );
