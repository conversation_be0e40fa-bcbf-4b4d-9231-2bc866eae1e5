window.addEventListener("message",(function(e){if(-1===["https://videopress.com","https://video.wordpress.com"].indexOf(e.origin))return;if("videopress_token_request"!==e.data.event)return;if(!window.videopressAjax)return;e.source.postMessage({event:"videopress_token_request_ack",guid:e.data.guid,requestId:e.data.requestId},"*");const s={action:"videopress-get-playback-jwt",guid:e.data.guid,post_id:window.videopressAjax.post_id||0};fetch(window.videopressAjax.ajaxUrl,{method:"POST",credentials:"same-origin",body:new URLSearchParams(s)}).then((function(e){if(e.ok)return e.json();throw Error("Response is not ok")})).then((function(t){t.success&&t.data?e.source.postMessage({event:"videopress_token_received",guid:s.guid,jwt:t.data.jwt,requestId:e.data.requestId},"*"):e.source.postMessage({event:"videopress_token_error",guid:s.guid,requestId:e.data.requestId},"*")}))}));