import*as e from"@wordpress/interactivity";var t={889:(e,t,n)=>{n.d(t,{S_:()=>r});const{__:__}=wp.i18n,r=(__("Warning.","jetpack-forms"),(e,t)=>{e.removeAttribute("aria-invalid"),e.removeAttribute("aria-describedby");const n=e.closest(t.hasInsetLabel?".contact-form__inset-label-wrap":".grunion-field-wrap");if(!n)return;const r=n.querySelector(".contact-form__input-error");r&&r.remove();const o=e.closest("form"),i=o.querySelectorAll(".contact-form__input-error"),a=o.querySelector(".contact-form__error");a&&0===i.length&&a.remove()})},833:(t,n,r)=>{t.exports=(e=>{var t={};return r.d(t,e),t})({getConfig:()=>e.getConfig,getContext:()=>e.getContext,getElement:()=>e.getElement,store:()=>e.store,withScope:()=>e.withScope})}},n={};function r(e){var o=n[e];if(void 0!==o)return o.exports;var i=n[e]={exports:{}};return t[e](i,i.exports,r),i.exports}r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var o=r(833),i=r(889);const a="jetpack/field-file";let s=null,l=null;const c=async()=>{if(s&&l&&Date.now()<l)return s;const{token:e,expiresAt:t}=await d();return s=e,l=1e3*t,s},d=async()=>{const{endpoint:e}=(0,o.getConfig)(a),t={token:null,expiresAt:0};try{const n=await fetch(`${e}/token`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({context:"file-upload"})});if(!n.ok)return t;const r=await n.json();return{token:r.token,expiresAt:r.expiration}}catch(e){if(e)return t}return t},f=(e,t=2)=>{const n=(0,o.getConfig)(a);if(0===e)return n.i18n.zeroBytes;const r=t<0?0:t,i=n.i18n.fileSizeUnits||["Bytes","KB","MB","GB","TB"],s=Math.floor(Math.log(e)/Math.log(1024)),l=parseFloat((e/Math.pow(1024,s)).toFixed(r));return`${new Intl.NumberFormat(n.i18n.locale,{minimumFractionDigits:r,maximumFractionDigits:r}).format(l)} ${i[s]}`},p=e=>{const t=new FileReader;t.readAsDataURL(e);const{ref:n}=(0,o.getElement)();(0,i.S_)(n,{hasInsetLabel:y.isInlineForm});const r=(0,o.getConfig)(a),s=(0,o.getContext)();let l=null;e.size>r.maxUploadSize&&(l=r.i18n.fileTooLarge),s.allowedMimeTypes.includes(e.type)||(l=r.i18n.invalidType);const c=s.files.filter((e=>!e.error));s.maxFiles<c.length+1&&(l=r.i18n.maxFiles);const d=performance.now()+"-"+Math.random();s.files.push({name:e.name,formattedSize:f(e.size,2),isUploaded:!1,hasError:!!l,id:d,error:l}),!l&&x.uploadFile(e,d),t.onload=(0,o.withScope)((()=>{h({url:"url("+t.result+")"},d)}))},g=new Map,m=(e,t)=>{const n=t.loaded/t.total*100;h({progress:Math.min(n,97)},e)},u=(e,t)=>{const n=t.target;if(4===n.readyState){if(200!==n.status){const t=(0,o.getConfig)(a);return void h({error:t.i18n.uploadFailed,hasError:!0},e)}{const t=JSON.parse(n.responseText);if(t.success)return void h({file_id:t.data.file_id,isUploaded:!0,name:t.data.name,type:t.data.type,size:t.data.size,fileJson:JSON.stringify({file_id:t.data.file_id,name:t.data.name,size:t.data.size,type:t.data.type})},e)}if(n.responseText){const t=JSON.parse(n.responseText);h({error:t.message,hasError:!0},e)}}},h=(e,t)=>{const n=(0,o.getContext)(),r=n.files.findIndex((e=>e.id===t));n.files[r]=Object.assign(n.files[r],e)},{state:y,actions:x}=(0,o.store)(a,{state:{get isInlineForm(){const{ref:e}=(0,o.getElement)(),t=e.closest(".wp-block-jetpack-contact-form");return t&&t.classList.contains("is-style-outlined")||t.classList.contains("is-style-animated")},get hasFiles(){return!!(0,o.getContext)().files.length>0},get hasMaxFiles(){const e=(0,o.getContext)();return e.maxFiles<=e.files.length}},actions:{openFilePicker(){const{ref:e}=(0,o.getElement)(),t=e.parentNode.querySelector(".jetpack-form-file-field");t&&(t.value="",t.click())},fileAdded(e){Array.from(e.target.files).forEach(p)},fileDropped:e=>{if(e.preventDefault(),e.dataTransfer)for(const t of Array.from(e.dataTransfer.items)){if(t.webkitGetAsEntry()?.isDirectory)return;p(t.getAsFile())}(0,o.getContext)().isDropping=!1},dragOver:e=>{(0,o.getContext)().isDropping=!0,e.preventDefault()},dragLeave:()=>{(0,o.getContext)().isDropping=!1},uploadFile:function*(e,t){const{endpoint:n,i18n:r}=(0,o.getConfig)(a),i=yield c();if(!i)return void h({error:r.uploadFailed,hasError:!0},t);const s=new XMLHttpRequest,l=new FormData,d=new AbortController;g.set(t,d),s.open("POST",n,!0),s.upload.addEventListener("progress",(0,o.withScope)(m.bind(this,t))),s.addEventListener("readystatechange",(0,o.withScope)(u.bind(this,t))),d.signal.addEventListener("abort",(()=>{s.abort()})),l.append("file",e),l.append("token",i),s.send(l)},removeFile:function*(e){e.preventDefault();const{ref:t}=(0,o.getElement)(),n=t.closest(".jetpack-form-file-field__container");(0,i.S_)(n,{hasInsetLabel:y.isInlineForm});const r=(0,o.getContext)(),s=e.target.dataset.id;if(g.has(s)){g.get(s).abort(),g.delete(s)}const l=r.files.find((e=>e.id===s));if(l&&l.file_id){const{endpoint:e}=(0,o.getConfig)(a),t=yield c();if(t){const n=new FormData;n.append("token",t),n.append("file_id",l.file_id),fetch(`${e}/remove`,{method:"POST",body:n})}}r.files=r.files.filter((e=>e.id!==s))}},callbacks:{}});