<?php
/**
 * Template for the view note
 *
 * Call:  view();
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-notes/load-more.php
 *
 * @see      https://docs.ultimatemember.com/article/1516-templates-map
 * @package  um_ext\um_user_notes\templates
 * @version  1.1.4
 * @var string $image
 * @var string $title
 * @var string $profile_link
 * @var string $avatar
 * @var string $post_date
 * @var string $author_name
 * @var string $content
 * @var int    $id
 * @var array  $buttons_options
 * @var array  $download_options
 * @var array  $print_options
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
$container_css = '';
$actions_css   = '';
if ( ! empty( $buttons_options ) && ! empty( $buttons_options['container'] ) ) {
	$container_css = $buttons_options['container'];
}
if ( ! empty( $buttons_options ) && ! empty( $buttons_options['actions'] ) ) {
	$actions_css = $buttons_options['actions'];
}
?>

<?php if ( ! empty( $buttons_options ) ) { ?>
	<div class="um-notes-action-buttons" style="<?php echo esc_attr( $buttons_options['position'] ); ?>">
		<?php if ( ! empty( $download_options ) ) { ?>
			<button id="um_user_notes_download" data-nonce="<?php echo esc_attr( wp_create_nonce( 'um_user_notes_download' ) ); ?>"  type="button" class="button um-modal-btn alt">
				<?php echo wp_kses( $download_options['title'], UM()->get_allowed_html( 'templates' ) ); ?>
			</button>
		<?php } ?>
		<?php if ( ! empty( $print_options ) ) { ?>
			<button id="um_user_notes_print" data-nonce="<?php echo esc_attr( wp_create_nonce( 'um_user_notes_print' ) ); ?>"  type="button" class="button um-modal-btn alt">
				<?php echo wp_kses( $print_options['title'], UM()->get_allowed_html( 'templates' ) ); ?>
			</button>
		<?php } ?>
	</div>
<?php } ?>

<div class="um-note-content" data-id="<?php echo esc_attr( $id ); ?>" style="<?php echo esc_attr( $container_css ); ?>">
	<?php
	if ( $image ) {
		$image_css = '';
		// inline CSS for download file
		if ( ! empty( $download_options ) ) {
			$image_css = 'max-width: 100%; width: auto;';
		}
		?>
		<div class="note-image">
			<img style="<?php echo esc_attr( $image_css ); ?>" src="<?php echo esc_attr( $image ); ?>" />
		</div>
	<?php } ?>
	<h1><?php echo esc_html( $title ); ?></h1>

	<span class="um_notes_author_date">

		<a class="um_notes_author_profile_link" href="<?php echo esc_url( $profile_link ); ?>">
			<?php echo wp_kses( $avatar, UM()->get_allowed_html( 'templates' ) ); ?>
			<?php echo esc_html( $author_name ); ?>
		</a>
		&bull;
		<?php echo esc_html( $post_date ); ?>
	</span>

	<div>
		<?php echo wp_kses( $content, UM()->get_allowed_html( 'templates' ) ); ?>
	</div>
</div>

<?php if ( um_is_profile_owner() && UM()->Notes()->is_note_author( $id ) ) { ?>
	<p style="text-align:right;<?php echo esc_attr( $actions_css ); ?>">
		<button class="um_notes_edit_note um-modal-btn" type="button"
				data-id="<?php echo esc_attr( $id ); ?>"
				data-nonce="<?php echo esc_attr( wp_create_nonce( 'um_user_notes_edit' ) ); ?>">
			<?php esc_html_e( 'Edit', 'um-user-notes' ); ?>
		</button>

		<button class="um_notes_delete_note um-modal-btn alt" type="button"
				data-id="<?php echo esc_attr( $id ); ?>"
				data-nonce="<?php echo esc_attr( wp_create_nonce( 'um_user_notes_delete' ) ); ?>">
			<?php esc_html_e( 'Delete', 'um-user-notes' ); ?>
		</button>
	</p>
	<?php
} else {
	if ( '' !== $actions_css ) {
		?>
		<div style="<?php echo esc_attr( $actions_css ); ?>"></div>
		<?php
	}
}
