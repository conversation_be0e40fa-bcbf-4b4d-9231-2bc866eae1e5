<?php
/**
 * Template for the note content
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-notes/profile/single-note.php
 *
 * @see        https://docs.ultimatemember.com/article/1516-templates-map
 * @package    um_ext\um_user_notes\templates
 * @version    1.1.4
 *  @var string $image
 *  @var string $title
 *  @var string $profile_link
 *  @var string $avatar
 *  @var string $post_date
 *  @var string $author_name
 *  @var string $content
 *  @var int    $note_id
 *  @var string $back_link
 *  @var bool   $hide
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
if ( 1 === absint( $hide ) ) {
	echo esc_html__( 'You do not have permission to view this note.', 'um-user-notes' );
} else { ?>
	<div class="um-note-content" data-id="<?php echo esc_attr( $note_id ); ?>">
		<a class="um-user-notes-back" href="<?php echo esc_url( $back_link ); ?>"><span class="um-icon-arrow-left-c"></span> <?php echo esc_html__( 'Back to notes', 'um-user-notes' ); ?></a>
		<?php if ( $image ) { ?>
			<div class="note-image">
				<img src="<?php echo esc_attr( $image ); ?>" />
			</div>
		<?php } ?>
		<h1><?php echo esc_html( $title ); ?></h1>

		<span class="um_notes_author_date">

			<a class="um_notes_author_profile_link" href="<?php echo esc_url( $profile_link ); ?>">
				<?php echo wp_kses( $avatar, UM()->get_allowed_html( 'templates' ) ); ?>
				<?php echo esc_html( $author_name ); ?>
			</a>
			&bull;
			<?php echo esc_html( $post_date ); ?>
		</span>

		<div>
			<?php echo wp_kses( $content, UM()->get_allowed_html( 'templates' ) ); ?>
		</div>
	</div>
	<?php
}
