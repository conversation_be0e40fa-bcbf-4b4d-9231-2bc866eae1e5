<?php
namespace um_ext\um_user_photos\common;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Integration with UM social activity
 * @todo how it works and make some fixes. blocked by new UI for activity.
 * Class User_Photos_Activity
 * @package um_ext\um_user_photos\common
 */
class Activity {

	/**
	 * Activity constructor.
	 */
	public function __construct() {

		add_action( 'um_user_photos_after_album_created', array( $this, 'um_social_activity_post' ) );

		add_action( 'um_user_photos_before_photo_deleted', array( $this, 'um_user_photos_before_photo_deleted' ), 10, 2 );

		add_action( 'um_user_photos_after_album_updated', array( $this, 'um_user_photos_after_album_updated' ) );

		add_action( 'um_user_photos_before_album_deleted', array( $this, 'um_social_activity_post_delete' ) );

		add_action( 'um_user_photos_after_user_albums_deleted', array( $this, 'um_user_photos_after_user_albums_deleted' ) );

		add_filter( 'um_activity_wall_args', array( $this, 'filter_social_activity' ), 35, 2 );

		add_filter( 'um_activity_comment_error', array( $this, 'um_activity_comment_error' ), 10, 2 );
		add_filter( 'um_activity_post_can_view', array( $this, 'activity_post_can_view' ), 10, 2 );
	}

	/**
	 * @param int $activity
	 * @param int $album_id
	 */
	public function update_activity_privacy( $activity, $album_id ) {
		$privacy = get_post_meta( $album_id, '_privacy', true );
		$privacy = ! empty( $privacy ) ? $privacy : 'everyone';

		update_post_meta( $activity, 'um_user_photos_privacy', $privacy );
	}

	/**
	 * Build activity template
	 *
	 * @param $album_id
	 * @param array $exclude
	 *
	 * @return string
	 */
	public function activity_template( $album_id, $exclude = array() ) {
		$photos = get_post_meta( $album_id, '_photos', true );

		if ( empty( $photos ) ) {
			$photos = array();
		}

		if ( has_post_thumbnail( $album_id ) ) {
			$photos[] = get_post_thumbnail_id( $album_id );
		}

		if ( ! is_array( $photos ) || 0 === count( $photos ) ) {
			return '';
		}

		$photos = array_unique( $photos );

		if ( ! empty( $exclude ) ) {
			$photos = array_diff( $photos, $exclude );
			sort( $photos );
		}

		$count_photos = count( $photos );
		ob_start();

		for ( $i = 0; $i < $count_photos; $i++ ) {
			$thumbnail  = wp_get_attachment_image_src( $photos[ $i ], 'large' );
			$attachment = get_post( $photos[ $i ] );
			?>
			<img style="max-width:100px;" data-author="<?php echo esc_attr( $attachment->post_author ); ?>" id="user_photo-<?php echo esc_attr( $photos[ $i ] ); ?>" title="<?php echo esc_attr( $attachment->post_title ); ?>" data-parent="<?php echo esc_attr( $attachment->post_parent ); ?>" src="<?php echo esc_url( $thumbnail[0] ); ?>" />
			<?php
		}
		$album_photos = ob_get_clean();
		return trim( $album_photos );
	}

	/**
	 * Get album's cover image
	 *
	 * @param $album_id
	 *
	 * @return string
	 */
	public function get_album_cover( $album_id ) {
		$cover_image = UM()->User_Photos()->common()->album()->get_cover( $album_id );
		ob_start();
		?>
		<span data-album="<?php esc_attr( $album_id ); ?>" class="post-image"><img src="<?php echo esc_attr( $cover_image ); ?>" class="um-activity-featured-img" alt="" title="" /></span>
		<?php
		$cover_image = ob_get_clean();

		return trim( $cover_image );
	}

	/**
	 * Create social activity when new album is created
	 *
	 * @param int $album_id
	 */
	public function um_social_activity_post( $album_id ) {
		$album   = get_post( $album_id );
		$user_id = $album->post_author;

		um_fetch_user( $user_id );
		$author_name    = um_user( 'display_name' );
		$author_profile = um_user_profile_url();
		$album_link     = add_query_arg( array( 'profiletab' => 'photos' ), $author_profile );

		$cover_image  = $this->get_album_cover( $album_id );
		$album_photos = $this->activity_template( $album_id );

		$file       = UM_USER_PHOTOS_PATH . '/templates/social-activity/new-album.php';
		$theme_file = get_stylesheet_directory() . '/ultimate-member/um-user-photos/social-activity/new-album.php';
		if ( file_exists( $theme_file ) ) {
			$file = $theme_file;
		}

		$activity = UM()->Activity_API()->api()->save(
			array(
				'template'       => 'new-album',
				'custom_path'    => $file,
				'wall_id'        => $user_id,
				'related_id'     => $album_id,
				'author'         => $user_id,
				'author_name'    => $author_name,
				'author_profile' => $author_profile,
				'post_title'     => $album->post_title,
				'post_url'       => $album_link,
				'post_excerpt'   => $album_photos,
				'post_image'     => $album_id,
			)
		);

		$this->update_activity_privacy( $activity, $album_id );
	}

	/**
	 * Update social activity content when photo in album is removed
	 *
	 * @param int $image_id
	 * @param int $album_id
	 */
	public function um_user_photos_before_photo_deleted( $image_id, $album_id ) {
		$activities = get_posts(
			array(
				'post_type'  => 'um_activity',
				'meta_query' => array(
					array(
						'key'     => '_related_id',
						'value'   => $album_id,
						'compare' => '=',
					),
					array(
						'key'     => '_action',
						'value'   => 'new-album',
						'compare' => '=',
					),
				),
			)
		);

		if ( empty( $activities ) ) {
			return;
		}

		foreach ( $activities as $post ) {

			setup_postdata( $post );

			$album   = get_post( $album_id );
			$user_id = $album->post_author;

			um_fetch_user( $user_id );
			$author_name    = um_user( 'display_name' );
			$author_profile = um_user_profile_url();
			$album_link     = add_query_arg( array( 'profiletab' => 'photos' ), $author_profile );

			$cover_image  = $this->get_album_cover( $album_id );
			$album_photos = $this->activity_template( $album_id, array( intval( $image_id ) ) );

			$file       = UM_USER_PHOTOS_PATH . '/templates/social-activity/new-album.php';
			$theme_file = get_stylesheet_directory() . '/ultimate-member/um-user-photos/social-activity/new-album.php';
			if ( file_exists( $theme_file ) ) {
				$file = $theme_file;
			}

			UM()->Activity_API()->api()->save(
				array(
					'template'       => 'new-album',
					'custom_path'    => $file,
					'wall_id'        => $user_id,
					'related_id'     => $album_id,
					'author'         => $user_id,
					'author_name'    => $author_name,
					'author_profile' => $author_profile,
					'post_title'     => $album->post_title,
					'post_url'       => $album_link,
					'post_excerpt'   => $album_photos,
					'post_image'     => $album_id,
				),
				true,
				$post->ID
			);
		}
		wp_reset_postdata();
	}

	/**
	 * Update social activity when user updated album
	 *
	 * @param $album_id
	 */
	public function um_user_photos_after_album_updated( $album_id ) {
		$activities = get_posts(
			array(
				'post_type'  => 'um_activity',
				'meta_query' => array(
					array(
						'key'     => '_related_id',
						'value'   => $album_id,
						'compare' => '=',
					),
					array(
						'key'     => '_action',
						'value'   => 'new-album',
						'compare' => '=',
					),
				),
			)
		);

		if ( empty( $activities ) ) {
			return;
		}

		foreach ( $activities as $post ) {
			setup_postdata( $post );

			$album   = get_post( $album_id );
			$user_id = $album->post_author;

			um_fetch_user( $user_id );
			$author_name    = um_user( 'display_name' );
			$author_profile = um_user_profile_url();
			$album_link     = add_query_arg( array( 'profiletab' => 'photos' ), $author_profile );

			$cover_image  = $this->get_album_cover( $album_id );
			$album_photos = $this->activity_template( $album_id );

			$file       = UM_USER_PHOTOS_PATH . '/templates/social-activity/new-album.php';
			$theme_file = get_stylesheet_directory() . '/ultimate-member/um-user-photos/social-activity/new-album.php';
			if ( file_exists( $theme_file ) ) {
				$file = $theme_file;
			}

			$activity = UM()->Activity_API()->api()->save(
				array(
					'template'       => 'new-album',
					'custom_path'    => $file,
					'wall_id'        => $user_id,
					'related_id'     => $album_id,
					'author'         => $user_id,
					'author_name'    => $author_name,
					'author_profile' => $author_profile,
					'post_title'     => $album->post_title,
					'post_url'       => $album_link,
					'post_excerpt'   => $album_photos,
					'post_image'     => $album_id,
				),
				true,
				$post->ID
			);

			$this->update_activity_privacy( $activity, $album_id );

		}
		wp_reset_postdata();
	}

	/**
	 * Delete social activity when album is deleted
	 *
	 * @param int $album_id
	 */
	public function um_social_activity_post_delete( $album_id = 0 ) {
		if ( ! $album_id ) {
			return;
		}

		$activities = get_posts(
			array(
				'post_type'  => 'um_activity',
				'meta_query' => array(
					array(
						'key'     => '_related_id',
						'value'   => $album_id,
						'compare' => '=',
					),
					array(
						'key'     => '_action',
						'value'   => 'new-album',
						'compare' => '=',
					),
				),
			)
		);

		if ( empty( $activities ) ) {
			return;
		}

		foreach ( $activities as $post ) {
			wp_delete_post( $post->ID, true );
		}
	}

	/**
	 * Delete all album related social activity when user deleted all photos & albums
	 *
	 * @param int $user_id
	 */
	public function um_user_photos_after_user_albums_deleted( $user_id ) {
		$activities = get_posts(
			array(
				'post_type'  => 'um_activity',
				'author'     => $user_id,
				'meta_query' => array(
					array(
						'key'     => '_action',
						'value'   => 'new-album',
						'compare' => '=',
					),
				),
			)
		);

		if ( empty( $activities ) ) {
			return;
		}

		foreach ( $activities as $post ) {
			wp_delete_post( $post->ID, true );
		}
	}

	/**
	 * Exclude user photos from wall if user does not have access to view album
	 *
	 * @version 1.0.3
	 *
	 * @param array    $args
	 * @param int|null $wall_id
	 *
	 * @return array
	 */
	public function filter_social_activity( $args, $wall_id ) {
		global $wpdb;

		$user_id = null;
		if ( is_user_logged_in() ) {
			$user_id = get_current_user_id();

			if ( absint( $wall_id ) === $user_id ) {
				// Keep WP_Posts query simple if we are on the own profile's activity wall.
				return $args;
			}
		}

		$metarows = $wpdb->get_col(
			"SELECT DISTINCT pm.meta_value
			FROM {$wpdb->postmeta} pm
			LEFT JOIN {$wpdb->posts} p ON p.ID = pm.post_id
			WHERE p.post_type = 'um_activity' AND
				  pm.meta_key='um_user_photos_privacy'"
		);

		if ( empty( $metarows ) || ( 1 === count( $metarows ) && in_array( 'everyone', $metarows, true ) ) ) {
			// Keep WP_Posts query simple if there are only 'everyone' privacy or not `um_user_photos_privacy` postmeta
			return $args;
		}

		if ( empty( $args['meta_query'] ) ) {
			$args['meta_query'] = array( 'relation' => 'AND' );
		}

		$query = array(
			'relation' => 'OR',
			array(
				'key'     => 'um_user_photos_privacy',
				'compare' => 'NOT EXISTS',
			),
		);

		// Activity wall on the user profile.
		if ( ! empty( $wall_id ) ) {
			$privacy_array = array();
			if ( in_array( 'everyone', $metarows, true ) ) {
				$privacy_array[] = 'everyone';
			}

			$privacy_array = apply_filters( 'um_user_photos_activity_query_privacy_array', $privacy_array, $user_id, $wall_id, $metarows, $args );

			$query[] = array(
				'key'     => 'um_user_photos_privacy',
				'value'   => $privacy_array,
				'compare' => 'IN',
			);
		} else {
			if ( in_array( 'everyone', $metarows, true ) ) {
				$query[] = array(
					'key'     => 'um_user_photos_privacy',
					'value'   => 'everyone',
					'compare' => '=',
				);
			}
		}

		if ( in_array( 'only_me', $metarows, true ) && is_user_logged_in() ) {
			$query[] = array(
				'relation' => 'AND',
				array(
					'key'     => 'um_user_photos_privacy',
					'value'   => 'only_me',
					'compare' => '=',
				),
				array(
					'key'     => '_wall_id',
					'value'   => $user_id,
					'compare' => '=',
				),
			);
		}

		$query = apply_filters( 'um_user_photos_activity_query_privacy', $query, $user_id, $wall_id, $metarows, $args );

		$args['meta_query'][] = $query;

		return $args;
	}

	public function um_activity_comment_error( $error, $post_id ) {
		$album_id = get_post_meta( $post_id, '_related_id', true );
		if ( ! empty( $album_id ) && ! UM()->User_Photos()->common()->user()->can_view_album( $album_id ) ) {
			$error = __( 'You are not authorized for this.', 'um-user-photos' );
		}

		return $error;
	}

	public function activity_post_can_view( $can_view, $post_id ) {
		if ( ! $can_view ) {
			return $can_view;
		}

		$photos_privacy = get_post_meta( $post_id, 'um_user_photos_privacy', true );
		if ( empty( $photos_privacy ) || 'everyone' === $photos_privacy ) {
			return $can_view;
		}

		if ( 'only_me' === $photos_privacy ) {
			$wall_id = UM()->Activity_API()->api()->get_wall( $post_id );
			if ( ! is_user_logged_in() || absint( $wall_id ) !== get_current_user_id() ) {
				return false;
			}
		}

		return apply_filters( 'um_user_photos_activity_post_can_view', $can_view, $post_id );
	}
}
