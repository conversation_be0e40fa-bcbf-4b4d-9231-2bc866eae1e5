@echo off
echo ===== TESTING RAEZG.LOCAL CONNECTION =====
echo.

echo 1. Testing ping to raezg.local...
ping -n 2 raezg.local
echo.

echo 2. Testing telnet connection to raezg.local port 80...
echo This will test if the TCP connection works...
powershell -Command "try { $tcpClient = New-Object System.Net.Sockets.TcpClient; $tcpClient.Connect('raezg.local', 80); $tcpClient.Connected; $tcpClient.Close() } catch { 'Connection failed: ' + $_.Exception.Message }"
echo.

echo 3. Testing telnet connection to 127.0.0.1 port 80...
powershell -Command "try { $tcpClient = New-Object System.Net.Sockets.TcpClient; $tcpClient.Connect('127.0.0.1', 80); $tcpClient.Connected; $tcpClient.Close() } catch { 'Connection failed: ' + $_.Exception.Message }"
echo.

echo 4. Testing HTTP request to raezg.local with detailed error...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://raezg.local' -TimeoutSec 10 -UseBasicParsing; Write-Host 'Success: HTTP' $response.StatusCode } catch { Write-Host 'Failed:' $_.Exception.GetType().Name ':' $_.Exception.Message }"
echo.

echo 5. Testing HTTP request to 127.0.0.1/raezg/...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://127.0.0.1/raezg/' -TimeoutSec 10 -UseBasicParsing; Write-Host 'Success: HTTP' $response.StatusCode } catch { Write-Host 'Failed:' $_.Exception.GetType().Name ':' $_.Exception.Message }"
echo.

echo 6. Testing with Host header manually...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://127.0.0.1' -Headers @{'Host'='raezg.local'} -TimeoutSec 10 -UseBasicParsing; Write-Host 'Success: HTTP' $response.StatusCode } catch { Write-Host 'Failed:' $_.Exception.GetType().Name ':' $_.Exception.Message }"
echo.

echo 7. Checking current Apache VirtualHost status...
C:\xampp\apache\bin\httpd.exe -S
echo.

pause
