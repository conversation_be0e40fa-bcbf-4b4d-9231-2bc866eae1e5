{"translation-revision-date": "2024-12-16 16:53:45+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "Rename": ["<PERSON><PERSON>"], "Uploading JSON files from unknown sources can be harmful and put your site at risk. For maximum safety, only install JSON files from trusted sources.": ["Uploading JSON files from unknown sources can be harmful and put your site at risk. For maximum safety, only install JSON files from trusted sources."], "Warning: JSON files may be unsafe": ["Warning: JSON files may be unsafe"], "Clear gallery": ["Clear gallery"], "Paste style": ["Paste style"], "Do not show this message again": ["Do not show this message again"], "An error occurred.": ["An error occurred."], "Selected": ["Selected"], "Decide Later": ["Decide Later"], "New options for \"Exit to...\"": ["New options for \"Exit to...\""], "Item #%d": ["Item #%d"], "Upgrade Now": ["Upgrade Now"], "Upgrade": ["Upgrade"], "Direction Row": ["Direction Row"], "Direction Column": ["Direction Column"], "Don’t apply": ["Don’t apply"], "This will override the design, layout, and other settings of the %s you’re working on.": ["This will override the design, layout, and other settings of the %s you’re working on."], "Apply the settings of this %s too?": ["Apply the settings of this %s too?"], "If you do not enable uploading unfiltered files, any SVG or JSON (including lottie) files used in the uploaded template will not be imported.": ["If you do not enable uploading unfiltered files, any SVG or JSON (including lottie) files used in the uploaded template will not be imported."], "Import Without Enabling": ["Import Without Enabling"], "Enable and Import": ["Enable and Import"], "Container": ["Container"], "Add New Container": ["Add New Container"], "Delete %d items": ["Delete %d items"], "That didn’t work. Try duplicating one kind of element at a time.": ["That didn’t work. Try duplicating one kind of element at a time."], "That didn’t work. Try copying one kind of element at a time.": ["That didn’t work. Try copying one kind of element at a time."], "Add to Favorites": ["Add to Favourites"], "Remove from Favorites": ["Remove from Favourites"], "Connect & Activate": ["Connect & Activate"], "Reload Now": ["Reload Now"], "You have made modifications to the list of Active Breakpoints. For these changes to take effect, you need to reload Elementor Editor.": ["You have made modifications to the list of Active Breakpoints. For these changes to take effect, you need to reload Elementor Editor."], "Reload Elementor Editor": ["Reload Elementor Editor"], "Your site doesn't have a default kit": ["Your site doesn't have a default kit"], "Got it": ["Got it"], "Select a color from any image, or from an element whose color you've manually defined.": ["Select a colour from any image, or from an element whose color you've manually defined."], "Seems like your kit was deleted, please create new one or try restore it from trash.": ["Seems like your kit was deleted, please create new one or try restore it from bin."], "Imported": ["Imported"], "Color Sampler": ["Colour Sampler"], "Recreate Kit": ["Recreate Kit"], "Please try again.": ["Please try again."], "Learn more": ["Learn more"], "color": ["colour"], "font": ["font"], "Font": ["Font"], "Would you like to exit?": ["Would you like to exit?"], "Item #%s": ["Item #%s"], "Cannot load editor": ["Cannot load editor"], "Would you like to save the changes you've made?": ["Would you like to save the changes you've made?"], "Save Changes": ["Save Changes"], "Item": ["<PERSON><PERSON>"], "Invalid Global Color": ["Invalid global colour"], "Delete Global %s": ["Delete global %s"], "Landing Pages": ["Landing pages"], "Global Colors": ["Global Colours"], "Add Color": ["Add Colour"], "Add Style": ["Add Style"], "Additional Settings": ["Additional Settings"], "Are you sure you want to create a new Global Color?": ["Are you sure you want to create a new Global Colour?"], "Are you sure you want to create a new Global Font setting?": ["Are you sure you want to create a new Global Font setting?"], "Create New Global Color": ["Create New Global Colour"], "Create New Global Font": ["Create New Global Font"], "Design System": ["Design System"], "Global Colors help you work smarter. Save a color, and use it anywhere throughout your site. Access and edit your global colors by clicking the Manage button.": ["Global Colours help you work smarter. Save a colour, and use it anywhere throughout your site. Access and edit your global colours by clicking the Manage button."], "Global Fonts help you work smarter. Save a Typography, and use it anywhere throughout your site. Access and edit your Global Fonts by clicking the Manage button.": ["Global Fonts help you work smarter. Save a Typography, and use it anywhere throughout your site. Access and edit your Global Fonts by clicking the Manage button."], "Manage Global Colors": ["Manage Global Colours"], "Manage Global Fonts": ["Manage Global Fonts"], "Navigate From Page": ["Navigate From Page"], "New Global Color": ["New Global Colour"], "New Item": ["New Item"], "New Typography Setting": ["New Typography Setting"], "Site Settings": ["Site Settings"], "User Preferences": ["User Preferences"], "Your changes have been updated.": ["Your changes have been updated."], "Please note that the same exact color already exists in your Global Colors list. Are you sure you want to create it?": ["Please note that the same exact colour already exists in your Global Colours list. Are you sure you want to create it?"], "System %s can't be deleted": ["System %s can't be deleted"], "You're about to delete a Global %1$s. Note that if it's being used anywhere on your site, it will inherit a default %1$s.": ["You're about to delete a Global %1$s. Note that if it's being used anywhere on your site, it will inherit a default %1$s."], "Enable Unfiltered File Uploads": ["Enable Unfiltered File Uploads"], "Back": ["Back"], "Create more personalized and dynamic sites by populating data from various sources with dozens of dynamic tags to choose from.": ["Create more personalised and dynamic sites by populating data from various sources with dozens of dynamic tags to choose from."], "Dynamic Content": ["Dynamic Content"], "Settings Reset": ["Settings Reset"], "Theme Style": ["Theme Style"], "Enabled": ["Enabled"], "Pasted": ["Pasted"], "More": ["More"], "Access this template and our entire library by creating a free personal account": ["Access this template and our entire library by creating a free personal account"], "Connect to Template Library": ["Connect to Template Library"], "Get Started": ["Get Started"], "Become a Pro user to upload unlimited font icon folders to your website.": ["Become a Pro user to upload unlimited font icon folders to your website."], "Upload": ["Upload"], "Click here for preview debug": ["Click here for preview debug"], "Elementor's New Icon Library": ["Elementor's New Icon Library"], "Icon Library": ["Icon Library"], "My Libraries": ["My Libraries"], "Elementor v2.6 includes an upgrade from Font Awesome 4 to 5. In order to continue using icons, be sure to click \"Update\".": ["Elementor v2.6 includes an upgrade from Font Awesome 4 to 5. In order to continue using icons, be sure to click \"Update\"."], "Before you enable unfiltered files upload, note that such files include a security risk. Elementor does run a process to remove possible malicious code, but there is still risk involved when using such files.": ["Before you enable unfiltered files upload, note that such files include a security risk. Elementor does run a process to remove possible malicious code, but there is still risk involved when using such files."], "Elementor 2.5 introduces key changes to the layout using CSS Flexbox. Your existing pages might have been affected, please review your page before publishing.": ["Elementor 2.5 introduces key changes to the layout using CSS Flexbox. Your existing pages might have been affected, please review your page before publishing."], "Note: Flexbox Changes": ["Note: Flexbox Changes"], "Custom Positioning": ["Custom Positioning"], "Theme Builder": ["Theme Builder"], "Continue": ["Continue"], "Keyboard Shortcuts": ["Keyboard Shortcuts"], "Undo": ["Undo"], "Finder": ["Finder"], "Create": ["Create"], "Connect": ["Connect"], "Connected successfully.": ["Connected successfully."], "Inner Section": ["Inner Section"], "Navigator": ["Navigator"], "All Content": ["All Content"], "Style Reset": ["Style Reset"], "Style Pasted": ["Style Pasted"], "Got It": ["Got It"], "Copy All Content": ["Copy All Content"], "Copy": ["Copy"], "Save as a global": ["Save as a global"], "Reset style": ["Reset style"], "Delete %s": ["Delete %s"], "Category": ["Category"], "Pages": ["Pages"], "Blocks": ["Blocks"], "Dynamic": ["Dynamic"], "Unknown Error": ["Unknown Error"], "Saving has been disabled until you’re reconnected.": ["Saving has been disabled until you’re reconnected."], "Server Error": ["Server Error"], "Connection Lost": ["Connection Lost"], "View All Revisions": ["View All Revisions"], "Have a look": ["Have a look"], "This is just a draft. Play around and when you're done - click update.": ["This is just a draft. Play around and when you're done - click update."], "Submit": ["Submit"], "Update": ["Update"], "Proceed Anyway": ["Proceed Anyway"], "Your browser isn't compatible with all of Elementor's editing features. We recommend you switch to another browser like Chrome or Firefox.": ["Your browser isn't compatible with all of Elementor's editing features. We recommend you switch to another browser like Chrome or Firefox."], "Your browser isn't compatible": ["Your browser isn't compatible"], "No Results Found": ["No Results Found"], "Please make sure your search is spelled correctly or try a different words.": ["Please make sure your search is spelled correctly or try a different words."], "No Favorite Templates": ["No Favourite Templates"], "You can mark any pre-designed template as a favorite.": ["You can mark any pre-designed template as a favourite."], "Show Panel": ["Show Panel"], "New": ["New"], "Duplicate": ["Duplicate"], "Hide Panel": ["Hide Panel"], "Publish": ["Publish"], "Type Here": ["Type Here"], "Revisions": ["Revisions"], "Actions": ["Actions"], "Moved": ["Moved"], "Added": ["Added"], "History": ["History"], "Edited": ["Edited"], "Editing Started": ["Editing Started"], "Add %s": ["Add %s"], "Edit %s": ["Edit %s"], "Duplicate %s": ["Duplicate %s"], "Enable": ["Enable"], "Template": ["Template"], "Disabled": ["Disabled"], "Back to Editor": ["Back to Editor"], "Global": ["Global"], "Attention: We are going to DELETE ALL CONTENT from this page. Are you sure you want to do that?": ["Attention: We are going to DELETE ALL CONTENT from this page. Are you sure you want to do that?"], "Delete All Content": ["Delete All Content"], "Color Picker": ["Colour Picker"], "Page": ["Page"], "The following error(s) occurred while processing the request:": ["The following error(s) occurred while processing the request:"], "This is where your templates should be. Design it. Save it. Reuse it.": ["This is where your templates should be. Design it. Save it. Reuse it."], "Haven’t Saved Templates Yet?": ["Haven’t Saved Templates Yet?"], "Your designs will be available for export and reuse on any page or website": ["Your designs will be available for export and reuse on any page or website"], "Insert": ["Insert"], "Library": ["Library"], "Close": ["Close"], "Templates": ["Templates"], "Learn More": ["Learn More"], "Global Fonts": ["Global Fonts"], "Paste": ["Paste"], "You must call 'the_content' function in the current template, in order for Elementor to work on this page.": ["You must call 'the_content' function in the current template, in order for <PERSON>ement<PERSON> to work on this page."], "Sorry, the content area was not found in your page.": ["Sorry, the content area was not found in your page."], "Insert Media": ["Insert Media"], "%s Images Selected": ["%s Images Selected"], "No Images Selected": ["No Images Selected"], "Are you sure you want to clear this gallery?": ["Are you sure you want to clear this gallery?"], "Elements": ["Elements"], "Settings": ["Settings"], "Color": ["Colour"], "View": ["View"], "Layout": ["Layout"], "Typography": ["Typography"], "Removed": ["Removed"], "Section": ["Section"], "Structure": ["Structure"], "Advanced": ["Advanced"], "Style": ["Style"], "Content": ["Content"], "Please note: All unsaved changes will be lost.": ["Please note: All unsaved changes will be lost."], "Go Back": ["Go Back"], "Take Over": ["Take Over"], "%s has taken over and is currently editing. Do you want to take over this page editing?": ["%s has taken over and is currently editing. Do you want to take over this page editing?"], "Apply": ["Apply"], "Discard": ["Discard"], "Cancel": ["Cancel"], "Preview": ["Preview"], "Save": ["Save"], "Delete": ["Delete"], "Custom": ["Custom"], "Clear": ["Clear"], "Default": ["<PERSON><PERSON><PERSON>"], "Add New Column": ["Add New Column"], "View Page": ["View Page"], "Inactive": ["Inactive"], "Active": ["Active"], "Exit": ["Exit"]}}, "comment": {"reference": "assets/js/editor.js"}}