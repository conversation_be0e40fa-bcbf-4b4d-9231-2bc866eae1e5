"use strict";var WPMailSMTPAdminNotifications=window.WPMailSMTPAdminNotifications||function(t){var a={$notifications:t("#wp-mail-smtp-notifications"),$nextButton:t("#wp-mail-smtp-notifications .navigation .next"),$prevButton:t("#wp-mail-smtp-notifications .navigation .prev"),$adminBarCounter:t("#wp-admin-bar-wp-mail-smtp-menu .wp-mail-smtp-admin-bar-menu-notification-counter")},i={init:function(){t(i.ready)},ready:function(){i.updateNavigation(),i.events()},events:function(){a.$notifications.on("click",".dismiss",i.dismiss).on("click",".next",i.navNext).on("click",".prev",i.navPrev)},dismiss:function(e){var n;0!==a.$currentMessage.length&&(n={action:"wp_mail_smtp_notification_dismiss",nonce:wp_mail_smtp.nonce,id:a.$currentMessage.data("message-id")},t.post(ajaxurl,n,function(e){e.success&&(1<(e=parseInt(a.$adminBarCounter.text(),10))?a.$adminBarCounter.html("<span>"+--e+"</span>"):a.$adminBarCounter.remove(),0===(e=a.$nextMessage.length<1?a.$prevMessage:a.$nextMessage).length?a.$notifications.remove():(a.$currentMessage.remove(),e.addClass("current"),i.updateNavigation()))}))},navNext:function(e){a.$nextButton.hasClass("disabled")||(a.$currentMessage.removeClass("current"),a.$nextMessage.addClass("current"),i.updateNavigation())},navPrev:function(e){a.$prevButton.hasClass("disabled")||(a.$currentMessage.removeClass("current"),a.$prevMessage.addClass("current"),i.updateNavigation())},updateNavigation:function(){a.$currentMessage=a.$notifications.find(".wp-mail-smtp-notifications-message.current"),a.$nextMessage=a.$currentMessage.next(".wp-mail-smtp-notifications-message"),a.$prevMessage=a.$currentMessage.prev(".wp-mail-smtp-notifications-message"),0===a.$nextMessage.length?a.$nextButton.addClass("disabled"):a.$nextButton.removeClass("disabled"),0===a.$prevMessage.length?a.$prevButton.addClass("disabled"):a.$prevButton.removeClass("disabled")}};return i}((document,window,jQuery));WPMailSMTPAdminNotifications.init();