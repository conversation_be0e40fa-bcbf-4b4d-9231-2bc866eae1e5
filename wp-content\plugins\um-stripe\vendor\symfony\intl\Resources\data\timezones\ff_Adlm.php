<?php

namespace UM_Stripe\Vendor\UM_Stripe\Vendor;

return ['Names' => ['Africa/Abidjan' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤀𞤦𞤭𞤶𞤢𞤲)', 'Africa/Accra' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤀𞤳𞤢𞤪𞤢)', 'Africa/Addis_Ababa' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤀𞤣𞤭𞤧𞤢𞤦𞤢𞤦𞤢)', 'Africa/Algiers' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤀𞤤𞤶𞤢𞤪𞤭𞥅)', 'Africa/Asmera' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤀𞤧𞤥𞤢𞤪𞤢)', 'Africa/Bamako' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤄𞤢𞤥𞤢𞤳𞤮𞥅)', 'Africa/Bangui' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤄𞤢𞤲𞤺𞤭)', 'Africa/Banjul' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤄𞤢𞤲𞤶𞤵𞤤)', 'Africa/Bissau' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤄𞤭𞤱𞤢𞤱𞤮)', 'Africa/Blantyre' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤄𞤭𞤤𞤢𞤲𞤼𞤭𞤪𞤫)', 'Africa/Brazzaville' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤄𞤢𞤪𞥁𞤢𞤾𞤭𞤤)', 'Africa/Bujumbura' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤄𞤵𞤶𞤵𞤥𞤦𞤵𞤪𞤢)', 'Africa/Cairo' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤑𞤢𞤴𞤪𞤢)', 'Africa/Casablanca' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤑𞤢𞥄𞤧𞤢𞤦𞤵𞤤𞤢𞤲𞤳𞤢𞥄)', 'Africa/Ceuta' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤅𞤭𞥅𞤼𞤢)', 'Africa/Conakry' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤑𞤮𞤲𞤢𞥄𞤳𞤭𞤪𞤭)', 'Africa/Dakar' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤁𞤢𞤳𞤢𞥄𞤪)', 'Africa/Dar_es_Salaam' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤁𞤢𞥄𞤪𞤫-𞤅𞤢𞤤𞤢𞥄𞤥𞤵)', 'Africa/Djibouti' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤔𞤭𞤦𞤵𞥅𞤼𞤭)', 'Africa/Douala' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤁𞤵𞤱𞤢𞤤𞤢)', 'Africa/El_Aaiun' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤂𞤢𞤴𞤵𞥅𞤲𞤢)', 'Africa/Freetown' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤊𞤭𞤪𞤼𞤮𞤲)', 'Africa/Gaborone' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤘𞤢𞤦𞤮𞤪𞤮𞥅𞤲)', 'Africa/Harare' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤖𞤢𞤪𞤢𞤪𞤫)', 'Africa/Johannesburg' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤱𞤪𞤵𞤲𞥋𞤣𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤔𞤮𞤸𞤢𞤲𞤢𞤧𞤦𞤵𞥅𞤪)', 'Africa/Juba' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤔𞤵𞤦𞤢)', 'Africa/Kampala' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤑𞤢𞤥𞤨𞤢𞤤𞤢)', 'Africa/Khartoum' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤝𞤢𞤪𞤼𞤵𞥅𞤥)', 'Africa/Kigali' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤑𞤭𞤺𞤢𞤤𞤭)', 'Africa/Kinshasa' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤑𞤭𞤲𞤧𞤢𞤧𞤢)', 'Africa/Lagos' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤂𞤢𞤺𞤮𞥅𞤧)', 'Africa/Libreville' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤂𞤭𞥅𞤦𞤫𞤪𞤾𞤭𞥅𞤤)', 'Africa/Lome' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤂𞤮𞤥𞤫)', 'Africa/Luanda' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤂𞤵𞤱𞤢𞤲𞤣𞤢𞥄)', 'Africa/Lubumbashi' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤂𞤵𞤦𞤵𞤥𞤦𞤢𞥃𞤭)', 'Africa/Lusaka' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤂𞤵𞤧𞤢𞤳𞤢)', 'Africa/Malabo' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤃𞤢𞤤𞤢𞤦𞤮𞥅)', 'Africa/Maputo' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤃𞤢𞤨𞤵𞤼𞤮)', 'Africa/Maseru' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤱𞤪𞤵𞤲𞥋𞤣𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤃𞤢𞤧𞤫𞤪𞤵)', 'Africa/Mbabane' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤱𞤪𞤵𞤲𞥋𞤣𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤐𞥋𞤄𞤢𞤦𞤢𞥄𞤲𞤫)', 'Africa/Mogadishu' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤃𞤵𞤹𞥆𞤢𞤧𞤮𞥅)', 'Africa/Monrovia' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤃𞤮𞤪𞤮𞤦𞤭𞤴𞤢)', 'Africa/Nairobi' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤐𞤢𞤴𞤪𞤮𞤦𞤭)', 'Africa/Ndjamena' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤐𞥋𞤔𞤢𞤥𞤫𞤲𞤢)', 'Africa/Niamey' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤐𞤭𞤴𞤢𞤥𞤫)', 'Africa/Nouakchott' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤐𞤵𞤱𞤢𞥄𞤳𞥃𞤵𞥅𞤼)', 'Africa/Ouagadougou' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤏𞤢𞤺𞤢𞤣𞤴𞤺𞤵)', 'Africa/Porto-Novo' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤆𞤮𞤪𞤼𞤮-𞤐𞤮𞤾𞤮𞥅)', 'Africa/Sao_Tome' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤅𞤢𞤱𞤮-𞤚𞤮𞤥𞤫𞥅)', 'Africa/Tripoli' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤚𞤪𞤭𞤨𞤮𞤤𞤭)', 'Africa/Tunis' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤚𞤵𞥅𞤲𞤵𞤧)', 'Africa/Windhoek' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤏𞤭𞤲𞤣𞤵𞥅𞤳)', 'America/Adak' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤱𞤢𞥄𞤴𞤭𞥅-𞤀𞤤𞤮𞤧𞤭𞤴𞤢𞤲 (𞤀𞤣𞤢𞤳)', 'America/Anchorage' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤤𞤢𞤧𞤳𞤢𞥄 (𞤀𞤲𞤧𞤮𞤪𞤢𞥄𞤶𞤵)', 'America/Anguilla' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤀𞤲𞤺𞤭𞤤𞤢𞥄)', 'America/Antigua' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤀𞤲𞤼𞤭𞤺𞤢)', 'America/Araguaina' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤄𞤪𞤢𞤧𞤭𞤤𞤭𞤴𞤢𞥄 (𞤀𞤪𞤢𞤺𞤵𞤱𞤢𞤲𞤢)', 'America/Argentina/La_Rioja' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤪𞤶𞤢𞤲𞤼𞤭𞤲𞤢𞥄 (𞤂𞤢-𞤈𞤭𞤴𞤮𞤸𞤢)', 'America/Argentina/Rio_Gallegos' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤪𞤶𞤢𞤲𞤼𞤭𞤲𞤢𞥄 (𞤈𞤭𞤮-𞤘𞤢𞤤𞤫𞤺𞤮𞤧)', 'America/Argentina/Salta' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤪𞤶𞤢𞤲𞤼𞤭𞤲𞤢𞥄 (𞤅𞤢𞤤𞤼𞤢)', 'America/Argentina/San_Juan' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤪𞤶𞤢𞤲𞤼𞤭𞤲𞤢𞥄 (𞤅𞤢𞤲-𞤝𞤵𞤱𞤢𞥄𞤲)', 'America/Argentina/San_Luis' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤪𞤶𞤢𞤲𞤼𞤭𞤲𞤢𞥄 (𞤅𞤢𞤲-𞤂𞤵𞤱𞤭𞥅𞤧)', 'America/Argentina/Tucuman' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤪𞤶𞤢𞤲𞤼𞤭𞤲𞤢𞥄 (𞤚𞤵𞤳𞤵𞤥𞤢𞥄𞤲)', 'America/Argentina/Ushuaia' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤪𞤶𞤢𞤲𞤼𞤭𞤲𞤢𞥄 (𞤓𞤧𞤱𞤢𞤭𞥅𞤶)', 'America/Aruba' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤀𞤪𞤵𞥅𞤦𞤢)', 'America/Asuncion' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤆𞤢𞥄𞤪𞤢𞤺𞤮𞤴 (𞤀𞤧𞤵𞤲𞤧𞤭𞤴𞤮𞤲)', 'America/Bahia' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤄𞤪𞤢𞤧𞤭𞤤𞤭𞤴𞤢𞥄 (𞤄𞤢𞤸𞤭𞤴𞤢)', 'America/Bahia_Banderas' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤄𞤢𞤸𞤭𞤴𞤢𞥄 𞤄𞤢𞤲𞤣𞤫𞤪𞤢𞥄𞤧)', 'America/Barbados' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤄𞤢𞤪𞤦𞤢𞥄𞤣𞤮𞤧)', 'America/Belem' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤄𞤪𞤢𞤧𞤭𞤤𞤭𞤴𞤢𞥄 (𞤄𞤫𞤤𞤫𞤥)', 'America/Belize' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤄𞤫𞤤𞤭𞥅𞥁)', 'America/Blanc-Sablon' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤄𞤵𞤤𞤢𞤲 𞤅𞤢𞤦𞤵𞤤𞤮𞤲)', 'America/Boa_Vista' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤥𞤢𞥁𞤮𞥅𞤲 (𞤄𞤮𞤱𞤢-𞤜𞤭𞤧𞤼𞤢)', 'America/Bogota' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤑𞤮𞤤𞤮𞤥𞤦𞤭𞤴𞤢𞥄 (𞤄𞤮𞤺𞤮𞤼𞤢)', 'America/Boise' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤆𞤫𞤤𞥆𞤭𞤲𞤳𞤮𞥅𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤄𞤮𞤴𞥁𞤭𞥅)', 'America/Buenos_Aires' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤪𞤶𞤢𞤲𞤼𞤭𞤲𞤢𞥄 (𞤄𞤭𞤴𞤲𞤮𞤧-𞤉𞥅𞤶𞤫𞤪𞤫𞥅𞤧)', 'America/Cambridge_Bay' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤆𞤫𞤤𞥆𞤭𞤲𞤳𞤮𞥅𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤑𞤢𞤥𞤦𞤭𞤪𞤭𞥅𞤶 𞤄𞤫𞥅)', 'America/Campo_Grande' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤥𞤢𞥁𞤮𞥅𞤲 (𞤑𞤢𞤥𞤨𞤮-𞤘𞤪𞤢𞤲𞤣𞤫)', 'America/Cancun' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤑𞤢𞤲𞤳𞤵𞥅𞤲)', 'America/Caracas' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤜𞤫𞤲𞤭𞥅𞥁𞤮𞥅𞤤𞤢 (𞤑𞤢𞤪𞤢𞤳𞤢𞤧)', 'America/Catamarca' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤪𞤶𞤢𞤲𞤼𞤭𞤲𞤢𞥄 (𞤑𞤢𞤼𞤢𞤥𞤢𞤪𞤳𞤢𞥄)', 'America/Cayenne' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤘𞤢𞤴𞤢𞤲𞤢𞥄-𞤊𞤪𞤢𞤲𞤧𞤭 (𞤑𞤢𞤴𞤫𞥅𞤲)', 'America/Cayman' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤑𞤢𞤴𞤥𞤢𞥄𞤲)', 'America/Chicago' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤕𞤭𞤳𞤢𞥄𞤺𞤮𞥅)', 'America/Chihuahua' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤕𞤋𞤱𞤢𞥄𞤱𞤢𞥄)', 'America/Ciudad_Juarez' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤆𞤫𞤤𞥆𞤭𞤲𞤳𞤮𞥅𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (Ciudad Juárez)', 'America/Coral_Harbour' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤀𞤼𞤭𞤳𞤮𞥅𞤳𞤢𞤲)', 'America/Cordoba' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤪𞤶𞤢𞤲𞤼𞤭𞤲𞤢𞥄 (𞤑𞤮𞤪𞤣𞤮𞤦𞤢𞥄)', 'America/Costa_Rica' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤑𞤮𞤧𞤼𞤢 𞤈𞤭𞤳𞤢𞥄)', 'America/Creston' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤆𞤫𞤤𞥆𞤭𞤲𞤳𞤮𞥅𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤑𞤪𞤫𞤧𞤼𞤮𞤲)', 'America/Cuiaba' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤥𞤢𞥁𞤮𞥅𞤲 (𞤑𞤵𞤶𞤢𞤦𞤢𞥄)', 'America/Curacao' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤑𞤵𞤪𞤢𞤧𞤢𞤱𞤮𞥅)', 'America/Danmarkshavn' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤁𞤢𞥄𞤲𞤥𞤢𞤪𞤳𞥃𞤢𞥄𞤾𞤲)', 'America/Dawson' => '𞤑𞤢𞤲𞤢𞤣𞤢𞥄 𞤑𞤭𞤶𞤮𞥅𞤪𞤫 (𞤁𞤮𞥅𞤧𞤮𞤲)', 'America/Dawson_Creek' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤆𞤫𞤤𞥆𞤭𞤲𞤳𞤮𞥅𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤁𞤮𞥅𞤧𞤮𞤲-𞤑𞤪𞤫𞤳)', 'America/Denver' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤆𞤫𞤤𞥆𞤭𞤲𞤳𞤮𞥅𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤁𞤫𞤲𞤾𞤮𞥅)', 'America/Detroit' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤁𞤭𞤼𞤪𞤮𞤴𞤼)', 'America/Dominica' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤁𞤮𞤥𞤭𞤲𞤭𞤳𞤢𞥄)', 'America/Edmonton' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤆𞤫𞤤𞥆𞤭𞤲𞤳𞤮𞥅𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤉𞤣𞤥𞤮𞤲𞤼𞤮𞤲)', 'America/Eirunepe' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞥄𞤳𞤭𞤪 (𞤉𞤪𞤵𞤲𞤫𞤨𞤫)', 'America/El_Salvador' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤉𞤤-𞤅𞤢𞤤𞤾𞤢𞤣𞤮𞥅𞤪)', 'America/Fort_Nelson' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤆𞤫𞤤𞥆𞤭𞤲𞤳𞤮𞥅𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤊𞤮𞤪𞤼-𞤐𞤫𞤤𞤧𞤮𞤲;)', 'America/Fortaleza' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤄𞤪𞤢𞤧𞤭𞤤𞤭𞤴𞤢𞥄 (𞤊𞤮𞤪𞤼𞤢𞤤𞤫𞥅𞥁𞤢)', 'America/Glace_Bay' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤘𞤤𞤫𞤧-𞤄𞤫𞥅)', 'America/Godthab' => '𞤘𞤭𞤪𞤤𞤢𞤲𞤣𞤭 𞤑𞤭𞤶𞤮𞥅𞤪𞤫 (𞤐𞤵𞥅𞤳)', 'America/Goose_Bay' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤘𞤮𞥅𞤧-𞤄𞤫𞥅)', 'America/Grand_Turk' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤘𞤪𞤢𞤲𞤣-𞤚𞤵𞤪𞤳)', 'America/Grenada' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤘𞤪𞤫𞤲𞤢𞥄𞤣𞤢)', 'America/Guadeloupe' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤘𞤵𞤱𞤢𞤣𞤫𞤤𞤵𞤨𞥆𞤫𞥅)', 'America/Guatemala' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤘𞤵𞤱𞤢𞤼𞤫𞤥𞤢𞤤𞤢)', 'America/Guayaquil' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤋𞤳𞤵𞤱𞤢𞤣𞤮𞥅𞤪 (𞤘𞤵𞤴𞤢𞤳𞤭𞤤)', 'America/Guyana' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤘𞤢𞤴𞤢𞤲𞤢𞥄 (𞤘𞤵𞤴𞤢𞤲𞤢𞥄)', 'America/Halifax' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤖𞤢𞤤𞤭𞤬𞤢𞤳𞤧𞤭)', 'America/Havana' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤑𞤵𞥅𞤦𞤢𞥄 (𞤖𞤢𞤾𞤢𞤲𞤢𞥄)', 'America/Hermosillo' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤁𞤫𞤰𞥆𞤮 𞤃𞤫𞤳𞤧𞤭𞤳𞤮𞥅 (𞤖𞤢𞤪𞤥𞤮𞤧𞤭𞤤𞤭𞤴𞤮𞥅)', 'America/Indiana/Knox' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤐𞤮𞤳𞤧𞤵, 𞤋𞤣𞤭𞤴𞤢𞤲𞤢𞥄)', 'America/Indiana/Marengo' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤃𞤢𞤪𞤫𞤲𞤺𞤮, 𞤋𞤲𞤣𞤭𞤴𞤢𞤲𞤢𞥄)', 'America/Indiana/Petersburg' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤆𞤫𞤼𞤮𞤧𞤄𞤵𞥅𞤪𞤺, 𞤋𞤲𞤣𞤭𞤴𞤢𞤲𞤢𞥄)', 'America/Indiana/Tell_City' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤚𞤫𞤤-𞤅𞤭𞤼𞤭𞥅, 𞤋𞤲𞤣𞤭𞤴𞤢𞤲𞤢𞥄)', 'America/Indiana/Vevay' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤜𞤫𞥅𞤾𞤫𞤴, 𞤋𞤲𞤣𞤭𞤴𞤢𞤲𞤢𞥄)', 'America/Indiana/Vincennes' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤜𞤭𞤲𞤧𞤫𞥅𞤲, 𞤋𞤲𞤣𞤭𞤴𞤢𞤲𞤢𞥄)', 'America/Indiana/Winamac' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤏𞤭𞤲𞤢𞤥𞤢𞤳, 𞤋𞤲𞤣𞤭𞤴𞤢𞤲𞤢𞥄)', 'America/Indianapolis' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤋𞤲𞤣𞤭𞤴𞤢𞤲𞤢𞥄𞤨𞤮𞤤𞤭𞤧)', 'America/Inuvik' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤆𞤫𞤤𞥆𞤭𞤲𞤳𞤮𞥅𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤋𞤲𞤵𞤾𞤭𞤳)', 'America/Iqaluit' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤋𞤳𞤢𞤤𞤵𞤱𞤭𞤼)', 'America/Jamaica' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤔𞤢𞤥𞤢𞥄𞤴𞤳𞤢)', 'America/Jujuy' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤪𞤶𞤢𞤲𞤼𞤭𞤲𞤢𞥄 (𞤔𞤵𞤶𞤵𞤴)', 'America/Juneau' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤤𞤢𞤧𞤳𞤢𞥄 (𞤔𞤵𞥅𞤲𞤮𞥅)', 'America/Kentucky/Monticello' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤃𞤮𞤲𞤼𞤭𞤷𞤫𞤤𞤮𞥅, 𞤑𞤫𞤲𞤼𞤮𞥅𞤳𞤭𞥅)', 'America/Kralendijk' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤑𞤪𞤢𞤤𞤫𞤲𞤶𞤭𞥅𞤳)', 'America/La_Paz' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤄𞤮𞤤𞤭𞤾𞤭𞤴𞤢𞥄 (𞤂𞤢-𞤆𞤢𞥄𞥁)', 'America/Lima' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤆𞤫𞤪𞤵𞥅 (𞤂𞤭𞥅𞤥𞤢)', 'America/Los_Angeles' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤁𞤫𞤰𞥆𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤂𞤮𞤧-𞤀𞤺𞤫𞤤𞤫𞥅𞤧)', 'America/Louisville' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤂𞤵𞤭𞤾𞤭𞤤)', 'America/Lower_Princes' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤂𞤮𞤱𞤮 𞤆𞤪𞤫𞤲𞤧𞤫𞥅𞤧 𞤑𞤮𞤣𞤮𞥅)', 'America/Maceio' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤄𞤪𞤢𞤧𞤭𞤤𞤭𞤴𞤢𞥄 (𞤃𞤢𞤧𞤫𞤴𞤮)', 'America/Managua' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤃𞤢𞤲𞤢𞤱𞤢𞥄)', 'America/Manaus' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤥𞤢𞥁𞤮𞥅𞤲 (𞤃𞤢𞤲𞤵𞥅𞤧)', 'America/Marigot' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤃𞤢𞤪𞤭𞤺𞤮𞥅)', 'America/Martinique' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤃𞤢𞤪𞤼𞤭𞤲𞤭𞤳)', 'America/Matamoros' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤃𞤢𞤼𞤢𞤥𞤮𞤪𞤮𞥅𞤧)', 'America/Mazatlan' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤁𞤫𞤰𞥆𞤮 𞤃𞤫𞤳𞤧𞤭𞤳𞤮𞥅 (𞤃𞤢𞥁𞤢𞤼𞤤𞤢𞤲)', 'America/Mendoza' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤪𞤶𞤢𞤲𞤼𞤭𞤲𞤢𞥄 (𞤃𞤫𞤲𞤣𞤮𞥅𞥁𞤢)', 'America/Menominee' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤃𞤫𞤲𞤮𞤥𞤭𞤲𞤭)', 'America/Merida' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤃𞤫𞤪𞤭𞤣𞤢)', 'America/Metlakatla' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤤𞤢𞤧𞤳𞤢𞥄 (𞤃𞤫𞤼𞤤𞤢𞤳𞤢𞤼𞤤𞤢)', 'America/Mexico_City' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤃𞤫𞤳𞤧𞤭𞤳𞤮𞥅 𞤅𞤭𞤼𞤭𞥅)', 'America/Miquelon' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤅𞤼. 𞤆𞤭𞤪𞤫𞥅𞤴 & 𞤃𞤭𞤳𞤫𞤤𞤮𞤲 (𞤃𞤫𞤳𞤫𞤤𞤮𞤲)', 'America/Moncton' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤃𞤮𞤲𞤳𞤼𞤮𞥅𞤲)', 'America/Monterrey' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤃𞤮𞤲𞤼𞤫𞤪𞤫𞥅𞤴)', 'America/Montevideo' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤒𞤵𞥅𞤪𞤺𞤮𞤴 (𞤃𞤮𞤲𞤼𞤫𞤾𞤭𞤣𞤭𞤴𞤮𞥅)', 'America/Montserrat' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤃𞤮𞤲𞤼𞤧𞤭𞤪𞤢𞤴𞤼)', 'America/Nassau' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤐𞤢𞤧𞤮𞥅)', 'America/New_York' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤐𞤫𞤱-𞤒𞤮𞤪𞤳)', 'America/Nome' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤤𞤢𞤧𞤳𞤢𞥄 (𞤐𞤮𞤱𞤥𞤵)', 'America/Noronha' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤫𞤪𞤲𞤢𞤲𞤣𞤮𞥅 𞤣𞤫 𞤐𞤮𞤪𞤮𞤲𞤽𞤢𞥄 (𞤃𞤢𞤪𞤮𞤲𞤿𞤢)', 'America/North_Dakota/Beulah' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤄𞤵𞤤𞤢𞥄, 𞤐𞤮𞤪𞤬-𞤁𞤢𞤳𞤮𞤼𞤢)', 'America/North_Dakota/Center' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤅𞤫𞤲𞤼𞤮𞥅, 𞤐𞤮𞤪𞤬-𞤁𞤢𞤳𞤮𞤼𞤢𞥄)', 'America/North_Dakota/New_Salem' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤐𞤫𞤱-𞤅𞤫𞤤𞤫𞤥, 𞤐𞤮𞤪𞤬-𞤁𞤢𞤳𞤮𞤼𞤢𞥄)', 'America/Ojinaga' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤌𞤶𞤭𞤲𞤢𞤺𞤢)', 'America/Panama' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤆𞤢𞤲𞤢𞤲𞤥𞤢𞥄)', 'America/Paramaribo' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤅𞤭𞤪𞤭𞤲𞤢𞤥 (𞤆𞤢𞤪𞤢𞤥𞤢𞤪𞤭𞤦𞤮)', 'America/Phoenix' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤆𞤫𞤤𞥆𞤭𞤲𞤳𞤮𞥅𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤊𞤭𞤲𞤭𞤳𞤧)', 'America/Port-au-Prince' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤆𞤮𞤪𞤼-𞤮-𞤆𞤪𞤫𞤲𞤧)', 'America/Port_of_Spain' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤆𞤮𞤪𞤼 𞤮𞤬 𞤅𞤭𞤨𞤫𞥅𞤲)', 'America/Porto_Velho' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤥𞤢𞥁𞤮𞥅𞤲 (𞤆𞤮𞤪𞤼𞤮-𞤜𞤫𞤤𞤸𞤮𞥅)', 'America/Puerto_Rico' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤆𞤮𞤪𞤼-𞤈𞤭𞤳𞤮𞥅)', 'America/Punta_Arenas' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤕𞤭𞤤𞤫𞥅 (𞤆𞤵𞤲𞤼𞤢-𞤀𞤪𞤫𞤲𞤢𞥁)', 'America/Rankin_Inlet' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤈𞤢𞤲𞤳𞤭𞤲 𞤋𞤲𞤤𞤫𞤼)', 'America/Recife' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤄𞤪𞤢𞤧𞤭𞤤𞤭𞤴𞤢𞥄 (𞤈𞤫𞤧𞤭𞤬𞤭)', 'America/Regina' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤈𞤭𞤺𞤭𞤲𞤢𞥄)', 'America/Resolute' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤈𞤭𞤧𞤮𞤤𞤵𞥅𞤼)', 'America/Rio_Branco' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞥄𞤳𞤭𞤪 (𞤈𞤭𞤴𞤮-𞤄𞤪𞤢𞤲𞤳𞤮)', 'America/Santarem' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤄𞤪𞤢𞤧𞤭𞤤𞤭𞤴𞤢𞥄 (𞤅𞤢𞤲𞤼𞤢𞤪𞤫𞥅𞤥)', 'America/Santiago' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤕𞤭𞤤𞤫𞥅 (𞤅𞤢𞤲𞤼𞤭𞤴𞤢𞤺𞤮𞥅)', 'America/Santo_Domingo' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤅𞤢𞤲𞤼𞤢-𞤁𞤮𞤥𞤭𞤲𞤺𞤮𞥅)', 'America/Sao_Paulo' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤄𞤪𞤢𞤧𞤭𞤤𞤭𞤴𞤢𞥄 (𞤅𞤢𞥄𞤱-𞤆𞤮𞤤𞤮𞥅)', 'America/Scoresbysund' => '𞤘𞤭𞤪𞤤𞤢𞤲𞤣𞤭 𞤑𞤭𞤶𞤮𞥅𞤪𞤫 (𞤋𞤼𞥆𞤮𞤳𞤮𞤪𞤼𞤮𞥅𞤪𞤥𞤭𞥅𞤼)', 'America/Sitka' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤤𞤢𞤧𞤳𞤢𞥄 (𞤅𞤭𞤼𞤳𞤢)', 'America/St_Barthelemy' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤅𞤫𞤲𞤼-𞤄𞤢𞤼𞤫𞤤𞤫𞤥𞤭𞥅)', 'America/St_Johns' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤐𞤫𞤱-𞤊𞤵𞤲𞤣𞤵𞤤𞤢𞤲𞤣 (𞤅𞤫𞤲𞤼-𞤔𞤮𞥅𞤲𞤧)', 'America/St_Kitts' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤅𞤫𞤲𞤼-𞤑𞤭𞤼𞥆𞤭𞤧)', 'America/St_Lucia' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤅𞤫𞤲𞤼-𞤂𞤵𞤧𞤭𞤢)', 'America/St_Thomas' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤅𞤫𞤲𞤼-𞤚𞤮𞤥𞤢𞥄𞤧)', 'America/St_Vincent' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤅𞤫𞤲𞤼-𞤜𞤫𞤲𞤧𞤫𞤲𞤼)', 'America/Swift_Current' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤅𞤭𞤬𞤼-𞤑𞤭𞤪𞥆𞤢𞤲𞤼)', 'America/Tegucigalpa' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤚𞤵𞤺𞤵𞤧𞤭𞤺𞤵𞤤𞤨𞤢)', 'America/Thule' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤚𞤵𞤤𞤫)', 'America/Tijuana' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤁𞤫𞤰𞥆𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤚𞤭𞤶𞤵𞤱𞤢𞥄𞤲𞤢)', 'America/Toronto' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤚𞤮𞤪𞤮𞤲𞤼𞤮𞥅)', 'America/Tortola' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤚𞤮𞤪𞤼𞤮𞤤𞤢𞥄)', 'America/Vancouver' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤁𞤫𞤰𞥆𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤜𞤫𞤲𞤳𞤵𞥅𞤾𞤮)', 'America/Whitehorse' => '𞤑𞤢𞤲𞤢𞤣𞤢𞥄 𞤑𞤭𞤶𞤮𞥅𞤪𞤫 (𞤏𞤢𞤴𞤼𞤸𞤮𞤪𞤧𞤫)', 'America/Winnipeg' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 (𞤏𞤭𞤲𞤭𞤨𞤫𞥅𞤺)', 'America/Yakutat' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤤𞤢𞤧𞤳𞤢𞥄 (𞤒𞤢𞤳𞤵𞤼𞤢𞤼)', 'Antarctica/Casey' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤌𞤧𞤼𞤢𞤪𞤤𞤭𞤴𞤢𞥄 (𞤑𞤢𞤴𞤧𞤫)', 'Antarctica/Davis' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤁𞤫𞥅𞤾𞤭𞤧 (𞤁𞤢𞤾𞤭𞥅𞤧)', 'Antarctica/DumontDUrville' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤁𞤭𞤥𞤮𞤲𞤼𞤵-𞤁𞤵𞤪𞤾𞤭𞤤', 'Antarctica/Macquarie' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤌𞤧𞤼𞤢𞤪𞤤𞤭𞤴𞤢𞥄 (𞤃𞤢𞤳𞤢𞥄𞤪𞤭)', 'Antarctica/Mawson' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤃𞤢𞤱𞤧𞤮𞤲', 'Antarctica/McMurdo' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤐𞤫𞤱-𞤟𞤫𞤤𞤢𞤲𞤣𞤭 (𞤃𞤢𞤳𞤥𞤵𞥅𞤪𞤣𞤮)', 'Antarctica/Palmer' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤕𞤭𞤤𞤫𞥅 (𞤆𞤢𞤤𞤥𞤫𞥅𞤪)', 'Antarctica/Rothera' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤈𞤮𞤼𞤫𞤪𞤢', 'Antarctica/Syowa' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤅𞤢𞥄𞤴𞤵𞤱𞤢', 'Antarctica/Troll' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤚𞤢𞤪𞤮𞥅𞤤)', 'Antarctica/Vostok' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤜𞤮𞤧𞤼𞤮𞤳', 'Arctic/Longyearbyen' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤂𞤮𞤲𞤶𞤭𞤪𞤦𞤭𞤴𞤫𞥅𞤲)', 'Asia/Aden' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞥄𞤪𞤢𞤦𞤭𞤴𞤢 (𞤀𞤣𞤫𞤲)', 'Asia/Almaty' => '𞤑𞤢𞥁𞤢𞤧𞤼𞤢𞥄𞤲 𞤑𞤭𞤶𞤮𞥅𞤪𞤫 (𞤀𞤤𞤥𞤢𞥄𞤼𞤭)', 'Asia/Amman' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤀𞤥𞤢𞥄𞤲𞤵)', 'Asia/Anadyr' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤲𞤢𞤣𞤭𞥅𞤪', 'Asia/Aqtau' => '𞤑𞤢𞥁𞤢𞤧𞤼𞤢𞥄𞤲 𞤑𞤭𞤶𞤮𞥅𞤪𞤫 (𞤀𞤳𞤼𞤢𞥄𞤱𞤵)', 'Asia/Aqtobe' => '𞤑𞤢𞥁𞤢𞤧𞤼𞤢𞥄𞤲 𞤑𞤭𞤶𞤮𞥅𞤪𞤫 (𞤀𞤳𞤼𞤮𞥅𞤦𞤫)', 'Asia/Ashgabat' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤪𞤳𞤵𞤥𞤫𞤲𞤭𞤧𞤼𞤢𞥄𞤲 (𞤀𞤧𞤺𞤢𞤦𞤢𞤼𞤵)', 'Asia/Atyrau' => '𞤑𞤢𞥁𞤢𞤧𞤼𞤢𞥄𞤲 𞤑𞤭𞤶𞤮𞥅𞤪𞤫 (𞤀𞤼𞤭𞤪𞤢𞤱𞤵)', 'Asia/Baghdad' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞥄𞤪𞤢𞤦𞤭𞤴𞤢 (𞤄𞤢𞤿𞤣𞤢𞥄𞤣𞤵)', 'Asia/Bahrain' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞥄𞤪𞤢𞤦𞤭𞤴𞤢 (𞤄𞤢𞤸𞤪𞤢𞤴𞤲𞤵)', 'Asia/Baku' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤶𞤫𞤪𞤦𞤢𞤴𞤶𞤢𞤲 (𞤄𞤢𞥄𞤳𞤵)', 'Asia/Bangkok' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤋𞤲𞤣𞤮𞤧𞤭𞥅𞤲 (𞤄𞤢𞤲𞤳𞤮𞥅𞤳𞤵)', 'Asia/Barnaul' => '𞤈𞤮𞥅𞤧𞤭𞤴𞤢 𞤑𞤭𞤶𞤮𞥅𞤪𞤫 (𞤄𞤢𞤪𞤲𞤢𞥄𞤤𞤵)', 'Asia/Beirut' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤄𞤫𞤴𞤪𞤵𞥅𞤼𞤵)', 'Asia/Bishkek' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤑𞤭𞤪𞤺𞤭𞤧𞤼𞤢𞥄𞤲 (𞤄𞤭𞤧𞤳𞤫𞥅𞤳𞤵)', 'Asia/Brunei' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤄𞤵𞤪𞤲𞤢𞤴', 'Asia/Calcutta' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤱𞤪𞤵𞤲𞥋𞤣𞤫 𞤋𞤲𞤣𞤭𞤴𞤢 (𞤑𞤮𞤤𞤳𞤢𞤼𞤢)', 'Asia/Chita' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤒𞤢𞤳𞤢𞤼𞤭𞤧𞤳𞤵 (𞤕𞤭𞥅𞤼𞤢)', 'Asia/Colombo' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤱𞤪𞤵𞤲𞥋𞤣𞤫 𞤋𞤲𞤣𞤭𞤴𞤢 (𞤑𞤮𞤤𞤮𞤥𞤦𞤢)', 'Asia/Damascus' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤁𞤢𞤥𞤢𞤧𞤹𞤢)', 'Asia/Dhaka' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤄𞤢𞤲𞤺𞤭𞤤𞤢𞤣𞤫𞥅𞤧 (𞤁𞤢𞤳𞤢𞥄)', 'Asia/Dili' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤚𞤭𞥅𞤥𞤮𞤪 (𞤁𞤫𞤤𞤭𞥅)', 'Asia/Dubai' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤂𞤮𞥅𞤻𞤵 (𞤁𞤵𞤦𞤢𞤴)', 'Asia/Dushanbe' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤢𞤶𞤭𞤳𞤭𞤧𞤼𞤢𞥄𞤲 (𞤁𞤵𞤧𞤢𞤲𞤦𞤫)', 'Asia/Famagusta' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤊𞤢𞤥𞤢𞤺𞤵𞤧𞤼𞤢)', 'Asia/Gaza' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤘𞤢𞥄𞥁𞤢)', 'Asia/Hebron' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤝𞤭𞤤𞤢𞥄𞤤𞤵)', 'Asia/Hong_Kong' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤮𞤲𞤺 𞤑𞤮𞤲𞤺', 'Asia/Hovd' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤮𞤬𞤣𞤵', 'Asia/Irkutsk' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤋𞤪𞤳𞤵𞤼𞤭𞤧𞤳𞤵', 'Asia/Jakarta' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤋𞤲𞤣𞤮𞤲𞤭𞥅𞤧𞤭𞤴𞤢 (𞤔𞤢𞤳𞤢𞤪𞤼𞤢𞥄)', 'Asia/Jayapura' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤋𞤲𞤣𞤮𞤲𞤭𞥅𞤧𞤭𞤴𞤢 (𞤔𞤢𞤴𞤢𞤨𞤵𞤪𞤢)', 'Asia/Jerusalem' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤋𞤧𞤪𞤢𞥄𞤭𞥅𞤤𞤵 (𞤗𞤵𞤣𞤵𞤧𞤵)', 'Asia/Kabul' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤬𞤺𞤢𞤲𞤭𞤧𞤼𞤢𞥄𞤲 (𞤑𞤢𞤦𞤵𞤤)', 'Asia/Kamchatka' => '𞤈𞤮𞥅𞤧𞤭𞤴𞤢 𞤑𞤭𞤶𞤮𞥅𞤪𞤫 (𞤑𞤢𞤥𞤷𞤢𞤼𞤭𞤳𞤢)', 'Asia/Karachi' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤆𞤢𞤳𞤭𞤧𞤼𞤢𞥄𞤲 (𞤑𞤢𞤪𞤢𞤷𞤭𞥅)', 'Asia/Katmandu' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤐𞤫𞤨𞤢𞤤 (𞤑𞤢𞤼𞤭𞤥𞤢𞤲𞤣𞤵)', 'Asia/Khandyga' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤒𞤢𞤳𞤢𞤼𞤭𞤧𞤳𞤵 (𞤝𞤢𞤲𞤣𞤭𞤺𞤢)', 'Asia/Krasnoyarsk' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤑𞤢𞤪𞤢𞤧𞤲𞤮𞤴𞤢𞤪𞤧𞤭𞤳 (𞤑𞤢𞤪𞤢𞤧𞤲𞤮𞤴𞤢𞤪𞤧𞤵𞤳𞤵)', 'Asia/Kuala_Lumpur' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤃𞤢𞤤𞤫𞥅𞤧𞤭𞤴𞤢 (𞤑𞤵𞤱𞤢𞤤𞤢-𞤂𞤮𞤥𞤨𞤵𞥅𞤪)', 'Asia/Kuching' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤃𞤢𞤤𞤫𞥅𞤧𞤭𞤴𞤢 (𞤑𞤵𞤷𞤭𞤲)', 'Asia/Kuwait' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞥄𞤪𞤢𞤦𞤭𞤴𞤢 (𞤑𞤵𞤱𞤢𞤴𞤼𞤭)', 'Asia/Macau' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤕𞤢𞤴𞤲𞤢 (𞤃𞤢𞤳𞤢𞤱𞤮)', 'Asia/Magadan' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤃𞤢𞤺𞤢𞤣𞤢𞤲', 'Asia/Makassar' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤮𞤥𞤦𞤮𞥅𞤪𞤭 𞤋𞤲𞤣𞤮𞤲𞤭𞥅𞤧𞤭𞤴𞤢 (𞤃𞤢𞤳𞤢𞤧𞤢𞥄𞤪)', 'Asia/Manila' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤭𞤤𞤭𞤨𞤭𞥅𞤲 (𞤃𞤢𞤲𞤭𞤤𞤢)', 'Asia/Muscat' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤂𞤮𞥅𞤻𞤵 (𞤃𞤵𞤧𞤳𞤢𞤼𞤵)', 'Asia/Nicosia' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤐𞤭𞤳𞤮𞤧𞤭𞤴𞤢)', 'Asia/Novokuznetsk' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤑𞤢𞤪𞤢𞤧𞤲𞤮𞤴𞤢𞤪𞤧𞤭𞤳 (𞤐𞤮𞤾𞤮𞤳𞤵𞥁𞤲𞤫𞤼𞤭𞤧𞤳𞤵)', 'Asia/Novosibirsk' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤐𞤮𞤾𞤮𞤧𞤦𞤭𞤪𞤧𞤭𞤳 (𞤐𞤮𞤾𞤮𞤧𞤭𞤦𞤭𞤪𞤧𞤵𞤳)', 'Asia/Omsk' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤌𞤥𞤧𞤵𞤳𞤵', 'Asia/Oral' => '𞤑𞤢𞥁𞤢𞤧𞤼𞤢𞥄𞤲 𞤑𞤭𞤶𞤮𞥅𞤪𞤫 (𞤓𞤪𞤢𞤤)', 'Asia/Phnom_Penh' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤋𞤲𞤣𞤮𞤧𞤭𞥅𞤲 (𞤆𞤢𞤲𞤮𞤥-𞤆𞤫𞤲)', 'Asia/Pontianak' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤋𞤲𞤣𞤮𞤲𞤭𞥅𞤧𞤭𞤴𞤢 (𞤆𞤮𞤲𞤼𞤭𞤴𞤢𞤲𞤢𞤳)', 'Asia/Pyongyang' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤑𞤮𞥅𞤪𞤫𞤴𞤢𞥄 (𞤆𞤭𞤴𞤮𞤲𞤴𞤢𞤲)', 'Asia/Qatar' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞥄𞤪𞤢𞤦𞤭𞤴𞤢 (𞤗𞤢𞤼𞤢𞤪)', 'Asia/Qostanay' => '𞤑𞤢𞥁𞤢𞤧𞤼𞤢𞥄𞤲 𞤑𞤭𞤶𞤮𞥅𞤪𞤫 (𞤑𞤮𞤧𞤼𞤢𞤲𞤢𞤴)', 'Asia/Qyzylorda' => '𞤑𞤢𞥁𞤢𞤧𞤼𞤢𞥄𞤲 𞤑𞤭𞤶𞤮𞥅𞤪𞤫 (𞤑𞤭𞥁𞤭𞤤𞤮𞤪𞤣𞤢)', 'Asia/Rangoon' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤃𞤭𞤴𞤢𞤥𞤢𞥄𞤪 (𞤈𞤢𞤲𞤺𞤵𞥅𞤲)', 'Asia/Riyadh' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞥄𞤪𞤢𞤦𞤭𞤴𞤢 (𞤈𞤭𞤴𞤢𞥄𞤣)', 'Asia/Saigon' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤋𞤲𞤣𞤮𞤧𞤭𞥅𞤲 (𞤅𞤢𞤸𞤪𞤫 𞤖𞤮𞥅-𞤕𞤭 𞤃𞤭𞥅𞤲)', 'Asia/Sakhalin' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤅𞤢𞤿𞤢𞤤𞤭𞥅𞤲', 'Asia/Samarkand' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤓𞥁𞤦𞤫𞤳𞤭𞤧𞤼𞤢𞥄𞤲 (𞤅𞤢𞤥𞤢𞤪𞤳𞤢𞤲𞤣𞤵)', 'Asia/Seoul' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤑𞤮𞥅𞤪𞤫𞤴𞤢𞥄 (𞤅𞤫𞤱𞤵𞤤)', 'Asia/Shanghai' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤕𞤢𞤴𞤲𞤢 (𞤅𞤢𞤲𞤸𞤢𞤴)', 'Asia/Singapore' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤱𞤪𞤵𞤲𞥋𞤣𞤫 𞤅𞤭𞤲𞤺𞤢𞤨𞤵𞥅𞤪', 'Asia/Srednekolymsk' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤃𞤢𞤺𞤢𞤣𞤢𞤲 (𞤅𞤭𞤪𞤫𞤣𞤳𞤮𞤤𞤭𞤥𞤧𞤵)', 'Asia/Taipei' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤢𞤴𞤨𞤫𞥅', 'Asia/Tashkent' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤓𞥁𞤦𞤫𞤳𞤭𞤧𞤼𞤢𞥄𞤲 (𞤚𞤢𞤧𞤳𞤫𞤲𞤼𞤵)', 'Asia/Tbilisi' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤔𞤮𞤪𞤶𞤭𞤴𞤢 (𞤚𞤭𞤦𞤭𞤤𞤭𞤧𞤭𞥅)', 'Asia/Tehran' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤋𞤪𞤢𞥄𞤲 (𞤚𞤫𞤸𞤭𞤪𞤢𞥄𞤲)', 'Asia/Thimphu' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤄𞤵𞤼𞤢𞥄𞤲 (𞤚𞤭𞤥𞤨𞤵)', 'Asia/Tokyo' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤐𞤭𞤨𞥆𞤮𞤲 (𞤚𞤮𞤳𞤭𞤴𞤮)', 'Asia/Tomsk' => '𞤈𞤮𞥅𞤧𞤭𞤴𞤢 𞤑𞤭𞤶𞤮𞥅𞤪𞤫 (𞤚𞤮𞤥𞤧𞤵𞤳𞤵)', 'Asia/Ulaanbaatar' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤓𞤤𞤢𞤲𞤦𞤢𞤼𞤢𞤪', 'Asia/Urumqi' => '𞤕𞤢𞤴𞤲𞤢 𞤑𞤭𞤶𞤮𞥅𞤪𞤫 (𞤓𞤪𞤵𞤥𞤳𞤵)', 'Asia/Ust-Nera' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤜𞤭𞤤𞤢𞤾𞤮𞤧𞤼𞤮𞤳 (𞤓𞤧𞤼𞤢-𞤐𞤫𞤪𞤢)', 'Asia/Vientiane' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤋𞤲𞤣𞤮𞤧𞤭𞥅𞤲 (𞤜𞤭𞤴𞤫𞤲𞤷𞤢𞥄𞤲)', 'Asia/Vladivostok' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤜𞤭𞤤𞤢𞤾𞤮𞤧𞤼𞤮𞤳 (𞤜𞤭𞤤𞤢𞤣𞤭𞤾𞤮𞤧𞤼𞤮𞥅𞤳𞤵)', 'Asia/Yakutsk' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤒𞤢𞤳𞤢𞤼𞤭𞤧𞤳𞤵 (𞤒𞤢𞤳𞤵𞤼𞤵𞤧𞤳𞤵)', 'Asia/Yekaterinburg' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤒𞤫𞤳𞤢𞤼𞤫𞤪𞤭𞤲𞤦𞤵𞤪𞤺𞤵 (𞤒𞤢𞤳𞤢𞤼𞤫𞤪𞤭𞤲𞤦𞤵𞤪𞤺𞤵)', 'Asia/Yerevan' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞤪𞤥𞤫𞤲𞤭𞤴𞤢𞥄 (𞤒𞤫𞤪𞤫𞤾𞤢𞥄𞤲)', 'Atlantic/Azores' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞥁𞤮𞤪𞤫𞤧 (𞤀𞥁𞤮𞤪𞤫𞥅𞤧)', 'Atlantic/Bermuda' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤫𞤳𞤵 (𞤄𞤢𞤪𞤥𞤵𞥅𞤣𞤢)', 'Atlantic/Canary' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤑𞤢𞤲𞤢𞤪𞤭)', 'Atlantic/Cape_Verde' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤑𞤢𞥄𞤦𞤮-𞤜𞤫𞤪𞤣𞤫', 'Atlantic/Faeroe' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤊𞤢𞤪𞤮𞥅)', 'Atlantic/Madeira' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤃𞤢𞤴𞤣𞤫𞤪𞤢)', 'Atlantic/Reykjavik' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤈𞤫𞤴𞤳𞤢𞤾𞤭𞤳𞤭)', 'Atlantic/South_Georgia' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤅𞤢𞤱𞤬-𞤔𞤮𞤪𞤶𞤭𞤴𞤢𞥄', 'Atlantic/St_Helena' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤅𞤫𞤲𞤼-𞤖𞤫𞤤𞤫𞤲𞤢𞥄)', 'Atlantic/Stanley' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤮𞤤𞤳𞤤𞤢𞤲𞤣-𞤀𞤴𞤤𞤢𞤲𞤣 (𞤅𞤭𞤼𞤢𞤲𞤤𞤫𞥅)', 'Australia/Adelaide' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤌𞤧𞤼𞤢𞤪𞤤𞤭𞤴𞤢𞥄 (𞤀𞤣𞤢𞤤𞤢𞤴𞤣𞤭)', 'Australia/Brisbane' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤌𞤧𞤼𞤢𞤪𞤤𞤭𞤴𞤢𞥄 (𞤄𞤭𞤪𞤧𞤭𞤦𞤢𞥄𞤲𞤵)', 'Australia/Broken_Hill' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤌𞤧𞤼𞤢𞤪𞤤𞤭𞤴𞤢𞥄 (𞤄𞤪𞤮𞤳𞤭𞤲-𞤖𞤭𞥅𞤤)', 'Australia/Darwin' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤌𞤧𞤼𞤢𞤪𞤤𞤭𞤴𞤢𞥄 (𞤁𞤢𞥄𞤪𞤱𞤭𞤲)', 'Australia/Eucla' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤌𞤧𞤼𞤢𞤪𞤤𞤭𞤴𞤢𞥄 (𞤓𞥅𞤳𞤵𞤤𞤢)', 'Australia/Hobart' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤌𞤧𞤼𞤢𞤪𞤤𞤭𞤴𞤢𞥄 (𞤖𞤵𞥅𞤦𞤢𞤪𞤼𞤵)', 'Australia/Lindeman' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤌𞤧𞤼𞤢𞤪𞤤𞤭𞤴𞤢𞥄 (𞤂𞤭𞤲𞤣𞤭𞥅𞤥𞤢𞥄𞤲)', 'Australia/Lord_Howe' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤂𞤮𞤪𞤣𞤵-𞤖𞤮𞤱𞤫', 'Australia/Melbourne' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤌𞤧𞤼𞤢𞤪𞤤𞤭𞤴𞤢𞥄 (𞤃𞤫𞤤𞤦𞤵𞥅𞤪𞤲𞤵)', 'Australia/Perth' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤌𞤧𞤼𞤢𞤪𞤤𞤭𞤴𞤢𞥄 (𞤆𞤫𞤪𞤧𞤭)', 'Australia/Sydney' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤌𞤧𞤼𞤢𞤪𞤤𞤭𞤴𞤢𞥄 (𞤅𞤭𞤣𞤲𞤫𞥅)', 'Etc/GMT' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧', 'Etc/UTC' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞤤𞥆𞤢𞤲𞤳𞤮𞥅𞤪𞤫 𞤊𞤮𞤲𞤣𞤢𞥄𞤲𞤣𞤫', 'Europe/Amsterdam' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤀𞤥𞤧𞤭𞤼𞤫𞤪𞤣𞤢𞥄𞤥)', 'Europe/Andorra' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤀𞤲𞤣𞤮𞥅𞤪𞤢)', 'Europe/Astrakhan' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤃𞤮𞤧𞤳𞤮 (𞤀𞤧𞤼𞤢𞤪𞤿𞤢𞥄𞤲)', 'Europe/Athens' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤀𞤼𞤫𞤲𞤧𞤭)', 'Europe/Belgrade' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤄𞤫𞤤𞤺𞤢𞤪𞤢𞥄𞤣)', 'Europe/Berlin' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤄𞤫𞤪𞤤𞤫𞤲)', 'Europe/Bratislava' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤄𞤢𞤪𞤢𞤼𞤭𞤧𞤤𞤢𞤾𞤢)', 'Europe/Brussels' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤄𞤭𞤪𞤭𞤳𞤧𞤫𞤤)', 'Europe/Bucharest' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤄𞤵𞤳𞤢𞤪𞤫𞤧𞤼𞤭)', 'Europe/Budapest' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤄𞤵𞤣𞤢𞤨𞤫𞤧𞤼)', 'Europe/Busingen' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤄𞤵𞤧𞤭𞤲𞤶𞤫𞤲)', 'Europe/Chisinau' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤕𞤭𞤧𞤭𞥅𞤲𞤮𞤱𞤢)', 'Europe/Copenhagen' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤑𞤮𞤨𞤫𞤲𞥆𞤢𞥄𞤺)', 'Europe/Dublin' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤁𞤵𞤦𞤵𞤤𞤫𞤲)', 'Europe/Gibraltar' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤔𞤭𞤦𞤢𞤪𞤢𞤤𞤼𞤢𞤪)', 'Europe/Guernsey' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤔𞤭𞤪𞤲𞤭𞤧𞤫𞤴)', 'Europe/Helsinki' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤖𞤫𞤤𞤧𞤭𞤲𞤳𞤭)', 'Europe/Isle_of_Man' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤅𞤵𞤪𞤭𞥅𞤪𞤫-𞤃𞤢𞥄𞤲)', 'Europe/Istanbul' => '𞤚𞤵𞤪𞤳𞤭𞤴𞤢𞥄 𞤑𞤭𞤶𞤮𞥅𞤪𞤫 (𞤋𞤧𞤼𞤢𞤥𞤦𞤵𞤤)', 'Europe/Jersey' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤔𞤫𞤪𞤧𞤭𞥅)', 'Europe/Kaliningrad' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤑𞤢𞤤𞤭𞤲𞤺𞤢𞤪𞤣)', 'Europe/Kiev' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤑𞤭𞤴𞤫𞥅𞤾)', 'Europe/Kirov' => '𞤈𞤮𞥅𞤧𞤭𞤴𞤢 𞤑𞤭𞤶𞤮𞥅𞤪𞤫 (𞤑𞤭𞤪𞤮𞥅𞤾𞤵)', 'Europe/Lisbon' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤂𞤭𞤧𞤦𞤮𞥅𞤲)', 'Europe/Ljubljana' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤋𞤶𞤵𞤦𞤵𞤤𞤶𞤢𞤲𞤢)', 'Europe/London' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤳𞤭𞤲𞤭𞥅𞤲𞥋𞤣𞤫 𞤘𞤪𞤭𞤲𞤱𞤭𞥅𞤧 (𞤂𞤮𞤲𞤣𞤮𞤲)', 'Europe/Luxembourg' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤂𞤭𞤳𞤧𞤢𞤲𞤦𞤵𞤪𞤺𞤵)', 'Europe/Madrid' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤃𞤢𞤣𞤭𞤪𞤭𞤣)', 'Europe/Malta' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤃𞤢𞤤𞤼𞤢)', 'Europe/Mariehamn' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤃𞤢𞤪𞤭𞤴𞤢𞤸𞤢𞥄𞤥𞤢𞥄𞤲)', 'Europe/Minsk' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤃𞤮𞤧𞤳𞤮 (𞤃𞤭𞤲𞤧𞤭𞤳𞤭)', 'Europe/Monaco' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤃𞤮𞤲𞤢𞤳𞤮𞤸)', 'Europe/Moscow' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤃𞤮𞤧𞤳𞤮', 'Europe/Oslo' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤌𞤧𞤤𞤮𞤸)', 'Europe/Paris' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤆𞤢𞤪𞤭)', 'Europe/Podgorica' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤆𞤮𞤣𞤭𞤺𞤮𞤪𞤭𞤳𞤢)', 'Europe/Prague' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤆𞤢𞤪𞤢𞥄𞤺𞤭)', 'Europe/Riga' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤈𞤭𞤺𞤢)', 'Europe/Rome' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤈𞤮𞥅𞤥𞤵)', 'Europe/Samara' => '𞤈𞤮𞥅𞤧𞤭𞤴𞤢 𞤑𞤭𞤶𞤮𞥅𞤪𞤫 (𞤅𞤢𞤥𞤢𞤪𞤢)', 'Europe/San_Marino' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤅𞤢𞤲-𞤃𞤢𞤪𞤭𞤲𞤮)', 'Europe/Sarajevo' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤅𞤢𞤪𞤢𞤴𞤫𞤾𞤮𞥅)', 'Europe/Saratov' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤃𞤮𞤧𞤳𞤮 (𞤅𞤢𞤪𞤢𞤼𞤮𞥅𞤾)', 'Europe/Simferopol' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤃𞤮𞤧𞤳𞤮 (𞤅𞤭𞤥𞤬𞤫𞤪𞤨𞤮𞥅𞤤)', 'Europe/Skopje' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤅𞤭𞤳𞤮𞥅𞤨𞤭𞤴𞤢)', 'Europe/Sofia' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤅𞤮𞤬𞤭𞤴𞤢)', 'Europe/Stockholm' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤅𞤭𞤼𞤮𞤳𞤮𞤤𞤥𞤵)', 'Europe/Tallinn' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤚𞤢𞤤𞤭𞥅𞤲𞤵)', 'Europe/Tirane' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤚𞤭𞤪𞤢𞤲𞤢)', 'Europe/Ulyanovsk' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤃𞤮𞤧𞤳𞤮 (𞤓𞤤𞤴𞤢𞤲𞤮𞤾𞤮𞤧𞤳𞤵)', 'Europe/Vaduz' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤜𞤢𞤣𞤵𞥅𞤶𞤵)', 'Europe/Vatican' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤜𞤢𞤼𞤭𞤳𞤢𞤲)', 'Europe/Vienna' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤜𞤭𞤴𞤫𞤲𞤢𞥄)', 'Europe/Vilnius' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤜𞤫𞤤𞤲𞤵𞥅𞤧)', 'Europe/Volgograd' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤜𞤮𞤤𞤺𞤮𞤺𞤢𞤪𞤢𞥄𞤣 (𞤜𞤮𞤤𞤺𞤮𞤺𞤢𞤪𞤢𞤣)', 'Europe/Warsaw' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤏𞤢𞤪𞤧𞤮)', 'Europe/Zagreb' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤟𞤢𞤺𞤪𞤫𞤦𞤵)', 'Europe/Zurich' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤪𞤮𞥅𞤦𞤢 (𞤟𞤵𞤪𞤵𞤳)', 'Indian/Antananarivo' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤀𞤲𞤼𞤢𞤲𞤢𞤲𞤢𞤪𞤭𞥅𞤾𞤮𞥅)', 'Indian/Chagos' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤃𞤢𞥄𞤴𞤮 𞤋𞤲𞤣𞤭𞤴𞤢𞤱𞤮 (𞤅𞤢𞤺𞤮𞤧)', 'Indian/Christmas' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤅𞤵𞤪𞤭𞥅𞤪𞤫 𞤑𞤭𞤪𞤧𞤭𞤥𞤢𞥄𞤧', 'Indian/Cocos' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤑𞤮𞥅𞤳𞤮𞤧', 'Indian/Comoro' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤑𞤮𞤥𞤮𞥅𞤪𞤮)', 'Indian/Kerguelen' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤂𞤫𞤴𞤪𞤭 𞤊𞤪𞤢𞤲𞤧𞤭 & 𞤀𞤪𞤼𞤢𞤲𞤼𞤭𞤳𞤢 (𞤑𞤫𞤪𞤺𞤫𞤤𞤫𞤲)', 'Indian/Mahe' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤅𞤫𞤴𞤭𞤧𞤫𞤤 (𞤃𞤢𞤸𞤫𞥅)', 'Indian/Maldives' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤃𞤢𞤤𞤣𞤢𞥄𞤴𞤭𞤧', 'Indian/Mauritius' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤃𞤮𞤪𞤭𞥅𞤧𞤭', 'Indian/Mayotte' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢𞥄 (𞤃𞤢𞤴𞤮𞥅𞤼𞤵)', 'Indian/Reunion' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤈𞤫𞤲𞤭𞤴𞤮𞤲 (𞤈𞤫𞥅𞤲𞤭𞤴𞤮𞤲)', 'Pacific/Apia' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤀𞥄𞤨𞤭𞤴𞤢', 'Pacific/Auckland' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤐𞤫𞤱-𞤟𞤫𞤤𞤢𞤲𞤣𞤭 (𞤌𞤳𞤤𞤢𞤲𞤣𞤭)', 'Pacific/Bougainville' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤆𞤢𞤨𞤵𞤱𞤢 𞤘𞤭𞤲𞤫 𞤖𞤫𞤧𞤮 (𞤄𞤵𞤺𞤫𞤲𞤾𞤭𞥅𞤤)', 'Pacific/Chatham' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤕𞤢𞤼𞤢𞥄𞤥 (𞤕𞤢𞥃𞤢𞥄𞤥)', 'Pacific/Easter' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤋𞤧𞤼𞤮𞥅-𞤀𞤴𞤤𞤢𞤲𞤣', 'Pacific/Efate' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤜𞤢𞤲𞤵𞤱𞤢𞥄𞤼𞤵 (𞤉𞤬𞤢𞤼𞤵)', 'Pacific/Enderbury' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤕𞤵𞤪𞤭𞥅𞤶𞤫 𞤊𞤫𞤲𞤭𞤳𞤧𞤭 (𞤉𞤲𞤣𞤫𞤪𞤦𞤵𞥅𞤪𞤭)', 'Pacific/Fakaofo' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤮𞥅𞤳𞤮𞤤𞤢𞥄𞤱𞤵 (𞤊𞤢𞤳𞤢𞤱𞤬𞤮)', 'Pacific/Fiji' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤊𞤭𞤶𞤭𞥅', 'Pacific/Funafuti' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤵𞥅𞤾𞤢𞤤𞤵 (𞤊𞤵𞤲𞤢𞤬𞤵𞤼𞤭𞥅)', 'Pacific/Galapagos' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤘𞤢𞤤𞤢𞤨𞤢𞤺𞤮𞥅𞤧 (𞤘𞤢𞤤𞤢𞤨𞤢𞤺𞤮𞤧)', 'Pacific/Gambier' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤘𞤢𞤥𞤦𞤭𞤴𞤫𞤪', 'Pacific/Guadalcanal' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤕𞤵𞤪𞤭𞥅𞤶𞤫 𞤅𞤵𞤤𞤢𞤴𞤥𞤢𞥄𞤲 (𞤘𞤵𞤱𞤢𞤣𞤢𞤤𞤳𞤢𞤲𞤢𞤤)', 'Pacific/Guam' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤱𞤪𞤵𞤲𞥋𞤣𞤫 𞤕𞤮𞤥𞤮𞥅𞤪𞤮 (𞤘𞤵𞤱𞤢𞥄𞤥)', 'Pacific/Honolulu' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤱𞤢𞥄𞤴𞤭𞥅-𞤀𞤤𞤮𞤧𞤭𞤴𞤢𞤲 (Honolulu)', 'Pacific/Kiritimati' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤕𞤵𞤪𞤭𞥅𞤶𞤫 𞤂𞤢𞤴𞤲𞤵 (𞤑𞤭𞤪𞤭𞤼𞤭𞤥𞤢𞤼𞤭)', 'Pacific/Kosrae' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤑𞤮𞤧𞤪𞤢𞤴 (𞤑𞤮𞤧𞤪𞤫𞤴)', 'Pacific/Kwajalein' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤅𞤵𞤪𞤭𞥅𞤶𞤫 𞤃𞤢𞤪𞤧𞤢𞤤 (𞤑𞤢𞤱𞤢𞤶𞤢𞤤𞤭𞥅𞤲)', 'Pacific/Majuro' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤅𞤵𞤪𞤭𞥅𞤶𞤫 𞤃𞤢𞤪𞤧𞤢𞤤 (𞤃𞤢𞤶𞤵𞤪𞤮𞥅)', 'Pacific/Marquesas' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤃𞤢𞤪𞤳𞤫𞤧𞤢𞤧 (𞤃𞤢𞤪𞤳𞤫𞤧𞤢𞥄𞤧)', 'Pacific/Midway' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤅𞤢𞤥𞤵𞤱𞤢 (𞤃𞤭𞤣𞤱𞤫𞥅)', 'Pacific/Nauru' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤐𞤵𞥅𞤪𞤵 (𞤐𞤢𞤱𞤪𞤵)', 'Pacific/Niue' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤐𞤵𞥅𞤱𞤭', 'Pacific/Norfolk' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤅𞤵𞤪𞤭𞥅𞤪𞤫 𞤐𞤮𞤪𞤬𞤮𞤤𞤳𞤵', 'Pacific/Noumea' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤑𞤢𞤤𞤭𞤣𞤮𞤲𞤭𞤴𞤢𞥄 𞤖𞤫𞤧𞤮 (𞤐𞤵𞤥𞤫𞤴𞤢)', 'Pacific/Pago_Pago' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤅𞤢𞤥𞤵𞤱𞤢 (𞤆𞤢𞤺𞤮-𞤆𞤢𞤺𞤮)', 'Pacific/Palau' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤆𞤮𞤤𞤢𞥄𞤱𞤮 (𞤆𞤢𞤤𞤢𞤱)', 'Pacific/Pitcairn' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤆𞤭𞤼𞤳𞤭𞥅𞤪𞤲𞤵', 'Pacific/Ponape' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤆𞤮𞤲𞤢𞥄𞤨𞤫 (𞤆𞤮𞤥𞤨𞤫𞥅)', 'Pacific/Port_Moresby' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤆𞤢𞤨𞤵𞤱𞤢 𞤘𞤭𞤲𞤫 𞤖𞤫𞤧𞤮 (𞤆𞤮𞤪𞤼𞤵-𞤃𞤮𞤪𞤫𞤧𞤦𞤭)', 'Pacific/Rarotonga' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤕𞤵𞤪𞤭𞥅𞤶𞤫 𞤑𞤵𞥅𞤳 (𞤈𞤢𞤪𞤮𞤼𞤮𞤲𞤺𞤢)', 'Pacific/Saipan' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤖𞤢𞤱𞤪𞤵𞤲𞥋𞤣𞤫 𞤕𞤮𞤥𞤮𞥅𞤪𞤮 (𞤅𞤢𞤴𞤨𞤢𞥄𞤲)', 'Pacific/Tahiti' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤢𞤸𞤭𞤼𞤭𞥅', 'Pacific/Tarawa' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤅𞤵𞤪𞤭𞥅𞤶𞤫 𞤘𞤭𞤤𞤦𞤫𞤪𞤼𞤵 (𞤚𞤫𞥅𞤪𞤢𞤱𞤢)', 'Pacific/Tongatapu' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤚𞤮𞤲𞤺𞤢 (𞤚𞤮𞤲𞤺𞤢𞤼𞤢𞥄𞤨𞤵)', 'Pacific/Truk' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤕𞤵𞥅𞤳𞤵', 'Pacific/Wake' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤅𞤵𞤪𞤭𞥅𞤪𞤫 𞤏𞤫𞥅𞤳𞤵', 'Pacific/Wallis' => '𞤑𞤭𞤶𞤮𞥅𞤪𞤫 𞤏𞤢𞤤𞥆𞤭𞥅𞤧 & 𞤊𞤵𞤼𞤵𞤲𞤢'], 'Meta' => ['GmtFormat' => '𞤑𞤖𞤘%s']];
