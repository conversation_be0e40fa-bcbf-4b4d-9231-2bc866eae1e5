(()=>{var e={51113:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var n=a(86087);const c=(0,n.forwardRef)((function({icon:e,size:t=24,...a},c){return(0,n.cloneElement)(e,{width:t,height:t,...a,ref:c})}))},25877:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(5573),c=a(10790);const s=(0,c.jsx)(n.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,c.jsx)(n.Path,{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm.5 16c0 .3-.2.5-.5.5H5c-.3 0-.5-.2-.5-.5V7h15v12zM9 10H7v2h2v-2zm0 4H7v2h2v-2zm4-4h-2v2h2v-2zm4 0h-2v2h2v-2zm-4 4h-2v2h2v-2zm4 0h-2v2h2v-2z"})})},23751:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(5573),c=a(10790);const s=(0,c.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(n.Path,{d:"M12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4ZM12.75 8V13H11.25V8H12.75ZM12.75 14.5V16H11.25V14.5H12.75Z"})})},83883:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(5573),c=a(10790);const s=(0,c.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(n.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})})},64969:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(5573),c=a(10790);const s=(0,c.jsx)(n.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,c.jsx)(n.Path,{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"})})},98248:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(5573),c=a(10790);const s=(0,c.jsx)(n.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,c.jsx)(n.Path,{d:"M6.5 12.4L12 8l5.5 4.4-.9 1.2L12 10l-4.5 3.6-1-1.2z"})})},31249:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(5573),c=a(10790);const s=(0,c.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(n.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})})},10991:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(5573),c=a(10790);const s=(0,c.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(n.Path,{d:"m13.06 12 6.47-6.47-1.06-1.06L12 10.94 5.53 4.47 4.47 5.53 10.94 12l-6.47 6.47 1.06 1.06L12 13.06l6.47 6.47 1.06-1.06L13.06 12Z"})})},53512:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(5573),c=a(10790);const s=(0,c.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(n.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})})},19783:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(5573),c=a(10790);const s=(0,c.jsx)(n.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,c.jsx)(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M5.5 12a6.5 6.5 0 1 0 13 0 6.5 6.5 0 0 0-13 0ZM12 4a8 8 0 1 0 0 16 8 8 0 0 0 0-16Zm.75 4v1.5h-1.5V8h1.5Zm0 8v-5h-1.5v5h1.5Z"})})},76673:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(5573),c=a(10790);const s=(0,c.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 5.5A2.25 2.25 0 0 0 9.878 7h4.244A2.251 2.251 0 0 0 12 5.5ZM12 4a3.751 3.751 0 0 0-3.675 3H5v1.5h1.27l.818 8.997a2.75 2.75 0 0 0 2.739 2.501h4.347a2.75 2.75 0 0 0 2.738-2.5L17.73 8.5H19V7h-3.325A3.751 3.751 0 0 0 12 4Zm4.224 4.5H7.776l.806 8.861a1.25 1.25 0 0 0 1.245 1.137h4.347a1.25 1.25 0 0 0 1.245-1.137l.805-8.861Z"})})},93936:(e,t,a)=>{"use strict";a.d(t,{M_:()=>n.M});var n=a(3282)},92433:(e,t,a)=>{"use strict";a.r(t),a.d(t,{closeModal:()=>v,disableComplementaryArea:()=>o,enableComplementaryArea:()=>l,openModal:()=>g,pinItem:()=>d,setDefaultComplementaryArea:()=>r,setFeatureDefaults:()=>h,setFeatureValue:()=>m,toggleFeature:()=>p,unpinItem:()=>u});var n=a(64040),c=a.n(n),s=a(41233),i=a(33837);const r=(e,t)=>({type:"SET_DEFAULT_COMPLEMENTARY_AREA",scope:e=(0,i.F)(e),area:t=(0,i.M)(e,t)}),l=(e,t)=>({registry:a,dispatch:n})=>{if(!t)return;e=(0,i.F)(e),t=(0,i.M)(e,t);a.select(s.store).get(e,"isComplementaryAreaVisible")||a.dispatch(s.store).set(e,"isComplementaryAreaVisible",!0),n({type:"ENABLE_COMPLEMENTARY_AREA",scope:e,area:t})},o=e=>({registry:t})=>{e=(0,i.F)(e);t.select(s.store).get(e,"isComplementaryAreaVisible")&&t.dispatch(s.store).set(e,"isComplementaryAreaVisible",!1)},d=(e,t)=>({registry:a})=>{if(!t)return;e=(0,i.F)(e),t=(0,i.M)(e,t);const n=a.select(s.store).get(e,"pinnedItems");!0!==n?.[t]&&a.dispatch(s.store).set(e,"pinnedItems",{...n,[t]:!0})},u=(e,t)=>({registry:a})=>{if(!t)return;e=(0,i.F)(e),t=(0,i.M)(e,t);const n=a.select(s.store).get(e,"pinnedItems");a.dispatch(s.store).set(e,"pinnedItems",{...n,[t]:!1})};function p(e,t){return function({registry:a}){c()("dispatch( 'core/interface' ).toggleFeature",{since:"6.0",alternative:"dispatch( 'core/preferences' ).toggle"}),a.dispatch(s.store).toggle(e,t)}}function m(e,t,a){return function({registry:n}){c()("dispatch( 'core/interface' ).setFeatureValue",{since:"6.0",alternative:"dispatch( 'core/preferences' ).set"}),n.dispatch(s.store).set(e,t,!!a)}}function h(e,t){return function({registry:a}){c()("dispatch( 'core/interface' ).setFeatureDefaults",{since:"6.0",alternative:"dispatch( 'core/preferences' ).setDefaults"}),a.dispatch(s.store).setDefaults(e,t)}}function g(e){return{type:"OPEN_MODAL",name:e}}function v(){return{type:"CLOSE_MODAL"}}},56675:(e,t,a)=>{"use strict";a.d(t,{E:()=>n});const n="core/interface"},33837:(e,t,a)=>{"use strict";a.d(t,{F:()=>s,M:()=>i});var n=a(64040),c=a.n(n);function s(e){return["core/edit-post","core/edit-site"].includes(e)?(c()(`${e} interface scope`,{alternative:"core interface scope",hint:"core/edit-post and core/edit-site are merging.",version:"6.6"}),"core"):e}function i(e,t){return"core"===e&&"edit-site/template"===t?(c()("edit-site/template sidebar",{alternative:"edit-post/document",version:"6.6"}),"edit-post/document"):"core"===e&&"edit-site/block-inspector"===t?(c()("edit-site/block-inspector sidebar",{alternative:"edit-post/block",version:"6.6"}),"edit-post/block"):t}},3282:(e,t,a)=>{"use strict";a.d(t,{M:()=>l});var n=a(47143),c=a(92433),s=a(904),i=a(86878),r=a(56675);const l=(0,n.createReduxStore)(r.E,{reducer:i.Ay,actions:c,selectors:s});(0,n.register)(l)},86878:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>c});var n=a(47143);const c=(0,n.combineReducers)({complementaryAreas:function(e={},t){switch(t.type){case"SET_DEFAULT_COMPLEMENTARY_AREA":{const{scope:a,area:n}=t;return e[a]?e:{...e,[a]:n}}case"ENABLE_COMPLEMENTARY_AREA":{const{scope:a,area:n}=t;return{...e,[a]:n}}}return e},activeModal:function(e=null,t){switch(t.type){case"OPEN_MODAL":return t.name;case"CLOSE_MODAL":return null}return e}})},904:(e,t,a)=>{"use strict";a.r(t),a.d(t,{getActiveComplementaryArea:()=>l,isComplementaryAreaLoading:()=>o,isFeatureActive:()=>u,isItemPinned:()=>d,isModalActive:()=>p});var n=a(47143),c=a(64040),s=a.n(c),i=a(41233),r=a(33837);const l=(0,n.createRegistrySelector)((e=>(t,a)=>{a=(0,r.F)(a);const n=e(i.store).get(a,"isComplementaryAreaVisible");if(void 0!==n)return!1===n?null:t?.complementaryAreas?.[a]})),o=(0,n.createRegistrySelector)((e=>(t,a)=>{a=(0,r.F)(a);const n=e(i.store).get(a,"isComplementaryAreaVisible"),c=t?.complementaryAreas?.[a];return n&&void 0===c})),d=(0,n.createRegistrySelector)((e=>(t,a,n)=>{var c;a=(0,r.F)(a),n=(0,r.M)(a,n);const s=e(i.store).get(a,"pinnedItems");return null===(c=s?.[n])||void 0===c||c})),u=(0,n.createRegistrySelector)((e=>(t,a,n)=>(s()("select( 'core/interface' ).isFeatureActive( scope, featureName )",{since:"6.0",alternative:"select( 'core/preferences' ).get( scope, featureName )"}),!!e(i.store).get(a,n))));function p(e,t){return e.activeModal===t}},46941:(e,t,a)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const a="color: "+this.color;t.splice(1,0,a,"color: inherit");let n=0,c=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(n++,"%c"===e&&(c=n))})),t.splice(c,0,a)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=a(53212)(t);const{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},53212:(e,t,a)=>{e.exports=function(e){function t(e){let a,c,s,i=null;function r(...e){if(!r.enabled)return;const n=r,c=Number(new Date),s=c-(a||c);n.diff=s,n.prev=a,n.curr=c,a=c,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((a,c)=>{if("%%"===a)return"%";i++;const s=t.formatters[c];if("function"==typeof s){const t=e[i];a=s.call(n,t),e.splice(i,1),i--}return a})),t.formatArgs.call(n,e);(n.log||t.log).apply(n,e)}return r.namespace=e,r.useColors=t.useColors(),r.color=t.selectColor(e),r.extend=n,r.destroy=t.destroy,Object.defineProperty(r,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==i?i:(c!==t.namespaces&&(c=t.namespaces,s=t.enabled(e)),s),set:e=>{i=e}}),"function"==typeof t.init&&t.init(r),r}function n(e,a){const n=t(this.namespace+(void 0===a?":":a)+e);return n.log=this.log,n}function c(e,t){let a=0,n=0,c=-1,s=0;for(;a<e.length;)if(n<t.length&&(t[n]===e[a]||"*"===t[n]))"*"===t[n]?(c=n,s=a,n++):(a++,n++);else{if(-1===c)return!1;n=c+1,s++,a=s}for(;n<t.length&&"*"===t[n];)n++;return n===t.length}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names,...t.skips.map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const a=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of a)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const a of t.skips)if(c(e,a))return!1;for(const a of t.names)if(c(e,a))return!0;return!1},t.humanize=a(44997),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((a=>{t[a]=e[a]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let a=0;for(let t=0;t<e.length;t++)a=(a<<5)-a+e.charCodeAt(t),a|=0;return t.colors[Math.abs(a)%t.colors.length]},t.enable(t.load()),t}},2467:e=>{"use strict";var t,a="object"==typeof Reflect?Reflect:null,n=a&&"function"==typeof a.apply?a.apply:function(e,t,a){return Function.prototype.apply.call(e,t,a)};t=a&&"function"==typeof a.ownKeys?a.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var c=Number.isNaN||function(e){return e!=e};function s(){s.init.call(this)}e.exports=s,e.exports.once=function(e,t){return new Promise((function(a,n){function c(a){e.removeListener(t,s),n(a)}function s(){"function"==typeof e.removeListener&&e.removeListener("error",c),a([].slice.call(arguments))}g(e,t,s,{once:!0}),"error"!==t&&function(e,t,a){"function"==typeof e.on&&g(e,"error",t,a)}(e,c,{once:!0})}))},s.EventEmitter=s,s.prototype._events=void 0,s.prototype._eventsCount=0,s.prototype._maxListeners=void 0;var i=10;function r(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function l(e){return void 0===e._maxListeners?s.defaultMaxListeners:e._maxListeners}function o(e,t,a,n){var c,s,i,o;if(r(a),void 0===(s=e._events)?(s=e._events=Object.create(null),e._eventsCount=0):(void 0!==s.newListener&&(e.emit("newListener",t,a.listener?a.listener:a),s=e._events),i=s[t]),void 0===i)i=s[t]=a,++e._eventsCount;else if("function"==typeof i?i=s[t]=n?[a,i]:[i,a]:n?i.unshift(a):i.push(a),(c=l(e))>0&&i.length>c&&!i.warned){i.warned=!0;var d=new Error("Possible EventEmitter memory leak detected. "+i.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");d.name="MaxListenersExceededWarning",d.emitter=e,d.type=t,d.count=i.length,o=d,console&&console.warn&&console.warn(o)}return e}function d(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function u(e,t,a){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:a},c=d.bind(n);return c.listener=a,n.wrapFn=c,c}function p(e,t,a){var n=e._events;if(void 0===n)return[];var c=n[t];return void 0===c?[]:"function"==typeof c?a?[c.listener||c]:[c]:a?function(e){for(var t=new Array(e.length),a=0;a<t.length;++a)t[a]=e[a].listener||e[a];return t}(c):h(c,c.length)}function m(e){var t=this._events;if(void 0!==t){var a=t[e];if("function"==typeof a)return 1;if(void 0!==a)return a.length}return 0}function h(e,t){for(var a=new Array(t),n=0;n<t;++n)a[n]=e[n];return a}function g(e,t,a,n){if("function"==typeof e.on)n.once?e.once(t,a):e.on(t,a);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function c(s){n.once&&e.removeEventListener(t,c),a(s)}))}}Object.defineProperty(s,"defaultMaxListeners",{enumerable:!0,get:function(){return i},set:function(e){if("number"!=typeof e||e<0||c(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");i=e}}),s.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},s.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||c(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},s.prototype.getMaxListeners=function(){return l(this)},s.prototype.emit=function(e){for(var t=[],a=1;a<arguments.length;a++)t.push(arguments[a]);var c="error"===e,s=this._events;if(void 0!==s)c=c&&void 0===s.error;else if(!c)return!1;if(c){var i;if(t.length>0&&(i=t[0]),i instanceof Error)throw i;var r=new Error("Unhandled error."+(i?" ("+i.message+")":""));throw r.context=i,r}var l=s[e];if(void 0===l)return!1;if("function"==typeof l)n(l,this,t);else{var o=l.length,d=h(l,o);for(a=0;a<o;++a)n(d[a],this,t)}return!0},s.prototype.addListener=function(e,t){return o(this,e,t,!1)},s.prototype.on=s.prototype.addListener,s.prototype.prependListener=function(e,t){return o(this,e,t,!0)},s.prototype.once=function(e,t){return r(t),this.on(e,u(this,e,t)),this},s.prototype.prependOnceListener=function(e,t){return r(t),this.prependListener(e,u(this,e,t)),this},s.prototype.removeListener=function(e,t){var a,n,c,s,i;if(r(t),void 0===(n=this._events))return this;if(void 0===(a=n[e]))return this;if(a===t||a.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete n[e],n.removeListener&&this.emit("removeListener",e,a.listener||t));else if("function"!=typeof a){for(c=-1,s=a.length-1;s>=0;s--)if(a[s]===t||a[s].listener===t){i=a[s].listener,c=s;break}if(c<0)return this;0===c?a.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(a,c),1===a.length&&(n[e]=a[0]),void 0!==n.removeListener&&this.emit("removeListener",e,i||t)}return this},s.prototype.off=s.prototype.removeListener,s.prototype.removeAllListeners=function(e){var t,a,n;if(void 0===(a=this._events))return this;if(void 0===a.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==a[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete a[e]),this;if(0===arguments.length){var c,s=Object.keys(a);for(n=0;n<s.length;++n)"removeListener"!==(c=s[n])&&this.removeAllListeners(c);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=a[e]))this.removeListener(e,t);else if(void 0!==t)for(n=t.length-1;n>=0;n--)this.removeListener(e,t[n]);return this},s.prototype.listeners=function(e){return p(this,e,!0)},s.prototype.rawListeners=function(e){return p(this,e,!1)},s.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):m.call(e,t)},s.prototype.listenerCount=m,s.prototype.eventNames=function(){return this._eventsCount>0?t(this._events):[]}},9839:()=>{},34436:()=>{},19994:()=>{},59835:()=>{},18546:()=>{},7628:()=>{},87748:()=>{},53290:()=>{},15583:()=>{},81500:()=>{},88904:()=>{},34006:()=>{},57482:()=>{},7683:()=>{},9944:()=>{},38742:()=>{},9383:()=>{},44288:()=>{},39653:()=>{},26075:()=>{},25634:()=>{},49488:()=>{},34229:()=>{},54447:()=>{},19569:()=>{},65486:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={container:"demNsMJjhi7BLY7xhjU5","icon-wrapper":"QiUjdjJSkqh6nH7YMG5A","is-error":"Q080AHcq29J2fc68Hhk5",icon:"hYWbIwhppukXmGnsiT9H","is-warning":"JjHuxWly0HI9C60gorbq","is-info":"Cm8ZFHi3mngl4cj9Gatx","is-success":"ytGBsU015p3LGwOPwFDx"}},77560:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={button:"zI5tJ_qhWE6Oe6Lk75GY","is-icon-button":"tuBt2DLqimiImoqVzPqo",small:"Na39I683LAaSA99REg14",normal:"ipS7tKy9GntCS4R3vekF",icon:"paGLQwtPEaJmtArCcmyK",regular:"lZAo6_oGfclXOO9CC6Rd","full-width":"xJDOiJxTt0R_wSl8Ipz_",loading:"q_tVWqMjl39RcY6WtQA6","external-icon":"CDuBjJp_8jxzx5j6Nept"}},4459:()=>{},84813:()=>{},47842:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={"star-icon":"cuoSlhSNrqf1dozY22Xb",jetpack:"lAIiifeLMmZAPlQ9n9ZR","checkmark-icon":"JLquNpQVlysAamuh5lJO",socialIcon:"cbOwD8Y4tFjwimmtchQI",bluesky:"aLWBKY0yRghEk7tNCgK3",facebook:"aHOlEBGD5EA8NKRw3xTw",twitter:"af4Y_zItXvLAOEoSDPSv",linkedin:"f68aqF3XSD1OBvXR1get",tumblr:"xFI0dt3UiXRlRQdqPWkx",google:"q7JEoyymveP6kF747M43",mastodon:"DKOBOTVmTLbh26gUH_73",nextdoor:"n5XodNsuMfMAAvqHFmbw",instagram:"cL3m0xBYTYhIKI7lCqDB",whatsapp:"fftumuc_lJ6v0tq4UMVR",threads:"inzgC27qxdt7hSdhTWRI"}},59053:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={sm:"(max-width: 599px)",md:"(min-width: 600px) and (max-width: 959px)",lg:"(min-width: 960px)"}},22073:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={reset:"WQVtrU6q0L1Igcj7wCrQ","headline-medium":"UujoBFTnQNY2cWU2SIsH","headline-small":"TeGO5V_thHw5lDAm1_2M","headline-small-regular":"WolQzb2MsSgiNmLtc7_j","title-medium":"hUB0JT8p1T2Hw28N6qC8","title-medium-semi-bold":"gKZWDv5chz3_O3Syp74H","title-small":"zY2No8Ga4b8shbOQGhnv",body:"tIj0D1t8Cc892ikmgFPZ","body-small":"KdcN0BnOaVeVhyLRKqhS","body-extra-small":"dso3Rh3tl3Xv1GumBktz","body-extra-small-bold":"mQ1UlbN9u4Mg9byO8m7v",label:"PItlW5vRExLnTj4a8eLE","m-0":"TwRpPlktzxhmFVeua7P5","mx-0":"zVfqx7gyb3o9mxfGynn1","my-0":"iSHVzNiB9iVleGljaQxy","mt-0":"xqDIp6cNVr_E6RXaiPyD","mr-0":"S8EwaXk1kyPizt6x4WH2","mb-0":"ODX5Vr1TARoLFkDDFooD","ml-0":"cphJ8dCpfimnky7P2FHg","m-1":"PFgIhNxIyiSuNvQjAIYj","mx-1":"M2jKmUzDxvJjjVEPU3zn","my-1":"io15gAh8tMTNbSEfwJKk","mt-1":"rcTN5uw9xIEeMEGL3Xi_","mr-1":"CQSkybjq2TcRM1Xo9COV","mb-1":"hfqOWgq6_MEGdFE82eOY","ml-1":"I8MxZQYTbuu595yfesWA","m-2":"kQkc6rmdpvLKPkyoJtVQ","mx-2":"j6vFPxWuu4Jan2ldoxpp","my-2":"hqr39dC4H_AbactPAkCG","mt-2":"c3dQnMi16C6J6Ecy4283","mr-2":"YNZmHOuRo6hU7zzKfPdP","mb-2":"Db8lbak1_wunpPk8NwKU","ml-2":"ftsYE5J9hLzquQ0tA5dY","m-3":"Det4MHzLUW7EeDnafPzq","mx-3":"h_8EEAztC29Vve1datb5","my-3":"YXIXJ0h1k47u6hzK8KcM","mt-3":"soADBBkcIKCBXzCTuV9_","mr-3":"zSX59ziEaEWGjnpZa4uV","mb-3":"yrVTnq_WBMbejg89c2ZQ","ml-3":"UKtHPJnI2cXBWtPDm5hM","m-4":"guexok_Tqd5Tf52hRlbT","mx-4":"oS1E2KfTBZkJ3F0tN7T6","my-4":"DN1OhhXi6AoBgEdDSbGd","mt-4":"ot2kkMcYHv53hLZ4LSn0","mr-4":"A1krOZZhlQ6Sp8Cy4bly","mb-4":"pkDbXXXL32237M0hokEh","ml-4":"XXv4kDTGvEnQeuGKOPU3","m-5":"yGqHk1a57gaISwkXwXe6","mx-5":"X8cghM358X3DkXLc9aNK","my-5":"GdfSmGwHlFnN2S6xBn1f","mt-5":"yqeuzwyGQ7zG0avrGqi_","mr-5":"g9emeCkuHvYhveiJbfXO","mb-5":"Lvk3dqcyHbZ07QCRlrUQ","ml-5":"r3yQECDQ9qX0XZzXlVAg","m-6":"aQhlPwht2Cz1X_63Miw0","mx-6":"JyHb0vK3wJgpblL9s5j8","my-6":"cY2gULL1lAv6WPNIRuf3","mt-6":"NBWQ9Lwhh_fnry3lg_p7","mr-6":"yIOniNe5E40C8fWvBm5V","mb-6":"t30usboNSyqfQWIwHvT3","ml-6":"Nm_TyFkYCMhOoghoToKJ","m-7":"C4qJKoBXpgKtpmrqtEKB","mx-7":"S93Srbu6NQ_PBr7DmTiD","my-7":"fJj8k6gGJDks3crUZxOS","mt-7":"cW6D6djs7Ppm7fD7TeoV","mr-7":"DuCnqNfcxcP3Z__Yo5Ro","mb-7":"im8407m2fw5vOg7O2zsw","ml-7":"G0fbeBgvz2sh3uTP9gNl","m-8":"kvW3sBCxRxUqz1jrVMJl","mx-8":"tOjEqjLONQdkiYx_XRnw","my-8":"op5hFSx318zgxsoZZNLN","mt-8":"c9WfNHP6TFKWIfLxv52J","mr-8":"sBA75QqcqRwwYSHJh2wc","mb-8":"GpL6idrXmSOM6jB8Ohsf","ml-8":"HbtWJoQwpgGycz8dGzeT","p-0":"uxX3khU88VQ_Ah49Ejsa","px-0":"KX0FhpBKwKzs9fOUdbNz","py-0":"PfK8vKDyN32dnimlzYjz","pt-0":"emxLHRjQuJsImnPbQIzE","pr-0":"kJ8WzlpTVgdViXt8ukP9","pb-0":"tg_UIUI11VBzrTAn2AzJ","pl-0":"uczvl8kaz84oPQJ2DB2R","p-1":"o7UHPcdVK3lt7q3lqV4o","px-1":"IDqEOxvDoYrFYxELPmtX","py-1":"DdywPW2qSYlu2pt8tpO2","pt-1":"npy3hw4A5QSkDicb2CJJ","pr-1":"LgbptTApNY5NwLQvEFAt","pb-1":"WZQy2SZuZso59bUsXXyl","pl-1":"o331apInxNunbYB3SfPE","p-2":"fMPIyD9Vqki1Lrc_yJnG","px-2":"i2pMcTcdrr10IQoiSm_L","py-2":"eA702gn32kwptiI1obXH","pt-2":"o9bGieUKcYc8o0Ij9oZX","pr-2":"SwZcFez1RDqWsOFjB5iG","pb-2":"eHpLc_idmuEqeqCTvqkN","pl-2":"vU39i2B4P1fUTMB2l6Vo","p-3":"JHWNzBnE29awhdu5BEh1","px-3":"X72lGbb56L3KFzC2xQ9N","py-3":"BzfNhRG8wXdCEB5ocQ6e","pt-3":"srV0KSDC83a2fiimSMMQ","pr-3":"lUWfkmbQjCskhcNwkyCm","pb-3":"Ts0dIlc3aTSL7V4cIHis","pl-3":"CzlqQXXhX6MvorArFZ8B","p-4":"TqMPkQtR_DdZuKb5vBoV","px-4":"a7UrjhI69Vetlcj9ZVzz","py-4":"StEhBzGs2Gi5dDEkjhAv","pt-4":"FGneZfZyvYrt1dG0zcnm","pr-4":"APEH216rpdlJWgD2fHc8","pb-4":"oGwXC3ohCic9XnAj6x69","pl-4":"U6gnT9y42ViPNOcNzBwb","p-5":"IpdRLBwnHqbqFrixgbYC","px-5":"HgNeXvkBa9o3bQ5fvFZm","py-5":"tJtFZM3XfPG9v9TSDfN1","pt-5":"PdifHW45QeXYfK568uD8","pr-5":"mbLkWTTZ0Za_BBbFZ5b2","pb-5":"vVWpZpLlWrkTt0hMk8XU","pl-5":"RxfaJj5a1Nt6IavEo5Zl","p-6":"SppJULDGdnOGcjZNCYBy","px-6":"palY2nLwdoyooPUm9Hhk","py-6":"WYw1JvZC0ppLdvSAPhr_","pt-6":"YEEJ9b90ueQaPfiU8aeN","pr-6":"QE0ssnsKvWJMqlhPbY5u","pb-6":"n8yA3jHlMRyLd5UIfoND","pl-6":"tXHmxYnHzbwtfxEaG51n","p-7":"kBTsPKkO_3g_tLkj77Um","px-7":"RyhrFx6Y1FGDrGAAyaxm","py-7":"CBwRpB0bDN3iEdQPPMJO","pt-7":"vQVSq6SvWKbOMu6r4H6b","pr-7":"oBy5__aEADMsH46mrgFX","pb-7":"KVEXoJqf1s92j0JMdNmN","pl-7":"ZMXGNrNaKW3k_3TLz0Fq","p-8":"tuiR9PhkHXhGyEgzRZRI","px-8":"U7454qyWkQNa2iaSJziu","py-8":"VLYIv2GVocjuN93e8HC8","pt-8":"X1rm9DQ1zLGLfogja5Gn","pr-8":"JS7G6kAuqJo5GIuF8S5t","pb-8":"Y8F9ga1TDCMbM1lj4gUz","pl-8":"AJuyNGrI63BOWql719H8"}},25196:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={global:"_fUXxnSp5pagKBp9gSN7"}},85605:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={wrapper:"O5Go3eBOBqWlXy7fX0su","connection-name":"fgQ4OdnJ1svMwDnOKu2H","profile-link":"ArQoWnNoP4okGpEbxF6b",confirmDialog:"QsEfjoVyfc0gVACXTvUM","mark-shared-wrap":"eCH_xkiqbqL0UkJjU_1p","connection-panel":"J2Rz6Qb6mWPyJDF0yoqw",description:"kigMrUb5_HJk7XhSh7Um","connection-list":"fXYYdRkn3LXtN9YH3354","connection-list-item":"LaK9P7Nfi4ZBH0NhtbSs","connection-item":"tI3FtttoJPuF9AJzCAMv","connection-item-name":"msoMjBB_4Uq5qpRgobsH","learn-more":"RjMLptH6v03AX5ABWBp3","connection-name-wrapper":"z5Do97zRnujyW3Oj0pPJ"}},49514:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={"connection-container":"KQcQQLxH5fI08DfOlKwL"}},71016:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={"connections-list":"Ua6eKcnk_tQQpFlgXMSn","components-notice":"SHqrIEguRfCILRHPyxE9","more-link":"ejyuBjiLmHz_UTFmBnjA","change-settings-button":"smqXe_da1eoP4Agql_Cs","enhanced-features-nudge":"eQZwqSCI_uVWrJ2jIRUA","enabled-connections-notice":"O_ZghPCUG7291iVsp1wx","no-connections-text":"nrOxj0JeSCWwCB4oaR94","broken-connection-btn":"Jxra4242dPPaUy28mM8H","broken-connection-service":"amoEz9VXiwF3eyLxTbOA","broken-connection":"irsbCLZJNayrJ0g_joAn","settings-button":"lIKYDl6xVVtMbvcUDrrn","share-post-form__media-section":"ScsA_DtvUBZysN0dB1o8","unsupported-connections-list":"QYv7me60wtXerCXYFPjl"}},60548:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={container:"OWstrHF6QZdaosKs0b6j",hidden:"HJMFrbZ0khdkKKkocrIK"}},18973:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={confirmation:"udAWaza1u34TkmZZUPq4","header-text":"JsO4jFxFAKjG6Go62bZF",form:"SeoSfJ6eII9O0PiDkl5g","accounts-list":"M6POQFFbhswwolN6D9VN","account-input":"Sk_WQxS97yTokR_rM3Ci","account-label":"CRjatFVv3dQMjG9_Vmjj","submit-wrap":"re3HrL6GJa59uqDgx771","profile-pic":"jPB4ApiYyEswu6AKKJqk","account-info":"JhIphWt8e2X_ee0l7KEZ"}},73842:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={modal:"Mu_jAq5ZcYDM1kmBbxcs","connect-form":"foAsC5GlXOWEzACnxT_P",small:"_7PKKGx44v2H9dJbUltFw","manual-share":"IGOk1gMf5SKkfOKyF7Xc"}},27681:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={wrapper:"pp_WJTHlGIX9bcHCLxJh",title:"IzRyqrcpwjKRBJKEtECz"}},97671:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={"preview-wrapper":"cZuW54OEuUN9yvuE_rNU","preview-image":"vCs0BOExDMOystFhG2wa",container:"sKpch49QzrpontC0DvF7",subtitle:"vOO47kVjHn7i63fQ0bx0",preview:"ziE_jq4PmQmGBedGskf_",remove:"qqGj0rmSJvZs9dyr0u_I","remove-loading":"TJGgifcTHqUtknPqVQpH"}},35270:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={wrapper:"f92zWdEJJIwBvwH5rRzn",disabled:"rBTOWyeewE4VxAqYgjoI","preview-wrapper":"PykCvaqoL7AIDzVKJAtg",notice:"SSMDWFYkP44va5KqPpTP",subtitle:"Q21myqk1zOYB3IH8YuEq","learn-more":"RoerAwwhu_PqirHlSJPz"}},93794:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={notice:"ptn8jt555LOvMXWaoLxs","notice--default":"IVzciZv5S5E8XgR5EsLp","notice--highlight":"OpbDsm3F65Z4K5Bz43cz","notice--warning":"dlrSwB0tyuf1TYcypDec","notice--error":"hkpqil8E11PObwu7TVU2",dismiss:"IArBTmNL2K4du5WPBUhg",actions:"T7E2eJJN4qjwPpv3mcjV"}},87170:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={panel:"U_WW2qLSQA71SJYyITOq"}},5780:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={description:"D8uyjb_rjCf4HTMT0mj0"}},55387:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={"loading-block":"dCsa7WNQhT_7RhRkZih1","loading-text":"VotVfPmHNdU_xItAWEKQ"}},61420:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={wrapper:"dhja1sVSpDACLZ6k4sBy"}},76445:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={prompt:"CW7R8fRQqVKfyXFYiNWR",buttons:"n34j0qWq2EZzYl1jAiej",button:"aIuCL8MdydCJGcrOqp4x",header:"qegtnlYWDc3UnIGPRlQb"}},66888:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={confirm:"x8WVbFyj5LjZJqqo_oOu"}},16522:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={wrapper:"SzASUwGICyjjoAsbHM5Q",content:"pCIQqRpTEWoH2XYIBcpQ","display-name":"wWWIVXmpYntMUIZFx61c",date:"P_9btwoVLBzgaKVdHmps",actions:"emlhImGIXMT1xcJSL91N","delete-button":"PdI0LtRaVMiSnkpmYRnJ"}},19235:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={list:"WfDFFNIikFIRYO5Sy1tU",item:"lxtx9Lxo6QJTRhFft12q"}},42475:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={services:"SbpyhN5jnda8H868J3rX","service-list-item":"ONeewrk3aNC1gYJebq7K","service-item":"RoTxOeGFqAwk3pXUZpvF","service-item-info":"AjlYaYB_xi9ciXXCqHIJ","service-basics":"kdYli5nS3b46T57Ih5TQ",heading:"ci8T5eNe1rggYILce596",badges:"W6mPdl5wGQlXfp1hYSK0",badge:"QbcyaAVqz3U_2xlIOZCw",title:"fXvUC1jZNUXR7RSrIylI",description:"d22_aeSA1etqpnckP_Mr",actions:"ddhaNmGMqOySRjR_TtqJ","learn-more":"nnSF_IK4vYviTlCOzPrg","service-panel":"u17AiTny2Jbh35JFAUYw","active-connection":"MyXskAb4aXwO7zitR97z","broken-connection-alert":"hMntEpO6BV5B11lyAitv","service-connection-list":"Y_4JpLfOnXVZKvEs179H","example-wrapper":"jdahJvXCrCw0hA5NP3D3",small:"LLeYGX7Owy6gmpNW88VV","service-connection":"EdjyE9tVLH6lWMoB9z3M","connection-details":"EQQHwkVXRn0caD6J7LqV","profile-pic":"vvlyiVm67wUWF9NhpMA4",disconnect:"zqX6xk8KCGK1_qbYKqY5","connect-form-wrapper":"U_Z9JvOsEQ6BtkH9P1IQ","connect-form":"fNeO_MZFp7cPkgtCdkAF","connect-button":"zK8sijAMFNs2m6WRAoi4","fields-wrapper":"UVvtbZph1hXruamo6ZXw",input:"bzrYZZHzlTKn71KsfFCP","fields-item":"rFJU1Nr26kL8C6HZax0G",error:"XIX7fVqeFgDdGkOv2_hr","mark-shared-wrap":"zLobzkXnF6jHxNvkfz5_","error-text":"IiaedU1hr9K0GZ5PeQBd"}},99811:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={"share-buttons":"JUeclE9CucaXRlTL5K9H",x:"rhyZKz4FQtFOFAMwLgLj",whatsapp:"DDRhGoqXUOM6Jug54nEB",facebook:"h57Cabo4jn8Ke77_DLOX",clipboard:"tvNWJpaNzPro4QaItBae",vertical:"p_VTpBCFklSkcb0iiD0K",container:"DyIAUIn_2bkciyB_XtgH",label:"Lx8xScOApJmJdhJF9JWd"}},14150:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={"trigger-wrapper":"kyLdsnatoC0HbIDJQm9S",trigger:"yTz9lb0giJmAyepwu1nw",wrapper:"Syp1HH254tFVzcFG2Zh2",modal:"YABlqQXOGnawHFcJa7zT","share-item-name-wrapper":"vEA7jwVDDYmYUaVhX9QR","share-item-name":"AsH9JOuWehE027IlR7Y2","share-status-wrapper":"hvN5L4O60W8ftCvW1gRX","share-status-success":"aF8rESnbAc_uQUN0H3py","share-status-failure":"mfUKWd8ZvydeEyWeKhJ2","share-status-label":"pvVeQhJ9oNx0CFoHYEit","share-status-icon-tooltip":"fXQtkkrybB5DooWXxt67","tooltip-text":"LVRjzREGBPlLa__A7tdR","share-status-icon":"bJAz31xuwL_vO4V97YfM","dataview-wrapper":"eYjvcy4kG7GH44dIVLYO","connection-name":"Zr_Eo4TmHx_X7UFoJcuM","retry-wrapper":"csen0rABejxv4X3l1cx5"}},53744:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={templateControl:"tfWupx32Rn5hHqVjTzui",customText:"qu9UsV1EEFeFxhU3Wgt5",mediaPicker:"nZhgKYFn6MQoIDGcKrUG"}},52002:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={templates:"m9360_cMWKFHTNk9xpNz",template:"j4EvSJfKIJLGEIAETiEY","template--active":"dQb3Zpl0puk4V6ezZslo"}},71411:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={wrapper:"hdNnFaXZr5DKiR3lTQfi",title:"mWpGELw8Ot4ytShV0HrT"}},47944:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={panel:"nOZgE6dP_guav2888GAK",modal:"jxndagXCphEhzYfnjHMZ","modal-content":"na9tpJVboVaxVNabSRsr","close-button":"VxWWpt730pSzZCEVjxA3","settings-header":"z14EiGfFuQwKazBW3LpF","settings-section":"LAWYmJPODB8LgzQMzsyx","settings-content":"vVbLRydJFLCVa49hSz83","preview-section":"uw1atyZ2fcJoKjwf6SZW","preview-content":"meKppCUznQhlkjwZuS6G","disabled-tab":"HS7f47n0w2xUqa4wQkwX","modal-description":"dKXzdVKZ7LYvSmUVrl26","share-actions":"Tx0hzW0XnsuUyAFen9g8","open-button":"rwHivd_460jN4qTITm95"}},76794:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={wrapper:"sP1aklNMfj7lCDxuhWaq",progress:"a6tcXi51GAewl3qx0AVi",duration:"uFtCCGeycoI94JbCnJh8"}},44997:e=>{var t=1e3,a=60*t,n=60*a,c=24*n,s=7*c,i=365.25*c;function r(e,t,a,n){var c=t>=1.5*a;return Math.round(e/a)+" "+n+(c?"s":"")}e.exports=function(e,l){l=l||{};var o=typeof e;if("string"===o&&e.length>0)return function(e){if((e=String(e)).length>100)return;var r=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!r)return;var l=parseFloat(r[1]);switch((r[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return l*i;case"weeks":case"week":case"w":return l*s;case"days":case"day":case"d":return l*c;case"hours":case"hour":case"hrs":case"hr":case"h":return l*n;case"minutes":case"minute":case"mins":case"min":case"m":return l*a;case"seconds":case"second":case"secs":case"sec":case"s":return l*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return l;default:return}}(e);if("number"===o&&isFinite(e))return l.long?function(e){var s=Math.abs(e);if(s>=c)return r(e,s,c,"day");if(s>=n)return r(e,s,n,"hour");if(s>=a)return r(e,s,a,"minute");if(s>=t)return r(e,s,t,"second");return e+" ms"}(e):function(e){var s=Math.abs(e);if(s>=c)return Math.round(e/c)+"d";if(s>=n)return Math.round(e/n)+"h";if(s>=a)return Math.round(e/a)+"m";if(s>=t)return Math.round(e/t)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},15762:(e,t,a)=>{"use strict";var n=a(53761);function c(){}function s(){}s.resetWarningCache=c,e.exports=function(){function e(e,t,a,c,s,i){if(i!==n){var r=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw r.name="Invariant Violation",r}}function t(){return e}e.isRequired=e;var a={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:s,resetWarningCache:c};return a.PropTypes=a,a}},28120:(e,t,a)=>{e.exports=a(15762)()},53761:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},86212:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var n=a(51609),c=a(76221),s=function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var a=[],_n=!0,n=!1,c=void 0;try{for(var s,i=e[Symbol.iterator]();!(_n=(s=i.next()).done)&&(a.push(s.value),!t||a.length!==t);_n=!0);}catch(e){n=!0,c=e}finally{try{!_n&&i.return&&i.return()}finally{if(n)throw c}}return a}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")},i=c.TT&&c.XD;const r=function(){var e=(0,c.Y9)(),t=s(e,1)[0],a=(0,n.useState)(t),r=s(a,2),l=r[0],o=r[1];return(0,n.useEffect)((function(){if(i){var e=function(){var e=(0,c.Y9)(),t=s(e,1)[0];o(t)};return document.addEventListener(c.XD.event,e),function(){document.removeEventListener(c.XD.event,e)}}}),[]),l}},76221:(e,t,a)=>{"use strict";a.d(t,{TT:()=>s,XD:()=>i,Y9:()=>r});var n="undefined"!=typeof document,c=[{hidden:"hidden",event:"visibilitychange",state:"visibilityState"},{hidden:"webkitHidden",event:"webkitvisibilitychange",state:"webkitVisibilityState"},{hidden:"mozHidden",event:"mozvisibilitychange",state:"mozVisibilityState"},{hidden:"msHidden",event:"msvisibilitychange",state:"msVisibilityState"},{hidden:"oHidden",event:"ovisibilitychange",state:"oVisibilityState"}],s=n&&Boolean(document.addEventListener),i=function(){if(!s)return null;var e=!0,t=!1,a=void 0;try{for(var n,i=c[Symbol.iterator]();!(e=(n=i.next()).done);e=!0){var r=n.value;if(r.hidden in document)return r}}catch(e){t=!0,a=e}finally{try{!e&&i.return&&i.return()}finally{if(t)throw a}}return null}(),r=function(){if(!i)return[!0,"visible"];var e=i.hidden,t=i.state;return[!document[e],document[t]]}},90372:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(46941);const c=a.n(n)()("dops:analytics");let s,i;window._tkq=window._tkq||[],window.ga=window.ga||function(){(window.ga.q=window.ga.q||[]).push(arguments)},window.ga.l=+new Date;const r={initialize:function(e,t,a){r.setUser(e,t),r.setSuperProps(a),r.identifyUser()},setGoogleAnalyticsEnabled:function(e,t=null){this.googleAnalyticsEnabled=e,this.googleAnalyticsKey=t},setMcAnalyticsEnabled:function(e){this.mcAnalyticsEnabled=e},setUser:function(e,t){i={ID:e,username:t}},setSuperProps:function(e){s=e},assignSuperProps:function(e){s=Object.assign(s||{},e)},mc:{bumpStat:function(e,t){const a=function(e,t){let a="";if("object"==typeof e){for(const t in e)a+="&x_"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);c("Bumping stats %o",e)}else a="&x_"+encodeURIComponent(e)+"="+encodeURIComponent(t),c('Bumping stat "%s" in group "%s"',t,e);return a}(e,t);r.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom-no-pv"+a+"&t="+Math.random())},bumpStatWithPageView:function(e,t){const a=function(e,t){let a="";if("object"==typeof e){for(const t in e)a+="&"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);c("Built stats %o",e)}else a="&"+encodeURIComponent(e)+"="+encodeURIComponent(t),c('Built stat "%s" in group "%s"',t,e);return a}(e,t);r.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom"+a+"&t="+Math.random())}},pageView:{record:function(e,t){r.tracks.recordPageView(e),r.ga.recordPageView(e,t)}},purchase:{record:function(e,t,a,n,c,s,i){r.ga.recordPurchase(e,t,a,n,c,s,i)}},tracks:{recordEvent:function(e,t){t=t||{},0===e.indexOf("akismet_")||0===e.indexOf("jetpack_")?(s&&(c("- Super Props: %o",s),t=Object.assign(t,s)),c('Record event "%s" called with props %s',e,JSON.stringify(t)),window._tkq.push(["recordEvent",e,t])):c('- Event name must be prefixed by "akismet_" or "jetpack_"')},recordJetpackClick:function(e){const t="object"==typeof e?e:{target:e};r.tracks.recordEvent("jetpack_wpa_click",t)},recordPageView:function(e){r.tracks.recordEvent("akismet_page_view",{path:e})},setOptOut:function(e){c("Pushing setOptOut: %o",e),window._tkq.push(["setOptOut",e])}},ga:{initialized:!1,initialize:function(){let e={};r.ga.initialized||(i&&(e={userId:"u-"+i.ID}),window.ga("create",this.googleAnalyticsKey,"auto",e),r.ga.initialized=!0)},recordPageView:function(e,t){r.ga.initialize(),c("Recording Page View ~ [URL: "+e+"] [Title: "+t+"]"),this.googleAnalyticsEnabled&&(window.ga("set","page",e),window.ga("send",{hitType:"pageview",page:e,title:t}))},recordEvent:function(e,t,a,n){r.ga.initialize();let s="Recording Event ~ [Category: "+e+"] [Action: "+t+"]";void 0!==a&&(s+=" [Option Label: "+a+"]"),void 0!==n&&(s+=" [Option Value: "+n+"]"),c(s),this.googleAnalyticsEnabled&&window.ga("send","event",e,t,a,n)},recordPurchase:function(e,t,a,n,c,s,i){window.ga("require","ecommerce"),window.ga("ecommerce:addTransaction",{id:e,revenue:n,currency:i}),window.ga("ecommerce:addItem",{id:e,name:t,sku:a,price:c,quantity:s}),window.ga("ecommerce:send")}},identifyUser:function(){i&&window._tkq.push(["identifyUser",i.ID,i.username])},setProperties:function(e){window._tkq.push(["setProperties",e])},clearedIdentity:function(){window._tkq.push(["clearIdentity"])}},l=r},42266:(e,t,a)=>{"use strict";a.d(t,{A:()=>p});var n=a(23751),c=a(19783),s=a(83883),i=a(51113),r=a(13022),l=a(51609),o=a.n(l),d=a(65486);const u=e=>{switch(e){case"error":case"warning":default:return n.A;case"info":return c.A;case"success":return s.A}},p=({level:e="warning",children:t,showIcon:a=!0,className:n})=>{const c=(0,r.A)(d.A.container,d.A[`is-${e}`],n);return o().createElement("div",{className:c},a&&o().createElement("div",{className:d.A["icon-wrapper"]},o().createElement(i.A,{icon:u(e),className:d.A.icon})),o().createElement("div",null,t))}},51112:(e,t,a)=>{"use strict";a.d(t,{A:()=>h});var n=a(96072),c=a.n(n),s=a(56427),i=a(27723),r=a(51113),l=a(53512),o=a(13022),d=a(51609),u=a.n(d),p=a(77560);const __=i.__,m=(0,d.forwardRef)(((e,t)=>{const{children:a,variant:n="primary",size:i="normal",weight:d="bold",icon:m,iconSize:h,disabled:g,isDestructive:v,isLoading:w,isExternalLink:b,className:f,text:k,fullWidth:_,...x}=e,E=(0,o.A)(p.A.button,f,{[p.A.normal]:"normal"===i,[p.A.small]:"small"===i,[p.A.icon]:Boolean(m),[p.A.loading]:w,[p.A.regular]:"regular"===d,[p.A["full-width"]]:_,[p.A["is-icon-button"]]:Boolean(m)&&!a});x.ref=t;const j="normal"===i?20:16,R=b&&u().createElement(u().Fragment,null,u().createElement(r.A,{size:j,icon:l.A,className:p.A["external-icon"]}),u().createElement(s.VisuallyHidden,{as:"span"},/* translators: accessibility text */
__("(opens in a new tab)","jetpack-publicize-pkg"))),y=b?"_blank":void 0,A=a?.[0]&&null!==a[0]&&"components-tooltip"!==a?.[0]?.props?.className;return u().createElement(s.Button,c()({target:y,variant:n,className:(0,o.A)(E,{"has-text":!!m&&A}),icon:b?void 0:m,iconSize:h,disabled:g,"aria-disabled":g,isDestructive:v,text:k},x),w&&u().createElement(s.Spinner,null),u().createElement("span",null,a),R)}));m.displayName="Button";const h=m},69222:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var n=a(96072),c=a.n(n),s=a(29491),i=a(86087),r=a(27723),l=a(51112),o=a(78478);const __=r.__,d=({buttonStyle:e="icon",textToCopy:t,onCopy:a,...n})=>{const[r,d]=(0,i.useState)(!1),u=(0,i.useRef)(),p=(0,s.useCopyToClipboard)(t,(()=>{u.current&&clearTimeout(u.current),d(!0),a?.(),u.current=setTimeout((()=>{d(!1),u.current=void 0}),3e3)}));(0,i.useEffect)((()=>()=>{u.current&&clearTimeout(u.current)}),[]);let m=null,h=null;"text"!==e&&(m=r?React.createElement(o.Nr,null):React.createElement(o.ui,null));const g=__("Copy to clipboard","jetpack-publicize-pkg");return"icon"!==e&&(h=r?__("Copied!","jetpack-publicize-pkg"):g),React.createElement(l.A,c()({"aria-label":g,icon:m,children:h,ref:p},n))}},63406:(e,t,a)=>{"use strict";a.d(t,{I:()=>s});var n=a(47143),c=a(692);function s(){const e=(0,n.useDispatch)(c.store),t=(0,n.useSelect)((e=>e(c.store).getNotices()),[]);return{...e,createNotice:(t,a,n)=>e.createNotice(t,a,{type:"snackbar",...n}),createErrorNotice:(t,a)=>e.createErrorNotice(t,{type:"snackbar",...a}),createInfoNotice:(t,a)=>e.createInfoNotice(t,{type:"snackbar",...a}),createSuccessNotice:(t,a)=>e.createSuccessNotice(t,{type:"snackbar",...a}),createWarningNotice:(t,a)=>e.createWarningNotice(t,{type:"snackbar",...a}),getNotices:()=>t}}},11883:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var n=a(27723),c=a(13022),s=a(51609);a(4459);const __=n.__;class i extends s.Component{static defaultProps={"aria-hidden":"false",focusable:"true"};needsOffset(e,t){return["gridicons-arrow-left","gridicons-arrow-right","gridicons-calendar","gridicons-cart","gridicons-folder","gridicons-help-outline","gridicons-info","gridicons-info-outline","gridicons-posts","gridicons-star-outline","gridicons-star"].indexOf(e)>=0&&t%18==0}getSVGDescription(e){if("description"in this.props)return this.props.description;switch(e){default:return"";case"gridicons-audio":return __("Has audio.","jetpack-publicize-pkg");case"gridicons-arrow-left":return __("Arrow left","jetpack-publicize-pkg");case"gridicons-arrow-right":return __("Arrow right","jetpack-publicize-pkg");case"gridicons-calendar":return __("Is an event.","jetpack-publicize-pkg");case"gridicons-cart":return __("Is a product.","jetpack-publicize-pkg");case"chevron-down":return __("Show filters","jetpack-publicize-pkg");case"gridicons-comment":return __("Matching comment.","jetpack-publicize-pkg");case"gridicons-cross":return __("Close.","jetpack-publicize-pkg");case"gridicons-filter":return __("Toggle search filters.","jetpack-publicize-pkg");case"gridicons-folder":return __("Category","jetpack-publicize-pkg");case"gridicons-help-outline":return __("Help","jetpack-publicize-pkg");case"gridicons-info":case"gridicons-info-outline":return __("Information.","jetpack-publicize-pkg");case"gridicons-image-multiple":return __("Has multiple images.","jetpack-publicize-pkg");case"gridicons-image":return __("Has an image.","jetpack-publicize-pkg");case"gridicons-page":return __("Page","jetpack-publicize-pkg");case"gridicons-post":return __("Post","jetpack-publicize-pkg");case"gridicons-jetpack-search":case"gridicons-search":return __("Magnifying Glass","jetpack-publicize-pkg");case"gridicons-tag":return __("Tag","jetpack-publicize-pkg");case"gridicons-video":return __("Has a video.","jetpack-publicize-pkg")}}renderIcon(e){switch(e){default:return null;case"gridicons-audio":return React.createElement("g",null,React.createElement("path",{d:"M8 4v10.184C7.686 14.072 7.353 14 7 14c-1.657 0-3 1.343-3 3s1.343 3 3 3 3-1.343 3-3V7h7v4.184c-.314-.112-.647-.184-1-.184-1.657 0-3 1.343-3 3s1.343 3 3 3 3-1.343 3-3V4H8z"}));case"gridicons-arrow-left":return React.createElement("g",null,React.createElement("path",{d:"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"}));case"gridicons-arrow-right":return React.createElement("g",null,React.createElement("path",{d:"M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8-8-8z"}));case"gridicons-block":return React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zM4 12c0-4.418 3.582-8 8-8 1.848 0 3.545.633 4.9 1.686L5.686 16.9C4.633 15.545 4 13.848 4 12zm8 8c-1.848 0-3.546-.633-4.9-1.686L18.314 7.1C19.367 8.455 20 10.152 20 12c0 4.418-3.582 8-8 8z"}));case"gridicons-calendar":return React.createElement("g",null,React.createElement("path",{d:"M19 4h-1V2h-2v2H8V2H6v2H5c-1.105 0-2 .896-2 2v13c0 1.104.895 2 2 2h14c1.104 0 2-.896 2-2V6c0-1.104-.896-2-2-2zm0 15H5V8h14v11z"}));case"gridicons-cart":return React.createElement("g",null,React.createElement("path",{d:"M9 20c0 1.1-.9 2-2 2s-1.99-.9-1.99-2S5.9 18 7 18s2 .9 2 2zm8-2c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2zm.396-5c.937 0 1.75-.65 1.952-1.566L21 5H7V4c0-1.105-.895-2-2-2H3v2h2v11c0 1.105.895 2 2 2h12c0-1.105-.895-2-2-2H7v-2h10.396z"}));case"gridicons-checkmark":return React.createElement("g",null,React.createElement("path",{d:"M11 17.768l-4.884-4.884 1.768-1.768L11 14.232l8.658-8.658C17.823 3.39 15.075 2 12 2 6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10c0-1.528-.353-2.97-.966-4.266L11 17.768z"}));case"gridicons-chevron-left":return React.createElement("g",null,React.createElement("path",{d:"M16.443 7.41L15.0399 6L9.06934 12L15.0399 18L16.443 16.59L11.8855 12L16.443 7.41Z"}));case"gridicons-chevron-right":return React.createElement("g",null,React.createElement("path",{d:"M10.2366 6L8.8335 7.41L13.391 12L8.8335 16.59L10.2366 18L16.2072 12L10.2366 6Z"}));case"gridicons-chevron-down":return React.createElement("g",null,React.createElement("path",{d:"M20 9l-8 8-8-8 1.414-1.414L12 14.172l6.586-6.586"}));case"gridicons-comment":return React.createElement("g",null,React.createElement("path",{d:"M3 6v9c0 1.105.895 2 2 2h9v5l5.325-3.804c1.05-.75 1.675-1.963 1.675-3.254V6c0-1.105-.895-2-2-2H5c-1.105 0-2 .895-2 2z"}));case"gridicons-computer":return React.createElement("g",null,React.createElement("path",{d:"M20 2H4c-1.104 0-2 .896-2 2v12c0 1.104.896 2 2 2h6v2H7v2h10v-2h-3v-2h6c1.104 0 2-.896 2-2V4c0-1.104-.896-2-2-2zm0 14H4V4h16v12z"}));case"gridicons-cross":return React.createElement("g",null,React.createElement("path",{d:"M18.36 19.78L12 13.41l-6.36 6.37-1.42-1.42L10.59 12 4.22 5.64l1.42-1.42L12 10.59l6.36-6.36 1.41 1.41L13.41 12l6.36 6.36z"}));case"gridicons-filter":return React.createElement("g",null,React.createElement("path",{d:"M10 19h4v-2h-4v2zm-4-6h12v-2H6v2zM3 5v2h18V5H3z"}));case"gridicons-folder":return React.createElement("g",null,React.createElement("path",{d:"M18 19H6c-1.1 0-2-.9-2-2V7c0-1.1.9-2 2-2h3c1.1 0 2 .9 2 2h7c1.1 0 2 .9 2 2v8c0 1.1-.9 2-2 2z"}));case"gridicons-help-outline":return React.createElement("g",null,React.createElement("path",{d:"M12 4c4.41 0 8 3.59 8 8s-3.59 8-8 8-8-3.59-8-8 3.59-8 8-8m0-2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 13h-2v2h2v-2zm-1.962-2v-.528c0-.4.082-.74.246-1.017.163-.276.454-.546.87-.808.333-.21.572-.397.717-.565.146-.168.22-.36.22-.577 0-.172-.078-.308-.234-.41-.156-.1-.358-.15-.608-.15-.62 0-1.34.22-2.168.658l-.854-1.67c1.02-.58 2.084-.872 3.194-.872.913 0 1.63.202 2.15.603.52.4.78.948.78 1.64 0 .495-.116.924-.347 1.287-.23.362-.6.705-1.11 1.03-.43.278-.7.48-.807.61-.108.13-.163.282-.163.458V13h-1.885z"}));case"gridicons-image":return React.createElement("g",null,React.createElement("path",{d:"M13 9.5c0-.828.672-1.5 1.5-1.5s1.5.672 1.5 1.5-.672 1.5-1.5 1.5-1.5-.672-1.5-1.5zM22 6v12c0 1.105-.895 2-2 2H4c-1.105 0-2-.895-2-2V6c0-1.105.895-2 2-2h16c1.105 0 2 .895 2 2zm-2 0H4v7.444L8 9l5.895 6.55 1.587-1.85c.798-.932 2.24-.932 3.037 0L20 15.426V6z"}));case"gridicons-image-multiple":return React.createElement("g",null,React.createElement("path",{d:"M15 7.5c0-.828.672-1.5 1.5-1.5s1.5.672 1.5 1.5S17.328 9 16.5 9 15 8.328 15 7.5zM4 20h14c0 1.105-.895 2-2 2H4c-1.1 0-2-.9-2-2V8c0-1.105.895-2 2-2v14zM22 4v12c0 1.105-.895 2-2 2H8c-1.105 0-2-.895-2-2V4c0-1.105.895-2 2-2h12c1.105 0 2 .895 2 2zM8 4v6.333L11 7l4.855 5.395.656-.73c.796-.886 2.183-.886 2.977 0l.513.57V4H8z"}));case"gridicons-info":return React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"}));case"gridicons-info-outline":return React.createElement("g",null,React.createElement("path",{d:"M13 9h-2V7h2v2zm0 2h-2v6h2v-6zm-1-7c-4.411 0-8 3.589-8 8s3.589 8 8 8 8-3.589 8-8-3.589-8-8-8m0-2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2z"}));case"gridicons-jetpack-search":return React.createElement("g",null,React.createElement("path",{d:"M0 9.257C0 4.15 4.151 0 9.257 0c5.105 0 9.256 4.151 9.256 9.257a9.218 9.218 0 01-2.251 6.045l.034.033h1.053L24 22.01l-1.986 1.989-6.664-6.662v-1.055l-.033-.033a9.218 9.218 0 01-6.06 2.264C4.15 18.513 0 14.362 0 9.257zm4.169 1.537h4.61V1.82l-4.61 8.973zm5.547-3.092v8.974l4.61-8.974h-4.61z"}));case"gridicons-phone":return React.createElement("g",null,React.createElement("path",{d:"M16 2H8c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h8c1.104 0 2-.896 2-2V4c0-1.104-.896-2-2-2zm-3 19h-2v-1h2v1zm3-2H8V5h8v14z"}));case"gridicons-pages":return React.createElement("g",null,React.createElement("path",{d:"M16 8H8V6h8v2zm0 2H8v2h8v-2zm4-6v12l-6 6H6c-1.105 0-2-.895-2-2V4c0-1.105.895-2 2-2h12c1.105 0 2 .895 2 2zm-2 10V4H6v16h6v-4c0-1.105.895-2 2-2h4z"}));case"gridicons-posts":return React.createElement("g",null,React.createElement("path",{d:"M16 19H3v-2h13v2zm5-10H3v2h18V9zM3 5v2h11V5H3zm14 0v2h4V5h-4zm-6 8v2h10v-2H11zm-8 0v2h5v-2H3z"}));case"gridicons-search":return React.createElement("g",null,React.createElement("path",{d:"M21 19l-5.154-5.154C16.574 12.742 17 11.42 17 10c0-3.866-3.134-7-7-7s-7 3.134-7 7 3.134 7 7 7c1.42 0 2.742-.426 3.846-1.154L19 21l2-2zM5 10c0-2.757 2.243-5 5-5s5 2.243 5 5-2.243 5-5 5-5-2.243-5-5z"}));case"gridicons-star-outline":return React.createElement("g",null,React.createElement("path",{d:"M12 6.308l1.176 3.167.347.936.997.042 3.374.14-2.647 2.09-.784.62.27.963.91 3.25-2.813-1.872-.83-.553-.83.552-2.814 1.87.91-3.248.27-.962-.783-.62-2.648-2.092 3.374-.14.996-.04.347-.936L12 6.308M12 2L9.418 8.953 2 9.257l5.822 4.602L5.82 21 12 16.89 18.18 21l-2.002-7.14L22 9.256l-7.418-.305L12 2z"}));case"gridicons-star":return React.createElement("g",null,React.createElement("path",{d:"M12 2l2.582 6.953L22 9.257l-5.822 4.602L18.18 21 12 16.89 5.82 21l2.002-7.14L2 9.256l7.418-.304"}));case"gridicons-tag":return React.createElement("g",null,React.createElement("path",{d:"M20 2.007h-7.087c-.53 0-1.04.21-1.414.586L2.592 11.5c-.78.78-.78 2.046 0 2.827l7.086 7.086c.78.78 2.046.78 2.827 0l8.906-8.906c.376-.374.587-.883.587-1.413V4.007c0-1.105-.895-2-2-2zM17.007 9c-1.105 0-2-.895-2-2s.895-2 2-2 2 .895 2 2-.895 2-2 2z"}));case"gridicons-video":return React.createElement("g",null,React.createElement("path",{d:"M20 4v2h-2V4H6v2H4V4c-1.105 0-2 .895-2 2v12c0 1.105.895 2 2 2v-2h2v2h12v-2h2v2c1.105 0 2-.895 2-2V6c0-1.105-.895-2-2-2zM6 16H4v-3h2v3zm0-5H4V8h2v3zm4 4V9l4.5 3-4.5 3zm10 1h-2v-3h2v3zm0-5h-2V8h2v3z"}));case"gridicons-lock":return React.createElement(React.Fragment,null,React.createElement("g",{id:"lock"},React.createElement("path",{d:"M18,8h-1V7c0-2.757-2.243-5-5-5S7,4.243,7,7v1H6c-1.105,0-2,0.895-2,2v10c0,1.105,0.895,2,2,2h12c1.105,0,2-0.895,2-2V10 C20,8.895,19.105,8,18,8z M9,7c0-1.654,1.346-3,3-3s3,1.346,3,3v1H9V7z M13,15.723V18h-2v-2.277c-0.595-0.346-1-0.984-1-1.723 c0-1.105,0.895-2,2-2s2,0.895,2,2C14,14.738,13.595,15.376,13,15.723z"})),React.createElement("g",{id:"Layer_1"}));case"gridicons-external":return React.createElement("g",null,React.createElement("path",{d:"M19 13v6c0 1.105-.895 2-2 2H5c-1.105 0-2-.895-2-2V7c0-1.105.895-2 2-2h6v2H5v12h12v-6h2zM13 3v2h4.586l-7.793 7.793 1.414 1.414L19 6.414V11h2V3h-8z"}))}}render(){const{size:e=24,className:t=""}=this.props,a=this.props.height||e,n=this.props.width||e,s=this.props.style||{height:a,width:n},i="gridicons-"+this.props.icon,r=(0,c.A)("gridicon",i,t,{"needs-offset":this.needsOffset(i,e)}),l=this.getSVGDescription(i);return React.createElement("svg",{className:r,focusable:this.props.focusable,height:a,onClick:this.props.onClick,style:s,viewBox:"0 0 24 24",width:n,xmlns:"http://www.w3.org/2000/svg","aria-hidden":this.props["aria-hidden"]},l?React.createElement("desc",null,l):null,this.renderIcon(i))}}const r=i},40597:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var n=a(56427),c=a(13022),s=a(51609),i=a.n(s),r=a(51112),l=a(11883);a(84813);const o=e=>({"top-end":"top left",top:"top center","top-start":"top right","bottom-end":"bottom left",bottom:"bottom center","bottom-start":"bottom right"}[e]),d=({className:e="",iconClassName:t="",placement:a="bottom-end",animate:d=!0,iconCode:u="info-outline",iconSize:p=18,offset:m=10,title:h,children:g,popoverAnchorStyle:v="icon",forceShow:w=!1,hoverShow:b=!1,wide:f=!1,inline:k=!0,shift:_=!1})=>{const[x,E]=(0,s.useState)(!1),[j,R]=(0,s.useState)(null),y=(0,s.useCallback)((()=>E(!1)),[E]),A=(0,s.useCallback)((e=>{e.preventDefault(),E(!x)}),[x,E]),C={position:o(a),placement:a,animate:d,noArrow:!1,resize:!1,flip:!1,offset:m,focusOnMount:"container",onClose:y,className:"icon-tooltip-container",inline:k,shift:_},z="wrapper"===v,N=(0,c.A)("icon-tooltip-wrapper",e),S={left:z?0:-(62-p/2)+"px"},M=z&&w,P=(0,s.useCallback)((()=>{b&&(j&&(clearTimeout(j),R(null)),E(!0))}),[b,j]),L=(0,s.useCallback)((()=>{if(b){const e=setTimeout((()=>{E(!1),R(null)}),100);R(e)}}),[b]);return i().createElement("div",{className:N,"data-testid":"icon-tooltip_wrapper",onMouseEnter:P,onMouseLeave:L},!z&&i().createElement(r.A,{variant:"link",onMouseDown:A},i().createElement(l.A,{className:t,icon:u,size:p})),i().createElement("div",{className:(0,c.A)("icon-tooltip-helper",{"is-wide":f}),style:S},(M||x)&&i().createElement(n.Popover,C,i().createElement("div",null,h&&i().createElement("div",{className:"icon-tooltip-title"},h),i().createElement("div",{className:"icon-tooltip-content"},g)))))}},78478:(e,t,a)=>{"use strict";a.d(t,{M5:()=>m,Nr:()=>u,b6:()=>d,ui:()=>p});var n=a(96072),c=a.n(n),s=a(56427),i=a(13022),r=a(84705),l=a(47842);const o=({className:e,size:t=24,viewBox:a="0 0 24 24",opacity:n=1,color:r="#2C3338",children:o})=>{const d={className:(0,i.A)(l.A.iconWrapper,e),width:t,height:t,viewBox:a,opacity:n,fill:void 0};return r&&(d.fill=r),React.createElement(s.SVG,c()({},d,{fillRule:"evenodd",clipRule:"evenodd",xmlns:"http://www.w3.org/2000/svg"}),React.createElement(s.G,{opacity:n},o))},d=({opacity:e=1,size:t,color:a})=>React.createElement(o,{size:t,opacity:e,color:a},React.createElement(s.Path,{d:"M15.5 3.97809V18.0219L7.5 15.5977V20H6V15.1431L3.27498 14.3173C2.22086 13.9979 1.5 13.0262 1.5 11.9248V10.0752C1.5 8.97375 2.22087 8.00207 3.27498 7.68264L15.5 3.97809ZM14 16L7.5 14.0303L7.5 7.96969L14 5.99999V16ZM6 8.42423L6 13.5757L3.70999 12.8818C3.28835 12.754 3 12.3654 3 11.9248V10.0752C3 9.63462 3.28835 9.24595 3.70999 9.11818L6 8.42423ZM17.5 11.75H21.5V10.25H17.5V11.75ZM21.5 16L17.5 15V13.5L21.5 14.5V16ZM17.5 8.5L21.5 7.5V6L17.5 7V8.5Z"})),u=({size:e,className:t=l.A["checkmark-icon"],color:a})=>React.createElement(o,{className:t,size:e,color:a},React.createElement(s.Path,{d:"M11 17.768l-4.884-4.884 1.768-1.768L11 14.232l8.658-8.658C17.823 3.39 15.075 2 12 2 6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10c0-1.528-.353-2.97-.966-4.266L11 17.768z"})),p=({size:e,className:t=l.A["clipboard-icon"],color:a})=>React.createElement(o,{className:t,size:e,color:a},React.createElement(s.Path,{d:"M5.625 5.5H15.375C15.444 5.5 15.5 5.55596 15.5 5.625V15.375C15.5 15.444 15.444 15.5 15.375 15.5H5.625C5.55596 15.5 5.5 15.444 5.5 15.375V5.625C5.5 5.55596 5.55596 5.5 5.625 5.5ZM4 5.625C4 4.72754 4.72754 4 5.625 4H15.375C16.2725 4 17 4.72754 17 5.625V10V15.375C17 16.2725 16.2725 17 15.375 17C15.375 17 6.52246 17 5.625 17C4.72754 17 4 16.2725 4 15.375V5.625ZM18.5 17.2812V8.28125H20V17.2812C20 18.7995 18.7704 20 17.2511 20H6.25V18.5H17.2511C17.9409 18.5 18.5 17.9721 18.5 17.2812Z"}));const m=({serviceName:e,className:t,iconSize:a})=>React.createElement(r.d6,{className:(0,i.A)(l.A.socialIcon,l.A[e],t),icon:e,size:a||24})},67142:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(96072),c=a.n(n),s=a(27723),i=a(13022),r=a(51609),l=a.n(r);const __=s.__,o=({logoColor:e="#069e08",showText:t=!0,className:a,height:n=32,...s})=>{const r=t?"0 0 118 32":"0 0 32 32";return l().createElement("svg",c()({xmlns:"http://www.w3.org/2000/svg",x:"0px",y:"0px",viewBox:r,className:(0,i.A)("jetpack-logo",a),"aria-labelledby":"jetpack-logo-title",height:n},s,{role:"img"}),l().createElement("title",{id:"jetpack-logo-title"},__("Jetpack Logo","jetpack-publicize-pkg")),l().createElement("path",{fill:e,d:"M16,0C7.2,0,0,7.2,0,16s7.2,16,16,16s16-7.2,16-16S24.8,0,16,0z M15,19H7l8-16V19z M17,29V13h8L17,29z"}),t&&l().createElement(l().Fragment,null,l().createElement("path",{d:"M41.3,26.6c-0.5-0.7-0.9-1.4-1.3-2.1c2.3-1.4,3-2.5,3-4.6V8h-3V6h6v13.4C46,22.8,45,24.8,41.3,26.6z"}),l().createElement("path",{d:"M65,18.4c0,1.1,0.8,1.3,1.4,1.3c0.5,0,2-0.2,2.6-0.4v2.1c-0.9,0.3-2.5,0.5-3.7,0.5c-1.5,0-3.2-0.5-3.2-3.1V12H60v-2h2.1V7.1 H65V10h4v2h-4V18.4z"}),l().createElement("path",{d:"M71,10h3v1.3c1.1-0.8,1.9-1.3,3.3-1.3c2.5,0,4.5,1.8,4.5,5.6s-2.2,6.3-5.8,6.3c-0.9,0-1.3-0.1-2-0.3V28h-3V10z M76.5,12.3 c-0.8,0-1.6,0.4-2.5,1.2v5.9c0.6,0.1,0.9,0.2,1.8,0.2c2,0,3.2-1.3,3.2-3.9C79,13.4,78.1,12.3,76.5,12.3z"}),l().createElement("path",{d:"M93,22h-3v-1.5c-0.9,0.7-1.9,1.5-3.5,1.5c-1.5,0-3.1-1.1-3.1-3.2c0-2.9,2.5-3.4,4.2-3.7l2.4-0.3v-0.3c0-1.5-0.5-2.3-2-2.3 c-0.7,0-2.3,0.5-3.7,1.1L84,11c1.2-0.4,3-1,4.4-1c2.7,0,4.6,1.4,4.6,4.7L93,22z M90,16.4l-2.2,0.4c-0.7,0.1-1.4,0.5-1.4,1.6 c0,0.9,0.5,1.4,1.3,1.4s1.5-0.5,2.3-1V16.4z"}),l().createElement("path",{d:"M104.5,21.3c-1.1,0.4-2.2,0.6-3.5,0.6c-4.2,0-5.9-2.4-5.9-5.9c0-3.7,2.3-6,6.1-6c1.4,0,2.3,0.2,3.2,0.5V13 c-0.8-0.3-2-0.6-3.2-0.6c-1.7,0-3.2,0.9-3.2,3.6c0,2.9,1.5,3.8,3.3,3.8c0.9,0,1.9-0.2,3.2-0.7V21.3z"}),l().createElement("path",{d:"M110,15.2c0.2-0.3,0.2-0.8,3.8-5.2h3.7l-4.6,5.7l5,6.3h-3.7l-4.2-5.8V22h-3V6h3V15.2z"}),l().createElement("path",{d:"M58.5,21.3c-1.5,0.5-2.7,0.6-4.2,0.6c-3.6,0-5.8-1.8-5.8-6c0-3.1,1.9-5.9,5.5-5.9s4.9,2.5,4.9,4.9c0,0.8,0,1.5-0.1,2h-7.3 c0.1,2.5,1.5,2.8,3.6,2.8c1.1,0,2.2-0.3,3.4-0.7C58.5,19,58.5,21.3,58.5,21.3z M56,15c0-1.4-0.5-2.9-2-2.9c-1.4,0-2.3,1.3-2.4,2.9 C51.6,15,56,15,56,15z"})))}},60442:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n=a(29491),c=a(59053);const s=["sm","md","lg"],i=(e,t)=>{const a=Array.isArray(e)?e:[e],i=Array.isArray(t)?t:[t],[r,l,o]=s,d={sm:(0,n.useMediaQuery)(c.A[r]),md:(0,n.useMediaQuery)(c.A[l]),lg:(0,n.useMediaQuery)(c.A[o])};return a.map(((e,t)=>{const a=i[t];return a?((e,t,a)=>{const n=s.indexOf(e),c=n+1,i=t.includes("=");let r=[];return t.startsWith("<")&&(r=s.slice(0,i?c:n)),t.startsWith(">")&&(r=s.slice(i?n:c)),r?.length?r.some((e=>a[e])):a[e]})(e,a,d):d[e]}))}},10110:(e,t,a)=>{"use strict";a.d(t,{Q:()=>n,Z:()=>c});const n={"headline-medium":"h1","headline-small":"h2","headline-small-regular":"h2","title-medium":"h3","title-medium-semi-bold":"h3","title-small":"h4",body:"p","body-small":"p","body-extra-small":"p","body-extra-small-bold":"p",label:"p"},c=["mt","mr","mb","ml","mx","my","m","pt","pr","pb","pl","px","py","p"]},47425:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>u});var n=a(96072),c=a.n(n),s=a(13022),i=a(51609),r=a.n(i),l=a(10110),o=a(22073);const d=(0,i.forwardRef)((({variant:e="body",children:t,component:a,className:n,...d},u)=>{const p=a||l.Q[e]||"span",m=(0,i.useMemo)((()=>l.Z.reduce(((e,t)=>(void 0!==d[t]&&(e+=o.A[`${t}-${d[t]}`]+" ",delete d[t]),e)),"")),[d]);return r().createElement(p,c()({className:(0,s.A)(o.A.reset,o.A[e],n,m)},d,{ref:u}),t)}));d.displayName="Text";const u=d},50723:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>p});var n=a(51609),c=a.n(n),s=a(25196);const i={"--font-headline-medium":"48px","--font-headline-small":"36px","--font-title-medium":"24px","--font-title-small":"20px","--font-body":"16px","--font-body-small":"14px","--font-body-extra-small":"12px","--font-title-large":"var(--font-headline-small)","--font-label":"var(--font-body-extra-small)"},r={"--jp-black":"#000000","--jp-black-80":"#2c3338","--jp-white":"#ffffff","--jp-white-off":"#f9f9f6","--jp-gray":"#dcdcde","--jp-gray-0":"#F6F7F7","--jp-gray-5":"var(--jp-gray)","--jp-gray-10":"#C3C4C7","--jp-gray-20":"#A7AAAD","--jp-gray-40":"#787C82","--jp-gray-50":"#646970","--jp-gray-60":"#50575E","--jp-gray-70":"#3C434A","--jp-gray-80":"#2C3338","--jp-gray-90":"#1d2327","--jp-gray-off":"#e2e2df","--jp-red-0":"#F7EBEC","--jp-red-5":"#FACFD2","--jp-red-40":"#E65054","--jp-red-50":"#D63638","--jp-red-60":"#B32D2E","--jp-red-70":"#8A2424","--jp-red-80":"#691C1C","--jp-red":"#d63639","--jp-yellow-5":"#F5E6B3","--jp-yellow-10":"#F2CF75","--jp-yellow-20":"#F0C930","--jp-yellow-30":"#DEB100","--jp-yellow-40":"#C08C00","--jp-yellow-50":"#9D6E00","--jp-yellow-60":"#7D5600","--jp-blue-20":"#68B3E8","--jp-blue-40":"#1689DB","--jp-pink":"#C9356E","--jp-green-0":"#f0f2eb","--jp-green-5":"#d0e6b8","--jp-green-10":"#9dd977","--jp-green-20":"#64ca43","--jp-green-30":"#2fb41f","--jp-green-40":"#069e08","--jp-green-50":"#008710","--jp-green-60":"#007117","--jp-green-70":"#005b18","--jp-green-80":"#004515","--jp-green-90":"#003010","--jp-green-100":"#001c09","--jp-green":"#069e08","--jp-green-primary":"var( --jp-green-40 )","--jp-green-secondary":"var( --jp-green-30 )"},l={"--jp-border-radius":"4px","--jp-menu-border-height":"1px","--jp-underline-thickness":"2px"},o={"--spacing-base":"8px"},d={},u=(e,t,a)=>{const n={...i,...r,...l,...o};for(const t in n)e.style.setProperty(t,n[t]);a&&e.classList.add(s.A.global),t&&(d[t]={provided:!0,root:e})},p=({children:e=null,targetDom:t,id:a,withGlobalStyles:s=!0})=>{const i=(0,n.useRef)(),r=d?.[a]?.provided;return(0,n.useLayoutEffect)((()=>{if(!r)return t?u(t,a,s):void(i?.current&&u(i.current,a,s))}),[t,i,r,a,s]),t?c().createElement(c().Fragment,null,e):c().createElement("div",{ref:i},e)}},3924:(e,t,a)=>{"use strict";function n(e,t={}){const a={};let n;if("undefined"!=typeof window&&(n=window?.JP_CONNECTION_INITIAL_STATE?.calypsoEnv),0===e.search("https://")){const t=new URL(e);e=`https://${t.host}${t.pathname}`,a.url=encodeURIComponent(e)}else a.source=encodeURIComponent(e);for(const e in t)a[e]=encodeURIComponent(t[e]);!Object.keys(a).includes("site")&&"undefined"!=typeof jetpack_redirects&&Object.hasOwn(jetpack_redirects,"currentSiteRawUrl")&&(a.site=jetpack_redirects.currentBlogID??jetpack_redirects.currentSiteRawUrl),n&&(a.calypso_env=n);return"https://jetpack.com/redirect/?"+Object.keys(a).map((e=>e+"="+a[e])).join("&")}a.d(t,{A:()=>n})},39148:(e,t,a)=>{"use strict";a.d(t,{O:()=>o});var n=a(3924),c=a(85985),s=a(56427),i=a(86087),r=a(27723),l=a(70407);const __=r.__,o=()=>{const{tracks:e}=(0,c.st)(),[t,a]=(0,i.useState)(!1),r=(0,i.useCallback)((()=>{e.recordEvent("jetpack_editor_publicize_enable")}),[e]),o=(0,i.useCallback)((()=>{t||(a(!0),e.recordEvent("jetpack_editor_publicize_placeholder_view"))}),[t,e]);return React.createElement(s.PanelBody,{className:"jetpack-publicize__placeholder",title:__("Share this post","jetpack-publicize-pkg"),initialOpen:!1,onToggle:o},React.createElement("p",null,__("Activate the Jetpack Social feature to connect your website to the social media networks you use.","jetpack-publicize-pkg")),React.createElement(s.Button,{onClick:r,variant:"link",href:(0,l.Jc)()},__("Activate Jetpack Social","jetpack-publicize-pkg")),React.createElement("div",{className:"components-placeholder__learn-more"},React.createElement(s.ExternalLink,{href:(0,n.A)("jetpack-support-publicize")},__("Learn more","jetpack-publicize-pkg"))))}},94191:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var n=a(50723),c=a(23633),s=a(82507),i=a(89640);const r=()=>React.createElement(n.Ay,{targetDom:document.body},React.createElement(i.p,null),React.createElement(c.A,null),React.createElement(s.A,null))},37932:(e,t,a)=>{"use strict";a.d(t,{A:()=>h});var n=a(82201),c=a(14309),s=a(43656),i=a(27723),r=a(38656),l=a(50001),o=a(75168),d=a(5699),u=a(29341),p=a(39380);const __=i.__,m=s.PluginPrePublishPanel||c.PluginPrePublishPanel,h=()=>{(0,o.F)();const{hasEnabledConnections:e}=(0,l.A)(),t=(0,r.e)();return React.createElement(React.Fragment,null,React.createElement(m,{initialOpen:e,title:__("Share this post","jetpack-publicize-pkg"),icon:React.createElement(n.j,null)},React.createElement(d.A,{prePublish:!0},React.createElement(p.M,null))),t&&React.createElement(m,{initialOpen:!0,title:__("Social Image Generator","jetpack-publicize-pkg"),icon:React.createElement(n.j,null)},React.createElement(u.A,{prePublish:!0})))}},21936:(e,t,a)=>{"use strict";a.d(t,{n:()=>o});var n=a(50723),c=a(43656),s=a(51371),i=a(47453),r=a(94191),l=a(37932);function o(){return(0,s.nE)().is_publicize_enabled?React.createElement(c.PostTypeSupportCheck,{supportKeys:"publicize"},React.createElement(n.Ay,{targetDom:document.body},React.createElement(l.A,null),React.createElement(r.A,null),React.createElement(i.m,null))):null}},35674:(e,t,a)=>{"use strict";a.d(t,{h:()=>w});var n=a(50723),c=a(56427),s=a(43656),i=a(27723),r=a(51609),l=a(38656),o=a(70407),d=a(5699),u=a(29341),p=a(81115),m=a(97192),h=a(39148),g=a(39380);const __=i.__,v=()=>{const e=(0,l.e)(),[t,a]=(0,r.useState)(!1),s=(0,r.useCallback)((()=>a(!0)),[]),i=(0,r.useCallback)((()=>a(!1)),[]);return React.createElement(n.Ay,{targetDom:document.body},React.createElement(d.A,null,React.createElement(g.M,null)),e&&React.createElement(u.A,null),t&&React.createElement(p.A,{onClose:i}),React.createElement(c.PanelBody,{title:__("Social Previews","jetpack-publicize-pkg")},React.createElement(m.A,{openModal:s})))};function w(){return React.createElement(s.PostTypeSupportCheck,{supportKeys:"publicize"},(0,o.nE)().is_publicize_enabled?React.createElement(v,null):React.createElement(h.O,null))}},39380:(e,t,a)=>{"use strict";a.d(t,{M:()=>m});var n=a(85985),c=a(56427),s=a(47143),i=a(43656),r=a(27723),l=a(53512),o=a(13022),d=a(77627),u=a(50001);const __=r.__,_x=r._x,p=()=>({start:__("Start sharing your posts by connecting your social media accounts.","jetpack-publicize-pkg"),enabled:__("Click on the social icons below to control where you want to share your post.","jetpack-publicize-pkg"),disabled:__("Use this tool to share your post on all your social media accounts.","jetpack-publicize-pkg"),reshare:__('Enable the social media accounts where you want to re-share your post, then click on the "Share post" button below.',"jetpack-publicize-pkg")});function m(){const{isRePublicizeUpgradableViaUpsell:e,isRePublicizeFeatureAvailable:t,isPublicizeEnabled:a}=(0,d.A)(),m=(0,n.tu)("republicize"),[h,g,v,w]=(0,n._6)(`${m}`),{hasConnections:b}=(0,u.A)(),f=a&&!e,k=(0,s.useSelect)((e=>e(i.store).isCurrentPostPublished()),[]);if(!k||k&&t)return React.createElement("div",{className:"jetpack-publicize__upsell"},function(e,t,a){const n=p();return a?e?n.reshare:t?n.enabled:n.disabled:n.start}(k,f,b));const _=w?.product_name||__("paid","jetpack-publicize-pkg"),x=!(0,n.d9)()&&!(0,n.Sy)(),E=x?__("Re-sharing your content","jetpack-publicize-pkg"):_x("Share Your Content Again","","jetpack-publicize-pkg"),j=x?"https://jetpack.com/support/jetpack-social/#re-sharing-your-content":"https://wordpress.com/support/jetpack-social/#share-your-content-again",R=__("Upgrade now","jetpack-publicize-pkg");return t||e?React.createElement("div",{className:"jetpack-publicize__upsell"},React.createElement("div",{className:"jetpack-publicize__upsell-description"},(0,r.sprintf)(/* translators: placeholder is the product name of the plan. */
__("To re-share a post, you need to upgrade to the %s plan","jetpack-publicize-pkg"),_)),React.createElement(c.Button,{href:v?null:h,onClick:g,target:"_top",icon:l.A,className:(0,o.A)("jetpack-publicize__upsell-button is-primary",{"jetpack-upgrade-plan__hidden":!h}),isBusy:v},v?__("Redirecting…","jetpack-publicize-pkg"):R)):React.createElement("div",{className:"jetpack-publicize__upsell"},React.createElement("strong",null,E),React.createElement("br",null),(0,r.sprintf)(/* translators: placeholder is the product name of the plan. */
__("This feature is for sites with a %s plan.","jetpack-publicize-pkg"),_),React.createElement("br",null),React.createElement(c.ExternalLink,{href:j},__("More information.","jetpack-publicize-pkg")))}},11279:(e,t,a)=>{"use strict";a.d(t,{k:()=>i});var n=a(47143),c=a(93936),s=a(31963);function i(e="jetpack-sidebar/jetpack",t=!1){const{enableComplementaryArea:a}=(0,n.dispatch)(c.M_);return a("core",e),(0,n.dispatch)(s.M_).openSharePostModal(),t}},41116:(e,t,a)=>{"use strict";var n=a(78478),c=a(85985),s=a(43656),i=a(92279),r=a(21936),l=a(35674),o=a(11279);function d(){return React.createElement(React.Fragment,null,React.createElement(s.PluginSidebar,{name:"jetpack-social",title:"Jetpack Social",icon:React.createElement(n.b6,null)},React.createElement(l.h,null)),React.createElement(r.n,null))}(0,c.Oc)("share_post",(()=>(0,o.k)("jetpack-social/jetpack-social"))),(0,i.registerPlugin)("jetpack-social",{render:()=>React.createElement(d,null)})},12141:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(78478),c=a(86087),s=a(13022),i=a(28120),r=a.n(i);const l=e=>{const{checked:t,serviceName:a,label:i,onClick:r,profilePicture:l}=e,[o,d]=(0,c.useState)(!!l),u=(0,c.useCallback)((()=>d(!1)),[]),p=(0,c.useCallback)((e=>{13===e.keyCode&&r()}),[r]);return React.createElement("div",{onClick:r,onKeyDown:p,role:"switch","aria-checked":t,tabIndex:"0",className:(0,s.A)("components-connection-icon",{"components-connection-icon__picture":o})},o&&React.createElement("img",{src:l,alt:i,onError:u}),React.createElement(n.M5,{alt:i,serviceName:"instagram-business"===a?"instagram":"twitter"===a?"x":a,className:"jetpack-publicize-gutenberg-social-icon"}))};l.propTypes={serviceName:r().string,label:r().string,profilePicture:r().string};const o=l},27817:(e,t,a)=>{"use strict";a.d(t,{s:()=>l});var n=a(56427),c=a(47143),s=a(27723),i=a(31963),r=a(85605);const __=s.__;function l({connection:e}){const t=(0,c.useSelect)((t=>t(i.M_).getUpdatingConnections().includes(e.connection_id)),[e.connection_id]);return React.createElement("div",{className:r.A["connection-name"]},e.profile_link?React.createElement(n.ExternalLink,{className:r.A["profile-link"],href:e.profile_link},e.display_name):React.createElement("span",{className:r.A["profile-link"]},e.display_name),t?React.createElement(n.Spinner,{color:"black","aria-label":__("Updating account","jetpack-publicize-pkg")}):null)}},86158:(e,t,a)=>{"use strict";a.d(t,{v:()=>u});var n=a(3924),c=a(56427),s=a(47143),i=a(86087),r=a(27723),l=a(31963),o=a(43119),d=a(76046);const __=r.__,_x=r._x;function u({connection:e,service:t}){const a=(0,s.useSelect)((t=>t(l.M_).getServicesBy("status","unsupported").some((({id:t})=>t===e.service_name))),[e]);return"broken"===e.status||"must_reauth"===e.status||a?React.createElement("div",null,React.createElement("span",{className:"description"},(u=a,p=e.status,u?(0,i.createInterpolateElement)((0,r.sprintf)("%1$s %2$s",__("This platform is no longer supported.","jetpack-publicize-pkg"),__("You can use our <link>Manual Sharing</link> feature instead.","jetpack-publicize-pkg")),{link:React.createElement(c.ExternalLink,{href:(0,n.A)("jetpack-social-manual-sharing-help")})}):"broken"===p?_x("There is an issue with this connection.","This notice is shown when a social media connection is broken.","jetpack-publicize-pkg"):_x("To keep sharing with this connection, please reconnect it.","This notice is shown when a social media connection needs to be reconnected.","jetpack-publicize-pkg")))," ",!a&&t?React.createElement(d.C,{connection:e,service:t}):React.createElement(o.V,{connection:e,variant:"link",isDestructive:!1})):null;var u,p}},43119:(e,t,a)=>{"use strict";a.d(t,{V:()=>d});var n=a(51112),c=a(56427),s=a(47143),i=a(86087),r=a(27723),l=a(31963),o=a(85605);const __=r.__,_x=r._x;function d({connection:e,variant:t="secondary",isDestructive:a=!0,buttonClassName:d}){const[u,p]=(0,i.useReducer)((e=>!e),!1),{deleteConnectionById:m}=(0,s.useDispatch)(l.M_),{isDisconnecting:h,canManageConnection:g}=(0,s.useSelect)((t=>{const{getDeletingConnections:a,canUserManageConnection:n}=t(l.M_);return{isDisconnecting:a().includes(e.connection_id),canManageConnection:n(e)}}),[e]),v=(0,i.useCallback)((async()=>{p(),await m({connectionId:e.connection_id})}),[e.connection_id,m]);return g?React.createElement(React.Fragment,null,React.createElement(c.__experimentalConfirmDialog,{className:o.A.confirmDialog,isOpen:u,onConfirm:v,onCancel:p,cancelButtonText:__("Cancel","jetpack-publicize-pkg"),confirmButtonText:__("Yes","jetpack-publicize-pkg")},(0,i.createInterpolateElement)((0,r.sprintf)(
// translators: %s: The name of the connection the user is disconnecting.
__("Are you sure you want to disconnect <strong>%s</strong>?","jetpack-publicize-pkg"),e.display_name),{strong:React.createElement("strong",null)})),React.createElement(n.A,{size:"small",onClick:p,disabled:h,variant:t,isDestructive:a,className:d},h?__("Disconnecting…","jetpack-publicize-pkg"):_x("Disconnect","Disconnect a social media account","jetpack-publicize-pkg"))):null}},52683:(e,t,a)=>{"use strict";a.d(t,{j:()=>l});var n=a(56427),c=a(47143),s=a(86087),i=a(27723),r=a(31963);const __=i.__;function l({connection:e}){const{updateConnectionById:t}=(0,c.useDispatch)(r.M_),{isUpdating:a}=(0,c.useSelect)((t=>{const{getUpdatingConnections:a}=t(r.M_);return{isUpdating:a().includes(e.connection_id)}}),[e.connection_id]),i=(0,s.useCallback)((a=>{t(e.connection_id,{shared:a})}),[e.connection_id,t]);return React.createElement(n.CheckboxControl,{checked:e.shared??!1,onChange:i,disabled:a||"broken"===e.status,label:__("Mark the connection as shared","jetpack-publicize-pkg"),__nextHasNoMarginBottom:!0})}},76046:(e,t,a)=>{"use strict";a.d(t,{C:()=>o});var n=a(51112),c=a(47143),s=a(86087),i=a(27723),r=a(31963),l=a(70745);const __=i.__,_x=i._x;function o({connection:e,service:t,variant:a="link"}){const{deleteConnectionById:i,setKeyringResult:o,openConnectionsModal:d,setReconnectingAccount:u}=(0,c.useDispatch)(r.M_),{isDisconnecting:p,canManageConnection:m}=(0,c.useSelect)((t=>{const{getDeletingConnections:a,canUserManageConnection:n}=t(r.M_);return{isDisconnecting:a().includes(e.connection_id),canManageConnection:n(e)}}),[e]),h=(0,s.useCallback)((e=>{o(e),e?.ID&&d()}),[d,o]),g=(0,l.P)({service:t,onConfirm:h}),v=(0,s.useCallback)((async()=>{if(!await i({connectionId:e.connection_id,showSuccessNotice:!1}))return;await u(e);const a=new FormData;"mastodon"===t.id&&a.set("instance",e.external_handle),"bluesky"===t.id?d():g(a)}),[e,i,d,g,t.id,u]);return m?React.createElement(n.A,{size:"small",onClick:v,disabled:p,variant:a},p?__("Disconnecting…","jetpack-publicize-pkg"):_x("Reconnect","Reconnect a social media account","jetpack-publicize-pkg")):null}},91910:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(13022),c=a(28120),s=a.n(c),i=a(12141);const r=e=>{const{checked:t,disabled:a,onChange:c,serviceName:s,label:r,profilePicture:l}=e,o=(0,n.A)("components-connection-toggle",{"is-not-checked":!t,"is-disabled":a});return React.createElement("div",{className:o,title:r},React.createElement(i.A,{checked:t,label:r,onClick:c,serviceName:s,profilePicture:l}))};r.propTypes={className:s().string,checked:s().bool,id:s().string.isRequired,disabled:s().bool,onChange:s().func,serviceName:s().string,label:s().string,profilePicture:s().string};const l=r},10267:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(47143),c=a(86087),s=a(31963),i=a(91910),r=a(49514);class l extends c.Component{onConnectionChange=()=>{const{id:e}=this.props;this.isDisabled()||this.props.toggleConnection(e)};connectionIsFailing(){const{failedConnections:e,name:t}=this.props;return e.some((e=>e.service_name===t))}isDisabled(){return this.props.disabled||this.connectionIsFailing()}render(){const{enabled:e,id:t,label:a,name:n,profilePicture:c}=this.props,s="connection-"+n+"-"+t,l=n.replace("_","-"),o=React.createElement(i.A,{id:s,className:r.A["connection-toggle"],checked:e,onChange:this.onConnectionChange,disabled:this.isDisabled(),serviceName:l,label:a,profilePicture:c});return React.createElement("li",null,React.createElement("div",{className:r.A["connection-container"]},o))}}const o=(0,n.withSelect)((e=>({failedConnections:e(s.Af).getFailedConnections()})))(l)},77125:(e,t,a)=>{"use strict";a.d(t,{l:()=>m});var n=a(51112),c=a(56427),s=a(47143),i=a(86087),r=a(27723),l=a(77627),o=a(31963),d=a(51371),u=a(51475),p=a(71016);const _n=r._n,m=()=>{const{brokenConnections:e,reauthConnections:t}=(0,s.useSelect)((e=>{const t=e(o.M_);return{brokenConnections:t.getBrokenConnections(),reauthConnections:t.getMustReauthConnections()}}),[]),{connectionsPageUrl:a}=(0,l.A)(),{useAdminUiV1:r}=(0,d.nE)().feature_flags,{openConnectionsModal:m}=(0,s.useDispatch)(o.M_),h=r?React.createElement(n.A,{variant:"link",onClick:m,className:p.A["broken-connection-btn"]}):React.createElement(c.ExternalLink,{href:a}),g=[...e,...t];return g.length?g.length>0&&React.createElement(u.A,{type:"error"},(0,i.createInterpolateElement)(_n("A social connection needs attention. <fixLink>Manage connections</fixLink> to fix it.","Some social connections need attention. <fixLink>Manage connections</fixLink> to fix them.",g.length,"jetpack-publicize-pkg"),{fixLink:h})):null}},64636:(e,t,a)=>{"use strict";a.d(t,{D:()=>d});var n=a(97999),c=a(56427),s=a(27723),i=a(77627),r=a(50001),l=a(25349),o=a(71016);const __=s.__,d=()=>{const{hasConnections:e}=(0,r.A)(),{needsUserConnection:t}=(0,i.A)();return t?React.createElement(c.PanelRow,null,React.createElement("p",null,__("You must connect your WordPress.com account to be able to add social media connections.","jetpack-publicize-pkg")," ",React.createElement("a",{href:(0,n.getMyJetpackUrl)("#/connection")},__("Connect now","jetpack-publicize-pkg")))):e?null:React.createElement(c.PanelRow,null,React.createElement("div",null,React.createElement("span",{className:o.A["no-connections-text"]},__("Sharing is disabled because there are no social media accounts connected.","jetpack-publicize-pkg")),React.createElement(l.C,{label:__("Connect an account","jetpack-publicize-pkg")})))}},14993:(e,t,a)=>{"use strict";a.d(t,{E:()=>g});var n=a(85985),c=a(51609),s=a(77627),i=a(50001),r=a(10267),l=a(77125),o=a(57193),d=a(75013),u=a(25349),p=a(71016),m=a(50923),h=a(39329);const g=()=>{const{recordEvent:e}=(0,n.st)(),{connections:t,toggleById:a}=(0,i.A)(),{canBeTurnedOn:g,shouldBeDisabled:v}=(0,h.j)(),{needsUserConnection:w,isPublicizeEnabled:b}=(0,s.A)(),f=(0,c.useCallback)(((t,n)=>()=>{a(t),e("jetpack_social_connection_toggled",{location:"editor",enabled:!n.enabled,service_name:n.service_name})}),[e,a]);return React.createElement("div",null,React.createElement("ul",{className:p.A["connections-list"]},t.map((e=>{const{display_name:t,service_name:a,profile_picture:n,connection_id:c}=e;return React.createElement(r.A,{disabled:v(e),enabled:g(e)&&e.enabled,key:c,id:c,label:t,name:a,toggleConnection:f(c,e),profilePicture:n})}))),b?React.createElement(React.Fragment,null,React.createElement(d.L,null),React.createElement(l.l,null),React.createElement(m.Z,null),React.createElement(o.H,null)):null,w?null:React.createElement(u.C,{variant:"secondary"}))}},57193:(e,t,a)=>{"use strict";a.d(t,{H:()=>o});var n=a(56427),c=a(27723),s=a(77627),i=a(50001),r=a(71016),l=a(39329);const _n=c._n;function o(){const{enabledConnections:e}=(0,i.A)(),{isPublicizeEnabled:t}=(0,s.A)(),{canBeTurnedOn:a,shouldBeDisabled:o}=(0,l.j)(),d=e.filter((e=>a(e)&&!o(e)));return d.length&&t?React.createElement(n.PanelRow,null,React.createElement("p",{className:r.A["enabled-connections-notice"]},(0,c.sprintf)(/* translators: %d: number of connections */
_n("This post will be shared to %d connection.","This post will be shared to %d connections.",d.length,"jetpack-publicize-pkg"),d.length))):null}},9573:(e,t,a)=>{"use strict";a.d(t,{X:()=>u});var n=a(3924),c=a(97999),s=a(85985),i=a(56427),r=a(27723),l=a(70407),o=a(71016),d=a(647);const _x=r._x,u=()=>{const e=(0,d.X)();return(0,c.isSimpleSite)()||(0,l.pq)()?null:React.createElement(i.PanelRow,{className:o.A["enhanced-features-nudge"]},React.createElement(i.Button,{key:"upgrade",variant:"link",onClick:e,href:(0,n.A)("jetpack-social-basic-plan-block-editor",{site:(0,s.GE)()||"",query:"redirect_to="+encodeURIComponent(window.location.href)})},_x("Unlock enhanced media sharing features.","Call to action to buy a new plan","jetpack-publicize-pkg")))}},70925:(e,t,a)=>{"use strict";a.d(t,{A:()=>k});var n=a(56427),c=a(47143),s=a(43656),i=a(86087),r=a(98420),l=a(54915),o=a(80624),d=a(52367),u=a(77627),p=a(50001),m=a(51371),h=a(48222),g=a(96390),v=a(64636),w=a(14993),b=a(9573),f=a(41190);function k(){const{hasConnections:e,hasEnabledConnections:t,connections:a}=(0,p.A)(),{isPublicizeEnabled:k,isPublicizeDisabledBySitePlan:_}=(0,u.A)(),{attachedMedia:x}=(0,r.A)(),E=(0,l.A)(),j=x[0]?.id||E,{validationErrors:R,isConvertible:y}=(0,d.A)(a,(0,o.A)(j)[0]),A=(0,c.useSelect)((e=>e(s.store).isCurrentPostPublished()),[]),C=k&&(t||x.length>0||0!==Object.keys(R).length&&!y),z=_?n.Disabled:i.Fragment,{feature_flags:N}=(0,m.nE)();return React.createElement(z,null,N.useAdminUiV1?React.createElement(h._,null):null,e?React.createElement(React.Fragment,null,React.createElement(n.PanelRow,null,React.createElement(w.E,null)),N.useEditorPreview&&k&&!A?React.createElement(g.n,null):null,React.createElement(b.X,null)):null,React.createElement(v.D,null),!_&&React.createElement(i.Fragment,null,C&&React.createElement(f.o,{analyticsData:{location:"editor"}})),A?React.createElement(g.n,null):null)}},62697:(e,t,a)=>{"use strict";a.d(t,{q:()=>o});var n=a(3924),c=a(97999),s=a(56427),i=a(27723),r=a(27844),l=a(51475);const __=i.__,o=()=>(0,c.siteHasFeature)(r.q.ENHANCED_PUBLISHING)?React.createElement(l.A,{type:"warning"},__("To share to Instagram, add an image/video, or enable Social Image Generator.","jetpack-publicize-pkg"),React.createElement("br",null),React.createElement(s.ExternalLink,{href:(0,n.A)("jetpack-social-share-to-instagram")},__("Learn more","jetpack-publicize-pkg"))):React.createElement(l.A,{type:"warning"},__("You need a featured image to share to Instagram.","jetpack-publicize-pkg"),React.createElement("br",null),React.createElement(s.ExternalLink,{href:(0,n.A)("jetpack-social-share-to-instagram")},__("Learn more","jetpack-publicize-pkg")))},31099:(e,t,a)=>{"use strict";a.d(t,{j:()=>p});var n=a(3924),c=a(56427),s=a(47143),i=a(86087),r=a(27723),l=a(80682),o=a(31963),d=a(51475),u=a(9515);const __=r.__,_x=r._x,p=({validationErrors:e})=>{const{getConnectionById:t}=(0,s.useSelect)((e=>e(o.M_)),[]),a=(0,u.h)(),r=(0,i.useMemo)((()=>Object.entries(e).reduce(((e,[n,c])=>{if(!c)return e;e[c]||(e[c]=[]);const s=a(t(n)?.service_name);return s&&!e[c].includes(s)&&e[c].push(s),e}),{})),[t,a,e]);return React.createElement(d.A,{type:"warning"},React.createElement("p",null,__("The selected media cannot be shared to some social media platforms.","jetpack-publicize-pkg")),React.createElement("ul",null,Object.entries(r).map((([e,t])=>t.length?React.createElement("li",{key:e},React.createElement("i",null,(0,l.$m)(e)),_x(":","Colon to display before the list of social media platforms","jetpack-publicize-pkg")+" ",t.map(((e,t,{length:a})=>React.createElement(i.Fragment,{key:e},React.createElement("b",null,e),t<a-1&&_x(",","Comma to separate list of social media platforms","jetpack-publicize-pkg")+" ")))):null))),React.createElement(c.ExternalLink,{href:(0,n.A)("jetpack-social-media-support-information")},__("Troubleshooting tips","jetpack-publicize-pkg")))}},75013:(e,t,a)=>{"use strict";a.d(t,{L:()=>u});var n=a(98420),c=a(54915),s=a(80624),i=a(52367),r=a(80682),l=a(50001),o=a(62697),d=a(31099);const u=()=>{const{connections:e}=(0,l.A)(),{attachedMedia:t}=(0,n.A)(),a=(0,c.A)(),u=t[0]?.id||a,{validationErrors:p,isConvertible:m}=(0,i.A)(e,(0,s.A)(u)[0]);return Object.keys(p).length?Object.values(p).includes(r.k3)?React.createElement(o.q,null):m?null:React.createElement(d.j,{validationErrors:p}):null}},25349:(e,t,a)=>{"use strict";a.d(t,{C:()=>d});var n=a(56427),c=a(47143),s=a(27723),i=a(77627),r=a(31963),l=a(51371),o=a(71016);const __=s.__;function d({label:e,variant:t="secondary"}){const{useAdminUiV1:a}=(0,l.nE)().feature_flags,{connections:s}=(0,c.useSelect)((e=>({connections:e(r.M_).getConnections()})),[]),{openConnectionsModal:d}=(0,c.useDispatch)(r.M_),{connectionsPageUrl:u}=(0,i.A)(),p=e||__("Manage connections","jetpack-publicize-pkg"),m=s.length>0;return a?React.createElement(n.Button,{onClick:d,variant:m?"link":t,className:o.A["settings-button"]},p):React.createElement(n.ExternalLink,{className:o.A["settings-button"],href:u},p)}},41190:(e,t,a)=>{"use strict";a.d(t,{o:()=>u});var n=a(97999),c=a(27723),s=a(39553),i=a(27844),r=a(31704),l=a(12015),o=a(78144),d=a(71016);const __=c.__,u=({analyticsData:e=null})=>{const{message:t,updateMessage:a,maxLength:c}=(0,s.A)(),u=(0,r.D)();return React.createElement(React.Fragment,null,!u&&React.createElement(o.Ay,{label:__("Message","jetpack-publicize-pkg"),maxLength:c,onChange:a,message:t,analyticsData:e}),(0,n.siteHasFeature)(i.q.ENHANCED_PUBLISHING)&&React.createElement("div",{className:d.A["share-post-form__media-section"]},React.createElement(l.A,{analyticsData:e})))}},50923:(e,t,a)=>{"use strict";a.d(t,{Z:()=>d});var n=a(56427),c=a(47143),s=a(27723),i=a(31963),r=a(70407),l=a(51475),o=a(71016);const __=s.__,_x=s._x,d=()=>{const e=(0,c.useSelect)((e=>{const{getServicesBy:t,getConnectionsByService:a}=e(i.M_);return t("status","unsupported").filter((e=>a(e.id).length))}),[]);return e.length?React.createElement(l.A,{type:"error"},_x("Following platforms are not supported anymore:","Followed by a list of social media platforms that are no longer supported by Publicize.","jetpack-publicize-pkg"),React.createElement("ul",{className:o.A["unsupported-connections-list"]},e.map((e=>React.createElement("li",{key:e.id},e.label)))),React.createElement(n.ExternalLink,{href:(0,r.Jc)()},__("Learn more","jetpack-publicize-pkg"))):null}},647:(e,t,a)=>{"use strict";a.d(t,{X:()=>i});var n=a(47143),c=a(43656),s=a(86087);function i(){const{isEditedPostDirty:e}=(0,n.useSelect)(c.store,[]),{autosave:t}=(0,n.useDispatch)(c.store);return(0,s.useCallback)((async a=>{if(!(a.target instanceof HTMLAnchorElement))return;const n=a.target.getAttribute("target");e()&&!n&&(a.preventDefault(),await t(),window.location.href=a.target.href),n&&(a.preventDefault(),window.open(a.target.href,n,"noreferrer"))}),[t,e])}},39329:(e,t,a)=>{"use strict";a.d(t,{j:()=>p});var n=a(86087),c=a(51609),s=a(98420),i=a(54915),r=a(80624),l=a(52367),o=a(80682),d=a(77627),u=a(50001);const p=()=>{const{connections:e}=(0,u.A)(),{isPublicizeEnabled:t,isPublicizeDisabledBySitePlan:a}=(0,d.A)(),{attachedMedia:p}=(0,s.A)(),m=(0,i.A)(),h=p[0]?.id||m,{validationErrors:g,isConvertible:v}=(0,l.A)(e,(0,r.A)(h)[0]),w=(0,n.useCallback)((e=>{const{connection_id:t,status:a}=e,n="broken"!==a,c=void 0!==g[t]&&!v,s=g[t]===o.k3;return n&&!c&&!s}),[v,g]),b=(0,n.useCallback)((e=>!t||!w(e)),[w,t]),f=(0,n.useCallback)((e=>!a&&w(e)),[w,a]);return(0,c.useMemo)((()=>({shouldBeDisabled:b,canBeTurnedOn:f})),[b,f])}},56457:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>b});var n=a(50723),c=a(1455),s=a.n(c),i=a(56427),r=a(47143),l=a(43656),o=a(27723),d=a(13022),u=a(51609),p=a.n(u),m=a(49747),h=a(60548),g=a(74820);const __=o.__,_x=o._x,v="featured-image-still-loading",w=(e,t,a,n)=>{if("none"===e||"custom"===e&&!t||"featured"===(e??"featured")&&!a)return null;const c=n("custom"===e?t:a);return c?(e=>e?.media_details?.sizes?.large?.source_url||e?.source_url)(c):v};function b({shouldDebounce:e=!0,onNewToken:t,...a}){const[c,o]=(0,u.useState)(null),[b,f]=(0,u.useState)(!0),{customText:k,imageType:_,imageId:x,template:E,setToken:j}={...(0,m.A)(),...a},{title:R,imageUrl:y}=(0,r.useSelect)((e=>{const t=e(l.store).getEditedPostAttribute("featured_media");return{title:e(l.store).getEditedPostAttribute("title"),imageUrl:w(_,x,t,e("core").getMedia)}})),A=(0,u.useMemo)((()=>k||R||" "),[k,R]),C=(0,u.useRef)(A),z=(0,u.useRef)(c);(0,u.useEffect)((()=>{z.current=c})),(0,u.useEffect)((()=>{const a=setTimeout((async()=>{if(f(!0),y===v)return;const e=await s()({path:"wpcom/v2/publicize/social-image-generator/generate-token",method:"POST",data:{text:A,image_url:y,template:E}});j?.(e),t?.(e);const a=(0,g.S)(e);a!==z.current?o(a):f(!1)}),A!==C.current&&e?1500:0);return()=>{clearTimeout(a),C.current=A}}),[A,E,y,t]);const N=(0,u.useCallback)((()=>{f(!1)}),[]);return p().createElement(n.Ay,null,p().createElement(i.BaseControl,{__nextHasNoMarginBottom:!0},p().createElement(i.BaseControl.VisualLabel,null,_x("Preview","Heading for the generated preview image","jetpack-publicize-pkg")),p().createElement("div",{className:h.A.container},p().createElement("img",{className:(0,d.A)({[h.A.hidden]:b}),src:c,alt:__("Generated preview","jetpack-publicize-pkg"),onLoad:N}),b&&p().createElement(i.Spinner,{"data-testid":"spinner"}))))}},74820:(e,t,a)=>{"use strict";a.d(t,{S:()=>c});var n=a(3924);function c(e){return e?(0,n.A)("sigenerate",{query:`t=${e}`}):""}},47453:(e,t,a)=>{"use strict";a.d(t,{m:()=>i});var n=a(97999),c=a(27844),s=a(7757);const i=()=>React.createElement(React.Fragment,null,(0,n.siteHasFeature)(c.q.SHARE_STATUS)?React.createElement(s.Q3,null):null)},59493:(e,t,a)=>{"use strict";a.d(t,{O:()=>g});var n=a(63406),c=a(3924),s=a(51112),i=a(56427),r=a(47143),l=a(86087),o=a(27723),d=a(51475),u=a(31963),p=a(39630),m=a(18973);const __=o.__,_x=o._x;function h({label:e,profile_picture:t}){return React.createElement("div",{className:m.A["account-info"]},t?React.createElement("img",{className:m.A["profile-pic"],src:t,alt:e}):null,React.createElement("span",null,e))}function g({keyringResult:e,onComplete:t,canMarkAsShared:a}){const o=(0,p.o)(),{existingConnections:g,reconnectingAccount:v}=(0,r.useSelect)((e=>{const t=e(u.M_);return{existingConnections:t.getConnections(),reconnectingAccount:t.getReconnectingAccount()}}),[]),{createErrorNotice:w}=(0,n.I)(),b=o.find((t=>t.id===e.service)),f=(0,l.useCallback)((e=>g.some((t=>t.service_name===b?.id&&t.external_id===e))),[g,b.id]),k=(0,l.useMemo)((()=>{const t=[],a=[];if(!b)return{connected:t,not_connected:a};const n=[];if(b.supports.additional_users_only||n.push({label:e.external_display||e.external_name,value:e.external_ID,profile_picture:e.external_profile_picture}),b.supports.additional_users&&e.additional_external_users?.length)for(const t of e.additional_external_users)n.push({label:t.external_name,value:t.external_ID,profile_picture:t.external_profile_picture});for(const e of n)f(e.value)?t.push(e):a.push(e);return{connected:t,not_connected:a}}),[f,e,b]),{createConnection:_,setReconnectingAccount:x}=(0,r.useDispatch)(u.M_),E=(0,l.useCallback)((async a=>{a.preventDefault(),a.stopPropagation();const n=a.target,c=new FormData(n),s=c.get("external_user_ID");if(!s)return void w(__("Please select an account to connect.","jetpack-publicize-pkg"));const i={external_user_ID:b.supports.additional_users?s:void 0,keyring_connection_ID:e.ID,shared:"1"===c.get("shared")||void 0},r=k.not_connected.find((e=>e.value===s));v&&x(void 0),_(i,{display_name:r?.label,profile_picture:r?.profile_picture,service_name:b.id,external_id:s.toString()}),t()}),[_,v,x,w,e.ID,t,b.supports,b.id,k.not_connected]);return React.createElement("section",{className:m.A.confirmation},k.not_connected.length?React.createElement("div",null,React.createElement("p",{className:m.A["header-text"]},__("Select the account you'd like to connect. All your new blog posts will be automatically shared to this account. You'll be able to change this option in the editor sidebar when you're writing a post.","jetpack-publicize-pkg")),e?.show_linkedin_warning&&React.createElement(d.A,{type:"warning"},React.createElement("p",null,__("We could not retrieve which company pages you have access to. This is a known issue with the LinkedIn API. If you would like to connect a company page, please retry after 5 minutes.","jetpack-publicize-pkg")," ",React.createElement(i.ExternalLink,{key:"linkedin-api-documentaion",href:(0,c.A)("jetpack-linkedin-permissions-warning")},__("Learn more","jetpack-publicize-pkg")))),React.createElement("form",{className:m.A.form,onSubmit:E,id:"connection-confirmation-form"},React.createElement("div",{className:m.A["accounts-list"]},k.not_connected.map(((e,t)=>{const a=v?v.service_name===b?.id&&v.external_id===e.value:0===t;return React.createElement("label",{key:e.value,htmlFor:`external_user_ID__${e.value}`,className:m.A["account-label"],"aria-required":!0},React.createElement("input",{type:"radio",id:`external_user_ID__${e.value}`,name:"external_user_ID",value:e.value,defaultChecked:a,className:m.A["account-input"],required:!0}),React.createElement(h,{label:e.label,profile_picture:e.profile_picture}))}))),a?React.createElement(i.BaseControl,{__nextHasNoMarginBottom:!0,id:"mark-connection-as-shared",help:`${__("If enabled, the connection will be available to all administrators, editors, and authors.","jetpack-publicize-pkg")} ${__("You can change this later.","jetpack-publicize-pkg")}`},React.createElement(i.__experimentalHStack,{justify:"flex-start",spacing:3},React.createElement("span",null,React.createElement("input",{type:"checkbox",id:"mark-connection-as-shared",name:"shared",value:"1"})),React.createElement(i.FlexBlock,{as:"label",htmlFor:"mark-connection-as-shared"},__("Mark the connection as shared","jetpack-publicize-pkg")))):null,React.createElement("input",{type:"hidden",name:"keyring_connection_ID",value:e.ID}))):React.createElement("p",{className:m.A["header-text"]},k.connected.length?_x("No more accounts/pages found.","Message shown when there are no connections found to connect","jetpack-publicize-pkg"):__("No accounts/pages found.","jetpack-publicize-pkg")),k.connected.length?React.createElement("section",null,React.createElement("h3",null,__("Already connected","jetpack-publicize-pkg")),React.createElement("ul",null,k.connected.map(((e,t)=>React.createElement("li",{key:e.label+t},React.createElement(h,{label:e.label,profile_picture:e.profile_picture})))))):null,React.createElement("div",{className:m.A["submit-wrap"]},React.createElement(s.A,{variant:"secondary",onClick:t},__("Cancel","jetpack-publicize-pkg")),k.not_connected.length?React.createElement(s.A,{form:"connection-confirmation-form",type:"submit"},__("Confirm","jetpack-publicize-pkg")):null))}},48222:(e,t,a)=>{"use strict";a.d(t,{_:()=>b});var n=a(60442),c=a(47425),s=a(3924),i=a(50723),r=a(56427),l=a(47143),o=a(86087),d=a(27723),u=a(13022),p=a(21259),m=a(31963),h=a(78362),g=a(59493),v=a(73842);const __=d.__,_x=d._x,w=()=>{const{keyringResult:e}=(0,l.useSelect)((e=>{const{getKeyringResult:t}=e(m.M_);return{keyringResult:t()}}),[]),{setKeyringResult:t,closeConnectionsModal:a,setReconnectingAccount:i}=(0,l.useDispatch)(m.M_),[d]=(0,n.A)("sm"),w=(0,o.useCallback)((()=>{t(null),i(void 0),a()}),[a,t,i]),b=Boolean(e?.ID),f=b?__("Connection confirmation","jetpack-publicize-pkg"):_x("Manage Jetpack Social connections","","jetpack-publicize-pkg"),k=(0,p._)();return React.createElement(r.Modal,{className:(0,u.A)(v.A.modal,{[v.A.small]:d}),onRequestClose:w,title:f},b?React.createElement(g.O,{keyringResult:e,onComplete:w,canMarkAsShared:k}):React.createElement(React.Fragment,null,React.createElement(h.b,null),React.createElement("div",{className:v.A["manual-share"]},React.createElement("em",null,React.createElement(c.Ay,null,__("Want to share to other networks? Use our Manual Sharing feature from the editor.","jetpack-publicize-pkg")," ",React.createElement(r.ExternalLink,{href:(0,s.A)("jetpack-social-manual-sharing-help")},__("Learn more","jetpack-publicize-pkg")))))))};function b(){const e=(0,l.useSelect)((e=>e(m.M_).isConnectionsModalOpen()),[]);return React.createElement(i.Ay,{targetDom:document.body},e?React.createElement(w,null):null)}},30642:(e,t,a)=>{"use strict";a.d(t,{F:()=>u});var n=a(50723),c=a(47425),s=a(40597),i=a(56427),r=a(27723),l=a(68541),o=a(34600),d=a(27681);const __=r.__;function u(e){return React.createElement(n.Ay,null,React.createElement("div",{className:d.A.wrapper},React.createElement(i.Flex,{align:"start",justify:"start"},React.createElement(c.Ay,{variant:"body-extra-small",className:d.A.title},__("Manual sharing","jetpack-publicize-pkg")),React.createElement(s.A,{inline:!1,shift:!0,iconSize:16,placement:"top-end"},React.createElement(o.P,e))),React.createElement(l.R,{buttonStyle:"icon"})))}},34600:(e,t,a)=>{"use strict";a.d(t,{P:()=>r});var n=a(47425),c=a(3924),s=a(56427),i=a(27723);const __=i.__;function r({...e}){return React.createElement(n.Ay,e,__('Just tap the social network or "Copy to Clipboard" icon, and we\'ll format your content for sharing.',"jetpack-publicize-pkg")," ",React.createElement(s.ExternalLink,{href:(0,c.A)("jetpack-social-manual-sharing-help")},__("Learn more","jetpack-publicize-pkg")))}},55858:(e,t,a)=>{"use strict";a.d(t,{A:()=>w});var n=a(51112),c=a(50723),s=a(94715),i=a(56427),r=a(86087),l=a(27723),o=a(51113),d=a(31249),u=a(13022),p=a(52367),m=a(2204),h=a(18699),g=a(97671);const __=l.__,v=e=>t=>{t.currentTarget.focus(),e()};function w({buttonLabel:e,subTitle:t,mediaId:a=null,mediaDetails:l={},onChange:w,wrapperClassName:b,allowedMediaTypes:f=null}){const{mediaData:{width:k,height:_,sourceUrl:x}={},metaData:{mime:E,length:j=null}={},previewData:{width:R,height:y,sourceUrl:A}={}}=l,C=!(x&&k&&_&&E),z=(0,r.useCallback)((()=>w(null)),[w]),N=(0,r.useCallback)((e=>{w(e)}),[w]),S=(0,r.useCallback)((e=>{const t=(0,p.c)(E);return React.createElement("div",{className:(0,u.A)(g.A["preview-wrapper"],b)},React.createElement("button",{className:g.A.remove,onClick:z},React.createElement(i.VisuallyHidden,null,__("Remove media","jetpack-publicize-pkg")),React.createElement(o.A,{icon:d.A})),React.createElement("button",{className:g.A.preview,onClick:v(e),"data-unstable-ignore-focus-outside-for-relatedtarget":".media-modal"},t?React.createElement(h.A,{sourceUrl:x,mime:E,duration:j}):React.createElement(i.ResponsiveWrapper,{naturalWidth:R||k,naturalHeight:y||_,isInline:!0},React.createElement("img",{src:A||x,alt:"",className:g.A["preview-image"]}))))}),[_,j,E,z,y,A,R,x,k,b]),M=(0,r.useCallback)((c=>React.createElement("div",{className:g.A.container},a?React.createElement(React.Fragment,null,React.createElement("button",{className:g.A["remove-loading"],onClick:z},React.createElement(i.VisuallyHidden,null,__("Remove media","jetpack-publicize-pkg")),React.createElement(o.A,{icon:d.A})),React.createElement(i.Spinner,{"data-testid":"spinner"})):React.createElement(React.Fragment,null,React.createElement(n.A,{variant:"secondary",size:"small",className:g.A.preview,onClick:v(c),"data-unstable-ignore-focus-outside-for-relatedtarget":".media-modal"},e),t&&React.createElement("span",null,t)))),[e,a,z,t]),P=(0,r.useCallback)((({open:e})=>a&&!C?S(e):M(e)),[a,C,S,M]);return React.createElement(c.Ay,null,React.createElement(s.MediaUploadCheck,null,React.createElement(s.MediaUpload,{allowedTypes:f??m.UB,title:e,onSelect:N,render:P,value:a})))}},12015:(e,t,a)=>{"use strict";a.d(t,{A:()=>v});var n=a(50723),c=a(3924),s=a(85985),i=a(56427),r=a(86087),l=a(27723),o=a(51609),d=a.n(o),u=a(98420),p=a(80624),m=a(55858),h=a(35270);const __=l.__,g=__("Choose Media","jetpack-publicize-pkg");function v({disabled:e=!1,disabledNoticeMessage:t="",CustomNotice:a=null,analyticsData:l}){const{attachedMedia:v,updateAttachedMedia:w}=(0,u.A)(),{recordEvent:b}=(0,s.st)(),[f]=(0,p.A)(v[0]?.id),k=(0,r.useCallback)((e=>{if(e){b("jetpack_social_media_attached",l);const{id:t,url:a,mime:n}=e;w([{id:t,url:a,type:n}])}else w([])}),[l,b,w]),_=e?i.Disabled:o.Fragment,x=e?{className:h.A.disabled,"data-testid":"disabled"}:{};return d().createElement(n.Ay,null,d().createElement(i.BaseControl,{__nextHasNoMarginBottom:!0,className:h.A.wrapper},d().createElement(i.BaseControl.VisualLabel,null,__("Attached Media","jetpack-publicize-pkg")),a||(t?d().createElement(i.Notice,{className:h.A.notice,isDismissible:!1,status:"warning"},d().createElement("p",{"data-testid":"notice"},t)):d().createElement("p",{className:h.A.subtitle},__("Choose a visual to accompany your post.","jetpack-publicize-pkg"))),d().createElement(_,x,d().createElement(m.A,{buttonLabel:g,subTitle:__("Add an image or video","jetpack-publicize-pkg"),mediaId:v[0]?.id,mediaDetails:f,onChange:k}),d().createElement(i.ExternalLink,{href:(0,c.A)("jetpack-social-media-support-information"),className:h.A["learn-more"]},__("Learn photo and video best practices","jetpack-publicize-pkg")))))}},78144:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>o});var n=a(85985),c=a(56427),s=a(27723),i=a(51609);const __=s.__,_n=s._n,r=()=>__("Write a custom message for your social audience here. This message will override your social post content.","jetpack-publicize-pkg"),l=()=>__("Message","jetpack-publicize-pkg");function o({label:e=l(),placeholder:t=r(),message:a="",onChange:o,disabled:d,maxLength:u,analyticsData:p=null}){const{recordEvent:m}=(0,n.st)(),h=(0,i.useRef)(!0),g=u-a.length,v=(0,i.useCallback)((e=>{o(e),h.current&&(m("jetpack_social_custom_message_changed",p),h.current=!1)}),[p,h,o,m]);return React.createElement(c.TextareaControl,{value:a,label:e,onChange:v,disabled:d,maxLength:u,placeholder:t,rows:4,help:(0,s.sprintf)(/* translators: placeholder is a number. */
_n("%d character remaining","%d characters remaining",g,"jetpack-publicize-pkg"),g),__nextHasNoMarginBottom:!0})}},51475:(e,t,a)=>{"use strict";a.d(t,{A:()=>p});var n=a(56427),c=a(27723),s=a(51113),i=a(31249),r=a(13022),l=a(28120),o=a.n(l),d=a(93794);const __=c.__,u=({children:e,type:t="default",actions:a=[],onDismiss:c})=>{const l=(0,r.A)(d.A.notice,d.A[`notice--${t}`]);return React.createElement("div",{className:l},React.createElement("div",null," ",e," "),c&&React.createElement("button",{className:d.A.dismiss,onClick:c},React.createElement(n.VisuallyHidden,null,__("Dismiss notice","jetpack-publicize-pkg")),React.createElement(s.A,{icon:i.A})),a&&a.length>0&&React.createElement("div",{className:d.A.actions},a.map((e=>e))))};u.propTypes={children:o().node.isRequired,type:o().oneOf(["default","highlight","warning","error"]),actions:o().arrayOf(o().element),onDismiss:o().func};const p=u},5699:(e,t,a)=>{"use strict";a.d(t,{A:()=>b});var n=a(97999),c=a(56427),s=a(47143),i=a(43656),r=a(86087),l=a(27723),o=a(77627),d=a(88584),u=a(49036),p=a(50001),m=a(27844),h=a(70925),g=a(30642),v=a(51353),w=a(87170);const __=l.__,_x=l._x,b=({prePublish:e,children:t})=>{const{refresh:a,hasConnections:l,hasEnabledConnections:b}=(0,p.A)(),f=(0,s.useSelect)((e=>e(i.store).isCurrentPostPublished()),[]),k=(0,d.A)(),{isPublicizeEnabled:_,hidePublicizeFeature:x,togglePublicizeFeature:E}=(0,o.A)();(0,u._D)((function(){b&&a()}),[b,a]);const j=e?r.Fragment:c.PanelBody,R=e?{}:{title:__("Share this post","jetpack-publicize-pkg"),className:w.A.panel};return k(),React.createElement(j,R,t,!x&&React.createElement(r.Fragment,null,!f&&React.createElement(c.PanelRow,null,React.createElement(c.ToggleControl,{label:_?__("Share when publishing","jetpack-publicize-pkg"):_x("Sharing is disabled","Label for publicize toggle","jetpack-publicize-pkg"),onChange:E,checked:_&&l,disabled:!l,__nextHasNoMarginBottom:!0})),React.createElement(h.A,null)),f&&React.createElement(React.Fragment,null,(0,n.siteHasFeature)(m.q.SHARE_STATUS)?React.createElement(v.E,null):null,React.createElement(g.F,null)))}},23633:(e,t,a)=>{"use strict";a.d(t,{A:()=>p});var n=a(82201),c=a(47143),s=a(14309),i=a(43656),r=a(27723),l=a(34600),o=a(68541),d=a(5780);const __=r.__,u=i.PluginPostPublishPanel||s.PluginPostPublishPanel;function p(){return(0,c.useSelect)((e=>e(i.store).isCurrentPostPublished()),[])?React.createElement(u,{initialOpen:!0,title:__("Manual sharing","jetpack-publicize-pkg"),icon:React.createElement(n.j,null)},React.createElement(l.P,{className:d.A.description,variant:"body-small"}),React.createElement(o.R,null)):null}},82507:(e,t,a)=>{"use strict";a.d(t,{A:()=>g});var n=a(3924),c=a(1455),s=a.n(c),i=a(14309),r=a(43656),l=a(86087),o=a(77627),d=a(49036),u=a(50001),p=a(70407),m=a(25300);const h=r.PluginPostPublishPanel||i.PluginPostPublishPanel,g=()=>{const{review:e}=(0,p.nE)(),[t,a]=(0,l.useState)(e?.dismissed??!0),[c,i]=(0,l.useState)(!1),{hasEnabledConnections:r}=(0,u.A)(),{isPublicizeEnabled:g,isPostAlreadyShared:v}=(0,o.A)();(0,d.Wv)((()=>{i(!v&&g&&r)}),[v,r,g]);const w=(0,l.useCallback)((()=>{s()({path:e?.dismiss_path,method:"POST",data:{dismissed:!0}}).catch((e=>{throw e})),a(!0)}),[e?.dismiss_path]);return t||!c?null:React.createElement(h,null,React.createElement(m.A,{href:(0,n.A)("jetpack-social-plugin-reviews"),onClose:w}))}},89640:(e,t,a)=>{"use strict";a.d(t,{p:()=>g});var n=a(97999),c=a(47143),s=a(14309),i=a(43656),r=a(92214),l=a(383),o=a(57275),d=a(49036),u=a(31963),p=a(27844),m=a(79594);const h=i.PluginPostPublishPanel||s.PluginPostPublishPanel;function g(){const{isPublicizeEnabled:e}=(0,l.h)(),{pollForPostShareStatus:t}=(0,c.useDispatch)(u.M_),{isPostPublished:a}=(0,c.useSelect)((e=>({isPostPublished:e(i.store).isCurrentPostPublished()})),[]),s=(0,o.z)((0,r.P)()),g=(0,o.z)((0,c.useSelect)((e=>e(u.M_).getEnabledConnections()),[])),v=e&&g.length>0&&s,w=(0,n.siteHasFeature)(p.q.SHARE_STATUS)&&v&&a;return(0,d._D)((()=>{w&&t({isRequestComplete:({postShareStatus:e})=>e.done})}),[w]),w?React.createElement(h,null,React.createElement(m.q,null)):null}},79594:(e,t,a)=>{"use strict";a.d(t,{q:()=>d});var n=a(56427),c=a(47143),s=a(27723),i=a(31963),r=a(51475),l=a(7757),o=a(55387);const __=s.__,_n=s._n;function d({reShareTimestamp:e}){const t=(0,c.useSelect)((e=>e(i.M_).getPostShareStatus()),[]),a=e?t.shares.filter((t=>t.timestamp>e)):t.shares;if(t.polling)return React.createElement("div",{className:o.A["loading-block"]},React.createElement(n.Spinner,null),React.createElement("span",{className:o.A["loading-text"]},__("Sharing to your social media…","jetpack-publicize-pkg")));const d=a.filter((e=>"failure"===e.status)).length;return d>0?React.createElement(r.A,{type:"warning"},React.createElement("p",null,(0,s.sprintf)(/* translators: %d: number of failed shares */
_n("Your post was unable to be shared to %d connection.","Your post was unable to be shared to %d connections.",d,"jetpack-publicize-pkg"),d)),React.createElement(l.JU,{variant:"link",analyticsData:{location:"post-publish-panel"}},__("Review status and try again","jetpack-publicize-pkg"))):t.done?a.length?React.createElement(React.Fragment,null,React.createElement("b",null,__("Your post was shared.","jetpack-publicize-pkg"))," ","🎉",React.createElement("p",null,(0,s.sprintf)(/* translators: %d: number of connections to which a post was shared */
_n("Your post was successfuly shared to %d connection.","Your post was successfuly shared to %d connections.",a.length,"jetpack-publicize-pkg"),a.length)),React.createElement(l.JU,{analyticsData:{location:e?"resharing-section":"post-publish-panel"}})):React.createElement("span",null,__("Your post was not shared.","jetpack-publicize-pkg")):React.createElement("span",null,__("The request to share your post is still in progress.","jetpack-publicize-pkg")," ",__("Please refresh and check again in a few minutes.","jetpack-publicize-pkg"))}},51353:(e,t,a)=>{"use strict";a.d(t,{E:()=>o});var n=a(47143),c=a(51609),s=a(31963),i=a(79594),r=a(7757),l=a(61420);const o=()=>{const e=(0,n.useSelect)((e=>e(s.M_).getPostShareStatus()),[]),[t,a]=(0,c.useState)(null);return(0,c.useEffect)((()=>{e.polling&&a(Date.now()/1e3)}),[e.polling]),t?React.createElement("div",{className:l.A.wrapper},React.createElement(i.q,{reShareTimestamp:t})):React.createElement(r.JU,{withWrapper:!0,analyticsData:{location:"editor"}})}},25300:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(50723),c=a(51112),s=a(85985),i=a(27723),r=a(51609),l=a(76445);const __=i.__,o=({href:e,onClose:t})=>{const{recordEvent:a}=(0,s.st)({pageViewEventName:"social_plugin_review_prompt",pageViewNamespace:"jetpack",pageViewSuffix:"view"}),o=(0,r.useCallback)((()=>{a("jetpack_social_plugin_review_prompt_new_review_click")}),[a]),d=(0,r.useCallback)((()=>{a("jetpack_social_plugin_review_prompt_dismiss_click"),t()}),[a,t]);return React.createElement(n.Ay,null,React.createElement("div",{className:l.A.prompt},React.createElement("h2",{className:l.A.header},(0,i.sprintf)(/* translators: %s is the celebration emoji */
__("Presto! %s","jetpack-publicize-pkg"),String.fromCodePoint(127881))),React.createElement("p",null,__("Just like that, Jetpack Social has shared your post to your connected social accounts.","jetpack-publicize-pkg")),React.createElement("p",null,__("Please leave a review to let others know how easy getting your posts on social media can be!","jetpack-publicize-pkg")),React.createElement("div",{className:l.A.buttons},React.createElement(c.A,{onClick:o,isExternalLink:!0,href:e,className:l.A.button},__("Leave a Review","jetpack-publicize-pkg")),React.createElement(c.A,{onClick:d,variant:"link",className:l.A.button},__("Dismiss","jetpack-publicize-pkg")))))}},25357:(e,t,a)=>{"use strict";a.d(t,{A:()=>u});var n=a(56427),c=a(38443),s=a(86087),i=a(27723),r=a(25877),l=a(66888);const __=i.__,_x=i._x,o=e=>{const t=new Date(e);return t.setDate(e.getDate()+1),!(0,c.isInTheFuture)(t)},d=({onClose:e,currentTimestamp:t,onTimestampChange:a,onChange:i,onConfirm:r})=>{const d=(0,s.useCallback)((()=>{r?.(t),e()}),[e,r,t]),u=(0,s.useCallback)((e=>{const t=Math.floor((0,c.getDate)(e).getTime()/1e3);a(t),i?.(t)}),[i,a]),p=(0,c.date)("Y-m-d\\TH:i:s",new Date(1e3*t),void 0);return React.createElement(React.Fragment,null,React.createElement(n.DateTimePicker,{onChange:u,currentDate:p,isInvalidDate:o}),React.createElement(n.Button,{variant:"primary",onClick:d,className:l.A.confirm,disabled:!(0,c.isInTheFuture)(p)},_x("Confirm","Confirms the date and time selected to be used to share the post","jetpack-publicize-pkg")))},u=({scheduleTimestamp:e,onChange:t,onConfirm:a,isBusy:c,isDisabled:i})=>{const l=e||Math.floor(Date.now()/1e3),[o,u]=(0,s.useState)(l),p=(0,s.useCallback)((({onToggle:e,isOpen:t})=>React.createElement(n.Button,{onClick:c?null:e,"aria-expanded":t,"aria-live":"polite",icon:r.A,isSecondary:!0,isBusy:c,disabled:i},__("Schedule","jetpack-publicize-pkg"))),[c,i]),m=(0,s.useCallback)((({onClose:e})=>React.createElement(d,{onClose:e,currentTimestamp:o,onTimestampChange:u,onChange:t,onConfirm:a})),[o,t,a]);return React.createElement(n.Dropdown,{popoverProps:{placement:"bottom-start"},renderToggle:p,renderContent:m})}},71444:(e,t,a)=>{"use strict";function n(){return React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none"},React.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.99967 15.4173C8.56309 15.4173 7.18533 14.8466 6.16951 13.8308C5.15369 12.815 4.58301 11.4372 4.58301 10.0007C4.58301 8.56406 5.15369 7.18631 6.16951 6.17049C7.18533 5.15467 8.56309 4.58398 9.99967 4.58398C11.4363 4.58398 12.814 5.15467 13.8298 6.17049C14.8457 7.18631 15.4163 8.56406 15.4163 10.0007C15.4163 11.4372 14.8457 12.815 13.8298 13.8308C12.814 14.8466 11.4363 15.4173 9.99967 15.4173ZM3.33301 10.0007C3.33301 8.23254 4.03539 6.53685 5.28563 5.28661C6.53587 4.03636 8.23156 3.33398 9.99967 3.33398C11.7678 3.33398 13.4635 4.03636 14.7137 5.28661C15.964 6.53685 16.6663 8.23254 16.6663 10.0007C16.6663 11.7688 15.964 13.4645 14.7137 14.7147C13.4635 15.9649 11.7678 16.6673 9.99967 16.6673C8.23156 16.6673 6.53587 15.9649 5.28563 14.7147C4.03539 13.4645 3.33301 11.7688 3.33301 10.0007ZM10.833 10.834V6.66732H9.58301V9.58398H7.91634V10.834H10.833Z",fill:"currentColor"}))}a.d(t,{O:()=>n})},76329:(e,t,a)=>{"use strict";a.d(t,{h:()=>u});var n=a(56427),c=a(38443),s=a(86087),i=a(27723),r=a(76673),l=a(12141),o=a(71444),d=a(16522);const __=i.__;function u({connection:e,scheduledAt:t,onDelete:a,confirmDeletion:i=!0}){const u=(0,c.format)("D, M j, o g:i A",new Date(1e3*t).toUTCString()),[p,m]=(0,s.useReducer)((e=>!e),!1),h=(0,s.useCallback)((()=>{a(),m()}),[a]);return React.createElement("div",{className:d.A.wrapper},React.createElement("div",{className:d.A.content},React.createElement(l.A,{serviceName:e.service_name,label:e.display_name,profilePicture:e.profile_picture}),React.createElement("div",{className:d.A["display-name"],title:e.display_name},e.display_name),React.createElement("div",{className:d.A.date},React.createElement(o.O,null),u)),React.createElement("div",{className:d.A.actions},React.createElement(n.Button,{label:__("Delete","jetpack-publicize-pkg"),icon:r.A,className:d.A["delete-button"],onClick:i?m:a}),i?React.createElement(n.__experimentalConfirmDialog,{isOpen:p,onConfirm:h,onCancel:m,confirmButtonText:__("Delete","jetpack-publicize-pkg")},__("Are you sure you want to delete this post?","jetpack-publicize-pkg")):null))}},38476:(e,t,a)=>{"use strict";a.d(t,{x:()=>i});var n=a(86087),c=a(76329),s=a(19235);function i({items:e,onDelete:t,confirmDeletion:a=!0}){const i=(0,n.useCallback)((e=>()=>{t(e)}),[t]);return React.createElement("ul",{className:s.A.list},e.map((({id:e,connection:t,scheduledAt:n})=>React.createElement("li",{key:`${e}`,className:s.A.item},React.createElement(c.h,{connection:t,scheduledAt:n,onDelete:i(e),confirmDeletion:a})))))}},53774:(e,t,a)=>{"use strict";a.d(t,{$:()=>p});var n=a(51112),c=a(47143),s=a(86087),i=a(27723),r=a(13022),l=a(31963),o=a(75018),d=a(42475),u=a(70745);const __=i.__,_x=i._x;function p({service:e,isSmall:t,onSubmit:a,displayInputs:i,hasConnections:p,buttonLabel:m}){const{setKeyringResult:h}=(0,c.useDispatch)(l.M_),{isConnectionsModalOpen:g}=(0,c.useSelect)((e=>e(l.M_)),[]),[v,w]=(0,s.useState)(!1),b=(0,c.useSelect)((e=>e(l.M_).isFetchingServicesList()),[]),f=(0,s.useCallback)((e=>{g()&&h(e)}),[h,g]),k=(0,u.P)({service:e,onConfirm:f}),_=(0,s.useCallback)((async e=>{if(e.preventDefault(),e.stopPropagation(),a)return a();w(!0);const t=new FormData(e.target);await k(t)}),[a,k]);return React.createElement("form",{className:(0,r.A)(d.A["connect-form"],{[d.A.small]:t}),onSubmit:_},i?React.createElement("div",{className:(0,r.A)(d.A["fields-wrapper"],d.A.input)},React.createElement(o.t,{service:e})):null,React.createElement("div",{className:d.A["fields-wrapper"]},React.createElement("div",{className:d.A["fields-item"]},React.createElement(n.A,{variant:p?"secondary":"primary",type:"submit",className:d.A["connect-button"],disabled:b},m||(b&&v?__("Connecting…","jetpack-publicize-pkg"):p?_x("Connect more","","jetpack-publicize-pkg"):__("Connect","jetpack-publicize-pkg"))))))}},75018:(e,t,a)=>{"use strict";a.d(t,{t:()=>u});var n=a(42266),c=a(56427),s=a(47143),i=a(86087),r=a(27723),l=a(13022),o=a(31963),d=a(42475);const __=r.__,_x=r._x;function u({service:e}){const t=(0,i.useId)(),[a,u]=(0,i.useState)(null),p=(0,s.useSelect)((e=>e(o.M_).getReconnectingAccount()),[]),m=(0,i.useCallback)((e=>{if(e.endsWith(".bsky.social")){if(e.replace(".bsky.social","").includes("."))return u((0,r.sprintf)(/* translators: %s is the handle suffix like .bsky.social */
__('Bluesky usernames cannot contain dots. If you are using a custom domain, enter it without "%s"',"jetpack-publicize-pkg"),".bsky.social")),!1}return u(null),!0}),[]),h=(0,i.useCallback)((e=>{m(e.target.value)}),[m]);return"mastodon"===e.id?React.createElement("div",{className:d.A["fields-item"]},React.createElement("label",{htmlFor:`${t}-handle`},_x("Handle","The handle of a social media account.","jetpack-publicize-pkg")),React.createElement("input",{id:`${t}-handle`,required:!0,type:"text",name:"instance",autoComplete:"off",autoCapitalize:"off",autoCorrect:"off",spellCheck:"false","aria-label":__("Mastodon handle","jetpack-publicize-pkg"),"aria-describedby":`${t}-handle-description`,placeholder:"@<EMAIL>"}),React.createElement("p",{className:"description",id:`${t}-handle-description`},__("You can find the handle in your Mastodon profile.","jetpack-publicize-pkg"))):"bluesky"===e.id?React.createElement(React.Fragment,null,React.createElement("div",{className:d.A["fields-item"]},React.createElement("label",{htmlFor:`${t}-handle`},_x("Handle","The handle of a social media account.","jetpack-publicize-pkg")),React.createElement("input",{id:`${t}-handle`,required:!0,type:"text",name:"handle",defaultValue:"bluesky"===p?.service_name?p?.external_handle:void 0,autoComplete:"off",autoCapitalize:"off",autoCorrect:"off",spellCheck:"false","aria-label":__("Bluesky handle","jetpack-publicize-pkg"),"aria-describedby":`${t}-handle-description`,placeholder:"username.bsky.social",onChange:h,className:a?d.A.error:void 0}),React.createElement("p",{className:(0,l.A)("description",a&&d.A["error-text"]),id:`${t}-handle-description`},a||React.createElement(React.Fragment,null,__("You can find the handle in your Bluesky profile.","jetpack-publicize-pkg")," ",(0,i.createInterpolateElement)((0,r.sprintf)(/* translators: %s is the bluesky handle suffix like .bsky.social */
__("This can either be %s or just the domain name if you are using a custom domain.","jetpack-publicize-pkg"),"<strong>username.bsky.social</strong>"),{strong:React.createElement("strong",null)})))),React.createElement("div",{className:d.A["fields-item"]},React.createElement("label",{htmlFor:`${t}-password`},__("App password","jetpack-publicize-pkg")),React.createElement("input",{id:`${t}-password`,required:!0,type:"password",name:"app_password",autoComplete:"off",autoCapitalize:"off",autoCorrect:"off",spellCheck:"false","aria-label":__("App password","jetpack-publicize-pkg"),"aria-describedby":`${t}-password-description`,placeholder:"xxxx-xxxx-xxxx-xxxx"}),React.createElement("p",{className:"description",id:`${t}-password-description`},(0,i.createInterpolateElement)(__("App password is needed to safely connect your account. App password is different from your account password. You can <link>generate it in Bluesky</link>.","jetpack-publicize-pkg"),{link:React.createElement(c.ExternalLink,{href:"https://bsky.app/settings/app-passwords"})})),"bluesky"===p?.service_name&&React.createElement(n.A,{level:"error",showIcon:!1},__("Please provide an app password to fix the connection.","jetpack-publicize-pkg")))):null}},77088:(e,t,a)=>{"use strict";a.d(t,{e:()=>m});var n=a(40597),c=a(47425),s=a(47143),i=a(27723),r=a(31963),l=a(27817),o=a(86158),d=a(43119),u=a(52683),p=a(42475);const __=i.__,m=({connection:e,service:t,canMarkAsShared:a})=>{const i=(0,s.useSelect)((t=>t(r.M_).canUserManageConnection(e)),[e]);return React.createElement("div",{className:p.A["service-connection"]},React.createElement("div",null,e.profile_picture?React.createElement("img",{className:p.A["profile-pic"],src:e.profile_picture,alt:e.display_name}):React.createElement(t.icon,{iconSize:40})),React.createElement("div",{className:p.A["connection-details"]},React.createElement(l.s,{connection:e}),"broken"!==(m=e).status&&"must_reauth"!==m.status||!i?a?React.createElement("div",{className:p.A["mark-shared-wrap"]},React.createElement(u.j,{connection:m}),React.createElement(n.A,{placement:"top",inline:!1,shift:!0},__("If enabled, the connection will be available to all administrators, editors, and authors.","jetpack-publicize-pkg"))):i?null:React.createElement(React.Fragment,null,React.createElement(c.Ay,{className:p.A.description},__("This connection is added by a site administrator.","jetpack-publicize-pkg")),"broken"===m.status?React.createElement(o.v,{connection:m,service:t}):null):React.createElement(o.v,{connection:m,service:t})),React.createElement("div",{className:p.A["connection-actions"]},React.createElement(d.V,{connection:e,isDestructive:!1,variant:"tertiary",buttonClassName:p.A.disconnect})));var m}},46985:(e,t,a)=>{"use strict";a.d(t,{x:()=>u});var n=a(60442),c=a(56427),s=a(47143),i=a(13022),r=a(21259),l=a(31963),o=a(77088),d=a(42475);function u({service:e,serviceConnections:t}){const[a]=(0,n.A)("sm"),{deletingConnections:u,updatingConnections:p}=(0,s.useSelect)((e=>{const{getDeletingConnections:t,getUpdatingConnections:a}=e(l.M_);return{deletingConnections:t(),updatingConnections:a()}}),[]),m=(0,r._)();return t.length?React.createElement("ul",{className:d.A["service-connection-list"]},t.map((t=>{const a=p.includes(t.connection_id)||u.includes(t.connection_id);return React.createElement("li",{key:t.connection_id},React.createElement(c.Disabled,{isDisabled:a},React.createElement(o.e,{connection:t,service:e,canMarkAsShared:m})))}))):React.createElement("div",{className:(0,i.A)(d.A["example-wrapper"],{[d.A.small]:a})},e.examples.map(((t,a)=>React.createElement("div",{key:e.id+a,className:d.A.example},React.createElement(t,null)))))}},59008:(e,t,a)=>{"use strict";a.d(t,{j:()=>w});var n=a(60442),c=a(51112),s=a(56427),i=a(47143),r=a(86087),l=a(27723),o=a(51113),d=a(98248),u=a(64969),p=a(31963),m=a(53774),h=a(46985),g=a(21159),v=a(42475);const __=l.__,_x=l._x;function w({service:e,serviceConnections:t,isPanelDefaultOpen:a}){const[l]=(0,n.A)("sm"),[w,b]=(0,r.useReducer)((e=>!e),a),f=(0,r.useRef)(null);(0,r.useEffect)((()=>{a&&f.current?.scrollIntoView({block:"center",behavior:"smooth"})}),[]);const k=w&&e.needsCustomInputs,_=t.filter((({status:e})=>"broken"===e)),x=t.filter((({status:e})=>"must_reauth"===e)),E=(0,i.useSelect)((e=>{const{canUserManageConnection:t}=e(p.M_);return _.some(t)}),[_]),j=k||E&&w,R=_.length>1?_x("Fix connections","Fix the social media connections","jetpack-publicize-pkg"):_x("Fix connection","Fix social media connection","jetpack-publicize-pkg");return React.createElement("div",{className:v.A["service-item"]},React.createElement("div",{className:v.A["service-item-info"]},React.createElement("div",null,React.createElement(e.icon,{iconSize:l?36:48})),React.createElement("div",{className:v.A["service-basics"]},React.createElement("div",{className:v.A.heading},React.createElement("span",{className:v.A.title},e.label),e.badges?.length?React.createElement("div",{className:v.A.badges},e.badges.map((({text:e,style:t},a)=>React.createElement("span",{key:a,className:v.A.badge,style:t},e)))):null),l||t.length?null:React.createElement("span",{className:v.A.description},e.description),React.createElement(g.E,{serviceConnections:t,brokenConnections:_,reauthConnections:x})),React.createElement("div",{className:v.A.actions},j?null:React.createElement(m.$,{service:e,isSmall:l,onSubmit:E||e.needsCustomInputs?b:void 0,hasConnections:t.length>0,buttonLabel:E?R:void 0}),React.createElement(c.A,{size:"small",className:v.A["learn-more"],variant:"tertiary",onClick:b,"aria-label":__("Learn more","jetpack-publicize-pkg")},React.createElement(o.A,{className:v.A.chevron,icon:w?d.A:u.A})))),React.createElement(s.Panel,{className:v.A["service-panel"],ref:f},React.createElement(s.PanelBody,{opened:w,onToggle:b},React.createElement(h.x,{service:e,serviceConnections:t}),e.needsCustomInputs&&!E?React.createElement("div",{className:v.A["connect-form-wrapper"]},React.createElement(m.$,{service:e,displayInputs:!0,isSmall:!1,buttonLabel:__("Connect","jetpack-publicize-pkg")})):null)))}},21159:(e,t,a)=>{"use strict";a.d(t,{E:()=>l});var n=a(42266),c=a(47143),s=a(27723),i=a(31963),r=a(42475);const __=s.__,_n=s._n;function l({serviceConnections:e,brokenConnections:t,reauthConnections:a}){const l=(0,c.useSelect)((e=>t.some(e(i.M_).canUserManageConnection)||a.some(e(i.M_).canUserManageConnection)),[t,a]);if(!e.length)return null;if(t.length||a.length){let e;return e=t.length?l?__("Please fix the broken connections or disconnect them to create more connections.","jetpack-publicize-pkg"):_n("Broken connection","Broken connections",t.length,"jetpack-publicize-pkg"):l?__("Reconnect to continue sharing.","jetpack-publicize-pkg"):_n("Expiring connection","Expiring connections",a.length,"jetpack-publicize-pkg"),React.createElement(n.A,{level:l?"error":"warning",showIcon:!1,className:r.A["broken-connection-alert"]},e)}return React.createElement("span",{className:r.A["active-connection"]},e.length>1?(0,s.sprintf)(
// translators: %d: Number of connections
__("%d connections","jetpack-publicize-pkg"),e.length):__("Connected","jetpack-publicize-pkg"))}},78362:(e,t,a)=>{"use strict";a.d(t,{b:()=>o});var n=a(47143),c=a(86087),s=a(31963),i=a(59008),r=a(42475),l=a(39630);function o(){const e=(0,l.o)(),t=(0,n.useSelect)((e=>e(s.M_).getConnections()),[]),a=(0,c.useMemo)((()=>t.reduce(((e,t)=>(e[t.service_name]||(e[t.service_name]=[]),e[t.service_name].push(t),e)),{})),[t]),o=(0,n.useSelect)((e=>e(s.M_).getReconnectingAccount()),[]);return React.createElement("ul",{className:r.A.services},e.map((e=>React.createElement("li",{key:e.id,className:r.A["service-list-item"]},React.createElement(i.j,{service:e,serviceConnections:a[e.id]||[],isPanelDefaultOpen:o?.service_name===e.id})))))}},70745:(e,t,a)=>{"use strict";a.d(t,{P:()=>d});var n=a(63406),c=a(47143),s=a(86087),i=a(27723),r=a(31963),l=a(70407);const __=i.__,o=e=>/^@?\b([A-Z0-9_]+)@([A-Z0-9.-]+\.[A-Z]{2,})$/gi.test(e);function d({service:e,onConfirm:t}){const{createErrorNotice:a}=(0,n.I)(),i=(0,c.useSelect)((e=>e(r.M_).isMastodonAccountAlreadyConnected),[]),d=(0,c.useSelect)((e=>e(r.M_).isBlueskyAccountAlreadyConnected),[]),{refreshServicesList:u}=(0,c.useDispatch)(r.M_),{getService:p}=(0,c.useSelect)((e=>e(r.M_)),[]);return(0,s.useCallback)((async n=>{let c=e.url;if(!c){await u();do{await new Promise((e=>setTimeout(e,100))),c=p(e.id)?.url}while(!c)}const s=new URL(c);switch(e.id){case"mastodon":{const e=n.get("instance").toString().trim();if(!o(e))return void a(__("Invalid Mastodon username","jetpack-publicize-pkg"));if(i?.(e))return void a(__("This Mastodon account is already connected","jetpack-publicize-pkg"));s.searchParams.set("instance",e);break}case"bluesky":{const e=(n.get("handle")?.toString()||"").trim().replace(/^@/,"");if(!function(e){const t=e.split(".").filter(Boolean);return!(t.length<2)&&t.every((e=>/^[a-z0-9_-]+$/i.test(e)))}(e))return void a(__("Invalid Bluesky handle","jetpack-publicize-pkg"));if(d?.(e))return void a(__("This Bluesky account is already connected","jetpack-publicize-pkg"));s.searchParams.set("handle",e),s.searchParams.set("app_password",(n.get("app_password")?.toString()||"").trim());break}}(0,l.x9)(s.toString(),t)}),[a,p,d,i,t,u,e])}},9515:(e,t,a)=>{"use strict";a.d(t,{h:()=>s});var n=a(51609),c=a(48712);function s(){const e=(0,c.h)();return(0,n.useCallback)((t=>e(t)?.label||t[0].toUpperCase()+t.substring(1)),[e])}},48712:(e,t,a)=>{"use strict";a.d(t,{h:()=>s});var n=a(51609),c=a(39630);function s(){const e=(0,c.o)(),t=(0,n.useMemo)((()=>e.reduce(((e,t)=>(e[t.id]=t,e)),{})),[e]);return(0,n.useCallback)((e=>t[e]),[t])}},39630:(e,t,a)=>{"use strict";a.d(t,{o:()=>w});var n=a(96072),c=a.n(n),s=a(78478),i=a(56427),r=a(47143),l=a(86087),o=a(27723),d=a(30081),u=a(26818),p=a(43443),m=a(11326),h=a(65398),g=a(9791),v=a(31963);const __=o.__;function w(){const e=(0,r.useSelect)((e=>e(v.M_).getServicesList()),[]),t=(0,l.useMemo)((()=>e.reduce(((e,t)=>({...e,[t.id]:t})),{})),[e]),a={text:__("New","jetpack-publicize-pkg"),style:{background:"#e9eff5",color:"#0675C4"}};return[{...t.facebook,icon:e=>React.createElement(s.M5,c()({serviceName:"facebook"},e)),description:__("Share to your pages","jetpack-publicize-pkg"),examples:[()=>React.createElement(React.Fragment,null,(0,l.createInterpolateElement)(__("<strong>Connect</strong> to automatically share posts on your Facebook page.","jetpack-publicize-pkg"),{strong:React.createElement("strong",null)})),()=>React.createElement("img",{src:d,alt:__("Add Facebook connection","jetpack-publicize-pkg")})]},{...t["instagram-business"],icon:e=>React.createElement(s.M5,c()({serviceName:"instagram"},e)),description:__("Share to your Instagram Business account.","jetpack-publicize-pkg"),examples:[()=>React.createElement(React.Fragment,null,__("Drive engagement and save time by automatically sharing images to Instagram when you publish blog posts.","jetpack-publicize-pkg"),React.createElement("div",{className:"instagram-business__requirements"},React.createElement("h4",null,__("Requirements for connecting Instagram:","jetpack-publicize-pkg")),React.createElement("ol",null,React.createElement("li",null,__("You must have an Instagram Business account.","jetpack-publicize-pkg")),React.createElement("li",null,__("Your Instagram Business account must be linked to a Facebook page.","jetpack-publicize-pkg")))),(0,l.createInterpolateElement)(__("<i>When you click “connect” you'll be asked to <strong>log into Facebook</strong>. If your Instagram Business account isn't listed, ensure it's linked to a Facebook page.</i>","jetpack-publicize-pkg"),{strong:React.createElement("strong",null),i:React.createElement("em",null)}),React.createElement("br",null),React.createElement("br",null),React.createElement(i.ExternalLink,{className:"instagram-business__help-link",href:"https://jetpack.com/redirect/?source=jetpack-social-instagram-business-help"},__("Learn how to convert & link your Instagram account.","jetpack-publicize-pkg"))),()=>React.createElement("img",{src:u,alt:__("Add Instagram photo","jetpack-publicize-pkg")})]},{...t.threads,icon:e=>React.createElement(s.M5,c()({serviceName:"threads"},e)),description:__("Share posts to your Threads feed.","jetpack-publicize-pkg"),examples:[()=>React.createElement(React.Fragment,null,__("Increase your presence in social media by sharing your posts automatically to Threads.","jetpack-publicize-pkg")),()=>React.createElement("img",{src:h,alt:__("Add Threads connection","jetpack-publicize-pkg")})]},{...t.bluesky,needsCustomInputs:!0,icon:e=>React.createElement(s.M5,c()({serviceName:"bluesky"},e)),badges:[a],description:__("Share with your network.","jetpack-publicize-pkg"),examples:[()=>React.createElement(React.Fragment,null,__("To share to Bluesky please enter your Bluesky handle and app password below, then click connect.","jetpack-publicize-pkg"))]},{...t.linkedin,icon:e=>React.createElement(s.M5,c()({serviceName:"linkedin"},e)),description:__("Share with your LinkedIn community.","jetpack-publicize-pkg"),examples:[()=>React.createElement(React.Fragment,null,(0,l.createInterpolateElement)(__("<strong>Connect</strong> to automatically share posts with your LinkedIn connections.","jetpack-publicize-pkg"),{strong:React.createElement("strong",null)})),()=>React.createElement("img",{src:p,alt:__("Add LinkedIn connection","jetpack-publicize-pkg")})]},{...t.nextdoor,icon:e=>React.createElement(s.M5,c()({serviceName:"nextdoor"},e)),description:__("Share on communities","jetpack-publicize-pkg"),examples:[()=>React.createElement(React.Fragment,null,(0,l.createInterpolateElement)(__("<strong>Connect</strong> with friends, neighbors, and local businesses by automatically sharing your posts to Nextdoor.","jetpack-publicize-pkg"),{strong:React.createElement("strong",null)})),()=>React.createElement("img",{src:m,alt:__("Add Instagram photo","jetpack-publicize-pkg")})]},{...t.tumblr,icon:e=>React.createElement(s.M5,c()({serviceName:"tumblr-alt"},e)),description:__("Share to your Tumblr blog.","jetpack-publicize-pkg"),examples:[()=>React.createElement(React.Fragment,null,(0,l.createInterpolateElement)(__("<strong>Connect</strong> to automatically share posts to your Tumblr blog.","jetpack-publicize-pkg"),{strong:React.createElement("strong",null)})),()=>React.createElement("img",{src:g,alt:__("Add Tumblr connection","jetpack-publicize-pkg")})]},{...t.mastodon,needsCustomInputs:!0,icon:e=>React.createElement(s.M5,c()({serviceName:"mastodon"},e)),description:__("Share with your network.","jetpack-publicize-pkg"),examples:[()=>React.createElement(React.Fragment,null,__("To share to Mastodon please enter your Mastodon username below, then click connect.","jetpack-publicize-pkg"))]}].filter((e=>Boolean(e.id)))}},73905:(e,t,a)=>{"use strict";a.d(t,{t:()=>c});var n=a(27723);const __=n.__,c=[{label:__("X","jetpack-publicize-pkg"),networkName:"x",url:"https://x.com/intent/tweet?text={{text}}&url={{url}}"},{label:__("WhatsApp","jetpack-publicize-pkg"),networkName:"whatsapp",url:"https://api.whatsapp.com/send?text={{text}}"},{label:__("Facebook","jetpack-publicize-pkg"),networkName:"facebook",url:"https://www.facebook.com/sharer/sharer.php?u={{url}}"}]},68541:(e,t,a)=>{"use strict";a.d(t,{R:()=>h});var n=a(78478),c=a(51112),s=a(47425),i=a(69222),r=a(85985),l=a(86087),o=a(27723),d=a(13022),u=a(73905),p=a(99811),m=a(53975);const __=o.__;function h({buttonStyle:e="icon",buttonVariant:t}){const a=(0,m.i)(),{recordEvent:h}=(0,r.st)(),g=(0,l.useCallback)((()=>{h("jetpack_social_share_button_clicked",{network:"clipboard"})}),[h]),v=(0,l.useCallback)((()=>a("{{text}}\n{{url}}",!1)),[a]),w=(0,l.useCallback)((function(e,t){return function(a){a.preventDefault(),h("jetpack_social_share_button_clicked",t),window.open(e,"","menubar=no,toolbar=no,resizable=yes,scrollbars=yes,height=600,width=600")}}),[h]);return React.createElement("div",{className:(0,d.A)(p.A["share-buttons"],{[p.A.vertical]:e.includes("text")})},u.t.map((({label:i,networkName:r,url:l})=>{const d=a(l),u="icon"===e?React.createElement(n.M5,{serviceName:r}):null,m=(0,o.sprintf)(/* translators: %s is the name of a social network, e.g. Twitter. */
__("Share on %s","jetpack-publicize-pkg"),i);return React.createElement("div",{className:p.A.container,key:r},React.createElement(c.A,{icon:u,variant:t,"aria-label":m,href:d,target:"_blank",rel:"noopener noreferrer",onClick:w(d,{network:r}),className:"icon"===e?p.A[r]:"has-text"},"icon"===e?null:React.createElement(React.Fragment,null,"icon-text"===e&&React.createElement(n.M5,{className:p.A[r],serviceName:r}),React.createElement(s.Ay,{className:p.A.label,component:"span"},m))))})),React.createElement("div",{className:p.A.container},React.createElement(i.A,{buttonStyle:e,onCopy:g,textToCopy:v,className:"icon"===e?p.A.clipboard:" has-text",variant:t},"icon"===e?null:React.createElement(s.Ay,{className:p.A.label,component:"span"},__("Copy to clipboard","jetpack-publicize-pkg")))))}},53975:(e,t,a)=>{"use strict";a.d(t,{i:()=>r});var n=a(47143),c=a(43656),s=a(86087),i=a(383);function r(){const{shareMessage:e}=(0,i.h)(),{message:t,link:a}=(0,n.useSelect)((t=>{const{getEditedPostAttribute:a}=t(c.store);return{link:a("link"),message:e||a("meta")?.jetpack_seo_html_title||a("title")}}),[e]);return(0,s.useCallback)(((e,n=!0)=>{let c=t,s=a;return e.includes("{{url}}")||(c=c+"\n\n"+s,s=""),n&&(c=encodeURIComponent(c),s=encodeURIComponent(s)),e.replace("{{text}}",c).replace("{{url}}",s)}),[a,t])}},1101:(e,t,a)=>{"use strict";a.d(t,{_:()=>v});var n=a(97999),c=a(85985),s=a(56427),i=a(47143),r=a(43656),l=a(86087),o=a(27723),d=a(692),u=a(44511),p=a(31873),m=a(31963),h=a(27844);const __=o.__;function g(e=__("Unable to share the Post","jetpack-publicize-pkg")){const{createErrorNotice:t}=(0,i.dispatch)(d.store);t(e,{id:"publicize-post-share-message"})}function v({onShareCompleted:e,isDisabled:t=!1}){const a=(0,n.siteHasFeature)(h.q.IMAGE_GENERATOR)||(0,n.siteHasFeature)(h.q.ENHANCED_PUBLISHING),{isFetching:o,isError:v,isSuccess:w,doPublicize:b}=(0,p.A)(),{isAutosaveablePost:f,isDirtyPost:k,isPostPublished:_,isSavingPost:x}=(0,i.useSelect)((e=>{const t=e(r.store);return{isAutosaveablePost:t.isEditedPostAutosaveable(),isDirtyPost:t.isEditedPostDirty(),isPostPublished:t.isCurrentPostPublished(),isSavingPost:t.isSavingPost()}}),[]),{pollForPostShareStatus:E}=(0,i.useDispatch)(m.M_),{recordEvent:j}=(0,c.st)(),R=(0,i.dispatch)(r.store).savePost;(0,l.useEffect)((()=>{if(!o)return v?g():void(w&&(!function(){const{createSuccessNotice:e}=(0,i.dispatch)(d.store);e(__("Request submitted successfully.","jetpack-publicize-pkg"),{id:"publicize-post-share-message",type:"snackbar"})}(),e()))}),[o,v,w,e]);const y=(0,u.C)(),A=(0,l.useCallback)((async()=>{if(!_)return g(__("You must publish your post before you can share it.","jetpack-publicize-pkg"));(0,i.dispatch)(d.store).removeNotice("publicize-post-share-message"),j("jetpack_social_reshare_clicked",{location:"editor",environment:(0,c.d9)()?"atomic":(0,c.Sy)()?"simple":"jetpack"}),k&&f&&a&&await R(),await b(),(0,n.siteHasFeature)(h.q.SHARE_STATUS)&&E()}),[_,j,k,f,a,b,R,E]);return React.createElement(s.Button,{variant:"primary",onClick:A,disabled:!y||t,isBusy:o||x},__("Share","jetpack-publicize-pkg"))}},7757:(e,t,a)=>{"use strict";a.d(t,{JU:()=>c.g,Q3:()=>n.Q});var n=a(59032),c=a(95499)},95499:(e,t,a)=>{"use strict";a.d(t,{g:()=>g});var n=a(96072),c=a.n(n),s=a(97999),i=a(85985),r=a(56427),l=a(47143),o=a(27723),d=a(13022),u=a(51609),p=a(31963),m=a(27844),h=a(14150);const __=o.__,g=(0,u.forwardRef)((({withWrapper:e=!1,analyticsData:t=null,...a},n)=>{const{recordEvent:o}=(0,i.st)(),{openShareStatusModal:g}=(0,l.useDispatch)(p.M_),v=(0,l.useSelect)((e=>e(p.M_).getPostShareStatus()),[]),w=(0,u.useCallback)((()=>{o("jetpack_social_share_status_modal_opened",t),g()}),[t,g,o]);if(!v||!v.shares||0===v.shares.length)return null;if(!(0,s.siteHasFeature)(m.q.SHARE_STATUS))return null;const b=React.createElement(r.Button,c()({variant:"secondary",onClick:w},a,{className:(0,d.A)(h.A.trigger,a.className),ref:n}),a.children||__("View sharing history","jetpack-publicize-pkg"));return e?React.createElement("div",{className:h.A["trigger-wrapper"]},b):b}))},59032:(e,t,a)=>{"use strict";a.d(t,{Q:()=>u});var n=a(50723),c=a(56427),s=a(47143),i=a(27723),r=a(31963),l=a(30557),o=a(14150);const __=i.__;function d(){const{closeShareStatusModal:e}=(0,s.useDispatch)(r.M_);return React.createElement("div",{className:o.A.wrapper},React.createElement(c.Modal,{onRequestClose:e,title:__("Sharing status","jetpack-publicize-pkg"),className:o.A.modal},React.createElement(l.W,null)))}function u(){const e=(0,s.useSelect)((e=>e(r.M_).isShareStatusModalOpen()),[]);return React.createElement(n.Ay,{targetDom:document.body},e?React.createElement(d,null):null)}},14329:(e,t,a)=>{"use strict";a.d(t,{f:()=>g});var n=a(40597),c=a(85985),s=a(56427),i=a(47143),r=a(43656),l=a(27723),o=a(51609),d=a(77627),u=a(31873),p=a(31963),m=a(59061),h=a(14150);const __=l.__,_x=l._x;function g({shareItem:e}){const{recordEvent:t}=(0,c.st)(),a=(0,i.useSelect)((e=>e(r.store).getCurrentPostId()),[]),l=(0,i.useSelect)((e=>e(p.M_).getConnections()),[]),{isRePublicizeFeatureAvailable:g}=(0,d.A)(),v=l.some((0,m.eJ)(e)),{doPublicize:w}=(0,u.A)(a),{pollForPostShareStatus:b}=(0,i.useDispatch)(p.M_),[f,k]=(0,o.useState)(!1),_=(0,o.useCallback)((async()=>{t("jetpack_social_share_status_retry",{service:e.service,location:"modal"});const a=(0,m.eJ)(e),n=l.filter((e=>!a(e))).map((({connection_id:e})=>e));n.length!==l.length&&(k(!0),await w(n),await b({isRequestComplete({postShareStatus:t,lastTimestamp:a}){const n=t.shares.some((t=>t.timestamp>a&&(0,m.Ri)(e,t)));return n&&k(!1),n}}))}),[t,e,l,w,b]);return f?React.createElement(s.Spinner,null):React.createElement("div",{className:h.A["retry-wrapper"]},(x=g,v&&x?React.createElement(s.Button,{variant:"link",onClick:_},__("Retry","jetpack-publicize-pkg")):React.createElement(React.Fragment,null,React.createElement(s.Button,{variant:"tertiary",disabled:!0},__("Retry","jetpack-publicize-pkg")),React.createElement(n.A,{shift:!0,placement:"bottom-end"},x?e.external_id?_x("This connection has been removed.","Social media connection","jetpack-publicize-pkg"):_x("This connection has been reconnected or removed.","Social media connection","jetpack-publicize-pkg"):__("To re-share a post, you need to upgrade to a paid plan.","jetpack-publicize-pkg")))));var x}},30557:(e,t,a)=>{"use strict";a.d(t,{W:()=>i});var n=a(47143),c=a(31963),s=a(48377);function i(){const e=(0,n.useSelect)((e=>e(c.M_).getPostShareStatus()),[]);return React.createElement("div",null,React.createElement(s.S,{postShareStatus:e}))}},368:(e,t,a)=>{"use strict";a.d(t,{C:()=>l});var n=a(85985),c=a(56427),s=a(27723),i=a(51609),r=a(14329);const __=s.__;function l({shareItem:e}){const{recordEvent:t}=(0,n.st)(),a=(0,i.useCallback)((()=>{t("jetpack_social_share_status_view",{service:e.service,location:"modal"})}),[t,e.service]);return"success"===e.status?React.createElement(c.ExternalLink,{href:e.message,onClick:a},__("View","jetpack-publicize-pkg")):React.createElement(r.f,{shareItem:e})}},69662:(e,t,a)=>{"use strict";a.d(t,{a:()=>p});var n=a(40597),c=a(47425),s=a(27723),i=a(51113),r=a(83883),l=a(13022),o=a(51609),d=a.n(o),u=a(14150);const __=s.__,_x=s._x;function p({status:e,message:t}){const a="success"===e,s=a?d().createElement(i.A,{className:u.A["share-status-icon"],icon:r.A}):d().createElement(n.A,{shift:!0,inline:!1,title:__("Sharing failed with the following message:","jetpack-publicize-pkg"),className:u.A["share-status-icon-tooltip"]},d().createElement(c.Ay,{variant:"body-small",className:u.A["tooltip-text"]},t));return d().createElement("div",{className:(0,l.A)(u.A["share-status-wrapper"],{[u.A["share-status-success"]]:a,[u.A["share-status-failure"]]:!a})},d().createElement("div",{className:u.A["share-status-icon"]},s),d().createElement("div",{className:u.A["share-status-label"]},a?_x("Shared","The sharing is successful","jetpack-publicize-pkg"):__("Failed","jetpack-publicize-pkg")))}},48377:(e,t,a)=>{"use strict";a.d(t,{S:()=>d});var n=a(38443),c=a(27723),s=a(12141),i=a(368),r=a(69662),l=a(14150);const __=c.__,o=e=>`${e.external_id||e.connection_id}:${e.timestamp}`;function d({postShareStatus:e}){return React.createElement("div",{className:l.A["dataview-wrapper"]},React.createElement("div",{className:"dataviews-wrapper"},React.createElement("table",{className:"dataviews-view-table"},React.createElement("thead",null,React.createElement("tr",{className:"dataviews-view-table__row"},React.createElement("th",null,__("Connection","jetpack-publicize-pkg")),React.createElement("th",null,__("Time","jetpack-publicize-pkg")),React.createElement("th",null,__("Status","jetpack-publicize-pkg")),React.createElement("th",null,__("Actions","jetpack-publicize-pkg")))),React.createElement("tbody",null,e.shares.map((e=>React.createElement("tr",{key:o(e),className:"dataviews-view-table__row"},React.createElement("td",null,React.createElement("div",{className:"dataviews-view-table__cell-content-wrapper"},React.createElement("div",{className:l.A["connection-name"]},React.createElement(s.A,{serviceName:e.service,label:e.external_name,profilePicture:e.profile_picture}),React.createElement("div",{className:l.A["share-item-name-wrapper"]},React.createElement("div",{className:l.A["share-item-name"]},e.external_name))))),React.createElement("td",null,React.createElement("div",{className:"dataviews-view-table__cell-content-wrapper"},(0,n.humanTimeDiff)(1e3*e.timestamp,(0,n.getDate)(null)))),React.createElement("td",null,React.createElement("div",{className:"dataviews-view-table__cell-content-wrapper"},React.createElement(r.a,{status:e.status,message:e.message}))),React.createElement("td",null,React.createElement("div",{className:"dataviews-view-table__cell-content-wrapper"},React.createElement(i.C,{shareItem:e}))))))))))}},29341:(e,t,a)=>{"use strict";a.d(t,{A:()=>u});var n=a(63406),c=a(56427),s=a(86087),i=a(27723),r=a(49747),l=a(38394),o=a(56457),d=a(77932);const __=i.__,_x=i._x,u=({prePublish:e=!1})=>{const t=e?s.Fragment:c.PanelBody,a=e?{}:{title:__("Social Image Generator","jetpack-publicize-pkg")},{isEnabled:i,setIsEnabled:u}=(0,r.A)(),[p,m]=(0,s.useState)(!1),h=(0,s.useCallback)((()=>m(!0)),[]),g=(0,s.useCallback)((()=>m(!1)),[]),[v,w]=(0,s.useState)(null),{createErrorNotice:b,createSuccessNotice:f}=(0,n.I)(),{save:k,isSaving:_}=(0,l.i)({onError:e=>{b(e.message)},onSuccess:()=>{f(__("Image saved to media library.","jetpack-publicize-pkg"))}}),x=(0,s.useCallback)((()=>{k(`https://s0.wp.com/_si/?t=${v}`,"generated-image.jpg")}),[v,k]);return React.createElement(t,a,p&&React.createElement(d.A,{onClose:g}),React.createElement(c.ToggleControl,{label:__("Enable Social Image","jetpack-publicize-pkg"),help:i?"":__("Social Image is disabled for this post.","jetpack-publicize-pkg"),checked:i,onChange:u,__nextHasNoMarginBottom:!0}),i&&React.createElement(React.Fragment,null,React.createElement("hr",null),React.createElement(o.Ay,{onNewToken:w}),React.createElement("hr",null),React.createElement(c.__experimentalHStack,{spacing:2,wrap:!0},React.createElement(c.Button,{variant:"secondary",onClick:h,label:__("Open the Social Image Generator settings","jetpack-publicize-pkg")},__("Settings","jetpack-publicize-pkg")),React.createElement(c.Button,{variant:"secondary",onClick:x,label:__("Save the generated image to your media library.","jetpack-publicize-pkg"),disabled:!v||_},_?_x("Saving…","Saving the file to media library","jetpack-publicize-pkg"):__("Save to media library","jetpack-publicize-pkg")))))}},77932:(e,t,a)=>{"use strict";a.d(t,{A:()=>g});var n=a(50723),c=a(56427),s=a(86087),i=a(27723),r=a(49747),l=a(80624),o=a(56457),d=a(55858),u=a(81299),p=a(53744);const __=i.__,m=["image/jpeg","image/png"],h=__("Choose Image","jetpack-publicize-pkg"),g=({onClose:e})=>{const{customText:t,imageType:a,imageId:i,template:g,updateSettings:v}=(0,r.A)(),[w,b]=(0,s.useState)(i),[f,k]=(0,s.useState)(a||"featured"),[_,x]=(0,s.useState)(t),[E,j]=(0,s.useState)(g),[R]=(0,l.A)(w),y=(0,s.useCallback)((()=>{v({template:E,image_type:f,custom_text:_||"",..."custom"===f&&{image_id:w}}),e()}),[v,E,f,w,_,e]),A=(0,s.useCallback)((e=>{b(e?.id)}),[b]);return React.createElement(n.Ay,{targetDom:document.body},React.createElement(c.Modal,{onRequestClose:e},React.createElement(o.Ay,{className:p.A.preview,imageId:w,customText:_,imageType:f,template:E}),React.createElement(c.SelectControl,{label:__("Image Type","jetpack-publicize-pkg"),value:f||"featured",options:[{label:__("Featured Image","jetpack-publicize-pkg"),value:"featured"},{label:__("Custom Image","jetpack-publicize-pkg"),value:"custom"},{label:__("No Image","jetpack-publicize-pkg"),value:"none"}],onChange:k,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),"custom"===f&&React.createElement(d.A,{buttonLabel:h,subTitle:__("Add a custom image","jetpack-publicize-pkg"),mediaId:w,mediaDetails:R,onChange:A,allowedMediaTypes:m,wrapperClassName:p.A.mediaPicker}),React.createElement(c.TextControl,{className:p.A.customText,value:_||"",onChange:x,label:__("Custom Header","jetpack-publicize-pkg"),help:__("By default the post title is used for the image. You can use this field to set your own text.","jetpack-publicize-pkg"),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),React.createElement(c.BaseControl,{__nextHasNoMarginBottom:!0,className:p.A.templateControl},React.createElement(c.BaseControl.VisualLabel,null,__("Templates","jetpack-publicize-pkg")),React.createElement(u.A,{value:E,onTemplateSelected:j})),React.createElement(c.Button,{onClick:e,variant:"tertiary"},__("Cancel","jetpack-publicize-pkg")),React.createElement(c.Button,{onClick:y,variant:"primary"},__("Save","jetpack-publicize-pkg"))))}},81299:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(86087),c=a(27723),s=a(13022),i=a(52002),r=a(10936);const __=c.__,l=({value:e=null,onTemplateSelected:t=null})=>{const a=(0,n.useCallback)((e=>{const a=e.target.id;t?.(a)}),[t]);return React.createElement("div",{className:i.A.templates},r.A.map((t=>React.createElement("button",{onClick:a,id:t.name,key:t.name,className:(0,s.A)(i.A.template,{[i.A["template--active"]]:t.name===e})},React.createElement("img",{src:t.image,alt:t.label}),React.createElement("span",{className:"screen-reader-text"},(0,c.sprintf)(/* translators: %s is the name of the template */
__("Pick the %s template","jetpack-publicize-pkg"),t.label))))))}},10936:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n=a(26365),c=a(45329),s=a(9509);const i=[{name:"highway",label:"Highway",image:a(22417)},{name:"dois",label:"Dois",image:n},{name:"edge",label:"Edge",image:c},{name:"fullscreen",label:"Fullscreen",image:s}]},96390:(e,t,a)=>{"use strict";a.d(t,{n:()=>g});var n=a(85985),c=a(56427),s=a(47143),i=a(43656),r=a(86087),l=a(27723),o=a(10991),d=a(31963),u=a(18603),p=a(94360),m=a(47944);const __=l.__,_x=l._x;function h({title:e,onClose:t}){return React.createElement(c.Modal,{onRequestClose:t,title:e,className:m.A.modal,__experimentalHideHeader:!0},React.createElement("div",{className:m.A["modal-content"]},React.createElement(p.X,{onReShared:t}),React.createElement(u.w,null)),React.createElement(c.Button,{className:m.A["close-button"],onClick:t,icon:o.A,label:__("Close","jetpack-publicize-pkg")}))}function g(){const e=(0,s.useSelect)((e=>e(d.M_).isSharePostModalOpen()),[]),{openSharePostModal:t,closeSharePostModal:a}=(0,s.useDispatch)(d.M_),{recordEvent:l}=(0,n.st)(),o=(0,s.useSelect)((e=>e(i.store).isCurrentPostPublished()),[]),u=(0,r.useCallback)((()=>{e||l("jetpack_social_preview_modal_opened"),t()}),[e,t,l]),p=(0,r.useCallback)((()=>{a(),"share_post"===(0,n.UB)()&&(0,n.Uq)()}),[a]);return React.createElement(c.PanelRow,{className:m.A.panel},e?React.createElement(h,{onClose:p,title:o?_x("Share Post","The title of the social modal","jetpack-publicize-pkg"):__("Social Previews","jetpack-publicize-pkg")}):null,React.createElement(c.Button,{variant:"secondary",onClick:u,className:m.A["open-button"]},o?_x("Preview & Share","The button label for the modal trigger","jetpack-publicize-pkg"):__("Preview social posts","jetpack-publicize-pkg")))}},98588:(e,t,a)=>{"use strict";a.d(t,{z:()=>x});var n=a(96072),c=a.n(n),s=a(29103),i=a(73188),r=a(73066),l=a(57984),o=a(48914),d=a(83945),u=a(73915),p=a(89075),m=a(95550),h=a(3582),g=a(47143),v=a(86087),w=a(18537),b=a(27723),f=a(39553),k=a(62697),_=a(31137);const __=b.__;function x({connection:e}){const t=(0,v.useMemo)((()=>({displayName:e.display_name,profileImage:e.profile_picture,externalName:e.external_handle})),[e]),{image:a,media:n,title:b,description:x,url:E,excerpt:j}=(0,_.s)(),R=((0,f.A)().message||"").trim(),y=(0,v.useMemo)((()=>({description:x,image:a,media:n,title:b,url:E})),[x,a,n,b,E]),A=(0,g.useSelect)((e=>{const{getUnstableBase:t}=e(h.store);return(0,w.decodeEntities)(t(void 0)?.name)}),[]),C=n?.some((({type:e})=>e.startsWith("image/")||e.startsWith("video/")));switch(e.service_name){case"bluesky":{const e=n?.[0],a=e?.type.startsWith("image/")?e.url:null;return React.createElement(s.O,c()({},y,{description:(0,w.decodeEntities)(j),user:{avatarUrl:t.profileImage,address:t.externalName,displayName:t.displayName},customText:(0,w.decodeEntities)(R||`${b}\n\n${j.replaceAll(/[\s\n]/g," ")}`),customImage:a}))}case"facebook":return C?React.createElement(i.h,c()({},y,{type:"article",customText:R||j||b,user:{...t,avatarUrl:t.profileImage}})):React.createElement(r.l,c()({},y,{type:"article",customText:R||j||b,user:{...t,avatarUrl:t.profileImage}}));case"instagram-business":{const e=Boolean(a);return C||e?React.createElement(l.l,c()({},y,{image:n?.[0]?.url||a,name:t.displayName,profileImage:t.profileImage,caption:R||b||x})):React.createElement(k.q,null)}case"linkedin":return React.createElement(o.$,c()({},y,{jobTitle:__("Job Title (Company Name)","jetpack-publicize-pkg"),name:t.displayName,profileImage:t.profileImage,description:R||b||x}));case"mastodon":{const e=n?.[0],a=e?.type.startsWith("image/")?e.url:null;return React.createElement(d._,c()({},y,{description:j,siteName:A,user:{avatarUrl:t.profileImage,address:t.displayName,displayName:t.displayName},customText:R,customImage:a}))}case"nextdoor":{const e=`${R||b||x} ${n.length?E:""}`.trim();return React.createElement(u.K,c()({},y,{description:e,name:t.displayName,profileImage:t.profileImage}))}case"threads":{let e=b;R?e=R:b&&j&&(e=`${b}\n\n${j}`);const a=500-E.length-2;return e=(0,w.decodeEntities)(e).slice(0,a),e+=`\n\n${E}`,React.createElement(p.s,c()({},y,{caption:e,name:t.displayName,profileImage:t.profileImage}))}case"tumblr":return React.createElement(m.B,c()({},y,{user:{displayName:t.displayName,avatarUrl:t.profileImage},customText:R}));default:return null}}},18603:(e,t,a)=>{"use strict";a.d(t,{w:()=>m});var n=a(85985),c=a(56427),s=a(47143),i=a(27723),r=a(51609),l=a(31963),o=a(12141),d=a(39329),u=a(98588),p=a(47944);const __=i.__,_x=i._x;function m(){const{recordEvent:e}=(0,n.st)(),{canBeTurnedOn:t,shouldBeDisabled:a}=(0,d.j)(),i=(0,s.useSelect)((e=>e(l.M_).getConnections()),[]),m=(0,s.useSelect)((e=>e(l.M_).getServicesBy("status","unsupported")),[]),h=(0,r.useMemo)((()=>{const e=m.map((e=>e.id));return i.filter((({service_name:t})=>!e.includes(t))).map((e=>{const n=e.display_name,c=`${e.service_name}-${e.connection_id}`,s=React.createElement(o.A,{label:n,serviceName:e.service_name,profilePicture:e.profile_picture}),i=a(e)||!t(e)||!e.enabled;return{...e,className:i?p.A["disabled-tab"]:"",name:c,title:n,icon:s}}))}),[i,t,a,m]),{toggleConnectionById:g}=(0,s.useDispatch)(l.M_),v=(0,r.useCallback)(((t,a)=>()=>{g(t),e("jetpack_social_connection_toggled",{location:"preview-modal",enabled:!a.enabled,service_name:a.service_name})}),[e,g]);return React.createElement("div",{className:p.A["preview-section"]},React.createElement(c.TabPanel,{tabs:h},(e=>{const n=Boolean(t(e)&&e.enabled);return React.createElement("div",{className:p.A["preview-content"]},a(e)?null:React.createElement(c.ToggleControl,{label:n?_x("Connection enabled","","jetpack-publicize-pkg"):__("Connection disabled","jetpack-publicize-pkg"),checked:n,onChange:v(e.connection_id,e),__nextHasNoMarginBottom:!0}),React.createElement(u.z,{connection:e}))})))}},28167:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var n=a(56427),c=a(47143),s=a(86087),i=a(27723),r=a(31963),l=a(38476),o=a(71411);const _x=i._x;function d({postId:e}){const t=(0,c.useSelect)((t=>t(r.M_).isFetchingScheduledSharesForPost(e)),[e]),{deleteScheduledShare:a}=(0,c.useDispatch)(r.M_),{getConnectionById:d,isDeletingScheduledShare:u}=(0,c.useSelect)(r.M_,[]),p=(0,c.useSelect)((t=>t(r.M_).getScheduledSharesForPost(e)),[e]),m=(0,s.useMemo)((()=>p.map((({id:e,connection_id:t,timestamp:a})=>({id:e,connection:d(t.toString()),scheduledAt:a}))).filter((({id:e,connection:t})=>t&&!u(e)))),[d,u,p]),h=t&&!m.length,g=h?_x("Scheduled (Loading…)","Scheduled posts/shares items being loaded.","jetpack-publicize-pkg"):(0,i.sprintf)(/* translators: %d: number of scheduled posts */
_x("Scheduled (%d)","Scheduled posts/shares items","jetpack-publicize-pkg"),m.length);return React.createElement(n.PanelBody,{title:g,initialOpen:!1},React.createElement(n.PanelRow,null,h?React.createElement(n.Spinner,null):m.length?React.createElement("section",{className:o.A.wrapper},React.createElement("h4",{className:o.A.title},_x("Upcoming shares","Upcoming posts scheduled for sharing.","jetpack-publicize-pkg")),React.createElement(l.x,{items:m,onDelete:a})):React.createElement("p",{className:o.A.empty},_x("No upcoming shares","No upcoming posts scheduled for sharing.","jetpack-publicize-pkg"))))}},94360:(e,t,a)=>{"use strict";a.d(t,{X:()=>b});var n=a(56427),c=a(47143),s=a(43656),i=a(27723),r=a(51609),l=a(44511),o=a(77627),d=a(56973),u=a(50001),p=a(31963),m=a(41190),h=a(25357),g=a(1101),v=a(28167),w=a(47944);const __=i.__,_x=i._x;function b({onReShared:e}){const t=(0,c.useSelect)((e=>e(s.store).isCurrentPostPublished()),[]),a=(0,c.useSelect)((e=>e(s.store).isSavingPost()),[]),i=(0,c.useSelect)((e=>e(s.store).getCurrentPostId()),[]),{isRePublicizeUpgradableViaUpsell:b}=(0,o.A)(),f=(0,l.C)(),{enabledConnections:k}=(0,u.A)(),{schedulePost:_}=(0,d.P)(),x=(0,c.useSelect)((e=>e(p.M_).isSavingScheduledShare()),[]),E=(0,r.useCallback)((async e=>{await _({connectionIds:k.map((e=>Number(e.connection_id))),timestamp:e})}),[_,k]);return React.createElement("div",{className:w.A["settings-section"]},React.createElement("div",{className:w.A["settings-header"]},React.createElement("h2",null,t?_x("Share Post","The title of the social modal","jetpack-publicize-pkg"):__("Social Preview","jetpack-publicize-pkg"))),React.createElement("div",{className:w.A["settings-content"]},React.createElement("p",{className:w.A["modal-description"]},__("Edit and preview your social post before sharing.","jetpack-publicize-pkg")),React.createElement(m.o,{analyticsData:{location:"preview-modal"}}),t&&!b&&React.createElement("div",{className:w.A["share-actions"]},React.createElement(h.A,{isDisabled:!f,onConfirm:E,isBusy:x||a}),React.createElement(g._,{onShareCompleted:e,isDisabled:x}))),t?React.createElement("div",null,React.createElement(n.Panel,null,React.createElement(v.A,{postId:i}))):null)}},42259:(e,t,a)=>{"use strict";a.d(t,{A:()=>u});var n=a(96072),c=a.n(n),s=a(91937),i=a(3582),r=a(47143),l=a(43656),o=a(18537),d=a(39553);const u=e=>{const{message:t}=(0,d.A)(),{content:a,siteName:n}=(0,r.useSelect)((e=>{const{getEditedPostAttribute:t}=e(l.store),{getUnstableBase:a}=e(i.store);return{content:t("content").split("\x3c!--more")[0],siteName:(0,o.decodeEntities)(a(void 0).name)}}),[]),u=e.media?.[0],p=u?.type.startsWith("image/")?u.url:null;return React.createElement(s.x,c()({},e,{siteName:n,description:(0,o.decodeEntities)(a),customText:(0,o.decodeEntities)(t||`${e.title}\n\n${a.replaceAll(/[\s\n]/g," ")}`),customImage:p,hidePostPreview:!0}))}},59540:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(96072),c=a.n(n),s=a(87070),i=a(47143),r=a(39553);const l=(0,i.withSelect)((e=>{const{getEditedPostAttribute:t}=e("core/editor");return{excerpt:t("excerpt"),content:t("content").split("\x3c!--more")[0]}}))((e=>{const{message:t}=(0,r.A)(),{title:a,excerpt:n,content:i}=e;return React.createElement(s.k,c()({},e,{type:"article",customText:t||n||i||a,hidePostPreview:!0}))}))},81048:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var n=a(96072),c=a.n(n),s=a(8797),i=a(47143),r=a(18537),l=a(51609),o=a.n(l);const d=function(e){const t=(0,i.useSelect)((e=>{const{getUnstableBase:t}=e("core"),a=t().name;return(0,r.decodeEntities)(a)}));return o().createElement(s.m,c()({},e,{siteTitle:t}))}},29650:(e,t,a)=>{"use strict";a.d(t,{p:()=>l});var n=a(7368),c=a(27723),s=a(51609),i=a.n(s),r=a(39553);const __=c.__;function l(e){const{title:t,url:a,image:c,media:s,description:l}=e,{message:o}=(0,r.A)(),d=`${o||t||l} ${s.length?a:""}`.trim();return i().createElement(n.G,{jobTitle:__("Job Title (Company Name)","jetpack-publicize-pkg"),image:c,title:t,description:d,url:a,media:s,hidePostPreview:!0})}},98175:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(96072),c=a.n(n),s=a(71751),i=a(47143),r=a(18537),l=a(39553);const o=e=>{const{message:t}=(0,l.A)(),{content:a,siteName:n}=(0,i.useSelect)((e=>{const{getEditedPostAttribute:t}=e("core/editor"),{getUnstableBase:a}=e("core");return{content:t("content").split("\x3c!--more")[0],siteName:(0,r.decodeEntities)(a().name)}})),o=e.media?.[0],d=o?.type.startsWith("image/")?o.url:null;return React.createElement(s.R,c()({},e,{siteName:n,description:a,customText:t,customImage:d,hidePostPreview:!0}))}},81115:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(56427),c=a(18537),s=a(27723),i=a(10991),r=a(42160),l=a(31137);const __=s.__,o=function({onClose:e,initialTabName:t=null}){const a=(0,r.r)(),{image:s,media:o,title:d,description:u,url:p,excerpt:m}=(0,l.s)();return React.createElement(n.Modal,{onRequestClose:e,className:"jetpack-social-previews__modal",__experimentalHideHeader:!0},React.createElement(n.Button,{className:"jetpack-social-previews__modal--close-btn",onClick:e,icon:i.A,label:__("Close","jetpack-publicize-pkg")}),React.createElement(n.TabPanel,{className:"jetpack-social-previews__modal-previews",tabs:a,initialTabName:t},(e=>React.createElement("div",null,React.createElement(e.preview,{excerpt:(0,c.decodeEntities)(m),title:(0,c.decodeEntities)(d),description:(0,c.decodeEntities)(u),url:p,image:s,media:o})))))}},49257:(e,t,a)=>{"use strict";a.d(t,{s:()=>r});var n=a(58581),c=a(51609),s=a.n(c),i=a(39553);function r(e){const{title:t,url:a,image:c,media:r,description:l}=e,{message:o}=(0,i.A)(),d=`${o||t||l} ${r.length?a:""}`.trim();return s().createElement(n.n,{image:c,title:t,description:d,url:a,media:r,hidePostPreview:!0})}},97192:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n=a(56427),c=a(27723),s=a(42160);const __=c.__,_x=c._x,i=({openModal:e})=>{const t=(0,s.r)();return React.createElement("div",{className:"jetpack-social-previews__panel"},React.createElement("p",null,__("Preview what this will look like on social networks and Google search.","jetpack-publicize-pkg")),React.createElement("div",{className:"jetpack-gutenberg-social-icons"},t.map((e=>React.createElement(e.icon,{key:e.name,className:"jetpack-social-previews__icon"})))),React.createElement(n.Button,{variant:"secondary",onClick:e,label:__("Open Social Previews","jetpack-publicize-pkg")},_x("Preview","Button label that opens the social previews modal","jetpack-publicize-pkg")))}},68655:(e,t,a)=>{"use strict";a.d(t,{Q:()=>l});var n=a(33389),c=a(18537),s=a(51609),i=a.n(s),r=a(383);function l({excerpt:e,title:t,description:a,image:l,url:o,media:d}){const{shareMessage:u}=(0,r.h)(),p=(0,s.useMemo)((()=>{let n=t;u?n=u:t&&e&&(n=`${t}\n\n${e}`);const s=500-o.length-2;return n=(0,c.decodeEntities)(n).slice(0,s),n+=`\n\n${o}`,[{caption:n,title:t,description:a,image:l,media:d,url:o}]}),[e,t,l,a,d,o,u]);return i().createElement(n.n,{posts:p,hidePostPreview:!0})}},22322:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(96072),c=a.n(n),s=a(19036),i=a(47143),r=a(39553);const l=e=>{const{content:t}=(0,i.useSelect)((e=>{const{getEditedPostAttribute:t}=e("core/editor");return{content:t("content").split("\x3c!--more")[0]}})),{message:a}=(0,r.A)();return React.createElement(s.s,c()({},e,{description:t,customText:a,hidePostPreview:!0}))}},91537:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var n=a(43731),c=a(51609),s=a.n(c),i=a(383);const r=function({title:e,description:t,image:a,url:r,media:l}){const{shareMessage:o}=(0,i.h)(),d=(0,c.useMemo)((()=>[{text:o+(l.length?` ${r}`:""),cardType:a?"summary_large_image":"summary",title:e,description:t,image:a,media:l,url:r}]),[e,a,t,l,r,o]);return s().createElement(n.p,{tweets:d,hidePostPreview:!0})}},42160:(e,t,a)=>{"use strict";a.d(t,{r:()=>b});var n=a(96072),c=a.n(n),s=a(78478),i=a(27723),r=a(51609),l=a.n(r),o=a(42259),d=a(59540),u=a(81048),p=a(29650),m=a(98175),h=a(49257),g=a(68655),v=a(22322),w=a(91537);const __=i.__,_x=i._x;function b(){return(0,r.useMemo)((()=>[{title:__("Google Search","jetpack-publicize-pkg"),icon:e=>l().createElement(s.M5,c()({serviceName:"google"},e)),name:"google",preview:u.A},{title:__("X","jetpack-publicize-pkg"),icon:e=>l().createElement(s.M5,c()({serviceName:"x"},e)),name:"x",preview:w.A},{title:__("Facebook","jetpack-publicize-pkg"),icon:e=>l().createElement(s.M5,c()({serviceName:"facebook"},e)),name:"facebook",preview:d.A},{title:_x("Threads","The name of the social media network - threads.net","jetpack-publicize-pkg"),icon:e=>l().createElement(s.M5,c()({serviceName:"threads"},e)),name:"threads",preview:g.Q},{title:__("LinkedIn","jetpack-publicize-pkg"),icon:e=>l().createElement(s.M5,c()({serviceName:"linkedin"},e)),name:"linkedin",preview:p.p},{title:__("Nextdoor","jetpack-publicize-pkg"),icon:e=>l().createElement(s.M5,c()({serviceName:"nextdoor"},e)),name:"nextdoor",preview:h.s},{title:__("Tumblr","jetpack-publicize-pkg"),icon:e=>l().createElement(s.M5,c()({serviceName:"tumblr-alt"},e)),name:"tumblr",preview:v.A},{title:__("Mastodon","jetpack-publicize-pkg"),icon:e=>l().createElement(s.M5,c()({serviceName:"mastodon"},e)),name:"mastodon",preview:m.A},{title:__("Bluesky","jetpack-publicize-pkg"),icon:e=>l().createElement(s.M5,c()({serviceName:"bluesky"},e)),name:"bluesky",preview:o.A}].filter(Boolean)),[])}},31137:(e,t,a)=>{"use strict";a.d(t,{s:()=>u});var n=a(3582),c=a(47143),s=a(43656),i=a(27723),r=a(51609),l=a(383),o=a(74820),d=a(25995);const __=i.__;function u(){const{attachedMedia:e,imageGeneratorSettings:t}=(0,l.h)(),{getMedia:a}=(0,c.useSelect)(n.store,[]),{getEditedPostAttribute:i,getEditedPostContent:u}=(0,c.useSelect)(s.store,[]);return(0,r.useMemo)((n=>{const c=i("featured_media");let s=c?(0,d.l)(a(c)):"";const r=t.enabled?(0,o.S)(t.token):"";if(r&&(s=r),!s){const e=(0,d.t)(u());e&&(s=e)}const l=[],p=e=>{const t=a(e);return t?{type:t.mime_type,url:(0,d.l)(t),alt:t.alt_text}:null};for(const{id:t}of e){const e=p(t);e&&l.push(e)}return{title:(i("meta")?.jetpack_seo_html_title||i("title")||"").trim(),description:(i("meta")?.advanced_seo_description||i("excerpt")||i("content").split("\x3c!--more")[0]||__("Visit the post for more.","jetpack-publicize-pkg")||"").trim(),url:i("link"),excerpt:(i("excerpt")||i("content").split("\x3c!--more")[0]||"").trim(),image:s,media:l,initialTabName:null}}),[e,i,u,a,t.enabled,t.token])}},25995:(e,t,a)=>{"use strict";function n(e){return e?e.media_details?.sizes?.large?.source_url||e.source_url:null}function c(e){const t=(new DOMParser).parseFromString(e,"text/html"),a=Array.from(t.querySelectorAll("img")),n=a[0]?.src;return n??null}a.d(t,{l:()=>n,t:()=>c})},18699:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(86087),c=a(76794);const s=({sourceUrl:e,mime:t,duration:a})=>{const[s,i]=(0,n.useState)(!1),[r,l]=(0,n.useState)(0),o=(0,n.useRef)(null),d=(0,n.useRef)(null),u=(0,n.useRef)(null);(0,n.useEffect)((()=>{o.current?.load()}),[e]);const p=(0,n.useCallback)((()=>{o.current.pause(),o.current.currentTime=0,clearInterval(d.current),clearTimeout(u.current),l(0),i(!1)}),[]),m=(0,n.useCallback)((()=>{u.current=setTimeout((()=>{s||(o.current.play(),i(!0),d.current=setInterval((()=>{l((e=>e+1))}),1e3))}),500)}),[s]),h=()=>{const e=a-r,t=Math.floor(e/60),n=String(Math.floor(e%60)).padStart(2,"0");return React.createElement("div",{className:c.A.progress},React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"6",height:"8",fill:"none"},React.createElement("path",{fill:"#fff",d:"M5.25 3.567a.5.5 0 0 1 0 .866L.75 7.031A.5.5 0 0 1 0 6.598V1.402A.5.5 0 0 1 .75.969l4.5 2.598Z"})),React.createElement("span",{className:c.A.duration},`${t}:${n}`))};return React.createElement("div",{className:c.A.wrapper,onMouseEnter:m,onMouseLeave:p},React.createElement("video",{ref:o,onEnded:p,muted:!0},React.createElement("source",{src:e,type:t})),React.createElement(h,null))}},98420:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var n=a(383);function c(){const{attachedMedia:e,updateJetpackSocialOptions:t}=(0,n.h)();return{attachedMedia:e,updateAttachedMedia:e=>t("attached_media",e)}}},54915:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(47143),c=a(43656);const s=()=>(0,n.useSelect)((e=>e(c.store).getEditedPostAttribute("featured_media")))},49747:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(47143),c=a(43656),s=a(86087),i=a(383);const r=(e,t)=>({isEnabled:e?.enabled??!t,customText:e?.custom_text??null,imageType:e?.image_type??null,imageId:e?.image_id??null,template:e?.template??null,token:e?.token??null});function l(){const{imageGeneratorSettings:e,jetpackSocialOptions:t,updateJetpackSocialOptions:a}=(0,i.h)(),{isPostPublished:l}=(0,n.useSelect)((e=>({isPostPublished:e(c.store).isCurrentPostPublished()}))),o=(0,s.useCallback)(((t,n)=>{const c={...e,[t]:n};a("image_generator_settings",c)}),[e,a]),d=(0,s.useCallback)((t=>{const n={...e,...t};a("image_generator_settings",n)}),[e,a]);return{...r(t.image_generator_settings,l),setIsEnabled:e=>o("enabled",e),setToken:e=>o("token",e),updateSettings:d}}},44511:(e,t,a)=>{"use strict";a.d(t,{C:()=>l});var n=a(47143),c=a(43656),s=a(92214),i=a(77627),r=a(31873);function l(){const{isPublicizeEnabled:e}=(0,i.A)(),t=(0,s.P)(),{isFetching:a}=(0,r.A)(),{isCurrentPostPublished:l,isSavingPost:o}=(0,n.useSelect)(c.store,[]);return e&&t&&!a&&l()&&!o()}},92214:(e,t,a)=>{"use strict";a.d(t,{P:()=>u});var n=a(47143),c=a(98420),s=a(54915),i=a(80624),r=a(52367),l=a(80682),o=a(50001),d=a(31963);function u(){const{enabledConnections:e}=(0,o.A)(),{attachedMedia:t}=(0,c.A)(),a=(0,s.A)(),u=t[0]?.id||a,{validationErrors:p,isConvertible:m}=(0,r.A)(e,(0,i.A)(u)[0]),h=(0,n.useSelect)((e=>e(d.M_).getBrokenConnections().map((e=>e.connection_id))),[]);return e.some((function({connection_id:e,service_name:t}){return!h.includes(e)&&(!(e in p)||("instagram-business"!==t||!Object.values(p).includes(l.k3))&&m)}))}},80624:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n=a(47143),c=a(86087);const s=async e=>{if(!e)return{};let t={mime:e.mime_type,fileSize:e.media_details.filesize,length:e.media_details?.length};"video/videopress"===e.mime_type&&(t=await(async e=>{if(!e?.media_details?.videopress?.original||!e?.media_details?.videopress?.duration)return{};const t=await fetch(e?.media_details?.videopress?.original,{method:"HEAD"}),a=t.headers.get("content-length"),n=t.headers.get("content-type");return a&&n?{mime:n,fileSize:a,length:Math.round(e.media_details.videopress.duration/1e3)}:{}})(e));const a=e?.media_details?.sizes??{};if(!a.full)return{mediaData:{width:e.media_details.width,height:e.media_details.height,sourceUrl:e.source_url},metaData:t};const n=a.medium||a.large,c={width:n.width,height:n.height,sourceUrl:n.source_url};return{mediaData:{width:a.full.width,height:a.full.height,sourceUrl:a.full.source_url},metaData:t,previewData:c}};function i(e=null){const[t,a]=(0,c.useState)([{}]),i=(0,n.useSelect)((t=>t("core").getMedia(e,{context:"view"})),[e]),r=(0,c.useCallback)((async()=>{try{const e=await s(i);a([e??{}])}catch{a([{}])}}),[i]);return(0,c.useEffect)((()=>{r()}),[r]),t}},80682:(e,t,a)=>{"use strict";a.d(t,{$m:()=>d,I1:()=>l,eF:()=>r,ec:()=>i,i1:()=>o,k3:()=>c,z9:()=>s});var n=a(27723);const __=n.__,c="NO_MEDIA_ERROR",s="FILE_TYPE_ERROR",i="FILE_SIZE_ERROR",r="VIDEO_LENGTH_TOO_LONG_ERROR",l="VIDEO_LENGTH_TOO_SHORT_ERROR",o="DIMENSION_ERROR";function d(e){return{[i]:__("File too big","jetpack-publicize-pkg"),[r]:__("Video too long","jetpack-publicize-pkg"),[l]:__("Video too short","jetpack-publicize-pkg"),[o]:__("Invalid dimensions","jetpack-publicize-pkg")}[e]||__("Invalid media","jetpack-publicize-pkg")}},52367:(e,t,a)=>{"use strict";a.d(t,{A:()=>u,c:()=>l});var n=a(86087),c=a(98420),s=a(49747),i=a(80682),r=a(2204);function l(e){return"video"===e.split("/")[0]}const o=e=>{if(!e?.mime||!e?.fileSize)return!1;const{mime:t,fileSize:a}=e;if(l(t))return!1;if(!r.nJ.includes(t))return!1;return!((a?a/Math.pow(1e3,2):0)>=55)},d=(e,t,a,n,c)=>{const s=r.mB[a]??r.EX;if(!e||0===Object.keys(e).length)return s.requiresMedia&&!c?i.k3:null;const{mime:o,fileSize:d}=e;if(!s.requiresMedia&&!n)return null;if(!o||!s.allowedMediaTypes.includes(o.toLowerCase()))return i.z9;if(!t?.width||!t?.height)return i.i1;const u=d?d/Math.pow(1e3,2):null;return l(o)?((e,t,a,n,c)=>{const{minSize:s=0,maxSize:l=r.TR,minLength:o=0,maxLength:d=r.TR,maxWidth:u=r.TR,aspectRatio:p=r.EX.video.aspectRatio}=c;if(!e||e>l||e<s)return i.ec;if(!t||t<o)return i.I1;if(t>d)return i.eF;const m=a/n;return m<p.min||m>p.max||a>u?i.i1:null})(u,e.length,t.width,t.height,s.video):((e,t,a,n)=>{const{maxSize:c=r.TR,minWidth:s=0,maxWidth:l=r.TR,aspectRatio:o=r.EX.image.aspectRatio}=n,d=t/a;return d<o.min||d>o.max||t>l||t<s?i.i1:!e||e>c?i.ec:null})(u,t.width,t.height,s.image)},u=(e,t)=>{const{attachedMedia:a}=(0,c.A)(),{isEnabled:i}=(0,s.A)(),r=a.length>0,l=(0,n.useRef)({});return(0,n.useMemo)((()=>{const a=e.reduce(((e,{connection_id:a,service_name:n})=>{const c=d(t.metaData,t.mediaData,n,r,i&&"instagram-business"===n);return c&&(e[a]=c),e}),{});return JSON.stringify(a)!==JSON.stringify(l.current)&&(l.current=a),{validationErrors:l.current,isConvertible:o(t.metaData)}}),[e,t.metaData,t.mediaData,r,i])}},2204:(e,t,a)=>{"use strict";a.d(t,{EX:()=>h,TR:()=>m,UB:()=>w,mB:()=>g,nJ:()=>v});const n="video/mp4",c="video/mov",s="video/videopress",i=["image/jpeg","image/jpg","image/png"],r=i.concat(["image/gif","image/bmp"]),l=["video/3g2","video/3gp","video/3gpp","video/asf","video/avi","video/dat","video/divx","video/dv","video/f4v","video/flv","video/gif","video/m2ts","video/m4v","video/mkv","video/mod","video/mov","video/mp4","video/mpe","video/mpeg","video/mpeg4","video/mpg","video/mts","video/nsv","video/ogm","video/ogv","video/qt","video/tod","video/ts","video/vob","video/wmv"],o=i.concat(["image/gif","image/heic","image/heif","image/webp","image/avif"]),d=["video/webm","video/quicktime","video/ogg"],u=i.concat(["image/gif","image/jpe","image/tif","image/tiff","image/webp"]),p=[c,"video/avi","video/mpg","video/mpeg","video/m4v"],m=1e5,h={requiresMedia:!1,allowedMediaTypes:i.concat([n,s,c]),image:{maxSize:4,minWidth:0,maxWidth:m,aspectRatio:{min:0,max:m}},video:{minLength:0,minSize:0,maxSize:m,maxLength:m,maxWidth:m,aspectRatio:{min:0,max:m}},charLimit:255},g={twitter:{allowedMediaTypes:i.concat([n,s]),image:{maxSize:5},video:{maxSize:512,maxLength:140}},facebook:{allowedMediaTypes:r.concat([s,...l]),image:{maxSize:8},video:{maxSize:1e4,maxLength:14400},charLimit:1e4},tumblr:{allowedMediaTypes:i.concat([n,c,s]),image:{maxSize:20},video:{maxSize:500,maxLength:600},charLimit:4096},linkedin:{allowedMediaTypes:i.concat([n,s]),image:{maxSize:20},video:{minSize:.075,maxSize:200,maxLength:600,minLength:3},charLimit:3e3},"instagram-business":{requiresMedia:!0,allowedMediaTypes:["image/jpg","image/jpeg",n,c,s],image:{maxSize:8,minWidth:320,maxWidth:1440,aspectRatio:{min:.8,max:1.91}},video:{maxLength:900,minLength:3,maxSize:1e3,maxWidth:1920,aspectRatio:{min:.01,max:10}},charLimit:2200},mastodon:{allowedMediaTypes:o.concat([...d,n,s]),image:{maxSize:10},video:{maxSize:40},charLimit:500},nextdoor:{allowedMediaTypes:u.concat([...p,n,s]),image:{maxSize:10},video:{maxSize:500},charLimit:1e4},bluesky:{allowedMediaTypes:i.concat([n,s,c,"video/webm","video/mpeg"]),image:{maxSize:1},video:{maxSize:1e4,maxLength:60},charLimit:300}},v=["image/png","image/jpeg","image/jpg","image/heic","image/heif","image/webp"],w=[...new Set([...i,...r,...o,...l,...d,...v])]},38656:(e,t,a)=>{"use strict";a.d(t,{e:()=>i});var n=a(97999),c=a(27844),s=a(31704);function i(){return!(0,s.D)()&&(0,n.siteHasFeature)(c.q.IMAGE_GENERATOR)}},383:(e,t,a)=>{"use strict";a.d(t,{h:()=>d});var n=a(47143),c=a(43656),s=a(86087),i=a(70407);const r=[],l={},o={enabled:!1};function d(){const{editPost:e}=(0,n.useDispatch)(c.store),t=(0,i.Oc)(),a=(0,n.useSelect)((e=>{const a=e(c.store).getEditedPostAttribute("meta")||l,n=a.jetpack_publicize_feature_enabled??!0,s=a.jetpack_social_options||l;return{isPublicizeEnabled:n,jetpackSocialOptions:s,attachedMedia:s.attached_media||r,imageGeneratorSettings:s.image_generator_settings??o,isPostAlreadyShared:a.jetpack_social_post_already_shared??!1,shareMessage:`${a.jetpack_publicize_message||""}`.substring(0,t)}}),[t]),d=(0,s.useCallback)(((t,a)=>{e({meta:{[t]:a}})}),[e]),u=(0,s.useCallback)((()=>{d("jetpack_publicize_feature_enabled",!a.isPublicizeEnabled)}),[a.isPublicizeEnabled,d]),p=(0,s.useCallback)(((e,t)=>{d("jetpack_social_options",{...a.jetpackSocialOptions,[e]:t,version:2})}),[a.jetpackSocialOptions,d]);return(0,s.useMemo)((()=>({...a,togglePublicizeFeature:u,updateJetpackSocialOptions:p,updateMeta:d})),[a,u,p,d])}},57275:(e,t,a)=>{"use strict";a.d(t,{z:()=>i});var n=a(47143),c=a(43656),s=a(86087);function i(e){const t=(0,n.useSelect)((e=>e(c.store).isPublishingPost()),[]),[a,i]=(0,s.useState)(e),r=(0,s.useRef)(!1);return(0,s.useEffect)((()=>{t&&(r.current=!0),r.current||i(e)}),[t,e]),a}},77627:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var n=a(39384),c=a(85985),s=a(47143),i=a(43656),r=a(70407),l=a(383);const o="republicize";function d(){const e=!(0,c.d9)()&&!(0,c.Sy)()||(0,c.FB)(o)?.available,t=(0,s.useSelect)((e=>e(i.store).isCurrentPostPublished()),[]),{isUserConnected:a}=(0,n.useConnection)(),{urls:d}=(0,r.nE)(),{isPublicizeEnabled:u,togglePublicizeFeature:p,isPostAlreadyShared:m}=(0,l.h)(),h=(0,c.W4)(o)&&!e,g=t?e:u,v=t&&h,w=t&&!e,b=!a&&!(0,c.Sy)();return{isPublicizeEnabledMeta:u,isPublicizeEnabled:g,togglePublicizeFeature:p,isPublicizeDisabledBySitePlan:v,isRePublicizeFeatureAvailable:e,isRePublicizeUpgradableViaUpsell:h,hidePublicizeFeature:w,isPostAlreadyShared:m,isSocialImageGeneratorEnabled:!!(0,c.$i)()?.social?.isSocialImageGeneratorEnabled,connectionsPageUrl:d.connectionsManagementPage,needsUserConnection:b}}},88584:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var n=a(29491),c=a(86087),s=a(86212),i=a(50001);function r(){const e=(0,c.useRef)(!1),t=(0,c.useRef)(!0),a=(0,s.A)(),{refresh:r}=(0,i.A)(),l=(0,n.useDebounce)(r,0),o=(0,n.useDebounce)(r,2e3);return()=>{t.current&&(l(),t.current=!1),a||(e.current=!0,o.cancel()),a&&e.current&&(o(),e.current=!1)}}},38394:(e,t,a)=>{"use strict";a.d(t,{i:()=>i});var n=a(27723),c=a(16480),s=a(51609);const __=n.__;function i({onError:e,onSuccess:t}){const[a,n]=(0,s.useState)(!1),i=(0,s.useCallback)((async(a,s="image")=>{try{n(!0);const i=await fetch(a);if(!i.ok)return void e(new Error(__("Failed to download image.","jetpack-publicize-pkg"),{cause:i}));const r=await i.blob(),l=new File([r],s,{type:r.type});await(0,c.uploadMedia)({filesList:[l],onFileChange:([e])=>{void 0!==e.id&&(t(e),n(!1))},onError:t=>{e(new Error(__("Failed to save image to library.","jetpack-publicize-pkg"),{cause:t})),n(!1)},maxUploadFileSize:0})}catch(t){e(new Error(__("Failed to save image to library.","jetpack-publicize-pkg"),{cause:t}))}finally{n(!1)}}),[e,t]);return(0,s.useMemo)((()=>({isSaving:a,save:i})),[a,i])}},49036:(e,t,a)=>{"use strict";a.d(t,{Wv:()=>l,_D:()=>r});var n=a(29491),c=a(47143),s=a(43656),i=a(86087);function r(e,t){const a=(0,c.useSelect)((e=>e(s.store).isPublishingPost()),[]),r=(0,n.usePrevious)(a);(0,i.useEffect)((()=>{r&&!a&&e()}),[a,r,e,t])}function l(e,t){const a=(0,c.useSelect)((e=>e(s.store).isPublishingPost()),[]),r=(0,n.usePrevious)(a);(0,i.useEffect)((()=>{!r&&a&&e()}),[a,r,e,t])}},56973:(e,t,a)=>{"use strict";a.d(t,{P:()=>d});var n=a(97999),c=a(47143),s=a(43656),i=a(86087),r=a(31963),l=a(70407),o=a(39553);function d(){const{postId:e,isAutosaveablePost:t,isDirtyPost:a}=(0,c.useSelect)((e=>{const t=e(s.store);return{postId:t.getCurrentPostId(),isAutosaveablePost:t.isEditedPostAutosaveable(),isDirtyPost:t.isEditedPostDirty()}}),[]),{createScheduledShare:d}=(0,c.useDispatch)(r.M_),{message:u}=(0,o.A)(),p=(0,n.siteHasFeature)(l.qT.IMAGE_GENERATOR)||(0,n.siteHasFeature)(l.qT.ENHANCED_PUBLISHING),m=(0,c.dispatch)(s.store).savePost;return{schedulePost:(0,i.useCallback)((async({connectionIds:n,timestamp:c})=>{if(!n.length||!c)return!1;a&&t&&p&&await m();return(await Promise.all(n.map((t=>d({post_id:e,connection_id:t,message:u,timestamp:c}))))).every(Boolean)}),[a,t,p,m,d,e,u])}}},31873:(e,t,a)=>{"use strict";a.d(t,{A:()=>m});var n=a(1455),c=a.n(n),s=a(47143),i=a(43656),r=a(86087),l=a(27723),o=a(50001),d=a(39553),u=a(51371);const __=l.__;function p(e){const t=e?.code,a=e?.errors?.length;if(!t&&!a)return!1;let n="";return t&&(n=__("Unable to share the Post","jetpack-publicize-pkg")),a&&(n=__("Unable to share the Post","jetpack-publicize-pkg")),{message:n,result:e}}function m(e){const{message:t}=(0,d.A)(),{skippedConnections:a}=(0,o.A)(),n=(0,s.useSelect)((e=>e(i.store).getCurrentPostId()),[]);e=e||n;const[l,m]=(0,r.useState)({data:[],error:{}}),h=(0,u.nE)().api_paths.resharePost.replace("{postId}",e),g=(0,r.useCallback)((async function(n=null){const s={isFetching:!1,isError:!1,isSuccess:!1,data:[],error:{},postId:e};if(l.isFetching)return;const i=n||a;return m({...s,isFetching:!0}),await c()({path:h,method:"POST",data:{message:t,skipped_connections:i,async:!0}}).then(((e={})=>{const t=p(e);if(t)return m((e=>({...e,isFetching:!1,isSuccess:!1,isError:!0,data:[],error:t})));m((t=>({...t,isFetching:!1,isSuccess:!0,isError:!1,data:e?.results,error:{}})))})).catch((e=>{m((t=>({...t,isFetching:!1,isSuccess:!1,isError:!0,data:[],error:p(e)})))})),function(){m(s)}}),[e,t,a,l.isFetching,h]);return{...l,doPublicize:g}}},50001:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n=a(47143),c=a(51609),s=a(31963);function i(){const{refreshConnectionTestResults:e,toggleConnectionById:t}=(0,n.useDispatch)(s.M_),a=(0,n.useSelect)((e=>{const t=e(s.M_),a=t.getConnections(),n=t.getEnabledConnections(),c=t.getDisabledConnections();return{connections:a,hasConnections:a.length>0,hasEnabledConnections:n.length>0,disabledConnections:c,enabledConnections:n}}),[]),i=(0,c.useMemo)((()=>a.disabledConnections.map((e=>e.connection_id))),[a.disabledConnections]);return(0,c.useMemo)((()=>({...a,skippedConnections:i,toggleById:t,refresh:e})),[a,e,i,t])}},39553:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(70407),c=a(383);function s(){const{updateMeta:e,shareMessage:t}=(0,c.h)();return{message:t,maxLength:(0,n.Oc)(),updateMessage:function(t){e("jetpack_publicize_message",t)}}}},75168:(e,t,a)=>{"use strict";a.d(t,{F:()=>r});var n=a(47143),c=a(43656),s=a(31963),i=a(49036);function r(){const{mergeConnections:e}=(0,n.useDispatch)(s.M_),{didPostSaveRequestSucceed:t,isCurrentPostPublished:a,getEditedPostAttribute:r}=(0,n.useSelect)((e=>e(c.store)),[]);(0,i._D)((()=>{if(t()&&a()){const t=r("jetpack_publicize_connections");t?.length&&e(t)}}),[t,a,r])}},21259:(e,t,a)=>{"use strict";a.d(t,{_:()=>i});var n=a(97999),c=a(3582),s=a(47143);function i(){return(0,s.useSelect)((e=>{const{getUser:t}=e(c.store),{current_user:a}=(0,n.getScriptData)().user;return Boolean(t(a.id)?.capabilities?.edit_others_posts)}),[])}},38716:(e,t,a)=>{"use strict";a.r(t),a.d(t,{abortRefreshConnectionsRequest:()=>f,abortRequest:()=>b,addConnection:()=>m,closeConnectionsModal:()=>L,createAbortController:()=>v,createConnection:()=>A,deleteConnection:()=>E,deleteConnectionById:()=>R,deletingConnection:()=>j,mergeConnections:()=>g,openConnectionsModal:()=>P,refreshConnectionTestResults:()=>k,removeAbortControllers:()=>w,setConnections:()=>u,setKeyringResult:()=>p,setReconnectingAccount:()=>N,syncConnectionsToPostMeta:()=>_,toggleConnection:()=>h,toggleConnectionById:()=>x,toggleConnectionsModal:()=>M,updateConnection:()=>C,updateConnectionById:()=>S,updatingConnection:()=>z});var n=a(692),c=a(1455),s=a.n(c),i=a(47143),r=a(43656),l=a(27723),o=a(51371),d=a(54118);const __=l.__;function u(e){return{type:d.dA,connections:e}}function p(e){return{type:d.BN,keyringResult:e}}function m(e){return{type:d.Bc,connection:e}}function h(e){return{type:d.af,connectionId:e}}function g(e){return function({dispatch:t,select:a}){const n=a.getConnections(),c=[],s={enabled:!0};for(const t of e){const e={...s,...n.find((e=>e.connection_id===t.connection_id)),...t};c.push(e)}t(u(c))}}function v(e,t){return{type:d.WB,requestType:t,abortController:e}}function w(e){return{type:d.Wp,requestType:e}}function b(e){return function({dispatch:t,select:a}){const n=a.getAbortControllers(e);for(const e of n)e.abort();t(w(e))}}function f(){return b(d.rz)}function k(e=!1){return async function({dispatch:t,select:a}){try{const n=(0,o.nE)().api_paths.refreshConnections;for(;a.getUpdatingConnections().length>0||a.getDeletingConnections().length>0;)await new Promise((e=>setTimeout(e,100)));const c=new AbortController;t(v(c,d.rz));t(g(await s()({path:n,signal:c.signal}))),e&&t(_())}catch(a){"AbortError"===a.name&&t(k(e))}}}function _(){return function({registry:e,select:t}){const a=t.getConnections();return e.dispatch(r.store).editPost({jetpack_publicize_connections:a})}}function x(e,t=!0){return function({dispatch:a}){a(h(e)),t&&a(_())}}function E(e){return{type:d.nC,connectionId:e}}function j(e,t=!0){return{type:d.WH,connectionId:e,deleting:t}}function R({connectionId:e,showSuccessNotice:t=!0}){return async function({registry:a,dispatch:c}){const{createErrorNotice:l,createSuccessNotice:o}=(0,i.dispatch)(n.store);try{const n=`/wpcom/v2/publicize/connections/${e}`;return c(f()),c(j(e)),await s()({method:"DELETE",path:n}),c(E(e)),t&&o(__("Account disconnected successfully.","jetpack-publicize-pkg"),{type:"snackbar",isDismissible:!0}),a.select(r.store).getCurrentPostId()&&c(_()),!0}catch(e){let t=__("Error disconnecting account.","jetpack-publicize-pkg");"object"==typeof e&&"message"in e&&e.message&&(t=`${t} ${e.message}`),l(t,{type:"snackbar",isDismissible:!0})}finally{c(j(e,!1))}return!1}}let y=1;function A(e,t={}){return async function({registry:a,dispatch:c}){const{createErrorNotice:o,createSuccessNotice:d}=(0,i.dispatch)(n.store),u="new-"+ ++y;try{const n="/wpcom/v2/publicize/connections/";c(m({connection_id:u,...t})),c(f()),c(z(u));const i=await s()({method:"POST",path:n,data:e});i&&(c(C(u,{...i,enabled:!0})),d((0,l.sprintf)(/* translators: %s is the name of the social media platform e.g. "Facebook" */
__("%s account connected successfully.","jetpack-publicize-pkg"),i.service_label),{type:"snackbar",isDismissible:!0}),a.select(r.store).getCurrentPostId()&&c(_()))}catch(e){let t=__("Error connecting account.","jetpack-publicize-pkg");"object"==typeof e&&"message"in e&&e.message&&(t=`${t} ${e.message}`),o(t,{type:"snackbar",isDismissible:!0})}finally{c(z(u,!1)),c(E(u))}}}function C(e,t){return{type:d.dw,connectionId:e,data:t}}function z(e,t=!0){return{type:d._6,connectionId:e,updating:t}}function N(e){return{type:d.Nv,reconnectingAccount:e}}function S(e,t){return async function({dispatch:a,select:c}){const{createErrorNotice:r,createSuccessNotice:l}=(0,i.dispatch)(n.store),o=c.getConnectionById(e);try{const n=`/wpcom/v2/publicize/connections/${e}`;a(f()),a(C(e,t)),a(z(e));await s()({method:"POST",path:n,data:t})&&l(__("Account updated successfully.","jetpack-publicize-pkg"),{type:"snackbar",isDismissible:!0})}catch(t){let n=__("Error updating account.","jetpack-publicize-pkg");"object"==typeof t&&"message"in t&&t.message&&(n=`${n} ${t.message}`),a(C(e,o)),r(n,{type:"snackbar",isDismissible:!0})}finally{a(z(e,!1))}}}function M(e){return{type:d.xW,isOpen:e}}function P(){return M(!0)}function L(){return M(!1)}},54118:(e,t,a)=>{"use strict";a.d(t,{BN:()=>u,Bc:()=>c,M6:()=>f,Mc:()=>k,Nv:()=>d,WB:()=>m,WH:()=>r,Wp:()=>h,_6:()=>o,_y:()=>b,aF:()=>_,af:()=>s,b5:()=>g,dA:()=>n,dw:()=>l,fz:()=>w,nC:()=>i,rz:()=>v,xW:()=>p});const n="SET_CONNECTIONS",c="ADD_CONNECTION",s="TOGGLE_CONNECTION",i="DELETE_CONNECTION",r="DELETING_CONNECTION",l="UPDATE_CONNECTION",o="UPDATING_CONNECTION",d="SET_RECONNECTING_ACCOUNT",u="SET_KEYRING_RESULT",p="TOGGLE_CONNECTIONS_MODAL",m="ADD_ABORT_CONTROLLER",h="REMOVE_ABORT_CONTROLLERS",g="DEFAULT",v="REFRESH_CONNECTIONS",w="FETCH_POST_SHARE_STATUS",b="RECEIVE_POST_SHARE_STATUS",f="TOGGLE_SHARE_STATUS_MODAL",k="TOGGLE_SHARE_POST_MODAL",_="POLLING_FOR_POST_SHARE_STATUS"},62491:(e,t,a)=>{"use strict";a.d(t,{A:()=>p});var n=a(38716),c=a(8773),s=a(83577),i=a(3819),r=a(21131),l=a(72451),o=a(64236),d=a(63491),u=a(95660);const p={...l,...r,...n,...o,...a(83117),...u,...c,...d,...i,...s}},8773:(e,t,a)=>{"use strict";a.r(t),a.d(t,{setShowPricingPage:()=>s});var n=a(3582),c=a(75238);function s(e){return async function({registry:t}){const{saveSite:a}=t.dispatch(n.store);await a({[c.HN]:e})}}},83577:(e,t,a)=>{"use strict";a.r(t),a.d(t,{createScheduledShare:()=>i,deleteScheduledShare:()=>r});var n=a(3582),c=a(27723),s=a(692);const __=c.__;function i(e){return async function({registry:t}){const{saveEntityRecord:a}=t.dispatch(n.store),{getLastEntitySaveError:c}=t.select(n.store),{createErrorNotice:i,createSuccessNotice:r}=t.dispatch(s.store),l=await a("wpcom/v2","publicize/scheduled-actions",e);if(l)r(__("Post scheduled successfully.","jetpack-publicize-pkg"),{type:"snackbar",id:"social-scheduled-share"});else{const e=c("wpcom/v2","publicize/scheduled-actions");let t=__("There was an error scheduling the post.","jetpack-publicize-pkg");e?.message&&(t+=" "+e.message),i(t,{type:"snackbar",id:"social-scheduled-share"})}return l}}function r(e){return async function({registry:t}){const{deleteEntityRecord:a}=t.dispatch(n.store),{getLastEntityDeleteError:c}=t.select(n.store),{createErrorNotice:i}=t.dispatch(s.store);if(!await a("wpcom/v2","publicize/scheduled-actions",e)){const t=c("wpcom/v2","publicize/scheduled-actions",e);let a=__("There was an error deleting the item.","jetpack-publicize-pkg");t?.message&&(a+=" "+t.message),i(a,{type:"snackbar"})}}}},3819:(e,t,a)=>{"use strict";a.r(t),a.d(t,{refreshServicesList:()=>c});var n=a(3582);function c(){return async function({registry:e}){e.dispatch(n.store).invalidateResolution("getEntityRecords",["wpcom/v2","publicize/services"])}}},21131:(e,t,a)=>{"use strict";a.r(t),a.d(t,{closeSharePostModal:()=>i,openSharePostModal:()=>s,toggleSharePostModal:()=>c});var n=a(54118);function c(e){return{type:n.Mc,isOpen:e}}function s(){return c(!0)}function i(){return c(!1)}},72451:(e,t,a)=>{"use strict";a.r(t),a.d(t,{closeShareStatusModal:()=>o,defaultIsRequestComplete:()=>d,fetchPostShareStatus:()=>s,openShareStatusModal:()=>l,pollForPostShareStatus:()=>h,pollingForPostShareStatus:()=>m,receivePostShareStaus:()=>i,toggleShareStatusModal:()=>r});var n=a(43656),c=a(54118);function s(e,t=!0){return{type:c.fz,postId:e,loading:t}}function i(e,t){return{type:c._y,shareStatus:e,postId:t}}function r(e){return{type:c.M6,isOpen:e}}function l(){return r(!0)}function o(){return r(!1)}const d=({lastTimestamp:e,postShareStatus:t})=>e?t.shares.some((t=>t.timestamp>e)):t.shares.length>0,u=6e4,p=3e3;function m(e,t=!0){return{type:c.aF,postId:e,polling:t}}function h({pollingInterval:e=p,postId:t,isRequestComplete:a=d,timeout:c=u}={}){return async function({dispatch:s,select:i,registry:r}){const l=Date.now(),o=t||r.select(n.store).getCurrentPostId(),d=i.getPostShareStatus(o).shares[0]?.timestamp||0;let u=!1,p=!1;s(m(o));do{i.getPostShareStatus(o).loading||s.invalidateResolution("getPostShareStatus",[o]),await new Promise((t=>setTimeout(t,e))),u=a({lastTimestamp:d,postShareStatus:i.getPostShareStatus(o)}),p=Date.now()-l>c}while(!u&&!p);s(m(o,!1))}}},64236:(e,t,a)=>{"use strict";a.r(t),a.d(t,{updateSocialImageGeneratorConfig:()=>s});var n=a(3582),c=a(75238);function s(e){return async function({registry:t}){const{saveSite:a}=t.dispatch(n.store);await a({[c.Am]:e})}}},63491:(e,t,a)=>{"use strict";a.r(t),a.d(t,{updateSocialModuleSettings:()=>s});var n=a(3582),c=a(70407);function s(e){return async function({registry:t}){const{socialToggleBase:a}=(0,c.nE)().api_paths,{saveEntityRecord:s}=t.dispatch(n.store);await s("jetpack/v4",a,e)}}},95660:(e,t,a)=>{"use strict";a.r(t),a.d(t,{toggleSocialNotes:()=>s,updateSocialNotesConfig:()=>i});var n=a(3582),c=a(75238);function s(e){return async function({registry:t}){const{saveSite:a}=t.dispatch(n.store);await a({[c.cK]:e})}}function i(e){return async function({registry:t}){const{saveSite:a}=t.dispatch(n.store);await a({[c.LQ]:e})}}},83117:(e,t,a)=>{"use strict";a.r(t),a.d(t,{updateUtmSettings:()=>s});var n=a(3582),c=a(75238);function s(e){return async function({registry:t}){const{saveSite:a}=t.dispatch(n.store);await a({[c.bn]:e})}}},75238:(e,t,a)=>{"use strict";a.d(t,{Am:()=>n,HN:()=>r,LQ:()=>i,Ml:()=>l,bn:()=>c,cK:()=>s});const n="jetpack_social_image_generator_settings",c="jetpack_social_utm_settings",s="jetpack-social-note",i="jetpack_social_notes_config",r="jetpack-social_show_pricing_page",l=[]},14967:(e,t,a)=>{"use strict";a.d(t,{g:()=>r});var n=a(3582),c=a(47143),s=a(27723),i=a(70407);const __=s.__;async function r(){const{addEntities:e,receiveEntityRecords:t,finishResolution:a}=(0,c.dispatch)(n.store),s=(0,i.nE)()?.api_paths?.socialToggleBase;(0,c.select)(n.store).getEntitiesConfig("jetpack/v4").some((({name:e})=>e===s))||(await e([{kind:"jetpack/v4",name:s,baseURL:`/jetpack/v4/${s}`,label:__("Social Settings","jetpack-publicize-pkg")}]),await t("jetpack/v4",s,{publicize:(0,i.nE)()?.is_publicize_enabled},!0),await a("getEntityRecord",["jetpack/v4",s]));const r=(0,c.select)(n.store).getEntitiesConfig("wpcom/v2");r.some((({name:e})=>"publicize/services"===e))||(await e([{kind:"wpcom/v2",name:"publicize/services",baseURL:"/wpcom/v2/publicize/services",label:__("Publicize services","jetpack-publicize-pkg")}]),await t("wpcom/v2","publicize/services",(0,i.nE)()?.supported_services,!0),await a("getEntityRecords",["wpcom/v2","publicize/services"])),r.some((({name:e})=>"publicize/shares-data"===e))||await e([{kind:"wpcom/v2",name:"publicize/shares-data",baseURL:"/wpcom/v2/publicize/shares-data",label:__("Publicize shares data","jetpack-publicize-pkg")}]),r.some((({name:e})=>"publicize/scheduled-actions"===e))||await e([{kind:"wpcom/v2",name:"publicize/scheduled-actions",baseURL:"/wpcom/v2/publicize/scheduled-actions",label:__("Publicize scheduled actions","jetpack-publicize-pkg")}])}},31963:(e,t,a)=>{"use strict";a.d(t,{Af:()=>d,M_:()=>p});var n=a(47143),c=a(51371),s=a(62491),i=a(14967),r=a(708),l=a(75772),o=a(62146);const d="jetpack-social-plugin",u={reducer:r.A,actions:s.A,selectors:o.A,resolvers:l.Ay,initialState:(0,c.nE)()?.store_initial_state},p=(0,n.createReduxStore)(d,u);(0,n.register)(p),(0,i.g)()},62015:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var n=a(54118);const c=(e={connections:[]},t)=>{switch(t.type){case n.xW:return{...e,isConnectionsModalOpen:t.isOpen};case n.Bc:return{...e,connections:[...e.connections,t.connection]};case n.dA:return{...e,connections:t.connections};case n.nC:return{...e,connections:e.connections.filter((({connection_id:e})=>e!==t.connectionId))};case n.WH:{const a=new Set(e.deletingConnections);return t.deleting?a.add(t.connectionId):a.delete(t.connectionId),{...e,deletingConnections:[...a]}}case n.Nv:return{...e,reconnectingAccount:t.reconnectingAccount};case n.dw:return{...e,connections:e.connections.map((e=>e.connection_id===t.connectionId?{...e,...t.data}:e))};case n._6:{const a=new Set(e.updatingConnections);return t.updating?a.add(t.connectionId):a.delete(t.connectionId),{...e,updatingConnections:[...a]}}case n.WB:{const a=t.requestType||n.b5;return{...e,abortControllers:{...e.abortControllers,[a]:[...e.abortControllers?.[a]||[],t.abortController]}}}case n.Wp:{const a=t.requestType||n.b5;return{...e,abortControllers:{...e.abortControllers,[a]:[]}}}case n.BN:return{...e,keyringResult:t.keyringResult};case n.af:return{...e,connections:e.connections.map((e=>e.connection_id===t.connectionId?{...e,enabled:!e.enabled}:e))}}return e}},708:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var n=a(47143),c=a(62015),s=a(46450),i=a(53202);const r=(0,n.combineReducers)({connectionData:c.A,shareStatus:i.q,sharePost:s.a})},46450:(e,t,a)=>{"use strict";a.d(t,{a:()=>c});var n=a(54118);function c(e={},t){return t.type===n.Mc?{...e,isModalOpen:t.isOpen}:e}},53202:(e,t,a)=>{"use strict";a.d(t,{q:()=>c});var n=a(54118);function c(e={},t){switch(t.type){case n.fz:return{...e,[t.postId]:{shares:[],...e?.[t.postId],loading:t.loading??!0}};case n.aF:return{...e,[t.postId]:{shares:[],...e?.[t.postId],polling:t.polling??!0}};case n._y:return{...e,[t.postId]:{...e?.[t.postId],...t.shareStatus,loading:!1}};case n.M6:return{...e,isModalOpen:t.isOpen}}return e}},75772:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>u});var n=a(97999),c=a(1455),s=a.n(c),i=a(43656),r=a(27844),l=a(59061),o=a(38716),d=a(72451);const u={getConnections:function(){return function({dispatch:e,registry:t}){const a=t.select(i.store);if(!a.getCurrentPostId())return;const n=a.getEditedPostAttribute("jetpack_publicize_connections");Array.isArray(n)?e((0,o.setConnections)(n||[])):console.error("Invalid connections data received from the post meta.",n)}},getPostShareStatus:function(e){return async({dispatch:t,registry:a})=>{const c=e||a.select(i.store).getCurrentPostId();if((0,n.siteHasFeature)(r.q.SHARE_STATUS))try{t((0,d.fetchPostShareStatus)(c));let e=await s()({path:`/wpcom/v2/publicize/share-status?post_id=${c}`});e=(0,l.xA)(e),t((0,d.receivePostShareStaus)(e,c))}catch{t((0,d.fetchPostShareStatus)(c,!1))}}}}},98789:(e,t,a)=>{"use strict";a.r(t),a.d(t,{canUserManageConnection:()=>R,getAbortControllers:()=>k,getBrokenConnections:()=>d,getConnectionById:()=>o,getConnections:()=>l,getConnectionsByService:()=>u,getDeletingConnections:()=>w,getDisabledConnections:()=>v,getEnabledConnections:()=>g,getFailedConnections:()=>m,getKeyringResult:()=>E,getMustReauthConnections:()=>h,getReconnectingAccount:()=>f,getUpdatingConnections:()=>b,hasConnections:()=>p,isBlueskyAccountAlreadyConnected:()=>x,isConnectionsModalOpen:()=>j,isMastodonAccountAlreadyConnected:()=>_});var n=a(97999),c=a(3582),s=a(47143),i=a(54118),r=a(75238);function l(e){return e.connectionData?.connections??r.Ml}function o(e,t){return l(e).find((e=>e.connection_id===t))}const d=(0,s.createSelector)((e=>l(e).filter((e=>"broken"===e.status))),(e=>[e.connectionData?.connections])),u=(0,s.createSelector)(((e,t)=>l(e).filter((({service_name:e})=>e===t))),(e=>[e.connectionData?.connections]));function p(e){return l(e).length>0}const m=(0,s.createSelector)((e=>l(e).filter((e=>"broken"===e.status))),(e=>[e.connectionData?.connections])),h=(0,s.createSelector)((e=>l(e).filter((e=>"must_reauth"===e.status)).map((e=>e.service_name))),(e=>[e.connectionData?.connections])),g=(0,s.createSelector)((e=>l(e).filter((e=>e.enabled))),(e=>[e.connectionData?.connections])),v=(0,s.createSelector)((e=>l(e).filter((e=>!e.enabled))),(e=>[e.connectionData?.connections]));function w(e){return e.connectionData?.deletingConnections??r.Ml}function b(e){return e.connectionData?.updatingConnections??r.Ml}function f(e){return e.connectionData?.reconnectingAccount}function k(e,t=i.b5){return e.connectionData?.abortControllers?.[t]??r.Ml}function _(e,t){return u(e,"mastodon").some((e=>e.external_handle===t))}function x(e,t){return u(e,"bluesky").some((e=>e.external_handle===t))}function E(e){return e.connectionData?.keyringResult}function j(e){return e.connectionData?.isConnectionsModalOpen??!1}const R=(0,s.createRegistrySelector)((e=>(t,a)=>{const s="string"==typeof a?o(t,a):a,{current_user:i}=(0,n.getScriptData)().user;if(i.wpcom?.ID===s.wpcom_user_id)return!0;const{getUser:r}=e(c.store);return r(i.id)?.capabilities?.edit_others_posts??!1}))},62146:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(98789),c=a(74998),s=a(96140),i=a(17320),r=a(49424),l=a(77357);const o={...n,...r,...i,...a(69356),...a(30513),...s,...l,...c}},74998:(e,t,a)=>{"use strict";a.r(t),a.d(t,{getScheduledSharesForPost:()=>i,isDeletingScheduledShare:()=>o,isFetchingScheduledSharesForPost:()=>l,isSavingScheduledShare:()=>r});var n=a(3582),c=a(47143),s=a(75238);const i=(0,c.createRegistrySelector)((e=>(t,a)=>e(n.store).getEntityRecords("wpcom/v2","publicize/scheduled-actions",{post_id:a})??s.Ml)),r=(0,c.createRegistrySelector)((e=>()=>e(n.store).isSavingEntityRecord("wpcom/v2","publicize/scheduled-actions",void 0))),l=(0,c.createRegistrySelector)((e=>(t,a)=>{const{isResolving:c}=e(n.store);return c("getEntityRecords",["wpcom/v2","publicize/scheduled-actions",{post_id:a}])})),o=(0,c.createRegistrySelector)((e=>(t,a)=>e(n.store).isDeletingEntityRecord("wpcom/v2","publicize/scheduled-actions",a)))},96140:(e,t,a)=>{"use strict";a.r(t),a.d(t,{getService:()=>r,getServicesBy:()=>o,getServicesList:()=>i,isFetchingServicesList:()=>l});var n=a(3582),c=a(47143),s=a(75238);const i=(0,c.createRegistrySelector)((e=>()=>e(n.store).getEntityRecords("wpcom/v2","publicize/services")??s.Ml));function r(e,t){return i().find((e=>e.id===t))}const l=(0,c.createRegistrySelector)((e=>()=>{const{isResolving:t}=e(n.store);return t("getEntityRecords",["wpcom/v2","publicize/services"])}));function o(e,t,a){return i().filter((e=>e[t]===a))}},17320:(e,t,a)=>{"use strict";function n(e){return e.sharePost?.isModalOpen??!1}a.r(t),a.d(t,{isSharePostModalOpen:()=>n})},49424:(e,t,a)=>{"use strict";a.r(t),a.d(t,{getPostShareStatus:()=>i,isShareStatusModalOpen:()=>r});var n=a(47143),c=a(43656),s=a(75238);const i=(0,n.createRegistrySelector)((e=>(t,a)=>{const n=a||e(c.store).getCurrentPostId();return t.shareStatus?.[n]??{shares:s.Ml}}));function r(e){return e.shareStatus?.isModalOpen??!1}},77357:(e,t,a)=>{"use strict";a.r(t),a.d(t,{getSharedPostsCount:()=>o,getTotalSharesCount:()=>l,isShareLimitEnabled:()=>d});var n=a(3582),c=a(47143),s=a(70407);const i={},r=e=>e(n.store).getEntityRecord("wpcom/v2","publicize/shares-data")||(0,s.nE)().shares_data||i,l=(0,c.createRegistrySelector)((e=>()=>{const t=r(e),a=(e=>e?.publicized_count??0)(t)+(e=>e?.to_be_publicized_count??0)(t);return Math.max(a,0)})),o=(0,c.createRegistrySelector)((e=>()=>{const t=r(e);return t?.shared_posts_count??0})),d=(0,c.createRegistrySelector)((e=>()=>{const t=r(e);return t?.is_share_limit_enabled??!1}))},69356:(e,t,a)=>{"use strict";a.r(t),a.d(t,{getSocialModuleSettings:()=>i,isSavingSocialModuleSettings:()=>r});var n=a(3582),c=a(47143),s=a(70407);const i=(0,c.createRegistrySelector)((e=>()=>{const{api_paths:t,is_publicize_enabled:a}=(0,s.nE)();return e(n.store).getEntityRecord("jetpack/v4",t.socialToggleBase)??{publicize:a}})),r=(0,c.createRegistrySelector)((e=>()=>{const{socialToggleBase:t}=(0,s.nE)().api_paths;return e(n.store).isSavingEntityRecord("jetpack/v4",t,void 0)}))},30513:(e,t,a)=>{"use strict";a.r(t),a.d(t,{getSocialSettings:()=>l,isSavingSiteSettings:()=>r});var n=a(97999),c=a(3582),s=a(47143),i=a(70407);const r=(0,s.createRegistrySelector)((e=>()=>e(c.store).isSavingEntityRecord("root","site",void 0))),l=(0,s.createRegistrySelector)((e=>()=>{const t=(0,n.currentUserCan)("manage_options")?e(c.store).getEntityRecord("root","site"):null,{settings:a}=(0,i.nE)();return t?{showPricingPage:t["jetpack-social_show_pricing_page"]??a.showPricingPage,socialImageGenerator:{...a.socialImageGenerator,...t.jetpack_social_image_generator_settings},utmSettings:{...a.utmSettings,...t.jetpack_social_utm_settings},socialNotes:{enabled:Boolean(t["jetpack-social-note"]),config:{...a.socialNotes.config,...t.jetpack_social_notes_config}}}:a}))},27844:(e,t,a)=>{"use strict";a.d(t,{q:()=>n});const n={ENHANCED_PUBLISHING:"social-enhanced-publishing",IMAGE_GENERATOR:"social-image-generator",SHARE_STATUS:"social-share-status"}},70407:(e,t,a)=>{"use strict";a.d(t,{Jc:()=>s.Jc,Oc:()=>n.Oc,nE:()=>s.nE,pq:()=>s.pq,qT:()=>i.q,x9:()=>c.x});var n=a(93530),c=a(5367),s=a(51371),i=a(27844)},5367:(e,t,a)=>{"use strict";a.d(t,{x:()=>c});var n=a(51119);const c=(e,t)=>{const a=new n.A;let c;a.open(e,null,"toolbar=0,location=0,status=0,menubar=0,"+a.getScreenCenterSpecs(780,700)),a.once("close",(()=>{t(c?.ID?c:{})})),a.on("message",(e=>{c=e?.data}))}},51371:(e,t,a)=>{"use strict";a.d(t,{Jc:()=>i,nE:()=>c,pq:()=>s});var n=a(97999);function c(){return(0,n.getScriptData)()?.social}function s(){return(0,n.siteHasFeature)("social-enhanced-publishing")}function i(){return(0,n.getAdminUrl)("admin.php?page=jetpack-social")}},59061:(e,t,a)=>{"use strict";function n(e){return e&&"shares"in e&&e.done&&e.shares.sort(((e,t)=>t.timestamp-e.timestamp)),e}function c(e){return t=>t.service_name===e.service&&(e.external_id?t.external_id===e.external_id:t.connection_id===e.connection_id.toString())}function s(e,t){return e.service===t.service&&(e.connection_id===t.connection_id||!(!e.external_id&&!t.external_id)&&e.external_id===t.external_id)}a.d(t,{Ri:()=>s,eJ:()=>c,xA:()=>n})},31704:(e,t,a)=>{"use strict";a.d(t,{D:()=>s});var n=a(47143),c=a(43656);function s(){return(0,n.useSelect)((e=>"jetpack-social-note"===e(c.store).getCurrentPostType()),[])}},93530:(e,t,a)=>{"use strict";a.d(t,{Oc:()=>i});var n=a(2204),c=a(50001);const s=255;function i(){const{enabledConnections:e}=(0,c.A)();return function(e=[]){const t=e.map((e=>n.mB[e]?.charLimit)).filter((e=>void 0!==e));return t.length>0?Math.min(...t):s}(e.map((e=>e.service_name)))}},85985:(e,t,a)=>{"use strict";a.d(t,{$i:()=>n.A,FB:()=>i.A,GE:()=>c.A,Oc:()=>d.Oc,Sy:()=>s.Sy,UB:()=>d.UB,Uq:()=>d.Uq,W4:()=>r.W4,_6:()=>o.A,d9:()=>s.d9,st:()=>l.A,tu:()=>r.tu});a(52810);var n=a(34972),c=a(54815),s=(a(41409),a(62634)),i=a(60703),r=(a(82034),a(65595),a(53265)),l=a(73489),o=(a(47119),a(58406),a(76923),a(30335),a(88290),a(9061),a(25929)),d=a(5765)},5765:(e,t,a)=>{"use strict";a.d(t,{Oc:()=>l,UB:()=>i,Uq:()=>r});var n=a(98490),c=a.n(n);const s="jetpack-editor-action";function i(){return new URL(window.location.href).searchParams.get(s)}function r(){const e=new URL(window.location.href);e.searchParams.delete(s),window.history.replaceState(null,"",e.toString())}function l(e,t){c()((()=>{const a=i();if(e!==a)return;t()&&r()}))}},52810:(e,t,a)=>{"use strict";a(38377).T["Jetpack Green 40"]},82201:(e,t,a)=>{"use strict";a.d(t,{j:()=>n.A});var n=a(15877);a(30984)},15877:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n=a(67142),c=a(51609),s=a.n(c);const i=()=>s().createElement(n.A,{className:"jetpack-editor-panel-logo",height:16,logoColor:"#1E1E1E",showText:!1})},30984:(e,t,a)=>{"use strict";a(56427),a(27723),a(51609)},30335:(e,t,a)=>{"use strict";a(86087)},34972:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});const n="Jetpack_Editor_Initial_State";function c(){return"object"==typeof window?window?.[n]??null:null}},60703:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var n=a(34972);function c(e){const t=(0,n.A)(),a=t?.available_blocks?.[e]?.available??!1,c=t?.available_blocks?.[e]?.unavailable_reason??"unknown";return{available:a,...!a&&{details:t?.available_blocks?.[e]?.details??[],unavailableReason:c}}}},54815:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var n=a(97999);function c(){return window&&window.Jetpack_Editor_Initial_State&&window.Jetpack_Editor_Initial_State.siteFragment?window.Jetpack_Editor_Initial_State.siteFragment:(0,n.getScriptData)()?.site?.suffix??null}},73489:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(90372),c=a(39384),s=a(86087);const{tracks:i}=n.A,{recordEvent:r}=i,l=({pageViewEventName:e=null,pageViewNamespace:t="jetpack",pageViewSuffix:a="page_view",pageViewEventProperties:l={}}={})=>{const[o,d]=(0,s.useState)(!1),{isUserConnected:u,isRegistered:p,userConnectionData:m={}}=(0,c.useConnection)(),{wpcomUser:{login:h,ID:g}={},blogId:v}=m.currentUser||{},w=(0,s.useCallback)((async(e,t={})=>{u&&g&&h&&r(e,t)}),[u,g,h]);return(0,s.useEffect)((()=>{u&&g&&h&&v&&n.A.initialize(g,h,{blog_id:v})}),[v,g,h,u]),(0,s.useEffect)((()=>{const n=e?`${t}_${e}_${a}`:null;p&&n&&(o||(w(n,l),d(!0)))}),[o,t,e,a,p,l,w]),{recordEvent:w,tracks:i}}},47119:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n=a(47143),c=a(86087),s=a(66087);function i(e=null,t=s.noop){const[a,i]=(0,c.useState)(!1),{isAutosaveablePost:r,isDirtyPost:l,currentPost:o}=(0,n.useSelect)((e=>{const t=e("core/editor");return{isAutosaveablePost:t.isEditedPostAutosaveable(),isDirtyPost:t.isEditedPostDirty(),currentPost:t.getCurrentPost()}}),[]),d=Object.keys(o).length>0,u=(0,n.useSelect)((e=>!!window.wp?.customize||!!e("core/edit-widgets"))),p=(0,n.dispatch)("core/editor").savePost,m=(0,n.useSelect)((e=>e("core").__experimentalGetDirtyEntityRecords())),h=async e=>{e.preventDefault(),d?l&&r&&await p(e):await(async()=>{for(let e=0;e<m.length;e++)await(0,n.dispatch)("core").saveEditedEntityRecord(m[e].kind,m[e].name,m[e].key)})()};return{autosave:h,autosaveAndRedirect:async n=>{n.preventDefault(),a||(i(!0),h(n).then((()=>{e&&function(e,t,a=!1){t&&t(e),a?window.open(e,"_blank"):window.top.location.href=e}(e,t,u)})))},isRedirecting:a}}},76923:(e,t,a)=>{"use strict";a(47143),a(86087),a(88290)},58406:(e,t,a)=>{"use strict";a(86087)},25929:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(47143),c=a(52619),s=a(53265),i=a(47119);const r=()=>{},l="a8c.wpcom-block-editor.openCheckoutModal";function o(e,t=r){const{checkoutUrl:a,planData:o}=(0,n.useSelect)((t=>{const a=t("core/editor"),n=t("wordpress-com/plans"),{id:c,type:i}=a.getCurrentPost(),r=n&&n.getPlan(e);return{checkoutUrl:(0,s.Q4)({plan:r,planSlug:e,postId:c,postType:i}),planData:r}}),[e]),{autosave:d,autosaveAndRedirect:u,isRedirecting:p}=(0,i.A)(a,t);return[a,async e=>{if(e.preventDefault(),(0,c.hasAction)(l))return e.preventDefault(),d(e),void(0,c.doAction)(l,{products:[o]});u(e)},p,o]}},9520:(e,t,a)=>{"use strict";var n=a(46941),c=a.n(n);window,c()("shared-extension-utils:connection")},9061:(e,t,a)=>{"use strict";a(9520)},27105:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>u,E9:()=>d});var n=a(47143),c=a(62634),s=a(94478),i=a(88290);const r="SET_JETPACK_MODULES";function l(e){return d({isLoading:e})}function o(e,t){return{type:"SET_MODULE_UPDATING",name:e,isUpdating:t}}function d(e){return{type:r,options:e}}const u={updateJetpackModuleStatus:function*(e){try{yield o(e.name,!0),yield(0,s.sB)(e);const t=yield(0,s.wz)();return yield d({data:t}),!0}catch{const e=(0,n.select)(i.F).getJetpackModules();return yield d(e),!1}finally{yield o(e.name,!1)}},setJetpackModules:d,fetchModules:function*(){if((0,c.Sy)())return!0;try{yield l(!0);const e=yield(0,s.wz)();return yield d({data:e}),!0}catch{const e=(0,n.select)(i.F).getJetpackModules();return yield d(e),!1}finally{yield l(!1)}}}},94478:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>o,sB:()=>l,wz:()=>r});var n=a(1455),c=a.n(n);const s="FETCH_JETPACK_MODULES",i="UPDATE_JETPACK_MODULE_STATUS",r=()=>({type:s}),l=e=>({type:i,settings:e}),o={[s]:function(){return c()({path:"/jetpack/v4/module/all",method:"GET"})},[i]:function({settings:e}){return c()({path:`/jetpack/v4/module/${e.name}/active`,method:"POST",data:{active:e.active}})}}},88290:(e,t,a)=>{"use strict";a.d(t,{F:()=>o});var n=a(47143),c=a(27105),s=a(94478),i=a(38862),r=a(62701),l=a(31640);const o="jetpack-modules",d=(0,n.createReduxStore)(o,{reducer:i.A,actions:c.Ay,controls:s.Ay,resolvers:r.A,selectors:l.A});(0,n.register)(d);const u=window?.Initial_State?.getModules||window?.Jetpack_Editor_Initial_State?.modules||null;null!==u&&(0,n.dispatch)(o).setJetpackModules({data:{...u}})},38862:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});const n={isLoading:!1,isUpdating:{},data:{}},c=(e=n,t)=>{switch(t.type){case"SET_JETPACK_MODULES":return{...e,...t.options};case"SET_MODULE_UPDATING":return{...e,isUpdating:{...e.isUpdating,[t.name]:t.isUpdating}}}return e}},62701:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(27105),c=a(94478);const s={getJetpackModules:function*(){try{const e=yield(0,c.wz)();if(e)return(0,n.E9)({data:e})}catch(e){console.error(e)}}}},31640:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var n=a(62634);const c={getJetpackModules:e=>e.data,isModuleActive:(e,t)=>(0,n.Sy)()||(e?.data?.[t]?.activated??!1),areModulesLoading:e=>e.isLoading??!1,isModuleUpdating:(e,t)=>e?.isUpdating?.[t]??!1}},53265:(e,t,a)=>{"use strict";a.d(t,{Q4:()=>o,W4:()=>d,tu:()=>u});var n=a(27723),c=a(93832),s=a(66087),i=a(60703),r=a(54815),l=a(62634);const __=n.__;function o({planSlug:e,plan:t,postId:a,postType:n}){const i=(0,s.startsWith)(e,"jetpack_")?e:(0,s.get)(t,["path_slug"]),o=(void 0===n?()=>{const e=new URLSearchParams(window.location.search);return(0,c.addQueryArgs)(window.location.protocol+`//${(0,r.A)().replace("::","/")}/wp-admin/site-editor.php`,{postId:e.get("postId"),postType:e.get("postType"),plan_upgraded:1})}:()=>{const e=["page","post"].includes(n)?"":"edit";return(0,l.Sy)()?(0,c.addQueryArgs)("/"+(0,s.compact)([e,n,(0,r.A)(),a]).join("/"),{plan_upgraded:1}):(0,c.addQueryArgs)(window.location.protocol+`//${(0,r.A)().replace("::","/")}/wp-admin/post.php`,{action:"edit",post:a,plan_upgraded:1})})();return(0,l.d9)()?(0,c.addQueryArgs)(`https://wordpress.com/plans/${(0,r.A)()}`,{redirect_to:o,customerType:"business"}):i&&(0,c.addQueryArgs)(`https://wordpress.com/checkout/${(0,r.A)()}/${i}`,{redirect_to:o})}function d(e){if(!e)return!1;const t=/^jetpack\//.test(e)?e.substr(8,e.length):e,{available:a,unavailableReason:n}=(0,i.A)(t);return!a&&"missing_plan"===n}function u(e){if(!e)return!1;const t=/^jetpack\//.test(e)?e.substr(8,e.length):e,{details:a,unavailableReason:n}=(0,i.A)(t);return function(e,t){return"missing_plan"===e&&t.required_plan}(n,a)}__("Upgrade your plan to use video covers","jetpack-publicize-pkg"),__("Upgrade your plan to upload audio","jetpack-publicize-pkg")},82034:(e,t,a)=>{"use strict";a(92279)},41409:(e,t,a)=>{"use strict";a(97999)},62634:(e,t,a)=>{"use strict";function n(){return"object"==typeof window&&"string"==typeof window._currentSiteType?window._currentSiteType:null}function c(){return"simple"===n()}function s(){return"atomic"===n()}a.d(t,{Sy:()=>c,d9:()=>s})},65595:(e,t,a)=>{"use strict";a(96072),a(29491)},84705:(e,t,a)=>{"use strict";a.d(t,{d6:()=>n.d});var n=a(58992);a(91135)},91135:(e,t,a)=>{"use strict";a.d(t,{$:()=>n});const n=[{name:"amazon",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M13.582 8.182c-1.648.185-3.802.308-5.344.984-1.781.769-3.03 2.337-3.03 4.644 0 2.953 1.86 4.429 4.253 4.429 2.02 0 3.125-.477 4.685-2.065.516.747.685 1.109 1.629 1.894a.59.59 0 0 0 .672-.066l.006.006c.567-.505 1.599-1.401 2.18-1.888.231-.188.19-.496.009-.754-.52-.718-1.072-1.303-1.072-2.634V8.305c0-1.876.133-3.599-1.249-4.891C15.23 2.369 13.422 2 12.04 2 9.336 2 6.318 3.01 5.686 6.351c-.068.355.191.542.423.594l2.754.298c.258-.013.445-.266.494-.523.236-1.151 1.2-1.706 2.284-1.706.584 0 1.249.215 1.595.738.398.584.346 1.384.346 2.061zm-.533 5.906c-.451.8-1.169 1.291-1.967 1.291-1.09 0-1.728-.83-1.728-2.061 0-2.42 2.171-2.86 4.227-2.86v.615c.001 1.108.027 2.031-.532 3.015m7.634 5.251C18.329 21.076 14.917 22 11.979 22c-4.118 0-7.826-1.522-10.632-4.057-.22-.199-.024-.471.241-.317 3.027 1.762 6.771 2.823 10.639 2.823 2.608 0 5.476-.541 8.115-1.66.397-.169.73.262.341.55m.653 1.704c-.194.163-.379.076-.293-.139.284-.71.92-2.298.619-2.684s-1.99-.183-2.749-.092c-.23.027-.266-.173-.059-.319 1.348-.946 3.555-.673 3.811-.356.26.32-.066 2.533-1.329 3.59"})))},{name:"behance",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M7.799 5.698c.589 0 1.12.051 1.606.156q.722.155 1.241.507.516.351.804.938c.188.387.281.871.281 1.443q0 .93-.421 1.551-.424.62-1.255 1.014 1.133.328 1.689 1.146.559.822.557 1.975 0 .935-.359 1.612a3.14 3.14 0 0 1-.973 1.114q-.613.432-1.399.637A6.1 6.1 0 0 1 7.963 18H2V5.698zm-.35 4.97q.721 0 1.192-.345.465-.344.463-1.119 0-.43-.152-.707a1.1 1.1 0 0 0-.416-.427 1.7 1.7 0 0 0-.596-.216 3.6 3.6 0 0 0-.697-.06H4.709v2.874zm.151 5.237q.401.001.759-.077c.243-.053.457-.137.637-.261.182-.12.332-.283.441-.491q.164-.31.163-.798-.002-.948-.533-1.357c-.356-.27-.83-.404-1.413-.404H4.709v3.388zm8.562-.041q.552.538 1.583.538.74 0 1.277-.374c.354-.248.571-.514.654-.79h2.155c-.347 1.072-.872 1.838-1.589 2.299-.708.463-1.572.693-2.58.693q-1.05 0-1.899-.337a4 4 0 0 1-1.439-.958 4.4 4.4 0 0 1-.904-1.484 5.4 5.4 0 0 1-.32-1.899q0-1 .329-1.863a4.4 4.4 0 0 1 .933-1.492q.607-.63 1.444-.994a4.6 4.6 0 0 1 1.857-.363q1.131-.001 1.98.44a3.94 3.94 0 0 1 1.389 1.181 4.8 4.8 0 0 1 .783 1.69q.24.947.171 1.983h-6.428c-.001.706.237 1.372.604 1.73m2.811-4.68c-.291-.321-.783-.496-1.384-.496q-.585 0-.973.2a2 2 0 0 0-.621.491 1.8 1.8 0 0 0-.328.628 2.7 2.7 0 0 0-.111.587h3.98c-.058-.625-.271-1.085-.563-1.41m-3.916-3.446h4.985V6.524h-4.985z"})))},{name:"blogger-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19.779 9.904h-.981l-.021.001a1.163 1.163 0 0 1-1.16-1.079l-.001-.013A5.813 5.813 0 0 0 11.803 3H8.871a5.813 5.813 0 0 0-5.813 5.813v6.375a5.813 5.813 0 0 0 5.813 5.813h6.257a5.814 5.814 0 0 0 5.813-5.813l.002-4.121a1.164 1.164 0 0 0-1.164-1.163M8.726 7.713h3.291a1.117 1.117 0 1 1 0 2.234H8.726a1.117 1.117 0 1 1 0-2.234m6.601 8.657H8.72a1.057 1.057 0 1 1 0-2.114h6.607a1.057 1.057 0 1 1 0 2.114"})))},{name:"blogger",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M14.722 14.019a.654.654 0 0 1-.654.654H9.977a.654.654 0 0 1 0-1.308h4.091c.361 0 .654.293.654.654m-4.741-3.321h2.038a.692.692 0 0 0 0-1.384H9.981a.692.692 0 0 0 0 1.384M21 5v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2m-3.456 6.39a.72.72 0 0 0-.72-.72h-.607l-.013.001a.72.72 0 0 1-.718-.668l-.001-.008a3.6 3.6 0 0 0-3.599-3.599H10.07a3.6 3.6 0 0 0-3.599 3.599v3.947a3.6 3.6 0 0 0 3.599 3.599h3.874a3.6 3.6 0 0 0 3.599-3.599z"})))},{name:"bluesky",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M21.2 3.3c-.5-.2-1.4-.5-3.6 1C15.4 6 12.9 9.2 12 11c-.9-1.8-3.4-5-5.7-6.7-2.2-1.6-3-1.3-3.6-1S2 4.6 2 5.1s.3 4.7.5 5.4c.7 2.3 3.1 3.1 5.3 2.8-3.3.5-6.2 1.7-2.4 5.9 4.2 4.3 5.7-.9 6.5-3.6.8 2.7 1.7 7.7 6.4 3.6 3.6-3.6 1-5.4-2.3-5.9 2.2.2 4.6-.5 5.3-2.8.4-.7.7-4.8.7-5.4 0-.5-.1-1.5-.8-1.8"})))},{name:"codepen",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"m22.016 8.84-.007-.037q-.006-.037-.015-.072-.007-.022-.013-.042l-.023-.062-.02-.042a.4.4 0 0 0-.03-.057l-.025-.038-.035-.052-.03-.037q-.021-.026-.043-.045-.015-.018-.035-.035a.4.4 0 0 0-.048-.04l-.037-.03-.015-.012-9.161-6.096a.86.86 0 0 0-.955 0L2.359 8.237l-.015.012-.038.028-.048.04a.638.638 0 0 0-.078.082q-.018.018-.03.037-.018.026-.035.052l-.025.038q-.016.031-.03.059l-.02.041a1 1 0 0 0-.034.106q-.01.034-.016.071-.003.02-.006.037a1 1 0 0 0-.009.114v6.093q0 .056.008.112l.007.038q.006.035.015.072a.2.2 0 0 0 .013.04q.01.032.022.063l.02.04a.4.4 0 0 0 .055.096l.035.052.03.037.042.045.035.035q.023.02.048.04l.038.03.013.01 9.163 6.095a.858.858 0 0 0 .959.004l9.163-6.095.015-.01q.02-.015.037-.03l.048-.04q.02-.017.035-.035.025-.024.043-.045l.03-.037.035-.052.025-.038a.4.4 0 0 0 .03-.058l.02-.04.023-.063c.003-.013.01-.027.013-.04q.009-.037.015-.072l.007-.037q.006-.062.007-.117V8.954a1 1 0 0 0-.008-.114m-9.154-4.376 6.751 4.49-3.016 2.013-3.735-2.492zm-1.724 0v4.009l-3.735 2.494-3.014-2.013zm-7.439 6.098L5.853 12l-2.155 1.438zm7.439 8.974-6.749-4.491 3.015-2.011 3.735 2.492zM12 14.035 8.953 12 12 9.966 15.047 12zm.862 5.501v-4.009l3.735-2.492 3.016 2.011zm7.441-6.098L18.147 12l2.156-1.438z"})))},{name:"deezer",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M20.129 3.662c.222-1.287.548-2.096.909-2.098h.001c.673.002 1.219 2.809 1.219 6.274s-.546 6.274-1.22 6.274c-.276 0-.531-.477-.736-1.276-.324 2.926-.997 4.937-1.776 4.937-.603 0-1.144-1.208-1.507-3.114-.248 3.624-.872 6.195-1.602 6.195-.458 0-.875-1.019-1.184-2.678C13.861 21.6 13.003 24 12.002 24s-1.861-2.399-2.231-5.824c-.307 1.659-.724 2.678-1.184 2.678-.73 0-1.352-2.571-1.602-6.195-.363 1.905-.903 3.114-1.507 3.114-.778 0-1.452-2.011-1.776-4.937-.204.802-.46 1.276-.736 1.276-.674 0-1.22-2.809-1.22-6.274s.546-6.274 1.22-6.274c.362 0 .685.812.91 2.098.357-2.22.94-3.662 1.6-3.662.784 0 1.463 2.04 1.784 5.002.314-2.156.791-3.53 1.325-3.53.749 0 1.385 2.703 1.621 6.474.443-1.933 1.085-3.146 1.795-3.146s1.352 1.214 1.795 3.146c.237-3.771.872-6.474 1.621-6.474.533 0 1.009 1.374 1.325 3.53.321-2.962 1-5.002 1.784-5.002.658 0 1.244 1.443 1.603 3.662M0 7.221c0-1.549.31-2.805.692-2.805s.692 1.256.692 2.805-.31 2.805-.692 2.805S0 8.77 0 7.221m22.616 0c0-1.549.31-2.805.692-2.805S24 5.672 24 7.221s-.31 2.805-.692 2.805-.692-1.256-.692-2.805"})))},{name:"discord",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M20.33 4.523A20 20 0 0 0 15.379 3a14 14 0 0 0-.634 1.289 18.4 18.4 0 0 0-5.495 0A14 14 0 0 0 8.615 3 20 20 0 0 0 3.66 4.527C.527 9.163-.323 13.684.102 18.141a20 20 0 0 0 6.073 3.049 14.7 14.7 0 0 0 1.301-2.097 13 13 0 0 1-2.048-.978q.258-.189.502-.378a14.27 14.27 0 0 0 12.142 0q.247.202.502.378a13 13 0 0 1-2.052.98 14.5 14.5 0 0 0 1.301 2.095 19.9 19.9 0 0 0 6.076-3.047c.498-5.168-.851-9.648-3.568-13.62M8.013 15.4c-1.183 0-2.161-1.074-2.161-2.395S6.796 10.6 8.01 10.6s2.183 1.083 2.163 2.405S9.22 15.4 8.013 15.4m7.974 0c-1.186 0-2.16-1.074-2.16-2.395s.944-2.405 2.16-2.405 2.178 1.083 2.157 2.405-.951 2.395-2.158 2.395"})))},{name:"dribbble",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10m8.434-8.631c-.292-.092-2.644-.794-5.32-.365 1.117 3.07 1.572 5.57 1.659 6.09a8.56 8.56 0 0 0 3.661-5.725m-5.098 6.507c-.127-.749-.623-3.361-1.822-6.477l-.056.019c-4.818 1.679-6.547 5.02-6.701 5.334A8.5 8.5 0 0 0 12 20.555a8.5 8.5 0 0 0 3.336-.679m-9.682-2.152c.193-.331 2.538-4.213 6.943-5.637q.167-.054.337-.102a29 29 0 0 0-.692-1.45c-4.266 1.277-8.405 1.223-8.778 1.216a8.497 8.497 0 0 0 2.19 5.973m-2.015-7.46c.382.005 3.901.02 7.897-1.041a55 55 0 0 0-3.167-4.94 8.57 8.57 0 0 0-4.73 5.981m6.359-6.555a46 46 0 0 1 3.187 5c3.037-1.138 4.323-2.867 4.477-3.085a8.51 8.51 0 0 0-7.664-1.915m8.614 2.903c-.18.243-1.612 2.078-4.77 3.367a27 27 0 0 1 .751 1.678c2.842-.357 5.666.215 5.948.275a8.5 8.5 0 0 0-1.929-5.32"})))},{name:"dropbox",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 6.134 6.069 9.797 2 6.54l5.883-3.843zm-10 6.92 5.883 3.843L12 13.459 6.069 9.797zm10 .405 4.116 3.439L22 13.054l-4.069-3.257zM22 6.54l-5.884-3.843L12 6.134l5.931 3.663zm-9.989 7.66-4.129 3.426-1.767-1.153v1.291l5.896 3.539 5.897-3.539v-1.291l-1.769 1.153z"})))},{name:"eventbrite",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M18.041 3.931 5.959 3A2.96 2.96 0 0 0 3 5.959v12.083A2.96 2.96 0 0 0 5.959 21l12.083-.931C19.699 19.983 21 18.744 21 17.11V6.89c0-1.634-1.259-2.863-2.959-2.959M16.933 8.17c-.082.215-.192.432-.378.551-.188.122-.489.132-.799.132-1.521 0-3.062-.048-4.607-.048q-.23 1.061-.451 2.128c.932-.004 1.873.005 2.81.005.726 0 1.462-.069 1.586.525.04.189-.001.426-.052.615-.105.38-.258.676-.625.783-.185.054-.408.058-.646.058-1.145 0-2.345.017-3.493.02-.169.772-.328 1.553-.489 2.333 1.57-.005 3.067-.041 4.633-.058.627-.007 1.085.194 1.009.85a2.2 2.2 0 0 1-.211.725c-.102.208-.248.376-.488.452-.237.075-.541.064-.862.078-.304.014-.614.008-.924.016-.309.009-.619.022-.919.022-1.253 0-2.429.08-3.683.073-.603-.004-1.014-.249-1.124-.757-.059-.273-.018-.58.036-.841a3543 3543 0 0 1 1.629-7.763c.056-.265.114-.511.225-.714a1.24 1.24 0 0 1 .79-.62c.368-.099.883-.047 1.344-.047.305 0 .612.008.914.016.925.026 1.817.03 2.747.053.304.007.615.016.915.016.621 0 1.17.073 1.245.614.039.288-.051.567-.132.783"})))},{name:"facebook",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.5 2 2 6.5 2 12c0 5 3.7 9.1 8.4 9.9v-7H7.9V12h2.5V9.8c0-2.5 1.5-3.9 3.8-3.9 1.1 0 2.2.2 2.2.2v2.5h-1.3c-1.2 0-1.6.8-1.6 1.6V12h2.8l-.4 2.9h-2.3v7C18.3 21.1 22 17 22 12c0-5.5-4.5-10-10-10"})))},{name:"fediverse",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 743 743"},React.createElement("g",null,React.createElement("path",{d:"M181.131 275.137a68.9 68.9 0 0 1-29.465 29.328l161.758 162.389 38.998-19.764zm213.363 214.187-38.998 19.764 81.963 82.283a68.9 68.9 0 0 1 29.471-29.332zM581.646 339.391l-91.576 46.41 6.752 43.189 103.616-52.513a68.9 68.9 0 0 1-18.792-37.086m-144.738 73.351L220.383 522.477a68.9 68.9 0 0 1 18.795 37.089L443.66 455.934zM367.275 142.438l-104.48 203.97 30.848 30.967 110.623-215.957a68.9 68.9 0 0 1-36.991-18.98M235.621 399.459l-52.922 103.314a68.9 68.9 0 0 1 36.987 18.979l46.781-91.328zM150.768 304.918a68.9 68.9 0 0 1-34.416 7.195 69 69 0 0 1-6.651-.695l30.903 197.662a68.9 68.9 0 0 1 34.416-7.195 69 69 0 0 1 6.646.695zM239.342 560.545c.707 4.589.949 9.239.72 13.877a68.9 68.9 0 0 1-7.267 27.18l197.629 31.712c-.708-4.59-.95-9.24-.723-13.878a68.9 68.9 0 0 1 7.27-27.178zM601.133 377.199l-91.219 178.082a68.9 68.9 0 0 1 36.994 18.983l91.217-178.08a68.9 68.9 0 0 1-36.992-18.985M476.723 125.33a68.9 68.9 0 0 1-29.471 29.332l141.266 141.811a68.9 68.9 0 0 1 29.468-29.332zM347.787 104.631l-178.576 90.498a68.9 68.9 0 0 1 18.793 37.086l178.574-90.502a68.9 68.9 0 0 1-18.791-37.082M446.926 154.826a68.9 68.9 0 0 1-34.983 7.483 69 69 0 0 1-6.029-.633l15.818 101.291 43.163 6.926zm-16 167.028 37.4 239.482a68.9 68.9 0 0 1 33.914-6.943q3.625.206 7.207.791L474.09 328.777zM188.131 232.975c.734 4.66.988 9.383.758 14.095a68.9 68.9 0 0 1-7.16 26.983l101.369 16.281 19.923-38.908zm173.736 27.9-19.926 38.912 239.514 38.467a69 69 0 0 1-.695-13.719 68.9 68.9 0 0 1 7.349-27.324z"}),React.createElement("path",{fillOpacity:".996",d:"M412.284 156.054c34.538 1.882 64.061-24.592 65.943-59.13s-24.592-64.062-59.131-65.943c-34.538-1.882-64.061 24.592-65.943 59.13s24.593 64.062 59.131 65.943M646.144 390.82c34.538 1.881 64.062-24.593 65.943-59.131s-24.592-64.061-59.13-65.943-64.062 24.593-65.943 59.131 24.592 64.061 59.13 65.943M495.086 685.719c34.538 1.881 64.062-24.592 65.943-59.13s-24.592-64.062-59.13-65.943-64.062 24.592-65.943 59.13 24.592 64.062 59.13 65.943M167.866 633.211c34.538 1.882 64.062-24.592 65.943-59.13s-24.592-64.062-59.13-65.943-64.062 24.592-65.943 59.13 24.592 64.062 59.13 65.943M116.692 305.86c34.538 1.882 64.062-24.592 65.943-59.13s-24.592-64.062-59.131-65.943c-34.538-1.881-64.061 24.592-65.943 59.13s24.593 64.062 59.131 65.943"})))},{name:"feed",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M2 8.667V12c5.515 0 10 4.485 10 10h3.333c0-7.363-5.97-13.333-13.333-13.333M2 2v3.333c9.19 0 16.667 7.477 16.667 16.667H22C22 10.955 13.045 2 2 2m2.5 15a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5"})))},{name:"flickr",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M6.5 7c-2.75 0-5 2.25-5 5s2.25 5 5 5 5-2.25 5-5-2.25-5-5-5m11 0c-2.75 0-5 2.25-5 5s2.25 5 5 5 5-2.25 5-5-2.25-5-5-5"})))},{name:"foursquare",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M17.573 2H6.905C5.434 2 5 3.107 5 3.805v16.948c0 .785.422 1.077.66 1.172.238.097.892.177 1.285-.275 0 0 5.035-5.843 5.122-5.93.132-.132.132-.132.262-.132h3.26c1.368 0 1.588-.977 1.732-1.552.078-.318.692-3.428 1.225-6.122l.675-3.368C19.56 2.893 19.14 2 17.573 2m-1.078 5.22c-.053.252-.372.518-.665.518h-4.157c-.467 0-.802.318-.802.787v.508c0 .467.337.798.805.798h3.528c.331 0 .655.362.583.715s-.407 2.102-.448 2.295c-.04.193-.262.523-.655.523h-2.88c-.523 0-.683.068-1.033.503-.35.437-3.505 4.223-3.505 4.223-.032.035-.063.027-.063-.015V4.852c0-.298.26-.648.648-.648h8.562c.315 0 .61.297.528.683z"})))},{name:"ghost",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M10.203 20.997H3.005v-3.599h7.198zm10.792-3.599h-7.193v3.599h7.193zm.003-7.198H3v3.599h17.998zm-7.195-7.197H3.005v3.599h10.798zm7.197 0h-3.599v3.599H21z"})))},{name:"git",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M23.519 10.947 13.053.482a1.543 1.543 0 0 0-2.183 0L8.696 2.656l2.756 2.756a1.83 1.83 0 0 1 1.886.439 1.84 1.84 0 0 1 .436 1.898l2.656 2.657a1.83 1.83 0 0 1 1.899.436 1.837 1.837 0 0 1 0 2.597 1.84 1.84 0 0 1-2.599 0 1.84 1.84 0 0 1-.4-1.998l-2.478-2.477v6.521a1.837 1.837 0 0 1 .485 2.945 1.837 1.837 0 0 1-2.597 0 1.837 1.837 0 0 1 0-2.598 1.8 1.8 0 0 1 .602-.401V8.85a1.8 1.8 0 0 1-.602-.4 1.84 1.84 0 0 1-.395-2.009L7.628 3.723.452 10.898a1.544 1.544 0 0 0 0 2.184l10.467 10.467a1.544 1.544 0 0 0 2.183 0l10.417-10.418a1.546 1.546 0 0 0 0-2.184"})))},{name:"github",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12c0 4.419 2.865 8.166 6.839 9.489.5.09.682-.218.682-.484 0-.236-.009-.866-.014-1.699-2.782.602-3.369-1.34-3.369-1.34-.455-1.157-1.11-1.465-1.11-1.465-.909-.62.069-.608.069-.608 1.004.071 1.532 1.03 1.532 1.03.891 1.529 2.341 1.089 2.91.833.091-.647.349-1.086.635-1.337-2.22-.251-4.555-1.111-4.555-4.943 0-1.091.39-1.984 1.03-2.682-.103-.254-.447-1.27.097-2.646 0 0 .84-.269 2.75 1.025A9.6 9.6 0 0 1 12 6.836c.85.004 1.705.114 2.504.336 1.909-1.294 2.748-1.025 2.748-1.025.546 1.376.202 2.394.1 2.646.64.699 1.026 1.591 1.026 2.682 0 3.841-2.337 4.687-4.565 4.935.359.307.679.917.679 1.852 0 1.335-.012 2.415-.012 2.741 0 .269.18.579.688.481A10 10 0 0 0 22 12c0-5.523-4.477-10-10-10"})))},{name:"google-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m-.05 16c-3.312 0-6-2.688-6-6s2.688-6 6-6c1.62 0 2.976.594 4.014 1.566L14.26 9.222c-.432-.408-1.188-.888-2.31-.888-1.986 0-3.606 1.65-3.606 3.672s1.62 3.672 3.606 3.672c2.298 0 3.144-1.59 3.3-2.532h-3.306v-2.238h5.616c.084.378.15.732.15 1.23 0 3.426-2.298 5.862-5.76 5.862"})))},{name:"google-plus-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M8 11h6.61c.06.35.11.7.11 1.16 0 4-2.68 6.84-6.72 6.84-3.87 0-7-3.13-7-7s3.13-7 7-7c1.89 0 3.47.69 4.69 1.83l-1.9 1.83c-.52-.5-1.43-1.08-2.79-1.08-2.39 0-4.34 1.98-4.34 4.42S5.61 16.42 8 16.42c2.77 0 3.81-1.99 3.97-3.02H8zm15 0h-2V9h-2v2h-2v2h2v2h2v-2h2"})))},{name:"google-plus",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m-1.919 14.05a4.051 4.051 0 0 1 0-8.1c1.094 0 2.009.401 2.709 1.057l-1.15 1.118a2.23 2.23 0 0 0-1.559-.599c-1.341 0-2.434 1.114-2.434 2.479s1.094 2.479 2.434 2.479c1.551 0 2.122-1.073 2.227-1.709h-2.232v-1.511h3.791c.057.255.101.494.101.83.001 2.312-1.55 3.956-3.887 3.956M19 12.75h-1.25V14h-1.5v-1.25H15v-1.5h1.25V10h1.5v1.25H19z"})))},{name:"google",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12.02 10.18v3.73h5.51c-.26 1.57-1.67 4.22-5.5 4.22-3.31 0-6.01-2.75-6.01-6.12s2.7-6.12 6.01-6.12c1.87 0 3.13.8 3.85 1.48l2.84-2.76C16.99 2.99 14.73 2 12.03 2c-5.52 0-10 4.48-10 10s4.48 10 10 10c5.77 0 9.6-4.06 9.6-9.77 0-.83-.11-1.42-.25-2.05z"})))},{name:"instagram",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 4.622c2.403 0 2.688.009 3.637.052.877.04 1.354.187 1.671.31.42.163.72.358 1.035.673s.51.615.673 1.035c.123.317.27.794.31 1.671.043.949.052 1.234.052 3.637s-.009 2.688-.052 3.637c-.04.877-.187 1.354-.31 1.671-.163.42-.358.72-.673 1.035s-.615.51-1.035.673c-.317.123-.794.27-1.671.31-.949.043-1.233.052-3.637.052s-2.688-.009-3.637-.052c-.877-.04-1.354-.187-1.671-.31a2.8 2.8 0 0 1-1.035-.673 2.8 2.8 0 0 1-.673-1.035c-.123-.317-.27-.794-.31-1.671-.043-.949-.052-1.234-.052-3.637s.009-2.688.052-3.637c.04-.877.187-1.354.31-1.671.163-.42.358-.72.673-1.035s.615-.51 1.035-.673c.317-.123.794-.27 1.671-.31.949-.043 1.234-.052 3.637-.052M12 3c-2.444 0-2.751.01-3.711.054-.958.044-1.612.196-2.184.418a4.4 4.4 0 0 0-1.594 1.039c-.5.5-.808 1.002-1.038 1.594-.223.572-.375 1.226-.419 2.184C3.01 9.249 3 9.556 3 12s.01 2.751.054 3.711c.044.958.196 1.612.418 2.185.23.592.538 1.094 1.038 1.594s1.002.808 1.594 1.038c.572.222 1.227.375 2.185.418.96.044 1.267.054 3.711.054s2.751-.01 3.711-.054c.958-.044 1.612-.196 2.185-.418a4.4 4.4 0 0 0 1.594-1.038c.5-.5.808-1.002 1.038-1.594.222-.572.375-1.227.418-2.185.044-.96.054-1.267.054-3.711s-.01-2.751-.054-3.711c-.044-.958-.196-1.612-.418-2.185A4.4 4.4 0 0 0 19.49 4.51c-.5-.5-1.002-.808-1.594-1.038-.572-.222-1.227-.375-2.185-.418C14.751 3.01 14.444 3 12 3m0 4.378a4.622 4.622 0 1 0 0 9.244 4.622 4.622 0 0 0 0-9.244M12 15a3 3 0 1 1 0-6 3 3 0 0 1 0 6m4.804-8.884a1.08 1.08 0 1 0 .001 2.161 1.08 1.08 0 0 0-.001-2.161"})))},{name:"json-feed",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"m8.522 17.424.027.027c1.076-1.076 1.854-.993 3.154.306l2.053 2.053c2.136 2.136 4.131 2.028 6.515-.356l.729-.728-1.548-1.548-.373.373c-1.349 1.349-2.293 1.366-3.585.075l-2.409-2.409c-1.242-1.242-2.475-1.366-3.659-.381l-.232-.232c1.01-1.225.911-2.368-.29-3.568l-2.16-2.162c-1.317-1.317-1.308-2.236.058-3.602l.372-.372-1.54-1.54-.728.729c-2.393 2.393-2.525 4.346-.439 6.433l1.78 1.78c1.3 1.3 1.383 2.095.315 3.163l.008.008a1.384 1.384 0 0 0 1.952 1.951"}),React.createElement("circle",{cx:"13.089",cy:"10.905",r:"1.383"}),React.createElement("circle",{cx:"16.349",cy:"7.644",r:"1.383"}),React.createElement("circle",{cx:"19.61",cy:"4.383",r:"1.383"})))},{name:"line",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M14.255 9.572v3.333c0 .084-.066.15-.15.15h-.534a.16.16 0 0 1-.122-.061l-1.528-2.063v1.978c0 .084-.066.15-.15.15h-.534a.15.15 0 0 1-.15-.15V9.576c0-.084.066-.15.15-.15h.529a.14.14 0 0 1 .122.066l1.528 2.063V9.577c0-.084.066-.15.15-.15h.534a.15.15 0 0 1 .155.145m-3.844-.15h-.534a.15.15 0 0 0-.15.15v3.333c0 .084.066.15.15.15h.534c.084 0 .15-.066.15-.15V9.572c0-.08-.066-.15-.15-.15m-1.289 2.794H7.664V9.572a.15.15 0 0 0-.15-.15H6.98a.15.15 0 0 0-.15.15v3.333q0 .062.042.103a.16.16 0 0 0 .103.042h2.142c.084 0 .15-.066.15-.15v-.534a.15.15 0 0 0-.145-.15m7.945-2.794h-2.142c-.08 0-.15.066-.15.15v3.333c0 .08.066.15.15.15h2.142c.084 0 .15-.066.15-.15v-.534a.15.15 0 0 0-.15-.15h-1.458v-.563h1.458c.084 0 .15-.066.15-.15v-.539a.15.15 0 0 0-.15-.15h-1.458v-.563h1.458c.084 0 .15-.066.15-.15v-.534c-.005-.08-.07-.15-.15-.15M22.5 5.33v13.373c-.005 2.1-1.725 3.802-3.83 3.797H5.297c-2.1-.005-3.802-1.73-3.797-3.83V5.297c.005-2.1 1.73-3.802 3.83-3.797h13.373c2.1.005 3.802 1.725 3.797 3.83m-2.888 5.747c0-3.422-3.431-6.206-7.645-6.206s-7.645 2.784-7.645 6.206c0 3.066 2.719 5.634 6.394 6.122.895.192.792.52.591 1.725-.033.192-.155.755.661.413s4.402-2.592 6.009-4.439c1.106-1.219 1.636-2.452 1.636-3.82"})))},{name:"link",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M17 13H7v-2h10zm1-6h-1c-1.631 0-3.065.792-3.977 2H18c1.103 0 2 .897 2 2v2c0 1.103-.897 2-2 2h-4.977c.913 1.208 2.347 2 3.977 2h1a4 4 0 0 0 4-4v-2a4 4 0 0 0-4-4M2 11v2a4 4 0 0 0 4 4h1c1.63 0 3.065-.792 3.977-2H6c-1.103 0-2-.897-2-2v-2c0-1.103.897-2 2-2h4.977C10.065 7.792 8.631 7 7 7H6a4 4 0 0 0-4 4"})))},{name:"linkedin",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19.7 3H4.3A1.3 1.3 0 0 0 3 4.3v15.4A1.3 1.3 0 0 0 4.3 21h15.4a1.3 1.3 0 0 0 1.3-1.3V4.3A1.3 1.3 0 0 0 19.7 3M8.339 18.338H5.667v-8.59h2.672zM7.004 8.574a1.548 1.548 0 1 1-.002-3.096 1.548 1.548 0 0 1 .002 3.096m11.335 9.764H15.67v-4.177c0-.996-.017-2.278-1.387-2.278-1.389 0-1.601 1.086-1.601 2.206v4.249h-2.667v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.779 3.203 4.092v4.711z"})))},{name:"mail",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M20 4H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2m0 4.236-8 4.882-8-4.882V6h16z"})))},{name:"mastodon",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M11.973 2.352c-2.468.02-4.842.286-6.225.921 0 0-2.742 1.229-2.742 5.415 0 .958-.018 2.105.012 3.32.1 4.094.75 8.128 4.535 9.129 1.745.462 3.244.56 4.45.494 2.19-.122 3.417-.781 3.417-.781l-.072-1.588s-1.565.491-3.32.431c-1.74-.06-3.576-.188-3.858-2.324a4 4 0 0 1-.04-.598s1.709.416 3.874.516c1.324.06 2.563-.076 3.824-.226 2.418-.29 4.524-1.78 4.79-3.141.416-2.144.38-5.232.38-5.232 0-4.186-2.74-5.415-2.74-5.415-1.383-.635-3.76-.9-6.227-.921zM9.18 5.622c1.028 0 1.804.395 2.318 1.185l.502.84.5-.84c.514-.79 1.292-1.186 2.32-1.186.888 0 1.605.313 2.15.922q.795.915.794 2.469v5.068h-2.008V9.16c0-1.037-.438-1.562-1.31-1.562-.966 0-1.448.622-1.448 1.857v2.693h-1.996V9.455c0-1.235-.484-1.857-1.45-1.857-.872 0-1.308.525-1.308 1.562v4.92H6.236V9.012q-.001-1.554.793-2.469c.547-.609 1.263-.922 2.15-.922"})))},{name:"medium-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{fillRule:"nonzero",d:"M7.423 6c3.27 0 5.922 2.686 5.922 6s-2.651 6-5.922 6S1.5 15.313 1.5 12s2.652-6 5.923-6m9.458.351c1.635 0 2.961 2.53 2.961 5.65 0 3.118-1.325 5.648-2.96 5.648S13.92 15.119 13.92 12s1.325-5.649 2.96-5.649m4.577.589c.576 0 1.042 2.265 1.042 5.06s-.466 5.06-1.042 5.06c-.575 0-1.04-2.265-1.04-5.06s.465-5.06 1.04-5.06"})))},{name:"medium",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M3 3v18h18V3zm15 4.26-1 .93a.28.28 0 0 0-.11.27v6.8a.27.27 0 0 0 .11.27l.94.93v.2h-4.75v-.2l1-1c.09-.1.09-.12.09-.27V9.74l-2.71 6.9h-.37L8 9.74v4.62a.67.67 0 0 0 .17.54l1.27 1.54v.2H5.86v-.2l1.27-1.54a.64.64 0 0 0 .17-.54V9a.5.5 0 0 0-.16-.4L6 7.26v-.2h3.52L12.23 13l2.38-5.94H18z"})))},{name:"messenger",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12.026.375C5.462.375.375 5.172.375 11.652c0 3.389 1.393 6.318 3.66 8.341.391.352.311.556.377 2.73a.934.934 0 0 0 1.307.823c2.48-1.092 2.512-1.178 2.933-1.064 7.185 1.977 14.973-2.621 14.973-10.83 0-6.48-5.035-11.277-11.599-11.277m6.996 8.678L15.6 14.47a1.75 1.75 0 0 1-2.527.465l-2.723-2.038a.7.7 0 0 0-.844 0l-3.674 2.786c-.49.372-1.133-.216-.802-.735l3.422-5.417a1.75 1.75 0 0 1 2.527-.465l2.722 2.037a.7.7 0 0 0 .844 0L18.22 8.32c.489-.374 1.132.213.801.732"})))},{name:"microblog",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19.641 17.086c1.294-1.522 2.067-3.438 2.067-5.521 0-4.957-4.371-8.972-9.763-8.972s-9.763 4.015-9.763 8.972 4.371 8.972 9.763 8.972a10.5 10.5 0 0 0 3.486-.59.315.315 0 0 1 .356.112c.816 1.101 2.09 1.876 3.506 2.191a.194.194 0 0 0 .192-.309 3.82 3.82 0 0 1 .162-4.858zm-3.065-6.575-2.514 1.909.912 3.022a.286.286 0 0 1-.437.317l-2.592-1.802-2.592 1.802a.285.285 0 0 1-.436-.317l.912-3.022-2.515-1.909a.285.285 0 0 1 .167-.513l3.155-.066 1.038-2.981a.285.285 0 0 1 .539 0l1.038 2.981 3.155.066a.285.285 0 0 1 .17.513"})))},{name:"nextdoor",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",strokeMiterlimit:"10",viewBox:"0 0 130 130"},React.createElement("g",null,React.createElement("path",{d:"M64.25 3.531c-31.144.337-57.596 24.22-60.469 55.907-3.064 33.799 21.857 63.685 55.657 66.75s63.685-21.857 66.75-55.657-21.857-63.686-55.657-66.75a62 62 0 0 0-6.281-.25m3.938 34.907C82.468 38.438 93.5 48.58 93.5 61.5v27c0 .685-.565 1.25-1.25 1.25H80.906a1.267 1.267 0 0 1-1.25-1.25V63.375c0-5.58-4.309-11.937-11.469-11.937-7.47 0-11.468 6.357-11.468 11.937V88.5c0 .685-.565 1.25-1.25 1.25H44.125c-.68 0-1.219-.57-1.219-1.25V64.156c0-.74-.529-1.364-1.25-1.531-13.13-2.93-15.115-10.285-15.375-21.125-.005-.332.142-.67.375-.906.233-.237.543-.375.875-.375l11.688.062c.66.01 1.187.529 1.218 1.188.13 4.44.438 9.406 4.438 9.406.83 0 1.443-1.179 1.813-1.719 4.41-6.48 12.28-10.718 21.5-10.718"})))},{name:"patreon",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M20 7.408c-.003-2.299-1.746-4.182-3.79-4.862-2.54-.844-5.888-.722-8.312.453-2.939 1.425-3.862 4.545-3.896 7.656-.028 2.559.22 9.297 3.92 9.345 2.75.036 3.159-3.603 4.43-5.356.906-1.247 2.071-1.599 3.506-1.963 2.465-.627 4.146-2.626 4.142-5.273"})))},{name:"pinterest-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12.289 2C6.617 2 3.606 5.648 3.606 9.622c0 1.846 1.025 4.146 2.666 4.878.25.111.381.063.439-.169.044-.175.267-1.029.365-1.428a.37.37 0 0 0-.091-.362c-.54-.63-.975-1.791-.975-2.873 0-2.777 2.194-5.464 5.933-5.464 3.23 0 5.49 2.108 5.49 5.122 0 3.407-1.794 5.768-4.13 5.768-1.291 0-2.257-1.021-1.948-2.277.372-1.495 1.089-3.112 1.089-4.191 0-.967-.542-1.775-1.663-1.775-1.319 0-2.379 1.309-2.379 3.059 0 1.115.394 1.869.394 1.869s-1.302 5.279-1.54 6.261c-.405 1.666.053 4.368.094 4.604.021.126.167.169.25.063.129-.165 1.699-2.419 2.142-4.051.158-.59.817-2.995.817-2.995.43.784 1.681 1.446 3.013 1.446 3.963 0 6.822-3.494 6.822-7.833C20.394 5.112 16.849 2 12.289 2"})))},{name:"pinterest",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12c0 4.236 2.636 7.855 6.356 9.312-.087-.791-.166-2.005.035-2.869.182-.78 1.173-4.971 1.173-4.971s-.299-.599-.299-1.484c0-1.39.806-2.429 1.809-2.429.853 0 1.265.641 1.265 1.409 0 .858-.546 2.141-.828 3.329-.236.996.499 1.807 1.481 1.807 1.777 0 3.144-1.874 3.144-4.579 0-2.394-1.72-4.068-4.177-4.068-2.845 0-4.515 2.134-4.515 4.34 0 .859.331 1.781.744 2.282a.3.3 0 0 1 .069.287c-.077.316-.246.995-.279 1.134-.044.183-.145.222-.334.134-1.249-.581-2.03-2.407-2.03-3.874 0-3.154 2.292-6.051 6.607-6.051 3.469 0 6.165 2.472 6.165 5.775 0 3.446-2.173 6.22-5.189 6.22-1.013 0-1.966-.526-2.292-1.148l-.623 2.377c-.226.869-.835 1.957-1.243 2.622.936.289 1.93.445 2.961.445 5.523 0 10-4.477 10-10S17.523 2 12 2"})))},{name:"pocket",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M21.927 4.194A1.82 1.82 0 0 0 20.222 3H3.839a1.823 1.823 0 0 0-1.813 1.814v6.035l.069 1.2c.29 2.73 1.707 5.115 3.899 6.778l.119.089.025.018a9.9 9.9 0 0 0 3.91 1.727 10.06 10.06 0 0 0 4.049-.014.3.3 0 0 0 .064-.023 9.9 9.9 0 0 0 3.753-1.691l.025-.018q.06-.043.119-.089c2.192-1.664 3.609-4.049 3.898-6.778l.069-1.2V4.814a1.8 1.8 0 0 0-.098-.62m-4.235 6.287-4.704 4.512a1.37 1.37 0 0 1-1.898 0l-4.705-4.512a1.371 1.371 0 1 1 1.898-1.979l3.756 3.601 3.755-3.601a1.372 1.372 0 0 1 1.898 1.979"})))},{name:"polldaddy",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.487 2 2 6.487 2 12c0 5.514 4.487 10 10 10 5.514 0 10-4.486 10-10 0-5.513-4.486-10-10-10m.991 1.68c2.361.084 4.657 1.251 6.197 3.136.283.334.541.693.774 1.067a7.78 7.78 0 0 0-6.094-2.94 7.76 7.76 0 0 0-5.896 2.703q-.008.006-.016.014l-.152.159-.031.032a6.12 6.12 0 0 0-1.633 4.165 6.15 6.15 0 0 0 6.143 6.143c.57 0 1.123-.081 1.649-.227-1.849.839-4.131.747-5.926-.324-1.841-1.089-3.171-3.111-3.433-5.313A7.39 7.39 0 0 1 6.69 6.137C8.294 4.5 10.634 3.563 12.991 3.68m3.373 8.519c-.049-2.024-1.587-3.889-3.544-4.174-1.927-.343-3.917.857-4.451 2.661a3.67 3.67 0 0 0 .2 2.653c.39.8 1.067 1.451 1.894 1.759 1.664.654 3.63-.27 4.173-1.863.593-1.58-.396-3.423-1.94-3.776-1.52-.407-3.161.757-3.204 2.243a2.36 2.36 0 0 0 .753 1.879c.501.476 1.23.667 1.871.529a2.07 2.07 0 0 0 1.469-1.134 1.91 1.91 0 0 0-.087-1.767c-.297-.513-.859-.863-1.429-.881a1.7 1.7 0 0 0-1.437.679 1.53 1.53 0 0 0-.18 1.489q.006.016.016.03c.193.634.774 1.1 1.467 1.117a1.6 1.6 0 0 1-.97-.183c-.466-.244-.809-.747-.893-1.29a1.8 1.8 0 0 1 .499-1.539 2.02 2.02 0 0 1 1.58-.606c.593.04 1.159.35 1.517.859.364.496.51 1.156.383 1.773-.116.62-.529 1.174-1.093 1.514a2.52 2.52 0 0 1-1.914.286c-.65-.161-1.226-.606-1.584-1.206a2.83 2.83 0 0 1-.341-2.031c.143-.7.573-1.321 1.176-1.753 1.193-.883 3.056-.751 4.106.411 1.106 1.1 1.327 3.027.406 4.371-.877 1.376-2.74 2.086-4.374 1.594-1.639-.449-2.913-2.079-3.031-3.853-.07-.884.13-1.797.583-2.577.445-.777 1.155-1.432 1.972-1.862 1.64-.88 3.816-.743 5.349.424 1.251.924 2.083 2.42 2.236 4.009l.001.03c0 2.9-2.359 5.26-5.26 5.26a5.2 5.2 0 0 1-1.947-.376 5 5 0 0 0 2.613-.079 4.96 4.96 0 0 0 2.514-1.751c.618-.828.95-1.861.901-2.869M12 21.113c-5.024 0-9.111-4.087-9.111-9.113 0-4.789 3.713-8.723 8.411-9.081a7 7 0 0 0-.397.06c-2.644.453-5.017 2.106-6.32 4.409-1.309 2.301-1.391 5.19-.3 7.527 1.056 2.34 3.253 4.156 5.776 4.553 2.497.44 5.133-.483 6.787-2.301 1.719-1.797 2.269-4.529 1.486-6.796-.583-1.81-1.976-3.331-3.7-4.046 3.417.594 6.174 3.221 6.174 6.781 0 1.004-.241 2.02-.657 2.966-1.498 2.984-4.586 5.041-8.149 5.041"})))},{name:"print",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M9 16h6v2H9zm13 1h-3v3a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-3H2V9a2 2 0 0 1 2-2h1V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v2h1a2 2 0 0 1 2 2zM7 7h10V5H7zm10 7H7v6h10zm3-3.5a1.5 1.5 0 1 0-3.001.001A1.5 1.5 0 0 0 20 10.5"})))},{name:"quora",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M47.736 16.521c-.41-.81-.898-1.631-1.846-1.631a1 1 0 0 0-.527.107l-.322-.644a2.93 2.93 0 0 1 1.836-.595c1.26 0 1.914.605 2.431 1.397a6.8 6.8 0 0 0 .449-2.675c0-2.773-.869-4.199-2.929-4.199-1.992 0-2.851 1.465-2.851 4.199s.859 4.17 2.851 4.17a4 4 0 0 0 .869-.107zm.498.966a6 6 0 0 1-1.367.185 5.27 5.27 0 0 1-5.263-5.204c0-3.114 2.558-5.233 5.263-5.233s5.282 2.109 5.282 5.233a5.08 5.08 0 0 1-1.992 4.072c.381.566.781.956 1.319.956.595 0 .839-.459.878-.82h.781c.049.488-.195 2.48-2.373 2.48-1.319 0-2.012-.761-2.529-1.66zm5.624-2.646v-3.563c0-.371-.146-.586-.615-.586h-.498v-.956h3.251v5.048c0 .849.459 1.231 1.161 1.231a1.56 1.56 0 0 0 1.465-.839V11.28c0-.371-.146-.586-.615-.586h-.527v-.957h3.28v5.302c0 .527.195.732.8.732h.107v.976l-2.929.468V16.21h-.057a3.12 3.12 0 0 1-2.509 1.152c-1.28 0-2.304-.644-2.304-2.558zm12.059 1.611c1.152 0 1.592-1.005 1.611-3.027.02-1.982-.459-2.929-1.611-2.929-1.005 0-1.641.956-1.641 2.929 0 2.021.625 3.027 1.641 3.027m0 .956a3.906 3.906 0 0 1-3.974-3.974c0-2.334 1.836-3.886 3.974-3.886 2.226 0 4.004 1.582 4.004 3.886a3.867 3.867 0 0 1-4.004 3.974m4.072-.146v-.956h.312c.781 0 .859-.224.859-.908v-4.121c0-.371-.215-.586-.732-.586h-.42v-.955h2.968l.146 1.553h.108c.371-1.113 1.221-1.699 2.051-1.699.693 0 1.221.39 1.221 1.181 0 .547-.264 1.093-1.005 1.093-.664 0-.8-.449-1.358-.449-.488 0-.869.468-.869 1.152v2.783c0 .673.166.908.937.908h.439v.956h-4.658zm9.901-1.093c.956 0 1.338-.898 1.338-1.797v-1.211c-.732.722-2.304.742-2.304 2.021 0 .625.371.986.966.986m1.387 0c-.39.752-1.191 1.26-2.314 1.26-1.309 0-2.148-.732-2.148-1.914 0-2.451 3.417-1.797 4.423-3.427v-.185c0-1.25-.488-1.445-1.035-1.445-1.524 0-.83 1.631-2.226 1.631-.673 0-.937-.371-.937-.859 0-.927 1.093-1.67 3.173-1.67 1.963 0 3.163.537 3.163 2.49v3.114q-.02.742.595.742a1 1 0 0 0 .449-.127l.254.615c-.205.312-.752.869-1.836.869-.908 0-1.465-.42-1.543-1.113h-.01zm-68.554 2.558c-.83-1.641-1.807-3.3-3.711-3.3a2.9 2.9 0 0 0-1.093.215l-.644-1.299a5.66 5.66 0 0 1 3.662-1.211c2.548 0 3.857 1.231 4.892 2.792q.917-2.012.908-5.38c0-5.585-1.748-8.417-5.829-8.417-4.033 0-5.76 2.87-5.76 8.417s1.738 8.397 5.76 8.397a5.9 5.9 0 0 0 1.748-.224zm.996 1.953a9.8 9.8 0 0 1-2.744.371C5.614 21.041.371 16.764.371 10.545.371 4.277 5.614 0 10.965 0c5.448 0 10.642 4.248 10.642 10.545a10.25 10.25 0 0 1-4.013 8.201c.732 1.152 1.563 1.914 2.665 1.914 1.201 0 1.689-.927 1.768-1.66h1.572c.088.966-.4 4.999-4.775 4.999-2.646 0-4.052-1.543-5.106-3.339z"})))},{name:"reddit",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M22 11.816a2.28 2.28 0 0 0-2.277-2.277c-.593 0-1.122.24-1.526.614-1.481-.965-3.455-1.594-5.647-1.69l1.171-3.702 3.18.748a1.88 1.88 0 0 0 1.876 1.862 1.88 1.88 0 0 0 1.877-1.878 1.88 1.88 0 0 0-1.877-1.877c-.769 0-1.431.466-1.72 1.13l-3.508-.826a.386.386 0 0 0-.46.261l-1.35 4.268c-2.316.038-4.411.67-5.97 1.671a2.24 2.24 0 0 0-1.492-.581A2.28 2.28 0 0 0 2 11.816c0 .814.433 1.523 1.078 1.925a4 4 0 0 0-.061.672c0 3.292 4.011 5.97 8.941 5.97s8.941-2.678 8.941-5.97q-.002-.32-.053-.632A2.26 2.26 0 0 0 22 11.816m-3.224-7.422a1.1 1.1 0 1 1-.001 2.199 1.1 1.1 0 0 1 .001-2.199M2.777 11.816c0-.827.672-1.5 1.499-1.5.313 0 .598.103.838.269-.851.676-1.477 1.479-1.812 2.36a1.48 1.48 0 0 1-.525-1.129m9.182 7.79c-4.501 0-8.164-2.329-8.164-5.193S7.457 9.22 11.959 9.22s8.164 2.329 8.164 5.193-3.663 5.193-8.164 5.193m8.677-6.605c-.326-.89-.948-1.701-1.797-2.384.248-.186.55-.301.883-.301.827 0 1.5.673 1.5 1.5.001.483-.23.911-.586 1.185m-11.64 1.703c-.76 0-1.397-.616-1.397-1.376s.637-1.397 1.397-1.397 1.376.637 1.376 1.397-.616 1.376-1.376 1.376m7.405-1.376c0 .76-.616 1.376-1.376 1.376s-1.399-.616-1.399-1.376.639-1.397 1.399-1.397 1.376.637 1.376 1.397m-1.172 3.38a.39.39 0 0 1 0 .55c-.674.674-1.727 1.002-3.219 1.002l-.011-.002-.011.002c-1.492 0-2.544-.328-3.218-1.002a.389.389 0 1 1 .55-.55c.521.521 1.394.775 2.669.775l.011.002.011-.002c1.275 0 2.148-.253 2.669-.775a.387.387 0 0 1 .549 0"})))},{name:"share",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M18 16c-.788 0-1.499.31-2.034.807L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.048 4.118A3 3 0 0 0 15 19a3 3 0 1 0 3-3"})))},{name:"skype",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"m10.113 2.699.1-.02q.05.025.098.051zM2.72 10.223l-.017.103q.025.048.051.095zm18.555 3.548q.009-.053.018-.106-.025-.047-.052-.095zm-7.712 7.428q.049.027.096.053l.105-.017zM22 16.386a5.55 5.55 0 0 1-1.637 3.953 5.55 5.55 0 0 1-3.953 1.637 5.6 5.6 0 0 1-2.75-.725l.105-.017-.202-.035q.049.027.096.053a9.5 9.5 0 0 1-1.654.147 9.4 9.4 0 0 1-3.676-.743 9.4 9.4 0 0 1-3.002-2.023 9.4 9.4 0 0 1-2.023-3.002 9.4 9.4 0 0 1-.743-3.676c0-.546.049-1.093.142-1.628q.025.048.051.095l-.034-.199-.017.103A5.6 5.6 0 0 1 2 7.615c0-1.493.582-2.898 1.637-3.953A5.56 5.56 0 0 1 7.59 2.024c.915 0 1.818.228 2.622.655l-.1.02.199.031q-.049-.026-.098-.051l.004-.001a9.5 9.5 0 0 1 1.788-.169 9.41 9.41 0 0 1 6.678 2.766 9.4 9.4 0 0 1 2.024 3.002 9.4 9.4 0 0 1 .743 3.676c0 .575-.054 1.15-.157 1.712q-.025-.047-.052-.095l.034.201q.009-.053.018-.106c.461.829.707 1.767.707 2.721m-5.183-2.248c0-1.331-.613-2.743-3.033-3.282l-2.209-.49c-.84-.192-1.807-.444-1.807-1.237s.679-1.348 1.903-1.348c2.468 0 2.243 1.696 3.468 1.696.645 0 1.209-.379 1.209-1.031 0-1.521-2.435-2.663-4.5-2.663-2.242 0-4.63.952-4.63 3.488 0 1.221.436 2.521 2.839 3.123l2.984.745c.903.223 1.129.731 1.129 1.189 0 .762-.758 1.507-2.129 1.507-2.679 0-2.307-2.062-3.743-2.062-.645 0-1.113.444-1.113 1.078 0 1.236 1.501 2.886 4.856 2.886 3.195 0 4.776-1.538 4.776-3.599"})))},{name:"sms",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M17.696 4C20.069 4 22 5.973 22 8.398v4.357c0 2.04-1.368 3.783-3.261 4.266v4.427l-5.234-4.295h-7.2C3.93 17.153 2 15.18 2 12.755V8.398C2 5.973 3.931 4 6.304 4zM7.028 8.515c-.98 0-1.66.562-1.66 1.349-.009.497.322.91.985 1.178l.39.142c.242.097.305.171.305.297 0 .162-.131.251-.442.251s-.76-.135-1.004-.284l-.112.046-.215.868c.359.258.832.364 1.33.364 1.104 0 1.764-.523 1.764-1.333-.008-.574-.305-.956-.954-1.216l-.393-.146c-.266-.108-.341-.181-.341-.287 0-.152.131-.243.387-.243.274 0 .587.093.808.214l.109-.047.214-.837c-.315-.224-.741-.316-1.171-.316m10.302 0c-.98 0-1.66.562-1.66 1.349-.008.497.322.91.985 1.178l.39.142c.243.097.305.171.305.297 0 .162-.13.251-.442.251-.311 0-.76-.135-1.004-.284l-.112.046-.215.868c.359.258.832.364 1.33.364 1.104 0 1.764-.523 1.764-1.333-.008-.574-.305-.956-.954-1.216l-.393-.146c-.266-.108-.341-.181-.341-.287 0-.152.131-.243.387-.243.274 0 .587.093.808.214l.109-.047.214-.837c-.316-.224-.741-.316-1.171-.316m-3.733 0c-.297 0-.55.066-.78.202l-.144.098a2 2 0 0 0-.264.247l-.078.095-.027-.077c-.15-.34-.55-.565-1.033-.565l-.169.007a1.36 1.36 0 0 0-.896.42l-.08.09-.038-.363-.075-.067H8.994l-.075.079.024.634c.005.2.008.397.008.604v2.652l.075.075h1.178l.075-.075v-2.269q-.002-.168.042-.274c.083-.23.262-.392.496-.392.314 0 .483.267.483.753v2.182l.075.075h1.179l.075-.075v-2.277c0-.097.016-.213.043-.285.077-.224.26-.373.486-.373.33 0 .5.272.5.817v2.118l.074.075h1.179l.075-.075v-2.293c0-1.131-.537-1.763-1.39-1.763Z"})))},{name:"snapchat",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M11.989 1.728c3.221.001 5.904 2.683 5.908 5.912q.002 1.133.067 2.094a.737.737 0 0 0 .902.669l1.009-.237a.6.6 0 0 1 .129-.015c.256 0 .492.175.55.434a.74.74 0 0 1-.479.861l-1.532.618a.823.823 0 0 0-.485.98c1.229 4.543 4.661 4.071 4.661 4.662 0 .743-2.587.848-2.821 1.082s-.01 1.368-.532 1.588a1.1 1.1 0 0 1-.409.056c-.393 0-.95-.077-1.536-.077-.509 0-1.04.058-1.507.273-1.239.572-2.433 1.641-3.914 1.641S9.325 21.2 8.086 20.628c-.467-.216-.998-.273-1.507-.273-.586 0-1.143.077-1.536.077-.17 0-.31-.014-.409-.056-.522-.22-.299-1.354-.532-1.588s-2.821-.337-2.821-1.08c0-.592 3.432-.119 4.661-4.662a.824.824 0 0 0-.486-.98l-1.532-.618a.74.74 0 0 1-.479-.861.56.56 0 0 1 .679-.419l1.009.237q.086.02.169.02a.737.737 0 0 0 .733-.689q.065-.961.067-2.094c.004-3.229 2.666-5.91 5.887-5.912m0-1.281c-.961 0-1.898.194-2.784.574A7.2 7.2 0 0 0 6.93 2.572a7.2 7.2 0 0 0-1.539 2.282A7.1 7.1 0 0 0 4.82 7.64a33 33 0 0 1-.029 1.369l-.375-.088a2 2 0 0 0-.421-.049 1.86 1.86 0 0 0-1.135.389 1.84 1.84 0 0 0-.666 1.049 2.024 2.024 0 0 0 1.271 2.335l1.124.454c-.744 2.285-2.117 2.723-3.041 3.018a5 5 0 0 0-.659.246C.087 16.76 0 17.436 0 17.708c0 .521.247.996.694 1.339.223.17.499.311.844.43.47.162 1.016.265 1.459.347.021.164.053.341.106.518.22.738.684 1.069 1.034 1.217.332.14.676.156.905.156.224 0 .462-.018.713-.036.269-.02.548-.041.823-.041.426 0 .743.051.97.155.311.144.64.337.989.542.972.571 2.073 1.217 3.462 1.217s2.49-.647 3.462-1.217c.349-.205.679-.399.989-.542.226-.105.544-.155.97-.155.275 0 .554.021.823.041.251.019.488.036.713.036.229 0 .573-.016.905-.156.35-.147.814-.478 1.034-1.217.053-.178.084-.354.106-.518.443-.082.989-.185 1.459-.347.345-.119.621-.259.844-.43.448-.342.694-.818.694-1.339 0-.272-.087-.948-.891-1.347a5 5 0 0 0-.659-.246c-.924-.295-2.297-.733-3.041-3.018l1.124-.454a2.025 2.025 0 0 0 1.271-2.335 1.83 1.83 0 0 0-.666-1.049 1.86 1.86 0 0 0-1.556-.34l-.375.088a33 33 0 0 1-.029-1.369 7.1 7.1 0 0 0-.575-2.789c-.365-.853-.886-1.62-1.547-2.282s-1.428-1.182-2.28-1.547a7.1 7.1 0 0 0-2.786-.574"})))},{name:"soundcloud",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M23.587 13.923a3.303 3.303 0 0 1-3.344 3.117h-8.037a.674.674 0 0 1-.667-.67V7.717a.74.74 0 0 1 .444-.705s.739-.512 2.296-.512a5.27 5.27 0 0 1 2.702.742 5.35 5.35 0 0 1 2.516 3.485 3.1 3.1 0 0 1 .852-.116 3.217 3.217 0 0 1 3.237 3.312m-13.05-5.659c.242 2.935.419 5.612 0 8.538a.261.261 0 0 1-.519 0c-.39-2.901-.221-5.628 0-8.538a.26.26 0 0 1 .398-.25.26.26 0 0 1 .12.25zm-1.627 8.541a.273.273 0 0 1-.541 0 32.7 32.7 0 0 1 0-7.533.274.274 0 0 1 .544 0 29.4 29.4 0 0 1-.003 7.533m-1.63-7.788c.264 2.69.384 5.099-.003 7.782a.262.262 0 0 1-.522 0c-.374-2.649-.249-5.127 0-7.782a.264.264 0 0 1 .525 0m-1.631 7.792a.268.268 0 0 1-.532 0 27.6 27.6 0 0 1 0-7.034.27.27 0 1 1 .541 0 25.8 25.8 0 0 1-.01 7.034zm-1.63-5.276c.412 1.824.227 3.435-.015 5.294a.255.255 0 0 1-.504 0c-.22-1.834-.402-3.482-.015-5.295a.268.268 0 0 1 .535 0m-1.626-.277c.378 1.869.254 3.451-.01 5.325-.031.277-.506.28-.531 0-.239-1.846-.352-3.476-.01-5.325a.277.277 0 0 1 .551 0m-1.643.907c.396 1.239.261 2.246-.016 3.517a.258.258 0 0 1-.514 0c-.239-1.246-.336-2.274-.021-3.517a.276.276 0 0 1 .55 0z"})))},{name:"spotify",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m4.586 14.424a.62.62 0 0 1-.857.207c-2.348-1.435-5.304-1.76-8.785-.964a.622.622 0 1 1-.277-1.215c3.809-.871 7.077-.496 9.713 1.115a.623.623 0 0 1 .206.857M17.81 13.7a.78.78 0 0 1-1.072.257c-2.687-1.652-6.785-2.131-9.965-1.166A.779.779 0 1 1 6.32 11.3c3.632-1.102 8.147-.568 11.234 1.328a.78.78 0 0 1 .256 1.072m.105-2.835c-3.223-1.914-8.54-2.09-11.618-1.156a.935.935 0 1 1-.542-1.79c3.532-1.072 9.404-.865 13.115 1.338a.936.936 0 1 1-.955 1.608"})))},{name:"squarespace",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M20.87 9.271a3.86 3.86 0 0 0-5.458 0l-6.141 6.141a.964.964 0 1 0 1.365 1.364l6.14-6.14a1.929 1.929 0 1 1 2.729 2.729l-6.022 6.022a1.93 1.93 0 0 0 2.729 0l4.658-4.658a3.86 3.86 0 0 0 0-5.458m-2.047 2.047a.965.965 0 0 0-1.365 0l-6.14 6.14a1.93 1.93 0 0 1-2.729 0 .964.964 0 1 0-1.364 1.364 3.86 3.86 0 0 0 5.458 0l6.14-6.14a.966.966 0 0 0 0-1.364m-2.047-6.141a3.86 3.86 0 0 0-5.458 0l-6.14 6.14a.964.964 0 1 0 1.364 1.364l6.141-6.14a1.93 1.93 0 0 1 2.729 0 .965.965 0 1 0 1.364-1.364m-2.047 2.047a.964.964 0 0 0-1.364 0l-6.14 6.141a1.929 1.929 0 1 1-2.729-2.729l6.022-6.022a1.93 1.93 0 0 0-2.729 0L3.13 9.271a3.86 3.86 0 0 0 5.458 5.458l6.14-6.141a.963.963 0 0 0 .001-1.364"})))},{name:"stackexchange",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M4 11.606h16v3.272H4zM4 7.377h16v3.272H4zM17.514 3H6.55C5.147 3 4 4.169 4 5.614v.848h16v-.85C20 4.167 18.895 3 17.514 3M4 15.813v.85c0 1.445 1.147 2.614 2.55 2.614h6.799v3.463l3.357-3.463h.744c1.402 0 2.55-1.169 2.55-2.614v-.85z"})))},{name:"stackoverflow",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M18.18 20.103V14.78h1.767v7.09H4v-7.09h1.767v5.323z"}),React.createElement("path",{d:"m7.717 14.275 8.673 1.813.367-1.744-8.673-1.813zm1.147-4.13 8.031 3.74.734-1.606-8.031-3.763zm2.226-3.946 6.815 5.667 1.124-1.354-6.815-5.667zM15.495 2l-1.423 1.055 5.277 7.113 1.423-1.055zM7.533 18.314h8.857v-1.767H7.533z"})))},{name:"stumbleupon",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 4.294a4.47 4.47 0 0 0-4.471 4.471v6.353a1.059 1.059 0 1 1-2.118 0v-2.824H2v2.941a4.471 4.471 0 0 0 8.942 0v-6.47a1.059 1.059 0 1 1 2.118 0v1.294l1.412.647 2-.647V8.765A4.473 4.473 0 0 0 12 4.294m1.059 8.059v2.882a4.471 4.471 0 0 0 8.941 0v-2.824h-3.412v2.824a1.059 1.059 0 1 1-2.118 0v-2.882l-2 .647z"})))},{name:"substack",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19.904 9.182H4.095V7.054h15.81v2.127M4.095 11.109V21L12 16.583 19.905 21v-9.891zM19.905 3H4.095v2.127h15.81z"})))},{name:"telegram",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m3.08 14.757s-.25.625-.936.325l-2.541-1.949-1.63 1.486s-.127.096-.266.036c0 0-.12-.011-.27-.486s-.911-2.972-.911-2.972L6 12.349s-.387-.137-.425-.438c-.037-.3.437-.462.437-.462l10.03-3.934s.824-.362.824.238z"})))},{name:"threads",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 192 192"},React.createElement("g",null,React.createElement("path",{d:"M141.537 88.988a67 67 0 0 0-2.518-1.143c-1.482-27.307-16.403-42.94-41.457-43.1h-.34c-14.986 0-27.449 6.396-35.12 18.036l13.779 9.452c5.73-8.695 14.724-10.548 21.348-10.548h.229c8.249.053 14.474 2.452 18.503 7.129 2.932 3.405 4.893 8.111 5.864 14.05-7.314-1.243-15.224-1.626-23.68-1.14-23.82 1.371-39.134 15.264-38.105 34.568.522 9.792 5.4 18.216 13.735 23.719 7.047 4.652 16.124 6.927 25.557 6.412 12.458-.683 22.231-5.436 29.049-14.127 5.178-6.6 8.453-15.153 9.899-25.93 5.937 3.583 10.337 8.298 12.767 13.966 4.132 9.635 4.373 25.468-8.546 38.376-11.319 11.308-24.925 16.2-45.488 16.351-22.809-.169-40.06-7.484-51.275-21.742C35.236 139.966 29.808 120.682 29.605 96c.203-24.682 5.63-43.966 16.133-57.317C56.954 24.425 74.204 17.11 97.013 16.94c22.975.17 40.526 7.52 52.171 21.847 5.71 7.026 10.015 15.86 12.853 26.162l16.147-4.308c-3.44-12.68-8.853-23.606-16.219-32.668C147.036 9.607 125.202.195 97.07 0h-.113C68.882.194 47.292 9.642 32.788 28.08 19.882 44.485 13.224 67.315 13.001 95.932L13 96v.067c.224 28.617 6.882 51.447 19.788 67.854C47.292 182.358 68.882 191.806 96.957 192h.113c24.96-.173 42.554-6.708 57.048-21.189 18.963-18.945 18.392-42.692 12.142-57.27-4.484-10.454-13.033-18.945-24.723-24.553M98.44 129.507c-10.44.588-21.286-4.098-21.82-14.135-.397-7.442 5.296-15.746 22.461-16.735q2.948-.17 5.79-.169c6.235 0 12.068.606 17.371 1.765-1.978 24.702-13.58 28.713-23.802 29.274"})))},{name:"tiktok-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M5 3a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2zm7.531 3h2.053s-.114 2.635 2.85 2.82v2.04s-1.582.099-2.85-.87l.021 4.207a3.804 3.804 0 1 1-3.802-3.802h.533v2.082a1.73 1.73 0 0 0-1.922.648 1.727 1.727 0 0 0 1.947 2.646 1.73 1.73 0 0 0 1.19-1.642z"})))},{name:"tiktok",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12.22 2h3.42s-.19 4.394 4.75 4.702v3.396s-2.636.166-4.75-1.448l.037 7.012a6.338 6.338 0 1 1-6.34-6.339h.89v3.472a2.882 2.882 0 1 0 2.024 2.752z"})))},{name:"tripadvisor",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M21.01 9.859c.236-1.002.985-2.003.985-2.003l-3.341-.002C16.779 6.643 14.502 6 11.979 6 9.363 6 7 6.659 5.135 7.877L2 7.88s.74.988.98 1.983a4.98 4.98 0 0 0-.977 2.961 5.01 5.01 0 0 0 5.009 5.003 5 5 0 0 0 3.904-1.875l1.065 1.592 1.076-1.606a4.96 4.96 0 0 0 1.838 1.448 4.98 4.98 0 0 0 3.831.151 5.01 5.01 0 0 0 2.963-6.431 5 5 0 0 0-.679-1.247m-13.998 6.96a4 4 0 0 1-3.998-3.995 4 4 0 0 1 3.998-3.997 4 4 0 0 1 3.996 3.997 4 4 0 0 1-3.996 3.995m4.987-4.36A5.007 5.007 0 0 0 7.11 7.821c1.434-.613 3.081-.947 4.867-.947 1.798 0 3.421.324 4.853.966a4.984 4.984 0 0 0-4.831 4.619m6.288 4.134a3.97 3.97 0 0 1-3.058-.122 3.96 3.96 0 0 1-2.075-2.245v-.001a3.97 3.97 0 0 1 .118-3.056 3.97 3.97 0 0 1 2.246-2.077 4.005 4.005 0 0 1 5.135 2.366 4.006 4.006 0 0 1-2.366 5.135"}),React.createElement("path",{d:"M6.949 10.307a2.477 2.477 0 0 0-2.475 2.472 2.48 2.48 0 0 0 2.475 2.474 2.474 2.474 0 0 0 0-4.946m0 4.094a1.626 1.626 0 0 1-1.624-1.623 1.621 1.621 0 1 1 1.624 1.623M16.981 10.307a2.477 2.477 0 0 0-2.474 2.472 2.48 2.48 0 0 0 2.474 2.474 2.476 2.476 0 0 0 2.472-2.474 2.475 2.475 0 0 0-2.472-2.472m0 4.094a1.625 1.625 0 0 1-1.622-1.623 1.622 1.622 0 1 1 1.622 1.623"}),React.createElement("path",{d:"M7.778 12.778a.832.832 0 1 1-1.664.002.832.832 0 0 1 1.664-.002M16.981 11.947a.832.832 0 1 0 .002 1.666.832.832 0 0 0-.002-1.666"})))},{name:"tumblr-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M16.749 17.396c-.357.17-1.041.319-1.551.332-1.539.041-1.837-1.081-1.85-1.896V9.847h3.861v-2.91h-3.847V2.039h-2.817c-.046 0-.127.041-.138.144-.165 1.499-.867 4.13-3.783 5.181v2.484h1.945v6.282c0 2.151 1.587 5.206 5.775 5.135 1.413-.024 2.982-.616 3.329-1.126z"})))},{name:"tumblr",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-5.569 14.265c-2.446.042-3.372-1.742-3.372-2.998v-3.668H8.923v-1.45c1.703-.614 2.113-2.15 2.209-3.025.007-.06.054-.084.081-.084h1.645V8.9h2.246v1.7H12.85v3.495c.008.476.182 1.131 1.081 1.107.298-.008.697-.094.906-.194l.54 1.601c-.205.296-1.121.641-1.946.656"})))},{name:"twitch",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M16.499 8.089h-1.636v4.91h1.636zm-4.499 0h-1.637v4.91H12zM4.228 3.178 3 6.451v13.092h4.499V22h2.456l2.454-2.456h3.681L21 14.636V3.178zm15.136 10.638L16.5 16.681H12l-2.453 2.453V16.68H5.863V4.814h13.501z"})))},{name:"twitter-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M22.23 5.924a8.2 8.2 0 0 1-2.357.646 4.12 4.12 0 0 0 1.804-2.27 8.2 8.2 0 0 1-2.606.996 4.103 4.103 0 0 0-6.991 3.742 11.65 11.65 0 0 1-8.457-4.287 4.1 4.1 0 0 0-.556 2.063 4.1 4.1 0 0 0 1.825 3.415 4.1 4.1 0 0 1-1.859-.513v.052a4.104 4.104 0 0 0 3.292 4.023 4.1 4.1 0 0 1-1.853.07 4.11 4.11 0 0 0 3.833 2.85 8.24 8.24 0 0 1-5.096 1.756 8 8 0 0 1-.979-.057 11.6 11.6 0 0 0 6.29 1.843c7.547 0 11.675-6.252 11.675-11.675q0-.267-.012-.531a8.3 8.3 0 0 0 2.047-2.123"})))},{name:"twitter",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-2.534 6.71q.007.148.007.298c0 3.045-2.318 6.556-6.556 6.556a6.5 6.5 0 0 1-3.532-1.035q.27.032.55.032a4.63 4.63 0 0 0 2.862-.986 2.31 2.31 0 0 1-2.152-1.6 2.3 2.3 0 0 0 1.04-.04 2.306 2.306 0 0 1-1.848-2.259v-.029c.311.173.666.276 1.044.288a2.303 2.303 0 0 1-.713-3.076 6.54 6.54 0 0 0 4.749 2.407 2.305 2.305 0 0 1 3.926-2.101 4.6 4.6 0 0 0 1.463-.559 2.3 2.3 0 0 1-1.013 1.275c.466-.056.91-.18 1.323-.363-.31.461-.7.867-1.15 1.192"})))},{name:"untappd",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"m11 13.299-5.824 8.133c-.298.416-.8.635-1.308.572-.578-.072-1.374-.289-2.195-.879S.392 19.849.139 19.323a1.4 1.4 0 0 1 .122-1.425l5.824-8.133a3.1 3.1 0 0 1 1.062-.927l1.146-.604c.23-.121.436-.283.608-.478.556-.631 2.049-2.284 4.696-4.957l.046-.212a.13.13 0 0 1 .096-.1l.146-.037a.135.135 0 0 0 .101-.141l-.015-.18a.13.13 0 0 1 .125-.142c.176-.005.518.046 1.001.393s.64.656.692.824a.13.13 0 0 1-.095.164l-.175.044a.13.13 0 0 0-.101.141l.012.15a.13.13 0 0 1-.063.123l-.186.112c-1.679 3.369-2.764 5.316-3.183 6.046a2.2 2.2 0 0 0-.257.73l-.205 1.281A3.1 3.1 0 0 1 11 13.3zm12.739 4.598-5.824-8.133a3.1 3.1 0 0 0-1.062-.927l-1.146-.605a2.1 2.1 0 0 1-.608-.478 51 51 0 0 0-.587-.654.09.09 0 0 0-.142.018 97 97 0 0 1-1.745 3.223 1.4 1.4 0 0 0-.171.485 3.5 3.5 0 0 0 0 1.103l.01.064c.075.471.259.918.536 1.305l5.824 8.133c.296.413.79.635 1.294.574a4.76 4.76 0 0 0 2.209-.881 4.76 4.76 0 0 0 1.533-1.802 1.4 1.4 0 0 0-.122-1.425zM8.306 3.366l.175.044a.134.134 0 0 1 .101.141l-.012.15a.13.13 0 0 0 .063.123l.186.112q.465.933.869 1.721c.026.051.091.06.129.019q.655-.703 1.585-1.668a.137.137 0 0 0 .003-.19c-.315-.322-.645-.659-1.002-1.02l-.046-.212a.13.13 0 0 0-.096-.099l-.146-.037a.135.135 0 0 1-.101-.141l.015-.18a.13.13 0 0 0-.123-.142c-.175-.005-.518.045-1.002.393-.483.347-.64.656-.692.824a.13.13 0 0 0 .095.164z"})))},{name:"vimeo",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M22.396 7.164q-.139 3.039-4.245 8.32Q13.907 21 10.97 21q-1.82 0-3.079-3.359l-1.68-6.159q-.934-3.36-2.005-3.36-.234.001-1.634.98l-.978-1.261q1.541-1.353 3.037-2.708 2.056-1.774 3.084-1.869 2.429-.234 2.99 3.321.607 3.836.841 4.769.7 3.181 1.542 3.181.653 0 1.963-2.065 1.307-2.063 1.401-3.142.187-1.781-1.401-1.782-.747.001-1.541.341 1.534-5.024 5.862-4.884 3.21.095 3.024 4.161"})))},{name:"vk",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{fillRule:"evenodd",d:"M1.687 1.687C0 3.374 0 6.09 0 11.52v.96c0 5.431 0 8.146 1.687 9.833S6.09 24 11.52 24h.96c5.431 0 8.146 0 9.833-1.687S24 17.91 24 12.48v-.96c0-5.431 0-8.146-1.687-9.833S17.91 0 12.48 0h-.96C6.09 0 3.374 0 1.687 1.687M4.05 7.3c.13 6.24 3.25 9.99 8.72 9.99h.31v-3.57c2.01.2 3.53 1.67 4.14 3.57h2.84c-.78-2.84-2.83-4.41-4.11-5.01 1.28-.74 3.08-2.54 3.51-4.98h-2.58c-.56 1.98-2.22 3.78-3.8 3.95V7.3H10.5v6.92c-1.6-.4-3.62-2.34-3.71-6.92z"})))},{name:"whatsapp",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"m2.048 22 1.406-5.136a9.9 9.9 0 0 1-1.323-4.955C2.133 6.446 6.579 2 12.042 2a9.85 9.85 0 0 1 7.011 2.906 9.85 9.85 0 0 1 2.9 7.011c-.002 5.464-4.448 9.91-9.91 9.91h-.004a9.9 9.9 0 0 1-4.736-1.206zm5.497-3.172.301.179a8.2 8.2 0 0 0 4.193 1.148h.003c4.54 0 8.235-3.695 8.237-8.237a8.2 8.2 0 0 0-2.41-5.828 8.18 8.18 0 0 0-5.824-2.416c-4.544 0-8.239 3.695-8.241 8.237a8.2 8.2 0 0 0 1.259 4.384l.196.312-.832 3.04zm9.49-4.554c-.062-.103-.227-.165-.475-.289s-1.465-.723-1.692-.806-.392-.124-.557.124-.64.806-.784.971-.289.186-.536.062-1.046-.385-1.991-1.229c-.736-.657-1.233-1.468-1.378-1.715s-.015-.382.109-.505c.111-.111.248-.289.371-.434.124-.145.165-.248.248-.413s.041-.31-.021-.434-.557-1.343-.763-1.839c-.202-.483-.407-.417-.559-.425-.144-.007-.31-.009-.475-.009a.9.9 0 0 0-.66.31c-.226.248-.866.847-.866 2.066s.887 2.396 1.011 2.562 1.746 2.666 4.23 3.739c.591.255 1.052.408 1.412.522.593.189 1.133.162 1.56.098.476-.071 1.465-.599 1.671-1.177.206-.58.206-1.075.145-1.179"})))},{name:"woocommerce",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19 2H5C3.3 2 2 3.3 2 5v11c0 1.7 1.3 3 3 3h4l6 3-1-3h5c1.7 0 3-1.3 3-3V5c0-1.7-1.3-3-3-3m-1.6 4.5c-.4.8-.8 2.1-1 3.9-.3 1.8-.4 3.1-.3 4.1 0 .3 0 .5-.1.7s-.3.4-.6.4-.6-.1-.9-.4c-1-1-1.8-2.6-2.4-4.6-.7 1.4-1.2 2.4-1.6 3.1-.6 1.2-1.2 1.8-1.6 1.9-.3 0-.5-.2-.8-.7-.5-1.4-1.1-4.2-1.7-8.2 0-.3 0-.5.2-.7.1-.2.4-.3.7-.4.5 0 .9.2.9.8.3 2.3.7 4.2 1.1 5.7l2.4-4.5c.2-.4.4-.6.8-.6q.75 0 .9.9c.3 1.4.6 2.6 1 3.7.3-2.7.8-4.7 1.4-5.9.2-.3.4-.5.7-.5.2 0 .5.1.7.2q.3.3.3.6c0 .3 0 .4-.1.5"})))},{name:"wordpress",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12.158 12.786 9.46 20.625a9 9 0 0 0 5.526-.144 1 1 0 0 1-.065-.124zM3.009 12a8.99 8.99 0 0 0 5.067 8.092L3.788 8.341A8.95 8.95 0 0 0 3.009 12m15.06-.454c0-1.112-.399-1.881-.741-2.48-.456-.741-.883-1.368-.883-2.109 0-.826.627-1.596 1.51-1.596q.06.002.116.007A8.96 8.96 0 0 0 12 3.009a8.98 8.98 0 0 0-7.512 4.052c.211.007.41.011.579.011.94 0 2.396-.114 2.396-.114.484-.028.541.684.057.741 0 0-.487.057-1.029.085l3.274 9.739 1.968-5.901-1.401-3.838c-.484-.028-.943-.085-.943-.085-.485-.029-.428-.769.057-.741 0 0 1.484.114 2.368.114.94 0 2.397-.114 2.397-.114.485-.028.542.684.057.741 0 0-.488.057-1.029.085l3.249 9.665.897-2.996q.684-1.753.684-2.907m1.82-3.86q.06.428.06.924c0 .912-.171 1.938-.684 3.22l-2.746 7.94a8.98 8.98 0 0 0 4.47-7.771 8.9 8.9 0 0 0-1.1-4.313M12 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10"})))},{name:"x",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M13.982 10.622 20.54 3h-1.554l-5.693 6.618L8.745 3H3.5l6.876 10.007L3.5 21h1.554l6.012-6.989L15.868 21h5.245zm-2.128 2.474-.697-.997-5.543-7.93H8l4.474 6.4.697.996 5.815 8.318h-2.387z"})))},{name:"xanga",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M9 9h6v6H9zM3 9h6V3H3zm12 0h6V3h-6zm0 12h6v-6h-6zM3 21h6v-6H3z"})))},{name:"youtube",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M21.8 8.001s-.195-1.378-.795-1.985c-.76-.797-1.613-.801-2.004-.847-2.799-.202-6.997-.202-6.997-.202h-.009s-4.198 0-6.997.202c-.39.047-1.242.051-2.003.847-.6.607-.795 1.985-.795 1.985S2 9.62 2 11.238v1.517c0 1.618.2 3.237.2 3.237s.195 1.378.795 1.985c.761.797 1.76.771 2.205.855 1.6.153 6.8.201 6.8.201s4.203-.006 7.001-.209c.391-.047 1.243-.051 2.004-.847.6-.607.795-1.985.795-1.985s.2-1.618.2-3.237v-1.517c0-1.618-.2-3.237-.2-3.237M9.935 14.594l-.001-5.62 5.404 2.82z"})))}]},58992:(e,t,a)=>{"use strict";a.d(t,{d:()=>d});var n=a(96072),c=a.n(n),s=a(28120),i=a.n(s),r=a(51609),l=a.n(r),o=a(91135);class d extends r.PureComponent{static defaultProps={size:24};static propTypes={icon:i().string.isRequired,size:i().number,onClick:i().func,className:i().string};render(){const{size:e,onClick:t,icon:a,className:n,...s}=this.props,i=["social-logo","social-logo-"+a,n].filter(Boolean).join(" "),r=o.$.find((e=>e.name===a));if(!r)return l().createElement("svg",c()({height:e,width:e},s));return l().cloneElement(r.svg,{className:i,height:e,width:e,onClick:t,...s})}}},51119:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n,c=a(2467);function s(){this.intervals={},this.monitorInterval=null,this.windowInstance=null,this.onMessage=e=>{e.source===this.windowInstance&&this.emit("message",e.data)}}n=s.prototype,Object.assign(n,c.EventEmitter.prototype),n.emitChange=function(){this.emit("change")},n.off=n.removeListener,s.prototype.open=function(e,t,a){return t=t||Date.now(),this.windowInstance=window.open(e,t,a),this.startMonitoring(t,this.windowInstance),window.addEventListener("message",this.onMessage,!1),this},s.prototype.getScreenCenterSpecs=function(e,t){const a=void 0!==window.screenTop?window.screenTop:window.screenY,n=void 0!==window.screenLeft?window.screenLeft:window.screenX;return["width="+e,"height="+t,"top="+(a+window.innerHeight/2-t/2),"left="+(n+window.innerWidth/2-e/2)].join()},s.prototype.isOpen=function(e){let t=!1;try{t=this.intervals[e]&&this.intervals[e].closed}catch(e){}return!t},s.prototype.checkStatus=function(){for(const e in this.intervals)this.intervals.hasOwnProperty(e)&&!this.isOpen(e)&&(this.emit("close",e),delete this.intervals[e]);0===Object.keys(this.intervals).length&&(clearInterval(this.monitorInterval),delete this.monitorInterval,window.removeEventListener("message",this.onMessage))},s.prototype.startMonitoring=function(e,t){this.monitorInterval||(this.monitorInterval=setInterval(this.checkStatus.bind(this),100)),this.intervals[e]=t};const i=s},36223:(e,t,a)=>{"use strict";a.d(t,{S6:()=>c,_t:()=>s,dN:()=>i});var n=a(31280);const c=e=>(0,n.RA)((0,n.f5)(200),(0,n.ZT)(200))((0,n.$J)(e))||"",s=(e,t={})=>{const{offset:a=0}=t;return(0,n.x5)(e,{platform:"bluesky",maxChars:260-a})},i=e=>(0,n.RA)((0,n.f5)(40),(0,n.ZT)(40))((0,n.$J)(e))||""},69409:(e,t,a)=>{"use strict";a.d(t,{c:()=>s});var n=a(10790),c=a(29103);const s=e=>(0,n.jsx)(c.O,{...e,user:void 0,media:void 0,customText:""})},29103:(e,t,a)=>{"use strict";a.d(t,{O:()=>d});var n=a(10790),c=a(13022),s=a(74769),i=a(82074),r=a(40824),l=a(20079),o=a(27354);a(7628);const d=e=>{const{user:t,media:a,appendUrl:d}=e;return(0,n.jsxs)("div",{className:"bluesky-preview__post",children:[(0,n.jsx)(o.Q,{user:t}),(0,n.jsxs)("div",{children:[(0,n.jsx)(l.A,{user:t}),(0,n.jsx)(i.A,{...e,appendUrl:d??Boolean(a?.length),children:a?.length?(0,n.jsx)("div",{className:(0,c.A)("bluesky-preview__media",{"as-grid":a.length>1}),children:a.map(((e,t)=>(0,n.jsx)("div",{className:"bluesky-preview__media-item",children:e.type.startsWith("video/")?(0,n.jsx)("video",{controls:!0,children:(0,n.jsx)("source",{src:e.url,type:e.type})}):(0,n.jsx)("img",{alt:e.alt||"",src:e.url})},`bluesky-preview__media-item-${t}`)))}):null}),a?.length?null:(0,n.jsx)(r.A,{...e}),(0,n.jsx)(s.A,{})]})]})}},74769:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var n=a(10790);a(9839);const c=()=>(0,n.jsxs)("div",{className:"bluesky-preview__post-actions",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("svg",{fill:"none",width:"18",viewBox:"0 0 24 24",height:"18",style:{color:"rgb(111, 134, 159)"},"aria-hidden":"true",children:(0,n.jsx)("path",{fill:"hsl(211, 20%, 53%)",fillRule:"evenodd",clipRule:"evenodd",d:"M2.002 6a3 3 0 0 1 3-3h14a3 3 0 0 1 3 3v10a3 3 0 0 1-3 3H12.28l-4.762 2.858A1 1 0 0 1 6.002 21v-2h-1a3 3 0 0 1-3-3V6Zm3-1a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h2a1 1 0 0 1 1 1v1.234l3.486-2.092a1 1 0 0 1 .514-.142h7a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1h-14Z"})}),(0,n.jsx)("span",{children:0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("svg",{fill:"none",width:"18",viewBox:"0 0 24 24",height:"18",style:{color:"rgb(111, 134, 159)"},"aria-hidden":"true",children:(0,n.jsx)("path",{fill:"hsl(211, 20%, 53%)",fillRule:"evenodd",clipRule:"evenodd",d:"M17.957 2.293a1 1 0 1 0-1.414 1.414L17.836 5H6a3 3 0 0 0-3 3v3a1 1 0 1 0 2 0V8a1 1 0 0 1 1-1h11.836l-1.293 1.293a1 1 0 0 0 1.414 1.414l2.47-2.47a1.75 1.75 0 0 0 0-2.474l-2.47-2.47ZM20 12a1 1 0 0 1 1 1v3a3 3 0 0 1-3 3H6.164l1.293 1.293a1 1 0 1 1-1.414 1.414l-2.47-2.47a1.75 1.75 0 0 1 0-2.474l2.47-2.47a1 1 0 0 1 1.414 1.414L6.164 17H18a1 1 0 0 0 1-1v-3a1 1 0 0 1 1-1Z"})}),(0,n.jsx)("span",{children:0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("svg",{fill:"none",width:"18",viewBox:"0 0 24 24",height:"18",style:{color:"rgb(111, 134, 159)"},"aria-hidden":"true",children:(0,n.jsx)("path",{fill:"hsl(211, 20%, 53%)",fillRule:"evenodd",clipRule:"evenodd",d:"M16.734 5.091c-1.238-.276-2.708.047-4.022 1.38a1 1 0 0 1-1.424 0C9.974 5.137 8.504 4.814 7.266 5.09c-1.263.282-2.379 1.206-2.92 2.556C3.33 10.18 4.252 14.84 12 19.348c7.747-4.508 8.67-9.168 7.654-11.7-.541-1.351-1.657-2.275-2.92-2.557Zm4.777 1.812c1.604 4-.494 9.69-9.022 14.47a1 1 0 0 1-.978 0C2.983 16.592.885 10.902 2.49 6.902c.779-1.942 2.414-3.334 4.342-3.764 1.697-.378 3.552.003 5.169 1.286 1.617-1.283 3.472-1.664 5.17-1.286 1.927.43 3.562 1.822 4.34 3.764Z"})}),(0,n.jsx)("span",{children:0})]}),(0,n.jsx)("div",{children:(0,n.jsx)("svg",{fill:"none",viewBox:"0 0 24 24",width:"20",height:"20","aria-hidden":"true",children:(0,n.jsx)("path",{fill:"hsl(211, 20%, 53%)",fillRule:"evenodd",clipRule:"evenodd",d:"M2 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0Zm16 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0Zm-6-2a2 2 0 1 0 0 4 2 2 0 0 0 0-4Z"})})})]})},82074:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(10790),c=a(36223);a(34436);const s=({customText:e,url:t,children:a,appendUrl:s})=>(0,n.jsxs)("div",{className:"bluesky-preview__body",children:[e?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{children:(0,c._t)(e)}),s&&t?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("br",{}),(0,n.jsx)("a",{href:t,target:"_blank",rel:"noreferrer noopener",children:(0,c.dN)(t.replace(/^https?:\/\//,""))})]}):null]}):null,a]})},40824:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n=a(10790),c=a(31280),s=a(36223);a(19994);const i=({title:e,description:t,url:a,image:i})=>(0,n.jsxs)("div",{className:"bluesky-preview__card",children:[i?(0,n.jsx)("div",{className:"bluesky-preview__card-image",children:(0,n.jsx)("img",{src:i,alt:""})}):null,(0,n.jsxs)("div",{className:"bluesky-preview__card-text",children:[(0,n.jsx)("div",{className:"bluesky-preview__card-site",children:(0,c.GA)(a)}),(0,n.jsx)("div",{className:"bluesky-preview__card-title",children:(0,s.S6)(e)||(0,c.X2)(t)}),(0,n.jsx)("div",{className:"bluesky-preview__card-description",children:(0,c.$J)(t)})]})]})},20079:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(10790),c=a(27723);a(59835);const __=c.__,_x=c._x,s=({user:e})=>{const{displayName:t,address:a}=e||{};return(0,n.jsxs)("div",{className:"bluesky-preview__post-header",children:[(0,n.jsxs)("div",{className:"bluesky-preview__post-header-user",children:[(0,n.jsx)("span",{className:"bluesky-preview__post-header--displayname",children:t||__("Account name","jetpack-publicize-pkg")})," ",(0,n.jsx)("span",{className:"bluesky-preview__post-header--username",children:a||"username.bsky.social"})]}),(0,n.jsx)("div",{className:"bluesky-preview__post-header--separator",children:"·"}),(0,n.jsx)("div",{className:"bluesky-preview__post-header--date",children:_x("1h",'refers to the time since the post was published, e.g. "1h"',"jetpack-publicize-pkg")})]})}},27354:(e,t,a)=>{"use strict";a.d(t,{Q:()=>c});var n=a(10790);a(18546);const c=({user:e})=>{const{avatarUrl:t}=e||{};return(0,n.jsx)("div",{className:"bluesky-preview__post-sidebar",children:(0,n.jsx)("div",{className:"bluesky-preview__post-sidebar-user",children:t?(0,n.jsx)("img",{className:"bluesky-preview__post-avatar",src:t,alt:""}):(0,n.jsxs)("svg",{className:"bluesky-preview__post-avatar",viewBox:"0 0 24 24",fill:"none",stroke:"none",role:"presentation",children:[(0,n.jsx)("circle",{cx:"12",cy:"12",r:"12",fill:"#0070ff"}),(0,n.jsx)("circle",{cx:"12",cy:"9.5",r:"3.5",fill:"#fff"}),(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",fill:"#fff",d:"M 12.058 22.784 C 9.422 22.784 7.007 21.836 5.137 20.262 C 5.667 17.988 8.534 16.25 11.99 16.25 C 15.494 16.25 18.391 18.036 18.864 20.357 C 17.01 21.874 14.64 22.784 12.058 22.784 Z"})]})})})}},91937:(e,t,a)=>{"use strict";a.d(t,{x:()=>l});var n=a(10790),c=a(27723),s=a(67556),i=a(69409),r=a(29103);const __=c.__,l=({headingLevel:e,hidePostPreview:t,hideLinkPreview:a,...c})=>(0,n.jsxs)("div",{className:"social-preview bluesky-preview",children:[!t&&(0,n.jsxs)("section",{className:"social-preview__section bluesky-preview__section",children:[(0,n.jsx)(s.w,{level:e,children:
// translators: refers to a social post on Bluesky
__("Your post","jetpack-publicize-pkg")}),(0,n.jsx)("p",{className:"social-preview__section-desc",children:__("This is what your social post will look like on Bluesky:","jetpack-publicize-pkg")}),(0,n.jsx)(r.O,{...c})]}),!a&&(0,n.jsxs)("section",{className:"social-preview__section bluesky-preview__section",children:[(0,n.jsx)(s.w,{level:e,children:
// translators: refers to a link to a Bluesky post
__("Link preview","jetpack-publicize-pkg")}),(0,n.jsx)("p",{className:"social-preview__section-desc",children:__("This is what it will look like when someone shares the link to your WordPress post on Bluesky.","jetpack-publicize-pkg")}),(0,n.jsx)(i.c,{...c})]})]})},46140:(e,t,a)=>{"use strict";a.d(t,{Kq:()=>n,_I:()=>c,q8:()=>s});const n="article",c="landscape",s="portrait"},5954:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n=a(10790),c=a(31280),s=a(57466);const i=({text:e,url:t,forceUrlDisplay:a})=>{let i;return(a||(0,c.ti)(e,"a"))&&(i=(0,n.jsx)("a",{className:"facebook-preview__custom-text-post-url",href:t,rel:"nofollow noopener noreferrer",target:"_blank",children:t})),(0,n.jsxs)("p",{className:"facebook-preview__custom-text",children:[(0,n.jsx)("span",{children:(0,c.x5)(e,{platform:"facebook",maxChars:s.zl})}),i]})}},57466:(e,t,a)=>{"use strict";a.d(t,{B2:()=>r,GA:()=>s,t2:()=>i,zl:()=>c});var n=a(31280);const c=440,s=e=>e.replace(/^[^/]+[/]*/,"").replace(/\/.*$/,""),i=e=>(0,n.RA)((0,n.f5)(110),(0,n.ZT)(110))((0,n.$J)(e))||"",r=e=>(0,n.RA)((0,n.f5)(200),(0,n.ZT)(200))((0,n.$J)(e))||""},72691:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n=a(27723),c=a(51609),s=a(46140);const __=n.__,i=({mode:e})=>{const[t,a]=(0,c.useState)(e),[n,i]=(0,c.useState)(!0),r=(0,c.useCallback)((({target:e})=>{if(!t){const t=e;a(t.naturalWidth>t.naturalHeight?s._I:s.q8)}i(!1)}),[t]),l=(0,c.useCallback)((()=>i(!1)),[]);return[t,n,{alt:__("Facebook Preview Thumbnail","jetpack-publicize-pkg"),onLoad:r,onError:l}]}},57181:(e,t,a)=>{"use strict";a.d(t,{D:()=>o});var n=a(10790),c=a(46140),s=a(5954),i=a(72691),r=a(72870),l=a(77114);const o=({url:e,customImage:t,user:a,customText:o,imageMode:d})=>{const[u,p,m]=(0,i.A)({mode:d}),h="is-"+(u===c.q8?"portrait":"landscape");return(0,n.jsxs)("div",{className:"facebook-preview__post",children:[(0,n.jsx)(l.A,{user:void 0}),(0,n.jsx)("div",{className:"facebook-preview__content",children:(0,n.jsxs)("div",{className:`facebook-preview__window ${h} ${t&&p?"is-loading":""}`,children:[(0,n.jsx)("div",{className:`facebook-preview__custom-image ${h}`,children:(0,n.jsx)("img",{src:t,...m})}),(0,n.jsx)(l.A,{user:a,timeElapsed:!0,hideOptions:!0}),o&&(0,n.jsx)(s.A,{text:o,url:e,forceUrlDisplay:!0})]})}),(0,n.jsx)(r.A,{})]})}},73066:(e,t,a)=>{"use strict";a.d(t,{l:()=>p});var n=a(10790),c=a(27723),s=a(46140),i=a(5954),r=a(57466),l=a(72691),o=a(72870),d=a(77114),u=a(29659);a(81500);const __=c.__,p=({url:e,title:t,description:a,image:c,user:p,customText:m,type:h,imageMode:g,compactDescription:v})=>{const[w,b,f]=(0,l.A)({mode:g}),k=h===s.Kq,_="is-"+(k&&!c||w===s.q8?"portrait":"landscape");return(0,n.jsxs)("div",{className:"facebook-preview__post",children:[(0,n.jsx)(d.A,{user:p}),(0,n.jsxs)("div",{className:"facebook-preview__content",children:[m&&(0,n.jsx)(i.A,{text:m,url:e}),(0,n.jsxs)("div",{className:`facebook-preview__body ${_} ${c&&b?"is-loading":""}`,children:[(c||k)&&(0,n.jsx)("div",{className:`facebook-preview__image ${c?"":"is-empty"} ${_}`,children:c&&(0,n.jsx)("img",{src:c,...f})}),(0,n.jsx)("div",{className:"facebook-preview__text",children:(0,n.jsxs)("div",{className:"facebook-preview__text-wrapper",children:[(0,n.jsx)("div",{className:"facebook-preview__url",children:(0,r.GA)(e)}),(0,n.jsx)("div",{className:"facebook-preview__title",children:(0,r.t2)(t)||(0,r.GA)(e)}),(0,n.jsxs)("div",{className:"facebook-preview__description "+(v?"is-compact":""),children:[a&&(0,r.B2)(a),k&&!a&&
// translators: Default description for a Facebook post
__("Visit the post for more.","jetpack-publicize-pkg")]}),(0,n.jsx)("div",{className:"facebook-preview__info",children:(0,n.jsx)(u.A,{name:"info"})})]})})]})]}),(0,n.jsx)(o.A,{})]})}},73188:(e,t,a)=>{"use strict";a.d(t,{h:()=>o});var n=a(10790),c=a(46140),s=a(5954),i=a(72691),r=a(72870),l=a(77114);a(81500);const o=({url:e,user:t,customText:a,media:o,imageMode:d})=>{const[u]=(0,i.A)({mode:d}),p="is-"+(u===c.q8?"portrait":"landscape");return(0,n.jsxs)("div",{className:"facebook-preview__post",children:[(0,n.jsx)(l.A,{user:t}),(0,n.jsxs)("div",{className:"facebook-preview__content",children:[a&&(0,n.jsx)(s.A,{text:a,url:e,forceUrlDisplay:!0}),(0,n.jsx)("div",{className:"facebook-preview__body",children:o?(0,n.jsx)("div",{className:`facebook-preview__media ${p}`,children:o.map(((e,t)=>(0,n.jsx)("div",{className:`facebook-preview__media-item ${p}`,children:e.type.startsWith("video/")?(0,n.jsx)("video",{controls:!0,children:(0,n.jsx)("source",{src:e.url,type:e.type})}):(0,n.jsx)("img",{alt:e.alt||"",src:e.url})},`facebook-preview__media-item-${t}`)))}):null})]}),(0,n.jsx)(r.A,{})]})}},72870:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n=a(10790),c=a(27723),s=a(29659);a(87748);const __=c.__,i=()=>(0,n.jsx)("ul",{className:"facebook-preview__post-actions",children:[{icon:"like",
// translators: Facebook "Like" action
label:__("Like","jetpack-publicize-pkg")},{icon:"comment",
// translators: Facebook "Comment" action
label:__("Comment","jetpack-publicize-pkg")},{icon:"share",
// translators: Facebook "Share" action
label:__("Share","jetpack-publicize-pkg")}].map((({icon:e,label:t})=>(0,n.jsxs)("li",{children:[(0,n.jsx)(s.A,{name:e}),(0,n.jsx)("span",{children:t})]},e)))})},77114:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(10790),c=a(27723),s=a(51609),i=a(29659);a(53290);const __=c.__,_x=c._x,r="data:image/png;base64,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",l=({user:e,timeElapsed:t,hideOptions:a})=>{const[c,l]=(0,s.useState)(e?.avatarUrl||r),o=(0,s.useCallback)((()=>{c!==r&&l(r)}),[c]);return(0,n.jsxs)("div",{className:"facebook-preview__post-header",children:[(0,n.jsxs)("div",{className:"facebook-preview__post-header-content",children:[(0,n.jsx)("img",{className:"facebook-preview__post-header-avatar",src:c,alt:"",onError:o}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"facebook-preview__post-header-name",children:e?.displayName||
// translators: name of a fictional Facebook User
__("Anonymous User","jetpack-publicize-pkg")}),(0,n.jsxs)("div",{className:"facebook-preview__post-header-share",children:[(0,n.jsx)("span",{className:"facebook-preview__post-header-time",children:t?__(
// translators: short version of `1 hour`
"1h","jetpack-publicize-pkg"):_x(
// translators: temporal indication of when a post was published
"Just now","","jetpack-publicize-pkg")}),(0,n.jsx)("span",{className:"facebook-preview__post-header-dot","aria-hidden":"true",children:"·"}),(0,n.jsx)(i.A,{name:"public"})]})]})]}),!a&&(0,n.jsx)("div",{className:"facebook-preview__post-header-more"})]})}},29659:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var n=a(10790);a(15583);const c=({name:e})=>(0,n.jsx)("i",{className:`facebook-preview__post-icon facebook-preview__post-icon-${e}`})},87070:(e,t,a)=>{"use strict";a.d(t,{k:()=>o});var n=a(10790),c=a(27723),s=a(67556),i=a(73066),r=a(57181),l=a(73188);const __=c.__,o=({headingLevel:e,hideLinkPreview:t,hidePostPreview:a,...c})=>{const o=!!c.media?.length,d=!!c.customImage;return(0,n.jsxs)("div",{className:"social-preview facebook-preview",children:[!a&&(0,n.jsxs)("section",{className:"social-preview__section facebook-preview__section",children:[(0,n.jsx)(s.A,{level:e,children:
// translators: refers to a social post on Facebook
__("Your post","jetpack-publicize-pkg")}),(0,n.jsx)("p",{className:"social-preview__section-desc",children:__("This is what your social post will look like on Facebook:","jetpack-publicize-pkg")}),o?(0,n.jsx)(l.h,{...c}):(0,n.jsx)(i.l,{...c})]}),!t&&(0,n.jsxs)("section",{className:"social-preview__section facebook-preview__section",children:[(0,n.jsx)(s.A,{level:e,children:
// translators: refers to a link to a Facebook post
__("Link preview","jetpack-publicize-pkg")}),(0,n.jsx)("p",{className:"social-preview__section-desc",children:__("This is what it will look like when someone shares the link to your WordPress post on Facebook.","jetpack-publicize-pkg")}),d?(0,n.jsx)(r.D,{...c}):(0,n.jsx)(i.l,{...c,compactDescription:!0,customText:"",user:void 0})]})]})}},8797:(e,t,a)=>{"use strict";a.d(t,{m:()=>o});var n=a(10790),c=a(31280);a(88904);const s=160,i=e=>{const t=e.startsWith("https://")?"https://":"http://",a=t+e.replace(t,"").split("/").join(" › ");return(0,c.RA)((0,c.f5)(68),(0,c.ZT)(68))(a)},r=(0,c.RA)((0,c.f5)(63),(0,c.y5)(23,73),(0,c.ZT)(63)),l=(0,c.RA)((0,c.f5)(s),(0,c.y5)(80,170),(0,c.ZT)(s)),o=({description:e="",siteTitle:t,title:a="",url:s=""})=>{const o=(0,c.GA)(s);return(0,n.jsx)("div",{className:"search-preview",children:(0,n.jsxs)("div",{className:"search-preview__display",children:[(0,n.jsxs)("div",{className:"search-preview__header",children:[(0,n.jsxs)("div",{className:"search-preview__branding",children:[(0,n.jsx)("img",{className:"search-preview__icon",src:`https://www.google.com/s2/favicons?sz=128&domain_url=${o}`,alt:""}),(0,n.jsxs)("div",{className:"search-preview__site",children:[(0,n.jsx)("div",{className:"search-preview__site--title",children:t||o}),(0,n.jsx)("div",{className:"search-preview__url",children:i(s)})]})]}),(0,n.jsx)("div",{className:"search-preview__menu",children:(0,n.jsx)("svg",{focusable:"false",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{d:"M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"})})})]}),(0,n.jsx)("div",{className:"search-preview__title",children:r(a)}),(0,n.jsx)("div",{className:"search-preview__description",children:l((0,c.$J)(e))})]})})}},31280:(e,t,a)=>{"use strict";a.d(t,{$J:()=>u,GA:()=>i,RA:()=>d,TR:()=>w,X2:()=>p,ZT:()=>o,f5:()=>r,hV:()=>v,tX:()=>h,ti:()=>m,vI:()=>g,x5:()=>f,y5:()=>l});var n=a(10790),c=a(86087),s=a(27723);const i=e=>e.replace(/^[^/]+[/]*/,"").replace(/\/.*$/,""),r=e=>t=>t.length<=e&&t,l=(e,t)=>a=>{const n=a.slice(0,t),c=n.lastIndexOf(" ");return c>e&&c<t&&n.slice(0,c).concat("…")},o=e=>t=>t.slice(0,e).concat("…"),d=(...e)=>t=>e.find((e=>!1!==e(t)))?.(t),u=(e,t=[])=>{const a=new RegExp(`(<([^${t.join("")}>]+)>)`,"gi");return e?e.replace(a,""):""},p=e=>u(e).substring(0,50),m=(e,t)=>new RegExp(`<${t}[^>]*>`,"gi").test(e),h=new Intl.DateTimeFormat("en-GB",{day:"numeric",month:"short"}).format,g=new Intl.DateTimeFormat("en-US",{day:"2-digit",month:"2-digit",year:"numeric"}).format,v=new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric"}).format,w=new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric",year:"numeric"}).format,b={twitter:"https://twitter.com/hashtag/%1$s",facebook:"https://www.facebook.com/hashtag/%1$s",linkedin:"https://www.linkedin.com/feed/hashtag/?keywords=%1$s",instagram:"https://www.instagram.com/explore/tags/%1$s",mastodon:"https://%2$s/tags/%1$s",nextdoor:"https://nextdoor.com/hashtag/%1$s",threads:"https://www.threads.net/search?q=%1$s&serp_type=tags",bluesky:"https://bsky.app/hashtag/%1$s"};function f(e,t){const{platform:a,maxChars:i,maxLines:r,hyperlinkHashtags:l=!0,hyperlinkUrls:d="instagram"!==a}=t;let p=u(e);if(p=p.replaceAll(/(?:\s*[\n\r]){2,}/g,"\n\n"),i&&p.length>i&&(p=o(i)(p)),r){const e=p.split("\n");e.length>r&&(p=e.slice(0,r).join("\n"))}const m={};if(d){(p.match(/(https?:\/\/\S+)/g)||[]).forEach(((e,t)=>{m[`Link${t}`]=(0,n.jsx)("a",{href:e,rel:"noopener noreferrer",target:"_blank",children:e}),p=p.replace(e,`<Link${t} />`)}))}if(l&&b[a]){const e=p.matchAll(/(^|\s)#(\w+)/g),c=b[a];[...e].forEach((([e,a,i],r)=>{const l=(0,s.sprintf)(c,i,t.hashtagDomain);m[`Hashtag${r}`]=(0,n.jsx)("a",{href:l,rel:"noopener noreferrer",target:"_blank",children:`#${i}`}),p=p.replace(e,`${a}<Hashtag${r} />`)}))}return p=p.replace(/\n/g,"<br />"),m.br=(0,n.jsx)("br",{}),(0,c.createInterpolateElement)(p,m)}},41266:(e,t,a)=>{"use strict";a.d(t,{_:()=>c,v:()=>n});const n=120,c=2},51296:(e,t,a)=>{"use strict";a.d(t,{X:()=>c});var n=a(10790);const c=()=>(0,n.jsx)("svg",{color:"rgb(38, 38, 38)",fill:"rgb(38, 38, 38)",height:"24",role:"img",viewBox:"0 0 24 24",width:"24",children:(0,n.jsx)("polygon",{fill:"none",points:"20 21 12 13.44 4 21 4 3 20 3 20 21",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2"})})},29791:(e,t,a)=>{"use strict";a.d(t,{M:()=>c});var n=a(10790);const c=()=>(0,n.jsx)("svg",{color:"rgb(38, 38, 38)",fill:"rgb(38, 38, 38)",height:"24",role:"img",viewBox:"0 0 24 24",width:"24",children:(0,n.jsx)("path",{d:"M20.656 17.008a9.993 9.993 0 1 0-3.59 3.615L22 22Z",fill:"none",stroke:"currentColor",strokeLinejoin:"round",strokeWidth:"2"})})},96969:(e,t,a)=>{"use strict";a.d(t,{f:()=>c});var n=a(10790);const c=()=>(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 340 340",width:"340",height:"340",children:(0,n.jsx)("path",{fill:"#DDD",d:"m169,.5a169,169 0 1,0 2,0zm0,86a76,76 0 1 1-2,0zM57,287q27-35 67-35h92q40,0 67,35a164,164 0 0,1-226,0"})})},91870:(e,t,a)=>{"use strict";a.d(t,{B:()=>c});var n=a(10790);const c=()=>(0,n.jsx)("svg",{color:"rgb(38, 38, 38)",fill:"rgb(38, 38, 38)",height:"24",role:"img",viewBox:"0 0 24 24",width:"24",children:(0,n.jsx)("path",{d:"M16.792 3.904A4.989 4.989 0 0 1 21.5 9.122c0 3.072-2.652 4.959-5.197 7.222-2.512 2.243-3.865 3.469-4.303 3.752-.477-.309-2.143-1.823-4.303-3.752C5.141 14.072 2.5 12.167 2.5 9.122a4.989 4.989 0 0 1 4.708-5.218 4.21 4.21 0 0 1 3.675 1.941c.84 1.175.98 1.763 1.12 1.763s.278-.588 1.11-1.766a4.17 4.17 0 0 1 3.679-1.938m0-2a6.04 6.04 0 0 0-4.797 2.127 6.052 6.052 0 0 0-4.787-2.127A6.985 6.985 0 0 0 .5 9.122c0 3.61 2.55 5.827 5.015 7.97.283.246.569.494.853.747l1.027.918a44.998 44.998 0 0 0 3.518 3.018 2 2 0 0 0 2.174 0 45.263 45.263 0 0 0 3.626-3.115l.922-.824c.293-.26.59-.519.885-.774 2.334-2.025 4.98-4.32 4.98-7.94a6.985 6.985 0 0 0-6.708-7.218Z"})})},90783:(e,t,a)=>{"use strict";a.d(t,{W:()=>c});var n=a(10790);const c=()=>(0,n.jsxs)("svg",{width:"17",height:"5",viewBox:"0 0 17 5",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("path",{d:"M2.11865 3.5C2.67094 3.5 3.11865 3.05228 3.11865 2.5C3.11865 1.94772 2.67094 1.5 2.11865 1.5C1.56637 1.5 1.11865 1.94772 1.11865 2.5C1.11865 3.05228 1.56637 3.5 2.11865 3.5Z",fill:"black",stroke:"black",strokeWidth:"2"}),(0,n.jsx)("path",{d:"M8.55933 3.5C9.11161 3.5 9.55933 3.05228 9.55933 2.5C9.55933 1.94772 9.11161 1.5 8.55933 1.5C8.00704 1.5 7.55933 1.94772 7.55933 2.5C7.55933 3.05228 8.00704 3.5 8.55933 3.5Z",fill:"black",stroke:"black",strokeWidth:"2"}),(0,n.jsx)("path",{d:"M15 3.5C15.5523 3.5 16 3.05228 16 2.5C16 1.94772 15.5523 1.5 15 1.5C14.4477 1.5 14 1.94772 14 2.5C14 3.05228 14.4477 3.5 15 3.5Z",fill:"black",stroke:"black",strokeWidth:"2"})]})},16993:(e,t,a)=>{"use strict";a.d(t,{S:()=>c});var n=a(10790);const c=()=>(0,n.jsxs)("svg",{color:"rgb(38, 38, 38)",fill:"rgb(38, 38, 38)",height:"24",role:"img",viewBox:"0 0 24 24",width:"24",children:[(0,n.jsx)("line",{fill:"none",stroke:"currentColor",strokeLinejoin:"round",strokeWidth:"2",x1:"22",x2:"9.218",y1:"3",y2:"10.083"}),(0,n.jsx)("polygon",{fill:"none",points:"11.698 20.334 22 3.001 2 3.001 9.218 10.084 11.698 20.334",stroke:"currentColor",strokeLinejoin:"round",strokeWidth:"2"})]})},57984:(e,t,a)=>{"use strict";a.d(t,{l:()=>m});var n=a(10790),c=a(27723),s=a(31280),i=a(41266),r=a(51296),l=a(29791),o=a(96969),d=a(91870),u=a(90783),p=a(16993);a(34006);const __=c.__;function m({image:e,media:t,name:a,profileImage:c,caption:m}){const h=a||"username",g=t?.[0];return(0,n.jsx)("div",{className:"instagram-preview__wrapper",children:(0,n.jsxs)("section",{className:"instagram-preview__container",children:[(0,n.jsxs)("div",{className:"instagram-preview__header",children:[(0,n.jsx)("div",{className:"instagram-preview__header--avatar",children:c?(0,n.jsx)("img",{src:c,alt:""}):(0,n.jsx)(o.f,{})}),(0,n.jsxs)("div",{className:"instagram-preview__header--profile",children:[(0,n.jsx)("div",{className:"instagram-preview__header--profile-name",children:h}),(0,n.jsx)("div",{className:"instagram-preview__header--profile-menu",children:(0,n.jsx)(u.W,{})})]})]}),(0,n.jsx)("div",{className:"instagram-preview__media",children:g?(0,n.jsx)("div",{className:"instagram-preview__media-item",children:g.type.startsWith("video/")?(0,n.jsx)("video",{controls:!1,className:"instagram-preview__media--video",children:(0,n.jsx)("source",{src:g.url,type:g.type})}):(0,n.jsx)("img",{className:"instagram-preview__media--image",src:g.url,alt:""})}):(0,n.jsx)("img",{className:"instagram-preview__media--image",src:e,alt:""})}),(0,n.jsxs)("div",{className:"instagram-preview__content",children:[(0,n.jsxs)("section",{className:"instagram-preview__content--actions",children:[(0,n.jsxs)("div",{className:"instagram-preview__content--actions-primary",children:[(0,n.jsx)(d.B,{}),(0,n.jsx)(l.M,{}),(0,n.jsx)(p.S,{})]}),(0,n.jsx)("div",{className:"instagram-preview__content--actions-secondary",children:(0,n.jsx)(r.X,{})})]}),(0,n.jsxs)("div",{className:"instagram-preview__content--body",children:[(0,n.jsx)("div",{className:"instagram-preview__content--name",children:h})," ",m?(0,n.jsx)("div",{className:"instagram-preview__content--text",children:(0,s.x5)(m,{platform:"instagram",maxChars:i.v,maxLines:i._})}):null]}),(0,n.jsx)("div",{className:"instagram-preview__content--footer",children:(0,n.jsx)("span",{children:__("View one comment","jetpack-publicize-pkg")})})]})]})})}},11260:(e,t,a)=>{"use strict";a.d(t,{_:()=>c,v:()=>n});const n=212,c=3},41047:(e,t,a)=>{"use strict";a.d(t,{f:()=>c});var n=a(10790);const c=()=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 128 128",width:"128",height:"128",children:[(0,n.jsx)("path",{fill:"#e7e2dc",d:"M0 0h128v128H0z"}),(0,n.jsx)("path",{d:"M88.41 84.67a32 32 0 10-48.82 0 66.13 66.13 0 0148.82 0z",fill:"#788fa5"}),(0,n.jsx)("path",{d:"M88.41 84.67a32 32 0 01-48.82 0A66.79 66.79 0 000 128h128a66.79 66.79 0 00-39.59-43.33z",fill:"#9db3c8"}),(0,n.jsx)("path",{d:"M64 96a31.93 31.93 0 0024.41-11.33 66.13 66.13 0 00-48.82 0A31.93 31.93 0 0064 96z",fill:"#56687a"})]})},78312:(e,t,a)=>{"use strict";a.d(t,{X:()=>i});var n=a(10790),c=a(31280),s=a(48914);function i(e){return(0,n.jsx)(s.$,{name:"",profileImage:"",...e,description:"",media:void 0,title:e.title||(0,c.X2)(e.description)})}},48914:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});var n=a(10790),c=a(27723),s=a(31280),i=a(11260),r=a(41047);a(57482);const __=c.__;function l({articleReadTime:e=5,image:t,jobTitle:a,name:l,profileImage:o,description:d,media:u,title:p,url:m}){const h=!!u?.length;return(0,n.jsx)("div",{className:"linkedin-preview__wrapper",children:(0,n.jsxs)("section",{className:"linkedin-preview__container "+(h?"has-media":""),children:[(0,n.jsxs)("div",{className:"linkedin-preview__header",children:[(0,n.jsx)("div",{className:"linkedin-preview__header--avatar",children:o?(0,n.jsx)("img",{src:o,alt:""}):(0,n.jsx)(r.f,{})}),(0,n.jsxs)("div",{className:"linkedin-preview__header--profile",children:[(0,n.jsxs)("div",{className:"linkedin-preview__header--profile-info",children:[(0,n.jsx)("div",{className:"linkedin-preview__header--profile-name",children:l||__("Account Name","jetpack-publicize-pkg")}),(0,n.jsx)("span",{children:"•"}),(0,n.jsx)("div",{className:"linkedin-preview__header--profile-actor",children:
// translators: refers to the actor level of the post being shared, e.g. "1st", "2nd", "3rd", etc.
__("1st","jetpack-publicize-pkg")})]}),a?(0,n.jsx)("div",{className:"linkedin-preview__header--profile-title",children:a}):null,(0,n.jsxs)("div",{className:"linkedin-preview__header--profile-meta",children:[(0,n.jsx)("span",{children:
// translators: refers to the time since the post was published, e.g. "1h"
__("1h","jetpack-publicize-pkg")}),(0,n.jsx)("span",{children:"•"}),(0,n.jsx)("svg",{viewBox:"0 0 16 16",fill:"currentColor",width:"16",height:"16",focusable:"false",children:(0,n.jsx)("path",{d:"M8 1a7 7 0 107 7 7 7 0 00-7-7zM3 8a5 5 0 011-3l.55.55A1.5 1.5 0 015 6.62v1.07a.75.75 0 00.22.53l.56.56a.75.75 0 00.53.22H7v.69a.75.75 0 00.22.53l.56.56a.75.75 0 01.22.53V13a5 5 0 01-5-5zm6.24 4.83l2-2.46a.75.75 0 00.09-.8l-.58-1.16A.76.76 0 0010 8H7v-.19a.51.51 0 01.28-.45l.38-.19a.74.74 0 01.68 0L9 7.5l.38-.7a1 1 0 00.12-.48v-.85a.78.78 0 01.21-.53l1.07-1.09a5 5 0 01-1.54 9z"})})]})]})]}),(0,n.jsxs)("div",{className:"linkedin-preview__content",children:[d?(0,n.jsx)("div",{className:"linkedin-preview__caption",children:(0,s.x5)(d,{platform:"linkedin",maxChars:i.v,maxLines:i._})}):null,h?(0,n.jsx)("div",{className:"linkedin-preview__media",children:u.map(((e,t)=>(0,n.jsx)("div",{className:"linkedin-preview__media-item",children:e.type.startsWith("video/")?(0,n.jsx)("video",{controls:!0,children:(0,n.jsx)("source",{src:e.url,type:e.type})}):(0,n.jsx)("img",{alt:e.alt||"",src:e.url})},`linkedin-preview__media-item-${t}`)))}):(0,n.jsxs)("article",{children:[t?(0,n.jsx)("img",{className:"linkedin-preview__image",src:t,alt:""}):null,m?(0,n.jsxs)("div",{className:"linkedin-preview__description",children:[(0,n.jsx)("h2",{className:"linkedin-preview__description--title",children:p||(0,s.X2)(d)}),(0,n.jsxs)("div",{className:"linkedin-preview__description--meta",children:[(0,n.jsx)("span",{className:"linkedin-preview__description--url",children:(0,s.GA)(m)}),(0,n.jsx)("span",{children:"•"}),(0,n.jsx)("span",{children:(0,c.sprintf)(
// translators: %d is the number of minutes it takes to read the article
__("%d min read","jetpack-publicize-pkg"),e)})]})]}):null]})]})]})})}},7368:(e,t,a)=>{"use strict";a.d(t,{G:()=>l});var n=a(10790),c=a(27723),s=a(67556),i=a(78312),r=a(48914);const __=c.__,l=({headingLevel:e,hideLinkPreview:t,hidePostPreview:a,...c})=>(0,n.jsxs)("div",{className:"social-preview linkedin-preview",children:[!a&&(0,n.jsxs)("section",{className:"social-preview__section linkedin-preview__section",children:[(0,n.jsx)(s.A,{level:e,children:
// translators: refers to a social post on LinkedIn
__("Your post","jetpack-publicize-pkg")}),(0,n.jsx)("p",{className:"social-preview__section-desc",children:__("This is what your social post will look like on LinkedIn:","jetpack-publicize-pkg")}),(0,n.jsx)(r.$,{...c})]}),!t&&(0,n.jsxs)("section",{className:"social-preview__section linkedin-preview__section",children:[(0,n.jsx)(s.A,{level:e,children:
// translators: refers to a link to a LinkedIn post
__("Link preview","jetpack-publicize-pkg")}),(0,n.jsx)("p",{className:"social-preview__section-desc",children:__("This is what it will look like when someone shares the link to your WordPress post on LinkedIn.","jetpack-publicize-pkg")}),(0,n.jsx)(i.X,{...c,name:"",profileImage:""})]})]})},50121:(e,t,a)=>{"use strict";a.d(t,{k:()=>c,w:()=>n});const n="mastodon.social",c="data:image/png;base64,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"},26065:(e,t,a)=>{"use strict";a.d(t,{DQ:()=>l,E6:()=>r,gD:()=>i,w0:()=>o});var n=a(31280),c=a(50121);const s=/^@([^@]*)@([^@]*)$/i,i=e=>(0,n.RA)((0,n.f5)(200),(0,n.ZT)(200))((0,n.$J)(e))||"",r=(e,t)=>{const{instance:a,offset:c}=t;return(0,n.x5)(e,{platform:"mastodon",maxChars:470-c,hashtagDomain:a})},l=e=>(0,n.RA)((0,n.f5)(30),(0,n.ZT)(30))((0,n.$J)(e))||"",o=e=>{const t=e.match(s);return{username:t?.[1]||"",instance:t?.[2]||c.w}}},82779:(e,t,a)=>{"use strict";a.d(t,{i:()=>l});var n=a(10790),c=a(13123),s=a(5240),i=a(32734),r=a(53633);a(44288);const l=e=>{const{user:t}=e;return(0,n.jsxs)("div",{className:"mastodon-preview__post",children:[(0,n.jsx)(r.A,{user:t}),(0,n.jsx)(s.A,{...e}),(0,n.jsx)(i.A,{...e,customImage:""}),(0,n.jsx)(c.A,{})]})}},83945:(e,t,a)=>{"use strict";a.d(t,{_:()=>o});var n=a(10790),c=a(13022),s=a(13123),i=a(5240),r=a(32734),l=a(53633);a(44288);const o=e=>{const{user:t,media:a}=e;return(0,n.jsxs)("div",{className:"mastodon-preview__post",children:[(0,n.jsx)(l.A,{user:t}),(0,n.jsx)(i.A,{...e,children:a?.length?(0,n.jsx)("div",{className:(0,c.A)("mastodon-preview__media",{"as-grid":a.length>1}),children:a.map(((e,t)=>(0,n.jsx)("div",{className:"mastodon-preview__media-item",children:e.type.startsWith("video/")?(0,n.jsx)("video",{controls:!0,children:(0,n.jsx)("source",{src:e.url,type:e.type})}):(0,n.jsx)("img",{alt:e.alt||"",src:e.url})},`mastodon-preview__media-item-${t}`)))}):null}),a?.length?null:(0,n.jsx)(r.A,{...e}),(0,n.jsx)(s.A,{})]})}},13123:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var n=a(10790);a(7683);const c=()=>(0,n.jsxs)("div",{className:"mastodon-preview__post-actions",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",height:"24",viewBox:"0 -960 960 960",width:"24","aria-hidden":"true",children:(0,n.jsx)("path",{d:"M760-200v-160q0-50-35-85t-85-35H273l144 144-57 56-240-240 240-240 57 56-144 144h367q83 0 141.5 58.5T840-360v160h-80Z"})})," ",(0,n.jsx)("span",{children:0})]}),(0,n.jsx)("div",{children:(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",height:"24",viewBox:"0 -960 960 960",width:"24","aria-hidden":"true",children:(0,n.jsx)("path",{d:"M280-80 120-240l160-160 56 58-62 62h406v-160h80v240H274l62 62-56 58Zm-80-440v-240h486l-62-62 56-58 160 160-160 160-56-58 62-62H280v160h-80Z"})})}),(0,n.jsx)("div",{children:(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",height:"24",viewBox:"0 -960 960 960",width:"24","aria-hidden":"true",children:(0,n.jsx)("path",{d:"m354-287 126-76 126 77-33-144 111-96-146-13-58-136-58 135-146 13 111 97-33 143ZM233-120l65-281L80-590l288-25 112-265 112 265 288 25-218 189 65 281-247-149-247 149Zm247-350Z"})})}),(0,n.jsx)("div",{children:(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",height:"24",viewBox:"0 -960 960 960",width:"24","aria-hidden":"true",children:(0,n.jsx)("path",{d:"M200-120v-640q0-33 23.5-56.5T280-840h400q33 0 56.5 23.5T760-760v640L480-240 200-120Zm80-122 200-86 200 86v-518H280v518Zm0-518h400-400Z"})})}),(0,n.jsx)("div",{children:(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",height:"24",viewBox:"0 -960 960 960",width:"24","aria-hidden":"true",children:(0,n.jsx)("path",{d:"M240-400q-33 0-56.5-23.5T160-480q0-33 23.5-56.5T240-560q33 0 56.5 23.5T320-480q0 33-23.5 56.5T240-400Zm240 0q-33 0-56.5-23.5T400-480q0-33 23.5-56.5T480-560q33 0 56.5 23.5T560-480q0 33-23.5 56.5T480-400Zm240 0q-33 0-56.5-23.5T640-480q0-33 23.5-56.5T720-560q33 0 56.5 23.5T800-480q0 33-23.5 56.5T720-400Z"})})})]})},5240:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n=a(10790),c=a(31280),s=a(26065);a(9944);const i=e=>{const{title:t,description:a,customText:i,url:r,user:l,children:o}=e,d={instance:l?.address?(0,s.w0)(l.address).instance:"",offset:0};let u;if(i)u=(0,n.jsx)("p",{children:(0,s.E6)(i,d)});else if(a)if(t){const e=(0,c.$J)(t);d.offset=e.length,u=(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("p",{children:e}),(0,n.jsx)("p",{children:(0,s.E6)(a,d)})]})}else u=(0,n.jsx)("p",{children:(0,s.E6)(a,d)});else u=(0,n.jsx)("p",{children:(0,s.E6)(t,d)});return(0,n.jsxs)("div",{className:"mastodon-preview__body",children:[u,(0,n.jsx)("a",{href:r,target:"_blank",rel:"noreferrer noopener",children:(0,s.DQ)(r.replace(/^https?:\/\//,""))}),o]})}},32734:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(10790),c=a(27723),s=a(13022),i=a(31280),r=a(26065);a(38742);const __=c.__,l=({siteName:e,title:t,description:a,url:c,image:l,customImage:o})=>(0,n.jsxs)("div",{className:(0,s.A)("mastodon-preview__card",{"has-image":l}),children:[(0,n.jsx)("div",{className:"mastodon-preview__card-img",children:l||o?(0,n.jsx)("img",{src:l||o,alt:__("Mastodon preview thumbnail","jetpack-publicize-pkg")}):(0,n.jsx)("div",{className:"mastodon-preview__card-img--fallback",children:(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",height:"24",viewBox:"0 -960 960 960",width:"24","aria-hidden":"true",children:(0,n.jsx)("path",{d:"M320-240h320v-80H320v80Zm0-160h320v-80H320v80ZM240-80q-33 0-56.5-23.5T160-160v-640q0-33 23.5-56.5T240-880h320l240 240v480q0 33-23.5 56.5T720-80H240Zm280-520h200L520-800v200Z"})})})}),(0,n.jsxs)("div",{className:"mastodon-preview__card-text",children:[(0,n.jsx)("span",{className:"mastodon-preview__card-site",children:e||(0,i.GA)(c)}),(0,n.jsx)("span",{className:"mastodon-preview__card-title",children:(0,r.gD)(t)||(0,i.X2)(a)}),(0,n.jsx)("span",{className:"mastodon-preview__card-description",children:(0,i.$J)(a)})]})]})},53633:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(10790),c=a(27723),s=a(31280),i=a(50121),r=a(83734);a(9383);const __=c.__,l=({user:e})=>{const{displayName:t,address:a,avatarUrl:c}=e||{};return(0,n.jsxs)("div",{className:"mastodon-preview__post-header",children:[(0,n.jsxs)("div",{className:"mastodon-preview__post-header-user",children:[(0,n.jsx)("img",{className:"mastodon-preview__post-avatar",src:c||i.k,alt:""}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"mastodon-preview__post-header-displayname",children:t||
// translators: username of a fictional Mastodon User
__("anonymous-user","jetpack-publicize-pkg")}),(0,n.jsx)("div",{className:"mastodon-preview__post-header-username",children:a?.replace(`@${i.w}`,"")||"@username"})]})]}),(0,n.jsxs)("div",{className:"mastodon-preview__post-header-audience",children:[(0,n.jsx)(r.f,{}),(0,s.TR)(new Date)]})]})}},83734:(e,t,a)=>{"use strict";a.d(t,{f:()=>c});var n=a(10790);function c(){return(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",height:"15",viewBox:"0 -960 960 960",width:"15",role:"img",children:(0,n.jsx)("path",{d:"M480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm-40-82v-78q-33 0-56.5-23.5T360-320v-40L168-552q-3 18-5.5 36t-2.5 36q0 121 79.5 212T440-162Zm276-102q20-22 36-47.5t26.5-53q10.5-27.5 16-56.5t5.5-59q0-98-54.5-179T600-776v16q0 33-23.5 56.5T520-680h-80v80q0 17-11.5 28.5T400-560h-80v80h240q17 0 28.5 11.5T600-440v120h40q26 0 47 15.5t29 40.5Z"})})}},71751:(e,t,a)=>{"use strict";a.d(t,{R:()=>l});var n=a(10790),c=a(27723),s=a(67556),i=a(82779),r=a(83945);const __=c.__,l=({headingLevel:e,hidePostPreview:t,hideLinkPreview:a,...c})=>(0,n.jsxs)("div",{className:"social-preview mastodon-preview",children:[!t&&(0,n.jsxs)("section",{className:"social-preview__section mastodon-preview__section",children:[(0,n.jsx)(s.w,{level:e,children:
// translators: refers to a social post on Mastodon
__("Your post","jetpack-publicize-pkg")}),(0,n.jsx)("p",{className:"social-preview__section-desc",children:__("This is what your social post will look like on Mastodon:","jetpack-publicize-pkg")}),(0,n.jsx)(r._,{...c})]}),!a&&(0,n.jsxs)("section",{className:"social-preview__section mastodon-preview__section",children:[(0,n.jsx)(s.w,{level:e,children:
// translators: refers to a link to a Mastodon post
__("Link preview","jetpack-publicize-pkg")}),(0,n.jsx)("p",{className:"social-preview__section-desc",children:__("This is what it will look like when someone shares the link to your WordPress post on Mastodon.","jetpack-publicize-pkg")}),(0,n.jsx)(i.i,{...c,user:void 0})]})]})},37171:(e,t,a)=>{"use strict";a.d(t,{_:()=>c,v:()=>n});const n=160,c=2},33549:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});var n=a(10790),c=a(27723),s=a(67766),i=a(2712),r=a(58528);const __=c.__;function l(){return(0,n.jsxs)("div",{className:"nextdoor-preview__footer--actions",children:[(0,n.jsxs)("div",{className:"nextdoor-preview__footer--actions-item",children:[(0,n.jsx)(i.p,{}),(0,n.jsx)("span",{children:__("Like","jetpack-publicize-pkg")})]}),(0,n.jsxs)("div",{className:"nextdoor-preview__footer--actions-item",children:[(0,n.jsx)(s.T,{}),(0,n.jsx)("span",{children:__("Comment","jetpack-publicize-pkg")})]}),(0,n.jsxs)("div",{className:"nextdoor-preview__footer--actions-item",children:[(0,n.jsx)(r.l,{}),(0,n.jsx)("span",{children:__("Share","jetpack-publicize-pkg")})]})]})}},43288:(e,t,a)=>{"use strict";a.d(t,{z:()=>c});var n=a(10790);function c(){return(0,n.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 20 20","aria-hidden":"true",children:(0,n.jsx)("path",{fill:"#dfe1e4",fillRule:"evenodd",d:"M7.127 13.876a.732.732 0 1 0 1.035 1.035l4.75-4.749a.732.732 0 0 0 0-1.035L8.123 4.34A.732.732 0 0 0 7.09 5.375l4.27 4.27-4.232 4.23Z"})})}},67766:(e,t,a)=>{"use strict";a.d(t,{T:()=>c});var n=a(10790);function c(){return(0,n.jsx)("svg",{width:"20",height:"20",fill:"none",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,n.jsx)("path",{fill:"currentColor",fillRule:"evenodd",d:"M2 10.031C2 5.596 5.574 2 10 2h4c4.427 0 8 3.596 8 8.031 0 4.435-3.573 8.031-8 8.031h-1.52a17.033 17.033 0 0 1-1.377 1.467c-.991.938-2.456 2.079-4.086 2.437a1.403 1.403 0 0 1-1.458-.565 1.55 1.55 0 0 1-.195-1.394c.28-.823.395-1.734.434-2.464.014-.257.018-.485.018-.672A8.017 8.017 0 0 1 2 10.031Zm5.798 6.178a7.02 7.02 0 0 1 .016.418c.005.252.004.606-.019 1.023-.03.573-.103 1.285-.266 2.024.775-.377 1.54-.974 2.202-1.598a15.066 15.066 0 0 0 1.448-1.586l.017-.022.003-.004a1 1 0 0 1 .801-.402h2c3.314 0 6-2.692 6-6.03C20 6.691 17.314 4 14 4h-4c-3.314 0-6 2.692-6 6.031 0 2.336 1.32 4.36 3.258 5.359.308.159.515.474.54.82Z",clipRule:"evenodd"})})}},73462:(e,t,a)=>{"use strict";a.d(t,{f:()=>c});var n=a(10790);const c=()=>(0,n.jsx)("div",{className:"nextdoor-preview__default-avatar",children:"A"})},45906:(e,t,a)=>{"use strict";a.d(t,{z:()=>c});var n=a(10790);function c(){return(0,n.jsx)("div",{className:"nextdoor-preview__default-image",children:(0,n.jsxs)("svg",{width:"24",height:"24",fill:"none",viewBox:"0 0 24 24","aria-hidden":"true",color:"#055c00",children:[(0,n.jsx)("path",{fill:"currentColor",d:"M13.207 5.207c1.51-1.51 4.076-1.51 5.586 0 1.51 1.51 1.51 4.076 0 5.586l-2.1 2.1c-1.51 1.51-4.077 1.51-5.586 0a1 1 0 1 0-1.414 1.414c2.29 2.29 6.123 2.29 8.414 0l2.1-2.1c2.29-2.29 2.29-6.124 0-8.414s-6.124-2.29-8.414 0l-.7.7a1 1 0 0 0 1.414 1.414l.7-.7Z"}),(0,n.jsx)("path",{fill:"currentColor",d:"M7.307 11.107c1.51-1.51 4.076-1.51 5.586 0a1 1 0 0 0 1.414-1.414c-2.29-2.29-6.124-2.29-8.414 0l-2.1 2.1c-2.29 2.29-2.29 6.123 0 8.414 2.29 2.29 6.124 2.29 8.414 0l.7-.7a1 1 0 0 0-1.414-1.414l-.7.7c-1.51 1.51-4.076 1.51-5.586 0-1.51-1.51-1.51-4.076 0-5.586l2.1-2.1Z"})]})})}},85086:(e,t,a)=>{"use strict";a.d(t,{f:()=>c});var n=a(10790);function c(){return(0,n.jsx)("svg",{width:"14",height:"14",fill:"none",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,n.jsx)("path",{fill:"currentColor",fillRule:"evenodd",d:"M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm9.495-7.643c.286-.335.46-.357.505-.357.045 0 .219.022.505.357.282.33.581.868.852 1.619.464 1.283.79 3.034.872 5.024H9.771c.082-1.99.408-3.741.871-5.024.272-.751.571-1.289.854-1.62ZM7.77 11c.084-2.181.439-4.171.992-5.704.093-.255.192-.502.298-.738A8.009 8.009 0 0 0 4.062 11h3.707Zm-3.707 2h3.707c.084 2.181.439 4.171.992 5.704.093.255.192.502.298.738A8.009 8.009 0 0 1 4.062 13Zm15.876-2a8.009 8.009 0 0 0-4.997-6.442c.106.236.205.483.298.738.553 1.533.908 3.523.992 5.704h3.707Zm-3.707 2h3.707a8.009 8.009 0 0 1-4.997 6.442c.106-.236.205-.483.298-.738.553-1.533.908-3.523.992-5.704Zm-2.002 0c-.082 1.99-.408 3.741-.871 5.024-.272.751-.571 1.289-.854 1.62-.285.334-.46.356-.504.356-.045 0-.219-.022-.505-.357-.282-.33-.581-.868-.852-1.619-.464-1.283-.79-3.034-.872-5.024h4.458Z",clipRule:"evenodd"})})}},2712:(e,t,a)=>{"use strict";a.d(t,{p:()=>c});var n=a(10790);function c(){return(0,n.jsx)("svg",{width:"20",height:"20",fill:"none",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,n.jsx)("path",{fill:"currentColor",fillRule:"evenodd",d:"M13.275 8.752a1.5 1.5 0 0 1-2.55 0C9.75 7.18 8.719 5.617 6.565 6.074 5.248 6.352 4 7.433 4 9.644c0 2.153 1.348 4.592 4.259 7.236A28.475 28.475 0 0 0 12 19.74a28.475 28.475 0 0 0 3.741-2.86C18.651 14.236 20 11.797 20 9.643c0-2.21-1.25-3.29-2.564-3.57-2.155-.456-3.187 1.106-4.16 2.68Zm-2.581-3.48C7.634 2.58 2 4.217 2 9.643c0 2.996 1.85 5.934 4.914 8.717 1.478 1.343 3.1 2.585 4.839 3.575a.5.5 0 0 0 .494 0c1.739-.99 3.361-2.232 4.84-3.575C20.148 15.577 22 12.64 22 9.643c0-5.426-5.634-7.062-8.694-4.371A5.287 5.287 0 0 0 12 7.04a5.287 5.287 0 0 0-1.306-1.77Z",clipRule:"evenodd"})})}},58528:(e,t,a)=>{"use strict";a.d(t,{l:()=>c});var n=a(10790);function c(){return(0,n.jsx)("svg",{width:"20",height:"20",fill:"none",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,n.jsx)("path",{fill:"currentColor",fillRule:"evenodd",d:"M11.617 2.076a1 1 0 0 1 1.09.217l9 9a1 1 0 0 1 0 1.414l-9 9A1 1 0 0 1 11 21v-4.436c-2.849.366-5.261 2.271-6.384 4.837a1 1 0 0 1-1.856-.06C2.338 20.182 2 18.86 2 17.5a9.959 9.959 0 0 1 9-9.951V3a1 1 0 0 1 .617-.924ZM13 5.414V8.5a1 1 0 0 1-1 1c-4.448 0-8 3.552-8 8 0 .31.023.625.066.94C5.905 16.067 8.776 14.5 12 14.5a1 1 0 0 1 1 1v3.086L19.586 12 13 5.414Z",clipRule:"evenodd"})})}},64477:(e,t,a)=>{"use strict";a.d(t,{g:()=>i});var n=a(10790),c=a(31280),s=a(73915);function i(e){return(0,n.jsx)(s.K,{name:"",profileImage:"",...e,description:"",media:void 0,title:e.title||(0,c.X2)(e.description)})}},73915:(e,t,a)=>{"use strict";a.d(t,{K:()=>m});var n=a(10790),c=a(27723),s=a(13022),i=a(31280),r=a(37171),l=a(33549),o=a(43288),d=a(73462),u=a(45906),p=a(85086);a(39653);const __=c.__;function m({image:e,name:t,profileImage:a,description:c,neighborhood:m,media:h,title:g,url:v}){const w=!!h?.length;return(0,n.jsx)("div",{className:"nextdoor-preview__wrapper",children:(0,n.jsx)("section",{className:"nextdoor-preview__container "+(w?"has-media":""),children:(0,n.jsxs)("div",{className:"nextdoor-preview__content",children:[(0,n.jsxs)("div",{className:"nextdoor-preview__header",children:[(0,n.jsx)("div",{className:"nextdoor-preview__header--avatar",children:a?(0,n.jsx)("img",{src:a,alt:""}):(0,n.jsx)(d.f,{})}),(0,n.jsxs)("div",{className:"nextdoor-preview__header--details",children:[(0,n.jsx)("div",{className:"nextdoor-preview__header--name",children:t||__("Account Name","jetpack-publicize-pkg")}),(0,n.jsxs)("div",{className:"nextdoor-preview__header--meta",children:[(0,n.jsx)("span",{children:m||__("Neighborhood","jetpack-publicize-pkg")}),(0,n.jsx)("span",{children:"•"}),(0,n.jsx)("span",{children:(0,i.tX)(Date.now())}),(0,n.jsx)("span",{children:"•"}),(0,n.jsx)(p.f,{})]})]})]}),(0,n.jsxs)("div",{className:"nextdoor-preview__body",children:[c?(0,n.jsx)("div",{className:"nextdoor-preview__caption",children:(0,i.x5)(c,{platform:"nextdoor",maxChars:r.v,maxLines:r._})}):null,w?(0,n.jsx)("div",{className:"nextdoor-preview__media",children:h.map(((e,t)=>(0,n.jsx)("div",{className:"nextdoor-preview__media-item",children:e?.type?.startsWith("video/")?(0,n.jsx)("video",{controls:!0,children:(0,n.jsx)("source",{src:e.url,type:e.type})}):(0,n.jsx)("img",{alt:e.alt||"",src:e.url})},`nextdoor-preview__media-item-${t}`)))}):null,(0,n.jsxs)("article",{className:(0,s.A)("nextdoor-preview__card",{"small-preview":!e||w}),children:[e?(0,n.jsx)("img",{className:"nextdoor-preview__image",src:e,alt:""}):(0,n.jsx)(u.z,{}),v?(0,n.jsxs)("div",{className:"nextdoor-preview__description",children:[(0,n.jsx)("h2",{className:"nextdoor-preview__description--title",children:g||(0,i.X2)(c)}),(0,n.jsx)("span",{className:"nextdoor-preview__description--url",children:(0,i.GA)(v)})]}):null,w?(0,n.jsx)("div",{className:"nextdoor-preview__card--chevron-wrapper",children:(0,n.jsx)(o.z,{})}):null]})]}),(0,n.jsx)("div",{className:"nextdoor-preview__footer",children:(0,n.jsx)(l.$,{})})]})})})}},58581:(e,t,a)=>{"use strict";a.d(t,{n:()=>l});var n=a(10790),c=a(27723),s=a(67556),i=a(64477),r=a(73915);const __=c.__,l=({headingLevel:e,hideLinkPreview:t,hidePostPreview:a,...c})=>(0,n.jsxs)("div",{className:"social-preview nextdoor-preview",children:[!a&&(0,n.jsxs)("section",{className:"social-preview__section nextdoor-preview__section",children:[(0,n.jsx)(s.A,{level:e,children:
// translators: refers to a social post on Nextdoor
__("Your post","jetpack-publicize-pkg")}),(0,n.jsx)("p",{className:"social-preview__section-desc",children:__("This is what your social post will look like on Nextdoor:","jetpack-publicize-pkg")}),(0,n.jsx)(r.K,{...c})]}),!t&&(0,n.jsxs)("section",{className:"social-preview__section nextdoor-preview__section",children:[(0,n.jsx)(s.A,{level:e,children:
// translators: refers to a link to a Nextdoor post
__("Link preview","jetpack-publicize-pkg")}),(0,n.jsx)("p",{className:"social-preview__section-desc",children:__("This is what it will look like when someone shares the link to your WordPress post on Nextdoor.","jetpack-publicize-pkg")}),(0,n.jsx)(i.g,{...c,name:"",profileImage:""})]})]})},67556:(e,t,a)=>{"use strict";a.d(t,{A:()=>i,w:()=>s});var n=a(10790);const c=[2,3,4,5,6],s=({className:e,level:t,children:a})=>{const s=`h${t&&c.includes(t)?t:3}`;return(0,n.jsx)(s,{className:`social-preview__section-heading ${e??""}`,children:a})},i=s},83382:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});var n=a(10790),c=a(13022),s=a(31280),i=a(26363);const r=({image:e,title:t,url:a})=>{const r=(0,c.A)({"threads-preview__card-has-image":!!e});return(0,n.jsx)("div",{className:"threads-preview__card",children:(0,n.jsxs)("div",{className:r,children:[e&&(0,n.jsx)("img",{className:"threads-preview__card-image",src:e,alt:""}),(0,n.jsxs)("div",{className:"threads-preview__card-body",children:[(0,n.jsx)("div",{className:"threads-preview__card-url",children:(0,s.GA)(a||"")}),(0,n.jsx)("div",{className:"threads-preview__card-title",children:(0,i.c)(t)})]})]})})}},96039:(e,t,a)=>{"use strict";a.d(t,{w:()=>c});var n=a(10790);const c=()=>(0,n.jsxs)("div",{className:"threads-preview__footer",children:[(0,n.jsx)("span",{className:"threads-preview__icon--like",children:(0,n.jsx)("svg",{role:"img",viewBox:"0 0 18 18",children:(0,n.jsx)("path",{d:"M1.34375 7.53125L1.34375 7.54043C1.34374 8.04211 1.34372 8.76295 1.6611 9.65585C1.9795 10.5516 2.60026 11.5779 3.77681 12.7544C5.59273 14.5704 7.58105 16.0215 8.33387 16.5497C8.73525 16.8313 9.26573 16.8313 9.66705 16.5496C10.4197 16.0213 12.4074 14.5703 14.2232 12.7544C15.3997 11.5779 16.0205 10.5516 16.3389 9.65585C16.6563 8.76296 16.6563 8.04211 16.6562 7.54043V7.53125C16.6562 5.23466 15.0849 3.25 12.6562 3.25C11.5214 3.25 10.6433 3.78244 9.99228 4.45476C9.59009 4.87012 9.26356 5.3491 9 5.81533C8.73645 5.3491 8.40991 4.87012 8.00772 4.45476C7.35672 3.78244 6.47861 3.25 5.34375 3.25C2.9151 3.25 1.34375 5.23466 1.34375 7.53125Z",strokeWidth:"1.25"})})}),(0,n.jsx)("span",{className:"threads-preview__icon--reply",children:(0,n.jsx)("svg",{role:"img",viewBox:"0 0 18 18",children:(0,n.jsx)("path",{d:"M15.376 13.2177L16.2861 16.7955L12.7106 15.8848C12.6781 15.8848 12.6131 15.8848 12.5806 15.8848C11.3779 16.5678 9.94767 16.8931 8.41995 16.7955C4.94194 16.5353 2.08152 13.7381 1.72397 10.2578C1.2689 5.63919 5.13697 1.76863 9.75264 2.22399C13.2307 2.58177 16.0261 5.41151 16.2861 8.92429C16.4161 10.453 16.0586 11.8841 15.376 13.0876C15.376 13.1526 15.376 13.1852 15.376 13.2177Z",strokeLinejoin:"round",strokeWidth:"1.25"})})}),(0,n.jsx)("span",{className:"threads-preview__icon--repost",children:(0,n.jsxs)("svg",{role:"img",viewBox:"0 0 18 18",children:[(0,n.jsx)("path",{d:"M6.41256 1.23531C6.6349 0.971277 7.02918 0.937481 7.29321 1.15982L9.96509 3.40982C10.1022 3.52528 10.1831 3.69404 10.1873 3.87324C10.1915 4.05243 10.1186 4.2248 9.98706 4.34656L7.31518 6.81971C7.06186 7.05419 6.66643 7.03892 6.43196 6.7856C6.19748 6.53228 6.21275 6.13685 6.46607 5.90237L7.9672 4.51289H5.20312C3.68434 4.51289 2.45312 5.74411 2.45312 7.26289V9.51289V11.7629C2.45312 13.2817 3.68434 14.5129 5.20312 14.5129C5.5483 14.5129 5.82812 14.7927 5.82812 15.1379C5.82812 15.4831 5.5483 15.7629 5.20312 15.7629C2.99399 15.7629 1.20312 13.972 1.20312 11.7629V9.51289V7.26289C1.20312 5.05375 2.99399 3.26289 5.20312 3.26289H7.85002L6.48804 2.11596C6.22401 1.89362 6.19021 1.49934 6.41256 1.23531Z"}),(0,n.jsx)("path",{d:"M11.5874 17.7904C11.3651 18.0545 10.9708 18.0883 10.7068 17.8659L8.03491 15.6159C7.89781 15.5005 7.81687 15.3317 7.81267 15.1525C7.80847 14.9733 7.8814 14.801 8.01294 14.6792L10.6848 12.206C10.9381 11.9716 11.3336 11.9868 11.568 12.2402C11.8025 12.4935 11.7872 12.8889 11.5339 13.1234L10.0328 14.5129H12.7969C14.3157 14.5129 15.5469 13.2816 15.5469 11.7629V9.51286V7.26286C15.5469 5.74408 14.3157 4.51286 12.7969 4.51286C12.4517 4.51286 12.1719 4.23304 12.1719 3.88786C12.1719 3.54269 12.4517 3.26286 12.7969 3.26286C15.006 3.26286 16.7969 5.05373 16.7969 7.26286V9.51286V11.7629C16.7969 13.972 15.006 15.7629 12.7969 15.7629H10.15L11.512 16.9098C11.776 17.1321 11.8098 17.5264 11.5874 17.7904Z"})]})}),(0,n.jsx)("span",{className:"threads-preview__icon--share",children:(0,n.jsxs)("svg",{role:"img",viewBox:"0 0 18 18",children:[(0,n.jsx)("path",{d:"M15.6097 4.09082L6.65039 9.11104",strokeLinejoin:"round",strokeWidth:"1.25"}),(0,n.jsx)("path",{d:"M7.79128 14.439C8.00463 15.3275 8.11131 15.7718 8.33426 15.932C8.52764 16.071 8.77617 16.1081 9.00173 16.0318C9.26179 15.9438 9.49373 15.5501 9.95761 14.7628L15.5444 5.2809C15.8883 4.69727 16.0603 4.40546 16.0365 4.16566C16.0159 3.95653 15.9071 3.76612 15.7374 3.64215C15.5428 3.5 15.2041 3.5 14.5267 3.5H3.71404C2.81451 3.5 2.36474 3.5 2.15744 3.67754C1.97758 3.83158 1.88253 4.06254 1.90186 4.29856C1.92415 4.57059 2.24363 4.88716 2.88259 5.52032L6.11593 8.7243C6.26394 8.87097 6.33795 8.94431 6.39784 9.02755C6.451 9.10144 6.4958 9.18101 6.53142 9.26479C6.57153 9.35916 6.59586 9.46047 6.64451 9.66309L7.79128 14.439Z",strokeLinejoin:"round",strokeWidth:"1.25"})]})})]})},20853:(e,t,a)=>{"use strict";a.d(t,{Y:()=>i});var n=a(10790),c=a(27723),s=a(31280);const __=c.__,i=({name:e,date:t})=>{const a=t||new Date;return(0,n.jsxs)("div",{className:"threads-preview__header",children:[(0,n.jsx)("span",{className:"threads-preview__name",children:e||__("Account Name","jetpack-publicize-pkg")}),(0,n.jsx)("time",{className:"threads-preview__date",dateTime:a.toISOString(),children:(0,s.vI)(a)})]})}},26363:(e,t,a)=>{"use strict";a.d(t,{_:()=>c,c:()=>s});var n=a(31280);const c=500,s=e=>(0,n.RA)((0,n.f5)(120),(0,n.ZT)(120))((0,n.$J)(e))||""},70718:(e,t,a)=>{"use strict";a.d(t,{f:()=>c});var n=a(10790);const c=()=>(0,n.jsx)("svg",{width:"36",height:"36",viewBox:"0 0 6.3500001 6.3500001",version:"1.1",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("g",{children:(0,n.jsxs)("g",{children:[(0,n.jsx)("circle",{fill:"#fff",fillOpacity:"1",stroke:"#dbdbdb",strokeWidth:"0.144331",strokeOpacity:"1",strokeDasharray:"none",cx:"3.175",cy:"3.175",r:"3.1028347"}),(0,n.jsx)("path",{fill:"#dbdbdb",fillOpacity:"1",stroke:"#dbdbdb",strokeWidth:"0.********",strokeOpacity:"1",d:"m 3.175,0.******** c -0.2931267,2e-6 -0.5848453,0.0419523 -0.866097,0.1245402 C 2.0276513,0.******** 1.7596787,0.******** 1.5130859,0.******** 1.1424103,0.******** 0.********,1.1424103 0.********,1.5130859 0.********,1.7596787 0.30840793,2.0276513 0.22582601,2.308903 0.14323609,2.5901547 0.10128781,2.8818733 0.********,3.175 c 5.02e-4,0.1391369 0.0101356,0.2782044 0.0294556,0.4159953 0.01723,0.1151309 0.0411535,0.2291443 0.0713135,0.3415812 0.0045,0.0218 0.009469,0.043412 0.0144694,0.065112 0.03449,0.1203489 0.076152,0.2387347 0.12505697,0.3539836 0.0045,0.01235 0.009253,0.0244 0.0139526,0.03669 0.05201,0.1191279 0.111111,0.235155 0.17776693,0.3467489 0.0038,0.0071 0.008066,0.013651 0.0118856,0.020671 0.0694099,0.1145998 0.14620121,0.2246049 0.22996012,0.3291788 0.0022,0.0029 0.004001,0.00589 0.006201,0.00879 0.0861099,0.1069859 0.17942183,0.208301 0.27905277,0.3028239 0.3233817,-0.5511754 0.8330913,-0.701365 1.065568,-0.697115 h 2.0629232 c 0.5904654,-0.0058 0.9954919,0.5335544 1.1032918,0.6945312 0.0993,-0.09444 0.1921923,-0.1954752 0.2780192,-0.3023071 0.0023,-0.0031 0.00442,-0.0062 0.00672,-0.0093 0.08334,-0.1042929 0.1598386,-0.2139114 0.2289266,-0.3281453 0.004,-0.0073 0.00841,-0.014891 0.012402,-0.022221 0.0663,-0.1112249 0.1254962,-0.2265057 0.1772502,-0.3451986 0.0048,-0.01266 0.00934,-0.025004 0.013953,-0.037724 0.04856,-0.1146958 0.090239,-0.2321796 0.1245402,-0.3519165 0.0053,-0.02288 0.010256,-0.046256 0.014986,-0.069246 0.02971,-0.1111369 0.05321,-0.2236822 0.07028,-0.3374471 0.01929,-0.1376588 0.028946,-0.2764746 0.029456,-0.4154785 0,-0.0503 -0.00112,-0.1006562 -0.00362,-0.1508952 -0.0049,-0.1004917 -0.014695,-0.20073 -0.029455,-0.3002469 C 6.2008813,2.6243448 6.1811701,2.5258685 6.1567301,2.4282756 6.1445102,2.3794856 6.1310225,2.330673 6.1164225,2.282548 6.0286626,1.9932503 5.8988742,1.7184607 5.7309163,1.4670939 5.6750164,1.383444 5.6149083,1.3030201 5.5510824,1.2252482 5.5191724,1.1863682 5.4861603,1.1477995 5.4523804,1.1105265 5.3848104,1.0359766 5.3140214,0.96518559 5.2394735,0.89761963 5.1276256,0.79624473 5.008419,0.70295057 4.8829061,0.61908366 4.7573922,0.53522374 4.6255915,0.46089717 4.4891317,0.39635824 c -0.09095,-0.04302 -0.1838061,-0.0813435 -0.278536,-0.11523845 -0.09473,-0.0339 -0.1912764,-0.0634 -0.2888713,-0.0878499 C 3.7752926,0.15658989 3.6265025,0.13054519 3.4762736,0.11575521 3.3761487,0.10585522 3.2756099,0.******** 3.175,0.******** Z m 0,1.44125569 A 1.4106187,1.4106187 0 0 1 4.5857666,2.9533081 1.4106187,1.4106187 0 0 1 3.175,4.3635579 1.4106187,1.4106187 0 0 1 1.7642334,2.9533081 1.4106187,1.4106187 0 0 1 3.175,1.5425415 Z"})]})})})},16037:(e,t,a)=>{"use strict";a.d(t,{G:()=>s});var n=a(10790),c=a(89075);const s=e=>(0,n.jsx)(c.s,{...e,caption:"",media:void 0})},70990:(e,t,a)=>{"use strict";a.d(t,{$:()=>i});var n=a(10790),c=a(13022),s=a(51609);const i=({media:e})=>{const t=e.filter((e=>e.type.startsWith("image/")||e.type.startsWith("video/"))).filter(((e,t,a)=>0===t||!a[0].type.startsWith("video/")&&"image/gif"!==a[0].type&&(!e.type.startsWith("video/")&&"image/gif"!==e.type))).slice(0,4);if(0===t.length)return null;const a=t[0].type.startsWith("video/"),i=(0,c.A)(["threads-preview__media","threads-preview__media-children-"+t.length]);return(0,n.jsx)("div",{className:i,children:t.map(((e,t)=>(0,n.jsx)(s.Fragment,{children:a?(0,n.jsx)("video",{controls:!0,children:(0,n.jsx)("source",{src:e.url,type:e.type})}):(0,n.jsx)("img",{alt:e.alt||"",src:e.url})},`threads-preview__media-item-${t}`)))})}},89075:(e,t,a)=>{"use strict";a.d(t,{s:()=>u});var n=a(10790),c=a(31280),s=a(83382),i=a(96039),r=a(20853),l=a(26363),o=a(70990),d=a(83390);a(26075);const u=({caption:e,date:t,image:a,media:u,name:p,profileImage:m,showThreadConnector:h,title:g,url:v})=>{const w=!!u?.length,b=v&&a&&!w;return(0,n.jsx)("div",{className:"threads-preview__wrapper",children:(0,n.jsxs)("div",{className:"threads-preview__container",children:[(0,n.jsx)(d.B,{profileImage:m,showThreadConnector:h}),(0,n.jsxs)("div",{className:"threads-preview__main",children:[(0,n.jsx)(r.Y,{name:p,date:t}),(0,n.jsxs)("div",{className:"threads-preview__content",children:[e?(0,n.jsx)("div",{className:"threads-preview__text",children:(0,c.x5)(e,{platform:"threads",maxChars:l._})}):null,w?(0,n.jsx)(o.$,{media:u}):null,b?(0,n.jsx)(s.Z,{image:a,title:g||"",url:v}):null]}),(0,n.jsx)(i.w,{})]})]})})}},33389:(e,t,a)=>{"use strict";a.d(t,{n:()=>l});var n=a(10790),c=a(27723),s=a(67556),i=a(16037),r=a(89075);const __=c.__,l=({headingLevel:e,hideLinkPreview:t,hidePostPreview:a,posts:c})=>c?.length?(0,n.jsxs)("div",{className:"social-preview threads-preview",children:[!a&&(0,n.jsxs)("section",{className:"social-preview__section threads-preview__section",children:[(0,n.jsx)(s.A,{level:e,children:
// translators: refers to a social post on Threads
__("Your post","jetpack-publicize-pkg")}),(0,n.jsx)("p",{className:"social-preview__section-desc",children:__("This is what your social post will look like on Threads:","jetpack-publicize-pkg")}),c.map(((e,t)=>{const a=t+1===c.length;return(0,n.jsx)(r.s,{...e,showThreadConnector:!a},`threads-preview__post-${t}`)}))]}),t?null:(0,n.jsxs)("section",{className:"social-preview__section threads-preview__section",children:[(0,n.jsx)(s.A,{level:e,children:
// translators: refers to a link to a Threads post
__("Link preview","jetpack-publicize-pkg")}),c[0].image?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("p",{className:"social-preview__section-desc",children:__("This is what it will look like when someone shares the link to your WordPress post on Threads.","jetpack-publicize-pkg")}),(0,n.jsx)(i.G,{...c[0],name:"",profileImage:""})]}):(0,n.jsx)("p",{className:"social-preview__section-desc",children:__("Threads link preview requires an image to be set for the post. Please add an image to see the preview.","jetpack-publicize-pkg")})]})]}):null},83390:(e,t,a)=>{"use strict";a.d(t,{B:()=>i});var n=a(10790),c=a(27723),s=a(70718);const __=c.__,i=({profileImage:e,showThreadConnector:t})=>(0,n.jsxs)("div",{className:"threads-preview__sidebar",children:[(0,n.jsx)("div",{className:"threads-preview__profile-image",children:e?(0,n.jsx)("img",{alt:__("Threads profile image","jetpack-publicize-pkg"),src:e}):(0,n.jsx)(s.f,{})}),t&&(0,n.jsx)("div",{className:"threads-preview__connector"})]})},47956:(e,t,a)=>{"use strict";a.d(t,{Z:()=>c,f:()=>s});var n=a(31280);const c=e=>(0,n.RA)((0,n.f5)(1e3),(0,n.ZT)(1e3))((0,n.$J)(e))||"",s=e=>(0,n.RA)((0,n.f5)(400),(0,n.ZT)(400))((0,n.$J)(e))||""},82484:(e,t,a)=>{"use strict";a.d(t,{Z:()=>o});var n=a(10790),c=a(27723),s=a(31280),i=a(47956),r=a(38336),l=a(52340);a(54447);const __=c.__,o=({title:e,description:t,image:a,user:c,url:o})=>{const d=c?.avatarUrl;return(0,n.jsxs)("div",{className:"tumblr-preview__post",children:[d&&(0,n.jsx)("img",{className:"tumblr-preview__avatar",src:d,alt:""}),(0,n.jsxs)("div",{className:"tumblr-preview__card",children:[(0,n.jsx)(l.A,{user:c}),(0,n.jsxs)("div",{className:"tumblr-preview__window",children:[a&&(0,n.jsxs)("div",{className:"tumblr-preview__window-top",children:[(0,n.jsx)("div",{className:"tumblr-preview__overlay",children:(0,n.jsx)("div",{className:"tumblr-preview__title",children:(0,i.Z)(e)})}),(0,n.jsx)("img",{className:"tumblr-preview__image",src:a,alt:__("Tumblr preview thumbnail","jetpack-publicize-pkg")})]}),(0,n.jsxs)("div",{className:"tumblr-preview__window-bottom "+(a?"":"is-full"),children:[!a&&(0,n.jsx)("div",{className:"tumblr-preview__title",children:(0,i.Z)(e)}),t&&a&&(0,n.jsx)("div",{className:"tumblr-preview__description",children:(0,i.f)(t)}),o&&(0,n.jsx)("div",{className:"tumblr-preview__site-name",children:(0,s.GA)(o)})]})]}),(0,n.jsx)(r.A,{})]})]})}},95550:(e,t,a)=>{"use strict";a.d(t,{B:()=>l});var n=a(10790),c=a(27723),s=a(47956),i=a(38336),r=a(52340);a(54447);const __=c.__,l=({title:e,description:t,image:a,user:c,url:l,customText:o,media:d})=>{const u=c?.avatarUrl,p=d?.[0];return(0,n.jsxs)("div",{className:"tumblr-preview__post",children:[u&&(0,n.jsx)("img",{className:"tumblr-preview__avatar",src:u,alt:""}),(0,n.jsxs)("div",{className:"tumblr-preview__card",children:[(0,n.jsx)(r.A,{user:c}),(0,n.jsxs)("div",{className:"tumblr-preview__body",children:[(0,n.jsx)("div",{className:"tumblr-preview__title",children:(0,s.Z)(e)}),o&&(0,n.jsx)("div",{className:"tumblr-preview__custom-text",children:o}),t&&(0,n.jsx)("div",{className:"tumblr-preview__description",children:(0,s.f)(t)}),p?(0,n.jsx)("div",{className:"tumblr-preview__media-item",children:p.type.startsWith("video/")?(0,n.jsx)("video",{controls:!0,className:"tumblr-preview__media--video",children:(0,n.jsx)("source",{src:p.url,type:p.type})}):(0,n.jsx)("img",{className:"tumblr-preview__image",src:p.url,alt:""})}):a&&(0,n.jsx)("img",{className:"tumblr-preview__image",src:a,alt:__("Tumblr preview thumbnail","jetpack-publicize-pkg")}),(0,n.jsx)("a",{className:"tumblr-preview__url",href:l,target:"_blank",rel:"noreferrer",children:__("View On WordPress","jetpack-publicize-pkg")})]}),(0,n.jsx)(i.A,{})]})]})}},38336:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n=a(10790),c=a(27723),s=a(45949);a(25634);const __=c.__,i=()=>(0,n.jsxs)("div",{className:"tumblr-preview__post-actions",children:[(0,n.jsxs)("div",{className:"tumblr-preview__post-manage-actions",children:[(0,n.jsxs)("div",{className:"tumblr-preview__post-actions-blaze",children:[(0,n.jsx)(s.A,{name:"blaze"})," Blaze"]}),(0,n.jsx)("ul",{children:[{icon:"delete",
// translators: "Delete" action on a Tumblr post
label:__("Delete","jetpack-publicize-pkg")},{icon:"edit",
// translators: "Edit" action on a Tumblr post
label:__("Edit","jetpack-publicize-pkg")}].map((({icon:e,label:t})=>(0,n.jsx)("li",{"aria-label":t,children:(0,n.jsx)(s.A,{name:e})},e)))})]}),(0,n.jsxs)("div",{className:"tumblr-preview__post-social-actions",children:[(0,n.jsx)("div",{children:
// translators: count of notes on a Tumblr post
__("0 notes","jetpack-publicize-pkg")}),(0,n.jsx)("ul",{children:[{icon:"share",
// translators: "Share" action on a Tumblr post
label:__("Share","jetpack-publicize-pkg")},{icon:"reply",
// translators: "Reply" action on a Tumblr post
label:__("Reply","jetpack-publicize-pkg")},{icon:"reblog",
// translators: "Reblog" action on a Tumblr post
label:__("Reblog","jetpack-publicize-pkg")},{icon:"like",
// translators: "Like" action on a Tumblr post
label:__("Like","jetpack-publicize-pkg")}].map((({icon:e,label:t})=>(0,n.jsx)("li",{"aria-label":t,children:(0,n.jsx)(s.A,{name:e})},e)))})]})]})},52340:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n=a(10790),c=a(27723),s=a(45949);a(49488);const __=c.__,i=({user:e})=>(0,n.jsxs)("div",{className:"tumblr-preview__post-header",children:[(0,n.jsx)("div",{className:"tumblr-preview__post-header-username",children:e?.displayName||
// translators: username of a fictional Tumblr User
__("anonymous-user","jetpack-publicize-pkg")}),(0,n.jsx)(s.A,{name:"ellipsis"})]})},45949:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var n=a(10790);a(34229);const c=({name:e})=>{let t;switch(e){case"blaze":t=(0,n.jsx)("svg",{viewBox:"0 0 25 22",children:(0,n.jsx)("path",{d:"m7.5059-0.24414c-0.79843 0.057223-1.2169 0.88587-1.1635 1.6128-0.2266 2.0449-1.4898 3.8696-3.1975 4.9778-3.0182 2.414-4.2201 6.8066-2.8033 10.411 0.92417 2.4679 2.9589 4.5674 5.4768 5.3928 0.95914 0.16102 1.7233-0.94358 1.3074-1.8059-0.11578-0.51062-0.17482-0.96516-0.17845-1.487 1.0413 1.5607 2.5484 2.8986 4.341 3.4975 1.0396-0.0154 1.98-0.64458 2.8516-1.1608 3.3821-2.1786 4.9604-6.7097 3.6597-10.518-0.49144-1.4599-1.2948-2.8935-2.5028-3.8698-0.7512-0.45498-1.661 0.09677-1.9202 0.86038-0.12274 0.16822-0.70352 1.1955-0.6191 0.61976 0.25488-3.4397-1.6789-7.0066-4.8123-8.4958-0.14322-0.037843-0.292-0.049464-0.43945-0.035156zm1.0586 3.5605c1.8947 2.0016 2.2326 5.1984 0.89062 7.5879-0.38498 0.96148 0.71762 2.0063 1.6567 1.5681 1.4159-0.4624 2.6998-1.3259 3.6577-2.4665 1.6442 2.5888 1.1465 6.2819-1.0629 8.3379-0.62378 0.60782-1.3666 1.0945-2.1754 1.4179-1.9543-0.989-3.3534-3.0966-3.5625-5.3125-0.25636-1.0253-1.81-1.2013-2.2852-0.25781-0.75058 1.3054-1.1846 2.7948-1.2305 4.3008-2.2396-1.9852-2.8468-5.4435-1.4609-8.0527 0.58926-1.239 1.651-2.13 2.724-2.9329 1.2958-1.1271 2.2791-2.62 2.7682-4.2683l0.071578 0.069832z"})});break;case"delete":t=(0,n.jsxs)("svg",{viewBox:"0 0 14 17",children:[(0,n.jsx)("path",{d:"M12 5v9c.1.7-.3 1-1 1H3c-.5 0-.9-.3-1-1V5c0-.6-.4-1-1-1-.5 0-1 .4-1 1v9.5C0 16.1 1.4 17 3 17h8c1.8 0 3-.8 3-2.5V5c0-.6-.5-1-1-1-.6 0-1 .5-1 1z"}),(0,n.jsx)("path",{d:"M4 12s0 1 1 1 1-1 1-1V5c0-.5-.4-1-1-1-.5 0-1 .5-1 1v7zm4 0s0 1 1 1 1-1 1-1V5c0-.5-.4-1-1-1-.5 0-1 .5-1 1v7zm5-10c.5 0 1-.4 1-1 0-.5-.4-.9-1-1H1C.5.1 0 .5 0 1c0 .6.6 1 1.1 1H13z"})]});break;case"edit":t=(0,n.jsx)("svg",{viewBox:"0 0 17.6 17.6",children:(0,n.jsx)("path",{d:"M5.3 13.8l-2.1.7.7-2.1L10.3 6l1.4 1.4-6.4 6.4zm6.4-9.3l-1.4-1.4-1.4 1.4-6.7 6.7-.2.5-2 5.9 3.8-1.3 2.1-.7.4-.1.3-.3 7.8-7.8c.1 0-2.7-2.9-2.7-2.9zm5.6-1.4L14.5.3c-.4-.4-1-.4-1.4 0l-1.4 1.4L15.9 6l1.4-1.4c.4-.5.4-1.1 0-1.5"})});break;case"share":t=(0,n.jsx)("svg",{viewBox:"0 0 24 24",children:(0,n.jsx)("path",{d:"M12.6173 1.07612C12.991 0.921338 13.4211 1.00689 13.7071 1.29289L22.7071 10.2929C23.0832 10.669 23.0991 11.2736 22.7433 11.669L13.7433 21.669C13.4663 21.9767 13.0283 22.082 12.6417 21.9336C12.2552 21.7853 12 21.414 12 21V16H11.5C7.31775 16 3.92896 18.2486 2.95256 21.3044C2.80256 21.7738 2.33292 22.064 1.84598 21.9881C1.35904 21.9122 1 21.4928 1 21V18.5C1 12.3162 5.88069 7.27245 12 7.01067V2C12 1.59554 12.2436 1.2309 12.6173 1.07612ZM14 4.41421V8C14 8.55228 13.5523 9 13 9H12.5C7.64534 9 3.64117 12.6414 3.06988 17.3419C5.09636 15.2366 8.18218 14 11.5 14H13C13.5523 14 14 14.4477 14 15V18.394L20.622 11.0362L14 4.41421Z"})});break;case"reply":t=(0,n.jsx)("svg",{viewBox:"0 0 17 17",children:(0,n.jsx)("path",{d:"M8.7 0C4.1 0 .4 3.7.4 8.3c0 1.2.2 2.3.7 3.4-.2.6-.4 1.5-.7 2.5L0 15.8c-.2.7.5 1.4 1.2 1.2l1.6-.4 2.4-.7c1.1.5 2.2.7 3.4.7 4.6 0 8.3-3.7 8.3-8.3C17 3.7 13.3 0 8.7 0zM15 8.3c0 3.5-2.8 6.3-6.4 6.3-1.2 0-2.3-.3-3.2-.9l-3.2.9.9-3.2c-.5-.9-.9-2-.9-3.2.1-3.4 3-6.2 6.5-6.2S15 4.8 15 8.3z"})});break;case"reblog":t=(0,n.jsx)("svg",{viewBox:"0 0 17 18.1",children:(0,n.jsx)("path",{d:"M12.8.2c-.4-.4-.8-.2-.8.4v2H2c-2 0-2 2-2 2v5s0 1 1 1 1-1 1-1v-4c0-1 .5-1 1-1h9v2c0 .6.3.7.8.4L17 3.6 12.8.2zM4.2 17.9c.5.4.8.2.8-.3v-2h10c2 0 2-2 2-2v-5s0-1-1-1-1 1-1 1v4c0 1-.5 1-1 1H5v-2c0-.6-.3-.7-.8-.4L0 14.6l4.2 3.3z"})});break;case"like":t=(0,n.jsx)("svg",{viewBox:"0 0 20 18",children:(0,n.jsx)("path",{d:"M14.658 0c-1.625 0-3.21.767-4.463 2.156-.06.064-.127.138-.197.225-.074-.085-.137-.159-.196-.225C8.547.766 6.966 0 5.35 0 4.215 0 3.114.387 2.162 1.117c-2.773 2.13-2.611 5.89-1.017 8.5 2.158 3.535 6.556 7.18 7.416 7.875A2.3 2.3 0 0 0 9.998 18c.519 0 1.028-.18 1.436-.508.859-.695 5.257-4.34 7.416-7.875 1.595-2.616 1.765-6.376-1-8.5C16.895.387 15.792 0 14.657 0h.001zm0 2.124c.645 0 1.298.208 1.916.683 1.903 1.461 1.457 4.099.484 5.695-1.973 3.23-6.16 6.7-6.94 7.331a.191.191 0 0 1-.241 0c-.779-.631-4.966-4.101-6.94-7.332-.972-1.595-1.4-4.233.5-5.694.619-.475 1.27-.683 1.911-.683 1.064 0 2.095.574 2.898 1.461.495.549 1.658 2.082 1.753 2.203.095-.12 1.259-1.654 1.752-2.203.8-.887 1.842-1.461 2.908-1.461h-.001z"})});break;case"ellipsis":t=(0,n.jsx)("svg",{viewBox:"0 0 17.5 3.9",children:(0,n.jsx)("path",{d:"M17.5 1.9c0 1.1-.9 1.9-1.9 1.9-1.1 0-1.9-.9-1.9-1.9S14.5 0 15.6 0c1 0 1.9.9 1.9 1.9m-6.8 0c0 1.1-.9 1.9-1.9 1.9-1.1.1-2-.8-2-1.9 0-1 .9-1.9 2-1.9s1.9.9 1.9 1.9m-6.8 0c0 1.1-.9 2-2 2-1 0-1.9-.9-1.9-2S.9 0 1.9 0c1.1 0 2 .9 2 1.9"})})}return(0,n.jsx)("span",{className:`tumblr-preview__post-icon tumblr-preview__post-icon-${e}`,children:t})}},19036:(e,t,a)=>{"use strict";a.d(t,{s:()=>l});var n=a(10790),c=a(27723),s=a(67556),i=a(82484),r=a(95550);const __=c.__,l=({headingLevel:e,hideLinkPreview:t,hidePostPreview:a,...c})=>{const l=!!c.media?.length;return(0,n.jsxs)("div",{className:"social-preview tumblr-preview",children:[!a&&(0,n.jsxs)("section",{className:"social-preview__section tumblr-preview__section",children:[(0,n.jsx)(s.w,{level:e,children:
// translators: refers to a social post on Tumblr
__("Your post","jetpack-publicize-pkg")}),(0,n.jsx)("p",{className:"social-preview__section-desc",children:__("This is what your social post will look like on Tumblr:","jetpack-publicize-pkg")}),l?(0,n.jsx)(r.B,{...c}):(0,n.jsx)(i.Z,{...c})]}),!t&&(0,n.jsxs)("section",{className:"social-preview__section tumblr-preview__section",children:[(0,n.jsx)(s.w,{level:e,children:
// translators: refers to a link on Tumblr
__("Link preview","jetpack-publicize-pkg")}),(0,n.jsx)("p",{className:"social-preview__section-desc",children:__("This is what it will look like when someone shares the link to your WordPress post on Tumblr.","jetpack-publicize-pkg")}),(0,n.jsx)(i.Z,{...c,user:void 0})]})]})}},74404:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});var n=a(10790),c=a(13022),s=a(31280);const i=(0,s.RA)((0,s.f5)(200),(0,s.ZT)(200)),r=({description:e,image:t,title:a,cardType:r,url:l})=>{const o=(0,c.A)(`twitter-preview__card-${r}`,{"twitter-preview__card-has-image":!!t});return(0,n.jsx)("div",{className:"twitter-preview__card",children:(0,n.jsxs)("div",{className:o,children:[t&&(0,n.jsx)("img",{className:"twitter-preview__card-image",src:t,alt:""}),(0,n.jsxs)("div",{className:"twitter-preview__card-body",children:[(0,n.jsx)("div",{className:"twitter-preview__card-url",children:(0,s.GA)(l||"")}),(0,n.jsx)("div",{className:"twitter-preview__card-title",children:a}),(0,n.jsx)("div",{className:"twitter-preview__card-description",children:i((0,s.$J)(e))})]})]})})}},5501:(e,t,a)=>{"use strict";a.d(t,{w:()=>c});var n=a(10790);const c=()=>(0,n.jsxs)("div",{className:"twitter-preview__footer",children:[(0,n.jsx)("span",{className:"twitter-preview__icon-replies",children:(0,n.jsx)("svg",{viewBox:"0 0 24 24",children:(0,n.jsx)("path",{d:"M1.751 10c0-4.42 3.584-8 8.005-8h4.366c4.49 0 8.129 3.64 8.129 8.13 0 2.96-1.607 5.68-4.196 7.11l-8.054 4.46v-3.69h-.067c-4.49.1-8.183-3.51-8.183-8.01zm8.005-6c-3.317 0-6.005 2.69-6.005 6 0 3.37 2.77 6.08 6.138 6.01l.351-.01h1.761v2.3l5.087-2.81c1.951-1.08 3.163-3.13 3.163-5.36 0-3.39-2.744-6.13-6.129-6.13H9.756z"})})}),(0,n.jsx)("span",{className:"twitter-preview__icon-retweets",children:(0,n.jsx)("svg",{viewBox:"0 0 24 24",children:(0,n.jsx)("path",{d:"M4.5 3.88l4.432 4.14-1.364 1.46L5.5 7.55V16c0 1.1.896 2 2 2H13v2H7.5c-2.209 0-4-1.79-4-4V7.55L1.432 9.48.068 8.02 4.5 3.88zM16.5 6H11V4h5.5c2.209 0 4 1.79 4 4v8.45l2.068-1.93 1.364 1.46-4.432 4.14-4.432-4.14 1.364-1.46 2.068 1.93V8c0-1.1-.896-2-2-2z"})})}),(0,n.jsx)("span",{className:"twitter-preview__icon-likes",children:(0,n.jsx)("svg",{viewBox:"0 0 24 24",children:(0,n.jsx)("path",{d:"M16.697 5.5c-1.222-.06-2.679.51-3.89 2.16l-.805 1.09-.806-1.09C9.984 6.01 8.526 5.44 7.304 5.5c-1.243.07-2.349.78-2.91 1.91-.552 1.12-.633 2.78.479 4.82 1.074 1.97 3.257 4.27 7.129 6.61 3.87-2.34 6.052-4.64 7.126-6.61 1.111-2.04 1.03-3.7.477-4.82-.561-1.13-1.666-1.84-2.908-1.91zm4.187 7.69c-1.351 2.48-4.001 5.12-8.379 7.67l-.503.3-.504-.3c-4.379-2.55-7.029-5.19-8.382-7.67-1.36-2.5-1.41-4.86-.514-6.67.887-1.79 2.647-2.91 4.601-3.01 1.651-.09 3.368.56 4.798 2.01 1.429-1.45 3.146-2.1 4.796-2.01 1.954.1 3.714 1.22 4.601 3.01.896 1.81.846 4.17-.514 6.67z"})})}),(0,n.jsx)("span",{className:"twitter-preview__icon-analytics",children:(0,n.jsx)("svg",{viewBox:"0 0 24 24",children:(0,n.jsx)("path",{d:"M8.75 21V3h2v18h-2zM18 21V8.5h2V21h-2zM4 21l.004-10h2L6 21H4zm9.248 0v-7h2v7h-2z"})})}),(0,n.jsx)("span",{className:"twitter-preview__icon-share",children:(0,n.jsx)("svg",{viewBox:"0 0 24 24",children:(0,n.jsx)("path",{d:"M12 2.59l5.7 5.7-1.41 1.42L13 6.41V16h-2V6.41l-3.3 3.3-1.41-1.42L12 2.59zM21 15l-.02 3.51c0 1.38-1.12 2.49-2.5 2.49H5.5C4.11 21 3 19.88 3 18.5V15h2v3.5c0 .28.22.5.5.5h12.98c.28 0 .5-.22.5-.5L19 15h2z"})})})]})},69463:(e,t,a)=>{"use strict";a.d(t,{Y:()=>i});var n=a(10790),c=a(27723),s=a(31280);const __=c.__,i=({name:e,screenName:t,date:a})=>(0,n.jsxs)("div",{className:"twitter-preview__header",children:[(0,n.jsx)("span",{className:"twitter-preview__name",children:e||__("Account Name","jetpack-publicize-pkg")}),(0,n.jsx)("span",{className:"twitter-preview__screen-name",children:t||"@account"}),(0,n.jsx)("span",{children:"·"}),(0,n.jsx)("span",{className:"twitter-preview__date",children:(0,s.hV)(a||Date.now())})]})},43204:(e,t,a)=>{"use strict";a.d(t,{f:()=>c});var n=a(10790);const c=()=>(0,n.jsxs)("svg",{version:"1.0",xmlns:"http://www.w3.org/2000/svg",width:"271pt",height:"270pt",viewBox:"0 0 271 270",children:[(0,n.jsx)("rect",{width:"100%",height:"100%",fill:"#ccd5de"}),(0,n.jsxs)("g",{transform:"translate(0,270) scale(0.1,-0.1)",fill:"#647785",stroke:"none",children:[(0,n.jsx)("path",{d:"M1251 2089 c-81 -14 -178 -65 -239 -125 -64 -65 -124 -185 -149 -299 -24 -111 -24 -339 -1 -415 38 -121 108 -193 227 -231 83 -27 339 -38 449 -19 139 23 236 91 286 200 59 130 52 395 -15 577 -88 235 -301 354 -558 312z"}),(0,n.jsx)("path",{d:"M1197 795 c-358 -68 -643 -357 -721 -732 l-14 -63 890 0 890 0 -7 42 c-11 70 -64 218 -102 288 -121 224 -336 394 -573 454 -92 24 -267 29 -363 11z"})]})]})},82431:(e,t,a)=>{"use strict";a.d(t,{E:()=>s});var n=a(10790),c=a(82445);const s=e=>(0,n.jsx)(c.O,{...e,text:"",media:void 0})},1336:(e,t,a)=>{"use strict";a.d(t,{$:()=>i});var n=a(10790),c=a(13022),s=a(51609);const i=({media:e})=>{const t=e.filter((e=>e.type.startsWith("image/")||e.type.startsWith("video/"))).filter(((e,t,a)=>0===t||!a[0].type.startsWith("video/")&&"image/gif"!==a[0].type&&(!e.type.startsWith("video/")&&"image/gif"!==e.type))).slice(0,4);if(0===t.length)return null;const a=t[0].type.startsWith("video/"),i=(0,c.A)(["twitter-preview__media","twitter-preview__media-children-"+t.length]);return(0,n.jsx)("div",{className:i,children:t.map(((e,t)=>(0,n.jsx)(s.Fragment,{children:a?(0,n.jsx)("video",{controls:!0,children:(0,n.jsx)("source",{src:e.url,type:e.type})}):(0,n.jsx)("img",{alt:e.alt||"",src:e.url})},`twitter-preview__media-item-${t}`)))})}},82445:(e,t,a)=>{"use strict";a.d(t,{O:()=>u});var n=a(10790),c=a(74404),s=a(5501),i=a(69463),r=a(1336),l=a(58024),o=a(940),d=a(16801);a(19569);const u=({date:e,description:t,image:a,media:u,name:p,profileImage:m,screenName:h,showThreadConnector:g,text:v,title:w,tweet:b,cardType:f,url:k})=>{const _=!!u?.length;return(0,n.jsx)("div",{className:"twitter-preview__wrapper",children:(0,n.jsxs)("div",{className:"twitter-preview__container",children:[(0,n.jsx)(o.B,{profileImage:m,showThreadConnector:g}),(0,n.jsxs)("div",{className:"twitter-preview__main",children:[(0,n.jsx)(i.Y,{name:p,screenName:h,date:e}),(0,n.jsxs)("div",{className:"twitter-preview__content",children:[v?(0,n.jsx)(d.E,{text:v,url:k||"",retainUrl:_}):null,_?(0,n.jsx)(r.$,{media:u}):null,b?(0,n.jsx)(l.g,{tweet:b}):null,!_&&k&&(0,n.jsx)(c.Z,{description:t||"",image:a,title:w||"",cardType:f||"",url:k})]}),(0,n.jsx)(s.w,{})]})]})})}},43731:(e,t,a)=>{"use strict";a.d(t,{p:()=>l});var n=a(10790),c=a(27723),s=a(67556),i=a(82431),r=a(82445);const __=c.__,l=({headingLevel:e,hideLinkPreview:t,hidePostPreview:a,tweets:c})=>c?.length?(0,n.jsxs)("div",{className:"social-preview twitter-preview",children:[!a&&(0,n.jsxs)("section",{className:"social-preview__section twitter-preview__section",children:[(0,n.jsx)(s.A,{level:e,children:
// translators: refers to a social post on Twitter
__("Your post","jetpack-publicize-pkg")}),(0,n.jsx)("p",{className:"social-preview__section-desc",children:__("This is what your social post will look like on X:","jetpack-publicize-pkg")}),c.map(((e,t)=>{const a=t+1===c.length;return(0,n.jsx)(r.O,{...e,showThreadConnector:!a},`twitter-preview__tweet-${t}`)}))]}),!t&&(0,n.jsxs)("section",{className:"social-preview__section twitter-preview__section",children:[(0,n.jsx)(s.A,{level:e,children:
// translators: refers to a link to a Twitter post
__("Link preview","jetpack-publicize-pkg")}),(0,n.jsx)("p",{className:"social-preview__section-desc",children:__("This is what it will look like when someone shares the link to your WordPress post on X.","jetpack-publicize-pkg")}),(0,n.jsx)(i.E,{...c[0],name:"",profileImage:"",screenName:""})]})]}):null},58024:(e,t,a)=>{"use strict";a.d(t,{g:()=>s});var n=a(10790),c=a(56427);const s=({tweet:e})=>e?(0,n.jsxs)("div",{className:"twitter-preview__quote-tweet",children:[(0,n.jsx)(c.SandBox,{html:`<blockquote class="twitter-tweet" data-conversation="none" data-dnt="true"><a href="${e}"></a></blockquote>`,scripts:["https://platform.twitter.com/widgets.js"],title:"Embedded tweet"}),(0,n.jsx)("div",{className:"twitter-preview__quote-tweet-overlay"})]}):null},940:(e,t,a)=>{"use strict";a.d(t,{B:()=>i});var n=a(10790),c=a(27723),s=a(43204);const __=c.__,i=({profileImage:e,showThreadConnector:t})=>(0,n.jsxs)("div",{className:"twitter-preview__sidebar",children:[(0,n.jsx)("div",{className:"twitter-preview__profile-image",children:e?(0,n.jsx)("img",{alt:__("Twitter profile image","jetpack-publicize-pkg"),src:e}):(0,n.jsx)(s.f,{})}),t&&(0,n.jsx)("div",{className:"twitter-preview__connector"})]})},16801:(e,t,a)=>{"use strict";a.d(t,{E:()=>s});var n=a(10790),c=a(31280);const s=({text:e,url:t,retainUrl:a})=>{if(!e)return null;const s=t&&!a&&e.endsWith(t)?e.substring(0,e.lastIndexOf(t)):e;return(0,n.jsx)("div",{className:"twitter-preview__text",children:(0,c.x5)(s,{platform:"twitter"})})}},30081:(e,t,a)=>{"use strict";e.exports=a.p+"images/connections-facebook-c8a9715c8b6c8707594f.png"},26818:(e,t,a)=>{"use strict";e.exports=a.p+"images/connections-instagram-business-53d4764803db1a7b3cbc.png"},43443:(e,t,a)=>{"use strict";e.exports=a.p+"images/connections-linkedin-553fcb77cb734fc8d08f.png"},11326:(e,t,a)=>{"use strict";e.exports=a.p+"images/connections-nextdoor-6e76141465483081fde8.png"},65398:(e,t,a)=>{"use strict";e.exports=a.p+"images/connections-threads-1c941351fac252724ae7.png"},9791:(e,t,a)=>{"use strict";e.exports=a.p+"images/connections-tumblr-ac23dad7016040416331.png"},26365:(e,t,a)=>{"use strict";e.exports=a.p+"images/dois-66b3f018328e8f585646.jpg"},45329:(e,t,a)=>{"use strict";e.exports=a.p+"images/edge-e1adff73f2dc1c1dc64e.jpg"},9509:(e,t,a)=>{"use strict";e.exports=a.p+"images/fullscreen-21ca1ee2bafcb1c8eb55.jpg"},22417:(e,t,a)=>{"use strict";e.exports=a.p+"images/highway-205f94656e2fcc4aeb86.jpg"},39384:e=>{"use strict";e.exports=window.JetpackConnection},97999:e=>{"use strict";e.exports=window.JetpackScriptDataModule},51609:e=>{"use strict";e.exports=window.React},10790:e=>{"use strict";e.exports=window.ReactJSXRuntime},66087:e=>{"use strict";e.exports=window.lodash},1455:e=>{"use strict";e.exports=window.wp.apiFetch},94715:e=>{"use strict";e.exports=window.wp.blockEditor},56427:e=>{"use strict";e.exports=window.wp.components},29491:e=>{"use strict";e.exports=window.wp.compose},3582:e=>{"use strict";e.exports=window.wp.coreData},47143:e=>{"use strict";e.exports=window.wp.data},38443:e=>{"use strict";e.exports=window.wp.date},64040:e=>{"use strict";e.exports=window.wp.deprecated},98490:e=>{"use strict";e.exports=window.wp.domReady},14309:e=>{"use strict";e.exports=window.wp.editPost},43656:e=>{"use strict";e.exports=window.wp.editor},86087:e=>{"use strict";e.exports=window.wp.element},52619:e=>{"use strict";e.exports=window.wp.hooks},18537:e=>{"use strict";e.exports=window.wp.htmlEntities},27723:e=>{"use strict";e.exports=window.wp.i18n},16480:e=>{"use strict";e.exports=window.wp.mediaUtils},692:e=>{"use strict";e.exports=window.wp.notices},92279:e=>{"use strict";e.exports=window.wp.plugins},41233:e=>{"use strict";e.exports=window.wp.preferences},5573:e=>{"use strict";e.exports=window.wp.primitives},93832:e=>{"use strict";e.exports=window.wp.url},96072:e=>{function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},13022:(e,t,a)=>{"use strict";function n(e){var t,a,c="";if("string"==typeof e||"number"==typeof e)c+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(a=n(e[t]))&&(c&&(c+=" "),c+=a)}else for(a in e)e[a]&&(c&&(c+=" "),c+=a);return c}a.d(t,{A:()=>c});const c=function(){for(var e,t,a=0,c="",s=arguments.length;a<s;a++)(e=arguments[a])&&(t=n(e))&&(c&&(c+=" "),c+=t);return c}},38377:e=>{"use strict";e.exports=JSON.parse('{"T":{"White":"#fff","Black":"#000","Gray 0":"#f6f7f7","Gray 5":"#dcdcde","Gray 10":"#c3c4c7","Gray 20":"#a7aaad","Gray 30":"#8c8f94","Gray 40":"#787c82","Gray 50":"#646970","Gray 60":"#50575e","Gray 70":"#3c434a","Gray 80":"#2c3338","Gray 90":"#1d2327","Gray 100":"#101517","Gray":"#646970","Blue 0":"#fbfcfe","Blue 5":"#f7f8fe","Blue 10":"#d6ddf9","Blue 20":"#adbaf3","Blue 30":"#7b90ff","Blue 40":"#546ff3","Blue 50":"#3858e9","Blue 60":"#2a46ce","Blue 70":"#1d35b4","Blue 80":"#1f3286","Blue 90":"#14215a","Blue 100":"#0a112d","Blue":"#3858e9","WordPress Blue 0":"#fbfcfe","WordPress Blue 5":"#f7f8fe","WordPress Blue 10":"#d6ddf9","WordPress Blue 20":"#adbaf3","WordPress Blue 30":"#7b90ff","WordPress Blue 40":"#546ff3","WordPress Blue 50":"#3858e9","WordPress Blue 60":"#2a46ce","WordPress Blue 70":"#1d35b4","WordPress Blue 80":"#1f3286","WordPress Blue 90":"#14215a","WordPress Blue 100":"#0a112d","WordPress Blue":"#3858e9","Purple 0":"#f2e9ed","Purple 5":"#ebcee0","Purple 10":"#e3afd5","Purple 20":"#d48fc8","Purple 30":"#c475bd","Purple 40":"#b35eb1","Purple 50":"#984a9c","Purple 60":"#7c3982","Purple 70":"#662c6e","Purple 80":"#4d2054","Purple 90":"#35163b","Purple 100":"#1e0c21","Purple":"#984a9c","Pink 0":"#f5e9ed","Pink 5":"#f2ceda","Pink 10":"#f7a8c3","Pink 20":"#f283aa","Pink 30":"#eb6594","Pink 40":"#e34c84","Pink 50":"#c9356e","Pink 60":"#ab235a","Pink 70":"#8c1749","Pink 80":"#700f3b","Pink 90":"#4f092a","Pink 100":"#260415","Pink":"#c9356e","Red 0":"#f7ebec","Red 5":"#facfd2","Red 10":"#ffabaf","Red 20":"#ff8085","Red 30":"#f86368","Red 40":"#e65054","Red 50":"#d63638","Red 60":"#b32d2e","Red 70":"#8a2424","Red 80":"#691c1c","Red 90":"#451313","Red 100":"#240a0a","Red":"#d63638","Orange 0":"#f5ece6","Orange 5":"#f7dcc6","Orange 10":"#ffbf86","Orange 20":"#faa754","Orange 30":"#e68b28","Orange 40":"#d67709","Orange 50":"#b26200","Orange 60":"#8a4d00","Orange 70":"#704000","Orange 80":"#543100","Orange 90":"#361f00","Orange 100":"#1f1200","Orange":"#b26200","Yellow 0":"#f5f1e1","Yellow 5":"#f5e6b3","Yellow 10":"#f2d76b","Yellow 20":"#f0c930","Yellow 30":"#deb100","Yellow 40":"#c08c00","Yellow 50":"#9d6e00","Yellow 60":"#7d5600","Yellow 70":"#674600","Yellow 80":"#4f3500","Yellow 90":"#320","Yellow 100":"#1c1300","Yellow":"#9d6e00","Green 0":"#e6f2e8","Green 5":"#b8e6bf","Green 10":"#68de86","Green 20":"#1ed15a","Green 30":"#00ba37","Green 40":"#00a32a","Green 50":"#008a20","Green 60":"#007017","Green 70":"#005c12","Green 80":"#00450c","Green 90":"#003008","Green 100":"#001c05","Green":"#008a20","Celadon 0":"#e4f2ed","Celadon 5":"#a7e8d3","Celadon 10":"#66deb9","Celadon 20":"#31cc9f","Celadon 30":"#09b585","Celadon 40":"#009e73","Celadon 50":"#008763","Celadon 60":"#007053","Celadon 70":"#005c44","Celadon 80":"#004533","Celadon 90":"#003024","Celadon 100":"#001c15","Celadon":"#008763","Automattic Blue 0":"#ebf4fa","Automattic Blue 5":"#c4e2f5","Automattic Blue 10":"#88ccf2","Automattic Blue 20":"#5ab7e8","Automattic Blue 30":"#24a3e0","Automattic Blue 40":"#1490c7","Automattic Blue 50":"#0277a8","Automattic Blue 60":"#036085","Automattic Blue 70":"#02506e","Automattic Blue 80":"#02384d","Automattic Blue 90":"#022836","Automattic Blue 100":"#021b24","Automattic Blue":"#24a3e0","Simplenote Blue 0":"#e9ecf5","Simplenote Blue 5":"#ced9f2","Simplenote Blue 10":"#abc1f5","Simplenote Blue 20":"#84a4f0","Simplenote Blue 30":"#618df2","Simplenote Blue 40":"#4678eb","Simplenote Blue 50":"#3361cc","Simplenote Blue 60":"#1d4fc4","Simplenote Blue 70":"#113ead","Simplenote Blue 80":"#0d2f85","Simplenote Blue 90":"#09205c","Simplenote Blue 100":"#05102e","Simplenote Blue":"#3361cc","WooCommerce Purple 0":"#f2edff","WooCommerce Purple 5":"#e1d7ff","WooCommerce Purple 10":"#d1c1ff","WooCommerce Purple 20":"#b999ff","WooCommerce Purple 30":"#a77eff","WooCommerce Purple 40":"#873eff","WooCommerce Purple 50":"#720eec","WooCommerce Purple 60":"#6108ce","WooCommerce Purple 70":"#5007aa","WooCommerce Purple 80":"#3c087e","WooCommerce Purple 90":"#2c045d","WooCommerce Purple 100":"#1f0342","WooCommerce Purple":"#720eec","Jetpack Green 0":"#f0f2eb","Jetpack Green 5":"#d0e6b8","Jetpack Green 10":"#9dd977","Jetpack Green 20":"#64ca43","Jetpack Green 30":"#2fb41f","Jetpack Green 40":"#069e08","Jetpack Green 50":"#008710","Jetpack Green 60":"#007117","Jetpack Green 70":"#005b18","Jetpack Green 80":"#004515","Jetpack Green 90":"#003010","Jetpack Green 100":"#001c09","Jetpack Green":"#069e08"}}')}},t={};function a(n){var c=t[n];if(void 0!==c)return c.exports;var s=t[n]={exports:{}};return e[n](s,s.exports,a),s.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;a.g.importScripts&&(e=a.g.location+"");var t=a.g.document;if(!e&&t&&(t.currentScript&&"SCRIPT"===t.currentScript.tagName.toUpperCase()&&(e=t.currentScript.src),!e)){var n=t.getElementsByTagName("script");if(n.length)for(var c=n.length-1;c>-1&&(!e||!/^http(s?):/.test(e));)e=n[c--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),a.p=e})(),(()=>{"use strict";a(41116)})()})();