/*! For license information please see jp-search-configure.js.LICENSE.txt */
(()=>{var e={1113:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(6087);const n=(0,s.forwardRef)((function({icon:e,size:t=24,...r},n){return(0,s.cloneElement)(e,{width:t,height:t,...r,ref:n})}))},2800:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(5573),n=r(790);const a=(0,n.jsx)(s.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(s.<PERSON>,{fillRule:"evenodd",d:"M10.289 4.836A1 1 0 0111.275 4h1.306a1 1 0 01.987.836l.244 1.466c.787.26 1.503.679 2.108 1.218l1.393-.522a1 1 0 011.216.437l.653 1.13a1 1 0 01-.23 1.273l-1.148.944a6.025 6.025 0 010 2.435l1.149.946a1 1 0 01.23 1.272l-.653 1.13a1 1 0 01-1.216.437l-1.394-.522c-.605.54-1.32.958-2.108 1.218l-.244 1.466a1 1 0 01-.987.836h-1.306a1 1 0 01-.986-.836l-.244-1.466a5.995 5.995 0 01-2.108-1.218l-1.394.522a1 1 0 01-1.217-.436l-.653-1.131a1 1 0 01.23-1.272l1.149-.946a6.026 6.026 0 010-2.435l-1.148-.944a1 1 0 01-.23-1.272l.653-1.131a1 1 0 011.217-.437l1.393.522a5.994 5.994 0 012.108-1.218l.244-1.466zM14.929 12a3 3 0 11-6 0 3 3 0 016 0z",clipRule:"evenodd"})})},5938:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(5573),n=r(790);const a=(0,n.jsx)(s.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)(s.Path,{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM5 4.5h14c.3 0 .5.2.5.5v8.4l-3-2.9c-.3-.3-.8-.3-1 0L11.9 14 9 12c-.3-.2-.6-.2-.8 0l-3.6 2.6V5c-.1-.3.1-.5.4-.5zm14 15H5c-.3 0-.5-.2-.5-.5v-2.4l4.1-3 3 1.9c.*******.9-.1L16 12l3.5 3.4V19c0 .3-.2.5-.5.5z"})})},7399:e=>{!function(){"use strict";var t=[],r=3988292384;function s(e){var t,s,n,a,o=-1;for(t=0,n=e.length;t<n;t+=1){for(a=255&(o^e[t]),s=0;s<8;s+=1)1&~a?a>>>=1:a=a>>>1^r;o=o>>>8^a}return~o}function n(e,r){var s,a,o;if(void 0!==n.crc&&r&&e||(n.crc=~0,e)){for(s=n.crc,a=0,o=e.length;a<o;a+=1)s=s>>>8^t[255&(s^e[a])];return n.crc=s,~s}}!function(){var e,s,n;for(s=0;s<256;s+=1){for(e=s,n=0;n<8;n+=1)1&e?e=r^e>>>1:e>>>=1;t[s]=e>>>0}}(),e.exports=function(e,t){var r;e="string"==typeof e?(r=e,Array.prototype.map.call(r,(function(e){return e.charCodeAt(0)}))):e;return((t?s(e):n(e))>>>0).toString(16)},e.exports.direct=s,e.exports.table=n}()},4224:e=>{"use strict";e.exports=function(e,t){t||(t={}),"function"==typeof t&&(t={cmp:t});var r,s="boolean"==typeof t.cycles&&t.cycles,n=t.cmp&&(r=t.cmp,function(e){return function(t,s){var n={key:t,value:e[t]},a={key:s,value:e[s]};return r(n,a)}}),a=[];return function e(t){if(t&&t.toJSON&&"function"==typeof t.toJSON&&(t=t.toJSON()),void 0!==t){if("number"==typeof t)return isFinite(t)?""+t:"null";if("object"!=typeof t)return JSON.stringify(t);var r,o;if(Array.isArray(t)){for(o="[",r=0;r<t.length;r++)r&&(o+=","),o+=e(t[r])||"null";return o+"]"}if(null===t)return"null";if(-1!==a.indexOf(t)){if(s)return JSON.stringify("__cycle__");throw new TypeError("Converting circular structure to JSON")}var i=a.push(t)-1,c=Object.keys(t).sort(n&&n(t));for(o="",r=0;r<c.length;r++){var l=c[r],u=e(t[l]);u&&(o&&(o+=","),o+=JSON.stringify(l)+":"+u)}return a.splice(i,1),"{"+o+"}"}}(e)}},254:(e,t,r)=>{"use strict";var s=r(5415),n={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},a={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},o={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},i={};function c(e){return s.isMemo(e)?o:i[e.$$typeof]||n}i[s.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},i[s.Memo]=o;var l=Object.defineProperty,u=Object.getOwnPropertyNames,p=Object.getOwnPropertySymbols,h=Object.getOwnPropertyDescriptor,d=Object.getPrototypeOf,f=Object.prototype;e.exports=function e(t,r,s){if("string"!=typeof r){if(f){var n=d(r);n&&n!==f&&e(t,n,s)}var o=u(r);p&&(o=o.concat(p(r)));for(var i=c(t),m=c(r),g=0;g<o.length;++g){var y=o[g];if(!(a[y]||s&&s[y]||m&&m[y]||i&&i[y])){var v=h(r,y);try{l(t,y,v)}catch(e){}}}}return t}},8693:(e,t,r)=>{var s=r(1665).Symbol;e.exports=s},600:e=>{e.exports=function(e,t){for(var r=-1,s=null==e?0:e.length,n=Array(s);++r<s;)n[r]=t(e[r],r,e);return n}},740:(e,t,r)=>{var s=r(8693),n=r(9079),a=r(9170),o=s?s.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":o&&o in Object(e)?n(e):a(e)}},1856:(e,t,r)=>{var s=r(8693),n=r(600),a=r(5413),o=r(7614),i=s?s.prototype:void 0,c=i?i.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(a(t))return n(t,e)+"";if(o(t))return c?c.call(t):"";var r=t+"";return"0"==r&&1/t==-1/0?"-0":r}},9156:(e,t,r)=>{var s=r(1284),n=/^\s+/;e.exports=function(e){return e?e.slice(0,s(e)+1).replace(n,""):e}},9324:e=>{var t="object"==typeof window&&window&&window.Object===Object&&window;e.exports=t},9079:(e,t,r)=>{var s=r(8693),n=Object.prototype,a=n.hasOwnProperty,o=n.toString,i=s?s.toStringTag:void 0;e.exports=function(e){var t=a.call(e,i),r=e[i];try{e[i]=void 0;var s=!0}catch(e){}var n=o.call(e);return s&&(t?e[i]=r:delete e[i]),n}},9170:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},1665:(e,t,r)=>{var s=r(9324),n="object"==typeof self&&self&&self.Object===Object&&self,a=s||n||Function("return this")();e.exports=a},1284:e=>{var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},3257:(e,t,r)=>{var s=r(9169),n=r(2776),a=r(4186),o=Math.max,i=Math.min;e.exports=function(e,t,r){var c,l,u,p,h,d,f=0,m=!1,g=!1,y=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function v(t){var r=c,s=l;return c=l=void 0,f=t,p=e.apply(s,r)}function _(e){var r=e-d;return void 0===d||r>=t||r<0||g&&e-f>=u}function b(){var e=n();if(_(e))return w(e);h=setTimeout(b,function(e){var r=t-(e-d);return g?i(r,u-(e-f)):r}(e))}function w(e){return h=void 0,y&&c?v(e):(c=l=void 0,p)}function E(){var e=n(),r=_(e);if(c=arguments,l=this,d=e,r){if(void 0===h)return function(e){return f=e,h=setTimeout(b,t),m?v(e):p}(d);if(g)return clearTimeout(h),h=setTimeout(b,t),v(d)}return void 0===h&&(h=setTimeout(b,t)),p}return t=a(t)||0,s(r)&&(m=!!r.leading,u=(g="maxWait"in r)?o(a(r.maxWait)||0,t):u,y="trailing"in r?!!r.trailing:y),E.cancel=function(){void 0!==h&&clearTimeout(h),f=0,c=d=l=h=void 0},E.flush=function(){return void 0===h?p:w(n())},E}},5413:e=>{var t=Array.isArray;e.exports=t},9169:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},1726:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},7614:(e,t,r)=>{var s=r(740),n=r(1726);e.exports=function(e){return"symbol"==typeof e||n(e)&&"[object Symbol]"==s(e)}},2776:(e,t,r)=>{var s=r(1665);e.exports=function(){return s.Date.now()}},4186:(e,t,r)=>{var s=r(9156),n=r(9169),a=r(7614),o=/^[-+]0x[0-9a-f]+$/i,i=/^0b[01]+$/i,c=/^0o[0-7]+$/i,l=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(a(e))return NaN;if(n(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=n(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=s(e);var r=i.test(e);return r||c.test(e)?l(e.slice(2),r?2:8):o.test(e)?NaN:+e}},6938:(e,t,r)=>{var s=r(1856);e.exports=function(e){return null==e?"":s(e)}},4436:(e,t,r)=>{var s=r(6938),n=0;e.exports=function(e){var t=++n;return s(e)+t}},4997:e=>{var t=1e3,r=60*t,s=60*r,n=24*s,a=7*n,o=365.25*n;function i(e,t,r,s){var n=t>=1.5*r;return Math.round(e/r)+" "+s+(n?"s":"")}e.exports=function(e,c){c=c||{};var l=typeof e;if("string"===l&&e.length>0)return function(e){if((e=String(e)).length>100)return;var i=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!i)return;var c=parseFloat(i[1]);switch((i[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return c*o;case"weeks":case"week":case"w":return c*a;case"days":case"day":case"d":return c*n;case"hours":case"hour":case"hrs":case"hr":case"h":return c*s;case"minutes":case"minute":case"mins":case"min":case"m":return c*r;case"seconds":case"second":case"secs":case"sec":case"s":return c*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}(e);if("number"===l&&isFinite(e))return c.long?function(e){var a=Math.abs(e);if(a>=n)return i(e,a,n,"day");if(a>=s)return i(e,a,s,"hour");if(a>=r)return i(e,a,r,"minute");if(a>=t)return i(e,a,t,"second");return e+" ms"}(e):function(e){var a=Math.abs(e);if(a>=n)return Math.round(e/n)+"d";if(a>=s)return Math.round(e/s)+"h";if(a>=r)return Math.round(e/r)+"m";if(a>=t)return Math.round(e/t)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},4936:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});var s=r(7399),n=r.n(s),a=r(67),o=r.n(a),i=r(1264),c=r.n(i);const l=o()("photon"),u={width:"w",height:"h",letterboxing:"lb",removeLetterboxing:"ulb"},p="__domain__.invalid",h=`https://${p}`,d="https://i0.wp.com";function f(e,t){let r;try{r=new URL(e,h)}catch{return null}const s="https:"===r.protocol,a=new URL(d);if(o=r.host,/^i[0-2]\.wp\.com$/.test(o))a.pathname=r.pathname,a.hostname=s?"i0.wp.com":r.hostname;else{if(r.search)return null;let e=r.href.replace(`${r.protocol}/`,"");"blob:"===r.protocol&&(e=r.pathname.replace("://","//")),r.hostname===p&&(e=r.pathname),a.pathname=e,a.hostname=function(e,t){if(t)return"i0.wp.com";const r=n()(e),s=c()(r),a="i"+Math.floor(3*s());return l('determined server "%s" to use with "%s"',a,e),a+".wp.com"}(e,"https:"===a.protocol),s&&a.searchParams.set("ssl","1")}var o;if(t)for(const[e,r]of Object.entries(t))"host"!==e&&"hostname"!==e?"secure"!==e||r?a.searchParams.set(u[e]??e,r.toString()):a.protocol="http:":a.hostname=r;return l("generated Photon URL: %s",a.href),a.href}},1473:(e,t)=>{"use strict";var r=Object.prototype,s=r.toString,n=r.hasOwnProperty,a="[object Object]",o="[object Array]";function i(e,t){return null!=e?e+"["+t+"]":t}t.U=function e(t,r,c){var l=s.call(t);if(void 0===c)if(l===a)c={};else{if(l!==o)return;c=[]}for(var u in t)if(n.call(t,u)){var p=t[u];if(null!=p)switch(s.call(p)){case o:case a:e(p,i(r,u),c);break;default:c[i(r,u)]=p}}return c}},5395:(e,t)=>{"use strict";var r="function"==typeof Symbol&&Symbol.for,s=r?Symbol.for("react.element"):60103,n=r?Symbol.for("react.portal"):60106,a=r?Symbol.for("react.fragment"):60107,o=r?Symbol.for("react.strict_mode"):60108,i=r?Symbol.for("react.profiler"):60114,c=r?Symbol.for("react.provider"):60109,l=r?Symbol.for("react.context"):60110,u=r?Symbol.for("react.async_mode"):60111,p=r?Symbol.for("react.concurrent_mode"):60111,h=r?Symbol.for("react.forward_ref"):60112,d=r?Symbol.for("react.suspense"):60113,f=r?Symbol.for("react.suspense_list"):60120,m=r?Symbol.for("react.memo"):60115,g=r?Symbol.for("react.lazy"):60116,y=r?Symbol.for("react.block"):60121,v=r?Symbol.for("react.fundamental"):60117,_=r?Symbol.for("react.responder"):60118,b=r?Symbol.for("react.scope"):60119;function w(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case s:switch(e=e.type){case u:case p:case a:case i:case o:case d:return e;default:switch(e=e&&e.$$typeof){case l:case h:case g:case m:case c:return e;default:return t}}case n:return t}}}function E(e){return w(e)===p}t.AsyncMode=u,t.ConcurrentMode=p,t.ContextConsumer=l,t.ContextProvider=c,t.Element=s,t.ForwardRef=h,t.Fragment=a,t.Lazy=g,t.Memo=m,t.Portal=n,t.Profiler=i,t.StrictMode=o,t.Suspense=d,t.isAsyncMode=function(e){return E(e)||w(e)===u},t.isConcurrentMode=E,t.isContextConsumer=function(e){return w(e)===l},t.isContextProvider=function(e){return w(e)===c},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===s},t.isForwardRef=function(e){return w(e)===h},t.isFragment=function(e){return w(e)===a},t.isLazy=function(e){return w(e)===g},t.isMemo=function(e){return w(e)===m},t.isPortal=function(e){return w(e)===n},t.isProfiler=function(e){return w(e)===i},t.isStrictMode=function(e){return w(e)===o},t.isSuspense=function(e){return w(e)===d},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===a||e===p||e===i||e===o||e===d||e===f||"object"==typeof e&&null!==e&&(e.$$typeof===g||e.$$typeof===m||e.$$typeof===c||e.$$typeof===l||e.$$typeof===h||e.$$typeof===v||e.$$typeof===_||e.$$typeof===b||e.$$typeof===y)},t.typeOf=w},5415:(e,t,r)=>{"use strict";e.exports=r(5395)},7776:(e,t)=>{"use strict";var r=60103,s=60106,n=60107,a=60108,o=60114,i=60109,c=60110,l=60112,u=60113,p=60120,h=60115,d=60116,f=60121,m=60122,g=60117,y=60129,v=60131;if("function"==typeof Symbol&&Symbol.for){var _=Symbol.for;r=_("react.element"),s=_("react.portal"),n=_("react.fragment"),a=_("react.strict_mode"),o=_("react.profiler"),i=_("react.provider"),c=_("react.context"),l=_("react.forward_ref"),u=_("react.suspense"),p=_("react.suspense_list"),h=_("react.memo"),d=_("react.lazy"),f=_("react.block"),m=_("react.server.block"),g=_("react.fundamental"),y=_("react.debug_trace_mode"),v=_("react.legacy_hidden")}function b(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case n:case o:case a:case u:case p:return e;default:switch(e=e&&e.$$typeof){case c:case l:case d:case h:case i:return e;default:return t}}case s:return t}}}t.isContextConsumer=function(e){return b(e)===c}},1444:(e,t,r)=>{"use strict";e.exports=r(7776)},2302:(e,t,r)=>{"use strict";r.d(t,{t:()=>n});var s=r(1609),n=r.n(s)().createContext(null)},1644:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var s=r(1609),n=r.n(s),a=r(2302),o=r(1833),i=r(47);const c=function(e){var t=e.store,r=e.context,c=e.children,l=(0,s.useMemo)((function(){var e=(0,o.K)(t);return{store:t,subscription:e}}),[t]),u=(0,s.useMemo)((function(){return t.getState()}),[t]);(0,i.E)((function(){var e=l.subscription;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),u!==t.getState()&&e.notifyNestedSubs(),function(){e.tryUnsubscribe(),e.onStateChange=null}}),[l,u]);var p=r||a.t;return n().createElement(p.Provider,{value:l},c)}},349:(e,t,r)=>{"use strict";r.d(t,{A:()=>E});var s=r(7790),n=r(4001),a=r(254),o=r.n(a),i=r(1609),c=r.n(i),l=r(1444),u=r(1833),p=r(47),h=r(2302),d=["getDisplayName","methodName","renderCountProp","shouldHandleStateChanges","storeKey","withRef","forwardRef","context"],f=["reactReduxForwardedRef"],m=[],g=[null,null];function y(e,t){var r=e[1];return[t.payload,r+1]}function v(e,t,r){(0,p.E)((function(){return e.apply(void 0,t)}),r)}function _(e,t,r,s,n,a,o){e.current=s,t.current=n,r.current=!1,a.current&&(a.current=null,o())}function b(e,t,r,s,n,a,o,i,c,l){if(e){var u=!1,p=null,h=function(){if(!u){var e,r,h=t.getState();try{e=s(h,n.current)}catch(e){r=e,p=e}r||(p=null),e===a.current?o.current||c():(a.current=e,i.current=e,o.current=!0,l({type:"STORE_UPDATED",payload:{error:r}}))}};r.onStateChange=h,r.trySubscribe(),h();return function(){if(u=!0,r.tryUnsubscribe(),r.onStateChange=null,p)throw p}}}var w=function(){return[null,0]};function E(e,t){void 0===t&&(t={});var r=t,a=r.getDisplayName,p=void 0===a?function(e){return"ConnectAdvanced("+e+")"}:a,E=r.methodName,k=void 0===E?"connectAdvanced":E,S=r.renderCountProp,j=void 0===S?void 0:S,C=r.shouldHandleStateChanges,x=void 0===C||C,A=r.storeKey,O=void 0===A?"store":A,N=(r.withRef,r.forwardRef),R=void 0!==N&&N,F=r.context,P=void 0===F?h.t:F,T=(0,n.A)(r,d),I=P;return function(t){var r=t.displayName||t.name||"Component",a=p(r),h=(0,s.A)({},T,{getDisplayName:p,methodName:k,renderCountProp:j,shouldHandleStateChanges:x,storeKey:O,displayName:a,wrappedComponentName:r,WrappedComponent:t}),d=T.pure;var E=d?i.useMemo:function(e){return e()};function S(r){var a=(0,i.useMemo)((function(){var e=r.reactReduxForwardedRef,t=(0,n.A)(r,f);return[r.context,e,t]}),[r]),o=a[0],p=a[1],d=a[2],k=(0,i.useMemo)((function(){return o&&o.Consumer&&(0,l.isContextConsumer)(c().createElement(o.Consumer,null))?o:I}),[o,I]),S=(0,i.useContext)(k),j=Boolean(r.store)&&Boolean(r.store.getState)&&Boolean(r.store.dispatch);Boolean(S)&&Boolean(S.store);var C=j?r.store:S.store,A=(0,i.useMemo)((function(){return function(t){return e(t.dispatch,h)}(C)}),[C]),O=(0,i.useMemo)((function(){if(!x)return g;var e=(0,u.K)(C,j?null:S.subscription),t=e.notifyNestedSubs.bind(e);return[e,t]}),[C,j,S]),N=O[0],R=O[1],F=(0,i.useMemo)((function(){return j?S:(0,s.A)({},S,{subscription:N})}),[j,S,N]),P=(0,i.useReducer)(y,m,w),T=P[0][0],M=P[1];if(T&&T.error)throw T.error;var L=(0,i.useRef)(),z=(0,i.useRef)(d),$=(0,i.useRef)(),D=(0,i.useRef)(!1),H=E((function(){return $.current&&d===z.current?$.current:A(C.getState(),d)}),[C,T,d]);v(_,[z,L,D,d,H,$,R]),v(b,[x,C,N,A,z,L,D,$,R,M],[C,N,A]);var B=(0,i.useMemo)((function(){return c().createElement(t,(0,s.A)({},H,{ref:p}))}),[p,t,H]);return(0,i.useMemo)((function(){return x?c().createElement(k.Provider,{value:F},B):B}),[k,B,F])}var C=d?c().memo(S):S;if(C.WrappedComponent=t,C.displayName=S.displayName=a,R){var A=c().forwardRef((function(e,t){return c().createElement(C,(0,s.A)({},e,{reactReduxForwardedRef:t}))}));return A.displayName=a,A.WrappedComponent=t,o()(A,t)}return o()(C,t)}}},8087:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var s=r(7790),n=r(4001),a=r(349),o=r(4480),i=r(3132),c=r(7721),l=r(5263),u=r(8792),p=["pure","areStatesEqual","areOwnPropsEqual","areStatePropsEqual","areMergedPropsEqual"];function h(e,t,r){for(var s=t.length-1;s>=0;s--){var n=t[s](e);if(n)return n}return function(t,s){throw new Error("Invalid value of type "+typeof e+" for "+r+" argument when connecting component "+s.wrappedComponentName+".")}}function d(e,t){return e===t}function f(e){var t=void 0===e?{}:e,r=t.connectHOC,f=void 0===r?a.A:r,m=t.mapStateToPropsFactories,g=void 0===m?c.Ay:m,y=t.mapDispatchToPropsFactories,v=void 0===y?i.Ay:y,_=t.mergePropsFactories,b=void 0===_?l.Ay:_,w=t.selectorFactory,E=void 0===w?u.Ay:w;return function(e,t,r,a){void 0===a&&(a={});var i=a,c=i.pure,l=void 0===c||c,u=i.areStatesEqual,m=void 0===u?d:u,y=i.areOwnPropsEqual,_=void 0===y?o.A:y,w=i.areStatePropsEqual,k=void 0===w?o.A:w,S=i.areMergedPropsEqual,j=void 0===S?o.A:S,C=(0,n.A)(i,p),x=h(e,g,"mapStateToProps"),A=h(t,v,"mapDispatchToProps"),O=h(r,b,"mergeProps");return f(E,(0,s.A)({methodName:"connect",getDisplayName:function(e){return"Connect("+e+")"},shouldHandleStateChanges:Boolean(e),initMapStateToProps:x,initMapDispatchToProps:A,initMergeProps:O,pure:l,areStatesEqual:m,areOwnPropsEqual:_,areStatePropsEqual:k,areMergedPropsEqual:j},C))}}const m=f()},3132:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>a});var s=r(1400),n=r(5030);const a=[function(e){return"function"==typeof e?(0,n.Qb)(e,"mapDispatchToProps"):void 0},function(e){return e?void 0:(0,n.o6)((function(e){return{dispatch:e}}))},function(e){return e&&"object"==typeof e?(0,n.o6)((function(t){return(0,s.A)(e,t)})):void 0}]},7721:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>n});var s=r(5030);const n=[function(e){return"function"==typeof e?(0,s.Qb)(e,"mapStateToProps"):void 0},function(e){return e?void 0:(0,s.o6)((function(){return{}}))}]},5263:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>a});var s=r(7790);function n(e,t,r){return(0,s.A)({},r,e,t)}const a=[function(e){return"function"==typeof e?function(e){return function(t,r){r.displayName;var s,n=r.pure,a=r.areMergedPropsEqual,o=!1;return function(t,r,i){var c=e(t,r,i);return o?n&&a(c,s)||(s=c):(o=!0,s=c),s}}}(e):void 0},function(e){return e?void 0:function(){return n}}]},8792:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>i});var s=r(4001),n=["initMapStateToProps","initMapDispatchToProps","initMergeProps"];function a(e,t,r,s){return function(n,a){return r(e(n,a),t(s,a),a)}}function o(e,t,r,s,n){var a,o,i,c,l,u=n.areStatesEqual,p=n.areOwnPropsEqual,h=n.areStatePropsEqual,d=!1;function f(n,d){var f,m,g=!p(d,o),y=!u(n,a);return a=n,o=d,g&&y?(i=e(a,o),t.dependsOnOwnProps&&(c=t(s,o)),l=r(i,c,o)):g?(e.dependsOnOwnProps&&(i=e(a,o)),t.dependsOnOwnProps&&(c=t(s,o)),l=r(i,c,o)):y?(f=e(a,o),m=!h(f,i),i=f,m&&(l=r(i,c,o)),l):l}return function(n,u){return d?f(n,u):(i=e(a=n,o=u),c=t(s,o),l=r(i,c,o),d=!0,l)}}function i(e,t){var r=t.initMapStateToProps,i=t.initMapDispatchToProps,c=t.initMergeProps,l=(0,s.A)(t,n),u=r(e,l),p=i(e,l),h=c(e,l);return(l.pure?o:a)(u,p,h,e,l)}},5030:(e,t,r)=>{"use strict";function s(e){return function(t,r){var s=e(t,r);function n(){return s}return n.dependsOnOwnProps=!1,n}}function n(e){return null!==e.dependsOnOwnProps&&void 0!==e.dependsOnOwnProps?Boolean(e.dependsOnOwnProps):1!==e.length}function a(e,t){return function(t,r){r.displayName;var s=function(e,t){return s.dependsOnOwnProps?s.mapToProps(e,t):s.mapToProps(e)};return s.dependsOnOwnProps=!0,s.mapToProps=function(t,r){s.mapToProps=e,s.dependsOnOwnProps=n(e);var a=s(t,r);return"function"==typeof a&&(s.mapToProps=a,s.dependsOnOwnProps=n(a),a=s(t,r)),a},s}}r.d(t,{Qb:()=>a,o6:()=>s})},9767:(e,t,r)=>{"use strict";r.d(t,{Kq:()=>s.A,Ng:()=>n.A});var s=r(1644),n=(r(349),r(2302),r(8087));r(1414),r(201),r(2887)},1414:(e,t,r)=>{"use strict";r(2302),r(2887)},7949:(e,t,r)=>{"use strict";r(1609),r(2302)},201:(e,t,r)=>{"use strict";r(1609),r(7949),r(1833),r(47),r(2302)},2887:(e,t,r)=>{"use strict";r(1609),r(2302),r(7949)},4952:(e,t,r)=>{"use strict";r.d(t,{Kq:()=>s.Kq,Ng:()=>s.Ng});var s=r(9767),n=r(3691);(0,r(8446).d)(n.r)},1833:(e,t,r)=>{"use strict";r.d(t,{K:()=>a});var s=r(8446);var n={notify:function(){},get:function(){return[]}};function a(e,t){var r,a=n;function o(){c.onStateChange&&c.onStateChange()}function i(){var n,i,c;r||(r=t?t.addNestedSub(o):e.subscribe(o),n=(0,s.f)(),i=null,c=null,a={clear:function(){i=null,c=null},notify:function(){n((function(){for(var e=i;e;)e.callback(),e=e.next}))},get:function(){for(var e=[],t=i;t;)e.push(t),t=t.next;return e},subscribe:function(e){var t=!0,r=c={callback:e,next:null,prev:c};return r.prev?r.prev.next=r:i=r,function(){t&&null!==i&&(t=!1,r.next?r.next.prev=r.prev:c=r.prev,r.prev?r.prev.next=r.next:i=r.next)}}})}var c={addNestedSub:function(e){return i(),a.subscribe(e)},notifyNestedSubs:function(){a.notify()},handleChangeWrapper:o,isSubscribed:function(){return Boolean(r)},trySubscribe:i,tryUnsubscribe:function(){r&&(r(),r=void 0,a.clear(),a=n)},getListeners:function(){return a}};return c}},8446:(e,t,r)=>{"use strict";r.d(t,{d:()=>n,f:()=>a});var s=function(e){e()},n=function(e){return s=e},a=function(){return s}},1400:(e,t,r)=>{"use strict";function s(e,t){var r={},s=function(s){var n=e[s];"function"==typeof n&&(r[s]=function(){return t(n.apply(void 0,arguments))})};for(var n in e)s(n);return r}r.d(t,{A:()=>s})},3691:(e,t,r)=>{"use strict";r.d(t,{r:()=>s.unstable_batchedUpdates});var s=r(5795)},4480:(e,t,r)=>{"use strict";function s(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}function n(e,t){if(s(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(var a=0;a<r.length;a++)if(!Object.prototype.hasOwnProperty.call(t,r[a])||!s(e[r[a]],t[r[a]]))return!1;return!0}r.d(t,{A:()=>n})},47:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var s=r(1609),n="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?s.useLayoutEffect:s.useEffect},6556:(e,t,r)=>{"use strict";r.d(t,{HY:()=>u,Tw:()=>h,y$:()=>l});var s=r(4021);function n(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var a="function"==typeof Symbol&&Symbol.observable||"@@observable",o=function(){return Math.random().toString(36).substring(7).split("").join(".")},i={INIT:"@@redux/INIT"+o(),REPLACE:"@@redux/REPLACE"+o(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+o()}};function c(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function l(e,t,r){var s;if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error(n(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw new Error(n(1));return r(l)(e,t)}if("function"!=typeof e)throw new Error(n(2));var o=e,u=t,p=[],h=p,d=!1;function f(){h===p&&(h=p.slice())}function m(){if(d)throw new Error(n(3));return u}function g(e){if("function"!=typeof e)throw new Error(n(4));if(d)throw new Error(n(5));var t=!0;return f(),h.push(e),function(){if(t){if(d)throw new Error(n(6));t=!1,f();var r=h.indexOf(e);h.splice(r,1),p=null}}}function y(e){if(!c(e))throw new Error(n(7));if(void 0===e.type)throw new Error(n(8));if(d)throw new Error(n(9));try{d=!0,u=o(u,e)}finally{d=!1}for(var t=p=h,r=0;r<t.length;r++){(0,t[r])()}return e}return y({type:i.INIT}),(s={dispatch:y,subscribe:g,getState:m,replaceReducer:function(e){if("function"!=typeof e)throw new Error(n(10));o=e,y({type:i.REPLACE})}})[a]=function(){var e,t=g;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw new Error(n(11));function r(){e.next&&e.next(m())}return r(),{unsubscribe:t(r)}}})[a]=function(){return this},e},s}function u(e){for(var t=Object.keys(e),r={},s=0;s<t.length;s++){var a=t[s];0,"function"==typeof e[a]&&(r[a]=e[a])}var o,c=Object.keys(r);try{!function(e){Object.keys(e).forEach((function(t){var r=e[t];if(void 0===r(void 0,{type:i.INIT}))throw new Error(n(12));if(void 0===r(void 0,{type:i.PROBE_UNKNOWN_ACTION()}))throw new Error(n(13))}))}(r)}catch(e){o=e}return function(e,t){if(void 0===e&&(e={}),o)throw o;for(var s=!1,a={},i=0;i<c.length;i++){var l=c[i],u=r[l],p=e[l],h=u(p,t);if(void 0===h){t&&t.type;throw new Error(n(14))}a[l]=h,s=s||h!==p}return(s=s||c.length!==Object.keys(e).length)?a:e}}function p(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}function h(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){return function(){var r=e.apply(void 0,arguments),a=function(){throw new Error(n(15))},o={getState:r.getState,dispatch:function(){return a.apply(void 0,arguments)}},i=t.map((function(e){return e(o)}));return a=p.apply(void 0,i)(r.dispatch),(0,s.A)((0,s.A)({},r),{},{dispatch:a})}}}},2047:e=>{"use strict";function t(e,r){var s;if(Array.isArray(r))for(s=0;s<r.length;s++)t(e,r[s]);else for(s in r)e[s]=(e[s]||[]).concat(r[s])}e.exports=function(e){var r,s={};return t(s,e),(r=function(e){return function(t){return function(r){var n,a,o=s[r.type],i=t(r);if(o)for(n=0;n<o.length;n++)(a=o[n](r,e))&&e.dispatch(a);return i}}}).effects=s,r}},1264:e=>{"use strict";var t=256,r=[],s=window,n=Math.pow(t,6),a=Math.pow(2,52),o=2*a,i=255,c=Math.random;function l(e){var r,s=e.length,n=this,a=0,o=n.i=n.j=0,c=n.S=[];for(s||(e=[s++]);a<t;)c[a]=a++;for(a=0;a<t;a++)c[a]=c[o=i&o+e[a%s]+(r=c[a])],c[o]=r;(n.g=function(e){for(var r,s=0,a=n.i,o=n.j,c=n.S;e--;)r=c[a=i&a+1],s=s*t+c[i&(c[a]=c[o=i&o+r])+(c[o]=r)];return n.i=a,n.j=o,s})(t)}function u(e,t){var r,s=[],n=(typeof e)[0];if(t&&"o"==n)for(r in e)try{s.push(u(e[r],t-1))}catch(e){}return s.length?s:"s"==n?e:e+"\0"}function p(e,t){for(var r,s=e+"",n=0;n<s.length;)t[i&n]=i&(r^=19*t[i&n])+s.charCodeAt(n++);return h(t)}function h(e){return String.fromCharCode.apply(0,e)}e.exports=function(i,c){if(c&&!0===c.global)return c.global=!1,Math.random=e.exports(i,c),c.global=!0,Math.random;var d=[],f=(p(u(c&&c.entropy||!1?[i,h(r)]:0 in arguments?i:function(e){try{return s.crypto.getRandomValues(e=new Uint8Array(t)),h(e)}catch(e){return[+new Date,s,s.navigator&&s.navigator.plugins,s.screen,h(r)]}}(),3),d),new l(d));return p(h(f.S),r),function(){for(var e=f.g(6),r=n,s=0;e<a;)e=(e+s)*t,r*=t,s=f.g(1);for(;e>=o;)e/=2,r/=2,s>>>=1;return(e+s)/r}},e.exports.resetGlobal=function(){Math.random=c},p(Math.random(),r)},1557:e=>{var t=/<\/?([a-z][a-z0-9]*)\b[^>]*>?/gi;e.exports=function(e){return(e=e||"").replace(t,"").trim()}},6442:(e,t,r)=>{"use strict";r.d(t,{A:()=>g});var s=r(6072),n=r.n(s),a=r(7723),o=r(4678),i=r(9611),c=r(459),l=r(5678),u=r(7479),p=r(7574),h=r(1388),d=r(8468),f=r(4952);const __=a.__;r.p=window.JetpackInstantSearchOptions.webpackPublicPath;const m={aggregations:(0,l.q1)([...window[u.O5].widgets,...window[u.O5].widgetsOutsideOverlay]),defaultSort:window[u.O5].defaultSort,hasOverlayWidgets:!!window[u.O5].hasOverlayWidgets,options:window[u.O5],themeOptions:(0,p.E)(window[u.O5])};function g(){const{color:e,excludedPostTypes:t,infiniteScroll:r,filteringOpensOverlay:s,postDate:a,resultFormat:l,showLogo:p,sort:g,sortEnabled:y,theme:v,trigger:_}=(0,i.A)(),b={...window[u.O5].overlayOptions,...(0,d.pickBy)({colorTheme:v,defaultSort:g,enableInfScroll:r,enableFilteringOpensOverlay:s,enablePostDate:a,enableSort:y,excludedPostTypes:t,highlightColor:e,overlayTrigger:_,resultFormat:l,showPoweredBy:p},(e=>void 0!==e))},{isLoading:w}=(0,o.A)();return React.createElement("div",{
/* translators: accessibility text for the widgets screen content landmark region. */
"aria-label":__("Jetpack Search customization preview","jetpack-search-pkg"),className:"jp-search-configure-app-wrapper",role:"region",tabIndex:"-1"},w?React.createElement("img",{className:"jp-search-configure-loading-spinner",width:"32",height:"32",alt:__("Loading","jetpack-search-pkg"),src:"//en.wordpress.com/i/loading/loading-64.gif"}):React.createElement(f.Kq,{store:h.A},React.createElement(c.A,n()({},m,{enableAnalytics:!1,initialIsVisible:!0,initialShowResults:!0,isInCustomizer:!1,overlayOptions:b,shouldCreatePortal:!1,shouldIntegrateWithDom:!1}))))}},288:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var s=r(6427),n=r(7723),a=r(1113),o=r(2800),i=r(7361);const __=n.__;const c=function({enableSidebar:e}){
// translators: Product name 'Jetpack Search' should not be translated
const t=__("Customize Jetpack Search","jetpack-search-pkg");return React.createElement("div",{className:"jp-search-configure-header"},React.createElement("div",{className:"jp-search-configure-header__navigable-toolbar-wrapper"},React.createElement("h1",{className:"jp-search-configure-header__title"},t)),React.createElement("div",{className:"jp-search-configure-header__actions"},React.createElement(i.A,null),React.createElement(s.Button,{"aria-label":__("Show settings","jetpack-search-pkg"),className:"jp-search-configure-header__show-settings-button",variant:"secondary",onClick:()=>e()},React.createElement(a.A,{icon:o.A}))))}},1017:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(7143),n=r(6087),a=r(6873),o=r(7479),i=r(7547),c=r(8394),l=r(584);function u(){const{isLargeViewport:e}=(0,s.useSelect)((e=>({isLargeViewport:e(a.store).isViewportMatch("large")}))),[t,r]=(0,n.useState)(c.B),u=(e=c.B)=>r(e);return(0,n.useEffect)((()=>{(0,i.n_)(),(0,i.wk)(window[o.O5].siteId),(0,i.yM)(`${i.be}_page_view`)}),[]),(0,n.useEffect)((()=>{e&&null===t&&u()}),[t,e]),React.createElement("div",{className:"jp-search-configure-root"},React.createElement(l.A,{disableSidebar:()=>r(null),enabledSidebarName:t,enableSidebar:u}))}},584:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(7723),n=r(6442),a=r(288),o=r(3161);const __=s.__;function i(e){const{enabledSidebarName:t,enableSidebar:r,disableSidebar:s}=e;return React.createElement("div",{className:"interface-interface-skeleton"},React.createElement("div",{className:"interface-interface-skeleton__editor"},React.createElement("div",{
/* translators: accessibility text for the widgets screen top bar landmark region. */
"aria-label":__("Jetpack Search customization top bar","jetpack-search-pkg"),className:"interface-interface-skeleton__header",role:"region",tabIndex:"-1"},React.createElement(a.A,{enableSidebar:r})),React.createElement("div",{className:"jp-search-configure-layout__body"},React.createElement(n.A,null),!!t&&React.createElement("div",{
/* translators: accessibility text for the widgets screen settings landmark region. */
"aria-label":__("Jetpack Search customization settings","jetpack-search-pkg"),className:"interface-interface-skeleton__sidebar",role:"region",tabIndex:"-1"},React.createElement(o.A,{disableSidebar:s,enabledSidebarName:t,enableSidebar:r})))))}},7361:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(6427),n=r(7723),a=r(617),o=r(7547);const __=n.__;function i(){const{isSaving:e,hasUnsavedEdits:t,saveRecords:r}=(0,a.A)();return React.createElement(s.Button,{"aria-disabled":e,className:"jp-search-configure-save-button",disabled:!t,isBusy:e,variant:"primary",onClick:(...t)=>{e||((0,o.yM)(`${o.be}_save_button_click`),r(...t))}},e?__("Saving…","jetpack-search-pkg"):__("Save","jetpack-search-pkg",0))}},3976:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(4715),n=r(7143),a=r(7723);const __=a.__,o=[{name:"Dull blue",color:"#463ECE"},{name:"Jazzberry jam",color:"#C6446F"},{name:"June bud",color:"#C4D455"}];function i({disabled:e,value:t,onChange:r}){const a=(0,n.useSelect)((e=>{const t=e("core/block-editor").getSettings()??{};return Array.isArray(t?.colors)&&t.colors.length>0?t.colors:o}));return React.createElement("div",{className:"jp-search-configure-color-input components-base-control"},React.createElement(s.__experimentalColorGradientControl,{label:__("Highlight for search terms","jetpack-search-pkg"),disabled:e,colorValue:t,colors:a,disableCustomColors:!1,disableCustomGradients:!0,onColorChange:r}))}},8659:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});r(6336),r(2926),r(887),r(3326),r(3893),r(3338),r(5508);var s=r(6427),n=r(6087),a=r(7723);const __=a.__,o=window.JetpackInstantSearchOptions.postTypes;function i({disabled:e,onChange:t,validPostTypes:r=o,value:a}){const i=(0,n.useMemo)((()=>Object.keys(r)),[r]),c=(0,n.useMemo)((()=>a&&Array.isArray(a)?new Set(a):new Set),[a]),l=c.size===i.length-1;return React.createElement("div",{className:"jp-search-configure-excluded-post-types-control components-base-control"},React.createElement("div",{className:"jp-search-configure-excluded-post-types-control__label"},__("Excluded post types","jetpack-search-pkg")),l&&React.createElement(s.Notice,{isDismissible:!1,status:"info"},__("You must leave at least one post type unchecked.","jetpack-search-pkg")),i.map((r=>{return React.createElement(s.CheckboxControl,{checked:c.has(r),disabled:e||!c.has(r)&&l,key:r,label:o[r].name,onChange:(n=r,e=>{const r=new Set(c);e?r.add(n):r.delete(n),t([...r])}),value:r,__nextHasNoMarginBottom:!0});var n})))}},3161:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var s=r(6427),n=r(7723),a=r(1113),o=r(5938),i=r(7361),c=r(8394),l=r(5372),u=r(2772),p=r(9081);const __=n.__;function h(e){const{enabledSidebarName:t,enableSidebar:r,disableSidebar:n}=e;return React.createElement("div",{className:"interface-complementary-area jp-search-configure-sidebar"},React.createElement("div",{className:"components-panel__header interface-complementary-area-header jp-search-configure-sidebar__panel-tabs",tabIndex:"-1"},React.createElement(p.A,{enabledSidebarName:t,enableSidebar:r}),React.createElement(i.A,null),React.createElement(s.Button,{"aria-label":__("Show preview","jetpack-search-pkg"),className:"jp-search-configure-sidebar__hide-settings-button",variant:"secondary",onClick:n},React.createElement(a.A,{icon:o.A}))),React.createElement("div",{className:"components-panel"},t===c.e&&React.createElement(l.A,null),t===c.B&&React.createElement(u.A,null)))}},5372:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var s=r(4715),n=r(6427),a=r(7723),o=r(3832),i=r(4120);const __=a.__;function c(){return React.createElement("div",{className:"jp-search-configure-sidebar-description"},React.createElement(s.BlockIcon,{icon:i.J}),React.createElement("div",null,React.createElement("p",null,__("Jetpack Search will allow your visitors to get search results as soon as they start typing. Customize this experience to offer better results that match your site.","jetpack-search-pkg")),React.createElement(n.Button,{href:"widgets.php",variant:"tertiary"},__("Edit widgets","jetpack-search-pkg")),React.createElement(n.Button,{href:(0,o.addQueryArgs)("customize.php",{"autofocus[section]":"jetpack_search",return:`${window.location.pathname}${window.location.search}`}),variant:"tertiary"},__("Configure in the Customizer","jetpack-search-pkg"))))}},2772:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});r(9060);var s=r(6427),n=r(7723),a=r(3022),o=r(617),i=r(4678),c=r(9611),l=r(7479),u=r(3976),p=r(8659),h=r(5666);const __=n.__,{isFreePlan:d=!1}=window[l.O5];function f(){const{color:e,excludedPostTypes:t,infiniteScroll:r=!0,filteringOpensOverlay:n=!0,resultFormat:f,setColor:m,setExcludedPostTypes:g,setInfiniteScroll:y,setFilteringOpensOverlay:v,setResultFormat:_,setShowLogo:b,setSort:w,setSortEnabled:E,setTheme:k,setTrigger:S,showLogo:j=!0,sort:C,sortEnabled:x=!0,theme:A,trigger:O,postDate:N=!1,setPostDate:R}=(0,c.A)(),{isSaving:F}=(0,o.A)(),{isLoading:P}=(0,i.A)(),T=F||P,I=[{label:__("Relevance (recommended)","jetpack-search-pkg"),value:"relevance"},{label:__("Newest first","jetpack-search-pkg"),value:"newest"},{label:__("Oldest first","jetpack-search-pkg"),value:"oldest"}];return f===l.s6&&I.push({label:__("Rating","jetpack-search-pkg"),value:"rating_desc"},{label:__("Price: low to high","jetpack-search-pkg"),value:"price_asc"},{label:__("Price: high to low","jetpack-search-pkg"),value:"price_desc"}),React.createElement(s.Panel,{className:(0,a.A)("jp-search-configure-sidebar-options",{"jp-search-configure-sidebar-options--is-disabled":T})},React.createElement(s.PanelBody,{title:__("Styling","jetpack-search-pkg"),initialOpen:!0},React.createElement(h.A,{disabled:T,onChange:k,value:A}),React.createElement(s.RadioControl,{className:"jp-search-configure-result-format-radios",label:__("Result format","jetpack-search-pkg"),selected:f,options:[{label:__("Minimal","jetpack-search-pkg"),value:"minimal"},{label:__("Expanded (shows images)","jetpack-search-pkg"),value:"expanded"},{label:__("Product (for WooCommerce stores)","jetpack-search-pkg"),value:"product"}],onChange:_}),React.createElement(u.A,{disabled:T,onChange:m,value:e})),React.createElement(s.PanelBody,{title:__("Search settings","jetpack-search-pkg"),initialOpen:!0},React.createElement(s.SelectControl,{className:"jp-search-configure-default-sort-select",disabled:T,label:__("Default sort","jetpack-search-pkg"),value:C,options:I,onChange:w,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),React.createElement(s.SelectControl,{className:"jp-search-configure-overlay-trigger-select",disabled:T,label:__("Overlay trigger","jetpack-search-pkg"),value:O,options:[{label:__("Open when user submits the form (recommended)","jetpack-search-pkg"),value:"submit"},{label:__("Open when user starts typing","jetpack-search-pkg"),value:"immediate"}],onChange:S,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),React.createElement(s.ToggleControl,{className:"jp-search-configure-filtering-opens-overlay-toggle",checked:n,disabled:T,help:__("Open overlay when filters are used outside the Jetpack Sidebar","jetpack-search-pkg"),label:__("Open overlay from filter links","jetpack-search-pkg"),onChange:v,__nextHasNoMarginBottom:!0}),React.createElement(p.A,{disabled:T,onChange:g,value:t})),React.createElement(s.PanelBody,{title:__("Additional settings","jetpack-search-pkg"),initialOpen:!0},React.createElement(s.ToggleControl,{className:"jp-search-configure-show-sort-toggle",checked:x,disabled:T,label:__("Show sort selector","jetpack-search-pkg"),onChange:E,__nextHasNoMarginBottom:!0}),React.createElement(s.ToggleControl,{className:"jp-search-configure-infinite-scroll-toggle",checked:r,disabled:T,label:__("Enable infinite scroll","jetpack-search-pkg"),onChange:y,__nextHasNoMarginBottom:!0}),"expanded"===f&&React.createElement(s.ToggleControl,{className:"jp-search-configure-post-date-toggle",checked:N,disabled:T,label:__("Show post date","jetpack-search-pkg"),onChange:R,__nextHasNoMarginBottom:!0}),!d&&React.createElement(s.ToggleControl,{className:"jp-search-configure-show-logo-toggle",checked:j,disabled:T,label:__('Show "Powered by Jetpack"',"jetpack-search-pkg"),onChange:b,__nextHasNoMarginBottom:!0})))}},9081:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(6427),n=r(7723),a=r(3022),o=r(8394);const __=n.__;function i({enabledSidebarName:e,enableSidebar:t}){return React.createElement("ul",null,React.createElement("li",null,React.createElement(c,{enableSidebar:t,identifier:o.e,isActive:e===o.e,label:__("Jetpack Search","jetpack-search-pkg")})),React.createElement("li",null,React.createElement(c,{enableSidebar:t,identifier:o.B,isActive:e===o.B,label:__("Options","jetpack-search-pkg")})))}function c({enableSidebar:e,identifier:t,label:r,isActive:o}){const i=o?(0,n.sprintf)(
// translators: %s: sidebar label e.g: "Options".
__("%s (selected)","jetpack-search-pkg"),r):r;return React.createElement(s.Button,{onClick:()=>e(t),className:(0,a.A)("jp-search-configure-sidebar__panel-tab",{"is-active":o}),"aria-label":i,"data-label":r},r)}},5666:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(6427),n=r(7723),a=r(3022),o=r(894);const __=n.__;function i({disabled:e,value:t,onChange:r}){return React.createElement("div",{className:"jp-search-configure-theme-buttons components-base-control"},React.createElement(s.Button,{className:(0,a.A)({"jp-search-configure-theme-button--selected":"light"===t}),disabled:e,onClick:()=>r("light"),variant:"link"},React.createElement(o.A,{theme:"light"}),React.createElement("span",{"aria-label":__("Light Theme","jetpack-search-pkg")},__("Light","jetpack-search-pkg"))),React.createElement(s.Button,{className:(0,a.A)({"jp-search-configure-theme-button--selected":"dark"===t}),disabled:e,onClick:()=>r("dark"),variant:"link"},React.createElement(o.A,{theme:"dark"}),React.createElement("span",{"aria-label":__("Dark Theme","jetpack-search-pkg")},__("Dark","jetpack-search-pkg"))))}},894:(e,t,r)=>{"use strict";function s({theme:e}){const t="dark"===e?"#000":"#fff",r="dark"===e?"#4F5861":"#DDE5EE";return React.createElement("svg",{width:"104",height:"80",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":!0},React.createElement("rect",{x:"7.5",y:"7.5",width:"89",height:"65",rx:"3.5",fill:t,stroke:r}),React.createElement("path",{d:"M16 20a4 4 0 014-4h49a4 4 0 010 8H20a4 4 0 01-4-4zM42 55.5a1.5 1.5 0 011.5-1.5h32a1.5 1.5 0 010 3h-32a1.5 1.5 0 01-1.5-1.5zM42 60.5a1.5 1.5 0 011.5-1.5h11a1.5 1.5 0 010 3h-11a1.5 1.5 0 01-1.5-1.5zM16 47a4 4 0 014-4h12a4 4 0 014 4v12a4 4 0 01-4 4H20a4 4 0 01-4-4V47zM42 48a3 3 0 013-3h40a3 3 0 110 6H45a3 3 0 01-3-3zM8 32h89v1H8z",fill:r}))}r.d(t,{A:()=>s})},617:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(7143),n=r(1609);function a(){const{saveEntityRecord:e,undo:t,redo:r}=(0,s.useDispatch)("core"),a=(0,s.useSelect)((e=>e("core").getEntityRecordEdits("root","site"))),o=a&&Object.keys(a).length>0,i=(0,s.useSelect)((e=>e("core").isSavingEntityRecord("root","site"))),c=(0,s.useSelect)((e=>e("core").hasUndo())),l=(0,s.useSelect)((e=>e("core").hasRedo())),u=(0,n.useCallback)((()=>{o&&e("root","site",a)}),[a,o,e]);return{editedEntities:a,hasRedo:c,hasUndo:l,hasUnsavedEdits:o,isSaving:i,redo:r,saveRecords:u,undo:t}}},4678:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(7143);function n(){return{isLoading:!(0,s.useSelect)((e=>e("core").getSite()))}}},9611:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(3582),n=r(7479),a=r(1609);const o=window.JetpackInstantSearchOptions.postTypes;function i(){const[e,t]=(0,s.useEntityProp)("root","site","jetpack_search_color_theme"),[r,i]=(0,s.useEntityProp)("root","site","jetpack_search_result_format"),[c,l]=(0,s.useEntityProp)("root","site","jetpack_search_default_sort"),[u,p]=(0,s.useEntityProp)("root","site","jetpack_search_overlay_trigger"),[h,d]=(0,s.useEntityProp)("root","site","jetpack_search_highlight_color"),[f,m]=(0,s.useEntityProp)("root","site","jetpack_search_enable_sort"),[g,y]=(0,s.useEntityProp)("root","site","jetpack_search_inf_scroll"),[v,_]=(0,s.useEntityProp)("root","site","jetpack_search_filtering_opens_overlay"),[b,w]=(0,s.useEntityProp)("root","site","jetpack_search_show_powered_by"),[E,k]=(0,s.useEntityProp)("root","site","jetpack_search_show_post_date"),[S,j]=(0,s.useEntityProp)("root","site","jetpack_search_excluded_post_types");return{color:h,excludedPostTypes:(0,a.useMemo)((()=>S?.split(",").filter((e=>e in o))),[S]),infiniteScroll:g,filteringOpensOverlay:v,resultFormat:r,setColor:d,setExcludedPostTypes:e=>j(e.filter((e=>e in o)).join(",")),setInfiniteScroll:y,setFilteringOpensOverlay:_,setResultFormat:e=>{const t=r;i(e),"product"===t&&n.Wq.has(c)&&l(n.x6)},setShowLogo:w,setSort:l,setSortEnabled:m,setTheme:t,setTrigger:p,showLogo:b,sort:c,sortEnabled:f,theme:e,trigger:u,postDate:E,setPostDate:k}}},7547:(e,t,r)=>{"use strict";r.d(t,{be:()=>n,n_:()=>a,wk:()=>o,yM:()=>i});var s=r(8924);const n="jetpack_search_customberg",a=(...e)=>(0,s.ze)(...e,!0),o=(...e)=>(0,s.wk)(...e,!0),i=(...e)=>(0,s.yM)(...e,!0)},8394:(e,t,r)=>{"use strict";r.d(t,{B:()=>n,e:()=>s});const s="jetpack-customize-search/info",n="jetpack-customize-search/options"},6084:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(1609),n=r(3398);class a extends s.Component{componentDidMount(){(0,n.UC)(this.handleOverlayOptionsUpdate),(0,n.Tx)(this.props.toggleResults)}handleOverlayOptionsUpdate=e=>{this.props.updateOverlayOptions(e,(()=>this.props.showResults()))};render(){return null}}},8165:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(3257),n=r.n(s),a=r(1609),o=r(7152);class i extends a.Component{constructor(){super(...arguments),this.state={isComposing:!1,bodyScrollTop:0,prefersReducedMotion:(0,o.y)(),previousStyle:null,previousBodyStyleAttribute:""},this.props.initializeQueryValues()}componentDidMount(){this.disableUnnecessaryFormAndInputAttributes(),this.addEventListeners()}componentWillUnmount(){this.removeEventListeners(),this.restoreBodyScroll()}componentDidUpdate(e){this.props.isVisible!==e.isVisible&&this.fixBodyScroll(),this.fixBodyOpacity()}disableUnnecessaryFormAndInputAttributes(){document.querySelectorAll(this.props.themeOptions.searchInputSelector).forEach((e=>{e.removeAttribute("required"),e.removeAttribute("autocomplete"),e.form.removeAttribute("autocomplete")}))}addEventListeners(){window.addEventListener("popstate",this.handleHistoryNavigation),document.querySelectorAll(this.props.themeOptions.searchInputSelector).forEach((e=>{e.form.addEventListener("submit",this.handleSubmit),e.addEventListener("keyup",this.handleKeyup),e.addEventListener("input",this.handleInput),e.addEventListener("compositionstart",this.handleCompositionStart),e.addEventListener("compositionend",this.handleCompositionEnd)})),document.querySelectorAll(this.props.themeOptions.overlayTriggerSelector).forEach((e=>{e.addEventListener("click",this.handleOverlayTriggerClick,!0)})),document.querySelectorAll(this.props.themeOptions.filterInputSelector).forEach((e=>{e.addEventListener("click",this.handleFilterInputClick)}))}removeEventListeners(){window.removeEventListener("popstate",this.handleHistoryNavigation),document.querySelectorAll(this.props.themeOptions.searchInputSelector).forEach((e=>{e.form.removeEventListener("submit",this.handleSubmit),e.removeEventListener("keyup",this.handleKeyup),e.removeEventListener("input",this.handleInput),e.removeEventListener("compositionstart",this.handleCompositionStart),e.removeEventListener("compositionend",this.handleCompositionEnd)})),document.querySelectorAll(this.props.themeOptions.overlayTriggerSelector).forEach((e=>{e.removeEventListener("click",this.handleOverlayTriggerClick,!0)})),document.querySelectorAll(this.props.themeOptions.filterInputSelector).forEach((e=>{e.removeEventListener("click",this.handleFilterInputClick)}))}handleCompositionStart=()=>this.setState({isComposing:!0});handleCompositionEnd=()=>this.setState({isComposing:!1});handleFilterInputClick=e=>{e.preventDefault(),e.currentTarget.dataset.filterType&&("taxonomy"===e.currentTarget.dataset.filterType?this.props.setFilter(e.currentTarget.dataset.taxonomy,e.currentTarget.dataset.val):this.props.setFilter(e.currentTarget.dataset.filterType,e.currentTarget.dataset.val)),this.props.setSearchQuery(""),this.props.showResults()};handleHistoryNavigation=()=>{this.props.initializeQueryValues({isHistoryNavigation:!0})};handleInput=n()((e=>{e.inputType?.includes("format")||""===e.target.value||this.state.isComposing||"submit"===this.props.overlayOptions.overlayTrigger||this.state.prefersReducedMotion||(this.props.setSearchQuery(e.target.value),["immediate","results"].includes(this.props.overlayOptions.overlayTrigger)&&this.props.showResults())}),200);handleKeyup=e=>{"Enter"===e.key&&(this.props.setSearchQuery(e.target.value),this.props.showResults())};handleOverlayTriggerClick=e=>{e.stopImmediatePropagation(),this.props.setSearchQuery(""),this.props.showResults()};handleSubmit=e=>{if(e.preventDefault(),this.handleInput.flush(),!this.props.isVisible){const t=e.target.querySelector(this.props.themeOptions.searchInputSelector)?.value;"string"==typeof t&&this.props.setSearchQuery(t),this.props.showResults()}};fixBodyScroll=()=>{this.props.isVisible?(this.preventBodyScroll(),window?.scrollTo(0,0)):this.props.isVisible||this.restoreBodyScroll()};fixBodyOpacity=()=>{"1"!==document.body.style.opacity&&(document.body.style.opacity="1")};preventBodyScroll(){this.setState({bodyScrollTop:parseInt(window.scrollY)||0,previousStyle:{top:document.body.style.top,left:document.body.style.left,right:document.body.style.right,scrollBehavior:document.documentElement.style.scrollBehavior},previousBodyStyleAttribute:document.body.getAttribute("style")},(()=>{const e=document.documentElement?.scrollHeight-document.body?.scrollHeight||0;document.body.setAttribute("style","position: fixed !important"),document.body.style.top=`-${this.state.bodyScrollTop-e}px`,document.body.style.left=0,document.body.style.right=0}))}restoreBodyScroll(){this.state.previousBodyStyleAttribute?document.body.setAttribute("style",this.state.previousBodyStyleAttribute):document.body.removeAttribute("style"),document.body.style.top=this.state.previousStyle?.top??"",document.body.style.left=this.state.previousStyle?.left??"",document.body.style.right=this.state.previousStyle?.right??"",document.documentElement.style.scrollBehavior="revert",this.state.bodyScrollTop>0&&window.scrollTo(0,this.state.bodyScrollTop),document.documentElement.style.scrollBehavior=this.state.previousStyle?.scrollBehavior??"",this.setState({bodyScrollTop:0,previousStyle:null,previousBodyStyleAttribute:""})}render(){return null}}},1539:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});r(9060);var s=r(7723),n=r(1609),a=r.n(n);const __=s.__;class o extends n.Component{static defaultProps={"aria-hidden":"false",focusable:"true"};needsOffset(e,t){return["gridicons-calendar","gridicons-cart","gridicons-folder","gridicons-info","gridicons-posts","gridicons-star-outline","gridicons-star"].indexOf(e)>=0&&t%18==0}getSVGTitle(e){if("title"in this.props)return this.props.title?a().createElement("title",null,this.props.title):null;switch(e){default:return null;case"gridicons-audio":return a().createElement("title",null,__("Has audio.","jetpack-search-pkg"));case"gridicons-calendar":return a().createElement("title",null,__("Is an event.","jetpack-search-pkg"));case"gridicons-cart":return a().createElement("title",null,__("Is a product.","jetpack-search-pkg"));case"chevron-down":return a().createElement("title",null,__("Show filters","jetpack-search-pkg"));case"gridicons-comment":return a().createElement("title",null,__("Matching comment.","jetpack-search-pkg"));case"gridicons-cross":return a().createElement("title",null,__("Close search results","jetpack-search-pkg"));case"gridicons-filter":return a().createElement("title",null,__("Toggle search filters.","jetpack-search-pkg"));case"gridicons-folder":return a().createElement("title",null,__("Category","jetpack-search-pkg"));case"gridicons-image-multiple":return a().createElement("title",null,__("Has multiple images.","jetpack-search-pkg"));case"gridicons-image":return a().createElement("title",null,__("Has an image.","jetpack-search-pkg"));case"gridicons-page":return a().createElement("title",null,__("Page","jetpack-search-pkg"));case"gridicons-post":return a().createElement("title",null,__("Post","jetpack-search-pkg"));case"gridicons-jetpack-search":case"gridicons-search":return a().createElement("title",null,__("Magnifying Glass","jetpack-search-pkg"));case"gridicons-tag":return a().createElement("title",null,__("Tag","jetpack-search-pkg"));case"gridicons-video":return a().createElement("title",null,__("Has a video.","jetpack-search-pkg"))}}renderIcon(e){switch(e){default:return null;case"gridicons-audio":return a().createElement("g",null,a().createElement("path",{d:"M8 4v10.184C7.686 14.072 7.353 14 7 14c-1.657 0-3 1.343-3 3s1.343 3 3 3 3-1.343 3-3V7h7v4.184c-.314-.112-.647-.184-1-.184-1.657 0-3 1.343-3 3s1.343 3 3 3 3-1.343 3-3V4H8z"}));case"gridicons-block":return a().createElement("g",null,a().createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zM4 12c0-4.418 3.582-8 8-8 1.848 0 3.545.633 4.9 1.686L5.686 16.9C4.633 15.545 4 13.848 4 12zm8 8c-1.848 0-3.546-.633-4.9-1.686L18.314 7.1C19.367 8.455 20 10.152 20 12c0 4.418-3.582 8-8 8z"}));case"gridicons-calendar":return a().createElement("g",null,a().createElement("path",{d:"M19 4h-1V2h-2v2H8V2H6v2H5c-1.105 0-2 .896-2 2v13c0 1.104.895 2 2 2h14c1.104 0 2-.896 2-2V6c0-1.104-.896-2-2-2zm0 15H5V8h14v11z"}));case"gridicons-cart":return a().createElement("g",null,a().createElement("path",{d:"M9 20c0 1.1-.9 2-2 2s-1.99-.9-1.99-2S5.9 18 7 18s2 .9 2 2zm8-2c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2zm.396-5c.937 0 1.75-.65 1.952-1.566L21 5H7V4c0-1.105-.895-2-2-2H3v2h2v11c0 1.105.895 2 2 2h12c0-1.105-.895-2-2-2H7v-2h10.396z"}));case"gridicons-checkmark":return a().createElement("g",null,a().createElement("path",{d:"M11 17.768l-4.884-4.884 1.768-1.768L11 14.232l8.658-8.658C17.823 3.39 15.075 2 12 2 6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10c0-1.528-.353-2.97-.966-4.266L11 17.768z"}));case"gridicons-chevron-down":return a().createElement("g",null,a().createElement("path",{d:"M20 9l-8 8-8-8 1.414-1.414L12 14.172l6.586-6.586"}));case"gridicons-comment":return a().createElement("g",null,a().createElement("path",{d:"M3 6v9c0 1.105.895 2 2 2h9v5l5.325-3.804c1.05-.75 1.675-1.963 1.675-3.254V6c0-1.105-.895-2-2-2H5c-1.105 0-2 .895-2 2z"}));case"gridicons-cross":return a().createElement("g",null,a().createElement("path",{d:"M18.36 19.78L12 13.41l-6.36 6.37-1.42-1.42L10.59 12 4.22 5.64l1.42-1.42L12 10.59l6.36-6.36 1.41 1.41L13.41 12l6.36 6.36z"}));case"gridicons-filter":return a().createElement("g",null,a().createElement("path",{d:"M10 19h4v-2h-4v2zm-4-6h12v-2H6v2zM3 5v2h18V5H3z"}));case"gridicons-folder":return a().createElement("g",null,a().createElement("path",{d:"M18 19H6c-1.1 0-2-.9-2-2V7c0-1.1.9-2 2-2h3c1.1 0 2 .9 2 2h7c1.1 0 2 .9 2 2v8c0 1.1-.9 2-2 2z"}));case"gridicons-image":return a().createElement("g",null,a().createElement("path",{d:"M13 9.5c0-.828.672-1.5 1.5-1.5s1.5.672 1.5 1.5-.672 1.5-1.5 1.5-1.5-.672-1.5-1.5zM22 6v12c0 1.105-.895 2-2 2H4c-1.105 0-2-.895-2-2V6c0-1.105.895-2 2-2h16c1.105 0 2 .895 2 2zm-2 0H4v7.444L8 9l5.895 6.55 1.587-1.85c.798-.932 2.24-.932 3.037 0L20 15.426V6z"}));case"gridicons-image-multiple":return a().createElement("g",null,a().createElement("path",{d:"M15 7.5c0-.828.672-1.5 1.5-1.5s1.5.672 1.5 1.5S17.328 9 16.5 9 15 8.328 15 7.5zM4 20h14c0 1.105-.895 2-2 2H4c-1.1 0-2-.9-2-2V8c0-1.105.895-2 2-2v14zM22 4v12c0 1.105-.895 2-2 2H8c-1.105 0-2-.895-2-2V4c0-1.105.895-2 2-2h12c1.105 0 2 .895 2 2zM8 4v6.333L11 7l4.855 5.395.656-.73c.796-.886 2.183-.886 2.977 0l.513.57V4H8z"}));case"gridicons-info":return a().createElement("g",null,a().createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"}));case"gridicons-jetpack-search":return a().createElement("g",null,a().createElement("path",{d:"M0 9.257C0 4.15 4.151 0 9.257 0c5.105 0 9.256 4.151 9.256 9.257a9.218 9.218 0 01-2.251 6.045l.034.033h1.053L24 22.01l-1.986 1.989-6.664-6.662v-1.055l-.033-.033a9.218 9.218 0 01-6.06 2.264C4.15 18.513 0 14.362 0 9.257zm4.169 1.537h4.61V1.82l-4.61 8.973zm5.547-3.092v8.974l4.61-8.974h-4.61z"}));case"gridicons-pages":return a().createElement("g",null,a().createElement("path",{d:"M16 8H8V6h8v2zm0 2H8v2h8v-2zm4-6v12l-6 6H6c-1.105 0-2-.895-2-2V4c0-1.105.895-2 2-2h12c1.105 0 2 .895 2 2zm-2 10V4H6v16h6v-4c0-1.105.895-2 2-2h4z"}));case"gridicons-posts":return a().createElement("g",null,a().createElement("path",{d:"M16 19H3v-2h13v2zm5-10H3v2h18V9zM3 5v2h11V5H3zm14 0v2h4V5h-4zm-6 8v2h10v-2H11zm-8 0v2h5v-2H3z"}));case"gridicons-search":return a().createElement("g",null,a().createElement("path",{d:"M21 19l-5.154-5.154C16.574 12.742 17 11.42 17 10c0-3.866-3.134-7-7-7s-7 3.134-7 7 3.134 7 7 7c1.42 0 2.742-.426 3.846-1.154L19 21l2-2zM5 10c0-2.757 2.243-5 5-5s5 2.243 5 5-2.243 5-5 5-5-2.243-5-5z"}));case"gridicons-star-outline":return a().createElement("g",null,a().createElement("path",{d:"M12 6.308l1.176 3.167.347.936.997.042 3.374.14-2.647 2.09-.784.62.27.963.91 3.25-2.813-1.872-.83-.553-.83.552-2.814 1.87.91-3.248.27-.962-.783-.62-2.648-2.092 3.374-.14.996-.04.347-.936L12 6.308M12 2L9.418 8.953 2 9.257l5.822 4.602L5.82 21 12 16.89 18.18 21l-2.002-7.14L22 9.256l-7.418-.305L12 2z"}));case"gridicons-star":return a().createElement("g",null,a().createElement("path",{d:"M12 2l2.582 6.953L22 9.257l-5.822 4.602L18.18 21 12 16.89 5.82 21l2.002-7.14L2 9.256l7.418-.304"}));case"gridicons-tag":return a().createElement("g",null,a().createElement("path",{d:"M20 2.007h-7.087c-.53 0-1.04.21-1.414.586L2.592 11.5c-.78.78-.78 2.046 0 2.827l7.086 7.086c.78.78 2.046.78 2.827 0l8.906-8.906c.376-.374.587-.883.587-1.413V4.007c0-1.105-.895-2-2-2zM17.007 9c-1.105 0-2-.895-2-2s.895-2 2-2 2 .895 2 2-.895 2-2 2z"}));case"gridicons-video":return a().createElement("g",null,a().createElement("path",{d:"M20 4v2h-2V4H6v2H4V4c-1.105 0-2 .895-2 2v12c0 1.105.895 2 2 2v-2h2v2h12v-2h2v2c1.105 0 2-.895 2-2V6c0-1.105-.895-2-2-2zM6 16H4v-3h2v3zm0-5H4V8h2v3zm4 4V9l4.5 3-4.5 3zm10 1h-2v-3h2v3zm0-5h-2V8h2v3z"}))}}render(){const{size:e=24,className:t=""}=this.props,r=this.props.height||e,s=this.props.width||e,n=this.props.style||{height:r,width:s},o="gridicons-"+this.props.icon;let i=["gridicon",o,t];return this.needsOffset(o,e)&&i.push("needs-offset"),i=i.join(" "),a().createElement("svg",{"aria-label":this.props.description,className:i,focusable:this.props.focusable,height:r,onClick:this.props.onClick,style:n,viewBox:"0 0 24 24",width:s,xmlns:"http://www.w3.org/2000/svg","aria-hidden":this.props["aria-hidden"]},this.getSVGTitle(o),this.renderIcon(o))}}const i=o},4120:(e,t,r)=>{"use strict";r.d(t,{A:()=>c,J:()=>i});var s=r(7723),n=r(1609),a=r.n(n);const __=s.__,o="#fff",i=a().createElement("svg",{className:"jetpack-instant-search__jetpack-colophon-logo",height:12,width:12,viewBox:"0 0 32 32"},a().createElement("path",{className:"jetpack-logo__icon-circle",fill:"#069e08",d:"M16,0C7.2,0,0,7.2,0,16s7.2,16,16,16s16-7.2,16-16S24.8,0,16,0z"}),a().createElement("polygon",{className:"jetpack-logo__icon-triangle",fill:o,points:"15,19 7,19 15,3 "}),a().createElement("polygon",{className:"jetpack-logo__icon-triangle",fill:o,points:"17,29 17,13 25,13 "})),c=e=>{const t="string"==typeof e.locale?e.locale.split("-",1)[0]:null,r=t&&"en"!==t?"https://"+t+".jetpack.com/upgrade/search?utm_source=poweredby":"https://jetpack.com/upgrade/search/?utm_source=poweredby";return a().createElement("div",{className:"jetpack-instant-search__jetpack-colophon"},a().createElement("a",{href:r,rel:"external noopener noreferrer nofollow",target:"_blank",className:"jetpack-instant-search__jetpack-colophon-link"},i,a().createElement("span",{className:"jetpack-instant-search__jetpack-colophon-text"},__("Search powered by Jetpack","jetpack-search-pkg"))))}},8961:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(1609),n=r.n(s),a=r(1539);const o=({type:e,children:t})=>"warning"!==e?null:n().createElement("div",{className:"jetpack-instant-search__notice jetpack-instant-search__notice--warning"},n().createElement(a.A,{icon:"info",size:20}),n().createElement("div",null,t))},6973:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(7723),n=r(1609),a=r.n(n),o=r(7479);const __=s.__,i=e=>{const{children:t,closeOverlay:r,colorTheme:s,hasOverlayWidgets:i,isVisible:c}=e;return(0,n.useEffect)((()=>{const e=e=>{"Escape"===e.key&&(e.preventDefault(),r())},t=e=>{if("Tab"===e.key){const t=document.getElementsByClassName(o.w4)[0].contains(e.target),r=document.getElementsByClassName(o.Md)[0],s=document.getElementById(o.m2);!0===e.shiftKey&&(e.target!==r&&!1!==t||(e.preventDefault(),s.focus())),!1===e.shiftKey&&(e.target!==s&&!1!==t||(e.preventDefault(),r.focus()))}},s=e=>{const t=document.getElementsByClassName("jetpack-instant-search__search-results-wrapper")[0];e.target?.isConnected&&t&&!t.contains(e.target)&&r()},n=()=>{window.removeEventListener("click",s),window.removeEventListener("keydown",e),window.removeEventListener("keydown",t)};return c?(window.addEventListener("click",s),window.addEventListener("keydown",e),window.addEventListener("keydown",t)):n(),()=>{n()}}),[r,c]),a().createElement("div",{"aria-hidden":!c,"aria-labelledby":"jetpack-instant-search__overlay-title",className:["jetpack-instant-search",o.w4,`jetpack-instant-search__overlay--${s}`,i?"":"jetpack-instant-search__overlay--no-sidebar",c?"":"is-hidden"].join(" "),role:"dialog"},a().createElement("h1",{id:"jetpack-instant-search__overlay-title",className:"screen-reader-text"},__("Search results","jetpack-search-pkg")),t)}},413:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(1609),n=r.n(s);const a=({className:e,onClick:t,url:r})=>{const s=function(e){const t=e.split("/").filter((e=>e.length>0));return t.shift(),t}(r);return s.length<1?null:n().createElement("div",{className:`jetpack-instant-search__path-breadcrumb ${e||""}`},n().createElement("a",{className:"jetpack-instant-search__path-breadcrumb-link",href:`//${r}`,onClick:t,tabIndex:"-1","aria-hidden":"true"},s.map(((e,t,r)=>n().createElement("span",{className:"jetpack-instant-search__path-breadcrumb-piece",key:e},decodeURIComponent(e),t!==r.length-1?" › ":"")))))}},2355:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var s=r(6072),n=r.n(s),a=r(1609),o=r.n(a),i=r(4809);const c=e=>{const{alt:t,isPhotonEnabled:r,maxHeight:s=600,maxWidth:a=600,src:c,lazyLoad:l=!0,...u}=e,p=(0,i.g)(c,a,s,r);return o().createElement("img",n()({alt:t,src:p,loading:""+(l?"lazy":"eager")},u))}},7846:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var s=r(7723),n=r(1609),a=r.n(n),o=r(113),i=r(1539);const __=s.__,c=["youtube","ooyala","anvplayer","wpvideo","bc_video","video","brightcove","tp_video","jwplayer","tempo-video","vimeo"],l=["gallery","ione_media_gallery"],u=["audio","soundcloud"],p={product:"cart",video:"video",gallery:"image-multiple",event:"calendar",events:"calendar"},h=({postType:e,shortcodeTypes:t,iconSize:r=18})=>{if(Object.keys(p).includes(e))return a().createElement(i.A,{icon:p[e],size:r});const s=(0,o.A)(t,c),n=(0,o.A)(t,u),h=(0,o.A)(t,l);return s?a().createElement(i.A,{icon:"video",size:r}):n?a().createElement(i.A,{icon:"audio",size:r}):"page"===e?a().createElement(i.A,{icon:"pages",size:r,description:__("Page","jetpack-search-pkg")}):h?a().createElement(i.A,{icon:"image-multiple",size:r,description:__("Image","jetpack-search-pkg")}):null}},2652:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(1609),n=r.n(s);class a extends s.Component{render(){const{formattedPrice:e,formattedSalePrice:t,formattedRegularPrice:r,price:a,salePrice:o}=this.props;return a?n().createElement("span",{className:"jetpack-instant-search__product-price"},o>0?n().createElement(s.Fragment,null,n().createElement("s",{className:"jetpack-instant-search__product-price-regular",dangerouslySetInnerHTML:{__html:r}}),n().createElement("span",{dangerouslySetInnerHTML:{__html:t}})):n().createElement("span",{dangerouslySetInnerHTML:{__html:e}})):null}}const o=a},6777:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(7723),n=r(1609),a=r.n(n),o=r(1539);const _n=s._n;function i({rating:e=0,count:t=0,permalink:r}){return a().createElement("div",{className:"jetpack-instant-search__product-rating"},a().createElement("span",{"aria-hidden":!0,className:"jetpack-instant-search__product-rating-stars"},Array(5).fill(a().createElement(o.A,{size:16,icon:"star-outline"})).fill(a().createElement(o.A,{size:16,icon:"star"}),0,e))," ",a().createElement("a",{"aria-hidden":!0,className:"jetpack-instant-search__product-rating-count",href:r+"#reviews"},(0,s.sprintf)(/* Translators: the placeholder is the number of product reviews. */
_n("%d review","%d reviews",t,"jetpack-search-pkg"),t)),a().createElement("span",{className:"screen-reader-text"},(0,s.sprintf)(/* Translators: the first placeholder is the average product rating out of 5; the second is the number of product reviews. */
_n("Average rating of %1$d out of 5 from %2$d review.","Average rating of %1$d out of 5 from %2$d reviews.",t,"jetpack-search-pkg"),Number(e).toFixed(2),t)))}},3845:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(7723),n=r(3257),a=r.n(n),o=r(1609),i=r.n(o),c=r(7479);const __=s.__;class l extends o.Component{scrollElement=document.getElementsByClassName(c.tr)[0];componentDidMount(){this.scrollElement.addEventListener("scroll",this.checkScroll)}componentDidUnmount(){this.scrollElement.removeEventListener("scroll",this.checkScroll)}checkScroll=a()((()=>{const e=this.scrollElement.clientHeight+this.scrollElement.scrollTop+c.ZJ;this.props.enableLoadOnScroll&&e>=this.scrollElement.scrollHeight&&this.props.onLoadNextPage()}),100);render(){return i().createElement("button",{className:"jetpack-instant-search__scroll-button",disabled:this.props.isLoading,onClick:this.props.onLoadNextPage},this.props.isLoading?i().createElement("span",null,__("Loading…","jetpack-search-pkg")):i().createElement("span",null,__("Load more","jetpack-search-pkg")))}}const u=l},459:(e,t,r)=>{"use strict";r.d(t,{A:()=>E});var s=r(4224),n=r.n(s),a=r(3257),o=r.n(a),i=r(1609),c=r.n(i),l=r(5795),u=r(4952),p=r(7479),h=r(3089),d=r(6122),f=r(8924),m=r(999),g=r(538),y=r(6084),v=r(8165),_=r(6973),b=r(8568);class w extends i.Component{static defaultProps={overlayOptions:{},widgets:[]};constructor(){super(...arguments),this.state={isVisible:!!this.props.initialIsVisible,overlayOptionsCustomizerOverride:{}},this.getResults=o()(this.getResults,200),this.props.enableAnalytics?this.initializeAnalytics():(0,f.Re)(),this.props.shouldIntegrateWithDom?this.props.initializeQueryValues():this.props.disableQueryStringIntegration()}static getDerivedStateFromProps(e,t){return{overlayOptions:{...e.overlayOptions,...t.overlayOptionsCustomizerOverride}}}componentDidMount(){(this.props.initialShowResults&&this.props.initialIsVisible||this.props.isInCustomizer)&&this.getResults(),this.props.hasActiveQuery&&this.props.overlayOptions.enableFilteringOpensOverlay&&this.showResults()}componentDidUpdate(e,t){e.searchQuery===this.props.searchQuery&&e.sort===this.props.sort&&n()(e.filters)===n()(this.props.filters)&&n()(e.staticFilters)===n()(this.props.staticFilters)||this.onChangeQueryString(this.props.isHistoryNavigation),t.overlayOptions.defaultSort!==this.state.overlayOptions.defaultSort&&this.props.setSort(this.state.overlayOptions.defaultSort),n()(t.overlayOptions.excludedPostTypes)!==n()(this.state.overlayOptions.excludedPostTypes)&&this.getResults()}initializeAnalytics(){(0,f.ze)(),!window[p.O5].preventTrackingCookiesReset&&(0,f.kX)(),(0,f.wk)(this.props.options.siteId)}getResultFormat=()=>{if(this.props.staticFilters&&this.props.staticFilters.group_id&&this.props.staticFilters.group_id!==p.$K)return p.yj;return(0,d.lz)()||this.state.overlayOptions.resultFormat};initializeStaticFilters=()=>{const e=(0,h.PM)();e.length>0&&0===Object.keys(this.props.staticFilters).length&&e.forEach((e=>this.props.setStaticFilter(e.filter_id,e.selected,!0)))};hideResults=e=>{this.props.shouldIntegrateWithDom&&(0,d.fX)(this.props.initialHref,(()=>{this.setState({isVisible:!1}),this.props.clearQueryValues()}),e)};toggleResults=e=>{this.props.shouldIntegrateWithDom&&this.state.isVisible!==e&&(e&&this.initializeStaticFilters(),this.setState({isVisible:e}))};showResults=this.toggleResults.bind(this,!0);onChangeQueryString=e=>{this.getResults(),this.props.hasActiveQuery&&!this.state.isVisible&&this.showResults(),!this.props.hasActiveQuery&&e&&this.hideResults(e),null!==this.props.searchQuery&&document.querySelectorAll(this.props.themeOptions.searchInputSelector).forEach((e=>{e.value=this.props.searchQuery}))};loadNextPage=()=>{this.props.hasNextPage&&this.getResults({pageHandle:this.props.response.page_handle})};getResults=({pageHandle:e}={})=>{this.props.makeSearchRequest({aggregations:e?{}:this.props.aggregations,excludedPostTypes:this.state.overlayOptions.excludedPostTypes,filter:this.props.filters,staticFilters:this.props.staticFilters,pageHandle:e,query:this.props.searchQuery,resultFormat:this.getResultFormat(),siteId:this.props.options.siteId,additionalBlogIds:this.props.options.additionalBlogIds,sort:this.props.sort,postsPerPage:this.props.options.postsPerPage,adminQueryFilter:this.props.options.adminQueryFilter,highlightFields:this.props.options.highlightFields,customResults:this.props.options.customResults,isInCustomizer:this.props.isInCustomizer})};updateOverlayOptions=(e,t)=>{this.setState((t=>({overlayOptionsCustomizerOverride:{...t.overlayOptionsCustomizerOverride,...e}})),t)};render(){const e=this.getResultFormat(),t=this.props.shouldCreatePortal?l.createPortal:e=>e;return c().createElement(i.Fragment,null,this.props.isInCustomizer&&c().createElement(y.A,{showResults:this.showResults,toggleResults:this.toggleResults,updateOverlayOptions:this.updateOverlayOptions}),this.props.shouldIntegrateWithDom&&c().createElement(v.A,{initializeQueryValues:this.props.initializeQueryValues,isVisible:this.state.isVisible,overlayOptions:this.state.overlayOptions,setFilter:this.props.setFilter,setSearchQuery:this.props.setSearchQuery,showResults:this.showResults,themeOptions:this.props.themeOptions}),t(c().createElement(_.A,{closeColor:this.state.overlayOptions.closeColor,closeOverlay:this.hideResults,colorTheme:this.state.overlayOptions.colorTheme,hasOverlayWidgets:this.props.hasOverlayWidgets,isVisible:this.state.isVisible},c().createElement(b.A,{closeOverlay:this.hideResults,enableLoadOnScroll:this.state.overlayOptions.enableInfScroll,enableFilteringOpensOverlay:this.state.overlayOptions.enableFilteringOpensOverlay,enableSort:this.state.overlayOptions.enableSort,filters:this.props.filters,staticFilters:this.props.staticFilters,hasError:this.props.hasError,hasNextPage:this.props.hasNextPage,highlightColor:this.state.overlayOptions.highlightColor,isLoading:this.props.isLoading,isPhotonEnabled:this.props.options.isPhotonEnabled,isPrivateSite:this.props.options.isPrivateSite,isVisible:this.state.isVisible,locale:this.props.options.locale,onChangeSearch:this.props.setSearchQuery,onChangeSort:this.props.setSort,onLoadNextPage:this.loadNextPage,overlayTrigger:this.state.overlayOptions.overlayTrigger,postTypes:this.props.options.postTypes,response:this.props.response,resultFormat:e,searchQuery:this.props.searchQuery,showPoweredBy:this.state.overlayOptions.showPoweredBy,sort:this.props.sort,widgets:this.props.options.widgets,widgetOutsideOverlay:this.props.widgetOutsideOverlay,hasNonSearchWidgets:this.props.options.hasNonSearchWidgets,additionalBlogIds:this.props.options.additionalBlogIds,showPostDate:this.state.overlayOptions.enablePostDate})),document.body))}}const E=(0,u.Ng)(((e,t)=>({filters:(0,g.gA)(e),staticFilters:(0,g.kJ)(e),hasActiveQuery:(0,g.xp)(e),hasError:(0,g.JD)(e),isHistoryNavigation:(0,g.xk)(e),hasNextPage:(0,g.rB)(e),isLoading:(0,g.VP)(e),response:(0,g.mi)(e),searchQuery:(0,g.V8)(e),sort:(0,g.FD)(e,t.overlayOptions.defaultSort),widgetOutsideOverlay:(0,g.S7)(e)})),{clearQueryValues:m.Qt,disableQueryStringIntegration:m.go,initializeQueryValues:m.JF,makeSearchRequest:m.oZ,setStaticFilter:m.$6,setFilter:m.R6,setSearchQuery:m.Ri,setSort:m.d1})(w)},3297:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var s=r(7723),n=r(4436),a=r.n(n),o=r(1609),i=r.n(o),c=r(7479),l=r(1539);const __=s.__;let u=null;const p=e=>{const[t]=(0,o.useState)((()=>a()("jetpack-instant-search__box-input-"))),r=(0,o.useRef)(null);return(0,o.useEffect)((()=>{var t;e.isVisible?(t=r.current,()=>{u=t.ownerDocument.activeElement,t.focus()})():e.shouldRestoreFocus&&u&&u.focus()}),[e.isVisible,e.shouldRestoreFocus]),i().createElement(o.Fragment,null,i().createElement("div",{className:"jetpack-instant-search__box"},i().createElement("label",{className:"jetpack-instant-search__box-label",htmlFor:t},i().createElement("div",{className:"jetpack-instant-search__box-gridicon"},i().createElement(l.A,{icon:"search",size:24})),i().createElement("input",{autoComplete:"off",id:t,className:"search-field "+c.Md,inputMode:"search",onChange:e.isVisible?e.onChange:null,ref:r,placeholder:__("Search…","jetpack-search-pkg"),type:"search",value:e.searchQuery??""}),"string"==typeof e.searchQuery&&e.searchQuery.length>0&&
/* Translators: Button is used to clear the search input query. */
i().createElement("input",{type:"button",value:__("clear","jetpack-search-pkg"),onClick:e.onClear}),i().createElement("button",{className:"screen-reader-text assistive-text",tabIndex:"-1"},__("Search","jetpack-search-pkg")))))}},1242:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(1609),n=r.n(s),a=r(1168);const o=e=>n().createElement("div",{className:"jetpack-instant-search__search-form-controls",role:"form"},e.children,e.enableSort&&n().createElement(a.A,{onChange:e.onChangeSort,resultFormat:e.resultFormat,value:e.sort}))},5450:(e,t,r)=>{"use strict";r.d(t,{A:()=>f,t:()=>h});var s=r(3022),n=r(4436),a=r.n(n),o=r(1609),i=r.n(o),c=r(4952),l=r(1557),u=r.n(l),p=r(7574);const h=e=>e.split(" ").join("T");class d extends o.Component{filtersList=(0,o.createRef)();idPrefix=a()("jetpack-instant-search__search-filter-");getIdentifier(){return"postType"===this.props.type?"post_types":"author"===this.props.type?"authors":"blogId"===this.props.type?"blog_ids":"date"===this.props.type?`${this.props.configuration.interval}_${this.props.configuration.field}`:"taxonomy"===this.props.type?this.props.configuration.taxonomy:"group"===this.props.type?this.props.configuration.filter_id:void 0}isChecked(e){return Boolean(this.props.value&&this.props.value.includes(e))}toggleFilter=()=>{this.props.onChange(this.getIdentifier(),(0,p.e)(this.filtersList.current))};toggleStaticFilter=e=>{this.props.onChange(this.getIdentifier(),e.target.value)};renderDate=({key_as_string:e,doc_count:t})=>{const{locale:r="en-US"}=this.props;return i().createElement("div",null,i().createElement("input",{checked:this.isChecked(e),disabled:!this.isChecked(e)&&0===t,id:`${this.idPrefix}-dates-${this.getIdentifier()}-${e}`,name:e,onChange:this.toggleFilter,type:"checkbox",className:"jetpack-instant-search__search-filter-list-input"}),i().createElement("label",{htmlFor:`${this.idPrefix}-dates-${this.getIdentifier()}-${e}`,className:"jetpack-instant-search__search-filter-list-label"},new Date(h(e)).toLocaleString(r,function(e){switch(e){case"day":return{year:"numeric",month:"long",day:"numeric"};case"month":return{year:"numeric",month:"long"};case"year":return{year:"numeric"}}return{year:"numeric",month:"long"}}(this.props.configuration.interval))," ","(",t,")"))};renderPostType=({key:e,doc_count:t})=>{const r=e in this.props.postTypes?this.props.postTypes[e].singular_name:e;return i().createElement("div",null,i().createElement("input",{checked:this.isChecked(e),disabled:!this.isChecked(e)&&0===t,id:`${this.idPrefix}-post-types-${e}`,name:e,onChange:this.toggleFilter,type:"checkbox",className:"jetpack-instant-search__search-filter-list-input"}),i().createElement("label",{htmlFor:`${this.idPrefix}-post-types-${e}`,className:"jetpack-instant-search__search-filter-list-label"},u()(r)," (",t,")"))};renderAuthor=({key:e,doc_count:t})=>{const[r,s]=e&&e.split(/\/(.+)/);return i().createElement("div",null,i().createElement("input",{checked:this.isChecked(r),disabled:!this.isChecked(r)&&0===t,id:`${this.idPrefix}-authors-${r}`,name:r,onChange:this.toggleFilter,type:"checkbox",className:"jetpack-instant-search__search-filter-list-input"}),i().createElement("label",{htmlFor:`${this.idPrefix}-authors-${r}`,className:"jetpack-instant-search__search-filter-list-label"},u()(s)," (",t,")"))};renderBlogId=({key:e,doc_count:t})=>{const r=e.toString(),s=this.props.blogIdFilteringLabels?.[e]||r;return i().createElement("div",null,i().createElement("input",{checked:this.isChecked(r),disabled:!this.isChecked(r)&&0===t,id:`${this.idPrefix}-blog-ids-${r}`,name:r,onChange:this.toggleFilter,type:"checkbox",className:"jetpack-instant-search__search-filter-list-input"}),i().createElement("label",{htmlFor:`${this.idPrefix}-blog-ids-${r}`,className:"jetpack-instant-search__search-filter-list-label"},u()(s)," (",t,")"))};renderTaxonomy=({key:e,doc_count:t})=>{const[r,s]=e&&e.split(/\/(.+)/);return i().createElement("div",null,i().createElement("input",{checked:this.isChecked(r),disabled:!this.isChecked(r)&&0===t,id:`${this.idPrefix}-taxonomies-${r}`,name:r,onChange:this.toggleFilter,type:"checkbox",className:"jetpack-instant-search__search-filter-list-input"}),i().createElement("label",{htmlFor:`${this.idPrefix}-taxonomies-${r}`,className:"jetpack-instant-search__search-filter-list-label"},u()(s)," (",t,")"))};renderGroup=e=>i().createElement("div",{className:"jetpack-instant-search__search-filter-group-item"},i().createElement("input",{checked:this.isChecked(e.value),id:`${this.idPrefix}-groups-${e.value}`,name:this.props.configuration.filter_id,onChange:this.toggleStaticFilter,value:e.value,type:"radio",className:"jetpack-instant-search__search-filter-list-input"}),i().createElement("label",{htmlFor:`${this.idPrefix}-groups-${e.value}`,className:"jetpack-instant-search__search-filter-list-label"},e.name));renderDates(){return[...this.props.aggregation.buckets.filter((e=>!!e)).map(this.renderDate)].reverse().slice(0,this.props.configuration.count)}renderPostTypes(){return this.props.aggregation.buckets.map(this.renderPostType)}renderAuthors(){return this.props.aggregation.buckets.map(this.renderAuthor)}renderBlogIds(){return this.props.aggregation.buckets.map(this.renderBlogId)}renderTaxonomies(){return this.props.aggregation.buckets.map(this.renderTaxonomy)}renderGroups(){return this.props.configuration.values.map(this.renderGroup)}render(){return i().createElement("div",{id:`${this.idPrefix}-${this.props.type}`},i().createElement("h3",{className:"jetpack-instant-search__search-filter-sub-heading"},this.props.configuration.name),i().createElement("div",{ref:this.filtersList},i().createElement("div",{className:(0,s.A)("jetpack-instant-search__search-filter-list","jetpack-instant-search__search-static-filter-list",`jetpack-instant-search__search-static-filter-variation-${this.props.configuration.variation}`)},"group"===this.props.type&&this.renderGroups()),this.props.aggregation&&"buckets"in this.props.aggregation&&i().createElement("div",{className:"jetpack-instant-search__search-filter-list"},"date"===this.props.type&&this.renderDates(),"postType"===this.props.type&&this.renderPostTypes(),"author"===this.props.type&&this.renderAuthors(),"blogId"===this.props.type&&this.renderBlogIds(),"taxonomy"===this.props.type&&this.renderTaxonomies())))}}const f=(0,c.Ng)((e=>({blogIdFilteringLabels:e.serverOptions.blogIdFilteringLabels})))(d)},1503:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var s=r(7723),n=r(1609),a=r.n(n),o=r(4952),i=r(3089),c=r(8924),l=r(999),u=r(5450);const __=s.__;class p extends n.Component{static defaultProps={showClearFiltersButton:!0,showTitle:!0};onChangeFilter=(e,t)=>{this.props.setFilter(e,t),this.props.onChange&&this.props.onChange()};onChangeStaticFilter=(e,t)=>{(0,c.oD)({filterName:e,filterValue:t}),this.props.setStaticFilter(e,t),this.props.onChange&&this.props.onChange()};onClearFilters=e=>{e.preventDefault(),this.props.clearFilters(),this.props.onChange&&this.props.onChange()};hasActiveFilters(){return Object.keys(this.props.filters).length>0}renderFilterComponent=({configuration:e,results:t})=>t&&a().createElement(u.A,{aggregation:t,configuration:e,locale:this.props.locale,onChange:this.onChangeFilter,postTypes:this.props.postTypes,type:(0,i.sm)(e),value:this.props.filters[(0,i.xf)(e)]});renderStaticFilterComponent=e=>Object.hasOwn(e,"visible")&&!e.visible?null:a().createElement(u.A,{aggregation:[],configuration:e,locale:this.props.locale,onChange:this.onChangeStaticFilter,postTypes:this.props.postTypes,type:(0,i.sm)(e),value:this.props.staticFilters[(0,i.xf)(e)]});render(){if(!this.props.widget)return null;const e=(0,i.PM)("sidebar"),t=this.props.results?.aggregations;return a().createElement("div",{className:"jetpack-instant-search__search-filters"},this.props.showTitle&&a().createElement("h2",{className:"jetpack-instant-search__search-filters-title"},__("Filter options","jetpack-search-pkg")),this.props.showClearFiltersButton&&this.hasActiveFilters()&&a().createElement("button",{className:"jetpack-instant-search__clear-filters-link",onClick:this.onClearFilters},__("Clear filters","jetpack-search-pkg")),this.props.widget?.filters&&this.props.widget.filters.length>0&&e.map(this.renderStaticFilterComponent),this.props.widget?.filters?.map((e=>t?{configuration:e,results:t[e.filter_id]}:null)).filter((e=>!!e)).filter((({results:e})=>!!e&&Array.isArray(e.buckets)&&e.buckets.length>0)).map(this.renderFilterComponent))}}const h=(0,o.Ng)(null,{clearFilters:l.hi,setFilter:l.R6,setStaticFilter:l.$6})(p)},5846:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var s=r(1609),n=r.n(s),a=r(3297);const o=e=>e.preventDefault();class i extends s.Component{onClear=()=>this.props.onChangeSearch("");onChangeSearch=e=>this.props.onChangeSearch(e.currentTarget.value);render(){return n().createElement("form",{autoComplete:"off",onSubmit:o,role:"search",className:this.props.className},n().createElement("div",{className:"jetpack-instant-search__search-form"},n().createElement(a.A,{isVisible:this.props.isVisible,onChange:this.onChangeSearch,onClear:this.onClear,shouldRestoreFocus:!0,searchQuery:this.props.searchQuery})))}}const c=i},5260:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(1609),n=r.n(s),a=r(1539);const o=({comments:e,iconSize:t=18})=>e?n().createElement("div",{className:"jetpack-instant-search__search-result-comments"},n().createElement(a.A,{icon:"comment",size:t}),n().createElement("span",{className:"jetpack-instant-search__search-result-comments-text",dangerouslySetInnerHTML:{__html:e.join(" ... ")}})):null},829:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(3832),n=r(1609),a=r.n(n),o=r(413),i=r(2355),c=r(5450),l=r(5260);function u(e){const{isMultiSite:t,locale:r="en-US",showPostDate:n}=e,{result_type:u,fields:p,highlight:h}=e.result;if("post"!==u)return null;const d=Array.isArray(p["image.url.raw"])?p["image.url.raw"][0]:p["image.url.raw"];return Array.isArray(p.author)&&(p.author.length>3?p.author=p.author.slice(0,3).join(", ")+"...":p.author=p.author.join(", ")),a().createElement("li",{className:["jetpack-instant-search__search-result","jetpack-instant-search__search-result-expanded",`jetpack-instant-search__search-result-expanded--${p.post_type}`,d?"":"jetpack-instant-search__search-result-expanded--no-image",t?"is-multisite":"",(()=>{let e=p["category.name.default"];return e?(Array.isArray(e)||(e=[e]),e):[]})().map((e=>"jetpack-instant-search__search-result-category--"+(0,s.cleanForSlug)(e))).join(" ")].join(" ")},a().createElement("div",{className:"jetpack-instant-search__search-result-expanded__content-container"},a().createElement("div",{className:"jetpack-instant-search__search-result-expanded__copy-container"},a().createElement("h3",{className:"jetpack-instant-search__search-result-title jetpack-instant-search__search-result-expanded__title"},a().createElement("a",{className:"jetpack-instant-search__search-result-title-link jetpack-instant-search__search-result-expanded__title-link",href:`//${p["permalink.url.raw"]}`,onClick:e.onClick},a().createElement("span",{dangerouslySetInnerHTML:{__html:h.title}}),"yes"===p["forum.topic_resolved"]&&a().createElement("span",{className:"jetpack-instant-search__search-result-title-checkmark"}))),!t&&a().createElement(o.A,{className:"jetpack-instant-search__search-result-expanded__path",onClick:e.onClick,url:`//${p["permalink.url.raw"]}`}),a().createElement("div",{className:"jetpack-instant-search__search-result-expanded__content",dangerouslySetInnerHTML:{__html:h&&"object"==typeof h?Object.entries(h).filter((([e,t])=>"comments"!==e&&"title"!==e&&Array.isArray(t))).map((([,e])=>e.join(" ... "))).join(" ... "):""}}),h.comments&&a().createElement(l.A,{comments:h.comments})),a().createElement("a",{className:"jetpack-instant-search__search-result-expanded__image-link",href:`//${p["permalink.url.raw"]}`,onClick:e.onClick,tabIndex:"-1","aria-hidden":"true"},a().createElement("div",{className:"jetpack-instant-search__search-result-expanded__image-container"},d?a().createElement(i.A,{alt:p["image.alt_text"],className:"jetpack-instant-search__search-result-expanded__image",isPhotonEnabled:e.isPhotonEnabled,src:`//${d}`}):null))),(t||n)&&a().createElement("ul",{className:"jetpack-instant-search__search-result-expanded__footer"},t&&a().createElement(a().Fragment,null,a().createElement("li",null,a().createElement(i.A,{alt:p.blog_name,className:"jetpack-instant-search__search-result-expanded__footer-blog-image",isPhotonEnabled:!1,height:24,width:24,src:p.blog_icon_url,lazyLoad:!1}),a().createElement("span",{className:"jetpack-instant-search__search-result-expanded__footer-blog"},p.blog_name)),a().createElement("li",null,a().createElement("span",{className:"jetpack-instant-search__search-result-expanded__footer-author"},p.author))),n&&a().createElement("li",null,a().createElement("span",{className:"jetpack-instant-search__search-result-expanded__footer-date"},new Date((0,c.t)(p.date)).toLocaleDateString(r,{year:"numeric",month:"short",day:"numeric"})))))}},6485:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var s=r(3832),n=r(1609),a=r.n(n),o=r(1539),i=r(413),c=r(7846),l=r(5260);class u extends n.Component{getIconSize(){return 18}getTags(){let e=this.props.result.fields["tag.name.default"];return e?(Array.isArray(e)||(e=[e]),e.slice(0,5)):[]}getCategories(e=!1){let t=this.props.result.fields["category.name.default"];return t?(Array.isArray(t)||(t=[t]),e?t:t.slice(0,5)):[]}renderNoMatchingContent(){const e=this.getTags(),t=this.getCategories(),r=0===e.length&&0===t.length;return a().createElement("div",{className:"jetpack-instant-search__search-result-minimal-content"},r&&a().createElement(i.A,{url:this.props.result.fields["permalink.url.raw"]}),a().createElement("div",{className:"jetpack-instant-search__search-result-minimal-cats-and-tags"},0!==e.length&&a().createElement("ul",{className:"jetpack-instant-search__search-result-minimal-tags"},e.map((e=>a().createElement("li",{key:e,className:"jetpack-instant-search__search-result-minimal-tag"},a().createElement(o.A,{icon:"tag",size:this.getIconSize()}),a().createElement("span",{className:"jetpack-instant-search__search-result-minimal-tag-text"},e))))),0!==t.length&&a().createElement("ul",{className:"jetpack-instant-search__search-result-minimal-cats"},t.map((e=>a().createElement("li",{key:e,className:"jetpack-instant-search__search-result-minimal-cat"},a().createElement(o.A,{icon:"folder",size:this.getIconSize()}),a().createElement("span",{className:"jetpack-instant-search__search-result-minimal-cat-text"},e)))))))}renderMatchingContent(){return a().createElement("div",{className:"jetpack-instant-search__search-result-minimal-content",dangerouslySetInnerHTML:{__html:this.props.result.highlight&&"object"==typeof this.props.result.highlight?Object.entries(this.props.result.highlight).filter((([e,t])=>"comments"!==e&&"title"!==e&&Array.isArray(t))).map((([,e])=>e.join(" ... "))).join(" ... "):""}})}render(){const{result_type:e,fields:t,highlight:r}=this.props.result;if("post"!==e)return null;const n=!r||"object"!=typeof r||Object.entries(r).every((([e,t])=>"comments"===e||"title"===e||!Array.isArray(t)||""===t[0]));return a().createElement("li",{className:["jetpack-instant-search__search-result","jetpack-instant-search__search-result-minimal",this.getCategories(!0).map((e=>"jetpack-instant-search__search-result-category--"+(0,s.cleanForSlug)(e))).join(" ")].join(" ")},a().createElement("h3",{className:"jetpack-instant-search__search-result-title jetpack-instant-search__search-result-minimal-title"},a().createElement(c.A,{postType:t.post_type,shortcodeTypes:t.shortcode_types}),a().createElement("a",{className:"jetpack-instant-search__search-result-title-link jetpack-instant-search__search-result-minimal-title-link",href:`//${t["permalink.url.raw"]}`,onClick:this.props.onClick},a().createElement("span",{dangerouslySetInnerHTML:{__html:r.title}}),"yes"===t["forum.topic_resolved"]&&a().createElement("span",{className:"jetpack-instant-search__search-result-title-checkmark"}))),n?this.renderNoMatchingContent():this.renderMatchingContent(),a().createElement(l.A,{comments:r&&r.comments}))}}const p=u},9761:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var s=r(7723),n=r(3832),a=r(1609),o=r.n(a),i=r(1539),c=r(2355),l=r(2652),u=r(6777);const __=s.__;class p extends a.Component{render(){const{result_type:e,fields:t,highlight:r}=this.props.result;if("post"!==e)return null;const s=Array.isArray(t["image.url.raw"])?t["image.url.raw"][0]:t["image.url.raw"],a=Array.isArray(r.title)&&r.title[0].length>0?r.title[0]:__("No title","jetpack-search-pkg"),p="string"==typeof this.props.searchQuery&&""!==this.props.searchQuery.trim(),h=a.includes("<mark>"),d=p&&!h&&"object"==typeof r&&Object.entries(r).some((([e,t])=>"title"!==e&&"comments"!==e&&Array.isArray(t)&&t[0]?.length>0));return o().createElement("li",{className:["jetpack-instant-search__search-result","jetpack-instant-search__search-result-product",(()=>{let e=t["category.name.default"];return e?(Array.isArray(e)||(e=[e]),e):[]})().map((e=>"jetpack-instant-search__search-result-category--"+(0,n.cleanForSlug)(e))).join(" ")].join(" ")},o().createElement("a",{className:"jetpack-instant-search__search-result-product-img-link",href:`//${t["permalink.url.raw"]}`,onClick:this.props.onClick},o().createElement("div",{className:"jetpack-instant-search__search-result-product-img-container "+(s?"":"jetpack-instant-search__search-result-product-img-container--placeholder")},s?o().createElement(c.A,{alt:t["image.alt_text"],className:"jetpack-instant-search__search-result-product-img",isPhotonEnabled:this.props.isPhotonEnabled,src:`//${s}`}):o().createElement("div",{className:"jetpack-instant-search__search-result-product-img"},o().createElement(i.A,{icon:"block",style:{}}),o().createElement(i.A,{icon:"image",style:{},title:__("Does not have an image","jetpack-search-pkg")})))),o().createElement("h3",{className:"jetpack-instant-search__search-result-title jetpack-instant-search__search-result-product-title"},o().createElement("a",{className:"jetpack-instant-search__search-result-title-link",href:`//${t["permalink.url.raw"]}`,onClick:this.props.onClick,dangerouslySetInnerHTML:{__html:a}})),o().createElement(l.A,{price:t["wc.price"],salePrice:t["wc.sale_price"],formattedPrice:t["wc.formatted_price"],formattedRegularPrice:t["wc.formatted_regular_price"],formattedSalePrice:t["wc.formatted_sale_price"]}),!!t["meta._wc_average_rating.double"]&&o().createElement(u.A,{count:t["meta._wc_review_count.long"],rating:t["meta._wc_average_rating.double"],permalink:`//${t["permalink.url.raw"]}`}),d&&o().createElement("div",{className:"jetpack-instant-search__search-result-product-match"},o().createElement("mark",null,o().createElement(i.A,{icon:"search",style:{},title:!1}),o().createElement("span",null,"comment"in r?__("Matches comments","jetpack-search-pkg"):__("Matches content","jetpack-search-pkg",0)))))}}const h=p},3735:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var s=r(6072),n=r.n(s),a=r(1609),o=r.n(a),i=r(7479),c=r(8924),l=r(829),u=r(6485),p=r(9761);class h extends a.Component{componentDidMount(){this.props.railcar&&(0,c.A8)(this.getCommonTrainTracksProps())}componentDidUpdate(e){this.props.railcar!==e.railcar&&this.props.railcar&&(0,c.A8)(this.getCommonTrainTracksProps())}getCommonTrainTracksProps(){let e="jetpack-instant-search-ui/v1";return this.props.resultFormat===i.s6?e+="-product":this.props.resultFormat===i.yj?e+="-expanded":e+="-minimal",this.props.result.custom&&(e+="-custom"),{fetch_algo:this.props.railcar.fetch_algo,fetch_position:this.props.railcar.fetch_position,fetch_query:this.props.railcar.fetch_query,railcar:this.props.railcar.railcar,rec_blog_id:this.props.railcar.rec_blog_id,rec_post_id:this.props.railcar.rec_post_id,session_id:this.props.railcar.session_id,ui_algo:e,ui_position:this.props.index}}onClick=()=>{this.props.railcar&&(0,c.wH)({...this.getCommonTrainTracksProps(),action:"click"})};render(){return this.props.resultFormat===i.s6?o().createElement(p.A,n()({onClick:this.onClick},this.props)):this.props.resultFormat===i.yj?o().createElement(l.A,n()({onClick:this.onClick},this.props)):o().createElement(u.A,n()({onClick:this.onClick},this.props))}}const d=h},8568:(e,t,r)=>{"use strict";r.d(t,{A:()=>w});var s=r(7723),n=r(3022),a=r(1609),o=r.n(a),i=r(7084),c=r(7479),l=r(6299),u=r(3089),p=r(1539),h=r(4120),d=r(8961),f=r(3845),m=r(1242),g=r(5846),y=r(3735),v=r(6097),_=r(1080);const __=s.__,_n=s._n;class b extends a.Component{state={shouldShowMobileSecondary:!1};toggleMobileSecondary=e=>{"click"!==e.type&&("keydown"!==e.type||"Enter"!==e.key&&" "!==e.key)||(" "===e.key&&e.preventDefault(),this.setState((e=>({shouldShowMobileSecondary:!e.shouldShowMobileSecondary}))))};hasFilterOptions(){let e=[...this.props.widgets];return this.props.widgetOutsideOverlay?.filters?.length>0&&(e=[this.props.widgetOutsideOverlay,...e]),e.length>0}getSearchTitle(){const{total:e=0,corrected_query:t=!1}=this.props.response,r=""!==this.props.searchQuery,n=!1!==t,a=this.props.staticFilters&&this.props.staticFilters.group_id&&this.props.staticFilters.group_id!==c.$K;if(this.props.isLoading)return r?__("Searching…","jetpack-search-pkg",0):__("Loading popular results…","jetpack-search-pkg");if(0===e||this.props.hasError)return __("No results found","jetpack-search-pkg");const o=(new Intl.NumberFormat).format(e);if(a){const t=(0,u.PM)().find((e=>"group_id"===e.filter_id)),r=t?.filter_id,n=this.props.staticFilters[r],a=t?.selected,i=t?.values?.[0],c=n||a||i?.value,l=t?.values?.find?.((e=>e.value===c));return l?.name?(0,s.sprintf)(/* translators: %1$s: number of results. - %2$s: site name. */
_n("Found %1$s result in %2$s","Found %1$s results in %2$s",e,"jetpack-search-pkg"),o,l?.name):(0,s.sprintf)(/* translators: %s: number of results. */
_n("Found %s result","Found %s results",e,"jetpack-search-pkg"),o)}return r?n?(0,s.sprintf)(/* translators: %1$s: number of results. %2$s: the corrected search query. */
_n('Found %1$s result for "%2$s"','Found %1$s results for "%2$s"',e,"jetpack-search-pkg"),o,t):(0,s.sprintf)(/* translators: %s: number of results. */
_n("Found %s result","Found %s results",e,"jetpack-search-pkg"),o):__("Showing popular results","jetpack-search-pkg")}renderPrimarySection(){const{highlightColor:e,searchQuery:t}=this.props,{results:r=[],total:n=0,corrected_query:u=!1}=this.props.response,p=(0,i.d)(e),h=!1!==u,m=n>0,g=this.props.additionalBlogIds?.length>0||this.props.staticFilters&&this.props.staticFilters.group_id&&this.props.staticFilters.group_id!==c.$K;return o().createElement(a.Fragment,null,o().createElement("style",{dangerouslySetInnerHTML:{__html:`\n\t\t\t\t\t\t\t.jetpack-instant-search *::selection,\n\t\t\t\t\t\t\t.jetpack-instant-search .jetpack-instant-search__search-results .jetpack-instant-search__search-results-primary .jetpack-instant-search__search-result mark {\n\t\t\t\t\t\t\t\tcolor: ${p};\n\t\t\t\t\t\t\t\tbackground-color: ${e};\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t`}}),o().createElement(_.A,null),o().createElement("h2",{className:"jetpack-instant-search__search-results-title"},this.getSearchTitle()),m&&h&&o().createElement("p",{className:"jetpack-instant-search__search-results-unused-query"},/* translators: %s: Search query. */ /* translators: %s: Search query. */
(0,s.sprintf)(__('No results for "%s"',"jetpack-search-pkg"),t)),this.props.hasError&&o().createElement(d.A,{type:"warning"},(0,l.u)(this.props.response.error)),m&&!this.props.hasError&&this.props.response._isOffline&&o().createElement(d.A,{type:"warning"},(0,l.u)({message:"offline"})),m&&!this.props.hasError&&o().createElement("ol",{className:`jetpack-instant-search__search-results-list is-format-${this.props.resultFormat}`},r.map(((e,t)=>o().createElement(y.A,{index:t,key:t,staticFilters:this.props.staticFilters,isPhotonEnabled:this.props.isPhotonEnabled,locale:this.props.locale,railcar:this.props.isVisible?e.railcar:null,result:e,resultFormat:this.props.resultFormat,searchQuery:this.props.searchQuery,isMultiSite:g,showPostDate:this.props.showPostDate})))),m&&this.props.hasNextPage&&o().createElement("div",{className:"jetpack-instant-search__search-results-pagination"},o().createElement(f.A,{enableLoadOnScroll:this.props.enableLoadOnScroll,isLoading:this.props.isLoading,onLoadNextPage:this.props.onLoadNextPage})))}renderSecondarySection(){return o().createElement(v.A,{filters:this.props.filters,staticFilters:this.props.staticFilters,isLoading:this.props.isLoading,locale:this.props.locale,postTypes:this.props.postTypes,response:this.props.response,widgets:this.props.widgets,widgetOutsideOverlay:this.props.widgetOutsideOverlay})}closeOverlay=e=>{e.preventDefault(),this.props.closeOverlay()};onKeyPressHandler=e=>{"Enter"===e.key&&(e.preventDefault(),this.props.closeOverlay())};render(){return o().createElement("div",{className:(0,n.A)("jetpack-instant-search__search-results-wrapper",{"has-colophon":this.props.showPoweredBy})},o().createElement("div",{"aria-hidden":!0===this.props.isLoading,className:"jetpack-instant-search__search-results"},o().createElement("div",{className:"jetpack-instant-search__search-results-controls",role:"form"},o().createElement(g.A,{"aria-controls":"jetpack-instant-search__search-results-content",className:"jetpack-instant-search__search-results-search-form",isVisible:this.props.isVisible,onChangeSearch:this.props.onChangeSearch,searchQuery:this.props.searchQuery}),o().createElement("button",{className:"jetpack-instant-search__overlay-close",onClick:this.closeOverlay,onKeyPress:this.onKeyPressHandler,tabIndex:"0","aria-label":__("Close search results","jetpack-search-pkg")},o().createElement(p.A,{icon:"cross",size:"24","aria-hidden":"true",focusable:"false"}))),o().createElement(m.A,{enableSort:this.props.enableSort,onChangeSort:this.props.onChangeSort,resultFormat:this.props.resultFormat,sort:this.props.sort},(this.hasFilterOptions()||this.props.hasNonSearchWidgets)&&o().createElement("div",{role:"button",onClick:this.toggleMobileSecondary,onKeyDown:this.toggleMobileSecondary,tabIndex:"0",className:"jetpack-instant-search__search-results-filter-button"},__("Filters","jetpack-search-pkg"),o().createElement(p.A,{icon:"chevron-down",size:16,alt:__("Show search filters","jetpack-search-pkg"),"aria-hidden":"true"}),o().createElement("span",{className:"screen-reader-text assistive-text"},this.state.shouldShowMobileSecondary?__("Hide filters","jetpack-search-pkg"):__("Show filters","jetpack-search-pkg")))),o().createElement("div",{"aria-live":"polite",className:"jetpack-instant-search__search-results-content",id:"jetpack-instant-search__search-results-content"},o().createElement("div",{className:"jetpack-instant-search__search-results-primary"},this.renderPrimarySection()),o().createElement("div",{className:["jetpack-instant-search__search-results-secondary",(this.state.shouldShowMobileSecondary?"jetpack-instant-search__search-results-secondary--show-as-modal":"")+" "].join(" ")},this.renderSecondarySection())),o().createElement("button",{id:c.m2,onClick:this.closeOverlay},"Close Search")),this.props.showPoweredBy&&o().createElement(h.A,{locale:this.props.locale}))}}const w=b},1168:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(7723),n=r(1609),a=r.n(n),o=r(3476);const __=s.__;class i extends n.Component{handleClick=e=>{this.props.value!==e.currentTarget.value&&(e.preventDefault(),this.props.onChange(e.currentTarget.dataset.value))};handleSelectChange=e=>{this.props.value!==e.currentTarget.value&&(e.preventDefault(),this.props.onChange(e.currentTarget.value))};render(){const e=(0,o.$)(this.props.resultFormat),t=e.size;let r=0;return e.size>3?a().createElement("div",{"aria-controls":"jetpack-instant-search__search-results-content",className:"jetpack-instant-search__search-sort jetpack-instant-search__search-sort-with-select"},a().createElement("label",{htmlFor:"jetpack-instant-search__search-sort-select"},__("Sort:","jetpack-search-pkg")),a().createElement("select",{className:"jetpack-instant-search__search-sort-select",id:"jetpack-instant-search__search-sort-select",onBlur:this.handleSelectChange,onChange:this.handleSelectChange},[...e.entries()].map((([e,t])=>a().createElement("option",{value:e,key:e,selected:this.props.value===e?"selected":""},t))))):a().createElement("div",{"aria-controls":"jetpack-instant-search__search-results-content",className:"jetpack-instant-search__search-sort jetpack-instant-search__search-sort-with-links"},a().createElement("div",{className:"screen-reader-text"},__("Sort by:","jetpack-search-pkg")," "),[...e.entries()].map((([e,s])=>a().createElement(a().Fragment,null,a().createElement("button",{"aria-current":this.props.value===e?"true":"false",className:"jetpack-instant-search__search-sort-option "+(this.props.value===e?"is-selected":""),"data-value":e,key:e,onClick:this.handleClick},s),++r<t?a().createElement("span",{"aria-hidden":"true",className:"jetpack-instant-search__search-sort-separator"},"•"):""))))}}},6097:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var s=r(1609),n=r.n(s),a=r(5795),o=r(1503),i=r(6465);const c=e=>n().createElement("div",{className:"jetpack-instant-search__sidebar"},n().createElement(o.A,{filters:e.filters,staticFilters:e.staticFilters,loading:e.isLoading,locale:e.locale,postTypes:e.postTypes,results:e.response,showClearFiltersButton:!0,widget:e.widgetOutsideOverlay}),n().createElement(i.A,null),e.widgets.map((t=>(0,a.createPortal)(n().createElement("div",{id:`${t.widget_id}-portaled-wrapper`,className:"jetpack-instant-search__portaled-wrapper"},n().createElement(o.A,{filters:e.filters,staticFilters:e.staticFilters,loading:e.isLoading,locale:e.locale,postTypes:e.postTypes,results:e.response,showClearFiltersButton:!1,showTitle:!1,widget:t})),document.getElementById(`${t.widget_id}-wrapper`)))))},1080:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(1609),n=r.n(s),a=r(4952),o=r(3089),i=r(999),c=r(5450);class l extends s.Component{onChangeStaticFilter=(e,t)=>{this.props.setStaticFilter(e,t),this.props.onChange&&this.props.onChange()};renderStaticFilterComponent=e=>Object.hasOwn(e,"visible")&&!e.visible?null:n().createElement(c.A,{aggregation:[],configuration:e,locale:this.props.locale,onChange:this.onChangeStaticFilter,postTypes:this.props.postTypes,type:(0,o.sm)(e),value:this.props.staticFilters[(0,o.xf)(e)],variation:"tabbed"});render(){const e=(0,o.PM)("tabbed");return e.length?n().createElement("div",{className:"jetpack-instant-search__search-tabbed-filters"},e.map(this.renderStaticFilterComponent)):null}}const u=(0,a.Ng)((e=>({staticFilters:e.staticFilters})),{setStaticFilter:i.$6})(l)},6465:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(1609),n=r.n(s);class a extends s.Component{container=(0,s.createRef)();componentDidMount(){const e=document.getElementsByClassName("jetpack-instant-search__widget-area")[0];e&&(e.style.removeProperty("display"),this.container.current.appendChild(e))}shouldComponentUpdate(){return!1}render(){return n().createElement("div",{className:"jetpack-instant-search__widget-area-container",ref:this.container})}}},9355:(e,t,r)=>{"use strict";function s(e,t,r){if(!e)return"";const s=decodeURIComponent(e);return(!t||"false"!==s)&&(!(!t||"true"!==s)||(r&&0*+s==0?+s:s))}function n(e,t,r){let n,a;const o={},i=e.split("&");for(t=void 0===t||t,r=void 0===r||r;n=i.shift();)n=n.split("="),a=n.shift(),void 0!==o[a]?o[a]=[].concat(o[a],s(n.shift(),t,r)):o[a]=s(n.shift(),t,r);return o}r.d(t,{D:()=>n})},7152:(e,t,r)=>{"use strict";function s(){return!window.matchMedia("(prefers-reduced-motion: no-preference)").matches}r.d(t,{y:()=>s})},5678:(e,t,r)=>{"use strict";r.d(t,{$P:()=>S,pA:()=>m,q1:()=>f});r(9060);var s=r(4224),n=r.n(s),a=r(1473),o=r(7216),i=r(5114),c=r(7479),l=r(3089);let u;const p=e=>Array.isArray(e)&&e.length>0,h=(0,i.A)(30,5*c.Az),d=(0,i.A)(30,30*c.Az);function f(e=[]){const t={};return e.forEach((({filters:e})=>e.forEach((e=>{t[e.filter_id]=function(e){switch(e.type){case"date_histogram":return{date_histogram:{field:"post_date_gmt"===e.field?"date_gmt":"date",interval:e.interval}};case"taxonomy":{let t=`taxonomy.${e.taxonomy}.slug_slash_name`;return"post_tag"===e.taxonomy?t="tag.slug_slash_name":"category"===e.taxonomy&&(t="category.slug_slash_name"),{terms:{field:t,size:e.count}}}case"post_type":case"blog_id":return{terms:{field:e.type,size:e.count}};case"author":return{terms:{field:"author_login_slash_name",size:e.count}}}}(e)})))),t}function m(e){return e=e??{},Object.fromEntries(Object.entries(e).filter((([,e])=>e?.buckets?.length>0)).map((([e,t])=>{const r=t.buckets.map((e=>({...e,doc_count:0})));return[e,{...t,buckets:r}]})))}k();const g=/(\d{4})-(\d{2})-(\d{2})/;function y(e,t,r){let s,n;"year"===r&&([,s,,]=t.match(g)),"month"===r&&([,s,n]=t.match(g));let a="",o="";if(n){const e=+n+1;a=`${s}-${n}-01`,o=e<=12?`${s}-${e<10?`0${e}`:`${e}`}-01`:+s+1+"-01-01"}else s&&(a=`${s}-01-01`,o=+s+1+"-01-01");return{range:{[e]:{gte:a,lt:o}}}}const v=new Map([["post_types",e=>({term:{post_type:e}})],["authors",e=>({term:{author_login:e}})],["blog_ids",e=>({term:{blog_id:e}})],["category",e=>({term:{"category.slug":e}})],["post_tag",e=>({term:{"tag.slug":e}})],["month_post_date",e=>y("date",e,"month")],["month_post_date_gmt",e=>y("date_gmt",e,"month")],["month_post_modified",e=>y("date",e,"month")],["month_post_modified_gmt",e=>y("date_gmt",e,"month")],["year_post_date",e=>y("date",e,"year")],["year_post_date_gmt",e=>y("date_gmt",e,"year")],["year_post_modified",e=>y("date",e,"year")],["year_post_modified_gmt",e=>y("date_gmt",e,"year")]]);function _(e){const t={};return Object.keys(e).forEach((r=>{const s=e[r];"group_id"===r&&s!==c.$K&&(t[r]=s)})),t}function b(e,t,r){const s={bool:{must:[]}};return(0,l.Xt)().filter((t=>p(e[t]))).forEach((t=>{e[t].forEach((e=>{v.has(t)?s.bool.must.push(v.get(t)(e)):s.bool.must.push({term:{[`taxonomy.${t}.slug`]:e}})}))})),t&&s.bool.must.push(t),r?.length>0&&s.bool.must.push({bool:{must_not:r.map((e=>v.get("post_types")(e)))}}),s}const w=new Map([["oldest","date_asc"],["newest","date_desc"],["relevance","score_default"]]);function E(e){return["price_asc","price_desc","rating_desc"].includes(e)?e:w.get(e,"score_default")}function k(){u&&u.abort(),u=new AbortController}function S(e,t){const r=n()(Array.from(arguments));if(!navigator.onLine&&d.get(r))return Promise.resolve(d.get(r)).then((e=>({_isCached:!0,_isError:!1,_isOffline:!0,...e})));if(h.get(r))return Promise.resolve(h.get(r)).then((e=>({_isCached:!0,_isError:!1,_isOffline:!1,...e})));const s=function({aggregations:e,excludedPostTypes:t,filter:r,staticFilters:s,pageHandle:n,query:i,resultFormat:l,sort:u,postsPerPage:p=10,adminQueryFilter:h,isInCustomizer:d=!1,additionalBlogIds:f=[],highlightFields:m=["title","content","comments"],customResults:g=[]}){null===i&&(i="");let y=["date","permalink.url.raw","tag.name.default","category.name.default","post_type","shortcode_types","forum.topic_resolved"];(l!==c.UM||d)&&(y=y.concat(["has.image","image.url.raw","image.alt_text"])),(l===c.s6||d)&&(y=y.concat(["meta._wc_average_rating.double","meta._wc_review_count.long","wc.formatted_price","wc.formatted_regular_price","wc.formatted_sale_price","wc.price","wc.sale_price"])),s&&s.group_id&&s.group_id!==c.$K&&(y=y.concat(["author","blog_name","blog_icon_url"]));let v={aggregations:e,fields:y,highlight_fields:m,filter:b(r,h,t),query:encodeURIComponent(i),sort:E(u),page_handle:n,size:p};return f?.length>0&&(v.fields=y.concat(["author","blog_name","blog_icon_url","blog_id"]),v.additional_blog_ids=f),g.every((e=>{let t=e.pattern;const r=e.ids;if(t.startsWith("regex:")){if(t="^"+t.replace("regex:","")+"$",i.match(t))return v.custom_results=r,!1}else if(i===t)return v.custom_results=r,!1;return!0})),s&&Object.keys(s).length>0&&(v={...v,..._(s)}),(0,o.l)((0,a.U)(v))}(e),i=(l=r,function(e){const t=h.get(l)||d.get(l);if("AbortError"===e.name)return t?{_isCached:!0,_isError:!1,_isOffline:!1,...t}:null;if(t)return{_isCached:!0,_isError:!0,_isOffline:!1,...t};throw e});var l;const p=function(e,t){return function(r){const s={...r,requestId:t};return h.set(e,s),d.set(e,s),s}}(r,t),f=`/sites/${e.siteId}/search?${s}`,{apiNonce:m,apiRoot:g,homeUrl:y,isPrivateSite:v,isWpcom:w}=window[c.O5];let S=`https://public-api.wordpress.com/rest/v1.3${f}`;return v&&w?S=`${y}/wp-json/wpcom-origin/v1.3${f}`:v&&(S=`${g}jetpack/v4/search?${s}`),k(),fetch(S,{headers:v?{"X-WP-Nonce":m}:{},credentials:v?"include":"same-origin",signal:u.signal}).then((e=>200!==e.status?e.json().then((e=>{throw new Error(e.error)})):e)).then((e=>e.json())).then(p).catch(i)}},113:(e,t,r)=>{"use strict";function s(e,t){Array.isArray(e)||(e=[e]);return 0!==e.filter((e=>t.includes(e))).length}r.d(t,{A:()=>s})},7084:(e,t,r)=>{"use strict";function s(e){const t=function(e){let t;return"#"===e[0]&&(t=e.substring(1)),3===t.length&&(t=t.split("").map((e=>`${e}${e}`)).join("")),t}(e);return(299*parseInt(t.substr(0,2),16)+587*parseInt(t.substr(2,2),16)+114*parseInt(t.substr(4,2),16))/1e3>=128?"black":"white"}r.d(t,{d:()=>s})},7479:(e,t,r)=>{"use strict";r.d(t,{$J:()=>y,$K:()=>n,Az:()=>m,Hs:()=>v,Md:()=>l,O5:()=>a,QB:()=>b,Ri:()=>p,SK:()=>_,UM:()=>d,Wq:()=>w,ZJ:()=>c,m2:()=>u,s6:()=>f,tr:()=>i,w4:()=>o,x6:()=>g,yj:()=>h});var s=r(7723);const __=s.__,n="__NO_GROUP__",a="JetpackInstantSearchOptions",o="jetpack-instant-search__overlay",i="jetpack-instant-search__search-results",c=70,l="jetpack-instant-search__box-input",u="jetpack-instant-search__overlay-focus-anchor",p="ASC",h="expanded",d="minimal",f="product",m=6e4,g="relevance",y=1e3,v=["newest","oldest",g,"price_asc","price_desc","rating_desc"],_=[h,d,f],b=new Map([[g,__("Relevance","jetpack-search-pkg")],["newest",__("Newest","jetpack-search-pkg")],["oldest",__("Oldest","jetpack-search-pkg")]]),w=new Map([["price_asc",__("Price: low to high","jetpack-search-pkg")],["price_desc",__("Price: high to low","jetpack-search-pkg")],["rating_desc",__("Rating","jetpack-search-pkg")]])},3398:(e,t,r)=>{"use strict";r.d(t,{Tx:()=>o,UC:()=>i});var s=r(7479);const n=new Map([["jetpack_search_color_theme","colorTheme"],["jetpack_search_enable_sort","enableSort"],["jetpack_search_highlight_color","highlightColor"],["jetpack_search_inf_scroll","enableInfScroll"],["jetpack_search_filtering_opens_overlay","enableFilteringOpensOverlay"],["jetpack_search_show_post_date","enablePostDate"],["jetpack_search_overlay_trigger","overlayTrigger"],["jetpack_search_show_powered_by","showPoweredBy"],["jetpack_search_result_format","resultFormat"]]);function a(){return"function"==typeof window?.wp?.customize}function o(e){a()&&window.addEventListener("message",(t=>{t.data&&t.target===window&&"jetpackSearchSectionOpen"===t.data?.key&&"expanded"in t.data&&e(t.data.expanded)}))}function i(e){a()&&n.forEach(((t,r)=>{window.wp.customize(r,(r=>{r.bind((function(r){const n={[t]:r};window[s.O5].showResults=!0,window[s.O5].overlayOptions={...window[s.O5].overlayOptions,...n},e&&e(n)}))}))}))}},7574:(e,t,r)=>{"use strict";function s(e){return[...e.querySelectorAll('input[type="checkbox"]').values()].filter((e=>e.checked)).map((e=>e.name))}function n(e){const t={searchInputSelector:['input[name="s"]:not(.jetpack-instant-search__box-input)',"#searchform input.search-field:not(.jetpack-instant-search__box-input)",".search-form input.search-field:not(.jetpack-instant-search__box-input)",".searchform input.search-field:not(.jetpack-instant-search__box-input)"].join(", "),filterInputSelector:["a.jetpack-search-filter__link"],overlayTriggerSelector:[".jetpack-instant-search__open-overlay-button","header#site-header .search-toggle[data-toggle-target]"].join(",")};return e.theme_options?{...t,...e.theme_options}:t}r.d(t,{E:()=>n,e:()=>s})},6299:(e,t,r)=>{"use strict";r.d(t,{u:()=>n});var s=r(7723);const __=s.__;function n(e){switch(e?.message){case"service_unavailable":return(0,s.sprintf)(
// translators: %s: Error code.
__("Jetpack Search is currently unavailable. Please try again later. [%s]","jetpack-search-pkg"),e?.message);case"offline":return __("It looks like you're offline. Please reconnect to load the latest results.","jetpack-search-pkg");default:return(0,s.sprintf)(
// translators: %s: Error code.
__("Jetpack Search has encountered an error. Please contact the site administrator if the issue persists. [%s]","jetpack-search-pkg"),e?.message??"unknown")}}},3089:(e,t,r)=>{"use strict";r.d(t,{PM:()=>o,Xt:()=>a,hM:()=>p,hX:()=>i,i$:()=>c,sm:()=>h,xf:()=>u});r(6336),r(2926),r(887),r(3326),r(3893),r(3338),r(5508);var s=r(7479);const n=Object.freeze(["blog_ids","authors","post_types","category","post_format","post_tag","month_post_date","month_post_date_gmt","month_post_modified","month_post_modified_gmt","year_post_date","year_post_date_gmt","year_post_modified","year_post_modified_gmt"]);function a(e=window[s.O5]?.widgets,t=window[s.O5]?.widgetsOutsideOverlay){const r=new Set(n);return[...e??[],...t??[]].map((e=>e.filters)).filter((e=>Array.isArray(e))).reduce(((e,t)=>e.concat(t)),[]).filter((e=>"taxonomy"===e.type)).forEach((e=>r.add(e.taxonomy))),[...r]}function o(e){return window[s.O5]?.staticFilters?window[s.O5].staticFilters.filter((t=>!e||("sidebar"===e&&!t.variation||!(!e||!t.variation)&&t.variation===e))):[]}function i(){const e=o(),t=new Set;return e.forEach((e=>t.add(e.filter_id))),[...t]}function c(e=window[s.O5]?.widgets){const t=function(e=window[s.O5]?.widgets){return e?.map(l).reduce(((e,t)=>e.concat(t)),[])??[]}(e);return a().filter((e=>!t.includes(e)))}function l(e){return e.filters.map(u).filter((e=>"string"==typeof e))}function u(e){return"date_histogram"===e.type?`${e.interval}_${e.field}`:"taxonomy"===e.type?`${e.taxonomy}`:"post_type"===e.type?"post_types":"author"===e.type?"authors":"blog_id"===e.type?"blog_ids":"group"===e.type?e.filter_id:null}function p(e){return e.includes("month")?{field:e.split("month_").pop(),type:"date_histogram",interval:"month"}:e.includes("year")?{field:e.split("year_").pop(),type:"date_histogram",interval:"year"}:"post_types"===e?{type:"post_type"}:"authors"===e?{type:"author"}:"blog_ids"===e?{type:"blog_id"}:"group"===e?{type:"group"}:{type:"taxonomy",taxonomy:e}}function h(e){return"date_histogram"===e.type?"date":"taxonomy"===e.type?"taxonomy":"post_type"===e.type?"postType":"author"===e.type?"author":"blog_id"===e.type?"blogId":"group"===e.type?"group":void 0}},4809:(e,t,r)=>{"use strict";r.d(t,{g:()=>a});var s=r(4936),n=r(1609);function a(e,t,r,a=!0){const[o,i]=(0,n.useState)(null),c=(l=e)?l.split("?",1)[0]:"";var l;const u=c?.substring(c.lastIndexOf(".")+1).toLowerCase(),p=["gif","jpg","jpeg","png","webp","heic"].includes(u);return(0,n.useEffect)((()=>{if(a&&p){const n=(0,s.A)(c,{resize:`${t},${r}`});i(n||e)}else i(e)}),[e,t,r,a,c,p]),o}},6122:(e,t,r)=>{"use strict";r.d(t,{$Z:()=>i,NY:()=>c,fX:()=>u,lz:()=>l});var s=r(7216),n=r(9355),a=r(7479),o=r(3089);function i(e=window.location.search){return(0,n.D)(e.substring(1),!1,!1)}function c(e){window.instantSearchSkipPushState||function(e){if(history.pushState){const t=new window.URL(window.location.href);window[a.O5]&&"homeUrl"in window[a.O5]&&(t.href=window[a.O5].homeUrl),t.search=e,window.history.pushState(null,null,t.toString())}}((0,s.l)(e))}function l(){const e=i();return a.SK.includes(e.result_format)?e.result_format:null}function u(e,t,r=!1){if(history.pushState&&history.replaceState){const n=new URL(e),a=i(n.search),c=[...(0,o.Xt)(),...(0,o.hX)(),"s","sort"],l=Object.keys(a).some((e=>c.includes(e)));if(l&&c.forEach((e=>delete a[e])),n.search=(0,s.l)(a),r?window.history.replaceState(null,null,n.toString()):window.history.pushState(null,null,n.toString()),l)return void window.location.reload();t()}}},3476:(e,t,r)=>{"use strict";r.d(t,{$:()=>n});var s=r(7479);function n(e=null){return e!==s.s6?s.QB:new Map([...s.QB,...s.Wq])}},8924:(e,t,r)=>{"use strict";r.d(t,{A8:()=>u,Re:()=>a,kX:()=>i,oD:()=>h,wH:()=>p,wk:()=>c,yM:()=>l,ze:()=>o});r(9060);let s=!0;const n={};function a(){s=!1}function o(e=!1){(e||s)&&(window._tkq=window._tkq||[])}function i(e=!1){(e||s)&&window._tkq.push(["clearIdentity"])}function c(e,t=!1){(t||s)&&(n.blog_id=e)}function l(e,t,r=!1){(r||s)&&window._tkq.push(["recordEvent",e,{...n,...t}])}function u(e,t=!1){l("jetpack_instant_search_traintracks_render",e,t)}function p(e,t=!1){l("jetpack_instant_search_traintracks_interact",e,t)}function h(e,t=!1){l("jetpack_instant_search_static_filter_select",e,t)}},999:(e,t,r)=>{"use strict";function s(e){return{type:"MAKE_SEARCH_REQUEST",options:e}}function n({options:e,response:t}){return{type:"RECORD_SUCCESSFUL_SEARCH_REQUEST",options:e,response:t}}function a(e){return{type:"RECORD_FAILED_SEARCH_REQUEST",error:e}}function o({isHistoryNavigation:e=!1}={}){return{type:"INITIALIZE_QUERY_VALUES",isHistoryNavigation:e}}function i(e,t=!0){return{type:"SET_SEARCH_QUERY",query:e,propagateToWindow:t}}function c(e,t=!0){return{type:"SET_SORT",sort:e,propagateToWindow:t}}function l(e,t,r=!0){return{type:"SET_FILTER",name:e,value:t,propagateToWindow:r}}function u(e,t,r=!0){return{type:"SET_STATIC_FILTER",name:e,value:t,propagateToWindow:r}}function p(e=!0){return{type:"CLEAR_FILTERS",propagateToWindow:e}}function h(){return{type:"CLEAR_QUERY_VALUES"}}function d(){return{type:"DISABLE_QUERY_STRING_INTEGRATION"}}r.d(t,{$6:()=>u,H6:()=>n,JF:()=>o,Qt:()=>h,R6:()=>l,Ri:()=>i,WL:()=>a,d1:()=>c,go:()=>d,hi:()=>p,oZ:()=>s})},8114:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var s=r(3257),n=r.n(s),a=r(5678),o=r(7479),i=r(3089),c=r(6122),l=r(999);let u=0,p=!0;const h=n()(c.NY,o.$J);const d={CLEAR_FILTERS:function(e){if(!1===e.propagateToWindow||!p)return;const t=(0,c.$Z)();(0,i.Xt)().forEach((e=>delete t[e])),(0,i.hX)().forEach((e=>delete t[e])),(0,c.NY)(t)},DISABLE_QUERY_STRING_INTEGRATION:function(){p=!1},INITIALIZE_QUERY_VALUES:function(e,t){const r=(0,c.$Z)();let s;"s"in r?t.dispatch((0,l.Ri)(r.s,!1)):t.dispatch((0,l.Ri)(null,!1)),o.Hs.includes(r.sort)?s=r.sort:"date"===r.orderby?s="string"==typeof r.order&&r.order.toUpperCase()===o.Ri?"oldest":"newest":"relevance"===r.orderby&&(s="relevance"),"string"==typeof s&&t.dispatch((0,l.d1)(s,!1)),t.dispatch((0,l.hi)(!1)),(0,i.Xt)().filter((e=>e in r)).forEach((e=>t.dispatch((0,l.R6)(e,r[e],!1)))),(0,i.hX)().filter((e=>e in r)).forEach((e=>t.dispatch((0,l.$6)(e,r[e],!1))))},MAKE_SEARCH_REQUEST:function(e,t){u++,(0,a.$P)(e.options,u).then((r=>{null!==r&&t.dispatch((0,l.H6)({options:e.options,response:r}))})).catch((e=>{console.error("Jetpack Search ",e),t.dispatch((0,l.WL)(e))}))},SET_FILTER:function(e){if(!1===e.propagateToWindow||!p)return;if(!(0,i.Xt)().includes(e.name))return;const t=(0,c.$Z)();t[e.name]=e.value,(0,c.NY)(t)},SET_STATIC_FILTER:function(e){if(!1===e.propagateToWindow)return;if(!(0,i.hX)().includes(e.name))return;const t=(0,c.$Z)();t[e.name]=e.value,(0,c.NY)(t)},SET_SEARCH_QUERY:function(e){if(!1===e.propagateToWindow||!p)return;const t=(0,c.$Z)();null!==e.query?t.s=e.query:delete t.s,h(t)},SET_SORT:function(e){if(!1===e.propagateToWindow||!p)return;if(!o.Hs.includes(e.sort))return;const t=(0,c.$Z)();t.sort=e.sort,delete t.order,delete t.orderby,(0,c.NY)(t)}}},1388:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(6556),n=r(2047),a=r.n(n),o=r(8114),i=r(2607);const c=[a()(o.A)],l=(0,s.y$)(i.Ay,{},(0,s.Tw)(...c))},1339:(e,t,r)=>{"use strict";r.d(t,{JD:()=>a,VP:()=>o,gw:()=>i});var s=r(5678);let n={};function a(e=!1,t){switch(t.type){case"MAKE_SEARCH_REQUEST":case"RECORD_SUCCESSFUL_SEARCH_REQUEST":return!1;case"RECORD_FAILED_SEARCH_REQUEST":return!0}return e}function o(e=!1,t){switch(t.type){case"MAKE_SEARCH_REQUEST":return!0;case"RECORD_SUCCESSFUL_SEARCH_REQUEST":case"RECORD_FAILED_SEARCH_REQUEST":return!1}return e}function i(e={},t){switch(t.type){case"RECORD_SUCCESSFUL_SEARCH_REQUEST":{if("requestId"in e&&"requestId"in t.response&&e.requestId>t.response.requestId)return e;const r={...t.response};return t.options.pageHandle&&(r.aggregations={..."aggregations"in e&&!Array.isArray(e)?e.aggregations:{},...Array.isArray(r.aggregations)?{}:r.aggregations},r.results=[..."results"in e?e.results:[],...r.results],n={}),Array.isArray(r.results)&&r.results.length>r.total&&(r.total=r.results.length),t.options.pageHandle||(r.results?.length>0?n=(0,s.pA)(r.aggregations):r.aggregations=n),r}case"RECORD_FAILED_SEARCH_REQUEST":return e.error=t.error,e}return e}},6035:(e,t,r)=>{"use strict";function s(e=!1,t){switch(t.type){case"INITIALIZE_QUERY_VALUES":return t.isHistoryNavigation;case"SET_SEARCH_QUERY":case"SET_SORT":case"CLEAR_FILTERS":case"SET_FILTER":return!t.propagateToWindow&&e}return e}r.d(t,{x:()=>s})},2607:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>c});var s=r(6556),n=r(1339),a=r(6035),o=r(1253),i=r(7647);const c=(0,s.HY)({filters:o.uW,staticFilters:o.gi,hasError:n.JD,isLoading:n.VP,isHistoryNavigation:a.x,response:n.gw,searchQuery:o.ZF,serverOptions:i.o,sort:o.di})},1253:(e,t,r)=>{"use strict";r.d(t,{ZF:()=>a,di:()=>o,gi:()=>c,uW:()=>i});var s=r(7479),n=r(3089);function a(e=null,t){switch(t.type){case"SET_SEARCH_QUERY":return t.query;case"CLEAR_QUERY_VALUES":return null}return e}function o(e=null,t){switch(t.type){case"SET_SORT":return s.Hs.includes(t.sort)?t.sort:e;case"CLEAR_QUERY_VALUES":return null}return e}function i(e={},t){switch(t.type){case"CLEAR_FILTERS":case"CLEAR_QUERY_VALUES":return{};case"SET_FILTER":if(!(0,n.Xt)().includes(t.name)||!Array.isArray(t.value)&&"string"!=typeof t.value)return e;if(0===t.value.length){const r={...e};return delete r[t.name],r}return{...e,[t.name]:"string"==typeof t.value?[t.value]:t.value}}return e}function c(e={},t){switch(t.type){case"CLEAR_QUERY_VALUES":return{};case"SET_STATIC_FILTER":return(0,n.hX)().includes(t.name)?{...e,[t.name]:t.value}:e}return e}},7647:(e,t,r)=>{"use strict";r.d(t,{o:()=>n});var s=r(7479);function n(e=window[s.O5]??{}){return e}},538:(e,t,r)=>{"use strict";r.d(t,{FD:()=>u,JD:()=>o,S7:()=>f,V8:()=>l,VP:()=>c,gA:()=>p,kJ:()=>h,mi:()=>a,rB:()=>i,xk:()=>m,xp:()=>d});var s=r(7479),n=r(3089);function a(e){return e.response}function o(e){return e.hasError}function i(e){return!o(e)&&a(e)?.page_handle}function c(e){return e.isLoading}function l(e){return e.searchQuery}function u(e,t){return"string"!=typeof t&&(t=s.x6),"string"==typeof e.sort?e.sort:t}function p(e){return e.filters}function h(e){return e.staticFilters}function d(e){return null!==l(e)||function(e){return Object.keys(e.filters).length>0}(e)&&e.serverOptions.overlayOptions.enableFilteringOpensOverlay||function(e){return Object.keys(e.staticFilters).length>0}(e)}function f(e){if(!e.serverOptions.widgets||!e.filters)return{};const t=(0,n.i$)(e.serverOptions.widgets);return{filters:Object.keys(e.filters).filter((e=>t.includes(e))).map(n.hM)}}function m(e){return e.isHistoryNavigation}},67:(e,t,r)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const r="color: "+this.color;t.splice(1,0,r,"color: inherit");let s=0,n=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(s++,"%c"===e&&(n=s))})),t.splice(n,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=r(8926)(t);const{formatters:s}=e.exports;s.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},8926:(e,t,r)=>{r(9060),e.exports=function(e){function t(e){let r,n,a,o=null;function i(...e){if(!i.enabled)return;const s=i,n=Number(new Date),a=n-(r||n);s.diff=a,s.prev=r,s.curr=n,r=n,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let o=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((r,n)=>{if("%%"===r)return"%";o++;const a=t.formatters[n];if("function"==typeof a){const t=e[o];r=a.call(s,t),e.splice(o,1),o--}return r})),t.formatArgs.call(s,e);(s.log||t.log).apply(s,e)}return i.namespace=e,i.useColors=t.useColors(),i.color=t.selectColor(e),i.extend=s,i.destroy=t.destroy,Object.defineProperty(i,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(n!==t.namespaces&&(n=t.namespaces,a=t.enabled(e)),a),set:e=>{o=e}}),"function"==typeof t.init&&t.init(i),i}function s(e,r){const s=t(this.namespace+(void 0===r?":":r)+e);return s.log=this.log,s}function n(e,t){let r=0,s=0,n=-1,a=0;for(;r<e.length;)if(s<t.length&&(t[s]===e[r]||"*"===t[s]))"*"===t[s]?(n=s,a=r,s++):(r++,s++);else{if(-1===n)return!1;s=n+1,a++,r=a}for(;s<t.length&&"*"===t[s];)s++;return s===t.length}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names,...t.skips.map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const r=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of r)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const r of t.skips)if(n(e,r))return!1;for(const r of t.names)if(n(e,r))return!0;return!1},t.humanize=r(4997),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((r=>{t[r]=e[r]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t),r|=0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}},5114:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});class s{constructor(e=0,t=0){this.first=null,this.items=Object.create(null),this.last=null,this.max=e,this.size=0,this.ttl=t}has(e){return e in this.items}clear(){return this.first=null,this.items=Object.create(null),this.last=null,this.size=0,this}delete(e){if(this.has(e)){const t=this.items[e];delete this.items[e],this.size--,null!==t.prev&&(t.prev.next=t.next),null!==t.next&&(t.next.prev=t.prev),this.first===t&&(this.first=t.next),this.last===t&&(this.last=t.prev)}return this}evict(){const e=this.first;return delete this.items[e.key],this.first=e.next,this.first.prev=null,this.size--,this}get(e){let t;if(this.has(e)){const r=this.items[e];this.ttl>0&&r.expiry<=(new Date).getTime()?this.delete(e):(t=r.value,this.set(e,t,!0))}return t}keys(){return Object.keys(this.items)}set(e,t,r=!1){let s;if(r||this.has(e)){if(s=this.items[e],s.value=t,!1===r&&(s.expiry=this.ttl>0?(new Date).getTime()+this.ttl:this.ttl),this.last!==s){const e=this.last,t=s.next,r=s.prev;this.first===s&&(this.first=s.next),s.next=null,s.prev=this.last,e.next=s,null!==r&&(r.next=t),null!==t&&(t.prev=r)}}else this.max>0&&this.size===this.max&&this.evict(),s=this.items[e]={expiry:this.ttl>0?(new Date).getTime()+this.ttl:this.ttl,key:e,prev:this.last,next:null,value:t},1==++this.size?this.first=s:this.last.next=s;return this.last=s,this}}function n(e=1e3,t=0){if(isNaN(e)||e<0)throw new TypeError("Invalid max value");if(isNaN(t)||t<0)throw new TypeError("Invalid ttl value");return new s(e,t)}},1609:e=>{"use strict";e.exports=window.React},5795:e=>{"use strict";e.exports=window.ReactDOM},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},8468:e=>{"use strict";e.exports=window.lodash},4715:e=>{"use strict";e.exports=window.wp.blockEditor},6427:e=>{"use strict";e.exports=window.wp.components},3582:e=>{"use strict";e.exports=window.wp.coreData},7143:e=>{"use strict";e.exports=window.wp.data},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},5573:e=>{"use strict";e.exports=window.wp.primitives},3832:e=>{"use strict";e.exports=window.wp.url},6873:e=>{"use strict";e.exports=window.wp.viewport},6072:e=>{function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)({}).hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},3136:(e,t,r)=>{"use strict";var s=r(2003),n=r(561),a=TypeError;e.exports=function(e){if(s(e))return e;throw new a(n(e)+" is not a function")}},3422:(e,t,r)=>{"use strict";var s=r(1656).has;e.exports=function(e){return s(e),e}},3773:(e,t,r)=>{"use strict";var s=r(2480),n=String,a=TypeError;e.exports=function(e){if(s(e))return e;throw new a(n(e)+" is not an object")}},8419:(e,t,r)=>{"use strict";var s=r(9351),n=r(1512),a=r(9496),o=function(e){return function(t,r,o){var i=s(t),c=a(i);if(0===c)return!e&&-1;var l,u=n(o,c);if(e&&r!=r){for(;c>u;)if((l=i[u++])!=l)return!0}else for(;c>u;u++)if((e||u in i)&&i[u]===r)return e||u||0;return!e&&-1}};e.exports={includes:o(!0),indexOf:o(!1)}},2625:(e,t,r)=>{"use strict";var s=r(7910),n=r(5866),a=TypeError,o=Object.getOwnPropertyDescriptor,i=s&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}();e.exports=i?function(e,t){if(n(e)&&!o(e,"length").writable)throw new a("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t}},8278:(e,t,r)=>{"use strict";var s=r(2322),n=s({}.toString),a=s("".slice);e.exports=function(e){return a(n(e),8,-1)}},1038:(e,t,r)=>{"use strict";var s=r(867),n=r(8337),a=r(2457),o=r(2931);e.exports=function(e,t,r){for(var i=n(t),c=o.f,l=a.f,u=0;u<i.length;u++){var p=i[u];s(e,p)||r&&s(r,p)||c(e,p,l(t,p))}}},1781:(e,t,r)=>{"use strict";var s=r(7910),n=r(2931),a=r(5762);e.exports=s?function(e,t,r){return n.f(e,t,a(1,r))}:function(e,t,r){return e[t]=r,e}},5762:e=>{"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},4386:(e,t,r)=>{"use strict";var s=r(2003),n=r(2931),a=r(2609),o=r(9447);e.exports=function(e,t,r,i){i||(i={});var c=i.enumerable,l=void 0!==i.name?i.name:t;if(s(r)&&a(r,l,i),i.global)c?e[t]=r:o(t,r);else{try{i.unsafe?e[t]&&(c=!0):delete e[t]}catch(e){}c?e[t]=r:n.f(e,t,{value:r,enumerable:!1,configurable:!i.nonConfigurable,writable:!i.nonWritable})}return e}},9447:(e,t,r)=>{"use strict";var s=r(642),n=Object.defineProperty;e.exports=function(e,t){try{n(s,e,{value:t,configurable:!0,writable:!0})}catch(r){s[e]=t}return t}},7910:(e,t,r)=>{"use strict";var s=r(6977);e.exports=!s((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},6337:(e,t,r)=>{"use strict";var s=r(642),n=r(2480),a=s.document,o=n(a)&&n(a.createElement);e.exports=function(e){return o?a.createElement(e):{}}},3163:e=>{"use strict";var t=TypeError;e.exports=function(e){if(e>9007199254740991)throw t("Maximum allowed index exceeded");return e}},2589:e=>{"use strict";e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},8093:(e,t,r)=>{"use strict";var s=r(642).navigator,n=s&&s.userAgent;e.exports=n?String(n):""},4965:(e,t,r)=>{"use strict";var s,n,a=r(642),o=r(8093),i=a.process,c=a.Deno,l=i&&i.versions||c&&c.version,u=l&&l.v8;u&&(n=(s=u.split("."))[0]>0&&s[0]<4?1:+(s[0]+s[1])),!n&&o&&(!(s=o.match(/Edge\/(\d+)/))||s[1]>=74)&&(s=o.match(/Chrome\/(\d+)/))&&(n=+s[1]),e.exports=n},9948:(e,t,r)=>{"use strict";var s=r(642),n=r(2457).f,a=r(1781),o=r(4386),i=r(9447),c=r(1038),l=r(4866);e.exports=function(e,t){var r,u,p,h,d,f=e.target,m=e.global,g=e.stat;if(r=m?s:g?s[f]||i(f,{}):s[f]&&s[f].prototype)for(u in t){if(h=t[u],p=e.dontCallGetSet?(d=n(r,u))&&d.value:r[u],!l(m?u:f+(g?".":"#")+u,e.forced)&&void 0!==p){if(typeof h==typeof p)continue;c(h,p)}(e.sham||p&&p.sham)&&a(h,"sham",!0),o(r,u,h,e)}}},6977:e=>{"use strict";e.exports=function(e){try{return!!e()}catch(e){return!0}}},1658:(e,t,r)=>{"use strict";var s=r(6977);e.exports=!s((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},7623:(e,t,r)=>{"use strict";var s=r(1658),n=Function.prototype.call;e.exports=s?n.bind(n):function(){return n.apply(n,arguments)}},7016:(e,t,r)=>{"use strict";var s=r(7910),n=r(867),a=Function.prototype,o=s&&Object.getOwnPropertyDescriptor,i=n(a,"name"),c=i&&"something"===function(){}.name,l=i&&(!s||s&&o(a,"name").configurable);e.exports={EXISTS:i,PROPER:c,CONFIGURABLE:l}},1040:(e,t,r)=>{"use strict";var s=r(2322),n=r(3136);e.exports=function(e,t,r){try{return s(n(Object.getOwnPropertyDescriptor(e,t)[r]))}catch(e){}}},2322:(e,t,r)=>{"use strict";var s=r(1658),n=Function.prototype,a=n.call,o=s&&n.bind.bind(a,a);e.exports=s?o:function(e){return function(){return a.apply(e,arguments)}}},6297:(e,t,r)=>{"use strict";var s=r(642),n=r(2003);e.exports=function(e,t){return arguments.length<2?(r=s[e],n(r)?r:void 0):s[e]&&s[e][t];var r}},4641:e=>{"use strict";e.exports=function(e){return{iterator:e,next:e.next,done:!1}}},8396:(e,t,r)=>{"use strict";var s=r(3136),n=r(9943);e.exports=function(e,t){var r=e[t];return n(r)?void 0:s(r)}},3123:(e,t,r)=>{"use strict";var s=r(3136),n=r(3773),a=r(7623),o=r(6709),i=r(4641),c="Invalid size",l=RangeError,u=TypeError,p=Math.max,h=function(e,t){this.set=e,this.size=p(t,0),this.has=s(e.has),this.keys=s(e.keys)};h.prototype={getIterator:function(){return i(n(a(this.keys,this.set)))},includes:function(e){return a(this.has,this.set,e)}},e.exports=function(e){n(e);var t=+e.size;if(t!=t)throw new u(c);var r=o(t);if(r<0)throw new l(c);return new h(e,r)}},642:function(e){"use strict";var t=function(e){return e&&e.Math===Math&&e};e.exports=t("object"==typeof globalThis&&globalThis)||t("object"==typeof window&&window)||t("object"==typeof self&&self)||t("object"==typeof window&&window)||t("object"==typeof this&&this)||function(){return this}()||Function("return this")()},867:(e,t,r)=>{"use strict";var s=r(2322),n=r(4707),a=s({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return a(n(e),t)}},8555:e=>{"use strict";e.exports={}},2159:(e,t,r)=>{"use strict";var s=r(7910),n=r(6977),a=r(6337);e.exports=!s&&!n((function(){return 7!==Object.defineProperty(a("div"),"a",{get:function(){return 7}}).a}))},9233:(e,t,r)=>{"use strict";var s=r(2322),n=r(6977),a=r(8278),o=Object,i=s("".split);e.exports=n((function(){return!o("z").propertyIsEnumerable(0)}))?function(e){return"String"===a(e)?i(e,""):o(e)}:o},2716:(e,t,r)=>{"use strict";var s=r(2322),n=r(2003),a=r(9487),o=s(Function.toString);n(a.inspectSource)||(a.inspectSource=function(e){return o(e)}),e.exports=a.inspectSource},5147:(e,t,r)=>{"use strict";var s,n,a,o=r(204),i=r(642),c=r(2480),l=r(1781),u=r(867),p=r(9487),h=r(8777),d=r(8555),f="Object already initialized",m=i.TypeError,g=i.WeakMap;if(o||p.state){var y=p.state||(p.state=new g);y.get=y.get,y.has=y.has,y.set=y.set,s=function(e,t){if(y.has(e))throw new m(f);return t.facade=e,y.set(e,t),t},n=function(e){return y.get(e)||{}},a=function(e){return y.has(e)}}else{var v=h("state");d[v]=!0,s=function(e,t){if(u(e,v))throw new m(f);return t.facade=e,l(e,v,t),t},n=function(e){return u(e,v)?e[v]:{}},a=function(e){return u(e,v)}}e.exports={set:s,get:n,has:a,enforce:function(e){return a(e)?n(e):s(e,{})},getterFor:function(e){return function(t){var r;if(!c(t)||(r=n(t)).type!==e)throw new m("Incompatible receiver, "+e+" required");return r}}}},5866:(e,t,r)=>{"use strict";var s=r(8278);e.exports=Array.isArray||function(e){return"Array"===s(e)}},2003:e=>{"use strict";var t="object"==typeof document&&document.all;e.exports=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},4866:(e,t,r)=>{"use strict";var s=r(6977),n=r(2003),a=/#|\.prototype\./,o=function(e,t){var r=c[i(e)];return r===u||r!==l&&(n(t)?s(t):!!t)},i=o.normalize=function(e){return String(e).replace(a,".").toLowerCase()},c=o.data={},l=o.NATIVE="N",u=o.POLYFILL="P";e.exports=o},9943:e=>{"use strict";e.exports=function(e){return null==e}},2480:(e,t,r)=>{"use strict";var s=r(2003);e.exports=function(e){return"object"==typeof e?null!==e:s(e)}},957:e=>{"use strict";e.exports=!1},9895:(e,t,r)=>{"use strict";var s=r(6297),n=r(2003),a=r(8599),o=r(4150),i=Object;e.exports=o?function(e){return"symbol"==typeof e}:function(e){var t=s("Symbol");return n(t)&&a(t.prototype,i(e))}},7193:(e,t,r)=>{"use strict";var s=r(7623);e.exports=function(e,t,r){for(var n,a,o=r?e:e.iterator,i=e.next;!(n=s(i,o)).done;)if(void 0!==(a=t(n.value)))return a}},6105:(e,t,r)=>{"use strict";var s=r(7623),n=r(3773),a=r(8396);e.exports=function(e,t,r){var o,i;n(e);try{if(!(o=a(e,"return"))){if("throw"===t)throw r;return r}o=s(o,e)}catch(e){i=!0,o=e}if("throw"===t)throw r;if(i)throw o;return n(o),r}},9496:(e,t,r)=>{"use strict";var s=r(2748);e.exports=function(e){return s(e.length)}},2609:(e,t,r)=>{"use strict";var s=r(2322),n=r(6977),a=r(2003),o=r(867),i=r(7910),c=r(7016).CONFIGURABLE,l=r(2716),u=r(5147),p=u.enforce,h=u.get,d=String,f=Object.defineProperty,m=s("".slice),g=s("".replace),y=s([].join),v=i&&!n((function(){return 8!==f((function(){}),"length",{value:8}).length})),_=String(String).split("String"),b=e.exports=function(e,t,r){"Symbol("===m(d(t),0,7)&&(t="["+g(d(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(t="get "+t),r&&r.setter&&(t="set "+t),(!o(e,"name")||c&&e.name!==t)&&(i?f(e,"name",{value:t,configurable:!0}):e.name=t),v&&r&&o(r,"arity")&&e.length!==r.arity&&f(e,"length",{value:r.arity});try{r&&o(r,"constructor")&&r.constructor?i&&f(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var s=p(e);return o(s,"source")||(s.source=y(_,"string"==typeof t?t:"")),e};Function.prototype.toString=b((function(){return a(this)&&h(this).source||l(this)}),"toString")},5983:e=>{"use strict";var t=Math.ceil,r=Math.floor;e.exports=Math.trunc||function(e){var s=+e;return(s>0?r:t)(s)}},2931:(e,t,r)=>{"use strict";var s=r(7910),n=r(2159),a=r(8576),o=r(3773),i=r(8543),c=TypeError,l=Object.defineProperty,u=Object.getOwnPropertyDescriptor,p="enumerable",h="configurable",d="writable";t.f=s?a?function(e,t,r){if(o(e),t=i(t),o(r),"function"==typeof e&&"prototype"===t&&"value"in r&&d in r&&!r[d]){var s=u(e,t);s&&s[d]&&(e[t]=r.value,r={configurable:h in r?r[h]:s[h],enumerable:p in r?r[p]:s[p],writable:!1})}return l(e,t,r)}:l:function(e,t,r){if(o(e),t=i(t),o(r),n)try{return l(e,t,r)}catch(e){}if("get"in r||"set"in r)throw new c("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},2457:(e,t,r)=>{"use strict";var s=r(7910),n=r(7623),a=r(6691),o=r(5762),i=r(9351),c=r(8543),l=r(867),u=r(2159),p=Object.getOwnPropertyDescriptor;t.f=s?p:function(e,t){if(e=i(e),t=c(t),u)try{return p(e,t)}catch(e){}if(l(e,t))return o(!n(a.f,e,t),e[t])}},9038:(e,t,r)=>{"use strict";var s=r(2846),n=r(2589).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return s(e,n)}},3683:(e,t)=>{"use strict";t.f=Object.getOwnPropertySymbols},8599:(e,t,r)=>{"use strict";var s=r(2322);e.exports=s({}.isPrototypeOf)},2846:(e,t,r)=>{"use strict";var s=r(2322),n=r(867),a=r(9351),o=r(8419).indexOf,i=r(8555),c=s([].push);e.exports=function(e,t){var r,s=a(e),l=0,u=[];for(r in s)!n(i,r)&&n(s,r)&&c(u,r);for(;t.length>l;)n(s,r=t[l++])&&(~o(u,r)||c(u,r));return u}},6691:(e,t)=>{"use strict";var r={}.propertyIsEnumerable,s=Object.getOwnPropertyDescriptor,n=s&&!r.call({1:2},1);t.f=n?function(e){var t=s(this,e);return!!t&&t.enumerable}:r},6772:(e,t,r)=>{"use strict";var s=r(7623),n=r(2003),a=r(2480),o=TypeError;e.exports=function(e,t){var r,i;if("string"===t&&n(r=e.toString)&&!a(i=s(r,e)))return i;if(n(r=e.valueOf)&&!a(i=s(r,e)))return i;if("string"!==t&&n(r=e.toString)&&!a(i=s(r,e)))return i;throw new o("Can't convert object to primitive value")}},8337:(e,t,r)=>{"use strict";var s=r(6297),n=r(2322),a=r(9038),o=r(3683),i=r(3773),c=n([].concat);e.exports=s("Reflect","ownKeys")||function(e){var t=a.f(i(e)),r=o.f;return r?c(t,r(e)):t}},3384:(e,t,r)=>{"use strict";var s=r(9943),n=TypeError;e.exports=function(e){if(s(e))throw new n("Can't call method on "+e);return e}},7104:(e,t,r)=>{"use strict";var s=r(1656),n=r(7135),a=s.Set,o=s.add;e.exports=function(e){var t=new a;return n(e,(function(e){o(t,e)})),t}},3566:(e,t,r)=>{"use strict";var s=r(3422),n=r(1656),a=r(7104),o=r(9932),i=r(3123),c=r(7135),l=r(7193),u=n.has,p=n.remove;e.exports=function(e){var t=s(this),r=i(e),n=a(t);return o(t)<=r.size?c(t,(function(e){r.includes(e)&&p(n,e)})):l(r.getIterator(),(function(e){u(t,e)&&p(n,e)})),n}},1656:(e,t,r)=>{"use strict";var s=r(2322),n=Set.prototype;e.exports={Set:Set,add:s(n.add),has:s(n.has),remove:s(n.delete),proto:n}},6504:(e,t,r)=>{"use strict";var s=r(3422),n=r(1656),a=r(9932),o=r(3123),i=r(7135),c=r(7193),l=n.Set,u=n.add,p=n.has;e.exports=function(e){var t=s(this),r=o(e),n=new l;return a(t)>r.size?c(r.getIterator(),(function(e){p(t,e)&&u(n,e)})):i(t,(function(e){r.includes(e)&&u(n,e)})),n}},8819:(e,t,r)=>{"use strict";var s=r(3422),n=r(1656).has,a=r(9932),o=r(3123),i=r(7135),c=r(7193),l=r(6105);e.exports=function(e){var t=s(this),r=o(e);if(a(t)<=r.size)return!1!==i(t,(function(e){if(r.includes(e))return!1}),!0);var u=r.getIterator();return!1!==c(u,(function(e){if(n(t,e))return l(u,"normal",!1)}))}},5752:(e,t,r)=>{"use strict";var s=r(3422),n=r(9932),a=r(7135),o=r(3123);e.exports=function(e){var t=s(this),r=o(e);return!(n(t)>r.size)&&!1!==a(t,(function(e){if(!r.includes(e))return!1}),!0)}},7105:(e,t,r)=>{"use strict";var s=r(3422),n=r(1656).has,a=r(9932),o=r(3123),i=r(7193),c=r(6105);e.exports=function(e){var t=s(this),r=o(e);if(a(t)<r.size)return!1;var l=r.getIterator();return!1!==i(l,(function(e){if(!n(t,e))return c(l,"normal",!1)}))}},7135:(e,t,r)=>{"use strict";var s=r(2322),n=r(7193),a=r(1656),o=a.Set,i=a.proto,c=s(i.forEach),l=s(i.keys),u=l(new o).next;e.exports=function(e,t,r){return r?n({iterator:l(e),next:u},t):c(e,t)}},5794:(e,t,r)=>{"use strict";var s=r(6297),n=function(e){return{size:e,has:function(){return!1},keys:function(){return{next:function(){return{done:!0}}}}}};e.exports=function(e){var t=s("Set");try{(new t)[e](n(0));try{return(new t)[e](n(-1)),!1}catch(e){return!0}}catch(e){return!1}}},9932:(e,t,r)=>{"use strict";var s=r(1040),n=r(1656);e.exports=s(n.proto,"size","get")||function(e){return e.size}},6228:(e,t,r)=>{"use strict";var s=r(3422),n=r(1656),a=r(7104),o=r(3123),i=r(7193),c=n.add,l=n.has,u=n.remove;e.exports=function(e){var t=s(this),r=o(e).getIterator(),n=a(t);return i(r,(function(e){l(t,e)?u(n,e):c(n,e)})),n}},3590:(e,t,r)=>{"use strict";var s=r(3422),n=r(1656).add,a=r(7104),o=r(3123),i=r(7193);e.exports=function(e){var t=s(this),r=o(e).getIterator(),c=a(t);return i(r,(function(e){n(c,e)})),c}},8777:(e,t,r)=>{"use strict";var s=r(4335),n=r(1026),a=s("keys");e.exports=function(e){return a[e]||(a[e]=n(e))}},9487:(e,t,r)=>{"use strict";var s=r(957),n=r(642),a=r(9447),o="__core-js_shared__",i=e.exports=n[o]||a(o,{});(i.versions||(i.versions=[])).push({version:"3.38.1",mode:s?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.38.1/LICENSE",source:"https://github.com/zloirock/core-js"})},4335:(e,t,r)=>{"use strict";var s=r(9487);e.exports=function(e,t){return s[e]||(s[e]=t||{})}},789:(e,t,r)=>{"use strict";var s=r(4965),n=r(6977),a=r(642).String;e.exports=!!Object.getOwnPropertySymbols&&!n((function(){var e=Symbol("symbol detection");return!a(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&s&&s<41}))},1512:(e,t,r)=>{"use strict";var s=r(6709),n=Math.max,a=Math.min;e.exports=function(e,t){var r=s(e);return r<0?n(r+t,0):a(r,t)}},9351:(e,t,r)=>{"use strict";var s=r(9233),n=r(3384);e.exports=function(e){return s(n(e))}},6709:(e,t,r)=>{"use strict";var s=r(5983);e.exports=function(e){var t=+e;return t!=t||0===t?0:s(t)}},2748:(e,t,r)=>{"use strict";var s=r(6709),n=Math.min;e.exports=function(e){var t=s(e);return t>0?n(t,9007199254740991):0}},4707:(e,t,r)=>{"use strict";var s=r(3384),n=Object;e.exports=function(e){return n(s(e))}},4603:(e,t,r)=>{"use strict";var s=r(7623),n=r(2480),a=r(9895),o=r(8396),i=r(6772),c=r(7369),l=TypeError,u=c("toPrimitive");e.exports=function(e,t){if(!n(e)||a(e))return e;var r,c=o(e,u);if(c){if(void 0===t&&(t="default"),r=s(c,e,t),!n(r)||a(r))return r;throw new l("Can't convert object to primitive value")}return void 0===t&&(t="number"),i(e,t)}},8543:(e,t,r)=>{"use strict";var s=r(4603),n=r(9895);e.exports=function(e){var t=s(e,"string");return n(t)?t:t+""}},561:e=>{"use strict";var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},1026:(e,t,r)=>{"use strict";var s=r(2322),n=0,a=Math.random(),o=s(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+o(++n+a,36)}},4150:(e,t,r)=>{"use strict";var s=r(789);e.exports=s&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},8576:(e,t,r)=>{"use strict";var s=r(7910),n=r(6977);e.exports=s&&n((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},204:(e,t,r)=>{"use strict";var s=r(642),n=r(2003),a=s.WeakMap;e.exports=n(a)&&/native code/.test(String(a))},7369:(e,t,r)=>{"use strict";var s=r(642),n=r(4335),a=r(867),o=r(1026),i=r(789),c=r(4150),l=s.Symbol,u=n("wks"),p=c?l.for||l:l&&l.withoutSetter||o;e.exports=function(e){return a(u,e)||(u[e]=i&&a(l,e)?l[e]:p("Symbol."+e)),u[e]}},9060:(e,t,r)=>{"use strict";var s=r(9948),n=r(4707),a=r(9496),o=r(2625),i=r(3163);s({target:"Array",proto:!0,arity:1,forced:r(6977)((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}}()},{push:function(e){var t=n(this),r=a(t),s=arguments.length;i(r+s);for(var c=0;c<s;c++)t[r]=arguments[c],r++;return o(t,r),r}})},6336:(e,t,r)=>{"use strict";var s=r(9948),n=r(3566);s({target:"Set",proto:!0,real:!0,forced:!r(5794)("difference")},{difference:n})},2926:(e,t,r)=>{"use strict";var s=r(9948),n=r(6977),a=r(6504);s({target:"Set",proto:!0,real:!0,forced:!r(5794)("intersection")||n((function(){return"3,2"!==String(Array.from(new Set([1,2,3]).intersection(new Set([3,2]))))}))},{intersection:a})},887:(e,t,r)=>{"use strict";var s=r(9948),n=r(8819);s({target:"Set",proto:!0,real:!0,forced:!r(5794)("isDisjointFrom")},{isDisjointFrom:n})},3326:(e,t,r)=>{"use strict";var s=r(9948),n=r(5752);s({target:"Set",proto:!0,real:!0,forced:!r(5794)("isSubsetOf")},{isSubsetOf:n})},3893:(e,t,r)=>{"use strict";var s=r(9948),n=r(7105);s({target:"Set",proto:!0,real:!0,forced:!r(5794)("isSupersetOf")},{isSupersetOf:n})},3338:(e,t,r)=>{"use strict";var s=r(9948),n=r(6228);s({target:"Set",proto:!0,real:!0,forced:!r(5794)("symmetricDifference")},{symmetricDifference:n})},5508:(e,t,r)=>{"use strict";var s=r(9948),n=r(3590);s({target:"Set",proto:!0,real:!0,forced:!r(5794)("union")},{union:n})},4577:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(6775);function n(e,t,r){return(t=(0,s.A)(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}},7790:(e,t,r)=>{"use strict";function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)({}).hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e},s.apply(null,arguments)}r.d(t,{A:()=>s})},4021:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(4577);function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,s)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){(0,s.A)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}},4001:(e,t,r)=>{"use strict";function s(e,t){if(null==e)return{};var r={};for(var s in e)if({}.hasOwnProperty.call(e,s)){if(-1!==t.indexOf(s))continue;r[s]=e[s]}return r}r.d(t,{A:()=>s})},129:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(7278);function n(e,t){if("object"!=(0,s.A)(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=(0,s.A)(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}},6775:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(7278),n=r(129);function a(e){var t=(0,n.A)(e,"string");return"symbol"==(0,s.A)(t)?t:t+""}},7278:(e,t,r)=>{"use strict";function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}r.d(t,{A:()=>s})},3022:(e,t,r)=>{"use strict";function s(e){var t,r,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(r=s(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}r.d(t,{A:()=>n});const n=function(){for(var e,t,r=0,n="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=s(e))&&(n&&(n+=" "),n+=t);return n}},7216:(e,t,r)=>{"use strict";function s(e,t){var r,s,n,a="";for(r in e)if(void 0!==(n=e[r]))if(Array.isArray(n))for(s=0;s<n.length;s++)a&&(a+="&"),a+=encodeURIComponent(r)+"="+encodeURIComponent(n[s]);else a&&(a+="&"),a+=encodeURIComponent(r)+"="+encodeURIComponent(n);return(t||"")+a}r.d(t,{l:()=>s})}},t={};function r(s){var n=t[s];if(void 0!==n)return n.exports;var a=t[s]={exports:{}};return e[s].call(a.exports,a,a.exports,r),a.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var s in t)r.o(t,s)&&!r.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e;r.g.importScripts&&(e=r.g.location+"");var t=r.g.document;if(!e&&t&&(t.currentScript&&"SCRIPT"===t.currentScript.tagName.toUpperCase()&&(e=t.currentScript.src),!e)){var s=t.getElementsByTagName("script");if(s.length)for(var n=s.length-1;n>-1&&(!e||!/^http(s?):/.test(e));)e=s[n--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),r.p=e})(),(()=>{"use strict";var e=r(6087),t=r(1017);window.jetpackSearchConfigureInit=function(r){document.body.classList.add("folded"),(0,e.createRoot)(document.getElementById(r)).render(React.createElement(t.A,null))}})()})();