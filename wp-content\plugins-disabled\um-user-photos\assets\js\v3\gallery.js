// --------------- Uploader hooks ---------------
wp.hooks.addAction( 'um_uploader_trigger_files_limit_true', 'um-user-photos', function( $uploader ) {
	let $error = $uploader.querySelector('.um-user-photos-error');
	if ( $error ) {
		$error.classList.remove('um-display-none');
	}
});

wp.hooks.addAction( 'um_uploader_trigger_files_limit_false', 'um-user-photos', function( $uploader ) {
	let $error = $uploader.querySelector('.um-user-photos-error');
	if ( $error ) {
		$error.classList.add('um-display-none');
	}
});

wp.hooks.addAction( 'um_uploader_after_file_row_removed', 'um-user-photos', function( $uploader ) {
	if ( ! $uploader.find('.um-uploader-button').prop('disabled') ) {
		$uploader.find('.um-user-photos-error').umHide();
	}
});

wp.hooks.addFilter( 'um_uploader_data', 'um-user-photos', function( uploaderData, handler, $button ) {
	if ( 'um-user-photos-upload' !== handler ) {
		return uploaderData;
	}

	let $albumField = $button.parents('form').find('input[name="album_id"]');
	if ( $albumField.length ) {
		let albumID = $albumField.val();
		if ( albumID ) {
			uploaderData.url += '&album_id=' + albumID;
		}
	}

	return uploaderData;
});

wp.hooks.addAction( 'um_uploader_after_file_row_error', 'um-user-photos', function( $uploader, up, err, $fileRow ) {
	let $button = $uploader.find( '.um-uploader-button' );
	let handler = $button.data( 'handler' );
	if ( 'um-user-photos-upload' !== handler ) {
		return;
	}

	$fileRow.find('.um-uploader-file-more-info, .um-toggle-block, .um-uploaded-value, .um-uploaded-filename, .um-uploaded-value-hash, .um-photo-order').remove();
	let $fileList = $fileRow.parents('.um-uploader-filelist');
	$fileList.trigger( 'sortupdate' );
	$fileList.sortable( 'refreshPositions' );
});

wp.hooks.addFilter( 'um_uploader_file_upload_failed', 'um-user-photos', function( preventDefault, $button, up, file, response ) {
	let handler = $button.data( 'handler' );
	if ( 'um-user-photos-upload' !== handler ) {
		return preventDefault;
	}

	if ( response.info && response.OK === 0 ) {
		let $uploader = $button.parents( '.um-uploader' );
		let $fileList  = $uploader.find( '.um-uploader-filelist' );
		if ( $fileList.length ) {
			let $fileRow = $fileList.find('#' + file.id);
			$fileRow.find('.um-supporting-text').text( response.info );
			$fileRow.addClass('um-upload-failed');
			$fileRow.find('.um-progress-bar-wrapper').remove();
			$fileRow.find('.um-uploader-file-more-info, .um-toggle-block, .um-uploaded-value, .um-uploaded-filename, .um-uploaded-value-hash, .um-photo-order').remove();

			$fileList.trigger( 'sortupdate' );
			$fileList.sortable( 'refreshPositions' );
		}
	}

	return preventDefault;
});

wp.hooks.addAction( 'um_uploader_files_added', 'um-user-photos', function( $uploader, up, files ) {
	let $button = $uploader.find( '.um-uploader-button' );
	let handler = $button.data( 'handler' );
	if ( 'um-user-photos-upload' !== handler ) {
		return;
	}

	let $fileList = $uploader.find( '.um-uploader-filelist' );

	if ( $fileList.length && $fileList.hasClass( 'um-uploader-filelist-sortable' ) ) {
		$fileList.sortable({
			create: function(event, ui){
				$fileList.find( '.um-uploader-file:not(.um-upload-failed)' ).each( function ( i ) {
					jQuery( this ).find( '.um-photo-order' ).val( i + 1 );
				});
			},
			update: function(event, ui){
				$fileList.find( '.um-uploader-file:not(.um-upload-failed)' ).each( function ( i ) {
					jQuery( this ).find( '.um-photo-order' ).val( i + 1 );
				});
			}
		});

		$fileList.on( 'sortupdate', function( event, ui ) {
			$fileList.find( '.um-uploader-file:not(.um-upload-failed)' ).each( function ( i ) {
				jQuery( this ).find( '.um-photo-order' ).val( i + 1 );
			});
		} );
		$fileList.trigger( 'sortupdate' );

		$fileList.sortable( 'refreshPositions' );
	}
});

wp.hooks.addFilter( 'um_uploader_init_sortable_filelist', 'um-user-photos', function( preventDefault, $fileList, handler ) {
	if ( 'um-user-photos-upload' !== handler ) {
		return preventDefault;
	}

	$fileList.sortable({
		create: function(event, ui){
			$fileList.find( '.um-uploader-file:not(.um-upload-failed)' ).each( function ( i ) {
				jQuery( this ).find( '.um-photo-order' ).val( i + 1 );
			});
		},
		update: function(event, ui){
			$fileList.find( '.um-uploader-file:not(.um-upload-failed)' ).each( function ( i ) {
				jQuery( this ).find( '.um-photo-order' ).val( i + 1 );
			});
		}
	});

	$fileList.on( 'sortupdate', function( event, ui ) {
		$fileList.find( '.um-uploader-file:not(.um-upload-failed)' ).each( function ( i ) {
			jQuery( this ).find( '.um-photo-order' ).val( i + 1 );
		});
	} );
	$fileList.trigger( 'sortupdate' );

	$fileList.sortable( 'refreshPositions' );
});

wp.hooks.addFilter( 'um_uploader_file_filtered', 'um-user-photos', function( preventDefault, $button, up, file ) {
	let handler = $button.data( 'handler' );
	if ( 'um-user-photos-upload' !== handler ) {
		return preventDefault;
	}

	let $uploader = $button.parents( '.um-uploader' );
	let $fileList = $uploader.find( '.um-uploader-filelist' );

	if ( $fileList.length ) {
		$fileList.umShow();

		// flush files list if there is only 1 file can be uploaded.
		if ( ! up.getOption( 'multi_selection' ) ) {
			$fileList.find( '.um-uploader-file' ).each(
				function ( u, item ) {
					up.removeFile( item.id );
				}
			);
		}

		let fileRow = $fileList.find( '#' + file.id );

		if ( ! fileRow.length ) {
			let $cloned = $uploader.find( '.um-uploader-file-placeholder' ).clone().addClass( 'um-uploader-file' ).removeClass( 'um-uploader-file-placeholder um-display-none' ).attr( 'id',file.id );

			let objSelectors = [
				'.um-uploaded-value',
				'.um-uploaded-filename',
				'.um-uploaded-value-hash',
				'.um-photo-order',
				'.um-photo-title',
				'.um-photo-link',
				'.um-photo-caption',
				'.um-disable-comments-checkbox',
				'.um-cover-photo-checkbox',
			];

			for ( let i = 0; i < objSelectors.length; i++ ) {
				$cloned.find( objSelectors[i] ).prop( 'disabled', false );

				let name = $cloned.find( objSelectors[i] ).attr( 'name' );
				if ( name ) {
					name = name.replace( '\{\{\{file_id\}\}\}', file.id );
					$cloned.find( objSelectors[i] ).attr( 'name', name );
				}

				let id = $cloned.find( objSelectors[i] ).attr( 'id' );
				if ( id ) {
					id = id.replace( '\{\{\{file_id\}\}\}', file.id );
					$cloned.find( objSelectors[i] ).attr( 'id', id );
				}

				let labelFor = $cloned.find( objSelectors[i] ).siblings( 'label' ).attr( 'for' );
				if ( labelFor ) {
					labelFor = labelFor.replace( '\{\{\{file_id\}\}\}', file.id );
					$cloned.find( objSelectors[i] ).siblings( 'label' ).attr( 'for', labelFor );
				}
			}

			// move new images to the top of the list
			$fileList.prepend( $cloned );

			fileRow = $fileList.find( '#' + file.id );

			fileRow.find( '.um-uploader-file-preview' ).attr( 'title', file.name );
			let img    = new moxie.image.Image();
			img.onload = function() {
				this.embed(
					fileRow.find( '.um-uploader-file-preview' ).get( 0 ),
					{
						width: 100,
						height: 100,
						crop: true
					}
				);
			};

			img.load( file.getSource() );

			let toggleClass = fileRow.find( '.um-uploader-file-more-info' ).data( 'um-toggle' );
			fileRow.find( '.um-uploader-file-more-info' ).data( 'um-toggle', toggleClass + '-' + file.id );
			fileRow.find( '.um-toggle-block' ).removeClass( 'um-image-data' ).addClass( toggleClass.substring( 1 ) + '-' + file.id );

			fileRow.find( '.um-uploader-file-name' ).text( file.name );
			fileRow.find( '.um-photo-title' ).val( file.name );
			fileRow.find( '.um-supporting-text' ).text( plupload.formatSize( file.size ) );
			fileRow.find( '.um-toggle-block-inner' ).removeClass( 'um-visible' );
		}
	}

	// mark first photo as cover if there aren't any other covers
	if ( 0 === jQuery( '.um-cover-photo-checkbox:checked' ).length ) {
		$fileList.find( '#' + file.id ).find( '.um-cover-photo-checkbox' ).prop( 'checked', true );
	}

	return true;
} );

wp.hooks.addFilter( 'um_uploader_file_uploaded', 'um-user-photos', function( preventDefault, $button, up, file, response ) {
	let handler = $button.data( 'handler' );
	if ( 'um-user-photos-upload' !== handler ) {
		return preventDefault;
	}

	let $uploader = $button.parents( '.um-uploader' );
	let $fileList = $uploader.find( '.um-uploader-filelist' );

	if ( $fileList.length ) {
		let fileRow = $fileList.find( '#' + file.id );
		fileRow.find( '.um-uploaded-value' ).attr( 'name', 'album_photos[' + file.id + '][path]' ).val( response.data[0].name_saved );
		fileRow.find( '.um-uploaded-filename' ).attr( 'name', 'album_photos[' + file.id + '][filename]' ).val( response.data[0].name_loaded );
		fileRow.find( '.um-uploaded-value-hash' ).attr( 'name', 'album_photos[' + file.id + '][hash]' ).val( response.data[0].hash );
	}

	return null;
});

wp.hooks.addAction( 'um_uploader_after_file_row_removed', 'um-user-photos', function( $uploader ) {
	let $button = $uploader.find( '.um-uploader-button' );
	let handler = $button.data( 'handler' );
	if ( 'um-user-photos-upload' !== handler ) {
		return;
	}

	let $fileList = $uploader.find( '.um-uploader-filelist' );
	if ( $fileList.length && $fileList.hasClass( 'um-uploader-filelist-sortable' ) ) {
		$fileList.sortable({
			create: function(event, ui){
				$fileList.find( '.um-uploader-file:not(.um-upload-failed)' ).each( function ( i ) {
					jQuery( this ).find( '.um-photo-order' ).val( i + 1 );
				});
			},
			update: function(event, ui){
				$fileList.find( '.um-uploader-file:not(.um-upload-failed)' ).each( function ( i ) {
					jQuery( this ).find( '.um-photo-order' ).val( i + 1 );
				});
			}
		});

		$fileList.on( 'sortupdate', function( event, ui ) {
			$fileList.find( '.um-uploader-file:not(.um-upload-failed)' ).each( function ( i ) {
				jQuery( this ).find( '.um-photo-order' ).val( i + 1 );
			});
		} );
		$fileList.trigger( 'sortupdate' );

		$fileList.sortable( 'refreshPositions' );
	}

	if ( 0 === $fileList.find( '.um-cover-photo-checkbox:checked' ).length ) {
		let el = $fileList.find( '.um-uploader-file' );
		el.first().find( '.um-cover-photo-checkbox' ).prop( 'checked', true );
	}
});

// --------------- Modal hooks ---------------
wp.hooks.addAction(
	'um-modal-before-close',
	'um-user-photos',
	function ( $modal ) {
		if ( $modal.find( '.um-user-photos-widget-image' ).length ) {
			let params = new URLSearchParams( window.location.search );
			if ( params.has( 'photo_id' ) ) {
				UM.frontend.url.deleteURLSearchParam( 'photo_id' );
			}
		}
	},
	10
);

// --------------- Toggle hooks ---------------
wp.hooks.addFilter( 'um_toggle_block', 'um-user-photos', function( $toggleBlock, $toggleButton ) {
	if ( $toggleButton.hasClass( 'um-user-photos-comments-toggle' ) ) {
		// Change toggle text.
		let textAfter  = $toggleButton.data( 'toggle-text' );
		let textBefore = $toggleButton.find( '.um-user-photos-comment-text' ).text();
		$toggleButton.data( 'toggle-text',textBefore );
		$toggleButton.find( '.um-user-photos-comment-text' ).text( textAfter )
	}

	return $toggleBlock;
});

/**
 * Get album layout.
 *
 * @param wrapper
 * @param albumID
 * @param nonce
 * @param isProfileTab
 */
function um_get_album( wrapper, albumID, nonce, isProfileTab ) {
	wp.ajax.send(
		'um_user_photos_get_single_album_view',
		{
			data: {
				id: albumID,
				is_profile_tab: isProfileTab,
				_wpnonce: nonce
			},
			success: function( response ) {
				wrapper.replaceWith( response );
				UM.frontend.image.lazyload.init();
				UM.frontend.dropdown.init();
			},
			error: function( data ) {
				console.log( data );
			}
		}
	);
}

jQuery( document ).ready( function( $ ) {
	// --------------- Albums page ---------------
	// Open "Add new" album form.
	$( document.body ).on( 'click', '.um-user-photos-new-album', function ( e ) {
		e.preventDefault();
		let $btn    = $( this );
		let nonce   = $btn.data( 'nonce' );
		let wrapper = $( '.um-user-photos-gallery' );

		$btn.prop( 'disabled', true ).umHide();
		wrapper.find( '.um-user-photos-albums, .um-user-photos-load-more-albums' ).umHide();
		wrapper.find( '> .um-supporting-text' ).umHide(); // `Nothing to display` text
		wrapper.find( '.um-user-photos-albums-loader' ).umShow();
		let isProfileTab = wrapper.parents( '.um-profile' ).length;

		wp.ajax.send(
			'um_user_photos_new_album',
			{
				data: {
					_wpnonce: nonce,
					is_profile_tab: isProfileTab,
				},
				success: function( response ) {
					wrapper.replaceWith( response );
					UM.frontend.responsive.setClass();
					UM.frontend.uploader.init();
					UM.frontend.choices.init();

					let params = new URLSearchParams( window.location.search );
					if ( ! params.has( 'gallery_action' ) || 'add_album' !== params.get( 'gallery_action' ) ) {
						UM.frontend.url.setURLSearchParam( 'gallery_action', 'add_album' );
					}
				},
				error: function( data ) {
					$btn.prop( 'disabled', false ).umShow();
					wrapper.find( '.um-user-photos-albums' ).umShow();
					wrapper.find( '.um-user-photos-albums-loader' ).umHide();
					console.log( data );
				}
			}
		);
	});
	// Open single album.
	$( document.body ).on( 'click', '.um-user-photos-album-block:not(.um-user-photos-album-direct-link)', function ( e ) {
		e.preventDefault();
		let album_id = parseInt( $( this ).data( 'id' ) );

		let wrapper = $( this ).parents( '.um-user-photos-gallery' );
		let nonce   = $( this ).data( 'nonce' );

		let isProfileTab = wrapper.parents( '.um-profile' ).length;

		wrapper.find( '.um-user-photos-albums-loader' ).umShow();
		wrapper.find( '.um-user-photos-albums, .um-user-photos-new-album, .um-user-photos-load-more-albums' ).umHide();

		um_get_album( wrapper, album_id, nonce, isProfileTab );

		let params = new URLSearchParams( window.location.search );
		if ( ! params.has( 'album_id' ) || album_id !== parseInt( params.get( 'album_id' ) ) ) {
			UM.frontend.url.setURLSearchParam( 'album_id', album_id );
		}
	});

	// --------------- Gallery page ---------------
	// Albums grid pagination.
	$( document.body ).on( 'click', '.um-user-photos-load-more-albums', function ( e ) {
		e.preventDefault();
		let $btn = $( this );
		let $loader   = $btn.siblings( '.um-user-photos-loader' );
		let profileID = $btn.data( 'profile' );
		let prePage     = $btn.data( 'per_page' );
		let nextPage  = parseInt( $btn.data( 'page' ) ) + 1;
		let $grid     = $btn.siblings( '.um-user-photos-albums' );
		let nonce     = jQuery( this ).data( 'wpnonce' );

		$loader.umShow();
		$btn.prop( 'disabled', true );

		wp.ajax.send(
			'um_user_photos_albums_load_more',
			{
				data: {
					profile: profileID,
					last_id: $btn.data('last_id'),
					per_page: prePage,
					_wpnonce: nonce
				},
				success: function( response ) {
					$loader.umHide();
					$btn.prop( 'disabled', false );

					if ( '' === response.content ) {
						$btn.remove();
					} else {
						$grid.append( response.content );

						UM.frontend.image.lazyload.init();

						let totalPages = $btn.data('pages')*1;
						if ( nextPage === totalPages ) {
							$btn.remove();
						} else {
							$btn.data( 'page', nextPage );
							$btn.data('last_id', response.last_id );
						}
					}
				},
				error: function( data ) {
					console.log( data );
					$loader.umHide();
					$btn.prop( 'disabled', false );
				}
			}
		);
	});

	// --------------- Single album page ---------------
	// Photos grid pagination.
	$( document.body ).on( 'click', '.um-user-photos-load-more-photos', function ( e ) {
		e.preventDefault();
		let $btn = $( this );
		let $loader   = $btn.siblings( '.um-user-photos-loader' );
		let profileID = $btn.data( 'profile' );
		let total     = $btn.data( 'total' );
		let nextPage  = parseInt( $btn.data( 'current_page' ) ) + 1;
		let $grid     = $btn.siblings( '.um-user-photos-grid' );
		let nonce     = jQuery( this ).data( 'wpnonce' );

		$loader.umShow();
		$btn.prop( 'disabled', true );

		wp.ajax.send(
			'um_user_photos_load_more',
			{
				data: {
					profile: profileID,
					page: nextPage,
					_wpnonce: nonce
				},
				success: function( response ) {
					$loader.umHide();
					$btn.prop( 'disabled', false ).data( 'current_page', nextPage );

					if ( '' === response ) {
						$btn.remove();
					} else {
						$grid.append( response );
						let imagesCount = $grid.find( '.um-user-photos-image-block' ).length;
						if ( parseInt( total ) <= parseInt( imagesCount ) ) {
							$btn.remove();
						}
						$grid.data( 'count', imagesCount );
					}

					UM.frontend.image.lazyload.init();
				},
				error: function( data ) {
					console.log( data );
					$loader.umHide();
					$btn.prop( 'disabled', false );
				}
			}
		);
	});
	// Back to gallery.
	$( document.body ).on( 'click', '.um-user-photos-back-to-gallery', function ( e ) {
		e.preventDefault();
		let userID = $( this ).data( 'profile' );
		let nonce  = $( this ).data( 'nonce' );

		let isProfileTab = $( this ).parents( '.um-profile' ).length;

		let wrapper;
		let searchAttr;
		if ( $( this ).parents( '.um-user-photos-single-album' ).length ) {
			wrapper = $( this ).parents( '.um-user-photos-single-album' );
			wrapper.find( '.um-user-photos-album-head, .um-user-photos-grid, .um-supporting-text' ).umHide();
			searchAttr = 'album_id';
		} else {
			wrapper = $( this ).parents( '.um-user-photos-add-album' );
			wrapper.find( '.um-user-photos-album-form, .um-user-photos-form-title' ).umHide();
			searchAttr = 'gallery_action';
		}

		wrapper.find( '.um-user-photos-albums-loader' ).umShow();
		wp.ajax.send(
			'um_user_photos_get_gallery',
			{
				data: {
					user_id: userID,
					is_profile_tab: isProfileTab,
					_wpnonce: nonce
				},
				success: function( response ) {
					wrapper.replaceWith( response );
					UM.frontend.responsive.setClass();

					UM.frontend.image.lazyload.init();

					let params = new URLSearchParams( window.location.search );
					if ( params.has( searchAttr ) ) {
						UM.frontend.url.deleteURLSearchParam( searchAttr );
					}
				},
				error: function( data ) {
					console.log( data );
				}
			}
		);
	});
	// Open "Edit" album form.
	$( document.body ).on( 'click', '.um-user-photos-edit-album', function ( e ) {
		e.preventDefault();
		let album = $( this ).data( 'id' );
		let nonce = $( this ).data( 'nonce' );

		let wrapper      = $( '.um-user-photos-single-album[data-album_id="' + album + '"]' );
		let isProfileTab = wrapper.parents( '.um-profile' ).length;

		wrapper.find( '.um-user-photos-albums-loader' ).umShow();
		wrapper.find( '.um-user-photos-album-head, .um-user-photos-grid, .um-supporting-text' ).umHide();

		wp.ajax.send(
			'um_user_photos_edit_album',
			{
				data: {
					album_id: album,
					_wpnonce: nonce,
					is_profile_tab: isProfileTab,
				},
				success: function( response ) {
					wrapper.replaceWith( response );
					UM.frontend.responsive.setClass();
					UM.frontend.uploader.init();
					UM.frontend.choices.init();

					let params = new URLSearchParams( window.location.search );
					if ( ! params.has( 'gallery_action' ) || 'edit_album' !== params.get( 'gallery_action' ) ) {
						UM.frontend.url.setURLSearchParam( 'gallery_action', 'edit_album' );
					}
				},
				error: function( data ) {
					wrapper.find ('.um-user-photos-albums-loader' ).umHide();
					wrapper.find( '.um-user-photos-album-head, .um-user-photos-grid, .um-supporting-text' ).umShow();
					console.log( data );
				}
			}
		);
	});
	// Delete album.
	$( document.body ).on( 'click', '.um-user-photos-delete-album', function ( e ) {
		e.preventDefault()
		let deleteLink = $( this );
		let albumID    = deleteLink.data( 'id' );
		let nonce      = deleteLink.data( 'nonce' );

		let wrapper = $( '.um-user-photos-single-album[data-album_id="' + albumID + '"]' );
		$.um_confirm(
			{
				title   : deleteLink.attr( 'title' ),
				message : deleteLink.data( 'confirm' ),
				onYes: function() {
					wrapper.find( '.um-user-photos-albums-loader' ).umShow();
					wrapper.find( '.um-user-photos-album-head, .um-user-photos-grid, .um-supporting-text' ).umHide();
					wp.ajax.send(
						'um_user_photos_delete_album',
						{
							data: {
								id: albumID,
								_wpnonce: nonce
							},
							success: function() {
								let params = new URLSearchParams( window.location.search );
								if ( params.has( 'album_id' ) ) {
									UM.frontend.url.deleteURLSearchParam( 'album_id', true );
								}
							},
							error: function( data ) {
								wrapper.find( '.um-user-photos-albums-loader' ).umHide();
								wrapper.find( '.um-user-photos-album-head, .um-user-photos-grid, .um-supporting-text' ).umShow();
							}
						}
					);
				},
				object: this
			}
		);
	});

	// --------------- Add/Edit album form ---------------
	// add/edit album.
	$( document.body ).on( 'click', '.um-gallery-album-update', function ( e ) {
		e.preventDefault();
		let $button = $( this );
		let $form = $button.parents( 'form' );

		let action, form_id;
		if ( 'um-album-add' === $button.attr('id') ) {
			action  = 'create_um_user_photos_album';
			form_id = 'um_user_photos_create_album';
		} else {
			action  = 'update_um_user_photos_album';
			form_id = 'um_user_photos_edit_album';
		}

		$form.find( '.um-field-error' ).remove();
		$form.find( 'input' ).removeClass( 'um-error' );
		$form.find( '.um-uploader-file-preview-error' ).umHide();

		$form.find('.um-alert.um-user-photos-album-form-submission-error').remove();

		if ( $( '.um-upload-error' ).length ) {
			$( '.um-uploader-file, .um-user-photos-photo' ).removeClass( 'um-upload-error' );
		}

		$button.siblings( '.um-user-photos-loader' ).umShow();
		$button.siblings( '.um-link' ).addClass('um-link-disabled');
		$button.prop( 'disabled', true );

		let formObjData = UM.common.form.vanillaSerialize( form_id );

		wp.ajax.send(
			action,
			{
				data: formObjData,
				success: function (response) {
					let params = new URLSearchParams( window.location.search );
					if ( params.has( 'gallery_action' ) ) {
						UM.frontend.url.deleteURLSearchParam( 'gallery_action', true );
					}
				},
				error: function( data ) {
					let image, el;
					$button.siblings( '.um-user-photos-loader' ).umHide();
					$button.siblings( '.um-link' ).removeClass('um-link-disabled');
					$button.prop( 'disabled', false );

					let response_div;

					if ( data.field ) {
						el = $( '#' + data.field );
						el.addClass( 'um-error' );
						el.after( '<p class="um-field-hint um-field-error" id="um-error-for-' + data.field + '">' + data.message + '</p>' );
						response_div = el;
					} else if ( data.image ) {
						if ( $( '#album-photo-' + data.image.id ).length ) {
							image = $( '#album-photo-' + data.image.id );
						} else {
							image = $( '#' + data.image.id );
						}
						if ( data.image.field ) {
							el = image.find( '#' + data.image.field );
							el.addClass( 'um-error' );
							el.after( '<p class="um-field-hint um-field-error" id="um-error-for-' + data.image.field + '">' + data.message + '</p>' );
						} else {
							image.find( '.um-uploader-file-preview-error' ).text( data.message ).umShow();
						}
						image.addClass( 'um-upload-error' );
						response_div = image;
					} else {
						$form.prepend( data.message );
						response_div = $form.find('.um-alert.um-user-photos-album-form-submission-error');
					}

					$( 'html, body' ).animate(
						{
							scrollTop: response_div.offset().top - 30
						},
						1000
					);
				}
			}
		);
	});
	// Cover image checkbox.
	$( document.body ).on( 'click', '.um-cover-photo-checkbox', function () {
		$( this ).parents( 'form' ).find( '.um-cover-photo-checkbox' ).prop( 'checked', false );
		$( this ).prop( 'checked', true );
	});
	// Delete photo from album on edit album screen.
	$( document.body ).on( 'click', '.um-user-photos-delete-photo', function ( e ) {
		e.preventDefault();

		if ( ! confirm( wp.i18n.__( 'Are you sure that you want to delete this photo?', 'um-user-photos' ) ) ) {
			return false;
		}

		let $uploader = $( this ).parents( '.um-uploader' );
		let $fileList = $uploader.find( '.um-uploader-filelist' );

		let uploaderObj = UM.frontend.uploaders[ $uploader.data('plupload') ];

		let button = uploaderObj.getOption( 'browse_button' )[0];

		if ( uploaderObj.getOption( 'filters' ).um_files_limit ) {
			let filters = uploaderObj.getOption( 'filters' );
			filters.um_files_limit = parseInt( filters.um_files_limit ) + 1;
			uploaderObj.setOption( 'filters', filters );

			uploaderObj.refresh();
		} else if ( button.hasAttribute('disabled') ) {
			let filters = uploaderObj.getOption( 'filters' );
			filters.um_files_limit = 1;
			uploaderObj.setOption( 'filters', filters );

			uploaderObj.refresh();
		}

		button.removeAttribute('disabled');
		let dropZone = uploaderObj.getOption( 'drop_element' )[0];
		if ( dropZone ) {
			dropZone.classList.remove('um-dropzone-disabled');
			let uploadLink = dropZone.querySelector( '.um-upload-link' );
			if ( uploadLink ) {
				uploadLink.classList.remove('um-link-disabled');
			}
		}

		$uploader.find('.um-user-photos-error').umHide();

		let $fileRow = $( $( this ).data( 'delete_photo' ) );
		$fileRow.remove();

		if ( 0 === $fileList.find( '.um-cover-photo-checkbox:checked' ).length ) {
			let el = $fileList.find( '.um-uploader-file' );
			el.first().find( '.um-cover-photo-checkbox' ).prop( 'checked', true );
		}

		if ( $fileList.length && $fileList.hasClass( 'um-uploader-filelist-sortable' ) ) {
			$fileList.sortable({
				create: function(event, ui){
					$fileList.find( '.um-uploader-file:not(.um-upload-failed)' ).each( function ( i ) {
						jQuery( this ).find( '.um-photo-order' ).val( i + 1 );
					});
				},
				update: function(event, ui){
					$fileList.find( '.um-uploader-file:not(.um-upload-failed)' ).each( function ( i ) {
						jQuery( this ).find( '.um-photo-order' ).val( i + 1 );
					});
				}
			});

			$fileList.on( 'sortupdate', function( event, ui ) {
				$fileList.find( '.um-uploader-file:not(.um-upload-failed)' ).each( function ( i ) {
					jQuery( this ).find( '.um-photo-order' ).val( i + 1 );
				});
			} );
			$fileList.trigger( 'sortupdate' );

			$fileList.sortable( 'refreshPositions' );
		}
	});
	// Back to album.
	$( document.body ).on( 'click', '.um-user-photos-back-to-album', function ( e ) {
		e.preventDefault();
		let album_id = $( this ).data( 'album' );
		let nonce    = $( this ).data( 'nonce' );

		let isProfileTab = $( this ).parents( '.um-profile' ).length;

		let wrapper = $( this ).parents( '.um-user-photos-edit-album-form' );
		wrapper.find( '.um-user-photos-album-form, .um-user-photos-form-title' ).umHide();
		wrapper.find( '.um-user-photos-albums-loader' ).umShow();

		um_get_album( wrapper, album_id, nonce, isProfileTab );

		let params = new URLSearchParams( window.location.search );
		if ( params.has( 'gallery_action' ) ) {
			UM.frontend.url.deleteURLSearchParam('gallery_action');
		}
	});

	// --------------- Edit image form ---------------
	// edit image modal open.
	$( document.body ).on( 'click', '.um-user-photos-edit-image', function ( e ) {
		e.preventDefault();

		let nonce = $( this ).data( 'nonce' );

		wp.ajax.send(
			'um_user_photos_edit_photo_modal',
			{
				data: {
					image_id: $( this ).data( 'photo-id' ),
					_wpnonce: nonce
				},
				success: function( response ) {
					let settings = {
						classes:  'um-user-photos-edit-image-modal',
						duration: 400,
						footer:   '',
						header:   wp.i18n.__( 'Edit Image', 'um-user-photos' ),
						size:     'normal',
						content:  response
					};

					UM.modal.addModal( settings, null );
				},
				error: function( data ) {
					console.log( data );
				}
			}
		);
	});
	// edit image handler.
	$( document.body ).on( 'click', '#um-user-photos-image-update-btn', function ( e ) {
		e.preventDefault();

		let $button = $( this );
		let formObj = $button.parents( 'form' );
		let modal = $( '.um-modal-body' );

		modal.find( '.um-field-error' ).remove();
		modal.find( 'input' ).removeClass( 'um-error' );

		$button.siblings( '.um-user-photos-loader' ).umShow();
		$button.prop( 'disabled', true );

		formObj.find('.um-alert').remove();

		let formObjData = UM.common.form.vanillaSerialize( 'um-user-photos-edit-image' );

		wp.ajax.send(
			'um_user_photos_image_update',
			{
				data: formObjData,
				success: function( response ) {
					$button.siblings( '.um-user-photos-loader' ).umHide();
					$button.prop( 'disabled', false );

					formObj.prepend( response.message );

					let image_block = $( '#um-user-photos-image-block-' + formObjData.id );

					image_block.find( 'a.um-user-photos-open-single-photo-modal' ).attr( 'title', response.title );
					image_block.find( 'img' ).attr( 'alt', response.title );

					if ( response.link ) {
						image_block.find( '.um-user-photo-related-link' ).attr('href', response.link).removeClass('um-hidden');
					} else {
						image_block.find( '.um-user-photo-related-link' ).attr('href', response.link).addClass('um-hidden');
					}

					setTimeout(
						function () {
							UM.modal.close();
						},
						1000
					);
					// close modal after success response
				},
				error: function( response ) {
					$button.siblings( '.um-user-photos-loader' ).umHide();
					$button.prop( 'disabled', false );

					if ( response.field ) {
						let el = $( '#' + response.field );
						el.addClass( 'um-error' ).after( '<p class="um-field-hint um-field-error" id="um-error-for-' + response.field + '">' + response.message + '</p>' );
					} else {
						formObj.prepend( response );
					}

					modal.animate(
						{
							scrollTop: modal.scrollTop()
						},
						1000
					);
				}
			}
		);
	});

	// --------------- Single image popup ---------------
	// Open modal with photo and comments section.
	$( document.body ).on( 'click', '.um-user-photos-open-single-photo-modal',function( e ){
		e.preventDefault();

		let btn      = $( this );
		let nonce    = btn.data( 'wpnonce' );
		let imageID = parseInt( btn.data( 'id' ) );
		let count    = $( '.um-user-photos-albums-counter' ).data( 'count' );
		let context  = btn.parents( '.um-user-photos-single-album' ).length ? 'album' : 'gallery';

		wp.ajax.send(
			'um_user_photos_get_comment_section',
			{
				data: {
					_wpnonce: nonce,
					image_id: imageID,
					context: context,
				},
				success: function( response ) {
					let settings = {
						classes:  'um-user-photos-single-photo-modal',
						duration: 200,
						footer:   '',
						header:   '',
						size:     'full-width',
						content:  response
					};

					UM.modal.addModal( settings, null );
					let params = new URLSearchParams( window.location.search );
					if ( ! params.has( 'photo_id' ) || imageID !== parseInt( params.get( 'photo_id' ) ) ) {
						UM.frontend.url.setURLSearchParam( 'photo_id', imageID );
					}

					if ( 2 > parseInt( count ) ) {
						$( '.um-photo-modal-prev, .um-photo-modal-next' ).hide();
					}

					um_init_new_dropdown();
					UM.frontend.image.lazyload.init();
				},
				error: function( data ) {
					console.log( data );
				}
			}
		);
	});
	// Copy link to photo.
	$( document.body ).on( 'click', '.um-user-photos-metadata-date', function( e ){
		e.preventDefault();
		let currentUrl  = window.location.href;
		let tempInput   = document.createElement( 'input' );
		tempInput.value = currentUrl;
		document.body.appendChild( tempInput );
		tempInput.select();
		document.execCommand( 'copy' );
		document.body.removeChild( tempInput );
	});
	// like/unlike photo.
	$( document.body ).on( 'click', '.um-user-photos-like', function( e ) {
		e.preventDefault();

		let $btn   = $( this );
		let $modal = $btn.parents( '.um-user-photos-widget' );
		let postID = $btn.data( 'id' );

		let action    = $btn.hasClass( 'active' ) ? 'unlike' : 'like';
		let actionRev = $btn.hasClass( 'active' ) ? 'like' : 'unlike';
		let nonce     = $btn.data( action + 'nonce' );
		let title     = $btn.data( actionRev + '_title' );

		let $count   = $modal.find( '.um-user-photos-post-likes-count .um-badge' );
		let $avatars = $modal.find( '.um-user-photos-post-likes-avatars' );

		$btn.prop( 'disabled', true );
		wp.ajax.send(
			'um_user_photos_' + action + '_photo',
			{
				data: {
					postid: postID,
					_wpnonce: nonce,
				},
				success: function (response) {
					$btn.prop( 'disabled', false ).attr( 'title', title ).toggleClass( 'active' );
					$count.text( response.count );
					let disabledProp = parseInt( response.count ) <= 0;
					$modal.find( '.um-user-photos-show-likes' ).prop( 'disabled', disabledProp );
					$avatars.html( response.content );
					if ( disabledProp ) {
						$avatars.umHide();
					} else {
						$avatars.umShow();
					}
				},
				error: function (response) {
					$btn.prop( 'disabled', false );
					console.log( response );
				}
			}
		);
	});
	// Show photo likes.
	$( document.body ).on( 'click', '.um-user-photos-show-likes', function( e ) {
		e.preventDefault();

		let $btn    = $( this );
		let nonce = $btn.data( 'wpnonce' );

		wp.ajax.send(
			'get_um_user_photo_likes',
			{
				data: {
					image_id: $btn.data( 'id' ),
					_wpnonce: nonce,
				},
				success: function( response ) {
					let settings = {
						classes:  'um-user-photos-likes-modal',
						duration: 400,
						footer:   '',
						header:   wp.i18n.__( 'Photo likes', 'um-user-photos' ),
						size:     'normal',
						content:  response.content
					};

					UM.modal.addModal( settings, null );
				},
				error: function( data ) {
					console.log( data );
				}
			}
		);

	});
	// Enable comment when type in textarea.
	$( document.body ).on( 'change keyup', '.um-user-photos-comment-textarea', function() {
		let $commentBtn = $( this ).parents( '.um-user-photos-comment-form' ).find( '.um-user-photos-comment-post' );
		if ( ! $commentBtn.length ) {
			// then check if related to the edit comment form
			$commentBtn = $( this ).parents( '.um-user-photos-comment-edit-section' ).find( '.um-user-photos-comment-update-btn' );
		}

		if ( $commentBtn.length ) {
			let disabledProp = ! $( this ).val().trim().length > 0;
			$commentBtn.prop( 'disabled', disabledProp );
		}
	});
	// Submit comment.
	$( document.body ).on( 'click', '.um-user-photos-comment-post', function( e ) {
		e.preventDefault();
		let $btn          = $( this );
		let $textarea     = $btn.parents( '.um-user-photos-comment-form' ).find( '.um-user-photos-comment-textarea' );
		let $commentsList = $textarea.parents( '.um-user-photos-widget' ).find( '.um-user-photos-comments-loop' );
		let $count        = $btn.parents( '.um-user-photos-widget' ).find( '.um-button-content .um-badge' );

		let comment = $textarea.val();
		let postid  = $btn.data( 'image' );
		let nonce   = $btn.data( 'wpnonce' );

		let $toggleComments  = $( '.um-user-photos-comments-toggle' );
		let $commentsWrapper = $( '.um-user-photos-comments.um-toggle-block' );

		$btn.siblings( '.um-user-photos-loader' ).umShow();
		wp.ajax.send(
			'um_user_photos_post_comment',
			{
				data: {
					image_id: postid,
					comment: comment,
					_wpnonce: nonce,
				},
				success: function( response ) {
					$commentsList.prepend( response.content );
					$count.html( response.count );
					$( '#um-user-photos-comments-load-more' ).data( 'all', response.count );
					$textarea.val( '' );
					$btn.prop( 'disabled', true );
					$btn.siblings( '.um-user-photos-loader' ).umHide();
					if ( $commentsWrapper.hasClass( 'um-toggle-block-collapsed' ) ) {
						$toggleComments.prop( 'disabled', false ).trigger( 'click' );
					}
				},
				error: function( data ) {
					console.log( data );
					$btn.prop( 'disabled', true );
					$btn.siblings( '.um-user-photos-loader' ).umHide();
				}
			}
		);
	});
	// Like/Unlike comment.
	$( document.body ).on( 'click', '.um-user-photos-comment-like', function( e ) {
		e.preventDefault();
		let $btn = $( this );
		let commentID = $btn.data( 'id' );

		let action    = $btn.hasClass( 'active' ) ? 'unlike' : 'like';
		let actionRev = $btn.hasClass( 'active' ) ? 'like' : 'unlike';
		let nonce     = $btn.data( action + 'nonce' );
		let title     = $btn.data( actionRev + '_title' );

		let $count   = $btn.parents( '.um-user-photos-comment-meta' ).find( '.um-user-photos-comment-likes-count .um-badge' );
		let $avatars = $btn.parents( '.um-user-photos-comment-meta' ).find( '.um-user-photos-comment-likes-avatars' );

		$btn.prop( 'disabled', true );
		wp.ajax.send(
			'um_user_photos_' + action + '_comment',
			{
				data: {
					commentid: commentID,
					_wpnonce: nonce,
				},
				success: function( response ) {
					$btn.prop( 'disabled', false ).attr( 'title', title ).toggleClass( 'active' );
					$count.text( response.count );
					let disabledProp = parseInt( response.count ) <= 0;
					$btn.parents( '.um-user-photos-comment-meta' ).find( '.um-user-photos-show-comment-likes' ).prop( 'disabled', disabledProp );
					$avatars.html( response.content );
					if ( disabledProp ) {
						$avatars.umHide();
					} else {
						$avatars.umShow();
					}
				},
				error: function( data ) {
					$btn.prop( 'disabled', false );
					console.log( data );
				}
			}
		);
	});
	// Show comment likes.
	$( document.body ).on( 'click', '.um-user-photos-show-comment-likes',function( e ){
		e.preventDefault();

		let $btn  = $( this );
		let nonce = $btn.data( 'wpnonce' );

		wp.ajax.send(
			'get_um_user_photos_comment_likes',
			{
				data: {
					comment_id: $btn.data( 'id' ),
					_wpnonce: nonce,
				},
				success: function( response ) {
					let settings = {
						classes:  'um-user-photos-comment-likes-modal',
						duration: 400,
						footer:   '',
						header:   wp.i18n.__( 'Comment likes', 'um-user-photos' ),
						size:     'normal',
						content:  response.content
					};

					UM.modal.addModal( settings, null );
				},
				error: function( data ) {
					console.log( data );
				}
			}
		);
	});
	// Delete comment.
	$( document.body ).on( 'click', '.um-user-photos-delete-comment',function( e ){
		e.preventDefault();

		let $deleteLink     = $( this );
		let $commentWrapper = $deleteLink.parents( '.um-user-photos-comment-wrap' )
		let commentID       = $deleteLink.data( 'commentid' );
		let nonce           = $deleteLink.data( 'wpnonce' );
		let $count          = jQuery( '.um-user-photos-comments-toggle .um-button-content > .um-badge' );

		$.um_confirm(
			{
				title   : $deleteLink.attr( 'title' ),
				message : $deleteLink.data( 'confirm' ),
				onYes: function() {
					// delete comment here
					wp.ajax.send(
						'um_user_photos_comment_delete',
						{
							data: {
								comment_id: commentID,
								_wpnonce: nonce,
							},
							success: function( response ) {
								$commentWrapper.remove();

								$( '#um-user-photos-comments-load-more' ).data( 'all', response.count );

								$count.text( response.count );
								let disabledProp = parseInt( response.count ) <= 0
								if ( disabledProp ) {
									if ( $( '.um-user-photos-comments-toggle' ).hasClass( 'um-toggle-button-active' ) ) {
										$( '.um-user-photos-comments-toggle' ).trigger( 'click' );
									}
									$( '.um-user-photos-comments-toggle' ).prop( 'disabled', disabledProp );
								}
							},
							error: function( data ) {
								console.log( data );
							}
						}
					);
				},
				object: this
			}
		);
	});
	// Show edit comment section.
	$( document.body ).on( 'click', '.um-user-photos-edit-comment',function( e ){
		e.preventDefault();
		let $wrap     = $( this ).parents( '.um-user-photos-comment-wrap' );
		let $textarea = $wrap.find( '.um-user-photos-comment-textarea' );
		$wrap.find( '.um-user-photos-comment-text, .um-user-photos-comment-edit-section' ).umToggle();
		$textarea.data( 'um-prev-val', $textarea.val() );
	});
	// Cancel comment edit.
	$( document.body ).on( 'click', '.um-user-photos-comment-cancel-btn',function( e ){
		e.preventDefault();

		let $wrap     = $( this ).parents( '.um-user-photos-comment-wrap' );
		let $textarea = $wrap.find( '.um-user-photos-comment-textarea' );
		$wrap.find( '.um-user-photos-comment-text, .um-user-photos-comment-edit-section' ).umToggle();
		$textarea.val( $textarea.data( 'um-prev-val' ) );

		$( this ).siblings( '.um-user-photos-comment-update-btn' ).prop( 'disabled', false );
	});
	// Comment update.
	$( document.body ).on( 'click', '.um-user-photos-comment-update-btn',function( e ){
		e.preventDefault();

		let $btn            = jQuery( this );
		let $wrap           = $btn.parents( '.um-user-photos-comment-wrap' );
		let $textarea       = $wrap.find( '.um-user-photos-comment-textarea' );
		let $textwrap       = $wrap.find( '.um-user-photos-comment-text' );
		let $formResponse   = $wrap.find( '.um-user-photos-edit-comment-status' );
		let $updatedMessage = $wrap.find( '.um-user-photos-comment-updated' );

		let nonce     = $btn.data( 'wpnonce' );
		let comment_id = $btn.data( 'commentid' );
		let content    = $textarea.val();

		$btn.prop( 'disabled', true );
		$btn.siblings( '.um-user-photos-loader' ).umShow();
		wp.ajax.send(
			'um_user_photos_comment_update',
			{
				data: {
					comment_id: comment_id,
					comment_content : content,
					_wpnonce: nonce,
				},
				success: function( response ) {
					$btn.prop( 'disabled', false );
					$btn.siblings( '.um-user-photos-loader' ).umHide();
					$wrap.find( '.um-user-photos-comment-text, .um-user-photos-comment-edit-section' ).umToggle();
					$textwrap.html( response.comment );
					$textarea.val( response.comment );

					$updatedMessage.umShow();
					$updatedMessage.siblings( '.um-user-photos-comment-time' ).umHide();

					setTimeout(
						function (){
							$updatedMessage.umHide();
							$updatedMessage.siblings( '.um-user-photos-comment-time' ).umShow();
						},
						1000
					);
				},
				error: function( data ) {
					console.log( data );
					$btn.prop( 'disabled', false );
					$btn.siblings( '.um-user-photos-loader' ).umHide();
					$formResponse.html( data.status ).umShow();
					setTimeout(
						function (){
							$formResponse.umHide();
						},
						1000
					);
				}
			}
		);
	});
	// load more comments.
	$( document.body ).on( 'click', '#um-user-photos-comments-load-more', function( e ){
		e.preventDefault();

		let $btn     = $( this );
		let nonce    = $btn.data( 'wpnonce' );
		let image_id = $btn.data( 'image_id' );
		let all      = $btn.data( 'all' );
		let $loader  = $btn.siblings( '.um-user-photos-loader' );
		let lastID   = $btn.data( 'last_id' );

		$loader.umShow();
		$btn.prop( 'disabled', true );
		wp.ajax.send(
			'um_user_photos_load_more_comments',
			{
				data: {
					image_id: image_id,
					last_id: lastID,
					_wpnonce: nonce
				},
				success: function( response ) {
					$( '.um-user-photos-comments-loop' ).append( response );
					$loader.umHide();
					lastID = $btn.siblings( '.um-user-photos-comments-loop' ).find( '.um-user-photos-comment-wrap' ).last().data( 'comment_id' );
					$btn.prop( 'disabled', false ).data( 'last_id', lastID );
					if ( parseInt( all ) <= $( '.um-user-photos-comments-loop .um-user-photos-comment-wrap' ).length ) {
						$btn.umHide();
					}
				},
				error: function( data ) {
					console.log( data );
					$loader.umHide();
					$btn.prop( 'disabled', false );
				}
			}
		);
	});

	/* Load more album photos */
	$( document ).on( 'click', '#um-user-photos-photos-load-more', function ( e ) {
		let btn    = $( this );
		let wrap   = btn.closest( '.um-user-photos-single-album' ).find( '.um-user-photos-grid' );
		let profileID = btn.data( 'profile' );
		let offset = wrap.find( '.um-user-photos-image-block' ).length;
		let photos = btn.data( 'count_photos' );
		let nonce  = btn.data( 'wpnonce' );
		let album  = btn.data( 'album_id' );

		wp.ajax.send(
			'um_user_photos_album_load_more',
			{
				data: {
					profile: profileID,
					offset: offset,
					album_id: album,
					_wpnonce: nonce,
				},
				success: function (response) {
					wrap.append( response );
					UM.frontend.image.lazyload.init();
					let new_count = wrap.find( '.um-user-photos-image-block' ).length;
					wrap.data( 'count', new_count );
					if ( new_count >= photos ) {
						btn.remove();
					}
				},
				error: function( data ) {
					console.log( data );
				}
			}
		);
	});

	// First page loading. Check for photo_id attribute and loading modal if needed.
	$(window).on( 'load', function(){
		let params = new URLSearchParams( window.location.search );

		// All gallery photos view.
		let $galleryPhotosWrapper = $( '.um-user-photos-gallery-photos' );
		if ( $galleryPhotosWrapper.length ) {
			if ( params.has( 'photo_id' ) ) {
				let $photo = $('.um-user-photos-open-single-photo-modal[data-id="' + params.get( 'photo_id' ) + '"]');
				if ( $photo.length ) {
					$photo.trigger( 'click' );
				} else {
					UM.frontend.url.deleteURLSearchParam( 'photo_id' );
				}
			}
			return;
		}

		// All gallery photos view.
		let $galleryWrapper = $( '.um-user-photos-single-album' );
		if ( $galleryWrapper.length ) {
			if ( params.has( 'photo_id' ) ) {
				let $photo = $('.um-user-photos-open-single-photo-modal[data-id="' + params.get( 'photo_id' ) + '"]');
				if ( $photo.length ) {
					$photo.trigger( 'click' );
				} else {
					UM.frontend.url.deleteURLSearchParam( 'photo_id' );
				}
			}
		}
	});

	// Switch browser history between albums list and selected album.
	$(window).on('popstate', function(event) {
		let params = new URLSearchParams( window.location.search );

		// All gallery photos view.
		let $galleryPhotosWrapper = $( '.um-user-photos-gallery-photos' );
		if ( $galleryPhotosWrapper.length ) {
			if ( params.has( 'photo_id' ) ) {
				let $photo = $('.um-user-photos-open-single-photo-modal[data-id="' + params.get( 'photo_id' ) + '"]');
				if ( $photo.length ) {
					$photo.trigger( 'click' );
				} else {
					UM.frontend.url.deleteURLSearchParam( 'photo_id' );
				}
			} else {
				UM.modal.close();
			}
			return;
		}

		// Gallery albums view.
		let $singleAlbum = $( '.um-user-photos-single-album' );
		let $addForm = $( '.um-user-photos-add-album' );
		let $editForm = $( '.um-user-photos-edit-album-form' );
		let $gallery = $( '.um-user-photos-gallery' );

		if ( params.has( 'gallery_action' ) ) {
			if ( 'add_album' === params.get( 'gallery_action' ) ) {
				let $newBtn = $('.um-user-photos-new-album');
				if ( $newBtn.length ) {
					$newBtn.trigger( 'click' );
				} else {
					UM.frontend.url.deleteURLSearchParam( 'gallery_action' );
				}
			} else if ( 'edit_album' === params.get( 'gallery_action' ) ) {
				if ( ! params.has( 'album_id' ) ) {
					UM.frontend.url.deleteURLSearchParam( 'gallery_action' );
				} else {
					let $newBtn = $('.um-user-photos-edit-album[data-id="' + parseInt( params.get( 'album_id' ) ) + '"]');
					if ( $newBtn.length ) {
						$newBtn.trigger( 'click' );
					} else {
						UM.frontend.url.deleteURLSearchParam( 'gallery_action' );
					}
				}
			}
			return;
		}

		if ( ! params.has( 'album_id' ) && ! params.has( 'gallery_action' ) && ( $singleAlbum.length > 0 || $addForm.length > 0 ) ) {
			// go to gallery from single album or add form.
			$( '.um-user-photos-back-to-gallery' ).trigger( 'click' );
		} else if ( params.has( 'album_id' ) && $gallery.length > 0 ) {
			let $albumBlock = $( '.um-user-photos-album-block[data-id=' + params.get( 'album_id' ) + ']' );
			if ( $albumBlock.length ) {
				$albumBlock.trigger( 'click' );
			} else {
				UM.frontend.url.deleteURLSearchParam( 'album_id' );
			}
		} else if ( params.has( 'album_id' ) && 'edit_album' !== params.get( 'gallery_action' ) && $editForm.length > 0 ) {
			$( '.um-user-photos-back-to-album' ).trigger( 'click' );
		} else if ( params.has( 'album_id' ) && params.has( 'photo_id' ) && $singleAlbum.length > 0 ) {
			let $photo = $('.um-user-photos-open-single-photo-modal[data-id="' + params.get( 'photo_id' ) + '"]');
			if ( $photo.length ) {
				$photo.trigger( 'click' );
			} else {
				UM.frontend.url.deleteURLSearchParam( 'photo_id' );
			}
		}
	});

	/* Albums grid pagination */
	$( document.body ).on( 'click', '.ultimatemember_albums .um-pagination-item:not(.disabled)', function ( e ) {
		e.preventDefault();
		let page;
		let $container = jQuery( e.currentTarget ).closest( '.ultimatemember_albums' );
		let nonce      = $container.data( 'wpnonce' );
		let per_page   = $container.data( 'um-pagi-per_page' );
		let column     = $container.data( 'um-pagi-column' );

		if ( $( this ).hasClass( 'um-pagination-arrow' ) ) {
			let current_page = $container.find( '.current' ).data( 'page' );
			let nav          = $( this ).data( 'page' );
			if ( 'prev' === nav) {
				page = parseInt( current_page ) - 1;
			} else {
				page = parseInt( current_page ) + 1;
			}
		} else {
			page = jQuery( e.currentTarget ).data( 'page' );
		}

		$container.find('.um-pagination-item').addClass('disabled');
		$container.find('.um-user-photos-album').addClass('um-skeleton-mode');

		wp.ajax.send(
			'um_user_photos_get_albums_content',
			{
				data: {
					page: page,
					per_page: per_page,
					column: column,
					_wpnonce: nonce,
				},
				success: function (response) {
					$container.html( jQuery( response ) );
					$container.find( '.um.ultimatemember_albums' ).unwrap();
					$( document ).trigger( 'resize' );
					UM.frontend.image.lazyload.init();
				},
				error: function( data ) {
					console.log( data );
				}
			}
		);
	});

	/* Single photo modal navigation */
	$( document.body ).on( 'click', '.um-photo-modal-next, .um-photo-modal-prev', function( e ){
		e.preventDefault();

		let currentImageID = $( '.um.um-modal .um-user-photos-widget-image' ).data( 'image' );

		let $currentBlock  = $( '#um-user-photos-image-block-' + currentImageID );

		let context  = $currentBlock.parents( '.um-user-photos-single-album' ).length ? 'album' : 'gallery';

		let $navBlock;
		if ( $(this).hasClass('um-photo-modal-next') ) {
			// next photo
			$navBlock = $currentBlock.nextAll( '.um-user-photos-image-block' ).filter( ':first' );
			if ( ! $navBlock.length ) {
				$navBlock = $currentBlock.parents('.um-user-photos-grid').find( '.um-user-photos-image-block' ).first();
			}
		} else {
			// previous photo
			$navBlock = $currentBlock.prevAll( '.um-user-photos-image-block' ).filter( ':first' );
			if ( ! $navBlock.length ) {
				$navBlock = $currentBlock.parents('.um-user-photos-grid').find( '.um-user-photos-image-block' ).last();
			}
		}

		let $newWrapper = $navBlock.find( '.um-user-photos-open-single-photo-modal' );
		let newID = parseInt( $newWrapper.data( 'id' ) );
		let nonce = $newWrapper.data( 'wpnonce' );

		$('.um-user-photos-widget-image').find( '.um-image-lazyload-wrapper' ).removeClass('um-loaded');
		$('.um-user-photos-widget').addClass('um-skeleton-mode');

		wp.ajax.send(
			'um_user_photos_get_comment_section',
			{
				data: {
					_wpnonce: nonce,
					image_id: newID,
					context: context
				},
				success: function( response ) {
					$('.um-modal-body').html( response );
					UM.modal.responsive();

					let params = new URLSearchParams( window.location.search );
					if ( ! params.has( 'photo_id' ) || newID !== parseInt( params.get( 'photo_id' ) ) ) {
						UM.frontend.url.setURLSearchParam( 'photo_id', newID );
					}

					UM.frontend.image.lazyload.init();
					um_init_new_dropdown();
				},
				error: function( data ) {
					console.log( data );
				}
			}
		);
	});
});
