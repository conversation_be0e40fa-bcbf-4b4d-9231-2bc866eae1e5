"use strict";var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.Filters=WPForms.Admin.Builder.Filters||function(e,n){let i={};const d={keywordList:null},a={init:function(){n(a.ready)},ready:function(){a.setup(),a.events(),a.initCountryList(),a.loadStates()},setup:function(){i={$builder:n("#wpforms-builder"),$panelToggle:n(".wpforms-panel-field-toggle-next-field"),$keywordsList:n(".wpforms-panel-field-keyword-keywords textarea"),$keywordsListContainer:n(".wpforms-panel-field-keyword-filter-keywords-container"),$keywordsListActions:n(".wpforms-panel-field-keyword-filter-actions"),$keywordsListSaveButton:n(".wpforms-settings-keyword-filter-save-changes"),$keywordsListCancelButton:n(".wpforms-settings-keyword-filter-cancel"),$keywordsListReformatButton:n(".wpforms-btn-keyword-filter-reformat"),$keywordsListToggle:n(".wpforms-settings-keyword-filter-toggle-list"),$keywordsListCount:n(".wpforms-panel-field-keyword-filter-keywords-count"),$countryCodes:n("#wpforms-panel-field-anti_spam-country_filter-country_codes"),$countryCodesHidden:n(".wpforms-panel-field-country-filter-country-codes-json")}},events:function(){i.$builder.on("change",".wpforms-panel-field-toggle-next-field",a.togglePanel).on("click",".wpforms-settings-keyword-filter-toggle-list",a.loadKeywords).on("click",".wpforms-settings-keyword-filter-toggle-list",a.toggleKeywordsList).on("click",".wpforms-settings-keyword-filter-save-changes",a.saveKeywords).on("click",".wpforms-settings-keyword-filter-cancel",a.cancelSavingKeywordList).on("click",".wpforms-btn-keyword-filter-reformat",a.reformatKeywords).on("change keyup paste cut",".wpforms-panel-field-keyword-keywords textarea",a.updateKeywordsCount).on("paste keyup",".wpforms-panel-field-keyword-keywords textarea",a.showReformatWarning).on("change","#wpforms-panel-field-anti_spam-country_filter-country_codes",a.changeCountryCodes).on("wpformsSaved",a.saveKeywords)},loadStates:function(){i.$panelToggle.trigger("change")},initCountryList:function(){"function"==typeof e.Choices&&void 0===i.$countryCodes.data("choicesjs")&&0!==i.$countryCodes.length&&(i.$countryCodes.data("choicesjs",new Choices(i.$countryCodes[0],{shouldSort:!1,allowHTML:!1,removeItemButton:!0,fuseOptions:{threshold:.1,distance:1e3},callbackOnInit(){wpf.initMultipleSelectWithSearch(this),wpf.showMoreButtonForChoices(this.containerOuter.element)}})),a.changeCountryCodes(null),!0===wpf.initialSave)&&(wpf.savedState=wpf.getFormState("#wpforms-builder-form"))},changeCountryCodes:function(){i.$countryCodes.length<=0||i.$countryCodesHidden.val(JSON.stringify(i.$countryCodes.val()))},togglePanel:function(){var e=n(this);e.closest(".wpforms-panel-field").next(".wpforms-panel-field").toggle(e.is(":checked"))},loadKeywords:function(e){e.preventDefault(),0===i.$keywordsList.val().length&&null===d.keywordList&&n.post(wpforms_builder.ajax_url,{nonce:wpforms_builder.nonce,action:"wpforms_builder_load_keywords"},function(e){e.success&&(d.keywordList=e.data.keywords.join("\r\n"),i.$keywordsList.val(d.keywordList),a.updateKeywordsCount())})},toggleKeywordsList:function(){var e=i.$keywordsListToggle.text(),t=i.$keywordsListToggle.data("collapse");i.$keywordsListToggle.text(t).data("collapse",e),i.$keywordsListContainer.toggle(),a.removeReformatWarning()},saveKeywords:function(e){if(e.preventDefault(),!(i.$keywordsListSaveButton.attr("disabled")&&0===i.$keywordsList.val().length||null===d.keywordList)){const t=i.$keywordsListSaveButton.find(".wpforms-settings-keyword-filter-save-changes-text"),o=t.text(),r=i.$keywordsListSaveButton.find(".wpforms-loading-spinner"),s=a.getKeywords().join("\r\n");i.$keywordsListSaveButton.attr("disabled","disabled").css("width",i.$keywordsListSaveButton.outerWidth()),n.post({url:wpforms_builder.ajax_url,data:{keywords:s,nonce:wpforms_builder.nonce,action:"wpforms_builder_save_keywords"},beforeSend:function(){r.removeClass("wpforms-hidden"),t.text(wpforms_builder.saving)},success:function(e){e.success&&(d.keywordList=s,i.$keywordsList.val(s))},complete:function(){setTimeout(function(){"wpformsSaved"!==e.type&&n("#wpforms-save").trigger("click"),r.addClass("wpforms-hidden"),t.text(o),i.$keywordsListSaveButton.removeAttr("disabled").removeAttr("style"),i.$keywordsListActions.addClass("wpforms-hidden"),a.removeReformatWarning()},1e3)}})}},cancelSavingKeywordList:function(e){e.preventDefault(),i.$keywordsList.val(d.keywordList),a.updateKeywordsCount(),i.$keywordsListActions.addClass("wpforms-hidden"),a.toggleKeywordsList()},updateKeywordsCount:function(){setTimeout(function(){i.$keywordsListCount.text("").text(a.getKeywords().length),i.$keywordsListActions.removeClass("wpforms-hidden")})},getKeywords:function(){let e=i.$keywordsList.val().split(/\r\n|\r|\n/);return e=(e=e.map(function(e){return e.trim()})).filter(function(e){return 0<e.length})},showReformatWarning:function(e){var t=wp.template("wpforms-settings-anti-spam-keyword-filter-reformat-warning-template"),e=void 0===e.originalEvent.clipboardData?n(this).val():e.originalEvent.clipboardData.getData("text"),o=(e.match(/,/g)||[]).length,r=(e.match(/;/g)||[]).length,e=e.split(/\r\n|\r|\n/).length;e<=o||e<=r?(a.removeReformatWarning(),i.$keywordsListActions.prepend(t())):a.removeReformatWarning()},removeReformatWarning:function(){n(".wpforms-alert-keyword-filter-reformat").remove()},successReformatWarning:function(){n(".wpforms-alert-keyword-filter-reformat .wpforms-alert-message p").text(wpforms_builder_anti_spam_filters.successfullReformatWarning),n(".wpforms-alert-keyword-filter-reformat .wpforms-alert-buttons").hide()},reformatKeywords:function(){a.successReformatWarning(),d.keywordList=i.$keywordsList.val(),i.$keywordsList.val(function(){return this.value.split(/[,;]+/).map(function(e){return e.trim()}).join("\n")}),a.updateKeywordsCount()}};return a}((document,window),jQuery),WPForms.Admin.Builder.Filters.init();