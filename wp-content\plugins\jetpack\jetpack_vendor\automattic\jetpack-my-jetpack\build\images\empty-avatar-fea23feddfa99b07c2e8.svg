<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 27.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 36 36" style="enable-background:new 0 0 36 36;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#FFFFFF;stroke:#FFFFFF;stroke-width:2;}
	.st1{clip-path:url(#SVGID_00000131361309518468547900000009062464345318814348_);}
	.st2{fill:#F6F7F7;filter:url(#Adobe_OpacityMaskFilter);}
	.st3{mask:url(#mask0_2900_10877_00000159448472586931246160000013733448166778715831_);}
	.st4{fill:#F0F2EB;}
	.st5{fill-rule:evenodd;clip-rule:evenodd;fill:#646970;}
</style>
<circle class="st0" cx="18" cy="18" r="17"/>
<g>
	<defs>
		<rect id="SVGID_1_" width="36" height="36"/>
	</defs>
	<clipPath id="SVGID_00000143603845024951162640000013916798552048052664_">
		<use xlink:href="#SVGID_1_"  style="overflow:visible;"/>
	</clipPath>
	<g style="clip-path:url(#SVGID_00000143603845024951162640000013916798552048052664_);">
		<defs>
			<filter id="Adobe_OpacityMaskFilter" filterUnits="userSpaceOnUse" x="0" y="0" width="36" height="36">
				<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
			</filter>
		</defs>
		
			<mask maskUnits="userSpaceOnUse" x="0" y="0" width="36" height="36" id="mask0_2900_10877_00000159448472586931246160000013733448166778715831_">
			<circle class="st2" cx="18" cy="18" r="18"/>
		</mask>
		<g class="st3">
			<circle class="st4" cx="18" cy="18" r="18"/>
			<path class="st5" d="M12.7,23c-1.2-1.3-2-3.1-2-5c0-4,3.3-7.3,7.3-7.3s7.3,3.3,7.3,7.3c0,1.9-0.7,3.7-2,5v-0.5
				c0-1.7-1.4-3.1-3.1-3.1h-4.5c-1.7,0-3.1,1.4-3.1,3.1V23z M14.3,24.3c1.1,0.6,2.3,1,3.7,1s2.6-0.4,3.7-1v-1.8
				c0-0.8-0.6-1.4-1.4-1.4h-4.5c-0.8,0-1.4,0.6-1.4,1.4V24.3z M9,18c0-5,4-9,9-9s9,4,9,9s-4,9-9,9S9,23,9,18z M20.2,15.8
				c0,1.2-1,2.2-2.2,2.2s-2.2-1-2.2-2.2s1-2.2,2.2-2.2S20.2,14.5,20.2,15.8z"/>
		</g>
	</g>
</g>
</svg>
