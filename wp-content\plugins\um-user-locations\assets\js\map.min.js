function um_waiting_response_handler(e,r){wp.UM.member_directory.map.ajax.response(e,r)}function um_whengoogleloadeddo(e,r){"undefined"!=typeof google?e(r[0],r[1]):setTimeout(function(){um_whengoogleloadeddo(e,r)},500)}function UMInitUserLocationDirectory(){jQuery(".um-directory").each(function(){var e=jQuery(this);wp.UM.member_directory.map.init(e)})}"object"!=typeof wp.UM&&(wp.UM={}),"object"!=typeof wp.UM.member_directory&&(wp.UM.member_directory={}),wp.UM.member_directory.map={infowindow:{customize:function(){var e=jQuery(".gm-style-iw"),r=e.prev(),r=(r.children(":nth-child(2)").css({display:"none"}),r.children(":nth-child(4)").css({display:"none"}),e.parent().parent().css({left:"115px"}),r.children(":nth-child(1)").attr("style",function(e,r){return r+"left: 76px !important;"}),r.children(":nth-child(3)").attr("style",function(e,r){return r+"left: 76px !important;"}),r.children(":nth-child(3)").find("div").children().css({"box-shadow":"rgba(72, 181, 233, 0.6) 0px 1px 6px","z-index":"1"}),e.next());r.css({opacity:"1",right:"48px",top:"20px","border-radius":"50%"}),r.mouseout(function(){jQuery(this).css({opacity:"1"})})},close:function(e,r){var o=um_members_get_hash(e);void 0!==wp.UM.member_directory.map.blocks[o]&&(void 0!==wp.UM.member_directory.map.blocks[o].infowindows&&jQuery.each(wp.UM.member_directory.map.blocks[o].infowindows,function(r){jQuery.each(wp.UM.member_directory.map.blocks[o].infowindows[r],function(e){wp.UM.member_directory.map.blocks[o].infowindows[r][e].close()})}),r)&&(wp.UM.member_directory.map.blocks[o].infowindows={})}},selectOnEnter:function(m){var a=m.addEventListener||m.attachEvent;function e(e,r){var o;"keydown"===e&&(o=r,r=function(e){var r=0<jQuery(".pac-item-selected").length;13!==e.which||r||(r=jQuery.Event("keydown",{keyCode:40,which:40}),o.apply(m,[r])),o.apply(m,[e])}),a.apply(m,[e,r])}m.addEventListener?m.addEventListener=e:m.attachEvent&&(m.attachEvent=e)},remove_markers:function(e){var o=um_members_get_hash(e);void 0!==wp.UM.member_directory.map.blocks[o]&&(void 0===wp.UM.member_directory.map.blocks[o].markers?wp.UM.member_directory.map.blocks[o].markers={}:(void 0!==wp.UM.member_directory.map.blocks[o].markers&&jQuery.each(wp.UM.member_directory.map.blocks[o].markers,function(r){jQuery.each(wp.UM.member_directory.map.blocks[o].markers[r],function(e){wp.UM.member_directory.map.blocks[o].markers[r][e].setMap(null)})}),wp.UM.member_directory.map.blocks[o].markers={},void 0!==wp.UM.member_directory.map.blocks[o].marker_clusterer&&wp.UM.member_directory.map.blocks[o].marker_clusterer.clearMarkers()))},is_dynamically_searched:function(e){return!!e.find('.um-member-directory-map[data-dynamic-search="1"]').length},is_searched:function(e){return!!e.find(".um_user_location_g_autocomplete").length},get_map:function(e){e=um_members_get_hash(e);return void 0!==wp.UM.member_directory.map.blocks[e]&&void 0!==wp.UM.member_directory.map.blocks[e].map?wp.UM.member_directory.map.blocks[e].map:null},move_map:function(e,r){var o,m;um_is_directory_busy(r)||(o=(m=e.getBounds()).getNorthEast(),m=m.getSouthWest(),e=e.getZoom(),um_members_show_preloader(r),um_set_url_from_data(r,"map_ne",o.lat()+","+o.lng()),um_set_url_from_data(r,"map_sw",m.lat()+","+m.lng()),um_set_url_from_data(r,"map_zoom",e),r.data("page",1),um_set_url_from_data(r,"page",""),um_ajax_get_members(r),r.data("searched",1),r.find(".um-member-directory-sorting-options").prop("disabled",!1),r.find(".um-member-directory-view-type").removeClass("um-disabled"))},init:function(y){var w,e,r,o,m,a,g=y.find(".um-member-directory-map");if(g.length&&!g.hasClass("um-map-inited"))return w=um_members_get_hash(y),wp.UM.member_directory.map.blocks[w]=[],o=new google.maps.LatLng(g.data("lat"),g.data("lng")),void 0===(r=g.data("zoom"))&&(r=12),o=wp.hooks.applyFilters("um_user_locations_map_args_init",{center:o,zoom:r},w,y),wp.UM.member_directory.map.blocks[w].map=new google.maps.Map(g[0],o),wp.UM.member_directory.map.is_dynamically_searched(y)?(r=um_get_data_for_directory(y,"map_ne"),o=um_get_data_for_directory(y,"map_sw"),a=um_get_data_for_directory(y,"map_zoom"),void 0!==r&&void 0!==o&&(g.removeClass(wp.UM.member_directory.map.classes.hidden),r=r.split(","),o=o.split(","),(e=new google.maps.LatLngBounds).extend(new google.maps.LatLng(o[0],o[1])),e.extend(new google.maps.LatLng(r[0],r[1])),wp.UM.member_directory.map.blocks[w].map.setZoom(parseInt(a)),+o[1]>+r[1]?(m=(180-+o[1]+-1*(-180-+r[1]))/2,m=+o[1]+m<=180?+o[1]+m:+r[1]-m,r=new google.maps.LatLng((+r[0]+ +o[0])/2,m),wp.UM.member_directory.map.blocks[w].map.setCenter(r)):wp.UM.member_directory.map.blocks[w].map.setCenter(e.getCenter())),void 0===wp.UM.member_directory.map.listeners[w]&&(wp.UM.member_directory.map.listeners[w]=[]),wp.UM.member_directory.map.listeners[w].moving=google.maps.event.addListener(wp.UM.member_directory.map.blocks[w].map,"idle",function(){wp.UM.member_directory.map.listeners[w].skip_first_idle?wp.UM.member_directory.map.move_map(this,y):wp.UM.member_directory.map.listeners[w].skip_first_idle=!0})):wp.UM.member_directory.map.is_searched(y)&&(o=um_get_data_for_directory(y,"search_lat"),m=um_get_data_for_directory(y,"search_lng"),a=um_get_data_for_directory(y,"map_zoom"),void 0!==o)&&void 0!==m&&(g.removeClass(wp.UM.member_directory.map.classes.hidden),wp.UM.member_directory.map.blocks[w].map.setZoom(parseInt(a)),wp.UM.member_directory.map.blocks[w].map.setCenter(new google.maps.LatLng(o,m))),y.find(".um_user_location_g_autocomplete").each(function(){wp.UM.member_directory.map.selectOnEnter(jQuery(this).get(0));var e=wp.hooks.applyFilters("um_user_locations_autocomplete_args",{types:["geocode"]},y,jQuery(this)),e=new google.maps.places.Autocomplete(jQuery(this).get(0),e),b=jQuery(this),r=um_get_data_for_directory(y,"location_search");void 0!==r&&b.val(r),e.addListener("place_changed",function(e){var p,n,r,o,m,a,t,s,i,_,d,c,l,u;um_is_directory_busy(y)||(um_members_show_preloader(y),u=this.getPlace(),void 0!==(u=wp.hooks.applyFilters("um_user_locations_place_changed_autocomplete",u,y))&&void 0!==u.geometry&&void 0!==u.geometry.location||void 0!==u.name?""!==u.name?(p=u.geometry.location.lat(),n=u.geometry.location.lng(),um_set_url_from_data(y,"location_search",b.val()),y.find(".um-member-directory-map").length?(r=g.hasClass(wp.UM.member_directory.map.classes.hidden),g.removeClass(wp.UM.member_directory.map.classes.hidden),wp.UM.member_directory.map.blocks[w].map.setCenter({lat:parseFloat(p),lng:parseFloat(n)}),wp.UM.member_directory.map.blocks[w].map.fitBounds(u.geometry.viewport),r?setTimeout(function(){var e,r,o,m,a,t,s,i,_,d,c=wp.UM.member_directory.map.blocks[w].map.getZoom();wp.UM.member_directory.map.is_dynamically_searched(y)?(e=(m=wp.UM.member_directory.map.blocks[w].map.getBounds()).getNorthEast(),r=m.getSouthWest(),um_set_url_from_data(y,"map_ne",e.lat()+","+e.lng()),um_set_url_from_data(y,"map_sw",r.lat()+","+r.lng()),um_set_url_from_data(y,"map_zoom",c)):wp.UM.member_directory.map.is_searched(y)&&(e=(m=wp.UM.member_directory.map.blocks[w].map.getBounds()).getNorthEast(),r=m.getSouthWest(),m=g.data("distance-unit"),o=g.data("predefined-radius"),m="km"===m?111:69,s=(e.lat()-r.lat())*m,d=(e.lng()-r.lng())*m,a=e.lat(),i=e.lng(),t=r.lat(),_=r.lng(),s<+o&&(s=+o/m/2,a=parseFloat(p)+s,t=parseFloat(p)-s),d<+o&&(s=+o/m/2,i=parseFloat(n)+s,_=parseFloat(n)-s),e=new google.maps.LatLng(a,i),r=new google.maps.LatLng(t,_),d=new google.maps.LatLngBounds(r,e),wp.UM.member_directory.map.blocks[w].map.fitBounds(d),c=wp.UM.member_directory.map.blocks[w].map.getZoom(),um_set_url_from_data(y,"search_lat",parseFloat(p)),um_set_url_from_data(y,"search_lng",parseFloat(n)),um_set_url_from_data(y,"map_zoom",c)),wp.hooks.doAction("um_user_locations_after_place_changed_autocomplete",u,y,wp.UM.member_directory.map.blocks[w].map),y.data("searched",1),y.find(".um-member-directory-sorting-options").prop("disabled",!1),y.find(".um-member-directory-view-type").removeClass("um-disabled"),y.data("page",1),um_set_url_from_data(y,"page",""),wp.UM.member_directory.map.is_dynamically_searched(y)||wp.UM.member_directory.map.remove_markers(y),um_ajax_get_members(y)},100):(r=wp.UM.member_directory.map.blocks[w].map.getZoom(),wp.UM.member_directory.map.is_dynamically_searched(y)?(o=(d=wp.UM.member_directory.map.blocks[w].map.getBounds()).getNorthEast(),m=d.getSouthWest(),um_set_url_from_data(y,"map_ne",o.lat()+","+o.lng()),um_set_url_from_data(y,"map_sw",m.lat()+","+m.lng()),um_set_url_from_data(y,"map_zoom",r)):wp.UM.member_directory.map.is_searched(y)&&(o=(d=wp.UM.member_directory.map.blocks[w].map.getBounds()).getNorthEast(),m=d.getSouthWest(),d=g.data("distance-unit"),_=g.data("predefined-radius"),d="km"===d?111:69,c=(o.lat()-m.lat())*d,i=(o.lng()-m.lng())*d,l=o.lat(),t=o.lng(),a=m.lat(),s=m.lng(),c<+_&&(c=+_/d/2,l=parseFloat(p)+c,a=parseFloat(p)-c),i<+_&&(c=+_/d/2,t=parseFloat(n)+c,s=parseFloat(n)-c),o=new google.maps.LatLng(l,t),m=new google.maps.LatLng(a,s),i=new google.maps.LatLngBounds(m,o),wp.UM.member_directory.map.blocks[w].map.fitBounds(i),r=wp.UM.member_directory.map.blocks[w].map.getZoom(),um_set_url_from_data(y,"search_lat",parseFloat(p)),um_set_url_from_data(y,"search_lng",parseFloat(n)),um_set_url_from_data(y,"map_zoom",r)),wp.hooks.doAction("um_user_locations_after_place_changed_autocomplete",u,y,wp.UM.member_directory.map.blocks[w].map),y.data("searched",1),y.find(".um-member-directory-sorting-options").prop("disabled",!1),y.find(".um-member-directory-view-type").removeClass("um-disabled"),y.data("page",1),um_set_url_from_data(y,"page",""),wp.UM.member_directory.map.is_dynamically_searched(y)||wp.UM.member_directory.map.remove_markers(y),um_ajax_get_members(y))):(y.data("searched",1),y.find(".um-member-directory-sorting-options").prop("disabled",!1),y.find(".um-member-directory-view-type").removeClass("um-disabled"),y.data("page",1),um_set_url_from_data(y,"page",""),um_ajax_get_members(y))):(um_set_url_from_data(y,"location_search",""),y.find(".um-member-directory-map").length&&(_=new google.maps.LatLng(-90,180),d=new google.maps.LatLng(90,-180),c=new google.maps.LatLngBounds(_,d),wp.UM.member_directory.map.blocks[w].map.fitBounds(c),wp.UM.member_directory.map.blocks[w].map.setCenter({lat:0,lng:0}),wp.UM.member_directory.map.blocks[w].map.setZoom(1),wp.UM.member_directory.map.is_dynamically_searched(y)?(um_set_url_from_data(y,"map_ne",""),um_set_url_from_data(y,"map_sw",""),um_set_url_from_data(y,"map_zoom","")):wp.UM.member_directory.map.is_searched(y)&&(um_set_url_from_data(y,"search_lat",""),um_set_url_from_data(y,"search_lng",""),um_set_url_from_data(y,"map_zoom",""))),wp.hooks.applyFilters("um_member_directory_ignore_after_search",!1,y)||1===y.data("must-search")&&(l=um_get_search(y),0!==y.find(".um-members-filter-remove").length||l||(y.data("searched",0),y.find(".um-members-grid, .um-members-list, .um-members-intro").remove(),y.find(".um-member-directory-sorting-options").prop("disabled",!0),y.find(".um-member-directory-view-type").addClass("um-disabled"),wp.hooks.doAction("um_member_directory_clear_not_searched",y))),y.data("page",1),um_set_url_from_data(y,"page",""),um_ajax_get_members(y)):um_members_hide_preloader(y))})}),g.addClass("um-map-inited"),g},ajax:{request:function(e,r){var o,m,a,t;return wp.UM.member_directory.map.is_dynamically_searched(r)?(o=um_get_data_for_directory(r,"map_ne"),m=um_get_data_for_directory(r,"map_sw"),void 0!==o&&void 0!==m?(e.map_ne=o,e.map_sw=m):(t=wp.UM.member_directory.map.get_map(r))&&(um_members_get_hash(r),void 0!==(t=t.getBounds()))&&(a=t.getNorthEast(),t=t.getSouthWest(),o=a.lat()+","+a.lng(),m=t.lat()+","+t.lng(),e.map_ne=o,e.map_sw=m)):wp.UM.member_directory.map.is_searched(r)&&(a=um_get_data_for_directory(r,"search_lat"),t=um_get_data_for_directory(r,"search_lng"),void 0!==a)&&void 0!==t&&(e.search_lat=a,e.search_lng=t),e},response:function(m,e){var o,a,r,s=um_members_get_hash(m),i=(m.find(".um-member-directory-map").removeClass(wp.UM.member_directory.map.classes.hidden),wp.UM.member_directory.map.get_map(m)),_={};Object.getOwnPropertyNames(_).forEach(function(e){delete _[e]}),jQuery.each(e.users,function(a){var t=e.users[a];void 0===_[t.id]&&(_[t.id]={}),jQuery.each(e.map_fields,function(e,r){var o,m;void 0!==t[e+"_lat"]&&void 0!==t[e+"_lng"]&&(m=new google.maps.LatLng(t[e+"_lat"],t[e+"_lng"]),o={content:wp.template("um-user-location-map-marker-infowindow")({field:e,userdata:t}),maxWidth:350},o=wp.hooks.applyFilters("um_user_locations_infowindow_data",o,s,t.id),m={position:m,map:i,title:t.display_name+" "+r,member_anchor:t.card_anchor,zIndex:a+1,optimized:!1,infowindow:o},""!==t.avatar_url&&(r=-1===t.avatar_url.indexOf("?")?"?um_avatar_marker=1":"&um_avatar_marker=1",m.icon={url:t.avatar_url+r,scaledSize:new google.maps.Size(32,32),size:new google.maps.Size(36,40),anchor:new google.maps.Point(18,18)},m.shape={coords:[18,18,20],type:"circle"}),m=wp.hooks.applyFilters("um_user_locations_marker_data",m,s,t),_[t.id][e]=m)})}),void 0===wp.UM.member_directory.map.blocks[s].markers&&(wp.UM.member_directory.map.blocks[s].markers={}),void 0===wp.UM.member_directory.map.blocks[s].infowindows&&(wp.UM.member_directory.map.blocks[s].infowindows={}),void 0!==wp.UM.member_directory.map.blocks[s].marker_clusterer&&wp.UM.member_directory.map.blocks[s].marker_clusterer.clearMarkers(),void 0!==wp.UM.member_directory.map.blocks[s].markers&&jQuery.each(wp.UM.member_directory.map.blocks[s].markers,function(r){jQuery.each(wp.UM.member_directory.map.blocks[s].markers[r],function(e){void 0===_[r]||void 0===_[r][e]?(wp.UM.member_directory.map.blocks[s].markers[r][e].setMap(null),wp.UM.member_directory.map.blocks[s].infowindows[r][e].close(),delete wp.UM.member_directory.map.blocks[s].markers[r][e],Object.keys(wp.UM.member_directory.map.blocks[s].markers[r]).length||delete wp.UM.member_directory.map.blocks[s].markers[r],delete wp.UM.member_directory.map.blocks[s].infowindows[r][e],Object.keys(wp.UM.member_directory.map.blocks[s].infowindows[r]).length||delete wp.UM.member_directory.map.blocks[s].infowindows[r]):(delete _[r][e],Object.keys(_[r]).length||delete _[r])})}),jQuery.each(_,function(o){void 0===wp.UM.member_directory.map.blocks[s].markers[o]&&(wp.UM.member_directory.map.blocks[s].markers[o]={}),void 0===wp.UM.member_directory.map.blocks[s].infowindows[o]&&(wp.UM.member_directory.map.blocks[s].infowindows[o]={}),jQuery.each(_[o],function(r){wp.UM.member_directory.map.blocks[s].markers[o][r]=new google.maps.Marker(_[o][r]),wp.UM.member_directory.map.blocks[s].infowindows[o][r]=new google.maps.InfoWindow(_[o][r].infowindow),wp.UM.member_directory.map.blocks[s].markers[o][r].addListener("click",function(e){void 0===e?wp.UM.member_directory.map.infowindow.close(m):(wp.UM.member_directory.map.infowindow.close(m),wp.UM.member_directory.map.blocks[s].infowindows[o][r].status="open",wp.UM.member_directory.map.blocks[s].infowindows[o][r].open(i,wp.UM.member_directory.map.blocks[s].markers[o][r]))}),google.maps.event.addListener(wp.UM.member_directory.map.blocks[s].infowindows[o][r],"closeclick",function(){wp.UM.member_directory.map.blocks[s].infowindows[o][r].status="closed"})})}),wp.hooks.doAction("um_member_directory_after_markers_init",m,_),wp.hooks.applyFilters("um_member_directory_disable_spiderfier",!1,m)||(o=new OverlappingMarkerSpiderfier(i,{keepSpiderfied:!0,event:"mouseover"}),a=[],void 0!==wp.UM.member_directory.map.blocks[s].markers&&jQuery.each(wp.UM.member_directory.map.blocks[s].markers,function(r){jQuery.each(wp.UM.member_directory.map.blocks[s].markers[r],function(e){a.push(wp.UM.member_directory.map.blocks[s].markers[r][e]),o.addMarker(wp.UM.member_directory.map.blocks[s].markers[r][e])})}),o.addListener("spiderfy",function(e){void 0!==wp.UM.member_directory.map.blocks[s].infowindows&&jQuery.each(wp.UM.member_directory.map.blocks[s].infowindows,function(r){jQuery.each(wp.UM.member_directory.map.blocks[s].infowindows[r],function(e){wp.UM.member_directory.map.blocks[s].infowindows[r][e].close()})})}),o.addListener("unspiderfy",function(e){void 0!==wp.UM.member_directory.map.blocks[s].infowindows&&jQuery.each(wp.UM.member_directory.map.blocks[s].infowindows,function(r){jQuery.each(wp.UM.member_directory.map.blocks[s].infowindows[r],function(e){wp.UM.member_directory.map.blocks[s].infowindows[r][e].close()})})}));wp.hooks.applyFilters("um_member_directory_disable_clustering",!1,m)||(r=wp.hooks.applyFilters("um_user_locations_marker_clustering_options",{imagePath:um_user_location_map.cluster_url},s,i,a),wp.UM.member_directory.map.blocks[s].marker_clusterer=new MarkerClusterer(i,a,r),wp.UM.member_directory.map.blocks[s].marker_clusterer.setMaxZoom(20),google.maps.event.addListener(wp.UM.member_directory.map.blocks[s].marker_clusterer,"clusterclick",function(e){var r=e.getMarkers()[0];setTimeout(function(){google.maps.event.trigger(r,"click")},1e3)}))}},blocks:[],listeners:[],classes:{hidden:"um-member-directory-hidden-map"}},wp.hooks.addAction("um_member_directory_clear_not_searched","um_user_locations",function(e){e.find(".um-member-directory-map").length&&e.find(".um-member-directory-map").addClass(wp.UM.member_directory.map.classes.hidden)}),wp.hooks.addFilter("um_member_directory_get_members_allow","um_user_locations",function(e,r,o){return e=!o.find(".um-member-directory-map").length||void 0!==wp.UM.member_directory.map.blocks[r]&&void 0!==wp.UM.member_directory.map.blocks[r].map?e:!1},10),wp.hooks.addFilter("um_member_directory_generate_header","um_user_locations",function(e,r){return e=r.find(".um-member-directory-map").length?!0:e},10),wp.hooks.addAction("um_member_directory_loaded","um_user_locations",function(e,r){e.find(".um-member-directory-map").length&&("undefined"==typeof google?um_whengoogleloadeddo(um_waiting_response_handler,[e,r]):wp.UM.member_directory.map.ajax.response(e,r))},10),wp.hooks.addFilter("um_member_directory_filter_request","um_user_locations",function(e){var r=jQuery('.um-directory[data-hash="'+e.directory_id+'"]');return e=r.length?wp.UM.member_directory.map.ajax.request(e,r):e},10),wp.hooks.addFilter("um_member_directory_ignore_after_search","um_user_locations",function(e,r){var o;if(r.find(".um-member-directory-map").length)return e=wp.UM.member_directory.map.is_dynamically_searched(r)&&(o=um_get_data_for_directory(r,"map_ne"),r=um_get_data_for_directory(r,"map_sw"),void 0!==o)&&void 0!==r?!0:e},10),wp.hooks.addAction("um_member_directory_on_init","um_user_locations",function(e,_){e.find(".um_current_user_location").on("click",function(){var s=jQuery(this),i=s.parents(".um-directory"),m=s.siblings(".um_user_location_g_autocomplete");return!navigator.geolocation&&um_user_location_map.is_ssl||navigator.geolocation.getCurrentPosition(function(t){void 0!==t.coords&&(geocoder=new google.maps.Geocoder).geocode({latLng:new google.maps.LatLng(t.coords.latitude,t.coords.longitude),region:um_user_location_map.region},function(a,e){var r,o;e==google.maps.GeocoderStatus.OK?void 0!==a[0].formatted_address&&(e=a[0].formatted_address,m.val(e),um_is_directory_busy(i)||(um_members_show_preloader(i),um_set_url_from_data(i,"location_search",e),i.find(".um-member-directory-map").length&&(i.find(".um-member-directory-map").removeClass(wp.UM.member_directory.map.classes.hidden),wp.UM.member_directory.map.blocks[_].map.setCenter({lat:parseFloat(t.coords.latitude),lng:parseFloat(t.coords.longitude)}),wp.UM.member_directory.map.blocks[_].map.fitBounds(a[0].geometry.viewport),wp.UM.member_directory.map.is_dynamically_searched(i)?(r=wp.UM.member_directory.map.blocks[_].map.getZoom(),e=a[0].geometry.viewport.getNorthEast(),o=a[0].geometry.viewport.getSouthWest(),um_set_url_from_data(i,"map_ne",e.lat()+","+e.lng()),um_set_url_from_data(i,"map_sw",o.lat()+","+o.lng()),um_set_url_from_data(i,"map_zoom",r)):wp.UM.member_directory.map.is_searched(i)&&(r=wp.UM.member_directory.map.blocks[_].map.getZoom(),um_set_url_from_data(i,"search_lat",parseFloat(t.coords.latitude)),um_set_url_from_data(i,"search_lng",parseFloat(t.coords.longitude)),um_set_url_from_data(i,"map_zoom",r))),i.data("page",1),um_set_url_from_data(i,"page",""),um_ajax_get_members(i),i.data("searched",1),i.find(".um-member-directory-sorting-options").prop("disabled",!1),i.find(".um-member-directory-view-type").removeClass("um-disabled"))):(jQuery.support.cors=!0,jQuery.ajax({url:"https://freegeoip.app/json/",type:"GET",crossDomain:!0,dataType:"jsonp",success:function(e){var r,o,m;void 0!==e.country_name?(e=e.country_name+","+e.region_name+","+e.city,s.prev().val(e),um_is_directory_busy(i)||(um_members_show_preloader(i),i.find(".um-member-directory-map").length&&(i.find(".um-member-directory-map").removeClass(wp.UM.member_directory.map.classes.hidden),wp.UM.member_directory.map.blocks[_].map.setCenter({lat:parseFloat(t.coords.latitude),lng:parseFloat(t.coords.longitude)}),wp.UM.member_directory.map.blocks[_].map.fitBounds(a[0].geometry.viewport),wp.UM.member_directory.map.is_dynamically_searched(i)?(r=wp.UM.member_directory.map.blocks[_].map.getZoom(),o=a[0].geometry.viewport.getNorthEast(),m=a[0].geometry.viewport.getSouthWest(),um_set_url_from_data(i,"map_ne",o.lat()+","+o.lng()),um_set_url_from_data(i,"map_sw",m.lat()+","+m.lng()),um_set_url_from_data(i,"map_zoom",r)):wp.UM.member_directory.map.is_searched(i)&&(r=wp.UM.member_directory.map.blocks[_].map.getZoom(),um_set_url_from_data(i,"search_lat",parseFloat(t.coords.latitude)),um_set_url_from_data(i,"search_lng",parseFloat(t.coords.longitude)),um_set_url_from_data(i,"map_zoom",r))),um_set_url_from_data(i,"location_search",e),i.data("page",1),um_set_url_from_data(i,"page",""),um_ajax_get_members(i),i.data("searched",1),i.find(".um-member-directory-sorting-options").prop("disabled",!1),i.find(".um-member-directory-view-type").removeClass("um-disabled"))):alert(wp.i18n.__("Can not get your current location","um-user-locations"))},error:function(e){alert(wp.i18n.__("Can not get your current location","um-user-locations"))}}))})}),!1})},10);var um_ul_script=document.createElement("script");um_ul_script.src="//maps.googleapis.com/maps/api/js?key="+um_user_location_map.api_key+"&libraries=places&callback=UMInitUserLocationDirectory",um_user_location_map.region&&(um_ul_script.src+="&language="+um_user_location_map.region),document.body.appendChild(um_ul_script);