<?php
/**
 * Template for the UM User Photos, the "Delete Album" modal content
 *
 * Page: "Profile", tab "Photos"
 * @version 2.1.8
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/modal/delete-album.php
 * @var object $comment
 * @var string $message
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
?>

<div class="um-form">
	<form>
		<div class="um-galley-form-response"></div>
		<h3 class="text-center" style="padding-top:0;margin-top:0;font-size:17px;margin-bottom:20px;">
			<?php echo esc_html( $message ); ?>
		</h3>
		<div class="um-clearfix"></div>
		<div class="um-user-photos-modal-footer text-right">

			<?php if ( ! empty( $comment ) ) { ?>
				<button
					data-wpnonce="<?php echo esc_attr( wp_create_nonce( 'um_user_photos_comment_delete' ) ); ?>"
					id="delete-um-user-photos-comment"
					class="um-modal-btn"
					data-id="<?php echo esc_attr( $comment->comment_ID ); ?>"
					data-action="um_user_photos_delete_comment"
					><?php esc_html_e( 'Delete', 'um-user-photos' ); ?></button>
			<?php } ?>

			<a href="javascript:void(0);" class="um-modal-btn alt um-user-photos-modal-close-link">
				<?php esc_html_e( 'Cancel', 'um-user-photos' ); ?>
			</a>
		</div>
	</form>
</div>
