(()=>{var e={6941:(e,n,t)=>{n.formatArgs=function(n){if(n[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+n[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const t="color: "+this.color;n.splice(1,0,t,"color: inherit");let o=0,r=0;n[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(o++,"%c"===e&&(r=o))})),n.splice(r,0,t)},n.save=function(e){try{e?n.storage.setItem("debug",e):n.storage.removeItem("debug")}catch(e){}},n.load=function(){let e;try{e=n.storage.getItem("debug")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},n.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},n.storage=function(){try{return localStorage}catch(e){}}(),n.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),n.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],n.log=console.debug||console.log||(()=>{}),e.exports=t(3212)(n);const{formatters:o}=e.exports;o.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},3212:(e,n,t)=>{e.exports=function(e){function n(e){let t,r,s,i=null;function a(...e){if(!a.enabled)return;const o=a,r=Number(new Date),s=r-(t||r);o.diff=s,o.prev=t,o.curr=r,t=r,e[0]=n.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((t,r)=>{if("%%"===t)return"%";i++;const s=n.formatters[r];if("function"==typeof s){const n=e[i];t=s.call(o,n),e.splice(i,1),i--}return t})),n.formatArgs.call(o,e);(o.log||n.log).apply(o,e)}return a.namespace=e,a.useColors=n.useColors(),a.color=n.selectColor(e),a.extend=o,a.destroy=n.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==i?i:(r!==n.namespaces&&(r=n.namespaces,s=n.enabled(e)),s),set:e=>{i=e}}),"function"==typeof n.init&&n.init(a),a}function o(e,t){const o=n(this.namespace+(void 0===t?":":t)+e);return o.log=this.log,o}function r(e,n){let t=0,o=0,r=-1,s=0;for(;t<e.length;)if(o<n.length&&(n[o]===e[t]||"*"===n[o]))"*"===n[o]?(r=o,s=t,o++):(t++,o++);else{if(-1===r)return!1;o=r+1,s++,t=s}for(;o<n.length&&"*"===n[o];)o++;return o===n.length}return n.debug=n,n.default=n,n.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},n.disable=function(){const e=[...n.names,...n.skips.map((e=>"-"+e))].join(",");return n.enable(""),e},n.enable=function(e){n.save(e),n.namespaces=e,n.names=[],n.skips=[];const t=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of t)"-"===e[0]?n.skips.push(e.slice(1)):n.names.push(e)},n.enabled=function(e){for(const t of n.skips)if(r(e,t))return!1;for(const t of n.names)if(r(e,t))return!0;return!1},n.humanize=t(4997),n.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((t=>{n[t]=e[t]})),n.names=[],n.skips=[],n.formatters={},n.selectColor=function(e){let t=0;for(let n=0;n<e.length;n++)t=(t<<5)-t+e.charCodeAt(n),t|=0;return n.colors[Math.abs(t)%n.colors.length]},n.enable(n.load()),n}},4997:e=>{var n=1e3,t=60*n,o=60*t,r=24*o,s=7*r,i=365.25*r;function a(e,n,t,o){var r=n>=1.5*t;return Math.round(e/t)+" "+o+(r?"s":"")}e.exports=function(e,c){c=c||{};var u=typeof e;if("string"===u&&e.length>0)return function(e){if((e=String(e)).length>100)return;var a=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!a)return;var c=parseFloat(a[1]);switch((a[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return c*i;case"weeks":case"week":case"w":return c*s;case"days":case"day":case"d":return c*r;case"hours":case"hour":case"hrs":case"hr":case"h":return c*o;case"minutes":case"minute":case"mins":case"min":case"m":return c*t;case"seconds":case"second":case"secs":case"sec":case"s":return c*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}(e);if("number"===u&&isFinite(e))return c.long?function(e){var s=Math.abs(e);if(s>=r)return a(e,s,r,"day");if(s>=o)return a(e,s,o,"hour");if(s>=t)return a(e,s,t,"minute");if(s>=n)return a(e,s,n,"second");return e+" ms"}(e):function(e){var s=Math.abs(e);if(s>=r)return Math.round(e/r)+"d";if(s>=o)return Math.round(e/o)+"h";if(s>=t)return Math.round(e/t)+"m";if(s>=n)return Math.round(e/n)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},372:(e,n,t)=>{"use strict";t.d(n,{A:()=>c});var o=t(6941);const r=t.n(o)()("dops:analytics");let s,i;window._tkq=window._tkq||[],window.ga=window.ga||function(){(window.ga.q=window.ga.q||[]).push(arguments)},window.ga.l=+new Date;const a={initialize:function(e,n,t){a.setUser(e,n),a.setSuperProps(t),a.identifyUser()},setGoogleAnalyticsEnabled:function(e,n=null){this.googleAnalyticsEnabled=e,this.googleAnalyticsKey=n},setMcAnalyticsEnabled:function(e){this.mcAnalyticsEnabled=e},setUser:function(e,n){i={ID:e,username:n}},setSuperProps:function(e){s=e},assignSuperProps:function(e){s=Object.assign(s||{},e)},mc:{bumpStat:function(e,n){const t=function(e,n){let t="";if("object"==typeof e){for(const n in e)t+="&x_"+encodeURIComponent(n)+"="+encodeURIComponent(e[n]);r("Bumping stats %o",e)}else t="&x_"+encodeURIComponent(e)+"="+encodeURIComponent(n),r('Bumping stat "%s" in group "%s"',n,e);return t}(e,n);a.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom-no-pv"+t+"&t="+Math.random())},bumpStatWithPageView:function(e,n){const t=function(e,n){let t="";if("object"==typeof e){for(const n in e)t+="&"+encodeURIComponent(n)+"="+encodeURIComponent(e[n]);r("Built stats %o",e)}else t="&"+encodeURIComponent(e)+"="+encodeURIComponent(n),r('Built stat "%s" in group "%s"',n,e);return t}(e,n);a.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom"+t+"&t="+Math.random())}},pageView:{record:function(e,n){a.tracks.recordPageView(e),a.ga.recordPageView(e,n)}},purchase:{record:function(e,n,t,o,r,s,i){a.ga.recordPurchase(e,n,t,o,r,s,i)}},tracks:{recordEvent:function(e,n){n=n||{},0===e.indexOf("akismet_")||0===e.indexOf("jetpack_")?(s&&(r("- Super Props: %o",s),n=Object.assign(n,s)),r('Record event "%s" called with props %s',e,JSON.stringify(n)),window._tkq.push(["recordEvent",e,n])):r('- Event name must be prefixed by "akismet_" or "jetpack_"')},recordJetpackClick:function(e){const n="object"==typeof e?e:{target:e};a.tracks.recordEvent("jetpack_wpa_click",n)},recordPageView:function(e){a.tracks.recordEvent("akismet_page_view",{path:e})},setOptOut:function(e){r("Pushing setOptOut: %o",e),window._tkq.push(["setOptOut",e])}},ga:{initialized:!1,initialize:function(){let e={};a.ga.initialized||(i&&(e={userId:"u-"+i.ID}),window.ga("create",this.googleAnalyticsKey,"auto",e),a.ga.initialized=!0)},recordPageView:function(e,n){a.ga.initialize(),r("Recording Page View ~ [URL: "+e+"] [Title: "+n+"]"),this.googleAnalyticsEnabled&&(window.ga("set","page",e),window.ga("send",{hitType:"pageview",page:e,title:n}))},recordEvent:function(e,n,t,o){a.ga.initialize();let s="Recording Event ~ [Category: "+e+"] [Action: "+n+"]";void 0!==t&&(s+=" [Option Label: "+t+"]"),void 0!==o&&(s+=" [Option Value: "+o+"]"),r(s),this.googleAnalyticsEnabled&&window.ga("send","event",e,n,t,o)},recordPurchase:function(e,n,t,o,r,s,i){window.ga("require","ecommerce"),window.ga("ecommerce:addTransaction",{id:e,revenue:o,currency:i}),window.ga("ecommerce:addItem",{id:e,name:n,sku:t,price:r,quantity:s}),window.ga("ecommerce:send")}},identifyUser:function(){i&&window._tkq.push(["identifyUser",i.ID,i.username])},setProperties:function(e){window._tkq.push(["setProperties",e])},clearedIdentity:function(){window._tkq.push(["clearIdentity"])}},c=a},7723:e=>{"use strict";e.exports=window.wp.i18n}},n={};function t(o){var r=n[o];if(void 0!==r)return r.exports;var s=n[o]={exports:{}};return e[o](s,s.exports,t),s.exports}t.n=e=>{var n=e&&e.__esModule?()=>e.default:()=>e;return t.d(n,{a:n}),n},t.d=(e,n)=>{for(var o in n)t.o(n,o)&&!t.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:n[o]})},t.o=(e,n)=>Object.prototype.hasOwnProperty.call(e,n),(()=>{"use strict";var e=t(372),n=t(7723);const __=n.__;document.addEventListener("DOMContentLoaded",(function(){const n=document.querySelector("a.page-title-action");if(n){const t=document.createElement("div");t.className="wpcom-media-library-action-buttons";const o=document.createElement("a");o.className="button-secondary",o.role="button",o.innerHTML=__("Import Media","jetpack-external-media"),o.href=window.JETPACK_EXTERNAL_MEDIA_IMPORT_BUTTON?.href,o.onclick=function(){e.A.tracks.recordEvent("jetpack_external_media_import_media_button_click",{page:"media-library"})};const r=n.parentNode,s=n.nextSibling;t.appendChild(n),t.appendChild(o),r.insertBefore(t,s)}}))})()})();