<?php
namespace um_ext\um_user_bookmarks\core;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Bookmark_Shortcode
 * @package um_ext\um_user_bookmarks\core
 */
class Bookmark_Shortcode {

	/**
	 * Bookmark_Shortcode constructor.
	 */
	public function __construct() {
		add_shortcode( 'um_user_bookmarks', array( $this, 'um_user_bookmarks_shortcode_html' ) );
		add_shortcode( 'um_user_bookmarks_all', array( $this, 'um_user_bookmarks_shortcode_all' ) );
		if ( ! shortcode_exists( 'um_bookmarks_button' ) ) {
			add_shortcode( 'um_bookmarks_button', array( $this, 'um_bookmarks_button_shortcode' ) );
		}
	}

	/**
	 * Shortcode: "Bookmark" template for the post
	 *
	 * @example [um_bookmarks_button post_id=""]
	 *
	 * @param array $atts
	 *		null|int post_id
	 *
	 * @return string
	 */
	public function um_bookmarks_button_shortcode( $atts = array() ) {
		$args = shortcode_atts(
			array(
				'post_id' => get_the_ID(),
			),
			$atts,
			'um_bookmarks_button'
		);

		$button = UM()->User_Bookmarks()->common()->get_bookmarks_button( absint( $args['post_id'] ), false );

		return $button;
	}

	/**
	 * Shortcode [um_user_bookmarks_all] displays all bookmarks for the user.
	 *
	 * @since 2.1.3
	 *
	 * @param array $atts Shortcode attributes.
	 *
	 * @return string
	 */
	public function um_user_bookmarks_shortcode_all( $atts = array() ) {
		$args = shortcode_atts(
			array(
				'user_id' => um_profile_id(),
			),
			$atts,
			'um_user_bookmarks_all'
		);

		if ( empty( $args['user_id'] ) ) {
			return '';
		}

		$user_id  = absint( $args['user_id'] );
		$user_obj = get_userdata( $user_id );
		if ( empty( $user_obj ) ) {
			return '';
		}

		$temp_user = um_user( 'ID' );
		um_fetch_user( $user_id );

		if ( ! um_user( 'enable_bookmark' ) ) {
			um_fetch_user( $temp_user );
			return '';
		}
		um_fetch_user( $temp_user );

		$html = '<div class="um um-profile">'
			. '<div class="um-profile-body shortcode">' // needed for styles.
			. UM()->User_Bookmarks()->profile()->get_user_profile_bookmarks_all( $user_id )
			. '</div>'
			. '</div>';

		return $html;
	}

	/**
	 * Shortcode callback to display Bookmark folder view.
	 *
	 * @param array $atts
	 *
	 * @return string
	 */
	public function um_user_bookmarks_shortcode_html( $atts = array() ) {
		$args = shortcode_atts(
			array(
				'user_id' => um_profile_id(),
			),
			$atts,
			'um_user_bookmarks'
		);

		if ( empty( $args['user_id'] ) ) {
			return '';
		}

		$user_id  = absint( $args['user_id'] );
		$user_obj = get_userdata( $user_id );
		if ( empty( $user_obj ) ) {
			return '';
		}

		// Display all bookmarks if the folder system is disabled.
		if ( UM()->options()->get( 'um_user_bookmarks_disable_folders' ) ) {
			return $this->um_user_bookmarks_shortcode_all( $atts );
		}

		// Use requested User ID or current User ID if attribute `user_id` is empty.
		$profile_id = empty( $atts['user_id'] ) ? um_profile_id() : absint( $atts['user_id'] );
		if ( empty( $profile_id ) ) {
			return '';
		}

		$include_private = false;
		if ( is_user_logged_in() && get_current_user_id() === $user_id ) {
			$include_private = true;
		}

		$user_bookmarks = get_user_meta( $user_id, '_um_user_bookmarks', true );
		if ( ! $user_bookmarks ) {
			return __( 'No bookmarks have been added.', 'um-user-bookmarks' );
		}

		wp_enqueue_script( 'um-user-bookmarks' );
		wp_enqueue_style( 'um-user-bookmarks' );

		ob_start(); ?>

		<div class="um-profile-body bookmarks-default shortcode">
			<?php
			if ( UM()->User_Bookmarks()->user_can_view_bookmark( $user_id ) ) {
				// Load plugin view : templates/profile/folder-view.php
				// Load theme view : ultimate-member/bookmarks/profile/folder-view.php
				UM()->get_template(
					'profile/folder-view.php',
					um_user_bookmarks_plugin,
					array(
						'user_bookmarks'  => $user_bookmarks,
						'include_private' => $include_private,
						'profile_id'      => $user_id,
					),
					true
				);
			}
			?>
			<div class="um-clear"></div>
		</div>
		<?php
		return ob_get_clean();
	}
}
