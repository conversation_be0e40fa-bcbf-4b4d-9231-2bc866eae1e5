<?php
namespace um_ext\um_user_photos\ajax;

use WP_Query;
use ZipArchive;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Photos
 *
 * @package um_ext\um_user_photos\ajax
 */
class Photos {

	/**
	 * Photos constructor.
	 */
	public function __construct() {
		// like photo
		add_action( 'wp_ajax_um_user_photos_like_photo', array( $this, 'um_user_photos_like_photo' ) );
		// unlike photo
		add_action( 'wp_ajax_um_user_photos_unlike_photo', array( $this, 'um_user_photos_unlike_photo' ) );
		// show photo likes modal
		add_action( 'wp_ajax_get_um_user_photo_likes', array( $this, 'get_um_user_photo_likes' ) );
		add_action( 'wp_ajax_nopriv_get_um_user_photo_likes', array( $this, 'get_um_user_photo_likes' ) );

		// download all photos
		add_action( 'wp_ajax_download_my_photos', array( $this, 'download_my_photos' ) );

		// load images
		add_action( 'wp_ajax_um_user_photos_load_more', array( $this, 'um_user_photos_load_more' ) );
		add_action( 'wp_ajax_nopriv_um_user_photos_load_more', array( $this, 'um_user_photos_load_more' ) );

		// load single album images
		add_action( 'wp_ajax_um_user_photos_album_load_more', array( $this, 'um_user_photos_album_load_more' ) );
		add_action( 'wp_ajax_nopriv_um_user_photos_album_load_more', array( $this, 'um_user_photos_album_load_more' ) );

		// Edit Photo modal
		add_action( 'wp_ajax_um_user_photos_edit_photo_modal', array( $this, 'load_edit_photo_modal' ) );
		// update image data
		add_action( 'wp_ajax_um_user_photos_image_update', array( $this, 'update_image' ) );

		// load comments section
		add_action( 'wp_ajax_um_user_photos_get_comment_section', array( $this, 'um_user_photos_get_comment_section' ) );
		add_action( 'wp_ajax_nopriv_um_user_photos_get_comment_section', array( $this, 'um_user_photos_get_comment_section' ) );
	}

	/**
	 * Like photo
	 */
	public function um_user_photos_like_photo() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_like_photo' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$post_id = absint( $_POST['postid'] );

		if ( ! UM()->User_Photos()->common()->user()->can_like_photo( $post_id ) ) {
			wp_send_json_error( __( 'You are not authorized to like this photo.', 'um-user-photos' ) );
		}

		$liked = UM()->User_Photos()->common()->photo()->like( $post_id );
		if ( false === $liked ) {
			wp_send_json_error( __( 'Something went wrong. Cannot like.', 'um-user-photos' ) );
		}

		$content = UM()->frontend()::layouts()::avatars_list(
			$liked,
			array(
				'wrapper' => 'span',
				'size'    => 's',
				'count'   => 5,
			)
		);
		wp_send_json_success(
			array(
				'count'   => count( $liked ),
				'content' => UM()->ajax()->esc_html_spaces( $content ),
			)
		);
	}

	/**
	 * Unlike photo
	 */
	public function um_user_photos_unlike_photo() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_unlike_photo' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$post_id = absint( $_POST['postid'] );

		if ( ! UM()->User_Photos()->common()->user()->can_unlike_photo( $post_id ) ) {
			wp_send_json_error( __( 'You are not authorized to unlike this photo.', 'um-user-photos' ) );
		}

		$liked = UM()->User_Photos()->common()->photo()->unlike( $post_id );
		if ( false === $liked ) {
			wp_send_json_error( __( 'Something went wrong. Cannot unlike.', 'um-user-photos' ) );
		}
		$content = '';
		if ( ! empty( $liked ) ) {
			$content = UM()->frontend()::layouts()::avatars_list(
				$liked,
				array(
					'wrapper' => 'span',
					'size'    => 's',
					'count'   => 5,
				)
			);
		}
		wp_send_json_success(
			array(
				'count'   => count( $liked ),
				'content' => UM()->ajax()->esc_html_spaces( $content ),
			)
		);
	}

	/**
	 * Show photo likes.
	 */
	public function get_um_user_photo_likes() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_show_likes' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$post_id = absint( $_POST['image_id'] );

		if ( ! UM()->User_Photos()->common()->user()->can_view_photo_likes( $post_id ) ) {
			wp_send_json_error( __( 'You are not authorized for this.', 'um-user-photos' ) );
		}

		$likes = UM()->User_Photos()->common()->photo()->get_likes( $post_id );
		if ( false === $likes ) {
			wp_send_json_error( __( 'Cannot get likes. Maybe photo doesn\'t exist.', 'um-user-photos' ) );
		}

		$content = UM()->ajax()->esc_html_spaces(
			UM()->get_template(
				'v3/modal/likes.php',
				UM_USER_PHOTOS_PLUGIN,
				array(
					'likes'   => $likes,
					'context' => 'photo',
				)
			)
		);

		wp_send_json_success( array( 'content' => $content ) );
	}

	/**
	 * Update Image.
	 */
	public function update_image() {
		// phpcs:disable WordPress.Security.NonceVerification
		if ( empty( absint( $_POST['id'] ) ) ) {
			$message = UM()->frontend()::layouts()::alert(
				__( 'Invalid image ID', 'um-user-photos' ),
				array(
					'underline' => false,
				)
			);
			wp_send_json_error( $message );
		}
		// phpcs:enable WordPress.Security.NonceVerification

		$id = absint( $_POST['id'] );
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_edit_image' . $id ) ) {
			$message = UM()->frontend()::layouts()::alert(
				__( 'Wrong nonce', 'um-user-photos' ),
				array(
					'underline' => false,
				)
			);
			wp_send_json_error( $message );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_edit_photo( $id ) ) {
			$message = UM()->frontend()::layouts()::alert(
				__( 'You cannot edit this photo.', 'um-user-photos' ),
				array(
					'underline' => false,
				)
			);
			wp_send_json_error( $message );
		}

		if ( ! isset( $_POST['title'] ) || '' === sanitize_text_field( $_POST['title'] ) ) {
			wp_send_json_error(
				array(
					'field'   => 'image-title-' . $id,
					'message' => __( 'Title is required', 'um-user-notes' ),
				)
			);
		}
		$title = sanitize_text_field( $_POST['title'] );

		wp_update_post(
			array(
				'ID'           => $id,
				'post_title'   => $title,
				'post_excerpt' => sanitize_textarea_field( $_POST['caption'] ),
			)
		);

		if ( ! empty( $_POST['link'] ) ) {
			$link = esc_url_raw( $_POST['link'] );
			update_post_meta( $id, '_link', $link );
		} else {
			delete_post_meta( $id, '_link' );
		}

		if ( ! UM()->options()->get( 'um_user_photos_disable_comments' ) ) {
			if ( ! empty( $_POST['disable_comments'] ) ) {
				update_post_meta( $id, '_disable_comment', true );
			} else {
				update_post_meta( $id, '_disable_comment', false );
			}
		} else {
			delete_post_meta( $id, '_disable_comment' );
		}

		$album_id = absint( $_POST['album'] );
		if ( ! UM()->options()->get( 'um_user_photos_disable_cover' ) ) {
			$cover_id = get_post_meta( $album_id, '_thumbnail_id', true );
			if ( empty( $_POST['cover_photo'] ) ) {
				if ( absint( $cover_id ) === absint( $id ) ) {
					$photos = get_post_meta( $album_id, '_photos', true );
					update_post_meta( $album_id, '_thumbnail_id', absint( $photos[0] ) );
				}
			} else {
				update_post_meta( $album_id, '_thumbnail_id', absint( $_POST['cover_photo'] ) );
			}
		} else {
			delete_post_meta( $album_id, '_thumbnail_id' );
		}

		do_action( 'um_user_photos_after_photo_updated', $id );

		$message = UM()->frontend()::layouts()::alert(
			__( 'Updated successfully.', 'um-user-photos' ),
			array(
				'type'      => 'success',
				'underline' => false,
			)
		);

		wp_send_json_success(
			array(
				'title'   => esc_attr( $title ),
				'link'    => ! empty( $link ) ? $link : '',
				'message' => $message,
			)
		);
	}

	/**
	 * Download all photos
	 */
	public function download_my_photos() {
		if ( ! wp_verify_nonce( $_REQUEST['_wpnonce'], 'um_user_photos_download_all' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$notice = '';
		if ( ! class_exists( '\ZipArchive' ) ) {
			$notice = __( 'Your download could not be created. It looks like you do not have ZipArchive installed on your server.', 'um-user-photos' );
		}

		$user_id = get_current_user_id();

		if ( empty( $notice ) ) {
			$photos = new WP_Query(
				array(
					'post_type'      => 'attachment',
					'author__in'     => array( $user_id ),
					'post_status'    => 'inherit',
					'post_mime_type' => 'image',
					'posts_per_page' => -1,
					'meta_query'     => array(
						array(
							'key'     => '_part_of_gallery',
							'value'   => 'yes',
							'compare' => '=',
						),
					),
				)
			);

			if ( $photos->have_posts() ) {

				$zip         = new ZipArchive();
				$zip_name    = time() . '.zip';
				$uploads_dir = WP_CONTENT_DIR . '/uploads/user-photos';
				if ( ! is_dir( $uploads_dir ) ) {
					mkdir( $uploads_dir );
				}
				$new_zip    = $uploads_dir . '/' . $zip_name;
				$zip_opened = $zip->open( $new_zip, ZipArchive::CREATE );

				while ( $photos->have_posts() ) {
					$photos->the_post();
					$file_path  = get_attached_file( get_the_ID(), true );
					$file_type  = $filetype = wp_check_filetype( $file_path );
					$ext        = $file_type['ext'];
					$image_name = get_the_title() . '.' . $ext;
					if ( file_exists( $file_path ) ) {
						$zip->addFile( $file_path, $image_name );
					}
				}

				$zip->close();
				header( 'Content-type:application/zip' );
				header( 'Content-Disposition: attachment; filename=' . $zip_name );
				readfile( $new_zip );
				unlink( $new_zip );
				die;
			}

			$notice = __( 'Nothing to download', 'um-user-photos' );
			wp_reset_postdata();
		}

		if ( $notice ) {
			// @todo review this place and remove using usermeta and return. Use schedule event instead in the future. ZIP archive creation can be heavy. And this button will start the creation process.
			// @todo Important thing that we need remove these zip files as soon as period for download is expired.
			update_user_meta( $user_id, 'um_download_my_photos_notice', $notice );
			if ( ! empty( $_SERVER['HTTP_REFERER'] ) ) {
				$location = $_SERVER['HTTP_REFERER'];
			} else {
				$location = um_get_core_page( 'account' ) . 'um_user_photos';
			}
			wp_safe_redirect( esc_url_raw( $location ) );
		}
	}

	/**
	 * Load more photos with ajax
	 */
	public function um_user_photos_load_more() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_load_more' ) ) {
			wp_send_json_error( esc_html__( 'Wrong Nonce', 'um-user-photos' ) );
		}

		if ( empty( $_POST['profile'] ) ) {
			wp_send_json_error( esc_html__( 'Wrong user ID.', 'um-user-photos' ) );
		}

		$profile_id = absint( $_POST['profile'] );
		$per_page   = UM()->User_Photos()->common()->gallery()->photos_per_page;
		$page_no    = ! empty( $_POST['page'] ) ? absint( $_POST['page'] ) : 1;
		$offset     = ( $page_no - 1 ) * $per_page;

		$is_my_profile = ( is_user_logged_in() && get_current_user_id() === $profile_id );
		if ( ! $is_my_profile && ! um_can_view_profile( $profile_id ) ) {
			wp_send_json_error( esc_html__( 'Nothing to display', 'um-user-photos' ) );
		}

		// Disable posts query filter by the taxonomy 'language'. Integration with the plugin 'Polylang'.
		add_action( 'pre_get_posts', array( UM()->User_Photos()->common()->query(), 'remove_language_filter' ), 9 );

		$query_args = array(
			'post_type'      => 'attachment',
			'author__in'     => array( $profile_id ),
			'post_status'    => 'inherit',
			'post_mime_type' => 'image',
			'posts_per_page' => $per_page,
			'offset'         => $offset,
			'meta_query'     => array(
				array(
					'key'     => '_part_of_gallery',
					'value'   => 'yes',
					'compare' => '=',
				),
			),
			'orderby'        => 'ID',
		);

		$args = array(
			'post_type'      => 'um_user_photos',
			'author__in'     => array( $profile_id ),
			'posts_per_page' => -1,
			'post_status'    => 'publish',
			'fields'         => 'ids',
		);
		$args = apply_filters( 'um_user_photo_query_args', $args, $profile_id );

		$albums = new WP_Query( $args );

		if ( ! $is_my_profile ) {
			$visible_photos = array();
			if ( ! empty( $albums->posts ) ) {
				foreach ( $albums->posts as $album_id ) {
					$photos_query_args  = array(
						'post_type'      => 'attachment',
						'author__in'     => array( $profile_id ),
						'post_status'    => 'inherit',
						'post_mime_type' => 'image',
						'posts_per_page' => -1,
						'meta_query'     => array(
							array(
								'key'     => '_part_of_gallery',
								'value'   => 'yes',
								'compare' => '=',
							),
						),
						'orderby'        => 'ID',
						'post_parent'    => $album_id,
						'fields'         => 'ids',
					);
					$album_photos_query = new WP_Query( $photos_query_args );
					$photos_result      = $album_photos_query->get_posts();

					if ( ! empty( $photos_result ) ) {
						$visible_photos[] = $photos_result;
					}
				}

				$visible_photos = array_merge( ...$visible_photos );
				$visible_photos = array_unique( $visible_photos );
			}

			if ( empty( $visible_photos ) ) {
				wp_send_json_success( '' );
			}

			$query_args['post__in'] = $visible_photos;
		}

		$latest_photos = new WP_Query( $query_args );

		if ( $latest_photos->have_posts() ) {
			$photos = array();
			foreach ( $latest_photos->posts as $photo ) {
				$photos[] = $photo->ID;
			}

			$args_t = compact( 'is_my_profile', 'photos' );
			$html   = UM()->ajax()->esc_html_spaces( UM()->get_template( 'v3/photos-grid.php', UM_USER_PHOTOS_PLUGIN, $args_t ) );

			wp_reset_postdata();
			wp_send_json_success( $html );
		}

		wp_reset_postdata();
		wp_send_json_success( '' );
	}

	/**
	 * Load more album photos with ajax
	 */
	public function um_user_photos_album_load_more() {
		if ( empty( $_POST['album_id'] ) ) {
			wp_send_json_error( __( 'Wrong album ID.', 'um-user-photos' ) );
		}
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'single_album_load_more' ) ) {
			wp_send_json_error( esc_html__( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$album_id = absint( $_POST['album_id'] );
		if ( ! UM()->User_Photos()->common()->user()->can_view_album( $album_id ) ) {
			wp_send_json_error( esc_html__( 'Nothing to display', 'um-user-photos' ) );
		}

		$album         = get_post( $album_id );
		$is_my_profile = ( is_user_logged_in() && get_current_user_id() === absint( $album->post_author ) );

		$offset = 0;
		if ( ! empty( $_POST['offset'] ) ) {
			$offset = $_POST['offset'];
		}
		$photos = get_post_meta( $album_id, '_photos', true );
		if ( empty( $photos ) ) {
			$photos = array();
		}
		$per_page = UM()->User_Photos()->common()->gallery()->photos_per_page;
		$photos   = array_slice( $photos, $offset, $per_page );

		$output = '';
		if ( ! empty( $photos ) ) {
			$args_t  = compact( 'photos', 'is_my_profile' );
			$output .= UM()->get_template( 'v3/photos-grid.php', UM_USER_PHOTOS_PLUGIN, $args_t );
		}

		wp_send_json_success( $output );
	}

	/**
	 * Load edit photo modal content.
	 */
	public function load_edit_photo_modal() {
		if ( empty( $_POST['image_id'] ) ) {
			wp_send_json_error( __( 'Wrong photo ID.', 'um-user-photos' ) );
		}
		$photo_id = absint( $_POST['image_id'] );

		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_edit_image_modal' . $photo_id ) ) {
			wp_send_json_error( __( 'Wrong nonce.', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_edit_photo( $photo_id ) ) {
			wp_send_json_error( esc_html__( 'You cannot edit this photo.', 'um-user-photos' ) );
		}

		$album_id = get_post_field( 'post_parent', $photo_id );
		if ( 0 === absint( $album_id ) ) {
			$album_id = $photo_id;
		}
		$album = get_post( $album_id );
		$photo = get_post( $photo_id );

		$disable_comment_option = UM()->options()->get( 'um_user_photos_disable_comments' );
		$disable_comment        = get_post_meta( $photo_id, '_disable_comment', true );
		$disable_cover          = UM()->options()->get( 'um_user_photos_disable_cover' );

		$cover_photo = '';
		if ( ! $disable_cover ) {
			$cover_photo = get_post_meta( $album_id, '_thumbnail_id', true );
		}

		$args_t = compact( 'album', 'photo', 'disable_comment_option', 'disable_comment', 'disable_comment', 'disable_cover', 'cover_photo' );
		$html   = UM()->get_template( 'v3/modal/edit-image.php', UM_USER_PHOTOS_PLUGIN, $args_t );

		wp_send_json_success( UM()->ajax()->esc_html_spaces( $html ) );
	}

	/**
	 *
	 */
	public function um_user_photos_get_comment_section() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_get_comment_section' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$post_id = absint( $_POST['image_id'] );

		$context = ! empty( $_POST['context'] ) ? sanitize_key( $_POST['context'] ) : 'gallery';

		if ( ! UM()->User_Photos()->common()->user()->can_view_photo( $post_id ) ) {
			wp_send_json_error( __( 'You are not authorized for this.', 'um-user-photos' ) );
		}

		$disable_comments = get_post_meta( $post_id, '_disable_comment', true );

		$comments       = array();
		$comments_count = 0;
		if ( ! UM()->options()->get( 'um_user_photos_disable_comments' ) && empty( $disable_comments ) ) {
			$comments = UM()->User_Photos()->common()->photo()->get_comments( $post_id );
			if ( empty( $comments ) ) {
				$comments = array();
			}
			$comments_count = wp_count_comments( $post_id );
			$comments_count = $comments_count->approved;
		}

		$image      = get_post( $post_id );
		$image_link = get_post_meta( $post_id, '_link', true );

		$likes = array();
		if ( ! UM()->options()->get( 'um_user_photos_disable_comments' ) ) {
			$likes = UM()->User_Photos()->common()->photo()->get_likes( $post_id );
		}

		$count_photos = 0;
		$album_id     = get_post_field( 'post_parent', $post_id );
		if ( ! empty( $album_id ) ) {
			$photos = get_post_meta( $album_id, '_photos', true );
			if ( empty( $photos ) ) {
				$photos = array();
			}
			$count_photos = count( $photos );
		}

		um_fetch_user( $image->post_author );

		$content = UM()->get_template(
			'v3/caption.php',
			UM_USER_PHOTOS_PLUGIN,
			array(
				'image'            => $image,
				'image_id'         => $post_id,
				'image_link'       => $image_link,
				'likes'            => $likes,
				'likes_count'      => count( $likes ),
				'disable_comments' => (bool) $disable_comments,
				'comments'         => $comments,
				'comment_count'    => $comments_count,
				'count_photos'     => $count_photos,
				'context'          => $context,
			)
		);

		um_reset_user();

		wp_send_json_success( $content );
	}
}
