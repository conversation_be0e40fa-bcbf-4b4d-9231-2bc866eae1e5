{"translation-revision-date": "2024-10-02 16:14:23+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "This may affect your ability to freely move the focal point of the image.": ["This may affect your ability to freely move the focal point of the image."], "Select “Cover” to have the image automatically fit its container.": ["Select “Cover” to have the image automatically fit its container."], "An error has prevented the block from being updated.": ["An error has prevented the block from being updated."], "Fixed background": ["Fixed background"], "Repeated background": ["Repeated background"], "Cover": ["Cover"], "Image fit": ["Image fit"], "Opacity": ["Opacity"], "Alt text (alternative text)": ["Alt text (alternative text)"], "Describe the purpose of the image": ["Describe the purpose of the image"], "Edit category image": ["Edit category image"], "Media settings": ["Media settings"], "Edit selected category": ["Edit selected category"], "%1$s, has %2$d variation": ["%1$s, has %2$d variation", "%1$s, has %2$d variations"], "%1$d variations": ["%1$d variations"], "%1$s, has %2$d review": ["%1$s, has %2$d review", "%1$s, has %2$d reviews"], "%1$s, has %2$d product": ["%1$s, has %2$d product", "%1$s, has %2$d products"], "Loading…": ["Loading…"], "Color": ["Colour"], "Clothing": ["Clothing"], "Branded t-shirts, jumpers, pants and more!": ["Branded t-shirts, jumpers, trousers and more!"], "The following error was returned": ["The following error was returned"], "Sorry, an error occurred": ["Sorry, an error occurred"], "The following error was returned from the API": ["The following error was returned from the API"], "Retry": ["Retry"], "%d review": ["%d review", "%d reviews"], "Search results updated.": ["Search results updated."], "%d item selected": ["%d item selected", "%d items selected"], "Search for items": ["Search for items"], "No results for %s": ["No results for %s"], "No items found.": ["No items found."], "Clear all selected items": ["Clear all selected items"], "Clear all": ["Clear all"], "Remove %s": ["Remove %s"], "%d category selected": ["%d category selected", "%d categories selected"], "All selected categories": ["All selected categories"], "Any selected categories": ["Any selected categories"], "Category search results updated.": ["Category search results updated."], "Clear all product categories": ["Clear all product categories"], "Done": ["Done"], "No product category is selected.": ["No product category is selected."], "Overlay": ["Overlay"], "Pick at least two categories to use this setting.": ["Pick at least two categories to use this setting."], "Product search results updated.": ["Product search results updated."], "Search for a product to display": ["Search for a product to display"], "Search for product categories": ["Search for product categories"], "Shop now": ["Shop now"], "Show description": ["Show description"], "Show price": ["Show price"], "Visually highlight a product category and encourage prompt action.": ["Visually highlight a product category and encourage prompt action."], "Your store doesn't have any product categories.": ["Your store doesn't have any product categories."], "Your store doesn't have any products.": ["Your store doesn't have any products."], "Featured Category": ["Featured Category"], "Showing Featured Product block preview.": ["Showing Featured Product block preview."], "Focal Point Picker": ["Focal Point Picker"], "Display products matching": ["Display products matching"], "Select a category": ["Select a category"], "%d product": ["%d product", "%d products"], "Content": ["Content"], "None": ["None"], "Reset": ["Reset"], "Products": ["Products"], "Product Categories": ["Product Categories"]}}, "comment": {"reference": "assets/client/blocks/featured-category.js"}}