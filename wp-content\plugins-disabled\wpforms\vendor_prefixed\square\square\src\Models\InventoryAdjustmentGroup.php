<?php

declare (strict_types=1);
namespace WPForms\Vendor\Square\Models;

use stdClass;
class InventoryAdjustmentGroup implements \JsonSerializable
{
    /**
     * @var string|null
     */
    private $id;
    /**
     * @var string|null
     */
    private $rootAdjustmentId;
    /**
     * @var string|null
     */
    private $fromState;
    /**
     * @var string|null
     */
    private $toState;
    /**
     * Returns Id.
     * A unique ID generated by Square for the
     * `InventoryAdjustmentGroup`.
     */
    public function getId() : ?string
    {
        return $this->id;
    }
    /**
     * Sets Id.
     * A unique ID generated by Square for the
     * `InventoryAdjustmentGroup`.
     *
     * @maps id
     */
    public function setId(?string $id) : void
    {
        $this->id = $id;
    }
    /**
     * Returns Root Adjustment Id.
     * The inventory adjustment of the composed variation.
     */
    public function getRootAdjustmentId() : ?string
    {
        return $this->rootAdjustmentId;
    }
    /**
     * Sets Root Adjustment Id.
     * The inventory adjustment of the composed variation.
     *
     * @maps root_adjustment_id
     */
    public function setRootAdjustmentId(?string $rootAdjustmentId) : void
    {
        $this->rootAdjustmentId = $rootAdjustmentId;
    }
    /**
     * Returns From State.
     * Indicates the state of a tracked item quantity in the lifecycle of goods.
     */
    public function getFromState() : ?string
    {
        return $this->fromState;
    }
    /**
     * Sets From State.
     * Indicates the state of a tracked item quantity in the lifecycle of goods.
     *
     * @maps from_state
     */
    public function setFromState(?string $fromState) : void
    {
        $this->fromState = $fromState;
    }
    /**
     * Returns To State.
     * Indicates the state of a tracked item quantity in the lifecycle of goods.
     */
    public function getToState() : ?string
    {
        return $this->toState;
    }
    /**
     * Sets To State.
     * Indicates the state of a tracked item quantity in the lifecycle of goods.
     *
     * @maps to_state
     */
    public function setToState(?string $toState) : void
    {
        $this->toState = $toState;
    }
    /**
     * Encode this object to JSON
     *
     * @param bool $asArrayWhenEmpty Whether to serialize this model as an array whenever no fields
     *        are set. (default: false)
     *
     * @return array|stdClass
     */
    #[\ReturnTypeWillChange] // @phan-suppress-current-line PhanUndeclaredClassAttribute for (php < 8.1)
    public function jsonSerialize(bool $asArrayWhenEmpty = \false)
    {
        $json = [];
        if (isset($this->id)) {
            $json['id'] = $this->id;
        }
        if (isset($this->rootAdjustmentId)) {
            $json['root_adjustment_id'] = $this->rootAdjustmentId;
        }
        if (isset($this->fromState)) {
            $json['from_state'] = $this->fromState;
        }
        if (isset($this->toState)) {
            $json['to_state'] = $this->toState;
        }
        $json = \array_filter($json, function ($val) {
            return $val !== null;
        });
        return !$asArrayWhenEmpty && empty($json) ? new stdClass() : $json;
    }
}
