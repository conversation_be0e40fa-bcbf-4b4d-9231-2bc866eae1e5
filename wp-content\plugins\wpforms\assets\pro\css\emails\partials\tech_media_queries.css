/**
 * Adjusts the display of header images based on the user's preference for dark color schemes.
 */
@media (prefers-color-scheme: light) {
  .header-wrapper.dark-mode {
    display: none !important;
  }
}

@media (prefers-color-scheme: dark) {
  .header-wrapper.dark-mode {
    display: table-row !important;
  }
  .header-wrapper.dark-mode + .light-mode {
    display: none !important;
  }
}

@media only screen and (max-width: 599px) {
  .wpforms-layout-table-display-rows .wpforms-layout-table-row .field-name {
    display: block !important;
  }
  .wpforms-layout-table .wpforms-layout-table-row {
    display: block;
    width: 100%;
  }
  .wpforms-layout-table .wpforms-layout-table-row table, .wpforms-layout-table .wpforms-layout-table-row thead, .wpforms-layout-table .wpforms-layout-table-row tbody, .wpforms-layout-table .wpforms-layout-table-row tr, .wpforms-layout-table .wpforms-layout-table-row td, .wpforms-layout-table .wpforms-layout-table-row th {
    display: block;
    width: 100% !important;
    padding-bottom: 0 !important;
  }
  .wpforms-layout-table .wpforms-layout-table-row tr {
    padding-top: 0 !important;
  }
  .wpforms-layout-table .wpforms-layout-table-row td.field-value {
    padding-bottom: 25px !important;
  }
  .wpforms-layout-table .wpforms-layout-table-row td.field-name {
    padding-bottom: 10px !important;
  }
  .wpforms-layout-table > td {
    padding-bottom: 0 !important;
  }
}

@media only screen and (max-width: 599px) {
  .body-inner {
    padding-top: 25px !important;
    padding-bottom: 25px !important;
  }
  .header {
    padding: 25px !important;
  }
  .header .has-image-size-small img {
    max-height: 100px !important;
  }
  .header .has-image-size-medium img {
    max-height: 140px !important;
  }
  .header .has-image-size-large img {
    max-height: 180px !important;
  }
  .content {
    padding: 25px 25px 0 25px !important;
  }
  .content tr td {
    padding-top: 0 !important;
  }
  .footer {
    padding: 25px !important;
  }
}
