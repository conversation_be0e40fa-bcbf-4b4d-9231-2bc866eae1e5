const WPFormsCaptcha=window.WPFormsCaptcha||function(a,t,p){const o={init(){p(o.ready),t.addEventListener("elementor/popup/show",function(){o.ready()})},ready(){p(".wpforms-captcha-equation").each(function(){var a=p(this).parent(),t=wpforms_captcha.cal[Math.floor(Math.random()*wpforms_captcha.cal.length)],n=o.randomNumber(wpforms_captcha.min,wpforms_captcha.max),r=o.randomNumber(wpforms_captcha.min,wpforms_captcha.max);a.find("span.n1").text(n),a.find("input.n1").val(n),a.find("span.n2").text(r),a.find("input.n2").val(r),a.find("span.cal").text(t),a.find("input.cal").val(t),a.find("input.a").attr({"data-cal":t,"data-n1":n,"data-n2":r})}),a.addEventListener("om.Html.append.after",function(){o.ready()}),o.loadValidation()},loadValidation(){void 0!==p.fn.validate&&p.validator.addMethod("wpf-captcha",function(a,t,n){var r=p(t);let o,e;if("math"===n){var n=Number(r.attr("data-n1")),i=Number(r.attr("data-n2")),d=r.attr("data-cal"),c=["-","+","*"],m={"+":(a,t)=>a+t,"-":(a,t)=>a-t,"*":(a,t)=>a*t};if(o=Number(a),e=!1,!c.includes(d))return!1;e=m[d](n,i)}else o=a.toString().toLowerCase().trim(),e=r.attr("data-a").toString().toLowerCase().trim();return this.optional(t)||o===e},p.validator.format(wpforms_captcha.errorMsg))},randomNumber(a,t){return Math.floor(Math.random()*(Number(t)-Number(a)+1))+Number(a)}};return o}(document,window,jQuery);WPFormsCaptcha.init();