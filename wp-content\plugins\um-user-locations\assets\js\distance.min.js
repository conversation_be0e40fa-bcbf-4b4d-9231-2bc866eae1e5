jQuery(document).ready(function(){jQuery("#um-user-location-current-lat").length&&navigator.geolocation&&navigator.geolocation.getCurrentPosition(function(n){jQuery("#um-user-location-current-lat").val(n.coords.latitude),jQuery("#um-user-location-current-lng").val(n.coords.longitude),jQuery(".um-field-distance, .um-distance-meta").each(function(){var e=jQuery(this).find(".um-user-location-distance-calculation-result"),n=e.data("location_source");wp.ajax.send("um_get_user_distance",{data:{location:[jQuery("#um-user-location-current-lat").val(),jQuery("#um-user-location-current-lng").val()],user_coords:[jQuery("#um-user-location-distance-"+n+"-lat").val(),jQuery("#um-user-location-distance-"+n+"-lng").val()],unit:e.data("distance_unit"),nonce:um_scripts.nonce},success:function(n){e.text(n)},error:function(n){console.log(n)}})})},function(n){(n.code=n.PERMISSION_DENIED)&&jQuery("#um-user-location-current-denied").val(1)})}),wp.hooks.addFilter("um_member_directory_get_members_allow","um_user_locations_distance",function(n,e,t){return t.find("#um-user-location-current-lat").length&&t.find("#um-user-location-current-lng").length&&(1===parseInt(t.find("#um-user-location-current-denied").val(),10)||""!==t.find("#um-user-location-current-lat").val()&&""!==t.find("#um-user-location-current-lng").val()||(n=!1)),n},10),wp.hooks.addFilter("um_member_directory_filter_request","um_user_locations",function(n){var e,t=jQuery('.um-directory[data-hash="'+n.directory_id+'"]');return t.length&&t.find("#um-user-location-current-lat").length&&t.find("#um-user-location-current-lng").length&&""!==t.find("#um-user-location-current-lat").val()&&""!==t.find("#um-user-location-current-lng").val()&&(e=t.find("#um-user-location-current-lat").val(),t=t.find("#um-user-location-current-lng").val(),n.current_user_location=[e,t]),n},10);