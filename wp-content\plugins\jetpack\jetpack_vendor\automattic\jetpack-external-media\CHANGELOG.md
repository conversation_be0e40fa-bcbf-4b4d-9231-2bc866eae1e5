# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.3.7] - 2025-05-05
### Changed
- Update package dependencies. [#43326]

### Fixed
- Linting: Address final rules in WordPress Stylelint config. [#43296]

## [0.3.6] - 2025-04-28
### Fixed
- Linting: Fix more Stylelint violations. [#43213]

## [0.3.5] - 2025-04-21
### Fixed
- Scripts: Prevent console warnings by ensuring styles are correctly added to iframe. [#42985]

## [0.3.4] - 2025-04-14
### Fixed
- Linting: Update stylesheets to use WordPress rules for fonts and colors. [#42920] [#42928]

## [0.3.3] - 2025-04-07
### Changed
- Linting: First pass of style coding standards. [#42734]
- Update dependencies. [#42820]
- Update package dependencies. [#42809]

## [0.3.2] - 2025-03-31
### Changed
- Update dependencies. [#42678]

### Fixed
- Components: Prevent deprecation notices by adding `__next40pxDefaultSize` to controls. [#42677]

## [0.3.1] - 2025-03-24
### Changed
- Update dependencies. [#42565]

## [0.3.0] - 2025-03-18
### Changed
- Update package dependencies. [#42511]

### Fixed
- Jetpack App: Fix uploader spacing. [#42472]

## [0.2.6] - 2025-03-17
### Changed
- Update dependencies. [#42498]

## [0.2.5] - 2025-03-12
### Changed
- Internal updates.

## [0.2.4] - 2025-03-10
### Changed
- Internal updates.

## [0.2.3] - 2025-03-03
### Changed
- Update package dependencies. [#42163]

## [0.2.2] - 2025-02-24
### Changed
- Update dependencies.

## [0.2.1] - 2025-02-17
### Changed
- Update dependencies.

## [0.2.0] - 2025-02-11
### Added
- Untangle Calypso Media Page: Enable the feature. [#41628]

## 0.1.0 - 2025-02-10
### Added
- Add external media modal on the Media Import page. [#41282]
- Add Import button in Media Library. [#41544]
- Add track events to the Import page and modal. [#41592]
- Initial version. [#41078]
- Media Library: Add track events for upload from URL feature. [#41620]
- Media Library: Add track event to the Import Media button. [#41626]

### Changed
- Update styles of the external media modal. [#41303]

### Fixed
- Fix the button size in the editor for Gutenberg 18 or below. [#41619]
- Media Library: Fix the Import Media button color in some color schemes. [#41664]

[0.3.7]: https://github.com/Automattic/jetpack-external-media/compare/v0.3.6...v0.3.7
[0.3.6]: https://github.com/Automattic/jetpack-external-media/compare/v0.3.5...v0.3.6
[0.3.5]: https://github.com/Automattic/jetpack-external-media/compare/v0.3.4...v0.3.5
[0.3.4]: https://github.com/Automattic/jetpack-external-media/compare/v0.3.3...v0.3.4
[0.3.3]: https://github.com/Automattic/jetpack-external-media/compare/v0.3.2...v0.3.3
[0.3.2]: https://github.com/Automattic/jetpack-external-media/compare/v0.3.1...v0.3.2
[0.3.1]: https://github.com/Automattic/jetpack-external-media/compare/v0.3.0...v0.3.1
[0.3.0]: https://github.com/Automattic/jetpack-external-media/compare/v0.2.6...v0.3.0
[0.2.6]: https://github.com/Automattic/jetpack-external-media/compare/v0.2.5...v0.2.6
[0.2.5]: https://github.com/Automattic/jetpack-external-media/compare/v0.2.4...v0.2.5
[0.2.4]: https://github.com/Automattic/jetpack-external-media/compare/v0.2.3...v0.2.4
[0.2.3]: https://github.com/Automattic/jetpack-external-media/compare/v0.2.2...v0.2.3
[0.2.2]: https://github.com/Automattic/jetpack-external-media/compare/v0.2.1...v0.2.2
[0.2.1]: https://github.com/Automattic/jetpack-external-media/compare/v0.2.0...v0.2.1
[0.2.0]: https://github.com/Automattic/jetpack-external-media/compare/v0.1.0...v0.2.0
