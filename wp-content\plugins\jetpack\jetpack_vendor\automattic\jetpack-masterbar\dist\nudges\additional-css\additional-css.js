(()=>{"use strict";!function(t){const o={init:function(){this.clickifyNavigateToButtons()},clickifyNavigateToButtons:function(){const t=document.querySelector(".navigate-to");t&&t.addEventListener("click",(function(){const t=this.getAttribute("data-navigate-to-page");t&&(window._tkq=window._tkq||[],window._tkq.push(["recordEvent","calypso_upgrade_nudge_cta_click",{cta_name:"customizer_css"}]),window.location.search.match(/calypso=true/)&&window.parent.location!==window.location?window.top.postMessage(JSON.stringify({calypso:!0,command:"navigateTo",destination:t}),"*"):window.location="https://wordpress.com"+t)}))}};t(document).ready((function(){o.init()}))}(jQuery)})();