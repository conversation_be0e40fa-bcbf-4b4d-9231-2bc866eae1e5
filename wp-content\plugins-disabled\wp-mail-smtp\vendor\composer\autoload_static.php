<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit64c5864e234999e61d47e3bb28138d69
{
    public static $files = array (
        '2bb094e40611cb5eccea789f32aff634' => __DIR__ . '/../..' . '/vendor_prefixed/symfony/polyfill-mbstring/bootstrap.php',
        '606299e0d90ec13f1e6b53164b8387df' => __DIR__ . '/../..' . '/vendor_prefixed/symfony/polyfill-intl-idn/bootstrap.php',
        '2d822e735b5b1897d96a7a28221d6513' => __DIR__ . '/../..' . '/vendor_prefixed/symfony/deprecation-contracts/function.php',
        '6fe0d6ea1deb6acc74bbe64573a83e1c' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/functions_include.php',
    );

    public static $prefixLengthsPsr4 = array (
        'W' => 
        array (
            'WPMailSMTP\\' => 11,
        ),
        'C' => 
        array (
            'Composer\\Installers\\' => 20,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'WPMailSMTP\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
        'Composer\\Installers\\' => 
        array (
            0 => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'Composer\\Installers\\AglInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/AglInstaller.php',
        'Composer\\Installers\\AkauntingInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/AkauntingInstaller.php',
        'Composer\\Installers\\AnnotateCmsInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/AnnotateCmsInstaller.php',
        'Composer\\Installers\\AsgardInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/AsgardInstaller.php',
        'Composer\\Installers\\AttogramInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/AttogramInstaller.php',
        'Composer\\Installers\\BaseInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/BaseInstaller.php',
        'Composer\\Installers\\BitrixInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/BitrixInstaller.php',
        'Composer\\Installers\\BonefishInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/BonefishInstaller.php',
        'Composer\\Installers\\BotbleInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/BotbleInstaller.php',
        'Composer\\Installers\\CakePHPInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/CakePHPInstaller.php',
        'Composer\\Installers\\ChefInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/ChefInstaller.php',
        'Composer\\Installers\\CiviCrmInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/CiviCrmInstaller.php',
        'Composer\\Installers\\ClanCatsFrameworkInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/ClanCatsFrameworkInstaller.php',
        'Composer\\Installers\\CockpitInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/CockpitInstaller.php',
        'Composer\\Installers\\CodeIgniterInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/CodeIgniterInstaller.php',
        'Composer\\Installers\\Concrete5Installer' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/Concrete5Installer.php',
        'Composer\\Installers\\ConcreteCMSInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/ConcreteCMSInstaller.php',
        'Composer\\Installers\\CroogoInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/CroogoInstaller.php',
        'Composer\\Installers\\DecibelInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/DecibelInstaller.php',
        'Composer\\Installers\\DframeInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/DframeInstaller.php',
        'Composer\\Installers\\DokuWikiInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/DokuWikiInstaller.php',
        'Composer\\Installers\\DolibarrInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/DolibarrInstaller.php',
        'Composer\\Installers\\DrupalInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/DrupalInstaller.php',
        'Composer\\Installers\\ElggInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/ElggInstaller.php',
        'Composer\\Installers\\EliasisInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/EliasisInstaller.php',
        'Composer\\Installers\\ExpressionEngineInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/ExpressionEngineInstaller.php',
        'Composer\\Installers\\EzPlatformInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/EzPlatformInstaller.php',
        'Composer\\Installers\\ForkCMSInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/ForkCMSInstaller.php',
        'Composer\\Installers\\FuelInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/FuelInstaller.php',
        'Composer\\Installers\\FuelphpInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/FuelphpInstaller.php',
        'Composer\\Installers\\GravInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/GravInstaller.php',
        'Composer\\Installers\\HuradInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/HuradInstaller.php',
        'Composer\\Installers\\ImageCMSInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/ImageCMSInstaller.php',
        'Composer\\Installers\\Installer' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/Installer.php',
        'Composer\\Installers\\ItopInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/ItopInstaller.php',
        'Composer\\Installers\\KanboardInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/KanboardInstaller.php',
        'Composer\\Installers\\KnownInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/KnownInstaller.php',
        'Composer\\Installers\\KodiCMSInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/KodiCMSInstaller.php',
        'Composer\\Installers\\KohanaInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/KohanaInstaller.php',
        'Composer\\Installers\\LanManagementSystemInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/LanManagementSystemInstaller.php',
        'Composer\\Installers\\LaravelInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/LaravelInstaller.php',
        'Composer\\Installers\\LavaLiteInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/LavaLiteInstaller.php',
        'Composer\\Installers\\LithiumInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/LithiumInstaller.php',
        'Composer\\Installers\\MODULEWorkInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/MODULEWorkInstaller.php',
        'Composer\\Installers\\MODXEvoInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/MODXEvoInstaller.php',
        'Composer\\Installers\\MagentoInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/MagentoInstaller.php',
        'Composer\\Installers\\MajimaInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/MajimaInstaller.php',
        'Composer\\Installers\\MakoInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/MakoInstaller.php',
        'Composer\\Installers\\MantisBTInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/MantisBTInstaller.php',
        'Composer\\Installers\\MatomoInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/MatomoInstaller.php',
        'Composer\\Installers\\MauticInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/MauticInstaller.php',
        'Composer\\Installers\\MayaInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/MayaInstaller.php',
        'Composer\\Installers\\MediaWikiInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/MediaWikiInstaller.php',
        'Composer\\Installers\\MiaoxingInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/MiaoxingInstaller.php',
        'Composer\\Installers\\MicroweberInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/MicroweberInstaller.php',
        'Composer\\Installers\\ModxInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/ModxInstaller.php',
        'Composer\\Installers\\MoodleInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/MoodleInstaller.php',
        'Composer\\Installers\\OctoberInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/OctoberInstaller.php',
        'Composer\\Installers\\OntoWikiInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/OntoWikiInstaller.php',
        'Composer\\Installers\\OsclassInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/OsclassInstaller.php',
        'Composer\\Installers\\OxidInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/OxidInstaller.php',
        'Composer\\Installers\\PPIInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/PPIInstaller.php',
        'Composer\\Installers\\PantheonInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/PantheonInstaller.php',
        'Composer\\Installers\\PhiftyInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/PhiftyInstaller.php',
        'Composer\\Installers\\PhpBBInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/PhpBBInstaller.php',
        'Composer\\Installers\\PiwikInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/PiwikInstaller.php',
        'Composer\\Installers\\PlentymarketsInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/PlentymarketsInstaller.php',
        'Composer\\Installers\\Plugin' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/Plugin.php',
        'Composer\\Installers\\PortoInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/PortoInstaller.php',
        'Composer\\Installers\\PrestashopInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/PrestashopInstaller.php',
        'Composer\\Installers\\ProcessWireInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/ProcessWireInstaller.php',
        'Composer\\Installers\\PuppetInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/PuppetInstaller.php',
        'Composer\\Installers\\PxcmsInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/PxcmsInstaller.php',
        'Composer\\Installers\\RadPHPInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/RadPHPInstaller.php',
        'Composer\\Installers\\ReIndexInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/ReIndexInstaller.php',
        'Composer\\Installers\\Redaxo5Installer' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/Redaxo5Installer.php',
        'Composer\\Installers\\RedaxoInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/RedaxoInstaller.php',
        'Composer\\Installers\\RoundcubeInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/RoundcubeInstaller.php',
        'Composer\\Installers\\SMFInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/SMFInstaller.php',
        'Composer\\Installers\\ShopwareInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/ShopwareInstaller.php',
        'Composer\\Installers\\SilverStripeInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/SilverStripeInstaller.php',
        'Composer\\Installers\\SiteDirectInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/SiteDirectInstaller.php',
        'Composer\\Installers\\StarbugInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/StarbugInstaller.php',
        'Composer\\Installers\\SyDESInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/SyDESInstaller.php',
        'Composer\\Installers\\SyliusInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/SyliusInstaller.php',
        'Composer\\Installers\\TaoInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/TaoInstaller.php',
        'Composer\\Installers\\TastyIgniterInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/TastyIgniterInstaller.php',
        'Composer\\Installers\\TheliaInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/TheliaInstaller.php',
        'Composer\\Installers\\TuskInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/TuskInstaller.php',
        'Composer\\Installers\\UserFrostingInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/UserFrostingInstaller.php',
        'Composer\\Installers\\VanillaInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/VanillaInstaller.php',
        'Composer\\Installers\\VgmcpInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/VgmcpInstaller.php',
        'Composer\\Installers\\WHMCSInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/WHMCSInstaller.php',
        'Composer\\Installers\\WinterInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/WinterInstaller.php',
        'Composer\\Installers\\WolfCMSInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/WolfCMSInstaller.php',
        'Composer\\Installers\\WordPressInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/WordPressInstaller.php',
        'Composer\\Installers\\YawikInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/YawikInstaller.php',
        'Composer\\Installers\\ZendInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/ZendInstaller.php',
        'Composer\\Installers\\ZikulaInstaller' => __DIR__ . '/..' . '/composer/installers/src/Composer/Installers/ZikulaInstaller.php',
        'WPMailSMTP\\AbstractConnection' => __DIR__ . '/../..' . '/src/AbstractConnection.php',
        'WPMailSMTP\\Admin\\AdminBarMenu' => __DIR__ . '/../..' . '/src/Admin/AdminBarMenu.php',
        'WPMailSMTP\\Admin\\Area' => __DIR__ . '/../..' . '/src/Admin/Area.php',
        'WPMailSMTP\\Admin\\ConnectionSettings' => __DIR__ . '/../..' . '/src/Admin/ConnectionSettings.php',
        'WPMailSMTP\\Admin\\DashboardWidget' => __DIR__ . '/../..' . '/src/Admin/DashboardWidget.php',
        'WPMailSMTP\\Admin\\DebugEvents\\DebugEvents' => __DIR__ . '/../..' . '/src/Admin/DebugEvents/DebugEvents.php',
        'WPMailSMTP\\Admin\\DebugEvents\\Event' => __DIR__ . '/../..' . '/src/Admin/DebugEvents/Event.php',
        'WPMailSMTP\\Admin\\DebugEvents\\EventsCollection' => __DIR__ . '/../..' . '/src/Admin/DebugEvents/EventsCollection.php',
        'WPMailSMTP\\Admin\\DebugEvents\\Migration' => __DIR__ . '/../..' . '/src/Admin/DebugEvents/Migration.php',
        'WPMailSMTP\\Admin\\DebugEvents\\Table' => __DIR__ . '/../..' . '/src/Admin/DebugEvents/Table.php',
        'WPMailSMTP\\Admin\\DomainChecker' => __DIR__ . '/../..' . '/src/Admin/DomainChecker.php',
        'WPMailSMTP\\Admin\\Education' => __DIR__ . '/../..' . '/src/Admin/Education.php',
        'WPMailSMTP\\Admin\\FlyoutMenu' => __DIR__ . '/../..' . '/src/Admin/FlyoutMenu.php',
        'WPMailSMTP\\Admin\\Notifications' => __DIR__ . '/../..' . '/src/Admin/Notifications.php',
        'WPMailSMTP\\Admin\\PageAbstract' => __DIR__ . '/../..' . '/src/Admin/PageAbstract.php',
        'WPMailSMTP\\Admin\\PageInterface' => __DIR__ . '/../..' . '/src/Admin/PageInterface.php',
        'WPMailSMTP\\Admin\\Pages\\About' => __DIR__ . '/../..' . '/src/Admin/Pages/About.php',
        'WPMailSMTP\\Admin\\Pages\\AboutTab' => __DIR__ . '/../..' . '/src/Admin/Pages/AboutTab.php',
        'WPMailSMTP\\Admin\\Pages\\ActionSchedulerTab' => __DIR__ . '/../..' . '/src/Admin/Pages/ActionSchedulerTab.php',
        'WPMailSMTP\\Admin\\Pages\\AdditionalConnectionsTab' => __DIR__ . '/../..' . '/src/Admin/Pages/AdditionalConnectionsTab.php',
        'WPMailSMTP\\Admin\\Pages\\AlertsTab' => __DIR__ . '/../..' . '/src/Admin/Pages/AlertsTab.php',
        'WPMailSMTP\\Admin\\Pages\\AuthTab' => __DIR__ . '/../..' . '/src/Admin/Pages/AuthTab.php',
        'WPMailSMTP\\Admin\\Pages\\ControlTab' => __DIR__ . '/../..' . '/src/Admin/Pages/ControlTab.php',
        'WPMailSMTP\\Admin\\Pages\\DebugEventsTab' => __DIR__ . '/../..' . '/src/Admin/Pages/DebugEventsTab.php',
        'WPMailSMTP\\Admin\\Pages\\EmailReports' => __DIR__ . '/../..' . '/src/Admin/Pages/EmailReports.php',
        'WPMailSMTP\\Admin\\Pages\\EmailReportsTab' => __DIR__ . '/../..' . '/src/Admin/Pages/EmailReportsTab.php',
        'WPMailSMTP\\Admin\\Pages\\ExportTab' => __DIR__ . '/../..' . '/src/Admin/Pages/ExportTab.php',
        'WPMailSMTP\\Admin\\Pages\\Logs' => __DIR__ . '/../..' . '/src/Admin/Pages/Logs.php',
        'WPMailSMTP\\Admin\\Pages\\LogsTab' => __DIR__ . '/../..' . '/src/Admin/Pages/LogsTab.php',
        'WPMailSMTP\\Admin\\Pages\\MiscTab' => __DIR__ . '/../..' . '/src/Admin/Pages/MiscTab.php',
        'WPMailSMTP\\Admin\\Pages\\SettingsTab' => __DIR__ . '/../..' . '/src/Admin/Pages/SettingsTab.php',
        'WPMailSMTP\\Admin\\Pages\\SmartRoutingTab' => __DIR__ . '/../..' . '/src/Admin/Pages/SmartRoutingTab.php',
        'WPMailSMTP\\Admin\\Pages\\TestTab' => __DIR__ . '/../..' . '/src/Admin/Pages/TestTab.php',
        'WPMailSMTP\\Admin\\Pages\\Tools' => __DIR__ . '/../..' . '/src/Admin/Pages/Tools.php',
        'WPMailSMTP\\Admin\\Pages\\VersusTab' => __DIR__ . '/../..' . '/src/Admin/Pages/VersusTab.php',
        'WPMailSMTP\\Admin\\ParentPageAbstract' => __DIR__ . '/../..' . '/src/Admin/ParentPageAbstract.php',
        'WPMailSMTP\\Admin\\PluginsInstallSkin' => __DIR__ . '/../..' . '/src/Admin/PluginsInstallSkin.php',
        'WPMailSMTP\\Admin\\Review' => __DIR__ . '/../..' . '/src/Admin/Review.php',
        'WPMailSMTP\\Admin\\SetupWizard' => __DIR__ . '/../..' . '/src/Admin/SetupWizard.php',
        'WPMailSMTP\\Compatibility\\Compatibility' => __DIR__ . '/../..' . '/src/Compatibility/Compatibility.php',
        'WPMailSMTP\\Compatibility\\Plugin\\Admin2020' => __DIR__ . '/../..' . '/src/Compatibility/Plugin/Admin2020.php',
        'WPMailSMTP\\Compatibility\\Plugin\\PluginAbstract' => __DIR__ . '/../..' . '/src/Compatibility/Plugin/PluginAbstract.php',
        'WPMailSMTP\\Compatibility\\Plugin\\PluginInterface' => __DIR__ . '/../..' . '/src/Compatibility/Plugin/PluginInterface.php',
        'WPMailSMTP\\Compatibility\\Plugin\\WPForms' => __DIR__ . '/../..' . '/src/Compatibility/Plugin/WPForms.php',
        'WPMailSMTP\\Compatibility\\Plugin\\WPFormsLite' => __DIR__ . '/../..' . '/src/Compatibility/Plugin/WPFormsLite.php',
        'WPMailSMTP\\Compatibility\\Plugin\\WooCommerce' => __DIR__ . '/../..' . '/src/Compatibility/Plugin/WooCommerce.php',
        'WPMailSMTP\\Conflicts' => __DIR__ . '/../..' . '/src/Conflicts.php',
        'WPMailSMTP\\Connect' => __DIR__ . '/../..' . '/src/Connect.php',
        'WPMailSMTP\\Connection' => __DIR__ . '/../..' . '/src/Connection.php',
        'WPMailSMTP\\ConnectionInterface' => __DIR__ . '/../..' . '/src/ConnectionInterface.php',
        'WPMailSMTP\\ConnectionsManager' => __DIR__ . '/../..' . '/src/ConnectionsManager.php',
        'WPMailSMTP\\Core' => __DIR__ . '/../..' . '/src/Core.php',
        'WPMailSMTP\\DBRepair' => __DIR__ . '/../..' . '/src/DBRepair.php',
        'WPMailSMTP\\Debug' => __DIR__ . '/../..' . '/src/Debug.php',
        'WPMailSMTP\\Geo' => __DIR__ . '/../..' . '/src/Geo.php',
        'WPMailSMTP\\Helpers\\Crypto' => __DIR__ . '/../..' . '/src/Helpers/Crypto.php',
        'WPMailSMTP\\Helpers\\DB' => __DIR__ . '/../..' . '/src/Helpers/DB.php',
        'WPMailSMTP\\Helpers\\Helpers' => __DIR__ . '/../..' . '/src/Helpers/Helpers.php',
        'WPMailSMTP\\Helpers\\PluginImportDataRetriever' => __DIR__ . '/../..' . '/src/Helpers/PluginImportDataRetriever.php',
        'WPMailSMTP\\Helpers\\UI' => __DIR__ . '/../..' . '/src/Helpers/UI.php',
        'WPMailSMTP\\MailCatcher' => __DIR__ . '/../..' . '/src/MailCatcher.php',
        'WPMailSMTP\\MailCatcherInterface' => __DIR__ . '/../..' . '/src/MailCatcherInterface.php',
        'WPMailSMTP\\MailCatcherTrait' => __DIR__ . '/../..' . '/src/MailCatcherTrait.php',
        'WPMailSMTP\\MailCatcherV6' => __DIR__ . '/../..' . '/src/MailCatcherV6.php',
        'WPMailSMTP\\Migration' => __DIR__ . '/../..' . '/src/Migration.php',
        'WPMailSMTP\\MigrationAbstract' => __DIR__ . '/../..' . '/src/MigrationAbstract.php',
        'WPMailSMTP\\Migrations' => __DIR__ . '/../..' . '/src/Migrations.php',
        'WPMailSMTP\\OptimizedEmailSending' => __DIR__ . '/../..' . '/src/OptimizedEmailSending.php',
        'WPMailSMTP\\Options' => __DIR__ . '/../..' . '/src/Options.php',
        'WPMailSMTP\\Pro\\AdditionalConnections\\AdditionalConnections' => __DIR__ . '/../..' . '/src/Pro/AdditionalConnections/AdditionalConnections.php',
        'WPMailSMTP\\Pro\\AdditionalConnections\\Admin\\SettingsTab' => __DIR__ . '/../..' . '/src/Pro/AdditionalConnections/Admin/SettingsTab.php',
        'WPMailSMTP\\Pro\\AdditionalConnections\\Admin\\TestTab' => __DIR__ . '/../..' . '/src/Pro/AdditionalConnections/Admin/TestTab.php',
        'WPMailSMTP\\Pro\\AdditionalConnections\\Connection' => __DIR__ . '/../..' . '/src/Pro/AdditionalConnections/Connection.php',
        'WPMailSMTP\\Pro\\AdditionalConnections\\ConnectionOptions' => __DIR__ . '/../..' . '/src/Pro/AdditionalConnections/ConnectionOptions.php',
        'WPMailSMTP\\Pro\\Admin\\Area' => __DIR__ . '/../..' . '/src/Pro/Admin/Area.php',
        'WPMailSMTP\\Pro\\Admin\\DashboardWidget' => __DIR__ . '/../..' . '/src/Pro/Admin/DashboardWidget.php',
        'WPMailSMTP\\Pro\\Admin\\Pages\\MiscTab' => __DIR__ . '/../..' . '/src/Pro/Admin/Pages/MiscTab.php',
        'WPMailSMTP\\Pro\\Admin\\PluginsList' => __DIR__ . '/../..' . '/src/Pro/Admin/PluginsList.php',
        'WPMailSMTP\\Pro\\Alerts\\AbstractOptions' => __DIR__ . '/../..' . '/src/Pro/Alerts/AbstractOptions.php',
        'WPMailSMTP\\Pro\\Alerts\\Admin\\SettingsTab' => __DIR__ . '/../..' . '/src/Pro/Alerts/Admin/SettingsTab.php',
        'WPMailSMTP\\Pro\\Alerts\\Alert' => __DIR__ . '/../..' . '/src/Pro/Alerts/Alert.php',
        'WPMailSMTP\\Pro\\Alerts\\Alerts' => __DIR__ . '/../..' . '/src/Pro/Alerts/Alerts.php',
        'WPMailSMTP\\Pro\\Alerts\\Handlers\\HandlerInterface' => __DIR__ . '/../..' . '/src/Pro/Alerts/Handlers/HandlerInterface.php',
        'WPMailSMTP\\Pro\\Alerts\\Loader' => __DIR__ . '/../..' . '/src/Pro/Alerts/Loader.php',
        'WPMailSMTP\\Pro\\Alerts\\Notifier' => __DIR__ . '/../..' . '/src/Pro/Alerts/Notifier.php',
        'WPMailSMTP\\Pro\\Alerts\\OptionsInterface' => __DIR__ . '/../..' . '/src/Pro/Alerts/OptionsInterface.php',
        'WPMailSMTP\\Pro\\Alerts\\Providers\\CustomWebhook\\Handler' => __DIR__ . '/../..' . '/src/Pro/Alerts/Providers/CustomWebhook/Handler.php',
        'WPMailSMTP\\Pro\\Alerts\\Providers\\CustomWebhook\\Options' => __DIR__ . '/../..' . '/src/Pro/Alerts/Providers/CustomWebhook/Options.php',
        'WPMailSMTP\\Pro\\Alerts\\Providers\\DiscordWebhook\\Handler' => __DIR__ . '/../..' . '/src/Pro/Alerts/Providers/DiscordWebhook/Handler.php',
        'WPMailSMTP\\Pro\\Alerts\\Providers\\DiscordWebhook\\Options' => __DIR__ . '/../..' . '/src/Pro/Alerts/Providers/DiscordWebhook/Options.php',
        'WPMailSMTP\\Pro\\Alerts\\Providers\\Email\\Handler' => __DIR__ . '/../..' . '/src/Pro/Alerts/Providers/Email/Handler.php',
        'WPMailSMTP\\Pro\\Alerts\\Providers\\Email\\Options' => __DIR__ . '/../..' . '/src/Pro/Alerts/Providers/Email/Options.php',
        'WPMailSMTP\\Pro\\Alerts\\Providers\\Push\\Handler' => __DIR__ . '/../..' . '/src/Pro/Alerts/Providers/Push/Handler.php',
        'WPMailSMTP\\Pro\\Alerts\\Providers\\Push\\Options' => __DIR__ . '/../..' . '/src/Pro/Alerts/Providers/Push/Options.php',
        'WPMailSMTP\\Pro\\Alerts\\Providers\\Push\\Provider' => __DIR__ . '/../..' . '/src/Pro/Alerts/Providers/Push/Provider.php',
        'WPMailSMTP\\Pro\\Alerts\\Providers\\SlackWebhook\\Handler' => __DIR__ . '/../..' . '/src/Pro/Alerts/Providers/SlackWebhook/Handler.php',
        'WPMailSMTP\\Pro\\Alerts\\Providers\\SlackWebhook\\Options' => __DIR__ . '/../..' . '/src/Pro/Alerts/Providers/SlackWebhook/Options.php',
        'WPMailSMTP\\Pro\\Alerts\\Providers\\TeamsWebhook\\Handler' => __DIR__ . '/../..' . '/src/Pro/Alerts/Providers/TeamsWebhook/Handler.php',
        'WPMailSMTP\\Pro\\Alerts\\Providers\\TeamsWebhook\\Options' => __DIR__ . '/../..' . '/src/Pro/Alerts/Providers/TeamsWebhook/Options.php',
        'WPMailSMTP\\Pro\\Alerts\\Providers\\TwilioSMS\\Handler' => __DIR__ . '/../..' . '/src/Pro/Alerts/Providers/TwilioSMS/Handler.php',
        'WPMailSMTP\\Pro\\Alerts\\Providers\\TwilioSMS\\Options' => __DIR__ . '/../..' . '/src/Pro/Alerts/Providers/TwilioSMS/Options.php',
        'WPMailSMTP\\Pro\\BackupConnections\\Admin\\SettingsTab' => __DIR__ . '/../..' . '/src/Pro/BackupConnections/Admin/SettingsTab.php',
        'WPMailSMTP\\Pro\\BackupConnections\\BackupConnections' => __DIR__ . '/../..' . '/src/Pro/BackupConnections/BackupConnections.php',
        'WPMailSMTP\\Pro\\ConditionalLogic\\CanProcessConditionalLogicTrait' => __DIR__ . '/../..' . '/src/Pro/ConditionalLogic/CanProcessConditionalLogicTrait.php',
        'WPMailSMTP\\Pro\\ConditionalLogic\\ConditionalLogicSettings' => __DIR__ . '/../..' . '/src/Pro/ConditionalLogic/ConditionalLogicSettings.php',
        'WPMailSMTP\\Pro\\ConnectionsManager' => __DIR__ . '/../..' . '/src/Pro/ConnectionsManager.php',
        'WPMailSMTP\\Pro\\DBRepair' => __DIR__ . '/../..' . '/src/Pro/DBRepair.php',
        'WPMailSMTP\\Pro\\Emails\\Control\\Admin\\SettingsTab' => __DIR__ . '/../..' . '/src/Pro/Emails/Control/Admin/SettingsTab.php',
        'WPMailSMTP\\Pro\\Emails\\Control\\Control' => __DIR__ . '/../..' . '/src/Pro/Emails/Control/Control.php',
        'WPMailSMTP\\Pro\\Emails\\Control\\Reload' => __DIR__ . '/../..' . '/src/Pro/Emails/Control/Reload.php',
        'WPMailSMTP\\Pro\\Emails\\Control\\Switcher' => __DIR__ . '/../..' . '/src/Pro/Emails/Control/Switcher.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Admin\\ArchivePage' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Admin/ArchivePage.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Admin\\PageAbstract' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Admin/PageAbstract.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Admin\\PrintPreview' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Admin/PrintPreview.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Admin\\SettingsTab' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Admin/SettingsTab.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Admin\\SinglePage' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Admin/SinglePage.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Admin\\Table' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Admin/Table.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Attachments\\Attachment' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Attachments/Attachment.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Attachments\\Attachments' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Attachments/Attachments.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Attachments\\Cleanup' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Attachments/Cleanup.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Attachments\\Migration' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Attachments/Migration.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\CanResendEmailTrait' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/CanResendEmailTrait.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\AbstractDeliveryVerifier' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/DeliveryVerification/AbstractDeliveryVerifier.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\DeliveryStatus' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/DeliveryVerification/DeliveryStatus.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\DeliveryVerification' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/DeliveryVerification/DeliveryVerification.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\ElasticEmail\\DeliveryVerifier' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/DeliveryVerification/ElasticEmail/DeliveryVerifier.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\Mailgun\\DeliveryVerifier' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/DeliveryVerification/Mailgun/DeliveryVerifier.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\Mailjet\\DeliveryVerifier' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/DeliveryVerification/Mailjet/DeliveryVerifier.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\Postmark\\DeliveryVerifier' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/DeliveryVerification/Postmark/DeliveryVerifier.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\SMTP2GO\\DeliveryVerifier' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/DeliveryVerification/SMTP2GO/DeliveryVerifier.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\SMTPcom\\DeliveryVerifier' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/DeliveryVerification/SMTPcom/DeliveryVerifier.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\Sendinblue\\DeliveryVerifier' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/DeliveryVerification/Sendinblue/DeliveryVerifier.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\Sendlayer\\DeliveryVerifier' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/DeliveryVerification/Sendlayer/DeliveryVerifier.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\SparkPost\\DeliveryVerifier' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/DeliveryVerification/SparkPost/DeliveryVerifier.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Email' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Email.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\EmailsCollection' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/EmailsCollection.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Export\\AbstractData' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Export/AbstractData.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Export\\Admin' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Export/Admin.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Export\\CanRemoveExportFileTrait' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Export/CanRemoveExportFileTrait.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Export\\EMLData' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Export/EMLData.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Export\\Export' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Export/Export.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Export\\File' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Export/File.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Export\\Handler' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Export/Handler.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Export\\Request' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Export/Request.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Export\\TableData' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Export/TableData.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Importers\\ImporterAbstract' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Importers/ImporterAbstract.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Importers\\ImporterInterface' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Importers/ImporterInterface.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Importers\\ImporterTabAbstract' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Importers/ImporterTabAbstract.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Importers\\ImporterTabAbstractInterface' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Importers/ImporterTabAbstractInterface.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Importers\\Importers' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Importers/Importers.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Importers\\WPMailLogging\\Importer' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Importers/WPMailLogging/Importer.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Importers\\WPMailLogging\\Tab' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Importers/WPMailLogging/Tab.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Logs' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Logs.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Migration' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Migration.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Providers\\Common' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Providers/Common.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Providers\\SMTP' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Providers/SMTP.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\RecheckDeliveryStatus' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/RecheckDeliveryStatus.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Reports\\Admin' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Reports/Admin.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Reports\\Emails\\Summary' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Reports/Emails/Summary.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Reports\\Report' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Reports/Report.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Reports\\Reports' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Reports/Reports.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Reports\\Table' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Reports/Table.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Resend' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Resend.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Tracking\\Cleanup' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Tracking/Cleanup.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\AbstractEvent' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Tracking/Events/AbstractEvent.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\EventFactory' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Tracking/Events/EventFactory.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\EventInterface' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Tracking/Events/EventInterface.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\Events' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Tracking/Events/Events.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\Injectable\\AbstractInjectableEvent' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Tracking/Events/Injectable/AbstractInjectableEvent.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\Injectable\\ClickLinkEvent' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Tracking/Events/Injectable/ClickLinkEvent.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\Injectable\\OpenEmailEvent' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Tracking/Events/Injectable/OpenEmailEvent.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Tracking\\Migration' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Tracking/Migration.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Tracking\\Tracking' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Tracking/Tracking.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\AbstractProcessor' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/AbstractProcessor.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\AbstractProvider' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/AbstractProvider.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\AbstractSubscriber' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/AbstractSubscriber.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Events\\Delivered' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Events/Delivered.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Events\\EventInterface' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Events/EventInterface.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Events\\Failed' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Events/Failed.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\ProcessorInterface' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/ProcessorInterface.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\ProviderInterface' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/ProviderInterface.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailgun\\Events\\Failed' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailgun/Events/Failed.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailgun\\Processor' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailgun/Processor.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailgun\\Provider' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailgun/Provider.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailgun\\Subscriber' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailgun/Subscriber.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailjet\\Events\\Failed' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailjet/Events/Failed.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailjet\\Processor' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailjet/Processor.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailjet\\Provider' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailjet/Provider.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailjet\\Subscriber' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailjet/Subscriber.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Postmark\\Events\\Failed' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/Postmark/Events/Failed.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Postmark\\Processor' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/Postmark/Processor.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Postmark\\Provider' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/Postmark/Provider.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Postmark\\Subscriber' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/Postmark/Subscriber.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTP2GO\\Events\\Failed' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTP2GO/Events/Failed.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTP2GO\\Processor' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTP2GO/Processor.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTP2GO\\Provider' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTP2GO/Provider.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTP2GO\\Subscriber' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTP2GO/Subscriber.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTPcom\\Events\\Failed' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTPcom/Events/Failed.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTPcom\\Processor' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTPcom/Processor.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTPcom\\Provider' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTPcom/Provider.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTPcom\\Subscriber' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTPcom/Subscriber.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendinblue\\Events\\Failed' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendinblue/Events/Failed.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendinblue\\Processor' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendinblue/Processor.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendinblue\\Provider' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendinblue/Provider.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendinblue\\Subscriber' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendinblue/Subscriber.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendlayer\\Events\\Failed' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendlayer/Events/Failed.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendlayer\\Processor' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendlayer/Processor.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendlayer\\Provider' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendlayer/Provider.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendlayer\\Subscriber' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendlayer/Subscriber.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SparkPost\\Events\\Failed' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/SparkPost/Events/Failed.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SparkPost\\Processor' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/SparkPost/Processor.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SparkPost\\Provider' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/SparkPost/Provider.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SparkPost\\Subscriber' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Providers/SparkPost/Subscriber.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\SubscriberInterface' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/SubscriberInterface.php',
        'WPMailSMTP\\Pro\\Emails\\Logs\\Webhooks\\Webhooks' => __DIR__ . '/../..' . '/src/Pro/Emails/Logs/Webhooks/Webhooks.php',
        'WPMailSMTP\\Pro\\Emails\\RateLimiting\\RateLimiting' => __DIR__ . '/../..' . '/src/Pro/Emails/RateLimiting/RateLimiting.php',
        'WPMailSMTP\\Pro\\Emails\\TestEmail' => __DIR__ . '/../..' . '/src/Pro/Emails/TestEmail.php',
        'WPMailSMTP\\Pro\\License\\License' => __DIR__ . '/../..' . '/src/Pro/License/License.php',
        'WPMailSMTP\\Pro\\License\\Updater' => __DIR__ . '/../..' . '/src/Pro/License/Updater.php',
        'WPMailSMTP\\Pro\\MailCatcher' => __DIR__ . '/../..' . '/src/Pro/MailCatcher.php',
        'WPMailSMTP\\Pro\\MailCatcherTrait' => __DIR__ . '/../..' . '/src/Pro/MailCatcherTrait.php',
        'WPMailSMTP\\Pro\\MailCatcherV6' => __DIR__ . '/../..' . '/src/Pro/MailCatcherV6.php',
        'WPMailSMTP\\Pro\\Migration' => __DIR__ . '/../..' . '/src/Pro/Migration.php',
        'WPMailSMTP\\Pro\\Multisite' => __DIR__ . '/../..' . '/src/Pro/Multisite.php',
        'WPMailSMTP\\Pro\\Pro' => __DIR__ . '/../..' . '/src/Pro/Pro.php',
        'WPMailSMTP\\Pro\\ProductApi\\Client' => __DIR__ . '/../..' . '/src/Pro/ProductApi/Client.php',
        'WPMailSMTP\\Pro\\ProductApi\\Credentials' => __DIR__ . '/../..' . '/src/Pro/ProductApi/Credentials.php',
        'WPMailSMTP\\Pro\\ProductApi\\CredentialsGenerationNonce' => __DIR__ . '/../..' . '/src/Pro/ProductApi/CredentialsGenerationNonce.php',
        'WPMailSMTP\\Pro\\ProductApi\\CredentialsGenerator' => __DIR__ . '/../..' . '/src/Pro/ProductApi/CredentialsGenerator.php',
        'WPMailSMTP\\Pro\\ProductApi\\CredentialsRepository' => __DIR__ . '/../..' . '/src/Pro/ProductApi/CredentialsRepository.php',
        'WPMailSMTP\\Pro\\ProductApi\\ProductApi' => __DIR__ . '/../..' . '/src/Pro/ProductApi/ProductApi.php',
        'WPMailSMTP\\Pro\\ProductApi\\Response' => __DIR__ . '/../..' . '/src/Pro/ProductApi/Response.php',
        'WPMailSMTP\\Pro\\Providers\\AmazonSES\\Auth' => __DIR__ . '/../..' . '/src/Pro/Providers/AmazonSES/Auth.php',
        'WPMailSMTP\\Pro\\Providers\\AmazonSES\\IdentitiesTable' => __DIR__ . '/../..' . '/src/Pro/Providers/AmazonSES/IdentitiesTable.php',
        'WPMailSMTP\\Pro\\Providers\\AmazonSES\\Identity' => __DIR__ . '/../..' . '/src/Pro/Providers/AmazonSES/Identity.php',
        'WPMailSMTP\\Pro\\Providers\\AmazonSES\\Mailer' => __DIR__ . '/../..' . '/src/Pro/Providers/AmazonSES/Mailer.php',
        'WPMailSMTP\\Pro\\Providers\\AmazonSES\\Options' => __DIR__ . '/../..' . '/src/Pro/Providers/AmazonSES/Options.php',
        'WPMailSMTP\\Pro\\Providers\\Gmail\\Api\\Client' => __DIR__ . '/../..' . '/src/Pro/Providers/Gmail/Api/Client.php',
        'WPMailSMTP\\Pro\\Providers\\Gmail\\Api\\OneTimeToken' => __DIR__ . '/../..' . '/src/Pro/Providers/Gmail/Api/OneTimeToken.php',
        'WPMailSMTP\\Pro\\Providers\\Gmail\\Api\\Response' => __DIR__ . '/../..' . '/src/Pro/Providers/Gmail/Api/Response.php',
        'WPMailSMTP\\Pro\\Providers\\Gmail\\Api\\SiteId' => __DIR__ . '/../..' . '/src/Pro/Providers/Gmail/Api/SiteId.php',
        'WPMailSMTP\\Pro\\Providers\\Gmail\\Auth' => __DIR__ . '/../..' . '/src/Pro/Providers/Gmail/Auth.php',
        'WPMailSMTP\\Pro\\Providers\\Gmail\\Mailer' => __DIR__ . '/../..' . '/src/Pro/Providers/Gmail/Mailer.php',
        'WPMailSMTP\\Pro\\Providers\\Gmail\\Options' => __DIR__ . '/../..' . '/src/Pro/Providers/Gmail/Options.php',
        'WPMailSMTP\\Pro\\Providers\\Gmail\\Provider' => __DIR__ . '/../..' . '/src/Pro/Providers/Gmail/Provider.php',
        'WPMailSMTP\\Pro\\Providers\\Outlook\\AttachmentsUploader' => __DIR__ . '/../..' . '/src/Pro/Providers/Outlook/AttachmentsUploader.php',
        'WPMailSMTP\\Pro\\Providers\\Outlook\\Auth' => __DIR__ . '/../..' . '/src/Pro/Providers/Outlook/Auth.php',
        'WPMailSMTP\\Pro\\Providers\\Outlook\\Mailer' => __DIR__ . '/../..' . '/src/Pro/Providers/Outlook/Mailer.php',
        'WPMailSMTP\\Pro\\Providers\\Outlook\\OneClick\\Auth' => __DIR__ . '/../..' . '/src/Pro/Providers/Outlook/OneClick/Auth.php',
        'WPMailSMTP\\Pro\\Providers\\Outlook\\OneClick\\Auth\\Client' => __DIR__ . '/../..' . '/src/Pro/Providers/Outlook/OneClick/Auth/Client.php',
        'WPMailSMTP\\Pro\\Providers\\Outlook\\OneClick\\Auth\\Response' => __DIR__ . '/../..' . '/src/Pro/Providers/Outlook/OneClick/Auth/Response.php',
        'WPMailSMTP\\Pro\\Providers\\Outlook\\OneClick\\Options' => __DIR__ . '/../..' . '/src/Pro/Providers/Outlook/OneClick/Options.php',
        'WPMailSMTP\\Pro\\Providers\\Outlook\\Options' => __DIR__ . '/../..' . '/src/Pro/Providers/Outlook/Options.php',
        'WPMailSMTP\\Pro\\Providers\\Outlook\\Provider' => __DIR__ . '/../..' . '/src/Pro/Providers/Outlook/Provider.php',
        'WPMailSMTP\\Pro\\Providers\\Providers' => __DIR__ . '/../..' . '/src/Pro/Providers/Providers.php',
        'WPMailSMTP\\Pro\\Providers\\Zoho\\Auth' => __DIR__ . '/../..' . '/src/Pro/Providers/Zoho/Auth.php',
        'WPMailSMTP\\Pro\\Providers\\Zoho\\Auth\\Zoho' => __DIR__ . '/../..' . '/src/Pro/Providers/Zoho/Auth/Zoho.php',
        'WPMailSMTP\\Pro\\Providers\\Zoho\\Auth\\ZohoUser' => __DIR__ . '/../..' . '/src/Pro/Providers/Zoho/Auth/ZohoUser.php',
        'WPMailSMTP\\Pro\\Providers\\Zoho\\Mailer' => __DIR__ . '/../..' . '/src/Pro/Providers/Zoho/Mailer.php',
        'WPMailSMTP\\Pro\\Providers\\Zoho\\Options' => __DIR__ . '/../..' . '/src/Pro/Providers/Zoho/Options.php',
        'WPMailSMTP\\Pro\\SiteHealth' => __DIR__ . '/../..' . '/src/Pro/SiteHealth.php',
        'WPMailSMTP\\Pro\\SmartRouting\\Admin\\SettingsTab' => __DIR__ . '/../..' . '/src/Pro/SmartRouting/Admin/SettingsTab.php',
        'WPMailSMTP\\Pro\\SmartRouting\\ConditionalLogic' => __DIR__ . '/../..' . '/src/Pro/SmartRouting/ConditionalLogic.php',
        'WPMailSMTP\\Pro\\SmartRouting\\SmartRouting' => __DIR__ . '/../..' . '/src/Pro/SmartRouting/SmartRouting.php',
        'WPMailSMTP\\Pro\\Tasks\\EmailLogCleanupTask' => __DIR__ . '/../..' . '/src/Pro/Tasks/EmailLogCleanupTask.php',
        'WPMailSMTP\\Pro\\Tasks\\LicenseCheckTask' => __DIR__ . '/../..' . '/src/Pro/Tasks/LicenseCheckTask.php',
        'WPMailSMTP\\Pro\\Tasks\\Logs\\BulkVerifySentStatusTask' => __DIR__ . '/../..' . '/src/Pro/Tasks/Logs/BulkVerifySentStatusTask.php',
        'WPMailSMTP\\Pro\\Tasks\\Logs\\ElasticEmail\\VerifySentStatusTask' => __DIR__ . '/../..' . '/src/Pro/Tasks/Logs/ElasticEmail/VerifySentStatusTask.php',
        'WPMailSMTP\\Pro\\Tasks\\Logs\\ExportCleanupTask' => __DIR__ . '/../..' . '/src/Pro/Tasks/Logs/ExportCleanupTask.php',
        'WPMailSMTP\\Pro\\Tasks\\Logs\\Mailgun\\VerifySentStatusTask' => __DIR__ . '/../..' . '/src/Pro/Tasks/Logs/Mailgun/VerifySentStatusTask.php',
        'WPMailSMTP\\Pro\\Tasks\\Logs\\Mailjet\\VerifySentStatusTask' => __DIR__ . '/../..' . '/src/Pro/Tasks/Logs/Mailjet/VerifySentStatusTask.php',
        'WPMailSMTP\\Pro\\Tasks\\Logs\\Postmark\\VerifySentStatusTask' => __DIR__ . '/../..' . '/src/Pro/Tasks/Logs/Postmark/VerifySentStatusTask.php',
        'WPMailSMTP\\Pro\\Tasks\\Logs\\ResendTask' => __DIR__ . '/../..' . '/src/Pro/Tasks/Logs/ResendTask.php',
        'WPMailSMTP\\Pro\\Tasks\\Logs\\SMTP2GO\\VerifySentStatusTask' => __DIR__ . '/../..' . '/src/Pro/Tasks/Logs/SMTP2GO/VerifySentStatusTask.php',
        'WPMailSMTP\\Pro\\Tasks\\Logs\\SMTPcom\\VerifySentStatusTask' => __DIR__ . '/../..' . '/src/Pro/Tasks/Logs/SMTPcom/VerifySentStatusTask.php',
        'WPMailSMTP\\Pro\\Tasks\\Logs\\Sendinblue\\VerifySentStatusTask' => __DIR__ . '/../..' . '/src/Pro/Tasks/Logs/Sendinblue/VerifySentStatusTask.php',
        'WPMailSMTP\\Pro\\Tasks\\Logs\\Sendlayer\\VerifySentStatusTask' => __DIR__ . '/../..' . '/src/Pro/Tasks/Logs/Sendlayer/VerifySentStatusTask.php',
        'WPMailSMTP\\Pro\\Tasks\\Logs\\SparkPost\\VerifySentStatusTask' => __DIR__ . '/../..' . '/src/Pro/Tasks/Logs/SparkPost/VerifySentStatusTask.php',
        'WPMailSMTP\\Pro\\Tasks\\Logs\\VerifySentStatusTaskAbstract' => __DIR__ . '/../..' . '/src/Pro/Tasks/Logs/VerifySentStatusTaskAbstract.php',
        'WPMailSMTP\\Pro\\Tasks\\Migrations\\EmailLogMigration11' => __DIR__ . '/../..' . '/src/Pro/Tasks/Migrations/EmailLogMigration11.php',
        'WPMailSMTP\\Pro\\Tasks\\Migrations\\EmailLogMigration4' => __DIR__ . '/../..' . '/src/Pro/Tasks/Migrations/EmailLogMigration4.php',
        'WPMailSMTP\\Pro\\Tasks\\Migrations\\EmailLogMigration5' => __DIR__ . '/../..' . '/src/Pro/Tasks/Migrations/EmailLogMigration5.php',
        'WPMailSMTP\\Pro\\Tasks\\NotifierTask' => __DIR__ . '/../..' . '/src/Pro/Tasks/NotifierTask.php',
        'WPMailSMTP\\Pro\\Translations' => __DIR__ . '/../..' . '/src/Pro/Translations.php',
        'WPMailSMTP\\Pro\\Upgrade' => __DIR__ . '/../..' . '/src/Pro/Upgrade.php',
        'WPMailSMTP\\Processor' => __DIR__ . '/../..' . '/src/Processor.php',
        'WPMailSMTP\\Providers\\AmazonSES\\Options' => __DIR__ . '/../..' . '/src/Providers/AmazonSES/Options.php',
        'WPMailSMTP\\Providers\\AuthAbstract' => __DIR__ . '/../..' . '/src/Providers/AuthAbstract.php',
        'WPMailSMTP\\Providers\\AuthInterface' => __DIR__ . '/../..' . '/src/Providers/AuthInterface.php',
        'WPMailSMTP\\Providers\\ElasticEmail\\Mailer' => __DIR__ . '/../..' . '/src/Providers/ElasticEmail/Mailer.php',
        'WPMailSMTP\\Providers\\ElasticEmail\\Options' => __DIR__ . '/../..' . '/src/Providers/ElasticEmail/Options.php',
        'WPMailSMTP\\Providers\\Gmail\\Auth' => __DIR__ . '/../..' . '/src/Providers/Gmail/Auth.php',
        'WPMailSMTP\\Providers\\Gmail\\Mailer' => __DIR__ . '/../..' . '/src/Providers/Gmail/Mailer.php',
        'WPMailSMTP\\Providers\\Gmail\\Options' => __DIR__ . '/../..' . '/src/Providers/Gmail/Options.php',
        'WPMailSMTP\\Providers\\Loader' => __DIR__ . '/../..' . '/src/Providers/Loader.php',
        'WPMailSMTP\\Providers\\Mail\\Mailer' => __DIR__ . '/../..' . '/src/Providers/Mail/Mailer.php',
        'WPMailSMTP\\Providers\\Mail\\Options' => __DIR__ . '/../..' . '/src/Providers/Mail/Options.php',
        'WPMailSMTP\\Providers\\MailerAbstract' => __DIR__ . '/../..' . '/src/Providers/MailerAbstract.php',
        'WPMailSMTP\\Providers\\MailerInterface' => __DIR__ . '/../..' . '/src/Providers/MailerInterface.php',
        'WPMailSMTP\\Providers\\Mailgun\\Mailer' => __DIR__ . '/../..' . '/src/Providers/Mailgun/Mailer.php',
        'WPMailSMTP\\Providers\\Mailgun\\Options' => __DIR__ . '/../..' . '/src/Providers/Mailgun/Options.php',
        'WPMailSMTP\\Providers\\Mailjet\\Mailer' => __DIR__ . '/../..' . '/src/Providers/Mailjet/Mailer.php',
        'WPMailSMTP\\Providers\\Mailjet\\Options' => __DIR__ . '/../..' . '/src/Providers/Mailjet/Options.php',
        'WPMailSMTP\\Providers\\OptionsAbstract' => __DIR__ . '/../..' . '/src/Providers/OptionsAbstract.php',
        'WPMailSMTP\\Providers\\OptionsInterface' => __DIR__ . '/../..' . '/src/Providers/OptionsInterface.php',
        'WPMailSMTP\\Providers\\Outlook\\Options' => __DIR__ . '/../..' . '/src/Providers/Outlook/Options.php',
        'WPMailSMTP\\Providers\\Outlook\\Provider' => __DIR__ . '/../..' . '/src/Providers/Outlook/Provider.php',
        'WPMailSMTP\\Providers\\PepipostAPI\\Mailer' => __DIR__ . '/../..' . '/src/Providers/PepipostAPI/Mailer.php',
        'WPMailSMTP\\Providers\\PepipostAPI\\Options' => __DIR__ . '/../..' . '/src/Providers/PepipostAPI/Options.php',
        'WPMailSMTP\\Providers\\Pepipost\\Mailer' => __DIR__ . '/../..' . '/src/Providers/Pepipost/Mailer.php',
        'WPMailSMTP\\Providers\\Pepipost\\Options' => __DIR__ . '/../..' . '/src/Providers/Pepipost/Options.php',
        'WPMailSMTP\\Providers\\Postmark\\Mailer' => __DIR__ . '/../..' . '/src/Providers/Postmark/Mailer.php',
        'WPMailSMTP\\Providers\\Postmark\\Options' => __DIR__ . '/../..' . '/src/Providers/Postmark/Options.php',
        'WPMailSMTP\\Providers\\SMTP2GO\\Mailer' => __DIR__ . '/../..' . '/src/Providers/SMTP2GO/Mailer.php',
        'WPMailSMTP\\Providers\\SMTP2GO\\Options' => __DIR__ . '/../..' . '/src/Providers/SMTP2GO/Options.php',
        'WPMailSMTP\\Providers\\SMTP\\Mailer' => __DIR__ . '/../..' . '/src/Providers/SMTP/Mailer.php',
        'WPMailSMTP\\Providers\\SMTP\\Options' => __DIR__ . '/../..' . '/src/Providers/SMTP/Options.php',
        'WPMailSMTP\\Providers\\SMTPcom\\Mailer' => __DIR__ . '/../..' . '/src/Providers/SMTPcom/Mailer.php',
        'WPMailSMTP\\Providers\\SMTPcom\\Options' => __DIR__ . '/../..' . '/src/Providers/SMTPcom/Options.php',
        'WPMailSMTP\\Providers\\Sendgrid\\Mailer' => __DIR__ . '/../..' . '/src/Providers/Sendgrid/Mailer.php',
        'WPMailSMTP\\Providers\\Sendgrid\\Options' => __DIR__ . '/../..' . '/src/Providers/Sendgrid/Options.php',
        'WPMailSMTP\\Providers\\Sendinblue\\Api' => __DIR__ . '/../..' . '/src/Providers/Sendinblue/Api.php',
        'WPMailSMTP\\Providers\\Sendinblue\\Mailer' => __DIR__ . '/../..' . '/src/Providers/Sendinblue/Mailer.php',
        'WPMailSMTP\\Providers\\Sendinblue\\Options' => __DIR__ . '/../..' . '/src/Providers/Sendinblue/Options.php',
        'WPMailSMTP\\Providers\\Sendlayer\\Mailer' => __DIR__ . '/../..' . '/src/Providers/Sendlayer/Mailer.php',
        'WPMailSMTP\\Providers\\Sendlayer\\Options' => __DIR__ . '/../..' . '/src/Providers/Sendlayer/Options.php',
        'WPMailSMTP\\Providers\\SparkPost\\Mailer' => __DIR__ . '/../..' . '/src/Providers/SparkPost/Mailer.php',
        'WPMailSMTP\\Providers\\SparkPost\\Options' => __DIR__ . '/../..' . '/src/Providers/SparkPost/Options.php',
        'WPMailSMTP\\Providers\\Zoho\\Options' => __DIR__ . '/../..' . '/src/Providers/Zoho/Options.php',
        'WPMailSMTP\\Queue\\Attachments' => __DIR__ . '/../..' . '/src/Queue/Attachments.php',
        'WPMailSMTP\\Queue\\Email' => __DIR__ . '/../..' . '/src/Queue/Email.php',
        'WPMailSMTP\\Queue\\Migration' => __DIR__ . '/../..' . '/src/Queue/Migration.php',
        'WPMailSMTP\\Queue\\Queue' => __DIR__ . '/../..' . '/src/Queue/Queue.php',
        'WPMailSMTP\\Reports\\Emails\\Summary' => __DIR__ . '/../..' . '/src/Reports/Emails/Summary.php',
        'WPMailSMTP\\Reports\\Reports' => __DIR__ . '/../..' . '/src/Reports/Reports.php',
        'WPMailSMTP\\SiteHealth' => __DIR__ . '/../..' . '/src/SiteHealth.php',
        'WPMailSMTP\\Tasks\\DebugEventsCleanupTask' => __DIR__ . '/../..' . '/src/Tasks/DebugEventsCleanupTask.php',
        'WPMailSMTP\\Tasks\\Meta' => __DIR__ . '/../..' . '/src/Tasks/Meta.php',
        'WPMailSMTP\\Tasks\\NotificationsUpdateTask' => __DIR__ . '/../..' . '/src/Tasks/NotificationsUpdateTask.php',
        'WPMailSMTP\\Tasks\\Queue\\CleanupQueueTask' => __DIR__ . '/../..' . '/src/Tasks/Queue/CleanupQueueTask.php',
        'WPMailSMTP\\Tasks\\Queue\\ProcessQueueTask' => __DIR__ . '/../..' . '/src/Tasks/Queue/ProcessQueueTask.php',
        'WPMailSMTP\\Tasks\\Queue\\SendEnqueuedEmailTask' => __DIR__ . '/../..' . '/src/Tasks/Queue/SendEnqueuedEmailTask.php',
        'WPMailSMTP\\Tasks\\Reports\\SummaryEmailTask' => __DIR__ . '/../..' . '/src/Tasks/Reports/SummaryEmailTask.php',
        'WPMailSMTP\\Tasks\\Task' => __DIR__ . '/../..' . '/src/Tasks/Task.php',
        'WPMailSMTP\\Tasks\\Tasks' => __DIR__ . '/../..' . '/src/Tasks/Tasks.php',
        'WPMailSMTP\\Upgrade' => __DIR__ . '/../..' . '/src/Upgrade.php',
        'WPMailSMTP\\Uploads' => __DIR__ . '/../..' . '/src/Uploads.php',
        'WPMailSMTP\\UsageTracking\\SendUsageTask' => __DIR__ . '/../..' . '/src/UsageTracking/SendUsageTask.php',
        'WPMailSMTP\\UsageTracking\\UsageTracking' => __DIR__ . '/../..' . '/src/UsageTracking/UsageTracking.php',
        'WPMailSMTP\\Vendor\\Google\\AccessToken\\Revoke' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/AccessToken/Revoke.php',
        'WPMailSMTP\\Vendor\\Google\\AccessToken\\Verify' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/AccessToken/Verify.php',
        'WPMailSMTP\\Vendor\\Google\\AuthHandler\\AuthHandlerFactory' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/AuthHandler/AuthHandlerFactory.php',
        'WPMailSMTP\\Vendor\\Google\\AuthHandler\\Guzzle5AuthHandler' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/AuthHandler/Guzzle5AuthHandler.php',
        'WPMailSMTP\\Vendor\\Google\\AuthHandler\\Guzzle6AuthHandler' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/AuthHandler/Guzzle6AuthHandler.php',
        'WPMailSMTP\\Vendor\\Google\\AuthHandler\\Guzzle7AuthHandler' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/AuthHandler/Guzzle7AuthHandler.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\AccessToken' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/AccessToken.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\ApplicationDefaultCredentials' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/ApplicationDefaultCredentials.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\CacheTrait' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/CacheTrait.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\Cache\\InvalidArgumentException' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/Cache/InvalidArgumentException.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\Cache\\Item' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/Cache/Item.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\Cache\\MemoryCacheItemPool' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/Cache/MemoryCacheItemPool.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\Cache\\SysVCacheItemPool' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/Cache/SysVCacheItemPool.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\Cache\\TypedItem' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/Cache/TypedItem.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\CredentialsLoader' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/CredentialsLoader.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\Credentials\\AppIdentityCredentials' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/Credentials/AppIdentityCredentials.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\Credentials\\GCECredentials' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/Credentials/GCECredentials.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\Credentials\\IAMCredentials' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/Credentials/IAMCredentials.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\Credentials\\ImpersonatedServiceAccountCredentials' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/Credentials/ImpersonatedServiceAccountCredentials.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\Credentials\\InsecureCredentials' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/Credentials/InsecureCredentials.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\Credentials\\ServiceAccountCredentials' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/Credentials/ServiceAccountCredentials.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\Credentials\\ServiceAccountJwtAccessCredentials' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/Credentials/ServiceAccountJwtAccessCredentials.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\Credentials\\UserRefreshCredentials' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/Credentials/UserRefreshCredentials.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\FetchAuthTokenCache' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/FetchAuthTokenCache.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\FetchAuthTokenInterface' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/FetchAuthTokenInterface.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\GCECache' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/GCECache.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\GetQuotaProjectInterface' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/GetQuotaProjectInterface.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\HttpHandler\\Guzzle5HttpHandler' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/HttpHandler/Guzzle5HttpHandler.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\HttpHandler\\Guzzle6HttpHandler' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/HttpHandler/Guzzle6HttpHandler.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\HttpHandler\\Guzzle7HttpHandler' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/HttpHandler/Guzzle7HttpHandler.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\HttpHandler\\HttpClientCache' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/HttpHandler/HttpClientCache.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\HttpHandler\\HttpHandlerFactory' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/HttpHandler/HttpHandlerFactory.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\Iam' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/Iam.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\IamSignerTrait' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/IamSignerTrait.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\Middleware\\AuthTokenMiddleware' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/Middleware/AuthTokenMiddleware.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\Middleware\\ProxyAuthTokenMiddleware' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/Middleware/ProxyAuthTokenMiddleware.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\Middleware\\ScopedAccessTokenMiddleware' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/Middleware/ScopedAccessTokenMiddleware.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\Middleware\\SimpleMiddleware' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/Middleware/SimpleMiddleware.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\OAuth2' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/OAuth2.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\ProjectIdProviderInterface' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/ProjectIdProviderInterface.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\ServiceAccountSignerTrait' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/ServiceAccountSignerTrait.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\SignBlobInterface' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/SignBlobInterface.php',
        'WPMailSMTP\\Vendor\\Google\\Auth\\UpdateMetadataInterface' => __DIR__ . '/../..' . '/vendor_prefixed/google/auth/src/UpdateMetadataInterface.php',
        'WPMailSMTP\\Vendor\\Google\\Client' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/Client.php',
        'WPMailSMTP\\Vendor\\Google\\Collection' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/Collection.php',
        'WPMailSMTP\\Vendor\\Google\\Exception' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/Exception.php',
        'WPMailSMTP\\Vendor\\Google\\Http\\Batch' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/Http/Batch.php',
        'WPMailSMTP\\Vendor\\Google\\Http\\MediaFileUpload' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/Http/MediaFileUpload.php',
        'WPMailSMTP\\Vendor\\Google\\Http\\REST' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/Http/REST.php',
        'WPMailSMTP\\Vendor\\Google\\Model' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/Model.php',
        'WPMailSMTP\\Vendor\\Google\\Service' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/Service.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Exception' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/Service/Exception.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\AutoForwarding' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/AutoForwarding.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\BatchDeleteMessagesRequest' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/BatchDeleteMessagesRequest.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\BatchModifyMessagesRequest' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/BatchModifyMessagesRequest.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\CseIdentity' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/CseIdentity.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\CseKeyPair' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/CseKeyPair.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\CsePrivateKeyMetadata' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/CsePrivateKeyMetadata.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Delegate' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Delegate.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\DisableCseKeyPairRequest' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/DisableCseKeyPairRequest.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Draft' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Draft.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\EnableCseKeyPairRequest' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/EnableCseKeyPairRequest.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Filter' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Filter.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\FilterAction' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/FilterAction.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\FilterCriteria' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/FilterCriteria.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ForwardingAddress' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/ForwardingAddress.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\History' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/History.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\HistoryLabelAdded' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/HistoryLabelAdded.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\HistoryLabelRemoved' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/HistoryLabelRemoved.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\HistoryMessageAdded' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/HistoryMessageAdded.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\HistoryMessageDeleted' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/HistoryMessageDeleted.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ImapSettings' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/ImapSettings.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\KaclsKeyMetadata' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/KaclsKeyMetadata.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Label' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Label.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\LabelColor' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/LabelColor.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\LanguageSettings' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/LanguageSettings.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListCseIdentitiesResponse' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListCseIdentitiesResponse.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListCseKeyPairsResponse' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListCseKeyPairsResponse.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListDelegatesResponse' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListDelegatesResponse.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListDraftsResponse' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListDraftsResponse.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListFiltersResponse' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListFiltersResponse.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListForwardingAddressesResponse' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListForwardingAddressesResponse.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListHistoryResponse' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListHistoryResponse.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListLabelsResponse' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListLabelsResponse.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListMessagesResponse' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListMessagesResponse.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListSendAsResponse' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListSendAsResponse.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListSmimeInfoResponse' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListSmimeInfoResponse.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ListThreadsResponse' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/ListThreadsResponse.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Message' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Message.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\MessagePart' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/MessagePart.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\MessagePartBody' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/MessagePartBody.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\MessagePartHeader' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/MessagePartHeader.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ModifyMessageRequest' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/ModifyMessageRequest.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ModifyThreadRequest' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/ModifyThreadRequest.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\ObliterateCseKeyPairRequest' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/ObliterateCseKeyPairRequest.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\PopSettings' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/PopSettings.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Profile' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Profile.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\Users' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/Users.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersDrafts' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersDrafts.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersHistory' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersHistory.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersLabels' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersLabels.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersMessages' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersMessages.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersMessagesAttachments' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersMessagesAttachments.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersSettings' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersSettings.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersSettingsCse' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersSettingsCse.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersSettingsCseIdentities' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersSettingsCseIdentities.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersSettingsCseKeypairs' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersSettingsCseKeypairs.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersSettingsDelegates' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersSettingsDelegates.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersSettingsFilters' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersSettingsFilters.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersSettingsForwardingAddresses' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersSettingsForwardingAddresses.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersSettingsSendAs' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersSettingsSendAs.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersSettingsSendAsSmimeInfo' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersSettingsSendAsSmimeInfo.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Resource\\UsersThreads' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Resource/UsersThreads.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\SendAs' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/SendAs.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\SmimeInfo' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/SmimeInfo.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\SmtpMsa' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/SmtpMsa.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\Thread' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/Thread.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\VacationSettings' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/VacationSettings.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\WatchRequest' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/WatchRequest.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Gmail\\WatchResponse' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient-services/src/Gmail/WatchResponse.php',
        'WPMailSMTP\\Vendor\\Google\\Service\\Resource' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/Service/Resource.php',
        'WPMailSMTP\\Vendor\\Google\\Task\\Composer' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/Task/Composer.php',
        'WPMailSMTP\\Vendor\\Google\\Task\\Exception' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/Task/Exception.php',
        'WPMailSMTP\\Vendor\\Google\\Task\\Retryable' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/Task/Retryable.php',
        'WPMailSMTP\\Vendor\\Google\\Task\\Runner' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/Task/Runner.php',
        'WPMailSMTP\\Vendor\\Google\\Utils\\UriTemplate' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/Utils/UriTemplate.php',
        'WPMailSMTP\\Vendor\\Google_AccessToken_Revoke' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\Google_AccessToken_Verify' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\Google_AuthHandler_AuthHandlerFactory' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\Google_AuthHandler_Guzzle5AuthHandler' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\Google_AuthHandler_Guzzle6AuthHandler' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\Google_AuthHandler_Guzzle7AuthHandler' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\Google_Client' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\Google_Collection' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\Google_Exception' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\Google_Http_Batch' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\Google_Http_MediaFileUpload' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\Google_Http_REST' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\Google_Model' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\Google_Service' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\Google_Service_Exception' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\Google_Service_Resource' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\Google_Task_Composer' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\Google_Task_Exception' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\Google_Task_Retryable' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\Google_Task_Runner' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\Google_Utils_UriTemplate' => __DIR__ . '/../..' . '/vendor_prefixed/google/apiclient/src/aliases.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\BodySummarizer' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/BodySummarizer.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\BodySummarizerInterface' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/BodySummarizerInterface.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Client' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Client.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\ClientInterface' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/ClientInterface.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\ClientTrait' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/ClientTrait.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Cookie\\CookieJar' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Cookie/CookieJar.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Cookie\\CookieJarInterface' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Cookie/CookieJarInterface.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Cookie\\FileCookieJar' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Cookie/FileCookieJar.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Cookie\\SessionCookieJar' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Cookie/SessionCookieJar.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Cookie\\SetCookie' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Cookie/SetCookie.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Exception\\BadResponseException' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/BadResponseException.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Exception\\ClientException' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/ClientException.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Exception\\ConnectException' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/ConnectException.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Exception\\GuzzleException' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/GuzzleException.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Exception\\InvalidArgumentException' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/InvalidArgumentException.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Exception\\RequestException' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/RequestException.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Exception\\ServerException' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/ServerException.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Exception\\TooManyRedirectsException' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/TooManyRedirectsException.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Exception\\TransferException' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/TransferException.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\HandlerStack' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/HandlerStack.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Handler\\CurlFactory' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/CurlFactory.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Handler\\CurlFactoryInterface' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/CurlFactoryInterface.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Handler\\CurlHandler' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/CurlHandler.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Handler\\CurlMultiHandler' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/CurlMultiHandler.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Handler\\EasyHandle' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/EasyHandle.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Handler\\HeaderProcessor' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/HeaderProcessor.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Handler\\MockHandler' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/MockHandler.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Handler\\Proxy' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/Proxy.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Handler\\StreamHandler' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/StreamHandler.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\MessageFormatter' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/MessageFormatter.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\MessageFormatterInterface' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/MessageFormatterInterface.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Middleware' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Middleware.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Pool' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Pool.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\PrepareBodyMiddleware' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/PrepareBodyMiddleware.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\AggregateException' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/promises/src/AggregateException.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\CancellationException' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/promises/src/CancellationException.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\Coroutine' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/promises/src/Coroutine.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\Create' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/promises/src/Create.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\Each' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/promises/src/Each.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\EachPromise' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/promises/src/EachPromise.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\FulfilledPromise' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/promises/src/FulfilledPromise.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\Is' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/promises/src/Is.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\Promise' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/promises/src/Promise.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\PromiseInterface' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/promises/src/PromiseInterface.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\PromisorInterface' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/promises/src/PromisorInterface.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\RejectedPromise' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/promises/src/RejectedPromise.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\RejectionException' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/promises/src/RejectionException.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\TaskQueue' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/promises/src/TaskQueue.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\TaskQueueInterface' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/promises/src/TaskQueueInterface.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Promise\\Utils' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/promises/src/Utils.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\AppendStream' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/AppendStream.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\BufferStream' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/BufferStream.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\CachingStream' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/CachingStream.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\DroppingStream' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/DroppingStream.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\Exception\\MalformedUriException' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/Exception/MalformedUriException.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\FnStream' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/FnStream.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\Header' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/Header.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\HttpFactory' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/HttpFactory.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\InflateStream' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/InflateStream.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\LazyOpenStream' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/LazyOpenStream.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\LimitStream' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/LimitStream.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\Message' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/Message.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\MessageTrait' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/MessageTrait.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\MimeType' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/MimeType.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\MultipartStream' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/MultipartStream.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\NoSeekStream' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/NoSeekStream.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\PumpStream' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/PumpStream.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\Query' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/Query.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\Request' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/Request.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\Response' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/Response.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\Rfc7230' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/Rfc7230.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\ServerRequest' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/ServerRequest.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\Stream' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/Stream.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\StreamDecoratorTrait' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/StreamDecoratorTrait.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\StreamWrapper' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/StreamWrapper.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\UploadedFile' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/UploadedFile.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\Uri' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/Uri.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\UriComparator' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/UriComparator.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\UriNormalizer' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/UriNormalizer.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\UriResolver' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/UriResolver.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Psr7\\Utils' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/psr7/src/Utils.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\RedirectMiddleware' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/RedirectMiddleware.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\RequestOptions' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/RequestOptions.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\RetryMiddleware' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/RetryMiddleware.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\TransferStats' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/TransferStats.php',
        'WPMailSMTP\\Vendor\\GuzzleHttp\\Utils' => __DIR__ . '/../..' . '/vendor_prefixed/guzzlehttp/guzzle/src/Utils.php',
        'WPMailSMTP\\Vendor\\Monolog\\Attribute\\AsMonologProcessor' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Attribute/AsMonologProcessor.php',
        'WPMailSMTP\\Vendor\\Monolog\\DateTimeImmutable' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/DateTimeImmutable.php',
        'WPMailSMTP\\Vendor\\Monolog\\ErrorHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/ErrorHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Formatter\\ChromePHPFormatter' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/ChromePHPFormatter.php',
        'WPMailSMTP\\Vendor\\Monolog\\Formatter\\ElasticaFormatter' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/ElasticaFormatter.php',
        'WPMailSMTP\\Vendor\\Monolog\\Formatter\\ElasticsearchFormatter' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/ElasticsearchFormatter.php',
        'WPMailSMTP\\Vendor\\Monolog\\Formatter\\FlowdockFormatter' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/FlowdockFormatter.php',
        'WPMailSMTP\\Vendor\\Monolog\\Formatter\\FluentdFormatter' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/FluentdFormatter.php',
        'WPMailSMTP\\Vendor\\Monolog\\Formatter\\FormatterInterface' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/FormatterInterface.php',
        'WPMailSMTP\\Vendor\\Monolog\\Formatter\\GelfMessageFormatter' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/GelfMessageFormatter.php',
        'WPMailSMTP\\Vendor\\Monolog\\Formatter\\GoogleCloudLoggingFormatter' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/GoogleCloudLoggingFormatter.php',
        'WPMailSMTP\\Vendor\\Monolog\\Formatter\\HtmlFormatter' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/HtmlFormatter.php',
        'WPMailSMTP\\Vendor\\Monolog\\Formatter\\JsonFormatter' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/JsonFormatter.php',
        'WPMailSMTP\\Vendor\\Monolog\\Formatter\\LineFormatter' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/LineFormatter.php',
        'WPMailSMTP\\Vendor\\Monolog\\Formatter\\LogglyFormatter' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/LogglyFormatter.php',
        'WPMailSMTP\\Vendor\\Monolog\\Formatter\\LogmaticFormatter' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/LogmaticFormatter.php',
        'WPMailSMTP\\Vendor\\Monolog\\Formatter\\LogstashFormatter' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/LogstashFormatter.php',
        'WPMailSMTP\\Vendor\\Monolog\\Formatter\\MongoDBFormatter' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/MongoDBFormatter.php',
        'WPMailSMTP\\Vendor\\Monolog\\Formatter\\NormalizerFormatter' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/NormalizerFormatter.php',
        'WPMailSMTP\\Vendor\\Monolog\\Formatter\\ScalarFormatter' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/ScalarFormatter.php',
        'WPMailSMTP\\Vendor\\Monolog\\Formatter\\WildfireFormatter' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Formatter/WildfireFormatter.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\AbstractHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/AbstractHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\AbstractProcessingHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\AbstractSyslogHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/AbstractSyslogHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\AmqpHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/AmqpHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\BrowserConsoleHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/BrowserConsoleHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\BufferHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/BufferHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\ChromePHPHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ChromePHPHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\CouchDBHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/CouchDBHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\CubeHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/CubeHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\Curl\\Util' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/Curl/Util.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\DeduplicationHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/DeduplicationHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\DoctrineCouchDBHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/DoctrineCouchDBHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\DynamoDbHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/DynamoDbHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\ElasticaHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ElasticaHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\ElasticsearchHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ElasticsearchHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\ErrorLogHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ErrorLogHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\FallbackGroupHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FallbackGroupHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\FilterHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FilterHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\FingersCrossedHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FingersCrossedHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\FingersCrossed\\ActivationStrategyInterface' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FingersCrossed/ActivationStrategyInterface.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\FingersCrossed\\ChannelLevelActivationStrategy' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FingersCrossed/ChannelLevelActivationStrategy.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\FingersCrossed\\ErrorLevelActivationStrategy' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FingersCrossed/ErrorLevelActivationStrategy.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\FirePHPHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FirePHPHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\FleepHookHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FleepHookHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\FlowdockHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FlowdockHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\FormattableHandlerInterface' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FormattableHandlerInterface.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\FormattableHandlerTrait' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/FormattableHandlerTrait.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\GelfHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/GelfHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\GroupHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/GroupHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\Handler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/Handler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\HandlerInterface' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/HandlerInterface.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\HandlerWrapper' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/HandlerWrapper.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\IFTTTHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/IFTTTHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\InsightOpsHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/InsightOpsHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\LogEntriesHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/LogEntriesHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\LogglyHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/LogglyHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\LogmaticHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/LogmaticHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\MailHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/MailHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\MandrillHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/MandrillHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\MissingExtensionException' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/MissingExtensionException.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\MongoDBHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/MongoDBHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\NativeMailerHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/NativeMailerHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\NewRelicHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/NewRelicHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\NoopHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/NoopHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\NullHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/NullHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\OverflowHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/OverflowHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\PHPConsoleHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/PHPConsoleHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\ProcessHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ProcessHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\ProcessableHandlerInterface' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ProcessableHandlerInterface.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\ProcessableHandlerTrait' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ProcessableHandlerTrait.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\PsrHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/PsrHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\PushoverHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/PushoverHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\RedisHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/RedisHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\RedisPubSubHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/RedisPubSubHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\RollbarHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/RollbarHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\RotatingFileHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/RotatingFileHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\SamplingHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SamplingHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\SendGridHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SendGridHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\SlackHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SlackHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\SlackWebhookHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SlackWebhookHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\Slack\\SlackRecord' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/Slack/SlackRecord.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\SocketHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SocketHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\SqsHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SqsHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\StreamHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/StreamHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\SwiftMailerHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SwiftMailerHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\SymfonyMailerHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SymfonyMailerHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\SyslogHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SyslogHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\SyslogUdpHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SyslogUdpHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\SyslogUdp\\UdpSocket' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/SyslogUdp/UdpSocket.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\TelegramBotHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/TelegramBotHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\TestHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/TestHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\WebRequestRecognizerTrait' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/WebRequestRecognizerTrait.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\WhatFailureGroupHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/WhatFailureGroupHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Handler\\ZendMonitorHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Handler/ZendMonitorHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\LogRecord' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/LogRecord.php',
        'WPMailSMTP\\Vendor\\Monolog\\Logger' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Logger.php',
        'WPMailSMTP\\Vendor\\Monolog\\Processor\\GitProcessor' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/GitProcessor.php',
        'WPMailSMTP\\Vendor\\Monolog\\Processor\\HostnameProcessor' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/HostnameProcessor.php',
        'WPMailSMTP\\Vendor\\Monolog\\Processor\\IntrospectionProcessor' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/IntrospectionProcessor.php',
        'WPMailSMTP\\Vendor\\Monolog\\Processor\\MemoryPeakUsageProcessor' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/MemoryPeakUsageProcessor.php',
        'WPMailSMTP\\Vendor\\Monolog\\Processor\\MemoryProcessor' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/MemoryProcessor.php',
        'WPMailSMTP\\Vendor\\Monolog\\Processor\\MemoryUsageProcessor' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/MemoryUsageProcessor.php',
        'WPMailSMTP\\Vendor\\Monolog\\Processor\\MercurialProcessor' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/MercurialProcessor.php',
        'WPMailSMTP\\Vendor\\Monolog\\Processor\\ProcessIdProcessor' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/ProcessIdProcessor.php',
        'WPMailSMTP\\Vendor\\Monolog\\Processor\\ProcessorInterface' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/ProcessorInterface.php',
        'WPMailSMTP\\Vendor\\Monolog\\Processor\\PsrLogMessageProcessor' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/PsrLogMessageProcessor.php',
        'WPMailSMTP\\Vendor\\Monolog\\Processor\\TagProcessor' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/TagProcessor.php',
        'WPMailSMTP\\Vendor\\Monolog\\Processor\\UidProcessor' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/UidProcessor.php',
        'WPMailSMTP\\Vendor\\Monolog\\Processor\\WebProcessor' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Processor/WebProcessor.php',
        'WPMailSMTP\\Vendor\\Monolog\\Registry' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Registry.php',
        'WPMailSMTP\\Vendor\\Monolog\\ResettableInterface' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/ResettableInterface.php',
        'WPMailSMTP\\Vendor\\Monolog\\SignalHandler' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/SignalHandler.php',
        'WPMailSMTP\\Vendor\\Monolog\\Test\\TestCase' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Test/TestCase.php',
        'WPMailSMTP\\Vendor\\Monolog\\Utils' => __DIR__ . '/../..' . '/vendor_prefixed/monolog/monolog/src/Monolog/Utils.php',
        'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\Base32' => __DIR__ . '/../..' . '/vendor_prefixed/paragonie/constant_time_encoding/src/Base32.php',
        'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\Base32Hex' => __DIR__ . '/../..' . '/vendor_prefixed/paragonie/constant_time_encoding/src/Base32Hex.php',
        'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\Base64' => __DIR__ . '/../..' . '/vendor_prefixed/paragonie/constant_time_encoding/src/Base64.php',
        'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\Base64DotSlash' => __DIR__ . '/../..' . '/vendor_prefixed/paragonie/constant_time_encoding/src/Base64DotSlash.php',
        'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\Base64DotSlashOrdered' => __DIR__ . '/../..' . '/vendor_prefixed/paragonie/constant_time_encoding/src/Base64DotSlashOrdered.php',
        'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\Base64UrlSafe' => __DIR__ . '/../..' . '/vendor_prefixed/paragonie/constant_time_encoding/src/Base64UrlSafe.php',
        'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\Binary' => __DIR__ . '/../..' . '/vendor_prefixed/paragonie/constant_time_encoding/src/Binary.php',
        'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\EncoderInterface' => __DIR__ . '/../..' . '/vendor_prefixed/paragonie/constant_time_encoding/src/EncoderInterface.php',
        'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\Encoding' => __DIR__ . '/../..' . '/vendor_prefixed/paragonie/constant_time_encoding/src/Encoding.php',
        'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\Hex' => __DIR__ . '/../..' . '/vendor_prefixed/paragonie/constant_time_encoding/src/Hex.php',
        'WPMailSMTP\\Vendor\\ParagonIE\\ConstantTime\\RFC4648' => __DIR__ . '/../..' . '/vendor_prefixed/paragonie/constant_time_encoding/src/RFC4648.php',
        'WPMailSMTP\\Vendor\\Psr\\Cache\\CacheException' => __DIR__ . '/../..' . '/vendor_prefixed/psr/cache/src/CacheException.php',
        'WPMailSMTP\\Vendor\\Psr\\Cache\\CacheItemInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/cache/src/CacheItemInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Cache\\CacheItemPoolInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/cache/src/CacheItemPoolInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Cache\\InvalidArgumentException' => __DIR__ . '/../..' . '/vendor_prefixed/psr/cache/src/InvalidArgumentException.php',
        'WPMailSMTP\\Vendor\\Psr\\Http\\Client\\ClientExceptionInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/http-client/src/ClientExceptionInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Http\\Client\\ClientInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/http-client/src/ClientInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Http\\Client\\NetworkExceptionInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/http-client/src/NetworkExceptionInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Http\\Client\\RequestExceptionInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/http-client/src/RequestExceptionInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\MessageInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/http-message/src/MessageInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\RequestFactoryInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/http-factory/src/RequestFactoryInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\RequestInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/http-message/src/RequestInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\ResponseFactoryInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/http-factory/src/ResponseFactoryInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\ResponseInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/http-message/src/ResponseInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\ServerRequestFactoryInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/http-factory/src/ServerRequestFactoryInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\ServerRequestInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/http-message/src/ServerRequestInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\StreamFactoryInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/http-factory/src/StreamFactoryInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\StreamInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/http-message/src/StreamInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\UploadedFileFactoryInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/http-factory/src/UploadedFileFactoryInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\UploadedFileInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/http-message/src/UploadedFileInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\UriFactoryInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/http-factory/src/UriFactoryInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Http\\Message\\UriInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/http-message/src/UriInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Log\\AbstractLogger' => __DIR__ . '/../..' . '/vendor_prefixed/psr/log/Psr/Log/AbstractLogger.php',
        'WPMailSMTP\\Vendor\\Psr\\Log\\InvalidArgumentException' => __DIR__ . '/../..' . '/vendor_prefixed/psr/log/Psr/Log/InvalidArgumentException.php',
        'WPMailSMTP\\Vendor\\Psr\\Log\\LogLevel' => __DIR__ . '/../..' . '/vendor_prefixed/psr/log/Psr/Log/LogLevel.php',
        'WPMailSMTP\\Vendor\\Psr\\Log\\LoggerAwareInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/log/Psr/Log/LoggerAwareInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Log\\LoggerAwareTrait' => __DIR__ . '/../..' . '/vendor_prefixed/psr/log/Psr/Log/LoggerAwareTrait.php',
        'WPMailSMTP\\Vendor\\Psr\\Log\\LoggerInterface' => __DIR__ . '/../..' . '/vendor_prefixed/psr/log/Psr/Log/LoggerInterface.php',
        'WPMailSMTP\\Vendor\\Psr\\Log\\LoggerTrait' => __DIR__ . '/../..' . '/vendor_prefixed/psr/log/Psr/Log/LoggerTrait.php',
        'WPMailSMTP\\Vendor\\Psr\\Log\\NullLogger' => __DIR__ . '/../..' . '/vendor_prefixed/psr/log/Psr/Log/NullLogger.php',
        'WPMailSMTP\\Vendor\\Psr\\Log\\Test\\DummyTest' => __DIR__ . '/../..' . '/vendor_prefixed/psr/log/Psr/Log/Test/DummyTest.php',
        'WPMailSMTP\\Vendor\\Psr\\Log\\Test\\LoggerInterfaceTest' => __DIR__ . '/../..' . '/vendor_prefixed/psr/log/Psr/Log/Test/LoggerInterfaceTest.php',
        'WPMailSMTP\\Vendor\\Psr\\Log\\Test\\TestLogger' => __DIR__ . '/../..' . '/vendor_prefixed/psr/log/Psr/Log/Test/TestLogger.php',
        'WPMailSMTP\\Vendor\\Symfony\\Polyfill\\Intl\\Idn\\Idn' => __DIR__ . '/../..' . '/vendor_prefixed/symfony/polyfill-intl-idn/Idn.php',
        'WPMailSMTP\\Vendor\\Symfony\\Polyfill\\Intl\\Idn\\Info' => __DIR__ . '/../..' . '/vendor_prefixed/symfony/polyfill-intl-idn/Info.php',
        'WPMailSMTP\\Vendor\\Symfony\\Polyfill\\Intl\\Idn\\Resources\\unidata\\DisallowedRanges' => __DIR__ . '/../..' . '/vendor_prefixed/symfony/polyfill-intl-idn/Resources/unidata/DisallowedRanges.php',
        'WPMailSMTP\\Vendor\\Symfony\\Polyfill\\Intl\\Idn\\Resources\\unidata\\Regex' => __DIR__ . '/../..' . '/vendor_prefixed/symfony/polyfill-intl-idn/Resources/unidata/Regex.php',
        'WPMailSMTP\\Vendor\\Symfony\\Polyfill\\Mbstring\\Mbstring' => __DIR__ . '/../..' . '/vendor_prefixed/symfony/polyfill-mbstring/Mbstring.php',
        'WPMailSMTP\\WP' => __DIR__ . '/../..' . '/src/WP.php',
        'WPMailSMTP\\WPMailArgs' => __DIR__ . '/../..' . '/src/WPMailArgs.php',
        'WPMailSMTP\\WPMailInitiator' => __DIR__ . '/../..' . '/src/WPMailInitiator.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit64c5864e234999e61d47e3bb28138d69::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit64c5864e234999e61d47e3bb28138d69::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit64c5864e234999e61d47e3bb28138d69::$classMap;

        }, null, ClassLoader::class);
    }
}
