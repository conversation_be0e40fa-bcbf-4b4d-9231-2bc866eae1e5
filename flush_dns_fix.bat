@echo off
echo ===== FLUSHING DNS CACHE AND FIXING RAEZG.LOCAL =====
echo.

echo Checking administrator privileges...
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click on the batch file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)
echo Administrator privileges confirmed.
echo.

echo 1. Flushing DNS cache...
ipconfig /flushdns
echo DNS cache flushed.

echo.
echo 2. Flushing ARP cache...
arp -d *
echo ARP cache flushed.

echo.
echo 3. Resetting Winsock...
netsh winsock reset
echo Winsock reset.

echo.
echo 4. Resetting TCP/IP stack...
netsh int ip reset
echo TCP/IP stack reset.

echo.
echo 5. Checking hosts file again...
type C:\Windows\System32\drivers\etc\hosts | findstr raezg
echo.

echo 6. Testing direct IP connection...
echo Testing 127.0.0.1...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://127.0.0.1/raezg/' -TimeoutSec 10; Write-Host '127.0.0.1/raezg/: HTTP' $response.StatusCode } catch { Write-Host '127.0.0.1/raezg/: Failed -' $_.Exception.Message }"

echo.
echo 7. Adding explicit hosts entries (in case of formatting issues)...
echo. >> C:\Windows\System32\drivers\etc\hosts
echo # Explicit raezg.local entries >> C:\Windows\System32\drivers\etc\hosts
echo 127.0.0.1	raezg.local >> C:\Windows\System32\drivers\etc\hosts
echo 127.0.0.1	www.raezg.local >> C:\Windows\System32\drivers\etc\hosts

echo.
echo 8. Flushing DNS cache again...
ipconfig /flushdns

echo.
echo 9. Testing raezg.local again...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://raezg.local' -TimeoutSec 10; Write-Host 'raezg.local: HTTP' $response.StatusCode } catch { Write-Host 'raezg.local: Failed -' $_.Exception.Message }"

echo.
echo 10. Testing with curl (if available)...
where curl >nul 2>&1
if %errorlevel% equ 0 (
    echo Testing with curl...
    curl -I http://raezg.local --connect-timeout 10
) else (
    echo curl not available, skipping curl test.
)

echo.
echo ===== DNS FLUSH AND FIX COMPLETE =====
echo.
echo IMPORTANT: You may need to restart your computer for all changes to take effect.
echo.
echo Try these steps:
echo 1. Close ALL browser windows
echo 2. Wait 30 seconds
echo 3. Open a new browser window
echo 4. Try http://raezg.local/
echo.
echo If it still doesn't work:
echo 1. Restart your computer
echo 2. Try http://127.0.0.1/raezg/ as an alternative
echo 3. Check if antivirus/firewall is blocking the connection
echo.
pause
