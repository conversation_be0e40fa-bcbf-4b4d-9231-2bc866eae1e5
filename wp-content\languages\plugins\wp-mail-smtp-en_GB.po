# Translation of Plugins - WP Mail SMTP by WPForms &#8211; The Most Popular SMTP and Email Log Plugin - Stable (latest release) in English (UK)
# This file is distributed under the same license as the Plugins - WP Mail SMTP by WPForms &#8211; The Most Popular SMTP and Email Log Plugin - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2023-06-04 17:58:10+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: en_GB\n"
"Project-Id-Version: Plugins - WP Mail SMTP by WPForms &#8211; The Most Popular SMTP and Email Log Plugin - Stable (latest release)\n"

#. translators: %s - plugin name.
#: src/WP.php:585
msgid "WP Core (%s)"
msgstr "WP Core (%s)"

#: src/Connection.php:52
msgid "Primary"
msgstr "Primary"

#: src/Admin/Pages/SmartRoutingTab.php:302
msgid "Friendly reminder, your Primary Connection will be used for all emails that do not match the conditions above."
msgstr "Friendly reminder, your Primary Connection will be used for all emails that do not match the conditions above."

#: src/Admin/Pages/SmartRoutingTab.php:269
msgid "Initiator"
msgstr "Initiator"

#: src/Admin/Pages/SmartRoutingTab.php:244
msgid "Contact Emails (SMTP.com)"
msgstr "Contact Emails (SMTP.com)"

#: src/Admin/Pages/SmartRoutingTab.php:235
#: src/Admin/Pages/SmartRoutingTab.php:294
msgid "Add New Group"
msgstr "Add New Group"

#: src/Admin/Pages/SmartRoutingTab.php:202
#: src/Admin/Pages/SmartRoutingTab.php:232
#: src/Admin/Pages/SmartRoutingTab.php:291
msgid "or"
msgstr "or"

#: src/Admin/Pages/SmartRoutingTab.php:185
#: src/Admin/Pages/SmartRoutingTab.php:215
#: src/Admin/Pages/SmartRoutingTab.php:274
msgid "Is"
msgstr "Is"

#: src/Admin/Pages/SmartRoutingTab.php:170
#: src/Admin/Pages/SmartRoutingTab.php:193
#: src/Admin/Pages/SmartRoutingTab.php:223
#: src/Admin/Pages/SmartRoutingTab.php:282
msgid "And"
msgstr "And"

#: src/Admin/Pages/SmartRoutingTab.php:166
msgid "Order"
msgstr "Order"

#: src/Admin/Pages/SmartRoutingTab.php:162
msgid "Contains"
msgstr "Contains"

#: src/Admin/Pages/SmartRoutingTab.php:141
#: src/Admin/Pages/SmartRoutingTab.php:253
msgid "Arrow Down"
msgstr "Arrow Down"

#: src/Admin/Pages/SmartRoutingTab.php:138
#: src/Admin/Pages/SmartRoutingTab.php:250
msgid "Arrow Up"
msgstr "Arrow Up"

#: src/Admin/Pages/SmartRoutingTab.php:134
#: src/Admin/Pages/SmartRoutingTab.php:246
msgid "if the following conditions are met..."
msgstr "if the following conditions are met..."

#: src/Admin/Pages/SmartRoutingTab.php:132
msgid "WooCommerce Emails (SendLayer)"
msgstr "WooCommerce Emails (SendLayer)"

#: src/Admin/Pages/SmartRoutingTab.php:130
#: src/Admin/Pages/SmartRoutingTab.php:242
msgid "Send with"
msgstr "Send with"

#: src/Admin/Pages/SmartRoutingTab.php:119
msgid "Enable Smart Routing"
msgstr "Enable Smart Routing"

#: src/Admin/Pages/SmartRoutingTab.php:301
msgid "Light bulb icon"
msgstr "Light bulb icon"

#. translators: %s - Smart routing settings page url.
#: src/Admin/Pages/SettingsTab.php:378
msgid "Once you add an <a href=\"%s\">additional connection</a>, you can select it here."
msgstr "Once you add an <a href=\"%s\">additional connection</a>, you can select it here."

#. translators: %s - WPMailSMTP.com Upgrade page URL.
#: src/Admin/Pages/SettingsTab.php:346
msgid "Don’t worry about losing emails. Add an additional connection, then set it as your Backup Connection. Emails that fail to send with the Primary Connection will be sent via the selected Backup Connection. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Upgrade to WP Mail SMTP Pro!</a>"
msgstr "Don’t worry about losing emails. Add an additional connection, then set it as your Backup Connection. Emails that fail to send with the Primary Connection will be sent via the selected Backup Connection. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Upgrade to WP Mail SMTP Pro!</a>"

#: src/Admin/Pages/SettingsTab.php:85
msgid "Primary Connection"
msgstr "Primary Connection"

#: src/Admin/Pages/AdditionalConnectionsTab.php:186
msgid "Create advanced routing rules"
msgstr "Create advanced routing rules"

#: src/Admin/Pages/AdditionalConnectionsTab.php:183
msgid "Use mailers for different purposes"
msgstr "Use mailers for different purposes"

#: src/Admin/Pages/AdditionalConnectionsTab.php:180
msgid "Set a Backup Connection"
msgstr "Set a Backup Connection"

#: src/Admin/Pages/AdditionalConnectionsTab.php:177
msgid "With additional connections you can..."
msgstr "With additional connections you can..."

#: src/Admin/Pages/AdditionalConnectionsTab.php:148
#: src/Admin/Pages/SmartRoutingTab.php:51
#: src/Admin/Pages/SmartRoutingTab.php:102
msgid "Smart Routing"
msgstr "Smart Routing"

#: src/Admin/Pages/AdditionalConnectionsTab.php:143
#: src/Admin/Pages/SettingsTab.php:340 src/Admin/Pages/SettingsTab.php:364
msgid "Backup Connection"
msgstr "Backup Connection"

#: src/Admin/Pages/AdditionalConnectionsTab.php:49
#: src/Admin/Pages/AdditionalConnectionsTab.php:106
msgid "Additional Connections"
msgstr "Additional Connections"

#: assets/languages/wp-mail-smtp-vue.php:826 src/Admin/Area.php:146
msgid "There was an error while processing the authentication request. The state key is invalid. Please try again."
msgstr "There was an error while processing the authentication request. The state key is invalid. Please try again."

#: assets/languages/wp-mail-smtp-vue.php:812
#: src/Providers/Gmail/Options.php:217
msgid "Remove OAuth Connection"
msgstr "Remove OAuth Connection"

#: assets/languages/wp-mail-smtp-vue.php:104
msgid "It looks like we can't remove OAuth connection."
msgstr "It looks like we can't remove OAuth connection."

#: src/SiteHealth.php:296
msgid "create the missing DB tables by clicking on this link"
msgstr "create the missing DB tables by clicking on this link"

#: src/SiteHealth.php:295
msgid "Go to WP Mail SMTP settings page."
msgstr "Go to WP Mail SMTP settings page."

#: src/DBRepair.php:215
msgid "Some DB Tables are still missing."
msgstr "Some DB tables are still missing."

#: src/DBRepair.php:189
msgid "Missing DB tables were created successfully."
msgstr "Missing DB tables were created successfully."

#: src/DBRepair.php:101
msgid "Unknown."
msgstr "Unknown."

#. translators: %d - days count.
#: src/Admin/Pages/DebugEventsTab.php:585
msgid "%d Day"
msgid_plural "%d Days"
msgstr[0] "%d Day"
msgstr[1] "%d Days"

#: src/Admin/Pages/DebugEventsTab.php:571
msgid "1 Year"
msgstr "1 Year"

#: src/Admin/Pages/DebugEventsTab.php:570
msgid "6 Months"
msgstr "6 Months"

#: src/Admin/Pages/DebugEventsTab.php:569
msgid "3 Months"
msgstr "3 Months"

#: src/Admin/Pages/DebugEventsTab.php:568
msgid "1 Month"
msgstr "1 Month"

#: src/Admin/Pages/DebugEventsTab.php:567
msgid "1 Week"
msgstr "1 Week"

#: src/Admin/Pages/DebugEventsTab.php:271
msgid "Debug events older than the selected period will be permanently deleted from the database."
msgstr "Debug events older than the selected period will be permanently deleted from the database."

#: src/Admin/Pages/DebugEventsTab.php:262
msgid "Forever"
msgstr "Forever"

#: src/Admin/Pages/DebugEventsTab.php:256
msgid "Events Retention Period"
msgstr "Events Retention Period"

#. translators: %1$s - Settings Page URL; %2$s - The aria label; %3$s - The
#. text that will appear on the link.
#: src/SiteHealth.php:293
msgid "WP Mail SMTP is using custom database tables for some of its features. In order to work properly, the custom tables should be created, and it seems they are missing. Please try to <a href=\"%1$s\" target=\"_self\" aria-label=\"%2$s\" rel=\"noopener noreferrer\">%3$s</a>. If this issue persists, please contact our support."
msgstr "WP Mail SMTP is using custom database tables for some of its features. In order to work properly, the custom tables should be created, and it seems they are missing. Please try to <a href=\"%1$s\" target=\"_self\" aria-label=\"%2$s\" rel=\"noopener noreferrer\">%3$s</a>. If this issue persists, please contact our support."

#. translators: %1$d - index number; %2$s - function name; %3$s - file path;
#. %4$s - line number.
#: src/Admin/DebugEvents/Event.php:395
msgid "[%1$d] %2$s called at [%3$s:%4$s]"
msgstr "[%1$d] %2$s called at [%3$s:%4$s]"

#: src/Admin/DebugEvents/Event.php:389
msgid "Backtrace:"
msgstr "Backtrace:"

#: assets/languages/wp-mail-smtp-vue.php:443
msgid "Track when a link in an email is clicked"
msgstr "Track when a link in an email is clicked"

#: assets/languages/wp-mail-smtp-vue.php:194
msgid "Complete Email Reports"
msgstr "Complete Email Reports"

#: assets/languages/wp-mail-smtp-vue.php:197
msgid "See the delivery status, track opens and clicks, and create deliverability graphs."
msgstr "See the delivery status, track opens and clicks, and create deliverability graphs."

#: assets/languages/wp-mail-smtp-vue.php:191
msgid "Get notifications via email, SMS, Slack, or webhook when emails fail to send."
msgstr "Get notifications via email, SMS, Slack, or webhook when emails fail to send."

#: assets/languages/wp-mail-smtp-vue.php:188
msgid "Instant Email Alerts"
msgstr "Instant Email Alerts"

#: assets/languages/wp-mail-smtp-vue.php:428
msgid "This option must be enabled if you'd like to be able to resend emails. Please be aware that all email content will be stored in your WordPress database. This may include sensitive data, passwords, and personal details."
msgstr "This option must be enabled if you'd like to be able to resend emails. Please be aware that all email content will be stored in your WordPress database. This may include sensitive data, passwords, and personal details."

#: assets/languages/wp-mail-smtp-vue.php:422
msgid "Enable these powerful logging features for more control of your WordPress emails."
msgstr "Enable these powerful logging features for more control of your WordPress emails."

#: assets/languages/wp-mail-smtp-vue.php:446
msgid "See which links were clicked in emails sent from your WordPress site. Click tracking works with emails that are sent in HTML format."
msgstr "See which links were clicked in emails sent from your WordPress site. Click tracking works with emails that are sent in HTML format."

#: assets/languages/wp-mail-smtp-vue.php:440
msgid "See which emails were opened by the recipients. Email open tracking works with emails that are sent in HTML format."
msgstr "See which emails were opened by the recipients. Email open tracking works with emails that are sent in HTML format."

#: assets/languages/wp-mail-smtp-vue.php:437
msgid "Track when an email is opened"
msgstr "Track when an email is opened"

#: assets/languages/wp-mail-smtp-vue.php:434
msgid "All file attachments sent from your site will be saved to the WordPress Uploads folder. Please note that this may reduce available disk space on your server."
msgstr "All file attachments sent from your site will be saved to the WordPress uploads folder. Please note that this may reduce available disk space on your server."

#: assets/languages/wp-mail-smtp-vue.php:431
msgid "Save file attachments sent from WordPress"
msgstr "Save file attachments sent from WordPress"

#: assets/languages/wp-mail-smtp-vue.php:425
msgid "Store the content for all sent emails"
msgstr "Store the content for all sent emails"

#: assets/languages/wp-mail-smtp-vue.php:419
msgid "Configure Email Logs"
msgstr "Configure Email Logs"

#. Translators: Link to the Mailgun API settings.
#: assets/languages/wp-mail-smtp-vue.php:228
msgid "%1$sFollow this link%2$s to get a Private API Key from Mailgun."
msgstr "%1$sFollow this link%2$s to get a Private API Key from Mailgun."

#: src/Reports/Emails/Summary.php:257
msgid "Let’s see how many emails you’ve sent with WP Mail SMTP."
msgstr "Let’s see how many emails you’ve sent with WP Mail SMTP."

#. translators: %s - path.
#: src/Core.php:1404
msgid "Current function path: %s"
msgstr "Current function path: %s"

#: src/Core.php:1399
msgid "It looks like it's overwritten in the \"wp-config.php\" file. Please reach out to your hosting provider on how to disable or remove the \"wp_mail\" function overwrite to prevent conflicts with WP Mail SMTP."
msgstr "It looks like it's overwritten in the \"wp-config.php\" file. Please reach out to your hosting provider on how to disable or remove the \"wp_mail\" function overwrite to prevent conflicts with WP Mail SMTP."

#. translators: %s - must-use plugin name.
#: src/Core.php:1395
msgid "It looks like the \"%s\" must-use plugin is overwriting the \"wp_mail\" function. Please reach out to your hosting provider on how to disable or remove the \"wp_mail\" function overwrite to prevent conflicts with WP Mail SMTP."
msgstr "It looks like the \"%s\" must-use plugin is overwriting the \"wp_mail\" function. Please reach out to your hosting provider on how to disable or remove the \"wp_mail\" function overwrite to prevent conflicts with WP Mail SMTP."

#. translators: %s - plugin name.
#: src/Core.php:1389
msgid "It looks like the \"%s\" plugin is overwriting the \"wp_mail\" function. Please reach out to the plugin developer on how to disable or remove the \"wp_mail\" function overwrite to prevent conflicts with WP Mail SMTP."
msgstr "It looks like the \"%s\" plugin is overwriting the \"wp_mail\" function. Please reach out to the plugin developer on how to disable or remove the \"wp_mail\" function overwrite to prevent conflicts with WP Mail SMTP."

#: src/Core.php:1384
msgid "WP Mail SMTP has detected incorrect \"wp_mail\" function location. Usually, this means that emails will not be sent successfully!"
msgstr "WP Mail SMTP has detected incorrect \"wp_mail\" function location. Usually, this means that emails will not be sent successfully!"

#: src/Conflicts.php:355
msgid "Or deactivate \"SMTP\" module in Branda > Emails > SMTP."
msgstr "Or deactivate \"SMTP\" module in Branda > Emails > SMTP."

#: src/Admin/Pages/AlertsTab.php:304
msgid "Webhook Alerts"
msgstr "Webhook Alerts"

#: src/Admin/Pages/AlertsTab.php:299
msgid "Paste in the webhook URL you’d like to use to receive alerts when email sending fails. Read our documentation on setting up webhook alerts."
msgstr "Paste in the webhook URL you’d like to use to receive alerts when email sending fails. Read our documentation on setting up webhook alerts."

#: src/Admin/Pages/AlertsTab.php:298
msgid "Webhook"
msgstr "Webhook"

#: src/Admin/Pages/AlertsTab.php:287
msgid "To Phone Number"
msgstr "To Phone Number"

#: src/Admin/Pages/AlertsTab.php:281
msgid "From Phone Number"
msgstr "From Phone Number"

#: src/Admin/Pages/AlertsTab.php:275
msgid "Twilio Auth Token"
msgstr "Twilio Auth Token"

#: src/Admin/Pages/AlertsTab.php:269
msgid "Twilio Account ID"
msgstr "Twilio Account ID"

#: src/Admin/Pages/AlertsTab.php:257
msgid "SMS via Twilio Alerts"
msgstr "SMS via Twilio Alerts"

#: src/Admin/Pages/AlertsTab.php:252
msgid "To receive SMS alerts, you’ll need a Twilio account. Read our documentation to learn how to set up Twilio SMS, then enter your connection details below."
msgstr "To receive SMS alerts, you’ll need a Twilio account. Read our documentation to learn how to set up Twilio SMS, then enter your connection details below."

#: src/Admin/Pages/AlertsTab.php:251
msgid "SMS via Twilio"
msgstr "SMS via Twilio"

#: src/Admin/Pages/AlertsTab.php:182 src/Admin/Pages/AlertsTab.php:211
#: src/Admin/Pages/AlertsTab.php:240 src/Admin/Pages/AlertsTab.php:316
msgid "Webhook URL"
msgstr "Webhook URL"

#: src/Admin/Pages/AlertsTab.php:170
msgid "Slack Alerts"
msgstr "Slack Alerts"

#: src/Admin/Pages/AlertsTab.php:165
msgid "Paste in the Slack webhook URL you’d like to use to receive alerts when email sending fails. Read our documentation on setting up Slack alerts."
msgstr "Paste in the Slack webhook URL you’d like to use to receive alerts when email sending fails. Read our documentation on setting up Slack alerts."

#: src/Admin/Pages/AlertsTab.php:164
msgid "Slack"
msgstr "Slack"

#: src/Admin/Pages/AlertsTab.php:141
msgid "Email Alerts"
msgstr "Email Alerts"

#: src/Admin/Pages/AlertsTab.php:136
msgid "Enter the email addresses (3 max) you’d like to use to receive alerts when email sending fails. Read our documentation on setting up email alerts."
msgstr "Enter the email addresses (3 max) you’d like to use to receive alerts when email sending fails. Read our documentation on setting up email alerts."

#: src/Admin/Pages/AlertsTab.php:43 src/Admin/Pages/AlertsTab.php:81
msgid "Alerts"
msgstr "Alerts"

#: assets/languages/wp-mail-smtp-vue.php:784
msgid "There was an error while processing the authentication request. The authorization code is missing. Please try again."
msgstr "There was an error while processing the authentication request. The authorisation code is missing. Please try again."

#: assets/languages/wp-mail-smtp-vue.php:781 src/Admin/Area.php:153
msgid "There was an error while processing the authentication request. The nonce is invalid. Please try again."
msgstr "There was an error while processing the authentication request. The nonce is invalid. Please try again."

#. translators: %s - API key link.
#: src/Providers/Sendlayer/Options.php:129
msgid "Follow this link to get an API Key from SendLayer: %s."
msgstr "Follow this link to get an API Key from SendLayer: %s."

#: src/Providers/Sendlayer/Options.php:73
msgid "SendLayer"
msgstr "SendLayer"

#. translators: %1$s - URL to sendlayer.com; %2$s - URL to SendLayer
#. documentation on wpmailsmtp.com.
#: src/Providers/Sendlayer/Options.php:41
msgid "<strong><a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">SendLayer</a> is our #1 recommended mailer.</strong> Its affordable pricing and simple setup make it the perfect choice for WordPress sites. SendLayer will authenticate your outgoing emails to make sure they always hit customers’ inboxes, and it has detailed documentation to help you authorize your domain.<br><br>You can send hundreds of emails for free when you sign up for a trial.<br><br>To get started, read our <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">SendLayer documentation</a>."
msgstr "<strong><a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">SendLayer</a> is our #1 recommended mailer.</strong> Its affordable pricing and simple setup make it the perfect choice for WordPress sites. SendLayer will authenticate your outgoing emails to make sure they always hit customers’ inboxes, and it has detailed documentation to help you authorise your domain.<br><br>You can send hundreds of emails for free when you sign up for a trial.<br><br>To get started, read our <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">SendLayer documentation</a>."

#: src/Admin/Pages/TestTab.php:996
msgid "Verify your domain Region is correct."
msgstr "Verify your domain Region is correct."

#. translators: %1$s - Mailgun domains area URL.
#: src/Admin/Pages/TestTab.php:985
msgid "Verify your <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">Domain Name</a> is correct."
msgstr "Verify your <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">Domain Name</a> is correct."

#: src/Admin/Pages/MiscTab.php:140
msgid "Hide Announcements"
msgstr "Hide Announcements"

#: src/Admin/Pages/AboutTab.php:279 src/Admin/Pages/SmartRoutingTab.php:278
msgid "WPForms"
msgstr "WPForms"

#: src/Admin/ConnectionSettings.php:193
msgid "Return Path indicates where non-delivery receipts - or bounce messages - are to be sent."
msgstr "Return Path indicates where non-delivery receipts - or bounce messages - are to be sent."

#: src/Admin/DashboardWidget.php:575
msgid "Save Changes"
msgstr "Save Changes"

#: src/Admin/Area.php:318 src/Admin/Area.php:319 src/Admin/Area.php:1285
msgid "Settings"
msgstr "Settings"

#: assets/languages/wp-mail-smtp-vue.php:591
#: src/Providers/OptionsAbstract.php:329
msgid "Authentication"
msgstr "Authentication"

#: assets/languages/wp-mail-smtp-vue.php:248
#: src/Admin/ConnectionSettings.php:70 src/Admin/Pages/SmartRoutingTab.php:180
#: src/Admin/Pages/SmartRoutingTab.php:210
msgid "From Email"
msgstr "From Email"

#: assets/languages/wp-mail-smtp-vue.php:240
#: src/Admin/ConnectionSettings.php:127 src/Admin/Pages/ExportTab.php:111
msgid "From Name"
msgstr "From Name"

#: assets/languages/wp-mail-smtp-vue.php:579
#: src/Providers/OptionsAbstract.php:293
msgid "SMTP Port"
msgstr "SMTP Port"

#: assets/languages/wp-mail-smtp-vue.php:524 src/Admin/Pages/TestTab.php:253
#: src/SiteHealth.php:352
msgid "Send a Test Email"
msgstr "Send a Test Email"

#: assets/languages/wp-mail-smtp-vue.php:576
#: src/Providers/OptionsAbstract.php:253
msgid "Encryption"
msgstr "Encryption"

#: assets/languages/wp-mail-smtp-vue.php:573
#: src/Providers/OptionsAbstract.php:239
msgid "SMTP Host"
msgstr "SMTP Host"

#: src/Admin/ConnectionSettings.php:177
msgid "Return Path"
msgstr "Return Path"

#: assets/languages/wp-mail-smtp-vue.php:642
#: src/Admin/ConnectionSettings.php:202 src/Admin/Pages/ExportTab.php:125
msgid "Mailer"
msgstr "Mailer"

#. Plugin Name of the plugin
#. Author of the plugin
#: wp_mail_smtp.php src/Admin/Area.php:307 src/Admin/Area.php:308
#: src/Admin/Area.php:365 src/Admin/Area.php:366
#: src/Admin/DashboardWidget.php:164 src/Admin/DashboardWidget.php:567
#: src/Providers/Outlook/Provider.php:74 src/SiteHealth.php:43
msgid "WP Mail SMTP"
msgstr "WP Mail SMTP"

#: src/Admin/Pages/AlertsTab.php:135
msgid "Email"
msgstr "Email"