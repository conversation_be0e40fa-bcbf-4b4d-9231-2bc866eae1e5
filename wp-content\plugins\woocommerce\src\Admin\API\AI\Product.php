<?php

declare( strict_types = 1 );

namespace Automattic\WooCommerce\Admin\API\AI;

use Automattic\WooCommerce\Blocks\AIContent\UpdateProducts;
use WP_Error;
use WP_REST_Request;
use WP_REST_Response;

defined( 'ABSPATH' ) || exit;

/**
 * Product controller
 *
 * @internal
 */
class Product extends AIEndpoint {
	/**
	 * The endpoint response option name.
	 *
	 * @var string
	 */
	const AI_CONTENT_GENERATED = 'ai_content_generated';

	/**
	 * Endpoint.
	 *
	 * @var string
	 */
	protected $endpoint = 'product';

	/**
	 * Register routes.
	 */
	public function register_routes() {
		$this->register(
			array(
				array(
					'methods'             => \WP_REST_Server::CREATABLE,
					'callback'            => array( $this, 'update_product' ),
					'permission_callback' => array( Middleware::class, 'is_authorized' ),
					'args'                => array(
						'products_information' => array(
							'description' => __( 'Data generated by AI for updating dummy products.', 'woocommerce' ),
							'type'        => 'object',
						),
						'last_product'         => array(
							'description' => __( 'Whether the product being updated is the last one in the loop', 'woocommerce' ),
							'type'        => 'boolean',
						),
					),
				),
			)
		);
	}

	/**
	 * Update product with the content and images powered by AI.
	 *
	 * @param  WP_REST_Request $request Request object.
	 *
	 * @return WP_REST_Response
	 */
	public function update_product( WP_REST_Request $request ) {
		$product_information = $request['products_information'] ?? array();

		if ( empty( $product_information ) ) {
			return rest_ensure_response(
				array(
					self::AI_CONTENT_GENERATED => true,
				)
			);
		}

		try {
			$product_updater = new UpdateProducts();
			$product_updater->update_product_content( $product_information );
		} catch ( \Exception $e ) {
			return rest_ensure_response( array( 'ai_content_generated' => false ) );
		}

		$last_product_to_update = $request['last_product'] ?? false;

		if ( $last_product_to_update ) {
			flush_rewrite_rules();
		}

		return rest_ensure_response(
			array(
				self::AI_CONTENT_GENERATED => true,
			)
		);
	}
}
