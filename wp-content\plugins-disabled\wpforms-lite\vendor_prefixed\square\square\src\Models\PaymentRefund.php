<?php

declare (strict_types=1);
namespace WPForms\Vendor\Square\Models;

use stdClass;
/**
 * Represents a refund of a payment made using Square. Contains information about
 * the original payment and the amount of money refunded.
 */
class PaymentRefund implements \JsonSerializable
{
    /**
     * @var string
     */
    private $id;
    /**
     * @var array
     */
    private $status = [];
    /**
     * @var array
     */
    private $locationId = [];
    /**
     * @var bool|null
     */
    private $unlinked;
    /**
     * @var array
     */
    private $destinationType = [];
    /**
     * @var DestinationDetails|null
     */
    private $destinationDetails;
    /**
     * @var Money
     */
    private $amountMoney;
    /**
     * @var Money|null
     */
    private $appFeeMoney;
    /**
     * @var array
     */
    private $processingFee = [];
    /**
     * @var array
     */
    private $paymentId = [];
    /**
     * @var array
     */
    private $orderId = [];
    /**
     * @var array
     */
    private $reason = [];
    /**
     * @var string|null
     */
    private $createdAt;
    /**
     * @var string|null
     */
    private $updatedAt;
    /**
     * @var string|null
     */
    private $teamMemberId;
    /**
     * @var string|null
     */
    private $terminalRefundId;
    /**
     * @param string $id
     * @param Money $amountMoney
     */
    public function __construct(string $id, Money $amountMoney)
    {
        $this->id = $id;
        $this->amountMoney = $amountMoney;
    }
    /**
     * Returns Id.
     * The unique ID for this refund, generated by Square.
     */
    public function getId() : string
    {
        return $this->id;
    }
    /**
     * Sets Id.
     * The unique ID for this refund, generated by Square.
     *
     * @required
     * @maps id
     */
    public function setId(string $id) : void
    {
        $this->id = $id;
    }
    /**
     * Returns Status.
     * The refund's status:
     * - `PENDING` - Awaiting approval.
     * - `COMPLETED` - Successfully completed.
     * - `REJECTED` - The refund was rejected.
     * - `FAILED` - An error occurred.
     */
    public function getStatus() : ?string
    {
        if (\count($this->status) == 0) {
            return null;
        }
        return $this->status['value'];
    }
    /**
     * Sets Status.
     * The refund's status:
     * - `PENDING` - Awaiting approval.
     * - `COMPLETED` - Successfully completed.
     * - `REJECTED` - The refund was rejected.
     * - `FAILED` - An error occurred.
     *
     * @maps status
     */
    public function setStatus(?string $status) : void
    {
        $this->status['value'] = $status;
    }
    /**
     * Unsets Status.
     * The refund's status:
     * - `PENDING` - Awaiting approval.
     * - `COMPLETED` - Successfully completed.
     * - `REJECTED` - The refund was rejected.
     * - `FAILED` - An error occurred.
     */
    public function unsetStatus() : void
    {
        $this->status = [];
    }
    /**
     * Returns Location Id.
     * The location ID associated with the payment this refund is attached to.
     */
    public function getLocationId() : ?string
    {
        if (\count($this->locationId) == 0) {
            return null;
        }
        return $this->locationId['value'];
    }
    /**
     * Sets Location Id.
     * The location ID associated with the payment this refund is attached to.
     *
     * @maps location_id
     */
    public function setLocationId(?string $locationId) : void
    {
        $this->locationId['value'] = $locationId;
    }
    /**
     * Unsets Location Id.
     * The location ID associated with the payment this refund is attached to.
     */
    public function unsetLocationId() : void
    {
        $this->locationId = [];
    }
    /**
     * Returns Unlinked.
     * Flag indicating whether or not the refund is linked to an existing payment in Square.
     */
    public function getUnlinked() : ?bool
    {
        return $this->unlinked;
    }
    /**
     * Sets Unlinked.
     * Flag indicating whether or not the refund is linked to an existing payment in Square.
     *
     * @maps unlinked
     */
    public function setUnlinked(?bool $unlinked) : void
    {
        $this->unlinked = $unlinked;
    }
    /**
     * Returns Destination Type.
     * The destination type for this refund.
     *
     * Current values include `CARD`, `BANK_ACCOUNT`, `WALLET`, `BUY_NOW_PAY_LATER`, `CASH`,
     * `EXTERNAL`, and `SQUARE_ACCOUNT`.
     */
    public function getDestinationType() : ?string
    {
        if (\count($this->destinationType) == 0) {
            return null;
        }
        return $this->destinationType['value'];
    }
    /**
     * Sets Destination Type.
     * The destination type for this refund.
     *
     * Current values include `CARD`, `BANK_ACCOUNT`, `WALLET`, `BUY_NOW_PAY_LATER`, `CASH`,
     * `EXTERNAL`, and `SQUARE_ACCOUNT`.
     *
     * @maps destination_type
     */
    public function setDestinationType(?string $destinationType) : void
    {
        $this->destinationType['value'] = $destinationType;
    }
    /**
     * Unsets Destination Type.
     * The destination type for this refund.
     *
     * Current values include `CARD`, `BANK_ACCOUNT`, `WALLET`, `BUY_NOW_PAY_LATER`, `CASH`,
     * `EXTERNAL`, and `SQUARE_ACCOUNT`.
     */
    public function unsetDestinationType() : void
    {
        $this->destinationType = [];
    }
    /**
     * Returns Destination Details.
     * Details about a refund's destination.
     */
    public function getDestinationDetails() : ?DestinationDetails
    {
        return $this->destinationDetails;
    }
    /**
     * Sets Destination Details.
     * Details about a refund's destination.
     *
     * @maps destination_details
     */
    public function setDestinationDetails(?DestinationDetails $destinationDetails) : void
    {
        $this->destinationDetails = $destinationDetails;
    }
    /**
     * Returns Amount Money.
     * Represents an amount of money. `Money` fields can be signed or unsigned.
     * Fields that do not explicitly define whether they are signed or unsigned are
     * considered unsigned and can only hold positive amounts. For signed fields, the
     * sign of the value indicates the purpose of the money transfer. See
     * [Working with Monetary Amounts](https://developer.squareup.com/docs/build-basics/working-with-
     * monetary-amounts)
     * for more information.
     */
    public function getAmountMoney() : Money
    {
        return $this->amountMoney;
    }
    /**
     * Sets Amount Money.
     * Represents an amount of money. `Money` fields can be signed or unsigned.
     * Fields that do not explicitly define whether they are signed or unsigned are
     * considered unsigned and can only hold positive amounts. For signed fields, the
     * sign of the value indicates the purpose of the money transfer. See
     * [Working with Monetary Amounts](https://developer.squareup.com/docs/build-basics/working-with-
     * monetary-amounts)
     * for more information.
     *
     * @required
     * @maps amount_money
     */
    public function setAmountMoney(Money $amountMoney) : void
    {
        $this->amountMoney = $amountMoney;
    }
    /**
     * Returns App Fee Money.
     * Represents an amount of money. `Money` fields can be signed or unsigned.
     * Fields that do not explicitly define whether they are signed or unsigned are
     * considered unsigned and can only hold positive amounts. For signed fields, the
     * sign of the value indicates the purpose of the money transfer. See
     * [Working with Monetary Amounts](https://developer.squareup.com/docs/build-basics/working-with-
     * monetary-amounts)
     * for more information.
     */
    public function getAppFeeMoney() : ?Money
    {
        return $this->appFeeMoney;
    }
    /**
     * Sets App Fee Money.
     * Represents an amount of money. `Money` fields can be signed or unsigned.
     * Fields that do not explicitly define whether they are signed or unsigned are
     * considered unsigned and can only hold positive amounts. For signed fields, the
     * sign of the value indicates the purpose of the money transfer. See
     * [Working with Monetary Amounts](https://developer.squareup.com/docs/build-basics/working-with-
     * monetary-amounts)
     * for more information.
     *
     * @maps app_fee_money
     */
    public function setAppFeeMoney(?Money $appFeeMoney) : void
    {
        $this->appFeeMoney = $appFeeMoney;
    }
    /**
     * Returns Processing Fee.
     * Processing fees and fee adjustments assessed by Square for this refund.
     *
     * @return ProcessingFee[]|null
     */
    public function getProcessingFee() : ?array
    {
        if (\count($this->processingFee) == 0) {
            return null;
        }
        return $this->processingFee['value'];
    }
    /**
     * Sets Processing Fee.
     * Processing fees and fee adjustments assessed by Square for this refund.
     *
     * @maps processing_fee
     *
     * @param ProcessingFee[]|null $processingFee
     */
    public function setProcessingFee(?array $processingFee) : void
    {
        $this->processingFee['value'] = $processingFee;
    }
    /**
     * Unsets Processing Fee.
     * Processing fees and fee adjustments assessed by Square for this refund.
     */
    public function unsetProcessingFee() : void
    {
        $this->processingFee = [];
    }
    /**
     * Returns Payment Id.
     * The ID of the payment associated with this refund.
     */
    public function getPaymentId() : ?string
    {
        if (\count($this->paymentId) == 0) {
            return null;
        }
        return $this->paymentId['value'];
    }
    /**
     * Sets Payment Id.
     * The ID of the payment associated with this refund.
     *
     * @maps payment_id
     */
    public function setPaymentId(?string $paymentId) : void
    {
        $this->paymentId['value'] = $paymentId;
    }
    /**
     * Unsets Payment Id.
     * The ID of the payment associated with this refund.
     */
    public function unsetPaymentId() : void
    {
        $this->paymentId = [];
    }
    /**
     * Returns Order Id.
     * The ID of the order associated with the refund.
     */
    public function getOrderId() : ?string
    {
        if (\count($this->orderId) == 0) {
            return null;
        }
        return $this->orderId['value'];
    }
    /**
     * Sets Order Id.
     * The ID of the order associated with the refund.
     *
     * @maps order_id
     */
    public function setOrderId(?string $orderId) : void
    {
        $this->orderId['value'] = $orderId;
    }
    /**
     * Unsets Order Id.
     * The ID of the order associated with the refund.
     */
    public function unsetOrderId() : void
    {
        $this->orderId = [];
    }
    /**
     * Returns Reason.
     * The reason for the refund.
     */
    public function getReason() : ?string
    {
        if (\count($this->reason) == 0) {
            return null;
        }
        return $this->reason['value'];
    }
    /**
     * Sets Reason.
     * The reason for the refund.
     *
     * @maps reason
     */
    public function setReason(?string $reason) : void
    {
        $this->reason['value'] = $reason;
    }
    /**
     * Unsets Reason.
     * The reason for the refund.
     */
    public function unsetReason() : void
    {
        $this->reason = [];
    }
    /**
     * Returns Created At.
     * The timestamp of when the refund was created, in RFC 3339 format.
     */
    public function getCreatedAt() : ?string
    {
        return $this->createdAt;
    }
    /**
     * Sets Created At.
     * The timestamp of when the refund was created, in RFC 3339 format.
     *
     * @maps created_at
     */
    public function setCreatedAt(?string $createdAt) : void
    {
        $this->createdAt = $createdAt;
    }
    /**
     * Returns Updated At.
     * The timestamp of when the refund was last updated, in RFC 3339 format.
     */
    public function getUpdatedAt() : ?string
    {
        return $this->updatedAt;
    }
    /**
     * Sets Updated At.
     * The timestamp of when the refund was last updated, in RFC 3339 format.
     *
     * @maps updated_at
     */
    public function setUpdatedAt(?string $updatedAt) : void
    {
        $this->updatedAt = $updatedAt;
    }
    /**
     * Returns Team Member Id.
     * An optional ID of the team member associated with taking the payment.
     */
    public function getTeamMemberId() : ?string
    {
        return $this->teamMemberId;
    }
    /**
     * Sets Team Member Id.
     * An optional ID of the team member associated with taking the payment.
     *
     * @maps team_member_id
     */
    public function setTeamMemberId(?string $teamMemberId) : void
    {
        $this->teamMemberId = $teamMemberId;
    }
    /**
     * Returns Terminal Refund Id.
     * An optional ID for a Terminal refund.
     */
    public function getTerminalRefundId() : ?string
    {
        return $this->terminalRefundId;
    }
    /**
     * Sets Terminal Refund Id.
     * An optional ID for a Terminal refund.
     *
     * @maps terminal_refund_id
     */
    public function setTerminalRefundId(?string $terminalRefundId) : void
    {
        $this->terminalRefundId = $terminalRefundId;
    }
    /**
     * Encode this object to JSON
     *
     * @param bool $asArrayWhenEmpty Whether to serialize this model as an array whenever no fields
     *        are set. (default: false)
     *
     * @return array|stdClass
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize(bool $asArrayWhenEmpty = \false)
    {
        $json = [];
        $json['id'] = $this->id;
        if (!empty($this->status)) {
            $json['status'] = $this->status['value'];
        }
        if (!empty($this->locationId)) {
            $json['location_id'] = $this->locationId['value'];
        }
        if (isset($this->unlinked)) {
            $json['unlinked'] = $this->unlinked;
        }
        if (!empty($this->destinationType)) {
            $json['destination_type'] = $this->destinationType['value'];
        }
        if (isset($this->destinationDetails)) {
            $json['destination_details'] = $this->destinationDetails;
        }
        $json['amount_money'] = $this->amountMoney;
        if (isset($this->appFeeMoney)) {
            $json['app_fee_money'] = $this->appFeeMoney;
        }
        if (!empty($this->processingFee)) {
            $json['processing_fee'] = $this->processingFee['value'];
        }
        if (!empty($this->paymentId)) {
            $json['payment_id'] = $this->paymentId['value'];
        }
        if (!empty($this->orderId)) {
            $json['order_id'] = $this->orderId['value'];
        }
        if (!empty($this->reason)) {
            $json['reason'] = $this->reason['value'];
        }
        if (isset($this->createdAt)) {
            $json['created_at'] = $this->createdAt;
        }
        if (isset($this->updatedAt)) {
            $json['updated_at'] = $this->updatedAt;
        }
        if (isset($this->teamMemberId)) {
            $json['team_member_id'] = $this->teamMemberId;
        }
        if (isset($this->terminalRefundId)) {
            $json['terminal_refund_id'] = $this->terminalRefundId;
        }
        $json = \array_filter($json, function ($val) {
            return $val !== null;
        });
        return !$asArrayWhenEmpty && empty($json) ? new stdClass() : $json;
    }
}
