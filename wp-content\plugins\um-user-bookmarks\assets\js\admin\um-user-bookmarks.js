wp.hooks.addAction( 'um_same_page_upgrade', 'um_user_bookmarks', function( field_key ) {
	if ( field_key === 'sync_user_bookmarks_disable_folders' ) {
		var metadata_pages = 0;
		var metadata_per_page = 500;
		var current_page = 1;

		current_page = 1;

		um_add_same_page_log( field_key, wp.i18n.__( 'Getting bookmarks metadata', 'um-user-bookmarks' ) );
		jQuery.ajax({
			url: wp.ajax.settings.url,
			type: 'POST',
			dataType: 'json',
			data: {
				action: 'um_same_page_update',
				cb_func: 'um_get_bookmarks_metadata',
				nonce: um_admin_scripts.nonce
			},
			success: function( response ) {
				if ( typeof response.data.count != 'undefined' ) {
					um_add_same_page_log( field_key, wp.i18n.__( 'There are ', 'um-user-bookmarks' ) + response.data.count + wp.i18n.__( ' bookmarks metadata rows...', 'um-user-bookmarks' ) );
					um_add_same_page_log( field_key, wp.i18n.__( 'Start bookmarks metadata upgrading...', 'um-user-bookmarks' ) );

					metadata_pages = Math.ceil( response.data.count / metadata_per_page );

					update_bookmarks_metadata_per_page();
				} else {
					um_same_page_wrong_ajax( field_key );
				}
			},
			error: function() {
				um_same_page_something_wrong( field_key );
			}
		});


		function update_bookmarks_metadata_per_page() {
			if ( current_page <= metadata_pages ) {
				jQuery.ajax({
					url: wp.ajax.settings.url,
					type: 'POST',
					dataType: 'json',
					data: {
						action: 'um_same_page_update',
						cb_func: 'um_update_bookmarks_metadata_single',
						page: current_page,
						nonce: um_admin_scripts.nonce
					},
					success: function( response ) {
						if ( typeof response.data != 'undefined' ) {
							um_add_same_page_log( field_key, response.data.message );
							current_page++;
							update_bookmarks_metadata_per_page();
						} else {
							um_same_page_wrong_ajax( field_key );
						}
					},
					error: function() {
						um_same_page_something_wrong( field_key );
					}
				});
			} else {
				jQuery( '#submit' ).trigger( 'click' );
			}
		}
	}
}, 10 );
