<?php
/**
 * Template for the UM User Photos, the "Edit Album" modal content
 *
 * Page: "Profile", tab "Photos"
 * Caller: User_Photos_Ajax->load_edit_album_modal() method
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/modal/edit-album.php
 * @var int        $count_user_photos
 * @var int        $limit_user_photos
 * @var int|string $limit_per_albums
 * @var int|string $count_per_album
 * @var object     $album
 * @var bool       $privacy
 * @var bool       $disable_title
 * @var array      $wrapper_classes
 * @var bool       $enable_upload
 * @var array      $uploaded_photos
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
?>

<div class="<?php echo esc_attr( implode( ' ', $wrapper_classes ) ); ?>">
	<h4 class="um-user-photos-form-title">
		<?php
		if ( ! $disable_title ) {
			// translators: %s - album title.
			echo esc_html( sprintf( __( 'Edit album: "%s"', 'um-user-photos' ), $album->post_title ) );
		} else {
			esc_html_e( 'Edit album', 'um-user-photos' );
		}
		?>
	</h4>
	<?php echo wp_kses( UM()->frontend()::layouts()::ajax_loader( 'l', array( 'classes' => array( 'um-user-photos-albums-loader', 'um-display-none' ) ) ), UM()->get_allowed_html( 'templates' ) ); ?>
	<form id="um_user_photos_edit_album" class="um-form-new um-user-photos-album-form">
		<div class="um-form-rows _um_row_1">
			<div class="um-form-row">
				<div class="um-form-cols um-form-cols-1">
					<div class="um-form-col um-form-col-1">
						<?php if ( ! $disable_title ) { ?>
							<div class="um-field um-field-text um-field-type_text">
								<label for="um-album-title">
									<?php esc_html_e( 'Album title', 'um-user-photos' ); ?>
									<?php if ( UM()->options()->get( 'form_asterisk' ) ) { ?>
										<span class="um-req" title="<?php esc_attr_e( 'Required', 'um-user-photos' ); ?>">*</span>
									<?php } ?>
								</label>
								<input type="text" name="album_title" id="um-album-title" required value="<?php echo esc_attr( $album->post_title ); ?>" />
							</div>
						<?php } ?>

						<div class="um-field um-field-select um-field-type_select">
							<label for="um-album-privacy"><?php esc_html_e( 'Album privacy', 'um-user-photos' ); ?></label>
							<select id="um-album-privacy" class="um-form-field js-choice um-no-search" name="album_privacy">
								<?php foreach ( UM()->User_Photos()->common()->album()->privacy_options as $key => $label ) { ?>
									<option value="<?php echo esc_attr( $key ); ?>" <?php selected( $privacy, $key ); ?>><?php echo esc_html( $label ); ?></option>
								<?php } ?>
							</select>
						</div>

						<div class="um-field um-field-uploader um-field-type_uploader">
							<?php
							if ( ! $enable_upload && empty( $uploaded_photos ) ) {
								$error_message = empty( $album_limit_error ) ? __( 'You cannot upload more photos, you have reached the limit of uploads. Delete old photos to add new ones.', 'um-user-photos' ) : __( 'You cannot upload more photos, you have reached the limit of uploads for this album. Delete old photos or upload few photos.', 'um-user-photos' );
								echo wp_kses(
									UM()->frontend()::layouts()::alert(
										$error_message,
										array(
											'type'      => 'warning',
											'underline' => false,
											'classes'   => array( 'um-user-photos-error' ),
										)
									),
									UM()->get_allowed_html( 'templates' )
								);
							} else {
								?>
								<label><?php esc_html_e( 'Photos', 'um-user-photos' ); ?></label>
								<?php
								$classes = array( 'um-user-photos-error' );
								if ( $enable_upload ) {
									$classes[] = 'um-display-none';
								}
								$dropzone_error = UM()->frontend()::layouts()::alert(
									__( 'You cannot upload more photos, you have reached the limit of uploads. Delete old photos to add new ones.', 'um-user-photos' ),
									array(
										'type'      => 'warning',
										'underline' => false,
										'classes'   => $classes,
									)
								);

								$uploader_args = array(
									'id'                => 'um_user_photos_uploader',
									'handler'           => 'um-user-photos-upload',
									'async'             => false,
									'field_id'          => 'um_user_photos_upload',
									'name'              => 'album_photos',
									'multiple'          => true,
									'types'             => UM()->User_Photos()->common()->uploader()->allowed_extensions,
									'value'             => $uploaded_photos,
									'sortable_files'    => true,
									'disable_drop_zone' => ! $enable_upload,
									'dropzone_error'    => $dropzone_error,
								);

								$max_files = false;
								if ( ! empty( $limit_per_albums ) ) {
									$max_files = absint( $limit_per_albums ) - count( $uploaded_photos );
								}

								if ( ! empty( $limit_user_photos ) ) {
									$user_photos_left = absint( $limit_user_photos ) - absint( $count_user_photos );
									if ( empty( $max_files ) || $max_files > $user_photos_left ) {
										$max_files = $user_photos_left;
									}
								}

								// Set files limit or remove multiple attribute if max files = 1.
								if ( ! empty( $max_files ) ) {
									$uploader_args['max_files'] = $max_files;
								}

								echo wp_kses(
									UM()->frontend()::layouts()::uploader( $uploader_args ),
									UM()->get_allowed_html( 'templates' )
								);
							}
							?>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="um-form-submit">
			<?php
			$loader = UM()->frontend()::layouts()::ajax_loader( 'm', array( 'classes' => array( 'um-user-photos-loader', 'um-display-none' ) ) );
			$submit = UM()->frontend()::layouts()::button(
				__( 'Update', 'um-user-photos' ),
				array(
					'design'  => 'primary',
					'type'    => 'submit',
					'size'    => 's',
					'classes' => array(
						'um-gallery-album-update',
					),
				)
			);
			$cancel = UM()->frontend()::layouts()::link(
				__( 'Cancel', 'um-user-photos' ),
				array(
					'type'    => 'button',
					'size'    => 's',
					'classes' => array(
						'um-user-photos-back-to-album',
					),
					'data'    => array(
						'nonce' => wp_create_nonce( 'um_user_photos_single_album_view' ),
						'album' => $album->ID,
					),
				)
			);
			echo wp_kses( $loader . $submit . $cancel, UM()->get_allowed_html( 'templates' ) );
			?>
		</div>
		<input type="hidden" name="album_id" value="<?php echo esc_attr( $album->ID ); ?>" />
		<input type="hidden" name="_wpnonce" value="<?php echo esc_attr( wp_create_nonce( 'um_edit_album' ) ); ?>" />
	</form>
</div>
