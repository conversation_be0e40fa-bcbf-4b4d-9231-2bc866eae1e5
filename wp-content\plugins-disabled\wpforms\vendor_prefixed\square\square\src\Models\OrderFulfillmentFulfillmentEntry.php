<?php

declare (strict_types=1);
namespace WPForms\Vendor\Square\Models;

use stdClass;
/**
 * Links an order line item to a fulfillment. Each entry must reference
 * a valid `uid` for an order line item in the `line_item_uid` field, as well as a `quantity` to
 * fulfill.
 */
class OrderFulfillmentFulfillmentEntry implements \JsonSerializable
{
    /**
     * @var array
     */
    private $uid = [];
    /**
     * @var string
     */
    private $lineItemUid;
    /**
     * @var string
     */
    private $quantity;
    /**
     * @var array
     */
    private $metadata = [];
    /**
     * @param string $lineItemUid
     * @param string $quantity
     */
    public function __construct(string $lineItemUid, string $quantity)
    {
        $this->lineItemUid = $lineItemUid;
        $this->quantity = $quantity;
    }
    /**
     * Returns Uid.
     * A unique ID that identifies the fulfillment entry only within this order.
     */
    public function getUid() : ?string
    {
        if (\count($this->uid) == 0) {
            return null;
        }
        return $this->uid['value'];
    }
    /**
     * Sets Uid.
     * A unique ID that identifies the fulfillment entry only within this order.
     *
     * @maps uid
     */
    public function setUid(?string $uid) : void
    {
        $this->uid['value'] = $uid;
    }
    /**
     * Unsets Uid.
     * A unique ID that identifies the fulfillment entry only within this order.
     */
    public function unsetUid() : void
    {
        $this->uid = [];
    }
    /**
     * Returns Line Item Uid.
     * The `uid` from the order line item.
     */
    public function getLineItemUid() : string
    {
        return $this->lineItemUid;
    }
    /**
     * Sets Line Item Uid.
     * The `uid` from the order line item.
     *
     * @required
     * @maps line_item_uid
     */
    public function setLineItemUid(string $lineItemUid) : void
    {
        $this->lineItemUid = $lineItemUid;
    }
    /**
     * Returns Quantity.
     * The quantity of the line item being fulfilled, formatted as a decimal number.
     * For example, `"3"`.
     * Fulfillments for line items with a `quantity_unit` can have non-integer quantities.
     * For example, `"1.70000"`.
     */
    public function getQuantity() : string
    {
        return $this->quantity;
    }
    /**
     * Sets Quantity.
     * The quantity of the line item being fulfilled, formatted as a decimal number.
     * For example, `"3"`.
     * Fulfillments for line items with a `quantity_unit` can have non-integer quantities.
     * For example, `"1.70000"`.
     *
     * @required
     * @maps quantity
     */
    public function setQuantity(string $quantity) : void
    {
        $this->quantity = $quantity;
    }
    /**
     * Returns Metadata.
     * Application-defined data attached to this fulfillment entry. Metadata fields are intended
     * to store descriptive references or associations with an entity in another system or store brief
     * information about the object. Square does not process this field; it only stores and returns it
     * in relevant API calls. Do not use metadata to store any sensitive information (such as personally
     * identifiable information or card details).
     * Keys written by applications must be 60 characters or less and must be in the character set
     * `[a-zA-Z0-9_-]`. Entries can also include metadata generated by Square. These keys are prefixed
     * with a namespace, separated from the key with a ':' character.
     * Values have a maximum length of 255 characters.
     * An application can have up to 10 entries per metadata field.
     * Entries written by applications are private and can only be read or modified by the same
     * application.
     * For more information, see [Metadata](https://developer.squareup.com/docs/build-basics/metadata).
     *
     * @return array<string,string>|null
     */
    public function getMetadata() : ?array
    {
        if (\count($this->metadata) == 0) {
            return null;
        }
        return $this->metadata['value'];
    }
    /**
     * Sets Metadata.
     * Application-defined data attached to this fulfillment entry. Metadata fields are intended
     * to store descriptive references or associations with an entity in another system or store brief
     * information about the object. Square does not process this field; it only stores and returns it
     * in relevant API calls. Do not use metadata to store any sensitive information (such as personally
     * identifiable information or card details).
     * Keys written by applications must be 60 characters or less and must be in the character set
     * `[a-zA-Z0-9_-]`. Entries can also include metadata generated by Square. These keys are prefixed
     * with a namespace, separated from the key with a ':' character.
     * Values have a maximum length of 255 characters.
     * An application can have up to 10 entries per metadata field.
     * Entries written by applications are private and can only be read or modified by the same
     * application.
     * For more information, see [Metadata](https://developer.squareup.com/docs/build-basics/metadata).
     *
     * @maps metadata
     *
     * @param array<string,string>|null $metadata
     */
    public function setMetadata(?array $metadata) : void
    {
        $this->metadata['value'] = $metadata;
    }
    /**
     * Unsets Metadata.
     * Application-defined data attached to this fulfillment entry. Metadata fields are intended
     * to store descriptive references or associations with an entity in another system or store brief
     * information about the object. Square does not process this field; it only stores and returns it
     * in relevant API calls. Do not use metadata to store any sensitive information (such as personally
     * identifiable information or card details).
     * Keys written by applications must be 60 characters or less and must be in the character set
     * `[a-zA-Z0-9_-]`. Entries can also include metadata generated by Square. These keys are prefixed
     * with a namespace, separated from the key with a ':' character.
     * Values have a maximum length of 255 characters.
     * An application can have up to 10 entries per metadata field.
     * Entries written by applications are private and can only be read or modified by the same
     * application.
     * For more information, see [Metadata](https://developer.squareup.com/docs/build-basics/metadata).
     */
    public function unsetMetadata() : void
    {
        $this->metadata = [];
    }
    /**
     * Encode this object to JSON
     *
     * @param bool $asArrayWhenEmpty Whether to serialize this model as an array whenever no fields
     *        are set. (default: false)
     *
     * @return array|stdClass
     */
    #[\ReturnTypeWillChange] // @phan-suppress-current-line PhanUndeclaredClassAttribute for (php < 8.1)
    public function jsonSerialize(bool $asArrayWhenEmpty = \false)
    {
        $json = [];
        if (!empty($this->uid)) {
            $json['uid'] = $this->uid['value'];
        }
        $json['line_item_uid'] = $this->lineItemUid;
        $json['quantity'] = $this->quantity;
        if (!empty($this->metadata)) {
            $json['metadata'] = $this->metadata['value'];
        }
        $json = \array_filter($json, function ($val) {
            return $val !== null;
        });
        return !$asArrayWhenEmpty && empty($json) ? new stdClass() : $json;
    }
}
