div.wpforms-container .wpforms-form div.wpforms-field-richtext .insert-media.add_media {
  display: none !important;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-container {
  color: initial;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-has-error .quicktags-toolbar {
  border-top-color: #cc0000;
  border-left-color: #cc0000;
  border-right-color: #cc0000;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-has-error .wp-switch-editor {
  border-color: #cc0000;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-has-error .wp-editor-container textarea.wp-editor-area {
  border-color: #cc0000;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .wp-switch-editor {
  float: left;
  box-sizing: border-box;
  position: relative;
  top: var(--wpforms-field-border-size, 1px);
  background: #e6e6e6;
  color: #595959;
  cursor: pointer;
  font-size: 13px;
  font-weight: normal;
  line-height: 1.46153846;
  height: 29px;
  margin: 0 0 0 5px;
  padding: 3px 8px 4px;
  border: 1px solid #cccccc;
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-tabs {
  float: right;
  position: relative;
  z-index: 1;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .html-active button.switch-html,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .tmce-active button.switch-tmce,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar .mce-btn-group .mce-widget.mce-btn button {
  border-bottom-color: transparent;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .tmce-active button.switch-tmce,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .html-active button.switch-html {
  background: #f5f5f5;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .tmce-active.wpforms-focused button.switch-tmce,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .html-active.wpforms-focused button.switch-html {
  top: 0;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .html-active .quicktags-toolbar {
  display: flex;
  flex-wrap: wrap;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar .mce-btn-group .mce-btn.active, div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar .mce-btn-group .mce-btn:active, div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar .mce-btn-group .mce-btn.mce-active,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .qt-dfw.active.active,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .qt-dfw.active:active,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .qt-dfw.active.mce-active {
  background-color: transparent;
  color: inherit;
  border-color: #8c8f94;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar .mce-btn-group .mce-btn.active.mce-btn-has-text, div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar .mce-btn-group .mce-btn:active.mce-btn-has-text, div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar .mce-btn-group .mce-btn.mce-active.mce-btn-has-text,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .qt-dfw.active.active.mce-btn-has-text,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .qt-dfw.active:active.mce-btn-has-text,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .qt-dfw.active.mce-active.mce-btn-has-text {
  background-color: #ffffff;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar .mce-btn-group .mce-btn:focus, div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar .mce-btn-group .mce-btn:hover,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .qt-dfw.active:focus,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .qt-dfw.active:hover {
  border-color: #8c8f94;
  box-shadow: inset 0 1px 0 #fff, 0 1px 0 rgba(0, 0, 0, 0.08);
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar {
  padding: 3px;
  position: relative;
  border: 1px solid #cccccc;
  border-top-left-radius: 2px;
  background: #f5f5f5;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar .button {
  height: 26px;
  min-height: 26px;
  line-height: 24px;
  border-width: 1px;
  border-style: solid;
  -webkit-appearance: none;
  border-radius: 3px;
  font-weight: 400;
  color: #2271b1;
  border-color: #2271b1;
  background: #f6f7f7;
  vertical-align: top;
  padding: 0 8px;
  margin-right: 4px;
  text-transform: none;
  text-decoration: none;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar .button:hover {
  text-decoration: none;
  background: #f6f7f7;
  border-color: #0a4b78;
  color: #0a4b78;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar .button[value="b"], div.wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar .button[value="/b"] {
  font-weight: bold;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar .button[value="i"], div.wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar .button[value="/i"] {
  font-style: italic;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar .button[value="link"] {
  text-decoration: underline;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar .button[value="del"], div.wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar .button[value="/del"] {
  text-decoration: line-through;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-container textarea.wp-editor-area {
  border-radius: 0 0 2px 2px;
  border-top: 0;
  border-color: #cccccc;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-container textarea.wp-editor-area:focus {
  outline: none;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar-grp .mce-active i {
  color: inherit;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar-grp .mce-active .mce-caret {
  border-top: 0;
  border-bottom: 6px solid #595959;
}

#wpforms-form-page-page div.wpforms-field-richtext button.wp-switch-editor {
  font-size: 13px;
}

.rtl div.wpforms-container .wpforms-form div.wpforms-field-richtext div.wp-editor-tabs {
  float: left;
}

.rtl div.wpforms-container .wpforms-form div.wpforms-field-richtext div.wp-editor-tabs button.switch-tmce {
  margin-left: 0;
}

.rtl div.wpforms-container .wpforms-form div.wpforms-field-richtext div.wp-editor-tabs button.switch-tmce:after {
  left: 0 !important;
}

.rtl .wpforms-form div.wpforms-field-richtext .mce-container-body .mce-resizehandle {
  right: auto;
  left: 0;
  padding-left: 0;
}

.rtl .wpforms-form div.wpforms-field-richtext .mce-container-body .mce-resizehandle .mce-i-resize {
  transform: rotate(90deg);
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-container textarea.wp-editor-area {
  border: 1px solid #cccccc;
  border-top: 0;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .html-active .wp-editor-container {
  border: 0 none;
}
