<?php
/**
 * Template for the UM User Photos, The single "Album" block
 *
 * Page: "Profile", tab "Photos"
 * Parent template: gallery.php
 * @version 2.1.9
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/album-block.php
 * @var string|bool $count_msg
 * @var string      $title
 * @var int         $author
 * @var int         $id
 * @var string      $img
 * @var bool        $disable_title
 * @var string      $context
 * @var string      $url
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

$album_classes = array( 'um-user-photos-album-block' );
if ( ! empty( $url ) ) {
	$album_classes[] = 'um-user-photos-album-direct-link';
}
?>
<!-- um-user-photos/templates/album-block.php -->
<div class="um-user-photos-album">
	<?php if ( 'gallery' !== $context ) { ?>
		<div class="um-user-photos-album-skeleton">
			<div class="um-user-photos-album-skeleton-inner">
				<div class="um-skeleton-photo um-skeleton-box"></div>
				<div class="um-skeleton-box-header">
					<div class="um-skeleton-box-avatar um-skeleton-box"></div>
					<div class="um-skeleton-box-header-title um-skeleton-box"></div>
				</div>
				<?php if ( ! $disable_title || $count_msg ) { ?>
					<div class="um-skeleton-box-details">
						<?php if ( ! $disable_title ) { ?>
							<div class="um-skeleton-box-title um-skeleton-box"></div>
						<?php } ?>
						<?php if ( $count_msg ) { ?>
							<div class="um-skeleton-box-photos-count um-skeleton-box"></div>
						<?php } ?>
					</div>
				<?php } ?>
			</div>
		</div>
	<?php } ?>
	<a href="<?php echo ! empty( $url ) ? esc_url( $url ) : '#'; ?>" class="<?php echo esc_attr( implode( ' ', $album_classes ) ); ?>"
		title="<?php echo ! $disable_title ? esc_attr( $title ) : ''; ?>" data-id="<?php echo esc_attr( $id ); ?>" data-nonce="<?php echo esc_attr( wp_create_nonce( 'um_user_photos_single_album_view' ) ); ?>">
		<?php
		if ( ! empty( $img ) ) {
			echo wp_kses(
				UM()->frontend()::layouts()::lazy_image(
					$img,
					array(
						'width' => '100%',
						'alt'   => ! $disable_title ? esc_attr( $title ) : '',
					)
				),
				UM()->get_allowed_html( 'templates' )
			);
		}
		?>
	</a>
	<?php
	if ( 'gallery' !== $context ) {
		echo wp_kses(
			UM()->frontend()::layouts()::small_data(
				$author,
				array(
					'avatar_size' => 's',
					'classes'     => array( 'um-user-photos-author' ),
					'clickable'   => true,
					'url'         => um_user_profile_url( $author ),
					'supporting'  => '',
				)
			),
			UM()->get_allowed_html( 'templates' )
		);
	}
	if ( ! $disable_title || $count_msg ) {
		?>
		<div class="um-user-photos-album-details">
			<?php if ( ! $disable_title ) { ?>
				<span class="um-user-photos-album-title"><?php echo esc_html( $title ); ?></span>
			<?php } ?>
			<?php if ( $count_msg ) { ?>
				<span class="um-user-photos-album-photos-count"><?php echo esc_html( $count_msg ); ?></span>
			<?php } ?>
		</div>
	<?php } ?>
</div>
