<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="ProcessRulesetAutoloadTest" xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/PHPCSStandards/PHP_CodeSniffer/master/phpcs.xsd">

    <autoload>./tests/Core/Ruleset/Fixtures/ThisFileDoesNotExist.php</autoload>

    <!-- Prevent a "no sniff were registered" error. -->
    <rule ref="Generic.PHP.BacktickOperator"/>
</ruleset>
