"use strict";let WPFormsEntriesOverview=window.WPFormsEntriesOverview||function(e,n,i,t){const o={},l={chart:null,datepicker:null,locale:t.locale,data:[],activeFormId:t.settings.active_form_id,type:1===t.settings.graph_style?"bar":"line",theme:t.settings.color_scheme||"1",total:0,delimiter:t.delimiter,tooltipFormat:t.date_format,heading:"",classNames:{hide:"wpforms-hide",selected:"is-selected"},timespan:"",get i18n(){return t.i18n},get xAxesDisplayFormat(){var e;return!this.timespan.length||(e=this.timespan.split(this.delimiter),!Array.isArray(e))||2!==e.length||moment(e[0]).format("YYYY")===moment(e[1]).format("YYYY")?"MMM D":"MMM D YYYY"},get colors(){var e="line"===this.type;return{1:{hoverBorderColor:"#da691f",hoverBackgroundColor:"#da691f",borderColor:"rgb(226, 119, 48)",pointBackgroundColor:"rgba(255, 255, 255, 1)",backgroundColor:e?"rgba(255, 129, 0, 0.135)":"rgb(226, 119, 48)"},2:{hoverBorderColor:"#055f9a",hoverBackgroundColor:"#055f9a",borderColor:"#056aab",pointBackgroundColor:"rgba(255, 255, 255, 1)",backgroundColor:e?"#e6f0f7":"#056aab"}}},get settings(){var e=n("body").hasClass("rtl");return{type:this.type,data:{labels:[],datasets:[{data:[],label:this.i18n?.label||"",borderWidth:2,pointRadius:4,pointBorderWidth:1,maxBarThickness:100,...this.colors[this.theme]}]},options:{maintainAspectRatio:!1,clip:!1,layout:{padding:{left:15,right:19,top:21,bottom:4}},scales:{x:{type:"timeseries",offset:"bar"===this.type,time:{tooltipFormat:this.tooltipFormat},reverse:e,ticks:{font:{size:13,color:"#787c82"},padding:10,minRotation:25,maxRotation:25,callback(e,t,a){var r=Math.floor(a.length/7);return r<1||(a.length-t-1)%r==0?moment(e).format(l.xAxesDisplayFormat):void 0}}},y:{beginAtZero:!0,ticks:{maxTicksLimit:6,font:{size:13,color:"#787c82"},padding:20,callback(e){if(Math.floor(e)===e)return e}}}},elements:{line:{tension:0,fill:!0}},animation:!1,plugins:{legend:{display:!1},tooltip:{enabled:!0,displayColors:!1,rtl:e}}}}}},d={init:function(){n(d.ready)},ready:function(){d.setup(),d.bindEvents(),d.initDatePicker(),d.initChart()},setup:function(){o.$document=n(e),o.$wrapper=n(".wpforms-entries-overview"),o.$heading=n(".wpforms-overview-top-bar-heading h2"),o.$spinner=n(".wpforms-overview-chart .spinner"),o.$canvas=n("#wpforms-entries-overview-canvas"),o.$filterBtn=n("#wpforms-datepicker-popover-button"),o.$datepicker=n("#wpforms-entries-overview-datepicker"),o.$filterForm=n(".wpforms-overview-top-bar-filter-form"),o.$notice=n(".wpforms-overview-chart-notice"),o.$total=n(".wpforms-overview-chart-total-items"),o.$nonce=n('.wpforms-entries-overview-table [name="nonce"]')},bindEvents:function(){o.$document.on("click",{selectors:[".wpforms-datepicker-popover",".wpforms-dash-widget-settings-menu"]},d.handleOnClickOutside),o.$wrapper.on("submit",".wpforms-overview-top-bar-filter-form",d.handleOnSubmitDatepicker).on("click",'.wpforms-overview-top-bar-filter-form [type="reset"]',d.handleOnResetDatepicker).on("change",'.wpforms-overview-top-bar-filter-form [type="radio"]',d.handleOnUpdateDatepicker).on("click",".wpforms-show-chart",d.handleOnShowChart).on("click",".wpforms-reset-chart",d.handleOnResetChart).on("click",".wpforms-dash-widget-settings-menu-save",d.handleOnSaveSettings).on("click","#wpforms-dash-widget-settings-button",{selector:".wpforms-dash-widget-settings-menu",hide:".wpforms-datepicker-popover"},d.handleOnToggle).on("click","#wpforms-datepicker-popover-button",{selector:".wpforms-datepicker-popover",hide:".wpforms-dash-widget-settings-menu"},d.handleOnToggle)},initDatePicker:function(){o.$datepicker.length&&(l.timespan=o.$datepicker.val(),l.datepicker=flatpickr(o.$datepicker,{mode:"range",inline:!0,allowInput:!1,enableTime:!1,clickOpens:!1,altInput:!0,altFormat:"M j, Y",dateFormat:"Y-m-d",locale:{...flatpickr.l10ns[l.locale]||{},rangeSeparator:l.delimiter},onChange:function(e,t,a){var r=o.$filterForm.find('input[value="custom"]');r.prop("checked",!0),d.selectDatepickerChoice(r.parent()),t&&o.$filterBtn.text(a.altInput.value)}}),this.handleOnUpdateDatepicker({},o.$filterForm.find('input[value="custom"]').prop("checked")))},handleOnSubmitDatepicker:function(){n(this).find('input[type="radio"]').attr("name",""),d.hideElm(o.$filterBtn.next())},handleOnResetDatepicker:function(e){e.preventDefault(),o.$filterForm.get(0).reset(),d.hideElm(o.$filterBtn.next()),d.handleOnUpdateDatepicker()},handleOnUpdateDatepicker:function(e=0,t=!1){var a=o.$filterForm.find("input:checked"),r=a.parent(),a=t?o.$datepicker:a,s=a.val().split(l.delimiter);o.$filterBtn.text(t?a.next().val():r.text()),d.selectDatepickerChoice(r),Array.isArray(s)&&2===s.length?l.datepicker.setDate(s):l.datepicker.clear()},initChart:function(){var e;o.$canvas.length&&(e=o.$canvas.get(0).getContext("2d"),l.chart=new Chart(e,l.settings),l.heading=o.$heading.text(),this.updateChartByFormId("",this.updateChart,this.updateChartActiveForm))},handleOnShowChart:function(e){e.preventDefault(),d.spinner();var e=n(this),t=e.data("form");d.maybeCleanupChart(e),e.addClass(l.classNames.hide),e.prev().removeClass(l.classNames.hide),e.closest("tr").addClass(l.classNames.selected),d.updateChartByFormId(t)},handleOnResetChart:function(e){e.preventDefault(),d.spinner();e=n(this).closest("#wpforms-entries-list").find(`tr.${l.classNames.selected} .wpforms-reset-chart`);e.length&&(e.addClass(l.classNames.hide),e.next().removeClass(l.classNames.hide),e.closest("tr").removeClass(l.classNames.selected)),o.$heading.next().addClass(l.classNames.hide).end().text(l.heading),n.post(i,{_ajax_nonce:o.$nonce.val(),action:"wpforms_entries_overview_flush_chart_active_form_id"}).done(function(){d.updateChart()})},handleOnSaveSettings:function(e){e.preventDefault();var e=n(this).closest(".wpforms-dash-widget-settings-container"),t=e.find('input[name="wpforms-style"]:checked').val(),e=e.find('input[name="wpforms-color"]:checked').val(),a=(l.type=1===Number(t)?"bar":"line",l.theme=e,Object.assign({},l.settings)),r=(a.data.labels=l.chart.data.labels,a.data.datasets[0].data=l.chart.data.datasets[0].data,l.chart.destroy(),o.$canvas.get(0).getContext("2d"));l.chart=new Chart(r,a),n.post(i,{graphStyle:t,colorScheme:e,_ajax_nonce:o.$nonce.val(),action:"wpforms_entries_overview_save_chart_preference_settings"}).done(function(){o.$wrapper.find(".wpforms-dash-widget-settings-menu").hide()})},handleOnToggle:function(e){e.preventDefault(),e.stopPropagation();const{selector:t,hide:a}=e["data"];o.$wrapper.find(t).toggle(0,function(){var e=n(t);e.attr("aria-expanded",e.is(":visible"))}),d.hideElm(o.$wrapper.find(a))},handleOnClickOutside:function(e){const{target:a,data:{selectors:t}}=e;n.each(t,function(e,t){n(a).closest(t+":visible").length||d.hideElm(o.$wrapper.find(t))})},processDatasetData:function(e){const r=[],s=[];if(n.isPlainObject(e)&&0<Object.keys(e).length)o.$notice.addClass(l.classNames.hide),n.each(e||l.data,function(e,t){var a=moment(t.day);r.push(a),s.push({x:a,y:t?.count||0})});else{o.$notice.removeClass(l.classNames.hide);var t,a=moment().startOf("day");for(let e=1;e<=30;e++)t=a.clone().subtract(e,"days"),r.push(t),s.push({x:t,y:Math.floor(16*Math.random())+5})}return{labels:r,datasets:s}},updateChart:function(e,t){l.activeFormId||o.$total.text(t||l.total);var{labels:t,datasets:e}=d.processDatasetData(e||l.data);l.chart.data.labels=t,l.chart.data.datasets[0].data=e,l.chart.update(),o.$spinner.addClass(l.classNames.hide)},updateChartByFormId:function(r,s,e){n.post(i,{form:r,dates:l.timespan,_ajax_nonce:o.$nonce.val(),action:"wpforms_entries_overview_refresh_chart_dataset_data"},function({data:{data:e,name:t,total:a}}){r||Object.keys(l.data).length||(l.data=e,l.total=a),d.updateChart(e,a.toString()),t&&o.$heading.next().removeClass(l.classNames.hide).end().text(t),"function"==typeof s&&s()}).done(e)},updateChartActiveForm:function(){var e,t=l["activeFormId"];t&&(d.spinner(),l.activeFormId=null,(e=n(`.wpforms-show-chart[data-form="${t}"]`)).length?e.trigger("click"):d.updateChartByFormId(t))},maybeCleanupChart:function(e){e=e.closest("tbody").find("tr.is-selected");e.length<=0||(e.removeClass(l.classNames.selected),e.find(".wpforms-reset-chart").addClass(l.classNames.hide),e.find(".wpforms-show-chart").removeClass(l.classNames.hide))},selectDatepickerChoice:function(e){o.$filterForm.find("label").removeClass(l.classNames.selected),e.addClass(l.classNames.selected)},spinner:function(){o.$spinner.removeClass(l.classNames.hide)},hideElm:function(e){e.attr("aria-expanded","false").hide()}};return d}(document,(window,jQuery),ajaxurl,wpforms_admin_entries_overview);WPFormsEntriesOverview.init();