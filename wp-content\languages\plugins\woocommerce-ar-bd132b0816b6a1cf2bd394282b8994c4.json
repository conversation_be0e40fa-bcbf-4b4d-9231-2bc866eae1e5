{"translation-revision-date": "2025-05-21 12:54:18+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=6; plural=(n == 0) ? 0 : ((n == 1) ? 1 : ((n == 2) ? 2 : ((n % 100 >= 3 && n % 100 <= 10) ? 3 : ((n % 100 >= 11 && n % 100 <= 99) ? 4 : 5))));", "lang": "ar"}, "Upgrade to the blockified Add to Cart with Options block": ["الترقية إلى مكوِّن \"إضافة إلى عربة التسوق مع الخيارات\" الموجودة في مكوِّن"], "a new blockified experience": ["تجربة جديدة للوجود في مكوِّن"], "Upgrade the Add to Cart with Options block to <strongText /> for more features!": ["بادر بالترقية إلى مكوِّن \"إضافة إلى عربة التسوق مع الخيارات\" لـ <strongText /> للحصول على مزيد من الميزات!"], "Stepper": ["متدرج"], "Input": ["إدخال"], "Quantity Selector": ["أداة تحديد الكمية"], "Shoppers can use buttons to change the number of items to add to cart.": ["يمكن للمتسوقين استخدام أزرار لتغيير عدد العناصر المطلوب إضافتها إلى عربة التسوق."], "Shoppers can enter a number of items to add to cart.": ["يمكن للمتسوقين إدخال عدد العناصر المطلوب إضافته إلى عربة التسوق."], "Customer will see product add-to-cart options in this space, dependent on the product type.": ["سيرى العميل خيارات إضافة المنتجات إلى عربة التسوق في هذه المساحة، استنادًا إلى نوع المنتج."], "Add to cart": ["إضافة إلى السلة"]}}, "comment": {"reference": "assets/client/blocks/add-to-cart-form.js"}}