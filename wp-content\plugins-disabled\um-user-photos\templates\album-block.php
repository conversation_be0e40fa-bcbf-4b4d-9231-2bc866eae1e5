<?php
/**
 * Template for the UM User Photos, The single "Album" block
 *
 * Page: "Profile", tab "Photos"
 * Parent template: gallery.php
 * @version 2.1.9
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/album-block.php
 * @var string|bool $count_msg
 * @var string      $title
 * @var int         $id
 * @var string      $img
 * @var string      $default
 * @var bool        $disable_title
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
?>
<!-- um-user-photos/templates/album-block.php -->
<div class="um-user-photos-album">
	<a href="javascript:void(0);" class="um-user-photos-album-block"
		title="<?php echo ! $disable_title ? esc_attr( $title ) : ''; ?>" data-id="<?php echo esc_attr( $id ); ?>" data-nonce="<?php echo esc_attr( wp_create_nonce( 'um_user_photos_single_album_view' ) ); ?>">
		<div class="album-overlay"></div>
		<img src="<?php echo esc_attr( $img ); ?>" alt="<?php echo ! $disable_title ? esc_attr( $title ) : ''; ?>" data-default="<?php echo esc_url( $default ); ?>"
			onerror="if ( ! this.getAttribute('data-load-error') ){ this.setAttribute('data-load-error', '1');this.setAttribute('src', this.getAttribute('data-default'));}" />
	</a>

	<div class="um-clear"></div>

	<p class="album-title">
		<?php if ( ! $disable_title ) { ?>
			<strong>
				<?php echo esc_html( $title ); ?>
			</strong>
			<br />
		<?php } ?>
		<?php if ( $count_msg ) { ?>
			<small><?php echo esc_html( $count_msg ); ?></small>
		<?php } ?>
	</p>
</div>
