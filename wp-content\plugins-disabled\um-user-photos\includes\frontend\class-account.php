<?php
namespace um_ext\um_user_photos\frontend;

use WP_Query;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Account
 * @package um_ext\um_user_photos\frontend
 */
class Account {

	/**
	 * Account constructor.
	 */
	public function __construct() {
		add_filter( 'um_account_page_default_tabs_hook', array( $this, 'add_user_photos_tab' ), 100 );
		add_filter( 'um_account_content_hook_um_user_photos', array( $this, 'um_account_content_hook_um_user_photos' ) );
	}

	/**
	 * @param array $tabs
	 *
	 * @return array
	 */
	public function add_user_photos_tab( $tabs ) {
		if ( ! UM()->roles()->um_user_can( 'enable_user_photos' ) ) {
			// if the "User Photos" feature is disabled for the user role.
			return $tabs;
		}

		if ( defined( 'UM_DEV_MODE' ) && UM_DEV_MODE && UM()->options()->get( 'enable_new_ui' ) ) {
			$albums = new WP_Query(
				array(
					'post_type'      => 'um_user_photos',
					'author__in'     => array( get_current_user_id() ),
					'posts_per_page' => 1,
				)
			);

			$count_user_photos = UM()->User_Photos()->common()->user()->photos_count( get_current_user_id() );
			if ( $albums->found_posts > 0 || ! empty( $count_user_photos ) ) {
				$tabs[800]['um_user_photos'] = array(
					'icon'            => 'um-faicon-image',
					'title'           => __( 'My Photos', 'um-user-photos' ),
					'custom'          => true,
					'without_setting' => true,
					'show_button'     => false,
					'description'     => __( 'Once photos and albums are deleted, they are deleted permanently and cannot be recovered.', 'um-user-photos' ),
				);
			}
		} else {
			$tabs[800]['um_user_photos'] = array(
				'icon'            => 'um-faicon-image',
				'title'           => __( 'My Photos', 'um-user-photos' ),
				'custom'          => true,
				'without_setting' => true,
				'show_button'     => false,
			);
		}

		return $tabs;
	}

	/**
	 * Get template for the "Account" page "My Photos" tab.
	 *
	 * @param   string $output
	 * @return  string
	 */
	public function um_account_content_hook_um_user_photos( $output = '' ) {
		wp_enqueue_script( 'um-user-photos-account' );
		wp_enqueue_style( 'um-user-photos' );

		$user_id = um_user( 'ID' );

		$download_my_photos_notice = get_user_meta( $user_id, 'um_download_my_photos_notice', true );
		if ( $download_my_photos_notice ) {
			update_user_meta( $user_id, 'um_download_my_photos_notice', '' );
		}

		$t_args = compact( 'download_my_photos_notice', 'user_id' );
		$t_name = 'account.php';
		if ( defined( 'UM_DEV_MODE' ) && UM_DEV_MODE && UM()->options()->get( 'enable_new_ui' ) ) {
			$t_name = 'v3/' . $t_name;
		}
		$output .= UM()->get_template( $t_name, UM_USER_PHOTOS_PLUGIN, $t_args );

		return $output;
	}
}
