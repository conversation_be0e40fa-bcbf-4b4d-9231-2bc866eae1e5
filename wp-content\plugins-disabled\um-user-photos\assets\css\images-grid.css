.imgs-grid {
  max-width: 800px;
  margin: 0 auto;
  font-size: 0;
}

.imgs-grid.imgs-grid-1 .imgs-grid-image {
  width: 100%;
  text-align: center;
}

.imgs-grid.imgs-grid-2 .imgs-grid-image,
.imgs-grid.imgs-grid-4 .imgs-grid-image {
  width: 50%;
}

.imgs-grid.imgs-grid-3 .imgs-grid-image,
.imgs-grid.imgs-grid-6 .imgs-grid-image {
  width: 33.333333333333336%;
}

.imgs-grid.imgs-grid-5 .imgs-grid-image:nth-child(1),
.imgs-grid.imgs-grid-5 .imgs-grid-image:nth-child(2),
.imgs-grid.imgs-grid-5 .imgs-grid-image:nth-child(3) {
  width: 33.333333333333336%;
}

.imgs-grid.imgs-grid-5 .imgs-grid-image:nth-child(4),
.imgs-grid.imgs-grid-5 .imgs-grid-image:nth-child(5) {
  width: 50%;
}

.imgs-grid .imgs-grid-image {
  position: relative;
  display: inline-block;
  padding: 1px;
  box-sizing: border-box;
  text-align: center;
}

.imgs-grid .imgs-grid-image:before {
  content: "";
  display: block;
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  background-color: #f0f0f0;
}

.imgs-grid .imgs-grid-image:hover {
  cursor: pointer;
}

.imgs-grid .imgs-grid-image .image-wrap {
  position: relative;
  display: inline-block;
  overflow: hidden;
  vertical-align: middle;
}

.imgs-grid .imgs-grid-image .image-wrap img {
  position: relative;
  width: 100%;
  height: auto;
  margin: 0;
}

.imgs-grid .imgs-grid-image .view-all {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  text-align: center;
}

.imgs-grid .imgs-grid-image .view-all:before {
  display: inline-block;
  content: "";
  vertical-align: middle;
  height: 100%;
}

.imgs-grid .imgs-grid-image .view-all:hover {
  cursor: pointer;
}

.imgs-grid .imgs-grid-image .view-all:hover .view-all-text {
  text-decoration: underline;
}

.imgs-grid .imgs-grid-image .view-all .view-all-cover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: black;
  opacity: 0.4;
}

.imgs-grid .imgs-grid-image .view-all .view-all-text {
  position: relative;
  font-size: 16px;
  font-family: sans-serif;
  color: white;
}

@media (max-width: 350px) {

  .imgs-grid .imgs-grid-image .view-all .view-all-text {
    font-size: 10px;
  }

}

.imgs-grid-modal {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.9);
  opacity: 0;
  z-index: 100;
  -webkit-user-select: none;
  -moz-user-select: -moz-none;
  -khtml-user-select: none;
  -o-user-select: none;
  user-select: none;
}

.imgs-grid-modal .modal-caption {
  padding: 30px 0;
  width:30%;
  float: right;
  height: 100vh;
  max-height: 100%;
  background: #fff;
  color: #827f7f;
  font-size: 14px;
  text-align: left;
  overflow-y: auto;
}

.imgs-grid-modal .modal-close {
  position: absolute;
  right:0;
  top: 0;
  width: 35px;
  height: 35px;
  text-align: center;
  line-height: 35px;
  font-size: 30px;
  color:#ffff;
}

.imgs-grid-modal .modal-close:hover {
  cursor: pointer;
}

.imgs-grid-modal .modal-inner {
  position: absolute;
  top: 60px;
  bottom: 60px;
  left: 0;
  right: 0;
  width: 70%;
	width: calc((100% - 0.5*35px)*0.7);
}

.imgs-grid-modal .modal-inner .modal-control {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 70px;
  visibility: hidden;
  opacity: 0.3;
}

.imgs-grid-modal .modal-inner:hover>.modal-control {
  visibility: visible;
}

.imgs-grid-modal .modal-inner .modal-control:hover {
  cursor: pointer;
}

.imgs-grid-modal .modal-inner .modal-control.left {
  left: 0;
}

.imgs-grid-modal .modal-inner .modal-control.right {
  right: 0;
}

.imgs-grid-modal .modal-inner .modal-control .arrow {
  margin: 0 auto;
  height: 100%;
  width: 35px;
  background-repeat: no-repeat;
  background-image: url(../images/imgs-grid-icons.png);
}

.imgs-grid-modal .modal-inner .modal-control .arrow.left {
  background-position: 0px center;
}

.imgs-grid-modal .modal-inner .modal-control .arrow.right {
  background-position: -45px center;
}

.imgs-grid-modal .modal-inner .modal-image {
  position: absolute;
  top: 0;
  left: 70px;
  right: 70px;
  bottom: 0;
  text-align: center;
}

.imgs-grid-modal .modal-inner .modal-image:before {
  display: inline-block;
  content: "";
  vertical-align: middle;
  height: 100%;
}

.imgs-grid-modal .modal-inner .modal-image img {
  max-width: 100%;
  max-height: 100%;
  vertical-align: middle;
  display: inline-block;
}

.imgs-grid-modal .modal-inner .modal-image img:hover {
  cursor: pointer;
}

.imgs-grid-modal .modal-inner .modal-loader {
  display: inline-block;
  vertical-align: middle;
  color: silver;
  font-size: 14px;
}

.imgs-grid-modal .modal-indicator {
  position: absolute;
  bottom: 0;
  height: 60px;
  width: 100%;
  text-align: center;
}

.imgs-grid-modal .modal-indicator ul {
  margin: 0;
  padding: 0;
}

.imgs-grid-modal .modal-indicator ul li {
  display: inline-block;
  width: 8px;
  height: 8px;
  border: 1px solid rgba(255,255,255,0.5);
  box-sizing: border-box;
  border-radius: 100%;
  margin: 0 3px;
  vertical-align: middle;
}

.imgs-grid-modal .modal-indicator ul li:hover {
  cursor: pointer;
}

.imgs-grid-modal .modal-indicator ul li.selected {
  background-color: rgba(255,255,255,0.8);
  width: 8px;
  height: 8px;
  margin: 0;
}





/* custom styles */
body.um-user-photos-modal-open{
	max-height: 100vh;
  overflow-y: hidden;
}
body.um-user-photos-modal-open #wpadminbar {
  display: none;
}

@-webkit-keyframes um-user-photos-ajax-spinning {
	0% {
		-webkit-transform: rotate(0);
		transform: rotate(0)
	}

	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg)
	}
}

@keyframes um-user-photos-ajax-spinning {
	0% {
		-webkit-transform: rotate(0);
		transform: rotate(0)
	}

	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg)
	}
}

.um-spin {
	-webkit-animation: um-user-photos-ajax-spinning 1.1s infinite linear;
	animation: um-user-photos-ajax-spinning 1.1s infinite linear;
	-ms-transform: translateZ(0);
	transform: translateZ(0);
}

div.um-user-photos-ajax-loading {
	color: #c6c6c6 !important;
	-webkit-transition: .1s opacity!important;
	-moz-transition: .1s opacity!important;
	-ms-transition: .1s opacity!important;
	-o-transition: .1s opacity!important;
	transition: .1s opacity!important;
	-webkit-animation: um-user-photos-ajax-spinning 1.1s infinite linear;
	animation: um-user-photos-ajax-spinning 1.1s infinite linear;
	border-top: .2em solid rgba(198, 198, 198, 0.4);
	border-right: .2em solid rgba(198, 198, 198, 0.4);
	border-bottom: .2em solid rgba(198, 198, 198, 0.4);
	border-left: .2em solid #a29f9f;
	font-size: 1.75em;
	filter: alpha(opacity=0);
	-ms-transform: translateZ(0);
	transform: translateZ(0);
	border-radius: 50%;
	display: inline-block;
	width: 2.5em;
	height: 2.5em;
	margin: 0;
	outline: 0;
	padding: 0;
	vertical-align: baseline;
}

/* comments section */

.imgs-grid-modal {
  padding: 35px;
}

.imgs-grid-modal .modal-caption {
  background: #fff;
}

.imgs-grid-modal .modal-caption .um-user-photos-widget {
	margin-top: -20px;
	box-shadow:none;
	border-radius: 3px;
}

.imgs-grid-modal .modal-caption .um-user-photos-widget .um-user-photos-head {
	background: transparent;
	border-radius: 3px 3px 0 0;
	padding: 8px 6px 8px 15px;
	border: none;
	border-bottom-color: #f3f3f3;
	color: #555;
	font-weight: 700;
	font-size: 12px;
}

.imgs-grid-modal .modal-caption .um-user-photos-widget .um-user-photos-head .um-user-photos-left.um-user-photos-author {
	position: relative;
	padding-left: 50px;
	min-height: 40px;
	float:left;
}

.imgs-grid-modal .modal-caption .um-user-photos-widget .um-user-photos-head .um-user-photos-author a {
	border: none!important;
}

.imgs-grid-modal .modal-caption .um-user-photos-widget .um-user-photos-head .um-user-photos-left.um-user-photos-author .um-user-photos-ava {
	position: absolute;
	top: 0;
	left: 0;
	width: 40px;
	height: 40px;
}

.imgs-grid-modal .modal-caption .um-user-photos-widget .um-user-photos-head .um-user-photos-left.um-user-photos-author .um-user-photos-ava a {
	width: 100%;
	height: 100%;
	display: block;
}

.imgs-grid-modal .modal-caption .um-user-photos-widget .um-user-photos-head .um-user-photos-left.um-user-photos-author .um-user-photos-ava img {
	border-radius: 50%;
	border: none;
	display: block;
	padding: 0 !important;
	width: 100%;
	height: auto;
}

.imgs-grid-modal .modal-caption .um-user-photos-widget .um-user-photos-head .um-user-photos-left.um-user-photos-author .um-user-photos-author-meta {}

.imgs-grid-modal .modal-caption .um-user-photos-widget .um-user-photos-head .um-user-photos-left.um-user-photos-author .um-user-photos-author-meta .um-user-photos-author-url a {
	font-weight: 700;
	line-height: 16px;
  color: #444444;
}

.imgs-grid-modal .modal-caption .um-user-photos-author-meta span.um-user-photos-metadata {
	display: block;
	color: #aaa;
	font-weight: 400;
	line-height: 1.4em;
}

.imgs-grid-modal .modal-caption .um-user-photos-author-meta span.um-user-photos-metadata a {
	color: #aaa!important;
	text-decoration: none;
}


.imgs-grid-modal .modal-caption .um-user-photos-body {
	min-height: 10px;
	border: none;
	border-top: 0;
	border-bottom: 0;
	background: transparent;
}

.imgs-grid-modal .modal-caption .um-user-photos-bodyinner {
	line-height: 1.5em;
	padding: 0.5em 15px;
}



.imgs-grid-modal .modal-caption .um-user-photos-left {
	float: left;
}

.imgs-grid-modal .modal-caption .um-user-photos-right {
	float: right;
}

.imgs-grid-modal .modal-caption .um-user-photos-disp {
	margin: 0px;
	font-size: 13px;
	border-top: 1px solid #f6f6f6;
	padding: 0.5em 15px;
	text-align:center;
}

.imgs-grid-modal .modal-caption .um-user-photos-disp-likes,
.imgs-grid-modal .modal-caption .um-user-photos-disp-comments
{
	display: inline-block;
	margin: 1px 28px 0 0;
	font-size: 20px;
	line-height: 20px;
	font-weight: bold;
}

.imgs-grid-modal .modal-caption .um-user-photos-disp-likes a,
.imgs-grid-modal .modal-caption .um-user-photos-disp-comments a {
  text-decoration: none;
}

.imgs-grid-modal .modal-caption  .um-user-photos-disp-comments a span.um-user-photos-disp-span,
.imgs-grid-modal .modal-caption  .um-user-photos-disp-likes a span.um-user-photos-disp-span {
	display: block;
	font-size: 13px;
	color: #999;
	font-weight: normal;
}

.imgs-grid-modal .modal-caption .um-user-photos-foot {
	padding: 0 5px 0 0;
	background: #fcfcfc;
	border: 1px solid #e5e5e5;
	border-top-color: #f3f3f3;
	border-bottom: 0;
	border-radius: 0 0 3px 3px;
}

.imgs-grid-modal .modal-caption .um-user-photos-foot.status {
	background: #fff;
	padding: 5px 0;
	border-bottom: 0;
	border-radius: 0;
	padding-bottom: 15px;
}

.imgs-grid-modal .modal-caption .um-user-photos-actions div {
	float: left;
}

.imgs-grid-modal .modal-caption .um-user-photos-actions a {
	text-decoration: none;
	border: none!important;
	font-size: 15px;
	color: #bbb;
	padding: 5px;
	margin: 0 10px;
	float: left;
	height: 20px;
	line-height: 20px;
}

.imgs-grid-modal .modal-caption .um-user-photos-comments {
	background: #fafafa;
	border: 1px solid #e5e5e5;
	border-top-color: #f3f3f3;
	border-bottom: 0;
	border-radius: 0 0 3px 3px;
	padding: 10px 15px 0 15px;
}

.imgs-grid-modal .modal-caption .um-user-photos-commentl.um-user-photos-comment-area {
	padding-bottom: 0;
}

.imgs-grid-modal .modal-caption .um-user-photos-commentl {
	padding: 0 0 10px 40px;
	position: relative;
	min-height: 30px;
}

.imgs-grid-modal .modal-caption .um-user-photos-commentwrap {
	padding-bottom: 1px!important;
}

.imgs-grid-modal .modal-caption .um-user-photos-comment-hide {
	border: none!important;
	color: #bbb;
	position: absolute;
	right: 0;
	top: 0;
	z-index: 55;
	font-size: 13px;
	height: 13px;
	line-height: 13px;
	display: none;
}

.imgs-grid-modal .modal-caption .um-user-photos-comment-avatar {
	position: absolute;
	top: 0;
	left: 0;
	height: 30px;
}

.imgs-grid-modal .modal-caption .um-user-photos-comment-hidden {
	position: relative;
	font-size: 12px;
	color: #aaa;
	padding-top: 4px;
	display: none;
}

.imgs-grid-modal .modal-caption .um-user-photos-comment-info {
	position: relative;
	top: -3px;
	font-size: 12px;
	padding-left:10px;
}

.imgs-grid-modal .modal-caption .um-user-photos-comment-info .um-user-photos-comment-text {
	word-wrap: break-word;
}

.imgs-grid-modal .modal-caption .um-user-photos-comment-child {
	border-left: 2px solid #e5e5e5;
	margin-top: -6px;
	margin-bottom: 12px;
	margin-left: 28px;
	padding-left: 10px;
}

.imgs-grid-modal .modal-caption .um-user-photos-comment-child .um-user-photos-commentl {
	padding: 3px 0 3px 28px;
	min-height: 20px;
}

.imgs-grid-modal .modal-caption .um-user-photos-comment-child .um-user-photos-comment-avatar {
	top: 3px;
	height: 20px;
}

.imgs-grid-modal .modal-caption .um-user-photos-comment-child .um-user-photos-comment-avatar img, .um-user-photos-comment-info .um-user-photos-comment-avatar img {
	width: 20px!important;
	height: 20px!important;
}

.imgs-grid-modal .modal-caption .um-user-photos-commentl.is-child .um-user-photos-comment-hidden {
	padding-top: 0;
}

.imgs-grid-modal .modal-caption .um-user-photos-comment-meta .um-user-photos-editc {
	position: relative;
}

.imgs-grid-modal .modal-caption .um-user-photos-comment-meta .um-user-photos-editc-d {
	position: absolute;
	top: 24px;
	right: -10px;
	width: 100px;
	background: #fff;
	box-shadow: 0 1px 2px #ddd;
	display: none;
	z-index: 999;
}


.imgs-grid-modal .modal-caption .um-user-photos-comment-meta .um-user-photos-editc-d a {
	display: block;
	font-size: 13px;
	padding: 8px 10px;
}

.imgs-grid-modal .modal-caption .um-user-photos-bodyinner-txt {
	font-size: 13px;
	line-height: 20px;
	color: #666;
	word-wrap: break-word;
}

.imgs-grid-modal .modal-caption  .um-user-photos-commentl .um-user-photos-comment-avatar img {
	border-radius:50%;
	width: 40px;
	height: 40px;
}

.imgs-grid-modal .modal-caption  .um-user-photos-commentl .um-user-photos-comment-box {
  padding-left:10px;
}

.imgs-grid-modal .modal-caption  .um-user-photos-commentl .um-user-photos-comment-box .um-user-photos-comment-textarea {
  font-size:12px;
  border:1px solid #ccc;
	box-sizing: border-box;
	min-height: 40px;
	resize: vertical;
	width: 100%;
}

.imgs-grid-modal .modal-caption .um-user-photos-comment-area a.um-button,
.imgs-grid-modal .modal-caption .um-user-photos-foot a.um-button {
	padding: 0 20px!important;
	font-size: 12px;
	font-weight: 700!important;
	width: auto!important;
	height: 29px!important;
	line-height: 26px!important;
	margin-top: 6px;
	margin-bottom: 5px;
}

.imgs-grid-modal .modal-caption .um-user-photos-comment-meta > span:after {
	content: "\00b7";
	margin: 0 2px 0 5px;
	color: #aaa;
}

.imgs-grid-modal .modal-caption .um-user-photos-comments-loop {
  margin-top:10px;
}

.imgs-grid-modal .modal-caption  .um-user-photos-comment-meta a.um-user-photos-comment-permalink {
	color: #aaa!important;
	text-decoration: none;
}

.imgs-grid-modal .modal-caption .um-user-photos-comment-meta .um-user-photos-editc a {
	font-size: 15px;
	color: #aaa;
}


.um-user-photos-like-list-item img {
  display: inline-block;
  border-radius: 50%;
}



@media (max-width:768px){


  .imgs-grid-modal .modal-caption,
  .imgs-grid-modal .modal-inner {
    width: 100%;
    float:none;
    position: static;
    height: auto;
  }

  .imgs-grid-modal {
    overflow-y: scroll;
    max-height: 100vh;
  }

  .imgs-grid-modal .modal-inner .modal-image {
    position: static;
  }

  .imgs-grid-modal .modal-inner>.modal-control {
    visibility: visible;
    opacity: 1;
    width: 35px;
  }

  .imgs-grid-modal .modal-inner .modal-control .arrow {
    -webkit-transform: scale(0.7);
    -moz-transform: scale(0.7);
    -o-transform: scale(0.7);
    -ms-transform: scale(0.7);
    transform: scale(0.7);
  }

  .imgs-grid-modal .modal-close {
    position:fixed;
    z-index:1000;
  }

  .um-user-photos-col-3 a {
    display: block;
    text-align: center;
    margin-bottom: 20px;
  }

  .um-user-photos-col-3 img {
    display: inline-block;
  }

  .um-user-photos-album-head .col-title h2 {
    font-size: 16px;
  }

}
