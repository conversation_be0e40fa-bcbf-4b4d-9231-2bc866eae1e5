<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="GetIgnorePatternsTest" xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/PHPCSStandards/PHP_CodeSniffer/master/phpcs.xsd">

    <exclude-pattern>./tests/</exclude-pattern>
    <exclude-pattern type="absolute">./vendor/*</exclude-pattern>
    <exclude-pattern type="relative">*/node-modules/*</exclude-pattern>

    <rule ref="PSR1"/>

    <rule ref="PSR1.Classes.ClassDeclaration">
        <exclude-pattern type="absolute">./src/*/file.php</exclude-pattern>
        <exclude-pattern type="relative">./bin/</exclude-pattern>
    </rule>

    <rule ref="Generic.Formatting.SpaceAfterCast">
        <exclude-pattern>./src/*/test\.php$</exclude-pattern>
    </rule>

</ruleset>
