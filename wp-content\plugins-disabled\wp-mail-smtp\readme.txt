=== WP Mail SMTP by WPForms - The Most Popular SMTP and Email Log Plugin ===
Contributors: wpforms, jaredatch, smub, slaFFik, capuderg
Tags: smtp, email, gmail, outlook, email logs
Requires at least: 5.5
Tested up to: 6.7
Stable tag: 4.4.0
Requires PHP: 7.2
License: GNU General Public License v3.0 or later

Make email delivery easy for WordPress. Connect with SMTP, Gmail, Outlook, SendGrid, Mailgun, SES, Zoho, + more. Rated #1 WordPress SMTP Email plugin.

== Description ==

### WordPress Mail SMTP Plugin

Is your WordPress site not sending emails? You're not alone. 3+ million websites use WP Mail SMTP to send emails reliably.

Our goal is to make email deliverability easy so that your emails always reach the inbox.

WP Mail SMTP fixes your email deliverability issues by reconfiguring WordPress to use a proper SMTP provider when sending emails.

Best of all, our easy-to-use Setup Wizard and detailed documentation guide you through the process.

WP Mail SMTP is free and has everything you need to reliably send your WordPress emails!

[**WP Mail SMTP Pro**](https://wpmailsmtp.com/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) unlocks even more powerful features like email logging, advanced email reporting and stats, backup connections, email alerts, smart conditional routing, and much more.

https://www.youtube.com/watch?v=QYJuPyiKKZc

#### What is SMTP?

SMTP (Simple Mail Transfer Protocol) is an industry-standard for sending emails. Proper SMTP configuration helps increase email deliverability by adding authentication to the emails sent from your site.

Popular email clients like Gmail, Yahoo, Office 365, and Zoho are in a constant battle with email spammers, so they check whether emails are originating from a genuine sender. If the proper authentication isn't there, emails either go in the SPAM folder or, worse, disappear.

This is a problem for a lot of WordPress sites. By default, WordPress uses the PHP mail function to send emails generated by WordPress or any contact form plugin like [WPForms](https://wpforms.com/?utm_source=wprepo-wpmailsmtp&utm_medium=link&utm_campaign=liteplugin&utm_content=readme).

But most [WordPress hosting companies](https://www.wpbeginner.com/wordpress-hosting/) don't have their servers properly configured for sending PHP emails.

This is why WordPress emails aren’t delivered.

#### How does WP Mail SMTP work?

WP Mail SMTP plugin easily resolves email delivery problems by changing the way your WordPress site sends email. We reconfigure the `wp_mail()` function to use proper SMTP host credentials or an SMTP mail provider.

With our built-in SMTP mail provider integrations (recommended), emails are sent using the provider's direct API. Even if your web host is blocking SMTP ports, your emails will still be sent successfully.

This helps you fix all [WordPress not sending email](https://wpmailsmtp.com/wordpress-not-sending-email/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) issues.

WP Mail SMTP plugin includes many different SMTP mailers:

1. SendLayer <strong>(#1 Recommended)</strong>
2. SMTP.com <strong>(Recommended)</strong>
3. Brevo (formerly Sendinblue) SMTP <strong>(Recommended)</strong>
4. Gmail SMTP (Gmail, Google Workspace, G Suite)
5. Elastic Email
6. Mailgun SMTP
7. Mailjet SMTP
8. SendGrid SMTP
9. Postmark SMTP
10. SparkPost SMTP
11. SMTP2GO
12. Microsoft SMTP One-Click Setup (Outlook.com and Office 365) [[Pro]](https://wpmailsmtp.com/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme)
13. Amazon SES SMTP [[Pro]](https://wpmailsmtp.com/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme)
14. Zoho Mail SMTP [[Pro]](https://wpmailsmtp.com/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme)
15. Other SMTP

For most options, you can specify the "from name" and "email address" for outgoing emails too.

All of these powerful features make WP Mail SMTP the best SMTP solution for WordPress.

If you don't know which mailer to choose, see our [Complete Guide to WP Mail SMTP Mailers](https://wpmailsmtp.com/docs/a-complete-guide-to-wp-mail-smtp-mailers/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme).

#### SendLayer

SendLayer is our #1 recommended transactional email service.

Its affordable pricing and simple setup make it the perfect choice for sending emails from WordPress. It also has open and click tracking and email logs.

SendLayer is reliable, fast, and easy to set up. You can send hundreds of emails for free when you sign up for a trial.

Read our [SendLayer documentation](https://wpmailsmtp.com/docs/how-to-set-up-the-sendlayer-mailer-in-wp-mail-smtp/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) for more details.

#### SMTP.COM

SMTP.com is a recommended transactional email service.

With over 22 years of email delivery expertise, SMTP.com has a reputation for being one of the most reliable senders on the internet.

You can start sending emails in minutes and benefit from 50,000 free emails in your first 30 days.

Read our [SMTP.com documentation](https://wpmailsmtp.com/docs/how-to-set-up-the-smtp-com-mailer-in-wp-mail-smtp/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) for more details.

#### Brevo (formerly Sendinblue) SMTP

Brevo is a recommended transactional email service. It serves 80,000+ companies worldwide.

Brevo is reliable, fast, and gives you 300 free emails per day.

Read our [Brevo documentation](https://wpmailsmtp.com/docs/how-to-set-up-the-sendinblue-mailer-in-wp-mail-smtp/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) for more details.

### WP Mail SMTP PRO

In addition to native Microsoft, Amazon SES, and Zoho Mail integrations, WP Mail SMTP Pro provides access to many other powerful features.

[Click here to purchase WP Mail SMTP Pro now!](https://wpmailsmtp.com/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme)

### Email Log

Email logging is a powerful feature that keeps a record of all sent emails in WordPress. Email logging helps you to archive, audit, resend, or test email delivery and formatting.

Our [WordPress email logs](https://wpmailsmtp.com/log-emails-wordpress/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) also include:

#### Email Log Details

Our email logs provide a complete history of all emails sent from WordPress. View the subject, sender, recipients, content, headers, open and click rates, delivery status, source plugin, and more!

#### Resend Emails

Resend emails individually or in bulk, whether they failed or were delivered successfully. You can also forward important emails to an alternative email address.

#### And many more Email Log Features

Store all email attachments, export email logs, print emails, see delivery status, and more.

### Email Reports

Review weekly sent and failed emails in a dashboard chart.

Email reports make it easy to track deliverability and engagement. Open rates and click-through rates are grouped by subject line, making it easy to see the performance of your campaigns or notifications.

#### Weekly Email Summary

Get statistics about WordPress emails, including how many emails are being sent and which ones are being opened and clicked. The Summary also shows you deliverability statistics without the need to log in to WordPress to check them.

#### Track Email Opens and Clicks

[View open and click stats for WordPress emails](https://wpmailsmtp.com/enable-wordpress-email-tracking/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme), grouped by subject line in your Email Report.

### Email Alerts

If your emails stop sending, get notified instantly via Slack, Microsoft Teams, Discord, SMS/ Twilio, webhooks, or email (via secure API).

In combination with our email logging and resending features, Email Alerts ensure that no important email will ever be lost.

### Backup Connection

Configure an extra connection that kicks in if your primary connection fails. WP Mail SMTP automatically detects connection issues and automatically switches to the backup mailer. It will also automatically retry emails that failed.

### Smart Conditional Routing

Create criteria to send different types of emails using different mailers. Filter by the contents of the email Subject or Message, From or To addresses, the plugin that generated the email, and more.

This allows you to mix transactional and marketing providers to improve deliverability.

### Rate Limiting

Control the number of emails your WordPress site sends in a specific amount of time so you stay within your SMTP provider’s rate limits.

WP Mail SMTP allows you to specify the maximum number of emails that will be sent every minute, hour, day, week, or month and automatically queues emails to stay within those limits.

### Optimized Email Sending

Are emails slowing down your site? Let WP Mail SMTP queue your emails for better performance.

With optimized sending, emails are queued in the background and sent when your server has sufficient resources, avoiding bottlenecks that can slow down your site.

### Manage WordPress Emails and Notifications

Control the default notifications WordPress sends. Use a simple switch to disable specific types of notifications if you don’t want to receive them.

### WordPress Multisite

#### WordPress Multisite Network Settings

For users running a multisite network, save time with a centralized location to easily configure your SMTP settings for all sites.

#### Manage Multisite Email Logs Easily

Network Admins can view and manage email logs for subsites with easy switching and dashboard views.

### Expert Support

We provide [limited support](https://wordpress.org/support/topic/wp-mail-smtp-support-policy/) on the WordPress.org forums. World-class one-on-one email support is available to [WP Mail SMTP Pro](https://wpmailsmtp.com/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) users.

#### White Glove Setup

If you’re not sure how to fix your emails, sit back and relax. We’ll set up WP Mail SMTP for you!

White Glove Setup includes installation, configuration in WordPress, DNS configuration, full mailer setup, and testing. White Glove Setup is available for our recommended mailers: SendLayer, Brevo, and SMTP.com.

### Credits

WP Mail SMTP plugin was originally created by Callum Macdonald. It is now owned and maintained by the team behind [WPForms](https://wpforms.com/?utm_source=wprepo-wpmailsmtp&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) - the best drag & drop form builder for WordPress.

You can try the [free version of WPForms plugin](https://wordpress.org/plugins/wpforms-lite/) to see why it's the best in the market.

== Installation ==

1. Install WP Mail SMTP by WPForms either via the WordPress.org plugin repository or by uploading the files to your server. (See instructions on [how to install a WordPress plugin](http://www.wpbeginner.com/beginners-guide/step-by-step-guide-to-install-a-wordpress-plugin-for-beginners/))
2. Activate WP Mail SMTP by WPForms.
3. Navigate to the Settings area of WP Mail SMTP in the WordPress admin.
4. Choose your SMTP option (SendLayer, SMTP.com, Brevo (formerly Sendinblue), Gmail SMTP, Elastic Email, Mailgun SMTP, Mailjet, SendGrid SMTP, Postmark, SparkPost, SMTP2GO, or Other SMTP) and follow the instructions to set it up.
5. Need more help? Get support with [WP Mail SMTP PRO](https://wpmailsmtp.com/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme).

== Frequently Asked Questions ==

= Which email providers does WP Mail SMTP support? =

**SendLayer**

SendLayer is our #1 recommended transactional email service.

Its affordable pricing and simple setup make it the perfect choice for sending emails from WordPress. It also has open and click tracking, email logs, and email list management.
SendLayer is the best choice if you want a mailer that's reliable, fast, and easy to set up. You can send hundreds of emails for free when you sign up for a trial.

Read our [SendLayer documentation](https://wpmailsmtp.com/docs/how-to-set-up-the-sendlayer-mailer-in-wp-mail-smtp/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) for more details.

**SMTP.COM**

SMTP.com is a recommended transactional email service.

With over 22 years of email delivery expertise, SMTP.com has been around for almost as long as email itself. They are known among internet providers as one of the most reliable senders on the internet.

Their easy integration process lets you start sending emails in minutes and benefit from years of experience. SMTP.com provides users 50,000 free emails the first 30 days.

Read our [SMTP.com documentation](https://wpmailsmtp.com/docs/how-to-set-up-the-smtp-com-mailer-in-wp-mail-smtp/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) for more details.

**Brevo (formerly Sendinblue) SMTP**

Brevo is a recommended transactional email service.

They serve 80,000+ growing companies around the world and send over 30 million emails each day.

Their email deliverability experts are constantly at work optimizing the reliability and speed of their SMTP infrastructure. Brevo provides users 300 free emails per day.

Read our [Brevo documentation](https://wpmailsmtp.com/docs/how-to-set-up-the-sendinblue-mailer-in-wp-mail-smtp/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) for more details.

**Gmail SMTP (Gmail, Google Workspace, G Suite)**

Often bloggers and small business owners don't want to use third-party SMTP services. Well you can use your Gmail or Google Workspace (also known as G Suite/Google Apps) account for SMTP emails.

This allows you to use your [professional email address](http://www.wpbeginner.com/beginners-guide/how-to-setup-a-professional-email-address-with-gmail-and-google-apps/) and improve email deliverability.

Unlike other Gmail SMTP plugins, our Gmail SMTP option uses OAuth to authenticate your Google account, keeping your login information 100% secure.

Our plugin also offers the "One-Click Setup" option, which allows you to start sending emails from your Gmail account with just a few clicks. It eliminates the need to manually configure your own Google App, which is a technical and time-consuming process.

Read our [Gmail documentation](https://wpmailsmtp.com/docs/how-to-set-up-the-gmail-mailer-in-wp-mail-smtp/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) for more details.

**Elastic Email**

Elastic Email is a cloud-based email marketing platform offering tools for email campaigns, automation, transactional emails, and analytics, designed for businesses of all sizes. Elastic Email offers a limited free plan where you can send emails to your verified addresses.

Read our [Elastic Email documentation](https://wpmailsmtp.com/docs/how-to-set-up-the-elastic-email-mailer-in-wp-mail-smtp/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) for more details.

**Mailgun SMTP**

Mailgun SMTP is a popular SMTP service provider that allows you to send large quantities of emails. They provide 5,000 free emails per month for 3 months.

WP Mail SMTP plugin offers a native integration with MailGun. All you have to do is connect your Mailgun account, and you will improve your email deliverability.

Read our [Mailgun documentation](https://wpmailsmtp.com/docs/how-to-set-up-the-mailgun-mailer-in-wp-mail-smtp/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) for more details.

**Mailjet SMTP**

Mailjet is a global email sending service that allows you to design, send, and track marketing and transactional emails. They provide 6,000 free emails per month (up to 200 emails per day).

WP Mail SMTP plugin offers seamless integration with Mailjet. By connecting your Mailjet account, you can enhance your email deliverability and ensure your WordPress emails reach your recipients' inboxes.

Read our [Mailjet documentation](https://wpmailsmtp.com/docs/how-to-set-up-the-mailjet-mailer-in-wp-mail-smtp/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) for more details.

**SendGrid SMTP**

SendGrid has a free SMTP plan that you can use to send up to 100 emails per day. With our native SendGrid SMTP integration, you can easily and securely set up SendGrid SMTP on your WordPress site.

Read our [SendGrid documentation](https://wpmailsmtp.com/docs/how-to-set-up-the-sendgrid-mailer-in-wp-mail-smtp/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) for more details.

**Postmark SMTP**

Send emails securely using your Postmark account with our API integration. You can sign up for a free trial without a credit card, which allows you to send up to 100 emails per month.

Read our [Postmark documentation](https://wpmailsmtp.com/docs/how-to-set-up-the-postmark-mailer-in-wp-mail-smtp/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) for more details.

**SparkPost SMTP**

SparkPost is a transactional email provider that's trusted by big brands and small businesses. It sends more than 4 trillion emails each year and reports 99.9% uptime. You can get started with the free test account that lets you send up to 500 emails per month.

Read our [SparkPost documentation](https://wpmailsmtp.com/docs/how-to-set-up-the-sparkpost-mailer-in-wp-mail-smtp/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) for more details.

**SMTP2GO**

SMTP2GO is a transactional email provider that offers a robust and reliable email delivery service with global infrastructure, real-time analytics, and advanced security features. If you're just starting out, you can use SMTP2GO's free plan to send up to 1000 emails per month.

Read our [SMTP2GO documentation](https://wpmailsmtp.com/docs/how-to-set-up-the-smtp2go-mailer-in-wp-mail-smtp/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) for more details.

**Microsoft SMTP (Outlook.com and Office 365)**

The Microsoft 365 / Outlook mailer is a great choice if you already use Microsoft's email services (Outlook, Office 365, Microsoft 365, or Hotmail). Due to the fairly complex manual Microsoft App configuration, we recommend the One-Click Setup, which will get you up and running in just a few seconds.

Read our [Outlook and Microsoft 365 documentation](https://wpmailsmtp.com/docs/how-to-set-up-the-outlook-mailer-in-wp-mail-smtp/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) for more details.

**Amazon SES SMTP**

Advanced or technical users can harness the power of Amazon AWS (Amazon Web Services) with the Amazon SES mailer. With this integration, you can send a high volume of emails at a very reasonable rate.

Read our [Amazon SES documentation](https://wpmailsmtp.com/docs/how-to-set-up-the-amazon-ses-mailer-in-wp-mail-smtp/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) for more details.

**Zoho Mail SMTP**

Send emails using your personal or business Zoho Mail account, all while keeping your login credentials safe.

Read our [Zoho Mail documentation](https://wpmailsmtp.com/docs/how-to-set-up-the-zoho-mailer-in-wp-mail-smtp/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) for more details.

**Other SMTP**

WP Mail SMTP plugin also works with all major email services such as Gmail, Yahoo, Outlook, Microsoft Live, and any other email sending service that offers SMTP.

You can set the following options:

* Specify an SMTP host.
* Specify an SMTP port.
* Choose SSL / TLS encryption.
* Choose to use SMTP authentication or not.
* Specify an SMTP username and password.

To see recommended settings for the popular services as well as troubleshooting tips, check out our [SMTP documentation](https://wpmailsmtp.com/docs/how-to-set-up-the-other-smtp-mailer-in-wp-mail-smtp/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme).

= Can I use this plugin to send email via Gmail, G Suite, Outlook.com, Office 365, Hotmail, Yahoo, or AOL SMTP? =

Yes! We have extensive documentation that covers setting up SMTP most popular email services.

[Read our docs](https://wpmailsmtp.com/docs/a-complete-guide-to-wp-mail-smtp-mailers/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin&utm_content=readme) to see the correct SMTP settings for each service.

= Help! I need support or have an issue. =

Please read [our support policy](https://wordpress.org/support/topic/wp-mail-smtp-support-policy/) for more information.

Limited support is available for WP Mail SMTP users via WordPress.org support forums.

Email support and set up assistance is available to WP Mail SMTP Pro users.

= How can I migrate from a different SMTP plugin to WP Mail SMTP? =

Want to switch from your old SMTP plugin to WP Mail SMTP? We made it easy for you to migrate your SMTP settings in one click!

WP Mail SMTP will automatically detect your existing SMTP plugin (Easy WP SMTP, FluentSMTP, etc) when you run our easy Setup Wizard. Just click Import to copy your settings over.

Our one-click migration tool supports all of these plugins:

- Easy WP SMTP
- FluentSMTP
- Post SMTP Mailer
- SMTP Mailer
- WP SMTP

= Is WP Mail SMTP available in other languages? =

We know that majority of people do not speak English, so we professionally translated WP Mail SMTP and WP Mail SMTP Pro into the following languages:

- Spanish (Spain),
- German,
- Portuguese (Brazil),
- Italian,
- French,
- Japanese,
- Polish,
- Dutch,
- Russian,
- Turkish

= How can I increase plugin security? =

The WP Mail SMTP team takes security very seriously. Not only does the plugin follow all security best practices, but we have several options available to ensure your site is safe and secure.

- Direct SMTP mailer integrations (recommended), such as SendLayer, SMTP.com, Brevo (formerly Sendinblue), Mailgun, SendGrid, Postmark, SparkPost and SMTP2GO, use the official provider APIs. This means you never enter your username or password in the plugin settings and these credentials are not stored in the database. Instead, we use tokens or API keys which are much more secure.

- When using Other SMTP mailer, we provide the option to insert your password in your `wp-config.php` file, so it's not visible in your WordPress settings or saved in the database.

= I found a bug, now what? =

If you've stumbled upon a bug, the best place to report it is in the [WP Mail SMTP GitHub repository](https://github.com/awesomemotive/wp-mail-smtp). GitHub is where the plugin is actively developed, and posting there will get your issue quickly seen by our developers (myself and Slava). Once posted, we'll review your bug report and triage the bug. When creating an issue, the more details you can add to your report, the faster the bug can be solved.

= Can you add feature x, y or z to the plugin? =

Short answer: maybe.

By all means please contact us to discuss features or options you'd like to see added to the plugin. We can't guarantee to add all of them, but we will consider all sensible requests. We can be contacted here:
[https://wpmailsmtp.com/contact/](https://wpmailsmtp.com/contact/).

== Screenshots ==

1. WP Mail SMTP Settings page
2. List of mailers with Other SMTP settings example
3. Backup Connection (Pro)
4. Setup Wizard - Select your mailer
5. Setup Wizard - Example mailer settings
6. Email Test page
7. Email Log settings page (Pro)
8. Email Controls settings page (Pro)
9. Email Log archive page (Pro)
10. Email Log single page (Pro)
11. Email Reports - Email Log statistics grouped by email subject (Pro)
12. Email Log bulk Export (Pro)
13. Email Alerts - Get notified about failed emails (Pro)
14. Additional Connections - List of connections (Pro)
15. Additional Connections - Configuration page (Pro)
16. Smart Routing - Conditional logic for email sending (Pro)

== Changelog ==

= 4.4.0 - 2025-03-05 =
- Fixed: Emails queue runner Action Scheduler task deadlock issue.
- Fixed: Undefined array key "wp_mail_smtp_reports_widget_lite" warning in the dashboard widget.

= 4.3.0 - 2024-12-11 =
- Added: New transactional mailer: Elastic Email integration.
- Changed: The "Tools -> Scheduled Actions" menu is now always visible when WooCommerce or the Action Scheduler plugin is active.
- Fixed: SMTP password and username fields ignored `WPMS_SMTP_AUTH` constant.

= 4.2.0 - 2024-11-06 =
- Added: New transactional mailer: Mailjet integration.
- Changed: Improved security for sensitive data (API keys).
- Fixed: SMTP2GO mailer special characters handling in from name.

= 4.1.1 - 2024-08-15 =
- Changed: Delete `wp-mail-smtp` uploads folder on plugin uninstall, if the "Misc > Uninstall" option is enabled.
- Changed: Decreased `PHPMailer` timeout value to 30 seconds from 5 minutes.
- Changed: Improved Weekly Summary Email sending.
- Fixed: Missing Reply-To support in SMTP2GO mailer.
- Fixed: Setup Wizard translations not working correctly.

= 4.1.0 - 2024-07-17 =
- Added: New transactional mailer: SMTP2GO.
- Changed: Recurring email queue tasks are now removed after completion.
- Fixed: Wrong namespace in PHP 8.0x Symfony polyfills.
- Fixed: All pending background tasks are now canceled on plugin deactivation.

= 4.0.1 - 2024-02-29 =
- Added: Optimized Email Sending - move email sending requests in the background process and speed up your site.
- Added: Automatic database table structure migrations after plugin update.
- Changed: Improved error handling when sending emails.
- Fixed: Database error while adding debug events if the `wpmailsmtp_debug_events` table does not exist.

= 3.11.1 - 2024-01-22 =
- Fixed: Setup Wizard texts.
- Fixed: Compatibility for List-Unsubscribe header.

= 3.11.0 - 2023-12-13 =
- Added: Filter to customize the capability required for managing the plugin.
- Changed: Hide test tab movement notice for new users.
- Changed: Improved keyboard navigation styles for the Setup Wizard.
- Changed: Removed `WPMailSMTP\Admin\PluginsInstallUpgrader` class and switched to the WordPress Core `Plugin_Upgrader` class.
- Changed: The "From email" dropdown to the input field in the Gmail mailer.
- Fixed: PHP deprecation notices in the Setup Wizard on WordPress 6.4 and above.
- Fixed: Compatibility issue with Action Scheduler lower than 3.3.0.

= 3.10.0 - 2023-11-08 =
- Added: Filter that allows to use your website's URL for Google OAuth redirect URL.
- Changed: Improve plugin settings UI, by changing checkboxes to toggles and some dividers cleanup.
- Changed: Replaced moment.js library to the WP Core's bundled one.
- Fixed: Translation strings on the Dashboard widget.

= 3.9.0 - 2023-08-30 =
- Changed: Moved the Email Test tab from the settings page to the tools page.
- Changed: Removed Sendinblue SDK library because it was deprecated.
- Changed: Mailgun API instructions.
- Fixed: Debug Event details popup scrolling.
- Fixed: Conflict with other plugins (Alt Manager) that made the WP Plugins install page unusable.

= 3.8.2 - 2023-07-20 =
- Changed: Improved notifications formatting and styles.
- Changed: Sendinblue rebranded to Brevo.
- Fixed: Explicitly set "Content-Type" header for the HTML test email.

= 3.8.0 - 2023-04-26 =
- IMPORTANT: Support for PHP 5.6, 7.0, and 7.1 has been discontinued. If you are running one of those versions, you MUST upgrade PHP before installing or upgrading to WP Mail SMTP v3.8. Failure to do that will disable WP Mail SMTP functionality.
- Changed: Updated Moment.js library to 2.29.4.
- Changed: Removed unneeded sodium_compat library.
- Fixed: Email address with apostrophes in the Email Test page.
- Fixed: Review request notice display on subsites admin area in WP Multisite installation.
- Fixed: Setup Wizard playing UA anthem for certain WP sites.

= 3.7.0 - 2022-12-15 =
- Changed: Improved Action Scheduler data cleanup on plugin uninstall.
- Changed: Improved performance for database table validation checks.
- Fixed: Tasks meta database table error.
- Fixed: Gmail mailer authorization error if the oAuth app already had other non mail scopes attached.
- Fixed: Email address validation in Setup wizard.
- Fixed: Removed unneeded composer libraries autoload code.
- Fixed: Conflict detection for plugin Sendinblue - WooCommerce Email Marketing (v3.0+)

= 3.6.1 - 2022-10-06 =
- Added: The `wp_mail` function call backtrace to the Debug Events if the "Debug Email Sending" option is enabled.
- Added: Plugin's DB tables re-creation process in WP Site Health.
- Added: Debug Events retention period setting.
- Changed: Updated the list of conflicting plugins (added Zoho Mail).
- Changed: Improved conflicting plugins' admin notices (display multiple at once)
- Changed: Switched to the WP Core function `is_email` for verifying email addresses.
- Changed: Improved the detection if `wp_mail` function is overwritten.
- Fixed: Gmail mailer not using the correct From Email Address in Domain Checker.
- Fixed: Setup Wizard steps navigation, when going backwards.

= 3.5.2 - 2022-08-17 =
- Fixed: The check if `wp_mail` function is overwritten on Windows servers.

= 3.5.1 - 2022-07-14 =
- Changed: Removed MailPoet from the list of conflicting plugins.
- Fixed: PHP warning for undefined variable when using the Default (none) mailer.

= 3.5.0 - 2022-07-14 =
- Added: Check if `wp_mail` function is overwritten.
- Added: DB table (`wpmailsmtp_tasks_meta`) cleanup after scheduled actions execution. Keeps DB size small.
- Changed: Updated the list of conflicting plugins (added Branda and MailPoet).
- Changed: Updated Action Scheduler library to 3.4.2.
- Fixed: SMTP.com mailer email content-encoding.
- Fixed: Dashboard widget graph when there is no email logs data.
- Fixed: Missing Sendinblue email body WP filter.
- Fixed: Chart.js library conflicts with other plugins.

= 3.4.0 - 2022-04-27 =
- Added: New transactional mailer: SendLayer integration.
- Changed: Improved Mailgun API error message extraction.
- Changed: Standardized error messages format and improved WP remote request errors extraction.
- Fixed: Lite plugin uninstall actions clearing plugin options while Pro version is active.
- Fixed: Hiding unrelated network admin notices on WP Mail SMTP pages.

= 3.3.0 - 2022-02-17 =
- IMPORTANT: Support for WordPress versions 5.1.x or lower has been discontinued. If you are using one of those versions, you MUST upgrade WordPress before installing or upgrading to WP Mail SMTP v3.3. Failure to do that will disable WP Mail SMTP functionality.
- Added: PHP 8.1 compatibility.
- Changed: Updated the list of conflicting plugins (added FluentSMTP and WP HTML Mail).
- Changed: Improved debug error message for the Other SMTP mailer in Debug Events.
- Changed: Updated Action Scheduler library to 3.4.0.
- Changed: Improved Action Scheduler performance.
- Fixed: PHP deprecated notices in Sendinblue library (PHP 7.4+).
- Fixed: DB tables row in Site Health Info section is now private.
- Fixed: Debug Events' screen options visible on general Tools page.
- Fixed: Screen Options right alignment.

= 3.2.1 - 2021-11-17 =
- Fixed: PHP 8 compatibility when existing Gmail mailer connection is revoked.

= 3.2.0 - 2021-11-11 =
- Added: New transactional mailer - SparkPost integration.
- Added: One-click migration from FluentSMTP plugin.
- Added: Plugin constants integration in Setup Wizard.
- Fixed: Early plugin deactivation issue with activity log plugins.

= 3.1.0 - 2021-09-28 =
- Added: New transactional mailer - Postmark integration.
- Added: Support for string attachments (added via PHPMailer object).
- Changed: Improved Email Source detection in Debug Events for WP Core sent emails.
- Changed: Improved uninstall process. It now removes all plugin DB data and tables.
- Fixed: Email Source detection in Debug Events for file paths with backslashes.
- Fixed: Blurry image assets in Weekly Email Summary.
- Fixed: PHP extension mb_strings not polyfilled correctly.
- Fixed: Added missing is_email_sent filters for Sendinblue, Mailgun, and Gmail mailers.
- Fixed: Debug Events double-entry DB save, because of a bug in is_email_sent method for certain mailers.

= 3.0.3 - 2021-08-09 =
- Fixed: Weekly Summary Email sending when migration code didn't trigger yet.

= 3.0.2 - 2021-08-05 =
- Fixed: Fatal PHP error on WP version 5.2 and lower (missing wp_timezone function).

= 3.0.1 - 2021-08-05 =
- Added: Weekly Email Summary - email sending statistics sent to your inbox.
- Added: Debug Events - logging all email sending errors and debug events.
- Added: Quick admin area links.
- Changed: Updated the successful Email Test screen.
- Changed: Updated Action Scheduler library to 3.2.1.
- Fixed: WP core admin spinner for the dashboard widget.
- Fixed: PHP error when objects implementing `__invoke()` method were used as hook callbacks for admin notices.
