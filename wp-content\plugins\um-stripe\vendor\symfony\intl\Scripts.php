<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace UM_Stripe\Vendor\Symfony\Intl;

use UM_Stripe\Vendor\Symfony\Intl\Exception\MissingResourceException;
/**
 * Gives access to script-related ICU data.
 *
 * <AUTHOR> <b<PERSON><PERSON><PERSON>@gmail.com>
 * <AUTHOR> <<EMAIL>>
 */
final class Scripts extends ResourceBundle
{
    /**
     * @return string[]
     */
    public static function getScriptCodes(): array
    {
        return self::readEntry(['Scripts'], 'meta');
    }
    public static function exists(string $script): bool
    {
        try {
            self::readEntry(['Names', $script]);
            return \true;
        } catch (MissingResourceException $e) {
            return \false;
        }
    }
    /**
     * @throws MissingResourceException if the script code does not exist
     */
    public static function getName(string $script, ?string $displayLocale = null): string
    {
        return self::readEntry(['Names', $script], $displayLocale);
    }
    /**
     * @return string[]
     */
    public static function getNames(?string $displayLocale = null): array
    {
        return self::asort(self::readEntry(['Names'], $displayLocale), $displayLocale);
    }
    protected static function getPath(): string
    {
        return Intl::getDataDirectory() . '/' . Intl::SCRIPT_DIR;
    }
}
