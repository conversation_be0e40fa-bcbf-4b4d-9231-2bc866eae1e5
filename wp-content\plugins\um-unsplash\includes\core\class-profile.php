<?php
namespace um_ext\um_unsplash\core;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Profile
 * @package um_ext\um_unsplash\core
 */
class Profile {

	/**
	 * Profile constructor.
	 */
	public function __construct() {
		add_filter( 'um_cover_area_content_dropdown_items', array( &$this, 'um_cover_area_content_dropdown_items' ) );
		add_action( 'wp_footer', array( &$this, 'modal_area' ) );

		add_action( 'um_after_remove_cover_photo', array( &$this, 'remove_unsplash_cover_pic' ) );
		add_action( 'um_after_upload_db_meta_cover_photo', array( &$this, 'remove_unsplash_cover_pic' ) );

		add_filter( 'um_user_cover_photo_uri__filter', array( &$this, 'um_user_cover_photo_uri__filter' ), 10, 3 );
	}

	/**
	 * Add "Select from Unsplash" item to cover image dropdown.
	 *
	 * @param array $items
	 *
	 * @return array
	 */
	public function um_cover_area_content_dropdown_items( $items ) {
		$arr   = array();
		$arr[] = '<a href="javascript:void(0);" class="um-unsplash-trigger">' . esc_html__( 'Select from Unsplash', 'um-unsplash' ) . '</a>';
		return array_merge( $arr, $items );
	}

	/**
	 * Load modal window on profile form for select Unsplash image.
	 */
	public function modal_area() {
		if ( um_is_core_page( 'user' ) && um_get_requested_user() ) {
			UM()->get_template( 'modal.php', um_unsplash_plugin, array(), true );
		}
	}

	/**
	 * Delete Unsplash photo when remove cover photo action is triggered.
	 * Delete Unsplash photo when regular upload cover photo.
	 *
	 * @param int $user_id
	 */
	public function remove_unsplash_cover_pic( $user_id ) {
		delete_user_meta( $user_id, '_um_unsplash_cover' );
		delete_user_meta( $user_id, '_um_unsplash_photo_author' );
	}

	/**
	 * Change cover photo url to display unsplash photo covers.
	 *
	 * @param string     $cover_uri
	 * @param bool       $is_default
	 * @param string|int $attrs
	 *
	 * @return string
	 */
	public function um_user_cover_photo_uri__filter( $cover_uri, $is_default, $attrs ) {
		$profile_id     = UM()->user()->id;
		$unsplash_image = get_user_meta( $profile_id, '_um_unsplash_cover', true );

		if ( $unsplash_image ) {
			$cover_uri = $unsplash_image;

			// phpcs:ignore WordPress.Security.NonceVerification -- already verified in `um_unsplash_save_cover_photo()`
			if ( ! um_is_core_page( 'user' ) && ! ( wp_doing_ajax() && isset( $_REQUEST['action'] ) && 'um_unsplash_update' === $_REQUEST['action'] ) ) {
				$percent_reduction = ( absint( $attrs ) / 1000 ) * 100;
				$new_height        = ( $percent_reduction / 100 ) * 370;

				$cover_uri = str_replace(
					array(
						'w=1000',
						'h=370',
					),
					array(
						'w=' . $attrs,
						'h=' . ceil( $new_height ),
					),
					$cover_uri
				);
			}
		}

		return $cover_uri;
	}
}
