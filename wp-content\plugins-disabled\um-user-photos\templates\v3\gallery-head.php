<?php
/**
 * Template for the UM User Photos, The "New Album" button
 *
 * Page: "Profile", tab "Photos"
 * Hook: 'ultimatemember_gallery'
 * Caller: User_Photos_Shortcodes->get_gallery_content() method
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/gallery-head.php
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

$svg_html = '<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-plus" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="#2c3e50" fill="none" stroke-linecap="round" stroke-linejoin="round">
				<path stroke="none" d="M0 0h24v24H0z" fill="none"/>
				<path d="M12 5l0 14" />
				<path d="M5 12l14 0" />
			</svg>';
echo wp_kses(
	UM()->frontend()::layouts()::button(
		esc_html__( 'New Album', 'um-user-photos' ),
		array(
			'type'          => 'button',
			'icon_position' => 'leading',
			'icon'          => $svg_html,
			'size'          => 's',
			'classes'       => array(
				'um-user-photos-new-album',
			),
			'data'          => array(
				'nonce' => wp_create_nonce( 'um_get_new_album_form' ),
			),
		)
	),
	UM()->get_allowed_html( 'templates' )
);
