# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [3.0.9] - 2025-04-28
### Changed
- Internal updates.

## [3.0.8] - 2025-03-21
### Changed
- Internal updates.

## [3.0.7] - 2025-03-12
### Changed
- Internal updates.

## [3.0.6] - 2025-03-05
### Changed
- Internal updates.

## [3.0.5] - 2025-02-24
### Changed
- Update dependencies.

## [3.0.4] - 2025-02-03
### Changed
- Internal updates.

## [3.0.3] - 2025-01-20
### Changed
- Code: Use function-style exit() and die() with a default status code of 0. [#41167]

## [3.0.2] - 2024-12-02
### Changed
- Exclude revoked licenses from results when filtering unattached licenses in `Licensing::get_user_licenses()`. Only unattached and valid licenses are now returned. [#40215]

## [3.0.1] - 2024-11-25
### Changed
- Updated dependencies. [#40286]

## [3.0.0] - 2024-11-14
### Removed
- General: Update minimum PHP version to 7.2. [#40147]

## [2.0.12] - 2024-11-04
### Added
- Enable test coverage. [#39961]

## [2.0.11] - 2024-10-29
### Changed
- Internal updates.

## [2.0.10] - 2024-09-23
### Changed
- Update dependencies.

## [2.0.9] - 2024-09-05
### Changed
- Update dependencies.

## [2.0.8] - 2024-09-05
### Changed
- Update dependencies.

## [2.0.7] - 2024-08-26
### Changed
- Updated package dependencies. [#39004]

## [2.0.6] - 2024-08-19
### Changed
- Internal updates.

## [2.0.5] - 2024-04-22
### Changed
- Internal updates.

## [2.0.4] - 2024-04-08
### Changed
- Internal updates.

## [2.0.3] - 2024-03-25
### Changed
- Internal updates.

## [2.0.2] - 2024-03-14
### Changed
- Internal updates.

## [2.0.1] - 2023-11-21

## [2.0.0] - 2023-11-20
### Changed
- Updated required PHP version to >= 7.0. [#34192]

## [1.8.4] - 2023-09-19

- Minor internal updates.

## [1.8.3] - 2023-08-23
### Changed
- Updated package dependencies. [#32605]

## [1.8.2] - 2023-04-10
### Added
- Add Jetpack Autoloader package suggestion. [#29988]

## [1.8.1] - 2023-02-20
### Changed
- Minor internal updates.

## [1.8.0] - 2023-02-08
### Added
- After connection flow, load unattached licenses. If any of them match the product that's being connected, redirect users to the license activation page. [#28509]

## [1.7.14] - 2023-01-11
### Changed
- Updated package dependencies.

## [1.7.13] - 2022-12-02
### Changed
- Updated package dependencies. [#27688]

## [1.7.12] - 2022-11-22
### Changed
- Updated package dependencies. [#27043]

## [1.7.11] - 2022-10-25
### Changed
- Updated package dependencies. [#26705]

## [1.7.10] - 2022-09-20
### Changed
- Updated package dependencies.

## [1.7.9] - 2022-09-08
### Changed
- Updated package dependencies.

## [1.7.8] - 2022-08-29
### Changed
- Updated package dependencies.

## [1.7.7] - 2022-08-23
### Changed
- Updated package dependencies. [#25628]

## [1.7.6] - 2022-08-03
### Changed
- Updated package dependencies. [#25300, #25315]

## [1.7.5] - 2022-07-26
### Changed
- Updated package dependencies. [#25158]

## [1.7.4] - 2022-06-21
### Changed
- Renaming master to trunk.

## [1.7.3] - 2022-06-14
### Changed
- Updated package dependencies. [#24529]

## [1.7.2] - 2022-05-04
### Changed
- Updated package dependencies. [#24095]

### Deprecated
- Moved the options class into Connection. [#24095]

## [1.7.1] - 2022-04-26
### Changed
- Updated package dependencies.

## [1.7.0] - 2022-04-19
### Changed
- Moved licensing endpoints from the Jetpack plugin to the Licensing package

## [1.6.4] - 2022-04-12
### Changed
- Updated package dependencies.

## [1.6.3] - 2022-03-02
### Changed
- Updated package dependencies.

## [1.6.2] - 2022-01-25
### Changed
- Updated package dependencies.

## [1.6.1] - 2022-01-18
### Changed
- Updated package dependencies.

## [1.6.0] - 2022-01-04
### Changed
- Switch to pcov for code coverage.
- Updated package dependencies
- Updated package textdomain from `jetpack` to `jetpack-licensing`.

## [1.5.4] - 2021-12-14
### Changed
- Updated package dependencies.

## [1.5.3] - 2021-12-03
### Changed
- Increases the timeout of the license activation request from 10 to 30 seconds.

## [1.5.2] - 2021-11-30
### Changed
- Updated package dependencies.

## [1.5.1] - 2021-11-23
### Changed
- Updated package dependencies.

## [1.5.0] - 2021-11-16
### Added
- Add a test for update to WPCOM return change.
- Added get_license_activation_notice_dismiss() function.

## [1.4.9] - 2021-11-02
### Changed
- Set `convertDeprecationsToExceptions` true in PHPUnit config.
- Update PHPUnit configs to include just what needs coverage rather than include everything then try to exclude stuff that doesn't.

## [1.4.8] - 2021-10-19
### Changed
- Updated package dependencies.

## [1.4.7] - 2021-10-12
### Changed
- Updated package dependencies

## [1.4.6] - 2021-09-28
### Changed
- Updated package dependencies.

## [1.4.5] - 2021-08-31
### Changed
- Run composer update on test-php command instead of phpunit.
- Tests: update PHPUnit polyfills dependency (yoast/phpunit-polyfills).
- Updated versions in annotations.

## [1.4.4] - 2021-07-27
### Changed
- Updated package dependencies.

## [1.4.3] - 2021-06-29
### Changed
- Updated package dependencies.

## [1.4.2] - 2021-05-25
### Changed
- Updated package dependencies.

## [1.4.1] - 2021-04-27
### Changed
- Updated package dependencies.

## [1.4.0] - 2021-03-30
### Added
- Composer alias for dev-master, to improve dependencies
- Dashboard: add new option to input license key.

### Changed
- Replace usage of deprecated is_active method
- Update package dependencies.

### Fixed
- Fix stored licenses not being attached on option creation
- Use `composer update` rather than `install` in scripts, as composer.lock isn't checked in.

## [1.3.4] - 2021-02-23

- CI: Make tests more generic
- codesniffer: Hack around mediawiki-codesniffer bug

## [1.3.3] - 2021-02-08

- Update dependencies to latest stable

## [1.3.2] - 2021-01-28

- Update dependencies to latest stable

## [1.3.1] - 2021-01-26

- Add mirror-repo information to all current composer packages
- Monorepo: Reorganize all projects

## [1.3.0] - 2021-01-05

- Pin dependencies
- Packages: Update for PHP 8 testing

## [1.2.4] - 2020-11-24

- Version packages for release

## [1.2.3] - 2020-11-24

- Updated PHPCS: Packages and Debugger

## [1.2.2] - 2020-11-05

- Update dependencies to latest stable

## [1.2.1] - 2020-10-29

- Update dependencies to latest stable

## [1.2.0] - 2020-10-27

- Licensing: use Oxford comma in error message

## [1.1.4] - 2020-10-14

- Update dependencies to latest stable

## [1.1.3] - 2020-10-09

- Update dependencies to latest stable

## [1.1.2] - 2020-10-06

- Update dependencies to latest stable

## [1.1.1] - 2020-10-01

- Update dependencies to latest stable

## [1.1.0] - 2020-09-29

- Update dependencies to latest stable

## 1.0.0 - 2020-09-24

- Licensing: Add support for Jetpack licenses

[3.0.9]: https://github.com/Automattic/jetpack-licensing/compare/v3.0.8...v3.0.9
[3.0.8]: https://github.com/Automattic/jetpack-licensing/compare/v3.0.7...v3.0.8
[3.0.7]: https://github.com/Automattic/jetpack-licensing/compare/v3.0.6...v3.0.7
[3.0.6]: https://github.com/Automattic/jetpack-licensing/compare/v3.0.5...v3.0.6
[3.0.5]: https://github.com/Automattic/jetpack-licensing/compare/v3.0.4...v3.0.5
[3.0.4]: https://github.com/Automattic/jetpack-licensing/compare/v3.0.3...v3.0.4
[3.0.3]: https://github.com/Automattic/jetpack-licensing/compare/v3.0.2...v3.0.3
[3.0.2]: https://github.com/Automattic/jetpack-licensing/compare/v3.0.1...v3.0.2
[3.0.1]: https://github.com/Automattic/jetpack-licensing/compare/v3.0.0...v3.0.1
[3.0.0]: https://github.com/Automattic/jetpack-licensing/compare/v2.0.12...v3.0.0
[2.0.12]: https://github.com/Automattic/jetpack-licensing/compare/v2.0.11...v2.0.12
[2.0.11]: https://github.com/Automattic/jetpack-licensing/compare/v2.0.10...v2.0.11
[2.0.10]: https://github.com/Automattic/jetpack-licensing/compare/v2.0.9...v2.0.10
[2.0.9]: https://github.com/Automattic/jetpack-licensing/compare/v2.0.8...v2.0.9
[2.0.8]: https://github.com/Automattic/jetpack-licensing/compare/v2.0.7...v2.0.8
[2.0.7]: https://github.com/Automattic/jetpack-licensing/compare/v2.0.6...v2.0.7
[2.0.6]: https://github.com/Automattic/jetpack-licensing/compare/v2.0.5...v2.0.6
[2.0.5]: https://github.com/Automattic/jetpack-licensing/compare/v2.0.4...v2.0.5
[2.0.4]: https://github.com/Automattic/jetpack-licensing/compare/v2.0.3...v2.0.4
[2.0.3]: https://github.com/Automattic/jetpack-licensing/compare/v2.0.2...v2.0.3
[2.0.2]: https://github.com/Automattic/jetpack-licensing/compare/v2.0.1...v2.0.2
[2.0.1]: https://github.com/Automattic/jetpack-licensing/compare/v2.0.0...v2.0.1
[2.0.0]: https://github.com/Automattic/jetpack-licensing/compare/v1.8.4...v2.0.0
[1.8.4]: https://github.com/Automattic/jetpack-licensing/compare/v1.8.3...v1.8.4
[1.8.3]: https://github.com/Automattic/jetpack-licensing/compare/v1.8.2...v1.8.3
[1.8.2]: https://github.com/Automattic/jetpack-licensing/compare/v1.8.1...v1.8.2
[1.8.1]: https://github.com/Automattic/jetpack-licensing/compare/v1.8.0...v1.8.1
[1.8.0]: https://github.com/Automattic/jetpack-licensing/compare/v1.7.14...v1.8.0
[1.7.14]: https://github.com/Automattic/jetpack-licensing/compare/v1.7.13...v1.7.14
[1.7.13]: https://github.com/Automattic/jetpack-licensing/compare/v1.7.12...v1.7.13
[1.7.12]: https://github.com/Automattic/jetpack-licensing/compare/v1.7.11...v1.7.12
[1.7.11]: https://github.com/Automattic/jetpack-licensing/compare/v1.7.10...v1.7.11
[1.7.10]: https://github.com/Automattic/jetpack-licensing/compare/v1.7.9...v1.7.10
[1.7.9]: https://github.com/Automattic/jetpack-licensing/compare/v1.7.8...v1.7.9
[1.7.8]: https://github.com/Automattic/jetpack-licensing/compare/v1.7.7...v1.7.8
[1.7.7]: https://github.com/Automattic/jetpack-licensing/compare/v1.7.6...v1.7.7
[1.7.6]: https://github.com/Automattic/jetpack-licensing/compare/v1.7.5...v1.7.6
[1.7.5]: https://github.com/Automattic/jetpack-licensing/compare/v1.7.4...v1.7.5
[1.7.4]: https://github.com/Automattic/jetpack-licensing/compare/v1.7.3...v1.7.4
[1.7.3]: https://github.com/Automattic/jetpack-licensing/compare/v1.7.2...v1.7.3
[1.7.2]: https://github.com/Automattic/jetpack-licensing/compare/v1.7.1...v1.7.2
[1.7.1]: https://github.com/Automattic/jetpack-licensing/compare/v1.7.0...v1.7.1
[1.7.0]: https://github.com/Automattic/jetpack-licensing/compare/v1.6.4...v1.7.0
[1.6.4]: https://github.com/Automattic/jetpack-licensing/compare/v1.6.3...v1.6.4
[1.6.3]: https://github.com/Automattic/jetpack-licensing/compare/v1.6.2...v1.6.3
[1.6.2]: https://github.com/Automattic/jetpack-licensing/compare/v1.6.1...v1.6.2
[1.6.1]: https://github.com/Automattic/jetpack-licensing/compare/v1.6.0...v1.6.1
[1.6.0]: https://github.com/Automattic/jetpack-licensing/compare/v1.5.4...v1.6.0
[1.5.4]: https://github.com/Automattic/jetpack-licensing/compare/v1.5.3...v1.5.4
[1.5.3]: https://github.com/Automattic/jetpack-licensing/compare/v1.5.2...v1.5.3
[1.5.2]: https://github.com/Automattic/jetpack-licensing/compare/v1.5.1...v1.5.2
[1.5.1]: https://github.com/Automattic/jetpack-licensing/compare/v1.5.0...v1.5.1
[1.5.0]: https://github.com/Automattic/jetpack-licensing/compare/v1.4.9...v1.5.0
[1.4.9]: https://github.com/Automattic/jetpack-licensing/compare/v1.4.8...v1.4.9
[1.4.8]: https://github.com/Automattic/jetpack-licensing/compare/v1.4.7...v1.4.8
[1.4.7]: https://github.com/Automattic/jetpack-licensing/compare/v1.4.6...v1.4.7
[1.4.6]: https://github.com/Automattic/jetpack-licensing/compare/v1.4.5...v1.4.6
[1.4.5]: https://github.com/Automattic/jetpack-licensing/compare/v1.4.4...v1.4.5
[1.4.4]: https://github.com/Automattic/jetpack-licensing/compare/v1.4.3...v1.4.4
[1.4.3]: https://github.com/Automattic/jetpack-licensing/compare/v1.4.2...v1.4.3
[1.4.2]: https://github.com/Automattic/jetpack-licensing/compare/v1.4.1...v1.4.2
[1.4.1]: https://github.com/Automattic/jetpack-licensing/compare/v1.4.0...v1.4.1
[1.4.0]: https://github.com/Automattic/jetpack-licensing/compare/v1.3.4...v1.4.0
[1.3.4]: https://github.com/Automattic/jetpack-licensing/compare/v1.3.3...v1.3.4
[1.3.3]: https://github.com/Automattic/jetpack-licensing/compare/v1.3.2...v1.3.3
[1.3.2]: https://github.com/Automattic/jetpack-licensing/compare/v1.3.1...v1.3.2
[1.3.1]: https://github.com/Automattic/jetpack-licensing/compare/v1.3.0...v1.3.1
[1.3.0]: https://github.com/Automattic/jetpack-licensing/compare/v1.2.4...v1.3.0
[1.2.4]: https://github.com/Automattic/jetpack-licensing/compare/v1.2.3...v1.2.4
[1.2.3]: https://github.com/Automattic/jetpack-licensing/compare/v1.2.2...v1.2.3
[1.2.2]: https://github.com/Automattic/jetpack-licensing/compare/v1.2.1...v1.2.2
[1.2.1]: https://github.com/Automattic/jetpack-licensing/compare/v1.2.0...v1.2.1
[1.2.0]: https://github.com/Automattic/jetpack-licensing/compare/v1.1.4...v1.2.0
[1.1.4]: https://github.com/Automattic/jetpack-licensing/compare/v1.1.3...v1.1.4
[1.1.3]: https://github.com/Automattic/jetpack-licensing/compare/v1.1.2...v1.1.3
[1.1.2]: https://github.com/Automattic/jetpack-licensing/compare/v1.1.1...v1.1.2
[1.1.1]: https://github.com/Automattic/jetpack-licensing/compare/v1.1.0...v1.1.1
[1.1.0]: https://github.com/Automattic/jetpack-licensing/compare/v1.0.0...v1.1.0
