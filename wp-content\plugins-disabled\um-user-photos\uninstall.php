<?php
/**
 * Uninstall UM User Photos
 *
 */

// Exit if accessed directly.
if ( ! defined( 'WP_UNINSTALL_PLUGIN' ) ) exit;


if ( ! defined( 'UM_USER_PHOTOS_PATH' ) ) {
	define( 'UM_USER_PHOTOS_PATH', plugin_dir_path( __FILE__ ) );
}

if ( ! defined( 'UM_USER_PHOTOS_URL' ) ) {
	define( 'UM_USER_PHOTOS_URL', plugin_dir_url( __FILE__ ) );
}

if ( ! defined( 'UM_USER_PHOTOS_PLUGIN' ) ) {
	define( 'UM_USER_PHOTOS_PLUGIN', plugin_basename( __FILE__ ) );
}

$options = get_option( 'um_options', array() );

if ( ! empty( $options['uninstall_on_delete'] ) ) {
	if ( ! class_exists( 'um_ext\um_user_photos\common\Setup' ) ) {
		require_once UM_USER_PHOTOS_PATH . 'includes/common/class-setup.php';
	}

	$user_photos_setup = new um_ext\um_user_photos\common\Setup();

	//remove settings
	foreach ( $user_photos_setup->settings_defaults as $k => $v ) {
		unset( $options[ $k ] );
	}

	unset( $options['um_user_photos_license_key'] );

	update_option( 'um_options', $options );

	$um_user_photos = get_posts( array(
		'post_type'     => array(
			'um_user_photos'
		),
		'numberposts'   => -1
	) );
	foreach ( $um_user_photos as $um_user_photo ){
		$attachments = get_attached_media( 'image', $um_user_photo->ID );
		foreach ( $attachments as $attachment ) {
			wp_delete_attachment( $attachment->ID, 1 );
		}
		wp_delete_post( $um_user_photo->ID, 1 );
	}

	delete_option( 'um_user_photos_last_version_upgrade' );
	delete_option( 'um_user_photos_version' );
}
