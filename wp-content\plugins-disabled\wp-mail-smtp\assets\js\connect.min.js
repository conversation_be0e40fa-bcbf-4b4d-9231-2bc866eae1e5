"use strict";var WPMailSMTPConnect=window.WPMailSMTPConnect||function(n,e){var c={$connectBtn:e("#wp-mail-smtp-setting-upgrade-license-button"),$connectKey:e("#wp-mail-smtp-setting-upgrade-license-key")},o={init:function(){e(o.ready)},ready:function(){o.events()},events:function(){o.connectBtnClick()},connectBtnClick:function(){c.$connectBtn.on("click",function(){o.gotoUpgradeUrl()})},proAlreadyInstalled:function(t){return{title:wp_mail_smtp_connect.text.almost_done,content:t.data.message,icon:'"></i><img src="'+wp_mail_smtp_connect.plugin_url+'/assets/images/font-awesome/check-circle-solid-green.svg" style="width: 40px; height: 40px;"><i class="',type:"green",buttons:{confirm:{text:wp_mail_smtp_connect.text.plugin_activate_btn,btnClass:"btn-confirm",keys:["enter"],action:function(){n.location.reload()}}}}},gotoUpgradeUrl:function(){var t={action:"wp_mail_smtp_connect_url",key:c.$connectKey.val(),nonce:wp_mail_smtp_connect.nonce};e.post(wp_mail_smtp_connect.ajax_url,t).done(function(t){return t.success?t.data.reload?void e.alert(o.proAlreadyInstalled(t)):void(n.location.href=t.data.url):void e.alert({title:wp_mail_smtp_connect.text.oops,content:t.data.message,icon:'"></i><img src="'+wp_mail_smtp_connect.plugin_url+'/assets/images/font-awesome/exclamation-circle-solid-orange.svg" style="width: 40px; height: 40px;"><i class="',type:"orange",buttons:{confirm:{text:wp_mail_smtp_connect.text.ok,btnClass:"btn-confirm",keys:["enter"]}}})}).fail(function(t){o.failAlert(t)})},failAlert:function(t){e.alert({title:wp_mail_smtp_connect.text.oops,content:wp_mail_smtp_connect.text.server_error+"<br>"+t.status+" "+t.statusText+" "+t.responseText,icon:'"></i><img src="'+wp_mail_smtp_connect.plugin_url+'/assets/images/font-awesome/exclamation-circle-regular-red.svg" style="width: 40px; height: 40px;"><i class="',type:"red",buttons:{confirm:{text:wp_mail_smtp_connect.text.ok,btnClass:"btn-confirm",keys:["enter"]}}})}};return o}((document,window),jQuery);WPMailSMTPConnect.init();