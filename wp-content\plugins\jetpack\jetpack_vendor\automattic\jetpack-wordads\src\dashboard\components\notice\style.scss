@import 'scss/calypso-colors';
@import 'scss/layout';
@import 'scss/calypso-mixins';
@import 'scss/mixin_icons';

.dops-notice {
	display: flex;
	position: relative;
	width: 100%;
	margin-bottom: 24px;
	box-sizing: border-box;
	animation: appear .3s ease-in-out;
	background: $gray-dark;
	color: $white;
	border-radius: 3px;
	line-height: 1.5;

	// Success!
	&.is-success {

		.dops-notice__icon-wrapper {
			background: $alert-green;
		}
	}

	// Warning
	&.is-warning {

		.dops-notice__icon-wrapper {
			background: $alert-yellow;
		}
	}

	// Error! OHNO!
	&.is-error {

		.dops-notice__icon-wrapper {
			background: $alert-red;
		}
	}

	// General notice
	&.is-info {

		.dops-notice__icon-wrapper {
			background: $blue-medium;
		}
	}

	&.is-success,
	&.is-error,
	&.is-warning,
	&.is-info {

		.dops-notice__dismiss {
			overflow: hidden;
		}
	}
}

.dops-notice__icon-wrapper {
	background: $gray-text-min;
	color: $white;
	display: flex;
	align-items: baseline;
	width: 47px;
	justify-content: center;
	border-radius: 3px 0 0 3px;
	flex-shrink: 0;
	align-self: stretch;

	.gridicon {
		margin-top: 10px;

		@include breakpoint( ">480px" ) {
			margin-top: 12px;
		}
	}
}

.dops-notice__content.dops-notice__content {
	padding: 13px;
	font-size: 12px;
	flex-grow: 1;

	@include breakpoint( ">480px" ) {
		font-size: 14px;
	}

	a {
		text-decoration: underline;
		color: $white;
	}

	a:hover {
		text-decoration: none;
	}
}

.dops-notice__text {

	a.dops-notice__text-no-underline {
		text-decoration: none;
	}

	a,
	a:visited {
		text-decoration: underline;
		color: $white;

		&:hover {
			color: $white;
			text-decoration: none;
		}
	}

	ul {
		margin-bottom: 0;
		margin-left: 0;
	}

	li {
		margin-left: 2em;
		margin-top: 0.5em;
	}

	p {
		margin-bottom: 0;
		margin-top: 0.5em;

		&:first-child {
			margin-top: 0;
		}
	}
}

.dops-notice__button {
	cursor: pointer;
	margin-left: 0.428em;
}

// "X" for dismissing a notice
.dops-notice__dismiss {
	flex-shrink: 0;
	padding: 12px;
	cursor: pointer;
	padding-bottom: 0;

	.gridicon {
		width: 18px;
		height: 18px;
	}

	@include breakpoint( ">480px" ) {
		padding: 11px;
		padding-bottom: 0;

		.gridicon {
			width: 24px;
			height: 24px;
		}
	}

	.dops-notice & {
		overflow: hidden;
		color: $gray-lighten-10;

		&:hover,
		&:focus {
			color: $white;
		}
	}
}

// specificity for general `a` elements within notice is too great
a.dops-notice__action {
	cursor: pointer;
	font-size: 12px;
	font-weight: 400;
	text-decoration: none;
	white-space: nowrap;
	color: $gray-lighten-10;
	padding: 13px;
	display: flex;
	align-items: center;

	@include breakpoint( ">480px" ) {
		flex-shrink: 1;
		flex-grow: 0;
		align-items: center;
		border-radius: 0;
		font-size: 14px;
		margin: 0 0 0 auto; // forces the element to the right;
		padding: 13px 16px;

		.gridicon {
			width: 24px;
			height: 24px;
		}
	}

	&:visited {
		color: $gray-lighten-10;
	}

	&:hover {
		color: $white;
	}

	.gridicon {
		margin-left: 8px;
		opacity: 0.7;
		width: 18px;
		height: 18px;
	}
}

// Compact notices
.dops-notice.is-compact {
	display: inline-flex;
	flex-wrap: nowrap;
	flex-direction: row;
	width: auto;
	border-radius: 3px;
	min-height: 20px;
	margin: 0;
	padding: 0;
	text-decoration: none;
	text-transform: none;
	vertical-align: middle;
	line-height: 1.5;

	.dops-notice__content {
		font-size: 12px;
		padding: 6px 10px;
	}

	.dops-notice__icon-wrapper {
		width: 28px;

		.dops-notice__icon {
			width: 18px;
			height: 18px;
			margin: 0;
		}

		.gridicon {
			margin-top: 6px;
		}
	}

	.dops-notice__dismiss {
		position: relative;
		align-self: center;
		flex: none;
		margin: 0 8px 0 0;
		padding: 0;

		.gridicon {
			width: 18px;
			height: 18px;
		}
	}

	a.dops-notice__action {
		background: transparent;
		display: inline-block;
		margin: 0;
		font-size: 12px;
		align-self: center;
		margin-left: 16px;
		padding: 0 10px;

		&:hover,
		&:active,
		&:focus {
			background: transparent;
		}

		.gridicon {
			margin-left: 8px;
			width: 14px;
			height: 14px;
			vertical-align: sub;
			opacity: 1;
		}
	}
}
