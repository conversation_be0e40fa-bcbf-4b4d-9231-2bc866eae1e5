(()=>{var e={6941:(e,t,n)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const n="color: "+this.color;t.splice(1,0,n,"color: inherit");let o=0,r=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(o++,"%c"===e&&(r=o))})),t.splice(r,0,n)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=n(3212)(t);const{formatters:o}=e.exports;o.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},3212:(e,t,n)=>{e.exports=function(e){function t(e){let n,r,s,a=null;function i(...e){if(!i.enabled)return;const o=i,r=Number(new Date),s=r-(n||r);o.diff=s,o.prev=n,o.curr=r,n=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let a=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((n,r)=>{if("%%"===n)return"%";a++;const s=t.formatters[r];if("function"==typeof s){const t=e[a];n=s.call(o,t),e.splice(a,1),a--}return n})),t.formatArgs.call(o,e);(o.log||t.log).apply(o,e)}return i.namespace=e,i.useColors=t.useColors(),i.color=t.selectColor(e),i.extend=o,i.destroy=t.destroy,Object.defineProperty(i,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==a?a:(r!==t.namespaces&&(r=t.namespaces,s=t.enabled(e)),s),set:e=>{a=e}}),"function"==typeof t.init&&t.init(i),i}function o(e,n){const o=t(this.namespace+(void 0===n?":":n)+e);return o.log=this.log,o}function r(e,t){let n=0,o=0,r=-1,s=0;for(;n<e.length;)if(o<t.length&&(t[o]===e[n]||"*"===t[o]))"*"===t[o]?(r=o,s=n,o++):(n++,o++);else{if(-1===r)return!1;o=r+1,s++,n=s}for(;o<t.length&&"*"===t[o];)o++;return o===t.length}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names,...t.skips.map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const n=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of n)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const n of t.skips)if(r(e,n))return!1;for(const n of t.names)if(r(e,n))return!0;return!1},t.humanize=n(7378),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((n=>{t[n]=e[n]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let n=0;for(let t=0;t<e.length;t++)n=(n<<5)-n+e.charCodeAt(t),n|=0;return t.colors[Math.abs(n)%t.colors.length]},t.enable(t.load()),t}},7378:e=>{var t=1e3,n=60*t,o=60*n,r=24*o,s=7*r,a=365.25*r;function i(e,t,n,o){var r=t>=1.5*n;return Math.round(e/n)+" "+o+(r?"s":"")}e.exports=function(e,c){c=c||{};var d=typeof e;if("string"===d&&e.length>0)return function(e){if((e=String(e)).length>100)return;var i=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!i)return;var c=parseFloat(i[1]);switch((i[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return c*a;case"weeks":case"week":case"w":return c*s;case"days":case"day":case"d":return c*r;case"hours":case"hour":case"hrs":case"hr":case"h":return c*o;case"minutes":case"minute":case"mins":case"min":case"m":return c*n;case"seconds":case"second":case"secs":case"sec":case"s":return c*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}(e);if("number"===d&&isFinite(e))return c.long?function(e){var s=Math.abs(e);if(s>=r)return i(e,s,r,"day");if(s>=o)return i(e,s,o,"hour");if(s>=n)return i(e,s,n,"minute");if(s>=t)return i(e,s,t,"second");return e+" ms"}(e):function(e){var s=Math.abs(e);if(s>=r)return Math.round(e/r)+"d";if(s>=o)return Math.round(e/o)+"h";if(s>=n)return Math.round(e/n)+"m";if(s>=t)return Math.round(e/t)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},8025:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var o=n(6941),r=n.n(o),s=n(5468);const a=r()("videopress:get-media-token");const i=async function(e,t={}){const{id:n=0,guid:o=0,flushToken:r}=t,i=`vpc-${e}-${n}-${o}`,c=window?.videopressAjax?.context||"main";let d;const u=localStorage.getItem(i);if(r)a("(%s) Flushing %o token",c,i),localStorage.removeItem(i);else try{if(u){if(d=await JSON.parse(u),d&&d.expire>Date.now())return a("(%s) Providing %o token from the store",c,i),d.data;a("(%s) Token %o expired. Clean.",c,i),localStorage.removeItem(i)}}catch{a("Invalid token in the localStore")}const l=await function(e,t={}){const{id:n=0,guid:o,subscriptionPlanId:r=0,adminAjaxAPI:a,filename:i}=t;return new Promise((function(t,c){const d=a||window.videopressAjax?.ajaxUrl||window?.ajaxurl||"/wp-admin/admin-ajax.php";if(!s.p.includes(e))return c("Invalid scope");const u={action:"videopress-get-playback-jwt"};switch(e){case"upload":u.action="videopress-get-upload-token",i&&(u.filename=i);break;case"upload-jwt":u.action="videopress-get-upload-jwt";break;case"playback":u.action="videopress-get-playback-jwt",u.guid=o,u.post_id=String(n),u.subscription_plan_id=r}fetch(d,{method:"POST",credentials:"same-origin",body:new URLSearchParams(u)}).then((e=>{if(!e.ok)throw new Error("Network response was not ok");return e.json()})).then((n=>{if(!n.success)throw new Error("Token is not achievable");switch(e){case"upload":case"upload-jwt":t({token:n.data.upload_token,blogId:n.data.upload_blog_id,url:n.data.upload_action_url});break;case"playback":t({token:n.data.jwt})}})).catch((()=>{console.warn("Token is not achievable"),t({token:null})}))}))}(e,t);return"playback"===e&&l?.token&&(a("(%s) Storing %o token",c,i),localStorage.setItem(i,JSON.stringify({data:l,expire:Date.now()+864e5}))),a("(%s) Providing %o token from request/response",c,i),l}},5468:(e,t,n)=>{"use strict";n.d(t,{p:()=>o});const o=["upload","playback","upload-jwt"]}},t={};function n(o){var r=t[o];if(void 0!==r)return r.exports;var s=t[o]={exports:{}};return e[o](s,s.exports,n),s.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=n(6941),t=n.n(e),o=n(8025);const r=t()("videopress:token-bridge"),{videopressAjax:s}=window;async function a(e){if(await new Promise((function(e){if("loading"!==document.readyState)return e();document.addEventListener("DOMContentLoaded",(function(){e()}))})),!window.__guidsToPlanIds)return 0;return window.__guidsToPlanIds[e]||0}async function i(e){if("videopress_token_request"!==e.data?.event)return;if(!s)return void r("(%s) videopressAjax is not accesible");const{context:t="main"}=s,{guid:n,requestId:i,isRetry:c}=e.data;if(!n||!i)return void r("(%s) Invalid request",t);const d=window?.videopressAjax.post_id||0,u=await a(n);if(-1===["https://videopress.com","https://video.wordpress.com"].indexOf(e.origin))return void r("(%s) Invalid origin",t);const{source:l}=e;if(l instanceof MessagePort||"undefined"!=typeof ServiceWorker&&l instanceof ServiceWorker)return void r("(%s) Invalid source",t);r("(%s) Token request accepted: %o | %o | %o",t,n,d,i),r("(%s) Send acknowledge receipt requested",t),l.postMessage({event:"videopress_token_request_ack",guid:n,requestId:i},{targetOrigin:"*"}),c&&r("(%s) client retrying request. Flush the token.",t);const f=await(0,o.A)("playback",{id:Number(d),guid:n,subscriptionPlanId:u,adminAjaxAPI:s.ajaxUrl,flushToken:c});if(!f?.token)return r("(%s) Error getting token",t),void l.postMessage({event:"videopress_token_error",guid:e.data.guid,requestId:i},{targetOrigin:"*"});r("(%s) sending token",t),l.postMessage({event:"videopress_token_received",guid:n,jwt:f.token,requestId:i},{targetOrigin:"*"})}s?(r("(%s) 👂 Listen token requester",s?.context||"main"),window.addEventListener("message",i)):r("(%s) videopressAjax is not accesible")})()})();