=flex ($direction, $justify, $align, $wrap)
  display: flex
  flex-direction: $direction
  justify-content: $justify
  align-items: $align
  flex-wrap: $wrap

=border-box
  box-sizing: border-box
  -moz-box-sizing: border-box
  -webkit-box-sizing: border-box

=transition ($time, $action)
  -webkit-transition: $time $action
  -moz-transition: $time $action
  -ms-transition: $time $action
  -o-transition: $time $action
  transition: $time $action


=keyframes ($name)
  @-webkit-keyframes #{$name}
    @content
  @-moz-keyframes #{$name}
    @content
  @-ms-keyframes #{$name}
    @content
  @keyframes #{$name}
    @content

=border-radius ($tl, $tr, $br, $bl)
  border-radius: $tl $tr $br $bl
  -moz-border-radius: $tl $tr $br $bl
  -webkit-border-radius: $tl $tr $br $bl