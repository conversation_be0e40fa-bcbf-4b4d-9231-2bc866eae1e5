window.WPFormsIframe=window.WPFormsIframe||function(i){const a={update(e={}){const t=this;if(!t.classList.contains("wpforms-iframe-updated")){t.classList.add("wpforms-iframe-updated");const r=i.createElement("iframe");r.onload=function(){a.iframeStyles(r,e),a.iframeBody(r,t.innerHTML),a.iframeFullHeight(r),t.remove()},t.after(r)}},iframeStyles(e,t={}){var r=e.contentWindow.document,n=r.querySelector("head"),r=r.createElement("style"),o=getComputedStyle(i.body).fontFamily,{color:t="inherit"}=t;r.setAttribute("type","text/css"),r.innerHTML="body.mce-content-body {\tmargin: 0 !important;\tbackground-color: transparent !important;\tfont-family: "+o+";\tcolor: "+t+";}*:first-child {\tmargin-top: 0}*:last-child {\tmargin-bottom: 0}pre {\twhite-space: pre !important;\toverflow-x: auto !important;}a,img {\tdisplay: inline-block;}",n.appendChild(r),a.entryPreviewAddLinkElement(e)},entryPreviewAddLinkElement(r){if(wpforms_settings.entry_preview_iframe_styles){const n=r.contentWindow.document,o=n.querySelector("head");wpforms_settings.entry_preview_iframe_styles.forEach(function(e){var t=n.createElement("link");t.setAttribute("rel","stylesheet"),t.setAttribute("href",e),t.onload=function(){a.iframeFullHeight(r)},o.appendChild(t)})}},iframeBody(e,t){var e=e.contentWindow.document,r=e.querySelector("body"),e=e.createElement("div");e.classList.add("wpforms-iframe-wrapper"),r.append(e),e.innerHTML=t,r.classList.add("mce-content-body"),r.querySelectorAll("a").forEach(function(e){e.setAttribute("rel","noopener"),e.hasAttribute("target")||e.setAttribute("target","_top")}),r.querySelectorAll("table")?.forEach?.(function(e){e.classList.add("mce-item-table")})},iframeFullHeight(e){var t;e.contentWindow&&e.contentWindow.document&&(t=e.contentWindow.document.querySelector(".wpforms-iframe-wrapper"),e.style.height=t.scrollHeight+"px")}};return a}(document);