{"translation-revision-date": "2024-10-02 16:14:23+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "\"%s\" was removed from your cart.": ["\"%s\" was removed from your basket."], "Polo": ["Polo"], "%s (optional)": ["%s (optional)"], "There was an error registering the payment method with id '%s': ": ["There was an error registering the payment method with ID '%s': "], "Orange": ["Orange"], "Lightweight baseball cap": ["Lightweight baseball cap"], "Cap": ["Cap"], "Yellow": ["Yellow"], "Warm hat for winter": ["Warm hat for winter"], "Beanie": ["<PERSON><PERSON>"], "example product in Cart Block\u0004Beanie": ["<PERSON><PERSON>"], "example product in Cart Block\u0004Beanie with Logo": ["Beanie with <PERSON><PERSON>"], "Something went wrong. Please contact us to get assistance.": ["Something went wrong. Please contact us to get assistance."], "Unable to get cart data from the API.": ["Unable to get basket data from the API."], "The response is not a valid JSON response.": ["The response is not a valid JSON response."], "Sales tax": ["Sales tax"], "Color": ["Colour"], "Small": ["Small"], "Size": ["Size"], "Free shipping": ["Free shipping"], "Shipping": ["Shipping"], "Fee": ["Fee"], "Local pickup": ["Local pickup"]}}, "comment": {"reference": "assets/client/blocks/wc-blocks-data.js"}}