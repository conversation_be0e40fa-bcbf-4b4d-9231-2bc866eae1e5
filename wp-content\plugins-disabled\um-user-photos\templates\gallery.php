<?php
/**
 * Template for the UM User Photos, The "Albums" block
 *
 * Page: "Profile", tab "Photos"
 * Hook: 'ultimatemember_gallery'
 * Caller: User_Photos_Shortcodes->get_gallery_content() method
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/gallery.php
 * @var object $albums
 * @var string $column
 * @var string $default
 * @var bool   $disable_title
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( $albums->have_posts() ) {
	?>
	<div class="um-user-photos-albums">
		<div class="um-user-photos-albums-wrap <?php echo esc_attr( $column ); ?>">
			<?php
			while ( $albums->have_posts() ) {
				$albums->the_post();

				$photos = get_post_meta( get_the_ID(), '_photos', true );
				if ( $photos ) {
					$count = count( $photos );
					// translators: %s is a photos count.
					$count_msg = sprintf( _n( '%s Photo', '%s Photos', $count, 'um-user-photos' ), number_format_i18n( $count ) );
				} else {
					$count_msg = false;
				}

				$album_id = get_the_ID();
				$img      = UM()->User_Photos()->common()->album()->get_cover( $album_id );

				$data_t = array(
					'id'            => $album_id,
					'title'         => get_the_title(),
					'count_msg'     => $count_msg,
					'column'        => $column,
					'img'           => $img,
					'disable_title' => $disable_title,
					'default'       => $default,
				);
				UM()->get_template( 'album-block.php', UM_USER_PHOTOS_PLUGIN, $data_t, true );
			}
			wp_reset_postdata();
			?>
		</div>
	</div>
	<?php
} else {
	?>
	<p class="text-center"><?php esc_html_e( 'Nothing to display', 'um-user-photos' ); ?></p>
	<?php
}
