<?php
/**
 * Template for the UM User Photos. The image caption and comments block.
 *
 * Call: UM()->User_Photos()->ajax()->um_user_photos_get_comment_section()
 * Page: "Profile", tab "Photos", the image popup
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/caption.php
 * @var WP_Post $image
 * @var int     $image_id
 * @var string  $image_link
 * @var array   $likes
 * @var int     $likes_count
 * @var bool    $disable_comments
 * @var array   $comments
 * @var int     $comment_count
 * @var int     $count_photos
 * @var string  $context
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
?>
<div class="um-user-photos-widget-image" data-image="<?php echo absint( $image_id ); ?>">
	<?php
	$image_url = wp_get_attachment_image_src( $image_id, 'large' );
	$lazy_img  = UM()->frontend()::layouts()::lazy_image(
		$image_url[0],
		array(
			'width' => '100%',
		)
	);

	if ( 'gallery' === $context || ( 'album' === $context && $count_photos > 1 ) ) {
		$left_icon  = '<svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icons-tabler-outline icon-tabler-chevron-left"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M15 6l-6 6l6 6" /></svg>';
		$right_icon = '<svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icons-tabler-outline icon-tabler-chevron-right"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M9 6l6 6l-6 6" /></svg>';

		$previous_button = UM()->frontend()::layouts()::button(
			$left_icon,
			array(
				'type'          => 'button',
				'size'          => 's',
				'design'        => 'link-gray',
				'icon_position' => 'content',
				'url'           => '',
				'classes'       => array(
					'um-photo-modal-prev',
				),
			)
		);

		$next_button = UM()->frontend()::layouts()::button(
			$right_icon,
			array(
				'type'          => 'button',
				'size'          => 's',
				'design'        => 'link-gray',
				'icon_position' => 'content',
				'url'           => '',
				'classes'       => array(
					'um-photo-modal-next',
				),
			)
		);

		echo wp_kses( $previous_button . $next_button . $lazy_img, UM()->get_allowed_html( 'templates' ) );
	} else {
		echo wp_kses( $lazy_img, UM()->get_allowed_html( 'templates' ) );
	}

	if ( $image_link && esc_url( $image_link ) ) {
		?>
		<div class="um-user-photos-link">
			<?php
			$svg_html = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icons-tabler-outline icon-tabler-link"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M9 15l6 -6" /><path d="M11 6l.463 -.536a5 5 0 0 1 7.071 7.072l-.534 .464" /><path d="M13 18l-.397 .534a5.068 5.068 0 0 1 -7.127 0a4.972 4.972 0 0 1 0 -7.071l.524 -.463" /></svg>';
			echo wp_kses(
				UM()->frontend()::layouts()::link(
					$svg_html,
					array(
						'type'          => 'button',
						'title'         => __( 'Related link', 'um-user-photos' ),
						'size'          => 's',
						'icon_position' => 'content',
						'url'           => $image_link,
						'target'        => '_blank',
					)
				),
				UM()->get_allowed_html( 'templates' )
			);
			?>
		</div>
	<?php } ?>
</div>
<div class="um-user-photos-widget" id="um-photoid-<?php echo esc_attr( $image_id ); ?>">
	<div class="um-user-photos-modal-skeleton">
		<div class="um-user-photos-modal-skeleton-inner">
			<div class="um-skeleton-box-header">
				<div class="um-skeleton-box-avatar um-skeleton-box"></div>
				<div class="um-skeleton-box-header-info">
					<div class="um-skeleton-box-title um-skeleton-box"></div>
					<div class="um-skeleton-box-time um-skeleton-box"></div>
				</div>
			</div>
			<div class="um-skeleton-box-description um-skeleton-box"></div>
			<div class="um-skeleton-box-description um-skeleton-box"></div>
			<div class="um-skeleton-box-description um-skeleton-box"></div>
		</div>
	</div>
	<div class="um-user-photos-photo-data">
		<?php
		$unix_published_date = get_post_datetime( $image_id, 'date', 'gmt' );
		$post_date           = wp_date( get_option( 'date_format', 'F j, Y' ) . ' ' . get_option( 'time_format', 'g:i a' ), $unix_published_date->getTimestamp() );

		echo wp_kses(
			UM()->frontend()::layouts()::small_data(
				$image->post_author,
				array(
					'avatar_size' => 'm',
					'classes'     => array( 'um-user-photos-author' ),
					'clickable'   => true,
					'url'         => um_user_profile_url( $image->post_author ),
					'supporting'  => '<a href="#" class="um-user-photos-metadata-date um-link um-link-secondary" title="' . esc_attr( $post_date ) . '">' .
											esc_html( UM()->datetime()->time_diff( $unix_published_date->getTimestamp() ) ) .
										'</a>',
				)
			),
			UM()->get_allowed_html( 'templates' )
		);

		$image_caption   = wp_get_attachment_caption( $image_id );
		$caption_classes = array( 'um-user-photos-image-caption' );
		if ( empty( $image_caption ) ) {
			$caption_classes[] = 'um-display-none';
		}
		?>

		<div class="<?php echo esc_attr( implode( ' ', $caption_classes ) ); ?>">
			<?php echo wp_kses( $image_caption, UM()->get_allowed_html( 'templates' ) ); ?>
		</div>

		<?php if ( ! UM()->options()->get( 'um_user_photos_disable_comments' ) ) { ?>
			<div class="um-user-photos-actions">
				<div class="um-user-photos-like-wrap">
					<?php
					$user_id   = get_current_user_id();
					$has_liked = in_array( $user_id, $likes, true );

					$like_classes = array( 'um-user-photos-like' );
					if ( $has_liked ) {
						$like_classes[] = 'active';
					}
					echo wp_kses(
						UM()->frontend()::layouts()::button(
							'<span class="um-like-icon"></span>',
							array(
								'size'     => 's',
								'design'   => 'link-gray',
								'title'    => $has_liked ? __( 'Unlike', 'um-user-photos' ) : __( 'Like', 'um-user-photos' ),
								'data'     => array(
									'id'           => $image_id,
									'likenonce'    => wp_create_nonce( 'um_user_photos_like_photo' ),
									'unlikenonce'  => wp_create_nonce( 'um_user_photos_unlike_photo' ),
									'like_title'   => __( 'Like', 'um-user-photos' ),
									'unlike_title' => __( 'Unlike', 'um-user-photos' ),
								),
								'classes'  => $like_classes,
								'disabled' => ! is_user_logged_in(),
							)
						),
						UM()->get_allowed_html( 'templates' )
					);

					$users_avatars = get_post_meta( $image_id, '_liked', true );
					if ( empty( $users_avatars ) ) {
						$users_avatars = array();
					}

					$avatars = UM()->frontend()::layouts()::avatars_list(
						$users_avatars,
						array(
							'wrapper' => 'span',
							'size'    => 's',
							'count'   => 5,
						)
					);

					$likes_count_badge = UM()->frontend()::layouts()::badge(
						$likes_count,
						array(
							'size' => 's',
						)
					);

					echo wp_kses(
						UM()->frontend()::layouts()::button(
							'<span class="um-user-photos-post-likes"><span class="um-user-photos-post-likes-avatars' . ( 0 >= absint( $likes_count ) ? ' um-display-none' : '' ) . '">' . wp_kses( $avatars, UM()->get_allowed_html( 'templates' ) ) . '</span><span class="um-user-photos-post-likes-count">' . wp_kses( $likes_count_badge, UM()->get_allowed_html( 'templates' ) ) . '</span></span>',
							array(
								'size'     => 's',
								'design'   => 'link-gray',
								'data'     => array(
									'id'      => $image_id,
									'wpnonce' => wp_create_nonce( 'um_user_photos_show_likes' ),
								),
								'title'    => __( 'Click to show all likes', 'um-user-photos' ),
								'classes'  => array( 'um-user-photos-show-likes' ),
								'disabled' => 0 >= absint( $likes_count ),
							)
						),
						UM()->get_allowed_html( 'templates' )
					);
					?>
				</div>

				<?php
				if ( ! $disable_comments && UM()->User_Photos()->common()->user()->can_comment( $image_id ) ) {
					$count    = UM()->frontend()::layouts()::badge(
						$comment_count,
						array(
							'size' => 's',
						)
					);
					$svg_html = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icons-tabler-outline icon-tabler-message-circle"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M3 20l1.3 -3.9c-2.324 -3.437 -1.426 -7.872 2.1 -10.374c3.526 -2.501 8.59 -2.296 11.845 .48c3.255 2.777 3.695 7.266 1.029 10.501c-2.666 3.235 -7.615 4.215 -11.574 2.293l-4.7 1" /></svg>';
					echo wp_kses(
						UM()->frontend()::layouts()::button(
							'<span class="um-user-photos-comment-text">' . esc_html__( 'Show comment', 'um-user-photos' ) . '</span>' . $count,
							array(
								'size'          => 's',
								'design'        => 'link-gray',
								'icon_position' => 'leading',
								'icon'          => $svg_html,
								'classes'       => array( 'um-user-photos-comments-toggle' ),
								'data'          => array(
									'toggle-text' => __( 'Hide comment', 'um-user-photos' ),
									'um-toggle'   => '.um-user-photos-comments',
								),
								'disabled'      => $comment_count <= 0,
							)
						),
						UM()->get_allowed_html( 'templates' )
					);
				}
				?>
			</div>
		<?php } ?>
	</div>
	<?php
	if ( ! UM()->options()->get( 'um_user_photos_disable_comments' ) && ! $disable_comments ) {
		?>
		<div class="um-user-photos-comments um-toggle-block um-toggle-block-collapsed">
			<div class="um-user-photos-comments-inner um-toggle-block-inner">
				<?php
				UM()->get_template(
					'v3/comments.php',
					UM_USER_PHOTOS_PLUGIN,
					array(
						'image_id'      => $image_id,
						'comments'      => $comments,
						'comment_count' => $comment_count,
					),
					true
				);
				?>
			</div>
		</div>
		<?php
		if ( is_user_logged_in() && UM()->User_Photos()->common()->user()->can_comment( $image_id ) ) {
			UM()->get_template(
				'v3/comment-form.php',
				UM_USER_PHOTOS_PLUGIN,
				array(
					'image_id' => $image_id,
				),
				true
			);
		}
	}
	?>
</div>
