{"translation-revision-date": "2024-10-02 16:14:23+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "To filter your products by price you first need to assign prices to your products.": ["To filter your products by price you first need to assign prices to your products."], "Show 'Apply filters' button": ["Show 'Apply filters' button"], "Show input fields inline with the slider.": ["Show input fields inline with the slider."], "Inline input fields": ["Inline input fields"], "Price Range Selector": ["Price Range Selector"], "Filter by Price": ["Filter by Price"], "Reset price filter": ["Reset price filter"], "Reset filter": ["Reset filter"], "Block title": ["Block title"], "Apply price filter": ["Apply price filter"], "Apply filter": ["Apply filter"], "Filter products by maximum price": ["Filter products by maximum price"], "Filter products by minimum price": ["Filter products by minimum price"], "Editable": ["Editable"], "Products will update when the button is clicked.": ["Products will only update when the button is clicked."], "Display a slider to filter products in your store by price.": ["Display a slider to filter products in your store by price."], "Text": ["Text"], "Filter by price": ["Filter by price"], "Add new product": ["Add new product"], "Learn more": ["Learn more"], "Reset": ["Reset"], "Apply": ["Apply"], "Settings": ["Settings"]}}, "comment": {"reference": "assets/client/blocks/price-filter.js"}}