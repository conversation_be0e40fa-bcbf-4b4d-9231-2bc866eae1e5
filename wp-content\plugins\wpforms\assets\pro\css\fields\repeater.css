div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-layout-row {
  padding: 0 10px;
  gap: 20px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-layout-row .wpforms-layout-column {
  padding: 0;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-layout-rows .wpforms-layout-column-100,
div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-medium .wpforms-layout-column-100 {
  width: 60%;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-layout-rows .wpforms-layout-column-100 + .wpforms-field-repeater-display-rows-buttons,
div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-medium .wpforms-layout-column-100 + .wpforms-field-repeater-display-rows-buttons {
  inset-inline-start: calc( 60% + 15px);
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-small .wpforms-layout-column-100 {
  width: 25%;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-small .wpforms-layout-column-100 + .wpforms-field-repeater-display-rows-buttons {
  inset-inline-start: calc( 25% + 20px);
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-large .wpforms-layout-column-100 {
  width: 100%;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-large .wpforms-layout-column-100 + .wpforms-field-repeater-display-rows-buttons {
  inset-inline: auto -45px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-large.wpforms-field-repeater-preset-100.wpforms-field-repeater-display-rows .wpforms-layout-row {
  width: calc( 100% - 35px);
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-large.wpforms-field-repeater-preset-100.wpforms-field-repeater-display-blocks .wpforms-layout-row {
  width: auto;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-rows:has(+ .wpforms-field-repeater) {
  padding-bottom: 0;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-rows .wpforms-field-layout-rows .wpforms-field {
  transition: all 0.07s ease;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-rows .wpforms-field-label ~ .wpforms-field-layout-rows:nth-of-type(1) .wpforms-field {
  padding-top: 15px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-rows .wpforms-field-repeater-clone-wrap:has(+ .wpforms-field-repeater-clone-wrap) .wpforms-field {
  padding-bottom: 15px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons {
  margin-top: 15px;
  display: flex;
  justify-content: flex-start;
  flex-wrap: nowrap;
  gap: 10px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons button {
  background: none;
  border: none;
  border-radius: 4px;
  min-height: 33px;
  max-width: 33%;
  padding: 8px 12px;
  line-height: 14px;
  font-size: 14px;
  font-weight: 400;
  color: #999999;
  cursor: pointer;
  transition: opacity 0.2s ease;
  outline: none;
  display: flex;
  align-items: center;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons button:hover {
  opacity: 0.75;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons button svg {
  display: inline;
  line-height: 18px;
  margin-inline-end: 5px;
  transform: scale(0.8);
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons button span {
  line-height: 14px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons button.wpforms-disabled {
  display: none;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="buttons_with_icons"] button {
  background: rgba(204, 204, 204, 0.35);
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="buttons"] button {
  background: rgba(204, 204, 204, 0.35);
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="buttons"] svg {
  display: none;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="icons_with_text"] {
  gap: 15px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="icons_with_text"] button {
  padding: 0;
  height: auto;
  line-height: 14px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="icons"] button {
  padding: 0;
  height: auto;
  line-height: 14px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="icons"] svg {
  transform: scale(1);
  margin: 0;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="icons"] span {
  display: none;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="plain_text"] {
  gap: 15px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="plain_text"] button {
  padding: 0;
  height: auto;
  line-height: 17px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="plain_text"] svg {
  display: none;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-layout-rows.wpforms-field-repeater-display-rows .wpforms-layout-row {
  position: relative;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-layout-rows.wpforms-field-repeater-display-rows .wpforms-field-description {
  display: none;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-layout-rows.wpforms-field-repeater-display-rows .wpforms-field-description.wpforms-init {
  display: block;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-layout-rows.wpforms-field-repeater-display-rows:not(.wpforms-field-repeater-preset-100) .wpforms-layout-row {
  padding-inline-end: 67px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-layout-rows .wpforms-field-repeater-display-rows-buttons {
  position: absolute;
  inset-inline: auto 10px;
  padding: 0;
  display: none;
  gap: 8px;
  transform: translateY(7px);
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-layout-rows .wpforms-field-repeater-display-rows-buttons.wpforms-init {
  display: flex;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-layout-rows .wpforms-field-repeater-display-rows-buttons button {
  background: none;
  border: none;
  cursor: pointer;
  color: #999999;
  height: 40px;
  width: 16px;
  min-width: auto;
  margin-top: 0;
  box-shadow: none;
  padding: 0;
  outline: none;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-layout-rows .wpforms-field-repeater-display-rows-buttons button:hover {
  opacity: 0.75;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-layout-rows .wpforms-field-repeater-display-rows-buttons button svg {
  transform: scale(0.97);
}

div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-layout-rows.hidden-placeholders .wpforms-layout-column {
  padding-bottom: 0;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater button.wpforms-disabled, div.wpforms-container .wpforms-form .wpforms-field-repeater button.wpforms-disabled:hover {
  opacity: 0.5 !important;
  cursor: default !important;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater > .wpforms-field-label,
div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-block-title {
  font-style: normal;
  font-weight: 700;
  font-size: 22px;
  line-height: 22px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater > .wpforms-field-description,
div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-clone-wrap > .wpforms-field-description {
  margin-block: -5px 15px;
  margin-inline: 0;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater > .wpforms-field-label {
  margin-top: 15px;
  padding: 0;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-blocks > .wpforms-field-label {
  margin-top: 0;
  padding-top: 45px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-blocks .wpforms-field-repeater-clone-wrap .wpforms-field-layout-rows:first-child {
  margin-top: 15px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-blocks .wpforms-field-repeater-clone-wrap > .wpforms-field-description:first-child {
  margin-top: 30px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-blocks > .wpforms-field-label,
div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-blocks .wpforms-field-repeater-block-title {
  margin-block: 30px 15px;
  margin-inline: 0;
  padding: 45px 0 0 0;
  border-top: 1px solid #DDDDDD;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-blocks .wpforms-field-repeater-block-title {
  margin-top: 45px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-blocks .wpforms-field-repeater-block-title:empty {
  padding-top: 5px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-blocks + .wpforms-field-repeater-display-blocks > .wpforms-field-label {
  margin-top: 15px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-rows + .wpforms-field-repeater-display-blocks > .wpforms-field-label {
  margin-top: 30px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-rows + .wpforms-field-repeater-display-rows {
  padding-top: 30px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-rows + .wpforms-field-divider {
  margin-top: 30px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-rows > .wpforms-field-layout-rows:not(:has(+ .wpforms-field-repeater-clone-wrap)) .wpforms-field {
  padding-bottom: 0;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-rows > .wpforms-field-repeater-clone-wrap:last-child .wpforms-field {
  padding-bottom: 0;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater > .wpforms-field-repeater-clone-wrap {
  display: block;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater > .wpforms-field-repeater-clone-wrap .wpforms-field-repeater-display-rows .wpforms-field-repeater-display-rows-buttons {
  padding-top: 5px;
  transform: translateY(-2px);
}

div.wpforms-container .wpforms-form .wpforms-field-repeater > .wpforms-field-repeater-clone-wrap .wpforms-field-repeater-display-rows .wpforms-field {
  padding-top: 5px;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater > .wpforms-field-repeater-clone-wrap .wpforms-field-repeater-display-rows .wpforms-field-label {
  display: none;
}

div.wpforms-container .wpforms-form .wpforms-field-repeater:last-child {
  margin-bottom: 30px;
}

div.wpforms-container .wpforms-form .wpforms-field:not(.wpforms-field-repeater) + .wpforms-field-repeater-display-blocks > .wpforms-field-label {
  margin-top: 15px;
}

div.wpforms-container .wpforms-form .wpforms-field:not(.wpforms-field-repeater) + .wpforms-field-repeater-display-rows:not(:has(> .wpforms-field-label)):not(:has(> .wpforms-field-description)) {
  padding-top: 0;
}

div.wpforms-container .wpforms-form .wpforms-field.wpforms-field-divider + .wpforms-field-repeater-display-blocks > .wpforms-field-label {
  margin-top: 0;
}

div.wpforms-container .wpforms-form .wpforms-field.wpforms-field-divider + .wpforms-field-repeater-display-rows > .wpforms-field-label {
  margin-top: 0;
}

div.wpforms-container .wpforms-form .wpforms-field.wpforms-field-divider:has(> .wpforms-field-description) + .wpforms-field-repeater-display-rows > .wpforms-field-label {
  margin-top: 25px;
}

div.wpforms-container .wpforms-form .wpforms-field.wpforms-field-divider:has(> .wpforms-field-description) + .wpforms-field-repeater-display-rows:not(:has(> .wpforms-field-label)) > .wpforms-field-description {
  margin-top: 30px;
}

div.wpforms-container .wpforms-form .wpforms-field-container .wpforms-field-repeater.wpforms-field-repeater-display-blocks:first-child > .wpforms-field-label {
  border-top: none;
  margin-top: 0;
  padding-top: 0;
}

div.wpforms-container .wpforms-form .wpforms-field-container .wpforms-page:last-child .wpforms-field-repeater-display-blocks:has(+ .wpforms-field-pagebreak) {
  padding-bottom: 15px;
}

.block-editor-block-list__block .wpforms-field-repeater-display-rows-buttons {
  display: flex !important;
  bottom: 0;
}

@media only screen and (max-width: 600px) {
  div.wpforms-container .wpforms-form .wpforms-field-repeater > .wpforms-field-layout-rows .wpforms-field-repeater-display-rows-buttons, div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-clone-wrap .wpforms-field-repeater-display-rows-buttons {
    display: block;
    bottom: 15px;
    top: unset !important;
  }
  div.wpforms-container .wpforms-form .wpforms-field-repeater > .wpforms-field-layout-rows .wpforms-layout-row, div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-repeater-clone-wrap .wpforms-layout-row {
    gap: 0;
  }
  div.wpforms-container .wpforms-form .wpforms-field-repeater .wpforms-field-layout-rows.wpforms-field-repeater-display-rows .wpforms-field-description {
    display: block !important;
  }
  div.wpforms-container .wpforms-form .wpforms-field-repeater > .wpforms-field-repeater-clone-wrap .wpforms-field-repeater-display-rows .wpforms-field-label {
    display: block;
  }
  div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-rows + .wpforms-field-repeater-display-blocks > .wpforms-field-label {
    margin-top: 15px;
  }
  div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-rows + .wpforms-field-repeater-display-rows {
    padding-top: 15px;
  }
  div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-rows + .wpforms-field-divider {
    margin-top: 15px;
  }
  div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-rows .wpforms-field-label ~ .wpforms-field-layout-rows:nth-of-type(1) .wpforms-field {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-rows .wpforms-field-repeater-clone-wrap:has(+ .wpforms-field-repeater-clone-wrap) .wpforms-field {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  div.wpforms-container .wpforms-form .wpforms-field-repeater.wpforms-field-repeater-display-rows > .wpforms-field-repeater-clone-wrap:last-child .wpforms-field {
    padding-top: 15px;
    padding-bottom: 15px;
  }
}
