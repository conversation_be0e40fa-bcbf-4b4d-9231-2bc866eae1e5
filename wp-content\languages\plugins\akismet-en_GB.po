# Translation of Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release) in English (UK)
# This file is distributed under the same license as the Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-07-10 22:32:29+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: en_GB\n"
"Project-Id-Version: Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release)\n"

#: views/setup.php:7
msgid "Choose an Akismet plan"
msgstr "Choose an Akismet plan"

#. translators: the placeholder is the URL to the Akismet pricing page.
#: views/notice.php:180
msgid "Please <a href=\"%s\" target=\"_blank\">choose a plan</a> to get started with Akismet."
msgstr "Please <a href=\"%s\" target=\"_blank\">choose a plan</a> to get started with Akismet."

#: views/notice.php:176
msgid "Your API key must have an Akismet plan before it can protect your site from spam."
msgstr "Your API key must have an Akismet plan before it can protect your site from spam."

#: class.akismet-rest-api.php:509
msgid "Multiple comments matched request."
msgstr "Multiple comments matched request."

#: class.akismet-rest-api.php:499
msgid "Could not find matching comment."
msgstr "Could not find matching comment."

#: class.akismet-rest-api.php:457
msgid "The 'comments' parameter must be an array."
msgstr "The “comments” parameter must be an array."

#: class.akismet-admin.php:755
msgid "Akismet cleared this comment during a recheck. It did not update the comment status because it had already been modified by another user or plugin."
msgstr "Akismet cleared this comment during a recheck. It did not update the comment status because it had already been modified by another user or plugin."

#: class.akismet-admin.php:752
msgid "Akismet determined this comment was spam during a recheck. It did not update the comment status because it had already been modified by another user or plugin."
msgstr "Akismet determined this comment was spam during a recheck. It did not update the comment status because it had already been modified by another user or plugin."

#: class.akismet-admin.php:749
msgid "Akismet cleared this comment and updated its status via webhook."
msgstr "Akismet cleared this comment and updated its status via webhook."

#: class.akismet-admin.php:746
msgid "Akismet caught this comment as spam and updated its status via webhook."
msgstr "Akismet caught this comment as spam and updated its status via webhook."

#: views/notice.php:198
msgid "Akismet is now protecting your site from spam."
msgstr "Akismet is now protecting your site from spam."

#: views/config.php:304
msgid "Account overview"
msgstr "Account overview"

#. translators: %1$s: spam folder link, %2$d: delete interval in days
#: views/config.php:192
msgid "Spam in the %1$s older than %2$d day is deleted automatically."
msgid_plural "Spam in the %1$s older than %2$d days is deleted automatically."
msgstr[0] "Spam in the %1$s older than %2$d day is deleted automatically."
msgstr[1] "Spam in the %1$s older than %2$d days is deleted automatically."

#: views/config.php:187
msgid "spam folder"
msgstr "spam folder"

#: views/stats.php:11
msgid "Akismet detailed stats"
msgstr "Akismet detailed stats"

#: views/stats.php:6
msgid "Back to settings"
msgstr "Back to settings"

#: views/config.php:268
msgid "Subscription type"
msgstr "Subscription type"

#: views/config.php:232
msgid "To help your site with transparency under privacy laws like the GDPR, Akismet can display a notice to your users under your comment forms."
msgstr "To help your site with transparency under privacy laws like the GDPR, Akismet can display a notice to your users under your comment forms."

#: views/config.php:154
msgid "Spam filtering"
msgstr "Spam filtering"

#: views/config.php:94 views/enter.php:9
msgid "API key"
msgstr "API key"

#: views/config.php:44
msgid "Akismet stats"
msgstr "Akismet stats"

#. Author of the plugin
#: akismet.php
msgid "Automattic - Anti-spam Team"
msgstr "Automattic - Anti-spam Team"

#. Plugin Name of the plugin
#: akismet.php
msgid "Akismet Anti-spam: Spam Protection"
msgstr "Akismet Anti-spam: Spam Protection"

#: views/notice.php:47
msgid "WP-Cron has been disabled using the DISABLE_WP_CRON constant. Comment rechecks may not work properly."
msgstr "WP-Cron has been disabled using the DISABLE_WP_CRON constant. Comment rechecks may not work correctly."

#. translators: %1$s is a human-readable time difference, like "3 hours ago",
#. and %2$s is an already-translated phrase describing how a comment's status
#. changed, like "This comment was reported as spam."
#: class.akismet-admin.php:793
msgid "%1$s - %2$s"
msgstr "%1$s - %2$s"

#: views/get.php:17
msgid "(opens in a new tab)"
msgstr "(opens in a new tab)"

#. translators: The placeholder is the name of a subscription level, like
#. "Plus" or "Enterprise" .
#: views/notice.php:341
msgid "Upgrade to %s"
msgstr "Upgrade to %s"

#: views/notice.php:336
msgid "Upgrade your subscription level"
msgstr "Upgrade your subscription level"

#: views/notice.php:293 views/notice.php:301 views/notice.php:309
#: views/notice.php:318
msgid "Learn more about usage limits."
msgstr "Learn more about usage limits."

#. translators: The first placeholder is a date, the second is a (formatted)
#. number, the third is another formatted number.
#: views/notice.php:285
msgid "Since %1$s, your account made %2$s API calls, compared to your plan&#8217;s limit of %3$s."
msgstr "Since %1$s, your account made %2$s API calls, compared to your plan&#8217;s limit of %3$s."

#: views/notice.php:315
msgid "Your Akismet usage has been over your plan&#8217;s limit for three consecutive months. We have restricted your account for the rest of the month. Upgrade your plan so Akismet can continue blocking spam."
msgstr "Your Akismet usage has been over your plan&#8217;s limit for three consecutive months. We have restricted your account for the rest of the month. Upgrade your plan so Akismet can continue blocking spam."

#: views/notice.php:306
msgid "Your Akismet usage is nearing your plan&#8217;s limit for the third consecutive month. We will restrict your account after you reach the limit. Upgrade your plan so Akismet can continue blocking spam."
msgstr "Your Akismet usage is nearing your plan&#8217;s limit for the third consecutive month. We will restrict your account after you reach the limit. Upgrade your plan so Akismet can continue blocking spam."

#: views/notice.php:298
msgid "Your Akismet usage has been over your plan&#8217;s limit for two consecutive months. Next month, we will restrict your account after you reach the limit. Please consider upgrading your plan."
msgstr "Your Akismet usage has been over your plan&#8217;s limit for two consecutive months. Next month, we will restrict your account after you reach the limit. Please consider upgrading your plan."

#: views/notice.php:272
msgid "Your account has been restricted"
msgstr "Your account has been restricted"

#: views/notice.php:268
msgid "Your Akismet account usage is approaching your plan&#8217;s limit"
msgstr "Your Akismet account usage is approaching your plan&#8217;s limit"

#: views/notice.php:265
msgid "Your Akismet account usage is over your plan&#8217;s limit"
msgstr "Your Akismet account usage is over your plan&#8217;s limit"

#. translators: The placeholder is a URL to the Akismet contact form.
#: views/notice.php:228
msgid "Please enter a new key or <a href=\"%s\" target=\"_blank\">contact Akismet support</a>."
msgstr "Please enter a new key or <a href=\"%s\" target=\"_blank\">contact Akismet support</a>."

#: views/notice.php:222
msgid "Your API key is no longer valid."
msgstr "Your API key is no longer valid."

#. translators: The placeholder is for showing how much of the process has
#. completed, as a percent. e.g., "Checking for Spam (40%)"
#: class.akismet-admin.php:481
msgid "Checking for Spam (%1$s%)"
msgstr "Checking for Spam (%1$s%)"

#: class.akismet-admin.php:809
msgid "No comment history."
msgstr "No comment history."

#: class.akismet-admin.php:742
msgid "Akismet was unable to recheck this comment."
msgstr "Akismet was unable to recheck this comment."

#: class.akismet-admin.php:734
msgid "Akismet was unable to check this comment but will automatically retry later."
msgstr "Akismet was unable to check this comment, but will automatically retry later."

#. translators: The placeholder is a WordPress PHP function name.
#: class.akismet-admin.php:703
msgid "Comment was caught by %s."
msgstr "Comment was caught by %s."

#: class.akismet.php:802
msgid "Akismet is not configured. Please enter an API key."
msgstr "Akismet is not configured. Please enter an API key."

#: views/enter.php:7
msgid "Enter your API key"
msgstr "Enter your API key"

#: views/connect-jp.php:92
msgid "Set up a different account"
msgstr "Set up a different account"

#: views/setup.php:2
msgid "Set up your Akismet account to enable spam filtering on this site."
msgstr "Set up your Akismet account to enable spam filtering on this site."

#: class.akismet-admin.php:1332
msgid "Akismet could not recheck your comments for spam."
msgstr "Akismet could not recheck your comments for spam."

#: class.akismet-admin.php:514
msgid "You don&#8217;t have permission to do that."
msgstr "You don't have permission to do that."

#: class.akismet-cli.php:167
msgid "Stats response could not be decoded."
msgstr "Stats response could not be decoded."

#: class.akismet-cli.php:161
msgid "Currently unable to fetch stats. Please try again."
msgstr "Currently unable to fetch stats. Please try again."

#: class.akismet-cli.php:135
msgid "API key must be set to fetch stats."
msgstr "API key must be set to fetch stats."

#: views/config.php:225
msgid "Do not display privacy notice."
msgstr "Do not display privacy notice."

#: views/config.php:217
msgid "Display a privacy notice under your comment forms."
msgstr "Display a privacy notice under your comment forms."

#: views/config.php:211
msgid "Akismet privacy notice"
msgstr "Akismet privacy notice"

#: views/config.php:206
msgid "Privacy"
msgstr "Privacy"

#: class.akismet-admin.php:108
msgid "We collect information about visitors who comment on Sites that use our Akismet Anti-spam service. The information we collect depends on how the User sets up Akismet for the Site, but typically includes the commenter's IP address, user agent, referrer, and Site URL (along with other information directly provided by the commenter such as their name, username, email address, and the comment itself)."
msgstr "We collect information about visitors who comment on sites that use our Akismet Anti-spam service. The information we collect depends on how the user sets up Akismet for the site, but typically includes the commenter's IP address, user agent, referrer, and site URL (along with other information directly provided by the commenter such as their name, username, email address, and the comment itself)."

#: class.akismet.php:430
msgid "Comment discarded."
msgstr "Comment discarded."

#: class.akismet-rest-api.php:206
msgid "This site's API key is hardcoded and cannot be deleted."
msgstr "This site's API key is hardcoded and cannot be deleted."

#: class.akismet-rest-api.php:190
msgid "The value provided is not a valid and registered API key."
msgstr "The value provided is not a valid and registered API key."

#: class.akismet-rest-api.php:184
msgid "This site's API key is hardcoded and cannot be changed via the API."
msgstr "This site's API key is hardcoded and cannot be changed via the API."

#: class.akismet-rest-api.php:84 class.akismet-rest-api.php:97
msgid "The time period for which to retrieve stats. Options: 60-days, 6-months, all"
msgstr "The time period for which to retrieve stats. Options: 60-days, 6-months, all"

#: class.akismet-rest-api.php:65
msgid "If true, show the number of approved comments beside each comment author in the comments list page."
msgstr "If true, show the number of approved comments beside each comment author in the comments list page."

#: class.akismet-rest-api.php:60
msgid "If true, Akismet will automatically discard the worst spam automatically rather than putting it in the spam folder."
msgstr "If true, Akismet will automatically discard the worst spam automatically rather than putting it in the spam folder."

#: class.akismet-rest-api.php:31 class.akismet-rest-api.php:122
#: class.akismet-rest-api.php:135 class.akismet-rest-api.php:148
msgid "A 12-character Akismet API key. Available at akismet.com/get/"
msgstr "A 12-character Akismet API key. Available at akismet.com/get/"

#: views/notice.php:109
msgid "Your site can&#8217;t connect to the Akismet servers."
msgstr "Your site can&#8217;t connect to the Akismet servers."

#. translators: %s is the wp-config.php file
#: views/predefined.php:7
msgid "An Akismet API key has been defined in the %s file for this site."
msgstr "An Akismet API key has been defined in the %s file for this site."

#: views/predefined.php:2
msgid "Manual Configuration"
msgstr "Manual Configuration"

#: class.akismet-admin.php:275
msgid "On this page, you are able to update your Akismet settings and view spam stats."
msgstr "On this page, you are able to update your Akismet settings and view spam stats."

#. Description of the plugin
#: akismet.php
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. Akismet Anti-spam keeps your site protected even while you sleep. To get started: activate the Akismet plugin and then go to your Akismet Settings page to set up your API key."
msgstr "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. Akismet Anti-spam keeps your site protected even while you sleep. To get started: activate the Akismet plugin and then go to your Akismet Settings page to set up your API key."

#: class.akismet-admin.php:135 class.akismet-admin.php:137
msgid "Akismet Anti-spam"
msgstr "Akismet Anti-spam"

#: views/enter.php:10
msgid "Connect with API key"
msgstr "Connect with API key"

#. translators: %s is the WordPress.com username
#: views/connect-jp.php:25 views/connect-jp.php:79
msgid "You are connected as %s."
msgstr "You are connected as %s."

#: views/connect-jp.php:10 views/connect-jp.php:18 views/connect-jp.php:38
#: views/connect-jp.php:72 views/connect-jp.php:91
msgid "Connect with Jetpack"
msgstr "Connect with Jetpack"

#: views/connect-jp.php:12 views/connect-jp.php:32 views/connect-jp.php:67
msgid "Use your Jetpack connection to set up Akismet."
msgstr "Use your Jetpack connection to set up Akismet."

#: views/title.php:2
msgid "Eliminate spam from your site"
msgstr "Eliminate spam from your site"

#. translators: The placeholder is a URL for checking pending comments.
#: views/notice.php:205
msgid "Would you like to <a href=\"%s\">check pending comments</a>?"
msgstr "Would you like to <a href=\"%s\">check pending comments</a>?"

#: views/notice.php:25
msgid "Set up your Akismet account"
msgstr "Set up your Akismet account"

#: views/config.php:36
msgid "Detailed stats"
msgstr "Detailed stats"

#: views/config.php:31
msgid "Statistics"
msgstr "Statistics"

#: class.akismet-admin.php:1448
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. It keeps your site protected even while you sleep. To get started, just go to <a href=\"admin.php?page=akismet-key-config\">your Akismet Settings page</a> to set up your API key."
msgstr "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. It keeps your site protected even while you sleep. To get started, just go to <a href=\"admin.php?page=akismet-key-config\">your Akismet Settings page</a> to set up your API key."

#: class.akismet-admin.php:1446
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. Your site is fully configured and being protected, even while you sleep."
msgstr "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. Your site is fully configured and being protected, even while you sleep."

#. translators: %s: Number of comments.
#: class.akismet-admin.php:1326
msgid "%s comment was caught as spam."
msgid_plural "%s comments were caught as spam."
msgstr[0] "%s comment was caught as spam."
msgstr[1] "%s comments were caught as spam."

#: class.akismet-admin.php:1323
msgid "No comments were caught as spam."
msgstr "No comments were caught as spam."

#. translators: %s: Number of comments.
#: class.akismet-admin.php:1319
msgid "Akismet checked %s comment."
msgid_plural "Akismet checked %s comments."
msgstr[0] "Akismet checked %s comment."
msgstr[1] "Akismet checked %s comments."

#: class.akismet-admin.php:1316
msgid "There were no comments to check. Akismet will only check comments awaiting moderation."
msgstr "There were no comments to check. Akismet will only check comments awaiting moderation."

#: class.akismet.php:808
msgid "Comment not found."
msgstr "Comment not found."

#. translators: %d: Number of comments.
#: class.akismet-cli.php:89
msgid "%d comment could not be checked."
msgid_plural "%d comments could not be checked."
msgstr[0] "%d comment could not be checked."
msgstr[1] "%d comments could not be checked."

#. translators: %d: Number of comments.
#: class.akismet-cli.php:85
msgid "%d comment moved to Spam."
msgid_plural "%d comments moved to Spam."
msgstr[0] "%d comment moved to Spam."
msgstr[1] "%d comments moved to Spam."

#. translators: %d: Number of comments.
#: class.akismet-cli.php:82
msgid "Processed %d comment."
msgid_plural "Processed %d comments."
msgstr[0] "Processed %d comment."
msgstr[1] "Processed %d comments."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:45
msgid "Comment #%d could not be checked."
msgstr "Comment #%d could not be checked."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:42
msgid "Failed to connect to Akismet."
msgstr "Failed to connect to Akismet."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:39
msgid "Comment #%d is not spam."
msgstr "Comment #%d is not spam."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:36
msgid "Comment #%d is spam."
msgstr "Comment #%d is spam."

#. translators: %s: number of false positive spam flagged by Akismet
#: views/config.php:66
msgid "%s false positive"
msgid_plural "%s false positives"
msgstr[0] "%s false positive"
msgstr[1] "%s false positives"

#. translators: %s: number of spam missed by Akismet
#: views/config.php:64
msgid "%s missed spam"
msgid_plural "%s missed spam"
msgstr[0] "%s missed spam"
msgstr[1] "%s missed spam"

#: views/notice.php:175
msgid "You don&#8217;t have an Akismet plan."
msgstr "You don&#8217;t have an Akismet plan."

#: views/notice.php:142
msgid "Your Akismet subscription is suspended."
msgstr "Your Akismet subscription is suspended."

#: views/notice.php:131
msgid "Your Akismet plan has been cancelled."
msgstr "Your Akismet plan has been cancelled."

#. translators: The placeholder is a URL.
#: views/notice.php:124
msgid "We cannot process your payment. Please <a href=\"%s\" target=\"_blank\">update your payment details</a>."
msgstr "We cannot process your payment. Please <a href=\"%s\" target=\"_blank\">update your payment details</a>."

#: views/notice.php:120
msgid "Please update your payment information."
msgstr "Please update your payment information."

#. translators: %s: Number of minutes.
#: class.akismet-admin.php:1226
msgid "Akismet has saved you %d minute!"
msgid_plural "Akismet has saved you %d minutes!"
msgstr[0] "Akismet has saved you %d minute!"
msgstr[1] "Akismet has saved you %d minutes!"

#. translators: %s: Number of hours.
#: class.akismet-admin.php:1223
msgid "Akismet has saved you %d hour!"
msgid_plural "Akismet has saved you %d hours!"
msgstr[0] "Akismet has saved you %d hour!"
msgstr[1] "Akismet has saved you %d hours!"

#. translators: %s: Number of days.
#: class.akismet-admin.php:1220
msgid "Akismet has saved you %s day!"
msgid_plural "Akismet has saved you %s days!"
msgstr[0] "Akismet has saved you %s day!"
msgstr[1] "Akismet has saved you %s days!"

#: class.akismet-admin.php:224 class.akismet-admin.php:262
#: class.akismet-admin.php:274
msgid "Akismet filters out spam, so you can focus on more important things."
msgstr "Akismet filters out spam, so you can focus on more important things."

#. translators: The placeholder is a URL.
#: views/notice.php:245
msgid "The connection to akismet.com could not be established. Please refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a> and check your server configuration."
msgstr "The connection to akismet.com could not be established. Please refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a> and check your server configuration."

#: views/notice.php:239
msgid "The API key you entered could not be verified."
msgstr "The API key you entered could not be verified."

#: views/config.php:121
msgid "All systems functional."
msgstr "All systems functional."

#: views/config.php:120
msgid "Enabled."
msgstr "Enabled."

#: views/config.php:118
msgid "Akismet encountered a problem with a previous SSL request and disabled it temporarily. It will begin using SSL for requests again shortly."
msgstr "Akismet encountered a problem with a previous SSL request and disabled it temporarily. It will begin using SSL for requests again shortly."

#: views/config.php:117
msgid "Temporarily disabled."
msgstr "Temporarily disabled."

#: views/config.php:112
msgid "Your Web server cannot make SSL requests; contact your Web host and ask them to add support for SSL requests."
msgstr "Your web server cannot make SSL requests; contact your web host and ask them to add support for SSL requests."

#: views/config.php:111
msgid "Disabled."
msgstr "Disabled."

#: views/config.php:108
msgid "SSL status"
msgstr "SSL status"

#: class.akismet-admin.php:720
msgid "This comment was reported as not spam."
msgstr "This comment was reported as not spam."

#: class.akismet-admin.php:712
msgid "This comment was reported as spam."
msgstr "This comment was reported as spam."

#. Author URI of the plugin
#: akismet.php
msgid "https://automattic.com/wordpress-plugins/"
msgstr "https://automattic.com/wordpress-plugins/"

#. Plugin URI of the plugin
#: akismet.php
msgid "https://akismet.com/"
msgstr "https://akismet.com/"

#: views/enter.php:2
msgid "Manually enter an API key"
msgstr "Manually enter an API key"

#: views/connect-jp.php:53 views/notice.php:333
msgid "Contact Akismet support"
msgstr "Contact Akismet support"

#: views/connect-jp.php:64
msgid "No worries! Get in touch and we&#8217;ll sort this out."
msgstr "No worries! Get in touch and we&#8217;ll sort this out."

#. translators: %s is the WordPress.com email address
#: views/connect-jp.php:60
msgid "Your subscription for %s is suspended."
msgstr "Your subscription for %s is suspended."

#. translators: %s is the WordPress.com email address
#: views/connect-jp.php:45
msgid "Your subscription for %s is cancelled."
msgstr "Your subscription for %s is cancelled."

#: views/notice.php:217
msgid "The key you entered is invalid. Please double-check it."
msgstr "The key you entered is invalid. Please double-check it."

#: views/notice.php:164
msgid "There is a problem with your API key."
msgstr "There is a problem with your API key."

#. translators: the placeholder is a clickable URL to the Akismet account
#. upgrade page.
#: views/notice.php:157
msgid "You can help us fight spam and upgrade your account by <a href=\"%s\" target=\"_blank\">contributing a token amount</a>."
msgstr "You can help us fight spam and upgrade your account by <a href=\"%s\" target=\"_blank\">contributing a token amount</a>."

#. translators: The placeholder is a URL.
#. translators: The placeholder is a URL to the Akismet contact form.
#: views/notice.php:146 views/notice.php:168
msgid "Please contact <a href=\"%s\" target=\"_blank\">Akismet support</a> for assistance."
msgstr "Please contact <a href=\"%s\" target=\"_blank\">Akismet support</a> for assistance."

#. translators: The placeholder is a URL.
#: views/notice.php:135
msgid "Please visit your <a href=\"%s\" target=\"_blank\">Akismet account page</a> to reactivate your subscription."
msgstr "Please visit your <a href=\"%s\" target=\"_blank\">Akismet account page</a> to reactivate your subscription."

#. translators: The placeholder is a URL.
#: views/notice.php:113
msgid "Your firewall may be blocking Akismet from connecting to its API. Please contact your host and refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a>."
msgstr "Your firewall may be blocking Akismet from connecting to its API. Please contact your host and refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a>."

#. translators: The placeholder is a URL.
#: views/notice.php:102
msgid "Your web host or server administrator has disabled PHP&#8217;s <code>gethostbynamel</code> function.  <strong>Akismet cannot work correctly until this is fixed.</strong>  Please contact your web host or firewall administrator and give them <a href=\"%s\" target=\"_blank\">this information about Akismet&#8217;s system requirements</a>."
msgstr "Your web host or server administrator has disabled PHP&#8217;s <code>gethostbynamel</code> function.  <strong>Akismet cannot work correctly until this is fixed.</strong>  Please contact your web host or firewall administrator and give them <a href=\"%s\" target=\"_blank\">this information about Akismet&#8217;s system requirements</a>."

#: views/notice.php:98
msgid "Network functions are disabled."
msgstr "Network functions are disabled."

#. translators: the placeholder is a clickable URL that leads to more
#. information regarding an error code.
#: views/notice.php:83
msgid "For more information: %s"
msgstr "For more information: %s"

#. translators: The placeholder is an error code returned by Akismet.
#: views/notice.php:78
msgid "Akismet error code: %s"
msgstr "Akismet error code: %s"

#: views/notice.php:37
msgid "Some comments have not yet been checked for spam by Akismet. They have been temporarily held for moderation and will automatically be rechecked later."
msgstr "Some comments have not yet been checked for spam by Akismet. They have been temporarily held for moderation and will automatically be rechecked later."

#: views/notice.php:36 views/notice.php:46
msgid "Akismet has detected a problem."
msgstr "Akismet has detected a problem."

#: views/config.php:312
msgid "Change"
msgstr "Change"

#: views/config.php:312
msgid "Upgrade"
msgstr "Upgrade"

#: views/config.php:293
msgid "Next billing date"
msgstr "Next billing date"

#: views/config.php:286
msgid "Active"
msgstr "Active"

#: views/config.php:284
msgid "No subscription found"
msgstr "No subscription found"

#: views/config.php:282
msgid "Missing"
msgstr "Missing"

#: views/config.php:280
msgid "Suspended"
msgstr "Suspended"

#: views/config.php:278
msgid "Cancelled"
msgstr "Cancelled"

#: views/config.php:249
msgid "Save changes"
msgstr "Save changes"

#: views/config.php:241
msgid "Disconnect this account"
msgstr "Disconnect this account"

#: views/config.php:180
msgid "Note:"
msgstr "Note:"

#: views/config.php:173
msgid "Always put spam in the Spam folder for review."
msgstr "Always put spam in the Spam folder for review."

#: views/config.php:165
msgid "Silently discard the worst and most pervasive spam so I never see it."
msgstr "Silently discard the worst and most pervasive spam so I never see it."

#: views/config.php:159
msgid "Akismet Anti-spam strictness"
msgstr "Akismet Anti-spam strictness"

#: views/config.php:146
msgid "Show the number of approved comments beside each comment author."
msgstr "Show the number of approved comments beside each comment author."

#: views/config.php:59
msgid "Accuracy"
msgstr "Accuracy"

#: views/config.php:54
msgid "All time"
msgstr "All time"

#: views/config.php:51 views/config.php:56
msgid "Spam blocked"
msgid_plural "Spam blocked"
msgstr[0] "Spam blocked"
msgstr[1] ""

#: views/config.php:49
msgid "Past six months"
msgstr "Past six months"

#. translators: 1: WordPress documentation URL, 2: Akismet download URL.
#: class.akismet.php:1732
msgid "Please <a href=\"%1$s\">upgrade WordPress</a> to a current version, or <a href=\"%2$s\">downgrade to version 2.4 of the Akismet plugin</a>."
msgstr "Please <a href=\"%1$s\">upgrade WordPress</a> to a current version, or <a href=\"%2$s\">downgrade to version 2.4 of the Akismet plugin</a>."

#: class.akismet-admin.php:727
msgid "Akismet cleared this comment during an automatic retry."
msgstr "Akismet cleared this comment during an automatic retry."

#: class.akismet-admin.php:724
msgid "Akismet caught this comment as spam during an automatic retry."
msgstr "Akismet caught this comment as spam during an automatic retry."

#. translators: The placeholder is a username.
#: class.akismet-admin.php:718
msgid "%s reported this comment as not spam."
msgstr "%s reported this comment as not spam."

#. translators: The placeholder is a username.
#: class.akismet-admin.php:710
msgid "%s reported this comment as spam."
msgstr "%s reported this comment as spam."

#. translators: %1$s is a username; %2$s is a short string (like 'spam' or
#. 'approved') denoting the new comment status.
#: class.akismet-admin.php:775
msgid "%1$s changed the comment status to %2$s."
msgstr "%1$s changed the comment status to %2$s."

#. translators: The placeholder is an error response returned by the API
#. server.
#: class.akismet-admin.php:732
msgid "Akismet was unable to check this comment (response: %s) but will automatically retry later."
msgstr "Akismet was unable to check this comment (response: %s) but will automatically retry later."

#: class.akismet-admin.php:697
msgid "Akismet cleared this comment."
msgstr "Akismet cleared this comment."

#. translators: The placeholder is a short string (like 'spam' or 'approved')
#. denoting the new comment status.
#: class.akismet-admin.php:769
msgid "Comment status was changed to %s"
msgstr "Comment status was changed to %s"

#: class.akismet-admin.php:691
msgid "Akismet caught this comment as spam."
msgstr "Akismet caught this comment as spam."

#. translators: The placeholder is the number of pieces of spam blocked by
#. Akismet.
#: class.akismet-widget.php:135
msgid "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"
msgid_plural "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"
msgstr[0] "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"
msgstr[1] "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"

#: class.akismet-widget.php:99
msgid "Title:"
msgstr "Title:"

#: class.akismet-widget.php:94 class.akismet-widget.php:116
msgid "Spam Blocked"
msgstr "Spam Blocked"

#: class.akismet-widget.php:17
msgid "Display the number of spam comments Akismet has caught"
msgstr "Display the number of spam comments Akismet has caught"

#: class.akismet-widget.php:16
msgid "Akismet Widget"
msgstr "Akismet Widget"

#: class.akismet-admin.php:1216
msgid "Cleaning up spam takes time."
msgstr "Cleaning up spam takes time."

#. translators: The Akismet configuration page URL.
#: class.akismet-admin.php:1088
msgid "Please check your <a href=\"%s\">Akismet configuration</a> and contact your web host if problems persist."
msgstr "Please check your <a href=\"%s\">Akismet configuration</a> and contact your web host if problems persist."

#. translators: The placeholder is an amount of time, like "7 seconds" or "3
#. days" returned by the function human_time_diff().
#: class.akismet-admin.php:789
msgid "%s ago"
msgstr "%s ago"

#. translators: %s: Number of comments.
#: class.akismet-admin.php:664
msgid "%s approved"
msgid_plural "%s approved"
msgstr[0] "%s approved"
msgstr[1] "%s approved"

#: class.akismet-admin.php:638
msgid "History"
msgstr "History"

#: class.akismet-admin.php:638 class.akismet-admin.php:646
msgid "View comment history"
msgstr "View comment history"

#. translators: %s: Username.
#: class.akismet-admin.php:625
msgid "Un-spammed by %s"
msgstr "Un-spammed by %s"

#. translators: %s: Username.
#: class.akismet-admin.php:622
msgid "Flagged as spam by %s"
msgstr "Flagged as spam by %s"

#: class.akismet-admin.php:616
msgid "Cleared by Akismet"
msgstr "Cleared by Akismet"

#: class.akismet-admin.php:614
msgid "Flagged as spam by Akismet"
msgstr "Flagged as spam by Akismet"

#: class.akismet-admin.php:610
msgid "Awaiting spam check"
msgstr "Awaiting spam check"

#. translators: The placeholder is an error response returned by the API
#. server.
#: class.akismet-admin.php:740
msgid "Akismet was unable to recheck this comment (response: %s)."
msgstr "Akismet was unable to recheck this comment (response: %s)."

#: class.akismet-admin.php:694
msgid "Akismet re-checked and cleared this comment."
msgstr "Akismet re-checked and cleared this comment."

#: class.akismet-admin.php:688
msgid "Akismet re-checked and caught this comment as spam."
msgstr "Akismet re-checked and caught this comment as spam."

#: class.akismet-admin.php:498
msgid "Check for Spam"
msgstr "Check for Spam"

#. translators: %s: Comments page URL.
#: class.akismet-admin.php:440
msgid "There&#8217;s nothing in your <a href='%s'>spam queue</a> at the moment."
msgstr "There&#8217;s nothing in your <a href='%s'>spam queue</a> at the moment."

#. translators: 1: Number of comments, 2: Comments page URL.
#: class.akismet-admin.php:429
msgid "There&#8217;s <a href=\"%2$s\">%1$s comment</a> in your spam queue right now."
msgid_plural "There are <a href=\"%2$s\">%1$s comments</a> in your spam queue right now."
msgstr[0] "There&#8217;s <a href=\"%2$s\">%1$s comment</a> in your spam queue just now."
msgstr[1] "There are <a href=\"%2$s\">%1$s comments</a> in your spam queue just now."

#. translators: %s: Akismet website URL.
#: class.akismet-admin.php:421
msgid "<a href=\"%s\">Akismet</a> blocks spam from getting to your blog. "
msgstr "<a href=\"%s\">Akismet</a> blocks spam from getting to your blog. "

#. translators: 1: Akismet website URL, 2: Number of spam comments.
#: class.akismet-admin.php:410
msgid "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comment already. "
msgid_plural "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comments already. "
msgstr[0] "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comment already. "
msgstr[1] "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comments already. "

#. translators: 1: Akismet website URL, 2: Comments page URL, 3: Number of spam
#. comments.
#: class.akismet-admin.php:393
msgid "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comment</a>."
msgid_plural "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comments</a>."
msgstr[0] "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comment</a>."
msgstr[1] "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comments</a>."

#: class.akismet-admin.php:389
msgctxt "comments"
msgid "Spam"
msgstr "Spam"

#: class.akismet-admin.php:316
msgid "Cheatin&#8217; uh?"
msgstr "Cheating, are we?"

#: class.akismet-admin.php:310
msgid "Akismet Support"
msgstr "Akismet Support"

#: class.akismet-admin.php:309
msgid "Akismet FAQ"
msgstr "Akismet FAQ"

#: class.akismet-admin.php:308
msgid "For more information:"
msgstr "For more information:"

#: class.akismet-admin.php:299
msgid "The subscription status - active, cancelled or suspended"
msgstr "The subscription status - active, cancelled or suspended"

#: class.akismet-admin.php:299 views/config.php:274
msgid "Status"
msgstr "Status"

#: class.akismet-admin.php:298
msgid "The Akismet subscription plan"
msgstr "The Akismet subscription plan"

#: class.akismet-admin.php:298
msgid "Subscription Type"
msgstr "Subscription Type"

#: class.akismet-admin.php:295 views/config.php:260
msgid "Account"
msgstr "Account"

#: class.akismet-admin.php:287
msgid "Choose to either discard the worst spam automatically or to always put all spam in spam folder."
msgstr "Choose to either discard the worst spam automatically or to always put all spam in spam folder."

#: class.akismet-admin.php:287
msgid "Strictness"
msgstr "Strictness"

#: class.akismet-admin.php:286
msgid "Show the number of approved comments beside each comment author in the comments list page."
msgstr "Show the number of approved comments beside each comment author in the comments list page."

#: class.akismet-admin.php:286 views/config.php:131
msgid "Comments"
msgstr "Comments"

#: class.akismet-admin.php:285
msgid "Enter/remove an API key."
msgstr "Enter/remove an API key."

#: class.akismet-admin.php:285
msgid "API Key"
msgstr "API Key"

#: class.akismet-admin.php:273 class.akismet-admin.php:284
#: class.akismet-admin.php:297
msgid "Akismet Configuration"
msgstr "Akismet Configuration"

#: class.akismet-admin.php:263
msgid "On this page, you are able to view stats on spam filtered on your site."
msgstr "On this page, you are able to view stats on spam filtered on your site."

#: class.akismet-admin.php:261
msgid "Akismet Stats"
msgstr "Akismet Stats"

#: class.akismet-admin.php:250
msgid "Click the Use this Key button."
msgstr "Click the Use this Key button."

#: class.akismet-admin.php:249
msgid "Copy and paste the API key into the text field."
msgstr "Copy and paste the API key into the text field."

#: class.akismet-admin.php:247
msgid "If you already have an API key"
msgstr "If you already have an API key"

#: class.akismet-admin.php:244
msgid "Enter an API Key"
msgstr "Enter an API Key"

#. translators: %s: a link to the signup page with the text 'Akismet.com'.
#: class.akismet-admin.php:237
msgid "Sign up for an account on %s to get an API Key."
msgstr "Sign up for an account on %s to get an API Key."

#: class.akismet-admin.php:235
msgid "You need to enter an API key to activate the Akismet service on your site."
msgstr "You need to enter an API key to activate the Akismet service on your site."

#: class.akismet-admin.php:232
msgid "New to Akismet"
msgstr "New to Akismet"

#: class.akismet-admin.php:225
msgid "On this page, you are able to set up the Akismet plugin."
msgstr "On this page, you are able to set up the Akismet plugin."

#: class.akismet-admin.php:223 class.akismet-admin.php:234
#: class.akismet-admin.php:246
msgid "Akismet Setup"
msgstr "Akismet Setup"

#: class.akismet-admin.php:221 class.akismet-admin.php:259
#: class.akismet-admin.php:271
msgid "Overview"
msgstr "Overview"

#: class.akismet-admin.php:190
msgid "Re-adding..."
msgstr "Re-adding..."

#: class.akismet-admin.php:189
msgid "(undo)"
msgstr "(undo)"

#: class.akismet-admin.php:188
msgid "URL removed"
msgstr "URL removed"

#: class.akismet-admin.php:187
msgid "Removing..."
msgstr "Removing..."

#: class.akismet-admin.php:186
msgid "Remove this URL"
msgstr "Remove this URL"

#: class.akismet-admin.php:107 class.akismet-admin.php:1463
msgid "Akismet"
msgstr "Akismet"

#: class.akismet-admin.php:128 class.akismet-admin.php:282
#: class.akismet-admin.php:816 views/config.php:83
msgid "Settings"
msgstr "Settings"

#: class.akismet-admin.php:103
msgid "Comment History"
msgstr "Comment History"