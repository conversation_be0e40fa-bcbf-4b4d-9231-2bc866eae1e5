<?php
/**
 * Template for the UM User Photos, The single "Album" block
 *
 * Page: "Profile", tab "Photos"
 * Parent template: gallery.php
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/album-block.php
 * @var int $image_id
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
?>
<div class="um-user-photos-comment-form">
	<div class="um-user-photos-comment-box">
		<?php echo wp_kses( UM()->frontend()::layouts()::single_avatar( get_current_user_id(), array( 'size' => 'm' ) ), UM()->get_allowed_html( 'templates' ) ); ?>
		<textarea class="um-user-photos-comment-textarea" placeholder="<?php esc_attr_e( 'Write a comment...', 'um-user-photos' ); ?>"></textarea>
	</div>
	<div class="um-form-submit">
		<?php
		$loader = UM()->frontend()::layouts()::ajax_loader( 's', array( 'classes' => array( 'um-user-photos-loader', 'um-display-none' ) ) );
		$submit = UM()->frontend()::layouts()::button(
			esc_html__( 'Comment', 'um-user-photos' ),
			array(
				'design'   => 'primary',
				'size'     => 's',
				'disabled' => true,
				'classes'  => array(
					'um-user-photos-comment-post',
				),
				'data'     => array(
					'image'   => $image_id,
					'wpnonce' => wp_create_nonce( 'um_user_photos_comment_post' ),
				),
			)
		);
		echo wp_kses( $loader . $submit, UM()->get_allowed_html( 'templates' ) );
		?>
	</div>
</div>
