"use strict";window.WPFormsPasswordField=window.WPFormsPasswordField||function(a){var d={passwordStrength(s,r){var t,r=a(r);let e=r.closest(".wpforms-field").find(".wpforms-pass-strength-result");return(""!==r.val().trim()||r.hasClass("wpforms-field-required"))&&(e.length||(e=a('<div class="wpforms-pass-strength-result"></div>')).css("max-width",r.css("max-width")),e.removeClass("short bad good strong empty"),s)&&""!==s.trim()?(t=Object.prototype.hasOwnProperty.call(wp.passwordStrength,"userInputDisallowedList")?wp.passwordStrength.userInputDisallowedList():wp.passwordStrength.userInputBlacklist(),t=wp.passwordStrength.meter(s,t,s),(e=d.updateStrengthResultEl(e,t)).insertAfter(r),r.addClass("wpforms-error-pass-strength"),t):(e.remove(),r.removeClass("wpforms-error-pass-strength"),0)},updateStrengthResultEl:function(s,r){switch(r){case-1:s.addClass("bad").html(pwsL10n.unknown);break;case 2:s.addClass("bad").html(pwsL10n.bad);break;case 3:s.addClass("good").html(pwsL10n.good);break;case 4:s.addClass("strong").html(pwsL10n.strong);break;default:s.addClass("short").html(pwsL10n.short)}return s}};return d}((document,window,jQuery));