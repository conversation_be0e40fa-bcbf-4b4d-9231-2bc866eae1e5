<?php

/**
 * This file is part of the league/oauth2-client library
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @copyright Copyright (c) <PERSON> <<EMAIL>>
 * @license http://opensource.org/licenses/MIT MIT
 * @link http://thephpleague.com/oauth2-client/ Documentation
 * @link https://packagist.org/packages/league/oauth2-client Packagist
 * @link https://github.com/thephpleague/oauth2-client GitHub
 */
namespace YoastSEO_Vendor\League\OAuth2\Client\Provider;

use InvalidArgumentException;
use YoastSEO_Vendor\League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use YoastSEO_Vendor\League\OAuth2\Client\Token\AccessToken;
use YoastSEO_Vendor\League\OAuth2\Client\Tool\BearerAuthorizationTrait;
use YoastSEO_Vendor\Psr\Http\Message\ResponseInterface;
/**
 * Represents a generic service provider that may be used to interact with any
 * OAuth 2.0 service provider, using Bearer token authentication.
 */
class GenericProvider extends \YoastSEO_Vendor\League\OAuth2\Client\Provider\AbstractProvider
{
    use BearerAuthorizationTrait;
    /**
     * @var string
     */
    private $urlAuthorize;
    /**
     * @var string
     */
    private $urlAccessToken;
    /**
     * @var string
     */
    private $urlResourceOwnerDetails;
    /**
     * @var string
     */
    private $accessTokenMethod;
    /**
     * @var string
     */
    private $accessTokenResourceOwnerId;
    /**
     * @var array|null
     */
    private $scopes = null;
    /**
     * @var string
     */
    private $scopeSeparator;
    /**
     * @var string
     */
    private $responseError = 'error';
    /**
     * @var string
     */
    private $responseCode;
    /**
     * @var string
     */
    private $responseResourceOwnerId = 'id';
    /**
     * @var string|null
     */
    private $pkceMethod = null;
    /**
     * @param array $options
     * @param array $collaborators
     */
    public function __construct(array $options = [], array $collaborators = [])
    {
        $this->assertRequiredOptions($options);
        $possible = $this->getConfigurableOptions();
        $configured = \array_intersect_key($options, \array_flip($possible));
        foreach ($configured as $key => $value) {
            $this->{$key} = $value;
        }
        // Remove all options that are only used locally
        $options = \array_diff_key($options, $configured);
        parent::__construct($options, $collaborators);
    }
    /**
     * Returns all options that can be configured.
     *
     * @return array
     */
    protected function getConfigurableOptions()
    {
        return \array_merge($this->getRequiredOptions(), ['accessTokenMethod', 'accessTokenResourceOwnerId', 'scopeSeparator', 'responseError', 'responseCode', 'responseResourceOwnerId', 'scopes', 'pkceMethod']);
    }
    /**
     * Returns all options that are required.
     *
     * @return array
     */
    protected function getRequiredOptions()
    {
        return ['urlAuthorize', 'urlAccessToken', 'urlResourceOwnerDetails'];
    }
    /**
     * Verifies that all required options have been passed.
     *
     * @param  array $options
     * @return void
     * @throws InvalidArgumentException
     */
    private function assertRequiredOptions(array $options)
    {
        $missing = \array_diff_key(\array_flip($this->getRequiredOptions()), $options);
        if (!empty($missing)) {
            throw new \InvalidArgumentException('Required options not defined: ' . \implode(', ', \array_keys($missing)));
        }
    }
    /**
     * @inheritdoc
     */
    public function getBaseAuthorizationUrl()
    {
        return $this->urlAuthorize;
    }
    /**
     * @inheritdoc
     */
    public function getBaseAccessTokenUrl(array $params)
    {
        return $this->urlAccessToken;
    }
    /**
     * @inheritdoc
     */
    public function getResourceOwnerDetailsUrl(\YoastSEO_Vendor\League\OAuth2\Client\Token\AccessToken $token)
    {
        return $this->urlResourceOwnerDetails;
    }
    /**
     * @inheritdoc
     */
    public function getDefaultScopes()
    {
        return $this->scopes;
    }
    /**
     * @inheritdoc
     */
    protected function getAccessTokenMethod()
    {
        return $this->accessTokenMethod ?: parent::getAccessTokenMethod();
    }
    /**
     * @inheritdoc
     */
    protected function getAccessTokenResourceOwnerId()
    {
        return $this->accessTokenResourceOwnerId ?: parent::getAccessTokenResourceOwnerId();
    }
    /**
     * @inheritdoc
     */
    protected function getScopeSeparator()
    {
        return $this->scopeSeparator ?: parent::getScopeSeparator();
    }
    /**
     * @inheritdoc
     */
    protected function getPkceMethod()
    {
        return $this->pkceMethod ?: parent::getPkceMethod();
    }
    /**
     * @inheritdoc
     */
    protected function checkResponse(\YoastSEO_Vendor\Psr\Http\Message\ResponseInterface $response, $data)
    {
        if (!empty($data[$this->responseError])) {
            $error = $data[$this->responseError];
            if (!\is_string($error)) {
                $error = \var_export($error, \true);
            }
            $code = $this->responseCode && !empty($data[$this->responseCode]) ? $data[$this->responseCode] : 0;
            if (!\is_int($code)) {
                $code = \intval($code);
            }
            throw new \YoastSEO_Vendor\League\OAuth2\Client\Provider\Exception\IdentityProviderException($error, $code, $data);
        }
    }
    /**
     * @inheritdoc
     */
    protected function createResourceOwner(array $response, \YoastSEO_Vendor\League\OAuth2\Client\Token\AccessToken $token)
    {
        return new \YoastSEO_Vendor\League\OAuth2\Client\Provider\GenericResourceOwner($response, $this->responseResourceOwnerId);
    }
}
