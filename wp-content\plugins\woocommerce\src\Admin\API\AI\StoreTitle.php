<?php

declare( strict_types = 1 );

namespace Automattic\WooCommerce\Admin\API\AI;

use Automattic\WooCommerce\Blocks\AI\Connection;
use WP_Error;
use WP_REST_Request;
use WP_REST_Response;

defined( 'ABSPATH' ) || exit;

/**
 * Store Title controller
 *
 * @internal
 */
class StoreTitle extends AIEndpoint {
	/**
	 * The store title option name.
	 *
	 * @var string
	 */
	const STORE_TITLE_OPTION_NAME = 'blogname';

	/**
	 * The AI generated store title option name.
	 *
	 * @var string
	 */
	const AI_STORE_TITLE_OPTION_NAME = 'ai_generated_site_title';

	/**
	 * The default store title.
	 *
	 * @var string
	 */
	const DEFAULT_TITLE = 'Site Title';

	/**
	 * Endpoint.
	 *
	 * @var string
	 */
	protected $endpoint = 'store-title';

	/**
	 * Register routes.
	 */
	public function register_routes() {
		$this->register(
			array(
				array(
					'methods'             => \WP_REST_Server::CREATABLE,
					'callback'            => array( $this, 'update_store_title' ),
					'permission_callback' => array( Middleware::class, 'is_authorized' ),
					'args'                => array(
						'business_description' => array(
							'description' => __( 'The business description for a given store.', 'woocommerce' ),
							'type'        => 'string',
						),
					),
				),
				'schema' => array( $this, 'get_schema' ),
			)
		);
	}

	/**
	 * Update the store title powered by AI.
	 *
	 * @param  WP_REST_Request $request Request object.
	 *
	 * @return WP_Error|WP_REST_Response
	 */
	public function update_store_title( $request ) {

		$business_description = $request->get_param( 'business_description' );

		if ( ! $business_description ) {
			return new WP_Error(
				'invalid_business_description',
				__( 'Invalid business description.', 'woocommerce' )
			);
		}

		$store_title                 = html_entity_decode( get_option( self::STORE_TITLE_OPTION_NAME, '' ) );
		$previous_ai_generated_title = html_entity_decode( get_option( self::AI_STORE_TITLE_OPTION_NAME, '' ) );

		if ( strtolower( trim( self::DEFAULT_TITLE ) ) === strtolower( trim( $store_title ) ) || ( ! empty( $store_title ) && $previous_ai_generated_title !== $store_title ) ) {
			return rest_ensure_response( array( 'ai_content_generated' => false ) );
		}

		$ai_generated_title = $this->generate_ai_title( $business_description );
		if ( is_wp_error( $ai_generated_title ) ) {
			return $ai_generated_title;
		}

		update_option( self::AI_STORE_TITLE_OPTION_NAME, $ai_generated_title );
		update_option( self::STORE_TITLE_OPTION_NAME, $ai_generated_title );

		return rest_ensure_response(
			array(
				'ai_content_generated' => true,
			)
		);
	}


	/**
	 * Generate the store title powered by AI.
	 *
	 * @param string $business_description The business description for a given store.
	 *
	 * @return string|WP_Error|WP_REST_Response The store title generated by AI.
	 */
	private function generate_ai_title( $business_description ) {
		$ai_connection = new Connection();

		$site_id = $ai_connection->get_site_id();
		if ( is_wp_error( $site_id ) ) {
			return $site_id;
		}

		$token = $ai_connection->get_jwt_token( $site_id );
		if ( is_wp_error( $token ) ) {
			return $token;
		}

		$prompt = "Generate a store title for a store that has the following: '$business_description'. The length of the title should be 1 and 3 words. The result should include only the store title without any other explanation, number or punctuation marks";

		$ai_response = $ai_connection->fetch_ai_response( $token, $prompt );
		if ( is_wp_error( $ai_response ) ) {
			return $ai_response;
		}

		if ( ! isset( $ai_response['completion'] ) ) {
			return '';
		}

		return $ai_response['completion'];
	}

	/**
	 * Get the Business Description response.
	 *
	 * @return array
	 */
	public function get_schema() {
		return array(
			'ai_content_generated' => true,
		);
	}
}
