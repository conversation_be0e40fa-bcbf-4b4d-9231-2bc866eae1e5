<?php
namespace um_ext\um_user_photos\admin;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Settings
 * @package um_ext\um_user_photos\admin
 */
class Settings {

	/**
	 * Settings constructor.
	 */
	public function __construct() {
		add_filter( 'um_settings_structure', array( &$this, 'extend_settings' ) );
		add_filter( 'um_settings_map', array( &$this, 'settings_map' ) );

		add_filter( 'um_override_templates_scan_files', array( &$this, 'um_user_photos_extend_scan_files' ) );
		add_filter( 'um_override_templates_get_template_path__um-user-photos', array( &$this, 'um_user_photos_get_path_template' ), 10, 2 );
	}

	/**
	 * Additional Settings for Photos.
	 *
	 * @param array $settings
	 *
	 * @return array
	 */
	public function extend_settings( $settings ) {
		$settings['licenses']['fields'][] = array(
			'id'        => 'um_user_photos_license_key',
			'label'     => __( 'User Photos License Key', 'um-user-photos' ),
			'item_name' => 'User Photos',
			'author'    => 'ultimatemember',
			'version'   => UM_USER_PHOTOS_VERSION,
		);

		if ( defined( 'UM_DEV_MODE' ) && UM_DEV_MODE && UM()->options()->get( 'enable_new_ui' ) ) {
			$settings_fields = array(
				array(
					'id'      => 'um_user_photos_albums_column',
					'type'    => 'select',
					'options' => array(
						1 => __( '1 column', 'um-user-photos' ),
						2 => __( '2 columns', 'um-user-photos' ),
						3 => __( '3 columns', 'um-user-photos' ),
						4 => __( '4 columns', 'um-user-photos' ),
						5 => __( '5 columns', 'um-user-photos' ),
						6 => __( '6 columns', 'um-user-photos' ),
					),
					'label'   => __( 'Album columns', 'um-user-photos' ),
					'size'    => 'medium',
				),
				array(
					'id'      => 'um_user_photos_images_column',
					'type'    => 'select',
					'options' => array(
						1 => __( '1 column', 'um-user-photos' ),
						2 => __( '2 columns', 'um-user-photos' ),
						3 => __( '3 columns', 'um-user-photos' ),
						4 => __( '4 columns', 'um-user-photos' ),
						5 => __( '5 columns', 'um-user-photos' ),
						6 => __( '6 columns', 'um-user-photos' ),
					),
					'label'   => __( 'Photo columns', 'um-user-photos' ),
					'size'    => 'medium',
				),
				array(
					'id'      => 'um_user_photos_images_row',
					'type'    => 'select',
					'options' => array(
						1 => __( 'Single row', 'um-user-photos' ),
						2 => __( '2 rows', 'um-user-photos' ),
						3 => __( '3 rows', 'um-user-photos' ),
						4 => __( '4 rows', 'um-user-photos' ),
						5 => __( '5 rows', 'um-user-photos' ),
						6 => __( '6 rows', 'um-user-photos' ),
					),
					'label'   => __( 'Photo rows', 'um-user-photos' ),
					'size'    => 'medium',
				),
				array(
					'id'          => 'um_user_photos_album_limit',
					'type'        => 'number',
					'label'       => __( 'Album photos limit', 'um-user-photos' ),
					'description' => __( 'Limit of photos in one album. Empty for unlimited.', 'um-user-photos' ),
					'size'        => 'small',
				),
				array(
					'id'          => 'um_user_photos_cover_size',
					'type'        => 'text',
					'placeholder' => __( 'Default : 350 x 350', 'um-user-photos' ),
					'label'       => __( 'Album Cover size', 'um-user-photos' ),
					'description' => __( 'You will need to regenerate thumbnails once this value is changed', 'um-user-photos' ),
					'size'        => 'small',
				),
				array(
					'id'          => 'um_user_photos_image_size',
					'type'        => 'text',
					'placeholder' => __( 'Default : 250 x 250', 'um-user-photos' ),
					'label'       => __( 'Photo thumbnail size', 'um-user-photos' ),
					'description' => __( 'You will need to regenerate thumbnails once this value is changed', 'um-user-photos' ),
					'size'        => 'small',
				),
				array(
					'id'             => 'um_user_photos_disable_cover',
					'type'           => 'checkbox',
					'label'          => __( 'Album cover photo', 'um-user-photos' ),
					'checkbox_label' => __( 'Disable cover photo', 'ultimate-member' ),
					'description'    => __( 'If disabled, then first album photo will be applied as cover.', 'um-user-photos' ),
				),
				array(
					'id'             => 'um_user_photos_disable_title',
					'type'           => 'checkbox',
					'label'          => __( 'Album title', 'um-user-photos' ),
					'checkbox_label' => __( 'Disable album title', 'ultimate-member' ),
					'description'    => __( 'Title field will be hidden.', 'um-user-photos' ),
				),
				array(
					'id'             => 'um_user_photos_disable_comments',
					'type'           => 'checkbox',
					'label'          => __( 'Photo comments & likes', 'um-user-photos' ),
					'checkbox_label' => __( 'Disable comment & like feature', 'ultimate-member' ),
					'description'    => __( 'Prevents creation of comments and likes on the single photo.', 'um-user-photos' ),
				),
				array(
					'id'             => 'um_user_photos_media_library',
					'type'           => 'checkbox',
					'label'          => __( 'Photos in WordPress Media', 'um-user-photos' ),
					'checkbox_label' => __( 'Display photos in WordPress Media', 'ultimate-member' ),
					'description'    => __( 'Display photos uploaded to albums in the WordPress Media.', 'um-user-photos' ),
				),
			);
		} else {
			$settings_fields = array(
				array(
					'id'          => 'um_user_photos_albums_column',
					'type'        => 'select',
					'placeholder' => '',
					'options'     => array(
						''                     => __( 'No. of columns', 'um-user-photos' ),
						'um-user-photos-col-2' => __( '2 columns', 'um-user-photos' ),
						'um-user-photos-col-3' => __( '3 columns', 'um-user-photos' ),
						'um-user-photos-col-4' => __( '4 columns', 'um-user-photos' ),
						'um-user-photos-col-5' => __( '5 columns', 'um-user-photos' ),
						'um-user-photos-col-6' => __( '6 columns', 'um-user-photos' ),
					),
					'label'       => __( 'Album columns', 'um-user-photos' ),
					'size'        => 'medium',
				),
				array(
					'id'      => 'um_user_photos_images_column',
					'type'    => 'select',
					'options' => array(
						''                     => __( 'No. of columns', 'um-user-photos' ),
						'um-user-photos-col-2' => __( '2 columns', 'um-user-photos' ),
						'um-user-photos-col-3' => __( '3 columns', 'um-user-photos' ),
						'um-user-photos-col-4' => __( '4 columns', 'um-user-photos' ),
						'um-user-photos-col-5' => __( '5 columns', 'um-user-photos' ),
						'um-user-photos-col-6' => __( '6 columns', 'um-user-photos' ),
					),
					'label'   => __( 'Photo columns', 'um-user-photos' ),
					'size'    => 'medium',
				),
				array(
					'id'      => 'um_user_photos_images_row',
					'type'    => 'select',
					'options' => array(
						1 => __( 'Single row', 'um-user-photos' ),
						2 => __( '2 rows', 'um-user-photos' ),
						3 => __( '3 rows', 'um-user-photos' ),
						4 => __( '4 rows', 'um-user-photos' ),
						5 => __( '5 rows', 'um-user-photos' ),
						6 => __( '6 rows', 'um-user-photos' ),
					),
					'label'   => __( 'Photo rows', 'um-user-photos' ),
					'size'    => 'medium',
				),
				array(
					'id'      => 'um_user_photos_album_limit',
					'type'    => 'number',
					'label'   => __( 'Album photos limit', 'um-user-photos' ),
					'tooltip' => __( 'Limit of photos in one album. Empty for unlimited. There is a one-time photo upload limit on your server - max_file_uploads = ', 'um-user-photos' ) . ini_get( 'max_file_uploads' ),
					'size'    => 'small',
				),
				array(
					'id'          => 'um_user_photos_cover_size',
					'type'        => 'text',
					'placeholder' => __( 'Default : 350 x 350', 'um-user-photos' ),
					'label'       => __( 'Album Cover size', 'um-user-photos' ),
					'tooltip'     => __( 'You will need to regenerate thumbnails once this value is changed', 'um-user-photos' ),
					'size'        => 'small',
				),
				array(
					'id'          => 'um_user_photos_image_size',
					'type'        => 'text',
					'placeholder' => __( 'Default : 250 x 250', 'um-user-photos' ),
					'label'       => __( 'Photo thumbnail size', 'um-user-photos' ),
					'tooltip'     => __( 'You will need to regenerate thumbnails once this value is changed', 'um-user-photos' ),
					'size'        => 'small',
				),
				array(
					'id'      => 'um_user_photos_disable_cover',
					'type'    => 'checkbox',
					'label'   => __( 'Disable cover photo', 'um-user-photos' ),
					'tooltip' => __( 'Album cover field will be hidden.', 'um-user-photos' ),
					'size'    => 'small',
				),
				array(
					'id'      => 'um_user_photos_disable_title',
					'type'    => 'checkbox',
					'label'   => __( 'Disable album title', 'um-user-photos' ),
					'tooltip' => __( 'Title field will be hidden.', 'um-user-photos' ),
					'size'    => 'small',
				),
				array(
					'id'          => 'um_user_photos_disable_comments',
					'type'        => 'checkbox',
					'placeholder' => __( 'Disable comments', 'um-user-photos' ),
					'label'       => __( 'Disable comment & Like feature', 'um-user-photos' ),
					'tooltip'     => __( 'Disable comments and like features', 'um-user-photos' ),
					'size'        => 'small',
				),
				array(
					'id'      => 'um_user_photos_media_library',
					'type'    => 'checkbox',
					'label'   => __( 'Display photos in Media Library', 'um-user-photos' ),
					'tooltip' => __( 'Display photos uploaded to albums in the Media Library', 'um-user-photos' ),
				),
			);
		}

		$settings['extensions']['sections']['um-user-photos'] = array(
			'title'       => __( 'User Photos', 'um-user-photos' ),
			'description' => __( 'Provides settings for controlling User Photos extension on your site.', 'um-user-photos' ),
			'fields'      => $settings_fields,
		);

		return $settings;
	}

	/**
	 * @param array $settings_map
	 *
	 * @return array
	 */
	public function settings_map( $settings_map ) {
		$columns = 'text';
		if ( defined( 'UM_DEV_MODE' ) && UM_DEV_MODE && UM()->options()->get( 'enable_new_ui' ) ) {
			$columns = 'absint';
		}
		return array_merge(
			$settings_map,
			array(
				'um_user_photos_albums_column'    => array(
					'sanitize' => $columns,
				),
				'um_user_photos_images_column'    => array(
					'sanitize' => $columns,
				),
				'um_user_photos_images_row'       => array(
					'sanitize' => 'absint',
				),
				'um_user_photos_album_limit'      => array(
					'sanitize' => 'text',
				),
				'um_user_photos_cover_size'       => array(
					'sanitize' => 'text',
				),
				'um_user_photos_image_size'       => array(
					'sanitize' => 'text',
				),
				'um_user_photos_disable_cover'    => array(
					'sanitize' => 'bool',
				),
				'um_user_photos_disable_title'    => array(
					'sanitize' => 'bool',
				),
				'um_user_photos_disable_comments' => array(
					'sanitize' => 'bool',
				),
				'um_user_photos_media_library'    => array(
					'sanitize' => 'bool',
				),
			)
		);
	}

	/**
	 * Scan templates from extension
	 *
	 * @param $scan_files
	 *
	 * @return array
	 */
	public function um_user_photos_extend_scan_files( $scan_files ) {
		$extension_files['um-user-photos'] = UM()->admin_settings()->scan_template_files( UM_USER_PHOTOS_PATH . '/templates/' );
		return array_merge( $scan_files, $extension_files );
	}

	/**
	 * Get template paths
	 *
	 * @param $located
	 * @param $file
	 *
	 * @return array
	 */
	public function um_user_photos_get_path_template( $located, $file ) {
		if ( file_exists( get_stylesheet_directory() . '/ultimate-member/um-user-photos/' . $file ) ) {
			$located = array(
				'theme' => get_stylesheet_directory() . '/ultimate-member/um-user-photos/' . $file,
				'core'  => UM_USER_PHOTOS_PATH . 'templates/' . $file,
			);
		}

		return $located;
	}
}
