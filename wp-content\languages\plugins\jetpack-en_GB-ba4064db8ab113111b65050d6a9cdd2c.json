{"translation-revision-date": "2025-02-20 19:47:04+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "Block <ExternalLink>brute force attacks</ExternalLink> and get immediate notifications if your site is down": ["Block <ExternalLink>brute force attacks</ExternalLink> and get immediate notifications if your site is down"], "Grow your traffic with automated social <ExternalLink>publishing and sharing</ExternalLink>": ["Grow your traffic with automated social <ExternalLink>publishing and sharing</ExternalLink>"], "Speed up your site and provide mobile-ready images with <ExternalLink>our CDN</ExternalLink>": ["Speed up your site and provide mobile-ready images with <ExternalLink>our CDN</ExternalLink>"], "Jetpack encountered an error and was unable to display the Dashboard. Please try refreshing the page.": ["Jetpack encountered an error and was unable to display the Dashboard. Please try refreshing the page."], "There seems to be a problem with your website.": ["There seems to be a problem with your website."], "There seems to be a problem with your connection to WordPress.com. If the problem persists, try reconnecting.": ["There seems to be a problem with your connection to WordPress.com. If the problem persists, try reconnecting."], "Are you sure you want to deactivate?": ["Are you sure you want to deactivate?"], "Jetpack has many powerful tools that can help you achieve your goals": ["Jetpack has many powerful tools that can help you achieve your goals"], "Jetpack is currently powering features on your site. Once you disconnect Jetpack, these features will no longer be available and your site may no longer function the same way.": ["Jetpack is currently powering features on your site. Once you disconnect Jetpack, these features will no longer be available and your site may no longer function the same way."], "Deactivate Jetpack": ["Deactivate Jetpack"]}}, "comment": {"reference": "_inc/build/plugins-page.js"}}