"use strict";var WPFormsBuilderPayments=window.WPFormsBuilderPayments||function(i,p){var l=p("#wpforms-panel-payments"),r={init:function(){p(r.ready)},ready:function(){r.defaultStates(),r.bindEvents()},defaultStates:function(){p(".wpforms-panel-content-section-payment-toggle input").each(r.toggleContent),p(".wpforms-panel-content-section-payment-plan-name input").each(r.checkPlanName)},bindEvents:function(){l.on("click",".wpforms-panel-content-section-payment-toggle input",r.toggleContent).on("click",".wpforms-panel-content-section-payment-plan-head-buttons-toggle",r.togglePlan).on("click",".wpforms-panel-content-section-payment-button-add-plan",r.addPlan).on("input",".wpforms-panel-content-section-payment-plan-name input",r.rename<PERSON>lan).on("focusout",".wpforms-panel-content-section-payment-plan-name input",r.checkPlanName).on("click",".wpforms-panel-content-section-payment-plan-head-buttons-delete",r.deletePlan).on("click",".wpforms-panel-content-section-payment-toggle-recurring input",r.addEmptyPlan).on("click",".wpforms-panel-content-section-payment-toggle-one-time input",function(){r.noteOneTimePaymentsDisabled(p(this))}),p(i).on("wpformsBeforeSave",r.showNoticesAfterFormSaved).on("wpformsRemoveConditionalLogicRules",function(n,e){r.disableOneTimePayments(e)})},toggleContent:function(){var n=p(this),e=n.closest(".wpforms-panel-content-section-payment"),t=e.find(".wpforms-panel-content-section-payment-toggled-body"),n=n.prop("checked")&&!p("#wpforms-panel-field-settings-disable_entries").prop("checked");t.toggle(n),e.toggleClass("wpforms-panel-content-section-payment-open",n)},addPlan:function(){if(!p(this).hasClass("education-modal")){const t=r.getProviderSection(p(this));p.confirm({title:!1,content:wpforms_builder.payment_plan_prompt+'<input autofocus="" type="text" id="wpforms-builder-payment-plan-name" placeholder="'+wpforms_builder.payment_plan_prompt_placeholder+'"><p class="error">'+wpforms_builder.payment_error_name+"</p>",backgroundDismiss:!1,closeIcon:!1,icon:"fa fa-info-circle",type:"blue",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action:function(){var n=this.$content.find("#wpforms-builder-payment-plan-name").val().trim(),e=this.$content.find(".error");if(!n)return e.show(),!1;r.createNewPlan(n,t)}},cancel:{text:wpforms_builder.cancel}}})}},createNewPlan:function(n,e){var t=e.find(".wpforms-panel-content-section-payment-recurring"),o=t.find(".wpforms-panel-content-section-payment-plan").last(),o=o.length?o.data("plan-id")+1:0,a=wp.template("wpforms-builder-payments-"+e.data("provider")+"-clone");a&&(t.append(a({index:o}).replaceAll("-dataindex-",`-${o}-`)),(t=(a=t.find(".wpforms-panel-content-section-payment-plan").last()).find(".wpforms-panel-content-section-payment-plan-name input")).val(n||r.getDefaultPlanName(o+1)),t.trigger("input"),p(i).trigger("wpformsFieldUpdate",wpf.getFields()),l.trigger("wpformsPaymentsPlanCreated",a,e.data("provider")),wpf.initTooltips())},addEmptyPlan:function(){var n=r.getProviderSection(p(this));p(this).prop("checked")&&!n.find(".wpforms-panel-content-section-payment-plan").length&&r.createNewPlan("",n)},togglePlan:function(){var n=p(this).closest(".wpforms-panel-content-section-payment-plan"),e=n.find(".wpforms-panel-content-section-payment-plan-body"),n=n.find(".wpforms-panel-content-section-payment-plan-head-buttons-toggle");n.toggleClass("fa-chevron-circle-up fa-chevron-circle-down"),e.toggle(n.hasClass("fa-chevron-circle-down"))},renamePlan:function(){var n=p(this),e=r.getProviderSection(n),t=n.closest(".wpforms-panel-content-section-payment-plan"),o=t.find(".wpforms-panel-content-section-payment-plan-head-title");n.val()?(o.html(n.val()),l.trigger("wpformsPaymentsPlanRenamed",n.val(),t,e.data("provider"))):o.html("")},checkPlanName(){var n=p(this),e=n.closest(".wpforms-panel-content-section-payment-plan"),t=e.find(".wpforms-panel-content-section-payment-plan-head-title");n.val()?t.html(n.val()):e.length?(e=r.getDefaultPlanName(e.data("plan-id")+1),t.html(e),n.val(e)):t.html("")},getDefaultPlanName:function(n){return wpforms_builder.payment_plan_placeholder.replace("{id}",n)},deletePlan:function(){var n=p(this),e=r.getProviderSection(n),t=n.closest(".wpforms-panel-content-section-payment-plan");"stripe"===e.data("provider")&&!wpforms_builder_stripe.is_pro||p.alert({title:wpforms_builder.heads_up,content:wpforms_builder.payment_plan_confirm,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action:function(){t.remove(),l.trigger("wpformsPaymentsPlanDeleted",t,e.data("provider")),e.find(".wpforms-panel-content-section-payment-plan").length||e.find(".wpforms-panel-content-section-payment-toggle-recurring input").trigger("click")}},cancel:{text:wpforms_builder.cancel}}})},disableOneTimePayments:function(n){n.prop("checked")||r.noteOneTimePaymentsDisabled(r.getProviderSection(n).find(".wpforms-panel-content-section-payment-toggle-one-time input"))},isAllowedOneTimePayments(n){var e;return!n.closest(".wpforms-panel-content-section-payment").find(".wpforms-panel-content-section-payment-toggle-recurring input").prop("checked")||(e=n.find(".wpforms-panel-content-section-payment-plan"),n=n.find(".wpforms-conditional-groups"),!!e.length&&e.length===n.length&&r.isRecurringConditionalsValid(e))},isRecurringConditionalsValid:function(n){var t=!1;return n.find(".wpforms-conditional-block").each(function(){var n=p(this);if(!n.find(".wpforms-conditionals-enable-toggle input").prop("checked"))return!(t=!0);n.find(".wpforms-conditional-row").each(function(){var n=p(this),e=n.find(".wpforms-conditional-value");if(!n.find(".wpforms-conditional-field").val()||!e.prop("disabled")&&!e.val())return!(t=!0)})}),!t},showNoticesAfterFormSaved:function(){var n=l.find(".wpforms-panel-content-section");n.length&&n.each(function(){r.noteOneTimePaymentsDisabled(p(this).find(".wpforms-panel-content-section-payment-toggle-one-time input"))})},noteOneTimePaymentsDisabled:function(n){var e=r.getProviderSection(n),t=e.find(".wpforms-panel-content-section-payment-one-time"),o=e.find(".wpforms-panel-content-section-payment-recurring");n.prop("checked")&&!r.isAllowedOneTimePayments(o)&&(t.hide(),n.prop("checked",!1),r.showPopupDisabledOneTimePayments(e.find(".wpforms-panel-content-section-title").text().trim()))},showPopupDisabledOneTimePayments:function(n){p.alert({title:wpforms_builder.heads_up,content:wpforms_builder.payment_one_time_payments_disabled.replaceAll("{provider}",n),icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})},getProviderSection:function(n){return n.closest(".wpforms-panel-content-section")}};return r}(document,(window,jQuery));WPFormsBuilderPayments.init();