<?php

declare (strict_types=1);
/*
 * This file is part of the Monolog package.
 *
 * (c) <PERSON><PERSON> <j.boggia<PERSON>@seld.be>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace WPMailSMTP\Vendor\Monolog\Handler\FingersCrossed;

use <PERSON>PMailSMTP\Vendor\Monolog\Logger;
use WPMailSMTP\Vendor\Psr\Log\LogLevel;
/**
 * Error level based activation strategy.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @phpstan-import-type Level from \Monolog\Logger
 * @phpstan-import-type LevelName from \Monolog\Logger
 */
class ErrorLevelActivationStrategy implements \WPMailSMTP\Vendor\Monolog\Handler\FingersCrossed\ActivationStrategyInterface
{
    /**
     * @var Level
     */
    private $actionLevel;
    /**
     * @param int|string $actionLevel Level or name or value
     *
     * @phpstan-param Level|LevelName|LogLevel::* $actionLevel
     */
    public function __construct($actionLevel)
    {
        $this->actionLevel = \WPMailSMTP\Vendor\Monolog\Logger::toMonologLevel($actionLevel);
    }
    public function isHandlerActivated(array $record) : bool
    {
        return $record['level'] >= $this->actionLevel;
    }
}
