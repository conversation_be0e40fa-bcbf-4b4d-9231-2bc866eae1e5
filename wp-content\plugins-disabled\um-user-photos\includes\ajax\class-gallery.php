<?php
namespace um_ext\um_user_photos\ajax;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Gallery
 *
 * @package um_ext\um_user_photos\ajax
 */
class Gallery {

	/**
	 * Gallery constructor.
	 */
	public function __construct() {
		// Get gallery
		add_action( 'wp_ajax_um_user_photos_get_gallery', array( $this, 'ajax_get_gallery' ) );
		add_action( 'wp_ajax_nopriv_um_user_photos_get_gallery', array( $this, 'ajax_get_gallery' ) );
	}

	/**
	 * Load view with ajax
	 */
	public function ajax_get_gallery() {
		if ( empty( $_POST['_wpnonce'] ) || ! wp_verify_nonce( $_POST['_wpnonce'], 'um_get_gallery' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		if ( empty( $_POST['user_id'] ) ) {
			wp_send_json_error( __( 'User ID is required', 'um-user-photos' ) );
		}

		if ( ! empty( $_POST['is_profile_tab'] ) ) {
			UM()->User_Photos()->common()->shortcodes()->is_profile = true;
		}

		$user_id = absint( $_POST['user_id'] );
		$content = UM()->User_Photos()->common()->shortcodes()->get_gallery_content( array( 'user_id' => $user_id ) );

		if ( ! empty( $_POST['is_profile_tab'] ) ) {
			UM()->User_Photos()->common()->shortcodes()->is_profile = false;
		}

		wp_send_json_success( $content );
	}
}
