<?php
namespace um_ext\um_user_photos\common;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Media
 *
 * @package um_ext\um_user_photos\common
 */
class Media {

	/**
	 * Media constructor.
	 */
	public function __construct() {
		add_action( 'after_setup_theme', array( $this, 'add_image_sizes' ) );
		add_filter( 'ajax_query_attachments_args', array( $this, 'hide_gallery_media' ) );
	}

	/**
	 * Create custom Image sizes for Gallery
	 */
	public function add_image_sizes() {
		// FYI: WordPress has a hook 'big_image_size_threshold' that prevents resize for images with width or height higher than predefined value. Default is 2560px.
		$cover_width  = 350;
		$cover_height = 250;
		$cover_size   = UM()->options()->get( 'um_user_photos_cover_size' );
		if ( $cover_size && '' !== trim( $cover_size ) ) {
			$cover_size = strtolower( $cover_size );
			$size       = explode( 'x', $cover_size );
			if ( is_array( $size ) && 2 === count( $size ) ) {
				$cover_width  = absint( $size[0] );
				$cover_height = absint( $size[1] );
			}
		}
		add_image_size( 'album_cover', $cover_width, $cover_height, true );

		$photo_width  = 250;
		$photo_height = 250;
		$photo_size   = UM()->options()->get( 'um_user_photos_image_size' );
		if ( $photo_size && '' !== trim( $photo_size ) ) {
			$photo_size = strtolower( $photo_size );
			$size       = explode( 'x', $photo_size );
			if ( is_array( $size ) && 2 === count( $size ) ) {
				$photo_width  = absint( $size[0] );
				$photo_height = absint( $size[1] );
			}
		}

		add_image_size( 'gallery_image', $photo_width, $photo_height, true );
	}

	/**
	 * Additional meta query for photos when Media Library modal is open.
	 * WordPress native AJAX request for getting attachments.
	 *
	 * @param array $args
	 *
	 * @return array
	 */
	public function hide_gallery_media( $args ) {
		// Bail if this is not the admin area.
		if ( ! is_admin() ) {
			return $args;
		}

		$args['meta_query'] = array(
			array(
				'key'     => '_part_of_gallery',
				'compare' => 'NOT EXISTS',
			),
		);

		return $args;
	}
}
