function get_caption_text(t,e){return t=parseInt(t.replace("user_photo-","")),jQuery("body").find(".imgs-grid-modal").find(".modal-caption").html('<div style="text-align:center;padding-top:50px;"><div class="um-user-photos-ajax-loading"></div></div>'),wp.ajax.send("um_user_photos_get_comment_section",{data:{_wpnonce:e,image_id:t},success:function(t){jQuery("body").find(".imgs-grid-modal").find(".modal-caption").html(t)},error:function(t){return console.log(t),t}}),""}function open_modal(t){var e=get_caption_text(t.id,t.nonce),o="",i=(1!==parseInt(user_photos_settings.disabled_comments)&&1!==parseInt(user_photos_settings.disabled_comments)||(o='style="width:100%;"'),jQuery("body").find(".imgs-grid-modal").first());i.length?(i.find(".modal-inner").find(".modal-image").html('<div class="um-user-photos-ajax-loading"></div>'),i.find(".modal-caption").html('<div style="text-align:center;padding-top:50px;"><div class="um-user-photos-ajax-loading"></div></div>'),i.find(".modal-inner").find(".modal-image").html('<img src="'+t.src+'" alt="" title="'+t.title+'" id="'+t.id+'">'),e&&i.find(".modal-caption").html(e)):(i="",i=(i=(i+='<div class="imgs-grid-modal" style="opacity: 1;">')+'<div class="modal-close um-user-photos-modal-close">&times;</div><div class="modal-inner" '+o+">")+'<div class="modal-image"><img src="'+t.src+'" alt="" title="'+t.title+'" id="'+t.id+'"><div class="modal-loader" style="display: none;">loading...</div></div>',(void 0===t.count||1<parseInt(t.count))&&(i+='<div class="modal-control left"><div class="arrow left um-photo-modal-prev"></div></div><div class="modal-control right"><div class="arrow right um-photo-modal-next"></div></div>'),i+="</div>",1===parseInt(user_photos_settings.disabled_comments)&&1===parseInt(user_photos_settings.disabled_comments)||(i+='<div class="modal-caption"><div style="text-align:center;padding-top:50px;"><div class="um-user-photos-ajax-loading"></div></div></div>'),i+="</div>",jQuery("body").append(i)),jQuery("body").addClass("um-user-photos-modal-open")}!function(l){function i(t){this.opts=t||{},this.$window=l(window),this.$element=this.opts.element,this.$gridItems=[],this.modal=null,this.imageLoadCount=0;t=this.opts.cells;this.opts.cells=t<1?1:6<t?6:t,this.onWindowResize=this.onWindowResize.bind(this),this.onImageClick=this.onImageClick.bind(this),this.init()}function e(t){this.opts=t||{},this.imageIndex=null,this.$document=l(document),this.$modal=null,this.$indicator=null,this.close=this.close.bind(this),this.prev=this.prev.bind(this),this.next=this.next.bind(this),this.onIndicatorClick=this.onIndicatorClick.bind(this),this.onImageLoaded=this.onImageLoaded.bind(this),this.onKeyUp=this.onKeyUp.bind(this),this.$document.on("keyup",this.onKeyUp)}l.fn.imagesGrid=function(e){var o=arguments;return this.each(function(){var t;if(l.isPlainObject(e))this._imgGrid instanceof i&&(this._imgGrid.destroy(),delete this._imgGrid),(t=l.extend({},l.fn.imagesGrid.defaults,e)).element=l(this),this._imgGrid=new i(t);else if("string"==typeof e&&this._imgGrid instanceof i)switch(e){case"modal.open":this._imgGrid.modal.open(o[1]);break;case"modal.close":this._imgGrid.modal.close(),jQuery("body").trigger("um_user_photos_modal_close"),jQuery("body").removeClass("um-user-photos-modal-open");break;case"destroy":this._imgGrid.destroy(),delete this._imgGrid,jQuery("body").removeClass("um-user-photos-modal-open")}})},l.fn.imagesGrid.defaults={images:[],cells:5,align:!1,nextOnClick:!0,showViewAll:"more",viewAllStartIndex:"auto",loading:"loading...",getViewAllText:function(t){return"View all "+t+" images"},onGridRendered:l.noop,onGridItemRendered:l.noop,onGridLoaded:l.noop,onGridImageLoaded:l.noop,onModalOpen:l.noop,onModalClose:l.noop,onModalImageClick:l.noop,onModalImageUpdate:l.noop},i.prototype.init=function(){this.setGridClass(),this.renderGridItems(),this.createModal(),this.$window.on("resize",this.onWindowResize)},i.prototype.createModal=function(){var t=this.opts;this.modal=new e({loading:t.loading,images:t.images,nextOnClick:t.nextOnClick,onModalOpen:t.onModalOpen,onModalClose:t.onModalClose,onModalImageClick:t.onModalImageClick,onModalImageUpdate:t.onModalImageUpdate})},i.prototype.setGridClass=function(){var t=this.opts,e=t.images.length,e=e<t.cells?e:t.cells;this.$element.addClass("imgs-grid imgs-grid-"+e)},i.prototype.renderGridItems=function(){var t=this.opts,e=t.images,o=e.length;if(e){this.$element.empty(),this.$gridItems=[];for(var i=0;i<o&&i!==t.cells;++i)this.renderGridItem(e[i],i);("always"===t.showViewAll||"more"===t.showViewAll&&o>t.cells)&&this.renderViewAll(),t.onGridRendered(this.$element)}},i.prototype.renderGridItem=function(e,t){var o=e,i="",s="",a="",n="",d=this.opts,r=this,t=(l.isPlainObject(e)&&(o=e.thumbnail||e.src,i=e.alt||"",s=e.title||"",a=e.id||"",n=e.author||""),l("<div>",{class:"imgs-grid-image",click:this.onImageClick,data:{index:t}}));t.append(l("<div>",{class:"image-wrap"}).append(l("<img>",{src:o,alt:i,title:s,author:n,id:parseInt(a.replace("user_photo-","")),on:{load:function(t){r.onImageLoaded(t,l(this),e)}}}))),this.$gridItems.push(t),this.$element.append(t),d.onGridItemRendered(t,e)},i.prototype.renderViewAll=function(){var t=this.opts;this.$element.find(".imgs-grid-image:last .image-wrap").append(l("<div>",{class:"view-all"}).append(l("<span>",{class:"view-all-cover"}),l("<span>",{class:"view-all-text",text:t.getViewAllText(t.images.length)})))},i.prototype.onWindowResize=function(t){this.opts.align&&this.align()},i.prototype.onImageClick=function(t){var e=this.opts,t=l(t.currentTarget),e=0<t.find(".view-all").length&&"number"==typeof e.viewAllStartIndex?e.viewAllStartIndex:t.data("index");this.modal.open(e),jQuery("body").trigger("um_user_photos_modal_open"),jQuery("body").addClass("um-user-photos-modal-open")},i.prototype.onImageLoaded=function(t,e,o){var i=this.opts;++this.imageLoadCount,i.onGridImageLoaded(t,e,o),this.imageLoadCount===this.$gridItems.length&&(this.imageLoadCount=0,this.onAllImagesLoaded())},i.prototype.onAllImagesLoaded=function(){var t=this.opts;t.align&&this.align(),t.onGridLoaded(this.$element)},i.prototype.align=function(){switch(this.$gridItems.length){case 2:case 3:this.alignItems(this.$gridItems);break;case 4:this.alignItems(this.$gridItems.slice(0,2)),this.alignItems(this.$gridItems.slice(2));break;case 5:case 6:this.alignItems(this.$gridItems.slice(0,3)),this.alignItems(this.$gridItems.slice(3))}},i.prototype.alignItems=function(t){var e=t.map(function(t){return t.find("img").height()}),i=Math.min.apply(null,e);l(t).each(function(){var t=l(this),e=t.find(".image-wrap"),t=t.find("img"),o=t.height();e.height(i),i<o&&(e=Math.floor((o-i)/2),t.css({top:-e}))})},i.prototype.destroy=function(){this.$window.off("resize",this.onWindowResize),this.$element.empty().removeClass("imgs-grid imgs-grid-"+this.$gridItems.length),this.modal.destroy(),jQuery("body").removeClass("um-user-photos-modal-open")},e.prototype.open=function(t){this.isOpened()||(this.imageIndex=parseInt(t)||0,this.render())},e.prototype.close=function(t){var e;this.$modal&&(e=this.opts,this.$modal.animate({opacity:0},{duration:100,complete:function(){this.$modal.remove(),this.$modal=null,this.$indicator=null,this.imageIndex=null,e.onModalClose()}.bind(this)}),jQuery("body").removeClass("um-user-photos-modal-open"))},e.prototype.isOpened=function(){return this.$modal&&this.$modal.is(":visible")},e.prototype.render=function(){var t=this.opts;this.renderModal(),this.renderCloseButton(),this.renderInnerContainer(),this.renderCaption(),this.$modal.animate({opacity:1},{duration:100,complete:function(){t.onModalOpen(this.$modal,t.images[this.imageIndex])}.bind(this)})},e.prototype.renderModal=function(){this.$modal=l("<div>",{class:"imgs-grid-modal"}).appendTo("body")},e.prototype.renderCloseButton=function(){this.$modal.append(l("<div>",{class:"modal-close",html:"&times;",click:this.close}))},e.prototype.renderInnerContainer=function(){var t="",e=(1==user_photos_settings.disabled_comments&&(t="width:100%;"),this.opts),o=this.getImage(this.imageIndex);this.$modal.append(l("<div>",{class:"modal-close",html:"&times;",click:this.close})),this.$modal.append(l("<div>",{class:"modal-inner"}).attr("style",t).append(l("<div>",{class:"modal-image"}).append(l("<img>",{src:o.src,alt:o.alt,title:o.title,id:o.id,on:{load:this.onImageLoaded,click:function(t){this.onImageClick(t,l(this),o)}.bind(this)}}),l("<div>",{class:"modal-loader",html:e.loading})),l("<div>",{class:"modal-control left",click:this.prev}).append(l("<div>",{class:"arrow left"})),l("<div>",{class:"modal-control right",click:this.next}).append(l("<div>",{class:"arrow right"})))),e.images.length<=1&&this.$modal.find(".modal-control").hide()},e.prototype.renderIndicatorContainer=function(){var t=this.opts.images.length;if(1!=t){this.$indicator=l("<div>",{class:"modal-indicator"});for(var e=l("<ul>"),o=0;o<t;++o)e.append(l("<li>",{class:this.imageIndex==o?"selected":"",click:this.onIndicatorClick,data:{index:o}}));this.$indicator.append(e),this.$modal.append(this.$indicator)}},e.prototype.prev=function(){var t=this.opts.images.length;0<this.imageIndex?--this.imageIndex:this.imageIndex=t-1,this.updateImage()},e.prototype.next=function(){var t=this.opts.images.length;this.imageIndex<t-1?++this.imageIndex:this.imageIndex=0,this.updateImage()},e.prototype.updateImage=function(){var t,e=this.opts,o=this.getImage(this.imageIndex),i=this.$modal.find(".modal-image img");i.attr({src:o.src,alt:o.alt,title:o.title,author:o.author,id:o.id}),this.$modal.find(".modal-caption").text(this.getImageCaption(o.id)),this.$indicator&&((t=this.$indicator.find("ul")).children().removeClass("selected"),t.children().eq(this.imageIndex).addClass("selected")),this.showLoader(),e.onModalImageUpdate(i,o)},e.prototype.renderCaption=function(){var t;1==user_photos_settings.disabled_comments&&"1"==user_photos_settings.disabled_comments||(t=this.getImage(this.imageIndex),t=this.getImageCaption(t.id),this.$caption=l("<div>",{class:"modal-caption",style:"background:#fff;",html:'<div style="text-align:center;padding-top:50px;"><div class="um-user-photos-ajax-loading"></div></div>'}).appendTo(this.$modal),t&&l("body").find(".imgs-grid-modal").find("modal-caption").html(t))},e.prototype.onImageClick=function(t,e,o){var i=this.opts;i.nextOnClick&&this.next(),i.onModalImageClick(t,e,o)},e.prototype.onImageLoaded=function(){this.hideLoader()},e.prototype.onIndicatorClick=function(t){t=l(t.target).data("index");this.imageIndex=t,this.updateImage()},e.prototype.onKeyUp=function(t){if(this.$modal)switch(t.keyCode){case 27:this.close();break;case 37:this.prev();break;case 39:this.next()}},e.prototype.getImage=function(t){t=this.opts.images[t];return l.isPlainObject(t)?t:{src:t,alt:"",title:""}},e.prototype.getImageCaption=function(t){jQuery("body").find(".imgs-grid-modal").find(".modal-caption").html('<div style="text-align:center;padding-top:50px;"><div class="um-user-photos-ajax-loading"></div></div>');t=get_caption_text(t);if(t)return t},e.prototype.showLoader=function(){this.$modal&&(this.$modal.find(".modal-image img").hide(),this.$modal.find(".modal-loader").show())},e.prototype.hideLoader=function(){this.$modal&&(this.$modal.find(".modal-image img").show(),this.$modal.find(".modal-loader").hide())},e.prototype.destroy=function(){this.$document.off("keyup",this.onKeyUp),this.close()}}(jQuery),jQuery(document).on("activity_loaded",function(){var t=jQuery("body").find(".um-profile-body");t.length&&(t=t.find(".um_user_photos_activity_view")).length&&jQuery.each(t,function(t,e){var e=jQuery(e),o=(e.attr("data-images"),e.find("img")),i=[],o=(jQuery.each(o,function(t,e){i.push({src:e.src,title:e.title,alt:e.alt,author:e.author,id:e.id})}),"#"+e.attr("id"));jQuery(o).imagesGrid({images:i,align:!0,getViewAllText:function(t){return"+ "+(t-5)+" more"}})})}),jQuery(function(s){var t=s("body").find(".um_user_photos_activity_view");t.length&&s.each(t,function(t,e){var e=s(e),o=(e.attr("data-images"),e.find("img")),i=[],o=(s.each(o,function(t,e){i.push({src:e.src,title:e.title,alt:e.alt,author:e.author,id:e.id})}),"#"+e.attr("id"));s(o).imagesGrid({images:i,align:!0,getViewAllText:function(t){return"+ "+(t-5)+" more"}})})}),jQuery(window).on("popstate",function(t){var e=new URLSearchParams(window.location.search);e.has("album_id")&&(!e.has("photo_id")&&0<jQuery(".imgs-grid-modal").length?jQuery(".um-user-photos-modal-close").trigger("click"):e.has("photo_id")&&0===jQuery(".imgs-grid-modal").length&&jQuery(".um-user-photos-album-block[data-id="+e.get("photo_id")+"]").trigger("click"))}),jQuery(window).on("load",function(){var a,t,e,o,i,s=new URLSearchParams(window.location.search),n=s.get("photo_id");s.has("album_id")&&s.has("photo_id")?(0===jQuery("a.um-user-photos-image[data-id="+n+"]").length&&(o=s.get("album_id"),jQuery(".um-user-photos-album-block[data-id="+o+"]").trigger("click")),a=setInterval(function(){var t,e,o,i,s;!1===window.album_load&&0<jQuery("a.um-user-photos-image[data-id="+n+"]").length&&(t=jQuery(".um-user-photos-albums-counter").attr("data-count"),e=(s=jQuery("a.um-user-photos-image[data-id="+n+"]")).attr("href"),o=s.attr("alt"),i=s.attr("title"),s=s.attr("data-wpnonce"),open_modal({id:n,src:e,alt:o,title:i,count:t,nonce:s}),clearInterval(a))},500)):!s.has("album_id")&&s.has("photo_id")&&!1===window.album_load&&0<jQuery("a.um-user-photos-image[data-id="+n+"]").length&&(s=(o=jQuery("a.um-user-photos-image[data-id="+n+"]")).attr("href"),t=o.attr("alt"),e=o.attr("title"),o=o.attr("data-wpnonce"),i=jQuery(".um-user-photos-albums").attr("data-count"),open_modal({id:n,src:s,alt:t,title:e,count:i,nonce:o}))}),jQuery(function(a){a(document).on("click",'[data-umaction="open_modal"]',function(t){t.preventDefault();var t=a(this),e=t.attr("data-id"),o=t.attr("href"),i=t.attr("alt"),s=t.attr("title"),t=t.attr("data-wpnonce"),o=(open_modal({id:e,src:o,alt:i,title:s,count:jQuery(".um-user-photos-albums-counter").attr("data-count"),nonce:t}),new URL(window.location.href));o.searchParams.set("photo_id",e),history.pushState({photoId:e},"",o)}),a(document).on("click",".um-user-photos-modal-close",function(t){t.preventDefault();var e=a(this).parents(".imgs-grid-modal"),t=(e.animate({opacity:0},{duration:100,complete:function(){e.remove(),a("body").removeClass("um-user-photos-modal-open")}}),new URL(window.location.href));t.searchParams.delete("photo_id"),history.pushState(null,"",t)}),a(document).on("click",".um-photo-modal-next",function(t){t.preventDefault(),a("body").find(".imgs-grid-modal").find(".modal-caption").html('<div style="text-align:center;padding-top:50px;"><div class="um-user-photos-ajax-loading"></div></div>');var t=null,e=a(this).parents(".imgs-grid-modal").find(".modal-image").find("img").attr("id"),e=a("body").find('a[data-id="'+e+'"][data-umaction="open_modal"]').first().parents(".um-user-photos-image").parent(".um-user-photos-image-block").nextAll(".um-user-photos-image-block").filter(":first"),e=(open_modal({id:(t=(e=e.length?e:a("body").find(".um-user-photos-image-block").first()).find('a[data-umaction="open_modal"]')).attr("data-id"),src:t.attr("href"),alt:"",title:"",nonce:t.attr("data-wpnonce")}),t.data("id")),t=new URL(window.location.href);t.searchParams.set("photo_id",e),history.pushState({photoId:e},"",t)}),a(document).on("click",".um-photo-modal-prev",function(t){t.preventDefault(),a("body").find(".imgs-grid-modal").find(".modal-caption").html('<div style="text-align:center;padding-top:50px;"><div class="um-user-photos-ajax-loading"></div></div>');var t=null,e=a(this).parents(".imgs-grid-modal"),e=(e.find(".modal-caption").html('<div style="text-align:center;padding-top:50px;"><div class="um-user-photos-ajax-loading"></div></div>'),e.find(".modal-image").find("img").attr("id")),e=a("body").find('a[data-id="'+e+'"][data-umaction="open_modal"]').first().parent(".um-user-photos-image").parent(".um-user-photos-image-block").prevAll(".um-user-photos-image-block").filter(":first"),e=(open_modal({id:(t=(e=e.length?e:a("body").find(".um-user-photos-image-block").last()).find('a[data-umaction="open_modal"]')).attr("data-id"),src:t.attr("href"),alt:"",title:"",nonce:t.attr("data-wpnonce")}),t.data("id")),t=new URL(window.location.href);t.searchParams.set("photo_id",e),history.pushState({photoId:e},"",t)})}),jQuery(document).ready(function(){jQuery(document.body).on("click",".um-user-photos-like:not(.active) a",function(t){t.preventDefault();t=jQuery(this).parents(".um-user-photos-widget").attr("id").replace("postid-","");jQuery(this).find("i").addClass("um-effect-pop"),jQuery(this).parent().addClass("active"),jQuery(this).find("span").html(jQuery(this).parent().data("unlike_text")),jQuery(this).find("i").addClass("um-active-color");let e=jQuery(this).parents(".um-user-photos-widget").find(".um-user-photos-post-likes"),o=parseInt(e.html());var i=jQuery(this).data("likenonce");wp.ajax.send("um_user_photos_like_photo",{data:{postid:t,_wpnonce:i},success:function(t){e.html(t),0===o&&0!==parseInt(t)&&e.closest("a").addClass("um-user-photos-show-likes")},error:function(t){console.log(t)}})}),jQuery(document.body).on("click",".um-user-photos-like.active a",function(t){t.preventDefault();t=jQuery(this).parents(".um-user-photos-widget").attr("id").replace("postid-","");jQuery(this).find("i").removeClass("um-effect-pop"),jQuery(this).parent().removeClass("active"),jQuery(this).find("span").html(jQuery(this).parent().data("like_text")),jQuery(this).find("i").removeClass("um-active-color");let e=jQuery(this).parents(".um-user-photos-widget").find(".um-user-photos-post-likes");var o=jQuery(this).data("unlikenonce");wp.ajax.send("um_user_photos_unlike_photo",{data:{postid:t,_wpnonce:o},success:function(t){0===t&&e.closest("a").removeClass("um-user-photos-show-likes"),e.html(t)},error:function(t){console.log(t)}})}),jQuery(document.body).on("click",".um-user-photos-comment a",function(){jQuery(this).parents(".um-user-photos-widget").hasClass("unready")||jQuery(this).parents(".um-user-photos-widget").find(".um-user-photos-comments .um-user-photos-comment-box textarea").trigger("focus")}),jQuery(document.body).on("keypress",".um-user-photos-comment-textarea",function(t){0<jQuery(this).val().trim().length?jQuery(this).parents(".um-user-photos-commentl").find(".um-user-photos-comment-post").removeClass("um-disabled"):jQuery(this).parents(".um-user-photos-commentl").find(".um-user-photos-comment-post").addClass("um-disabled")}),jQuery(document.body).on("click",".um-user-photos-comment-post",function(t){t.preventDefault();var e=jQuery(this),o=e.parents(".um-user-photos-commentl").find(".um-user-photos-comment-textarea"),t=o.val(),i=o.parents(".um-user-photos-widget").attr("id").replace("postid-",""),s=o.parents(".um-user-photos-widget").find(".um-user-photos-comments-loop"),a=jQuery(this).parents(".um-user-photos-widget").find(".um-user-photos-post-comments"),n=jQuery(this).attr("data-wpnonce");wp.ajax.send("um_user_photos_post_comment",{data:{image_id:i,comment:t,_wpnonce:n},success:function(t){s.prepend(t.content),a.html(t.count),o.val(""),e.addClass("um-disabled")},error:function(t){return console.log(t),t}})}),jQuery(document.body).on("click",".um-user-photos-editc a",function(t){t.preventDefault(),jQuery(this).parents(".um-user-photos-comment-meta").find(".um-user-photos-editc-d").toggle()}),jQuery(document.body).on("click",".um-user-photos-comment-like",function(t){t.preventDefault();var e,o=jQuery(this),t=o.attr("data-id"),i=o.attr("data-unlike_text"),s=o.parents(".um-user-photos-comment-meta").find(".um-user-photos-ajaxdata-commentlikes"),a=(parseInt(s.html()),"um_user_photos_like_comment");o.hasClass("active")?(e=jQuery(this).attr("data-unlikenonce"),i=o.attr("data-like_text"),a="um_user_photos_unlike_comment",o.removeClass("active")):(e=jQuery(this).attr("data-likenonce"),o.addClass("active")),wp.ajax.send(a,{data:{commentid:t,_wpnonce:e},success:function(t){o.text(i),s.text(t.count)},error:function(t){console.log(t)}})}),jQuery(document.body).on("click",".um-user-photos-show-likes",function(t){t.preventDefault();var t=jQuery(this),e=jQuery("body").find('[data-scope="um-user-photos-modal"]'),o=jQuery(this).attr("data-wpnonce"),i=(e.length||(jQuery("body").append('<div class="um-user-photos-modal" data-scope="um-user-photos-modal"><div class="um-user-photos-modal-body"><div class="um-user-photos-modal-head"><div class="um-user-photos-modal-title"></div></div><div class="um-user-photos-modal-content"></div></div></div>'),e=jQuery("body").find('[data-scope="um-user-photos-modal"]')),t.attr("data-modal_title")),s=e.find(".um-user-photos-modal-content");s.html('<div class="text-center"><div class="um-user-photos-ajax-loading"></div></div>'),e.show(),t.attr("data-template");wp.ajax.send("get_um_user_photo_likes",{data:{image_id:t.attr("data-id"),_wpnonce:o},success:function(t){s.html(t.content),e.find(".um-user-photos-modal-title").text(i)},error:function(t){console.log(t)}})}),jQuery(document.body).on("click",".um-user-photos-comment-likes a",function(t){t.preventDefault();var t=jQuery(this),e=jQuery("body").find('[data-scope="um-user-photos-modal"]'),o=jQuery(this).attr("data-wpnonce"),i=(e.length||(jQuery("body").append('<div class="um-user-photos-modal" data-scope="um-user-photos-modal"><div class="um-user-photos-modal-body"><div class="um-user-photos-modal-head"><div class="um-user-photos-modal-title"></div></div><div class="um-user-photos-modal-content"></div></div></div>'),e=jQuery("body").find('[data-scope="um-user-photos-modal"]')),t.attr("data-modal_title")),s=e.find(".um-user-photos-modal-content");s.html('<div class="text-center"><div class="um-user-photos-ajax-loading"></div></div>'),e.show(),t.attr("data-template");wp.ajax.send("get_um_user_photos_comment_likes",{data:{comment_id:t.attr("data-id"),_wpnonce:o},success:function(t){s.html(t.content),e.find(".um-user-photos-modal-title").text(i)},error:function(t){console.log(t)}})}),jQuery(document.body).on("click",".um-user-photos-editc-d .edit",function(t){t.preventDefault();var t=jQuery(this),e=jQuery("body").find('[data-scope="um-user-photos-modal"]'),o=jQuery(this).attr("data-wpnonce"),i=(e.length||(jQuery("body").append('<div class="um-user-photos-modal" data-scope="um-user-photos-modal"><div class="um-user-photos-modal-body"><div class="um-user-photos-modal-head"><div class="um-user-photos-modal-title"></div></div><div class="um-user-photos-modal-content"></div></div></div>'),e=jQuery("body").find('[data-scope="um-user-photos-modal"]')),t.attr("data-modal_title")),s=e.find(".um-user-photos-modal-content");s.html('<div class="text-center"><div class="um-user-photos-ajax-loading"></div></div>'),e.show(),t.attr("data-template");wp.ajax.send("get_um_user_photos_comment_edit",{data:{comment_id:t.attr("data-commentid"),_wpnonce:o},success:function(t){s.html(t.content),e.find(".um-user-photos-modal-title").text(i)},error:function(t){console.log(t)}})}),jQuery(document.body).on("click",".um-user-photos-editc-d .delete",function(t){t.preventDefault();var t=jQuery(this),e=jQuery("body").find('[data-scope="um-user-photos-modal"]'),o=jQuery(this).attr("data-wpnonce"),i=(e.length||(jQuery("body").append('<div class="um-user-photos-modal" data-scope="um-user-photos-modal"><div class="um-user-photos-modal-body"><div class="um-user-photos-modal-head"><div class="um-user-photos-modal-title"></div></div><div class="um-user-photos-modal-content"></div></div></div>'),e=jQuery("body").find('[data-scope="um-user-photos-modal"]')),t.attr("data-modal_title")),s=e.find(".um-user-photos-modal-content");s.html('<div class="text-center"><div class="um-user-photos-ajax-loading"></div></div>'),e.show(),t.attr("data-template");wp.ajax.send("get_um_user_photos_comment_delete",{data:{comment_id:t.attr("data-commentid"),msg:t.attr("data-msg"),_wpnonce:o},success:function(t){s.html(t.content),e.find(".um-user-photos-modal-title").text(i)},error:function(t){console.log(t)}})}),jQuery(document.body).on("click","#delete-um-user-photos-comment",function(t){t.preventDefault();var e,o,i=jQuery(this),t=jQuery(this).attr("data-wpnonce");i.hasClass("busy")||(i.addClass("busy"),e=jQuery(".um-user-photos-post-comments"),o=i.text(),i.html('<i class="um-user-photos-ajax-loading"></i>'),wp.ajax.send("um_user_photos_comment_delete",{data:{comment_id:i.attr("data-id"),_wpnonce:t},success:function(t){i.html(o),i.removeClass("busy"),jQuery("body").find('.um-user-photos-commentwrap[data-comment_id="'+i.attr("data-id")+'"]').remove(),i.parents(".um-user-photos-modal").hide(),e.html(t.count)},error:function(t){i.html(o),i.removeClass("busy"),console.log(t)}}))}),jQuery(document.body).on("click","#um-user-photos-comment-update-btn",function(t){t.preventDefault();var e,o,i=jQuery(this),t=jQuery(this).attr("data-wpnonce");i.hasClass("busy")||(i.addClass("busy"),e=i.text(),i.html('<i class="um-user-photos-ajax-loading"></i>'),o=i.parents("form"),wp.ajax.send("um_user_photos_comment_update",{data:{comment_id:i.attr("data-commentid"),comment_content:i.parents("form").find('[name="comment_text"]').val(),_wpnonce:t},success:function(t){i.html(e),i.removeClass("busy"),o.find(".um-galley-form-response").html(t.message),jQuery("body").find(".modal-caption").find('[data-comment_id="'+t.comment_id+'"]').find(".um-user-photos-comment-text").html(t.comment),setTimeout(function(){i.parents(".um-user-photos-modal").hide()},2e3)},error:function(t){i.html(e),i.removeClass("busy"),console.log(t)}}))}),jQuery(document.body).on("click",".um-user-photos-metadata-date",function(t){t.preventDefault();var t=window.location.href,e=document.createElement("input");e.value=t,document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e)})});