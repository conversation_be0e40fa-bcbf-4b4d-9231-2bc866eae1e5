"use strict";var WPMailSMTP=window.WPMailSMTP||{};WPMailSMTP.Admin=WPMailSMTP.Admin||{},WPMailSMTP.Admin.About=WPMailSMTP.Admin.About||function(a,t,d){var i={init:function(){d(i.ready)},ready:function(){i.pageHolder=d(".wp-mail-smtp-page-about"),i.bindActions(),d(".wp-mail-smtp-page").trigger("WPMailSMTP.Admin.About.ready")},bindActions:function(){jQuery(".wp-mail-smtp-admin-about-plugins .plugin-item .details").matchHeight(),d(a).on("click",".wp-mail-smtp-admin-about-plugins .plugin-item .action-button .button",function(a){a.preventDefault();var i=d(this);if(i.hasClass("disabled")||i.hasClass("loading"))return!1;var s,n,l,e,o,u=i.closest(".plugin-item"),a=i.attr("data-plugin");if(i.addClass("loading disabled"),i.text(wp_mail_smtp_about.plugin_processing),i.hasClass("status-inactive"))s="about_plugin_activate",n="status-active button button-secondary disabled",l=wp_mail_smtp_about.plugin_active,e=wp_mail_smtp_about.plugin_activated;else{if(!i.hasClass("status-download"))return i.hasClass("status-open")?(t.open(i.attr("href"),"_blank").focus(),i.removeClass("loading disabled"),void i.text(wp_mail_smtp_about.plugin_visit)):void 0;s="about_plugin_install",n="status-active button disabled",l=wp_mail_smtp_about.plugin_active,e=wp_mail_smtp_about.plugin_activated}a={action:"wp_mail_smtp_ajax",task:s,nonce:wp_mail_smtp_about.nonce,plugin:a};d.post(wp_mail_smtp_about.ajax_url,a,function(a){var t;a.success?(t=!0,"about_plugin_install"===s?(i.attr("data-plugin",a.data.basename),o=a.data.msg,a.data.is_activated||(n="button",l=wp_mail_smtp_about.plugin_inactive,e=wp_mail_smtp_about.plugin_activate)):o=a.data,u.find(".actions").append('<div class="msg success">'+o+"</div>"),u.find("span.status-label").removeClass("status-active status-inactive status-download").addClass(n).removeClass("button button-primary button-secondary disabled").text(l),i.removeClass("status-active status-inactive status-download").removeClass("button button-primary button-secondary disabled").addClass(n).html(e)):(t=!1,a.hasOwnProperty("data")&&a.data.hasOwnProperty(0)&&a.data[0].hasOwnProperty("code")?u.find(".actions").append('<div class="msg error">'+wp_mail_smtp_about.plugin_install_error+"</div>"):u.find(".actions").append('<div class="msg error">'+a.data+"</div>"),i.html(wp_mail_smtp_about.plugin_download_btn)),t||i.removeClass("disabled"),i.removeClass("loading"),setTimeout(function(){d(".plugin-item .msg").remove()},3e3)}).fail(function(a){console.log(a.responseText)})})}};return i}(document,window,jQuery),WPMailSMTP.Admin.About.init();