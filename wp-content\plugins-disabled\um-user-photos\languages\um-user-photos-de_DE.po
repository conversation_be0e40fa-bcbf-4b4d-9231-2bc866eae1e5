msgid ""
msgstr ""
"Project-Id-Version: Ultimate Member - User Photos\n"
"POT-Creation-Date: 2019-11-10 23:34+0200\n"
"PO-Revision-Date: 2019-12-07 13:10+0100\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: de_DE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.2.4\n"
"X-Poedit-Basepath: ..\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: um-user-photos.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: includes/admin/class-admin.php:39
msgid "User Photos License Key"
msgstr "Lizenzschlüssel für Benutzerfotos"

#: includes/admin/class-admin.php:47 includes/admin/class-admin.php:115
#: includes/core/class-user-photos-common.php:34
#: includes/core/class-user-photos-common.php:35
#: includes/core/class-user-photos-common.php:61
msgid "User Photos"
msgstr "Benutzerfotos"

#: includes/admin/class-admin.php:54 includes/admin/class-admin.php:68
msgid "No. of columns"
msgstr "Anzahl der Spalten"

#: includes/admin/class-admin.php:55 includes/admin/class-admin.php:69
msgid "2 columns"
msgstr "2 Spalten"

#: includes/admin/class-admin.php:56 includes/admin/class-admin.php:70
msgid "3 columns"
msgstr "3 Spalten"

#: includes/admin/class-admin.php:57 includes/admin/class-admin.php:71
msgid "4 columns"
msgstr "4 Spalten"

#: includes/admin/class-admin.php:58 includes/admin/class-admin.php:72
msgid "5 columns"
msgstr "5 Spalten"

#: includes/admin/class-admin.php:59 includes/admin/class-admin.php:73
msgid "6 columns"
msgstr "6 Spalten"

#: includes/admin/class-admin.php:61
msgid "Album columns"
msgstr "Albumspalten"

#: includes/admin/class-admin.php:75
msgid "Photo columns"
msgstr "Fotospalten"

#: includes/admin/class-admin.php:81
msgid "Default : 350 x 350"
msgstr "Standard: 350 x 350"

#: includes/admin/class-admin.php:82
msgid "Album Cover size"
msgstr "Album Covergröße"

#: includes/admin/class-admin.php:83 includes/admin/class-admin.php:91
msgid "You will need to regenerate thumbnails once this value is changed"
msgstr ""
"Du musst die Miniaturansichten neu generieren, sobald dieser Wert geändert "
"wird"

#: includes/admin/class-admin.php:89
msgid "Default : 250 x 250"
msgstr "Standard : 250 x 250"

#: includes/admin/class-admin.php:90
msgid "Photo thumbnail size"
msgstr "Miniaturansichtsgröße der Fotos"

#: includes/admin/templates/role/photos.php:15
msgid "Enable photos feature?"
msgstr "Fotofunktion aktivieren?"

#: includes/admin/templates/role/photos.php:16
msgid "Can this role have user photos feature?"
msgstr "Kann diese Rolle die Funktion Benutzerfotos haben?"

#: includes/core/class-user-photos-account.php:32
msgid "My Photos"
msgstr "Meine Fotos"

#: includes/core/class-user-photos-ajax.php:269
#: includes/core/class-user-photos-ajax.php:442
#: includes/core/class-user-photos-ajax.php:615
#: includes/core/class-user-photos-ajax.php:690
#: includes/core/class-user-photos-ajax.php:764
#: includes/core/class-user-photos-ajax.php:840
msgid "Invalid nonce"
msgstr "Ungültige Nonce"

#: includes/core/class-user-photos-ajax.php:290
#: includes/core/class-user-photos-ajax.php:462
msgid "Album title is required"
msgstr "Albumtitel ist erforderlich"

#: includes/core/class-user-photos-ajax.php:297
msgid "files are not allowed"
msgstr "Dateien sind nicht erlaubt"

#: includes/core/class-user-photos-ajax.php:307
#: includes/core/class-user-photos-ajax.php:471
#: includes/core/class-user-photos-ajax.php:476
#: includes/core/class-user-photos-ajax.php:631
#: includes/core/class-user-photos-ajax.php:636
#: includes/core/class-user-photos-ajax.php:703
#: includes/core/class-user-photos-ajax.php:710
#: includes/core/class-user-photos-ajax.php:849
#: includes/core/class-user-photos-ajax.php:926
msgid "Invalid request"
msgstr "Ungültige Anfrage"

#: includes/core/class-user-photos-ajax.php:716
msgid "Title is required"
msgstr "Titel ist erforderlich"

#: includes/core/class-user-photos-ajax.php:744
msgid "Update successfull"
msgstr "Update erfolgreich"

#: includes/core/class-user-photos-ajax.php:930
msgid ""
"Your download could not be created. It looks like you do not have ZipArchive "
"installed on your server."
msgstr ""
"Dein Download konnte nicht erstellt werden. Es sieht so aus, als hättest du "
"ZipArchive nicht auf deinem Server installiert."

#: includes/core/class-user-photos-ajax.php:979
msgid "Nothing to download"
msgstr "Nichts zum Herunterladen"

#: includes/core/class-user-photos-ajax.php:1025
msgid "Your image is invalid!"
msgstr "Dein Bild ist ungültig!"

#: includes/core/class-user-photos-ajax.php:1041
msgid "Your image is invalid or too large!"
msgstr "Dein Bild ist ungültig oder zu groß!"

#: includes/core/class-user-photos-ajax.php:1068
#: includes/core/class-user-photos-ajax.php:1101
msgid "You must login to like"
msgstr "Du musst dich anmelden, um Gefällt mir"

#: includes/core/class-user-photos-ajax.php:1071
#: includes/core/class-user-photos-ajax.php:1104
msgid "Invalid photo"
msgstr "Ungültiges Foto"

#: includes/core/class-user-photos-ajax.php:1136
msgid "Login to post a comment"
msgstr "Anmelden, um einen Kommentar zu schreiben"

#: includes/core/class-user-photos-ajax.php:1140
msgid "Invalid wall post"
msgstr "Ungültiger Pinnwandbeitrag"

#: includes/core/class-user-photos-ajax.php:1144
msgid "Enter a comment first"
msgstr "Gib zuerst einen Kommentar ein"

#: includes/core/class-user-photos-ajax.php:1349
msgid "Comment updated"
msgstr "Kommentar aktualisiert"

#: includes/core/class-user-photos-common.php:32
msgctxt "Post Type General Name"
msgid "User Photos"
msgstr "Benutzerfotos"

#: includes/core/class-user-photos-common.php:33
msgctxt "Post Type Singular Name"
msgid "User Album"
msgstr "Benutzeralbum"

#: includes/core/class-user-photos-common.php:36
msgid "Item Archives"
msgstr "Artikelarchive"

#: includes/core/class-user-photos-common.php:37
msgid "Item Attributes"
msgstr "Artikelattribute"

#: includes/core/class-user-photos-common.php:38
msgid "Parent Item:"
msgstr "Übergeordneter Artikel:"

#: includes/core/class-user-photos-common.php:39
msgid "All Items"
msgstr "Alle Artikel"

#: includes/core/class-user-photos-common.php:40
msgid "Add New Item"
msgstr "Neuen Artikel hinzufügen"

#: includes/core/class-user-photos-common.php:41
msgid "Add New"
msgstr "Neu hinzufügen"

#: includes/core/class-user-photos-common.php:42
msgid "New Item"
msgstr "Neuer Artikel"

#: includes/core/class-user-photos-common.php:43
msgid "Edit Item"
msgstr "Artikel bearbeiten"

#: includes/core/class-user-photos-common.php:44
msgid "Update Item"
msgstr "Artikel aktualisieren"

#: includes/core/class-user-photos-common.php:45
msgid "View Item"
msgstr "Artikel anzeigen"

#: includes/core/class-user-photos-common.php:46
msgid "View Items"
msgstr "Artikel anzeigen"

#: includes/core/class-user-photos-common.php:47
msgid "Search Item"
msgstr "Suchbegriff"

#: includes/core/class-user-photos-common.php:48
msgid "Not found"
msgstr "Nicht gefunden"

#: includes/core/class-user-photos-common.php:49
msgid "Not found in Trash"
msgstr "Nicht im Papierkorb gefunden"

#: includes/core/class-user-photos-common.php:50
msgid "Album Cover"
msgstr "Albumcover"

#: includes/core/class-user-photos-common.php:51
msgid "Set album cover"
msgstr "Albumcover festlegen"

#: includes/core/class-user-photos-common.php:52
msgid "Remove album cover"
msgstr "Albumcover entfernen"

#: includes/core/class-user-photos-common.php:53
msgid "Use as album cover"
msgstr "Als Albumcover verwenden"

#: includes/core/class-user-photos-common.php:54
msgid "Insert into item"
msgstr "In Artikel einfügen"

#: includes/core/class-user-photos-common.php:55
msgid "Uploaded to this item"
msgstr "Hochgeladen zu diesem Artikel"

#: includes/core/class-user-photos-common.php:56
msgid "Items list"
msgstr "Artikelliste"

#: includes/core/class-user-photos-common.php:57
msgid "Items list navigation"
msgstr "Navigation in der Artikelliste"

#: includes/core/class-user-photos-common.php:58
msgid "Filter items list"
msgstr "Filtern der Artikelliste"

#: includes/core/class-user-photos-common.php:62
msgid "Image gallery for Ultimate member Users"
msgstr "Bildergalerie für Ultimate Member Benutzer"

#: includes/core/class-user-photos-profile.php:38
#: includes/core/class-user-photos-profile.php:59
msgid "Photos"
msgstr "Fotos"

#: includes/core/class-user-photos-profile.php:58
msgid "Albums"
msgstr "Albums"

#: includes/core/um-user-photos-notifications.php:78
msgid "User Photos - Album Email"
msgstr "Benutzerfotos - Album E-Mail"

#: includes/core/um-user-photos-notifications.php:81
msgid ""
"Send a notification to admin when user create, delete ot update an album"
msgstr ""
"Eine Benachrichtigung an den Administrator senden, wenn der Benutzer ein "
"Album erstellt, löscht oder aktualisiert"

#: templates/account.php:17
msgid ""
"Once photos and albums are deleted, they are deleted permanently and cannot "
"be recovered."
msgstr ""
"Sobald Fotos und Alben gelöscht wurden, werden sie dauerhaft gelöscht und "
"können nicht mehr wiederhergestellt werden."

#: templates/account.php:24
msgid "Download my photos"
msgstr "Meine Fotos herunterladen"

#: templates/account.php:36
msgid "Are you sure to delete all your albums & photos?"
msgstr "Bist du sicher, dass du alle deine Alben und Fotos löschen willst?"

#: templates/account.php:37
msgid "Delete my all albums & photos"
msgstr "Alle meine Alben und Fotos löschen"

#: templates/album-head.php:24
msgid "Back"
msgstr "Zurück"

#: templates/album-head.php:51
msgid "Edit album"
msgstr "Album bearbeiten"

#: templates/album-head.php:57 templates/album-head.php:59
#: templates/album-head.php:66
msgid "Delete album"
msgstr "Album löschen"

#: templates/caption.php:45 templates/comment.php:88
msgid " ago"
msgstr " vor"

#: templates/caption.php:78 templates/comment.php:67
msgid "Likes"
msgstr "Likes"

#: templates/caption.php:85
msgid "Comments"
msgstr "Kommentare"

#: templates/caption.php:106 templates/caption.php:112 templates/comment.php:55
#: templates/comment.php:59
msgid "Like"
msgstr "Like"

#: templates/caption.php:106 templates/caption.php:115 templates/comment.php:55
#: templates/comment.php:59
msgid "Unlike"
msgstr "Unlike"

#: templates/caption.php:121 templates/comment-form.php:29
msgid "Comment"
msgstr "Kommentar"

#: templates/comment-form.php:22
msgid "Write a reply..."
msgstr "Eine Antwort schreiben....."

#: templates/comment-form.php:25 templates/modal/edit-comment.php:23
msgid "Write a comment..."
msgstr "Einen Kommentar schreiben......"

#: templates/comment.php:31
msgid ""
"Comment hidden. <a href=\"javascript:void(0);\" class=\"um-link\">Show this "
"comment</a>"
msgstr ""
"Kommentar ausgeblendet. <a href = \"Javascript: void (0);\" class = \"um-link"
"\"> Diesen Kommentar anzeigen </a>"

#: templates/comment.php:79
msgid "Reply"
msgstr "Antwort"

#: templates/comment.php:99
msgid "Edit comment"
msgstr "Kommentar bearbeiten"

#: templates/comment.php:102
msgid "Edit"
msgstr "Bearbeiten"

#: templates/comment.php:106
msgid "Delete comment"
msgstr "Kommentar löschen"

#: templates/comment.php:106
msgid "Are you sure you want to delete this comment?"
msgstr "Bist du sicher, dass du diesen Kommentar löschen möchtest?"

#: templates/comment.php:106 templates/modal/delete-album.php:31
#: templates/modal/delete-comment.php:30
msgid "Delete"
msgstr "Löschen"

#: templates/gallery-head.php:22 templates/gallery-head.php:28
msgid "New Album"
msgstr "Neues Album"

#: templates/gallery.php:27
#, php-format
msgid "%s Photo"
msgid_plural "%s Photos"
msgstr[0] "%s Foto"
msgstr[1] "%s Fotos"

#: templates/gallery.php:50 templates/photos.php:43
#: templates/single-album.php:93
msgid "Nothing to display"
msgstr "Nichts zum Anzeigen"

#: templates/modal/add-album.php:16
msgid "is too large. File should be less than "
msgstr "ist zu groß. Die Datei sollte kleiner sein als "

#: templates/modal/add-album.php:21 templates/modal/edit-album.php:25
msgid "Album title"
msgstr "Albumtitel"

#: templates/modal/add-album.php:29 templates/modal/edit-album.php:33
msgid "Album cover"
msgstr "Albumcover"

#: templates/modal/add-album.php:40
msgid "Add"
msgstr "Hinzufügen"

#: templates/modal/add-album.php:43 templates/modal/edit-album.php:78
msgid "Add photos"
msgstr "Fotos hinzufügen"

#: templates/modal/add-album.php:46 templates/modal/delete-album.php:35
#: templates/modal/delete-comment.php:34 templates/modal/edit-album.php:81
#: templates/modal/edit-comment.php:35 templates/modal/edit-image.php:45
msgid "Cancel"
msgstr "Abbrechen"

#: templates/modal/delete-album.php:19
msgid "Are you sure to delete this album?"
msgstr "Bist du sicher, dass du dieses Album löschen willst?"

#: templates/modal/edit-album.php:20
msgid " is too large. File should be less than "
msgstr " ist zu groß. Die Datei sollte kleiner sein als "

#: templates/modal/edit-album.php:58
msgid "Delete photo"
msgstr "Foto löschen"

#: templates/modal/edit-album.php:59
msgid "Sure to delete photo?"
msgstr "Möchtest du das Foto wirklich löschen?"

#: templates/modal/edit-album.php:76 templates/modal/edit-comment.php:33
#: templates/modal/edit-image.php:44
msgid "Update"
msgstr "Aktualisieren"

#: templates/modal/edit-image.php:23
msgid "Image title"
msgstr "Bildtitel"

#: templates/modal/edit-image.php:27
msgid "Image caption"
msgstr "Bildunterschrift"

#: templates/modal/edit-image.php:38
msgid "Disable comments"
msgstr "Kommentare deaktivieren"

#: templates/modal/likes.php:15
msgid "Close"
msgstr "Schließen"

#: templates/photos.php:34
msgid "Load more"
msgstr "Mehr laden"

#: templates/single-album.php:56 templates/single-album.php:59
msgid "Edit Image"
msgstr "Bild bearbeiten"

#: templates/social-activity/new-album.php:13
msgid "created a new photo album"
msgstr "ein neues Fotoalbum erstellt"

#: um-user-photos.php:42 um-user-photos.php:58
#, php-format
msgid ""
"The <strong>%s</strong> extension requires the Ultimate Member plugin to be "
"activated to work properly. You can download it <a href=\"https://wordpress."
"org/plugins/ultimate-member\">here</a>"
msgstr ""
"Die Erweiterung <strong>%s</strong> erfordert, dass das Ultimate Member "
"Plugin aktiviert ist, um richtig zu funktionieren. Sie können es <a href="
"\"https://wordpress.org/plugins/ultimate-member\">hier</a> herunterladen"

#. Plugin Name of the plugin/theme
msgid "Ultimate Member - User Photos"
msgstr "Ultimate Member - Benutzerfotos"

#. Plugin URI of the plugin/theme
msgid "http://ultimatemember.com/extensions/user-photos"
msgstr "http://ultimatemember.com/extensions/user-photos"

#. Description of the plugin/theme
msgid "Let users add albums and photos"
msgstr "Lassen Sie Benutzer Alben und Fotos hinzufügen"

#. Author of the plugin/theme
msgid "Ultimate Member"
msgstr "Ultimate Member"

#. Author URI of the plugin/theme
msgid "http://ultimatemember.com/"
msgstr "http://ultimatemember.com/"

#~ msgid "Title"
#~ msgstr "Titel"

