var WPFormsViewEntry=window.WPFormsViewEntry||function(e,n){var i={init(){n(i.ready)},ready(){i.loadAllRichTextFields(),i.bindSettingsToggle(),i.addAlternateStyles(),i.hideDropdown()},saveData(){const e=[],t=n("#wpforms-entries-settings-button").data("form-id");n(".wpforms-entries-settings-menu-items input[type=checkbox]:checked").each(function(){e.push(n(this).attr("name"))});var i={action:"wpforms_update_single_entry_filter_settings",nonce:wpforms_admin.nonce,wpforms_entry_view_settings:e,formId:t};n.post(wpforms_admin.ajax_url,i)},updateSettings:{$container:n("#wpforms-entry-fields"),$layoutWrapper:n(".wpforms-entries-fields-wrapper"),showFieldDescriptions(e){i.updateSettings.$container.find(".wpforms-entry-field-description").toggleClass("wpforms-hide",!e)},showEmptyFields(e){i.updateSettings.$container.find(".empty").toggleClass("wpforms-hide",!e)},showSectionDividers(e){i.updateSettings.$container.find(".wpforms-field-entry-divider").toggleClass("wpforms-hide",!e)},showPageBreaks(e){i.updateSettings.$container.find(".wpforms-field-entry-pagebreak").toggleClass("wpforms-hide",!e)},showUnselectedChoices(e){i.updateSettings.$container.find(".wpforms-field-entry-toggle").find(".wpforms-entry-field-value-is-choice").toggleClass("wpforms-hide",!e),i.updateSettings.$container.find(".wpforms-field-entry-toggle").find(".wpforms-entry-field-value").toggleClass("wpforms-hide",e)},showHtmlFields(e){var t=e=>[".wpforms-entries-fields-wrapper > "+e,`.wpforms-field-layout-column ${e} .wpforms-entry-field-value`].join(", ");i.updateSettings.$container.find(t(".wpforms-field-entry-html")).toggleClass("wpforms-hide",!e),i.updateSettings.$container.find(t(".wpforms-field-entry-content")).toggleClass("wpforms-hide",!e)},maintainLayouts(e){i.updateSettings.$layoutWrapper.toggleClass("wpforms-entry-maintain-layout",e),i.updateSettings.$layoutWrapper.removeClass("wpforms-entry-compact-layout"),n("#wpforms-entry-setting-compact_view").prop("checked",!1)},compactView(e){i.updateSettings.$layoutWrapper.toggleClass("wpforms-entry-compact-layout",e),i.updateSettings.$layoutWrapper.removeClass("wpforms-entry-maintain-layout"),n("#wpforms-entry-setting-maintain_layouts").prop("checked",!1)}},loadAllRichTextFields(){n(".wpforms-entry-field-value-richtext").each(function(){const e=this;var t=n(this);t.on("load",function(){i.loadRichTextField(e)}),t.attr("src",t.data("src"))})},loadRichTextField(e){var t=n(e.contentWindow.document.documentElement);t.find("body").css("font-family",n("body").css("font-family")),t.find("ul, ol").css("padding-inline-start","30px"),t.find("li").css("list-style-position","outside"),t.find("table").addClass("mce-item-table"),i.resizeRichTextField(e),i.addLinksAttr(e)},resizeRichTextField(e){var t,i;e&&e.contentWindow&&(t=e.contentWindow.document.documentElement||!1)&&(i=t.scrollHeight,i+=t.scrollWidth>t.clientWidth?20:0,e.style.height=i+"px")},addLinksAttr(e){n(e).contents().find("a").each(function(){var e=n(this);e.attr("rel","noopener"),e.attr("target")||e.attr("target","_top")})},bindSettingsToggle(){n("#wpforms-entries-settings-button").on("click",function(e){e.preventDefault(),e.stopPropagation(),n(".wpforms-entries-settings-menu").toggle(0,function(){var e=n(this);e.attr("aria-expanded",e.is(":visible"))})}),n(".wpforms-entries-settings-menu-items input").on("change",i.toggleMode)},hideDropdown(){n(e).on("click",function(e){var t=n(".wpforms-entries-settings-menu");n(e.target).closest(".wpforms-entries-settings-menu:visible").length||t.attr("aria-expanded","false").hide()})},toggleMode(){var e=n(this),t=e.is(":checked"),e=e.attr("name").replace(/([-_][a-z])/g,e=>e.toUpperCase()).replaceAll("_","");i.updateSettings[e](t),i.saveData()},addAlternateStyles(){n(".wpforms-field-entry-fields").each(function(e){e%2!=0&&n(this).addClass("wpforms-entry-field-row-alt")})}};return i}(document,(window,jQuery));WPFormsViewEntry.init();