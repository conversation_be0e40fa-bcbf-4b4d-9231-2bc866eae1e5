.wpforms-form .wpforms-field-stripe-credit-card .StripeElement {
  margin-bottom: 5px;
}

.wpforms-form .wpforms-field-stripe-credit-card .StripeElement .__PrivateStripeElement iframe {
  margin: 0 0 0 -4px !important;
}

.wpforms-form .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview {
  display: flex;
  position: absolute;
  width: fit-content;
  height: 36px;
  line-height: 36px;
  padding: 0 0 0 10px;
  top: 0;
  left: 0;
  bottom: 0;
}

.wpforms-form .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview svg {
  width: 24px;
  fill: #000000;
  margin-right: 10px;
}

.wpforms-form .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview span {
  opacity: 0.5;
  font-size: 16px;
  color: #333333;
}

.wpforms-form .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-expcvc-preview {
  display: block;
  position: absolute;
  width: fit-content;
  height: 36px;
  line-height: 36px;
  padding: 0 10px 0 0;
  font-size: 16px;
  top: 0;
  right: 0;
  bottom: 0;
  color: #333333;
  opacity: 0.5;
}

.wpforms-form .wpforms-field-stripe-credit-card .wpforms-field-row {
  container-type: inline-size;
  container-name: wpforms-field-row-small  wpforms-field-row-responsive;
}

@container wpforms-field-row-small (max-width: 200px) {
  .wpforms-form .wpforms-field-stripe-credit-card .wpforms-field-row .wpforms-stripe-payment-element-cvc-preview svg,
  .wpforms-form .wpforms-field-stripe-credit-card .wpforms-field-row .wpforms-stripe-payment-element-cardnumber-preview,
  .wpforms-form .wpforms-field-stripe-credit-card .wpforms-field-row .wpforms-field-stripe-credit-card-number-expcvc-preview {
    display: none;
  }
}

.wpforms-lead-forms-container.wpforms-container .wpforms-field-stripe-credit-card .wpforms-field-row {
  margin-left: 0;
  margin-right: 0;
}

.wpforms-lead-forms-container.wpforms-container .wpforms-field-stripe-credit-card .wpforms-field-row .StripeElement {
  width: calc( 100% + 8px);
}

.wpforms-lead-forms-container.wpforms-container .wpforms-field-stripe-credit-card .wpforms-field-row select {
  max-width: 100%;
}

.wpforms-lead-forms-container.wpforms-container .wpforms-field-stripe-credit-card-number-placeholder-preview {
  height: 46px;
  line-height: 46px;
  color: rgba(var(--wpforms-lead-forms-secondary-text-color), 1);
}

.wpforms-lead-forms-container.wpforms-container .wpforms-field-stripe-credit-card-number-placeholder-preview span {
  color: rgba(var(--wpforms-lead-forms-secondary-text-color), 1);
}

.wpforms-lead-forms-container.wpforms-container .wpforms-field-stripe-credit-card-number-expcvc-preview {
  height: 46px;
  line-height: 46px;
  color: rgba(var(--wpforms-lead-forms-secondary-text-color), 0.5);
}

.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=date],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=datetime],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=datetime-local],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=email],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=month],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=number],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=password],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=range],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=search],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=tel],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=text],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=time],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=url],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=week],
.wpforms-lead-forms-container.wpforms-container .wpforms-field select,
.wpforms-lead-forms-container.wpforms-container .wpforms-field textarea {
  opacity: 1;
  background-color: transparent;
}

.wpforms-lead-forms-container.wpforms-container .wpforms-field .wpforms-field-row.wpforms-no-columns {
  display: block;
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-cardnumber {
  background-color: var(--wpforms-field-background-color);
  box-sizing: border-box;
  border-radius: var(--wpforms-field-border-radius);
  color: var(--wpforms-field-text-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--wpforms-field-size-font-size);
  border-width: var(--wpforms-field-border-size);
  border-style: var(--wpforms-field-border-style);
  border-color: var(--wpforms-field-border-color);
  padding: 0 var(--wpforms-field-size-padding-h);
  height: var(--wpforms-field-size-input-height);
  width: 100%;
  line-height: 1;
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-cardnumber.wpforms-stripe-element-focus {
  border-width: var(--wpforms-field-border-size, 0);
  border-style: solid;
  border-color: var(--wpforms-button-background-color);
  box-shadow: 0 0 0 1px var(--wpforms-button-background-color), 0px 1px 2px rgba(0, 0, 0, 0.15);
  outline: none;
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-cardnumber.wpforms-stripe-element-invalid {
  border-width: var(--wpforms-field-border-size);
  border-style: solid;
  border-color: var(--wpforms-label-error-color);
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-cardnumber.wpforms-stripe-element-invalid:hover {
  border-width: var(--wpforms-field-border-size);
  border-style: solid;
  border-color: var(--wpforms-label-error-color);
  box-shadow: 0 0 2px 0 var(--wpforms-label-error-color);
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-cardnumber.wpforms-stripe-element-invalid.wpforms-stripe-element-focus {
  border-width: var(--wpforms-field-border-size);
  border-style: solid;
  border-color: var(--wpforms-label-error-color);
  box-shadow: 0 0 0 1px var(--wpforms-label-error-color);
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-cardnumber > .__PrivateStripeElement {
  width: 100%;
  height: calc( var( --wpforms-field-size-font-size ) + 4px);
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-sublabel + .wpforms-error {
  margin-top: calc( 1.5 * var( --wpforms-field-size-sublabel-spacing ));
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview {
  display: flex;
  position: absolute;
  width: 100%;
  height: var(--wpforms-field-size-input-height);
  line-height: var(--wpforms-field-size-input-height);
  padding: 0 0 0 var(--wpforms-field-size-padding-h);
  top: 0;
  left: 0;
  bottom: 0;
  overflow-x: hidden;
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview svg {
  width: 24px;
  fill: #000000;
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview span {
  opacity: 0.5;
  font-size: var(--wpforms-field-size-font-size);
  color: var(--wpforms-field-text-color);
  white-space: nowrap;
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-expcvc-preview {
  display: block;
  position: absolute;
  width: fit-content;
  height: var(--wpforms-field-size-input-height);
  line-height: var(--wpforms-field-size-input-height);
  padding: 0 calc( var( --wpforms-field-size-padding-h ) * 2) 0 0;
  font-size: var(--wpforms-field-size-font-size);
  top: 0;
  right: 0;
  bottom: 0;
  color: var(--wpforms-field-text-color);
  opacity: 0.5;
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-row {
  container-type: inline-size;
  container-name: wpforms-field-row-small;
}

@container wpforms-field-row-small (max-width: 300px) {
  div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-row .wpforms-field-stripe-credit-card-number-expcvc-preview {
    display: none;
  }
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-stripe-payment-element-cvc-preview svg {
  position: absolute !important;
  bottom: calc( ( var( --wpforms-field-size-input-height ) - 24px ) / 2);
  right: var(--wpforms-field-size-padding-h);
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-stripe-payment-element .wpforms-field-row {
  container-type: inline-size;
  container-name: wpforms-field-row-xs wpforms-field-row-s wpforms-field-row-m wpforms-field-row-responsive;
}

@container wpforms-field-row-m (max-width: 320px) {
  div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-stripe-payment-element .wpforms-field-row .wpforms-stripe-payment-element-cardnumber-preview {
    width: 64px;
  }
}

@container wpforms-field-row-s (max-width: 240px) {
  div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-stripe-payment-element .wpforms-field-row .wpforms-stripe-payment-element-cardnumber-preview {
    width: 32px;
  }
}

@container wpforms-field-row-xs (max-width: 210px) {
  div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-stripe-payment-element .wpforms-field-row .wpforms-stripe-payment-element-cardnumber-preview {
    display: none;
  }
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-stripe-payment-element-cardnumber-preview {
  position: absolute;
  bottom: calc( ( var( --wpforms-field-size-input-height ) - 20px ) / 2);
  right: var(--wpforms-field-size-padding-h);
  width: 136px;
  height: 20px;
  background-image: url("../../../images/integrations/stripe/cc-preview.png");
  background-repeat: no-repeat;
  background-size: 136px 20px;
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .StripeElement {
  margin: 0 4px var(--wpforms-field-size-input-spacing) -4px;
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .StripeElement:last-of-type {
  margin-bottom: 0;
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .StripeElement .__PrivateStripeElement iframe {
  margin: 0 !important;
}
