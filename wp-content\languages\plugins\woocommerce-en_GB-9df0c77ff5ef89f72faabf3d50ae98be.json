{"translation-revision-date": "2024-10-02 16:14:23+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "An error has prevented the block from being updated.": ["An error has prevented the block from being updated."], "Edit selected product": ["Edit selected product"], "%1$s, has %2$d variation": ["%1$s, has %2$d variation", "%1$s, has %2$d variations"], "%1$d variations": ["%1$d variations"], "Loading…": ["Loading…"], "Edit this product's details": ["Edit this product's details"], "Single Product Block Error": ["Single Product Block Error"], "Reset layout": ["Reset layout"], "Edit details such as title, price, description and more.": ["Edit details such as title, price, description, and more."], "There was an error loading the content.": ["There was an error loading the content."], "Oops!": ["Oops!"], "Display the title of a product.": ["Display the title of a product."], "Reset layout to default": ["Reset layout to default"], "The following error was returned": ["The following error was returned"], "Sorry, an error occurred": ["Sorry, an error occurred"], "The following error was returned from the API": ["The following error was returned from the API"], "Retry": ["Retry"], "Search results updated.": ["Search results updated."], "%d item selected": ["%d item selected", "%d items selected"], "Search for items": ["Search for items"], "No results for %s": ["No results for %s"], "No items found.": ["No items found."], "Clear all selected items": ["Clear all selected items"], "Clear all": ["Clear all"], "Remove %s": ["Remove %s"], "Done": ["Done"], "Layout": ["Layout"], "Product search results updated.": ["Product search results updated."], "Search for a product to display": ["Search for a product to display"], "Your store doesn't have any products.": ["Your store doesn't have any products."], "Product Title": ["Product Title"], "Error:": ["Error:"], "Products": ["Products"], "Product": ["Product"]}}, "comment": {"reference": "assets/client/blocks/single-product.js"}}