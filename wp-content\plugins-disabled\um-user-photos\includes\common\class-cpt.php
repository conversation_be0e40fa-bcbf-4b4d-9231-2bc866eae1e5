<?php
namespace um_ext\um_user_photos\common;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class CPT
 *
 * @package um_ext\um_user_photos\common
 */
class CPT {

	/**
	 * CPT constructor.
	 */
	public function __construct() {
		add_action( 'init', array( $this, 'photos_post_type' ), 0 );
		add_filter( 'um_cpt_list', array( &$this, 'add_cpt' ) );
		add_action( 'template_redirect', array( $this, 'photos_redirect' ) );
	}

	/**
	 * Register Custom Post Type
	 */
	public function photos_post_type() {
		$labels = array(
			'name'                  => _x( 'User Photos', 'Post Type General Name', 'um-user-photos' ),
			'singular_name'         => _x( 'User Album', 'Post Type Singular Name', 'um-user-photos' ),
			'menu_name'             => __( 'User Photos', 'um-user-photos' ),
			'name_admin_bar'        => __( 'User Photos', 'um-user-photos' ),
			'archives'              => __( 'Item Archives', 'um-user-photos' ),
			'attributes'            => __( 'Item Attributes', 'um-user-photos' ),
			'parent_item_colon'     => __( 'Parent Item:', 'um-user-photos' ),
			'all_items'             => __( 'All Items', 'um-user-photos' ),
			'add_new_item'          => __( 'Add New Item', 'um-user-photos' ),
			'add_new'               => __( 'Add New', 'um-user-photos' ),
			'new_item'              => __( 'New Item', 'um-user-photos' ),
			'edit_item'             => __( 'Edit Item', 'um-user-photos' ),
			'update_item'           => __( 'Update Item', 'um-user-photos' ),
			'view_item'             => __( 'View Item', 'um-user-photos' ),
			'view_items'            => __( 'View Items', 'um-user-photos' ),
			'search_items'          => __( 'Search Item', 'um-user-photos' ),
			'not_found'             => __( 'Not found', 'um-user-photos' ),
			'not_found_in_trash'    => __( 'Not found in Trash', 'um-user-photos' ),
			'featured_image'        => __( 'Album Cover', 'um-user-photos' ),
			'set_featured_image'    => __( 'Set album cover', 'um-user-photos' ),
			'remove_featured_image' => __( 'Remove album cover', 'um-user-photos' ),
			'use_featured_image'    => __( 'Use as album cover', 'um-user-photos' ),
			'insert_into_item'      => __( /** @lang text */ 'Insert into item', 'um-user-photos' ),
			'uploaded_to_this_item' => __( 'Uploaded to this item', 'um-user-photos' ),
			'items_list'            => __( 'Items list', 'um-user-photos' ),
			'items_list_navigation' => __( 'Items list navigation', 'um-user-photos' ),
			'filter_items_list'     => __( 'Filter items list', 'um-user-photos' ),
		);
		$args   = array(
			'label'               => __( 'User Photos', 'um-user-photos' ),
			'description'         => __( 'Image gallery for Ultimate member Users', 'um-user-photos' ),
			'labels'              => $labels,
			'supports'            => array( 'title', 'thumbnail', 'author' ),
			'hierarchical'        => false,
			'public'              => false,
			'show_ui'             => false,
			'show_in_menu'        => false,
			'menu_position'       => 5,
			'show_in_admin_bar'   => false,
			'show_in_nav_menus'   => false,
			'can_export'          => true,
			'has_archive'         => false,
			'exclude_from_search' => true,
			'publicly_queryable'  => true,
			'capability_type'     => 'page',
		);
		register_post_type( 'um_user_photos', $args );
	}

	/**
	 * @param string[] $um_cpt
	 *
	 * @return string[]
	 */
	public function add_cpt( $um_cpt ) {
		$um_cpt[] = 'um_user_photos';
		return $um_cpt;
	}

	/**
	 * No access to single um_user_photos post type
	 */
	public function photos_redirect() {
		if ( is_singular( 'um_user_photos' ) ) {
			wp_safe_redirect( home_url() );
			exit;
		}
	}
}
