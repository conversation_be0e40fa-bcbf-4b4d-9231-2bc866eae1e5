.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset {
  margin-top: 10px;
  margin-bottom: 0;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset:after,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset:after {
  content: "";
  display: block;
  width: 106px;
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input {
  display: none;
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input + label,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input + label {
  background-color: #86919e;
  background-size: 100% 100%;
  border: none;
  border-radius: 4px;
  width: 106px;
  height: 76px;
  padding: 0;
  margin-bottom: 20px;
  cursor: pointer;
  transition-property: all;
  transition-duration: 0.05s;
  transition-timing-function: ease-out;
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input + label:nth-child(3n),
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input + label:nth-child(3n) {
  margin-inline-end: 0;
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input + label:hover,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input + label:hover {
  box-shadow: 0 0 0 1px #86919e;
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input + label.preset-100,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input + label.preset-100 {
  background-image: url("../images/layout/100.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input + label.preset-50-50,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input + label.preset-50-50 {
  background-image: url("../images/layout/50-50.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input + label.preset-67-33,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input + label.preset-67-33 {
  background-image: url("../images/layout/67-33.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input + label.preset-33-67,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input + label.preset-33-67 {
  background-image: url("../images/layout/33-67.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input + label.preset-33-33-33,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input + label.preset-33-33-33 {
  background-image: url("../images/layout/33-33-33.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input + label.preset-50-25-25,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input + label.preset-50-25-25 {
  background-image: url("../images/layout/50-25-25.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input + label.preset-25-25-50,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input + label.preset-25-25-50 {
  background-image: url("../images/layout/25-25-50.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input + label.preset-25-50-25,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input + label.preset-25-50-25 {
  background-image: url("../images/layout/25-50-25.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input + label.preset-25-25-25-25,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input + label.preset-25-25-25-25 {
  background-image: url("../images/layout/25-25-25-25.svg");
}

[dir="rtl"] .wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input + label, [dir="rtl"]
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input + label {
  transform: scaleX(-1);
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input:checked + label,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input:checked + label {
  border-color: #215d8f;
  background-color: #215d8f;
  box-shadow: 0 0 0 1px #056aab, 0 2px 4px rgba(0, 0, 0, 0.1);
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input:checked + label.preset-100,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input:checked + label.preset-100 {
  background-image: url("../images/layout/100-a.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input:checked + label.preset-50-50,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input:checked + label.preset-50-50 {
  background-image: url("../images/layout/50-50-a.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input:checked + label.preset-67-33,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input:checked + label.preset-67-33 {
  background-image: url("../images/layout/67-33-a.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input:checked + label.preset-33-67,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input:checked + label.preset-33-67 {
  background-image: url("../images/layout/33-67-a.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input:checked + label.preset-33-33-33,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input:checked + label.preset-33-33-33 {
  background-image: url("../images/layout/33-33-33-a.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input:checked + label.preset-50-25-25,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input:checked + label.preset-50-25-25 {
  background-image: url("../images/layout/50-25-25-a.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input:checked + label.preset-25-25-50,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input:checked + label.preset-25-25-50 {
  background-image: url("../images/layout/25-25-50-a.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input:checked + label.preset-25-50-25,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input:checked + label.preset-25-50-25 {
  background-image: url("../images/layout/25-50-25-a.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset input:checked + label.preset-25-25-25-25,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset input:checked + label.preset-25-25-25-25 {
  background-image: url("../images/layout/25-25-25-25-a.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset.wpforms-layout-display-rows input + label.preset-100,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset.wpforms-layout-display-rows input + label.preset-100 {
  background-image: url("../images/layout/100-r.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset.wpforms-layout-display-rows input + label.preset-50-50,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset.wpforms-layout-display-rows input + label.preset-50-50 {
  background-image: url("../images/layout/50-50-r.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset.wpforms-layout-display-rows input + label.preset-67-33,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset.wpforms-layout-display-rows input + label.preset-67-33 {
  background-image: url("../images/layout/67-33-r.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset.wpforms-layout-display-rows input + label.preset-33-67,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset.wpforms-layout-display-rows input + label.preset-33-67 {
  background-image: url("../images/layout/33-67-r.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset.wpforms-layout-display-rows input + label.preset-33-33-33,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset.wpforms-layout-display-rows input + label.preset-33-33-33 {
  background-image: url("../images/layout/33-33-33-r.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset.wpforms-layout-display-rows input + label.preset-50-25-25,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset.wpforms-layout-display-rows input + label.preset-50-25-25 {
  background-image: url("../images/layout/50-25-25-r.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset.wpforms-layout-display-rows input + label.preset-25-25-50,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset.wpforms-layout-display-rows input + label.preset-25-25-50 {
  background-image: url("../images/layout/25-25-50-r.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset.wpforms-layout-display-rows input + label.preset-25-50-25,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset.wpforms-layout-display-rows input + label.preset-25-50-25 {
  background-image: url("../images/layout/25-50-25-r.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset.wpforms-layout-display-rows input + label.preset-25-25-25-25,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset.wpforms-layout-display-rows input + label.preset-25-25-25-25 {
  background-image: url("../images/layout/25-25-25-25-r.svg");
}

[dir="rtl"] .wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset.wpforms-layout-display-rows input + label, [dir="rtl"]
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset.wpforms-layout-display-rows input + label {
  transform: scaleX(-1);
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset.wpforms-layout-display-rows input:checked + label.preset-100,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset.wpforms-layout-display-rows input:checked + label.preset-100 {
  background-image: url("../images/layout/100-r-a.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset.wpforms-layout-display-rows input:checked + label.preset-50-50,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset.wpforms-layout-display-rows input:checked + label.preset-50-50 {
  background-image: url("../images/layout/50-50-r-a.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset.wpforms-layout-display-rows input:checked + label.preset-67-33,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset.wpforms-layout-display-rows input:checked + label.preset-67-33 {
  background-image: url("../images/layout/67-33-r-a.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset.wpforms-layout-display-rows input:checked + label.preset-33-67,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset.wpforms-layout-display-rows input:checked + label.preset-33-67 {
  background-image: url("../images/layout/33-67-r-a.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset.wpforms-layout-display-rows input:checked + label.preset-33-33-33,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset.wpforms-layout-display-rows input:checked + label.preset-33-33-33 {
  background-image: url("../images/layout/33-33-33-r-a.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset.wpforms-layout-display-rows input:checked + label.preset-50-25-25,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset.wpforms-layout-display-rows input:checked + label.preset-50-25-25 {
  background-image: url("../images/layout/50-25-25-r-a.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset.wpforms-layout-display-rows input:checked + label.preset-25-25-50,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset.wpforms-layout-display-rows input:checked + label.preset-25-25-50 {
  background-image: url("../images/layout/25-25-50-r-a.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset.wpforms-layout-display-rows input:checked + label.preset-25-50-25,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset.wpforms-layout-display-rows input:checked + label.preset-25-50-25 {
  background-image: url("../images/layout/25-50-25-r-a.svg");
}

.wpforms-panel-fields .wpforms-field-option-layout .wpforms-field-option-row-preset.wpforms-layout-display-rows input:checked + label.preset-25-25-25-25,
.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-preset.wpforms-layout-display-rows input:checked + label.preset-25-25-25-25 {
  background-image: url("../images/layout/25-25-25-25-r-a.svg");
}

.wpforms-panel-fields .wpforms-field-layout > .label-title,
.wpforms-panel-fields .wpforms-field-repeater > .label-title {
  font-size: 20px;
}

.wpforms-panel-fields .wpforms-field-layout > .description,
.wpforms-panel-fields .wpforms-field-repeater > .description {
  margin: 0 0 5px 0;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns {
  display: flex;
  margin-top: -50px;
  margin-bottom: -15px;
  margin-inline-start: -10px;
  margin-inline-end: -15px;
  align-items: stretch;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-column {
  margin: 0;
  padding-block: 50px 65px;
  padding-inline: 0 5px;
  position: relative;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column .wpforms-field,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-column .wpforms-field {
  padding: 15px 10px;
  overflow-x: auto;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column-20,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-column-20 {
  width: 20%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column-25,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-column-25 {
  width: 25%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column-30,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-column-30 {
  width: 30%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column-33,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-column-33 {
  width: 33.33333%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column-40,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-column-40 {
  width: 40%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column-50,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-column-50 {
  width: 50%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column-60,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-column-60 {
  width: 60%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column-67,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-column-67 {
  width: 66.66666%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column-70,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-column-70 {
  width: 70%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column-100,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-column-100 {
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-drag-placeholder,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-drag-placeholder {
  min-height: 108px;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-drag-pending,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-drag-pending {
  min-height: 108px;
  padding-top: 40px;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column-placeholder,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-column-placeholder {
  width: calc( 100% - 25px);
  border-radius: 4px;
  border: 1px dashed #cccccc;
  height: 40px;
  padding: 10px;
  position: absolute;
  bottom: 15px;
  inset-inline-start: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column-placeholder .wpforms-plus-path,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-column-placeholder .wpforms-plus-path {
  fill: #a6a6a6;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column-placeholder span,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-column-placeholder span {
  color: #999999;
  font-size: 14px;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-fields-sortable-default .wpforms-layout-column-placeholder,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-fields-sortable-default .wpforms-layout-column-placeholder {
  background-color: #ffffff;
  border: 1px solid #e27730;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-fields-sortable-default .wpforms-layout-column-placeholder .wpforms-plus-path,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-fields-sortable-default .wpforms-layout-column-placeholder .wpforms-plus-path {
  fill: #e27730;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-fields-sortable-default .wpforms-layout-column-placeholder:hover,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-fields-sortable-default .wpforms-layout-column-placeholder:hover {
  background-color: #ffffff;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-fields-sortable-default .wpforms-layout-column-placeholder .normal-icon,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-fields-sortable-default .wpforms-layout-column-placeholder .normal-icon {
  display: none;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-fields-sortable-default .wpforms-layout-column-placeholder .active-icon,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-fields-sortable-default .wpforms-layout-column-placeholder .active-icon {
  display: block;
}

.wpforms-panel-fields .wpforms-field-layout > .wpforms-alert,
.wpforms-panel-fields .wpforms-field-repeater > .wpforms-alert {
  margin: 15px 0;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column .wpforms-alert-dismissible,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-column .wpforms-alert-dismissible {
  max-height: fit-content;
  overflow: auto;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field input[type=text],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field input[type=range],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field input[type=email],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field input[type=url],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field input[type=tel],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field input[type=number],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field input[type=password],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field input[type=file],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field select:not(.quantity-input),
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field textarea,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field input[type=text],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field input[type=range],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field input[type=email],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field input[type=url],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field input[type=tel],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field input[type=number],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field input[type=password],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field input[type=file],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field select:not(.quantity-input),
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field textarea,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100 input[type=text],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100 input[type=range],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100 input[type=email],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100 input[type=url],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100 input[type=tel],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100 input[type=number],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100 input[type=password],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100 input[type=file],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100 select:not(.quantity-input),
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100 textarea {
  width: 60%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large input[type=text],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large input[type=range],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large input[type=email],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large input[type=url],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large input[type=tel],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large input[type=number],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large input[type=password],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large input[type=file],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large select,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large textarea,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large input[type=text],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large input[type=range],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large input[type=email],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large input[type=url],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large input[type=tel],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large input[type=number],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large input[type=password],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large input[type=file],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large select,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large textarea,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-large input[type=text],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-large input[type=range],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-large input[type=email],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-large input[type=url],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-large input[type=tel],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-large input[type=number],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-large input[type=password],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-large input[type=file],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-large select,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-large textarea {
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large.payment-quantity-enabled .item-price,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large.payment-quantity-enabled .primary-input,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large.payment-quantity-enabled .choices,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large.payment-quantity-enabled .item-price,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large.payment-quantity-enabled .primary-input,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-large.payment-quantity-enabled .choices,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-large.payment-quantity-enabled .item-price,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-large.payment-quantity-enabled .primary-input,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-large.payment-quantity-enabled .choices {
  width: calc(100% - 85px) !important;
  min-width: calc(100% - 85px) !important;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-medium input[type=text],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-medium input[type=range],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-medium input[type=email],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-medium input[type=url],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-medium input[type=tel],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-medium input[type=number],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-medium input[type=password],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-medium input[type=file],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-medium select,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-medium textarea,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-medium input[type=text],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-medium input[type=range],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-medium input[type=email],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-medium input[type=url],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-medium input[type=tel],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-medium input[type=number],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-medium input[type=password],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-medium input[type=file],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-medium select,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-medium textarea,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-medium input[type=text],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-medium input[type=range],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-medium input[type=email],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-medium input[type=url],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-medium input[type=tel],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-medium input[type=number],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-medium input[type=password],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-medium input[type=file],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-medium select,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-medium textarea {
  width: 60%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-small input[type=text],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-small input[type=range],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-small input[type=email],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-small input[type=url],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-small input[type=tel],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-small input[type=number],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-small input[type=password],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-small input[type=file],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-small select,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-small textarea,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-small input[type=text],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-small input[type=range],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-small input[type=email],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-small input[type=url],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-small input[type=tel],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-small input[type=number],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-small input[type=password],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-small input[type=file],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-small select,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.size-small textarea,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-small input[type=text],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-small input[type=range],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-small input[type=email],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-small input[type=url],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-small input[type=tel],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-small input[type=number],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-small input[type=password],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-small input[type=file],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-small select,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-small textarea {
  width: 25%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.payment-quantity-enabled select.quantity-input,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.payment-quantity-enabled select.quantity-input,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.payment-quantity-enabled select.quantity-input {
  width: 70px !important;
  min-width: 70px !important;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.wpforms-field-date-time .format-selected,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.wpforms-field-date-time .format-selected,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.wpforms-field-date-time .format-selected {
  flex-wrap: wrap;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.wpforms-field-date-time .wpforms-date-dropdown select,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.wpforms-field-date-time .wpforms-date-dropdown select,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.wpforms-field-date-time .wpforms-date-dropdown select {
  max-width: calc(100% / 3 - 20px / 3);
  min-width: initial !important;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.wpforms-field-date-time:not(.size-small) .format-selected-date-time .wpforms-date-type-datepicker,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.wpforms-field-date-time:not(.size-small) .format-selected-date-time .wpforms-date-type-datepicker + .wpforms-time,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.wpforms-field-date-time:not(.size-small) .format-selected-date-time .wpforms-date-type-datepicker,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field.wpforms-field-date-time:not(.size-small) .format-selected-date-time .wpforms-date-type-datepicker + .wpforms-time,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.wpforms-field-date-time:not(.size-small) .format-selected-date-time .wpforms-date-type-datepicker,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.wpforms-field-date-time:not(.size-small) .format-selected-date-time .wpforms-date-type-datepicker + .wpforms-time {
  width: calc(50% - 10px);
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field input[type=text],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field input[type=range],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field input[type=email],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field input[type=url],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field input[type=tel],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field input[type=number],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field input[type=password],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field input[type=file],
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field select,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field textarea,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field .wpforms-address-scheme,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field .format-selected,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field .choices,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field .wpforms-field-content-preview,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field .wpforms-confirm,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field .wpforms-order-summary-container,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field input[type=text],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field input[type=range],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field input[type=email],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field input[type=url],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field input[type=tel],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field input[type=number],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field input[type=password],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field input[type=file],
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field select,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field textarea,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field .wpforms-address-scheme,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field .format-selected,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field .choices,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field .wpforms-field-content-preview,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field .wpforms-confirm,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field .wpforms-order-summary-container,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) input[type=text],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) input[type=range],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) input[type=email],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) input[type=url],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) input[type=tel],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) input[type=number],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) input[type=password],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) input[type=file],
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) select,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) textarea,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) .wpforms-address-scheme,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) .format-selected,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) .choices,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) .wpforms-field-content-preview,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) .wpforms-confirm,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) .wpforms-order-summary-container {
  width: 100% !important;
  min-width: 100% !important;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field.wpforms-field-date-time .format-selected,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.wpforms-field-date-time .format-selected,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).wpforms-field-date-time .format-selected {
  flex-wrap: wrap;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field.wpforms-field-date-time .wpforms-date-dropdown select,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.wpforms-field-date-time .wpforms-date-dropdown select,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).wpforms-field-date-time .wpforms-date-dropdown select {
  max-width: calc( 100% / 3 - 20px / 3);
  min-width: initial !important;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field.wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field.wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker + .wpforms-time,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker + .wpforms-time,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker + .wpforms-time {
  width: calc(50% - 10px);
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field.payment-quantity-enabled select.quantity-input,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.payment-quantity-enabled select.quantity-input,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).payment-quantity-enabled select.quantity-input {
  width: 70px !important;
  min-width: 70px !important;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field.payment-quantity-enabled .item-price,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field.payment-quantity-enabled .primary-input,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field.payment-quantity-enabled .choices,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.payment-quantity-enabled .item-price,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.payment-quantity-enabled .primary-input,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.payment-quantity-enabled .choices,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).payment-quantity-enabled .item-price,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).payment-quantity-enabled .primary-input,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).payment-quantity-enabled .choices {
  width: calc( 100% - 85px) !important;
  min-width: calc( 100% - 85px) !important;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column.wpforms-layout-column-20 .wpforms-field.payment-quantity-enabled select.quantity-input,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-20.payment-quantity-enabled select.quantity-input {
  width: 100% !important;
  min-width: 100% !important;
  margin-top: 15px;
  margin-inline-start: 0;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column.wpforms-layout-column-20 .wpforms-field.payment-quantity-enabled .item-price,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column.wpforms-layout-column-20 .wpforms-field.payment-quantity-enabled .primary-input,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column.wpforms-layout-column-20 .wpforms-field.payment-quantity-enabled .choices,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-20.payment-quantity-enabled .item-price,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-20.payment-quantity-enabled .primary-input,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-20.payment-quantity-enabled .choices {
  width: 100% !important;
  min-width: 100% !important;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column.wpforms-layout-column-25 .wpforms-field.payment-quantity-enabled select.quantity-input,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-25.payment-quantity-enabled select.quantity-input {
  width: 100% !important;
  min-width: 100% !important;
  margin-top: 15px;
  margin-inline-start: 0;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column.wpforms-layout-column-25 .wpforms-field.payment-quantity-enabled .item-price,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column.wpforms-layout-column-25 .wpforms-field.payment-quantity-enabled .primary-input,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column.wpforms-layout-column-25 .wpforms-field.payment-quantity-enabled .choices,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-25.payment-quantity-enabled .item-price,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-25.payment-quantity-enabled .primary-input,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-25.payment-quantity-enabled .choices {
  width: 100% !important;
  min-width: 100% !important;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-20 .wpforms-field.wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker.wpforms-date,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-20 .wpforms-field.wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker.wpforms-date + .wpforms-time,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-20.wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker.wpforms-date,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-20.wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker.wpforms-date + .wpforms-time {
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-20 .wpforms-field.wpforms-field-date-time .wpforms-date-type-dropdown + .wpforms-time,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-20.wpforms-field-date-time .wpforms-date-type-dropdown + .wpforms-time {
  min-width: 100%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-20 .wpforms-field.wpforms-summary-enabled .wpforms-order-summary-container,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-20.wpforms-summary-enabled .wpforms-order-summary-container {
  display: none;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-20 .wpforms-field.wpforms-summary-enabled .wpforms-total-amount,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-20.wpforms-summary-enabled .wpforms-total-amount {
  display: block;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-25 .wpforms-field.wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker.wpforms-date,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-25 .wpforms-field.wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker.wpforms-date + .wpforms-time,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-25.wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker.wpforms-date,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-25.wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker.wpforms-date + .wpforms-time {
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-25 .wpforms-field.wpforms-field-date-time .wpforms-date-type-dropdown + .wpforms-time,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-25.wpforms-field-date-time .wpforms-date-type-dropdown + .wpforms-time {
  min-width: 100%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-25 .wpforms-field.wpforms-summary-enabled .wpforms-order-summary-container,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-25.wpforms-summary-enabled .wpforms-order-summary-container {
  display: none;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-25 .wpforms-field.wpforms-summary-enabled .wpforms-total-amount,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-25.wpforms-summary-enabled .wpforms-total-amount {
  display: block;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-30 .wpforms-field.wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker.wpforms-date,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-30 .wpforms-field.wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker.wpforms-date + .wpforms-time,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-30.wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker.wpforms-date,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-30.wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker.wpforms-date + .wpforms-time {
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-30 .wpforms-field.wpforms-field-date-time .wpforms-date-type-dropdown + .wpforms-time,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-30.wpforms-field-date-time .wpforms-date-type-dropdown + .wpforms-time {
  min-width: 100%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-30 .wpforms-field.wpforms-summary-enabled .wpforms-order-summary-container,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-30.wpforms-summary-enabled .wpforms-order-summary-container {
  display: none;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-30 .wpforms-field.wpforms-summary-enabled .wpforms-total-amount,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-30.wpforms-summary-enabled .wpforms-total-amount {
  display: block;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-33 .wpforms-field.wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker.wpforms-date,
.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-33 .wpforms-field.wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker.wpforms-date + .wpforms-time,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-33.wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker.wpforms-date,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-33.wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker.wpforms-date + .wpforms-time {
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-33 .wpforms-field.wpforms-field-date-time .wpforms-date-type-dropdown + .wpforms-time,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-33.wpforms-field-date-time .wpforms-date-type-dropdown + .wpforms-time {
  min-width: 100%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-33 .wpforms-field.wpforms-summary-enabled .wpforms-order-summary-container,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-33.wpforms-summary-enabled .wpforms-order-summary-container {
  display: none;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-33 .wpforms-field.wpforms-summary-enabled .wpforms-total-amount,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column-33.wpforms-summary-enabled .wpforms-total-amount {
  display: block;
}

.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column {
  overflow-x: hidden;
}

.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-20 .wpforms-field-authorize_net .wpforms-field-row, .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-25 .wpforms-field-authorize_net .wpforms-field-row, .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-30 .wpforms-field-authorize_net .wpforms-field-row, .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-33 .wpforms-field-authorize_net .wpforms-field-row {
  display: flex;
  flex-direction: column;
}

.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-20 .wpforms-field-authorize_net .wpforms-field-row > div, .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-25 .wpforms-field-authorize_net .wpforms-field-row > div, .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-30 .wpforms-field-authorize_net .wpforms-field-row > div, .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-33 .wpforms-field-authorize_net .wpforms-field-row > div {
  position: relative;
  margin-bottom: 10px;
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-20 .wpforms-list-inline ul:not(.wpforms-icon-choices),
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-20 .wpforms-list-2-columns ul:not(.wpforms-icon-choices),
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-20 .wpforms-list-3-columns ul:not(.wpforms-icon-choices), .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-25 .wpforms-list-inline ul:not(.wpforms-icon-choices),
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-25 .wpforms-list-2-columns ul:not(.wpforms-icon-choices),
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-25 .wpforms-list-3-columns ul:not(.wpforms-icon-choices), .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-30 .wpforms-list-inline ul:not(.wpforms-icon-choices),
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-30 .wpforms-list-2-columns ul:not(.wpforms-icon-choices),
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-30 .wpforms-list-3-columns ul:not(.wpforms-icon-choices), .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-33 .wpforms-list-inline ul:not(.wpforms-icon-choices),
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-33 .wpforms-list-2-columns ul:not(.wpforms-icon-choices),
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-33 .wpforms-list-3-columns ul:not(.wpforms-icon-choices) {
  flex-direction: column;
}

.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-20 .wpforms-list-inline ul:not(.wpforms-icon-choices) li,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-20 .wpforms-list-2-columns ul:not(.wpforms-icon-choices) li,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-20 .wpforms-list-3-columns ul:not(.wpforms-icon-choices) li, .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-25 .wpforms-list-inline ul:not(.wpforms-icon-choices) li,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-25 .wpforms-list-2-columns ul:not(.wpforms-icon-choices) li,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-25 .wpforms-list-3-columns ul:not(.wpforms-icon-choices) li, .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-30 .wpforms-list-inline ul:not(.wpforms-icon-choices) li,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-30 .wpforms-list-2-columns ul:not(.wpforms-icon-choices) li,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-30 .wpforms-list-3-columns ul:not(.wpforms-icon-choices) li, .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-33 .wpforms-list-inline ul:not(.wpforms-icon-choices) li,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-33 .wpforms-list-2-columns ul:not(.wpforms-icon-choices) li,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-33 .wpforms-list-3-columns ul:not(.wpforms-icon-choices) li {
  width: 100%;
  max-width: 100%;
  margin: 0 0 5px 0;
}

.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-20 .wpforms-list-inline ul.wpforms-icon-choices,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-20 .wpforms-list-2-columns ul.wpforms-icon-choices,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-20 .wpforms-list-3-columns ul.wpforms-icon-choices, .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-25 .wpforms-list-inline ul.wpforms-icon-choices,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-25 .wpforms-list-2-columns ul.wpforms-icon-choices,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-25 .wpforms-list-3-columns ul.wpforms-icon-choices, .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-30 .wpforms-list-inline ul.wpforms-icon-choices,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-30 .wpforms-list-2-columns ul.wpforms-icon-choices,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-30 .wpforms-list-3-columns ul.wpforms-icon-choices, .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-33 .wpforms-list-inline ul.wpforms-icon-choices,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-33 .wpforms-list-2-columns ul.wpforms-icon-choices,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-33 .wpforms-list-3-columns ul.wpforms-icon-choices {
  flex-direction: column;
}

.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-20 .wpforms-list-inline ul.wpforms-icon-choices li,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-20 .wpforms-list-2-columns ul.wpforms-icon-choices li,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-20 .wpforms-list-3-columns ul.wpforms-icon-choices li, .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-25 .wpforms-list-inline ul.wpforms-icon-choices li,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-25 .wpforms-list-2-columns ul.wpforms-icon-choices li,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-25 .wpforms-list-3-columns ul.wpforms-icon-choices li, .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-30 .wpforms-list-inline ul.wpforms-icon-choices li,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-30 .wpforms-list-2-columns ul.wpforms-icon-choices li,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-30 .wpforms-list-3-columns ul.wpforms-icon-choices li, .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-33 .wpforms-list-inline ul.wpforms-icon-choices li,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-33 .wpforms-list-2-columns ul.wpforms-icon-choices li,
.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column-33 .wpforms-list-3-columns ul.wpforms-icon-choices li {
  width: 100%;
  max-width: 100%;
}

.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.wpforms-field-file-upload .wpforms-file-upload-builder-modern {
  text-align: center;
}

.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.wpforms-field-captcha .format-selected-math.format-selected input[type=text] {
  width: 70px !important;
  min-width: 70px !important;
}

.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.wpforms-field-internal-information .internal-information-wrap {
  margin-inline-end: 0;
  padding-inline-end: 20px;
}

.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field .wpforms-richtext-wrap {
  min-width: auto;
}

.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.wpforms-field-textarea textarea, .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.wpforms-field-richtext textarea {
  height: 110px;
}

.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.wpforms-field-textarea.size-small textarea, .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.wpforms-field-richtext.size-small textarea {
  height: 60px;
}

.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.wpforms-field-textarea.size-medium textarea, .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.wpforms-field-richtext.size-medium textarea {
  height: 110px;
}

.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.wpforms-field-textarea.size-large textarea, .wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.wpforms-field-richtext.size-large textarea {
  height: 300px;
}

.wpforms-panel-fields .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field ul.wpforms-icon-choices {
  margin-bottom: -15px;
}

#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field-address .wpforms-geolocation-map, #wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100).wpforms-field-address .wpforms-geolocation-map,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field-text .wpforms-geolocation-map, #wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100).wpforms-field-text .wpforms-geolocation-map,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) .wpforms-field-address .wpforms-geolocation-map,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).wpforms-field-address .wpforms-geolocation-map,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) .wpforms-field-text .wpforms-geolocation-map,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).wpforms-field-text .wpforms-geolocation-map {
  min-width: 100%;
  max-width: 100%;
}

#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field-address .wpforms-city,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field-address .wpforms-state,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field-address .wpforms-postal,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field-address .wpforms-country, #wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100).wpforms-field-address .wpforms-city,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100).wpforms-field-address .wpforms-state,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100).wpforms-field-address .wpforms-postal,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100).wpforms-field-address .wpforms-country,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) .wpforms-field-address .wpforms-city,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) .wpforms-field-address .wpforms-state,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) .wpforms-field-address .wpforms-postal,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) .wpforms-field-address .wpforms-country,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).wpforms-field-address .wpforms-city,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).wpforms-field-address .wpforms-state,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).wpforms-field-address .wpforms-postal,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).wpforms-field-address .wpforms-country {
  float: none;
  width: 100%;
  margin: 0 0 10px 0;
}

#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field-name .wpforms-simple,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field-name .wpforms-first-name,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field-name .wpforms-middle-name,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field-name .wpforms-last-name, #wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100).wpforms-field-name .wpforms-simple,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100).wpforms-field-name .wpforms-first-name,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100).wpforms-field-name .wpforms-middle-name,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100).wpforms-field-name .wpforms-last-name,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) .wpforms-field-name .wpforms-simple,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) .wpforms-field-name .wpforms-first-name,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) .wpforms-field-name .wpforms-middle-name,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) .wpforms-field-name .wpforms-last-name,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).wpforms-field-name .wpforms-simple,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).wpforms-field-name .wpforms-first-name,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).wpforms-field-name .wpforms-middle-name,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).wpforms-field-name .wpforms-last-name {
  float: none;
  width: 100%;
  margin: 0 0 10px 0;
}

#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field-email .wpforms-confirm-primary,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field-email .wpforms-confirm-confirmation, #wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100).wpforms-field-email .wpforms-confirm-primary,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100).wpforms-field-email .wpforms-confirm-confirmation,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) .wpforms-field-email .wpforms-confirm-primary,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) .wpforms-field-email .wpforms-confirm-confirmation,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).wpforms-field-email .wpforms-confirm-primary,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).wpforms-field-email .wpforms-confirm-confirmation {
  float: none;
  width: 100%;
  margin: 0 0 10px 0;
}

#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field-password .wpforms-confirm-primary,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field-password .wpforms-confirm-confirmation, #wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100).wpforms-field-password .wpforms-confirm-primary,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100).wpforms-field-password .wpforms-confirm-confirmation,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) .wpforms-field-password .wpforms-confirm-primary,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) .wpforms-field-password .wpforms-confirm-confirmation,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).wpforms-field-password .wpforms-confirm-primary,
#wpforms-builder .wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100).wpforms-field-password .wpforms-confirm-confirmation {
  float: none;
  width: 100%;
  margin: 0 0 10px 0;
}

#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field-name .wpforms-simple input,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field-name .wpforms-first-name input,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field-name .wpforms-middle-name input,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field-name .wpforms-last-name input {
  width: 100%;
}

#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field-address input[type=text],
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field-address select {
  width: 100%;
  min-width: initial;
}

#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field-email .wpforms-confirm-primary input,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field-email .wpforms-confirm-confirmation input {
  width: 100%;
}

#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field-password .wpforms-confirm-primary input,
#wpforms-builder .wpforms-field-layout-columns .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field-password .wpforms-confirm-confirmation input {
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-display {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-display input {
  display: none;
}

.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-display input + label {
  background-color: #86919e;
  background-size: 100% 100%;
  border: none;
  border-radius: 4px;
  width: 170px;
  height: 110px;
  padding: 0;
  margin: 0;
  cursor: pointer;
  transition-property: box-shadow;
  transition-duration: 0.05s;
  transition-timing-function: ease-out;
}

.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-display input + label:hover {
  box-shadow: 0 0 0 1px #86919e;
}

.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-display input + label.display-rows {
  background-image: url("../images/repeater/display-rows.svg");
}

[dir="rtl"] .wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-display input + label.display-rows {
  background-image: url("../images/repeater/display-rows-rtl.svg");
}

.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-display input + label.display-blocks {
  background-image: url("../images/repeater/display-blocks.svg");
  margin-inline-end: 0;
}

[dir="rtl"] .wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-display input + label.display-blocks {
  background-image: url("../images/repeater/display-blocks-rtl.svg");
}

.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-display input:checked + label {
  border-color: #215d8f;
  background-color: #215d8f;
  background-size: 180px 120px;
  background-position: -5px -3px;
  box-shadow: 0 0 0 1px #056aab, 0 2px 4px rgba(0, 0, 0, 0.1);
}

.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-display input:checked + label.display-rows {
  background-image: url("../images/repeater/display-rows-a.svg");
}

[dir="rtl"] .wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-display input:checked + label.display-rows {
  background-image: url("../images/repeater/display-rows-rtl-a.svg");
}

.wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-display input:checked + label.display-blocks {
  background-image: url("../images/repeater/display-blocks-a.svg");
}

[dir="rtl"] .wpforms-panel-fields .wpforms-field-option-repeater .wpforms-field-option-row-display input:checked + label.display-blocks {
  background-image: url("../images/repeater/display-blocks-rtl-a.svg");
}

.wpforms-panel-fields .wpforms-field-repeater > .label-title {
  font-size: 20px;
}

.wpforms-panel-fields .wpforms-field-repeater > .description {
  margin: 0 0 5px 0;
}

.wpforms-panel-fields .wpforms-field-repeater.size-small .wpforms-layout-display-blocks .wpforms-layout-column-100 {
  width: calc( 25% + 20px);
  min-width: 275px;
}

.wpforms-panel-fields .wpforms-field-repeater.size-small .wpforms-layout-display-rows .wpforms-layout-column-100 {
  width: calc( 25% + 20px);
  min-width: 275px;
}

.wpforms-panel-fields .wpforms-field-repeater.size-small .wpforms-layout-display-rows .wpforms-layout-column-100 + .wpforms-field-repeater-display-rows-buttons {
  inset-inline-start: clamp(265px, calc( 25% + 25px), calc( 25% + 25px));
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-display-blocks .wpforms-layout-column-100, .wpforms-panel-fields .wpforms-field-repeater.size-medium .wpforms-layout-display-blocks .wpforms-layout-column-100 {
  width: calc( 60% + 10px);
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-display-rows .wpforms-layout-column-100, .wpforms-panel-fields .wpforms-field-repeater.size-medium .wpforms-layout-display-rows .wpforms-layout-column-100 {
  width: calc( 60% + 10px);
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-display-rows .wpforms-layout-column-100 + .wpforms-field-repeater-display-rows-buttons, .wpforms-panel-fields .wpforms-field-repeater.size-medium .wpforms-layout-display-rows .wpforms-layout-column-100 + .wpforms-field-repeater-display-rows-buttons {
  inset-inline-start: calc( 60% + 15px);
}

.wpforms-panel-fields .wpforms-field-repeater.size-large .wpforms-layout-display-blocks .wpforms-layout-column-100 {
  width: calc( 100% + 25px);
}

.wpforms-panel-fields .wpforms-field-repeater.size-large .wpforms-layout-display-rows .wpforms-layout-column-100 {
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-repeater.size-large .wpforms-layout-display-rows .wpforms-layout-column + .wpforms-field-repeater-display-rows-buttons {
  inset-inline: auto 15px;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-display-rows .wpforms-layout-column {
  padding-bottom: 15px;
  min-height: 105px;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-display-rows .wpforms-layout-column .wpforms-layout-column-placeholder:not(:only-child) {
  display: none;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-display-rows .wpforms-layout-column.hide-placeholder .wpforms-layout-column-placeholder {
  display: none;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons {
  margin-top: 15px;
  display: flex;
  justify-content: flex-start;
  flex-wrap: nowrap;
  gap: 10px;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons button {
  background: none;
  border: none;
  border-radius: 4px;
  min-height: 33px;
  max-width: 33%;
  padding: 6px 12px;
  line-height: 18px;
  font-size: 14px;
  font-weight: 400;
  color: #999999;
  cursor: pointer;
  transition-property: width;
  transition-duration: 0.05s;
  transition-timing-function: ease-out;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons button i {
  font-size: 14px;
  line-height: 18px;
  margin-inline-end: 5px;
  height: 18px;
  width: 14px;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="buttons_with_icons"] button {
  background: #e8e8e8;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="buttons"] button {
  background: #e8e8e8;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="buttons"] i {
  display: none;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="icons_with_text"] {
  gap: 20px;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="icons_with_text"] button {
  padding: 0;
  height: auto;
  line-height: 14px;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="icons_with_text"] i {
  line-height: 14px;
  height: auto;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="icons"] button {
  padding: 0;
  height: auto;
  line-height: 14px;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="icons"] i {
  line-height: 16px;
  font-size: 16px;
  height: auto;
  margin: 0;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="icons"] span {
  display: none;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="plain_text"] {
  gap: 20px;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="plain_text"] button {
  padding: 0;
  height: auto;
  line-height: 17px;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-repeater-display-blocks-buttons[data-button-type="plain_text"] i {
  display: none;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-display-rows {
  position: relative;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-display-rows .wpforms-layout-column:not(.wpforms-layout-column-100) + .wpforms-field-repeater-display-rows-buttons {
  inset-inline: auto 15px;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-display-rows .wpforms-layout-column:has(+ .wpforms-field-repeater-display-rows-buttons) {
  margin-inline-end: 60px;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-display-rows .wpforms-field-repeater-display-rows-buttons {
  position: absolute;
  display: flex;
  gap: 10px;
  padding: 11px 0 0 0;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-display-rows .wpforms-field-repeater-display-rows-buttons button {
  background: none;
  border: none;
  cursor: pointer;
  color: #999999;
  height: 40px;
  margin: 0;
  font-size: 16px;
  width: 16px;
  padding: 0;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-display-rows.hidden-placeholders .wpforms-layout-column {
  padding-bottom: 0;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-display-rows .wpforms-field-duplicate {
  display: none;
}

.wpforms-panel-fields .wpforms-field-repeater .wpforms-layout-display-rows .wpforms-alert {
  margin: 15px 10px;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field-payment-coupon .wpforms-field-payment-coupon-wrapper,
.wpforms-panel-fields .wpforms-field.wpforms-field-payment-coupon.wpforms-field-drag-to-column.wpforms-layout-column-100 .wpforms-field-payment-coupon-wrapper {
  max-width: 60%;
}

@media screen and (max-width: 1280px) {
  .wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field-payment-coupon .wpforms-field-payment-coupon-wrapper,
  .wpforms-panel-fields .wpforms-field.wpforms-field-payment-coupon.wpforms-field-drag-to-column.wpforms-layout-column-100 .wpforms-field-payment-coupon-wrapper {
    max-width: 60%;
  }
}

.wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.wpforms-field-payment-coupon .wpforms-field-payment-coupon-wrapper input[type=text].wpforms-field-payment-coupon-input {
  min-width: 80px !important;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field .wpforms-square-cardnumber-wrapper,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-field-drag-to-column-100 .wpforms-square-cardnumber-wrapper {
  width: 60% !important;
  min-width: auto !important;
  flex-direction: column !important;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field.size-large .wpforms-square-cardnumber-wrapper,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-field-drag-to-column-100.size-large .wpforms-square-cardnumber-wrapper {
  width: 100% !important;
  min-width: auto !important;
  flex-direction: row !important;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field.size-large .wpforms-square-cardnumber-wrapper .card-number,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-field-drag-to-column-100.size-large .wpforms-square-cardnumber-wrapper .card-number {
  border: none !important;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field.size-medium .wpforms-square-cardnumber-wrapper,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-field-drag-to-column-100.size-medium .wpforms-square-cardnumber-wrapper {
  width: 60% !important;
  min-width: auto !important;
  flex-direction: column !important;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field.size-small .wpforms-square-cardnumber-wrapper,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-field-drag-to-column-100.size-small .wpforms-square-cardnumber-wrapper {
  width: 25% !important;
  min-width: 250px !important;
  flex-direction: column !important;
}

#wpforms-panel-fields .wpforms-field-wrap .wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field .wpforms-paypal-commerce-credit-card-fields,
#wpforms-panel-fields .wpforms-field-wrap .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100 .wpforms-paypal-commerce-credit-card-fields,
#wpforms-panel-revisions .wpforms-field-wrap .wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field .wpforms-paypal-commerce-credit-card-fields,
#wpforms-panel-revisions .wpforms-field-wrap .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100 .wpforms-paypal-commerce-credit-card-fields {
  width: 60% !important;
}

#wpforms-panel-fields .wpforms-field-wrap .wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field.size-small .wpforms-paypal-commerce-credit-card-fields,
#wpforms-panel-fields .wpforms-field-wrap .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-small .wpforms-paypal-commerce-credit-card-fields,
#wpforms-panel-revisions .wpforms-field-wrap .wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field.size-small .wpforms-paypal-commerce-credit-card-fields,
#wpforms-panel-revisions .wpforms-field-wrap .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-small .wpforms-paypal-commerce-credit-card-fields {
  width: 25% !important;
}

#wpforms-panel-fields .wpforms-field-wrap .wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field.size-large .wpforms-paypal-commerce-credit-card-fields,
#wpforms-panel-fields .wpforms-field-wrap .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-large .wpforms-paypal-commerce-credit-card-fields,
#wpforms-panel-revisions .wpforms-field-wrap .wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field.size-large .wpforms-paypal-commerce-credit-card-fields,
#wpforms-panel-revisions .wpforms-field-wrap .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-large .wpforms-paypal-commerce-credit-card-fields {
  width: 100% !important;
}

.wpforms-panel-fields .wpforms-field.wpforms-field-authorize_net input[type=text],
.wpforms-panel-fields .wpforms-field.wpforms-field-authorize_net select {
  width: 100% !important;
  min-width: initial;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field.wpforms-field-signature.size-medium .wpforms-signature-wrap,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.wpforms-field-signature.size-medium .wpforms-signature-wrap {
  width: 65%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field.wpforms-field-signature.size-small .wpforms-signature-wrap,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.wpforms-field-signature.size-small .wpforms-signature-wrap {
  width: 25%;
}

.wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field.size-small > .wpforms-geolocation-map,
.wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-small > .wpforms-geolocation-map {
  max-width: 25%;
  min-width: 250px;
}

.wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field.size-medium > .wpforms-geolocation-map,
.wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-medium > .wpforms-geolocation-map {
  max-width: 60%;
}

.wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field.size-large > .wpforms-geolocation-map,
.wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100.size-large > .wpforms-geolocation-map {
  max-width: 100%;
}

.wpforms-builder-notifications-advanced .choices__inner .choices__list .choices__item .wpfroms-notifications-restrictions-enabled {
  display: none;
}

.wpforms-builder-notifications-advanced .choices__list .choices__item .wpfroms-notifications-restrictions-enabled {
  position: absolute;
  inset-inline-end: 10px;
}

.wpforms-builder-notifications-advanced .choices__list .choices__item .wpfroms-notifications-restrictions-enabled {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}
