<?php
/**
 * Main class functions
 *
 * @package um_ext\um_stripe
 */

namespace um_ext\um_stripe;

use UM_Stripe\Vendor\Symfony\Intl\Currencies;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}


/**
 * Class Functions
 *
 * @package um_ext\um_stripe
 */
class Functions {

	/**
	 * Price Options
	 *
	 * @since 1.0.0
	 * @var array $price_option
	 */
	public static $price_option = array();

	/**
	 * Get Stripe Public key
	 *
	 * @since 1.0.0
	 */
	public function get_public_key() {
		$stripe_sandbox_mode = UM()->options()->get( 'stripe_sandbox_mode' );

		if ( ! $stripe_sandbox_mode ) {
			$public_key = UM()->options()->get( 'stripe_live_publish_key' );
		} else {
			$public_key = UM()->options()->get( 'stripe_test_publish_key' );
		}

		return $public_key;
	}


	/**
	 * Get Stripe Secret key
	 *
	 * @since 1.0.0
	 */
	public function get_secret_key() {
		$stripe_sandbox_mode = UM()->options()->get( 'stripe_sandbox_mode' );

		if ( ! $stripe_sandbox_mode ) {
			$secret_key = UM()->options()->get( 'stripe_live_secret_key' );
		} else {
			$secret_key = UM()->options()->get( 'stripe_test_secret_key' );
		}

		return $secret_key;
	}


	/**
	 * Get Stripe Webhook key
	 *
	 * @since 1.0.0
	 */
	public function get_webhook_key() {

		if ( defined( 'UM_STRIPE_DEV_LOCAL' ) ) {
			$webhook = UM_STRIPE_DEV_LOCAL;
		} else {

			$stripe_sandbox_mode = UM()->options()->get( 'stripe_sandbox_mode' );

			if ( ! $stripe_sandbox_mode ) {
				$webhook = UM()->options()->get( 'stripe_live_webhook_secret' );
			} else {
				$webhook = UM()->options()->get( 'stripe_test_webhook_secret' );
			}
		}

		return $webhook;
	}


	/**
	 * Get Delete Roles option
	 *
	 * @since 1.0.0
	 * @return bool
	 */
	public function is_delete_roles() {
		return UM()->options()->get( 'stripe_merge_role' ) ? true : false;
	}


	/**
	 * Gets the webhook URL for Stripe triggers. Used mainly for
	 * asyncronous redirect payment methods in which statuses are
	 * not immediately chargeable.
	 *
	 * @since 1.0.0
	 *
	 * @return string
	 */
	public static function get_webhook_url() {

		/**
		 * Modify Stripe Webhook name
		 *
		 * @param string $webhook_name Webhook Name for the Stripe Webhook URL.
		 *
		 * @since 1.0.0
		 */
		$webhook_name = apply_filters( 'um_stripe_webhook_name', 'umm-stripe-webhook' );

		return add_query_arg( $webhook_name, 'true', trailingslashit( get_home_url() ) );
	}


	/**
	 * Get Stripe Prices
	 *
	 * @since 1.0.0
	 */
	public function get_stripe_prices() {
		$has_cache = get_option( 'um_stripe_prices_cache' );
		if ( ! empty( $has_cache ) ) {
			return $has_cache;
		}

		return false;
	}

	/**
	 * Get Stripe Plans
	 *
	 * @param bool   $include_id_in_labels Whether to include ID in the labels.
	 * @param string $mode  Filter plans by subscription/price mode.
	 *
	 * @since 1.0.0
	 * @return array Posts.
	 */
	public function get_plans( $include_id_in_labels = false, $mode = 'all' ) {

		$plans                      = get_posts( 'post_type=umm_stripe&numberposts=-1' );
		$plans                      = wp_list_pluck( $plans, 'post_title', 'ID' );
		$original_webhook_hashed_id = UM()->Stripe_API()->get_hashed_webhook();

		if ( $include_id_in_labels ) {
			foreach ( $plans as $k => $plan ) {

				$webhook_hash_id = get_post_meta( $k, '_um_subscription_webhook_hashed_id', true );
				if ( $webhook_hash_id !== $original_webhook_hashed_id ) {
					unset( $plans[ $k ] );
					continue;
				}
				if ( 'recurring' === $mode ) {
					$data = $this->get_price_data_by_post_id( $k );
					$type = $data['data']['type'];
					if ( 'recurring' === $type ) {
						$plans[ $k ] = 'ID ' . $k . ' — ' . $plan;
					} else {
						unset( $plans[ $k ] );
					}
				} else {
					$plans[ $k ] = 'ID ' . $k . ' — ' . $plan;
				}
			}
		} else {

			foreach ( $plans as $k => $plan ) {

				$webhook_hash_id = get_post_meta( $k, '_um_subscription_webhook_hashed_id', true );
				if ( $webhook_hash_id !== $original_webhook_hashed_id ) {
					unset( $plans[ $k ] );
					continue;
				}
				$plans[ $k ] = $plan;
			}
		}
		return $plans;
	}

	/**
	 * Get Stripe Plans sorted by Prices
	 *
	 * @param bool   $include_id_in_labels Whether to include ID in the labels.
	 * @param string $mode  Filter plans by subscription/price mode.
	 *
	 * @since 1.0.0
	 * @return array Posts.
	 */
	public function get_plans_sorted_by_prices( $include_id_in_labels = false, $mode = 'all' ) {

		$plans                      = get_posts( 'post_type=umm_stripe&numberposts=-1' );
		$plans                      = wp_list_pluck( $plans, 'post_title', 'ID' );
		$original_webhook_hashed_id = md5( UM()->options()->get( 'stripe_webhook_id' ) );

		if ( $include_id_in_labels ) {
			foreach ( $plans as $k => $plan ) {
				$data = $this->get_price_data_by_post_id( $k );
				if ( 'recurring' === $mode ) {
					$type = isset( $data['data']['type'] ) ? $data['data']['type'] : '';
					if ( 'recurring' === $type ) {
						$plans[ $data['data']['id'] ][] = array( $k => 'ID ' . $k . ' — ' . $plan );
					} else {
						unset( $plans[ $k ] );
					}
				} else {
					$plans[ $data['data']['id'] ][ $k ] = 'ID ' . $k . ' — ' . $plan;
				}
			}
		} else {

			foreach ( $plans as $k => $plan ) {

				$plans[ $k ] = $plan;
			}
		}

		return $plans;
	}
	/**
	 *  Get Price by Post ID
	 *
	 * @param integer $post_id Post ID.
	 *
	 * @since 1.0.0
	 * @return string Formatted Price.
	 */
	public function get_price_by_post_id( $post_id ) {

		$data = $this->get_price_data_by_post_id( $post_id );
		if ( empty( $data ) ) {
			return '-';
		}
		$price = UM()->Stripe_API()->common()->subscription()->get_amount_decimal_formatted( $data['data']['unit_amount_decimal'], $data['data']['currency'], true );

		if ( 'recurring' === $data['data']['type'] ) {
			$interval       = $data['data']['recurring']['interval'];
			$interval_count = $data['data']['recurring']['interval_count'];
			$price          = $this->get_billing_plan_title( $price, $interval, $interval_count, true );
		}

		return $price;
	}

	/**
	 * Get Price Mode by Post ID
	 *
	 * @param integer $post_id The Post ID.
	 *
	 * @since 1.0.0
	 * @return string $mode Mode in HTML format.
	 */
	public function get_price_mode_by_post_id( $post_id ) {

		$data = $this->get_price_data_by_post_id( $post_id );
		if ( empty( $data ) ) {
			return '-';
		}
		$mode = $data['data']['livemode'] ? '<i class="um-faicon-circle um-stripe-element-green"></i> ' . __( 'Live', 'um-stripe' ) : '<i class="um-faicon-circle um-stripe-element-orange"></i> ' . __( 'Sandbox', 'um-stripe' );

		return $mode;
	}

	/**
	 * Get Billing Plan Title
	 *
	 * @param float   $price Stripe Price.
	 * @param string  $interval Stripe Price Interval.
	 * @param integer $interval_count Stripe Interval Count.
	 * @param boolean $bill_next_line Add breakline before "every".
	 *
	 * @since 1.0.0
	 * @return string Formatted Billing Plan Title.
	 */
	public function get_billing_plan_title( $price, $interval, $interval_count, $bill_next_line = false ) {

		if ( $interval_count > 1 ) {
			if ( ! $bill_next_line ) {
				$price = $price . ' ' . __( 'every', 'um-stripe' ) . ' ';
			} else {
				$price = $price . '<br/>' . __( 'every', 'um-stripe' ) . ' ';
			}
		}

		switch ( $interval ) {
			case 'month':
				if ( $interval_count <= 1 ) {
					$price = $price . '/' . __( 'month', 'um-stripe' );
				} else {
					$price = $price . sprintf( /* translators: 1: Months */ _n( '%s month', '%s months', $interval_count, 'um-stripe' ), $interval_count );
				}
				break;
			case 'year':
				if ( $interval_count <= 1 ) {
					$price = $price . '/' . __( 'year', 'um-stripe' );
				} else {
					$price = $price . sprintf( /* translators: 1: Years */ _n( '%s year', '%s years', $interval_count, 'um-stripe' ), $interval_count );
				}
				break;
			case 'week':
				if ( $interval_count <= 1 ) {
					$price = $price . '/' . __( 'week', 'um-stripe' );
				} else {
					$price = $price . sprintf( /* translators: 1: Weeks */ _n( '%s week', '%s weeks', $interval_count, 'um-stripe' ), $interval_count );
				}
				break;
			case 'day':
				if ( $interval_count <= 1 ) {
					$price = $price . '/' . __( 'day', 'um-stripe' );
				} else {
					$price = $price . sprintf( /* translators: 1: Days */ _n( '%s day', '%s days', $interval_count, 'um-stripe' ), $interval_count );
				}
				break;
		}

		return $price;
	}

	/**
	 * Get formatted Billing Plan Title
	 *
	 * @param object  $price_data Stripe Price.
	 * @param string  $interval Stripe Price Interval.
	 * @param integer $interval_count Stripe Interval Count.
	 * @param boolean $bill_next_line Add breakline before "every".
	 * @param boolean $onetime Render title for one-time payment plan.
	 *
	 * @since 1.0.0
	 * @return string Formatted Billing Plan Title.
	 */
	public function get_billing_plan_formatted_title( $price_data, $interval, $interval_count, $bill_next_line = false, $onetime = false ) {

		$price = '<span class="um-stripe-price-currency">' . Currencies::getSymbol( strtoupper( $price_data->currency ) ) . '</span><span class="um-stripe-price-amount">' . UM()->Stripe_API()->common()->subscription()->get_amount_decimal_formatted( $price_data->unit_amount_decimal, $price_data->currency, false ) . '</span>';

		if ( $onetime ) {
			return $price;
		}

		if ( $interval_count > 1 ) {
			if ( ! $bill_next_line ) {
				$price = $price . ' <span class="um-stripe-price-every">' . __( 'every', 'um-stripe' ) . '</span> ';
			} else {
				$price = $price . '<br/><span class="um-stripe-price-every">' . __( 'every', 'um-stripe' ) . '</span> ';
			}
		}

		switch ( $interval ) {
			case 'month':
				if ( $interval_count <= 1 ) {
					$price = $price . '<span class="um-stripe-price-sep">/</span><span class="um-stripe-price-duration">' . __( 'month', 'um-stripe' ) . '</span>';
				} else {
					$price = $price . '<span class="um-stripe-price-duration">' . sprintf( /* translators: 1: Months */ _n( '%s month', '%s months', $interval_count, 'um-stripe' ), $interval_count ) . '</span>';
				}
				break;
			case 'year':
				if ( $interval_count <= 1 ) {
					$price = $price . '<span class="um-stripe-price-sep">/</span><span class="um-stripe-price-duration">' . __( 'year', 'um-stripe' ) . '</span>';
				} else {
					$price = $price . '<span class="um-stripe-price-duration">' . sprintf( /* translators: 1: Years */ _n( '%s year', '%s years', $interval_count, 'um-stripe' ), $interval_count ) . '</span>';
				}
				break;
			case 'week':
				if ( $interval_count <= 1 ) {
					$price = $price . '<span class="um-stripe-price-sep">/</span><span class="um-stripe-price-duration">' . __( 'week', 'um-stripe' ) . '</span>';
				} else {
					$price = $price . '<span class="um-stripe-price-duration">' . sprintf( /* translators: 1: Weeks */ _n( '%s week', '%s weeks', $interval_count, 'um-stripe' ), $interval_count ) . '</span>';
				}
				break;
			case 'day':
				if ( $interval_count <= 1 ) {
					$price = $price . '<span class="um-stripe-price-sep">/</span><span class="um-stripe-price-duration">' . __( 'day', 'um-stripe' ) . '</span>';
				} else {
					$price = $price . '<span class="um-stripe-price-duration">' . sprintf( /* translators: 1: Days */ _n( '%s day', '%s days', $interval_count, 'um-stripe' ), $interval_count ) . '</span>';
				}
				break;
		}

		return $price;
	}

	/**
	 * Get Price Data from Cache
	 *
	 * @param integer $post_id The Post ID.
	 *
	 * @since 1.0.0
	 * @return mixed Price data array, false when nothing found in the cache.
	 */
	public function get_price_data_by_post_id( $post_id ) {

		$price_id = get_post_meta( $post_id, '_um_subscription_price_id_raw', true );
		if ( isset( self::$price_option[ $price_id ] ) ) {
			return self::$price_option[ $price_id ];
		}

		return false;
	}

	/**
	 * Get Price Data from Cache by Price ID
	 *
	 * @param integer $price_id The Stripe Price ID.
	 *
	 * @since 1.0.8
	 * @return mixed Price data array, false when nothing found in the cache.
	 */
	public function get_price_data_by_price_id( $price_id ) {

		if ( isset( self::$price_option[ $price_id ] ) ) {
			return self::$price_option[ $price_id ];
		}

		return false;
	}
	/**
	 * Get Subscription Mode label
	 *
	 * @param string $mode_slug Subscription Mode slug.
	 *
	 * @since 1.0.0
	 * @return string Subscription Label
	 */
	public function get_subscription_mode_title( $mode_slug ) {

		$arr_subscription_modes = UM()->Stripe_API()->common()->cpt()->get_subscription_modes();

		return isset( $arr_subscription_modes[ $mode_slug ] ) ? $arr_subscription_modes[ $mode_slug ] : '-';
	}

	/**
	 * Get Webhook Hashed ID
	 *
	 * @since 1.2.4
	 */
	public function get_hashed_webhook() {
		if ( ! UM()->options()->get( 'stripe_sandbox_mode' ) ) {
			return md5( UM()->options()->get( 'stripe_webhook_id' ) );
		} else {
			return md5( UM()->options()->get( 'stripe_sandbox_webhook_id' ) );
		}
	}
}
