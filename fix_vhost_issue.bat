@echo off
echo ===== FIXING VIRTUALHOST CONFIGURATION ISSUE =====
echo.

echo Checking administrator privileges...
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click on the batch file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)
echo Administrator privileges confirmed.
echo.

echo 1. Backing up current VirtualHost configuration...
copy "C:\xampp\apache\conf\extra\httpd-vhosts.conf" "C:\xampp\apache\conf\extra\httpd-vhosts.conf.backup" > nul
echo Backup created: httpd-vhosts.conf.backup

echo.
echo 2. Checking current VirtualHost configuration...
findstr /C:"ServerName localhost" C:\xampp\apache\conf\extra\httpd-vhosts.conf > nul
if %errorlevel% neq 0 (
    echo Adding default localhost VirtualHost...
    echo. >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo # Default VirtualHost for localhost >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo ^<VirtualHost *:80^> >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo     DocumentRoot "C:/xampp/htdocs" >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo     ServerName localhost >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo     ServerAlias 127.0.0.1 >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo ^</VirtualHost^> >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo Default localhost VirtualHost added.
) else (
    echo Default localhost VirtualHost already exists.
)

echo.
echo 3. Checking raezg.local VirtualHost...
findstr /C:"ServerName raezg.local" C:\xampp\apache\conf\extra\httpd-vhosts.conf > nul
if %errorlevel% equ 0 (
    echo raezg.local VirtualHost already exists.
) else (
    echo Adding raezg.local VirtualHost...
    echo. >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo # raezg.local VirtualHost >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo ^<VirtualHost *:80^> >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo     ServerName raezg.local >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo     ServerAlias www.raezg.local >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo     DocumentRoot "C:/xampp/htdocs/raezg" >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo     ErrorLog "logs/raezg.local-error.log" >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo     CustomLog "logs/raezg.local-access.log" common >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo     ^<Directory "C:/xampp/htdocs/raezg"^> >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo         Options Indexes FollowSymLinks >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo         AllowOverride All >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo         Require all granted >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo     ^</Directory^> >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo ^</VirtualHost^> >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
    echo raezg.local VirtualHost added.
)

echo.
echo 4. Testing Apache configuration...
C:\xampp\apache\bin\httpd.exe -t
if %errorlevel% neq 0 (
    echo ERROR: Apache configuration test failed!
    echo Restoring backup...
    copy "C:\xampp\apache\conf\extra\httpd-vhosts.conf.backup" "C:\xampp\apache\conf\extra\httpd-vhosts.conf" > nul
    echo Configuration restored. Please check the Apache error log.
    pause
    exit /b 1
) else (
    echo Apache configuration test passed!
)

echo.
echo 5. Restarting Apache...
echo Stopping Apache processes...
taskkill /F /IM httpd.exe /T > nul 2>&1
timeout /t 3 > nul

echo Starting Apache...
net start Apache2.4 > nul 2>&1
if %errorlevel% neq 0 (
    net start Apache > nul 2>&1
    if %errorlevel% neq 0 (
        echo Failed to start Apache service. Starting manually...
        start /B C:\xampp\apache\bin\httpd.exe
        timeout /t 3 > nul
    )
)

echo.
echo 6. Verifying Apache is running...
netstat -an | findstr :80 | findstr LISTENING > nul
if %errorlevel% equ 0 (
    echo ✓ Apache is listening on port 80
) else (
    echo ✗ Apache is not listening on port 80
    echo Please check the Apache error log: C:\xampp\apache\logs\error.log
)

echo.
echo ===== CONFIGURATION COMPLETE =====
echo.
echo Please test the following URLs:
echo 1. http://localhost/ (should work)
echo 2. http://raezg.local/ (should work)
echo 3. http://127.0.0.1/raezg/ (alternative)
echo.
echo If raezg.local still doesn't work:
echo - Clear browser cache (Ctrl+F5)
echo - Try a different browser
echo - Check hosts file: C:\Windows\System32\drivers\etc\hosts
echo - Check Apache error log: C:\xampp\apache\logs\error.log
echo.
pause
