"use strict";var WPFormsRichTextField=window.WPFormsRichTextField||function(d,a,s){var e={mediaPostIdUpdateEvent:!1},r={init(){s(d).on("wpformsReady",r.customizeRichTextField).on("wpformsAjaxSubmitSuccessConfirmation",r.updateIframes),a.addEventListener("elementor/popup/show",function(e){r.reInitRichTextFields(e.detail.instance.$element)})},customizeRichTextField:function(){var n=s(d);n.on("tinymce-editor-setup",function(e,t){t.on("keyup",function(){r.validateRichTextField(t)}),t.on("focus",function(e){s(e.target.editorContainer).closest(".wp-editor-wrap").addClass("wpforms-focused")}),t.on("blur",function(e){s(e.target.editorContainer).closest(".wp-editor-wrap").removeClass("wpforms-focused")})}),n.on("wpformsRichTextContentChange",function(e,t,i){r.validateRichTextField(i),r.enableAddMediaButtons(t)}),n.on("tinymce-editor-init",function(e,t){var i=t.getDoc().body.style,o=s("body");i.fontFamily=o.css("font-family"),i.background="transparent",r.initEditorModernMarkupMode(t),r.mediaPostIdUpdate(),r.observeEditorChanges(t),r.cleanImages(t),n.trigger("wpformsRichTextEditorInit",[t])}),s("textarea.wp-editor-area").each(function(){var e=s(this);e.hasClass("wpforms-field-required")&&e.prop("required",!0)}),n.on("click",".media-modal-close, .media-modal-backdrop",r.enableAddMediaButtons),"undefined"!=typeof wp&&"function"==typeof wp.media&&wp.media.view.Modal.prototype.on("escape",function(){r.enableAddMediaButtons("escapeEvent")}),n.on("click",".switch-html",function(){const e=s(this).closest(".wp-editor-wrap");setTimeout(function(){e.find(".wp-editor-area").trigger("focus"),e.addClass("wpforms-focused")},0)}),n.on("click",".switch-tmce",function(e){e.preventDefault();var e=s(this).closest(".wp-editor-wrap"),t=e.find(".wp-editor-area").attr("id");const i=tinyMCE.get(t);i&&(e.addClass("wpforms-focused"),setTimeout(()=>{i.focus(!1)},0))}),n.on("focus",".wp-editor-area",function(){s(this).closest(".wp-editor-wrap").addClass("wpforms-focused")}),n.on("blur",".wp-editor-area",function(e){s(this).closest(".wp-editor-wrap").removeClass("wpforms-focused")})},cleanImages:function(e){var t=e.getContent({format:"raw"}),i=d.createElement("div"),o=(i.innerHTML=t,i.querySelectorAll("img"));for(let e=0;e<o.length;e++)o[e].outerHTML=o[e].outerHTML.replace(/"”|”"|"″|″"/g,'"');e.setContent(i.innerHTML)},addMediaButton(e){console.warn('WARNING! Function "WPFormsRichTextField.addMediaButton()" has been deprecated!'),wpforms_settings.richtext_add_media_button&&e.addButton("wp_add_media",{tooltip:"Add Media",icon:"dashicon dashicons-admin-media",cmd:"WP_Medialib"})},enableAddMediaButtons:function(e){("escapeEvent"===e||r.isCloseEvent(e)||r.isMutationImage(e))&&s(".mce-btn-group button i.dashicons-admin-media").closest(".mce-btn").removeClass("mce-btn-disabled")},isCloseEvent:function(e){return void 0!==e.target&&(e.target.classList.contains("media-modal-icon")||e.target.classList.contains("media-modal-backdrop"))},isMutationImage:function(e){var t;return void 0!==e.addedNodes&&void 0!==e.addedNodes[0]&&(t=!1,e.addedNodes.forEach(function(e){return"IMG"===e.tagName||"A"===e.tagName&&e.querySelector("img")?!(t=!0):void 0}),t)},disableAddMediaButtons:function(){s(".mce-btn-group button i.dashicons-admin-media").closest(".mce-btn").addClass("mce-btn-disabled")},mediaPostIdUpdate:function(){e.mediaPostIdUpdateEvent||(s(".wpforms-field-richtext-media-enabled .mce-toolbar .mce-btn").on("click touchstart",function(e){var t,e=s(e.target);!e.hasClass("dashicons-admin-media")&&0===e.find(".dashicons-admin-media").length||(t=e.closest("form").data("formid"),e=e.closest(".wpforms-field-richtext").data("field-id"),wp.media.model.settings.post.id="wpforms-"+t+"-field_"+e,r.disableAddMediaButtons())}),e.mediaPostIdUpdateEvent=!0)},observeEditorChanges:function(o){new MutationObserver(function(e,t){for(var i in e)"childList"===e[i].type&&s(d).trigger("wpformsRichTextContentChange",[e[i],o])}).observe(o.iframeElement.contentWindow.document.body,{childList:!0,subtree:!0,attributes:!0})},validateRichTextField:function(e){var t;e&&s(e.iframeElement).closest("form").data("validator")&&(t=s("#"+e.id),e.getContent()!==t.val())&&(e.save(),t.valid())},reInitRichTextFields(e){"undefined"!=typeof tinyMCEPreInit&&"undefined"!=typeof tinymce&&e.find(".wp-editor-area").each(function(){var e=s(this).attr("id");tinymce.get(e)&&tinyMCE.execCommand("mceRemoveEditor",!1,e),a.quicktags(tinyMCEPreInit.qtInit[e]),s("#"+e).css("visibility","initial"),tinymce.init(tinyMCEPreInit.mceInit[e])})},initEditorModernMarkupMode:function(e){var t,i,o,n,d;wpforms.isModernMarkupEnabled()&&!a.WPFormsEditEntry&&a.WPForms.FrontendModern&&(t=e.getDoc().body.style,d=(i=s(e.getElement())).closest(".wpforms-field"),i=i.closest(".wpforms-form"),o=(i=a.WPForms.FrontendModern.getCssVars(i))["field-size-input-height"]?i["field-size-input-height"].replace("px",""):43,n="medium",n=d.hasClass("wpforms-field-small")?"small":"medium",n=d.hasClass("wpforms-field-large")?"large":n,d=e.getWin().clientWidth,e.theme.resizeTo(d,o*{small:1.8,medium:2.79,large:5.12}[n]),t.color=i["field-text-color"],t.fontSize=i["field-size-font-size"])},updateIframes(e){s(e.target).find(".wpforms-iframe").each(a?.WPFormsIframe?.update)}};return r}(document,window,jQuery);WPFormsRichTextField.init();