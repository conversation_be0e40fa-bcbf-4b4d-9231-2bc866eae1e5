<?php

declare (strict_types=1);
namespace WPForms\Vendor\Square\Models;

use stdClass;
class Shipping<PERSON>ee implements \JsonSerializable
{
    /**
     * @var array
     */
    private $name = [];
    /**
     * @var Money
     */
    private $charge;
    /**
     * @param Money $charge
     */
    public function __construct(Money $charge)
    {
        $this->charge = $charge;
    }
    /**
     * Returns Name.
     * The name for the shipping fee.
     */
    public function getName() : ?string
    {
        if (\count($this->name) == 0) {
            return null;
        }
        return $this->name['value'];
    }
    /**
     * Sets Name.
     * The name for the shipping fee.
     *
     * @maps name
     */
    public function setName(?string $name) : void
    {
        $this->name['value'] = $name;
    }
    /**
     * Unsets Name.
     * The name for the shipping fee.
     */
    public function unsetName() : void
    {
        $this->name = [];
    }
    /**
     * Returns Charge.
     * Represents an amount of money. `Money` fields can be signed or unsigned.
     * Fields that do not explicitly define whether they are signed or unsigned are
     * considered unsigned and can only hold positive amounts. For signed fields, the
     * sign of the value indicates the purpose of the money transfer. See
     * [Working with Monetary Amounts](https://developer.squareup.com/docs/build-basics/working-with-
     * monetary-amounts)
     * for more information.
     */
    public function getCharge() : Money
    {
        return $this->charge;
    }
    /**
     * Sets Charge.
     * Represents an amount of money. `Money` fields can be signed or unsigned.
     * Fields that do not explicitly define whether they are signed or unsigned are
     * considered unsigned and can only hold positive amounts. For signed fields, the
     * sign of the value indicates the purpose of the money transfer. See
     * [Working with Monetary Amounts](https://developer.squareup.com/docs/build-basics/working-with-
     * monetary-amounts)
     * for more information.
     *
     * @required
     * @maps charge
     */
    public function setCharge(Money $charge) : void
    {
        $this->charge = $charge;
    }
    /**
     * Encode this object to JSON
     *
     * @param bool $asArrayWhenEmpty Whether to serialize this model as an array whenever no fields
     *        are set. (default: false)
     *
     * @return array|stdClass
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize(bool $asArrayWhenEmpty = \false)
    {
        $json = [];
        if (!empty($this->name)) {
            $json['name'] = $this->name['value'];
        }
        $json['charge'] = $this->charge;
        $json = \array_filter($json, function ($val) {
            return $val !== null;
        });
        return !$asArrayWhenEmpty && empty($json) ? new stdClass() : $json;
    }
}
