<?php
namespace um_ext\um_user_photos\ajax;

use WP_Query;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class User_Photos_Ajax
 * Note: It will be deprecated since new UI is live.
 * @todo deprecate since new UI is live.
 * @package um_ext\um_user_photos\ajax
 */
class Ajax_V2 {

	/**
	 * Ajax_V2 constructor.
	 */
	public function __construct() {
		// delete image
		add_action( 'wp_ajax_um_delete_album_photo', array( $this, 'um_delete_album_photo' ) );

		// update image data
		add_action( 'wp_ajax_um_user_photos_image_update', array( $this, 'update_image' ) );

		// update album
		add_action( 'wp_ajax_update_um_user_photos_album', array( $this, 'update_um_user_photos_album' ) );

		// create new album
		add_action( 'wp_ajax_create_um_user_photos_album', array( $this, 'create_um_user_photos_album' ) );

		// Delete all albums & photos
		add_action( 'wp_ajax_um_user_photos_flush_albums', array( $this, 'flush_albums' ) );

		// download all photos
		add_action( 'wp_ajax_download_my_photos', array( $this, 'download_my_photos' ) );

		// load images
		add_action( 'wp_ajax_um_user_photos_load_more', array( $this, 'um_user_photos_load_more' ) );
		add_action( 'wp_ajax_nopriv_um_user_photos_load_more', array( $this, 'um_user_photos_load_more' ) );

		// Edit Photo modal
		add_action( 'wp_ajax_um_user_photos_edit_photo_modal', array( $this, 'load_edit_photo_modal' ) );
		// New Album modal
		add_action( 'wp_ajax_um_user_photos_new_album_modal', array( $this, 'load_new_album_modal' ) );
		// Edit Album modal
		add_action( 'wp_ajax_um_user_photos_edit_album_modal', array( $this, 'load_edit_album_modal' ) );
		// Delete Album
		add_action( 'wp_ajax_um_user_photos_delete_album', array( $this, 'ajax_delete_album' ) );
		// Get gallery
		add_action( 'wp_ajax_um_user_photos_get_gallery', array( $this, 'ajax_get_gallery' ) );
		add_action( 'wp_ajax_nopriv_um_user_photos_get_gallery', array( $this, 'ajax_get_gallery' ) );

		// load comments section
		add_action( 'wp_ajax_um_user_photos_get_comment_section', array( $this, 'um_user_photos_get_comment_section' ) );

		add_action( 'wp_ajax_nopriv_um_user_photos_get_comment_section', array( $this, 'um_user_photos_get_comment_section' ) );

		//single album
		add_action( 'wp_ajax_um_user_photos_get_single_album_view', array( $this, 'get_single_album_view' ) );
		add_action( 'wp_ajax_nopriv_um_user_photos_get_single_album_view', array( $this, 'get_single_album_view' ) );

		// like photo
		add_action( 'wp_ajax_um_user_photos_like_photo', array( $this, 'um_user_photos_like_photo' ) );

		// unlike photo
		add_action( 'wp_ajax_um_user_photos_unlike_photo', array( $this, 'um_user_photos_unlike_photo' ) );

		// update comment
		add_action( 'wp_ajax_um_user_photos_comment_update', array( $this, 'um_user_photos_comment_update' ) );

		// post comment
		add_action( 'wp_ajax_um_user_photos_post_comment', array( $this, 'um_user_photos_post_comment' ) );

		// like comment
		add_action( 'wp_ajax_um_user_photos_like_comment', array( $this, 'um_user_photos_like_comment' ) );

		// unlike comment
		add_action( 'wp_ajax_um_user_photos_unlike_comment', array( $this, 'um_user_photos_unlike_comment' ) );

		// show photo likes modal
		add_action( 'wp_ajax_get_um_user_photo_likes', array( $this, 'get_um_user_photo_likes' ) );

		add_action( 'wp_ajax_nopriv_get_um_user_photo_likes', array( $this, 'get_um_user_photo_likes' ) );

		// show photo edit comment
		add_action( 'wp_ajax_get_um_user_photos_comment_edit', array( $this, 'get_um_user_photos_comment_edit' ) );

		// delete comment modal
		add_action( 'wp_ajax_get_um_user_photos_comment_delete', array( $this, 'get_um_user_photos_comment_delete' ) );

		// delete comment
		add_action( 'wp_ajax_um_user_photos_comment_delete', array( $this, 'um_user_photos_comment_delete' ) );

		// show photo likes modal
		add_action( 'wp_ajax_get_um_user_photos_comment_likes', array( $this, 'get_um_user_photos_comment_likes' ) );

		add_action( 'wp_ajax_nopriv_get_um_user_photos_comment_likes', array( $this, 'get_um_user_photos_comment_likes' ) );

		// Shortcode: [ultimatemember_albums]
		add_action( 'wp_ajax_um_user_photos_get_albums_content', array( $this, 'get_albums_content' ) );
		add_action( 'wp_ajax_nopriv_um_user_photos_get_albums_content', array( $this, 'get_albums_content' ) );
	}

	/**
	 * AJAX handler for get_albums_content request
	 */
	public function get_albums_content() {
		$atts = array();
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_pagination' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		if ( isset( $_POST['page'] ) ) {
			$atts['page'] = absint( $_POST['page'] );
		}

		if ( isset( $_POST['per_page'] ) ) {
			$atts['per_page'] = absint( $_POST['per_page'] );
		}

		if ( isset( $_POST['column'] ) ) {
			$atts['column'] = absint( $_POST['column'] );
		}

		$output = UM()->User_Photos()->common()->shortcodes_v2()->get_albums_content( $atts );
		wp_send_json_success( $output );
	}

	/**
	 * Load more photos with ajax
	 */
	public function um_user_photos_load_more() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_load_more' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$profile_id = absint( $_POST['profile'] );
		$per_page   = absint( $_POST['per_page'] );
		$page_no    = absint( $_POST['page'] );
		$offset     = ( $page_no - 1 ) * $per_page;

		$is_my_profile = ( is_user_logged_in() && get_current_user_id() === $profile_id );
		if ( ! $is_my_profile && ! um_can_view_profile( $profile_id ) ) {
			wp_send_json_error( '<p class="text-center">' . esc_html__( 'Nothing to display', 'um-user-photos' ) . '</p>' );
		}

		$user_role         = UM()->roles()->get_priority_user_role( $profile_id );
		$user_role_data    = UM()->roles()->role_data( $user_role );
		if ( empty( $user_role_data['enable_user_photos'] ) ) {
			wp_send_json_error( '<p class="text-center">' . esc_html__( 'Nothing to display', 'um-user-photos' ) . '</p>' );
		}

		// Disable posts query filter by the taxonomy 'language'. Integration with the plugin 'Polylang'.
		add_action( 'pre_get_posts', array( UM()->User_Photos()->common()->query(), 'remove_language_filter' ), 9 );

		$query_args = array(
			'post_type'      => 'attachment',
			'author__in'     => array( $profile_id ),
			'post_status'    => 'inherit',
			'post_mime_type' => 'image',
			'posts_per_page' => $per_page,
			'offset'         => $offset,
			'meta_query'     => array(
				array(
					'key'     => '_part_of_gallery',
					'value'   => 'yes',
					'compare' => '=',
				),
			),
			'orderby'        => 'ID',
		);

		$args = array(
			'post_type'      => 'um_user_photos',
			'author__in'     => array( $profile_id ),
			'posts_per_page' => -1,
			'post_status'    => 'publish',
			'fields'         => 'ids',
		);
		$args = apply_filters( 'um_user_photo_query_args', $args, $profile_id );

		$albums = new WP_Query( $args );

		if ( ! $is_my_profile ) {
			$visible_photos = array();
			if ( ! empty( $albums->posts ) ) {
				foreach ( $albums->posts as $album_id ) {
					$photos_query_args  = array(
						'post_type'      => 'attachment',
						'author__in'     => array( $profile_id ),
						'post_status'    => 'inherit',
						'post_mime_type' => 'image',
						'posts_per_page' => -1,
						'meta_query'     => array(
							array(
								'key'     => '_part_of_gallery',
								'value'   => 'yes',
								'compare' => '=',
							),
						),
						'orderby'        => 'ID',
						'post_parent'    => $album_id,
						'fields'         => 'ids',
					);
					$album_photos_query = new WP_Query( $photos_query_args );
					$photos_result      = $album_photos_query->get_posts();

					if ( ! empty( $photos_result ) ) {
						$visible_photos[] = $photos_result;
					}
				}

				$visible_photos = array_merge( ...$visible_photos );
				$visible_photos = array_unique( $visible_photos );
			}

			if ( empty( $visible_photos ) ) {
				wp_send_json_success( '' );
			}

			$query_args['post__in'] = $visible_photos;
		}

		$latest_photos = new WP_Query( $query_args );

		if ( $latest_photos->have_posts() ) {
			$photos = array();
			foreach ( $latest_photos->posts as $photo ) {
				$photos[] = $photo->ID;
			}

			$args_t = compact( 'is_my_profile', 'photos' );
			$html   = UM()->get_template( 'photos-grid.php', UM_USER_PHOTOS_PLUGIN, $args_t );

			if ( $html ) {
				$html = preg_replace(
					array( '/^\s+/im', '/\\r\\n/im', '/\\n/im', '/\\t+/im' ),
					array( '', ' ', ' ', ' ' ),
					$html
				);
			}

			wp_reset_postdata();
			wp_send_json_success( $html );
		}

		wp_reset_postdata();
		wp_send_json_success( '' );
	}

	/**
	 * Load view with ajax
	 */
	public function ajax_get_gallery() {
		// phpcs:disable WordPress.Security.NonceVerification
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_get_gallery' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$content = UM()->User_Photos()->common()->shortcodes_v2()->get_gallery_content();

		wp_send_json_success( $content );
	}

	/**
	 * Load edit photo modal content.
	 */
	public function load_edit_photo_modal() {
		if ( empty( $_POST['image_id'] ) ) {
			wp_send_json_error( __( 'Wrong photo ID.', 'um-user-photos' ) );
		}
		$photo_id = absint( $_POST['image_id'] );

		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_edit_image_modal' . $photo_id ) ) {
			wp_send_json_error( __( 'Wrong nonce.', 'um-user-photos' ) );
		}

		$can_edit = UM()->User_Photos()->common()->user()->can_edit_photo( $photo_id );
		if ( ! $can_edit ) {
			wp_send_json_error( __( 'You are not authorized to edit this photo.', 'um-user-photos' ) );
		}

		$album_id = get_post_field( 'post_parent', $photo_id );
		if ( 0 === absint( $album_id ) ) {
			$album_id = $photo_id;
		}
		$album = get_post( $album_id );
		$photo = get_post( $photo_id );

		$args_t = compact( 'album', 'photo' );
		$html   = UM()->get_template( 'modal/edit-image.php', UM_USER_PHOTOS_PLUGIN, $args_t );

		wp_send_json_success( $html );
	}

	/**
	 * Load edit album modal content.
	 */
	public function load_edit_album_modal() {
		if ( empty( $_POST['album_id'] ) ) {
			wp_send_json_error( __( 'Wrong album ID.', 'um-user-photos' ) );
		}
		$album_id = absint( $_POST['album_id'] );

		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_edit_album_modal' . $album_id ) ) {
			wp_send_json_error( __( 'Wrong nonce.', 'um-user-photos' ) );
		}

		$can_edit = UM()->User_Photos()->common()->user()->can_edit_album( $album_id );
		if ( ! $can_edit ) {
			wp_send_json_error( __( 'You are not authorized to edit this album.', 'um-user-photos' ) );
		}

		$album = get_post( $album_id );
		$privacy = get_post_meta( $album_id, '_privacy', true );

		$args_t = compact( 'album', 'privacy' );
		$html   = UM()->get_template( 'modal/edit-album.php', UM_USER_PHOTOS_PLUGIN, $args_t );

		wp_send_json_success( $html );
	}

	/**
	 * Load edit photo modal content.
	 */
	public function load_new_album_modal() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_new_album_modal' ) ) {
			wp_send_json_error( __( 'Wrong nonce.', 'um-user-photos' ) );
		}

		$user_id           = get_current_user_id();
		$count_user_photos = UM()->User_Photos()->common()->user()->photos_count( $user_id );
		$limit_user_photos = UM()->User_Photos()->common()->user()->photos_limit( $user_id );
		$limit_per_albums  = UM()->options()->get( 'um_user_photos_album_limit' );
		$limit_per_albums  = ! empty( $limit_per_albums ) ? $limit_per_albums : '';

		$args_t = compact( 'count_user_photos', 'limit_user_photos', 'limit_per_albums' );
		$html   = UM()->get_template( 'modal/add-album.php', UM_USER_PHOTOS_PLUGIN, $args_t );

		wp_send_json_success( $html );
	}

	/**
	 * Single album loading
	 *
	 * @param string $res - 'exit' or 'return'
	 */
	public function get_single_album_view( $res = 'exit' ) {
		// phpcs:ignore WordPress.Security.NonceVerification
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_single_album_view' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$album_id = absint( $_POST['id'] );
		if ( empty( $album_id ) ) {
			wp_send_json_error( __( 'Wrong album ID', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_view_album( $album_id ) ) {
			wp_send_json_error( __( 'You are not authorized for this.', 'um-user-photos' ) );
		}

		$album  = get_post( $album_id );
		$photos = get_post_meta( $album_id, '_photos', true );
		if ( empty( $photos ) ) {
			$photos = array();
		}
		$is_my_profile = is_user_logged_in() && get_current_user_id() === absint( $album->post_author );
		$count         = count( $photos );
		$images_column = UM()->options()->get( 'um_user_photos_images_column' );
		if ( ! $images_column ) {
			$images_column = 'um-user-photos-col-3';
		}
		$columns = absint( substr( $images_column, -1 ) );

		$args_t = compact( 'album', 'album_id', 'columns', 'count', 'is_my_profile', 'photos' );

		$html  = '<div class="um-user-photos-albums um-user-photos-albums-counter" data-count="' . esc_attr( $count ) . '">';
		$html .= UM()->get_template( 'album-head.php', UM_USER_PHOTOS_PLUGIN, $args_t );
		$html .= UM()->get_template( 'single-album.php', UM_USER_PHOTOS_PLUGIN, $args_t );
		$html .= '</div>';

		wp_send_json_success( $html );
	}

	/**
	 * Create new album
	 */
	public function create_um_user_photos_album() {
		$disable_title = UM()->options()->get( 'um_user_photos_disable_title' );
		$disable_cover = UM()->options()->get( 'um_user_photos_disable_cover' );

		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_add_album' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_add_album() ) {
			wp_send_json_error( __( 'You are not authorized for this.', 'um-user-photos' ) );
		}

		// Validation.
		$error   = false;
		$allowed = array(
			'image/jpeg',
			'image/png',
			'image/jpg',
			'image/gif',
		);
		$allowed = apply_filters( 'um_user_photos_allowed_image_types', $allowed );

		if ( ! $disable_title ) {
			if ( ! isset( $_POST['title'] ) || '' === sanitize_text_field( $_POST['title'] ) ) {
				$error    = true;
				$response = __( 'Album title is required', 'um-user-photos' );
			}
		}

		if ( ! is_user_logged_in() ) {
			$error    = true;
			$response = __( 'Invalid request', 'um-user-photos' );
		}

		if ( $error ) {
			wp_send_json_error( $response );
		}

		/*end validation*/
		require_once( ABSPATH . 'wp-admin/includes/image.php' );

		if ( isset( $_POST['title'] ) ) {
			$alb_title = sanitize_text_field( $_POST['title'] );
		} else {
			$alb_title = '';
		}

		$user_id = get_current_user_id();
		$post_id = wp_insert_post(
			array(
				'post_type'   => 'um_user_photos',
				'post_title'  => $alb_title,
				'post_author' => $user_id,
				'post_status' => 'publish',
			)
		);

		$photos = array();

		$limit_user_photos = UM()->User_Photos()->common()->user()->photos_limit( $user_id );
		if ( false !== $limit_user_photos && isset( $_FILES['album_images'] ) ) {
			$count_files       = count( $_FILES['album_images']['name'] );
			$count_user_photos = UM()->User_Photos()->common()->user()->photos_count( $user_id );
			if ( (int) $count_user_photos + (int) $count_files > (int) $limit_user_photos ) {
				// translators: %s is a number for uploading photos limit
				$response = sprintf( esc_html__( 'You cannot upload more photos, you have reached the limit of uploads. Delete old photos or upload few photos. Photo limit = %d', 'um-user-photos' ), $limit_user_photos );
				wp_send_json_error( $response );
			}
		}

		if ( isset( $_FILES['album_images'] ) && count( $_FILES['album_images'] ) ) {

			$gallery_images = $_FILES['album_images'];
			$count_images   = count( $_FILES['album_images']['name'] );

			if ( ! $disable_cover ) {
				$cover = absint( $_POST['album_cover'] );
			}
			for ( $i = 0; $i < $count_images; $i++ ) {

				if ( ! isset( $_FILES['album_images']['tmp_name'][ $i ] ) || '' === trim( $_FILES['album_images']['tmp_name'][ $i ] ) ) {
					continue;
				}

				$uploadedfile = array(
					'name'     => $_FILES['album_images']['name'][ $i ],
					'type'     => $_FILES['album_images']['type'][ $i ],
					'tmp_name' => $_FILES['album_images']['tmp_name'][ $i ],
					'error'    => $_FILES['album_images']['error'][ $i ],
					'size'     => $_FILES['album_images']['size'][ $i ],
				);

				$upload_overrides = array( 'test_form' => false );
				$movefile         = wp_handle_upload( $uploadedfile, $upload_overrides );

				if ( $movefile && ! isset( $movefile['error'] ) ) {

					$wp_filetype   = $movefile['type'];
					$filename      = $movefile['file'];
					$wp_upload_dir = wp_upload_dir();
					$attachment    = array(
						'guid'           => $wp_upload_dir['url'] . '/' . basename( $filename ),
						'post_mime_type' => $wp_filetype,
						'post_title'     => preg_replace( '/\.[^.]+$/', '', basename( $filename ) ),
						'post_content'   => '',
						'post_parent'    => $post_id,
						'post_author'    => get_current_user_id(),
						'post_status'    => 'inherit',
					);

					$attach_id   = wp_insert_attachment( $attachment, $filename );
					$attach_data = wp_generate_attachment_metadata( $attach_id, $filename );
					wp_update_attachment_metadata( $attach_id, $attach_data );
					update_post_meta( $attach_id, '_part_of_gallery', 'yes' );

					$photos[] = $attach_id;

					if ( isset( $cover ) && $cover === $i ) {
						update_post_meta( $post_id, '_thumbnail_id', $attach_id );
					}
				} else {
					wp_send_json_error( $movefile['error'] );
				}
			}
		}

		$privacy = sanitize_key( $_POST['user_photos_privacy'] );
		update_post_meta( $post_id, '_privacy', $privacy );

		update_post_meta( $post_id, '_photos', $photos );

		/*
		@param $post_id (int)
		add_action('um_user_photos_after_album_created',function($post_id){
			// custom code
		});
		*/
		do_action( 'um_user_photos_after_album_created', $post_id );

		wp_send_json_success();
	}


	/**
	 * Update album
	 */
	public function update_um_user_photos_album() {
		$disable_title   = UM()->options()->get( 'um_user_photos_disable_title' );
		$disable_cover   = UM()->options()->get( 'um_user_photos_disable_cover' );

		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_edit_album' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		/*validation*/
		$error = false;
		if ( ! isset( $_POST['album_id'] ) || ! is_numeric( sanitize_key( $_POST['album_id'] ) ) ) {
			wp_send_json_error( __( 'Invalid album.', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_edit_album( absint( $_POST['album_id'] ) ) ) {
			wp_send_json_error( __( 'You are not authorized for this.', 'um-user-photos' ) );
		}

		if ( ! $disable_title ) {
			if ( ! isset( $_POST['album_title'] ) || '' === sanitize_text_field( $_POST['album_title'] ) ) {
				$error    = true;
				$response = __( 'Album title is required', 'um-user-photos' );
			}
		}

		$album   = get_post( absint( $_POST['album_id'] ) );
		$user_id = get_current_user_id();
		if ( $album && is_user_logged_in() ) {
			if ( absint( $album->post_author ) !== absint( $user_id ) ) {
				$error    = true;
				$response = __( 'Invalid request', 'um-user-photos' );
			}
		} else {
			$error    = true;
			$response = __( 'Invalid request', 'um-user-photos' );
		}

		if ( $error ) {
			wp_send_json_error( $response );
		}

		um_maybe_unset_time_limit();

		add_filter( 'wp_handle_upload_prefilter', array( $this, 'validate_upload' ) );

		/*end validation*/
		if ( isset( $_POST['album_title'] ) ) {
			$alb_title = sanitize_text_field( $_POST['album_title'] );
		} else {
			$alb_title = '';
		}
		$post_id = wp_update_post(
			array(
				'ID'         => absint( $_POST['album_id'] ),
				'post_title' => $alb_title,
			)
		);

		$photos = array();
		if ( isset( $_POST['photos'] ) && is_array( $_POST['photos'] ) && ! empty( $_POST['photos'] ) ) {
			$photos = $_POST['photos'];
		}

		$limit_user_photos = UM()->User_Photos()->common()->user()->photos_limit( $user_id );
		if ( false !== $limit_user_photos && isset( $_FILES['album_images'] ) ) {
			$count_files       = count( $_FILES['album_images']['name'] );
			$count_user_photos = UM()->User_Photos()->common()->user()->photos_count( $user_id );
			if ( (int) $count_user_photos + (int) $count_files > (int) $limit_user_photos ) {
				// translators: %s is a number for uploading photos limit
				$response = sprintf( esc_html__( 'You cannot upload more photos, you have reached the limit of uploads. Delete old photos or upload few photos. Photo limit = %d', 'um-user-photos' ), $limit_user_photos );
				wp_send_json_error( $response );
			}
		}

		// upload more photos and add to $photos array
		if ( isset( $_FILES['album_images'] ) && count( $_FILES['album_images'] ) ) {

			$gallery_images = $_FILES['album_images'];
			$count_images   = count( $_FILES['album_images']['name'] );

			if ( ! $disable_cover && isset( $_POST['album_cover'] ) && '' !== $_POST['album_cover'] ) {
				$cover = absint( $_POST['album_cover'] );
			}

			for ( $i = 0; $i < $count_images; $i++ ) {

				if ( ! isset( $_FILES['album_images']['tmp_name'][ $i ] ) || '' === trim( $_FILES['album_images']['tmp_name'][ $i ] ) ) {
					continue;
				}

				$uploadedfile = array(
					'name'     => $_FILES['album_images']['name'][ $i ],
					'type'     => $_FILES['album_images']['type'][ $i ],
					'tmp_name' => $_FILES['album_images']['tmp_name'][ $i ],
					'error'    => $_FILES['album_images']['error'][ $i ],
					'size'     => $_FILES['album_images']['size'][ $i ],
				);

				$upload_overrides = array( 'test_form' => false );
				$movefile         = wp_handle_upload( $uploadedfile, $upload_overrides );

				if ( $movefile && ! isset( $movefile['error'] ) ) {

					$wp_filetype   = $movefile['type'];
					$filename      = $movefile['file'];
					$wp_upload_dir = wp_upload_dir();
					$attachment    = array(
						'guid'           => $wp_upload_dir['url'] . '/' . basename( $filename ),
						'post_mime_type' => $wp_filetype,
						'post_title'     => preg_replace( '/\.[^.]+$/', '', basename( $filename ) ),
						'post_content'   => '',
						'post_parent'    => $post_id,
						'post_author'    => get_current_user_id(),
						'post_status'    => 'inherit',
					);

					$attach_id   = wp_insert_attachment( $attachment, $filename );
					$attach_data = wp_generate_attachment_metadata( $attach_id, $filename );
					wp_update_attachment_metadata( $attach_id, $attach_data );
					update_post_meta( $attach_id, '_part_of_gallery', 'yes' );

					$photos[] = $attach_id;

					if ( isset( $cover ) && $cover === $i ) {
						update_post_meta( $post_id, '_thumbnail_id', $attach_id );
					}
				} else {
					wp_send_json_error( $movefile['error'] );
				}
			}
		}

		if ( ! $disable_cover && isset( $_POST['album_cover_id'] ) && '' !== $_POST['album_cover_id'] ) {
			update_post_meta( $post_id, '_thumbnail_id', absint( $_POST['album_cover_id'] ) );
		}

		$privacy = sanitize_key( $_POST['user_photos_privacy'] );
		update_post_meta( $_POST['album_id'], '_privacy', $privacy );

		if ( is_array( $photos ) && ! empty( $photos ) ) {
			update_post_meta( absint( $_POST['album_id'] ), '_photos', $photos );
		} else {
			delete_post_meta( absint( $_POST['album_id'] ), '_photos' );
		}

		/*
		@param $post_id (int)
		add_action('um_user_photos_after_album_updated',function($post_id){
			// custom code
		});
		*/
		do_action( 'um_user_photos_after_album_updated', absint( $_POST['album_id'] ) );

		wp_send_json_success();
	}

	/**
	 * Delete album.
	 */
	public function ajax_delete_album() {
		if ( empty( $_POST['id'] ) ) {
			wp_send_json_error( __( 'Wrong album ID.', 'um-user-photos' ) );
		}

		$id = absint( $_POST['id'] );
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_delete_album' . $id ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_delete_album( $id ) ) {
			wp_send_json_error( __( 'You cannot delete this album.', 'um-user-photos' ) );
		}

		$album      = get_post( $id );
		$photos     = get_post_meta( $id, '_photos', true );
		$wall_photo = get_post_meta( $id, '_thumbnail_id', true );

		/*
		@param $post_id (int)
		add_action('um_user_photos_before_album_deleted',function($post_id){
			// custom code
		});
		*/
		do_action( 'um_user_photos_before_album_deleted', $id );

		$result = wp_delete_post( $id, true );
		if ( empty( $result ) ) {
			wp_send_json_error( __( 'Unknown error. Cannot delete this album.', 'um-user-photos' ) );
		}

		if ( is_array( $photos ) && ! empty( $photos ) ) {
			foreach ( $photos as $photo_id ) {
				wp_delete_attachment( $photo_id, true );
			}
		}
		if ( $wall_photo ) {
			wp_delete_attachment( $wall_photo, true );
		}

		/*
		@param $post_id (int)
		add_action('um_user_photos_after_album_deleted',function($post_id){
			// custom code
		});
		*/
		do_action( 'um_user_photos_after_album_deleted', $id, $album );

		wp_send_json_success();
	}

	/**
	 * Update Image.
	 */
	public function update_image() {
		if ( empty( $_POST['id'] ) ) {
			wp_send_json_error( __( 'Invalid image ID', 'um-user-photos' ) );
		}
		$id = absint( $_POST['id'] );

		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_edit_image' . $id ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_edit_photo( $id ) ) {
			wp_send_json_error( __( 'You cannot edit this photo.', 'um-user-photos' ) );
		}

		if ( ! isset( $_POST['title'] ) || '' === sanitize_text_field( $_POST['title'] ) ) {
			wp_send_json_error( __( 'Title is required', 'um-user-photos' ) );
		}

		wp_update_post(
			array(
				'ID'           => $id,
				'post_title'   => sanitize_text_field( $_POST['title'] ),
				'post_excerpt' => sanitize_text_field( $_POST['caption'] ),
			)
		);

		if ( ! empty( $_POST['link'] ) ) {
			$link = esc_url_raw( $_POST['link'] );
			update_post_meta( $id, '_link', $link );
		} else {
			delete_post_meta( $id, '_link' );
		}

		if ( ! UM()->options()->get( 'um_user_photos_disable_comments' ) ) {
			if ( ! empty( $_POST['disable_comments'] ) ) {
				update_post_meta( $id, '_disable_comment', true );
			} else {
				update_post_meta( $id, '_disable_comment', false );
			}
		} else {
			delete_post_meta( $id, '_disable_comment' );
		}

		$album_id = absint( $_POST['album'] );
		if ( ! UM()->options()->get( 'um_user_photos_disable_cover' ) ) {
			$cover_id = get_post_meta( $album_id, '_thumbnail_id', true );
			if ( empty( $_POST['cover_photo'] ) ) {
				if ( absint( $cover_id ) === absint( $id ) ) {
					$photos = get_post_meta( $album_id, '_photos', true );
					update_post_meta( $album_id, '_thumbnail_id', absint( $photos[0] ) );
				}
			} else {
				update_post_meta( $album_id, '_thumbnail_id', absint( $_POST['cover_photo'] ) );
			}
		} else {
			delete_post_meta( $album_id, '_thumbnail_id' );
		}

		do_action( 'um_user_photos_after_photo_updated', $id );

		wp_send_json_success( __( 'Updated successfully', 'um-user-photos' ) );
	}

	/**
	 * Delete image
	 */
	public function um_delete_album_photo() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_delete_photo' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$image_id = absint( $_POST['image_id'] );

		if ( ! UM()->User_Photos()->common()->user()->can_delete_photo( $image_id ) ) {
			wp_send_json_error( __( 'You cannot delete this photo.', 'um-user-photos' ) );
		}

		$album_id = get_post_field( 'post_parent', $image_id );

		/*
		@param $attachment_id (int)
		@param $album_id (int)
		add_action('um_user_photos_before_photo_delete',function($attachment_id,$album_id){
			// custom code
		});
		*/
		do_action( 'um_user_photos_before_photo_delete', $image_id, $album_id );

		$album = get_post( $album_id );
		$image = get_post( $image_id );

		$user_id = 0;

		if ( is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		if ( ! $user_id || ! $image || ! $album ) {
			wp_send_json_error( __( 'Invalid request', 'um-user-photos' ) );
		} elseif ( absint( $image->post_author ) !== absint( $user_id ) || absint( $album->post_author ) !== absint( $user_id ) ) {
			wp_send_json_error( __( 'Invalid request', 'um-user-photos' ) );
		} else {

			/*
				@param $attachment_id (int)
				@param $album_id (int)
				add_action('um_user_photos_before_photo_deleted',function($image_id,$album_id){
					// custom code
				});
			*/
			do_action( 'um_user_photos_before_photo_deleted', $image_id, $album_id );

			wp_delete_attachment( $image_id, true );

			// Update `_photos` meta when album photo deleted.
			$photos = get_post_meta( $album_id, '_photos', true );
			if ( empty( $photos ) || ! is_array( $photos ) ) {
				$photos = array();
			}
			$index = array_search( $image_id, $photos, true );
			if ( false !== $index ) {
				unset( $photos[ $index ] );
				$photos = array_values( $photos );
				update_post_meta( $album_id, '_photos', $photos );
			}

			/*
				@param $attachment_id (int)
				@param $album_id (int)
				add_action('um_user_photos_after_photo_deleted',function($image_id,$album_id){
					// custom code
				});
			*/
			do_action( 'um_user_photos_after_photo_deleted', $image_id, $album_id );

			wp_send_json_success();
		}
	}

	/**
	 * Delete my all albums & photos
	 *
	 */
	public function flush_albums() {
		if ( ! wp_verify_nonce( $_REQUEST['_wpnonce'], 'um_user_photos_delete_all' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$user_id = get_current_user_id();

		/* Remove photos */
		$photos = new WP_Query(
			array(
				'post_type'      => 'attachment',
				'author__in'     => array( $user_id ),
				'post_status'    => 'inherit',
				'post_mime_type' => 'image',
				'posts_per_page' => -1,
				'meta_query'     => array(
					array(
						'key'     => '_part_of_gallery',
						'value'   => 'yes',
						'compare' => '=',
					),
				),
			)
		);

		if ( $photos->have_posts() ) {
			while ( $photos->have_posts() ) {
				$photos->the_post();
				wp_delete_attachment( get_the_ID(), true );
			}
		}
		wp_reset_postdata();

		/* Remove albums */
		$albums = new WP_Query(
			array(
				'post_type'      => 'um_user_photos',
				'author__in'     => array( $user_id ),
				'posts_per_page' => -1,
			)
		);
		if ( $albums->have_posts() ) {
			while ( $albums->have_posts() ) {
				$albums->the_post();
				wp_delete_post( get_the_ID(), true );
			}
		} // has albums
		wp_reset_postdata();

		do_action( 'um_user_photos_after_user_albums_deleted', $user_id );
		wp_send_json_success();
	}

	/**
	 * Download all photos
	 */
	public function download_my_photos() {
		if ( ! wp_verify_nonce( $_REQUEST['_wpnonce'], 'um_user_photos_download_all' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$profile = absint( $_REQUEST['profile_id'] );
		$user_id = get_current_user_id();
		$notice  = '';

		if ( ! is_user_logged_in() ) {
			$notice = __( 'Invalid request', 'um-user-photos' );
		}

		if ( ! class_exists( '\ZipArchive' ) ) {
			$notice = __( 'Your download could not be created. It looks like you do not have ZipArchive installed on your server.', 'um-user-photos' );
		}

		if ( empty( $notice ) && absint( $profile ) === absint( $user_id ) ) {

			$photos = new WP_Query(
				array(
					'post_type'      => 'attachment',
					'author__in'     => array( $user_id ),
					'post_status'    => 'inherit',
					'post_mime_type' => 'image',
					'posts_per_page' => -1,
					'meta_query'     => array(
						array(
							'key'     => '_part_of_gallery',
							'value'   => 'yes',
							'compare' => '=',
						),
					),
				)
			);

			if ( $photos->have_posts() ) {

				$zip         = new \ZipArchive();
				$zip_name    = time() . '.zip';
				$uploads_dir = WP_CONTENT_DIR . '/uploads/user-photos';
				if ( ! is_dir( $uploads_dir ) ) {
					mkdir( $uploads_dir );
				}
				$new_zip    = $uploads_dir . '/' . $zip_name;
				$zip_opened = $zip->open( $new_zip, \ZipArchive::CREATE );

				while ( $photos->have_posts() ) {
					$photos->the_post();
					$file_path  = get_attached_file( get_the_ID(), true );
					$file_type  = $filetype = wp_check_filetype( $file_path );
					$ext        = $file_type['ext'];
					$image_name = get_the_title() . '.' . $ext;
					if ( file_exists( $file_path ) ) {
						$zip->addFile( $file_path, $image_name );
					}
				}

				$zip->close();
				header( 'Content-type:application/zip' );
				header( 'Content-Disposition: attachment; filename=' . $zip_name );
				readfile( $new_zip );
				unlink( $new_zip );
				die;
			}

			$notice = __( 'Nothing to download', 'um-user-photos' );
			wp_reset_postdata();
		}

		if ( $notice ) {
			update_user_meta( $user_id, 'um_download_my_photos_notice', $notice );
			if ( ! empty( $_SERVER['HTTP_REFERER'] ) ) {
				$location = $_SERVER['HTTP_REFERER'];
			} else {
				$location = um_get_core_page( 'account' ) . 'um_user_photos';
			}
			wp_safe_redirect( esc_url_raw( $location ) );
		}
	}

	/**
	 * @param $file
	 *
	 * @return mixed
	 */
	public function validate_upload( $file ) {

		$error = $this->validate_image_data( $file['tmp_name'] );

		if ( $error ) {
			$file['error'] = $error;
		}

		return $file;
	}


	/**
	 * Check image upload and handle errors
	 *
	 * @param $file
	 *
	 * @return null|string
	 */
	public function validate_image_data( $file ) {
		$error = null;

		if ( ! function_exists( 'wp_get_image_editor' ) ) {
			require_once( ABSPATH . 'wp-admin/includes/media.php' );
		}

		$image = wp_get_image_editor( $file );
		if ( is_wp_error( $image ) ) {
			return __( 'Your image is invalid!', 'um-user-photos' );
		}

		$image_sizes = $image->get_size();
		$image_info['width'] = $image_sizes['width'];
		$image_info['height'] = $image_sizes['height'];
		$image_info['ratio'] = $image_sizes['width'] / $image_sizes['height'];

		$image_info['quality'] = $image->get_quality();

		$image_type = wp_check_filetype( $file );
		$image_info['extension'] = $image_type['ext'];
		$image_info['mime']= $image_type['type'];
		$image_info['size'] = filesize( $file );

		if ( isset( $image_info['invalid_image'] ) && $image_info['invalid_image'] == true ) {
			$error = __( 'Your image is invalid or too large!', 'um-user-photos' );
		}

		return $error;
	}


	/**
	 *
	 */
	public function um_user_photos_get_comment_section() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_get_comment_section' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		if ( UM()->options()->get( 'um_user_photos_disable_comments' ) ) {
			wp_send_json_success( '' );
		}

		$post_id = absint( $_POST['image_id'] ); // phpcs:ignore WordPress.Security.NonceVerification

		if ( ! UM()->User_Photos()->common()->user()->can_view_photo( $post_id ) ) {
			wp_send_json_error( __( 'You are not authorized for this.', 'um-user-photos' ) );
		}

		$image      = get_post( $post_id );
		$image_link = get_post_meta( $post_id, '_link', true );

		$likes = UM()->User_Photos()->common()->photo()->get_likes( $post_id );

		$disable_comments = get_post_meta( $post_id, '_disable_comment', true );

		$comments = array();
		if ( empty( $disable_comments ) ) {
			$comments = UM()->User_Photos()->common()->photo()->get_comments( $post_id, array( 'per_page' => false ) );

			if ( empty( $comments ) ) {
				$comments = array();
			}
		}

		um_fetch_user( $image->post_author );

		$content = UM()->get_template(
			'caption.php',
			UM_USER_PHOTOS_PLUGIN,
			array(
				'image'            => $image,
				'image_id'         => $post_id,
				'image_link'       => $image_link,
				'likes'            => $likes,
				'likes_count'      => count( $likes ),
				'disable_comments' => (bool) $disable_comments,
				'comments'         => $comments,
				'comment_count'    => count( $comments ),
			)
		);

		um_reset_user();

		wp_send_json_success( $content );
	}

	/**
	 * like photo
	 */
	public function um_user_photos_like_photo() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_like_photo' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$post_id = absint( $_POST['postid'] ); // phpcs:ignore WordPress.Security.NonceVerification

		if ( ! UM()->User_Photos()->common()->user()->can_like_photo( $post_id ) ) {
			wp_send_json_error( __( 'You are not authorized to like this photo.', 'um-user-photos' ) );
		}

		$liked = UM()->User_Photos()->common()->photo()->like( $post_id );
		if ( false === $liked ) {
			wp_send_json_error( __( 'Something went wrong. Cannot like.', 'um-user-photos' ) );
		}

		wp_send_json_success( count( $liked ) );
	} // um_user_photos_like_photo

	/**
	 * unlike photo
	 */
	public function um_user_photos_unlike_photo() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_unlike_photo' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$post_id = absint( $_POST['postid'] ); // phpcs:ignore WordPress.Security.NonceVerification
		if ( ! UM()->User_Photos()->common()->user()->can_unlike_photo( $post_id ) ) {
			wp_send_json_error( __( 'You are not authorized to unlike this photo.', 'um-user-photos' ) );
		}

		$liked = UM()->User_Photos()->common()->photo()->unlike( $post_id );
		if ( false === $liked ) {
			wp_send_json_error( __( 'Something went wrong. Cannot unlike.', 'um-user-photos' ) );
		}

		wp_send_json_success( count( $liked ) );
	} // um_user_photos_like_photo

	/**
	 * post comment
	 */
	public function um_user_photos_post_comment() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_comment_post' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		// phpcs:disable WordPress.Security.NonceVerification
		$post_id = absint( $_POST['image_id'] );
		$user_id = get_current_user_id();

		if ( ! UM()->User_Photos()->common()->user()->can_comment( $post_id ) ) {
			wp_send_json_error( __( 'You are not authorized to comment this photo.', 'um-user-photos' ) );
		}

		if ( ! isset( $_POST['comment'] ) || '' === sanitize_text_field( $_POST['comment'] ) ) {
			$message = esc_html__( 'Enter a comment first', 'um-user-photos' );
			wp_send_json_error( $message );
		}

		um_fetch_user( $user_id );

		$orig_content    = sanitize_text_field( $_POST['comment'] );
		$comment_content = wp_kses(
			$orig_content,
			array(
				'br' => array(),
			)
		);
		// phpcs:enable WordPress.Security.NonceVerification

		$time = current_time( 'mysql' );
		$data = array(
			'comment_post_ID'      => $post_id,
			'comment_author'       => um_user( 'display_name' ),
			'comment_author_email' => um_user( 'user_email' ),
			'comment_author_url'   => um_user_profile_url(),
			'comment_content'      => trim( $comment_content ),
			'user_id'              => $user_id,
			'comment_approved'     => 1,
			'comment_author_IP'    => um_user_ip(),
			'comment_type'         => 'um-user-photos',
			'comment_date'         => $time,
		);

		$commentid = wp_insert_comment( $data );

		wp_update_comment_count_now( $post_id );

		$is_url  = filter_var( $comment_content, FILTER_VALIDATE_URL );
		$content = $is_url ? '<a href="' . esc_url( $comment_content ) . '" target="_blank">' . esc_html( $comment_content ) . '</a>' : esc_html( $comment_content );

		ob_start();

		UM()->get_template(
			'comment.php',
			UM_USER_PHOTOS_PLUGIN,
			array(
				'user_id'  => um_user( 'ID' ),
				'content'  => $content,
				'date'     => $time,
				'id'       => $commentid,
				'image_id' => $post_id,
			),
			true
		);

		$content = ob_get_clean();

		$comment_count = UM()->User_Photos()->common()->photo()->get_comment_count( $post_id );

		wp_send_json_success(
			array(
				'content' => $content,
				'count'   => $comment_count,
			)
		);

	} // um_user_photos_post_comment

	/**
	 * like comment
	 */
	public function um_user_photos_like_comment() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_comment_like' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$comment_id = absint( $_POST['commentid'] ); // phpcs:ignore WordPress.Security.NonceVerification

		if ( empty( $comment_id ) ) {
			wp_send_json_error( __( 'Invalid comment ID', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_like_comment( $comment_id ) ) {
			wp_send_json_error( __( 'You are not authorized to like this comment', 'um-user-photos' ) );
		}

		$likes = UM()->User_Photos()->common()->photo()->like_comment( $comment_id );
		if ( false === $likes ) {
			wp_send_json_error( __( 'Cannot like this comment.', 'um-user-photos' ) );
		}

		wp_send_json_success(
			array(
				'count'   => count( $likes ),
				'user_id' => get_current_user_id(),
			)
		);
	}


	/**
	 * unlike comment
	 */
	public function um_user_photos_unlike_comment() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_comment_unlike' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$comment_id = absint( $_POST['commentid'] ); // phpcs:ignore WordPress.Security.NonceVerification

		if ( empty( $comment_id ) ) {
			wp_send_json_error( __( 'Invalid comment ID', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_unlike_comment( $comment_id ) ) {
			wp_send_json_error( __( 'You are not authorized to like this comment', 'um-user-photos' ) );
		}

		$likes = UM()->User_Photos()->common()->photo()->unlike_comment( $comment_id );
		if ( false === $likes ) {
			wp_send_json_error( __( 'Cannot unlike this comment.', 'um-user-photos' ) );
		}

		wp_send_json_success(
			array(
				'count'   => count( $likes ),
				'user_id' => get_current_user_id(),
			)
		);
	}


	/**
	 * Show photo likes.
	 */
	public function get_um_user_photo_likes() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_show_likes' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$post_id = absint( $_POST['image_id'] ); // phpcs:ignore WordPress.Security.NonceVerification

		if ( ! UM()->User_Photos()->common()->user()->can_view_photo_likes( $post_id ) ) {
			wp_send_json_error( __( 'You are not authorized for this.', 'um-user-photos' ) );
		}

		$likes = UM()->User_Photos()->common()->photo()->get_likes( $post_id );
		if ( false === $likes ) {
			wp_send_json_error( __( 'Cannot get likes. Maybe photo doesn\'t exist.', 'um-user-photos' ) );
		}

		$content = UM()->get_template(
			'modal/likes.php',
			UM_USER_PHOTOS_PLUGIN,
			array(
				'likes'   => $likes,
				'context' => 'photo',
			)
		);

		wp_send_json_success( array( 'content' => $content ) );
	}


	/**
	 * Show photo's comments likes.
	 */
	public function get_um_user_photos_comment_likes() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_get_comment_likes' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$comment_id = absint( $_POST['comment_id'] ); // phpcs:ignore WordPress.Security.NonceVerification

		if ( empty( $comment_id ) ) {
			wp_send_json_error( __( 'Invalid comment ID', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_view_comment_likes( $comment_id ) ) {
			wp_send_json_error( __( 'You are not authorized for this', 'um-user-photos' ) );
		}

		$likes = UM()->User_Photos()->common()->photo()->get_comment_likes( $comment_id );

		$content = UM()->get_template(
			'modal/likes.php',
			UM_USER_PHOTOS_PLUGIN,
			array(
				'likes'   => $likes,
				'context' => 'comment',
			)
		);

		wp_send_json_success( array( 'content' => $content ) );
	}

	/**
	 * show edit comment
	 */
	public function get_um_user_photos_comment_edit() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_comment_edit' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$comment_id = absint( $_POST['comment_id'] ); // phpcs:ignore WordPress.Security.NonceVerification

		if ( empty( $comment_id ) ) {
			wp_send_json_error( __( 'Invalid comment ID', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_edit_comment( $comment_id ) ) {
			wp_send_json_error( __( 'You are not authorized for this', 'um-user-photos' ) );
		}

		$comment = get_comment( $comment_id );

		ob_start();

		UM()->get_template(
			'modal/edit-comment.php',
			UM_USER_PHOTOS_PLUGIN,
			array(
				'comment' => $comment,
			),
			true
		);

		$content = ob_get_clean();

		wp_send_json_success( array( 'content' => $content ) );
	}

	// delete comment modal
	public function get_um_user_photos_comment_delete() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'get_um_user_photos_comment_delete' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$comment_id = absint( $_POST['comment_id'] ); // phpcs:ignore WordPress.Security.NonceVerification

		if ( empty( $comment_id ) ) {
			wp_send_json_error( __( 'Invalid comment ID', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_delete_comment( $comment_id ) ) {
			wp_send_json_error( __( 'You are not authorized for this', 'um-user-photos' ) );
		}

		$comment = get_comment( $comment_id );

		ob_start();

		UM()->get_template(
			'modal/delete-comment.php',
			UM_USER_PHOTOS_PLUGIN,
			array(
				'comment' => $comment,
				'message' => sanitize_text_field( $_POST['msg'] ), // phpcs:ignore WordPress.Security.NonceVerification
			),
			true
		);

		$content = ob_get_clean();

		wp_send_json_success( array( 'content' => $content ) );
	}

	// delete comment
	public function um_user_photos_comment_delete() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_comment_delete' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$comment_id = absint( $_POST['comment_id'] ); // phpcs:ignore WordPress.Security.NonceVerification
		if ( empty( $comment_id ) ) {
			wp_send_json_error( __( 'Invalid comment ID', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_delete_comment( $comment_id ) ) {
			wp_send_json_error( __( 'You are not authorized for this', 'um-user-photos' ) );
		}

		$comment = get_comment( $comment_id );
		$post_id = absint( $comment->comment_post_ID );

		$deleted = wp_delete_comment( $comment_id, true );

		$comment_count = UM()->User_Photos()->common()->photo()->get_comment_count( $post_id );

		if ( $deleted ) {
			wp_send_json_success( array( 'count' => $comment_count ) );
		} else {
			wp_send_json_error( __( 'Could not be deleted.', 'um-user-photos' ) );
		}
	}



	public function um_user_photos_comment_update() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_comment_update' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$comment_id = absint( $_POST['comment_id'] ); // phpcs:ignore WordPress.Security.NonceVerification

		if ( empty( $comment_id ) ) {
			wp_send_json_error( __( 'Invalid comment ID', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_edit_comment( $comment_id ) ) {
			wp_send_json_error( __( 'You are not authorized for this', 'um-user-photos' ) );
		}

		$comment_content = sanitize_textarea_field( $_POST['comment_content'] ); // phpcs:ignore WordPress.Security.NonceVerification

		$updated = wp_update_comment(
			array(
				'comment_ID'      => $comment_id,
				'comment_content' => $comment_content,
			)
		);

		if ( $updated ) {

			wp_send_json_success(
				array(
					'message'    => '<p style="background: green;color: #fff;text-align: center; line-height: 40px;border-radius: 5px;">' . __( 'Comment updated', 'um-user-photos' ) . '</p>',
					'comment'    => $comment_content,
					'comment_id' => $comment_id,
				)
			);

		} else {
			wp_send_json_error( __( 'Could not update', 'um-user-photos' ) );
		}
	}
}
