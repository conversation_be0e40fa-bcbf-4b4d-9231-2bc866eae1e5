<?php
// Temporarily disable all WordPress plugins
echo "<h1>WordPress Plugin Deactivation Tool</h1>";

// Load WordPress
define('WP_USE_THEMES', false);
require_once('./wp-load.php');

if (isset($_GET['action']) && $_GET['action'] === 'disable') {
    // Backup current active plugins
    $active_plugins = get_option('active_plugins');
    update_option('active_plugins_backup', $active_plugins);
    
    // Disable all plugins
    update_option('active_plugins', array());
    
    echo "<div style='background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
    echo "✓ All plugins have been disabled!<br>";
    echo "✓ Plugin list backed up to 'active_plugins_backup'<br>";
    echo "</div>";
    
    echo "<p><a href='http://raezg.local/' target='_blank' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none;'>Test WordPress Now</a></p>";
    echo "<p><a href='?action=restore'>Restore All Plugins</a></p>";
    
} elseif (isset($_GET['action']) && $_GET['action'] === 'restore') {
    // Restore plugins from backup
    $backup_plugins = get_option('active_plugins_backup');
    if ($backup_plugins) {
        update_option('active_plugins', $backup_plugins);
        delete_option('active_plugins_backup');
        
        echo "<div style='background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
        echo "✓ All plugins have been restored!<br>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
        echo "✗ No plugin backup found!<br>";
        echo "</div>";
    }
    
    echo "<p><a href='http://raezg.local/' target='_blank' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none;'>Test WordPress Now</a></p>";
    
} else {
    // Show current status
    $active_plugins = get_option('active_plugins');
    $backup_exists = get_option('active_plugins_backup');
    
    echo "<h2>Current Status</h2>";
    
    if ($active_plugins && count($active_plugins) > 0) {
        echo "<p><strong>Active Plugins:</strong> " . count($active_plugins) . "</p>";
        echo "<ul>";
        foreach ($active_plugins as $plugin) {
            echo "<li>$plugin</li>";
        }
        echo "</ul>";
        
        echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; margin: 10px 0;'>";
        echo "<strong>Recommendation:</strong> Disable all plugins to test if they're causing the timeout issue.";
        echo "</div>";
        
        echo "<p><a href='?action=disable' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none;'>Disable All Plugins</a></p>";
        
    } else {
        echo "<p><strong>No active plugins found.</strong></p>";
        
        if ($backup_exists) {
            echo "<p>Plugin backup exists. You can restore them if needed.</p>";
            echo "<p><a href='?action=restore' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none;'>Restore All Plugins</a></p>";
        }
    }
    
    echo "<h2>Active Theme</h2>";
    $theme = wp_get_theme();
    echo "<p><strong>Current Theme:</strong> " . $theme->get('Name') . " (Version: " . $theme->get('Version') . ")</p>";
    
    echo "<h2>Quick Actions</h2>";
    echo "<p><a href='http://raezg.local/' target='_blank' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none;'>Test WordPress Site</a></p>";
    echo "<p><a href='http://raezg.local/wp-admin/' target='_blank' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none;'>Test WordPress Admin</a></p>";
}

echo "<hr>";
echo "<p><small>This tool temporarily disables plugins to help diagnose WordPress issues. Always test on a backup first!</small></p>";
?>
