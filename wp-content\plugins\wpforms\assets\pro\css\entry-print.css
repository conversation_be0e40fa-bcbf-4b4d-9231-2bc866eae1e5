/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */
/* Document
   ========================================================================== */
/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */
html {
  line-height: 1.15;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
}

/* Sections
	 ========================================================================== */
/**
   * Remove the margin in all browsers.
   */
body {
  margin: 0;
}

/**
   * Render the `main` element consistently in IE.
   */
main {
  display: block;
}

/**
   * Correct the font size and margin on `h1` elements within `section` and
   * `article` contexts in Chrome, Firefox, and Safari.
   */
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

/* Grouping content
	 ========================================================================== */
/**
   * 1. Add the correct box sizing in Firefox.
   * 2. Show the overflow in Edge and IE.
   */
hr {
  box-sizing: content-box;
  /* 1 */
  height: 0;
  /* 1 */
  overflow: visible;
  /* 2 */
}

/**
   * 1. Correct the inheritance and scaling of font size in all browsers.
   * 2. Correct the odd `em` font sizing in all browsers.
   */
pre {
  font-family: monospace, monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

/* Text-level semantics
	 ========================================================================== */
/**
   * Remove the gray background on active links in IE 10.
   */
a {
  background-color: transparent;
}

/**
   * 1. Remove the bottom border in Chrome 57-
   * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
   */
abbr[title] {
  border-bottom: none;
  /* 1 */
  text-decoration: underline;
  /* 2 */
  text-decoration: underline dotted;
  /* 2 */
}

/**
   * Add the correct font weight in Chrome, Edge, and Safari.
   */
b,
strong {
  font-weight: bolder;
}

/**
   * 1. Correct the inheritance and scaling of font size in all browsers.
   * 2. Correct the odd `em` font sizing in all browsers.
   */
code,
kbd,
samp {
  font-family: monospace, monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

/**
   * Add the correct font size in all browsers.
   */
small {
  font-size: 80%;
}

/**
   * Prevent `sub` and `sup` elements from affecting the line height in
   * all browsers.
   */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Embedded content
	 ========================================================================== */
/**
   * Remove the border on images inside links in IE 10.
   */
img {
  border-style: none;
}

/* Forms
	 ========================================================================== */
/**
   * 1. Change the font styles in all browsers.
   * 2. Remove the margin in Firefox and Safari.
   */
button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  line-height: 1.15;
  /* 1 */
  margin: 0;
  /* 2 */
}

/**
   * Show the overflow in IE.
   * 1. Show the overflow in Edge.
   */
button,
input {
  /* 1 */
  overflow: visible;
}

/**
   * Remove the inheritance of text transform in Edge, Firefox, and IE.
   * 1. Remove the inheritance of text transform in Firefox.
   */
button,
select {
  /* 1 */
  text-transform: none;
}

/**
   * Correct the inability to style clickable types in iOS and Safari.
   */
button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}

/**
   * Remove the inner border and padding in Firefox.
   */
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/**
   * Restore the focus styles unset by the previous rule.
   */
button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
   * Correct the padding in Firefox.
   */
fieldset {
  padding: 0.35em 0.75em 0.625em;
}

/**
   * 1. Correct the text wrapping in Edge and IE.
   * 2. Correct the color inheritance from `fieldset` elements in IE.
   * 3. Remove the padding so developers are not caught out when they zero out
   *    `fieldset` elements in all browsers.
   */
legend {
  box-sizing: border-box;
  /* 1 */
  color: inherit;
  /* 2 */
  display: table;
  /* 1 */
  max-width: 100%;
  /* 1 */
  padding: 0;
  /* 3 */
  white-space: normal;
  /* 1 */
}

/**
   * Add the correct vertical alignment in Chrome, Firefox, and Opera.
   */
progress {
  vertical-align: baseline;
}

/**
   * Remove the default vertical scrollbar in IE 10+.
   */
textarea {
  overflow: auto;
}

/**
   * 1. Add the correct box sizing in IE 10.
   * 2. Remove the padding in IE 10.
   */
[type="checkbox"],
[type="radio"] {
  box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */
}

/**
   * Correct the cursor style of increment and decrement buttons in Chrome.
   */
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

/**
   * 1. Correct the odd appearance in Chrome and Safari.
   * 2. Correct the outline style in Safari.
   */
[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/**
   * Remove the inner padding in Chrome and Safari on macOS.
   */
[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
   * 1. Correct the inability to style clickable types in iOS and Safari.
   * 2. Change font properties to `inherit` in Safari.
   */
::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/* Interactive
	 ========================================================================== */
/*
   * Add the correct display in Edge, IE 10+, and Firefox.
   */
details {
  display: block;
}

/*
   * Add the correct display in all browsers.
   */
summary {
  display: list-item;
}

/* Misc
	 ========================================================================== */
/**
   * Add the correct display in IE 10+.
   */
template {
  display: none;
}

/**
   * Add the correct display in IE 10.
   */
[hidden] {
  display: none;
}

html,
input[type="search"] {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

body {
  background-color: #f0f0f1;
  color: #444;
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.4286;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 700;
  line-height: 1.2;
  margin: 0 0 16px;
  padding: 0;
}

h1 {
  font-size: 22px;
  font-weight: 700;
  line-height: 27px;
  font-style: normal;
  margin: 0;
}

h1 span {
  font-weight: 400;
}

a {
  color: #036aab;
}

a:hover, a:active, a:focus {
  color: #215d8f;
}

.close-window {
  position: fixed;
  top: 20px;
  right: 20px;
}

.close-window svg {
  fill: #bbb;
}

.close-window:hover svg {
  fill: #777;
}

.print-preview {
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  margin: 40px auto;
  overflow: auto;
  padding: 40px;
  max-width: 800px;
  min-height: 600px;
}

.print-preview .page-title {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin: 0 0 20px 0;
}

.print-preview iframe {
  border: 0;
}

.print-preview .buttons {
  display: flex;
  align-items: center;
}

.print-preview .buttons .button {
  font-weight: normal;
  text-align: center;
  margin-left: 10px;
  line-height: 28px;
  cursor: pointer;
}

.print-preview .buttons .button-print {
  color: #fff;
  text-decoration: none;
  border-radius: 3px;
  font-size: 13px;
  line-height: 16px;
  padding: 7px 12px;
  background: #036aab;
  border: 0;
}

.print-preview .buttons .button-print:hover, .print-preview .buttons .button-print:active {
  background: #215d8f;
  color: #fff;
}

.print-preview .buttons .button-settings {
  background: #f8f8f8;
  box-shadow: 0 0 0 1px #777;
  border: 1px solid transparent;
  border-radius: 3px;
  color: #777;
  width: 34px;
  height: 30px;
  font-size: 20px;
  display: block;
}

.print-preview .buttons .button-settings:hover, .print-preview .buttons .button-settings.active {
  background: #f8f8f8;
  box-shadow: 0 0 0 2px #036aab;
  color: #036aab;
}

.print-preview .settings {
  position: relative;
}

.print-preview .actions {
  text-align: left;
  margin: 10px 0 0;
  line-height: 17px;
  align-items: center;
  background: #fff;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  border-radius: 6px;
  padding: 0 0 7.5px;
  position: absolute;
  inset-inline-end: -10px;
  top: 100%;
  width: 250px;
  display: none;
  flex-direction: column;
  z-index: 3;
}

.print-preview .actions.active {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
}

.print-preview .actions .title {
  display: block;
  width: 100%;
  font-size: 11px;
  line-height: 13px;
  text-transform: uppercase;
  padding: 14px 15px;
  border-top: 1px solid #f1f1f1;
  border-bottom: 1px solid #f1f1f1;
  margin: 7.5px 0;
}

.print-preview .actions .title:first-child {
  border-top: 0;
  margin-top: 0;
}

.print-preview .actions .switch-container {
  display: flex;
  align-items: center;
}

.print-preview .actions .switch-container a {
  font-family: Helvetica Neue, sans-serif;
  font-style: normal;
  font-weight: normal;
  color: #444;
  text-decoration: none;
  padding: 7.5px 15px;
  display: flex;
  align-items: center;
  width: 100%;
}

.print-preview .actions .switch-container a:hover .switch {
  background: #777;
}

.print-preview .actions .switch-container a:hover .switch.active {
  background: #215d8f;
}

.print-preview .actions .switch-container a .switch {
  cursor: pointer;
  height: 18px;
  width: 28px;
  background: #bbb;
  display: block;
  border-radius: 10px;
  position: relative;
  margin-inline-end: 7px;
}

.print-preview .actions .switch-container a .switch:after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 14px;
  height: 14px;
  background: #fff;
  border-radius: 10px;
  transition: 0.3s;
}

.print-preview .actions .switch-container a .switch.active {
  background: #036aab;
}

.print-preview .actions .switch-container a .switch.active:after {
  left: calc(100% - 2px);
  transform: translateX(-100%);
}

.print-preview .print-body {
  border-left: 1px solid #ddd;
  border-right: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
}

.print-preview .print-body .print-item {
  border-top: 1px solid #ddd;
  padding: 15px;
}

.print-preview .print-body .print-item-title, .print-preview .print-body .print-item-value, .print-preview .print-body .print-item-description {
  word-break: break-word;
}

.print-preview .print-body .wpforms-hidden {
  display: none;
}

.print-preview .print-body .wpforms-field-layout,
.print-preview .print-body .wpforms-field-layout-rows,
.print-preview .print-body .wpforms-field-layout-column {
  border: 0;
  padding: 0;
}

.print-preview .print-body .wpforms-field-pagebreak {
  background-color: #ddd;
}

.print-preview .print-body .wpforms-field-pagebreak .print-item-title {
  font-size: 18px;
  line-height: 20px;
  font-weight: 500;
}

.print-preview .print-body .wpforms-field-divider {
  background-color: #f8f8f8;
}

.print-preview .print-body .wpforms-field-divider .print-item-title {
  font-size: 16px;
  line-height: 19px;
  font-weight: 400;
}

.print-preview .print-body .wpforms-field-pagebreak:last-child {
  display: none !important;
}

.print-preview .print-body .field.wpforms-field-content .field-value {
  word-break: break-word;
}

.print-preview .print-body .field.wpforms-field-content .field-value h1, .print-preview .print-body .field.wpforms-field-content .field-value h2, .print-preview .print-body .field.wpforms-field-content .field-value h3, .print-preview .print-body .field.wpforms-field-content .field-value h4, .print-preview .print-body .field.wpforms-field-content .field-value h5, .print-preview .print-body .field.wpforms-field-content .field-value h6 {
  margin: 20px 0;
  padding: 0;
  clear: unset;
}

.print-preview .print-body .field.wpforms-field-content .field-value h1:first-child, .print-preview .print-body .field.wpforms-field-content .field-value h2:first-child, .print-preview .print-body .field.wpforms-field-content .field-value h3:first-child, .print-preview .print-body .field.wpforms-field-content .field-value h4:first-child, .print-preview .print-body .field.wpforms-field-content .field-value h5:first-child, .print-preview .print-body .field.wpforms-field-content .field-value h6:first-child {
  margin-top: 0;
}

.print-preview .print-body .field.wpforms-field-content .field-value h1 {
  font-size: 32px;
  line-height: 40px;
}

.print-preview .print-body .field.wpforms-field-content .field-value h2 {
  font-size: 28px;
  line-height: 36px;
}

.print-preview .print-body .field.wpforms-field-content .field-value h3 {
  font-size: 24px;
  line-height: 32px;
}

.print-preview .print-body .field.wpforms-field-content .field-value h4 {
  font-size: 20px;
  line-height: 28px;
}

.print-preview .print-body .field.wpforms-field-content .field-value h5 {
  font-size: 18px;
  line-height: 26px;
}

.print-preview .print-body .field.wpforms-field-content .field-value h6 {
  font-size: 16px;
  line-height: 24px;
  text-transform: uppercase;
}

.print-preview .print-body .field.wpforms-field-content .field-value p, .print-preview .print-body .field.wpforms-field-content .field-value blockquote, .print-preview .print-body .field.wpforms-field-content .field-value pre, .print-preview .print-body .field.wpforms-field-content .field-value table {
  margin: 0 0 20px 0;
}

.print-preview .print-body .field.wpforms-field-content .field-value li {
  margin: 0 0 10px 0;
}

.print-preview .print-body .field.wpforms-field-content .field-value a {
  text-decoration: underline;
}

.print-preview .print-body .field.wpforms-field-content .field-value a:hover {
  text-decoration: none;
}

.print-preview .print-body .field.wpforms-field-content .field-value code, .print-preview .print-body .field.wpforms-field-content .field-value pre {
  font-family: monospace;
  overflow: auto;
}

.print-preview .print-body .field.wpforms-field-content .field-value del {
  text-decoration: line-through;
}

.print-preview .print-body .field.wpforms-field-content .field-value ins {
  text-decoration: underline;
}

.print-preview .print-body .field.wpforms-field-content .field-value small {
  font-size: smaller;
}

.print-preview .print-body .field.wpforms-field-content .field-value dt {
  margin: 5px 0;
}

.print-preview .print-body .field.wpforms-field-content .field-value dd {
  margin-left: 25px;
}

.print-preview .print-body .field.wpforms-field-content .field-value abbr, .print-preview .print-body .field.wpforms-field-content .field-value acronym {
  text-decoration: underline dotted;
}

.print-preview .print-body .field.wpforms-field-content .field-value ul {
  list-style: disc outside none !important;
  padding-inline-start: 29px !important;
  margin-bottom: 20px !important;
}

.print-preview .print-body .field.wpforms-field-content .field-value ul ul {
  list-style-type: circle !important;
  margin-top: 10px !important;
  margin-bottom: 0 !important;
}

.print-preview .print-body .field.wpforms-field-content .field-value ul ul ul {
  list-style-type: square !important;
}

.print-preview .print-body .field.wpforms-field-content .field-value ul ol {
  margin-top: 10px;
  margin-bottom: 0;
}

.print-preview .print-body .field.wpforms-field-content .field-value ul li {
  list-style: inherit !important;
  margin-bottom: 10px !important;
}

.print-preview .print-body .field.wpforms-field-content .field-value ol {
  list-style: decimal outside none;
  padding-inline-start: 29px;
  margin-bottom: 20px;
}

.print-preview .print-body .field.wpforms-field-content .field-value ol ol {
  margin-top: 10px;
  margin-bottom: 0;
}

.print-preview .print-body .field.wpforms-field-content .field-value ol ul {
  margin-top: 10px !important;
  margin-bottom: 0 !important;
}

.print-preview .print-body .field.wpforms-field-content .field-value ol li {
  list-style: inherit;
}

.print-preview .print-body .field.wpforms-field-content .field-value blockquote {
  border-left: 4px solid rgba(0, 0, 0, 0.15);
  padding-left: 20px;
}

.print-preview .print-body .field.wpforms-field-content .field-value blockquote:before, .print-preview .print-body .field.wpforms-field-content .field-value blockquote:after {
  display: none;
}

.print-preview .print-body .field.wpforms-field-content .field-value table {
  width: 100%;
  border-collapse: collapse;
  word-break: normal;
}

.print-preview .print-body .field.wpforms-field-content .field-value table th, .print-preview .print-body .field.wpforms-field-content .field-value table td {
  padding: 0.5em;
  border: 1px solid;
}

.print-preview .print-body .field.wpforms-field-content .field-value sup, .print-preview .print-body .field.wpforms-field-content .field-value sub {
  font-size: smaller;
  line-height: calc( 100% + 11px);
}

.print-preview .print-body .field.wpforms-field-content .field-value sup {
  vertical-align: super;
}

.print-preview .print-body .field.wpforms-field-content .field-value sub {
  vertical-align: sub;
}

.print-preview .print-body .field.wpforms-field-content .field-value img {
  max-width: 100%;
  height: auto;
}

.print-preview .print-body .field.wpforms-field-content .field-value .alignleft {
  float: left;
  margin: 0 30px 20px 0;
}

.print-preview .print-body .field.wpforms-field-content .field-value .alignright {
  float: right;
  margin: 0 0 20px 30px;
}

.print-preview .print-body .field.wpforms-field-content .field-value .aligncenter {
  display: block;
  clear: both;
  text-align: center;
  margin: 0 auto 20px;
}

.print-preview .print-body .field.wpforms-field-content .field-value .alignnone {
  display: block;
  clear: both;
  margin: 0 0 20px 0;
}

.print-preview .print-body .field.wpforms-field-content .field-value .wp-caption-dt,
.print-preview .print-body .field.wpforms-field-content .field-value .wp-caption-dd {
  margin: 0;
}

.print-preview .print-body .field.wpforms-field-content .field-value .wp-caption {
  position: relative;
  left: auto;
  right: auto;
  transform: none;
  max-width: 100%;
}

.print-preview .print-body .field.wpforms-field-content .field-value .wp-caption .wp-caption-text,
.print-preview .print-body .field.wpforms-field-content .field-value .wp-caption .wp-caption-dd {
  text-align: center;
  font-size: 14px;
  margin-top: 0.5em;
}

.print-preview .print-body .field.wpforms-field-content .field-value .wpforms-field-content-preview-end {
  clear: both;
}

.print-preview .print-item-title {
  font-weight: 600;
  margin: 0;
}

.print-preview .print-item-value {
  margin: 10px 0 0;
}

.print-preview .print-item-description {
  display: block;
  color: #999999;
  font-weight: 400;
}

.print-preview .note-item + .note-item {
  margin-top: 10px;
}

.print-preview .note-item p {
  margin: 0;
}

.print-preview .note-item p + p {
  margin-top: 10px;
}

.print-preview .field-value iframe {
  width: 100%;
}

.print-preview .field-value-choice {
  display: block;
  position: relative;
}

.print-preview .field-value-choice.field-value-choice-image .field-value-choice-image-wrapper {
  display: inline-block;
  width: auto;
  max-width: 200px;
  position: relative;
}

.print-preview .field-value-choice.field-value-choice-image.field-value-choice-checked:before {
  content: none;
}

.print-preview .field-value-choice.field-value-choice-image.field-value-choice-checked .field-value-choice-image-wrapper {
  min-width: 40px;
  min-height: 40px;
}

.print-preview .field-value-choice.field-value-choice-image.field-value-choice-checked .field-value-choice-image-wrapper:before, .print-preview .field-value-choice.field-value-choice-image.field-value-choice-checked .field-value-choice-image-wrapper:after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  display: block;
}

.print-preview .field-value-choice.field-value-choice-image.field-value-choice-checked .field-value-choice-image-wrapper:before {
  width: 40px;
  height: 40px;
  background: linear-gradient(180deg, #e8e8e8 0%, #fff 68.23%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  border: 0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);
}

.print-preview .field-value-choice.field-value-choice-image.field-value-choice-checked .field-value-choice-image-wrapper:after {
  width: 20px;
  height: 12px;
  transform: translate(-50%, -50%) rotate(-45deg);
  border-left: 4px solid #777;
  border-bottom: 4px solid #777;
  margin-top: -1px;
}

.print-preview .field-value-choice.field-value-choice-image img {
  display: block;
  max-width: 100%;
  height: auto;
}

.print-preview .field-value-choice + .field-value-choice {
  margin-top: 5px;
}

.print-preview .field-value-choice + .field-value-choice-image {
  margin-top: 10px;
}

.print-preview .field-value-choice input {
  appearance: none;
  width: 16px;
  height: 16px;
  border: none;
  box-shadow: 0 0 0 1px #ccc;
  background: linear-gradient(180deg, #e8e8e8 0%, #fff 68.23%);
  margin-right: 12px;
  position: relative;
  left: 1px;
  top: 3px;
}

.print-preview .field-value-choice input[type=radio] {
  border-radius: 50%;
}

.print-preview .field-value-choice input[type=checkbox] {
  border-radius: 2px;
}

.print-preview .field-value-choice-checked:before {
  content: '';
  position: absolute;
  z-index: 2;
}

.print-preview .field-value-choice-checked.field-value-choice-checkbox:before {
  left: 6px;
  top: 5px;
  width: 6px;
  height: 10px;
  border-right: 2px solid #777;
  border-bottom: 2px solid #777;
  transform: rotate(45deg);
}

.print-preview .field-value-choice-checked.field-value-choice-radio:before {
  left: 4px;
  top: 6px;
  width: 10px;
  height: 10px;
  border: 5px solid #777;
  border-radius: 50%;
}

.print-preview .file-icon {
  padding-right: 10px;
}

.print-preview .file-icon img {
  vertical-align: middle;
}

.print-preview .wpforms-field-repeater-block > .print-item-title,
.print-preview .wpforms-field-repeater-row > .print-item-title,
.print-preview .wpforms-field-layout-row > .print-item-title,
.print-preview .wpforms-field-layout-column > .print-item-title {
  background-color: #f8f8f8;
  padding: 15px;
  height: auto;
  font-size: 16px;
  line-height: 19px;
  font-weight: 400;
  border-bottom: 1px solid #ddd;
}

.print-preview .wpforms-field-repeater-block > .print-item-title:has(.print-item-description),
.print-preview .wpforms-field-repeater-row > .print-item-title:has(.print-item-description),
.print-preview .wpforms-field-layout-row > .print-item-title:has(.print-item-description),
.print-preview .wpforms-field-layout-column > .print-item-title:has(.print-item-description) {
  border-top: 1px solid #ddd;
}

.print-preview .wpforms-field-repeater-block > .print-item-title .print-item-title-wrapper,
.print-preview .wpforms-field-repeater-row > .print-item-title .print-item-title-wrapper,
.print-preview .wpforms-field-layout-row > .print-item-title .print-item-title-wrapper,
.print-preview .wpforms-field-layout-column > .print-item-title .print-item-title-wrapper {
  display: block;
}

.print-preview .wpforms-field-repeater-block > .print-item-title .print-item-description,
.print-preview .wpforms-field-repeater-row > .print-item-title .print-item-description,
.print-preview .wpforms-field-layout-row > .print-item-title .print-item-description,
.print-preview .wpforms-field-layout-column > .print-item-title .print-item-description {
  font-size: 14px;
}

.print-preview .wpforms-field-repeater-block .print-item,
.print-preview .wpforms-field-repeater-row .print-item,
.print-preview .wpforms-field-layout-row .print-item,
.print-preview .wpforms-field-layout-column .print-item {
  border: 0;
}

.print-preview .wpforms-field-repeater-block .print-item,
.print-preview .wpforms-field-repeater-row .print-item {
  padding: 0;
}

.print-preview .wpforms-field-repeater-row .wpforms-field-repeater-rows .wpforms-layout-row .wpforms-field-layout-column {
  padding-left: 15px;
  padding-right: 15px;
}

.print-preview .wpforms-field-repeater-block .wpforms-field-layout-column {
  padding: 15px;
}

.print-preview.wpforms-preview-mode-compact .print-body .wpforms-field-layout-column,
.print-preview.wpforms-preview-mode-compact .print-body .wpforms-field-repeater-row .wpforms-field-repeater-rows .wpforms-layout-row .wpforms-field-layout-column {
  padding: 0;
}

.print-preview.wpforms-preview-mode-compact .print-body .wpforms-field-repeater-block .wpforms-layout-row .print-item-title,
.print-preview.wpforms-preview-mode-compact .print-body .wpforms-field-repeater-block .wpforms-layout-row .print-item-value,
.print-preview.wpforms-preview-mode-compact .print-body .wpforms-field-repeater-row .wpforms-layout-row .print-item-title,
.print-preview.wpforms-preview-mode-compact .print-body .wpforms-field-repeater-row .wpforms-layout-row .print-item-value {
  padding-bottom: 0;
}

.print-preview.wpforms-preview-mode-compact .print-body .wpforms-field-repeater-block .wpforms-layout-row:last-child,
.print-preview.wpforms-preview-mode-compact .print-body .wpforms-field-repeater-row .wpforms-layout-row:last-child {
  padding-bottom: 10px;
}

.print-preview.wpforms-preview-mode-compact .print-body .print-item {
  display: flex;
  padding: 0;
}

.print-preview.wpforms-preview-mode-compact .print-body .print-item .wpforms-field-layout,
.print-preview.wpforms-preview-mode-compact .print-body .print-item .wpforms-field-layout-column {
  display: block;
}

.print-preview.wpforms-preview-mode-compact .print-body .print-item .print-item-title,
.print-preview.wpforms-preview-mode-compact .print-body .print-item .print-item-value {
  padding: 10px;
}

.print-preview.wpforms-preview-mode-compact .print-body .print-item .print-item-title {
  width: 200px;
  min-width: 200px;
}

.print-preview.wpforms-preview-mode-compact .print-body .print-item .wpforms-field-divider .print-item-title,
.print-preview.wpforms-preview-mode-compact .print-body .print-item .wpforms-field-pagebreak .print-item-title {
  width: 100%;
}

.print-preview.wpforms-preview-mode-compact .print-body .print-item .print-item-value {
  margin: 0;
  width: 100%;
}

.print-preview.wpforms-preview-mode-compact .wpforms-field-repeater-rows .print-item,
.print-preview.wpforms-preview-mode-compact .wpforms-field-repeater-blocks .print-item {
  display: block;
}

.print-preview:not(.wpforms-preview-mode-compact):not(.wpforms-preview-mode-maintain-layout) .wpforms-layout-row .wpforms-field-layout-column {
  padding-top: 15px;
  padding-bottom: 15px;
}

.print-preview.wpforms-preview-mode-maintain-layout .wpforms-field-repeater-row .wpforms-layout-row:not(:first-child) .print-item-title {
  display: none;
}

.print-preview.wpforms-preview-mode-maintain-layout .wpforms-layout-row {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
}

.print-preview.wpforms-preview-mode-maintain-layout .wpforms-layout-row .wpforms-field-layout-column {
  border: 0;
}

.print-preview.wpforms-preview-mode-maintain-layout .wpforms-layout-row .wpforms-field-layout-column + .wpforms-field-layout-column {
  border-left: 1px solid #ddd;
}

.print-preview.wpforms-preview-mode-maintain-layout .wpforms-layout-row .wpforms-field-layout-column:empty {
  border-top: 1px solid #ddd !important;
}

.print-preview.wpforms-preview-mode-maintain-layout .wpforms-layout-row:first-child .wpforms-field-layout-column {
  padding-top: 15px;
}

.print-preview.wpforms-preview-mode-maintain-layout .wpforms-layout-row:last-child .wpforms-field-layout-column {
  padding-bottom: 15px;
}

.print-preview.wpforms-preview-mode-maintain-layout .wpforms-field-layout {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
}

.print-preview.wpforms-preview-mode-maintain-layout .wpforms-field-layout .wpforms-field-layout-column {
  border: 0;
}

.print-preview.wpforms-preview-mode-maintain-layout .wpforms-field-layout .wpforms-field-layout-column + .wpforms-field-layout-column {
  border-left: 1px solid #ddd;
}

.print-preview.wpforms-preview-mode-maintain-layout .wpforms-field-layout-column {
  width: var(--field-layout-column-width, auto);
}

.print-preview.wpforms-preview-mode-empty .wpforms-layout-row .wpforms-field-layout-column .print-item.wpforms-field-empty {
  display: block !important;
}

.print-preview.wpforms-preview-mode-empty .wpforms-field-repeater-block-empty {
  display: block;
}

.print-preview:not(.wpforms-preview-mode-maintain-layout) .wpforms-field-layout-rows, .print-preview:not(.wpforms-preview-mode-maintain-layout) .wpforms-field-layout {
  display: block !important;
}

.print-preview:not(.wpforms-preview-mode-description) .field-description {
  display: none;
}

.print-preview:not(.wpforms-preview-mode-description) .wpforms-field-repeater-block > .print-item-title:not(:has(.print-item-title-wrapper)),
.print-preview:not(.wpforms-preview-mode-description) .wpforms-field-repeater-row > .print-item-title:not(:has(.print-item-title-wrapper)),
.print-preview:not(.wpforms-preview-mode-description) .wpforms-field-layout-row > .print-item-title:not(:has(.print-item-title-wrapper)),
.print-preview:not(.wpforms-preview-mode-description) .wpforms-field-layout-column > .print-item-title:not(:has(.print-item-title-wrapper)) {
  height: 0;
  padding: 0;
  border-top: none;
}

.print-preview:not(.wpforms-preview-mode-empty) .wpforms-field-repeater-block.wpforms-conditional-hidden:has(.wpforms-field-layout-column > .print-item.wpforms-field-empty) > .print-item-title.field-name,
.print-preview:not(.wpforms-preview-mode-empty) .wpforms-field-repeater-row.wpforms-conditional-hidden:has(.wpforms-field-layout-column > .print-item.wpforms-field-empty) > .print-item-title.field-name,
.print-preview:not(.wpforms-preview-mode-empty) .wpforms-field-layout-row.wpforms-conditional-hidden:has(.wpforms-field-layout-column > .print-item.wpforms-field-empty) > .print-item-title.field-name,
.print-preview:not(.wpforms-preview-mode-empty) .wpforms-field-layout-column.wpforms-conditional-hidden:has(.wpforms-field-layout-column > .print-item.wpforms-field-empty) > .print-item-title.field-name {
  display: none;
}

.print-preview:not(.wpforms-preview-mode-empty) .wpforms-field-repeater-block.wpforms-conditional-hidden:has(.wpforms-field-layout-column > .print-item:not(.wpforms-field-empty)) > .print-item-title.field-name,
.print-preview:not(.wpforms-preview-mode-empty) .wpforms-field-repeater-row.wpforms-conditional-hidden:has(.wpforms-field-layout-column > .print-item:not(.wpforms-field-empty)) > .print-item-title.field-name,
.print-preview:not(.wpforms-preview-mode-empty) .wpforms-field-layout-row.wpforms-conditional-hidden:has(.wpforms-field-layout-column > .print-item:not(.wpforms-field-empty)) > .print-item-title.field-name,
.print-preview:not(.wpforms-preview-mode-empty) .wpforms-field-layout-column.wpforms-conditional-hidden:has(.wpforms-field-layout-column > .print-item:not(.wpforms-field-empty)) > .print-item-title.field-name {
  display: revert;
}

.print-preview:not(.wpforms-preview-mode-empty) .wpforms-field-repeater-block-empty {
  display: none;
}

.print-preview:not(.wpforms-preview-mode-empty) .print-body .wpforms-field-layout-empty {
  display: none;
}

.print-preview:not(.wpforms-preview-mode-empty) .print-body .print-item.wpforms-field-empty {
  border-top: none;
  padding: 0;
}

.print-preview:not(.wpforms-preview-mode-empty) .print-body .wpforms-field-empty.wpforms-conditional-hidden {
  display: none !important;
}

.print-preview:not(.wpforms-preview-mode-empty) .print-body .wpforms-field-empty .print-item-value,
.print-preview:not(.wpforms-preview-mode-empty) .print-body .wpforms-field-empty .print-item-title {
  display: none !important;
}

.print-preview:not(.wpforms-preview-mode-empty) .print-body .wpforms-layout-row:first-child .wpforms-field-layout-column:not(.wpforms-field-layout-column-empty) .wpforms-field-empty:first-child .print-item-title {
  display: block !important;
}

.print-preview:not(.wpforms-preview-mode-empty) .print-body .wpforms-field-layout-column-empty .wpforms-field-empty .print-item-title {
  display: none !important;
}

.print-preview:not(.wpforms-preview-mode-html) .wpforms-field-html,
.print-preview:not(.wpforms-preview-mode-html) .wpforms-field-content {
  display: none !important;
}

.print-preview:not(.wpforms-preview-mode-pagebreak) .print-item:not(.wpforms-field-layout):not(.wpforms-field-html):not(.wpforms-field-content):not(.wpforms-field-empty):not(.wpforms-field-location).wpforms-field-pagebreak {
  display: none !important;
}

.print-preview:not(.wpforms-preview-mode-divider) .print-item:not(.wpforms-field-layout):not(.wpforms-field-html):not(.wpforms-field-content):not(.wpforms-field-empty):not(.wpforms-field-location).wpforms-field-divider {
  display: none !important;
}

.print-preview .field-value-choices-mode {
  display: none;
}

.print-preview.wpforms-preview-mode-unselected-choices .field-value-choices-mode {
  display: block;
}

.print-preview.wpforms-preview-mode-unselected-choices .field-value-default-mode {
  display: none;
}

.print-preview:not(.wpforms-preview-mode-note) .wpforms-field-notes {
  display: none;
}

@media (max-width: 1023px) {
  .close-window {
    display: none;
  }
  .print-preview {
    margin: 0 auto;
    border-radius: 0;
    padding: 30px;
  }
  .print-preview .page-title {
    display: block;
  }
  .print-preview .buttons {
    margin-top: 15px;
  }
  .print-preview .buttons .button-print {
    margin-left: 0;
  }
  .print-preview .actions {
    left: -10px;
    right: auto;
    max-width: calc( 100vw - 90px);
  }
  .print-preview.wpforms-preview-mode-compact .print-item-title {
    min-width: 120px;
    width: 120px;
  }
}

.site {
  text-align: center;
  font-size: 12px;
}

.site a {
  text-decoration: underline;
}

@media print {
  .print-preview {
    border: none;
    box-shadow: none;
    padding: 30px 0 15px;
    margin: 0;
    width: 100%;
    max-width: 100%;
  }
  .print-preview h1 {
    display: block;
    width: 100%;
    text-align: center;
  }
  .no-print,
  .no-print * {
    display: none !important;
  }
  .page-break {
    /* Always force page breaks before the element. */
    page-break-before: always;
    break-before: page;
  }
}
