<?php

declare (strict_types=1);
namespace WPForms\Vendor\Square\Models;

use stdClass;
/**
 * Represents an output from a call to [UpdateVendor]($e/Vendors/UpdateVendor).
 */
class UpdateVendorResponse implements \JsonSerializable
{
    /**
     * @var Error[]|null
     */
    private $errors;
    /**
     * @var Vendor|null
     */
    private $vendor;
    /**
     * Returns Errors.
     * Errors occurred when the request fails.
     *
     * @return Error[]|null
     */
    public function getErrors() : ?array
    {
        return $this->errors;
    }
    /**
     * Sets Errors.
     * Errors occurred when the request fails.
     *
     * @maps errors
     *
     * @param Error[]|null $errors
     */
    public function setErrors(?array $errors) : void
    {
        $this->errors = $errors;
    }
    /**
     * Returns Vendor.
     * Represents a supplier to a seller.
     */
    public function getVendor() : ?Vendor
    {
        return $this->vendor;
    }
    /**
     * Sets Vendor.
     * Represents a supplier to a seller.
     *
     * @maps vendor
     */
    public function setVendor(?Vendor $vendor) : void
    {
        $this->vendor = $vendor;
    }
    /**
     * Encode this object to JSON
     *
     * @param bool $asArrayWhenEmpty Whether to serialize this model as an array whenever no fields
     *        are set. (default: false)
     *
     * @return array|stdClass
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize(bool $asArrayWhenEmpty = \false)
    {
        $json = [];
        if (isset($this->errors)) {
            $json['errors'] = $this->errors;
        }
        if (isset($this->vendor)) {
            $json['vendor'] = $this->vendor;
        }
        $json = \array_filter($json, function ($val) {
            return $val !== null;
        });
        return !$asArrayWhenEmpty && empty($json) ? new stdClass() : $json;
    }
}
