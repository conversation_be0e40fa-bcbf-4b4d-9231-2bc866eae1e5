{"translation-revision-date": "2025-05-14 12:54:08+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=6; plural=(n == 0) ? 0 : ((n == 1) ? 1 : ((n == 2) ? 2 : ((n % 100 >= 3 && n % 100 <= 10) ? 3 : ((n % 100 >= 11 && n % 100 <= 99) ? 4 : 5))));", "lang": "ar"}, "Open overlay from filter links": ["فتح التراكب من روابط التصفية"], "Open overlay when filters are used outside the Jetpack Sidebar": ["فتح التراكب عند استخدام عوامل التصفية خارج الشريط الجانبي لـ Jetpack"], "Show post date": ["عرض تاريخ التدوينة"], "Jetpack Search has encountered an error. Please contact the site administrator if the issue persists. [%s]": ["لقد واجه Jetpack Search خطأ. يرجى الاتصال بمسؤول الموقع إذا استمرت المشكلة. [%s]"], "Jetpack Search is currently unavailable. Please try again later. [%s]": ["Jetpack Search غير متاح في الوقت الحالي. يرجى المحاولة مرة أخرى لاحقًا. [%s]"], "Open when user submits the form (recommended)": ["فتح عندما يقوم المستخدم بإرسال النموذج (موصى به)"], "Magnifying Glass": ["عدسة مكبِّرة"], "Matches content": ["يطاب<PERSON> المحتوى"], "Matches comments": ["يطابق التعليقات"], "Show preview": ["إظهار المعاينة"], "You must leave at least one post type unchecked.": ["يجب أن تترك نوع مقالة غير محدَّدة بحد أدنى."], "Excluded post types": ["أنواع المقالة المستبعدة"], "Loading…": ["جاري التحميل…"], "Default sort": ["الفرز الافتراضي"], "Result format": ["تنسيق النتيجة"], "Show settings": ["إظهار الإعدادات"], "Customize Jetpack Search": ["تخصيص Jetpack Search"], "Dark Theme": ["قالب داكن"], "Light Theme": ["قالب فاتح"], "Show \"Powered by Jetpack\"": ["إظهار \"مدعوم من Jetpack\""], "Styling": ["التصميم"], "Configure in the Customizer": ["تكوين في أداة التخصيص"], "Edit widgets": ["تحرير المربعات الجانبية"], "Jetpack Search will allow your visitors to get search results as soon as they start typing. Customize this experience to offer better results that match your site.": ["سيسمح Jetpack Search لزائريك بالحصول على نتائج البحث بمجرد بدء الكتابة. قم بتخصيص هذه التجربة لتوفير نتائج أفضل تناسب موقعك."], "%s (selected)": ["%s مُحدَّد"], "Jetpack Search customization settings": ["إعدادات تخصيص Jetpack Search"], "Jetpack Search customization preview": ["معاينة تخصيص Jetpack Search"], "Jetpack Search customization top bar": ["تخصيص الشريط العلوي في Jetpack Search"], "Found %s result": ["تم العثور على %s نتيجة", "تم العثور على %s من النتائج", "تم العثور على %s من النتائج", "تم العثور على %s من النتائج", "تم العثور على %s من النتائج", "تم العثور على %s من النتائج"], "Found %1$s result in %2$s": ["تم العثور على ⁦%1$s⁩ نتيجة في ⁦%2$s⁩", "تم العثور على ⁦%1$s⁩ من النتائج في ⁦%2$s⁩", "تم العثور على ⁦%1$s⁩ من النتائج في ⁦%2$s⁩", "تم العثور على ⁦%1$s⁩ من النتائج في ⁦%2$s⁩", "تم العثور على ⁦%1$s⁩ من النتائج في ⁦%2$s⁩", "تم العثور على ⁦%1$s⁩ من النتائج في ⁦%2$s⁩"], "Filter options": ["خيارات عامل التصفية"], "Does not have an image": ["لا تتضمن صورة"], "%d review": ["%d مراجعة", "%d مراجعات", "%d مراجعات", "%d مراجعات", "%d مراجعات", "%d مراجعات"], "clear": ["م<PERSON><PERSON>"], "Sort:": ["فرز:"], "Product (for WooCommerce stores)": ["المنتج (خاص بمتاجر WooCommerce)"], "Expanded (shows images)": ["تم التوسيع (يعرض الصور)"], "Show search filters": ["إظهار عوامل تصفية البحث"], "Average rating of %1$d out of 5 from %2$d review.": ["متوسط التقييم ⁦%1$d⁩ من 5 من ⁦%2$d⁩ مراجعة.", "متوسط التقييم ⁦%1$d⁩ من 5 من ⁦%2$d⁩ من المراجعات.", "متوسط التقييم ⁦%1$d⁩ من 5 من ⁦%2$d⁩ من المراجعات.", "متوسط التقييم ⁦%1$d⁩ من 5 من ⁦%2$d⁩ من المراجعات.", "متوسط التقييم ⁦%1$d⁩ من 5 من ⁦%2$d⁩ من المراجعات.", "متوسط التقييم ⁦%1$d⁩ من 5 من ⁦%2$d⁩ من المراجعات."], "Price: high to low": ["السعر: عالٍ إلى منخفض"], "Price: low to high": ["السعر: من<PERSON><PERSON><PERSON> إلى عالٍ"], "No title": ["بلا عنوان"], "Showing popular results": ["عرض النتائج الشائعة"], "Loading popular results…": ["جارٍ تحميل النتائج الشائعة..."], "Close search results": ["إغلاق نتائج البحث"], "Open when user starts typing": ["فتح عندما يبدأ مستخدم بالكتابة"], "It looks like you're offline. Please reconnect to load the latest results.": ["يبدو أنك غير متصل بالإنترنت. يرجى إعادة الاتصال لتحميل أحدث النتائج."], "No results for \"%s\"": ["لا توجد نتائج لـ \"%s\""], "Found %1$s result for \"%2$s\"": ["تم العثور على ⁦%1$s⁩ من النتائج الخاصة بـ \"⁦%2$s⁩\"", "تم العثور على ⁦%1$s⁩ من النتائج الخاصة بـ \"⁦%2$s⁩\"", "تم العثور على ⁦%1$s⁩ من النتائج الخاصة بـ \"⁦%2$s⁩\"", "تم العثور على ⁦%1$s⁩ من النتائج الخاصة بـ \"⁦%2$s⁩\"", "تم العثور على ⁦%1$s⁩ من النتائج الخاصة بـ \"⁦%2$s⁩\"", "تم العثور على ⁦%1$s⁩ من النتائج الخاصة بـ \"⁦%2$s⁩\""], "Searching…": ["جارٍ البحث..."], "Clear filters": ["مس<PERSON> عوا<PERSON>ل التصفية"], "Hide filters": ["إخفاء عوامل التصفية"], "Filters": ["عوامل التصفية"], "Search…": ["بحث..."], "Oldest": ["الأقدم"], "Search powered by Jetpack": ["البحث مدعوم من Jetpack"], "Load more": ["تحميل المزيد"], "Has a video.": ["يحتوي على فيديو."], "Tag": ["وسم"], "Post": ["تدوينة"], "Page": ["صفحة"], "Has an image.": ["يحتوي على صورة."], "Has multiple images.": ["يحتوي على عدة صور."], "Toggle search filters.": ["قم بتبديل عوامل تصفية البحث."], "Matching comment.": ["التعليق المتطابق."], "Show filters": ["عرض عوامل التصفية"], "Is a product.": ["يُعد منتجًا."], "Is an event.": ["يُعد حدثًا."], "Has audio.": ["يحتوي على صوت."], "Show sort selector": ["إظهار مُحدِّد الفرز"], "Minimal": ["ال<PERSON><PERSON> الأدنى"], "Overlay trigger": ["مشغّل التراكب"], "Oldest first": ["الأقدم أولاً"], "Newest first": ["الأحدث أولاً"], "Relevance": ["الملاءمة"], "Relevance (recommended)": ["الملاءمة (موصى بها)"], "Jetpack Search": ["Jetpack Search"], "Saving…": ["جاري الحفظ…"], "No results found": ["لم يتم العثور على نتائج"], "Loading": ["تحميل"], "Image": ["صورة"], "Category": ["الفئة"], "Search results": ["نتائج البحث"], "Rating": ["التقدير"], "Save": ["<PERSON><PERSON><PERSON>"], "Options": ["إعدادات"], "Dark": ["غامق"], "Light": ["خ<PERSON><PERSON><PERSON>"], "Newest": ["الأحدث"], "Sort by:": ["ترتيب حسب:"], "Search": ["ب<PERSON><PERSON>"]}}, "comment": {"reference": "jetpack_vendor/automattic/jetpack-search/build/customberg/jp-search-configure.js"}}