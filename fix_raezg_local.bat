@echo off
echo ===== FIXING RAEZG.LOCAL CONFIGURATION =====
echo.

echo 1. Checking hosts file...
findstr /C:"127.0.0.1 raezg.local" C:\Windows\System32\drivers\etc\hosts > nul
if %errorlevel% neq 0 (
    echo Adding raezg.local to hosts file...
    echo 127.0.0.1 raezg.local >> C:\Windows\System32\drivers\etc\hosts
    echo 127.0.0.1 www.raezg.local >> C:\Windows\System32\drivers\etc\hosts
) else (
    echo Hosts file entries already exist.
)

echo.
echo 2. Creating test files...
if not exist C:\xampp\htdocs\raezg mkdir C:\xampp\htdocs\raezg

echo ^<!DOCTYPE html^>^<html^>^<head^>^<title^>Test Page^</title^>^</head^>^<body^>^<h1^>raezg.local is working!^</h1^>^<p^>This is a test page.^</p^>^</body^>^</html^> > C:\xampp\htdocs\raezg\index.html

echo ^<?php echo "^<h1^>PHP Test^</h1^>"; echo "PHP Version: " . phpversion(); ?^> > C:\xampp\htdocs\raezg\test.php

echo.
echo 3. Updating VirtualHost configuration...
echo # Default VirtualHost must come first > C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo ^<VirtualHost *:80^> >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo     DocumentRoot "C:/xampp/htdocs" >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo     ServerName localhost >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo ^</VirtualHost^> >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo. >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo ^<VirtualHost *:80^> >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo     ServerName raezg.local >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo     ServerAlias www.raezg.local >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo     DocumentRoot "C:/xampp/htdocs/raezg" >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo     ErrorLog "logs/raezg.local-error.log" >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo     CustomLog "logs/raezg.local-access.log" common >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo     ^<Directory "C:/xampp/htdocs/raezg"^> >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo         Options Indexes FollowSymLinks >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo         AllowOverride All >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo         Require all granted >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo     ^</Directory^> >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo ^</VirtualHost^> >> C:\xampp\apache\conf\extra\httpd-vhosts.conf

echo.
echo 4. Ensuring VirtualHosts are enabled in httpd.conf...
powershell -Command "(Get-Content C:\xampp\apache\conf\httpd.conf) -replace '#Include conf/extra/httpd-vhosts.conf', 'Include conf/extra/httpd-vhosts.conf' | Set-Content C:\xampp\apache\conf\httpd.conf"

echo.
echo 5. Checking for port conflicts...
netstat -ano | findstr :80
echo If you see other programs using port 80, you may need to close them.

echo.
echo 6. Restarting Apache...
taskkill /F /IM httpd.exe /T > nul 2>&1
timeout /t 2 > nul
net start Apache2.4 > nul 2>&1
if %errorlevel% neq 0 (
    echo Failed to start Apache. Trying to start from XAMPP Control Panel...
    start "" "C:\xampp\xampp-control.exe"
    echo Please click "Start" for Apache in the XAMPP Control Panel.
) else (
    echo Apache restarted successfully!
)

echo.
echo ===== CONFIGURATION COMPLETE =====
echo.
echo Now try accessing http://raezg.local/ in your browser.
echo If it still doesn't work, try these troubleshooting steps:
echo 1. Clear your browser cache or try a different browser
echo 2. Check C:\xampp\apache\logs\error.log for errors
echo 3. Try alternative URLs:
echo    - http://localhost/raezg/
echo    - http://127.0.0.1/raezg/
echo 4. Temporarily disable Windows Firewall
echo.
pause