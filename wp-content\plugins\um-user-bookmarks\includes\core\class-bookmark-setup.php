<?php
namespace um_ext\um_user_bookmarks\core;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}


/**
 * Class Bookmark_Setup
 *
 * @package um_ext\um_user_bookmarks\core
 */
class Bookmark_Setup {

	/**
	 * @var array
	 */
	public $settings_defaults;


	/**
	 * Bookmark_Setup constructor.
	 */
	public function __construct() {
		//settings defaults
		$this->settings_defaults = array(
			'profile_tab_bookmarks'                  => true,
			'um_user_bookmarks_post_types'           => array( 'post', 'page' ),
			'um_user_bookmarks_archive_page'         => 0,
			'um_user_bookmarks_profile'              => 0,
			'um_user_bookmarks_position'             => 'bottom',
			'um_user_bookmarks_add_text'             => __( 'Bookmark', 'um-user-bookmarks' ),
			'um_user_bookmarks_remove_text'          => __( 'Remove bookmark', 'um-user-bookmarks' ),
			'um_user_bookmarks_disable_folders'      => 0,
			'um_user_bookmarks_folders_text'         => __( 'Folders', 'um-user-bookmarks' ),
			'um_user_bookmarks_folder_text'          => __( 'Folder', 'um-user-bookmarks' ),
			'um_user_bookmarks_profile_folders_text' => __( 'Users', 'um-user-bookmarks' ),
			'um_user_bookmarks_profile_folder_text'  => __( 'User', 'um-user-bookmarks' ),
			'um_user_bookmarks_bookmarked_icon'      => 'um-faicon-bookmark',
			'um_user_bookmarks_regular_icon'         => 'um-faicon-bookmark-o',
			'um_user_bookmarks_page_builder'         => 0,
			'um_user_bookmarks_default_folder'       => 0,
			'um_user_bookmarks_default_folder_name'  => __( 'My bookmarks', 'um-user-bookmarks' ),
		);
	}


	/**
	 * Set default settings function
	 */
	public function set_default_settings() {
		$options = get_option( 'um_options', array() );

		foreach ( $this->settings_defaults as $key => $value ) {
			//set new options to default
			if ( ! isset( $options[ $key ] ) ) {
				$options[ $key ] = $value;
			}
		}

		update_option( 'um_options', $options );
	}


	/**
	 * Run User Bookmark Setup
	 */
	public function run_setup() {
		$this->set_default_settings();
	}
}
