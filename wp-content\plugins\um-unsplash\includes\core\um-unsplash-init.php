<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! class_exists( 'UM_Unsplash' ) ) {

	/**
	 * Class UM_Unsplash
	 */
	class UM_Unsplash extends UM_Unsplash_Functions {

		public $proxy_url = 'https://ultimatemember.com/wp-admin/admin-ajax.php';

		/**
		 * @var
		 */
		private static $instance;

		/**
		 * @return UM_Unsplash
		 */
		public static function instance() {
			if ( is_null( self::$instance ) ) {
				self::$instance = new self();
				self::$instance->_um_unsplash_construct();
			}

			return self::$instance;
		}

		/**
		 * Unsplash_API constructor.
		 */
		public function _um_unsplash_construct() {
			add_filter( 'um_call_object_Unsplash', array( &$this, 'get_this' ) );
			add_filter( 'um_settings_default_values', array( &$this, 'default_settings' ) );

			$license = UM()->options()->get( 'um_unsplash_license_key' );
			if ( empty( $license ) ) {
				$this->admin();
			} else {
				if ( UM()->is_request( 'ajax' ) ) {
					$this->ajax();
				} elseif ( UM()->is_request( 'admin' ) ) {
					$this->admin();
					$this->ajax();
				} elseif ( UM()->is_request( 'frontend' ) ) {
					$this->enqueue();
				}

				$this->profile();
			}
		}

		/**
		 * @return $this
		 */
		public function get_this() {
			return $this;
		}

		/**
		 * @param array $defaults
		 *
		 * @return array
		 */
		public function default_settings( $defaults ) {
			return array_merge( $defaults, $this->setup()->settings_defaults );
		}

		/**
		 * @return um_ext\um_unsplash\core\Unsplash_Setup
		 */
		public function setup() {
			if ( empty( UM()->classes['um_unsplash_setup'] ) ) {
				UM()->classes['um_unsplash_setup'] = new um_ext\um_unsplash\core\Unsplash_Setup();
			}

			return UM()->classes['um_unsplash_setup'];
		}

		/**
		 * @return um_ext\um_unsplash\admin\Admin
		 */
		public function admin() {
			if ( empty( UM()->classes['um_unsplash_admin'] ) ) {
				UM()->classes['um_unsplash_admin'] = new um_ext\um_unsplash\admin\Admin();
			}

			return UM()->classes['um_unsplash_admin'];
		}

		/**
		 * @return um_ext\um_unsplash\core\Unsplash_Enqueue
		 */
		public function enqueue() {
			if ( empty( UM()->classes['um_unsplash_enqueue'] ) ) {
				UM()->classes['um_unsplash_enqueue'] = new um_ext\um_unsplash\core\Unsplash_Enqueue();
			}

			return UM()->classes['um_unsplash_enqueue'];
		}

		/**
		 * @return um_ext\um_unsplash\core\Profile
		 */
		public function profile() {
			if ( empty( UM()->classes['um_unsplash_profile'] ) ) {
				UM()->classes['um_unsplash_profile'] = new um_ext\um_unsplash\core\Profile();
			}

			return UM()->classes['um_unsplash_profile'];
		}

		/**
		 * @return um_ext\um_unsplash\core\Unsplash_Ajax
		 */
		public function ajax() {
			if ( empty( UM()->classes['um_unsplash_ajax'] ) ) {
				UM()->classes['um_unsplash_ajax'] = new um_ext\um_unsplash\core\Unsplash_Ajax();
			}

			return UM()->classes['um_unsplash_ajax'];
		}
	}
}

//create class var
add_action( 'plugins_loaded', 'um_init_unsplash', -10 );
if ( ! function_exists( 'um_init_unsplash' ) ) {
	function um_init_unsplash() {
		if ( function_exists( 'UM' ) ) {
			UM()->set_class( 'Unsplash', true );
		}
	}
}
