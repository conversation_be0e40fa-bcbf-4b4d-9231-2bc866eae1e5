<?php
namespace um_ext\um_user_photos\common;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Setup
 *
 * @package um_ext\um_user_photos\common
 */
class Setup {

	/**
	 * @var array
	 */
	public $settings_defaults;

	/**
	 * User_Photos_Setup constructor.
	 */
	public function __construct() {
		//settings defaults
		$this->settings_defaults = array(
			'um_user_photos_albums_column' => 'um-user-photos-col-3',
			'um_user_photos_images_column' => 'um-user-photos-col-3',
			'um_user_photos_images_row'    => 2,
			'um_user_photos_cover_size'    => '',
			'um_user_photos_image_size'    => '',
			'um_user_photos_disable_title' => 0,
			'um_user_photos_disable_cover' => 0,
			'um_user_photos_album_limit'   => '',
			'um_user_photos_media_library' => 0,
			'profile_tab_photos'           => 1,

			'new_album_on'                 => 1,
			'new_album_sub'                => __( '[{site_name}] User Photo - has been created.', 'um-user-photos' ),
			'new_album'                    => 'User "{user_name}" created the album "{album_title}".<br />Click on the following link to see user\'s albums:<br />{profile_url}',
			'album_deleted_on'             => 1,
			'album_deleted_sub'            => __( '[{site_name}] User Photo - Album has been deleted.', 'um-user-photos' ),
			'album_deleted'                => 'User "{user_name}" deleted the album "{album_title}".<br />Click on the following link to see user\'s albums:<br />{profile_url}',
			'album_updated_on'             => 1,
			'album_updated_sub'            => __( '[{site_name}] User Photo - Album has been updated.', 'um-user-photos' ),
			'album_updated'                => 'User "{user_name}" updated the album "{album_title}".<br />Click on the following link to see user\'s albums:<br />{profile_url}',
		);

		if ( defined( 'UM_DEV_MODE' ) && UM_DEV_MODE && UM()->options()->get( 'enable_new_ui' ) ) {
			$this->settings_defaults['um_user_photos_albums_column'] = 3;
			$this->settings_defaults['um_user_photos_images_column'] = 3;
		}
	}

	/**
	 * Set default settings function
	 */
	public function set_default_settings() {
		$options = get_option( 'um_options', array() );

		foreach ( $this->settings_defaults as $key => $value ) {
			// Set new options to default
			if ( ! isset( $options[ $key ] ) ) {
				$options[ $key ] = $value;
			}
		}

		update_option( 'um_options', $options );
	}

	/**
	 * Run User Photos Setup
	 */
	public function run_setup() {
		$this->set_default_settings();
	}
}
