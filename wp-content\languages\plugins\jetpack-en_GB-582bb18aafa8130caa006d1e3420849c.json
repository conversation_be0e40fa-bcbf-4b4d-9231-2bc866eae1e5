{"translation-revision-date": "2025-02-20 19:47:04+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "<strong>Notice:</strong> It appears that your 'wp-config.php' file might be using dynamic site URL values. Dynamic site URLs could cause Jetpack to enter Safe Mode. <dynamicSiteUrlSupportLink>Learn how to set a static site URL.</dynamicSiteUrlSupportLink>": ["<strong>Notice:</strong> it appears that your 'wp-config.php' file might be using dynamic site URL values. Dynamic site URLs could cause Jetpack to enter Safe Mode. <dynamicSiteUrlSupportLink>Learn how to set a static site URL.</dynamicSiteUrlSupportLink>"], "Finishing setting up Safe mode…": ["Finishing setting up Safe mode…"], "Or decide later and stay in <button>Safe mode</button>": ["Or decide later and stay in <button>Safe mode</button>"], "Safe mode": ["Safe mode"], "Could not stay in safe mode. Retry or find out more <a>here</a>.": ["Could not stay in safe mode. Retry or find out more <a>here</a>."], "This site is in Safe Mode because there are 2 Jetpack-powered sites that appear to be duplicates. 2 sites that are telling Jetpack they’re the same site. <safeModeLink>Learn more about safe mode.</safeModeLink>": ["This site is in Safe Mode because there are two Jetpack-powered sites that appear to be duplicates. Two sites that are telling Jetpack they’re the same site. <safeModeLink>Learn more about safe mode.</safeModeLink>"], "Safe Mode": ["Safe Mode"], "Your site is in Safe Mode because you have 2 Jetpack-powered sites that appear to be duplicates. Two sites that are telling Jetpack they’re the same site. <safeModeLink>Learn more about safe mode.</safeModeLink>": ["Your site is in Safe Mode because you have two Jetpack-powered sites that appear to be duplicates. Two sites that are telling Jetpack they’re the same site. <safeModeLink>Learn more about safe mode.</safeModeLink>"], "Please select an option": ["Please select an option"], "Got it, thanks": ["Got it, thanks"], "Your Jetpack settings have migrated successfully": ["Your Jetpack settings have migrated successfully"], "Safe Mode has been switched off for <hostname>%1$s</hostname> website and Jetpack is fully functional.": ["Safe Mode has been switched off for <hostname>%1$s</hostname> website and Jetpack is fully functional."], "Move all your settings, stats and subscribers to your other URL, <hostname>%1$s</hostname>. <hostname>%2$s</hostname> will be disconnected from Jetpack.": ["Move all your settings, stats, and subscribers to your other URL, <hostname>%1$s</hostname>. <hostname>%2$s</hostname> will be disconnected from Jetpack."], "Could not move your settings. Retry or find out more <a>here</a>.": ["Could not move your settings. Retry or find out more <a>here</a>."], "Safe Mode has been activated": ["Safe Mode has been activated"], "Create a fresh connection": ["Create a fresh connection"], "Treat each site as independent sites": ["Treat each site as independent sites"], "<hostname>%1$s</hostname> settings, stats, and subscribers will start fresh. <hostname>%2$s</hostname> will keep its data as is.": ["<hostname>%1$s</hostname> settings, stats, and subscribers will start fresh. <hostname>%2$s</hostname> will keep its data as is."], "Could not create the connection. Retry or find out more <a>here</a>.": ["Could not create the connection. Retry or find out more <a>here</a>."], "Move your settings": ["Move your settings"], "Move Jetpack data": ["Move Jetpack data"], "Jetpack Logo": ["Jetpack Logo"], "An administrator of this site can take Jetpack out of Safe Mode.": ["An administrator of this site can take Jetpack out of Safe Mode."]}}, "comment": {"reference": "jetpack_vendor/automattic/jetpack-connection/dist/identity-crisis.js"}}