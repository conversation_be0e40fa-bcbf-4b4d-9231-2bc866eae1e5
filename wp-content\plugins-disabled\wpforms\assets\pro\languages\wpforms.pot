msgid ""
msgstr ""
"Project-Id-Version: WPForms 1.9.5.2\n"
"Report-Msgid-Bugs-To: https://wpforms.com/support/\n"
"Last-Translator: WPForms <<EMAIL>>\n"
"Language-Team: WPForms <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-05-02T09:19:03+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.7.1\n"
"X-Domain: wpforms\n"

#. Plugin Name of the plugin
#. Author of the plugin
#: src/Pro/Access/Integrations.php:103
#: src/Pro/Access/Integrations.php:160
#: src/Pro/Admin/DashboardWidget.php:243
#: src/Pro/Admin/SiteHealth.php:37
msgid "WPForms"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
msgid "https://wpforms.com"
msgstr ""

#. Description of the plugin
msgid "Beginner friendly WordPress contact form plugin. Use our Drag & Drop form builder to create your WordPress forms."
msgstr ""

#: pro/includes/admin/ajax-actions.php:43
#: pro/includes/admin/entries/class-entries-single.php:259
#: src/Pro/Admin/Entries/ListTable.php:1427
msgid "Entry starred."
msgstr ""

#: pro/includes/admin/ajax-actions.php:53
#: pro/includes/admin/entries/class-entries-single.php:290
#: src/Pro/Admin/Entries/ListTable.php:1487
msgid "Entry unstarred."
msgstr ""

#: pro/includes/admin/ajax-actions.php:113
#: pro/includes/admin/entries/class-entries-single.php:725
#: src/Pro/Admin/Entries/Edit.php:512
#: src/Pro/Admin/Entries/ListTable.php:1223
msgid "Entry read."
msgstr ""

#: pro/includes/admin/ajax-actions.php:123
#: pro/includes/admin/entries/class-entries-single.php:357
#: src/Pro/Admin/Entries/ListTable.php:1283
msgid "Entry unread."
msgstr ""

#: pro/includes/admin/ajax-actions.php:160
msgid "This feature requires an active license. Please contact the site administrator."
msgstr ""

#: pro/includes/admin/ajax-actions.php:165
#: pro/includes/admin/ajax-actions.php:209
#: src/Pro/Admin/Education/StringsTrait.php:83
msgid "Please enter a license key."
msgstr ""

#: pro/includes/admin/class-license.php:154
#: pro/includes/admin/class-license.php:293
#: pro/includes/admin/class-license.php:377
msgid "There was an error connecting to the remote key API. Please try again later."
msgstr ""

#: pro/includes/admin/class-license.php:176
msgid "Congratulations! This site is now receiving automatic updates."
msgstr ""

#: pro/includes/admin/class-license.php:392
msgid "You have deactivated the key from this site successfully."
msgstr ""

#. translators: %1$s - WPForms.com Account dashboard URL, %2$s - WPForms.com pricing URL.
#: pro/includes/admin/class-license.php:495
msgid "Your license key can be found in your <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">WPForms Account Dashboard</a>. Don’t have a license? <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">Sign up today!</a>"
msgstr ""

#: pro/includes/admin/class-license.php:511
msgid "<strong>Your license has expired.</strong> An active license is needed to create new forms and edit existing forms. It also provides access to new features & addons, plugin updates (including security improvements), and our world class support!"
msgstr ""

#: pro/includes/admin/class-license.php:518
msgid "<strong>Your license key has been disabled.</strong> Please use a different key to continue receiving automatic updates."
msgstr ""

#: pro/includes/admin/class-license.php:525
msgid "<strong>Your license key is invalid.</strong> The key no longer exists or the user associated with the key has been deleted. Please use a different key to continue receiving automatic updates."
msgstr ""

#. translators: %1$s - WPForms.com Account licenses URL.
#: pro/includes/admin/class-license.php:533
msgid "<strong>Sorry, but this license has no activations left.</strong> You can manage your site activations, upgrade your license, or purchase a new one in <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">your account</a>."
msgstr ""

#. translators: %1$s - WPForms.com license key support URL.
#: pro/includes/admin/class-license.php:550
msgid "<strong>Heads up! Before you can activate this key, we'd like to check in with you.</strong> Please <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">reach out to support here.</a>"
msgstr ""

#. translators: %s - Link to the Settings > General screen in the plugin, where users can enter their license key.
#: pro/includes/admin/class-license.php:594
msgid "To access all features, addons, and enable automatic updates, please <a href=\"%s\" target=\"_blank\">activate your WPForms license.</a>"
msgstr ""

#: pro/includes/admin/class-license.php:640
#: src/Pro/Admin/Builder/LicenseAlert.php:131
msgid "Heads up! Your WPForms license has expired."
msgstr ""

#: pro/includes/admin/class-license.php:641
msgid "An active license is needed to create new forms and edit existing forms. It also provides access to new features & addons, plugin updates (including security improvements), and our world class support!"
msgstr ""

#: pro/includes/admin/class-license.php:643
#: pro/includes/admin/class-license.php:667
#: pro/includes/admin/class-license.php:690
#: pro/includes/admin/class-license.php:713
#: src/Pro/Admin/Builder/LicenseAlert.php:133
#: src/Pro/Admin/Education/StringsTrait.php:91
msgid "Renew Now"
msgstr ""

#: pro/includes/admin/class-license.php:645
#: pro/includes/admin/class-license.php:669
#: pro/includes/admin/class-license.php:692
#: pro/includes/admin/class-license.php:715
#: pro/templates/education/admin/did-you-know.php:30
#: src/Pro/Admin/Builder/LicenseAlert.php:134
#: src/Pro/Admin/DashboardWidget.php:329
#: src/Pro/Admin/DashboardWidget.php:670
msgid "Learn More"
msgstr ""

#: pro/includes/admin/class-license.php:664
msgid "Heads up! Your WPForms license has been disabled."
msgstr ""

#: pro/includes/admin/class-license.php:665
msgid "Your license key for WPForms has been disabled. Please use a different key to continue receiving automatic updates"
msgstr ""

#: pro/includes/admin/class-license.php:687
msgid "Heads up! Your WPForms license is invalid."
msgstr ""

#: pro/includes/admin/class-license.php:688
msgid "The key no longer exists or the user associated with the key has been deleted. Please use a different key to continue receiving automatic updates."
msgstr ""

#: pro/includes/admin/class-license.php:710
msgid "Heads up! Your WPForms license has no activations left."
msgstr ""

#: pro/includes/admin/class-license.php:711
msgid "Sorry, but this license has no activations left. You can update the list of your sites, upgrade the license in the Account area or purchase a new license key."
msgstr ""

#: pro/includes/admin/class-license.php:739
#: src/Pro/Admin/Education/StringsTrait.php:55
msgid "Heads up!"
msgstr ""

#: pro/includes/admin/class-license.php:740
msgid "Before you can activate this key, we'd like to check in with you. Please reach out to support."
msgstr ""

#: pro/includes/admin/class-license.php:742
#: src/Pro/Admin/Education/StringsTrait.php:97
msgid "Contact Support"
msgstr ""

#: pro/includes/admin/class-license.php:1036
msgid "You've Exceeded the Allowed License Verification Attempts"
msgstr ""

#: pro/includes/admin/class-license.php:1037
msgid "Double-check the license key in your account and try again later. If your license key is no longer valid, please renew or install the free version of WPForms."
msgstr ""

#: pro/includes/admin/class-license.php:1079
msgid "Your license key for WPForms is invalid. The key no longer exists or the user associated with the key has been deleted. Please use a different key to continue receiving automatic updates."
msgstr ""

#: pro/includes/admin/class-license.php:1103
msgid "Your license key for WPForms has expired. Please renew your license key on WPForms.com to continue receiving automatic updates."
msgstr ""

#: pro/includes/admin/class-license.php:1127
msgid "Your license key for WPForms has been disabled. Please use a different key to continue receiving automatic updates."
msgstr ""

#: pro/includes/admin/class-license.php:1151
msgid "Sorry, but this license has no activations left. You can manage your site activations, upgrade your license, or purchase a new one in your account."
msgstr ""

#. translators: %1$s - WPForms.com license key support URL.
#: pro/includes/admin/class-license.php:1178
msgid "Heads up! Before you can activate this key, we'd like to check in with you. Please <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">reach out to support here.</a>"
msgstr ""

#: pro/includes/admin/class-license.php:1217
msgid "Your key has been refreshed successfully."
msgstr ""

#: pro/includes/admin/entries/class-entries-export.php:224
#: src/Pro/Admin/Entries/Table/Facades/Columns.php:145
#: src/Pro/Forms/Fields/DateTime/Field.php:121
msgid "Date"
msgstr ""

#: pro/includes/admin/entries/class-entries-export.php:225
msgid "Date GMT"
msgstr ""

#: pro/includes/admin/entries/class-entries-export.php:226
msgid "ID"
msgstr ""

#: pro/includes/admin/entries/class-entries-export.php:367
msgid "You do not have permission to export entries."
msgstr ""

#: pro/includes/admin/entries/class-entries-export.php:368
msgid "Error"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:136
#: src/Pro/Forms/Fields/Richtext/EntryViewContent.php:66
msgid "Sorry, you are not allowed to view this entry."
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:269
msgid "This entry has been starred."
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:300
msgid "This entry has been unstarred."
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:365
msgid "This entry has been marked unread."
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:394
msgid "Note deleted."
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:435
msgid "Something went wrong while deleting a note. Please try again."
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:487
msgid "Please provide a meaningful content for the note."
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:496
msgid "Note added."
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:583
msgid "Notifications were resent!"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:599
msgid "It looks like the provided entry ID isn't valid."
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:619
#: src/Pro/Admin/Entries/Edit.php:443
msgid "It looks like the entry you are trying to access is no longer available."
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:628
msgid "You can't view this entry because it's in the trash."
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:765
#: src/Pro/Admin/Entries/Edit.php:400
msgid "View Entry"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:810
#: pro/templates/admin/empty-states/no-entries.php:24
#: pro/templates/admin/empty-states/no-entry.php:24
msgid "Back to All Entries"
msgstr ""

#. translators: %1$d - current number of the entry, %2$d - total number of entries.
#: pro/includes/admin/entries/class-entries-single.php:818
msgid "Entry %1$d of %2$d"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:827
msgid "Previous form entry"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:835
msgid "Current form entry"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:841
msgid "Next form entry"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:915
msgid "Field Descriptions"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:919
#: pro/templates/admin/entry-print/legend-start.php:20
msgid "Empty Fields"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:923
#: pro/templates/admin/entry-print/legend-start.php:21
msgid "Unselected Choices"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:927
msgid "HTML/Content Fields"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:931
#: pro/templates/admin/entry-print/legend-start.php:23
msgid "Section Dividers"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:935
#: pro/templates/admin/entry-print/legend-start.php:24
msgid "Page Breaks"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:941
msgid "Maintain Layouts"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:945
#: pro/templates/admin/entry-print/legend-start.php:28
msgid "Compact View"
msgstr ""

#. translators: %d - form ID.
#: pro/includes/admin/entries/class-entries-single.php:995
#: src/Pro/Admin/Entries/Page.php:1273
msgid "Form (#%d)"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1027
#: pro/templates/admin/entry-print/no-fields.php:14
#: src/Pro/Admin/Entries/Edit.php:714
msgid "This entry does not have any fields"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1248
#: pro/includes/admin/entries/class-entries-single.php:1298
#: pro/templates/admin/entry-print/field.php:41
#: src/Pro/Admin/Entries/Edit.php:406
#: src/Pro/Admin/Entries/Edit.php:1026
#: src/Pro/Admin/Entries/PrintPreview.php:462
#: src/Pro/Forms/Fields/EntryPreview/Field.php:971
msgid "Empty"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1407
#: pro/templates/admin/entry-print/legend-start.php:32
#: pro/templates/admin/entry-print/notes.php:19
msgid "Notes"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1417
#: pro/includes/admin/entries/class-entries-single.php:1433
msgid "Add Note"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1434
#: src/Pro/Admin/Entries/Export/Admin.php:133
#: src/Pro/AntiSpam/KeywordFilter.php:122
#: src/Pro/Integrations/Gutenberg/FormSelector.php:162
msgid "Cancel"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1443
msgid "No notes."
msgstr ""

#. translators: %1$s - user name, %2$s - date.
#: pro/includes/admin/entries/class-entries-single.php:1484
#: pro/includes/admin/entries/class-entries-single.php:1561
#: pro/templates/admin/entry-print/notes.php:32
msgid "Added by %1$s on %2$s"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1492
msgctxt "Entry: note"
msgid "Delete"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1530
msgid "Log"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1538
msgid "No logs."
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1658
msgid "Debug Information"
msgstr ""

#. translators: %1$s - formatted date, %2$s - formatted time.
#: pro/includes/admin/entries/class-entries-single.php:1685
#: src/Pro/Admin/Entries/Edit.php:1303
msgid "%1$s at %2$s"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1697
msgid "Entry Details"
msgstr ""

#. translators: %s - entry ID.
#: pro/includes/admin/entries/class-entries-single.php:1710
msgid "Entry ID: %s"
msgstr ""

#. translators: %1$s - post type name, %2$s - post type ID.
#: pro/includes/admin/entries/class-entries-single.php:1728
msgid "%1$s ID: %2$s"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1739
msgid "Submitted:"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1748
msgid "Modified:"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1759
msgid "User:"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1776
msgid "User IP:"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1795
msgid "Type:"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1796
#: src/Pro/Admin/Entries/Export/Ajax.php:939
#: src/Pro/Admin/Entries/ListTable.php:514
msgid "Completed"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1849
msgid "Trash Entry"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1886
#: src/Pro/Admin/Entries/Export/Ajax.php:1070
#: src/Pro/Admin/Entries/ListTable.php:443
#: src/Pro/Admin/Entries/ListTable.php:2126
#: src/Pro/Admin/Entries/ListTable.php:2135
msgid "N/A"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1981
#: src/Pro/Admin/Entries/ListTable.php:979
msgid "Unstar"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:1981
#: src/Pro/Admin/Entries/ListTable.php:978
msgid "Star"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:2001
#: pro/templates/admin/entry-print/legend-start.php:96
#: src/Pro/Admin/Entries/ListTable.php:980
msgid "Print"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:2006
msgid "Export (CSV)"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:2011
msgid "Export (XLSX)"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:2029
#: src/Pro/Admin/Entries/ListTable.php:977
msgid "Mark as Unread"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:2061
msgid "Delete Entry"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:2072
#: src/Pro/Admin/Entries/ListTable.php:2232
#: src/Pro/Admin/Entries/Table/Facades/Columns.php:457
msgid "Actions"
msgstr ""

#. translators: %s - a list of addons that disable the link.
#: pro/includes/admin/entries/class-entries-single.php:2088
msgid "Unavailable because %s is enabled."
msgid_plural "Unavailable because %s are enabled."
msgstr[0] ""
msgstr[1] ""

#: pro/includes/admin/entries/class-entries-single.php:2094
#: src/Pro/Forms/Fields/Repeater/Builder.php:107
msgid "and"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:2163
msgid "Resend Notifications"
msgstr ""

#. translators: %s - payment add-on name.
#: pro/includes/admin/entries/class-entries-single.php:2186
msgid "the \"%s completed payments\" setting"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:2251
msgid "Related Entries"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:2257
msgid "The user who created this entry also submitted the entries below."
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:2273
msgid "(Abandoned)"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:2442
msgid "Section Divider"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:2446
msgid "Page Break"
msgstr ""

#: pro/includes/admin/entries/class-entries-single.php:2450
msgid "Content Field"
msgstr ""

#. translators: %d - field ID.
#: pro/includes/admin/entries/class-entries-single.php:2472
msgid "Field ID #%s"
msgstr ""

#. translators: %s - choice number.
#: pro/includes/admin/entries/class-entries-single.php:2566
#: pro/includes/admin/entries/class-entries-single.php:2628
#: src/Pro/Admin/Entries/Export/Ajax.php:686
#: src/Pro/Admin/Entries/PrintPreview.php:510
msgid "Choice %s"
msgstr ""

#. translators: %s - item number.
#: pro/includes/admin/entries/class-entries-single.php:2572
msgid "Item %s"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:233
msgid "this field if"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:237
msgid "Show"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:238
msgid "Hide"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:251
#: pro/includes/class-conditional-logic-core.php:339
#: src/Pro/Admin/Builder/Builder.php:112
msgid "Enable Conditional Logic"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:255
#: pro/includes/class-conditional-logic-core.php:344
#: src/Pro/Admin/Builder/Builder.php:90
msgid "How to use Conditional Logic"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:290
msgid "this connection if"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:295
msgid "Process"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:296
msgid "Don't process"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:430
msgid "--- Select Field ---"
msgstr ""

#. translators: %d - field ID.
#: pro/includes/class-conditional-logic-core.php:452
#: src/Pro/Admin/Builder/Notifications/Advanced/Settings.php:251
#: src/Pro/Admin/Entries/Export/Admin.php:265
#: src/Pro/Admin/Entries/Export/Admin.php:391
#: src/Pro/Admin/Entries/Export/Admin.php:418
#: src/Pro/Admin/Entries/Export/Ajax.php:334
#: src/Pro/Admin/Entries/Export/Ajax.php:1312
#: src/Pro/Admin/Entries/Table/DataObjects/FieldColumn.php:37
msgid "Field #%d"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:482
#: src/Pro/Admin/Entries/Export/Admin.php:449
#: src/Pro/Admin/Entries/ListTable.php:1928
#: src/Pro/Admin/Entries/Page.php:709
msgid "is"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:483
#: src/Pro/Admin/Entries/Export/Admin.php:450
#: src/Pro/Admin/Entries/ListTable.php:1931
#: src/Pro/Admin/Entries/Page.php:710
msgid "is not"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:484
msgid "empty"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:485
msgid "not empty"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:495
#: src/Pro/Admin/Entries/Export/Admin.php:447
#: src/Pro/Admin/Entries/ListTable.php:1922
#: src/Pro/Admin/Entries/Page.php:707
msgid "contains"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:496
#: src/Pro/Admin/Entries/Export/Admin.php:448
#: src/Pro/Admin/Entries/ListTable.php:1925
#: src/Pro/Admin/Entries/Page.php:708
msgid "does not contain"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:497
msgid "starts with"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:498
msgid "ends with"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:499
msgid "greater than"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:500
msgid "less than"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:545
msgid "--- Select Choice ---"
msgstr ""

#. translators: %d - choice number.
#. translators: %d - choice ID.
#: pro/includes/class-conditional-logic-core.php:553
#: pro/includes/class-conditional-logic-fields.php:920
#: src/Pro/Admin/Entries/Export/Ajax.php:1524
msgid "Choice %d"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:576
msgid "Create new rule"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:576
msgctxt "Conditional Logic: new rule logic."
msgid "And"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:577
msgid "Delete rule"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:586
msgctxt "Conditional Logic: new rule logic."
msgid "or"
msgstr ""

#: pro/includes/class-conditional-logic-core.php:592
msgid "Add New Group"
msgstr ""

#: pro/includes/class-conditional-logic-fields.php:197
msgid "Smart Logic"
msgstr ""

#: pro/includes/class-conditional-logic-fields.php:531
msgid "An Entry Notification was not sent due to conditional logic."
msgstr ""

#: pro/includes/class-conditional-logic-fields.php:588
msgid "Entry Confirmation stopped by conditional logic."
msgstr ""

#. translators: %s - payment provider.
#: pro/includes/payments/class-payment.php:173
msgid "<p>One of %s's recurring payment plans doesn't have conditional logic, which means that One-Time Payments will never work and were disabled.</p>"
msgstr ""

#. translators: %s - payment provider.
#: pro/includes/payments/class-payment.php:183
msgid "<p>You should check your settings in <strong>Payments » %s</strong>.</p>"
msgstr ""

#: pro/includes/payments/class-payment.php:198
msgid "Enter a plan name"
msgstr ""

#: pro/includes/payments/class-payment.php:199
msgid "Eg: Monthly Subscription"
msgstr ""

#: pro/includes/payments/class-payment.php:200
msgid "Plan Name #{id}"
msgstr ""

#: pro/includes/payments/class-payment.php:201
msgid "Are you sure you want to delete this recurring plan?"
msgstr ""

#: pro/includes/payments/class-payment.php:202
msgid "You must provide a plan name."
msgstr ""

#: pro/includes/payments/class-payment.php:306
msgid "One-Time Payments"
msgstr ""

#: pro/includes/payments/class-payment.php:314
msgid "Enable one-time payments"
msgstr ""

#: pro/includes/payments/class-payment.php:318
msgid "Allow your customers to one-time pay via the form."
msgstr ""

#: pro/includes/payments/class-payment.php:340
msgid "Recurring Payments"
msgstr ""

#: pro/includes/payments/class-payment.php:343
msgid "Add New Plan"
msgstr ""

#: pro/includes/payments/class-payment.php:351
msgid "Enable recurring subscription payments"
msgstr ""

#: pro/includes/payments/class-payment.php:355
msgid "Allow your customer to pay recurringly via the form."
msgstr ""

#. translators: %s - addon title.
#: pro/templates/admin/addons-item.php:30
msgid "%s logo"
msgstr ""

#: pro/templates/admin/addons-item.php:52
msgid "Learn more"
msgstr ""

#: pro/templates/admin/addons-item.php:71
msgid "This addon is not compatible."
msgstr ""

#: pro/templates/admin/addons-item.php:86
msgid "Settings"
msgstr ""

#: pro/templates/admin/empty-states/no-entries.php:17
#: pro/templates/admin/empty-states/no-entry.php:17
msgid "Hi there!"
msgstr ""

#: pro/templates/admin/entries/single-entry/payment-details.php:27
msgid "Payment Details"
msgstr ""

#. translators: %s - payment status.
#: pro/templates/admin/entries/single-entry/payment-details.php:36
msgid "Status: <strong>%s</strong>"
msgstr ""

#. translators: %s - payment type.
#: pro/templates/admin/entries/single-entry/payment-details.php:51
msgid "Type: <strong>%s</strong>"
msgstr ""

#. translators: %s - payment total.
#: pro/templates/admin/entries/single-entry/payment-details.php:71
msgid "Total: <strong>%s</strong>"
msgstr ""

#. translators: %s - payment gateway.
#: pro/templates/admin/entries/single-entry/payment-details.php:84
msgid "Gateway: <strong>%s</strong>"
msgstr ""

#: pro/templates/admin/entries/single-entry/payment-details.php:95
msgctxt "Gateway mode"
msgid "Test"
msgstr ""

#: pro/templates/admin/entries/single-entry/payment-details.php:110
msgid "View Payment"
msgstr ""

#: pro/templates/admin/entries/single-entry/settings.php:28
#: pro/templates/admin/entry-print/legend-start.php:102
msgid "Field Settings"
msgstr ""

#: pro/templates/admin/entries/single-entry/settings.php:44
#: pro/templates/admin/entry-print/legend-start.php:116
msgid "Display Settings"
msgstr ""

#: pro/templates/admin/entry-print/head.php:52
#: src/Pro/Admin/Builder/LicenseAlert.php:165
#: src/Pro/Integrations/Gutenberg/FormSelector.php:165
msgid "Close"
msgstr ""

#: pro/templates/admin/entry-print/legend-start.php:19
msgid "Field Description"
msgstr ""

#: pro/templates/admin/entry-print/legend-start.php:22
msgid "HTML/Content fields"
msgstr ""

#: pro/templates/admin/entry-print/legend-start.php:27
msgid "Maintain Layout"
msgstr ""

#. translators: %d - entry ID.
#: pro/templates/admin/entry-print/legend-start.php:83
msgid "Entry #%d"
msgstr ""

#: pro/templates/admin/entry-print/legend-start.php:98
msgid "Cog"
msgstr ""

#: pro/templates/admin/lite-connect/dashboard-widget-notice-in-progress.php:20
#: src/Pro/Integrations/LiteConnect/Admin.php:184
msgid "Entry Restore in Progress"
msgstr ""

#: pro/templates/admin/lite-connect/dashboard-widget-notice-in-progress.php:23
#: src/Pro/Integrations/LiteConnect/Admin.php:185
msgid "Your entries are currently being imported. This should only take a few minutes. An admin notice will be displayed when the process is complete."
msgstr ""

#: pro/templates/admin/lite-connect/dashboard-widget-notice-restore.php:25
#: src/Pro/Integrations/LiteConnect/Admin.php:213
msgid "Restore Your Form Entries"
msgstr ""

#: pro/templates/admin/lite-connect/dashboard-widget-notice-restore.php:29
msgid "Restore them now and get instant access to reports."
msgstr ""

#: pro/templates/admin/lite-connect/dashboard-widget-notice-restore.php:33
#: src/Pro/Integrations/LiteConnect/Admin.php:215
msgid "Restore Entries Now"
msgstr ""

#: pro/templates/builder/antispam/reformat-warning.php:14
msgid "It appears your keyword filter list is comma-separated. Would you like to reformat it?"
msgstr ""

#: pro/templates/builder/antispam/reformat-warning.php:19
msgid "Yes, Reformat"
msgstr ""

#: pro/templates/builder/context-menu.php:37
msgid "Duplicate Template"
msgstr ""

#: pro/templates/builder/context-menu.php:54
msgid "Duplicate Form"
msgstr ""

#: pro/templates/builder/context-menu.php:68
msgid "Save as Template"
msgstr ""

#: pro/templates/builder/context-menu.php:87
#: src/Pro/Admin/Settings/Access.php:186
#: src/Pro/Integrations/LiteConnect/Admin.php:280
msgid "View Entries"
msgstr ""

#: pro/templates/builder/context-menu.php:100
#: src/Pro/Admin/Entries/Page.php:1309
msgid "View Payments"
msgstr ""

#: pro/templates/builder/context-menu.php:114
msgid "Keyboard Shortcuts"
msgstr ""

#: pro/templates/builder/context-menu.php:128
msgid "What's New"
msgstr ""

#: pro/templates/education/admin/did-you-know.php:25
msgid "Did You Know?"
msgstr ""

#: pro/templates/education/admin/did-you-know.php:33
msgid "Upgrade to Pro"
msgstr ""

#: pro/templates/education/admin/did-you-know.php:35
#: pro/wpforms-pro.php:1066
msgid "Dismiss this message."
msgstr ""

#: pro/templates/education/admin/entries/geolocation.php:29
#: pro/templates/education/admin/entries/geolocation.php:43
#: src/Pro/Admin/Entries/Export/Ajax.php:1015
msgid "Location"
msgstr ""

#: pro/templates/education/admin/entries/geolocation.php:47
#: src/Pro/Admin/Entries/Export/Ajax.php:1024
msgid "Zipcode"
msgstr ""

#: pro/templates/education/admin/entries/geolocation.php:51
#: src/Pro/Admin/Entries/Export/Ajax.php:1033
#: src/Pro/Admin/Entries/Export/Ajax.php:1420
msgid "Country"
msgstr ""

#: pro/templates/education/admin/entries/geolocation.php:55
#: src/Pro/Admin/Entries/Export/Ajax.php:996
msgid "Lat/Long"
msgstr ""

#: pro/templates/education/admin/entries/geolocation.php:63
msgid "Geolocation"
msgstr ""

#: pro/templates/education/admin/entries/geolocation.php:69
msgid "Geolocation allows you to quickly see where your visitors are located!"
msgstr ""

#: pro/templates/education/admin/entries/geolocation.php:71
msgid "You can install the Geolocation addon with just a few clicks!"
msgstr ""

#: pro/templates/education/admin/entries/geolocation.php:80
#: pro/templates/education/admin/entries/user-journey.php:258
#: pro/wpforms-pro.php:551
#: pro/wpforms-pro.php:713
#: pro/wpforms-pro.php:2020
msgid "Activate"
msgstr ""

#: pro/templates/education/admin/entries/geolocation.php:81
#: pro/templates/education/admin/entries/user-journey.php:259
msgid "Install & Activate"
msgstr ""

#: pro/templates/education/admin/entries/geolocation.php:85
msgid "Please upgrade to the PRO plan to unlock Geolocation and more awesome features."
msgstr ""

#: pro/templates/education/admin/entries/geolocation.php:88
#: pro/templates/education/admin/entries/user-journey.php:266
msgid "Upgrade to WPForms Pro"
msgstr ""

#: pro/templates/education/admin/entries/user-journey.php:29
#: pro/templates/education/admin/entries/user-journey.php:239
msgid "User Journey"
msgstr ""

#: pro/templates/education/admin/entries/user-journey.php:61
msgid "Search Results"
msgstr ""

#: pro/templates/education/admin/entries/user-journey.php:84
msgid "Homepage"
msgstr ""

#: pro/templates/education/admin/entries/user-journey.php:107
msgid "Frequently Asked Questions"
msgstr ""

#: pro/templates/education/admin/entries/user-journey.php:130
msgid "About Us"
msgstr ""

#: pro/templates/education/admin/entries/user-journey.php:153
msgid "Meet The Team"
msgstr ""

#: pro/templates/education/admin/entries/user-journey.php:176
msgid "Testimonials"
msgstr ""

#: pro/templates/education/admin/entries/user-journey.php:199
msgid "Contact Us"
msgstr ""

#: pro/templates/education/admin/entries/user-journey.php:223
msgid "Contact form submitted"
msgstr ""

#: pro/templates/education/admin/entries/user-journey.php:227
msgid "User took 7 steps over 14 mins"
msgstr ""

#: pro/templates/education/admin/entries/user-journey.php:245
msgid "Easily trace each visitor’s path through your site, right up to the moment they hit ‘Submit’!"
msgstr ""

#: pro/templates/education/admin/entries/user-journey.php:248
msgid "You can install the User Journey addon with just a few clicks!"
msgstr ""

#: pro/templates/education/admin/entries/user-journey.php:263
msgid "Please upgrade to the PRO plan to unlock User Journey and more awesome features."
msgstr ""

#. translators: %s - provider name.
#: pro/templates/education/admin/settings/integrations-item.php:43
msgid "Integrate %s with WPForms"
msgstr ""

#: pro/templates/education/builder/providers-item.php:45
msgid "Recommended"
msgstr ""

#. translators: %1$s - site URL; %2$s - site title.
#: pro/templates/emails/elegant-footer.php:26
#: pro/templates/emails/modern-footer.php:26
#: pro/templates/emails/tech-footer.php:26
msgid "Sent from <a href=\"%1$s\">%2$s</a>"
msgstr ""

#: pro/templates/fields/file-upload-backend.php:22
#: pro/templates/fields/file-upload-frontend.php:40
msgid "Click or drag a file to this area to upload."
msgid_plural "Click or drag files to this area to upload."
msgstr[0] ""
msgstr[1] ""

#: pro/templates/fields/richtext-single-iframe.php:22
msgid "View Entry &gt; Rich Text field"
msgstr ""

#: pro/templates/frontend/file/file-not-found.php:18
#: pro/templates/frontend/file/file-not-found.php:25
msgid "File Not Found"
msgstr ""

#: pro/templates/frontend/file/file-not-found.php:24
#: pro/templates/frontend/file/file-password-required.php:38
#: pro/templates/frontend/file/file-protected.php:24
#: pro/templates/frontend/file/file-protected.php:25
msgid "File Protected"
msgstr ""

#: pro/templates/frontend/file/file-not-found.php:26
msgid "Sorry, the file you’re looking for could not be found."
msgstr ""

#: pro/templates/frontend/file/file-password-required.php:20
#: pro/templates/frontend/file/file-password-required.php:39
msgid "Password Required"
msgstr ""

#: pro/templates/frontend/file/file-password-required.php:40
msgid "This file is protected. Enter the password to access."
msgstr ""

#: pro/templates/frontend/file/file-password-required.php:43
msgid "Enter Password"
msgstr ""

#: pro/templates/frontend/file/file-password-required.php:44
msgid "Submit"
msgstr ""

#: pro/templates/frontend/file/file-protected.php:18
msgid "Access Denied"
msgstr ""

#: pro/templates/frontend/file/file-protected.php:26
msgid "Sorry, you don’t have access to this file."
msgstr ""

#: pro/wpforms-pro.php:462
msgid "Go to WPForms.com Support page"
msgstr ""

#: pro/wpforms-pro.php:463
msgid "Support"
msgstr ""

#: pro/wpforms-pro.php:479
msgid "Read the documentation"
msgstr ""

#: pro/wpforms-pro.php:480
msgid "Docs"
msgstr ""

#: pro/wpforms-pro.php:498
msgid "Inactive &mdash; You've got a paid version of WPForms"
msgstr ""

#: pro/wpforms-pro.php:573
#: pro/wpforms-pro.php:714
msgid "You cannot activate WPForms Lite because you have a paid version of WPForms activated."
msgstr ""

#: pro/wpforms-pro.php:637
msgid "Verify Key"
msgstr ""

#: pro/wpforms-pro.php:643
msgid "Renew License"
msgstr ""

#: pro/wpforms-pro.php:649
msgid "Remove Key"
msgstr ""

#. translators: $s - license type.
#: pro/wpforms-pro.php:659
msgid "Your license key level is %s."
msgstr ""

#. translators: %s - refresh link.
#: pro/wpforms-pro.php:666
msgid "If your license has been upgraded or is incorrect, then please %1$sforce a refresh%2$s."
msgstr ""

#: pro/wpforms-pro.php:755
msgid "Website URL"
msgstr ""

#: pro/wpforms-pro.php:757
#: pro/wpforms-pro.php:1751
#: src/Pro/Forms/Fields/Url/Field.php:76
msgid "Please enter a valid URL."
msgstr ""

#: pro/wpforms-pro.php:761
msgid "Phone"
msgstr ""

#: pro/wpforms-pro.php:763
#: src/Pro/Forms/Fields/Phone/Field.php:220
#: src/Pro/Forms/Fields/Phone/Field.php:325
msgid "Please enter a valid phone number."
msgstr ""

#: pro/wpforms-pro.php:767
msgid "File Extension"
msgstr ""

#: pro/wpforms-pro.php:769
#: pro/wpforms-pro.php:1754
#: src/Pro/Forms/Fields/FileUpload/Field.php:253
#: src/Pro/Forms/Fields/FileUpload/Field.php:1928
#: src/Pro/Forms/Fields/FileUpload/Field.php:1956
msgid "File type is not allowed."
msgstr ""

#: pro/wpforms-pro.php:773
msgid "File Size"
msgstr ""

#: pro/wpforms-pro.php:775
#: pro/wpforms-pro.php:1755
msgid "File exceeds max size allowed. File was not uploaded."
msgstr ""

#: pro/wpforms-pro.php:779
msgid "File Uploads"
msgstr ""

#. translators: %s - max number of files allowed.
#: pro/wpforms-pro.php:782
#: src/Pro/Forms/Fields/FileUpload/Field.php:249
#: src/Pro/Forms/Fields/FileUpload/Field.php:986
msgid "File uploads exceed the maximum number allowed (%s)."
msgstr ""

#: pro/wpforms-pro.php:788
msgid "Time (12 hour)"
msgstr ""

#: pro/wpforms-pro.php:790
#: pro/wpforms-pro.php:1746
msgid "Please enter time in 12-hour AM/PM format (eg 8:45 AM)."
msgstr ""

#: pro/wpforms-pro.php:794
msgid "Time (24 hour)"
msgstr ""

#: pro/wpforms-pro.php:796
#: pro/wpforms-pro.php:1747
msgid "Please enter time in 24-hour format (eg 22:45)."
msgstr ""

#: pro/wpforms-pro.php:800
msgid "Limit Hours"
msgstr ""

#: pro/wpforms-pro.php:802
#: pro/wpforms-pro.php:1748
#: src/Pro/Forms/Fields/DateTime/Field.php:744
msgid "Please enter time between {minTime} and {maxTime}."
msgstr ""

#: pro/wpforms-pro.php:806
msgid "File Upload Total Size"
msgstr ""

#. translators: %1$s - total size of the selected files in megabytes, %2$s - allowed file upload limit in megabytes.
#: pro/wpforms-pro.php:809
#: pro/wpforms-pro.php:1739
msgid "The total size of the selected files %1$s MB exceeds the allowed limit %2$s MB."
msgstr ""

#: pro/wpforms-pro.php:816
msgid "Password Strength"
msgstr ""

#: pro/wpforms-pro.php:818
#: src/Pro/Forms/Fields/Password/Field.php:450
msgid "A stronger password is required. Consider using upper and lower case letters, numbers, and symbols."
msgstr ""

#: pro/wpforms-pro.php:827
msgid "Disable User Cookies"
msgstr ""

#: pro/wpforms-pro.php:828
msgid "Disable user tracking cookies. This will disable the Related Entries feature and the Form Abandonment addon."
msgstr ""

#: pro/wpforms-pro.php:835
msgid "Disable User Details"
msgstr ""

#: pro/wpforms-pro.php:836
msgid "Disable storage IP addresses and User Agent on all forms. If unchecked, then this can be managed on a form-by-form basis inside the form builder under Settings → General"
msgstr ""

#: pro/wpforms-pro.php:926
msgid "Disable storing entry information in WordPress"
msgstr ""

#: pro/wpforms-pro.php:938
msgid "Disable storing user details (IP address and user agent)"
msgstr ""

#: pro/wpforms-pro.php:963
#: src/Pro/Admin/AdminBarMenu.php:96
#: src/Pro/Admin/DashboardWidget.php:223
#: src/Pro/Admin/Entries/Overview/Page.php:187
#: src/Pro/Admin/Entries/Overview/Page.php:244
#: src/Pro/Admin/Entries/Page.php:958
#: src/Pro/Admin/Entries/Page.php:1094
#: src/Pro/Admin/Settings/Access.php:376
msgid "Entries"
msgstr ""

#. translators: %s - form name.
#: pro/wpforms-pro.php:1040
msgid "New %s Entry"
msgstr ""

#: pro/wpforms-pro.php:1057
msgid "Notifications"
msgstr ""

#: pro/wpforms-pro.php:1059
msgid "Add New Notification"
msgstr ""

#. translators: %s - link to the WPForms.com doc article.
#: pro/wpforms-pro.php:1070
msgid "Notifications are emails sent when a form is submitted. By default, these emails include entry details. For setup and customization options, including a video overview, please <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">see our tutorial</a>."
msgstr ""

#. translators: 1$s, %2$s - links to the WPForms.com doc articles.
#: pro/wpforms-pro.php:1085
msgid "After saving these settings, be sure to <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">test a form submission</a>. This lets you see how emails will look, and to ensure that they <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">are delivered successfully</a>."
msgstr ""

#: pro/wpforms-pro.php:1107
msgid "Enable Notifications"
msgstr ""

#: pro/wpforms-pro.php:1115
msgid "Default Notification"
msgstr ""

#: pro/wpforms-pro.php:1177
msgid "Clone"
msgstr ""

#: pro/wpforms-pro.php:1178
#: pro/wpforms-pro.php:1554
#: src/Pro/Admin/Entries/ListTable.php:801
#: src/Pro/Admin/Entries/ListTable.php:1027
msgid "Delete"
msgstr ""

#: pro/wpforms-pro.php:1179
#: pro/wpforms-pro.php:1555
msgid "Open / Close"
msgstr ""

#: pro/wpforms-pro.php:1191
#: pro/wpforms-pro.php:1566
#: src/Pro/Admin/Entries/Edit.php:561
#: src/Pro/Admin/Entries/ListTable.php:839
msgid "Edit"
msgstr ""

#: pro/wpforms-pro.php:1202
msgid "Send To Email Address"
msgstr ""

#: pro/wpforms-pro.php:1205
msgid "Enter the email address to receive form entry notifications. For multiple notifications, separate email addresses with a comma."
msgstr ""

#: pro/wpforms-pro.php:1224
msgid "CC"
msgstr ""

#: pro/wpforms-pro.php:1226
msgid "Enter the email address to add it to the carbon copy of the form entry notifications. For multiple notifications, separate email addresses with a comma."
msgstr ""

#: pro/wpforms-pro.php:1244
msgid "Email Subject Line"
msgstr ""

#. translators: %s - form name.
#: pro/wpforms-pro.php:1247
msgid "New Entry: %s"
msgstr ""

#: pro/wpforms-pro.php:1264
msgid "From Name"
msgstr ""

#: pro/wpforms-pro.php:1298
msgid "From Email"
msgstr ""

#: pro/wpforms-pro.php:1332
msgid "Reply-To"
msgstr ""

#. translators: %s - <<EMAIL>>.
#: pro/wpforms-pro.php:1336
msgid "Enter the email address or email address with recipient's name in \"First Last %s\" format."
msgstr ""

#: pro/wpforms-pro.php:1357
msgid "Email Message"
msgstr ""

#. translators: %s - all fields smart tag.
#: pro/wpforms-pro.php:1370
msgid "To display all form fields, use the %s Smart Tag."
msgstr ""

#: pro/wpforms-pro.php:1382
msgid "Send"
msgstr ""

#: pro/wpforms-pro.php:1383
msgid "Don't send"
msgstr ""

#: pro/wpforms-pro.php:1385
msgid "this notification if"
msgstr ""

#: pro/wpforms-pro.php:1386
msgid "Email notifications"
msgstr ""

#: pro/wpforms-pro.php:1493
#: pro/wpforms-pro.php:1601
msgid "Thanks for contacting us! We will be in touch with you shortly."
msgstr ""

#: pro/wpforms-pro.php:1506
msgid "Confirmations"
msgstr ""

#: pro/wpforms-pro.php:1507
msgid "Add New Confirmation"
msgstr ""

#: pro/wpforms-pro.php:1512
msgid "Default Confirmation"
msgstr ""

#: pro/wpforms-pro.php:1578
msgid "Confirmation Type"
msgstr ""

#: pro/wpforms-pro.php:1582
msgid "Message"
msgstr ""

#: pro/wpforms-pro.php:1583
msgid "Show Page"
msgstr ""

#: pro/wpforms-pro.php:1584
msgid "Go to URL (Redirect)"
msgstr ""

#: pro/wpforms-pro.php:1599
#: src/Pro/Admin/Builder/Builder.php:96
msgid "Confirmation Message"
msgstr ""

#: pro/wpforms-pro.php:1622
msgid "Automatically scroll to the confirmation message"
msgstr ""

#: pro/wpforms-pro.php:1636
msgid "Confirmation Page"
msgstr ""

#: pro/wpforms-pro.php:1656
msgid "Confirmation Redirect URL"
msgstr ""

#: pro/wpforms-pro.php:1670
msgid "Open confirmation in new tab"
msgstr ""

#: pro/wpforms-pro.php:1687
msgid "Use"
msgstr ""

#: pro/wpforms-pro.php:1688
msgid "Don't use"
msgstr ""

#: pro/wpforms-pro.php:1690
msgid "this confirmation if"
msgstr ""

#: pro/wpforms-pro.php:1691
msgid "Form confirmations"
msgstr ""

#. translators: %s - WPForms.com announcement page URL.
#: pro/wpforms-pro.php:1777
msgid "Conditional logic functionality is now included in the core WPForms plugin! The WPForms Conditional Logic addon can be removed without affecting your forms. For more details <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">read our announcement</a>."
msgstr ""

#. translators: %s - WPForms.com Account dashboard URL.
#: pro/wpforms-pro.php:1818
msgid "Your WPForms license key has expired. In order to continue receiving support and plugin updates you must renew your license key. Please log in to <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">your WPForms.com account</a> to renew your license."
msgstr ""

#: pro/wpforms-pro.php:1938
msgid "Addon auto-updates controlled by WPForms"
msgstr ""

#: pro/wpforms-pro.php:2019
msgid "Active"
msgstr ""

#: pro/wpforms-pro.php:2021
msgid "Inactive"
msgstr ""

#: pro/wpforms-pro.php:2022
msgid "Deactivate"
msgstr ""

#: src/Pro/Access/Capabilities.php:70
#: src/Pro/Admin/Settings/Access.php:96
msgid "Create Forms"
msgstr ""

#: src/Pro/Access/Capabilities.php:71
msgid "View Own Forms"
msgstr ""

#: src/Pro/Access/Capabilities.php:72
msgid "View Others' Forms"
msgstr ""

#: src/Pro/Access/Capabilities.php:73
msgid "Edit Own Forms"
msgstr ""

#: src/Pro/Access/Capabilities.php:74
msgid "Edit Others' Forms"
msgstr ""

#: src/Pro/Access/Capabilities.php:75
msgid "Delete Own Forms"
msgstr ""

#: src/Pro/Access/Capabilities.php:76
msgid "Delete Others' Forms"
msgstr ""

#: src/Pro/Access/Capabilities.php:78
msgid "View Own Forms Entries"
msgstr ""

#: src/Pro/Access/Capabilities.php:79
msgid "View Others' Forms Entries"
msgstr ""

#: src/Pro/Access/Capabilities.php:80
msgid "Edit Own Forms Entries"
msgstr ""

#: src/Pro/Access/Capabilities.php:81
msgid "Edit Others' Forms Entries"
msgstr ""

#: src/Pro/Access/Capabilities.php:82
msgid "Delete Own Forms Entries"
msgstr ""

#: src/Pro/Access/Capabilities.php:83
msgid "Delete Others' Forms Entries"
msgstr ""

#: src/Pro/Access/File.php:285
msgid "Sorry, the password you entered is incorrect."
msgstr ""

#: src/Pro/Admin/Addons/GoogleSheets.php:100
msgid "Important Update for Google Sheets Addon Users"
msgstr ""

#. translators: %1$s - Google Sheets Re-Authentication doc link.
#: src/Pro/Admin/Addons/GoogleSheets.php:103
msgid "The Google Sheets addon for WPForms will be updated soon. All users who are sending entries to Google Sheets will need to <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">update the addon and re-authenticate their Google connection</a> as soon as version 2.0 becomes available to avoid interruptions in service."
msgstr ""

#: src/Pro/Admin/Addons/GoogleSheets.php:123
msgid "Urgent Action Required for Google Sheets Addon Users"
msgstr ""

#. translators: %1$s - Google Sheets Re-Authentication doc link.
#: src/Pro/Admin/Addons/GoogleSheets.php:126
msgid "WPForms Google Sheets addon version 2.0 is now available. All users who are sending entries to Google Sheets need to <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">update the addon and re-authenticate their Google connection</a> as soon as possible to avoid interruptions in service."
msgstr ""

#: src/Pro/Admin/Builder/AntiSpam.php:47
msgid "Your keyword filter list has been reformatted. Please save these changes."
msgstr ""

#: src/Pro/Admin/Builder/Builder.php:52
msgid " - clone"
msgstr ""

#. translators: %1$s - payment provider completed payments. Example: "PayPal Standard completed payments".
#: src/Pro/Admin/Builder/Builder.php:55
msgid "<p>You have just enabled this notification for <strong>%1$s</strong>. Please note that this email notification will only send for <strong>%1$s</strong>.</p><p>If you'd like to set up additional notifications for this form, please see our <a href=\"https://wpforms.com/docs/setup-form-notification-wpforms/\" rel=\"nofollow noopener\" target=\"_blank\">tutorial</a>.</p>"
msgstr ""

#. translators: %1$s - payment provider completed payments. Example: "PayPal Standard completed payments", %2$s - disabled Payment provider completed payments.
#: src/Pro/Admin/Builder/Builder.php:68
msgid "<p>You have just <strong>disabled</strong> the notification for <strong>%2$s</strong> and <strong>enabled</strong> the notification for <strong>%1$s</strong>. Please note that this email notification will only send for <strong>%1$s</strong>.</p><p>If you'd like to set up additional notifications for this form, please see our <a href=\"https://wpforms.com/docs/setup-form-notification-wpforms/\" rel=\"nofollow noopener\" target=\"_blank\">tutorial</a>.</p>"
msgstr ""

#: src/Pro/Admin/Builder/LicenseAlert.php:132
msgid "An active license is needed to access new features & addons, plugin updates (including security improvements), and our world class support!"
msgstr ""

#: src/Pro/Admin/Builder/LicenseAlert.php:135
#: src/Pro/Admin/DashboardWidget.php:673
msgid "Dismiss"
msgstr ""

#: src/Pro/Admin/Builder/LicenseAlert.php:161
msgid "Heads up! A WPForms license key is required."
msgstr ""

#: src/Pro/Admin/Builder/LicenseAlert.php:162
msgid "To create more forms, please verify your WPForms license."
msgstr ""

#: src/Pro/Admin/Builder/LicenseAlert.php:163
#: src/Pro/Admin/Education/StringsTrait.php:57
#: src/Pro/Admin/Education/StringsTrait.php:81
msgid "Enter License Key"
msgstr ""

#: src/Pro/Admin/Builder/LicenseAlert.php:164
msgid "Get WPForms Pro"
msgstr ""

#: src/Pro/Admin/Builder/Notifications/Advanced/EntryCsvAttachment.php:149
msgid "All Fields"
msgstr ""

#: src/Pro/Admin/Builder/Notifications/Advanced/EntryCsvAttachment.php:206
msgid "Enable Entry CSV Attachment"
msgstr ""

#: src/Pro/Admin/Builder/Notifications/Advanced/EntryCsvAttachment.php:222
msgid "File Name"
msgstr ""

#: src/Pro/Admin/Builder/Notifications/Advanced/EntryCsvAttachment.php:261
msgid "Entry Information"
msgstr ""

#: src/Pro/Admin/Builder/Notifications/Advanced/EntryCsvAttachment.php:263
msgid "At least one item must be selected for inclusion in the CSV file."
msgstr ""

#: src/Pro/Admin/Builder/Notifications/Advanced/FileUploadAttachment.php:56
msgid "You do not have any file upload fields"
msgstr ""

#: src/Pro/Admin/Builder/Notifications/Advanced/FileUploadAttachment.php:57
msgid "Restrictions Enabled"
msgstr ""

#: src/Pro/Admin/Builder/Notifications/Advanced/FileUploadAttachment.php:81
msgid "Enable File Upload Attachments"
msgstr ""

#. translators: %s - link to the WPForms.com doc article.
#: src/Pro/Admin/Builder/Notifications/Advanced/FileUploadAttachment.php:114
msgid "<strong>Heads up!</strong> Some email providers have limits on attachment file size. If your visitors upload large files, your notifications may not be delivered. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More</a>"
msgstr ""

#: src/Pro/Admin/Builder/Notifications/Advanced/FileUploadAttachment.php:134
msgid "You allow attaching up to <strong><span class=\"notifications-file-upload-attachment-size\">0</span> MB</strong>"
msgstr ""

#: src/Pro/Admin/Builder/Notifications/Advanced/FileUploadAttachment.php:148
msgid "File Upload Fields"
msgstr ""

#: src/Pro/Admin/Builder/Notifications/Advanced/FileUploadAttachment.php:151
msgid "Select the file upload field(s) containing the files you’d like to receive as attachments."
msgstr ""

#: src/Pro/Admin/Builder/Notifications/Advanced/Settings.php:48
msgid "Field #"
msgstr ""

#: src/Pro/Admin/Builder/Notifications/Advanced/Settings.php:104
msgid "Advanced"
msgstr ""

#: src/Pro/Admin/DashboardWidget.php:219
#: src/Pro/Admin/DashboardWidget.php:633
msgid "Show More"
msgstr ""

#: src/Pro/Admin/DashboardWidget.php:220
msgid "Show Less"
msgstr ""

#: src/Pro/Admin/DashboardWidget.php:222
#: src/Pro/Admin/DashboardWidget.php:349
#: src/Pro/Admin/DashboardWidget.php:397
#: src/Pro/Admin/Entries/Overview/Page.php:347
msgid "Total Entries"
msgstr ""

#: src/Pro/Admin/DashboardWidget.php:224
msgid "Form Entries"
msgstr ""

#: src/Pro/Admin/DashboardWidget.php:318
msgid "Sullie the WPForms mascot"
msgstr ""

#: src/Pro/Admin/DashboardWidget.php:319
msgid "Create Your First Form to Start Collecting Leads"
msgstr ""

#: src/Pro/Admin/DashboardWidget.php:320
msgid "You can use WPForms to build contact forms, surveys, payment forms, and more with just a few clicks."
msgstr ""

#: src/Pro/Admin/DashboardWidget.php:324
msgid "Create Your Form"
msgstr ""

#: src/Pro/Admin/DashboardWidget.php:369
#: src/Pro/Admin/Entries/Overview/Page.php:299
#: src/Pro/Admin/Entries/Overview/Table.php:329
msgid "Reset chart to display all forms"
msgstr ""

#: src/Pro/Admin/DashboardWidget.php:444
msgid "No entries for selected period."
msgstr ""

#: src/Pro/Admin/DashboardWidget.php:586
#: src/Pro/Admin/Entries/Overview/Table.php:333
msgid "Display only this form data in the graph"
msgstr ""

#: src/Pro/Admin/DashboardWidget.php:590
msgid "Reset graph to display all forms"
msgstr ""

#: src/Pro/Admin/DashboardWidget.php:632
msgid "Show all forms"
msgstr ""

#: src/Pro/Admin/DashboardWidget.php:662
msgid "Recommended Plugin:"
msgstr ""

#: src/Pro/Admin/DashboardWidget.php:667
msgid "Install"
msgstr ""

#. translators: %s - WPForms version.
#: src/Pro/Admin/DashboardWidget.php:690
msgid "Welcome to <strong>WPForms %s</strong>"
msgstr ""

#: src/Pro/Admin/DashboardWidget.php:729
#: src/Pro/Admin/Entries/Overview/Page.php:349
msgid "No entries for selected period"
msgstr ""

#: src/Pro/Admin/DashboardWidget.php:730
#: src/Pro/Admin/Entries/Overview/Page.php:350
msgid "Please select a different period or check back later."
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:94
msgid "You can capture email addresses from partial form entries to get more leads. Abandoned cart emails have an average open rate of 45%!"
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:99
msgid "You can easily integrate your forms with 7,000+ useful apps by using WPForms + Zapier."
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:104
msgid "You can integrate your forms to automatically send entries to your most used apps. Perfect for users of Salesforce, Slack, Trello, and 7,000+ others."
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:109
msgid "You can make distraction-free and custom landing pages in WPForms! Perfect for getting more leads."
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:114
msgid "You can build and customize your own professional-looking landing page. A great alternative to Google Forms!"
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:119
msgid "You don’t have to build your forms from scratch. The Form Templates Pack addon gives you access to 150+ additional templates."
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:124
msgid "You can password-protect your forms. Perfect for collecting reviews or success stories from customers!"
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:129
msgid "You can automatically close a form at a specific date and time. Great for applications!"
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:134
msgid "You can generate more fresh content for your website for free by accepting guest blog posts."
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:139
msgid "You can easily add a field to your forms that let users draw their signature then saves it as an image with their entry."
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:144
msgid "You can set up your forms to let your site visitors pick which payment method they want to use."
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:149
msgid "You can increase your revenue by accepting recurring payments on your forms."
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:154
msgid "For added insight into your customers, you can collect your user's city, state, and country behind-the-scenes with Geolocation!"
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:159
msgid "You can let people automatically register as users on your WordPress site. Perfect for things like accepting guest blog posts!"
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:164
msgid "You can limit one form submission per person to avoid duplicate entries. Perfect for applications and giveaway!"
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:169
msgid "You can use NPS Surveys to learn about your visitors. A tactic used by some of the biggest brands around!"
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:174
msgid "If you're planning an event, you can create an RSVP form to stay organized and get higher response rates!"
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:179
msgid "With the Offline Forms addon, you can save data entered into your forms even if the user loses their internet connection."
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:184
msgid "You can accept PayPal on your website — a great way to increase your revenue."
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:189
msgid "You can make money selling digital downloads on your site by using Stripe or PayPal."
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:194
msgid "You can create a simple order form on your site to sell services or products online."
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:199
msgid "You can create surveys or polls and see interactive visual reports of your user's answers."
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:204
msgid "You can add a customer feedback form to your site. Try automatically emailing it out after a sale!"
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:209
msgid "You can add a Likert rating scale to your WordPress forms. Great for measuring your customer’s experience with your business!"
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:214
msgid "You can easily add a poll to your site! Helpful for making business decisions based on your audience's needs."
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:219
msgid "You can create a customer cancellation survey to find out what you can do to improve."
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:224
msgid "WPForms is a great alternative to SurveyMonkey! You can create your first survey or poll today."
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:229
msgid "You can make your forms interactive and easier to complete. A great way to get more leads!"
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:234
msgid "You can easily display survey results graphically. Great for presentations!"
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:239
msgid "You can make your forms feel like a one-on-one conversation and boost conversion rates."
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:244
msgid "You can put a pre-built job application form on your website. Perfect if you’re looking for new employees!"
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:249
msgid "You can automatically send form entries to your Google Calendar. Perfect for appointments!"
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:254
msgid "You can automatically send uploaded files from your form entries to Dropbox for safekeeping and organization!"
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:259
msgid "When a user submits an uploaded file to your form, it can upload automatically to your Google Drive for better organization!"
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:264
msgid "You can get notified via text when someone completes your form! Great for closing deals faster."
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:269
msgid "Save time on invoicing! You can automatically add customers to Quickbooks after they complete a form."
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:274
msgid "You can let users upload videos to your YouTube channel. Perfect for collecting testimonials!"
msgstr ""

#: src/Pro/Admin/Education/Admin/DidYouKnow.php:279
msgid "You can automatically save submitted form info in a free Google Sheets spreadsheet. Great for keeping track of your entries!"
msgstr ""

#: src/Pro/Admin/Education/Admin/Settings/Integrations.php:37
msgid "Almost done! Would you like to refresh the page?"
msgstr ""

#: src/Pro/Admin/Education/Admin/Settings/Integrations.php:38
msgid "Refresh page"
msgstr ""

#. translators: %s - field name.
#: src/Pro/Admin/Education/Builder/Fields.php:171
msgid "%s field"
msgstr ""

#. translators: %s - addon name.
#: src/Pro/Admin/Education/Builder/Fields.php:181
msgid "%s addon"
msgstr ""

#. translators: %s - WPForms.com announcement page URL.
#: src/Pro/Admin/Education/Builder/Fields.php:282
msgid "They will not be present in the published form. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Upgrade now</a> to unlock these features."
msgstr ""

#: src/Pro/Admin/Education/Builder/Fields.php:297
msgid "They will still be visible in the form preview, but will not be present in the published form."
msgstr ""

#: src/Pro/Admin/Education/Builder/Fields.php:302
msgid "Your Form Contains Fields From Inactive Addons"
msgstr ""

#: src/Pro/Admin/Education/Builder/Fields.php:309
msgid "Your Form Contains Fields From Incompatible Addons"
msgstr ""

#. translators: %1$s - addon download URL, %2$s - link to manual installation guide, %3$s - link to contact support.
#: src/Pro/Admin/Education/StringsTrait.php:25
msgid "Could not install the addon. Please <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">download it from wpforms.com</a> and <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">install it manually</a>, or <a href=\"%3$s\" target=\"_blank\" rel=\"noopener noreferrer\">contact support</a> for assistance."
msgstr ""

#: src/Pro/Admin/Education/StringsTrait.php:40
msgid "Incompatible Addon"
msgstr ""

#: src/Pro/Admin/Education/StringsTrait.php:41
msgid "Check for Update"
msgstr ""

#: src/Pro/Admin/Education/StringsTrait.php:56
msgid "To access the %name%, please enter and activate your WPForms license key in the plugin settings."
msgstr ""

#: src/Pro/Admin/Education/StringsTrait.php:63
msgid "To access the %name%, please enter your WPForms license key."
msgstr ""

#. translators: %s - WPForms.com account licenses page URL.
#: src/Pro/Admin/Education/StringsTrait.php:67
msgid "Your key can be found inside the <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">WPForms.com Account Dashboard</a>."
msgstr ""

#: src/Pro/Admin/Education/StringsTrait.php:78
msgid "Your license was activated"
msgstr ""

#: src/Pro/Admin/Education/StringsTrait.php:79
msgid "All templates and fields will be available once the form builder is reloaded."
msgstr ""

#: src/Pro/Admin/Education/StringsTrait.php:80
msgid "Would you like to save and reload now?"
msgstr ""

#: src/Pro/Admin/Education/StringsTrait.php:82
msgid "Activate License"
msgstr ""

#: src/Pro/Admin/Education/StringsTrait.php:84
msgid "Yes, Save and Reload"
msgstr ""

#: src/Pro/Admin/Education/StringsTrait.php:90
msgid "Your WPForms license is expired. To access the %name%, please renew your license."
msgstr ""

#: src/Pro/Admin/Education/StringsTrait.php:96
msgid "Your WPForms license is not active. To access the %name%, please contact support for more details."
msgstr ""

#: src/Pro/Admin/Entries/Ajax/Columns.php:83
msgid "Cannot save columns order."
msgstr ""

#: src/Pro/Admin/Entries/Ajax/Columns.php:100
msgid "Most likely, your session expired. Please reload the page."
msgstr ""

#: src/Pro/Admin/Entries/Ajax/Columns.php:104
msgid "Form ID is missing."
msgstr ""

#: src/Pro/Admin/Entries/Ajax/Columns.php:111
#: src/Pro/Forms/Fields/FileUpload/Field.php:1737
msgid "You are not allowed to perform this action."
msgstr ""

#: src/Pro/Admin/Entries/Edit.php:397
#: src/Pro/Admin/Entries/Edit.php:1088
msgid "Update"
msgstr ""

#: src/Pro/Admin/Entries/Edit.php:398
msgid "Success"
msgstr ""

#: src/Pro/Admin/Entries/Edit.php:399
msgid "Continue Editing"
msgstr ""

#: src/Pro/Admin/Entries/Edit.php:401
msgid "The entry was successfully saved."
msgstr ""

#. translators: %s - file name.
#: src/Pro/Admin/Entries/Edit.php:403
msgid "Are you sure you want to permanently delete the file \"%s\"?"
msgstr ""

#: src/Pro/Admin/Entries/Edit.php:452
msgid "You can't edit this entry because it's in the trash."
msgstr ""

#: src/Pro/Admin/Entries/Edit.php:605
#: src/Pro/Admin/Entries/Edit.php:663
msgid "Edit Entry"
msgstr ""

#: src/Pro/Admin/Entries/Edit.php:610
msgid "Back to Entry"
msgstr ""

#: src/Pro/Admin/Entries/Edit.php:703
msgid "Show Empty Fields"
msgstr ""

#: src/Pro/Admin/Entries/Edit.php:703
msgid "Hide Empty Fields"
msgstr ""

#. translators: %d - field ID.
#: src/Pro/Admin/Entries/Edit.php:971
#: src/Pro/Forms/Fields/EntryPreview/Field.php:878
msgid "Field ID #%d"
msgstr ""

#. translators: %s - form edit URL.
#: src/Pro/Admin/Entries/Edit.php:1053
msgid "You don't have any fields in this form. <a href=\"%s\">Add some!</a>"
msgstr ""

#: src/Pro/Admin/Entries/Edit.php:1063
msgid "You don't have any fields in this form."
msgstr ""

#: src/Pro/Admin/Entries/Edit.php:1105
#: src/Pro/Admin/Entries/Edit.php:1146
msgid "Invalid form."
msgstr ""

#: src/Pro/Admin/Entries/Edit.php:1109
msgid "Invalid Entry."
msgstr ""

#: src/Pro/Admin/Entries/Edit.php:1113
#: src/Pro/Admin/Entries/Page.php:448
#: src/Pro/Admin/Entries/Page.php:528
msgid "You do not have permission to perform this action."
msgstr ""

#: src/Pro/Admin/Entries/Edit.php:1155
msgid "Invalid entry."
msgstr ""

#: src/Pro/Admin/Entries/Edit.php:1194
msgid "Entry has not been saved, please see the fields errors."
msgstr ""

#: src/Pro/Admin/Entries/Edit.php:1295
msgid "Entry edited."
msgstr ""

#. translators: %s - name of the file that has been deleted.
#: src/Pro/Admin/Entries/Edit.php:1710
msgid "The uploaded file \"%s\" has been deleted."
msgstr ""

#: src/Pro/Admin/Entries/Export/Admin.php:64
msgid "Export Entries"
msgstr ""

#: src/Pro/Admin/Entries/Export/Admin.php:66
msgid "Select a form to export entries, then select the fields you would like to include. You can also define search and date filters to further personalize the list of entries you want to retrieve. WPForms will generate a downloadable CSV/XLSX file of your entries."
msgstr ""

#: src/Pro/Admin/Entries/Export/Admin.php:78
#: src/Pro/Admin/Entries/Page.php:1155
msgid "Form Fields"
msgstr ""

#: src/Pro/Admin/Entries/Export/Admin.php:86
msgid "Payment Fields"
msgstr ""

#: src/Pro/Admin/Entries/Export/Admin.php:94
msgid "Additional Information"
msgstr ""

#: src/Pro/Admin/Entries/Export/Admin.php:101
msgid "Custom Date Range"
msgstr ""

#: src/Pro/Admin/Entries/Export/Admin.php:108
#: src/Pro/Admin/Entries/ListTable.php:954
msgid "Select a date range"
msgstr ""

#: src/Pro/Admin/Entries/Export/Admin.php:113
msgid "Clear Start Date"
msgstr ""

#: src/Pro/Admin/Entries/Export/Admin.php:123
#: src/Pro/Admin/Entries/Page.php:1064
msgid "Search"
msgstr ""

#: src/Pro/Admin/Entries/Export/Admin.php:130
msgid "Download Export File"
msgstr ""

#: src/Pro/Admin/Entries/Export/Admin.php:165
msgid "Select a Form"
msgstr ""

#: src/Pro/Admin/Entries/Export/Admin.php:182
msgid "You need to have at least one form before you can use entries export."
msgstr ""

#: src/Pro/Admin/Entries/Export/Admin.php:207
msgid "Status"
msgstr ""

#: src/Pro/Admin/Entries/Export/Admin.php:331
msgid "Export Options"
msgstr ""

#: src/Pro/Admin/Entries/Export/Admin.php:381
#: src/Pro/Admin/Entries/ListTable.php:1889
msgid "Form fields"
msgstr ""

#: src/Pro/Admin/Entries/Export/Admin.php:382
#: src/Pro/Admin/Entries/ListTable.php:1890
msgid "Any form field"
msgstr ""

#: src/Pro/Admin/Entries/Export/Admin.php:404
msgid "Payment fields"
msgstr ""

#: src/Pro/Admin/Entries/Export/Admin.php:410
msgid "No payment fields found"
msgstr ""

#: src/Pro/Admin/Entries/Export/Admin.php:432
#: src/Pro/Admin/Entries/ListTable.php:1905
msgid "Advanced Options"
msgstr ""

#: src/Pro/Admin/Entries/Export/Admin.php:549
#: src/Pro/Admin/Entries/Export/Export.php:214
msgid "Select All"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:383
#: src/Pro/Admin/Entries/Export/Ajax.php:570
#: src/Pro/Admin/Entries/Export/Ajax.php:1385
#: src/Pro/Admin/Entries/Export/Ajax.php:1477
msgid " (modified)"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:603
msgid "Checked"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:862
msgid "Yes"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:862
msgid "No"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1008
msgid "Map"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1026
msgid "Postal"
msgstr ""

#. translators: %d - deleted field ID.
#: src/Pro/Admin/Entries/Export/Ajax.php:1136
msgid "Deleted field #%d"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1227
msgid "Total"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1228
msgid "Currency"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1229
msgid "Gateway"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1230
#: src/Pro/Admin/Entries/Export/Export.php:156
msgid "Type"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1231
msgid "Mode"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1232
msgid "Transaction"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1233
msgid "Customer"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1234
msgid "Subscription"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1235
msgid "Subscription Status"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1270
msgid "Payment Note"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1271
msgid "Subscription Period"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1387
msgid "File"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1403
#: src/Pro/Admin/Entries/Export/Ajax.php:1407
msgid "First"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1404
#: src/Pro/Admin/Entries/Export/Ajax.php:1409
msgid "Last"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1408
msgid "Middle"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1415
msgid "Address Line 1"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1416
msgid "Address Line 2"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1417
msgid "City"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1418
msgid "State"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1419
msgid "Zip/Postal Code"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1426
msgid "Value"
msgstr ""

#: src/Pro/Admin/Entries/Export/Ajax.php:1427
msgid "Quantity"
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:153
#: src/Pro/Admin/Entries/Helpers.php:22
#: src/Pro/Admin/Entries/Table/Facades/Columns.php:137
#: src/Pro/SmartTags/SmartTags.php:26
msgid "Entry ID"
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:154
#: src/Pro/SmartTags/SmartTags.php:27
msgid "Entry Date"
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:155
#: src/Pro/Admin/Entries/Helpers.php:23
#: src/Pro/Admin/Entries/Table/Facades/Columns.php:141
msgid "Entry Notes"
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:157
msgid "Viewed"
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:158
#: src/Pro/Admin/Entries/ListTable.php:260
msgid "Starred"
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:159
#: src/Pro/Admin/Entries/Helpers.php:25
#: src/Pro/Admin/Entries/Table/Facades/Columns.php:154
msgid "User Agent"
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:160
#: src/Pro/Admin/Entries/Table/Facades/Columns.php:151
msgid "User IP"
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:161
msgid "Unique Generated User ID"
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:162
msgid "Payment Status"
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:163
msgid "Payment Gateway Information"
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:164
msgid "Include data of previously deleted fields"
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:168
msgid "Geolocation Details"
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:185
msgid "Export in Microsoft Excel (.xlsx)"
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:188
msgid "Separate dynamic choices into individual columns"
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:198
msgid "There were problems while preparing your export file. Please recheck export settings and try again."
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:199
msgid "You don't have enough capabilities to complete this request."
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:200
msgid "Incorrect form ID has been specified."
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:201
msgid "Incorrect entry ID has been specified."
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:202
msgid "Specified form seems to be broken."
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:203
msgid "Unknown request."
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:204
msgid "Export file cannot be retrieved from a file system."
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:205
msgid "Export file is empty."
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:206
msgid "The form does not have any fields for export."
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:207
msgid "File system is not configured."
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:215
msgid "Generating a list of entries according to your filters."
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:216
msgid "This can take a while. Please wait."
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:217
msgid "No entries found after applying your filters."
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:218
msgid "The file was generated successfully."
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:219
msgid "If the download does not start automatically"
msgstr ""

#: src/Pro/Admin/Entries/Export/Export.php:220
msgid "click here"
msgstr ""

#. translators: %d - dynamic columns count.
#: src/Pro/Admin/Entries/Export/Traits/Export.php:25
msgid "This form has %d dynamic columns. Exporting dynamic columns will increase the size of the exported table."
msgstr ""

#: src/Pro/Admin/Entries/Export/Traits/Export.php:172
msgid "Published"
msgstr ""

#: src/Pro/Admin/Entries/Helpers.php:24
msgid "IP Address"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:234
msgid "All"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:240
msgid "Unread"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:252
#: src/Pro/Admin/Entries/Table/Facades/Columns.php:165
msgid "Payment"
msgid_plural "Payments"
msgstr[0] ""
msgstr[1] ""

#: src/Pro/Admin/Entries/ListTable.php:306
#: src/Pro/Admin/Entries/ListTable.php:861
msgid "Trash"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:471
msgid "Recurring"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:544
msgid "The payment in the Trash."
msgstr ""

#. translators: date and time separator.
#: src/Pro/Admin/Entries/ListTable.php:650
msgid "at"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:737
msgid "Unstar entry"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:737
msgid "Star entry"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:742
msgid "Mark entry unread"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:742
msgid "Mark entry read"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:781
msgid "Restore Form Entry"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:782
#: src/Pro/Admin/Entries/ListTable.php:1022
msgid "Restore"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:800
msgid "Delete Form Entry"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:818
msgid "View Form Entry"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:819
#: src/Pro/Admin/Entries/Overview/Page.php:69
msgid "View"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:838
msgid "Edit Form Entry"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:860
msgid "Trash Form Entry"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:925
#: src/Pro/Admin/Entries/Page.php:1344
msgid "Empty Trash"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:958
msgid "Filter"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:976
msgid "Mark as Read"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:1013
msgid "----------"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:1016
#: src/Pro/AntiSpam/SpamEntry.php:367
#: src/Pro/AntiSpam/SpamEntry.php:665
msgid "Mark as Not Spam"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:1018
#: src/Pro/AntiSpam/SpamEntry.php:563
#: src/Pro/AntiSpam/SpamEntry.php:688
msgid "Mark as Spam"
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:1024
msgid "Move to Trash"
msgstr ""

#. translators: %d - number of processed entries.
#: src/Pro/Admin/Entries/ListTable.php:1733
msgid "%d entry was successfully marked as read."
msgid_plural "%d entries were successfully marked as read."
msgstr[0] ""
msgstr[1] ""

#. translators: %d - number of processed entries.
#: src/Pro/Admin/Entries/ListTable.php:1735
msgid "%d entry was successfully marked as unread."
msgid_plural "%d entries were successfully marked as unread."
msgstr[0] ""
msgstr[1] ""

#. translators: %d - number of processed entries.
#: src/Pro/Admin/Entries/ListTable.php:1737
msgid "%d entry was successfully marked as spam."
msgid_plural "%d entries were successfully marked as spam."
msgstr[0] ""
msgstr[1] ""

#. translators: %d - number of processed entries.
#: src/Pro/Admin/Entries/ListTable.php:1739
msgid "%d entry was successfully marked as not spam."
msgid_plural "%d entries were successfully marked as not spam."
msgstr[0] ""
msgstr[1] ""

#. translators: %d - number of processed entries.
#: src/Pro/Admin/Entries/ListTable.php:1741
msgid "%d entry was successfully starred."
msgid_plural "%d entries were successfully starred."
msgstr[0] ""
msgstr[1] ""

#. translators: %d - number of processed entries.
#: src/Pro/Admin/Entries/ListTable.php:1743
msgid "%d entry was successfully unstarred."
msgid_plural "%d entries were successfully unstarred."
msgstr[0] ""
msgstr[1] ""

#. translators: %d - number of processed entries.
#: src/Pro/Admin/Entries/ListTable.php:1745
msgid "%d entry was successfully deleted."
msgid_plural "%d entries were successfully deleted."
msgstr[0] ""
msgstr[1] ""

#. translators: %d - number of processed entries.
#: src/Pro/Admin/Entries/ListTable.php:1747
msgid "%d entry was successfully trashed."
msgid_plural "%d entries were successfully trashed."
msgstr[0] ""
msgstr[1] ""

#. translators: %d - number of processed entries.
#: src/Pro/Admin/Entries/ListTable.php:1749
msgid "%d entry was successfully restored."
msgid_plural "%d entries were successfully restored."
msgstr[0] ""
msgstr[1] ""

#: src/Pro/Admin/Entries/ListTable.php:1753
msgid "All entries for the currently selected form were successfully deleted."
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:1757
msgid "All entries for the currently selected form were successfully trashed."
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:1786
msgid "No entries found."
msgstr ""

#: src/Pro/Admin/Entries/ListTable.php:1819
msgid "Field"
msgstr ""

#: src/Pro/Admin/Entries/Overview/Page.php:52
msgid "Pagination"
msgstr ""

#: src/Pro/Admin/Entries/Overview/Page.php:55
#: src/Pro/Admin/Entries/Overview/Page.php:223
msgid "Number of forms per page:"
msgstr ""

#: src/Pro/Admin/Entries/Overview/Page.php:72
msgid "Show form templates"
msgstr ""

#: src/Pro/Admin/Entries/Overview/Page.php:298
msgid "All Forms"
msgstr ""

#: src/Pro/Admin/Entries/Overview/Table.php:160
msgid "No forms found."
msgstr ""

#: src/Pro/Admin/Entries/Overview/Table.php:173
msgid "Form Name"
msgstr ""

#: src/Pro/Admin/Entries/Overview/Table.php:174
msgid "Created"
msgstr ""

#: src/Pro/Admin/Entries/Overview/Table.php:175
msgid "Last Entry"
msgstr ""

#: src/Pro/Admin/Entries/Overview/Table.php:176
msgid "All Time"
msgstr ""

#: src/Pro/Admin/Entries/Overview/Table.php:179
msgid "Graph"
msgstr ""

#: src/Pro/Admin/Entries/Page.php:137
msgid "You do not have permission to view this form's entries."
msgstr ""

#: src/Pro/Admin/Entries/Page.php:143
msgid "It looks like the form you are trying to access is no longer available."
msgstr ""

#: src/Pro/Admin/Entries/Page.php:263
#: src/Pro/Admin/Entries/PageOptions.php:64
msgid "Number of entries per page:"
msgstr ""

#: src/Pro/Admin/Entries/Page.php:385
msgid "All entries marked as read."
msgstr ""

#: src/Pro/Admin/Entries/Page.php:436
#: src/Pro/Admin/Entries/Page.php:516
#: src/Pro/Forms/Fields/FileUpload/Field.php:1732
msgid "Your session expired. Please reload the builder."
msgstr ""

#: src/Pro/Admin/Entries/Page.php:443
#: src/Pro/Admin/Entries/Page.php:457
#: src/Pro/Admin/Entries/Page.php:472
#: src/Pro/Admin/Entries/Page.php:523
#: src/Pro/Admin/Entries/Page.php:539
msgid "Something went wrong while performing this action."
msgstr ""

#: src/Pro/Admin/Entries/Page.php:731
msgid "any form field"
msgstr ""

#. translators: %1$s - field name, %2$s - operation, %3$s term.
#: src/Pro/Admin/Entries/Page.php:735
msgid "where %1$s %2$s \"%3$s\""
msgstr ""

#. translators: %s: date.
#: src/Pro/Admin/Entries/Page.php:791
msgid "on %s"
msgstr ""

#. translators: %1$s - date, %2$s - date.
#: src/Pro/Admin/Entries/Page.php:798
msgid "between %1$s and %2$s"
msgstr ""

#. translators: %s - WPForms Builder page.
#: src/Pro/Admin/Entries/Page.php:871
msgid "Whoops, you haven't created a form yet. Want to <a href=\"%s\">give it a go</a>?"
msgstr ""

#: src/Pro/Admin/Entries/Page.php:993
#: src/Pro/Admin/Entries/Page.php:1510
msgid "Storing entry information has been disabled for this form."
msgstr ""

#: src/Pro/Admin/Entries/Page.php:994
msgid "It looks like you don't have any form entries just yet - check back soon!"
msgstr ""

#. translators: %s - number of entries found.
#: src/Pro/Admin/Entries/Page.php:1028
msgid "Found <strong>%s entry</strong>"
msgid_plural "Found <strong>%s entries</strong>"
msgstr[0] ""
msgstr[1] ""

#: src/Pro/Admin/Entries/Page.php:1156
msgid "Entry Meta"
msgstr ""

#: src/Pro/Admin/Entries/Page.php:1282
#: src/Pro/Admin/Entries/Page.php:1390
msgid "Template"
msgstr ""

#: src/Pro/Admin/Entries/Page.php:1288
msgid "Select Form"
msgstr ""

#: src/Pro/Admin/Entries/Page.php:1302
msgid "All Entries"
msgstr ""

#: src/Pro/Admin/Entries/Page.php:1316
msgid "Edit This Template"
msgstr ""

#: src/Pro/Admin/Entries/Page.php:1316
msgid "Edit This Form"
msgstr ""

#: src/Pro/Admin/Entries/Page.php:1323
msgid "Preview Template"
msgstr ""

#: src/Pro/Admin/Entries/Page.php:1323
msgid "Preview Form"
msgstr ""

#: src/Pro/Admin/Entries/Page.php:1330
msgid "Export Filtered"
msgstr ""

#: src/Pro/Admin/Entries/Page.php:1330
msgid "Export All"
msgstr ""

#: src/Pro/Admin/Entries/Page.php:1336
msgid "Mark All Read"
msgstr ""

#: src/Pro/Admin/Entries/Page.php:1344
msgid "Trash All"
msgstr ""

#: src/Pro/Admin/Entries/Page.php:1371
msgid "Open form selector"
msgstr ""

#. translators: %d - number of form entries.
#: src/Pro/Admin/Entries/Page.php:1458
msgid "See %d new entry"
msgid_plural "See %d new entries"
msgstr[0] ""
msgstr[1] ""

#: src/Pro/Admin/Entries/Table/Facades/Columns.php:148
msgid "Entry Type"
msgstr ""

#: src/Pro/Admin/Entries/Table/Facades/Columns.php:157
msgid "Unique User ID"
msgstr ""

#: src/Pro/Admin/Pages/Addons.php:164
msgid "WPForms Addons"
msgstr ""

#: src/Pro/Admin/Pages/Addons.php:167
msgid "Refresh Addons"
msgstr ""

#: src/Pro/Admin/Pages/Addons.php:172
msgid "Search Addons"
msgstr ""

#: src/Pro/Admin/Pages/Addons.php:192
msgid "Activated Addons"
msgstr ""

#: src/Pro/Admin/Pages/Addons.php:204
msgid "All Addons"
msgstr ""

#: src/Pro/Admin/Pages/Addons.php:214
msgid "Sorry, we didn't find any addons that match your criteria."
msgstr ""

#: src/Pro/Admin/Pages/Addons.php:231
msgid "There was an issue retrieving Addons for this site. Please click on the button above to refresh."
msgstr ""

#: src/Pro/Admin/Pages/Addons.php:237
msgid "In order to get access to Addons, you need to resolve your license key errors."
msgstr ""

#: src/Pro/Admin/Pages/Addons.php:243
msgid "Addons have successfully been refreshed."
msgstr ""

#: src/Pro/Admin/Pages/Addons.php:383
msgid "Upgrade Now"
msgstr ""

#: src/Pro/Admin/Pages/Addons.php:393
#: src/Pro/Admin/Pages/Addons.php:395
msgid "Activated"
msgstr ""

#: src/Pro/Admin/Pages/Addons.php:393
#: src/Pro/Admin/Pages/Addons.php:397
msgid "Deactivated"
msgstr ""

#: src/Pro/Admin/Pages/Addons.php:412
msgid "Install Addon"
msgstr ""

#. translators: %1$s - WPForms Pro URL; %2$s - WPForms Pro purchase link.
#: src/Pro/Admin/PluginList.php:607
msgid "<a href=\"%1$s\">Activate WPForms Pro</a> to receive features, updates, and support. Don't have a license? <a target=\"_blank\" href=\"%2$s\" rel=\"noopener noreferrer\">Purchase one now</a>."
msgstr ""

#. translators: %1$s - WPForms Pro URL; %2$s - WPForms Pro purchase link.
#: src/Pro/Admin/PluginList.php:619
msgid "<a href=\"%1$s\">Activate</a> your license to access this update, new features, and support. Don't have a license? <a target=\"_blank\" href=\"%2$s\" rel=\"noopener noreferrer\">Purchase one now</a>."
msgstr ""

#: src/Pro/Admin/PluginList.php:643
msgid "Your WPForms Pro license is expired."
msgstr ""

#: src/Pro/Admin/PluginList.php:644
msgid "Your WPForms Pro license is disabled."
msgstr ""

#: src/Pro/Admin/PluginList.php:645
msgid "Your WPForms Pro license is invalid."
msgstr ""

#: src/Pro/Admin/PluginList.php:646
msgid "Your WPForms Pro license has no activations left."
msgstr ""

#: src/Pro/Admin/PluginList.php:647
msgid "Your WPForms Pro license needs support."
msgstr ""

#. translators: %s - WPForms Pro key support link.
#: src/Pro/Admin/PluginList.php:654
msgid "Before you can activate this key, we'd like to check in with you. Please <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">reach out to support here.</a>"
msgstr ""

#. translators: %s - WPForms Pro renew link.
#: src/Pro/Admin/PluginList.php:665
msgid "<a target=\"_blank\" href=\"%1$s\" rel=\"noopener noreferrer\">Renew now</a> to receive new features, updates, and support."
msgstr ""

#. translators: %1$s - WPForms Pro Changelog link; %2$s - WPForms Pro latest version.
#: src/Pro/Admin/PluginList.php:696
msgid "There is a new version of WPForms available. <a class=\"thickbox open-plugin-details-modal\" href=\"%1$s\" rel=\"noopener noreferrer\">View version %2$s details</a>"
msgstr ""

#. translators: %1$s - WPForms Pro upgrade URL; %2$s - Update now link aria-label attribute.
#: src/Pro/Admin/PluginList.php:711
msgid "or <a href=\"%1$s\" class=\"update-link\" aria-label=\"%2$s\">update now</a>."
msgstr ""

#: src/Pro/Admin/PluginList.php:716
msgid "Update Now"
msgstr ""

#. translators: 1: Plugin name, 2: Version number.
#: src/Pro/Admin/PluginList.php:784
msgid "View %1$s version %2$s details"
msgstr ""

#. translators: 1: Plugin name, 2: Details URL, 3: Link attributes, 4: Version number, 5: Components.
#: src/Pro/Admin/PluginList.php:825
msgid "Sorry, the %1$s addon can't be updated to <a href=\"%2$s\" %3$s>version %4$s</a> because it requires %5$s."
msgstr ""

#. translators: 1: Details URL, 2: Additional link attributes, 3: Version number.
#: src/Pro/Admin/PluginList.php:846
msgid "If no update is available, try <a href=\"%1$s\">checking for new plugin updates</a>."
msgstr ""

#. translators: 1:URL to Update PHP page.
#: src/Pro/Admin/PluginList.php:862
msgid "Learn more about <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">updating PHP</a>."
msgstr ""

#. translators: %s: Plugin name.
#: src/Pro/Admin/PluginList.php:907
msgid "Update %s now"
msgstr ""

#. translators: 1: Plugin name, 2: Details URL, 3: Additional link attributes, 4: Version number, 5: Update URL, 6: Additional link attributes.
#: src/Pro/Admin/PluginList.php:915
msgid "There is a new version of %1$s available. <a href=\"%2$s\" %3$s>View version %4$s details</a>%5$s."
msgstr ""

#: src/Pro/Admin/PluginListDisabler.php:72
msgid "WPForms license is not valid."
msgstr ""

#. translators: %1$s - capability being granted, %2$s - capability(s) required for a capability being granted, %3$s - role a capability is granted to.
#: src/Pro/Admin/Settings/Access.php:77
msgid "In order to give %1$s access, %2$s access is also required."
msgstr ""

#. translators: %1$s - capability being granted, %2$s - capability(s) required for a capability being granted, %3$s - role a capability is granted to.
#: src/Pro/Admin/Settings/Access.php:77
msgid "Would you like to also grant %2$s access to %3$s?"
msgstr ""

#. translators: %1$s - capability being granted, %2$s - capability(s) required for a capability being granted, %3$s - role a capability is granted to.
#: src/Pro/Admin/Settings/Access.php:79
msgid "In order to remove %1$s access, %2$s access is also required to be removed."
msgstr ""

#. translators: %1$s - capability being granted, %2$s - capability(s) required for a capability being granted, %3$s - role a capability is granted to.
#: src/Pro/Admin/Settings/Access.php:79
msgid "Would you like to also remove %2$s access from %3$s?"
msgstr ""

#: src/Pro/Admin/Settings/Access.php:101
msgid "Can create new forms."
msgstr ""

#: src/Pro/Admin/Settings/Access.php:110
msgid "View Forms"
msgstr ""

#: src/Pro/Admin/Settings/Access.php:114
msgid "Can view forms created by <strong>themselves</strong>."
msgstr ""

#: src/Pro/Admin/Settings/Access.php:122
msgid "Can view forms created by <strong>others</strong>."
msgstr ""

#: src/Pro/Admin/Settings/Access.php:131
msgid "Edit Forms"
msgstr ""

#: src/Pro/Admin/Settings/Access.php:135
msgid "Can edit forms created by <strong>themselves</strong>."
msgstr ""

#: src/Pro/Admin/Settings/Access.php:143
msgid "Can edit forms created by <strong>others</strong>."
msgstr ""

#: src/Pro/Admin/Settings/Access.php:152
msgid "Delete Forms"
msgstr ""

#: src/Pro/Admin/Settings/Access.php:156
msgid "Can delete forms created by <strong>themselves</strong>."
msgstr ""

#: src/Pro/Admin/Settings/Access.php:164
msgid "Can delete forms created by <strong>others</strong>."
msgstr ""

#: src/Pro/Admin/Settings/Access.php:190
msgid "Can view entries of forms created by <strong>themselves</strong>."
msgstr ""

#: src/Pro/Admin/Settings/Access.php:198
msgid "Can view entries of forms created by <strong>others</strong>."
msgstr ""

#: src/Pro/Admin/Settings/Access.php:207
msgid "Edit Entries"
msgstr ""

#: src/Pro/Admin/Settings/Access.php:211
msgid "Can edit entries of forms created by <strong>themselves</strong>."
msgstr ""

#: src/Pro/Admin/Settings/Access.php:219
msgid "Can edit entries of forms created by <strong>others</strong>."
msgstr ""

#: src/Pro/Admin/Settings/Access.php:228
msgid "Delete Entries"
msgstr ""

#: src/Pro/Admin/Settings/Access.php:232
msgid "Can delete entries of forms created by <strong>themselves</strong>."
msgstr ""

#: src/Pro/Admin/Settings/Access.php:240
msgid "Can delete entries of forms created by <strong>others</strong>."
msgstr ""

#: src/Pro/Admin/Settings/Access.php:264
#: src/Pro/Admin/Settings/Access.php:287
msgid "Access"
msgstr ""

#: src/Pro/Admin/Settings/Access.php:266
msgid "Save Settings"
msgstr ""

#. translators: %s - WPForms.com access control link.
#: src/Pro/Admin/Settings/Access.php:290
msgid "By default, all permissions are provided only to administrator users. Please see our <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Access Controls documentation</a> for full details."
msgstr ""

#: src/Pro/Admin/Settings/Access.php:348
msgid "Forms"
msgstr ""

#: src/Pro/Admin/Settings/Access.php:348
msgid "Select the user roles that are allowed to manage forms."
msgstr ""

#: src/Pro/Admin/Settings/Access.php:376
msgid "Select the user roles that are allowed to manage entries."
msgstr ""

#: src/Pro/Admin/SiteHealth.php:54
msgid "not detected"
msgstr ""

#. translators: %s - license status.
#: src/Pro/Admin/SiteHealth.php:66
msgid "Your WPForms license is %s"
msgstr ""

#: src/Pro/Admin/SiteHealth.php:71
msgid "Security"
msgstr ""

#: src/Pro/Admin/SiteHealth.php:76
msgid "You have access to updates, addons, new features, and more."
msgstr ""

#: src/Pro/Admin/SiteHealth.php:98
msgid "A valid license is required for following benefits. Please read carefully."
msgstr ""

#: src/Pro/Admin/SiteHealth.php:99
msgid "Plugin and Addon Updates"
msgstr ""

#: src/Pro/Admin/SiteHealth.php:100
msgid "New Features"
msgstr ""

#: src/Pro/Admin/SiteHealth.php:101
msgid "New Addons and Integrations"
msgstr ""

#: src/Pro/Admin/SiteHealth.php:102
msgid "WordPress Compatibility Updates"
msgstr ""

#: src/Pro/Admin/SiteHealth.php:103
msgid "Marketing and Payment Integration Compatibility Updates"
msgstr ""

#: src/Pro/Admin/SiteHealth.php:104
msgid "Security Improvements"
msgstr ""

#: src/Pro/Admin/SiteHealth.php:105
msgid "World Class Support"
msgstr ""

#: src/Pro/Admin/SiteHealth.php:106
msgid "Plugin and Addon Access"
msgstr ""

#: src/Pro/Admin/SiteHealth.php:111
msgid "Login to your WPForms account to update"
msgstr ""

#: src/Pro/Admin/SiteHealth.php:133
msgid "Total entries"
msgstr ""

#: src/Pro/Admin/SiteHealth.php:150
msgid "License status"
msgstr ""

#: src/Pro/Admin/SiteHealth.php:155
msgid "License key type"
msgstr ""

#: src/Pro/Admin/SiteHealth.php:160
msgid "License key location"
msgstr ""

#: src/Pro/AntiSpam/CountryFilter.php:87
msgid "Enable country filter"
msgstr ""

#: src/Pro/AntiSpam/CountryFilter.php:91
msgid "Allow or deny entries from the countries you select."
msgstr ""

#: src/Pro/AntiSpam/CountryFilter.php:98
msgid "Country Filter"
msgstr ""

#: src/Pro/AntiSpam/CountryFilter.php:111
msgid "Allow"
msgstr ""

#: src/Pro/AntiSpam/CountryFilter.php:112
msgid "Deny"
msgstr ""

#: src/Pro/AntiSpam/CountryFilter.php:118
msgid "entries from"
msgstr ""

#: src/Pro/AntiSpam/CountryFilter.php:144
msgid "Country Filter Message"
msgstr ""

#: src/Pro/AntiSpam/CountryFilter.php:149
msgid "Displayed if a visitor from a restricted country tries to submit your form."
msgstr ""

#: src/Pro/AntiSpam/CountryFilter.php:160
msgid "Restrict form entries based on customizable filters or conditions."
msgstr ""

#: src/Pro/AntiSpam/CountryFilter.php:161
msgid "Filtering"
msgstr ""

#: src/Pro/AntiSpam/CountryFilter.php:536
msgid "Sorry, this form does not accept submissions from your country."
msgstr ""

#: src/Pro/AntiSpam/KeywordFilter.php:87
msgid "Enable keyword filter"
msgstr ""

#: src/Pro/AntiSpam/KeywordFilter.php:91
msgid "Block entries that contain one or more keywords you define."
msgstr ""

#. translators: %s - number of phrases.
#: src/Pro/AntiSpam/KeywordFilter.php:100
msgid "Your keyword filter contains %s phrase(s)."
msgstr ""

#: src/Pro/AntiSpam/KeywordFilter.php:104
msgid "Collapse keyword list."
msgstr ""

#: src/Pro/AntiSpam/KeywordFilter.php:105
msgid "Edit keyword list."
msgstr ""

#: src/Pro/AntiSpam/KeywordFilter.php:111
msgid "Keyword Filter List"
msgstr ""

#: src/Pro/AntiSpam/KeywordFilter.php:111
msgid "Keywords that will be blocked if they are found in a form entry."
msgstr ""

#: src/Pro/AntiSpam/KeywordFilter.php:114
msgid "Each word or phrase should be on its own line."
msgstr ""

#: src/Pro/AntiSpam/KeywordFilter.php:119
msgid "Save Changes"
msgstr ""

#: src/Pro/AntiSpam/KeywordFilter.php:133
msgid "Keyword Filter Message"
msgstr ""

#: src/Pro/AntiSpam/KeywordFilter.php:137
msgid "Displayed if a visitor tries to submit an entry that contains a blocked keyword."
msgstr ""

#: src/Pro/AntiSpam/KeywordFilter.php:423
msgid "Sorry, your message can't be submitted because it contains prohibited words."
msgstr ""

#: src/Pro/AntiSpam/SpamEntry.php:280
#: src/Pro/AntiSpam/SpamEntry.php:689
msgid "Spam"
msgstr ""

#: src/Pro/AntiSpam/SpamEntry.php:317
msgid "Entry successfully unmarked as spam."
msgstr ""

#. translators: %s - antispam method.
#: src/Pro/AntiSpam/SpamEntry.php:364
msgid "This entry was marked as spam by %s."
msgstr ""

#. translators: %d - number of days.
#: src/Pro/AntiSpam/SpamEntry.php:397
msgid "%d days"
msgstr ""

#. translators: %s - number of days wrapped in the link to the settings page.
#: src/Pro/AntiSpam/SpamEntry.php:407
msgid "Spam entries older than %s are automatically deleted."
msgstr ""

#: src/Pro/AntiSpam/SpamEntry.php:598
msgid "Marked as not spam."
msgstr ""

#: src/Pro/AntiSpam/SpamEntry.php:666
msgid "Not Spam"
msgstr ""

#: src/Pro/AntiSpam/SpamEntry.php:746
msgid "Empty Spam"
msgstr ""

#. translators: %1$s - WPForms.com license help URL.
#: src/Pro/Emails/LicenseBanner.php:154
msgid "Have questions? <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn why you should always use the latest version of WPForms</a>."
msgstr ""

#: src/Pro/Emails/LicenseBanner.php:172
msgid "Heads Up! Your License is Invalid"
msgstr ""

#: src/Pro/Emails/LicenseBanner.php:174
msgid "Your key either no longer exists or the user associated with the key has been deleted. Please use a different key to continue receiving automatic updates."
msgstr ""

#: src/Pro/Emails/LicenseBanner.php:180
msgid "Purchase a New License"
msgstr ""

#: src/Pro/Emails/LicenseBanner.php:189
msgid "Heads Up! Your License Has Expired"
msgstr ""

#: src/Pro/Emails/LicenseBanner.php:191
msgid "Without an active license, you lose access to plugin updates and new features. Renew your license today so you don’t miss out!"
msgstr ""

#: src/Pro/Emails/LicenseBanner.php:197
msgid "Renew Your License Now"
msgstr ""

#: src/Pro/Emails/LicenseBanner.php:206
msgid "Heads Up! Your License Will Expire Soon"
msgstr ""

#: src/Pro/Emails/LicenseBanner.php:208
msgid "Without an active license, you will lose access to plugin updates and new features. Enable automatic renewal today so you don’t miss out!"
msgstr ""

#: src/Pro/Emails/LicenseBanner.php:214
msgid "Turn on Automatic Renewal"
msgstr ""

#: src/Pro/Forms/Fields/CreditCard/Field.php:109
msgid "Card Number"
msgstr ""

#: src/Pro/Forms/Fields/CreditCard/Field.php:132
msgid "Security Code"
msgstr ""

#: src/Pro/Forms/Fields/CreditCard/Field.php:153
msgid "Name on Card"
msgstr ""

#: src/Pro/Forms/Fields/CreditCard/Field.php:167
msgid "Expiration"
msgstr ""

#: src/Pro/Forms/Fields/CreditCard/Field.php:307
#: src/Pro/Forms/Fields/CreditCard/Frontend.php:62
msgid "This page is insecure. Credit Card field should be used for testing purposes only."
msgstr ""

#: src/Pro/Forms/Fields/CreditCard/Frontend.php:189
msgid "Expiration month"
msgstr ""

#: src/Pro/Forms/Fields/CreditCard/Frontend.php:218
msgid "Expiration year"
msgstr ""

#: src/Pro/Forms/Fields/CustomCaptcha/Builder.php:80
msgid "Custom Captcha field should contain at least one not empty question."
msgstr ""

#: src/Pro/Forms/Fields/CustomCaptcha/Field.php:325
#: src/Pro/Forms/Fields/CustomCaptcha/Field.php:378
msgid "Incorrect answer"
msgstr ""

#: src/Pro/Forms/Fields/CustomCaptcha/Field.php:358
msgid "Incorrect operation"
msgstr ""

#. translators: %1$s - URL to the documentation.
#: src/Pro/Forms/Fields/CustomCaptcha/Field.php:424
msgid "WPForms 1.8.7 core includes Custom Captcha. The Custom Captcha addon will only work on WPForms 1.8.6 and earlier versions. <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More</a>"
msgstr ""

#: src/Pro/Forms/Fields/CustomCaptcha/Frontend.php:83
msgid "Incorrect answer."
msgstr ""

#: src/Pro/Forms/Fields/DateTime/Field.php:174
msgid "Time"
msgstr ""

#: src/Pro/Forms/Fields/DateTime/Field.php:468
msgid "Clear Date"
msgstr ""

#: src/Pro/Forms/Fields/DateTime/Field.php:552
msgid "MM"
msgstr ""

#: src/Pro/Forms/Fields/DateTime/Field.php:553
msgid "DD"
msgstr ""

#: src/Pro/Forms/Fields/DateTime/Field.php:554
msgid "YYYY"
msgstr ""

#: src/Pro/Forms/Fields/DateTime/Frontend.php:70
msgid "Day"
msgstr ""

#: src/Pro/Forms/Fields/DateTime/Frontend.php:71
msgid "Month"
msgstr ""

#: src/Pro/Forms/Fields/DateTime/Frontend.php:72
msgid "Year"
msgstr ""

#: src/Pro/Forms/Fields/EntryPreview/Field.php:954
msgid "Preview not available"
msgstr ""

#: src/Pro/Forms/Fields/EntryPreview/Field.php:1097
msgid "Updating preview…"
msgstr ""

#: src/Pro/Forms/Fields/EntryPreview/Field.php:1125
msgid "Page breaks are required for entry previews to work. If you'd like to remove page breaks, you'll have to first remove the entry preview field."
msgstr ""

#: src/Pro/Forms/Fields/EntryPreview/Field.php:1127
msgid "You can't hide the previous button because it is required for the entry preview field on this page."
msgstr ""

#: src/Pro/Forms/Fields/EntryPreview/Field.php:1147
msgid "Show entry preview after confirmation message"
msgstr ""

#: src/Pro/Forms/Fields/EntryPreview/Field.php:1161
msgid "Preview Style"
msgstr ""

#. translators: %s - link to the Media Library.
#: src/Pro/Forms/Fields/FileUpload/EntriesEdit.php:108
msgid "Please use the default <a href=\"%s\">WordPress Media</a> interface to remove this file."
msgstr ""

#: src/Pro/Forms/Fields/FileUpload/Field.php:244
#: src/Pro/Forms/Fields/FileUpload/Field.php:1556
#: src/Pro/Forms/Fields/FileUpload/Field.php:1611
#: src/Pro/Forms/Fields/FileUpload/Field.php:1639
msgid "Something went wrong, please try again."
msgstr ""

#: src/Pro/Forms/Fields/FileUpload/Field.php:245
msgid "This file was not uploaded."
msgstr ""

#: src/Pro/Forms/Fields/FileUpload/Field.php:254
msgid "File exceeds the max size allowed."
msgstr ""

#. translators: %s - max allowed file size by a server.
#: src/Pro/Forms/Fields/FileUpload/Field.php:256
msgid "File exceeds the upload limit allowed (%s)."
msgstr ""

#: src/Pro/Forms/Fields/FileUpload/Field.php:260
msgid "File upload is in progress. Please submit the form once uploading is completed."
msgstr ""

#: src/Pro/Forms/Fields/FileUpload/Field.php:972
msgid "File(s) not uploaded. Remove and re-attach file(s)."
msgstr ""

#: src/Pro/Forms/Fields/FileUpload/Field.php:1524
msgid "Something went wrong while removing the file."
msgstr ""

#: src/Pro/Forms/Fields/FileUpload/Field.php:1741
msgid "Incorrect usage of this operation."
msgstr ""

#: src/Pro/Forms/Fields/FileUpload/Field.php:1806
msgid "The uploaded file exceeds the upload_max_filesize directive in php.ini."
msgstr ""

#: src/Pro/Forms/Fields/FileUpload/Field.php:1807
msgid "The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form."
msgstr ""

#: src/Pro/Forms/Fields/FileUpload/Field.php:1808
msgid "The uploaded file was only partially uploaded."
msgstr ""

#: src/Pro/Forms/Fields/FileUpload/Field.php:1809
msgid "No file was uploaded."
msgstr ""

#: src/Pro/Forms/Fields/FileUpload/Field.php:1811
msgid "Missing a temporary folder."
msgstr ""

#: src/Pro/Forms/Fields/FileUpload/Field.php:1812
msgid "Failed to write file to disk."
msgstr ""

#: src/Pro/Forms/Fields/FileUpload/Field.php:1813
msgid "File upload stopped by extension."
msgstr ""

#. translators: %s - error text.
#: src/Pro/Forms/Fields/FileUpload/Field.php:1818
msgid "File upload error. %s"
msgstr ""

#. translators: $s - allowed file size in MB.
#: src/Pro/Forms/Fields/FileUpload/Field.php:1899
msgid "File exceeds max size allowed (%s)."
msgstr ""

#: src/Pro/Forms/Fields/FileUpload/Field.php:1923
msgid "File must have an extension."
msgstr ""

#: src/Pro/Forms/Fields/Layout/Builder.php:57
msgid "Enter text for the Layout field label. It will help identify your layout block inside the form builder, but will not be displayed in the form."
msgstr ""

#: src/Pro/Forms/Fields/Layout/Builder.php:68
#: src/Pro/Forms/Fields/Repeater/Builder.php:219
msgid "Display"
msgstr ""

#: src/Pro/Forms/Fields/Layout/Builder.php:80
msgid "Rows - fields are ordered from left to right"
msgstr ""

#: src/Pro/Forms/Fields/Layout/Builder.php:81
msgid "Columns - fields are ordered from top to bottom"
msgstr ""

#: src/Pro/Forms/Fields/Layout/Field.php:84
#: src/Pro/Forms/Fields/Traits/Layout/Builder.php:101
#: src/Pro/Forms/Fields/Traits/Layout/Builder.php:334
msgid "Layout"
msgstr ""

#: src/Pro/Forms/Fields/Layout/Field.php:85
msgid "column, row"
msgstr ""

#. translators: %1$s - current step in multipage form, %2$d - total number of pages.
#: src/Pro/Forms/Fields/Pagebreak/Field.php:187
msgid "Step %1$s of %2$d"
msgstr ""

#: src/Pro/Forms/Fields/Pagebreak/Field.php:262
msgid "Previous"
msgstr ""

#. translators: %1$s - current step in multi-page form, %2$s - total number of pages.
#: src/Pro/Forms/Fields/Pagebreak/Frontend.php:184
msgid "Step %1$s of %2$s"
msgstr ""

#: src/Pro/Forms/Fields/Password/Field.php:87
msgid "Password"
msgstr ""

#: src/Pro/Forms/Fields/Password/Field.php:111
msgid "Confirm Password"
msgstr ""

#: src/Pro/Forms/Fields/Password/Field.php:298
msgid "Field values do not match."
msgstr ""

#: src/Pro/Forms/Fields/Phone/Field.php:54
msgid "Phone number"
msgstr ""

#. translators: %1$s - rating value, %2$s - rating scale.
#: src/Pro/Forms/Fields/Rating/Field.php:201
msgid "Rate %1$d out of %2$d"
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:68
msgid "Field size cannot be changed when used in a repeater."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:69
#: src/Pro/Forms/Fields/Traits/Layout/Builder.php:337
msgid "When a field is placed inside a column, the field size always equals the column width."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:72
msgid "Not Allowed"
msgstr ""

#. translators: %s - Field name.
#: src/Pro/Forms/Fields/Repeater/Builder.php:74
msgid "The %s field can’t be placed inside a Repeater field."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:75
msgid "Only one field is allowed in each column when the Display option is set to Rows."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:76
msgid "You can’t change Display to Rows because only one field per column is allowed."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:77
msgid "Conditional Logic cannot be enabled when the field is inside a Repeater."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:78
msgid "Conditional Logic has been disabled because this field has been placed inside a Repeater."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:79
msgid "Calculation cannot be enabled when the field is inside a Repeater."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:80
msgid "Calculation has been disabled because this field has been placed inside a Repeater."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:81
msgid "When a field is placed inside a Repeater field, Calculation is disabled."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:82
msgid "Are you sure you want to delete the Repeater field? Deleting this field will also delete the fields inside it."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:83
msgid "Conversational Forms cannot be enabled because your form contains a Repeater field."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:84
msgid "The Repeater field cannot be used when Conversational Forms is enabled."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:96
msgid "Lead Forms cannot be enabled because your form contains a Repeater field."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:103
msgid "The Repeater field cannot be used when Lead Forms is enabled."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:106
msgid "Are you sure you want to move this field?"
msgstr ""

#. translators: %s - Addon name.
#: src/Pro/Forms/Fields/Repeater/Builder.php:109
msgid "It's currently mapped to %s, which will be reset if you add this field to a repeater."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:140
msgid "Enter text for the repeater field label. Repeater labels are more like headings and can be hidden in the Advanced Settings."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:220
msgid "Choose whether you want your fields to be repeated as single rows or as a block of fields."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:260
msgid "Button Type"
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:261
msgid "Select the type of buttons to use for the repeater field."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:273
msgid "Buttons with icons"
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:274
msgid "Buttons"
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:275
msgid "Icons with text"
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:276
msgid "Icons"
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:277
msgid "Plain text"
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:316
msgid "Button Labels"
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:317
msgid "Enter text for the repeater field buttons."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:333
#: src/Pro/Forms/Fields/Repeater/Builder.php:348
msgid "Add Label"
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:377
msgid "Limit"
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:378
msgid "Set the minimum and maximum number of times the field can be repeated."
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:395
msgid "Minimum"
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Builder.php:411
msgid "Maximum"
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Field.php:145
msgid "Repeater"
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Field.php:146
msgid "repeater, row, column"
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Field.php:162
msgid "Add"
msgstr ""

#: src/Pro/Forms/Fields/Repeater/Field.php:163
msgid "Remove"
msgstr ""

#: src/Pro/Forms/Fields/Richtext/Field.php:663
msgid "Uploaded to this form"
msgstr ""

#: src/Pro/Forms/Fields/Traits/Layout/Builder.php:101
msgid "Select a Layout."
msgstr ""

#: src/Pro/Forms/Fields/Traits/Layout/Builder.php:109
msgid "Select a predefined layout."
msgstr ""

#. translators: %1$s - WPForms.com URL to a related doc.
#: src/Pro/Forms/Fields/Traits/Layout/Builder.php:316
msgid "We’ve added a new field to help you build advanced form layouts more easily. Give the Layout Field a try! Layout CSS classes are still supported. <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More</a>"
msgstr ""

#. translators: %s - field name.
#: src/Pro/Forms/Fields/Traits/Layout/Builder.php:333
msgid "The %s field can’t be placed inside a Layout field."
msgstr ""

#: src/Pro/Forms/Fields/Traits/Layout/Builder.php:335
msgid "Got it!"
msgstr ""

#: src/Pro/Forms/Fields/Traits/Layout/Builder.php:336
msgid "Field size cannot be changed when used in a layout."
msgstr ""

#: src/Pro/Forms/Fields/Traits/Layout/Builder.php:338
msgid "Don’t Show Again"
msgstr ""

#: src/Pro/Forms/Fields/Traits/Layout/Builder.php:339
msgid "Layouts Have Moved!"
msgstr ""

#: src/Pro/Forms/Fields/Traits/Layout/Builder.php:341
msgid "Conversational Forms cannot be enabled because your form contains a Layout field."
msgstr ""

#: src/Pro/Forms/Fields/Traits/Layout/Builder.php:342
msgid "The Layout field cannot be used when Conversational Forms is enabled."
msgstr ""

#: src/Pro/Forms/Fields/Traits/Layout/Builder.php:343
msgid "Are you sure you want to delete the Layout field? Deleting this field will also delete the fields inside it."
msgstr ""

#: src/Pro/Forms/Fields/Traits/Layout/Builder.php:344
msgid "Cannot be enabled because the Layout field contains Conditional Logic."
msgstr ""

#: src/Pro/Forms/Fields/Traits/Layout/Builder.php:345
msgid "Conditional Logic has been disabled because the Layout field contains Conditional Logic."
msgstr ""

#: src/Pro/Forms/Fields/Traits/Layout/Builder.php:367
msgid "Add Fields"
msgstr ""

#: src/Pro/Integrations/Gutenberg/FormSelector.php:158
msgid "The theme you’ve selected has a background image."
msgstr ""

#: src/Pro/Integrations/Gutenberg/FormSelector.php:159
msgid "In order to use Stock Photos, an image library must be downloaded and installed."
msgstr ""

#: src/Pro/Integrations/Gutenberg/FormSelector.php:160
msgid "It's quick and easy, and you'll only have to do this once."
msgstr ""

#: src/Pro/Integrations/Gutenberg/FormSelector.php:161
msgid "Continue"
msgstr ""

#: src/Pro/Integrations/Gutenberg/FormSelector.php:163
msgid "Installing"
msgstr ""

#: src/Pro/Integrations/Gutenberg/FormSelector.php:164
msgid "Uh oh!"
msgstr ""

#: src/Pro/Integrations/Gutenberg/FormSelector.php:166
msgid "Something went wrong while performing an AJAX request."
msgstr ""

#: src/Pro/Integrations/Gutenberg/FormSelector.php:167
msgid "Choose a Stock Photo"
msgstr ""

#: src/Pro/Integrations/Gutenberg/FormSelector.php:168
msgid "Browse for the perfect image for your form background."
msgstr ""

#: src/Pro/Integrations/Gutenberg/StockPhotos.php:175
msgid "Can't download file."
msgstr ""

#: src/Pro/Integrations/Gutenberg/StockPhotos.php:191
msgid "Can't unzip file."
msgstr ""

#: src/Pro/Integrations/Gutenberg/StockPhotos.php:201
msgid "Can't move unzipped files."
msgstr ""

#: src/Pro/Integrations/Gutenberg/StockPhotos.php:229
msgid "Can't create the stock photos storage directory."
msgstr ""

#: src/Pro/Integrations/LiteConnect/Admin.php:278
msgid "Entry Restore Complete"
msgstr ""

#. translators: %d - number of imported entries.
#: src/Pro/Integrations/LiteConnect/Admin.php:325
msgid "%d entry has been successfully imported."
msgid_plural "%d entries have been successfully imported."
msgstr[0] ""
msgstr[1] ""

#. translators: %d - number of imported entries.
#: src/Pro/Integrations/LiteConnect/Admin.php:339
msgid "%d entry was not imported."
msgid_plural "%d entries were not imported."
msgstr[0] ""
msgstr[1] ""

#: src/Pro/Integrations/LiteConnect/Admin.php:351
msgid "No entries were imported."
msgstr ""

#. translators: %s - WPForms Logs admin page URL.
#: src/Pro/Integrations/LiteConnect/Admin.php:357
msgid "<a href=\"%s\">View log</a> for details."
msgstr ""

#: src/Pro/Integrations/LiteConnect/Admin.php:383
msgid "Unfortunately, there were some issues during the entries importing process. Don't worry - data is not lost. Please try again later."
msgstr ""

#. translators: %d - backed up entries count.
#: src/Pro/Integrations/LiteConnect/Admin.php:550
msgid "%d form entry has been backed up"
msgid_plural "%d form entries have been backed up"
msgstr[0] ""
msgstr[1] ""

#. translators: %s - time when Lite Connect was enabled.
#: src/Pro/Integrations/LiteConnect/Admin.php:563
msgid "since you enabled Lite Connect on %s"
msgstr ""

#: src/Pro/Integrations/LiteConnect/Integration.php:159
msgid "The form no longer exists"
msgstr ""

#: src/Pro/Integrations/LiteConnect/Integration.php:160
msgid "Unknown"
msgstr ""

#: src/Pro/Integrations/LiteConnect/Integration.php:487
msgid "Your form entries have been restored successfully!"
msgstr ""

#. translators: %1$s - WPForms Entries Overview admin page URL.
#: src/Pro/Integrations/LiteConnect/Integration.php:494
msgid "You can view your form entries inside the WordPress Dashboard from the <a href=\"%s\" rel=\"noreferrer noopener\" target=\"_blank\">Entries Overview report</a>."
msgstr ""

#. translators: %s - resume page URL.
#: src/Pro/Migrations/Upgrade143.php:252
msgid "WPForms database upgrade is incomplete, click <a href=\"%s\">here</a> to resume."
msgstr ""

#. translators: %s - entries upgrade page URL.
#: src/Pro/Migrations/Upgrade143.php:255
msgid "WPForms needs to upgrade the database, click <a href=\"%s\">here</a> to start the upgrade."
msgstr ""

#: src/Pro/Migrations/Upgrade143.php:287
msgid "WPForms needs to upgrade the database, click the button below to begin."
msgstr ""

#: src/Pro/Migrations/Upgrade143.php:288
msgid "Run Upgrade"
msgstr ""

#: src/Pro/Migrations/Upgrade143.php:291
msgid "WPForms database upgrade is incomplete, click the button below to resume."
msgstr ""

#: src/Pro/Migrations/Upgrade143.php:292
msgid "Resume Upgrade"
msgstr ""

#: src/Pro/Migrations/Upgrade143.php:297
msgid "Upgrade"
msgstr ""

#: src/Pro/Migrations/Upgrade143.php:299
msgid "Please do not leave this page or close the browser while the upgrade is in progress."
msgstr ""

#. translators: %1$s - total number of entries upgraded, %2$s - total number of entries on site.
#: src/Pro/Migrations/Upgrade143.php:307
msgid "Updated %1$s of %2$s entries."
msgstr ""

#: src/Pro/Migrations/Upgrade143.php:319
msgid "No updates are currently needed."
msgstr ""

#: src/Pro/SmartTags/SmartTags.php:28
msgid "Entry Details URL"
msgstr ""
