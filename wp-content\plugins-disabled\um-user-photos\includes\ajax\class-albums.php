<?php
namespace um_ext\um_user_photos\ajax;

use WP_Filesystem_Base;
use WP_Query;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Albums
 *
 * @package um_ext\um_user_photos\ajax
 */
class Albums {

	/**
	 * Albums constructor.
	 */
	public function __construct() {
		// load images
		add_action( 'wp_ajax_um_user_photos_albums_load_more', array( $this, 'um_user_photos_albums_load_more' ) );
		add_action( 'wp_ajax_nopriv_um_user_photos_albums_load_more', array( $this, 'um_user_photos_albums_load_more' ) );

		// update album
		add_action( 'wp_ajax_update_um_user_photos_album', array( $this, 'update_um_user_photos_album' ) );

		// create new album
		add_action( 'wp_ajax_create_um_user_photos_album', array( $this, 'create_um_user_photos_album' ) );

		// Delete all albums & photos
		add_action( 'wp_ajax_um_user_photos_flush_albums', array( $this, 'flush_albums' ) );

		// New Album modal
		add_action( 'wp_ajax_um_user_photos_new_album', array( $this, 'load_new_album' ) );
		// Edit Album modal
		add_action( 'wp_ajax_um_user_photos_edit_album', array( $this, 'load_edit_album' ) );
		// Delete Album
		add_action( 'wp_ajax_um_user_photos_delete_album', array( $this, 'ajax_delete_album' ) );

		//single album
		add_action( 'wp_ajax_um_user_photos_get_single_album_view', array( $this, 'get_single_album_view' ) );
		add_action( 'wp_ajax_nopriv_um_user_photos_get_single_album_view', array( $this, 'get_single_album_view' ) );

		// Shortcode: [ultimatemember_albums]
		add_action( 'wp_ajax_um_user_photos_get_albums_content', array( $this, 'get_albums_content' ) );
		add_action( 'wp_ajax_nopriv_um_user_photos_get_albums_content', array( $this, 'get_albums_content' ) );
	}

	/**
	 * Load more albums with ajax
	 */
	public function um_user_photos_albums_load_more() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_load_more_albums' ) ) {
			wp_send_json_error( esc_html__( 'Wrong Nonce', 'um-user-photos' ) );
		}

		if ( empty( $_POST['last_id'] ) ) {
			wp_send_json_error( esc_html__( 'Wrong last ID.', 'um-user-photos' ) );
		}

		if ( empty( $_POST['profile'] ) ) {
			wp_send_json_error( esc_html__( 'Wrong user ID.', 'um-user-photos' ) );
		}

		$profile_id = absint( $_POST['profile'] );
		$per_page   = ! empty( $_POST['per_page'] ) ? absint( $_POST['per_page'] ) : UM()->User_Photos()->common()->gallery()->albums_per_page;
		$last_id    = absint( $_POST['last_id'] );

		$is_my_profile = ( is_user_logged_in() && get_current_user_id() === $profile_id );
		if ( ! $is_my_profile && ! um_can_view_profile( $profile_id ) ) {
			wp_send_json_error( esc_html__( 'Nothing to display', 'um-user-photos' ) );
		}

		$args = array(
			'post_type'      => 'um_user_photos',
			'author__in'     => array( $profile_id ),
			'post_status'    => 'publish',
			'posts_per_page' => $per_page,
		);
		$args = apply_filters( 'um_user_photo_query_args', $args, $profile_id );

		$filter_handler = static function ( $where ) use ( $last_id ) {
			global $wpdb;
			return $where . $wpdb->prepare( " AND {$wpdb->posts}.ID < %d", $last_id );
		};

		add_filter( 'posts_where', $filter_handler );
		$albums_query = new WP_Query( $args );
		remove_filter( 'posts_where', $filter_handler );

		$last_id = 0;
		if ( $albums_query->posts ) {
			$last_post = end( $albums_query->posts );
			$last_id   = $last_post->ID;
		}

		$disable_title = UM()->options()->get( 'um_user_photos_disable_title' );

		$content = '';
		if ( ! empty( $albums_query->posts ) ) {
			foreach ( $albums_query->posts as $album ) {
				$photos = get_post_meta( $album->ID, '_photos', true );
				if ( $photos ) {
					$count = count( $photos );
					// translators: %s is a photos count.
					$count_msg = sprintf( _n( '%s Photo', '%s Photos', $count, 'um-user-photos' ), number_format_i18n( $count ) );
				} else {
					$count_msg = false;
				}

				$data_t = array(
					'id'            => $album->ID,
					'title'         => get_the_title( $album->ID ),
					'author'        => $album->post_author,
					'count_msg'     => $count_msg,
					'img'           => UM()->User_Photos()->common()->album()->get_cover( $album->ID ),
					'disable_title' => $disable_title,
					'context'       => 'gallery',
					'url'           => '',
				);

				$content .= UM()->get_template( 'v3/album-block.php', UM_USER_PHOTOS_PLUGIN, $data_t );
			}
		}

		$content = wp_kses( $content, UM()->get_allowed_html( 'templates' ) );
		wp_send_json_success(
			array(
				'content' => UM()->ajax()->esc_html_spaces( $content ),
				'last_id' => $last_id,
			)
		);
	}

	/**
	 * Load edit photo modal content.
	 */
	public function load_new_album() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_get_new_album_form' ) ) {
			wp_send_json_error( __( 'Wrong nonce.', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_add_album() ) {
			wp_send_json_error( __( 'You are not authorized for adding new album.', 'um-user-photos' ) );
		}

		if ( ! empty( $_POST['is_profile_tab'] ) ) {
			UM()->User_Photos()->common()->shortcodes()->is_profile = true;
		}

		$user_id           = get_current_user_id();
		$count_user_photos = UM()->User_Photos()->common()->user()->photos_count( $user_id );
		$limit_user_photos = UM()->User_Photos()->common()->user()->photos_limit( $user_id );
		$limit_per_albums  = UM()->options()->get( 'um_user_photos_album_limit' );
		$limit_per_albums  = ! empty( $limit_per_albums ) ? $limit_per_albums : '';
		$is_profile_tab    = UM()->User_Photos()->common()->shortcodes()->is_profile;

		$wrapper_classes = array( 'um-user-photos-add-album' );
		if ( ! $is_profile_tab ) {
			$wrapper_classes[] = 'um';
		}

		$disable_title = UM()->options()->get( 'um_user_photos_disable_title' );
		$disable_cover = UM()->options()->get( 'um_user_photos_disable_cover' );

		$enable_upload = true;
		if ( false !== $limit_user_photos && absint( $count_user_photos ) >= absint( $limit_user_photos ) ) {
			$enable_upload = false;
		}

		$args_t = compact( 'count_user_photos', 'limit_user_photos', 'limit_per_albums', 'wrapper_classes', 'disable_title', 'disable_cover', 'enable_upload' );
		$html   = UM()->ajax()->esc_html_spaces( UM()->get_template( 'v3/album-add.php', UM_USER_PHOTOS_PLUGIN, $args_t ) );

		wp_send_json_success( $html );
	}

	/**
	 * Load edit album modal content.
	 */
	public function load_edit_album() {
		if ( empty( $_POST['album_id'] ) ) {
			wp_send_json_error( __( 'Wrong album ID.', 'um-user-photos' ) );
		}
		$album_id = absint( $_POST['album_id'] );

		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_edit_album_modal' . $album_id ) ) {
			wp_send_json_error( __( 'Wrong nonce.', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_edit_album( $album_id ) ) {
			wp_send_json_error( __( 'You are not authorized for edit this album.', 'um-user-photos' ) );
		}

		if ( ! empty( $_POST['is_profile_tab'] ) ) {
			UM()->User_Photos()->common()->shortcodes()->is_profile = true;
		}

		$album          = get_post( $album_id );
		$privacy        = get_post_meta( $album_id, '_privacy', true );
		$is_profile_tab = UM()->User_Photos()->common()->shortcodes()->is_profile;

		$wrapper_classes = array( 'um-user-photos-edit-album-form' );
		if ( ! $is_profile_tab ) {
			$wrapper_classes[] = 'um';
		}

		$photos                 = get_post_meta( $album->ID, '_photos', true );
		$disable_title          = UM()->options()->get( 'um_user_photos_disable_title' );
		$disable_cover          = UM()->options()->get( 'um_user_photos_disable_cover' );
		$disable_comment_option = UM()->options()->get( 'um_user_photos_disable_comments' );

		$user_id           = get_current_user_id();
		$count_user_photos = UM()->User_Photos()->common()->user()->photos_count( $user_id );
		$limit_user_photos = UM()->User_Photos()->common()->user()->photos_limit( $user_id );

		$limit_per_albums   = UM()->options()->get( 'um_user_photos_album_limit' );
		$limit_per_albums   = ! empty( $limit_per_albums ) ? $limit_per_albums : '';
		$album_photos_array = get_post_meta( $album->ID, '_photos', true );
		$album_photos_array = ! empty( $album_photos_array ) && is_array( $album_photos_array ) ? $album_photos_array : array();
		$count_per_album    = count( $album_photos_array );

		$enable_upload     = true;
		$album_limit_error = false;
		if ( false !== $limit_user_photos && absint( $count_user_photos ) >= absint( $limit_user_photos ) ) {
			$enable_upload = false;
		}

		if ( ! empty( $limit_per_albums ) && absint( $count_per_album ) >= absint( $limit_per_albums ) ) {
			$enable_upload     = false;
			$album_limit_error = true;
		}

		$uploaded_photos = array();
		if ( ! empty( $photos ) && is_array( $photos ) ) {
			foreach ( $photos as $photo_id ) {
				$image = wp_get_attachment_image_src( $photo_id );
				if ( ! $image ) {
					continue;
				}

				$image_data     = get_post( $photo_id );
				$image_guid     = $image_data->guid;
				$last_slash_pos = strrpos( $image_guid, '/' );
				$image_filename = substr( $image_guid, $last_slash_pos + 1 );

				$disable_comment = get_post_meta( $image_data->ID, '_disable_comment', true );
				$cover_photo     = get_post_meta( $album->ID, '_thumbnail_id', true );

				$args = array(
					'photo_id'     => $photo_id,
					'preview_url'  => $image[0],
					'title'        => $image_data->post_title,
					'filename'     => $image_filename,
					'caption'      => $image_data->post_excerpt,
					'related_link' => $image_data->_link,
				);
				if ( ! $disable_comment_option ) {
					$args['disable_comment'] = ! empty( $disable_comment );
				}
				if ( ! $disable_cover ) {
					$args['cover_photo'] = absint( $cover_photo ) === absint( $image_data->ID );
				}
				$uploaded_photos[] = $args;
			}
		}

		$args_t = compact( 'count_user_photos', 'limit_user_photos', 'limit_per_albums', 'count_per_album', 'album', 'privacy', 'disable_title', 'wrapper_classes', 'enable_upload', 'uploaded_photos', 'album_limit_error' );
		$html   = UM()->ajax()->esc_html_spaces( UM()->get_template( 'v3/album-edit.php', UM_USER_PHOTOS_PLUGIN, $args_t ) );

		wp_send_json_success( $html );
	}

	/**
	 * Single album loading
	 */
	public function get_single_album_view() {
		// phpcs:ignore WordPress.Security.NonceVerification
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_single_album_view' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$album_id = absint( $_POST['id'] );

		if ( empty( $album_id ) ) {
			wp_send_json_error( __( 'Wrong album ID', 'um-user-photos' ) );
		}

		if ( ! UM()->User_Photos()->common()->user()->can_view_album( $album_id ) ) {
			wp_send_json_error( __( 'You are not authorized for this.', 'um-user-photos' ) );
		}

		if ( ! empty( $_POST['is_profile_tab'] ) ) {
			UM()->User_Photos()->common()->shortcodes()->is_profile = true;
		}

		$album  = get_post( $album_id );
		$photos = get_post_meta( $album_id, '_photos', true );
		if ( empty( $photos ) ) {
			$photos = array();
		}
		$count_photos = count( $photos );
		$per_page     = UM()->User_Photos()->common()->gallery()->photos_per_page;
		$photos       = array_slice( $photos, 0, $per_page );

		$is_my_profile = is_user_logged_in() && get_current_user_id() === absint( $album->post_author );
		$count         = count( $photos );
		$disable_title = UM()->options()->get( 'um_user_photos_disable_title' );
		$columns       = UM()->options()->get( 'um_user_photos_images_column' );
		if ( empty( $columns ) ) {
			$columns = 3;
		}

		$is_profile_tab = UM()->User_Photos()->common()->shortcodes()->is_profile;

		$args_t = compact( 'album', 'album_id', 'columns', 'count', 'is_my_profile', 'photos', 'is_profile_tab', 'count_photos', 'disable_title' );
		$html   = UM()->ajax()->esc_html_spaces(
			UM()->get_template(
				'v3/single-album.php',
				UM_USER_PHOTOS_PLUGIN,
				$args_t
			)
		);

		if ( ! empty( $_POST['is_profile_tab'] ) ) {
			UM()->User_Photos()->common()->shortcodes()->is_profile = false;
		}

		wp_send_json_success( $html );
	}

	/**
	 * Create new album
	 */
	public function create_um_user_photos_album() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_add_album' ) ) {
			$message = UM()->frontend()::layouts()::alert(
				__( 'Wrong Nonce', 'um-user-photos' ),
				array(
					'underline' => false,
					'classes'   => array( 'um-user-photos-album-form-submission-error' ),
				)
			);
			wp_send_json_error(
				array(
					'message' => $message,
				)
			);
		}

		if ( ! UM()->User_Photos()->common()->user()->can_add_album() ) {
			$message = UM()->frontend()::layouts()::alert(
				__( 'You are not authorized for adding album.', 'um-user-photos' ),
				array(
					'underline' => false,
					'classes'   => array( 'um-user-photos-album-form-submission-error' ),
				)
			);
			wp_send_json_error(
				array(
					'message' => $message,
				)
			);
		}

		$user_id = get_current_user_id();
		$allowed = UM()->User_Photos()->common()->uploader()->allowed_mime_types;

		$disable_title = UM()->options()->get( 'um_user_photos_disable_title' );
		if ( ! $disable_title ) {
			if ( ! isset( $_POST['album_title'] ) || '' === sanitize_text_field( $_POST['album_title'] ) ) {
				wp_send_json_error(
					array(
						'field'   => 'um-album-title',
						'message' => esc_html__( 'Album title is required', 'um-user-photos' ),
					)
				);
			}
		}

		$photos = array();

		$photos_upload = array();
		$count_files   = 0;
		if ( ! empty( $_POST['album_photos'] ) ) {
			$photos_upload = $_POST['album_photos'];
			$count_files   = count( $photos_upload );
		}

		$limit_user_photos = UM()->User_Photos()->common()->user()->photos_limit( $user_id );
		if ( false !== $limit_user_photos && ! empty( $photos_upload ) ) {
			$count_user_photos = UM()->User_Photos()->common()->user()->photos_count( $user_id );
			$new_count         = $count_user_photos + $count_files;
			if ( $new_count > $limit_user_photos ) {
				// translators: %d is a number for uploading photos limit
				$response = sprintf( esc_html__( 'You cannot upload more photos, you have reached the limit of uploads. Delete old photos or upload few photos. Photo limit = %d', 'um-user-photos' ), $limit_user_photos );
				wp_send_json_error(
					array(
						'field'   => 'um_user_photos_uploader',
						'message' => $response,
					)
				);
			}
		}

		if ( UM()->options()->get( 'um_user_photos_album_limit' ) ) {
			$limit_per_albums = UM()->options()->get( 'um_user_photos_album_limit' );
			if ( $count_files > absint( $limit_per_albums ) ) {
				// translators: %d is a number for uploading photos limit
				$response = sprintf( esc_html__( 'You cannot upload more photos, you have reached the limit of uploads for this album. Delete old photos or upload few photos. Photo limit = %d', 'um-user-photos' ), $limit_per_albums );
				wp_send_json_error(
					array(
						'field'   => 'um_user_photos_uploader',
						'message' => $response,
					)
				);
			}
		}
		if ( ! empty( $photos_upload ) ) {
			foreach ( $photos_upload as $photo_id => $photo ) {
				if ( empty( $photo['title'] ) ) {
					$response = array(
						'message' => esc_html__( 'Image title is required.', 'um-user-photos' ),
						'image'   => array(
							'id'    => $photo_id,
							'field' => 'um-photo-title-' . $photo_id,
						),
					);
					wp_send_json_error( $response );
				}

				if ( empty( $photo['path'] ) ) {
					$response = array(
						'message' => esc_html__( 'Invalid image path.', 'um-user-photos' ),
						'image'   => array(
							'id' => $photo_id,
						),
					);
					wp_send_json_error( $response );
				}

				$path = sanitize_file_name( $photo['path'] );
				if ( ! UM()->ajax()->files()->is_file_author( $path ) ) {
					$response = array(
						'message' => esc_html__( 'You do not have permission to move files. Please check your permissions.', 'um-user-photos' ),
						'image'   => array(
							'id' => $photo_id,
						),
					);
					wp_send_json_error( $response );
				}

				$filename = sanitize_file_name( $photo['filename'] );

				$image_type    = wp_check_filetype( $path, $allowed );
				$filename_type = wp_check_filetype( $filename, $allowed );
				if ( ! $image_type['ext'] || ! $filename_type['ext'] ) {
					$response = array(
						'message' => esc_html__( 'Invalid image mime-type.', 'um-user-photos' ),
						'image'   => array(
							'id' => $photo_id,
						),
					);
					wp_send_json_error( $response );
				}
			}
		}

		um_maybe_unset_time_limit();

		global $wp_filesystem;

		if ( ! $wp_filesystem instanceof WP_Filesystem_Base ) {
			require_once ABSPATH . 'wp-admin/includes/file.php';

			$credentials = request_filesystem_credentials( site_url() );
			WP_Filesystem( $credentials );
		}

		require_once ABSPATH . 'wp-admin/includes/image.php';

		$alb_title = '';
		if ( ! $disable_title ) {
			$alb_title = sanitize_text_field( $_POST['album_title'] );
		}

		$privacy  = sanitize_key( $_POST['album_privacy'] );
		$album_id = wp_insert_post(
			array(
				'post_type'   => 'um_user_photos',
				'post_title'  => $alb_title,
				'post_author' => $user_id,
				'post_status' => 'publish',
				'meta_input'  => array(
					'_privacy' => $privacy,
				),
			)
		);

		if ( empty( $album_id ) || is_wp_error( $album_id ) ) {
			$message = UM()->frontend()::layouts()::alert(
				__( 'Cannot insert album. Unknown error.', 'um-user-photos' ),
				array(
					'underline' => false,
					'classes'   => array( 'um-user-photos-album-form-submission-error' ),
				)
			);

			wp_send_json_error(
				array(
					'message' => $message,
				)
			);
		}

		if ( ! empty( $photos_upload ) ) {
			$wp_upload_dir = wp_upload_dir();
			foreach ( $photos_upload as $photo ) {
				$path       = sanitize_file_name( $photo['path'] );
				$filename   = sanitize_file_name( $photo['filename'] );
				$filename   = wp_unique_filename( $wp_upload_dir['path'], $filename ); // Make the file name unique in the (new) upload directory.
				$image_type = wp_check_filetype( $path, $allowed ); // Don't need checking empty condition below, because had validation above.

				$old_path = wp_normalize_path( UM()->common()->filesystem()->temp_upload_dir . DIRECTORY_SEPARATOR . $path ); // Old path has temp name.
				$new_path = wp_normalize_path( $wp_upload_dir['path'] . DIRECTORY_SEPARATOR . $filename ); // New path we use original filename.

				$move_result = $wp_filesystem->move( $old_path, $new_path, true );
				if ( ! $move_result ) {
					continue;
				}

				$attachment = array(
					'guid'           => $wp_upload_dir['url'] . '/' . basename( $new_path ),
					'post_mime_type' => $image_type['type'],
					'post_title'     => sanitize_text_field( $photo['title'] ),
					'post_content'   => '',
					'post_parent'    => $album_id,
					'post_author'    => get_current_user_id(),
					'post_status'    => 'inherit',
					'post_excerpt'   => sanitize_textarea_field( $photo['caption'] ),
				);

				$attach_id   = wp_insert_attachment( $attachment, $new_path );
				$attach_data = wp_generate_attachment_metadata( $attach_id, $new_path );
				wp_update_attachment_metadata( $attach_id, $attach_data );
				update_post_meta( $attach_id, '_part_of_gallery', 'yes' );

				$photos[] = $attach_id;

				if ( ! empty( $photo['link'] ) ) {
					$link = esc_url_raw( $photo['link'] );
					update_post_meta( $attach_id, '_link', $link );
				}

				if ( ! UM()->options()->get( 'um_user_photos_disable_comments' ) ) {
					$disable_comments = ! empty( $photo['disable_comments'] );
					update_post_meta( $attach_id, '_disable_comment', $disable_comments );
				}

				if ( ! UM()->options()->get( 'um_user_photos_disable_cover' ) ) {
					if ( ! empty( $photo['cover_photo'] ) ) {
						update_post_meta( $album_id, '_thumbnail_id', $attach_id );
					}
				} else {
					delete_post_meta( $album_id, '_thumbnail_id' );
				}

				$order = ! empty( $photo['order'] ) ? $photo['order'] : 0;
				update_post_meta( $attach_id, 'um_order', $order );
			}
		}

		update_post_meta( $album_id, '_photos', $photos );

		do_action( 'um_user_photos_after_album_created', $album_id );
		wp_send_json_success();
	}

	/**
	 * Update album
	 */
	public function update_um_user_photos_album() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_edit_album' ) ) {
			$message = UM()->frontend()::layouts()::alert(
				__( 'Wrong Nonce', 'um-user-photos' ),
				array(
					'underline' => false,
					'classes'   => array( 'um-user-photos-album-form-submission-error' ),
				)
			);
			wp_send_json_error(
				array(
					'message' => $message,
				)
			);
		}

		if ( ! isset( $_POST['album_id'] ) ) {
			$message = UM()->frontend()::layouts()::alert(
				__( 'Invalid album ID.', 'um-user-photos' ),
				array(
					'underline' => false,
					'classes'   => array( 'um-user-photos-album-form-submission-error' ),
				)
			);
			wp_send_json_error(
				array(
					'message' => $message,
				)
			);
		}

		$album_id = absint( $_POST['album_id'] );

		if ( ! UM()->User_Photos()->common()->user()->can_edit_album( $album_id ) ) {
			$message = UM()->frontend()::layouts()::alert(
				__( 'You are not authorized for edit this album.', 'um-user-photos' ),
				array(
					'underline' => false,
					'classes'   => array( 'um-user-photos-album-form-submission-error' ),
				)
			);
			wp_send_json_error(
				array(
					'message' => $message,
				)
			);
		}

		$user_id = get_current_user_id();
		$allowed = UM()->User_Photos()->common()->uploader()->allowed_mime_types;

		$disable_title = UM()->options()->get( 'um_user_photos_disable_title' );
		if ( ! $disable_title ) {
			if ( ! isset( $_POST['album_title'] ) || '' === sanitize_text_field( $_POST['album_title'] ) ) {
				wp_send_json_error(
					array(
						'field'   => 'um-album-title',
						'message' => esc_html__( 'Album title is required', 'um-user-photos' ),
					)
				);
			}
		}

		um_maybe_unset_time_limit();

		$photos        = array();
		$photos_upload = array();
		$count_files   = 0;
		if ( ! empty( $_POST['album_photos'] ) ) {
			$photos_upload = $_POST['album_photos'];
			$count_files   = count( $photos_upload );
		}

		$old_photos_value = get_post_meta( $album_id, '_photos', true );

		$limit_user_photos = UM()->User_Photos()->common()->user()->photos_limit( $user_id );
		if ( false !== $limit_user_photos && ! empty( $photos_upload ) ) {
			$count_user_photos = UM()->User_Photos()->common()->user()->photos_count( $user_id );
			$update_diff       = count( $old_photos_value );
			$new_count         = $count_user_photos - $update_diff + $count_files;
			if ( $new_count > $limit_user_photos ) {
				// translators: %d is a number for uploading photos limit
				$response = sprintf( esc_html__( 'You cannot upload more photos, you have reached the limit of uploads. Delete old photos or upload few photos. Photo limit = %d', 'um-user-photos' ), $limit_user_photos );
				wp_send_json_error(
					array(
						'field'   => 'um_user_photos_uploader',
						'message' => $response,
					)
				);
			}
		}

		if ( UM()->options()->get( 'um_user_photos_album_limit' ) ) {
			$limit_per_albums = UM()->options()->get( 'um_user_photos_album_limit' );
			if ( $count_files > absint( $limit_per_albums ) ) {
				// translators: %d is a number for uploading photos limit
				$response = sprintf( esc_html__( 'You cannot upload more photos, you have reached the limit of uploads for this album. Delete old photos or upload few photos. Photo limit = %d', 'um-user-photos' ), $limit_per_albums );
				wp_send_json_error(
					array(
						'field'   => 'um_user_photos_uploader',
						'message' => $response,
					)
				);
			}
		}
		if ( ! empty( $photos_upload ) ) {
			foreach ( $photos_upload as $photo_id => $photo ) {
				if ( empty( $photo['title'] ) ) {
					$response = array(
						'message' => esc_html__( 'Image title is required.', 'um-user-photos' ),
						'image'   => array(
							'id'    => $photo_id,
							'field' => 'um-photo-title-' . $photo_id,
						),
					);
					wp_send_json_error( $response );
				}

				if ( is_numeric( $photo_id ) ) {
					// This is already uploaded file.
					$attachment = get_post( $photo_id );
					if ( absint( $attachment->post_parent ) !== $album_id ) {
						$response = array(
							'message' => esc_html__( 'Invalid attachment id.', 'um-user-photos' ),
							'image'   => array(
								'id' => $photo_id,
							),
						);
						wp_send_json_error( $response );
					}
				} else {
					// Below validation only for the new uploaded files.
					if ( empty( $photo['path'] ) ) {
						$response = array(
							'message' => esc_html__( 'Invalid image path.', 'um-user-photos' ),
							'image'   => array(
								'id' => $photo_id,
							),
						);
						wp_send_json_error( $response );
					}

					$path = sanitize_file_name( $photo['path'] );
					if ( ! UM()->ajax()->files()->is_file_author( $path ) ) {
						$response = array(
							'message' => esc_html__( 'You do not have permission to move files. Please check your permissions.', 'um-user-photos' ),
							'image'   => array(
								'id' => $photo_id,
							),
						);
						wp_send_json_error( $response );
					}

					$filename = sanitize_file_name( $photo['filename'] );

					$image_type    = wp_check_filetype( $path, $allowed );
					$filename_type = wp_check_filetype( $filename, $allowed );
					if ( ! $image_type['ext'] || ! $filename_type['ext'] ) {
						$response = array(
							'message' => esc_html__( 'Invalid image mime-type.', 'um-user-photos' ),
							'image'   => array(
								'id' => $photo_id,
							),
						);
						wp_send_json_error( $response );
					}
				}
			}
		}

		global $wp_filesystem;

		if ( ! $wp_filesystem instanceof WP_Filesystem_Base ) {
			require_once ABSPATH . 'wp-admin/includes/file.php';

			$credentials = request_filesystem_credentials( site_url() );
			WP_Filesystem( $credentials );
		}

		require_once ABSPATH . 'wp-admin/includes/image.php';

		$alb_title = '';
		if ( ! $disable_title ) {
			$alb_title = sanitize_text_field( $_POST['album_title'] );
		}

		$privacy       = sanitize_key( $_POST['album_privacy'] );
		$update_result = wp_update_post(
			array(
				'ID'         => $album_id,
				'post_title' => $alb_title,
				'meta_input' => array(
					'_privacy' => $privacy,
				),
			)
		);

		if ( empty( $update_result ) || is_wp_error( $update_result ) ) {
			$message = UM()->frontend()::layouts()::alert(
				__( 'Cannot update album. Unknown error.', 'um-user-photos' ),
				array(
					'underline' => false,
					'classes'   => array( 'um-user-photos-album-form-submission-error' ),
				)
			);

			wp_send_json_error(
				array(
					'message' => $message,
				)
			);
		}

		$setting_disable_cover = UM()->options()->get( 'um_user_photos_disable_cover' );

		if ( ! empty( $photos_upload ) ) {
			$wp_upload_dir = wp_upload_dir();
			foreach ( $photos_upload as $photo_id => $photo ) {
				if ( ! is_numeric( $photo_id ) ) {
					// New upload
					$path       = sanitize_file_name( $photo['path'] );
					$filename   = sanitize_file_name( $photo['filename'] );
					$filename   = wp_unique_filename( $wp_upload_dir['path'], $filename ); // Make the file name unique in the (new) upload directory.
					$image_type = wp_check_filetype( $path, $allowed ); // Don't need checking empty condition below, because had validation above.

					$old_path = wp_normalize_path( UM()->common()->filesystem()->temp_upload_dir . DIRECTORY_SEPARATOR . $path ); // Old path has temp name.
					$new_path = wp_normalize_path( $wp_upload_dir['path'] . DIRECTORY_SEPARATOR . $filename ); // New path we use original filename.

					$move_result = $wp_filesystem->move( $old_path, $new_path, true );
					if ( ! $move_result ) {
						continue;
					}

					$attachment = array(
						'guid'           => $wp_upload_dir['url'] . '/' . basename( $new_path ),
						'post_mime_type' => $image_type['type'],
						'post_title'     => sanitize_text_field( $photo['title'] ),
						'post_content'   => '',
						'post_parent'    => $album_id,
						'post_author'    => get_current_user_id(),
						'post_status'    => 'inherit',
						'post_excerpt'   => sanitize_textarea_field( $photo['caption'] ),
					);

					$attach_id   = wp_insert_attachment( $attachment, $new_path );
					$attach_data = wp_generate_attachment_metadata( $attach_id, $new_path );
					wp_update_attachment_metadata( $attach_id, $attach_data );
					update_post_meta( $attach_id, '_part_of_gallery', 'yes' );
				} else {
					// Update old attachment.
					$photo_id    = absint( $photo_id );
					$attach_id   = $photo_id;
					$attachment  = get_post( $photo_id );
					$new_title   = sanitize_text_field( $photo['title'] );
					$new_excerpt = sanitize_textarea_field( $photo['caption'] );

					$args = array();
					if ( $new_title !== $attachment->post_title ) {
						$args['post_title'] = $new_title;
					}
					if ( $new_excerpt !== $attachment->post_excerpt ) {
						$args['post_excerpt'] = $new_excerpt;
					}
					if ( ! empty( $args ) ) {
						$args['ID'] = $photo_id;
						wp_update_post( $args );
					}
				}

				$photos[] = $attach_id;

				if ( ! empty( $photo['link'] ) ) {
					$link = esc_url_raw( $photo['link'] );
					update_post_meta( $attach_id, '_link', $link );
				} else {
					delete_post_meta( $attach_id, '_link' );
				}

				if ( ! UM()->options()->get( 'um_user_photos_disable_comments' ) ) {
					$disable_comments = ! empty( $photo['disable_comments'] );
					update_post_meta( $attach_id, '_disable_comment', $disable_comments );
				}

				$order = ! empty( $photo['order'] ) ? $photo['order'] : 0;
				update_post_meta( $attach_id, 'um_order', $order );

				if ( ! empty( $photo['cover_photo'] ) && ! $setting_disable_cover ) {
					update_post_meta( $album_id, '_thumbnail_id', $attach_id );
				}
			}
		}

		if ( $setting_disable_cover ) {
			delete_post_meta( $album_id, '_thumbnail_id' );
		}

		$attachments_to_delete = array_diff( $old_photos_value, $photos );
		if ( ! empty( $attachments_to_delete ) ) {
			foreach ( $attachments_to_delete as $attachment_id ) {
				wp_delete_attachment( $attachment_id, true );
			}
		}

		update_post_meta( $album_id, '_photos', $photos );

		do_action( 'um_user_photos_after_album_updated', $album_id );

		wp_send_json_success();
	}

	/**
	 * Delete album.
	 */
	public function ajax_delete_album() {
		if ( empty( $_POST['id'] ) ) {
			wp_send_json_error( __( 'Wrong album ID.', 'um-user-photos' ) );
		}

		$id = absint( $_POST['id'] );
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_delete_album' . $id ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		// Album can be deleted only by its own author.
		if ( ! UM()->User_Photos()->common()->user()->can_delete_album( $id ) ) {
			wp_send_json_error( __( 'You cannot delete this album.', 'um-user-photos' ) );
		}

		$album      = get_post( $id );
		$photos     = get_post_meta( $id, '_photos', true );
		$wall_photo = get_post_meta( $id, '_thumbnail_id', true );

		do_action( 'um_user_photos_before_album_deleted', $id );

		$result = wp_delete_post( $id, true );
		if ( empty( $result ) ) {
			wp_send_json_error( __( 'Unknown error. Cannot delete this album.', 'um-user-photos' ) );
		}

		if ( is_array( $photos ) && ! empty( $photos ) ) {
			foreach ( $photos as $photo_id ) {
				wp_delete_attachment( $photo_id, true );
			}
		}
		if ( $wall_photo ) {
			wp_delete_attachment( $wall_photo, true );
		}

		do_action( 'um_user_photos_after_album_deleted', $id, $album );

		wp_send_json_success();
	}

	/**
	 * Delete my all albums & photos
	 *
	 */
	public function flush_albums() {
		if ( ! wp_verify_nonce( $_REQUEST['_wpnonce'], 'um_user_photos_delete_all' ) ) {
			$message = UM()->frontend()::layouts()::alert(
				__( 'Wrong Nonce', 'um-user-photos' ),
				array(
					'underline' => false,
				)
			);
			wp_send_json_error( $message );
		}

		$user_id = get_current_user_id();

		/* Remove photos */
		$photos = new WP_Query(
			array(
				'post_type'      => 'attachment',
				'author__in'     => array( $user_id ),
				'post_status'    => 'inherit',
				'post_mime_type' => 'image',
				'posts_per_page' => -1,
				'meta_query'     => array(
					array(
						'key'     => '_part_of_gallery',
						'value'   => 'yes',
						'compare' => '=',
					),
				),
			)
		);

		if ( $photos->have_posts() ) {
			while ( $photos->have_posts() ) {
				$photos->the_post();
				wp_delete_attachment( get_the_ID(), true );
			}
		}
		wp_reset_postdata();

		/* Remove albums */
		$albums = new WP_Query(
			array(
				'post_type'      => 'um_user_photos',
				'author__in'     => array( $user_id ),
				'posts_per_page' => -1,
			)
		);
		if ( $albums->have_posts() ) {
			while ( $albums->have_posts() ) {
				$albums->the_post();
				wp_delete_post( get_the_ID(), true );
			}
		} // has albums
		wp_reset_postdata();

		do_action( 'um_user_photos_after_user_albums_deleted', $user_id );

		$supporting = __( 'You will be redirected to account in 3 sec.', 'um-user-photos' );
		$message    = UM()->frontend()::layouts()::alert(
			__( 'Deleted Successfully', 'um-user-photos' ),
			array(
				'type'       => 'success',
				'underline'  => false,
				'supporting' => $supporting,
			)
		);
		wp_send_json_success( $message );
	}

	/**
	 * AJAX handler for get_albums_content request
	 */
	public function get_albums_content() {
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_photos_pagination' ) ) {
			wp_send_json_error( __( 'Wrong Nonce', 'um-user-photos' ) );
		}

		$atts = array();
		if ( isset( $_POST['page'] ) ) {
			$atts['page'] = absint( $_POST['page'] );
		}

		if ( isset( $_POST['per_page'] ) ) {
			$atts['per_page'] = absint( $_POST['per_page'] );
		}

		if ( isset( $_POST['column'] ) ) {
			$atts['column'] = absint( $_POST['column'] );
		}

		$output = UM()->ajax()->esc_html_spaces( UM()->User_Photos()->common()->shortcodes()->get_albums_content( $atts ) );
		wp_send_json_success( $output );
	}
}
