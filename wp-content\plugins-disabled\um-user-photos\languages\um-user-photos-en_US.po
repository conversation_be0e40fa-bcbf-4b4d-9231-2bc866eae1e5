# Copyright (C) 2024 Ultimate Member
# This file is distributed under the same license as the Ultimate Member - User Photos plugin.
msgid ""
msgstr ""
"Project-Id-Version: Ultimate Member - User Photos 2.2.0\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/user-photos\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-11-15T01:57:13+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: um-user-photos\n"

#. Plugin Name of the plugin
#: um-user-photos.php
msgid "Ultimate Member - User Photos"
msgstr ""

#. Plugin URI of the plugin
#: um-user-photos.php
msgid "http://ultimatemember.com/extensions/user-photos"
msgstr ""

#. Description of the plugin
#: um-user-photos.php
msgid "Let users add albums and photos"
msgstr ""

#. Author of the plugin
#: um-user-photos.php
msgid "Ultimate Member"
msgstr ""

#. Author URI of the plugin
#: um-user-photos.php
msgid "http://ultimatemember.com/"
msgstr ""

#: includes/admin/class-menu.php:27
#: includes/admin/class-menu.php:28
#: includes/admin/class-metabox.php:29
#: includes/admin/class-settings.php:237
#: includes/common/class-cpt.php:31
#: includes/common/class-cpt.php:32
#: includes/common/class-cpt.php:58
msgid "User Photos"
msgstr ""

#: includes/admin/class-settings.php:35
#: um-user-photos.php:91
msgid "User Photos License Key"
msgstr ""

#: includes/admin/class-settings.php:47
#: includes/admin/class-settings.php:61
msgid "1 column"
msgstr ""

#: includes/admin/class-settings.php:48
#: includes/admin/class-settings.php:62
#: includes/admin/class-settings.php:145
#: includes/admin/class-settings.php:159
msgid "2 columns"
msgstr ""

#: includes/admin/class-settings.php:49
#: includes/admin/class-settings.php:63
#: includes/admin/class-settings.php:146
#: includes/admin/class-settings.php:160
msgid "3 columns"
msgstr ""

#: includes/admin/class-settings.php:50
#: includes/admin/class-settings.php:64
#: includes/admin/class-settings.php:147
#: includes/admin/class-settings.php:161
msgid "4 columns"
msgstr ""

#: includes/admin/class-settings.php:51
#: includes/admin/class-settings.php:65
#: includes/admin/class-settings.php:148
#: includes/admin/class-settings.php:162
msgid "5 columns"
msgstr ""

#: includes/admin/class-settings.php:52
#: includes/admin/class-settings.php:66
#: includes/admin/class-settings.php:149
#: includes/admin/class-settings.php:163
msgid "6 columns"
msgstr ""

#: includes/admin/class-settings.php:54
#: includes/admin/class-settings.php:151
msgid "Album columns"
msgstr ""

#: includes/admin/class-settings.php:68
#: includes/admin/class-settings.php:165
msgid "Photo columns"
msgstr ""

#: includes/admin/class-settings.php:75
#: includes/admin/class-settings.php:172
msgid "Single row"
msgstr ""

#: includes/admin/class-settings.php:76
#: includes/admin/class-settings.php:173
msgid "2 rows"
msgstr ""

#: includes/admin/class-settings.php:77
#: includes/admin/class-settings.php:174
msgid "3 rows"
msgstr ""

#: includes/admin/class-settings.php:78
#: includes/admin/class-settings.php:175
msgid "4 rows"
msgstr ""

#: includes/admin/class-settings.php:79
#: includes/admin/class-settings.php:176
msgid "5 rows"
msgstr ""

#: includes/admin/class-settings.php:80
#: includes/admin/class-settings.php:177
msgid "6 rows"
msgstr ""

#: includes/admin/class-settings.php:82
#: includes/admin/class-settings.php:179
msgid "Photo rows"
msgstr ""

#: includes/admin/class-settings.php:88
#: includes/admin/class-settings.php:185
msgid "Album photos limit"
msgstr ""

#: includes/admin/class-settings.php:89
msgid "Limit of photos in one album. Empty for unlimited."
msgstr ""

#: includes/admin/class-settings.php:95
#: includes/admin/class-settings.php:192
msgid "Default : 350 x 350"
msgstr ""

#: includes/admin/class-settings.php:96
#: includes/admin/class-settings.php:193
msgid "Album Cover size"
msgstr ""

#: includes/admin/class-settings.php:97
#: includes/admin/class-settings.php:105
#: includes/admin/class-settings.php:194
#: includes/admin/class-settings.php:202
msgid "You will need to regenerate thumbnails once this value is changed"
msgstr ""

#: includes/admin/class-settings.php:103
#: includes/admin/class-settings.php:200
msgid "Default : 250 x 250"
msgstr ""

#: includes/admin/class-settings.php:104
#: includes/admin/class-settings.php:201
msgid "Photo thumbnail size"
msgstr ""

#: includes/admin/class-settings.php:111
msgid "Album cover photo"
msgstr ""

#: includes/admin/class-settings.php:113
msgid "If disabled, then first album photo will be applied as cover."
msgstr ""

#: includes/admin/class-settings.php:118
#: templates/modal/add-album.php:26
#: templates/modal/edit-album.php:50
#: templates/v3/album-add.php:34
#: templates/v3/album-edit.php:46
msgid "Album title"
msgstr ""

#: includes/admin/class-settings.php:120
#: includes/admin/class-settings.php:216
msgid "Title field will be hidden."
msgstr ""

#: includes/admin/class-settings.php:125
msgid "Photo comments & likes"
msgstr ""

#: includes/admin/class-settings.php:127
msgid "Prevents creation of comments and likes on the single photo."
msgstr ""

#: includes/admin/class-settings.php:132
msgid "Photos in WordPress Media"
msgstr ""

#: includes/admin/class-settings.php:134
msgid "Display photos uploaded to albums in the WordPress Media."
msgstr ""

#: includes/admin/class-settings.php:144
#: includes/admin/class-settings.php:158
msgid "No. of columns"
msgstr ""

#: includes/admin/class-settings.php:186
msgid "Limit of photos in one album. Empty for unlimited. There is a one-time photo upload limit on your server - max_file_uploads = "
msgstr ""

#: includes/admin/class-settings.php:208
msgid "Disable cover photo"
msgstr ""

#: includes/admin/class-settings.php:209
msgid "Album cover field will be hidden."
msgstr ""

#: includes/admin/class-settings.php:215
msgid "Disable album title"
msgstr ""

#: includes/admin/class-settings.php:222
#: includes/common/class-uploader.php:136
#: includes/common/class-uploader.php:300
#: templates/modal/edit-image.php:47
#: templates/v3/modal/edit-image.php:48
msgid "Disable comments"
msgstr ""

#: includes/admin/class-settings.php:223
msgid "Disable comment & Like feature"
msgstr ""

#: includes/admin/class-settings.php:224
msgid "Disable comments and like features"
msgstr ""

#: includes/admin/class-settings.php:230
msgid "Display photos in Media Library"
msgstr ""

#: includes/admin/class-settings.php:231
msgid "Display photos uploaded to albums in the Media Library"
msgstr ""

#: includes/admin/class-settings.php:238
msgid "Provides settings for controlling User Photos extension on your site."
msgstr ""

#: includes/admin/templates/role/photos.php:24
msgid "User Photos feature"
msgstr ""

#: includes/admin/templates/role/photos.php:25
msgid "Allows this role to have user photos feature"
msgstr ""

#: includes/admin/templates/role/photos.php:32
msgid "User gallery limit"
msgstr ""

#: includes/admin/templates/role/photos.php:33
msgid "The number of photos that the user can add. 0 or empty for unlimited upload"
msgstr ""

#: includes/ajax/class-ajax-v2.php:113
#: includes/ajax/class-ajax-v2.php:137
#: includes/ajax/class-ajax-v2.php:260
#: includes/ajax/class-ajax-v2.php:354
#: includes/ajax/class-ajax-v2.php:397
#: includes/ajax/class-ajax-v2.php:543
#: includes/ajax/class-ajax-v2.php:704
#: includes/ajax/class-ajax-v2.php:758
#: includes/ajax/class-ajax-v2.php:819
#: includes/ajax/class-ajax-v2.php:896
#: includes/ajax/class-ajax-v2.php:952
#: includes/ajax/class-ajax-v2.php:1092
#: includes/ajax/class-ajax-v2.php:1148
#: includes/ajax/class-ajax-v2.php:1170
#: includes/ajax/class-ajax-v2.php:1191
#: includes/ajax/class-ajax-v2.php:1272
#: includes/ajax/class-ajax-v2.php:1304
#: includes/ajax/class-ajax-v2.php:1336
#: includes/ajax/class-ajax-v2.php:1368
#: includes/ajax/class-ajax-v2.php:1400
#: includes/ajax/class-ajax-v2.php:1434
#: includes/ajax/class-ajax-v2.php:1469
#: includes/ajax/class-ajax-v2.php:1499
#: includes/ajax/class-albums.php:56
#: includes/ajax/class-albums.php:281
#: includes/ajax/class-albums.php:339
#: includes/ajax/class-albums.php:586
#: includes/ajax/class-albums.php:903
#: includes/ajax/class-albums.php:943
#: includes/ajax/class-albums.php:1014
#: includes/ajax/class-comments.php:62
#: includes/ajax/class-comments.php:92
#: includes/ajax/class-comments.php:127
#: includes/ajax/class-comments.php:180
#: includes/ajax/class-comments.php:227
#: includes/ajax/class-comments.php:304
#: includes/ajax/class-comments.php:345
#: includes/ajax/class-gallery.php:29
#: includes/ajax/class-photos.php:56
#: includes/ajax/class-photos.php:91
#: includes/ajax/class-photos.php:128
#: includes/ajax/class-photos.php:267
#: includes/ajax/class-photos.php:347
#: includes/ajax/class-photos.php:462
#: includes/ajax/class-photos.php:537
msgid "Wrong Nonce"
msgstr ""

#: includes/ajax/class-ajax-v2.php:147
#: includes/ajax/class-ajax-v2.php:153
#: includes/ajax/class-albums.php:73
#: includes/ajax/class-photos.php:361
#: includes/ajax/class-photos.php:467
#: includes/common/class-shortcodes-v2.php:88
#: includes/common/class-shortcodes-v2.php:93
#: includes/common/class-shortcodes-v2.php:156
#: includes/common/class-shortcodes-v2.php:161
#: templates/gallery.php:58
#: templates/photos.php:50
#: templates/single-album.php:30
#: templates/v3/gallery-photos.php:68
#: templates/v3/gallery.php:108
#: templates/v3/single-album.php:72
msgid "Nothing to display"
msgstr ""

#: includes/ajax/class-ajax-v2.php:273
#: includes/ajax/class-photos.php:498
msgid "Wrong photo ID."
msgstr ""

#: includes/ajax/class-ajax-v2.php:278
#: includes/ajax/class-ajax-v2.php:309
#: includes/ajax/class-ajax-v2.php:331
#: includes/ajax/class-albums.php:142
#: includes/ajax/class-albums.php:189
#: includes/ajax/class-photos.php:503
msgid "Wrong nonce."
msgstr ""

#: includes/ajax/class-ajax-v2.php:283
msgid "You are not authorized to edit this photo."
msgstr ""

#: includes/ajax/class-ajax-v2.php:304
#: includes/ajax/class-ajax-v2.php:699
#: includes/ajax/class-albums.php:184
#: includes/ajax/class-albums.php:898
#: includes/ajax/class-photos.php:459
msgid "Wrong album ID."
msgstr ""

#: includes/ajax/class-ajax-v2.php:314
msgid "You are not authorized to edit this album."
msgstr ""

#: includes/ajax/class-ajax-v2.php:359
#: includes/ajax/class-albums.php:287
msgid "Wrong album ID"
msgstr ""

#: includes/ajax/class-ajax-v2.php:363
#: includes/ajax/class-ajax-v2.php:401
#: includes/ajax/class-ajax-v2.php:553
#: includes/ajax/class-ajax-v2.php:1102
#: includes/ajax/class-ajax-v2.php:1342
#: includes/ajax/class-albums.php:291
#: includes/ajax/class-photos.php:134
#: includes/ajax/class-photos.php:545
#: includes/common/class-activity.php:459
msgid "You are not authorized for this."
msgstr ""

#: includes/ajax/class-ajax-v2.php:417
#: includes/ajax/class-ajax-v2.php:559
#: includes/ajax/class-albums.php:376
#: includes/ajax/class-albums.php:640
msgid "Album title is required"
msgstr ""

#: includes/ajax/class-ajax-v2.php:423
#: includes/ajax/class-ajax-v2.php:568
#: includes/ajax/class-ajax-v2.php:572
#: includes/ajax/class-ajax-v2.php:849
#: includes/ajax/class-ajax-v2.php:851
#: includes/ajax/class-ajax-v2.php:960
msgid "Invalid request"
msgstr ""

#. translators: %s is a number for uploading photos limit
#. translators: %d is a number for uploading photos limit
#: includes/ajax/class-ajax-v2.php:457
#: includes/ajax/class-ajax-v2.php:607
#: includes/ajax/class-albums.php:397
#: includes/ajax/class-albums.php:665
msgid "You cannot upload more photos, you have reached the limit of uploads. Delete old photos or upload few photos. Photo limit = %d"
msgstr ""

#: includes/ajax/class-ajax-v2.php:549
msgid "Invalid album."
msgstr ""

#: includes/ajax/class-ajax-v2.php:708
#: includes/ajax/class-albums.php:908
msgid "You cannot delete this album."
msgstr ""

#: includes/ajax/class-ajax-v2.php:725
#: includes/ajax/class-albums.php:919
msgid "Unknown error. Cannot delete this album."
msgstr ""

#: includes/ajax/class-ajax-v2.php:753
#: includes/ajax/class-photos.php:163
msgid "Invalid image ID"
msgstr ""

#: includes/ajax/class-ajax-v2.php:762
#: includes/ajax/class-photos.php:185
#: includes/ajax/class-photos.php:507
msgid "You cannot edit this photo."
msgstr ""

#: includes/ajax/class-ajax-v2.php:766
msgid "Title is required"
msgstr ""

#: includes/ajax/class-ajax-v2.php:811
msgid "Updated successfully"
msgstr ""

#: includes/ajax/class-ajax-v2.php:825
msgid "You cannot delete this photo."
msgstr ""

#: includes/ajax/class-ajax-v2.php:964
#: includes/ajax/class-photos.php:272
msgid "Your download could not be created. It looks like you do not have ZipArchive installed on your server."
msgstr ""

#: includes/ajax/class-ajax-v2.php:1016
#: includes/ajax/class-photos.php:325
msgid "Nothing to download"
msgstr ""

#: includes/ajax/class-ajax-v2.php:1064
msgid "Your image is invalid!"
msgstr ""

#: includes/ajax/class-ajax-v2.php:1080
msgid "Your image is invalid or too large!"
msgstr ""

#: includes/ajax/class-ajax-v2.php:1154
#: includes/ajax/class-photos.php:62
msgid "You are not authorized to like this photo."
msgstr ""

#: includes/ajax/class-ajax-v2.php:1159
#: includes/ajax/class-photos.php:67
msgid "Something went wrong. Cannot like."
msgstr ""

#: includes/ajax/class-ajax-v2.php:1175
#: includes/ajax/class-photos.php:97
msgid "You are not authorized to unlike this photo."
msgstr ""

#: includes/ajax/class-ajax-v2.php:1180
#: includes/ajax/class-photos.php:102
msgid "Something went wrong. Cannot unlike."
msgstr ""

#: includes/ajax/class-ajax-v2.php:1199
#: includes/ajax/class-comments.php:233
msgid "You are not authorized to comment this photo."
msgstr ""

#: includes/ajax/class-ajax-v2.php:1203
#: includes/ajax/class-comments.php:237
msgid "Enter a comment first"
msgstr ""

#: includes/ajax/class-ajax-v2.php:1278
#: includes/ajax/class-ajax-v2.php:1310
#: includes/ajax/class-ajax-v2.php:1374
#: includes/ajax/class-ajax-v2.php:1406
#: includes/ajax/class-ajax-v2.php:1440
#: includes/ajax/class-ajax-v2.php:1474
#: includes/ajax/class-ajax-v2.php:1505
#: includes/ajax/class-comments.php:67
#: includes/ajax/class-comments.php:98
#: includes/ajax/class-comments.php:186
#: includes/ajax/class-comments.php:310
#: includes/ajax/class-comments.php:351
msgid "Invalid comment ID"
msgstr ""

#: includes/ajax/class-ajax-v2.php:1282
#: includes/ajax/class-ajax-v2.php:1314
#: includes/ajax/class-comments.php:314
#: includes/ajax/class-comments.php:355
msgid "You are not authorized to like this comment"
msgstr ""

#: includes/ajax/class-ajax-v2.php:1287
#: includes/ajax/class-comments.php:319
msgid "Cannot like this comment."
msgstr ""

#: includes/ajax/class-ajax-v2.php:1319
#: includes/ajax/class-comments.php:360
msgid "Cannot unlike this comment."
msgstr ""

#: includes/ajax/class-ajax-v2.php:1347
#: includes/ajax/class-photos.php:139
msgid "Cannot get likes. Maybe photo doesn't exist."
msgstr ""

#: includes/ajax/class-ajax-v2.php:1378
#: includes/ajax/class-ajax-v2.php:1410
#: includes/ajax/class-ajax-v2.php:1444
#: includes/ajax/class-ajax-v2.php:1478
#: includes/ajax/class-ajax-v2.php:1509
#: includes/ajax/class-comments.php:71
#: includes/ajax/class-comments.php:102
#: includes/ajax/class-comments.php:136
#: includes/ajax/class-comments.php:190
msgid "You are not authorized for this"
msgstr ""

#: includes/ajax/class-ajax-v2.php:1491
#: includes/ajax/class-comments.php:83
msgid "Could not be deleted."
msgstr ""

#: includes/ajax/class-ajax-v2.php:1525
#: includes/ajax/class-comments.php:206
msgid "Comment updated"
msgstr ""

#: includes/ajax/class-ajax-v2.php:1532
msgid "Could not update"
msgstr ""

#: includes/ajax/class-albums.php:60
msgid "Wrong last ID."
msgstr ""

#: includes/ajax/class-albums.php:64
#: includes/ajax/class-photos.php:351
msgid "Wrong user ID."
msgstr ""

#. translators: %s is a photos count.
#. translators: %s is a photos count
#: includes/ajax/class-albums.php:108
#: templates/albums.php:55
#: templates/gallery.php:32
#: templates/v3/albums.php:45
#: templates/v3/gallery.php:54
msgid "%s Photo"
msgid_plural "%s Photos"
msgstr[0] ""
msgstr[1] ""

#: includes/ajax/class-albums.php:146
msgid "You are not authorized for adding new album."
msgstr ""

#: includes/ajax/class-albums.php:193
#: includes/ajax/class-albums.php:618
#: includes/ajax/class-uploader.php:58
msgid "You are not authorized for edit this album."
msgstr ""

#: includes/ajax/class-albums.php:354
#: includes/ajax/class-uploader.php:61
msgid "You are not authorized for adding album."
msgstr ""

#. translators: %d is a number for uploading photos limit
#: includes/ajax/class-albums.php:411
#: includes/ajax/class-albums.php:679
msgid "You cannot upload more photos, you have reached the limit of uploads for this album. Delete old photos or upload few photos. Photo limit = %d"
msgstr ""

#: includes/ajax/class-albums.php:424
#: includes/ajax/class-albums.php:692
msgid "Image title is required."
msgstr ""

#: includes/ajax/class-albums.php:435
#: includes/ajax/class-albums.php:717
msgid "Invalid image path."
msgstr ""

#: includes/ajax/class-albums.php:446
#: includes/ajax/class-albums.php:728
msgid "You do not have permission to move files. Please check your permissions."
msgstr ""

#: includes/ajax/class-albums.php:460
#: includes/ajax/class-albums.php:742
msgid "Invalid image mime-type."
msgstr ""

#: includes/ajax/class-albums.php:503
msgid "Cannot insert album. Unknown error."
msgstr ""

#: includes/ajax/class-albums.php:601
msgid "Invalid album ID."
msgstr ""

#: includes/ajax/class-albums.php:706
msgid "Invalid attachment id."
msgstr ""

#: includes/ajax/class-albums.php:782
msgid "Cannot update album. Unknown error."
msgstr ""

#: includes/ajax/class-albums.php:997
msgid "You will be redirected to account in 3 sec."
msgstr ""

#: includes/ajax/class-albums.php:999
msgid "Deleted Successfully"
msgstr ""

#: includes/ajax/class-comments.php:131
msgid "Comments are disabled."
msgstr ""

#: includes/ajax/class-comments.php:141
msgid "Comments are disabled for this photo."
msgstr ""

#: includes/ajax/class-comments.php:207
msgid "Saved"
msgstr ""

#: includes/ajax/class-comments.php:216
msgid "Error"
msgstr ""

#: includes/ajax/class-gallery.php:33
msgid "User ID is required"
msgstr ""

#: includes/ajax/class-photos.php:175
msgid "Wrong nonce"
msgstr ""

#: includes/ajax/class-photos.php:246
msgid "Updated successfully."
msgstr ""

#: includes/ajax/class-uploader.php:51
msgid "Invalid nonce."
msgstr ""

#: includes/common/class-album.php:40
#: templates/modal/add-album.php:54
#: templates/modal/edit-album.php:116
msgid "Everyone"
msgstr ""

#: includes/common/class-album.php:41
#: templates/modal/add-album.php:55
#: templates/modal/edit-album.php:117
msgid "Only me"
msgstr ""

#: includes/common/class-cpt.php:29
msgctxt "Post Type General Name"
msgid "User Photos"
msgstr ""

#: includes/common/class-cpt.php:30
msgctxt "Post Type Singular Name"
msgid "User Album"
msgstr ""

#: includes/common/class-cpt.php:33
msgid "Item Archives"
msgstr ""

#: includes/common/class-cpt.php:34
msgid "Item Attributes"
msgstr ""

#: includes/common/class-cpt.php:35
msgid "Parent Item:"
msgstr ""

#: includes/common/class-cpt.php:36
msgid "All Items"
msgstr ""

#: includes/common/class-cpt.php:37
msgid "Add New Item"
msgstr ""

#: includes/common/class-cpt.php:38
msgid "Add New"
msgstr ""

#: includes/common/class-cpt.php:39
msgid "New Item"
msgstr ""

#: includes/common/class-cpt.php:40
msgid "Edit Item"
msgstr ""

#: includes/common/class-cpt.php:41
msgid "Update Item"
msgstr ""

#: includes/common/class-cpt.php:42
msgid "View Item"
msgstr ""

#: includes/common/class-cpt.php:43
msgid "View Items"
msgstr ""

#: includes/common/class-cpt.php:44
msgid "Search Item"
msgstr ""

#: includes/common/class-cpt.php:45
msgid "Not found"
msgstr ""

#: includes/common/class-cpt.php:46
msgid "Not found in Trash"
msgstr ""

#: includes/common/class-cpt.php:47
msgid "Album Cover"
msgstr ""

#: includes/common/class-cpt.php:48
msgid "Set album cover"
msgstr ""

#: includes/common/class-cpt.php:49
msgid "Remove album cover"
msgstr ""

#: includes/common/class-cpt.php:50
msgid "Use as album cover"
msgstr ""

#: includes/common/class-cpt.php:52
msgid "Uploaded to this item"
msgstr ""

#: includes/common/class-cpt.php:53
msgid "Items list"
msgstr ""

#: includes/common/class-cpt.php:54
msgid "Items list navigation"
msgstr ""

#: includes/common/class-cpt.php:55
msgid "Filter items list"
msgstr ""

#: includes/common/class-cpt.php:59
msgid "Image gallery for Ultimate member Users"
msgstr ""

#: includes/common/class-email-notifications.php:38
msgid "User Photos - New album has been created"
msgstr ""

#: includes/common/class-email-notifications.php:39
#: includes/common/class-setup.php:38
msgid "[{site_name}] User Photo - has been created."
msgstr ""

#: includes/common/class-email-notifications.php:41
msgid "Send a notification to admin when user creates an album."
msgstr ""

#: includes/common/class-email-notifications.php:48
msgid "User Photos - Album has been deleted"
msgstr ""

#: includes/common/class-email-notifications.php:49
#: includes/common/class-setup.php:41
msgid "[{site_name}] User Photo - Album has been deleted."
msgstr ""

#: includes/common/class-email-notifications.php:51
msgid "Send a notification to admin when user deletes an album."
msgstr ""

#: includes/common/class-email-notifications.php:58
msgid "User Photos - Album has been updated"
msgstr ""

#: includes/common/class-email-notifications.php:59
#: includes/common/class-setup.php:44
msgid "[{site_name}] User Photo - Album has been updated."
msgstr ""

#: includes/common/class-email-notifications.php:61
msgid "Send a notification to admin when user updates an album."
msgstr ""

#: includes/common/class-profile.php:33
#: includes/common/class-profile.php:61
#: includes/common/class-profile.php:67
#: templates/v3/album-add.php:56
#: templates/v3/album-edit.php:81
msgid "Photos"
msgstr ""

#: includes/common/class-profile.php:58
#: includes/common/class-profile.php:66
msgid "Albums"
msgstr ""

#: includes/common/class-uploader.php:89
#: includes/common/class-uploader.php:251
msgid "More info"
msgstr ""

#: includes/common/class-uploader.php:114
#: includes/common/class-uploader.php:119
#: includes/common/class-uploader.php:278
#: includes/common/class-uploader.php:283
#: templates/modal/edit-image.php:29
#: templates/modal/edit-image.php:30
#: templates/v3/modal/edit-image.php:29
#: templates/v3/modal/edit-image.php:34
msgid "Image title"
msgstr ""

#: includes/common/class-uploader.php:116
#: includes/common/class-uploader.php:280
#: templates/v3/album-add.php:36
#: templates/v3/album-edit.php:48
#: templates/v3/modal/edit-image.php:31
msgid "Required"
msgstr ""

#: includes/common/class-uploader.php:123
#: includes/common/class-uploader.php:124
#: includes/common/class-uploader.php:287
#: includes/common/class-uploader.php:288
#: templates/modal/edit-image.php:34
#: templates/modal/edit-image.php:35
#: templates/v3/modal/edit-image.php:37
#: templates/v3/modal/edit-image.php:38
msgid "Image caption"
msgstr ""

#: includes/common/class-uploader.php:128
#: includes/common/class-uploader.php:129
#: includes/common/class-uploader.php:292
#: includes/common/class-uploader.php:293
#: templates/caption.php:30
#: templates/modal/edit-image.php:39
#: templates/modal/edit-image.php:40
#: templates/single-image.php:29
#: templates/single-image.php:42
#: templates/v3/caption.php:82
#: templates/v3/modal/edit-image.php:41
#: templates/v3/modal/edit-image.php:42
#: templates/v3/single-image.php:35
#: templates/v3/single-image.php:77
msgid "Related link"
msgstr ""

#: includes/common/class-uploader.php:145
#: includes/common/class-uploader.php:309
#: templates/modal/edit-image.php:55
#: templates/v3/modal/edit-image.php:56
msgid "Set as album cover"
msgstr ""

#: includes/common/class-uploader.php:233
#: templates/modal/edit-album.php:79
msgid "Delete photo"
msgstr ""

#: includes/frontend/class-account.php:48
#: includes/frontend/class-account.php:58
msgid "My Photos"
msgstr ""

#: includes/frontend/class-account.php:52
#: templates/account.php:34
msgid "Once photos and albums are deleted, they are deleted permanently and cannot be recovered."
msgstr ""

#: templates/account.php:45
#: templates/v3/account.php:26
msgid "Download my photos"
msgstr ""

#: templates/account.php:63
#: templates/v3/account.php:58
msgid "Are you sure to delete all your albums & photos?"
msgstr ""

#: templates/account.php:64
#: templates/v3/account.php:49
msgid "Delete all my albums & photos"
msgstr ""

#: templates/account.php:69
msgid "There are no photos and albums in your account."
msgstr ""

#: templates/album-head.php:27
#: templates/v3/album-head.php:25
msgid "Back"
msgstr ""

#: templates/album-head.php:50
#: templates/album-head.php:51
#: templates/album-head.php:54
#: templates/v3/album-edit.php:33
#: templates/v3/album-head.php:57
#: templates/v3/album-head.php:63
#: templates/v3/album-head.php:68
#: templates/v3/album-head.php:85
msgid "Edit album"
msgstr ""

#: templates/album-head.php:60
#: templates/album-head.php:64
#: templates/v3/album-head.php:79
#: templates/v3/album-head.php:90
msgid "Delete album"
msgstr ""

#: templates/album-head.php:61
#: templates/v3/album-head.php:92
msgid "Are you sure to delete this album?"
msgstr ""

#: templates/album-head.php:67
#: templates/modal/add-album.php:70
#: templates/modal/delete-comment.php:37
#: templates/modal/edit-album.php:132
#: templates/modal/edit-comment.php:30
#: templates/modal/edit-image.php:62
#: templates/v3/album-add.php:140
#: templates/v3/album-edit.php:154
#: templates/v3/comment.php:148
#: templates/v3/modal/edit-image.php:83
msgid "Cancel"
msgstr ""

#. translators: %s is a photos count
#: templates/albums.php:55
msgid "No photos"
msgstr ""

#: templates/caption.php:80
#: templates/caption.php:85
#: templates/comment.php:79
msgid "Likes"
msgstr ""

#: templates/caption.php:92
msgid "Comments"
msgstr ""

#: templates/caption.php:116
#: templates/caption.php:120
#: templates/comment.php:73
#: templates/comment.php:75
#: templates/v3/caption.php:159
#: templates/v3/caption.php:164
#: templates/v3/comment.php:173
#: templates/v3/comment.php:178
msgid "Like"
msgstr ""

#: templates/caption.php:116
#: templates/caption.php:123
#: templates/comment.php:73
#: templates/comment.php:75
#: templates/v3/caption.php:159
#: templates/v3/caption.php:165
#: templates/v3/comment.php:173
#: templates/v3/comment.php:179
msgid "Unlike"
msgstr ""

#: templates/caption.php:132
#: templates/comment-form.php:31
#: templates/v3/comment-form.php:25
msgid "Comment"
msgstr ""

#: templates/comment-form.php:24
msgid "Write a reply..."
msgstr ""

#: templates/comment-form.php:27
#: templates/modal/edit-comment.php:23
#: templates/v3/comment-form.php:19
#: templates/v3/comment.php:119
msgid "Write a comment..."
msgstr ""

#: templates/comment.php:50
msgid "Comment hidden."
msgstr ""

#: templates/comment.php:51
msgid "Show this comment"
msgstr ""

#: templates/comment.php:102
msgid "Edit comment"
msgstr ""

#: templates/comment.php:105
#: templates/v3/comment.php:79
msgid "Edit"
msgstr ""

#: templates/comment.php:109
msgid "Delete comment"
msgstr ""

#: templates/comment.php:109
#: templates/v3/comment.php:103
msgid "Are you sure you want to delete this comment?"
msgstr ""

#: templates/comment.php:109
#: templates/modal/delete-comment.php:33
#: templates/v3/comment.php:97
msgid "Delete"
msgstr ""

#: templates/gallery-head.php:17
#: templates/gallery-head.php:20
#: templates/v3/gallery-head.php:23
msgid "New Album"
msgstr ""

#: templates/modal/add-album.php:20
msgid "is too large. File should be less than "
msgstr ""

#: templates/modal/add-album.php:34
#: templates/modal/edit-album.php:73
#: templates/modal/edit-album.php:95
msgid "Cover photo"
msgstr ""

#: templates/modal/add-album.php:37
#: templates/modal/edit-album.php:98
#: templates/v3/album-add.php:59
#: templates/v3/album-add.php:105
#: templates/v3/album-edit.php:67
#: templates/v3/album-edit.php:88
msgid "You cannot upload more photos, you have reached the limit of uploads. Delete old photos to add new ones."
msgstr ""

#: templates/modal/add-album.php:64
#: templates/v3/album-add.php:128
msgid "Publish"
msgstr ""

#: templates/modal/add-album.php:67
#: templates/modal/edit-album.php:129
msgid "Select photos"
msgstr ""

#: templates/modal/edit-album.php:44
msgid " is too large. File should be less than "
msgstr ""

#: templates/modal/edit-album.php:80
msgid "Sure to delete photo?"
msgstr ""

#: templates/modal/edit-album.php:94
msgid "Uploaded photos"
msgstr ""

#: templates/modal/edit-album.php:127
#: templates/modal/edit-comment.php:28
#: templates/modal/edit-image.php:61
#: templates/v3/album-edit.php:143
#: templates/v3/comment.php:135
#: templates/v3/modal/edit-image.php:68
msgid "Update"
msgstr ""

#: templates/modal/likes.php:22
#: templates/v3/modal/likes.php:22
msgid "Nobody has liked this photo yet."
msgstr ""

#: templates/modal/likes.php:24
#: templates/v3/modal/likes.php:24
msgid "Nobody has liked this comment yet."
msgstr ""

#: templates/modal/likes.php:26
#: templates/v3/modal/likes.php:26
msgid "Invalid template context."
msgstr ""

#: templates/modal/likes.php:47
msgid "Close"
msgstr ""

#: templates/photos.php:39
#: templates/v3/comments.php:54
#: templates/v3/gallery-photos.php:50
#: templates/v3/gallery.php:88
#: templates/v3/single-album.php:53
msgid "Load more"
msgstr ""

#: templates/single-image.php:45
#: templates/single-image.php:47
#: templates/v3/single-image.php:101
#: assets/js/v3/gallery.js:841
msgid "Edit Image"
msgstr ""

#: templates/social-activity/new-album.php:14
msgid "created a new photo album"
msgstr ""

#: templates/v3/album-add.php:24
msgid "New album"
msgstr ""

#: templates/v3/album-add.php:44
#: templates/v3/album-edit.php:56
msgid "Album privacy"
msgstr ""

#: templates/v3/album-add.php:105
#: templates/v3/album-edit.php:67
msgid "You cannot upload more photos, you have reached the limit of uploads for this album. Delete old photos or upload few photos."
msgstr ""

#. translators: %s - album title.
#: templates/v3/album-edit.php:31
msgid "Edit album: \"%s\""
msgstr ""

#: templates/v3/caption.php:205
#: templates/v3/comment.php:219
msgid "Click to show all likes"
msgstr ""

#: templates/v3/caption.php:226
msgid "Show comment"
msgstr ""

#: templates/v3/caption.php:234
msgid "Hide comment"
msgstr ""

#: templates/v3/comment.php:42
msgid "Comment has been updated."
msgstr ""

#. translators: %s is the User Photos extension name.
#: um-user-photos.php:58
#: um-user-photos.php:75
msgid "The <strong>%s</strong> extension requires the Ultimate Member plugin to be activated to work properly. You can download it <a href=\"https://wordpress.org/plugins/ultimate-member\">here</a>"
msgstr ""

#: assets/js/account.js:15
msgid "Processing...Please wait."
msgstr ""

#: assets/js/um-user-photos.js:448
msgid "Loading"
msgstr ""

#: assets/js/v3/gallery.js:733
msgid "Are you sure that you want to delete this photo?"
msgstr ""

#: assets/js/v3/gallery.js:1044
msgid "Photo likes"
msgstr ""

#: assets/js/v3/gallery.js:1174
msgid "Comment likes"
msgstr ""
