div.wpforms-container-full .wpforms-form .sq-card-iframe-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

div.wpforms-container-full .wpforms-form .sq-card-iframe-container::before {
  border-width: 1px;
  border-radius: 2px;
}

div.wpforms-container-full .wpforms-form .sq-card-wrapper.sq-focus .sq-card-iframe-container::before {
  border-color: #999;
}

div.wpforms-container-full .wpforms-form .sq-card-wrapper.sq-error .sq-card-iframe-container::before {
  border-color: #cc0000;
}

div.wpforms-container-full .wpforms-form .sq-card-component {
  position: relative;
  height: inherit;
  width: 100%;
  border: 1px solid #ccc;
  background-color: #fff;
  border-radius: 2px;
}

div.wpforms-container-full .wpforms-form .wpforms-field-square-consent {
  border-left: 3px solid;
  padding-left: 15px;
  font-size: 0.75em;
  color: #666;
}

div.wpforms-container-full .wpforms-form .wpforms-field-square .wpforms-field-row.wpforms-field-small .wpforms-field-square-cardname {
  min-width: 250px;
}

div.wpforms-container-full .wpforms-form .wpforms-field-layout .wpforms-layout-column .wpforms-field-row.wpforms-field-small .wpforms-field-square-cardname {
  min-width: 100%;
}

div.wpforms-container-full.wpforms-render-modern .wpforms-form .sq-card-wrapper .sq-card-iframe-container {
  border-radius: var(--wpforms-field-border-radius);
  border: var(--wpforms-field-border-size, 1px) var(--wpforms-field-border-style, solid) var(--wpforms-field-border-color);
  border-color: var(--wpforms-field-border-color);
  background-color: var(--wpforms-field-background-color);
}

div.wpforms-container-full.wpforms-render-modern .wpforms-form .sq-card-wrapper .sq-card-iframe-container::before {
  border-color: transparent;
  border-radius: var(--wpforms-field-border-radius);
}

div.wpforms-container-full.wpforms-render-modern .wpforms-form .sq-card-wrapper .sq-card-iframe-container iframe {
  border: none !important;
  border-radius: var(--wpforms-field-border-radius);
  background: transparent;
}

div.wpforms-container-full.wpforms-render-modern .wpforms-form .sq-card-wrapper.sq-focus .sq-card-iframe-container {
  border-width: var(--wpforms-field-border-size, 0);
  border-style: solid;
  border-color: var(--wpforms-button-background-color);
  box-shadow: 0 0 0 1px var(--wpforms-button-background-color), 0px 1px 2px rgba(0, 0, 0, 0.15);
  outline: none;
  border-color: var(--wpforms-button-background-color) !important;
}

div.wpforms-container-full.wpforms-render-modern .wpforms-form .sq-card-wrapper.sq-focus .sq-card-iframe-container::before {
  border: none;
}

div.wpforms-container-full.wpforms-render-modern .wpforms-form .sq-card-wrapper.sq-error .sq-card-iframe-container {
  border-width: var(--wpforms-field-border-size);
  border-style: solid;
  border-color: var(--wpforms-label-error-color);
  border-color: var(--wpforms-label-error-color) !important;
}

div.wpforms-container-full.wpforms-render-modern .wpforms-form .sq-card-wrapper.sq-error:hover .sq-card-iframe-container {
  border-width: var(--wpforms-field-border-size);
  border-style: solid;
  border-color: var(--wpforms-label-error-color);
  box-shadow: 0 0 2px 0 var(--wpforms-label-error-color);
  border-color: var(--wpforms-label-error-color) !important;
}

div.wpforms-container-full.wpforms-render-modern .wpforms-form .sq-card-wrapper.sq-error.sq-focus .sq-card-iframe-container {
  border-width: var(--wpforms-field-border-size);
  border-style: solid;
  border-color: var(--wpforms-label-error-color);
  box-shadow: 0 0 0 1px var(--wpforms-label-error-color);
  border-color: var(--wpforms-label-error-color) !important;
}

div.wpforms-container-full.wpforms-render-modern .wpforms-form .wpforms-field-square .wpforms-field-sublabel + .wpforms-error {
  margin-top: calc( 1.5 * var( --wpforms-field-size-sublabel-spacing ));
}

.wpforms-field-square .sq-card-message {
  display: none;
}

#wpforms-conversational-form-page .sq-card-iframe-container,
#wpforms-conversational-form-page .sq-card-component {
  background-color: transparent;
  border: none;
}

#sq-nudata-modal {
  display: block !important;
  position: fixed !important;
  left: 0 !important;
  top: 0 !important;
}

@media only screen and (max-width: 600px) {
  div.wpforms-container-full.wpforms-render-modern .wpforms-form .sq-card-wrapper {
    max-width: calc( 100% - 2px);
  }
}
