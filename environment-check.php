<?php
// Environment comparison tool
echo "<h1>🔍 Local vs Hosting Environment Analysis</h1>";

echo "<style>
body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
.section { background: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid #007cba; }
.warning { border-left-color: #ffc107; background: #fff3cd; }
.error { border-left-color: #dc3545; background: #f8d7da; }
.success { border-left-color: #28a745; background: #d4edda; }
table { width: 100%; border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
th { background-color: #f2f2f2; }
</style>";

echo "<div class='section'>";
echo "<h2>📊 Current Local Environment (XAMPP)</h2>";
echo "<table>";
echo "<tr><th>Setting</th><th>Value</th><th>Typical Hosting</th><th>Status</th></tr>";

// PHP Version
$php_version = phpversion();
echo "<tr><td>PHP Version</td><td>$php_version</td><td>7.4+ or 8.0+</td>";
if (version_compare($php_version, '7.4', '>=')) {
    echo "<td style='color: green;'>✓ Good</td>";
} else {
    echo "<td style='color: red;'>⚠ May be too old</td>";
}
echo "</tr>";

// Memory Limit
$memory_limit = ini_get('memory_limit');
echo "<tr><td>Memory Limit</td><td>$memory_limit</td><td>256M or higher</td>";
$memory_bytes = return_bytes($memory_limit);
if ($memory_bytes >= return_bytes('256M')) {
    echo "<td style='color: green;'>✓ Good</td>";
} else {
    echo "<td style='color: red;'>⚠ Too low</td>";
}
echo "</tr>";

// Max Execution Time
$max_execution_time = ini_get('max_execution_time');
echo "<tr><td>Max Execution Time</td><td>{$max_execution_time}s</td><td>30-300s</td>";
if ($max_execution_time >= 30) {
    echo "<td style='color: green;'>✓ Good</td>";
} else {
    echo "<td style='color: red;'>⚠ Too low</td>";
}
echo "</tr>";

// Upload Max Filesize
$upload_max = ini_get('upload_max_filesize');
echo "<tr><td>Upload Max Filesize</td><td>$upload_max</td><td>64M+</td>";
if (return_bytes($upload_max) >= return_bytes('64M')) {
    echo "<td style='color: green;'>✓ Good</td>";
} else {
    echo "<td style='color: orange;'>⚠ Could be higher</td>";
}
echo "</tr>";

// Post Max Size
$post_max = ini_get('post_max_size');
echo "<tr><td>Post Max Size</td><td>$post_max</td><td>64M+</td>";
if (return_bytes($post_max) >= return_bytes('64M')) {
    echo "<td style='color: green;'>✓ Good</td>";
} else {
    echo "<td style='color: orange;'>⚠ Could be higher</td>";
}
echo "</tr>";

echo "</table>";
echo "</div>";

// WordPress specific checks
if (file_exists('./wp-config.php')) {
    define('WP_USE_THEMES', false);
    require_once('./wp-load.php');
    
    echo "<div class='section'>";
    echo "<h2>🔧 WordPress Configuration</h2>";
    echo "<table>";
    
    // Debug mode
    $debug = defined('WP_DEBUG') && WP_DEBUG ? 'Enabled' : 'Disabled';
    echo "<tr><td>WP_DEBUG</td><td>$debug</td><td>Disabled on hosting</td>";
    if (!WP_DEBUG) {
        echo "<td style='color: green;'>✓ Matches hosting</td>";
    } else {
        echo "<td style='color: orange;'>⚠ Different from hosting</td>";
    }
    echo "</tr>";
    
    // WordPress version
    global $wp_version;
    echo "<tr><td>WordPress Version</td><td>$wp_version</td><td>Latest</td>";
    echo "<td style='color: green;'>✓ Check if same as hosting</td>";
    echo "</tr>";
    
    // Database
    global $wpdb;
    echo "<tr><td>Database Host</td><td>" . DB_HOST . "</td><td>Usually localhost or IP</td>";
    if (DB_HOST === 'localhost') {
        echo "<td style='color: green;'>✓ Standard</td>";
    } else {
        echo "<td style='color: orange;'>⚠ Different setup</td>";
    }
    echo "</tr>";
    
    echo "</table>";
    echo "</div>";
}

// Check for common hosting vs local differences
echo "<div class='section warning'>";
echo "<h2>⚠️ Common Hosting vs Local Issues</h2>";
echo "<ul>";
echo "<li><strong>URL Differences:</strong> Hosting uses HTTPS, local uses HTTP</li>";
echo "<li><strong>File Permissions:</strong> Different on Windows vs Linux hosting</li>";
echo "<li><strong>PHP Extensions:</strong> Some extensions may be missing locally</li>";
echo "<li><strong>Database Collation:</strong> May differ between environments</li>";
echo "<li><strong>Caching:</strong> Hosting may have server-level caching</li>";
echo "<li><strong>Security:</strong> Hosting has different security restrictions</li>";
echo "</ul>";
echo "</div>";

// PHP Extensions check
echo "<div class='section'>";
echo "<h2>🔌 PHP Extensions</h2>";
$required_extensions = ['mysqli', 'curl', 'gd', 'mbstring', 'xml', 'zip', 'json'];
echo "<table>";
echo "<tr><th>Extension</th><th>Status</th><th>Required for</th></tr>";

foreach ($required_extensions as $ext) {
    $loaded = extension_loaded($ext);
    $status = $loaded ? '✓ Loaded' : '✗ Missing';
    $color = $loaded ? 'green' : 'red';
    
    $purpose = [
        'mysqli' => 'Database connection',
        'curl' => 'HTTP requests, API calls',
        'gd' => 'Image processing',
        'mbstring' => 'String handling',
        'xml' => 'XML processing',
        'zip' => 'File compression',
        'json' => 'JSON data handling'
    ];
    
    echo "<tr><td>$ext</td><td style='color: $color;'>$status</td><td>{$purpose[$ext]}</td></tr>";
}
echo "</table>";
echo "</div>";

// Recommendations
echo "<div class='section success'>";
echo "<h2>💡 Recommendations to Match Hosting Environment</h2>";
echo "<ol>";
echo "<li><strong>Update PHP:</strong> Ensure XAMPP uses same PHP version as hosting</li>";
echo "<li><strong>Increase Limits:</strong> Match memory_limit and execution time to hosting</li>";
echo "<li><strong>Check SSL:</strong> If hosting uses HTTPS, configure local SSL</li>";
echo "<li><strong>Database Import:</strong> Import fresh database from hosting</li>";
echo "<li><strong>Plugin Versions:</strong> Ensure same plugin versions as hosting</li>";
echo "<li><strong>WordPress Version:</strong> Match WordPress version exactly</li>";
echo "</ol>";
echo "</div>";

// Quick fixes
echo "<div class='section'>";
echo "<h2>🚀 Quick Fix Options</h2>";
echo "<p><a href='?fix_memory=1' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px;'>Increase Memory Limit</a></p>";
echo "<p><a href='?fix_execution=1' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px;'>Increase Execution Time</a></p>";
echo "<p><a href='?disable_debug=1' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px;'>Disable Debug Mode</a></p>";
echo "</div>";

// Handle quick fixes
if (isset($_GET['fix_memory'])) {
    ini_set('memory_limit', '512M');
    echo "<div class='section success'><p>✅ Memory limit increased to 512M for this session</p></div>";
}

if (isset($_GET['fix_execution'])) {
    ini_set('max_execution_time', 300);
    echo "<div class='section success'><p>✅ Execution time increased to 300 seconds for this session</p></div>";
}

if (isset($_GET['disable_debug']) && file_exists('./wp-config.php')) {
    echo "<div class='section success'><p>✅ To permanently disable debug, edit wp-config.php and set WP_DEBUG to false</p></div>";
}

function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }
    return $val;
}
?>
