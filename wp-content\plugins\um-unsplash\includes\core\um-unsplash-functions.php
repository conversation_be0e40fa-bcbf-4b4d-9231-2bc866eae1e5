<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! class_exists( 'UM_Unsplash_Functions' ) ) {

	/**
	 * Class UM_Unsplash_Functions
	 */
	class UM_Unsplash_Functions {

		/**
		 * UM_Unsplash_Functions constructor.
		 */
		public function __construct() {
			add_action( 'um_cover_area_content', array( $this, 'show_photo_attribution' ), 10, 1 );
			add_action( 'after_unsplash_cover_update', array( $this, 'trigger_download_event' ), 10, 2 );

			// Fix for profile completeness extension.
			add_filter( 'um_profile_completeness_get_field_progress', array( $this, 'profile_completeness' ), 10, 3 );
		}

		/**
		 *
		 */
		public function show_photo_attribution() {
			$cover_pic    = get_user_meta( um_profile_id(), '_um_unsplash_cover', true );
			$pic_author   = get_user_meta( um_profile_id(), '_um_unsplash_photo_author', true );
			$author_url   = get_user_meta( um_profile_id(), '_um_unsplash_photo_author_url', true );
			$author_url  .= '?utm_source=' . UM()->options()->get( 'unsplash_app_name' ) . '&utm_medium=referral';
			$unsplash_url = 'https://unsplash.com/?utm_source=' . UM()->options()->get( 'unsplash_app_name' ) . '&utm_medium=referral';

			if ( $cover_pic && $pic_author ) {
				?>
				<span class="um-unsplash-attribution">
					<?php // translators: %1$s is a photo author URL, %2$s is an author name, %3$s is a link to Unsplash ?>
					<?php echo wp_kses( sprintf( __( 'Photo by <a target="_blank" class="author-url" href="%1$s">%2$s</a> on <a target="_blank" href="%3$s">Unsplash</a>', 'um-unsplash' ), $author_url, ucfirst( esc_html( $pic_author ) ), $unsplash_url ), UM()->get_allowed_html( 'templates' ) ); ?>
				</span>
				<?php
			}
		}

		/**
		 * Send remote post for download cover photo data.
		 *
		 * @param $user_id
		 * @param $photo_download_url
		 *
		 * @return void
		 */
		public function trigger_download_event( $user_id, $photo_download_url ) {
			$get_app_name = wp_remote_post(
				UM()->Unsplash()->proxy_url,
				array(
					'timeout'   => 45,
					'sslverify' => false,
					'body'      => array(
						'action'           => 'um_unsplash_appname',
						'license'          => UM()->options()->get( 'um_unsplash_license_key' ),
						'_wp_http_referer' => get_site_url(),
					),
					'headers'   => array( 'Referer' => site_url() ),
				)
			);

			if ( ! is_wp_error( $get_app_name ) ) {
				$get_app_name = json_decode( wp_remote_retrieve_body( $get_app_name ) );
			} else {
				$get_app_name = wp_remote_post(
					UM()->Unsplash()->proxy_url,
					array(
						'timeout'   => 45,
						'sslverify' => true,
						'body'      => array(
							'action'           => 'um_unsplash_appname',
							'license'          => UM()->options()->get( 'um_unsplash_license_key' ),
							'_wp_http_referer' => get_site_url(),
						),
						'headers'   => array( 'Referer' => site_url() ),
					)
				);

				if ( ! is_wp_error( $get_app_name ) ) {
					$get_app_name = json_decode( wp_remote_retrieve_body( $get_app_name ) );
				}
			}

			UM()->options()->update( 'unsplash_app_name', esc_attr( $get_app_name->data ) );

			$photo_id = get_user_meta( $user_id, '_um_unsplash_photo_id', true );
			$url      = add_query_arg(
				array(
					'action'   => 'um_unsplash_download',
					'license'  => UM()->options()->get( 'um_unsplash_license_key' ),
					'photo_id' => $photo_id,
				),
				UM()->Unsplash()->proxy_url
			);
			$data = wp_remote_get( $url );
			$data = wp_remote_retrieve_body( $data );
		}

		/**
		 * Fix for profile completeness extension.
		 *
		 * @since 2.1.4
		 *
		 * @param bool   $completed If the field is 'completed'.
		 * @param string $key       The Meta Key of the field.
		 * @param int    $user_id   User ID.
		 *
		 * @return boolean Returns `true` if the field is 'completed'.
		 */
		public function profile_completeness( $completed, $key, $user_id ) {
			if ( 'cover_photo' === $key && ( get_user_meta( $user_id, 'cover_photo', true ) || get_user_meta( $user_id, '_um_unsplash_cover', true ) ) ) {
				$completed = true;
			}
			return $completed;
		}
	}
}
