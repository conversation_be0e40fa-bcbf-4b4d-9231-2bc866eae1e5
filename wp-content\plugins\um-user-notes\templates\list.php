<?php
/**
 * Template for the notes list template.
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-notes/list.php
 *
 * @see      https://docs.ultimatemember.com/article/1516-templates-map
 * @package  um_ext\um_user_notes\templates
 * @version  1.1.1
 *
 * @var array     $args
 * @var int       $total
 * @var int       $per_page
 * @var \WP_Query $latest_notes
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( $latest_notes->have_posts() ) {
	?>

	<div class="um-notes-holder um-notes-list">

		<?php
		while ( $latest_notes->have_posts() ) {
			$latest_notes->the_post();

			UM()->get_template(
				'profile/note.php',
				um_user_notes_plugin,
				array(
					'id' => get_the_id(),
				),
				true
			);
		}
		?>
	</div>

	<?php
	if ( $total > $per_page ) {
		$t_args = compact( 'per_page' );
		UM()->get_template( 'profile/load-more.php', um_user_notes_plugin, $t_args, true );
	}
} else {
	UM()->get_template( 'profile/empty.php', um_user_notes_plugin, array(), true );
}
