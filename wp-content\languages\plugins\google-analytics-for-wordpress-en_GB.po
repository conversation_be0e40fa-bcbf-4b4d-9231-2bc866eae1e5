# Translation of Plugins - MonsterInsights &#8211; Google Analytics Dashboard for WordPress (Website Stats Made Easy) - Stable (latest release) in English (UK)
# This file is distributed under the same license as the Plugins - MonsterInsights &#8211; Google Analytics Dashboard for WordPress (Website Stats Made Easy) - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-07-08 16:14:11+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: en_GB\n"
"Project-Id-Version: Plugins - MonsterInsights &#8211; Google Analytics Dashboard for WordPress (Website Stats Made Easy) - Stable (latest release)\n"

#. Translators: Placeholders add the PHP version, a link to the MonsterInsights
#. blog and a line break.
#: includes/admin/admin.php:586
msgid "Your site is running an outdated, insecure version of PHP (%1$s), which could be putting your site at risk for being hacked.%4$sWordPress is working towards discontinuing support for your PHP version.%4$sUpdating PHP only takes a few minutes and will make your website significantly faster and more secure.%4$s%2$sLearn more about updating PHP%3$s"
msgstr "Your site is running an outdated, insecure version of PHP (%1$s), which could be putting your site at risk of being hacked.%4$sWordPress is working towards discontinuing support for your PHP version.%4$sUpdating PHP only takes a few minutes and will make your website significantly faster and more secure.%4$s%2$sLearn more about updating PHP%3$s"

#. Translators: Placeholders add the PHP version, a link to the MonsterInsights
#. blog and a line break.
#: includes/admin/admin.php:583
msgid "Your site is running an outdated, insecure version of PHP (%1$s), which could be putting your site at risk for being hacked.%4$sWordPress stopped supporting your PHP version in November, 2019.%4$sUpdating PHP only takes a few minutes and will make your website significantly faster and more secure.%4$s%2$sLearn more about updating PHP%3$s"
msgstr "Your site is running an outdated, insecure version of PHP (%1$s), which could be putting your site at risk of being hacked.%4$sWordPress stopped supporting your PHP version in November 2019.%4$sUpdating PHP only takes a few minutes and will make your website significantly faster and more secure.%4$s%2$sLearn more about updating PHP%3$s"

#: includes/admin/admin.php:283
msgid "SEO:"
msgstr "SEO:"

#: includes/admin/admin.php:149
msgid "Upgrade to Pro:"
msgstr "Upgrade to Pro:"

#: includes/admin/admin.php:118 includes/admin/admin.php:283
msgid "SEO"
msgstr "SEO"

#: includes/helpers.php:2109
msgid "Samoa"
msgstr "Samoa"

#: includes/admin/routes.php:601
msgid "Easily display Instagram content on your WordPress site without writing any code. Comes with multiple templates, ability to show content from multiple accounts, hashtags, and more. Trusted by 1 million websites."
msgstr "Easily display Instagram content on your WordPress site without writing any code. Comes with multiple templates, ability to show content from multiple accounts, hashtags, and more. Trusted by one million websites."

#: includes/admin/routes.php:576
msgid "Boost your sales and conversions by up to 15% with real-time social proof notifications. TrustPulse helps you show live user activity and purchases to help convince other users to purchase."
msgstr "Boost your sales and conversions by up to 15% with real-time social proof notifications. TrustPulse helps you show live user activity and purchases to help convince other users to purchase."

#: includes/admin/routes.php:565
msgid "Turn your website visitors into brand ambassadors! Easily grow your email list, website traffic, and social media followers with the most powerful giveaways & contests plugin for WordPress."
msgstr "Turn your website visitors into brand ambassadors! Easily grow your email list, website traffic, and social media followers, with the most powerful giveaways and contests plugin for WordPress."

#: includes/admin/routes.php:554
msgid "The fastest drag & drop landing page builder for WordPress. Create custom landing pages without writing code, connect them with your CRM, collect subscribers, and grow your audience. Trusted by 1 million sites."
msgstr "The fastest drag and drop landing page builder for WordPress. Create custom landing pages without writing code, connect them with your CRM, collect subscribers, and grow your audience. Trusted by one million sites."

#: includes/admin/routes.php:517
msgid "The original WordPress SEO plugin and toolkit that improves your website’s search rankings. Comes with all the SEO features like Local SEO, WooCommerce SEO, sitemaps, SEO optimizer, schema, and more."
msgstr "The original WordPress SEO plugin and toolkit that improves your website’s search rankings. Comes with all the SEO features, like Local SEO, WooCommerce SEO, sitemaps, SEO optimiser, schema, and more."

#. Translators: Placeholders add links to articles.
#: includes/admin/common.php:666
msgid "To update, we recommend following this %1$sstep by step guide for updating WordPress%2$s from IsItWP and afterwards check out %3$sWhy You Should Always Use the Latest Version of WordPress%4$s on WPBeginner."
msgstr "To update, we recommend following this %1$sstep-by-step guide for updating WordPress%2$s from IsItWP and, afterwards, checking out %3$sWhy You Should Always Use the Latest Version of WordPress%4$s on WPBeginner."

#: includes/admin/common.php:664
msgid "The good news: updating WordPress has never been easier and only takes a few moments."
msgstr "The good news: updating WordPress has never been easier and only takes a few moments."

#. Translators: Placeholder is for the current WordPress version.
#: includes/admin/common.php:662
msgid "In the next major release of MonsterInsights we are planning to remove support for the version of WordPress you are using (version %s). This version is several years out of date, and most plugins do not support this version anymore, so you could be missing out on critical updates for performance and security already!"
msgstr "In the next major release of MonsterInsights, we are planning to remove support for the version of WordPress you are using (version %s). This version is several years out of date, and most plugins do not support this version anymore, so you could already be missing out on critical updates for performance and security!"

#: includes/admin/common.php:659
msgid "ACTION REQUIRED: Your WordPress version is putting your site at risk!"
msgstr "ACTION REQUIRED: your WordPress version is putting your site at risk!"

#: includes/admin/common.php:640
msgid "Upgrading your PHP version will make sure you are able to continue using WordPress without issues in the future, keep your site secure, and will also make your website up to 400% faster!"
msgstr "Upgrading your PHP version will make sure you are able to continue using WordPress without issues in the future, keep your site secure, and will also make your website up to 400% faster!"

#. Translators: Placeholders add a link to an article.
#: includes/admin/common.php:639
msgid "To ensure MonsterInsights and other plugins on your site continue to function properly, and avoid putting your site at risk, please take a few minutes to ask your website hosting provider to upgrade the version of PHP to a modern PHP version (7.2 or newer). We provide helpful templates for how to ask them %1$shere%2$s."
msgstr "To ensure MonsterInsights and other plugins on your site continue to function properly, and avoid putting your site at risk, please take a few minutes to ask your website hosting provider to upgrade the version of PHP to a modern PHP version (7.2 or newer). We provide helpful templates for how to ask them %1$shere%2$s."

#. Translators: Placeholder is for the current PHP version.
#: includes/admin/common.php:636
msgid "In the next major release of MonsterInsights we are planning to remove support for the version of PHP you are using (%s). This insecure version is no longer supported by WordPress itself, so you are already missing out on the latest features of WordPress along with critical updates for security and performance (modern PHP versions make websites much faster)."
msgstr "In the next major release of MonsterInsights, we are planning to remove support for the version of PHP you are using (%s). This insecure version is no longer supported by WordPress itself, so you are already missing out on the latest features of WordPress along with critical updates for security and performance (modern PHP versions make websites much faster)."

#: includes/admin/common.php:629
msgid "ACTION REQUIRED: Speed your website up 400% with a single email!"
msgstr "ACTION REQUIRED: speed up your website 400% with a single email!"

#: includes/admin/common.php:627
msgid "ACTION REQUIRED: Your PHP version is putting your site at risk!"
msgstr "ACTION REQUIRED: your PHP version is putting your site at risk!"

#: includes/admin/sharedcount.php:313
msgid "There was an error grabbing data from SharedCount, please check the API Key"
msgstr "There was an error grabbing data from SharedCount, please check the API Key"

#: includes/admin/sharedcount.php:309
msgid "The SharedCount API key is invalid"
msgstr "The SharedCount API key is invalid"

#: includes/admin/sharedcount.php:98
msgid "The SharedCount API key is not set"
msgstr "The SharedCount API key is not set"

#: includes/admin/ajax.php:180
msgid "You are not allowed to deactivate plugins"
msgstr "You are not allowed to deactivate plugins"

#: includes/admin/ajax.php:131
msgid "You are not allowed to activate plugins"
msgstr "You are not allowed to activate plugins"

#: includes/admin/routes.php:1341
msgid "Homepage"
msgstr "Homepage"

#: includes/admin/notifications/notification-to-add-more-file-extensions.php:44
msgid "Add File Extensions"
msgstr "Add File Extensions"

#. Translators: upgrade to pro notification content
#: includes/admin/notifications/notification-upgrade-to-pro.php:29
msgid "By upgrading to MonsterInsights Pro you get access to additional reports right in your WordPress dashboard and advanced tracking features like eCommerce, Custom Dimensions, Forms tracking and more!"
msgstr "By upgrading to MonsterInsights Pro, you get access to additional reports right in your WordPress dashboard and advanced tracking features like eCommerce, Custom Dimensions, Forms tracking, and more!"

#: includes/admin/notifications/notification-to-setup-affiliate-links.php:52
msgid "Read More"
msgstr "Read More"

#: includes/admin/notifications/notification-to-setup-affiliate-links.php:42
msgid "Set Up Affiliate Link Tracking"
msgstr "Set Up Affiliate Link Tracking"

#: includes/admin/notifications/notification-audience.php:51
#: includes/admin/notifications/notification-bounce-rate.php:45
#: includes/admin/notifications/notification-mobile-device-low-traffic.php:37
#: includes/admin/notifications/notification-traffic-dropping.php:48
#: includes/admin/notifications/notification-visitors.php:42
msgid "View Report"
msgstr "View Report"

#. Translators: visitors notification content
#: includes/admin/notifications/notification-visitors.php:38
msgid "Your website has been visited by %s visitors in the past 30 days. Click the button below to view the full analytics report."
msgstr "Your website has been visited by %s visitors in the past 30 days. Click the button below to view the full analytics report."

#. Translators: Readable time to display
#: includes/admin/notifications.php:315
msgid "%1$s ago"
msgstr "%1$s ago"

#: includes/admin/admin.php:104
msgid "Popular Posts:"
msgstr "Popular Posts:"

#: includes/helpers.php:2122
msgid "United States of America"
msgstr "United States of America"

#: includes/helpers.php:2107
msgid "St Vincent and the Grenadines"
msgstr "St Vincent and the Grenadines"

#: includes/helpers.php:2106
msgid "St Lucia"
msgstr "St Lucia"

#: includes/helpers.php:2105
msgid "St Kitts and Nevis"
msgstr "St Kitts and Nevis"

#: includes/helpers.php:2117
msgid "The Bahamas"
msgstr "The Bahamas"

#: lite/includes/popular-posts/class-popular-posts-widget-sidebar.php:377
msgctxt "google-analytics-for-wordpress"
msgid "%dpx"
msgstr "%dpx"

#: lite/includes/popular-posts/class-popular-posts-widget-sidebar.php:292
msgctxt "Widget"
msgid "Only Show Posts from These Categories:"
msgstr "Only Show Posts from These Categories:"

#: lite/includes/popular-posts/class-popular-posts-widget-sidebar.php:286
msgctxt "Widget"
msgid "Comments Count Color:"
msgstr "Comments Count Colour:"

#: lite/includes/popular-posts/class-popular-posts-widget-sidebar.php:283
msgctxt "Widget"
msgid "Background Color:"
msgstr "Background Colour:"

#: lite/includes/popular-posts/class-popular-posts-widget-sidebar.php:280
msgctxt "Widget"
msgid "Border Color:"
msgstr "Border Colour:"

#: lite/includes/popular-posts/class-popular-posts-widget-sidebar.php:277
msgctxt "Widget"
msgid "Label Text:"
msgstr "Label Text:"

#: lite/includes/popular-posts/class-popular-posts-widget-sidebar.php:274
msgctxt "Widget"
msgid "Label Color:"
msgstr "Label Colour:"

#: lite/includes/popular-posts/class-popular-posts-widget-sidebar.php:271
msgctxt "Widget"
msgid "Title Font Size:"
msgstr "Title Font Size:"

#: lite/includes/popular-posts/class-popular-posts-widget-sidebar.php:266
msgctxt "Widget"
msgid "Title Color:"
msgstr "Title Colour:"

#: lite/includes/popular-posts/class-popular-posts-widget-sidebar.php:242
msgctxt "Widget"
msgid "Theme:"
msgstr "Theme:"

#: lite/includes/popular-posts/class-popular-posts-widget-sidebar.php:232
msgctxt "Widget"
msgid "Number of posts to display:"
msgstr "Number of posts to display:"

#: lite/includes/popular-posts/class-popular-posts-widget-sidebar.php:227
msgctxt "Widget"
msgid "Display Widget Title"
msgstr "Display Widget Title"

#: lite/includes/popular-posts/class-popular-posts-widget-sidebar.php:208
msgctxt "Widget"
msgid "Widget Title:"
msgstr "Widget Title:"

#: lite/includes/popular-posts/class-popular-posts-widget-sidebar.php:86
msgctxt "Widget"
msgid "Popular Posts - MonsterInsights"
msgstr "Popular Posts – MonsterInsights"

#: lite/includes/popular-posts/class-popular-posts-widget-sidebar.php:75
msgctxt "Widget"
msgid "Display popular posts."
msgstr "Display popular posts."

#: lite/includes/popular-posts/class-popular-posts-widget-sidebar.php:260
msgid "Theme Preview"
msgstr "Theme Preview"

#: includes/admin/admin.php:104 assets/gutenberg/js/editor.js:6663
msgid "Popular Posts"
msgstr "Popular Posts"

#: includes/admin/licensing/autoupdate.php:64
msgid "Manage auto-updates"
msgstr "Manage auto-updates"

#: includes/admin/licensing/autoupdate.php:58
msgid "Enable the MonsterInsights PRO plugin to manage auto-updates"
msgstr "Enable the MonsterInsights PRO plugin to manage auto-updates"

#: includes/gutenberg/headline-tool/headline-tool.php:1021
msgid "something"
msgstr "something"

#: includes/gutenberg/headline-tool/headline-tool.php:1020
msgid "more"
msgstr "more"

#: includes/gutenberg/headline-tool/headline-tool.php:1019
msgid "guy"
msgstr "guy"

#: includes/gutenberg/headline-tool/headline-tool.php:1017
msgid "mind"
msgstr "mind"

#: includes/gutenberg/headline-tool/headline-tool.php:1016
msgid "good"
msgstr "good"

#: includes/gutenberg/headline-tool/headline-tool.php:1015
msgid "seen"
msgstr "seen"

#: includes/gutenberg/headline-tool/headline-tool.php:1013
msgid "girl"
msgstr "girl"

#: includes/gutenberg/headline-tool/headline-tool.php:1012
msgid "you'll"
msgstr "you'll"

#: includes/gutenberg/headline-tool/headline-tool.php:1011
msgid "see"
msgstr "see"

#: includes/gutenberg/headline-tool/headline-tool.php:1010
msgid "man"
msgstr "man"

#: includes/gutenberg/headline-tool/headline-tool.php:1009
msgid "found"
msgstr "found"

#: includes/gutenberg/headline-tool/headline-tool.php:1008
msgid "years"
msgstr "years"

#: includes/gutenberg/headline-tool/headline-tool.php:1007
msgid "right"
msgstr "right"

#: includes/gutenberg/headline-tool/headline-tool.php:1006
msgid "makes"
msgstr "makes"

#: includes/gutenberg/headline-tool/headline-tool.php:1004
msgid "year"
msgstr "year"

#: includes/gutenberg/headline-tool/headline-tool.php:1003
msgid "reasons"
msgstr "reasons"

#: includes/gutenberg/headline-tool/headline-tool.php:1002
msgid "make"
msgstr "make"

#: includes/gutenberg/headline-tool/headline-tool.php:1001
msgid "facebook"
msgstr "Facebook"

#: includes/gutenberg/headline-tool/headline-tool.php:1000
msgid "world"
msgstr "world"

#: includes/gutenberg/headline-tool/headline-tool.php:999
msgid "really"
msgstr "really"

#: includes/gutenberg/headline-tool/headline-tool.php:998
msgid "made"
msgstr "made"

#: includes/gutenberg/headline-tool/headline-tool.php:997
msgid "down"
msgstr "down"

#: includes/gutenberg/headline-tool/headline-tool.php:996
msgid "ways"
msgstr "ways"

#: includes/gutenberg/headline-tool/headline-tool.php:995
msgid "photos"
msgstr "photos"

#: includes/gutenberg/headline-tool/headline-tool.php:993
msgid "dog"
msgstr "dog"

#: includes/gutenberg/headline-tool/headline-tool.php:992
msgid "way"
msgstr "way"

#: includes/gutenberg/headline-tool/headline-tool.php:991
msgid "people"
msgstr "people"

#: includes/gutenberg/headline-tool/headline-tool.php:990
msgid "look"
msgstr "look"

#: includes/gutenberg/headline-tool/headline-tool.php:989
msgid "boy"
msgstr "boy"

#: includes/gutenberg/headline-tool/headline-tool.php:988
msgid "watch"
msgstr "watch"

#: includes/gutenberg/headline-tool/headline-tool.php:987
msgid "out"
msgstr "out"

#: includes/gutenberg/headline-tool/headline-tool.php:986
msgid "little"
msgstr "little"

#: includes/gutenberg/headline-tool/headline-tool.php:984
msgid "want"
msgstr "want"

#: includes/gutenberg/headline-tool/headline-tool.php:982
msgid "life"
msgstr "life"

#: includes/gutenberg/headline-tool/headline-tool.php:981
msgid "best"
msgstr "best"

#: includes/gutenberg/headline-tool/headline-tool.php:980
msgid "video"
msgstr "video"

#: includes/gutenberg/headline-tool/headline-tool.php:979
msgid "old"
msgstr "old"

#: includes/gutenberg/headline-tool/headline-tool.php:978
msgid "know"
msgstr "know"

#: includes/gutenberg/headline-tool/headline-tool.php:977
msgid "being"
msgstr "being"

#: includes/gutenberg/headline-tool/headline-tool.php:976
msgid "valentines"
msgstr "valentines"

#: includes/gutenberg/headline-tool/headline-tool.php:974
msgid "its"
msgstr "its"

#: includes/gutenberg/headline-tool/headline-tool.php:972
msgid "time"
msgstr "time"

#: includes/gutenberg/headline-tool/headline-tool.php:970
msgid "here"
msgstr "here"

#: includes/gutenberg/headline-tool/headline-tool.php:969
msgid "baby"
msgstr "baby"

#: includes/gutenberg/headline-tool/headline-tool.php:968
msgid "think"
msgstr "think"

#: includes/gutenberg/headline-tool/headline-tool.php:967
msgid "never"
msgstr "never"

#: includes/gutenberg/headline-tool/headline-tool.php:966
msgid "heart"
msgstr "heart"

#: includes/gutenberg/headline-tool/headline-tool.php:965
msgid "awesome"
msgstr "awesome"

#: includes/gutenberg/headline-tool/headline-tool.php:964
msgid "thing"
msgstr "thing"

#: includes/gutenberg/headline-tool/headline-tool.php:963
msgid "need"
msgstr "need"

#: includes/gutenberg/headline-tool/headline-tool.php:962
msgid "happened"
msgstr "happened"

#: includes/gutenberg/headline-tool/headline-tool.php:961
msgid "actually"
msgstr "actually"

#: includes/gutenberg/headline-tool/headline-tool.php:943
msgid "there"
msgstr "there"

#: includes/gutenberg/headline-tool/headline-tool.php:941
msgid "their"
msgstr "their"

#: includes/gutenberg/headline-tool/headline-tool.php:939
msgid "the"
msgstr "the"

#: includes/gutenberg/headline-tool/headline-tool.php:938
msgid "with"
msgstr "with"

#: includes/gutenberg/headline-tool/headline-tool.php:937
msgid "that"
msgstr "that"

#: includes/gutenberg/headline-tool/headline-tool.php:935
msgid "so"
msgstr "so"

#: includes/gutenberg/headline-tool/headline-tool.php:934
msgid "why"
msgstr "why"

#: includes/gutenberg/headline-tool/headline-tool.php:933
msgid "sould"
msgstr "should"

#: includes/gutenberg/headline-tool/headline-tool.php:932
msgid "who"
msgstr "who"

#: includes/gutenberg/headline-tool/headline-tool.php:929
msgid "re"
msgstr "re"

#: includes/gutenberg/headline-tool/headline-tool.php:927
msgid "on"
msgstr "on"

#: includes/gutenberg/headline-tool/headline-tool.php:926
msgid "was"
msgstr "was"

#: includes/gutenberg/headline-tool/headline-tool.php:925
msgid "of"
msgstr "of"

#: includes/gutenberg/headline-tool/headline-tool.php:924
msgid "up"
msgstr "up"

#: includes/gutenberg/headline-tool/headline-tool.php:923
msgid "not"
msgstr "not"

#: includes/gutenberg/headline-tool/headline-tool.php:922
msgid "to"
msgstr "to"

#: includes/gutenberg/headline-tool/headline-tool.php:921
msgid "no"
msgstr "no"

#: includes/gutenberg/headline-tool/headline-tool.php:920
msgid "this"
msgstr "this"

#: includes/gutenberg/headline-tool/headline-tool.php:918
msgid "things"
msgstr "things"

#: includes/gutenberg/headline-tool/headline-tool.php:917
msgid "most"
msgstr "most"

#: includes/gutenberg/headline-tool/headline-tool.php:915
msgid "me"
msgstr "me"

#: includes/gutenberg/headline-tool/headline-tool.php:914
msgid "these"
msgstr "these"

#: includes/gutenberg/headline-tool/headline-tool.php:913
msgid "ll"
msgstr "I’ll"

#: includes/gutenberg/headline-tool/headline-tool.php:912
msgid "like"
msgstr "like"

#: includes/gutenberg/headline-tool/headline-tool.php:911
msgid "ever"
msgstr "ever"

#: includes/gutenberg/headline-tool/headline-tool.php:910
msgid "just"
msgstr "just"

#: includes/gutenberg/headline-tool/headline-tool.php:907
msgid "did"
msgstr "did"

#: includes/gutenberg/headline-tool/headline-tool.php:906
msgid "is"
msgstr "is"

#: includes/gutenberg/headline-tool/headline-tool.php:904
msgid "in"
msgstr "in"

#: includes/gutenberg/headline-tool/headline-tool.php:903
msgid "by"
msgstr "by"

#: includes/gutenberg/headline-tool/headline-tool.php:902
msgid "if"
msgstr "if"

#: includes/gutenberg/headline-tool/headline-tool.php:901
msgid "but"
msgstr "but"

#: includes/gutenberg/headline-tool/headline-tool.php:900
msgid "I"
msgstr "I"

#: includes/gutenberg/headline-tool/headline-tool.php:899
msgid "be"
msgstr "be"

#: includes/gutenberg/headline-tool/headline-tool.php:897
msgid "at"
msgstr "at"

#: includes/gutenberg/headline-tool/headline-tool.php:896
msgid "his"
msgstr "his"

#: includes/gutenberg/headline-tool/headline-tool.php:895
msgid "as"
msgstr "as"

#: includes/gutenberg/headline-tool/headline-tool.php:894
msgid "her"
msgstr "her"

#: includes/gutenberg/headline-tool/headline-tool.php:891
msgid "and"
msgstr "and"

#: includes/gutenberg/headline-tool/headline-tool.php:889
msgid "an"
msgstr "an"

#: includes/gutenberg/headline-tool/headline-tool.php:887
msgid "all"
msgstr "all"

#: includes/gutenberg/headline-tool/headline-tool.php:886
msgid "get"
msgstr "get"

#: includes/gutenberg/headline-tool/headline-tool.php:885
msgid "after"
msgstr "after"

#: includes/gutenberg/headline-tool/headline-tool.php:884
msgid "from"
msgstr "from"

#: includes/gutenberg/headline-tool/headline-tool.php:883
msgid "about"
msgstr "about"

#: includes/gutenberg/headline-tool/headline-tool.php:882
msgid "for"
msgstr "for"

#: includes/gutenberg/headline-tool/headline-tool.php:881
msgid "a"
msgstr "a"

#: includes/gutenberg/headline-tool/headline-tool.php:864
msgid "download"
msgstr "download"

#: includes/gutenberg/headline-tool/headline-tool.php:863
msgid "delighted"
msgstr "delighted"

#: includes/gutenberg/headline-tool/headline-tool.php:862
msgid "excellent"
msgstr "excellent"

#: includes/gutenberg/headline-tool/headline-tool.php:861
msgid "practical"
msgstr "practical"

#: includes/gutenberg/headline-tool/headline-tool.php:860
msgid "insider"
msgstr "insider"

#: includes/gutenberg/headline-tool/headline-tool.php:859
msgid "surprise"
msgstr "surprise"

#: includes/gutenberg/headline-tool/headline-tool.php:858
msgid "sensational"
msgstr "sensational"

#: includes/gutenberg/headline-tool/headline-tool.php:857
msgid "introducing"
msgstr "introducing"

#: includes/gutenberg/headline-tool/headline-tool.php:856
msgid "value"
msgstr "value"

#: includes/gutenberg/headline-tool/headline-tool.php:855
msgid "weird"
msgstr "weird"

#: includes/gutenberg/headline-tool/headline-tool.php:854
msgid "revealing"
msgstr "revealing"

#: includes/gutenberg/headline-tool/headline-tool.php:853
msgid "huge gift"
msgstr "huge gift"

#: includes/gutenberg/headline-tool/headline-tool.php:852
msgid "strange"
msgstr "strange"

#: includes/gutenberg/headline-tool/headline-tool.php:851
msgid "reward"
msgstr "reward"

#: includes/gutenberg/headline-tool/headline-tool.php:850
msgid "portfolio"
msgstr "portfolio"

#: includes/gutenberg/headline-tool/headline-tool.php:849
msgid "announcing"
msgstr "announcing"

#: includes/gutenberg/headline-tool/headline-tool.php:848
msgid "simple"
msgstr "simple"

#: includes/gutenberg/headline-tool/headline-tool.php:847
msgid "proven"
msgstr "proven"

#: includes/gutenberg/headline-tool/headline-tool.php:846
msgid "exciting"
msgstr "exciting"

#: includes/gutenberg/headline-tool/headline-tool.php:845
msgid "authentic"
msgstr "authentic"

#: includes/gutenberg/headline-tool/headline-tool.php:844
msgid "direct"
msgstr "direct"

#: includes/gutenberg/headline-tool/headline-tool.php:843
msgid "magic"
msgstr "magic"

#: includes/gutenberg/headline-tool/headline-tool.php:842
msgid "amazing"
msgstr "amazing"

#: includes/gutenberg/headline-tool/headline-tool.php:841
msgid "fortune"
msgstr "fortune"

#: includes/gutenberg/headline-tool/headline-tool.php:840
msgid "easy"
msgstr "easy"

#: includes/gutenberg/headline-tool/headline-tool.php:839
msgid "miracle"
msgstr "miracle"

#: includes/gutenberg/headline-tool/headline-tool.php:838
msgid "urgent"
msgstr "urgent"

#: includes/gutenberg/headline-tool/headline-tool.php:837
msgid "unlock"
msgstr "unlock"

#: includes/gutenberg/headline-tool/headline-tool.php:836
msgid "quick"
msgstr "quick"

#: includes/gutenberg/headline-tool/headline-tool.php:835
msgid "revolutionary"
msgstr "revolutionary"

#: includes/gutenberg/headline-tool/headline-tool.php:834
msgid "important"
msgstr "important"

#: includes/gutenberg/headline-tool/headline-tool.php:833
#: includes/gutenberg/headline-tool/headline-tool.php:975
msgid "now"
msgstr "now"

#: includes/gutenberg/headline-tool/headline-tool.php:832
msgid "startling"
msgstr "startling"

#: includes/gutenberg/headline-tool/headline-tool.php:831
msgid "quickly"
msgstr "quickly"

#: includes/gutenberg/headline-tool/headline-tool.php:830
msgid "latest"
msgstr "latest"

#: includes/gutenberg/headline-tool/headline-tool.php:829
msgid "colossal"
msgstr "colossal"

#: includes/gutenberg/headline-tool/headline-tool.php:828
msgid "monumental"
msgstr "monumental"

#: includes/gutenberg/headline-tool/headline-tool.php:827
msgid "refundable"
msgstr "refundable"

#: includes/gutenberg/headline-tool/headline-tool.php:826
msgid "high tech"
msgstr "high tech"

#: includes/gutenberg/headline-tool/headline-tool.php:825
msgid "largest"
msgstr "largest"

#: includes/gutenberg/headline-tool/headline-tool.php:824
msgid "last minute"
msgstr "last minute"

#: includes/gutenberg/headline-tool/headline-tool.php:823
msgid "security"
msgstr "security"

#: includes/gutenberg/headline-tool/headline-tool.php:822
msgid "greatest"
msgstr "greatest"

#: includes/gutenberg/headline-tool/headline-tool.php:821
msgid "survival"
msgstr "survival"

#: includes/gutenberg/headline-tool/headline-tool.php:820
msgid "opportunities"
msgstr "opportunities"

#: includes/gutenberg/headline-tool/headline-tool.php:819
msgid "bonanza"
msgstr "bonanza"

#: includes/gutenberg/headline-tool/headline-tool.php:818
msgid "imagination"
msgstr "imagination"

#: includes/gutenberg/headline-tool/headline-tool.php:817
msgid "useful"
msgstr "useful"

#: includes/gutenberg/headline-tool/headline-tool.php:816
msgid "successful"
msgstr "successful"

#: includes/gutenberg/headline-tool/headline-tool.php:815
msgid "timely"
msgstr "timely"

#: includes/gutenberg/headline-tool/headline-tool.php:814
msgid "astonishing"
msgstr "astonishing"

#: includes/gutenberg/headline-tool/headline-tool.php:813
msgid "spotlight"
msgstr "spotlight"

#: includes/gutenberg/headline-tool/headline-tool.php:812
msgid "wealth"
msgstr "wealth"

#: includes/gutenberg/headline-tool/headline-tool.php:811
msgid "unconditional"
msgstr "unconditional"

#: includes/gutenberg/headline-tool/headline-tool.php:810
msgid "promising"
msgstr "promising"

#: includes/gutenberg/headline-tool/headline-tool.php:809
msgid "enormous"
msgstr "enormous"

#: includes/gutenberg/headline-tool/headline-tool.php:808
msgid "sturdy"
msgstr "sturdy"

#: includes/gutenberg/headline-tool/headline-tool.php:807
msgid "luxury"
msgstr "luxury"

#: includes/gutenberg/headline-tool/headline-tool.php:806
msgid "growth"
msgstr "growth"

#: includes/gutenberg/headline-tool/headline-tool.php:805
msgid "shrewd"
msgstr "shrewd"

#: includes/gutenberg/headline-tool/headline-tool.php:804
msgid "selected"
msgstr "selected"

#: includes/gutenberg/headline-tool/headline-tool.php:803
msgid "noted"
msgstr "noted"

#: includes/gutenberg/headline-tool/headline-tool.php:802
msgid "crammed"
msgstr "crammed"

#: includes/gutenberg/headline-tool/headline-tool.php:801
#: includes/gutenberg/headline-tool/headline-tool.php:985
msgid "better"
msgstr "better"

#: includes/gutenberg/headline-tool/headline-tool.php:800
msgid "technology"
msgstr "technology"

#: includes/gutenberg/headline-tool/headline-tool.php:799
msgid "sampler"
msgstr "sampler"

#: includes/gutenberg/headline-tool/headline-tool.php:798
#: includes/gutenberg/headline-tool/headline-tool.php:973
msgid "beautiful"
msgstr "beautiful"

#: includes/gutenberg/headline-tool/headline-tool.php:797
msgid "reduced"
msgstr "reduced"

#: includes/gutenberg/headline-tool/headline-tool.php:796
msgid "special offer"
msgstr "special offer"

#: includes/gutenberg/headline-tool/headline-tool.php:794
msgid "soar"
msgstr "soar"

#: includes/gutenberg/headline-tool/headline-tool.php:793
msgid "skill"
msgstr "skill"

#: includes/gutenberg/headline-tool/headline-tool.php:792
msgid "helpful"
msgstr "helpful"

#: includes/gutenberg/headline-tool/headline-tool.php:791
msgid "emerging"
msgstr "emerging"

#: includes/gutenberg/headline-tool/headline-tool.php:789
msgid "launching"
msgstr "launching"

#: includes/gutenberg/headline-tool/headline-tool.php:788
msgid "tremendous"
msgstr "tremendous"

#: includes/gutenberg/headline-tool/headline-tool.php:787
msgid "breakthrough"
msgstr "breakthrough"

#: includes/gutenberg/headline-tool/headline-tool.php:786
msgid "just arrived"
msgstr "just arrived"

#: includes/gutenberg/headline-tool/headline-tool.php:785
msgid "perspective"
msgstr "perspective"

#: includes/gutenberg/headline-tool/headline-tool.php:782
msgid "it's here"
msgstr "it's here"

#: includes/gutenberg/headline-tool/headline-tool.php:781
msgid "suddenly"
msgstr "suddenly"

#: includes/gutenberg/headline-tool/headline-tool.php:780
msgid "zinger"
msgstr "zinger"

#: includes/gutenberg/headline-tool/headline-tool.php:779
msgid "reliable"
msgstr "reliable"

#: includes/gutenberg/headline-tool/headline-tool.php:778
msgid "innovative"
msgstr "innovative"

#: includes/gutenberg/headline-tool/headline-tool.php:777
msgid "under priced"
msgstr "under priced"

#: includes/gutenberg/headline-tool/headline-tool.php:776
msgid "bottom line"
msgstr "bottom line"

#: includes/gutenberg/headline-tool/headline-tool.php:775
msgid "lavishly"
msgstr "lavishly"

#: includes/gutenberg/headline-tool/headline-tool.php:774
msgid "mammoth"
msgstr "mammoth"

#: includes/gutenberg/headline-tool/headline-tool.php:773
msgid "fundamentals"
msgstr "fundamentals"

#: includes/gutenberg/headline-tool/headline-tool.php:772
msgid "odd"
msgstr "odd"

#: includes/gutenberg/headline-tool/headline-tool.php:771
#: includes/gutenberg/headline-tool/headline-tool.php:994
msgid "love"
msgstr "love"

#: includes/gutenberg/headline-tool/headline-tool.php:770
msgid "full"
msgstr "full"

#: includes/gutenberg/headline-tool/headline-tool.php:769
msgid "discount"
msgstr "discount"

#: includes/gutenberg/headline-tool/headline-tool.php:768
msgid "compromise"
msgstr "compromise"

#: includes/gutenberg/headline-tool/headline-tool.php:767
msgid "gigantic"
msgstr "gigantic"

#: includes/gutenberg/headline-tool/headline-tool.php:766
msgid "competitive"
msgstr "competitive"

#: includes/gutenberg/headline-tool/headline-tool.php:765
msgid "unlimited"
msgstr "unlimited"

#: includes/gutenberg/headline-tool/headline-tool.php:764
msgid "fascinating"
msgstr "fascinating"

#: includes/gutenberg/headline-tool/headline-tool.php:763
msgid "quality"
msgstr "quality"

#: includes/gutenberg/headline-tool/headline-tool.php:762
msgid "approved"
msgstr "approved"

#: includes/gutenberg/headline-tool/headline-tool.php:761
msgid "endorsed"
msgstr "endorsed"

#: includes/gutenberg/headline-tool/headline-tool.php:760
msgid "unparalleled"
msgstr "unparalleled"

#: includes/gutenberg/headline-tool/headline-tool.php:759
msgid "exploit"
msgstr "exploit"

#: includes/gutenberg/headline-tool/headline-tool.php:758
msgid "easily"
msgstr "easily"

#: includes/gutenberg/headline-tool/headline-tool.php:756
msgid "superior"
msgstr "superior"

#: includes/gutenberg/headline-tool/headline-tool.php:755
msgid "last chance"
msgstr "last chance"

#: includes/gutenberg/headline-tool/headline-tool.php:754
msgid "attractive"
msgstr "attractive"

#: includes/gutenberg/headline-tool/headline-tool.php:753
msgid "valuable"
msgstr "valuable"

#: includes/gutenberg/headline-tool/headline-tool.php:752
msgid "edge"
msgstr "edge"

#: includes/gutenberg/headline-tool/headline-tool.php:751
msgid "complete"
msgstr "complete"

#: includes/gutenberg/headline-tool/headline-tool.php:750
msgid "willpower"
msgstr "willpower"

#: includes/gutenberg/headline-tool/headline-tool.php:749
msgid "exclusive"
msgstr "exclusive"

#: includes/gutenberg/headline-tool/headline-tool.php:748
msgid "rare"
msgstr "rare"

#: includes/gutenberg/headline-tool/headline-tool.php:747
msgid "mainstream"
msgstr "mainstream"

#: includes/gutenberg/headline-tool/headline-tool.php:746
msgid "ultimate"
msgstr "ultimate"

#: includes/gutenberg/headline-tool/headline-tool.php:745
msgid "popular"
msgstr "popular"

#: includes/gutenberg/headline-tool/headline-tool.php:744
msgid "liberal"
msgstr "liberal"

#: includes/gutenberg/headline-tool/headline-tool.php:743
msgid "informative"
msgstr "informative"

#: includes/gutenberg/headline-tool/headline-tool.php:742
msgid "affordable"
msgstr "affordable"

#: includes/gutenberg/headline-tool/headline-tool.php:741
msgid "big"
msgstr "big"

#: includes/gutenberg/headline-tool/headline-tool.php:740
msgid "instructive"
msgstr "instructive"

#: includes/gutenberg/headline-tool/headline-tool.php:739
msgid "genuine"
msgstr "genuine"

#: includes/gutenberg/headline-tool/headline-tool.php:738
msgid "colorful"
msgstr "colourful"

#: includes/gutenberg/headline-tool/headline-tool.php:737
msgid "powerful"
msgstr "powerful"

#: includes/gutenberg/headline-tool/headline-tool.php:736
msgid "energy"
msgstr "energy"

#: includes/gutenberg/headline-tool/headline-tool.php:735
msgid "unsurpassed"
msgstr "unsurpassed"

#: includes/gutenberg/headline-tool/headline-tool.php:734
msgid "compare"
msgstr "compare"

#: includes/gutenberg/headline-tool/headline-tool.php:733
msgid "simplistic"
msgstr "simplistic"

#: includes/gutenberg/headline-tool/headline-tool.php:732
msgid "outstanding"
msgstr "outstanding"

#: includes/gutenberg/headline-tool/headline-tool.php:731
msgid "destiny"
msgstr "destiny"

#: includes/gutenberg/headline-tool/headline-tool.php:730
msgid "the truth about"
msgstr "the truth about"

#: includes/gutenberg/headline-tool/headline-tool.php:729
msgid "limited"
msgstr "limited"

#: includes/gutenberg/headline-tool/headline-tool.php:728
msgid "unusual"
msgstr "unusual"

#: includes/gutenberg/headline-tool/headline-tool.php:727
msgid "pioneering"
msgstr "pioneering"

#: includes/gutenberg/headline-tool/headline-tool.php:726
msgid "advice"
msgstr "advice"

#: includes/gutenberg/headline-tool/headline-tool.php:725
msgid "immediately"
msgstr "immediately"

#: includes/gutenberg/headline-tool/headline-tool.php:724
msgid "strong"
msgstr "strong"

#: includes/gutenberg/headline-tool/headline-tool.php:723
msgid "daring"
msgstr "daring"

#: includes/gutenberg/headline-tool/headline-tool.php:722
msgid "expert"
msgstr "expert"

#: includes/gutenberg/headline-tool/headline-tool.php:721
msgid "improved"
msgstr "improved"

#: includes/gutenberg/headline-tool/headline-tool.php:720
msgid "alert famous"
msgstr "alert famous"

#: includes/gutenberg/headline-tool/headline-tool.php:719
msgid "hurry"
msgstr "hurry"

#: includes/gutenberg/headline-tool/headline-tool.php:718
msgid "highest"
msgstr "highest"

#: includes/gutenberg/headline-tool/headline-tool.php:717
msgid "tested"
msgstr "tested"

#: includes/gutenberg/headline-tool/headline-tool.php:716
msgid "scarce"
msgstr "scarce"

#: includes/gutenberg/headline-tool/headline-tool.php:715
msgid "bargain"
msgstr "bargain"

#: includes/gutenberg/headline-tool/headline-tool.php:714
msgid "lifetime"
msgstr "lifetime"

#: includes/gutenberg/headline-tool/headline-tool.php:713
msgid "special"
msgstr "special"

#: includes/gutenberg/headline-tool/headline-tool.php:712
msgid "secrets"
msgstr "secrets"

#: includes/gutenberg/headline-tool/headline-tool.php:710
msgid "challenge"
msgstr "challenge"

#: includes/gutenberg/headline-tool/headline-tool.php:709
msgid "guaranteed"
msgstr "guaranteed"

#: includes/gutenberg/headline-tool/headline-tool.php:708
msgid "delivered"
msgstr "delivered"

#: includes/gutenberg/headline-tool/headline-tool.php:707
msgid "revisited"
msgstr "revisited"

#: includes/gutenberg/headline-tool/headline-tool.php:706
msgid "interesting"
msgstr "interesting"

#: includes/gutenberg/headline-tool/headline-tool.php:705
msgid "professional"
msgstr "professional"

#: includes/gutenberg/headline-tool/headline-tool.php:704
msgid "wonderful"
msgstr "wonderful"

#: includes/gutenberg/headline-tool/headline-tool.php:703
msgid "surging"
msgstr "surging"

#: includes/gutenberg/headline-tool/headline-tool.php:702
msgid "absolutely lowest"
msgstr "absolutely lowest"

#: includes/gutenberg/headline-tool/headline-tool.php:701
#: includes/gutenberg/headline-tool/headline-tool.php:971
msgid "new"
msgstr "new"

#: includes/gutenberg/headline-tool/headline-tool.php:700
msgid "sizable"
msgstr "sizeable"

#: includes/gutenberg/headline-tool/headline-tool.php:699
msgid "obsession"
msgstr "obsession"

#: includes/gutenberg/headline-tool/headline-tool.php:698
msgid "wanted"
msgstr "wanted"

#: includes/gutenberg/headline-tool/headline-tool.php:697
msgid "sale"
msgstr "sale"

#: includes/gutenberg/headline-tool/headline-tool.php:696
msgid "confidential"
msgstr "confidential"

#: includes/gutenberg/headline-tool/headline-tool.php:695
msgid "remarkable"
msgstr "remarkable"

#: includes/gutenberg/headline-tool/headline-tool.php:694
msgid "focus"
msgstr "focus"

#: includes/gutenberg/headline-tool/headline-tool.php:693
msgid "free"
msgstr "free"

#: includes/gutenberg/headline-tool/headline-tool.php:692
msgid "great"
msgstr "great"

#: includes/gutenberg/headline-tool/headline-tool.php:675
msgid "vaporize"
msgstr "vaporise"

#: includes/gutenberg/headline-tool/headline-tool.php:674
msgid "to be"
msgstr "to be"

#: includes/gutenberg/headline-tool/headline-tool.php:673
msgid "surprisingly"
msgstr "surprisingly"

#: includes/gutenberg/headline-tool/headline-tool.php:672
msgid "you see"
msgstr "you see"

#: includes/gutenberg/headline-tool/headline-tool.php:671
msgid "tired"
msgstr "tired"

#: includes/gutenberg/headline-tool/headline-tool.php:670
msgid "surprising"
msgstr "surprising"

#: includes/gutenberg/headline-tool/headline-tool.php:669
msgid "you see what"
msgstr "you see what"

#: includes/gutenberg/headline-tool/headline-tool.php:668
msgid "you need to"
msgstr "you need to"

#: includes/gutenberg/headline-tool/headline-tool.php:667
msgid "valor"
msgstr "valour"

#: includes/gutenberg/headline-tool/headline-tool.php:666
msgid "thrilling"
msgstr "thrilling"

#: includes/gutenberg/headline-tool/headline-tool.php:665
msgid "you need to know"
msgstr "you need to know"

#: includes/gutenberg/headline-tool/headline-tool.php:664
msgid "thrilled"
msgstr "thrilled"

#: includes/gutenberg/headline-tool/headline-tool.php:663
msgid "surge"
msgstr "surge"

#: includes/gutenberg/headline-tool/headline-tool.php:662
msgid "wounded"
msgstr "wounded"

#: includes/gutenberg/headline-tool/headline-tool.php:661
msgid "this is"
msgstr "this is"

#: includes/gutenberg/headline-tool/headline-tool.php:660
msgid "sure"
msgstr "sure"

#: includes/gutenberg/headline-tool/headline-tool.php:659
msgid "worry"
msgstr "worry"

#: includes/gutenberg/headline-tool/headline-tool.php:658
msgid "uplifting"
msgstr "uplifting"

#: includes/gutenberg/headline-tool/headline-tool.php:657
msgid "this is what"
msgstr "this is what"

#: includes/gutenberg/headline-tool/headline-tool.php:656
msgid "wondrous"
msgstr "wondrous"

#: includes/gutenberg/headline-tool/headline-tool.php:655
msgid "unusually"
msgstr "unusually"

#: includes/gutenberg/headline-tool/headline-tool.php:654
msgid "this is what happens"
msgstr "this is what happens"

#: includes/gutenberg/headline-tool/headline-tool.php:653
msgid "this is the"
msgstr "this is the"

#: includes/gutenberg/headline-tool/headline-tool.php:652
msgid "withheld"
msgstr "withheld"

#: includes/gutenberg/headline-tool/headline-tool.php:651
msgid "thing ive ever seen"
msgstr "thing I've ever seen"

#: includes/gutenberg/headline-tool/headline-tool.php:650
msgid "unscrupulous"
msgstr "unscrupulous"

#: includes/gutenberg/headline-tool/headline-tool.php:649
msgid "the reason why is"
msgstr "the reason why is"

#: includes/gutenberg/headline-tool/headline-tool.php:648
msgid "stupid"
msgstr "stupid"

#: includes/gutenberg/headline-tool/headline-tool.php:647
msgid "will make you"
msgstr "will make you"

#: includes/gutenberg/headline-tool/headline-tool.php:646
msgid "the most"
msgstr "the most"

#: includes/gutenberg/headline-tool/headline-tool.php:645
msgid "stunning"
msgstr "stunning"

#: includes/gutenberg/headline-tool/headline-tool.php:644
msgid "wicked"
msgstr "wicked"

#: includes/gutenberg/headline-tool/headline-tool.php:643
msgid "the ranking of"
msgstr "the ranking of"

#: includes/gutenberg/headline-tool/headline-tool.php:642
msgid "stuck up"
msgstr "stuck up"

#: includes/gutenberg/headline-tool/headline-tool.php:641
msgid "whopping"
msgstr "whopping"

#: includes/gutenberg/headline-tool/headline-tool.php:640
msgid "the best"
msgstr "the best"

#: includes/gutenberg/headline-tool/headline-tool.php:639
msgid "whip"
msgstr "whip"

#: includes/gutenberg/headline-tool/headline-tool.php:638
msgid "that will"
msgstr "that will"

#: includes/gutenberg/headline-tool/headline-tool.php:637
msgid "strangle"
msgstr "strangle"

#: includes/gutenberg/headline-tool/headline-tool.php:636
msgid "when you"
msgstr "when you"

#: includes/gutenberg/headline-tool/headline-tool.php:635
msgid "unexpected"
msgstr "unexpected"

#: includes/gutenberg/headline-tool/headline-tool.php:634
msgid "that will make"
msgstr "that will make"

#: includes/gutenberg/headline-tool/headline-tool.php:633
msgid "undo"
msgstr "undo"

#: includes/gutenberg/headline-tool/headline-tool.php:632
msgid "that will make you"
msgstr "that will make you"

#: includes/gutenberg/headline-tool/headline-tool.php:631
msgid "what this"
msgstr "what this"

#: includes/gutenberg/headline-tool/headline-tool.php:630
msgid "underhanded"
msgstr "underhanded"

#: includes/gutenberg/headline-tool/headline-tool.php:629
msgid "staggering"
msgstr "staggering"

#: includes/gutenberg/headline-tool/headline-tool.php:628
msgid "what happens"
msgstr "what happens"

#: includes/gutenberg/headline-tool/headline-tool.php:627
msgid "under"
msgstr "under"

#: includes/gutenberg/headline-tool/headline-tool.php:626
msgid "terror"
msgstr "terror"

#: includes/gutenberg/headline-tool/headline-tool.php:625
msgid "what happens when"
msgstr "what happens when"

#: includes/gutenberg/headline-tool/headline-tool.php:624
msgid "spirit"
msgstr "spirit"

#: includes/gutenberg/headline-tool/headline-tool.php:623
msgid "what happened"
msgstr "what happened"

#: includes/gutenberg/headline-tool/headline-tool.php:622
msgid "uncommonly"
msgstr "uncommonly"

#: includes/gutenberg/headline-tool/headline-tool.php:621
msgid "tempting"
msgstr "tempting"

#: includes/gutenberg/headline-tool/headline-tool.php:620
msgid "spine"
msgstr "spine"

#: includes/gutenberg/headline-tool/headline-tool.php:619
msgid "unbelievably"
msgstr "unbelievably"

#: includes/gutenberg/headline-tool/headline-tool.php:618
msgid "temporary fix"
msgstr "temporary fix"

#: includes/gutenberg/headline-tool/headline-tool.php:617
msgid "spectacular"
msgstr "spectacular"

#: includes/gutenberg/headline-tool/headline-tool.php:616
msgid "unauthorized"
msgstr "unauthorised"

#: includes/gutenberg/headline-tool/headline-tool.php:615
msgid "teetering"
msgstr "teetering"

#: includes/gutenberg/headline-tool/headline-tool.php:614
msgid "warning"
msgstr "warning"

#: includes/gutenberg/headline-tool/headline-tool.php:613
msgid "soaring"
msgstr "soaring"

#: includes/gutenberg/headline-tool/headline-tool.php:612
msgid "wanton"
msgstr "wanton"

#: includes/gutenberg/headline-tool/headline-tool.php:611
msgid "turn the tables"
msgstr "turn the tables"

#: includes/gutenberg/headline-tool/headline-tool.php:610
msgid "tech"
msgstr "tech"

#: includes/gutenberg/headline-tool/headline-tool.php:609
msgid "try before you buy"
msgstr "try before you buy"

#: includes/gutenberg/headline-tool/headline-tool.php:608
msgid "tawdry"
msgstr "tawdry"

#: includes/gutenberg/headline-tool/headline-tool.php:607
msgid "snob"
msgstr "snob"

#: includes/gutenberg/headline-tool/headline-tool.php:606
msgid "vulnerable"
msgstr "vulnerable"

#: includes/gutenberg/headline-tool/headline-tool.php:605
msgid "truth"
msgstr "truth"

#: includes/gutenberg/headline-tool/headline-tool.php:604
msgid "targeted"
msgstr "targeted"

#: includes/gutenberg/headline-tool/headline-tool.php:603
msgid "sniveling"
msgstr "sniveling"

#: includes/gutenberg/headline-tool/headline-tool.php:602
msgid "volatile"
msgstr "volatile"

#: includes/gutenberg/headline-tool/headline-tool.php:601
msgid "triumph"
msgstr "triumph"

#: includes/gutenberg/headline-tool/headline-tool.php:600
msgid "tantalizing"
msgstr "tantalising"

#: includes/gutenberg/headline-tool/headline-tool.php:599
msgid "smuggled"
msgstr "smuggled"

#: includes/gutenberg/headline-tool/headline-tool.php:598
msgid "viral"
msgstr "viral"

#: includes/gutenberg/headline-tool/headline-tool.php:597
msgid "triple"
msgstr "triple"

#: includes/gutenberg/headline-tool/headline-tool.php:596
msgid "tank"
msgstr "tank"

#: includes/gutenberg/headline-tool/headline-tool.php:595
msgid "smug"
msgstr "smug"

#: includes/gutenberg/headline-tool/headline-tool.php:594
msgid "vindication"
msgstr "vindication"

#: includes/gutenberg/headline-tool/headline-tool.php:593
msgid "tailspin"
msgstr "tailspin"

#: includes/gutenberg/headline-tool/headline-tool.php:592
msgid "smash"
msgstr "smash"

#: includes/gutenberg/headline-tool/headline-tool.php:591
msgid "victory"
msgstr "victory"

#: includes/gutenberg/headline-tool/headline-tool.php:590
msgid "treasure"
msgstr "treasure"

#: includes/gutenberg/headline-tool/headline-tool.php:589
msgid "taboo"
msgstr "taboo"

#: includes/gutenberg/headline-tool/headline-tool.php:588
msgid "sleazy"
msgstr "sleazy"

#: includes/gutenberg/headline-tool/headline-tool.php:587
msgid "victim"
msgstr "victim"

#: includes/gutenberg/headline-tool/headline-tool.php:586
msgid "trap"
msgstr "trap"

#: includes/gutenberg/headline-tool/headline-tool.php:585
msgid "swindle"
msgstr "swindle"

#: includes/gutenberg/headline-tool/headline-tool.php:584
msgid "slaughter"
msgstr "slaughter"

#: includes/gutenberg/headline-tool/headline-tool.php:583
msgid "vibrant"
msgstr "vibrant"

#: includes/gutenberg/headline-tool/headline-tool.php:582
msgid "toxic"
msgstr "toxic"

#: includes/gutenberg/headline-tool/headline-tool.php:581
msgid "skyrocket"
msgstr "skyrocket"

#: includes/gutenberg/headline-tool/headline-tool.php:580
msgid "varify"
msgstr "varify"

#: includes/gutenberg/headline-tool/headline-tool.php:579
msgid "to the"
msgstr "to the"

#: includes/gutenberg/headline-tool/headline-tool.php:578
msgid "surrender"
msgstr "surrender"

#: includes/gutenberg/headline-tool/headline-tool.php:577
msgid "research"
msgstr "research"

#: includes/gutenberg/headline-tool/headline-tool.php:576
msgid "never again"
msgstr "never again"

#: includes/gutenberg/headline-tool/headline-tool.php:574
msgid "remarkably"
msgstr "remarkably"

#: includes/gutenberg/headline-tool/headline-tool.php:573
msgid "poor"
msgstr "poor"

#: includes/gutenberg/headline-tool/headline-tool.php:572
msgid "myths"
msgstr "myths"

#: includes/gutenberg/headline-tool/headline-tool.php:571
msgid "sinful"
msgstr "sinful"

#: includes/gutenberg/headline-tool/headline-tool.php:570
msgid "pointless"
msgstr "pointless"

#: includes/gutenberg/headline-tool/headline-tool.php:569
msgid "murder"
msgstr "murder"

#: includes/gutenberg/headline-tool/headline-tool.php:568
msgid "plunge"
msgstr "plunge"

#: includes/gutenberg/headline-tool/headline-tool.php:567
msgid "plummet"
msgstr "plummet"

#: includes/gutenberg/headline-tool/headline-tool.php:566
msgid "moneyback"
msgstr "moneyback"

#: includes/gutenberg/headline-tool/headline-tool.php:565
msgid "refund"
msgstr "refund"

#: includes/gutenberg/headline-tool/headline-tool.php:564
msgid "pluck"
msgstr "pluck"

#: includes/gutenberg/headline-tool/headline-tool.php:563
msgid "money-grubbing"
msgstr "money-grubbing"

#: includes/gutenberg/headline-tool/headline-tool.php:562
msgid "silly"
msgstr "silly"

#: includes/gutenberg/headline-tool/headline-tool.php:561
msgid "refugee"
msgstr "refugee"

#: includes/gutenberg/headline-tool/headline-tool.php:560
msgid "played"
msgstr "played"

#: includes/gutenberg/headline-tool/headline-tool.php:559
msgid "money"
msgstr "money"

#: includes/gutenberg/headline-tool/headline-tool.php:558
msgid "sick and tired"
msgstr "sick and tired"

#: includes/gutenberg/headline-tool/headline-tool.php:557
msgid "plague"
msgstr "plague"

#: includes/gutenberg/headline-tool/headline-tool.php:556
msgid "mistakes"
msgstr "mistakes"

#: includes/gutenberg/headline-tool/headline-tool.php:555
msgid "reclaim"
msgstr "reclaim"

#: includes/gutenberg/headline-tool/headline-tool.php:554
msgid "pitfall"
msgstr "pitfall"

#: includes/gutenberg/headline-tool/headline-tool.php:553
msgid "mired"
msgstr "mired"

#: includes/gutenberg/headline-tool/headline-tool.php:552
msgid "shellacking"
msgstr "shellacking"

#: includes/gutenberg/headline-tool/headline-tool.php:551
msgid "reckoning"
msgstr "reckoning"

#: includes/gutenberg/headline-tool/headline-tool.php:550
msgid "piranha"
msgstr "piranha"

#: includes/gutenberg/headline-tool/headline-tool.php:549
msgid "shatter"
msgstr "shatter"

#: includes/gutenberg/headline-tool/headline-tool.php:548
msgid "rave"
msgstr "rave"

#: includes/gutenberg/headline-tool/headline-tool.php:547
msgid "minute"
msgstr "minute"

#: includes/gutenberg/headline-tool/headline-tool.php:546
msgid "shameless"
msgstr "shameless"

#: includes/gutenberg/headline-tool/headline-tool.php:545
msgid "mind-blowing"
msgstr "mind-blowing"

#: includes/gutenberg/headline-tool/headline-tool.php:544
msgid "peril"
msgstr "peril"

#: includes/gutenberg/headline-tool/headline-tool.php:543
msgid "might look like a"
msgstr "might look like a"

#: includes/gutenberg/headline-tool/headline-tool.php:542
msgid "payback"
msgstr "payback"

#: includes/gutenberg/headline-tool/headline-tool.php:541
msgid "meltdown"
msgstr "meltdown"

#: includes/gutenberg/headline-tool/headline-tool.php:540
msgid "seize"
msgstr "seize"

#: includes/gutenberg/headline-tool/headline-tool.php:539
msgid "pay zero"
msgstr "pay zero"

#: includes/gutenberg/headline-tool/headline-tool.php:538
msgid "massive"
msgstr "massive"

#: includes/gutenberg/headline-tool/headline-tool.php:537
msgid "secutively"
msgstr "consecutively"

#: includes/gutenberg/headline-tool/headline-tool.php:536
msgid "quadruple"
msgstr "quadruple"

#: includes/gutenberg/headline-tool/headline-tool.php:535
msgid "panic"
msgstr "panic"

#: includes/gutenberg/headline-tool/headline-tool.php:534
msgid "marked down"
msgstr "marked down"

#: includes/gutenberg/headline-tool/headline-tool.php:533
msgid "punish"
msgstr "punish"

#: includes/gutenberg/headline-tool/headline-tool.php:532
msgid "pale"
msgstr "pale"

#: includes/gutenberg/headline-tool/headline-tool.php:531
msgid "secure"
msgstr "secure"

#: includes/gutenberg/headline-tool/headline-tool.php:530
msgid "pummel"
msgstr "pummel"

#: includes/gutenberg/headline-tool/headline-tool.php:529
msgid "painful"
msgstr "painful"

#: includes/gutenberg/headline-tool/headline-tool.php:528
msgid "make you"
msgstr "make you"

#: includes/gutenberg/headline-tool/headline-tool.php:527
msgid "provocative"
msgstr "provocative"

#: includes/gutenberg/headline-tool/headline-tool.php:526
msgid "overcome"
msgstr "overcome"

#: includes/gutenberg/headline-tool/headline-tool.php:525
msgid "searing"
msgstr "searing"

#: includes/gutenberg/headline-tool/headline-tool.php:524
msgid "scream"
msgstr "scream"

#: includes/gutenberg/headline-tool/headline-tool.php:523
msgid "protected"
msgstr "protected"

#: includes/gutenberg/headline-tool/headline-tool.php:522
msgid "outlawed"
msgstr "outlawed"

#: includes/gutenberg/headline-tool/headline-tool.php:521
msgid "lying"
msgstr "lying"

#: includes/gutenberg/headline-tool/headline-tool.php:520
msgid "scary"
msgstr "scary"

#: includes/gutenberg/headline-tool/headline-tool.php:518
msgid "on the"
msgstr "on the"

#: includes/gutenberg/headline-tool/headline-tool.php:517
msgid "luxurious"
msgstr "luxurious"

#: includes/gutenberg/headline-tool/headline-tool.php:516
msgid "official"
msgstr "official"

#: includes/gutenberg/headline-tool/headline-tool.php:515
msgid "lust"
msgstr "lust"

#: includes/gutenberg/headline-tool/headline-tool.php:514
msgid "ruthless"
msgstr "ruthless"

#: includes/gutenberg/headline-tool/headline-tool.php:513
msgid "prize"
msgstr "prize"

#: includes/gutenberg/headline-tool/headline-tool.php:512
msgid "offer"
msgstr "offer"

#: includes/gutenberg/headline-tool/headline-tool.php:511
msgid "lurking"
msgstr "lurking"

#: includes/gutenberg/headline-tool/headline-tool.php:510
msgid "risky"
msgstr "risky"

#: includes/gutenberg/headline-tool/headline-tool.php:509
msgid "private"
msgstr "private"

#: includes/gutenberg/headline-tool/headline-tool.php:508
msgid "off-limits"
msgstr "off-limits"

#: includes/gutenberg/headline-tool/headline-tool.php:507
msgid "lunatic"
msgstr "lunatic"

#: includes/gutenberg/headline-tool/headline-tool.php:506
msgid "rich"
msgstr "rich"

#: includes/gutenberg/headline-tool/headline-tool.php:505
msgid "privacy"
msgstr "privacy"

#: includes/gutenberg/headline-tool/headline-tool.php:504
msgid "of the"
msgstr "of the"

#: includes/gutenberg/headline-tool/headline-tool.php:503
msgid "lowest"
msgstr "lowest"

#: includes/gutenberg/headline-tool/headline-tool.php:502
msgid "prison"
msgstr "prison"

#: includes/gutenberg/headline-tool/headline-tool.php:501
msgid "lost"
msgstr "lost"

#: includes/gutenberg/headline-tool/headline-tool.php:500
msgid "priced"
msgstr "priced"

#: includes/gutenberg/headline-tool/headline-tool.php:499
msgid "looming"
msgstr "looming"

#: includes/gutenberg/headline-tool/headline-tool.php:498
msgid "revolting"
msgstr "revolting"

#: includes/gutenberg/headline-tool/headline-tool.php:497
msgid "preposterous"
msgstr "preposterous"

#: includes/gutenberg/headline-tool/headline-tool.php:496
msgid "obnoxious"
msgstr "obnoxious"

#: includes/gutenberg/headline-tool/headline-tool.php:495
msgid "looks like a"
msgstr "looks like a"

#: includes/gutenberg/headline-tool/headline-tool.php:494
msgid "lonely"
msgstr "lonely"

#: includes/gutenberg/headline-tool/headline-tool.php:493
msgid "revenge"
msgstr "revenge"

#: includes/gutenberg/headline-tool/headline-tool.php:492
msgid "no questions asked"
msgstr "no questions asked"

#: includes/gutenberg/headline-tool/headline-tool.php:491
msgid "loathsome"
msgstr "loathsome"

#: includes/gutenberg/headline-tool/headline-tool.php:490
msgid "pound"
msgstr "pound"

#: includes/gutenberg/headline-tool/headline-tool.php:489
msgid "no good"
msgstr "no good"

#: includes/gutenberg/headline-tool/headline-tool.php:488
msgid "line"
msgstr "line"

#: includes/gutenberg/headline-tool/headline-tool.php:487
msgid "results"
msgstr "results"

#: includes/gutenberg/headline-tool/headline-tool.php:486
msgid "nightmare"
msgstr "nightmare"

#: includes/gutenberg/headline-tool/headline-tool.php:485
msgid "like a normal"
msgstr "like a normal"

#: includes/gutenberg/headline-tool/headline-tool.php:484
msgid "gambling"
msgstr "gambling"

#: includes/gutenberg/headline-tool/headline-tool.php:483
msgid "exposed"
msgstr "exposed"

#: includes/gutenberg/headline-tool/headline-tool.php:482
msgid "lies"
msgstr "lies"

#: includes/gutenberg/headline-tool/headline-tool.php:481
msgid "explode"
msgstr "explode"

#: includes/gutenberg/headline-tool/headline-tool.php:480
msgid "lick"
msgstr "lick"

#: includes/gutenberg/headline-tool/headline-tool.php:478
msgid "illegal"
msgstr "illegal"

#: includes/gutenberg/headline-tool/headline-tool.php:477
msgid "frugal"
msgstr "frugal"

#: includes/gutenberg/headline-tool/headline-tool.php:476
msgid "lawsuit"
msgstr "lawsuit"

#: includes/gutenberg/headline-tool/headline-tool.php:475
msgid "hypnotic"
msgstr "hypnotic"

#: includes/gutenberg/headline-tool/headline-tool.php:474
msgid "frightening"
msgstr "frightening"

#: includes/gutenberg/headline-tool/headline-tool.php:473
msgid "fresh on the mind"
msgstr "fresh on the mind"

#: includes/gutenberg/headline-tool/headline-tool.php:472
msgid "excited"
msgstr "excited"

#: includes/gutenberg/headline-tool/headline-tool.php:471
msgid "hurricane"
msgstr "hurricane"

#: includes/gutenberg/headline-tool/headline-tool.php:470
msgid "frenzy"
msgstr "frenzy"

#: includes/gutenberg/headline-tool/headline-tool.php:469
msgid "freebie"
msgstr "freebie"

#: includes/gutenberg/headline-tool/headline-tool.php:468
msgid "evil"
msgstr "evil"

#: includes/gutenberg/headline-tool/headline-tool.php:467
msgid "how to make"
msgstr "how to make"

#: includes/gutenberg/headline-tool/headline-tool.php:466
msgid "epic"
msgstr "epic"

#: includes/gutenberg/headline-tool/headline-tool.php:465
msgid "know it all"
msgstr "know it all"

#: includes/gutenberg/headline-tool/headline-tool.php:463
msgid "frantic"
msgstr "frantic"

#: includes/gutenberg/headline-tool/headline-tool.php:462
msgid "killer"
msgstr "killer"

#: includes/gutenberg/headline-tool/headline-tool.php:461
msgid "hope"
msgstr "hope"

#: includes/gutenberg/headline-tool/headline-tool.php:460
msgid "foul"
msgstr "foul"

#: includes/gutenberg/headline-tool/headline-tool.php:459
msgid "jubilant"
msgstr "jubilant"

#: includes/gutenberg/headline-tool/headline-tool.php:458
msgid "hoax"
msgstr "hoax"

#: includes/gutenberg/headline-tool/headline-tool.php:457
msgid "energize"
msgstr "energise"

#: includes/gutenberg/headline-tool/headline-tool.php:456
msgid "jeopardy"
msgstr "jeopardy"

#: includes/gutenberg/headline-tool/headline-tool.php:455
msgid "forgotten"
msgstr "forgotten"

#: includes/gutenberg/headline-tool/headline-tool.php:454
msgid "jaw-dropping"
msgstr "jaw-dropping"

#: includes/gutenberg/headline-tool/headline-tool.php:453
msgid "high"
msgstr "high"

#: includes/gutenberg/headline-tool/headline-tool.php:452
msgid "force-fed"
msgstr "force-fed"

#: includes/gutenberg/headline-tool/headline-tool.php:451
msgid "empower"
msgstr "empower"

#: includes/gutenberg/headline-tool/headline-tool.php:450
msgid "jail"
msgstr "jail"

#: includes/gutenberg/headline-tool/headline-tool.php:449
msgid "hidden"
msgstr "hidden"

#: includes/gutenberg/headline-tool/headline-tool.php:448
msgid "forbidden"
msgstr "forbidden"

#: includes/gutenberg/headline-tool/headline-tool.php:447
msgid "jackpot"
msgstr "jackpot"

#: includes/gutenberg/headline-tool/headline-tool.php:446
msgid "help are the"
msgstr "help are the"

#: includes/gutenberg/headline-tool/headline-tool.php:445
msgid "for the first time"
msgstr "for the first time"

#: includes/gutenberg/headline-tool/headline-tool.php:444
msgid "embarrass"
msgstr "embarrass"

#: includes/gutenberg/headline-tool/headline-tool.php:443
msgid "it looks like a"
msgstr "it looks like a"

#: includes/gutenberg/headline-tool/headline-tool.php:442
msgid "helpless"
msgstr "helpless"

#: includes/gutenberg/headline-tool/headline-tool.php:441
msgid "fooled"
msgstr "fooled"

#: includes/gutenberg/headline-tool/headline-tool.php:440
msgid "is what happens when"
msgstr "is what happens when"

#: includes/gutenberg/headline-tool/headline-tool.php:439
msgid "fool"
msgstr "fool"

#: includes/gutenberg/headline-tool/headline-tool.php:438
msgid "is the"
msgstr "is the"

#: includes/gutenberg/headline-tool/headline-tool.php:437
msgid "hazardous"
msgstr "hazardous"

#: includes/gutenberg/headline-tool/headline-tool.php:436
msgid "irresistibly"
msgstr "irresistibly"

#: includes/gutenberg/headline-tool/headline-tool.php:435
msgid "hate"
msgstr "hate"

#: includes/gutenberg/headline-tool/headline-tool.php:433
msgid "dumb"
msgstr "dumb"

#: includes/gutenberg/headline-tool/headline-tool.php:432
msgid "ironclad"
msgstr "ironclad"

#: includes/gutenberg/headline-tool/headline-tool.php:431
msgid "happy"
msgstr "happy"

#: includes/gutenberg/headline-tool/headline-tool.php:430
msgid "floundering"
msgstr "floundering"

#: includes/gutenberg/headline-tool/headline-tool.php:429
msgid "drowning"
msgstr "drowning"

#: includes/gutenberg/headline-tool/headline-tool.php:428
msgid "invasion"
msgstr "invasion"

#: includes/gutenberg/headline-tool/headline-tool.php:427
msgid "had enough"
msgstr "had enough"

#: includes/gutenberg/headline-tool/headline-tool.php:425
msgid "hack"
msgstr "hack"

#: includes/gutenberg/headline-tool/headline-tool.php:423
msgid "double"
msgstr "double"

#: includes/gutenberg/headline-tool/headline-tool.php:422
msgid "gullible"
msgstr "gullible"

#: includes/gutenberg/headline-tool/headline-tool.php:421
msgid "feeble"
msgstr "feeble"

#: includes/gutenberg/headline-tool/headline-tool.php:420
msgid "dollar"
msgstr "dollar"

#: includes/gutenberg/headline-tool/headline-tool.php:419
msgid "insidious"
msgstr "insidious"

#: includes/gutenberg/headline-tool/headline-tool.php:418
msgid "feast"
msgstr "feast"

#: includes/gutenberg/headline-tool/headline-tool.php:417
msgid "disinformation"
msgstr "disinformation"

#: includes/gutenberg/headline-tool/headline-tool.php:416
msgid "fearless"
msgstr "fearless"

#: includes/gutenberg/headline-tool/headline-tool.php:415
msgid "disgusting"
msgstr "disgusting"

#: includes/gutenberg/headline-tool/headline-tool.php:414
msgid "insanely"
msgstr "insanely"

#: includes/gutenberg/headline-tool/headline-tool.php:413
msgid "grit"
msgstr "grit"

#: includes/gutenberg/headline-tool/headline-tool.php:412
msgid "greed"
msgstr "greed"

#: includes/gutenberg/headline-tool/headline-tool.php:411
msgid "fantastic"
msgstr "fantastic"

#: includes/gutenberg/headline-tool/headline-tool.php:410
msgid "disastrous"
msgstr "disastrous"

#: includes/gutenberg/headline-tool/headline-tool.php:409
msgid "famous"
msgstr "famous"

#: includes/gutenberg/headline-tool/headline-tool.php:408
msgid "dirty"
msgstr "dirty"

#: includes/gutenberg/headline-tool/headline-tool.php:407
msgid "inexpensive"
msgstr "inexpensive"

#: includes/gutenberg/headline-tool/headline-tool.php:406
msgid "grateful"
msgstr "grateful"

#: includes/gutenberg/headline-tool/headline-tool.php:405
msgid "faith"
msgstr "faith"

#: includes/gutenberg/headline-tool/headline-tool.php:404
msgid "in the"
msgstr "in the"

#: includes/gutenberg/headline-tool/headline-tool.php:403
msgid "fail"
msgstr "fail"

#: includes/gutenberg/headline-tool/headline-tool.php:402
msgid "devoted"
msgstr "devoted"

#: includes/gutenberg/headline-tool/headline-tool.php:401
msgid "in the world"
msgstr "in the world"

#: includes/gutenberg/headline-tool/headline-tool.php:400
msgid "gift"
msgstr "gift"

#: includes/gutenberg/headline-tool/headline-tool.php:399
msgid "eye-opening"
msgstr "eye-opening"

#: includes/gutenberg/headline-tool/headline-tool.php:398
msgid "devastating"
msgstr "devastating"

#: includes/gutenberg/headline-tool/headline-tool.php:397
msgid "in a"
msgstr "in a"

#: includes/gutenberg/headline-tool/headline-tool.php:396
msgid "extra"
msgstr "extra"

#: includes/gutenberg/headline-tool/headline-tool.php:395
msgid "destroy"
msgstr "destroy"

#: includes/gutenberg/headline-tool/headline-tool.php:316
msgid "Question"
msgstr "Question"

#: includes/gutenberg/headline-tool/headline-tool.php:305
#: includes/gutenberg/headline-tool/headline-tool.php:919
msgid "my"
msgstr "my"

#: includes/gutenberg/headline-tool/headline-tool.php:303
#: includes/gutenberg/headline-tool/headline-tool.php:908
msgid "it"
msgstr "it"

#: includes/gutenberg/headline-tool/headline-tool.php:302
#: includes/gutenberg/headline-tool/headline-tool.php:942
msgid "your"
msgstr "your"

#: includes/gutenberg/headline-tool/headline-tool.php:301
#: includes/gutenberg/headline-tool/headline-tool.php:931
msgid "she"
msgstr "she"

#: includes/gutenberg/headline-tool/headline-tool.php:300
#: includes/gutenberg/headline-tool/headline-tool.php:892
msgid "he"
msgstr "he"

#: includes/gutenberg/headline-tool/headline-tool.php:299
#: includes/gutenberg/headline-tool/headline-tool.php:304
#: includes/gutenberg/headline-tool/headline-tool.php:916
msgid "they"
msgstr "they"

#: includes/gutenberg/headline-tool/headline-tool.php:298
#: includes/gutenberg/headline-tool/headline-tool.php:940
msgid "you"
msgstr "you"

#: includes/gutenberg/headline-tool/headline-tool.php:295
#: includes/gutenberg/headline-tool/headline-tool.php:312
#: includes/gutenberg/headline-tool/headline-tool.php:936
msgid "will"
msgstr "will"

#: includes/gutenberg/headline-tool/headline-tool.php:294
#: includes/gutenberg/headline-tool/headline-tool.php:311
#: includes/gutenberg/headline-tool/headline-tool.php:893
msgid "are"
msgstr "are"

#: includes/gutenberg/headline-tool/headline-tool.php:293
#: includes/gutenberg/headline-tool/headline-tool.php:310
#: includes/gutenberg/headline-tool/headline-tool.php:905
msgid "can"
msgstr "can"

#: includes/gutenberg/headline-tool/headline-tool.php:292
#: includes/gutenberg/headline-tool/headline-tool.php:309
#: includes/gutenberg/headline-tool/headline-tool.php:909
msgid "do"
msgstr "do"

#: includes/gutenberg/headline-tool/headline-tool.php:291
#: includes/gutenberg/headline-tool/headline-tool.php:308
msgid "does"
msgstr "does"

#: includes/gutenberg/headline-tool/headline-tool.php:290
#: includes/gutenberg/headline-tool/headline-tool.php:307
#: includes/gutenberg/headline-tool/headline-tool.php:888
msgid "has"
msgstr "has"

#: includes/gutenberg/headline-tool/headline-tool.php:289
#: includes/gutenberg/headline-tool/headline-tool.php:306
#: includes/gutenberg/headline-tool/headline-tool.php:890
msgid "have"
msgstr "have"

#: includes/gutenberg/headline-tool/headline-tool.php:288
#: includes/gutenberg/headline-tool/headline-tool.php:928
msgid "what"
msgstr "what"

#: includes/gutenberg/headline-tool/headline-tool.php:287
#: includes/gutenberg/headline-tool/headline-tool.php:898
msgid "how"
msgstr "how"

#: includes/gutenberg/headline-tool/headline-tool.php:286
#: includes/gutenberg/headline-tool/headline-tool.php:930
msgid "when"
msgstr "when"

#: includes/gutenberg/headline-tool/headline-tool.php:285
msgid "where"
msgstr "where"

#: includes/gutenberg/headline-tool/headline-tool.php:279
msgid "List"
msgstr "List"

#: includes/gutenberg/headline-tool/headline-tool.php:274
msgid "thousand"
msgstr "thousand"

#: includes/gutenberg/headline-tool/headline-tool.php:273
msgid "hundred"
msgstr "hundred"

#: includes/gutenberg/headline-tool/headline-tool.php:272
msgid "fift"
msgstr "fifteen"

#: includes/gutenberg/headline-tool/headline-tool.php:271
msgid "thirt"
msgstr "thirteen"

#: includes/gutenberg/headline-tool/headline-tool.php:270
msgid "twelve"
msgstr "twelve"

#: includes/gutenberg/headline-tool/headline-tool.php:269
msgid "eleven"
msgstr "eleven"

#: includes/gutenberg/headline-tool/headline-tool.php:268
msgid "nine"
msgstr "nine"

#: includes/gutenberg/headline-tool/headline-tool.php:267
msgid "eight"
msgstr "eight"

#: includes/gutenberg/headline-tool/headline-tool.php:266
msgid "seven"
msgstr "seven"

#: includes/gutenberg/headline-tool/headline-tool.php:265
msgid "six"
msgstr "six"

#: includes/gutenberg/headline-tool/headline-tool.php:264
msgid "five"
msgstr "five"

#: includes/gutenberg/headline-tool/headline-tool.php:263
msgid "four"
msgstr "four"

#: includes/gutenberg/headline-tool/headline-tool.php:262
msgid "three"
msgstr "three"

#: includes/gutenberg/headline-tool/headline-tool.php:261
msgid "two"
msgstr "two"

#: includes/gutenberg/headline-tool/headline-tool.php:260
#: includes/gutenberg/headline-tool/headline-tool.php:983
msgid "one"
msgstr "one"

#: includes/gutenberg/headline-tool/headline-tool.php:254
msgid "How-To"
msgstr "How-To"

#: includes/gutenberg/headline-tool/headline-tool.php:253
msgid "howto"
msgstr "howto"

#: includes/gutenberg/headline-tool/headline-tool.php:253
#: includes/gutenberg/headline-tool/headline-tool.php:757
msgid "how to"
msgstr "how to"

#: includes/gutenberg/headline-tool/headline-tool.php:234
msgid "Perfect"
msgstr "Perfect"

#: includes/gutenberg/headline-tool/headline-tool.php:224
msgid "power"
msgstr "power"

#: includes/gutenberg/headline-tool/headline-tool.php:218
msgid "uncommon"
msgstr "uncommon"

#: includes/gutenberg/headline-tool/headline-tool.php:212
msgid "common"
msgstr "common"

#: includes/gutenberg/headline-tool/headline-tool.php:206
msgid "emotion"
msgstr "emotion"

#: includes/gutenberg/headline-tool/headline-tool.php:202
msgid "Can Be Improved"
msgstr "Can Be Improved"

#: includes/gutenberg/headline-tool/headline-tool.php:145
msgid "Bad Input"
msgstr "Bad Input"

#. translators: %s - link to a site.
#: includes/emails/templates/footer-default.php:45
msgid "Sent from %s"
msgstr "Sent from %s"

#: includes/emails/class-emails.php:384
msgid "You cannot send emails with MI_WP_Emails() until init/admin_init has been reached."
msgstr "You cannot send emails with MI_WP_Emails() until init/admin_init has been reached."

#: includes/helpers.php:730
msgid "Unknown Country"
msgstr "Unknown Country"

#: includes/admin/routes.php:684
msgid "Pretty Links helps you shrink, beautify, track, manage and share any URL on or off of your WordPress website. Create links that look how you want using your own domain name!"
msgstr "Pretty Links helps you shrink, beautify, track, manage and share any URL on or off of your WordPress website. Create links that look how you want using your own domain name!"

#. Translators: Placeholders add links to the settings panel.
#: includes/admin/admin.php:669
msgid "Click %1$shere%2$s to reauthenticate to be able to access reports. For more information why this is required, see our %3$sblog post%4$s."
msgstr "Click %1$shere%2$s to reauthenticate to be able to access reports. For more information why this is required, see our %3$sblog post%4$s."

#: lite/includes/admin/metaboxes.php:46 assets/gutenberg/js/editor.js:7613
msgid "Last 30 days"
msgstr "Last 30 days"

#: lite/includes/admin/metaboxes.php:50 assets/gutenberg/js/editor.js:7614
msgid "Yesterday"
msgstr "Yesterday"

#: lite/includes/admin/wp-site-health.php:462
msgid "MonsterInsights has detected that you have a coming soon or maintenance mode plugin currently activated on your site. This plugin does not allow other plugins (like MonsterInsights) to output Javascript, and thus MonsterInsights is not currently tracking your users (expected). Once the coming soon/maintenance mode plugin is deactivated, tracking will resume automatically."
msgstr "MonsterInsights has detected that you have a coming soon or maintenance mode plugin currently activated on your site. This plugin does not allow other plugins (like MonsterInsights) to output JavaScript, and thus MonsterInsights is not currently tracking your users (expected). Once the coming soon/maintenance mode plugin is deactivated, tracking will resume automatically."

#: lite/includes/admin/wp-site-health.php:461
msgid "Tracking code disabled: coming soon/maintenance mode plugin present"
msgstr "Tracking code disabled: coming soon/maintenance mode plugin present"

#: includes/popular-posts/class-popular-posts-themes.php:397
msgid "November"
msgstr "November"

#: includes/gutenberg/site-insights/templates/scorecard/class-scorecard-gender.php:59
msgid "Gender"
msgstr "Gender"

#: lite/includes/admin/reports/report-year-in-review.php:22
msgid "Year in Review"
msgstr "Year in Review"

#. Translators: %s is the link to the article where more details about tracking
#. are listed.
#: includes/frontend/frontend.php:273
msgid "To keep stats accurate, we do not load Google Analytics scripts for admin users. %1$sLearn More &raquo;%2$s"
msgstr "To keep stats accurate, we do not load Google Analytics scripts for admin users. %1$sLearn More &raquo;%2$s"

#: includes/frontend/frontend.php:260
msgid "Tracking is Disabled for Administrators"
msgstr "Tracking is Disabled for Administrators"

#: includes/admin/admin.php:517
msgid "Please Setup Website Analytics to See Audience Insights"
msgstr "Please Setup Website Analytics to See Audience Insights"

#. Translators: makes text bold.
#: lite/includes/admin/helpers.php:78
msgid "%1$sBonus:%2$s You also get 50%% off discount for being a loyal MonsterInsights Lite user."
msgstr "%1$sBonus:%2$s you also get a 50% discount for being a loyal MonsterInsights Lite user."

#: lite/includes/admin/helpers.php:74
msgid "MonsterInsights Pro shows you the stats that matter, so you can boost your business growth!"
msgstr "MonsterInsights Pro shows you the stats that matter, so you can boost your business growth!"

#: lite/includes/admin/helpers.php:73
msgid "It's easy to double your traffic and sales when you know exactly how people find and use your website."
msgstr "It's easy to double your traffic and sales when you know exactly how people find and use your website."

#: lite/includes/admin/helpers.php:72
msgid "Grow Your Business with MonsterInsights Pro"
msgstr "Grow Your Business with MonsterInsights Pro"

#: lite/includes/admin/helpers.php:67
msgid "Get Better Insights. Grow FASTER!"
msgstr "Get Better Insights. Grow FASTER!"

#: lite/includes/admin/dashboard-widget.php:150
msgid "Setup Website Analytics"
msgstr "Set Up Website Analytics"

#: lite/includes/admin/dashboard-widget.php:148
msgid "To see your website stats, please connect MonsterInsights to Google Analytics."
msgstr "To see your website stats, please connect MonsterInsights to Google Analytics."

#: lite/includes/admin/connect.php:56
msgid "Please enter your license key to connect."
msgstr "Please enter your licence key to connect."

#: lite/includes/admin/wp-site-health.php:465
msgid "MonsterInsights has automatically detected an issue with your tracking setup"
msgstr "MonsterInsights has automatically detected an issue with your tracking setup"

#: lite/includes/admin/wp-site-health.php:452
msgid "The Google Analytics tracking code is being output correctly, and no duplicate Google Analytics scripts have been detected."
msgstr "The Google Analytics tracking code is being output correctly, and no duplicate Google Analytics scripts have been detected."

#: lite/includes/admin/wp-site-health.php:446
msgid "Tracking code is properly being output."
msgstr "Tracking code is being output properly."

#: lite/includes/admin/wp-site-health.php:86
msgid "MonsterInsights Tracking Code"
msgstr "MonsterInsights Tracking Code"

#. Translators: Placeholders make the text bold.
#: includes/admin/pages/settings.php:102
msgid "If you are using an %1$sad blocker%2$s, please disable or whitelist the current page to load MonsterInsights correctly."
msgstr "If you are using an %1$sad blocker%2$s, please disable or whitelist the current page to load MonsterInsights correctly."

#: lite/includes/admin/welcome.php:56 lite/includes/admin/welcome.php:57
msgid "Welcome to MonsterInsights"
msgstr "Welcome to MonsterInsights"

#. Translators: The error message received.
#: lite/includes/admin/wp-site-health.php:428
msgid "Error message: %s"
msgstr "Error message: %s"

#: lite/includes/admin/wp-site-health.php:424
msgid "Your server is blocking external requests to monsterinsights.com, please check your firewall settings or contact your host for more details."
msgstr "Your server is blocking external requests to monsterinsights.com, please check your firewall settings or contact your host for more details."

#: lite/includes/admin/wp-site-health.php:423
msgid "The MonsterInsights server is not reachable."
msgstr "The MonsterInsights server is not reachable."

#: lite/includes/admin/wp-site-health.php:408
msgid "The MonsterInsights API is reachable and no connection issues have been detected."
msgstr "The MonsterInsights API is reachable and no connection issues have been detected."

#: lite/includes/admin/wp-site-health.php:402
msgid "Can connect to MonsterInsights.com correctly"
msgstr "Can connect to MonsterInsights.com correctly"

#: lite/includes/admin/wp-site-health.php:372
msgid "AMP pages are not being tracked"
msgstr "AMP pages are not being tracked"

#: lite/includes/admin/wp-site-health.php:357
#: lite/includes/admin/wp-site-health.php:383
msgid "View Addons"
msgstr "View Add-ons"

#: lite/includes/admin/wp-site-health.php:345
msgid "eCommerce data is not being tracked"
msgstr "eCommerce data is not being tracked"

#: lite/includes/admin/wp-site-health.php:330
msgid "Update Settings"
msgstr "Update Settings"

#: lite/includes/admin/wp-site-health.php:326
msgid "MonsterInsights automatic updates are disabled. We recommend enabling automatic updates so you can get access to the latest features, bugfixes, and security updates as they are released."
msgstr "MonsterInsights automatic updates are disabled. We recommend enabling automatic updates, so you can get access to the latest features, bug fixes, and security updates as they are released."

#: lite/includes/admin/wp-site-health.php:325
msgid "Automatic updates are disabled"
msgstr "Automatic updates are disabled"

#: lite/includes/admin/wp-site-health.php:321
msgid "MonsterInsights minor updates are enabled and you are getting the latest bugfixes and security updates, but not major features."
msgstr "MonsterInsights minor updates are enabled and you are getting the latest bug fixes and security updates, but not major features."

#: lite/includes/admin/wp-site-health.php:320
msgid "Your website is receiving minor updates"
msgstr "Your website is receiving minor updates"

#: lite/includes/admin/wp-site-health.php:313
msgid "MonsterInsights automatic updates are enabled and you are getting the latest features, bugfixes, and security updates as they are released."
msgstr "MonsterInsights automatic updates are enabled and you are getting the latest features, bug fixes, and security updates as they are released."

#: lite/includes/admin/wp-site-health.php:307
msgid "Your website is receiving automatic updates"
msgstr "Your website is receiving automatic updates"

#: lite/includes/admin/wp-site-health.php:292
msgid "Go to License Settings"
msgstr "Go to Licence Settings"

#: lite/includes/admin/wp-site-health.php:288
msgid "A valid license has been added to MonsterInsights but you are still using the Lite version."
msgstr "A valid licence has been added to MonsterInsights but you are still using the Lite version."

#: lite/includes/admin/wp-site-health.php:287
msgid "MonsterInsights Upgrade not applied"
msgstr "MonsterInsights Upgrade not applied"

#: lite/includes/admin/wp-site-health.php:261
msgid "Your traffic is not being tracked by MonsterInsights at the moment and you are losing data. Authenticate and get access to the reporting area and advanced tracking features."
msgstr "Your traffic is not being tracked by MonsterInsights at the moment and you are losing data. Authenticate and get access to the reporting area and advanced tracking features."

#: lite/includes/admin/wp-site-health.php:260
msgid "Please configure your Google Analytics settings"
msgstr "Please configure your Google Analytics settings"

#: lite/includes/admin/wp-site-health.php:254
#: lite/includes/admin/wp-site-health.php:265
msgid "Authenticate now"
msgstr "Authenticate now"

#: lite/includes/admin/wp-site-health.php:250
msgid "We highly recommend authenticating with MonsterInsights so that you can access our new reporting area and take advantage of new MonsterInsights features."
msgstr "We highly recommend authenticating with MonsterInsights so that you can access our new reporting area and take advantage of new MonsterInsights features."

#: lite/includes/admin/wp-site-health.php:238
msgid "View Reports"
msgstr "View Reports"

#: lite/includes/admin/wp-site-health.php:234
msgid "MonsterInsights integrates your WordPress website with Google Analytics."
msgstr "MonsterInsights integrates your WordPress website with Google Analytics."

#: lite/includes/admin/wp-site-health.php:228
msgid "Your website is authenticated with MonsterInsights"
msgstr "Your website is authenticated with MonsterInsights"

#: lite/includes/admin/wp-site-health.php:80
msgid "MonsterInsights Connection"
msgstr "MonsterInsights Connection"

#: lite/includes/admin/wp-site-health.php:74
msgid "MonsterInsights AMP"
msgstr "MonsterInsights AMP"

#: lite/includes/admin/wp-site-health.php:67
msgid "MonsterInsights eCommerce"
msgstr "MonsterInsights eCommerce"

#: lite/includes/admin/wp-site-health.php:61
msgid "MonsterInsights Automatic Updates"
msgstr "MonsterInsights Automatic Updates"

#: lite/includes/admin/wp-site-health.php:56
msgid "MonsterInsights Authentication"
msgstr "MonsterInsights Authentication"

#. translators: Post type archive title. %s: Post type name
#: includes/helpers.php:1427
msgid "Archives: %s"
msgstr "Archives: %s"

#: includes/helpers.php:1423
msgctxt "post format archive title"
msgid "Chats"
msgstr "Chats"

#: includes/helpers.php:1421
msgctxt "post format archive title"
msgid "Audio"
msgstr "Audio"

#: includes/helpers.php:1419
msgctxt "post format archive title"
msgid "Statuses"
msgstr "Statuses"

#: includes/helpers.php:1417
msgctxt "post format archive title"
msgid "Links"
msgstr "Links"

#: includes/helpers.php:1415
msgctxt "post format archive title"
msgid "Quotes"
msgstr "Quotes"

#: includes/helpers.php:1413
msgctxt "post format archive title"
msgid "Videos"
msgstr "Videos"

#: includes/helpers.php:1411
msgctxt "post format archive title"
msgid "Images"
msgstr "Images"

#: includes/helpers.php:1409
msgctxt "post format archive title"
msgid "Galleries"
msgstr "Galleries"

#: includes/helpers.php:1407
msgctxt "post format archive title"
msgid "Asides"
msgstr "Asides"

#. translators: Daily archive title. %s: Date
#: includes/helpers.php:1404
msgctxt "daily archives date format"
msgid "F j, Y"
msgstr "j F Y"

#. translators: Daily archive title. %s: Date
#: includes/helpers.php:1404
msgid "Day: %s"
msgstr "Day: %s"

#. translators: Monthly archive title. %s: Month name and year
#: includes/helpers.php:1401
msgctxt "monthly archives date format"
msgid "F Y"
msgstr "F Y"

#. translators: Monthly archive title. %s: Month name and year
#: includes/helpers.php:1401
msgid "Month: %s"
msgstr "Month: %s"

#. translators: Yearly archive title. %s: Year
#: includes/helpers.php:1398
msgctxt "yearly archives date format"
msgid "Y"
msgstr "Y"

#. translators: Yearly archive title. %s: Year
#: includes/helpers.php:1398
msgid "Year: %s"
msgstr "Year: %s"

#. translators: Author archive title. %s: Author name
#: includes/helpers.php:1395
msgid "Author: %s"
msgstr "Author: %s"

#. translators: Tag archive title. %s: Tag name
#: includes/helpers.php:1392
msgid "Tag: %s"
msgstr "Tag: %s"

#. translators: Category archive title. %s: Category name
#: includes/helpers.php:1389
msgid "Category: %s"
msgstr "Category: %s"

#: includes/helpers.php:1385
msgid "Archives"
msgstr "Archives"

#: includes/admin/admin.php:138 includes/admin/admin.php:291
msgid "About Us:"
msgstr "About Us:"

#: lite/includes/admin/connect.php:147 lite/includes/admin/connect.php:153
#: lite/includes/admin/connect.php:206
msgid "Plugin installed & activated."
msgstr "Plugin installed & activated."

#: lite/includes/admin/connect.php:176
msgid "You are not licensed."
msgstr "You are not licensed."

#: lite/includes/admin/onboarding-wizard.php:193
msgid "Return to Dashboard"
msgstr "Return to Dashboard"

#: includes/admin/routes.php:1180
msgid "Missing plugin name."
msgstr "Missing plugin name."

#: includes/admin/routes.php:1051 includes/admin/routes.php:1460
msgid "Add your license"
msgstr "Add your licence"

#. Translators: Adds a link to the settings panel.
#: includes/admin/admin.php:784
msgid "Warning: MonsterInsights found cross-domain settings in the custom code field and converted them to the new settings structure.  %1$sPlease click here to review and remove the code no longer needed.%2$s"
msgstr "Warning: MonsterInsights found cross-domain settings in the custom code field and converted them to the new settings structure.  %1$sPlease click here to review and remove the code no longer needed.%2$s"

#: includes/admin/reports/abstract-report.php:570
msgid "Please ask your webmaster to enable this addon."
msgstr "Please ask your webmaster to enable this add-on."

#: includes/admin/admin.php:138 includes/admin/admin.php:291
msgid "About Us"
msgstr "About Us"

#: includes/admin/pages/settings.php:131
msgid "View supported browsers"
msgstr "View supported browsers"

#: includes/admin/pages/settings.php:128
msgid "You are using a browser which is no longer supported by MonsterInsights. Please update or use another browser in order to access the plugin settings."
msgstr "You are using a browser which is no longer supported by MonsterInsights. Please update or use another browser in order to access the plugin settings."

#: includes/admin/pages/settings.php:126
msgid "Your browser version is not supported"
msgstr "Your browser version is not supported"

#: includes/admin/pages/settings.php:121
msgid "Resolve This Issue"
msgstr "Resolve This Issue"

#: includes/admin/pages/settings.php:117
msgid "Copy the error message above and paste it in a message to the MonsterInsights support team."
msgstr "Copy the error message above and paste it in a message to the MonsterInsights support team."

#: includes/admin/pages/settings.php:97
msgid "There seems to be an issue running JavaScript on your website, which MonsterInsights is crafted in to give you the best experience possible."
msgstr "There seems to be an issue running JavaScript on your website, which MonsterInsights crafted in to give you the best experience possible."

#: includes/admin/pages/settings.php:95
msgid "Ooops! It Appears JavaScript Didn’t Load"
msgstr "Oops! It Appears JavaScript Didn’t Load"

#. Translators: Placeholders add the PHP version, a link to the MonsterInsights
#. blog and a line break.
#: includes/admin/admin.php:580
msgid "Your site is running an outdated, insecure version of PHP (%1$s), which could be putting your site at risk for being hacked.%4$sWordPress stopped supporting your PHP version in April, 2019.%4$sUpdating PHP only takes a few minutes and will make your website significantly faster and more secure.%4$s%2$sLearn more about updating PHP%3$s"
msgstr "Your site is running an outdated, insecure version of PHP (%1$s), which could be putting your site at risk of being hacked.%4$sWordPress stopped supporting your PHP version in April 2019.%4$sUpdating PHP only takes a few minutes and will make your website significantly faster and more secure.%4$s%2$sLearn more about updating PHP%3$s"

#. Translators: The placeholders are for making the "We have detected multiple
#. tracking codes" text bold & adding a link to support.
#: includes/helpers.php:1549
msgid "%1$sWe have detected multiple tracking codes%2$s! You should remove non-MonsterInsights ones. If you need help finding them please %3$sread this article%4$s."
msgstr "%1$sWe have detected multiple tracking codes%2$s! You should remove non-MonsterInsights ones. If you need help finding them please %3$sread this article%4$s."

#: includes/helpers.php:1486
msgid "%1$sWe noticed you're using a caching plugin or caching from your hosting provider.%2$s Be sure to clear the cache to ensure the tracking appears on all pages and posts. %3$s(See this guide on how to clear cache)%4$s."
msgstr "%1$sWe noticed you're using a caching plugin or caching from your hosting provider.%2$s Be sure to clear the cache to ensure the tracking appears on all pages and posts. %3$s(See this guide on how to clear cache)%4$s."

#: includes/admin/ajax.php:56
msgid "You are not allowed to install plugins"
msgstr "You are not allowed to install plugins"

#: lite/includes/admin/onboarding-wizard.php:178
msgid "MonsterInsights &rsaquo; Onboarding Wizard"
msgstr "MonsterInsights &rsaquo; Onboarding Wizard"

#: includes/admin/admin.php:149
#: includes/admin/notifications/notification-upgrade-to-pro-high-traffic.php:41
#: includes/admin/notifications/notification-upgrade-to-pro.php:33
msgid "Upgrade to Pro"
msgstr "Upgrade to Pro"

#: includes/admin/admin.php:520 includes/admin/common.php:646
#: includes/admin/common.php:672 includes/admin/common.php:704
#: includes/admin/notifications/notification-audience.php:55
#: includes/admin/notifications/notification-bounce-rate.php:52
#: includes/admin/notifications/notification-headline-analyzer.php:39
#: includes/admin/notifications/notification-mobile-device-low-traffic.php:41
#: includes/admin/notifications/notification-returning-visitors.php:43
#: includes/admin/notifications/notification-traffic-dropping.php:43
msgid "Learn More"
msgstr "Learn More"

#: includes/admin/notifications/notification-upgrade-for-custom-dimensions.php:25
#: includes/admin/notifications/notification-upgrade-for-events-reporting.php:25
#: includes/admin/notifications/notification-upgrade-for-post-templates.php:25
#: lite/includes/admin/helpers.php:85
msgid "Upgrade to MonsterInsights Pro"
msgstr "Upgrade to MonsterInsights Pro"

#: includes/frontend/tracking/class-tracking-gtag.php:197
msgid "Note: MonsterInsights does not track you as a logged-in site administrator to prevent site owners from accidentally skewing their own Google Analytics data."
msgstr "Note: MonsterInsights does not track you as a logged-in site administrator to prevent site owners from accidentally skewing their own Google Analytics data."

#: lite/includes/admin/reports/report-realtime.php:22
msgid "Real Time"
msgstr "Real Time"

#: includes/helpers.php:610
msgid "Macedonia (FYROM)"
msgstr "Macedonia (FYROM)"

#: includes/api-request.php:509
msgid "Reason: The API was unreachable because no external hosts are allowed on this site."
msgstr "Reason: The API was unreachable because no external hosts are allowed on this site."

#: includes/api-request.php:504 includes/api-request.php:526
msgid "Reason: The API was unreachable because the call to Google failed."
msgstr "Reason: The API was unreachable because the call to Google failed."

#: includes/api-request.php:489
msgid "Reason: The API was unreachable because the API url is on the WP HTTP blocklist."
msgstr "Reason: The API was unreachable because the API URL is on the WP HTTP blocklist."

#. Translators: Placeholder gets replaced with the error message.
#: includes/api-request.php:230
msgid "The firewall of your server is blocking outbound calls. Please contact your hosting provider to fix this issue. %s"
msgstr "The firewall of your server is blocking outbound calls. Please contact your hosting provider to fix this issue. %s"

#. Plugin URI of the plugin
#: googleanalytics.php
msgid "https://www.monsterinsights.com/?utm_source=liteplugin&utm_medium=pluginheader&utm_campaign=pluginurl&utm_content=7%2E0%2E0"
msgstr "https://www.monsterinsights.com/?utm_source=liteplugin&utm_medium=pluginheader&utm_campaign=pluginurl&utm_content=7%2E0%2E0"

#: includes/admin/review.php:157
msgid "I already did"
msgstr "I already did"

#: includes/admin/review.php:149
msgid "Nope, maybe later"
msgstr "Nope, maybe later"

#: includes/admin/review.php:141
msgid "Ok, you deserve it"
msgstr "OK, you deserve it"

#: googleanalytics.php:584
msgid "Please uninstall and remove MonsterInsights Pro before activating Google Analytics for WordPress by MonsterInsights. The Lite version has not been activated. %1$sClick here to return to the Dashboard%2$s."
msgstr "Please uninstall and remove MonsterInsights Pro before activating Google Analytics for WordPress by MonsterInsights. The Lite version has not been activated. %1$sClick here to return to the Dashboard%2$s."

#: includes/gutenberg/site-insights/templates/graph/class-graph-sessions.php:36
#: includes/gutenberg/site-insights/templates/scorecard/class-scorecard-age.php:60
#: includes/gutenberg/site-insights/templates/scorecard/class-scorecard-gender.php:60
#: includes/gutenberg/site-insights/templates/scorecard/class-scorecard-sessions.php:27
#: includes/gutenberg/site-insights/templates/scorecard/class-scorecard-top10countries.php:56
#: lite/includes/emails/templates/body-summaries-test-plain.php:28
#: lite/includes/emails/templates/body-summaries-test.php:30
msgid "Sessions"
msgstr "Sessions"

#: lite/includes/admin/reports/report-queries.php:22
msgid "Search Console"
msgstr "Search Console"

#: lite/includes/admin/reports/report-publisher.php:22
msgid "Publishers"
msgstr "Publishers"

#: lite/includes/admin/reports/report-dimensions.php:22
msgid "Dimensions"
msgstr "Dimensions"

#: includes/frontend/tracking/class-tracking-gtag.php:200
msgid "Note: The site owner has disabled Google Analytics tracking for your user role."
msgstr "Note: The site owner has disabled Google Analytics tracking for your user role."

#: includes/frontend/tracking/class-tracking-gtag.php:194
msgid "Note: MonsterInsights is not currently configured on this site. The site owner needs to authenticate with Google Analytics in the MonsterInsights settings panel."
msgstr "Note: MonsterInsights is not currently configured on this site. The site owner needs to authenticate with Google Analytics in the MonsterInsights settings panel."

#: includes/api-request.php:232
msgid "The firewall of your server is blocking outbound calls. Please contact your hosting provider to fix this issue."
msgstr "The firewall of your server is blocking outbound calls. Please contact your hosting provider to fix this issue."

#: includes/gutenberg/headline-tool/headline-tool.php:711
msgid "unique"
msgstr "unique"

#: includes/gutenberg/site-insights/templates/scorecard/class-scorecard-top10countries.php:55
msgid "Top 10 Countries"
msgstr "Top 10 Countries"

#: includes/gutenberg/site-insights/templates/graph/class-graph-device.php:41
#: includes/gutenberg/site-insights/templates/scorecard/class-scorecard-device.php:33
msgid "Device Breakdown"
msgstr "Device Breakdown"

#: includes/gutenberg/site-insights/templates/scorecard/class-scorecard-pageviews.php:27
#: lite/includes/emails/templates/body-summaries-test-plain.php:31
#: lite/includes/emails/templates/body-summaries-test.php:33
msgid "Avg. Session Duration"
msgstr "Avg. Session Duration"

#: includes/gutenberg/site-insights/templates/scorecard/class-scorecard-sessions.php:36
msgid "Pageviews"
msgstr "Pageviews"

#: lite/includes/admin/metaboxes.php:80 lite/includes/admin/metaboxes.php:120
#: lite/includes/emails/templates/body-summaries-plain.php:62
#: lite/includes/emails/templates/body-summaries-test-plain.php:30
#: lite/includes/emails/templates/body-summaries-test-plain.php:98
#: lite/includes/emails/templates/body-summaries-test.php:32
#: lite/includes/emails/templates/body-summaries-test.php:149
#: lite/includes/emails/templates/body-summaries.php:120
#: assets/gutenberg/js/editor.js:7619
msgid "Page Views"
msgstr "Page Views"

#. Translators: Placeholders make the text bold, add the license level and add
#. a link to upgrade.
#: includes/admin/reports/abstract-report.php:523
msgid "Upgrading is easy! To upgrade, navigate to %1$ssour pricing page%2$s, purchase the required license, and then follow the %3$sinstructions in the email receipt%4$s to upgrade. It only takes a few minutes to unlock the most powerful, yet easy to use analytics tracking system for WordPress."
msgstr "Upgrading is easy! To upgrade, navigate to %1$ssour pricing page%2$s, purchase the required licence, and then follow the %3$sinstructions in the email receipt%4$s to upgrade. It only takes a few minutes to unlock the most powerful, yet easy to use analytics tracking system for WordPress."

#. Translators: Placeholders make the text bold, add the license level and add
#. a link to upgrade.
#: includes/admin/reports/abstract-report.php:516
msgid "Do you you want to access to %1$s reporting right now%2$s in your WordPress Dashboard? That comes with %3$s level%4$s of our paid packages. To get instant access, you'll want to buy a MonsterInsights license, which also gives you access to powerful addons, expanded reporting (including the ability to use custom date ranges), comprehensive tracking features (like UserID tracking) and access to our world-class support team."
msgstr "Do you you want to get access to %1$s reporting right now%2$s in your WordPress Dashboard? That comes with %3$s level%4$s of our paid packages. To get instant access, you'll want to buy a MonsterInsights licence, which also gives you access to powerful add-ons, expanded reporting (including the ability to use custom date ranges), comprehensive tracking features (like UserID tracking) and access to our world-class support team."

#. Translators: Placeholder adds a smiley face.
#: includes/admin/reports/abstract-report.php:509
msgid "Hey there! %s It looks like you've got the free version of MonsterInsights installed on your site. That's awesome!"
msgstr "Hey there! %s It looks like you've got the free version of MonsterInsights installed on your site. That's awesome!"

#: includes/admin/reports/abstract-report.php:504
#: includes/admin/reports/abstract-report.php:527
msgid "If you have any questions, don't hesitate to reach out. We're here to help."
msgstr "If you have any questions, don't hesitate to reach out. We're here to help."

#. Translators: Placeholdes add links to the account area and a guide.
#: includes/admin/reports/abstract-report.php:500
msgid "It's easy! To upgrade, navigate to %1$sMy Account%2$s on MonsterInsights.com, go to the licenses tab, and click upgrade. We also have a %3$sstep by step guide%4$s with pictures of this process."
msgstr "It's easy! To upgrade, navigate to %1$sMy Account%2$s on MonsterInsights.com, go to the licences tab, and click upgrade. We also have a %3$sstep by step guide%4$s with pictures of this process."

#. Translators: Placeholders add the report title and license level.
#: includes/admin/reports/abstract-report.php:493
msgid "Do you want to access to %1$s reporting right now%2$s in your WordPress Dashboard? That comes with the %3$s level%4$s of our paid packages. You'll need to upgrade your license to get instant access."
msgstr "Do you want to get access to %1$s reporting right now%2$s in your WordPress Dashboard? That comes with the %3$s level%4$s of our paid packages. You'll need to upgrade your licence to get instant access."

#. Translators: License level and smiley.
#: includes/admin/reports/abstract-report.php:486
msgid "Hey there! It looks like you've got the %1$s license installed on your site. That's awesome! %s"
msgstr "Hey there! It looks like you've got the %1$s licence installed on your site. That's awesome! %s"

#: includes/admin/reports/abstract-report.php:480
msgid "(And Crush Your Competition?)"
msgstr "(And Crush Your Competition?)"

#: includes/admin/reports/abstract-report.php:478
msgid "Ready to Get Analytics Super-Powers?"
msgstr "Ready to Get Analytics Super-Powers?"

#. Translators: Placeholders add the license level and the report title.
#: includes/admin/reports/abstract-report.php:464
msgid "You currently have a %1$s level license, but this report requires at least a %2$s level license to view the %3$s. Please upgrade to view this report."
msgstr "You currently have a %1$s level licence, but this report requires at least a %2$s level licence to view the %3$s. Please upgrade to view this report."

#: includes/admin/reports/abstract-report.php:137
msgid "No data found"
msgstr "No data found"

#: includes/admin/notification-event.php:257
#: lite/includes/emails/summaries.php:690
msgid "You don't have permission to view MonsterInsights reports."
msgstr "You don't have permission to view MonsterInsights reports."

#: includes/admin/api-auth.php:605
msgid "Successfully force deauthenticated."
msgstr "Successfully force de-authenticated."

#: includes/admin/api-auth.php:602
msgid "Successfully deauthenticated."
msgstr "Successfully de-authenticated."

#: includes/admin/api-auth.php:494
msgid "Successfully verified."
msgstr "Successfully verified."

#. Translators: Placeholders add a link to the MonsterInsights website.
#: includes/admin/admin.php:716 includes/admin/admin.php:768
msgid "%1$sGet MonsterInsights Pro%2$s"
msgstr "%1$sGet MonsterInsights Pro%2$s"

#: includes/admin/admin.php:558
msgid "Your network license key for MonsterInsights is invalid. The key no longer exists or the user associated with the key has been deleted. Please use a different key."
msgstr "Your network licence key for MonsterInsights is invalid. The key no longer exists or the user associated with the key has been deleted. Please use a different key."

#: includes/admin/admin.php:556
msgid "Your network license key for MonsterInsights has been disabled. Please use a different key."
msgstr "Your network licence key for MonsterInsights has been disabled. Please use a different key."

#. Translators: Adds a link to renew license.
#: includes/admin/admin.php:554
msgid "Your network license key for MonsterInsights has expired. %1$sPlease click here to renew your license key.%2$s"
msgstr "Your network licence key for MonsterInsights has expired. %1$sPlease click here to renew your licence key.%2$s"

#: includes/admin/admin.php:549 includes/admin/api-auth.php:305
msgid "Your license key for MonsterInsights is invalid. The key no longer exists or the user associated with the key has been deleted. Please use a different key."
msgstr "Your licence key for MonsterInsights is invalid. The key no longer exists or the user associated with the key has been deleted. Please use a different key."

#: includes/admin/admin.php:547
msgid "Your license key for MonsterInsights has been disabled. Please use a different key."
msgstr "Your licence key for MonsterInsights has been disabled. Please use a different key."

#. Translators: Adds a link to the license renewal.
#: includes/admin/admin.php:545
msgid "Your license key for MonsterInsights has expired. %1$sPlease click here to renew your license key.%2$s"
msgstr "Your licence key for MonsterInsights has expired. %1$sPlease click here to renew your licence key.%2$s"

#. Translators: Adds a link to retrieve the license.
#: includes/admin/admin.php:533
msgid "Warning: No valid license key has been entered for MonsterInsights. You are currently not getting updates, and are not able to view reports. %1$sPlease click here to enter your license key and begin receiving updates and reports.%2$s"
msgstr "Warning: No valid licence key has been entered for MonsterInsights. You are currently not getting updates, and are not able to view reports. %1$sPlease click here to enter your licence key and begin receiving updates and reports.%2$s"

#: includes/admin/admin.php:363
msgid "MonsterInsights Knowledge Base"
msgstr "MonsterInsights Knowledge Base"

#: includes/admin/admin.php:271 includes/admin/admin.php:273
msgid "Network Settings:"
msgstr "Network Settings:"

#: includes/admin/admin.php:62 includes/admin/admin.php:69
#: includes/admin/admin.php:275
msgid "General Reports:"
msgstr "General Reports:"

#: includes/admin/class-monsterinsights-am-deactivation-survey.php:379
msgid "Skip %s Deactivate"
msgstr "Skip %s Deactivate"

#: includes/admin/class-monsterinsights-am-deactivation-survey.php:378
msgid "Submit %s Deactivate"
msgstr "Submit %s Deactivate"

#: includes/admin/class-monsterinsights-am-deactivation-survey.php:363
msgid "If you have a moment, please share why you are deactivating %s:"
msgstr "If you have a moment, please share why you are deactivating %s:"

#: includes/admin/class-monsterinsights-am-deactivation-survey.php:362
msgid "Quick Feedback"
msgstr "Quick Feedback"

#: includes/admin/class-monsterinsights-am-deactivation-survey.php:355
msgid "Please share the reason"
msgstr "Please share the reason"

#: includes/admin/class-monsterinsights-am-deactivation-survey.php:354
msgid "Other"
msgstr "Other"

#: includes/admin/class-monsterinsights-am-deactivation-survey.php:351
msgid "It's a temporary deactivation"
msgstr "It's a temporary deactivation"

#: includes/admin/class-monsterinsights-am-deactivation-survey.php:348
msgid "I couldn't get the plugin to work"
msgstr "I couldn't get the plugin to work"

#: includes/admin/class-monsterinsights-am-deactivation-survey.php:345
msgid "Please share which plugin"
msgstr "Please share which plugin"

#: includes/admin/class-monsterinsights-am-deactivation-survey.php:344
msgid "I'm switching to a different plugin"
msgstr "I'm switching to a different plugin"

#: includes/admin/class-monsterinsights-am-deactivation-survey.php:341
msgid "I no longer need the plugin"
msgstr "I no longer need the plugin"

#: includes/admin/class-monsterinsights-am-deactivation-survey.php:210
msgid "Please select an option"
msgstr "Please select an option"

#: lite/includes/admin/tools.php:11
msgid "By upgrading to MonsterInsights Pro, you can unlock the MonsterInsights URL builder that helps you better track your advertising and email marketing campaigns."
msgstr "By upgrading to MonsterInsights Pro, you can unlock the MonsterInsights URL builder that helps you better track your advertising and email marketing campaigns."

#: lite/includes/admin/tools.php:10
msgid "Want even more fine tuned control over your website analytics?"
msgstr "Want even more fine-tuned control over your website analytics?"

#: lite/includes/admin/tools.php:13
msgid "Click here to Upgrade"
msgstr "Click here to Upgrade"

#: includes/helpers.php:539
msgid "Czechia"
msgstr "Czechia"

#: includes/admin/routes.php:968
msgid "Please upload a valid .json file"
msgstr "Please upload a valid .json file"

#: includes/admin/admin.php:121
msgid "Tools"
msgstr "Tools"

#: includes/admin/admin.php:121
msgid "Tools:"
msgstr "Tools:"

#. Description of the plugin
#: googleanalytics.php
msgid "The best Google Analytics plugin for WordPress. See how visitors find and use your website, so you can keep them coming back."
msgstr "The best Google Analytics plugin for WordPress. See how visitors find and use your website, so you can keep them coming back."

#. Plugin Name of the plugin
#: googleanalytics.php
msgid "Google Analytics for WordPress by MonsterInsights"
msgstr "Google Analytics for WordPress by MonsterInsights"

#: includes/helpers.php:729 includes/helpers.php:2126
msgid "Zimbabwe"
msgstr "Zimbabwe"

#: includes/helpers.php:728 includes/helpers.php:2125
msgid "Zambia"
msgstr "Zambia"

#: includes/helpers.php:727
msgid "Yemen"
msgstr "Yemen"

#: includes/helpers.php:726
msgid "Western Samoa"
msgstr "Western Samoa"

#: includes/helpers.php:725
msgid "Western Sahara"
msgstr "Western Sahara"

#: includes/helpers.php:724
msgid "Wallis and Futuna Islands"
msgstr "Wallis and Futuna Islands"

#: includes/helpers.php:723
msgid "Virgin Islands (USA)"
msgstr "Virgin Islands (USA)"

#: includes/helpers.php:722
msgid "Virgin Islands (British)"
msgstr "Virgin Islands (British)"

#: includes/helpers.php:721
msgid "Vietnam"
msgstr "Vietnam"

#: includes/helpers.php:720
msgid "Venezuela"
msgstr "Venezuela"

#: includes/helpers.php:719 includes/helpers.php:2124
msgid "Vanuatu"
msgstr "Vanuatu"

#: includes/helpers.php:718
msgid "Uzbekistan"
msgstr "Uzbekistan"

#: includes/helpers.php:717
msgid "US Minor Outlying Islands"
msgstr "US Minor Outlying Islands"

#: includes/helpers.php:716
msgid "Uruguay"
msgstr "Uruguay"

#: includes/helpers.php:715
msgid "United Arab Emirates"
msgstr "United Arab Emirates"

#: includes/helpers.php:714
msgid "Ukraine"
msgstr "Ukraine"

#: includes/helpers.php:713 includes/helpers.php:2123
msgid "Uganda"
msgstr "Uganda"

#: includes/helpers.php:712 includes/helpers.php:2120
msgid "Tuvalu"
msgstr "Tuvalu"

#: includes/helpers.php:711
msgid "Turks and Caicos Islands"
msgstr "Turks and Caicos Islands"

#: includes/helpers.php:710
msgid "Turkmenistan"
msgstr "Turkmenistan"

#: includes/helpers.php:709
msgid "Turkey"
msgstr "Turkey"

#: includes/helpers.php:708
msgid "Tunisia"
msgstr "Tunisia"

#: includes/helpers.php:707 includes/helpers.php:2116
msgid "Trinidad and Tobago"
msgstr "Trinidad and Tobago"

#: includes/helpers.php:706 includes/helpers.php:2119
msgid "Tonga"
msgstr "Tonga"

#: includes/helpers.php:705
msgid "Tokelau"
msgstr "Tokelau"

#: includes/helpers.php:704
msgid "Togo"
msgstr "Togo"

#: includes/helpers.php:703
msgid "Timor-Leste"
msgstr "Timor-Leste"

#: includes/helpers.php:702
msgid "Thailand"
msgstr "Thailand"

#: includes/helpers.php:701 includes/helpers.php:2118
msgid "Tanzania"
msgstr "Tanzania"

#: includes/helpers.php:700
msgid "Tajikistan"
msgstr "Tajikistan"

#: includes/helpers.php:699
msgid "Taiwan"
msgstr "Taiwan"

#: includes/helpers.php:698
msgid "Syrian Arab Republic"
msgstr "Syrian Arab Republic"

#: includes/helpers.php:697
msgid "Switzerland"
msgstr "Switzerland"

#: includes/helpers.php:696
msgid "Sweden"
msgstr "Sweden"

#: includes/helpers.php:695 includes/helpers.php:2108
msgid "Swaziland"
msgstr "Swaziland"

#: includes/helpers.php:694
msgid "Svalbard and Jan Mayen Islands"
msgstr "Svalbard and Jan Mayen Islands"

#: includes/helpers.php:693
msgid "Suriname"
msgstr "Suriname"

#: includes/helpers.php:692 includes/helpers.php:2115
msgid "Sudan"
msgstr "Sudan"

#: includes/helpers.php:691
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: includes/helpers.php:690
msgid "Spain"
msgstr "Spain"

#: includes/helpers.php:689 includes/helpers.php:2114
msgid "South Sudan"
msgstr "South Sudan"

#: includes/helpers.php:688
msgid "South Korea"
msgstr "South Korea"

#: includes/helpers.php:687
msgid "South Georgia"
msgstr "South Georgia"

#: includes/helpers.php:686 includes/helpers.php:2113
msgid "South Africa"
msgstr "South Africa"

#: includes/helpers.php:685
msgid "Somalia"
msgstr "Somalia"

#: includes/helpers.php:684 includes/helpers.php:2112
msgid "Solomon Islands"
msgstr "Solomon Islands"

#: includes/helpers.php:683
msgid "Slovenia"
msgstr "Slovenia"

#: includes/helpers.php:682
msgid "Slovak Republic"
msgstr "Slovak Republic"

#: includes/helpers.php:681 includes/helpers.php:2104
msgid "Singapore"
msgstr "Singapore"

#: includes/helpers.php:680 includes/helpers.php:2111
msgid "Sierra Leone"
msgstr "Sierra Leone"

#: includes/helpers.php:679 includes/helpers.php:2110
msgid "Seychelles"
msgstr "Seychelles"

#: includes/helpers.php:678
msgid "Serbia"
msgstr "Serbia"

#: includes/helpers.php:677
msgid "Senegal"
msgstr "Senegal"

#: includes/helpers.php:676
msgid "Saudi Arabia"
msgstr "Saudi Arabia"

#: includes/helpers.php:675
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe"
msgstr "S&atilde;o Tom&eacute; and Pr&iacute;ncipe"

#: includes/helpers.php:674
msgid "San Marino"
msgstr "San Marino"

#: includes/helpers.php:673
msgid "Saint Vincent and the Grenadines"
msgstr "Saint Vincent and the Grenadines"

#: includes/helpers.php:672
msgid "Saint Pierre and Miquelon"
msgstr "Saint Pierre and Miquelon"

#: includes/helpers.php:671
msgid "Saint Martin (Dutch)"
msgstr "Saint Martin (Dutch)"

#: includes/helpers.php:670
msgid "Saint Martin (French)"
msgstr "Saint Martin (French)"

#: includes/helpers.php:669
msgid "Saint Lucia"
msgstr "Saint Lucia"

#: includes/helpers.php:668
msgid "Saint Kitts and Nevis"
msgstr "Saint Kitts and Nevis"

#: includes/helpers.php:667
msgid "Saint Helena"
msgstr "Saint Helena"

#: includes/helpers.php:666
msgid "Saint Barth&eacute;lemy"
msgstr "Saint Barth&eacute;lemy"

#: includes/helpers.php:665 includes/helpers.php:2103
msgid "Rwanda"
msgstr "Rwanda"

#: includes/helpers.php:664
msgid "Russian Federation"
msgstr "Russian Federation"

#: includes/helpers.php:663
msgid "Romania"
msgstr "Romania"

#: includes/helpers.php:662
msgid "Reunion Island"
msgstr "Reunion Island"

#: includes/helpers.php:661
msgid "Republic of Kosovo"
msgstr "Republic of Kosovo"

#: includes/helpers.php:660
msgid "Qatar"
msgstr "Qatar"

#: includes/helpers.php:659
msgid "Puerto Rico"
msgstr "Puerto Rico"

#: includes/helpers.php:658
msgid "Portugal"
msgstr "Portugal"

#: includes/helpers.php:657
msgid "Poland"
msgstr "Poland"

#: includes/helpers.php:656
msgid "Pitcairn Island"
msgstr "Pitcairn Island"

#: includes/helpers.php:655 includes/helpers.php:2102
msgid "Philippines"
msgstr "Philippines"

#: includes/helpers.php:654
msgid "Peru"
msgstr "Peru"

#: includes/helpers.php:653
msgid "Paraguay"
msgstr "Paraguay"

#: includes/helpers.php:652 includes/helpers.php:2101
msgid "Papua New Guinea"
msgstr "Papua New Guinea"

#: includes/helpers.php:651
msgid "Panama"
msgstr "Panama"

#: includes/helpers.php:650
msgid "Palestinian Territories"
msgstr "Palestinian Territories"

#: includes/helpers.php:649 includes/helpers.php:2100
msgid "Palau"
msgstr "Palau"

#: includes/helpers.php:648 includes/helpers.php:2099
msgid "Pakistan"
msgstr "Pakistan"

#: includes/helpers.php:647
msgid "Oman"
msgstr "Oman"

#: includes/helpers.php:646
msgid "Norway"
msgstr "Norway"

#: includes/helpers.php:645
msgid "Northern Mariana Islands"
msgstr "Northern Mariana Islands"

#: includes/helpers.php:644
msgid "North Korea"
msgstr "North Korea"

#: includes/helpers.php:643
msgid "Norfolk Island"
msgstr "Norfolk Island"

#: includes/helpers.php:642
msgid "Niue"
msgstr "Niue"

#: includes/helpers.php:641 includes/helpers.php:2098
msgid "Nigeria"
msgstr "Nigeria"

#: includes/helpers.php:640
msgid "Niger"
msgstr "Niger"

#: includes/helpers.php:639
msgid "Nicaragua"
msgstr "Nicaragua"

#: includes/helpers.php:638 includes/helpers.php:2095
msgid "New Zealand"
msgstr "New Zealand"

#: includes/helpers.php:637
msgid "New Caledonia"
msgstr "New Caledonia"

#: includes/helpers.php:636
msgid "Netherlands Antilles"
msgstr "Netherlands Antilles"

#: includes/helpers.php:635
msgid "Netherlands"
msgstr "Netherlands"

#: includes/helpers.php:634
msgid "Nepal"
msgstr "Nepal"

#: includes/helpers.php:633 includes/helpers.php:2097
msgid "Nauru"
msgstr "Nauru"

#: includes/helpers.php:632 includes/helpers.php:2096
msgid "Namibia"
msgstr "Namibia"

#: includes/helpers.php:631
msgid "Myanmar"
msgstr "Myanmar"

#: includes/helpers.php:630
msgid "Mozambique"
msgstr "Mozambique"

#: includes/helpers.php:629
msgid "Morocco"
msgstr "Morocco"

#: includes/helpers.php:628
msgid "Montserrat"
msgstr "Montserrat"

#: includes/helpers.php:627
msgid "Montenegro"
msgstr "Montenegro"

#: includes/helpers.php:626
msgid "Mongolia"
msgstr "Mongolia"

#: includes/helpers.php:625
msgid "Monaco"
msgstr "Monaco"

#: includes/helpers.php:624
msgid "Moldova, Republic of"
msgstr "Moldova, Republic of"

#: includes/helpers.php:623 includes/helpers.php:2094
msgid "Micronesia"
msgstr "Micronesia"

#: includes/helpers.php:622
msgid "Mexico"
msgstr "Mexico"

#: includes/helpers.php:621
msgid "Mayotte"
msgstr "Mayotte"

#: includes/helpers.php:620 includes/helpers.php:2093
msgid "Mauritius"
msgstr "Mauritius"

#: includes/helpers.php:619
msgid "Mauritania"
msgstr "Mauritania"

#: includes/helpers.php:618
msgid "Martinique"
msgstr "Martinique"

#: includes/helpers.php:617 includes/helpers.php:2092
msgid "Marshall Islands"
msgstr "Marshall Islands"

#: includes/helpers.php:616 includes/helpers.php:2091
msgid "Malta"
msgstr "Malta"

#: includes/helpers.php:615
msgid "Mali"
msgstr "Mali"

#: includes/helpers.php:614
msgid "Maldives"
msgstr "Maldives"

#: includes/helpers.php:613
msgid "Malaysia"
msgstr "Malaysia"

#: includes/helpers.php:612 includes/helpers.php:2090
msgid "Malawi"
msgstr "Malawi"

#: includes/helpers.php:611
msgid "Madagascar"
msgstr "Madagascar"

#: includes/helpers.php:609
msgid "Macau"
msgstr "Macau"

#: includes/helpers.php:608
msgid "Luxembourg"
msgstr "Luxembourg"

#: includes/helpers.php:607
msgid "Lithuania"
msgstr "Lithuania"

#: includes/helpers.php:606
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: includes/helpers.php:605
msgid "Libyan Arab Jamahiriya"
msgstr "Libyan Arab Jamahiriya"

#: includes/helpers.php:604 includes/helpers.php:2089
msgid "Liberia"
msgstr "Liberia"

#: includes/helpers.php:603 includes/helpers.php:2088
msgid "Lesotho"
msgstr "Lesotho"

#: includes/helpers.php:602
msgid "Lebanon"
msgstr "Lebanon"

#: includes/helpers.php:601
msgid "Latvia"
msgstr "Latvia"

#: includes/helpers.php:600
msgid "Lao People's Democratic Republic"
msgstr "Lao People's Democratic Republic"

#: includes/helpers.php:599
msgid "Kyrgyzstan"
msgstr "Kyrgyzstan"

#: includes/helpers.php:598
msgid "Kuwait"
msgstr "Kuwait"

#: includes/helpers.php:597 includes/helpers.php:2087
msgid "Kiribati"
msgstr "Kiribati"

#: includes/helpers.php:596 includes/helpers.php:2086
msgid "Kenya"
msgstr "Kenya"

#: includes/helpers.php:595
msgid "Kazakhstan"
msgstr "Kazakhstan"

#: includes/helpers.php:594
msgid "Jordan"
msgstr "Jordan"

#: includes/helpers.php:593
msgid "Jersey"
msgstr "Jersey"

#: includes/helpers.php:592
msgid "Japan"
msgstr "Japan"

#: includes/helpers.php:591 includes/helpers.php:2085
msgid "Jamaica"
msgstr "Jamaica"

#: includes/helpers.php:590
msgid "Italy"
msgstr "Italy"

#: includes/helpers.php:589
msgid "Israel"
msgstr "Israel"

#: includes/helpers.php:588
msgid "Isle of Man"
msgstr "Isle of Man"

#: includes/helpers.php:587 includes/helpers.php:2083
msgid "Ireland"
msgstr "Ireland"

#: includes/helpers.php:586
msgid "Iraq"
msgstr "Iraq"

#: includes/helpers.php:585
msgid "Iran"
msgstr "Iran"

#: includes/helpers.php:584
msgid "Indonesia"
msgstr "Indonesia"

#: includes/helpers.php:583 includes/helpers.php:2084
#: includes/popular-posts/class-popular-posts-themes.php:296
msgid "India"
msgstr "India"

#: includes/helpers.php:582
msgid "Iceland"
msgstr "Iceland"

#: includes/helpers.php:581
msgid "Hungary"
msgstr "Hungary"

#: includes/helpers.php:580
msgid "Hong Kong"
msgstr "Hong Kong"

#: includes/helpers.php:579
msgid "Honduras"
msgstr "Honduras"

#: includes/helpers.php:578
msgid "Holy See (City Vatican State)"
msgstr "Holy See (City Vatican State)"

#: includes/helpers.php:577
msgid "Heard and McDonald Islands"
msgstr "Heard and McDonald Islands"

#: includes/helpers.php:576
msgid "Haiti"
msgstr "Haiti"

#: includes/helpers.php:575 includes/helpers.php:2080
msgid "Guyana"
msgstr "Guyana"

#: includes/helpers.php:574
msgid "Guinea-Bissau"
msgstr "Guinea-Bissau"

#: includes/helpers.php:573
msgid "Guinea"
msgstr "Guinea"

#: includes/helpers.php:572
msgid "Guernsey"
msgstr "Guernsey"

#: includes/helpers.php:571
msgid "Guatemala"
msgstr "Guatemala"

#: includes/helpers.php:570
msgid "Guam"
msgstr "Guam"

#: includes/helpers.php:569
msgid "Guadeloupe"
msgstr "Guadeloupe"

#: includes/helpers.php:568 includes/helpers.php:2079
msgid "Grenada"
msgstr "Grenada"

#: includes/helpers.php:567
msgid "Greenland"
msgstr "Greenland"

#: includes/helpers.php:566
msgid "Gibraltar"
msgstr "Gibraltar"

#: includes/helpers.php:565 includes/helpers.php:2082
msgid "Ghana"
msgstr "Ghana"

#: includes/helpers.php:564
msgid "Greece"
msgstr "Greece"

#: includes/helpers.php:563
msgid "Germany"
msgstr "Germany"

#: includes/helpers.php:562
msgid "Georgia"
msgstr "Georgia"

#: includes/helpers.php:561 includes/helpers.php:2081
msgid "Gambia"
msgstr "Gambia"

#: includes/helpers.php:560
msgid "Gabon"
msgstr "Gabon"

#: includes/helpers.php:559
msgid "French Southern Territories"
msgstr "French Southern Territories"

#: includes/helpers.php:558
msgid "French Polynesia"
msgstr "French Polynesia"

#: includes/helpers.php:557
msgid "French Guiana"
msgstr "French Guiana"

#: includes/helpers.php:556
msgid "France"
msgstr "France"

#: includes/helpers.php:555
msgid "Finland"
msgstr "Finland"

#: includes/helpers.php:554 includes/helpers.php:2078
msgid "Fiji"
msgstr "Fiji"

#: includes/helpers.php:553
msgid "Faroe Islands"
msgstr "Faroe Islands"

#: includes/helpers.php:552
msgid "Falkland Islands"
msgstr "Falkland Islands"

#: includes/helpers.php:551
msgid "Ethiopia"
msgstr "Ethiopia"

#: includes/helpers.php:550
msgid "Estonia"
msgstr "Estonia"

#: includes/helpers.php:549
msgid "Eritrea"
msgstr "Eritrea"

#: includes/helpers.php:548
msgid "El Salvador"
msgstr "El Salvador"

#: includes/helpers.php:547
msgid "Equatorial Guinea"
msgstr "Equatorial Guinea"

#: includes/helpers.php:546
msgid "Egypt"
msgstr "Egypt"

#: includes/helpers.php:545
msgid "Ecuador"
msgstr "Ecuador"

#: includes/helpers.php:544
msgid "East Timor"
msgstr "East Timor"

#: includes/helpers.php:543
msgid "Dominican Republic"
msgstr "Dominican Republic"

#: includes/helpers.php:542 includes/helpers.php:2077
msgid "Dominica"
msgstr "Dominica"

#: includes/helpers.php:541
msgid "Djibouti"
msgstr "Djibouti"

#: includes/helpers.php:540
msgid "Denmark"
msgstr "Denmark"

#: includes/helpers.php:538
msgid "Cyprus"
msgstr "Cyprus"

#: includes/helpers.php:537
msgid "Cura&Ccedil;ao"
msgstr "Cura&Ccedil;ao"

#: includes/helpers.php:536
msgid "Cuba"
msgstr "Cuba"

#: includes/helpers.php:535
msgid "Croatia/Hrvatska"
msgstr "Croatia/Hrvatska"

#: includes/helpers.php:534
msgid "Cote d'Ivoire"
msgstr "Cote d'Ivoire"

#: includes/helpers.php:533
msgid "Costa Rica"
msgstr "Costa Rica"

#: includes/helpers.php:532
msgid "Cook Islands"
msgstr "Cook Islands"

#: includes/helpers.php:531
msgid "Congo, Republic of"
msgstr "Congo, Republic of"

#: includes/helpers.php:530
msgid "Congo, Democratic People's Republic"
msgstr "Congo, Democratic People's Republic"

#: includes/helpers.php:529
msgid "Comoros"
msgstr "Comoros"

#: includes/helpers.php:528
msgid "Colombia"
msgstr "Colombia"

#: includes/helpers.php:527
msgid "Cocos Islands"
msgstr "Cocos Islands"

#: includes/helpers.php:526
msgid "Christmas Island"
msgstr "Christmas Island"

#: includes/helpers.php:525
msgid "China"
msgstr "China"

#: includes/helpers.php:524
msgid "Chile"
msgstr "Chile"

#: includes/helpers.php:523
msgid "Chad"
msgstr "Chad"

#: includes/helpers.php:522
msgid "Central African Republic"
msgstr "Central African Republic"

#: includes/helpers.php:521
msgid "Cayman Islands"
msgstr "Cayman Islands"

#: includes/helpers.php:520
msgid "Cape Verde"
msgstr "Cape Verde"

#: includes/helpers.php:519 includes/helpers.php:2075
msgid "Cameroon"
msgstr "Cameroon"

#: includes/helpers.php:518
msgid "Cambodia"
msgstr "Cambodia"

#: includes/helpers.php:517 includes/helpers.php:2074
msgid "Burundi"
msgstr "Burundi"

#: includes/helpers.php:516
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: includes/helpers.php:515
msgid "Bulgaria"
msgstr "Bulgaria"

#: includes/helpers.php:514
msgid "Brunei Darrussalam"
msgstr "Brunei Darrussalam"

#: includes/helpers.php:513
msgid "British Indian Ocean Territory"
msgstr "British Indian Ocean Territory"

#: includes/helpers.php:512
msgid "Brazil"
msgstr "Brazil"

#: includes/helpers.php:511
msgid "Bouvet Island"
msgstr "Bouvet Island"

#: includes/helpers.php:510 includes/helpers.php:2073
msgid "Botswana"
msgstr "Botswana"

#: includes/helpers.php:509
msgid "Bosnia and Herzegovina"
msgstr "Bosnia and Herzegovina"

#: includes/helpers.php:508
msgid "Bonaire, Saint Eustatius and Saba"
msgstr "Bonaire, Saint Eustatius and Saba"

#: includes/helpers.php:507
msgid "Bolivia"
msgstr "Bolivia"

#: includes/helpers.php:506
msgid "Bhutan"
msgstr "Bhutan"

#: includes/helpers.php:505
msgid "Bermuda"
msgstr "Bermuda"

#: includes/helpers.php:504
msgid "Benin"
msgstr "Benin"

#: includes/helpers.php:503 includes/helpers.php:2072
msgid "Belize"
msgstr "Belize"

#: includes/helpers.php:502
msgid "Belgium"
msgstr "Belgium"

#: includes/helpers.php:501
msgid "Belarus"
msgstr "Belarus"

#: includes/helpers.php:500 includes/helpers.php:2071
msgid "Barbados"
msgstr "Barbados"

#: includes/helpers.php:499
msgid "Bangladesh"
msgstr "Bangladesh"

#: includes/helpers.php:498
msgid "Bahrain"
msgstr "Bahrain"

#: includes/helpers.php:497
msgid "Bahamas"
msgstr "Bahamas"

#: includes/helpers.php:496
msgid "Azerbaijan"
msgstr "Azerbaijan"

#: includes/helpers.php:495
msgid "Austria"
msgstr "Austria"

#: includes/helpers.php:494 includes/helpers.php:2070
msgid "Australia"
msgstr "Australia"

#: includes/helpers.php:493
msgid "Aruba"
msgstr "Aruba"

#: includes/helpers.php:492
msgid "Armenia"
msgstr "Armenia"

#: includes/helpers.php:491
msgid "Argentina"
msgstr "Argentina"

#: includes/helpers.php:490 includes/helpers.php:2069
msgid "Antigua and Barbuda"
msgstr "Antigua and Barbuda"

#: includes/helpers.php:489
msgid "Antarctica"
msgstr "Antarctica"

#: includes/helpers.php:488
msgid "Anguilla"
msgstr "Anguilla"

#: includes/helpers.php:487
msgid "Angola"
msgstr "Angola"

#: includes/helpers.php:486
msgid "Andorra"
msgstr "Andorra"

#: includes/helpers.php:485
msgid "American Samoa"
msgstr "American Samoa"

#: includes/helpers.php:484
msgid "Algeria"
msgstr "Algeria"

#: includes/helpers.php:483
msgid "Albania"
msgstr "Albania"

#: includes/helpers.php:482
msgid "&#197;land Islands"
msgstr "&#197;land Islands"

#: includes/helpers.php:481
msgid "Afghanistan"
msgstr "Afghanistan"

#: includes/helpers.php:480 includes/helpers.php:2121
msgid "United Kingdom"
msgstr "United Kingdom"

#: includes/helpers.php:479 includes/helpers.php:2076
msgid "Canada"
msgstr "Canada"

#: includes/helpers.php:478
msgid "United States"
msgstr "United States"

#: includes/frontend/tracking/class-tracking-preview.php:77
msgid "You are currently in a preview window. MonsterInsights doesn't track preview window traffic to avoid false visit reports."
msgstr "You are currently in a preview window. MonsterInsights doesn't track preview window traffic to avoid false visit reports."

#. Translators: Placeholders add the hook name, plugin version and bold text.
#: includes/deprecated.php:190
msgid "%1$s is %3$sdeprecated%4$s since MonsterInsights version %2$s."
msgstr "%1$s is %3$sdeprecated%4$s since MonsterInsights version %2$s."

#. Translators: Placeholders add the hook name, plugin version and bold text.
#: includes/deprecated.php:133
msgid "%1$s is %3$sdeprecated%4$s since MonsterInsights version %2$s!"
msgstr "%1$s is %3$sdeprecated%4$s since MonsterInsights version %2$s!"

#: includes/admin/tracking.php:245
msgid "Once Weekly"
msgstr "Once Weekly"

#: includes/admin/exclude-page-metabox.php:144
#: lite/includes/admin/user-journey/providers/class-abstract-lite-metabox.php:78
#: lite/includes/admin/user-journey/providers/class-abstract-lite-metabox.php:116
#: assets/gutenberg/js/editor.js:7878
msgid "Upgrade"
msgstr "Upgrade"

#. Translators: Placeholders are for links to fix the issue.
#: includes/admin/common.php:508
msgid "MonsterInsights has detected that it's files are being blocked. This is usually caused by a adblock browser plugin (particularly uBlock Origin), or a conflicting WordPress theme or plugin. This issue only affects the admin side of MonsterInsights. To solve this, ensure MonsterInsights is whitelisted for your website URL in any adblock browser plugin you use. For step by step directions on how to do this, %1$sclick here%2$s. If this doesn't solve the issue (rare), send us a ticket %3$shere%2$s and we'll be happy to help diagnose the issue."
msgstr "MonsterInsights has detected that it's files are being blocked. This is usually caused by an adblock browser plugin (particularly uBlock Origin), or a conflicting WordPress theme or plugin. This issue only affects the admin side of MonsterInsights. To solve this, ensure MonsterInsights is whitelisted for your website URL in any adblock browser plugin you use. For step-by-step directions on how to do this, %1$sclick here%2$s. If this doesn't solve the issue (rare), send us a ticket %3$shere%2$s and we'll be happy to help diagnose the issue."

#: includes/gutenberg/headline-tool/headline-tool.php:1014
msgid "media"
msgstr "media"

#: lite/includes/admin/reports/report-ecommerce.php:22
msgid "eCommerce"
msgstr "eCommerce"

#: lite/includes/admin/reports/report-forms.php:22
msgid "Forms"
msgstr "Forms"

#: includes/gutenberg/headline-tool/headline-tool.php:1018
msgid "social"
msgstr "social"

#. Translators: Placeholders add a link to the settings panel.
#: includes/admin/reports/abstract-report.php:72
msgid "Please %1$senable the dashboard%2$s to see report data."
msgstr "Please %1$senable the dashboard%2$s to see report data."

#: includes/admin/notifications/notification-upgrade-for-form-conversion.php:31
#: includes/admin/notifications/notification-upgrade-for-search-console.php:32
#: includes/admin/reports/abstract-report.php:533
#: lite/includes/admin/woocommerce-marketing.php:79
msgid "Upgrade Now"
msgstr "Upgrade Now"

#: includes/admin/notice.php:238
msgid "Dismiss this notice"
msgstr "Dismiss this notice"

#. Translators: Placeholders add a link to the wordpress.org repository.
#: includes/admin/admin.php:458
msgid "Please rate %1$sMonsterInsights%2$s on %3$s %4$sWordPress.org%5$s to help us spread the word. Thank you from the MonsterInsights team!"
msgstr "Please rate %1$sMonsterInsights%2$s on %3$s %4$sWordPress.org%5$s to help us spread the word. Thank you from the MonsterInsights team!"

#: includes/admin/admin.php:368 includes/admin/admin.php:371
msgid "Support"
msgstr "Support"

#: includes/admin/admin.php:385
#: includes/admin/notifications/notification-upgrade-eu-traffic.php:86
#: includes/admin/notifications/notification-upgrade-for-custom-dimensions.php:31
#: includes/admin/notifications/notification-upgrade-for-email-summaries.php:36
#: includes/admin/notifications/notification-upgrade-for-events-reporting.php:31
#: includes/admin/notifications/notification-upgrade-for-post-templates.php:31
#: includes/admin/reports/abstract-report.php:536
msgid "Get MonsterInsights Pro"
msgstr "Get MonsterInsights Pro"

#: includes/admin/admin.php:363
msgid "Documentation"
msgstr "Documentation"

#: includes/admin/admin.php:273 includes/admin/admin.php:376
msgid "Network Settings"
msgstr "Network Settings"

#: includes/admin/admin.php:126 includes/admin/admin.php:286
msgid "Addons"
msgstr "Addons"

#: includes/admin/admin.php:126 includes/admin/admin.php:286
msgid "Addons:"
msgstr "Addons:"

#: googleanalytics.php:400
msgid "Please %1$suninstall%2$s the MonsterInsights Lite Plugin. Your Pro version of MonsterInsights may not work as expected until the Lite version is uninstalled."
msgstr "Please %1$suninstall%2$s the MonsterInsights Lite Plugin. Your Pro version of MonsterInsights may not work as expected until the Lite version is uninstalled."

#: googleanalytics.php:282 googleanalytics.php:296
msgid "Cheatin&#8217; huh?"
msgstr "Cheatin&#8217; huh?"

#. Author of the plugin
#: googleanalytics.php includes/admin/admin.php:55 includes/admin/admin.php:66
#: includes/admin/eea-compliance.php:208 includes/admin/wp-site-health.php:35
#: includes/emails/class-emails.php:255
#: lite/includes/admin/dashboard-widget.php:99
#: lite/includes/admin/wp-site-health.php:231
#: lite/includes/admin/wp-site-health.php:283
#: lite/includes/admin/wp-site-health.php:310
#: lite/includes/admin/wp-site-health.php:348
#: lite/includes/admin/wp-site-health.php:375
#: lite/includes/admin/wp-site-health.php:405
#: lite/includes/admin/wp-site-health.php:449
msgid "MonsterInsights"
msgstr "MonsterInsights"

#: includes/admin/admin.php:55 includes/admin/admin.php:69
#: includes/admin/admin.php:227 includes/admin/admin.php:230
#: includes/admin/admin.php:271
msgid "Insights"
msgstr "Insights"

#: includes/gutenberg/headline-tool/headline-tool.php:323
msgid "General"
msgstr "General"

#: includes/admin/reports/overview.php:34
msgid "Overview"
msgstr "Overview"

#: includes/gutenberg/site-insights/templates/scorecard/class-scorecard-pageviews.php:36
#: lite/includes/emails/summaries.php:785
#: lite/includes/emails/templates/body-summaries-test-plain.php:32
#: lite/includes/emails/templates/body-summaries-test.php:34
#: assets/gutenberg/js/editor.js:7615
msgid "Bounce Rate"
msgstr "Bounce Rate"

#: includes/admin/admin.php:62 includes/admin/admin.php:275
msgid "Reports"
msgstr "Reports"

#: includes/gutenberg/headline-tool/headline-tool.php:1005
msgid "first"
msgstr "first"

#: includes/admin/admin.php:66 includes/admin/admin.php:378
msgid "Settings"
msgstr "Settings"