<?php
/**
 * Template for the UM User Photos, The "Album header" block
 *
 * Page: "Profile", tab "Photos"
 * Caller: User_Photos_Ajax->get_single_album_view() method
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/album-head.php
 * @var object $album
 * @var bool   $is_my_profile
 * @var int    $album_id
 * @var bool   $disable_title
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
$disable_title = UM()->options()->get( 'um_user_photos_disable_title' );
?>
<div class="um-user-photos-album-head">
	<?php
	$svg_html = '<svg  xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icons-tabler-outline icon-tabler-arrow-narrow-left"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 12l14 0" /><path d="M5 12l4 4" /><path d="M5 12l4 -4" /></svg>';
	echo wp_kses(
		UM()->frontend()::layouts()::button(
			esc_html__( 'Back', 'um-user-photos' ),
			array(
				'type'          => 'button',
				'icon_position' => 'leading',
				'icon'          => $svg_html,
				'design'        => 'secondary-gray',
				'size'          => 's',
				'classes'       => array(
					'um-user-photos-back-to-gallery',
				),
				'data'          => array(
					'nonce'   => wp_create_nonce( 'um_get_gallery' ),
					'profile' => $album->post_author,
				),
			)
		),
		UM()->get_allowed_html( 'templates' )
	);
	?>

	<h3 class="um-user-photos-album-title">
	<?php
	if ( ! $disable_title ) {
		echo esc_html( $album->post_title );
	}
	?>
	</h3>

	<?php if ( $is_my_profile ) { ?>
		<?php
		$edit_link = wp_kses(
			UM()->frontend()::layouts()::link(
				esc_html__( 'Edit album', 'um-user-photos' ),
				array(
					'type'          => 'raw',
					'size'          => 's',
					'design'        => 'primary',
					'icon_position' => '',
					'title'         => esc_attr__( 'Edit album', 'um-user-photos' ),
					'classes'       => array(
						'um-user-photos-edit-album',
					),
					'data'          => array(
						'modal_title' => esc_attr__( 'Edit album', 'um-user-photos' ),
						'nonce'       => wp_create_nonce( 'um_edit_album_modal' . $album_id ),
						'id'          => $album_id,
					),
				)
			),
			UM()->get_allowed_html( 'templates' )
		);

		$delete_link = wp_kses(
			UM()->frontend()::layouts()::link(
				esc_html__( 'Delete album', 'um-user-photos' ),
				array(
					'type'          => 'raw',
					'size'          => 's',
					'design'        => 'primary',
					'icon_position' => '',
					'title'         => esc_attr__( 'Edit album', 'um-user-photos' ),
					'classes'       => array(
						'um-user-photos-delete-album',
					),
					'data'          => array(
						'title'   => esc_attr__( 'Delete album', 'um-user-photos' ),
						'nonce'   => wp_create_nonce( 'um_delete_album' . $album_id ),
						'confirm' => esc_attr__( 'Are you sure to delete this album?', 'um-user-photos' ),
						'id'      => $album_id,
					),
				)
			),
			UM()->get_allowed_html( 'templates' )
		);

		echo wp_kses(
			UM()->frontend()::layouts()::dropdown_menu(
				'um-photos-album-actions-toggle',
				array(
					array(
						$edit_link,
						$delete_link,
					),
				),
				array( 'width' => 150 )
			),
			UM()->get_allowed_html( 'templates' )
		);
		?>
	<?php } ?>
</div>
