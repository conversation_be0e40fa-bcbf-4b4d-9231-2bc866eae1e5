﻿=== Ultimate Member - User Photos ===
Author URI: https://ultimatemember.com/
Plugin URI: https://ultimatemember.com/extensions/user-photos/
Contributors: ultimatemember, champsupertramp, nsinelnikov
Tags: albums, photos, user, community
Requires at least: 6.2
Tested up to: 6.7
Stable tag: 2.2.0
License: GNU Version 2 or Any Later Version
License URI: http://www.gnu.org/licenses/gpl-3.0.txt
Requires UM core at least: 2.9.0

Allow users to create public and private notes from their profile.

== Description ==

Allow users to create public and private notes from their profile.

= Key Features: =

* Select which content bookmarks show on e.g pages, posts, CPTs
* Disable bookmarking for individual page/posts
* Bookmark link can appear at top or bottom of page/post content
* Bookmarks can be organized into different user created folders
* Folders can be made public or private by users
* Users can view and manage their bookmark folders and bookmarks from their profiles
* Users can view other users public bookmark folders

= Development * Translations =

Want to add a new language to Ultimate Member? Great! You can contribute via [translate.wordpress.org](https://translate.wordpress.org/projects/wp-plugins/ultimate-member).

If you are a developer and you need to know the list of UM Hooks, make this via our [Hooks Documentation](https://docs.ultimatemember.com/article/1324-hooks-list).

= Documentation & Support =

Got a problem or need help with Ultimate Member? Head over to our [documentation](http://docs.ultimatemember.com/) and perform a search of the knowledge base. If you can’t find a solution to your issue then you can create a topic on the [support forum](https://wordpress.org/support/plugin/um-forumwp).

== Installation ==

1. Activate the plugin
2. That's it. Go to Ultimate Member > Settings > Extensions > User Photos to customize plugin options
3. For more details, please visit the official [Documentation](https://docs.ultimatemember.com/article/1466-user-photos) page.

== Changelog ==

= 2.2.0: November 15, 2024 =

* Added: Compatibility with Ultimate Member 2.9.0 and Action Scheduler for email sending
* Fixed: Load textdomain for getting plugin data
* Fixed: The "My Photos" account tab visibility
* Fixed: URLs in the [ultimatemember_gallery_photos] shortcode
* Tweak: Compatibility with the new UI of Ultimate Member core

* Templates required update:

  - modal/edit-album.php
  - modal/edit-comment.php
  - modal/edit-image.php
  - modal/likes.php
  - account.php
  - albums.php
  - caption.php
  - comment.php
  - comment-form.php
  - comments.php
  - gallery.php
  - gallery-head.php
  - photos.php
  - photos-grid.php
  - single-album.php
  - single-image.php

* Cached and optimized/minified assets(JS/CSS) must be flushed/re-generated after upgrade

= 2.1.9: May 22, 2024 =

* Fixed: Grid layout for albums
* Fixed: Frontend UI after async actions for image, likes, comments
* Fixed: Hide comments and likes from hidden user roles
* Tweak: Ability to select the album photo as cover. Cover image is the part of the album for now.

* Templates required update:

  - modal/add-album.php
  - modal/edit-album.php
  - modal/edit-image.php
  - album-block.php
  - album-head.php
  - albums.php
  - caption.php
  - comment.php
  - comments.php
  - gallery.php
  - photos.php
  - single-album.php

* Deleted templates:

  - modal/delete-album.php

* Cached and optimized/minified assets(JS/CSS) must be flushed/re-generated after upgrade

= 2.1.8: February 21, 2024 =

* Added: wp-admin submenu for user photos preview
* Fixed: Album and photo can view capability and changed all restrictions based on this function
* Fixed: Deletion of the cover photo after album deletion
* Fixed: Added unique security wpnonce per AJAX action
* Fixed: Removed "Disabled comments" option if the comments are disabled generally
* Fixed: Delete and download all photos
* Fixed: Shortcode layouts

* Templates required update:

  - modal/delete-comment.php
  - modal/edit-comment.php
  - modal/edit-image.php
  - modal/modal.php
  - account.php
  - album-block.php
  - album-head.php
  - caption.php
  - comment-form.php
  - comment.php
  - gallery-head.php
  - pagination.php
  - photos.php
  - single-album.php

* Cached and optimized/minified assets(JS/CSS) must be flushed/re-generated after upgrade

= 2.1.7: December 20, 2023 =

* Fixed: Album privacy settings with Followers, Following and Friends users. Performance for Activity wall loading.
* Fixed: URL when going from the gallery to the album and back when Photos is the default Profile tab
* Fixed: Redirect URL after album deletion

= 2.1.6: December 11, 2023 =

* Fixed: Album privacy settings with Followers and Following users. User Profile page, Activity wall posts
* Fixed: Album privacy settings with Friends users. User Profile page, Activity wall posts
* Tweak: Using enqueue scripts suffix from UM core class. Dependency from UM core 2.7.0
* Tweak: Added CPT as UM registered post type for define proper UM screen on wp-admin
* Tweak: `um-admin-clear` CSS class. It duplicates WordPress native `clear`. Using WordPress native instead
* Tweak: Enhancements related to WPCS
* Tweak: Using `UM()->datetime()->time_diff()` function instead local registered functions duplicates
* Fixed: Added nonce for getting photos or comments likes
* Fixed: Unlike photo action
* Fixed: Texts in templates

* Templates required update:

  - modal/likes.php

= 2.1.5: October 11, 2023 =

* Fixed: Case when extension isn't active based on dependency, but we can provide the license key field

= 2.1.4: October 4, 2023 =

* Fixed: Made redirect safe

= 2.1.3: September 01, 2023 =

* Added: Album ID and Photo ID as query args to the URL for easy sharing
* Fixed: Max files upload when "Album photos limit" setting is empty
* Fixed: Max files size upload
* Fixed: The "Load more" button is at the end of the photo list displaying
* Fixed: Displaying photos in popup after pagination
* Fixed: Edit comment action
* Tweak: Removed `extract()` function and increase supporting WordPress Code Standards

= 2.1.2: June 14, 2023 =

* Added: Hook for extends uploaded image types `um_user_photos_allowed_image_types`
* Tweak: Template overwrite versioning
* Fixed: PHP warnings

= 2.1.1: August 17, 2022 =

* Added: Privacy settings for the albums on the frontend
* Added: Method that deletes all user photos
* Added: Global setting for the album photos limit
* Fixed: Account > User Photos > Download all button displaying

= 2.1.0: February 9, 2022 =

* Added: Photo uploading limits for the roles
* Changed: Separated album notification from 1 to 3 actions (album added, album edited, album deleted)
* Fixed: Extension settings structure
* Fixed: Modal does not close when deleting album
* Fixed: Empty album cover URL

* Templates required update:
  - email/new_album.php

= 2.0.9: December 20, 2021 =

* Added: Photo sorting on album edit modal
* Fixed: Conflict with All in One SEO. Moved modal template to the footer.
* Fixed: Update album photos on image delete
* Fixed: Visibility of the user photos uploads on media library list view.

= 2.0.8: March 29, 2021 =

* Tweak: WordPress 5.7 compatibility

= 2.0.7: December 8, 2020 =

* Added: "Disable title", "Disable cover photo" and "Disable comments" options
* Fixed: HTML attribute for disable_comments field (added value="1")
* Fixed: Typo errors in templates
* Fixed: Activity image grid and lightbox

= 2.0.6: August 11, 2020 =

* Added: Setting "Photo rows"
* Added: Field "Related link" in the popup "Edit Image"
* Added: A link to the related page in the photo details popup
* Added: *.pot translations file
* Changed: Wrap a link inside the image comment
* Changed: A grid layout for the photos gallery
* Changed: Templates structure
* Fixed: Issue with modal window duplicates
* Fixed: Layout styles for photos in the activity wall
* Fixed: Modal windows loading PHP issues
* Fixed: Template "single-album"
* Fixed: Photo Likes

= 2.0.5: January 24, 2020 =

* Added: Shortcode [ultimatemember_albums]
* Fixed: CSS issue

= 2.0.4: November 11, 2019 =

* Added: Sanitize functions for request variables
* Added: esc_attr functions to avoid XSS vulnerabilities
* Added: Email notifications to admin when user create, delete ot update an album
* Fixed: Account page my Photos
* Fixed: Empty download issue
* Fixed: Uninstall process

= 2.0.3: February 8, 2019 =

* Optimization: use method UM()->get_template() to load templates
* Fixed: Profile Tabs
* Fixed: Close album options dropdown
* Fixed: Album photos grid style
* Fixed: Download my photo's function is not working on account page
* Fixed: JS/CSS enqueue

= 2.0.2: November 12, 2018 =

* Fixed: Force image unlink
* Optimized: JS/CSS enqueue

= 2.0.1: October 1, 2018 =

* Initial Release
