/* Set the width of the thumbnail column */
.column-thumbnail {
	width: 50px;
}

/* Style the thumbnail image. */
.column-thumbnail img {
	width: 50px;
	height: 50px;
	border-radius: 4px;
	object-fit: cover;
}

/* Style the Dashicon placeholder for empty thumbnails. */
.column-thumbnail .dashicons {
	font-size: 50px;
	width: 50px;
	height: 50px;
	background: #c3c4c7;
	border-radius: 4px;
	color: #f6f7f7;
}

/* Keep data-colname from appearing on mobile view. */
.column-thumbnail::before {
	content: "" !important;
}

/* Move the "Thumbnail" and "Title" columns closer together. */
th.column-thumbnail {
	padding-right:0;
}

/* Don't display the "Thumbnail" column name in the header or footer. */
th.column-thumbnail span {
	display:none;
}

/* Pull "Title" header name over in "Thumbnail"s place. */
th.column-thumbnail + th a {
	margin-left:-60px;
}

/* If thumbnail column has .hidden class. The entire column is toggled off. Don't pull "Title" header name over. */
th.column-thumbnail.hidden + th a {
	margin-left:0;
}

@media screen and (max-width: 782px) {

	/*
	 * Keep it as a table-cell.
	 * See https://github.com/WordPress/wordpress-develop/blob/4a9a928dbcd1c91d3633c8de51614dd90d8ea0ac/src/wp-admin/css/list-tables.css#L1969.
	 */
	.wp-list-table .is-expanded td:not(.hidden).column-thumbnail {
		display: table-cell !important;
	}
}
