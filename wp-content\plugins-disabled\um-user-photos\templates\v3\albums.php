<?php
/**
 * Template for the UM User Photos, common "Albums" block
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/albums.php
 *
 * Use via shortcode: [ultimatemember_albums]
 *
 * @version 2.1.9
 *
 * @var array $args
 * @var array $albums
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( empty( $albums ) || ! $albums->have_posts() ) {
	return;
}
$disable_title = UM()->options()->get( 'um_user_photos_disable_title' );
?>
<div class="um ultimatemember_albums" data-wpnonce="<?php echo esc_attr( wp_create_nonce( 'um_user_photos_pagination' ) ); ?>" data-um-pagi-column="<?php echo esc_attr( $args['column'] ); ?>" data-um-pagi-per_page="<?php echo esc_attr( $args['per_page'] ); ?>">

	<?php do_action( 'ultimatemember_albums_before', $albums, $args ); ?>

	<div class="um-user-photos-albums um-grid um-grid-col-<?php echo esc_attr( $args['column'] ); ?>">
		<?php
		foreach ( $albums->posts as $i => $album ) {
			$user = get_userdata( $album->post_author );
			if ( ! $user ) {
				continue;
			}
			$photos    = $album->_photos;
			$album_url = add_query_arg(
				array(
					'profiletab' => 'photos',
					'album_id'   => $album->ID,
				),
				um_user_profile_url( $album->post_author )
			);
			if ( $photos ) {
				$count = count( $photos );
				// translators: %s is a photos count.
				$count_msg = sprintf( _n( '%s Photo', '%s Photos', $count, 'um-user-photos' ), number_format_i18n( $count ) );
			} else {
				$count_msg = false;
			}

			$data_t = array(
				'id'            => $album->ID,
				'title'         => get_the_title( $album->ID ),
				'author'        => $album->post_author,
				'count_msg'     => $count_msg,
				'img'           => UM()->User_Photos()->common()->album()->get_cover( $album->ID ),
				'disable_title' => $disable_title,
				'context'       => 'albums',
				'url'           => $album_url,
			);

			UM()->get_template( 'v3/album-block.php', UM_USER_PHOTOS_PLUGIN, $data_t, true );
		}
		?>
	</div>

	<?php
	if ( $albums->found_posts > $args['per_page'] ) {
		$pagination = UM()->frontend()::layouts()::pagination(
			array(
				'page'     => absint( $args['page'] ),
				'total'    => $albums->found_posts,
				'per_page' => $args['per_page'],
			)
		);
		echo wp_kses( $pagination, UM()->get_allowed_html( 'templates' ) );
	}
	?>

	<?php do_action( 'ultimatemember_albums_after', $albums, $args ); ?>
</div>
