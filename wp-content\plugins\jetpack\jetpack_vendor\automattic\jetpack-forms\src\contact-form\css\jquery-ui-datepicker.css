.ui-datepicker {
	padding: 0;
	margin: 0;
	border-radius: 0;
	background-color: #fff;
	border: 1px solid #dcdcde;
	border-top: none;
	box-shadow: 0 3px 6px rgba(0, 0, 0, 0.075);
	width: auto;
}

.ui-datepicker-calendar,
.ui-datepicker-header .ui-datepicker-title,
.ui-datepicker-header a {
  color: #444;
  text-decoration: none;
}

.ui-datepicker * {
	padding: 0;
	border-radius: 0;
}

.ui-datepicker table {
	width: auto;
	margin: 0;
	border: none;
	border-collapse: collapse;
}

.ui-datepicker .ui-widget-header,
.ui-datepicker .ui-datepicker-header {
	background-image: none;
	border: none;
	font-weight: 400;
}

.ui-datepicker .ui-datepicker-header .ui-state-hover {
	background: transparent;
	border-color: transparent;
	cursor: pointer;
}

.ui-datepicker .ui-datepicker-title {
	margin: 0;
	padding: 10px 0;
	font-size: 14px;
	line-height: 14px;
	text-align: center;
}

.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
	position: relative;
	top: 0;
	height: 34px;
	width: 34px;
}

.ui-datepicker .ui-state-hover.ui-datepicker-prev,
.ui-datepicker .ui-state-hover.ui-datepicker-next {
	border: none;
}

.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-prev-hover {
	left: 0;
}

.ui-datepicker .ui-datepicker-next,
.ui-datepicker .ui-datepicker-next-hover {
	right: 0;
}

.ui-datepicker .ui-datepicker-next span,
.ui-datepicker .ui-datepicker-prev span {
	display: none;
}

.ui-datepicker .ui-datepicker-prev {
	float: left;
}

.ui-datepicker .ui-datepicker-next {
	float: right;
}

.ui-datepicker .ui-datepicker-prev::before,
.ui-datepicker .ui-datepicker-next::before {
	font: 400 20px/34px dashicons;
	padding-left: 7px;
	speak: none;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	width: 34px;
	height: 34px;
}

.ui-datepicker .ui-datepicker-prev::before {
	content: '\f341';
}

.ui-datepicker .ui-datepicker-next::before {
	content: '\f345';
}

.ui-datepicker .ui-datepicker-prev-hover::before,
.ui-datepicker .ui-datepicker-next-hover::before {
	opacity: 0.7;
}

.ui-datepicker select.ui-datepicker-month,
.ui-datepicker select.ui-datepicker-year {
	width: 33%;
}

.ui-datepicker thead {
	font-weight: 600;
}

.ui-datepicker th {
	padding: 10px;
	border-width: 1px;
}

.ui-datepicker td {
	padding: 0;
	border: 1px solid #f4f4f4;
}

.ui-datepicker td.ui-datepicker-other-month {
	border: transparent;
}

.ui-datepicker td.ui-datepicker-week-end {
	background-color: #f4f4f4;
	border: 1px solid #f4f4f4;
}

.ui-datepicker td.ui-datepicker-today {
	background-color: #f0f0c0;
}

.ui-datepicker td.ui-datepicker-current-day {
	background: #bd8;
}

.ui-datepicker td .ui-state-default {
	background: transparent;
	border: none;
	text-align: center;
	text-decoration: none;
	width: auto;
	display: block;
	padding: 5px 10px;
	font-weight: 400;
	color: #444;
}

.ui-datepicker td.ui-state-disabled .ui-state-default {
	opacity: 0.5;
}
