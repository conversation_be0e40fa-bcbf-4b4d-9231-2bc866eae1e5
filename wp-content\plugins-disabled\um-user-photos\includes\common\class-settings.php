<?php
namespace um_ext\um_user_photos\common;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class User
 *
 * @package um_ext\um_user_photos\common
 */
class Settings {

	/**
	 * Settings constructor.
	 */
	public function __construct() {
		add_filter( 'um_get_option_filter__um_user_photos_albums_column', array( &$this, 'check_legacy_value' ) );
		add_filter( 'um_get_option_filter__um_user_photos_images_column', array( &$this, 'check_legacy_value' ) );
	}

	/**
	 * Operate with legacy setting value.
	 *
	 * @todo deprecate since new UI is released and old UI deprecated.
	 * @since 2.1.1
	 *
	 * @param int|string $value Setting value.
	 *
	 * @return string|int
	 */
	public function check_legacy_value( $value ) {
		if ( empty( $value ) ) {
			return $value;
		}

		if ( defined( 'UM_DEV_MODE' ) && UM_DEV_MODE && UM()->options()->get( 'enable_new_ui' ) ) {
			if ( ! is_numeric( $value ) ) {
				$value = str_replace( 'um-user-photos-col-', '', $value );
			}
		} elseif ( is_numeric( $value ) ) {
			$value = 'um-user-photos-col-' . $value;
		}
		return $value;
	}
}
