<?php
namespace um_ext\um_user_bookmarks\admin;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}


if ( ! class_exists( 'um_ext\um_user_bookmarks\admin\Admin' ) ) {


	/**
	 * Class Admin
	 * @package um_ext\um_user_bookmarks\admin
	 */
	class Admin {


		/**
		 * Admin constructor.
		 */
		public function __construct() {
			add_filter( 'admin_enqueue_scripts', array( &$this, 'admin_scripts' ), 11 );
			add_filter( 'um_settings_structure', array( &$this, 'extend_settings' ) );
			add_filter( 'um_admin_role_metaboxes', array( &$this, 'um_user_bookmarks_add_role_metabox' ), 10, 1 );

			add_action( 'um_settings_save', array( $this, 'on_settings_save' ) );

			add_filter( 'um_override_templates_scan_files', array( $this, 'um_bookmarks_extend_scan_files' ), 10, 1 );
			add_filter( 'um_override_templates_get_template_path__um-user-bookmarks', array( $this, 'um_bookmarks_get_path_template' ), 10, 2 );
		}


		/**
		 *
		 */
		public function on_settings_save() {
			// phpcs:disable WordPress.Security.NonceVerification
			if ( ! empty( $_POST['um_options'] ) ) {
				if ( isset( $_POST['um_options']['um_user_bookmarks_disable_folders'] ) ) {
					if ( empty( $_POST['um_options']['um_user_bookmarks_disable_folders'] ) ) {
						update_option( 'um_user_bookmarks_disable_folders_truncated', time() );
					}
				}
			}
			// phpcs:enable WordPress.Security.NonceVerification
		}

		/**
		 *
		 */
		public function admin_scripts( $hook ) {
			$suffix = UM()->admin()->enqueue()::get_suffix();
			if ( 'ultimate-member_page_um_options' === $hook ) {
				wp_register_script( 'um_user_bookmarks', um_user_bookmarks_url . 'assets/js/admin/um-user-bookmarks' . $suffix . '.js', array( 'um_admin_forms' ), um_user_bookmarks_version, true );
				wp_enqueue_script( 'um_user_bookmarks' );
			}
		}

		/**
		 * Additional Settings for Photos
		 *
		 * @param array $settings
		 *
		 * @return array
		 */
		public function extend_settings( $settings ) {

			$settings['licenses']['fields'][] = array(
				'id'        => 'um_user_bookmarks_license_key',
				'label'     => __( 'User Bookmarks License Key', 'um-user-bookmarks' ),
				'item_name' => 'User Bookmarks',
				'author'    => 'ultimatemember',
				'version'   => um_user_bookmarks_version,
			);

			$latest_update   = get_option( 'um_user_bookmarks_disable_folders_update', false );
			$latest_truncate = get_option( 'um_user_bookmarks_disable_folders_truncated', false );

			$same_page_update = array(
				'id'      => 'um_user_bookmarks_disable_folders',
				'type'    => 'same_page_update',
				'label'   => __( 'Disable folder system for Bookmarks', 'um-user-bookmarks' ),
				'tooltip' => __( 'Check this box if you would like to enable the using User Bookmarks functionality without folders.', 'um-user-bookmarks' ),
			);

			if ( empty( $latest_update ) || ( ! empty( $latest_truncate ) && $latest_truncate > $latest_update ) ) {
				$same_page_update['upgrade_cb']          = 'sync_user_bookmarks_disable_folders';
				$same_page_update['upgrade_description'] = '<p>' . __( 'We recommend creating a backup of your site before running the update process. Do not exit the page before the update process has complete.', 'um-user-bookmarks' ) . '</p>
<p>' . __( 'After clicking the <strong>"Run"</strong> button, the update process will start. All information will be displayed in the field below.', 'um-user-bookmarks' ) . '</p>
<p>' . __( 'If the update was successful, you will see a corresponding message. Otherwise, contact technical support if the update failed.', 'um-user-bookmarks' ) . '</p>';
			}

			$settings['extensions']['sections']['um-user-bookmarks'] = array(
				'title'  => __( 'User Bookmarks', 'um-user-bookmarks' ),
				'fields' => array(
					array(
						'id'          => 'um_user_bookmarks_position',
						'type'        => 'select',
						'placeholder' => '',
						'options'     => array(
							'bottom' => __( 'Bottom', 'um-user-bookmarks' ),
							'top'    => __( 'Top', 'um-user-bookmarks' ),
						),
						'label'       => __( 'Bookmark icon position', 'um-user-bookmarks' ),
						'size'        => 'medium',
					),
					array(
						'id'          => 'um_user_bookmarks_post_types',
						'type'        => 'select',
						'multi'       => true,
						'placeholder' => '',
						'options'     => $this->um_user_bookmarks_get_public_post_types(),
						'label'       => __( 'Enable bookmark', 'um-user-bookmarks' ),
						'size'        => 'medium',
					),
					array(
						'id'    => 'um_user_bookmarks_archive_page',
						'type'  => 'checkbox',
						'label' => __( 'Enable bookmark on archive pages', 'um-user-bookmarks' ),
					),
					array(
						'id'    => 'um_user_bookmarks_profile',
						'type'  => 'checkbox',
						'label' => __( 'Enable bookmark on profile pages', 'um-user-bookmarks' ),
					),
					array(
						'id'          => 'um_user_bookmarks_profile_folders_text',
						'type'        => 'text',
						'label'       => __( 'Profile folders text (Plural)', 'um-user-bookmarks' ),
						'size'        => 'medium',
						'conditional' => array( 'um_user_bookmarks_profile', '=', 1 ),
					),
					array(
						'id'          => 'um_user_bookmarks_profile_folder_text',
						'type'        => 'text',
						'placeholder' => '',
						'label'       => __( 'Profile folder text (Singular)', 'um-user-bookmarks' ),
						'size'        => 'medium',
						'conditional' => array( 'um_user_bookmarks_profile', '=', 1 ),
					),
					array(
						'id'          => 'um_user_bookmarks_add_text',
						'type'        => 'text',
						'placeholder' => '',
						'label'       => __( 'Add bookmark button text', 'um-user-bookmarks' ),
						'size'        => 'medium',
					),
					array(
						'id'          => 'um_user_bookmarks_remove_text',
						'type'        => 'text',
						'placeholder' => '',
						'label'       => __( 'Remove bookmark button text', 'um-user-bookmarks' ),
						'size'        => 'medium',
					),
					$same_page_update,
					array(
						'id'          => 'um_user_bookmarks_folders_text',
						'type'        => 'text',
						'placeholder' => '',
						'label'       => __( 'Folders text (Plural)', 'um-user-bookmarks' ),
						'size'        => 'medium',
						'conditional' => array( 'um_user_bookmarks_disable_folders', '!=', 1 ),
					),
					array(
						'id'          => 'um_user_bookmarks_folder_text',
						'type'        => 'text',
						'placeholder' => '',
						'label'       => __( 'Folder text (Singular)', 'um-user-bookmarks' ),
						'size'        => 'medium',
						'conditional' => array( 'um_user_bookmarks_disable_folders', '!=', 1 ),
					),
					array(
						'id'          => 'um_user_bookmarks_bookmarked_icon',
						'type'        => 'text',
						'placeholder' => '',
						'label'       => __( 'Bookmarked Icon (css class)', 'um-user-bookmarks' ),
						'size'        => 'medium',
					),
					array(
						'id'          => 'um_user_bookmarks_regular_icon',
						'type'        => 'text',
						'placeholder' => '',
						'label'       => __( 'Regular Icon (css class)', 'um-user-bookmarks' ),
						'size'        => 'medium',
					),
					array(
						'id'      => 'um_user_bookmarks_page_builder',
						'type'    => 'checkbox',
						'label'   => __( 'Using Page Builder', 'um-user-bookmarks' ),
						'tooltip' => __( 'If it\'s not checked - show post excerpt without applying shortcodes', 'um-user-bookmarks' ),
					),
					array(
						'id'          => 'um_user_bookmarks_default_folder',
						'type'        => 'checkbox',
						'label'       => __( 'Create default folder', 'um-user-bookmarks' ),
						'tooltip'     => __( 'Automatically create the first bookmarks folder for a newly created member', 'um-user-bookmarks' ),
						'conditional' => array( 'um_user_bookmarks_disable_folders', '!=', 1 ),
					),
					array(
						'id'          => 'um_user_bookmarks_default_folder_name',
						'type'        => 'text',
						'label'       => __( 'Default folder name', 'um-user-bookmarks' ),
						'size'        => 'medium',
						'conditional' => array( 'um_user_bookmarks_default_folder', '=', 1 ),
					),
				),
			);

			return $settings;
		}


		/**
		 * @return array
		 */
		public function um_user_bookmarks_get_public_post_types() {
			$public_post_type = array();

			$post_types = get_post_types(
				array(
					'public'              => true,
					'exclude_from_search' => false,
					'_builtin'            => false,
					'supports'            => array( 'title', 'editor' ),
				),
				'objects',
				'or'
			);

			if ( count( $post_types ) ) {
				$escape = array(
					'um_private_content',
					'um_directory',
					'um_form',
					'um_profile_tabs',
					'um_user_photos',
					'reply',
					'um_activity',
				);

				foreach ( $post_types as $post_type_key => $post_type_data ) {
					if ( in_array( $post_type_key, $escape, true ) ) {
						continue;
					}

					$public_post_type[ $post_type_key ] = $post_type_data->label;
				}
			}

			return apply_filters( 'um_user_bookmarks_admin_post_types', $public_post_type );
		}


		/**
		 * @param $roles_metaboxes
		 *
		 * @return array
		 */
		public function um_user_bookmarks_add_role_metabox( $roles_metaboxes ) {
			$roles_metaboxes[] = array(
				'id'       => 'um-admin-form-bookmark{' . um_user_bookmarks_path . '}',
				'title'    => __( 'User Bookmarks', 'um-user-bookmarks' ),
				'callback' => array( UM()->metabox(), 'load_metabox_role' ),
				'screen'   => 'um_role_meta',
				'context'  => 'normal',
				'priority' => 'default',
			);

			return $roles_metaboxes;
		}


		/**
		 * Scan templates from extension
		 *
		 * @param $scan_files
		 *
		 * @return array
		 */
		public function um_bookmarks_extend_scan_files( $scan_files ) {
			$extension_files['um-user-bookmarks'] = UM()->admin_settings()->scan_template_files( um_user_bookmarks_path . '/templates/' );
			$scan_files                           = array_merge( $scan_files, $extension_files );

			return $scan_files;
		}


		/**
		 * Get template paths
		 *
		 * @param $located
		 * @param $file
		 *
		 * @return array
		 */
		public function um_bookmarks_get_path_template( $located, $file ) {
			if ( file_exists( get_stylesheet_directory() . '/ultimate-member/um-user-bookmarks/' . $file ) ) {
				$located = array(
					'theme' => get_stylesheet_directory() . '/ultimate-member/um-user-bookmarks/' . $file,
					'core'  => um_user_bookmarks_path . 'templates/' . $file,
				);
			}

			return $located;
		}
	}
}
