name: PHPUnit tests

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  php-version:

    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        php-version:
          - "5.4"
          - "5.5"
          - "5.6"
          - "7.0"
          - "7.1"
          - "7.2"
          - "7.3"
          - "7.4"
          - "8.0"
          - "8.1"

    steps:

        - uses: actions/checkout@v2

        - name: Install PHP
          uses: "shivammathur/setup-php@v2"
          with:
            php-version: "${{ matrix.php-version }}"
            coverage: "none"
            ini-values: "zend.assertions=1"

        - name: Install Composer dependencies
          run: composer install --no-progress --ansi

        - name: Run tests ${{ matrix.php-version }}
          run: SYMFONY_PHPUNIT_REMOVE_RETURN_TYPEHINT=1 bin/simple-phpunit --color=always
