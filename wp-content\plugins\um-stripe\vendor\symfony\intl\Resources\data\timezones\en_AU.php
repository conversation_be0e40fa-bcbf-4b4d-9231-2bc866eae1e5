<?php

namespace UM_Stripe\Vendor\UM_Stripe\Vendor;

return ['Names' => ['Africa/Addis_Ababa' => 'Eastern Africa Time (Addis Ababa)', 'Africa/Asmera' => 'Eastern Africa Time (Asmara)', 'Africa/Dar_es_Salaam' => 'Eastern Africa Time (Dar es Salaam)', 'Africa/Djibouti' => 'Eastern Africa Time (Djibouti)', 'Africa/Kampala' => 'Eastern Africa Time (Kampala)', 'Africa/Mogadishu' => 'Eastern Africa Time (Mogadishu)', 'Africa/Nairobi' => 'Eastern Africa Time (Nairobi)', 'Antarctica/Casey' => 'Australian Western Time (Casey)', 'Antarctica/Macquarie' => 'Australian Eastern Time (Macquarie)', 'Asia/Aden' => 'Arabia Time (Aden)', 'Asia/Baghdad' => 'Arabia Time (Baghdad)', 'Asia/Bahrain' => 'Arabia Time (Bahrain)', 'Asia/Kuwait' => 'Arabia Time (Kuwait)', 'Asia/Pyongyang' => 'Korea Time (Pyongyang)', 'Asia/Qatar' => 'Arabia Time (Qatar)', 'Asia/Riyadh' => 'Arabia Time (Riyadh)', 'Asia/Seoul' => 'Korea Time (Seoul)', 'Australia/Adelaide' => 'Australian Central Time (Adelaide)', 'Australia/Brisbane' => 'Australian Eastern Time (Brisbane)', 'Australia/Broken_Hill' => 'Australian Central Time (Broken Hill)', 'Australia/Darwin' => 'Australian Central Time (Darwin)', 'Australia/Hobart' => 'Australian Eastern Time (Hobart)', 'Australia/Lindeman' => 'Australian Eastern Time (Lindeman)', 'Australia/Melbourne' => 'Australian Eastern Time (Melbourne)', 'Australia/Perth' => 'Australian Western Time (Perth)', 'Australia/Sydney' => 'Australian Eastern Time (Sydney)', 'Indian/Antananarivo' => 'Eastern Africa Time (Antananarivo)', 'Indian/Comoro' => 'Eastern Africa Time (Comoro)', 'Indian/Mayotte' => 'Eastern Africa Time (Mayotte)', 'Pacific/Rarotonga' => 'Cook Island Time (Rarotonga)'], 'Meta' => []];
