<?php
/**
 * Template for the UM User Photos. The "Album" block
 *
 * Call: UM()->User_Photos()->ajax()->um_user_photos_load_more()
 * Call: UM()->User_Photos()->ajax()->um_user_photos_get_single_album_view()
 * Page: "Profile", tab "Photos"
 * Parent template: photos.php
 * @version 2.1.9
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/single-album.php
 * @var WP_Post $album
 * @var int     $album_id
 * @var int     $columns
 * @var bool    $is_my_profile
 * @var bool    $count
 * @var array   $photos
 * @var array   $is_profile_tab
 * @var int     $count_photos
 * @var int     $disable_title
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
$wrapper_classes = array( 'um-user-photos-single-album' );
if ( ! $is_profile_tab ) {
	$wrapper_classes[] = 'um';
}
?>

<div class="<?php echo esc_attr( implode( ' ', $wrapper_classes ) ); ?>" data-album_id="<?php echo esc_attr( $album_id ); ?>">
	<?php
	echo wp_kses( UM()->frontend()::layouts()::ajax_loader( 'l', array( 'classes' => array( 'um-user-photos-albums-loader', 'um-display-none' ) ) ), UM()->get_allowed_html( 'templates' ) );

	UM()->get_template(
		'v3/album-head.php',
		UM_USER_PHOTOS_PLUGIN,
		compact( 'album', 'album_id', 'columns', 'count', 'is_my_profile', 'photos', 'disable_title' ),
		true
	);

	if ( ! empty( $photos ) && is_array( $photos ) ) {
		?>
		<div class="um-grid um-user-photos-grid um-grid-col-<?php echo esc_attr( $columns ); ?>" data-count="<?php echo esc_attr( count( $photos ) ); ?>">
			<?php
			$args_t = compact( 'is_my_profile', 'photos' );
			UM()->get_template( 'v3/photos-grid.php', UM_USER_PHOTOS_PLUGIN, $args_t, true );
			?>
		</div>
		<?php
		if ( $count_photos > count( $photos ) ) {
			$button = UM()->frontend()::layouts()::button(
				__( 'Load more', 'um-user-photos' ),
				array(
					'id'     => 'um-user-photos-photos-load-more',
					'type'   => 'button',
					'size'   => 's',
					'design' => 'tertiary-gray',
					'data'   => array(
						'wpnonce'      => wp_create_nonce( 'single_album_load_more' ),
						'album_id'     => $album_id,
						'profile'      => $album->post_author,
						'count_photos' => $count_photos,
					),
				)
			);

			echo wp_kses( $button, UM()->get_allowed_html( 'templates' ) );
		}
	} else {
		?>
		<p class="um-supporting-text um-align-center"><?php esc_html_e( 'Nothing to display', 'um-user-photos' ); ?></p>
		<?php
	}
	?>
</div>
