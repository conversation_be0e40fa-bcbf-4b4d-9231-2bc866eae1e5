{"classic": {"name": "Classic", "settings": {"fieldSize": "medium", "fieldBorderRadius": "0px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "#ffffff", "fieldBorderColor": "#cccccc", "fieldTextColor": "#444444", "fieldMenuColor": "#ffffff", "labelSize": "small", "labelColor": "#262626", "labelSublabelColor": "#7f7f7f", "labelErrorColor": "#ff0000", "buttonSize": "medium", "buttonBorderStyle": "solid", "buttonBorderSize": "1px", "buttonBorderRadius": "0px", "buttonBorderColor": "#cccccc", "buttonBackgroundColor": "#dddddd", "buttonTextColor": "#666666", "pageBreakColor": "#dddddd", "containerShadowSize": "none", "containerPadding": "0px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "3px", "backgroundImage": "none", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "rgba( 0, 0, 0, 0 )", "backgroundUrl": "url()"}}, "default": {"name": "<PERSON><PERSON><PERSON>", "settings": {"fieldSize": "medium", "fieldBorderRadius": "3px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "#ffffff", "fieldBorderColor": "rgba( 0, 0, 0, 0.25 )", "fieldTextColor": "rgba( 0, 0, 0, 0.7 )", "fieldMenuColor": "#ffffff", "labelSize": "medium", "labelColor": "rgba( 0, 0, 0, 0.85 )", "labelSublabelColor": "rgba( 0, 0, 0, 0.55 )", "labelErrorColor": "#d63637", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "3px", "buttonBorderColor": "#066aab", "buttonBackgroundColor": "#066aab", "buttonTextColor": "#ffffff", "pageBreakColor": "#066aab", "containerShadowSize": "none", "containerPadding": "0px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "3px", "backgroundImage": "none", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "rgba( 0, 0, 0, 0 )", "backgroundUrl": "url()"}}, "cerulean": {"name": "<PERSON><PERSON><PERSON>", "settings": {"fieldSize": "medium", "fieldBorderRadius": "3px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "#ffffff", "fieldBorderColor": "#afb6b9", "fieldTextColor": "#262c2e", "fieldMenuColor": "#ffffff", "labelSize": "medium", "labelColor": "#262c2e", "labelSublabelColor": "#4c585d", "labelErrorColor": "#d63636", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "3px", "buttonBorderColor": "#0090bf", "buttonBackgroundColor": "#0090bf", "buttonTextColor": "#e5f4f9", "pageBreakColor": "#0090bf", "containerShadowSize": "none", "containerPadding": "0px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "3px", "backgroundImage": "none", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "rgba( 0, 0, 0, 0 )", "backgroundUrl": "url()"}}, "ocean": {"name": "Ocean", "settings": {"fieldSize": "medium", "fieldBorderRadius": "3px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "#ffffff", "fieldBorderColor": "#9ea3a3", "fieldTextColor": "#181c1c", "fieldMenuColor": "#ffffff", "labelSize": "medium", "labelColor": "#181c1c", "labelSublabelColor": "#636c6c", "labelErrorColor": "#d63636", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "3px", "buttonBorderColor": "#348180", "buttonBackgroundColor": "#348180", "buttonTextColor": "#ebf2f2", "pageBreakColor": "#348180", "containerShadowSize": "none", "containerPadding": "0px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "0px", "backgroundImage": "none", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "rgba( 0, 0, 0, 0 )", "backgroundUrl": "url()"}}, "fern": {"name": "Fern", "settings": {"fieldSize": "medium", "fieldBorderRadius": "3px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "#ffffff", "fieldBorderColor": "#c6cbc6", "fieldTextColor": "#383c39", "fieldMenuColor": "#ffffff", "labelSize": "medium", "labelColor": "#383c39", "labelSublabelColor": "#707971", "labelErrorColor": "#d63636", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "3px", "buttonBorderColor": "#58bb5d", "buttonBackgroundColor": "#58bb5d", "buttonTextColor": "#eef8ef", "pageBreakColor": "#58bb5d", "containerShadowSize": "none", "containerPadding": "0px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "0px", "backgroundImage": "none", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "rgba( 0, 0, 0, 0 )", "backgroundUrl": "url()"}}, "lilac": {"name": "Lilac", "settings": {"fieldSize": "medium", "fieldBorderRadius": "3px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "#ffffff", "fieldBorderColor": "#bfbdc1", "fieldTextColor": "#333135", "fieldMenuColor": "#ffffff", "labelSize": "medium", "labelColor": "#333135", "labelSublabelColor": "#807b84", "labelErrorColor": "#d63636", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "3px", "buttonBorderColor": "#9864c0", "buttonBackgroundColor": "#9864c0", "buttonTextColor": "#f5eff9", "pageBreakColor": "#9864c0", "containerShadowSize": "none", "containerPadding": "0px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "0px", "backgroundImage": "none", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "rgba( 0, 0, 0, 0 )", "backgroundUrl": "url()"}}, "taffy": {"name": "<PERSON><PERSON>", "settings": {"fieldSize": "medium", "fieldBorderRadius": "3px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "#ffffff", "fieldBorderColor": "#dad4d7", "fieldTextColor": "#484446", "fieldMenuColor": "#ffffff", "labelSize": "medium", "labelColor": "#484446", "labelSublabelColor": "#b5aab0", "labelErrorColor": "#d63636", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "3px", "buttonBorderColor": "#cccccc", "buttonBackgroundColor": "#f785c0", "buttonTextColor": "#fef3f9", "pageBreakColor": "#f785c0", "containerShadowSize": "none", "containerPadding": "0px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "0px", "backgroundImage": "none", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "rgba( 0, 0, 0, 0 )", "backgroundUrl": "url()"}}, "tangerine": {"name": "Tangerine", "settings": {"fieldSize": "medium", "fieldBorderRadius": "3px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "#ffffff", "fieldBorderColor": "#cbc6c2", "fieldTextColor": "#3d3835", "fieldMenuColor": "#ffffff", "labelSize": "medium", "labelColor": "#3d3835", "labelSublabelColor": "#79716b", "labelErrorColor": "#d63636", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "3px", "buttonBorderColor": "#ff4500", "buttonBackgroundColor": "#e27730", "buttonTextColor": "#fcf1ea", "pageBreakColor": "#e27730", "containerShadowSize": "none", "containerPadding": "0px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "0px", "backgroundImage": "none", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "rgba( 0, 0, 0, 0 )", "backgroundUrl": "url()"}}, "slate": {"name": "Slate", "settings": {"fieldSize": "medium", "fieldBorderRadius": "3px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "#ffffff", "fieldBorderColor": "#c3c3c7", "fieldTextColor": "#3a3a41", "fieldMenuColor": "#ffffff", "labelSize": "medium", "labelColor": "#3a3a41", "labelSublabelColor": "#686974", "labelErrorColor": "#d63636", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "3px", "buttonBorderColor": "#67687d", "buttonBackgroundColor": "#67687d", "buttonTextColor": "#f0f0f2", "pageBreakColor": "#67687d", "containerShadowSize": "none", "containerPadding": "0px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "0px", "backgroundImage": "none", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "rgba( 0, 0, 0, 0 )", "backgroundUrl": "url()"}}, "iron": {"name": "Iron", "settings": {"fieldSize": "medium", "fieldBorderRadius": "3px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "#ffffff", "fieldBorderColor": "#cbcacb", "fieldTextColor": "#2e2c2e", "fieldMenuColor": "#ffffff", "labelSize": "medium", "labelColor": "#2e2c2e", "labelSublabelColor": "#969596", "labelErrorColor": "#d63636", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "3px", "buttonBorderColor": "#cccccc", "buttonBackgroundColor": "#2e2c2f", "buttonTextColor": "#eaeaea", "pageBreakColor": "#2e2c2f", "containerShadowSize": "none", "containerPadding": "0px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "0px", "backgroundImage": "none", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "rgba( 0, 0, 0, 0 )", "backgroundUrl": "url()"}}, "aero": {"name": "Aero", "settings": {"fieldSize": "medium", "fieldBorderRadius": "6px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "#ffffff", "fieldBorderColor": "#D2D2D2", "fieldTextColor": "#6A6A6A", "fieldMenuColor": "#ffffff", "labelSize": "medium", "labelColor": "#000000", "labelSublabelColor": "#6A6A6A", "labelErrorColor": "#D80F0F", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "6px", "buttonBorderColor": "#3961FF", "buttonBackgroundColor": "#3961FF", "buttonTextColor": "#FFFFFF", "pageBreakColor": "#3961FF", "containerShadowSize": "large", "containerPadding": "40px", "containerBorderStyle": "solid", "containerBorderWidth": "10px", "containerBorderColor": "#ffffff", "containerBorderRadius": "12px", "backgroundImage": "stock", "backgroundPosition": "bottom center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#f8f8f8", "backgroundUrl": "url()"}}, "blossom": {"name": "Blossom", "settings": {"fieldSize": "medium", "fieldBorderRadius": "10px", "fieldBorderStyle": "solid", "fieldBorderSize": "2px", "fieldBackgroundColor": "rgba( 255, 255, 255, 0.2 )", "fieldBorderColor": "rgba( 51, 26, 0, 0.15 )", "fieldTextColor": "#634d36", "fieldMenuColor": "#d3bfa6", "labelSize": "medium", "labelColor": "#32261b", "labelSublabelColor": "#634d36", "labelErrorColor": "#b20000", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "10px", "buttonBorderColor": "#d75e42", "buttonBackgroundColor": "#d75e42", "buttonTextColor": "#ffffff", "pageBreakColor": "#d75e42", "containerShadowSize": "large", "containerPadding": "60px", "containerBorderStyle": "solid", "containerBorderWidth": "10px", "containerBorderColor": "#ffffff", "containerBorderRadius": "30px", "backgroundImage": "stock", "backgroundPosition": "bottom center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#d3bfa6", "backgroundUrl": "mitchell-luo-_A1pTfsMNY4-unsplash.jpg"}}, "bokeh": {"name": "Bokeh", "settings": {"fieldSize": "medium", "fieldBorderRadius": "22px", "fieldBorderStyle": "none", "fieldBorderSize": "1px", "fieldBackgroundColor": "#FCF5F3", "fieldBorderColor": "#FCF5F3", "fieldTextColor": "#272633", "fieldMenuColor": "#a4a3b3", "labelSize": "medium", "labelColor": "#272633", "labelSublabelColor": "#625E53", "labelErrorColor": "#272633", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "22px", "buttonBorderColor": "#272633", "buttonBackgroundColor": "#272633", "buttonTextColor": "#FFFFFF", "pageBreakColor": "#272633", "containerShadowSize": "large", "containerPadding": "60px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "rgba( 0, 0, 0, 0 )", "containerBorderRadius": "20px", "backgroundImage": "stock", "backgroundPosition": "bottom center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#a4a3b3", "backgroundUrl": "alexander-grey-62vi3TG5EDg-unsplash.jpg"}}, "concrete": {"name": "Concrete", "settings": {"fieldSize": "medium", "fieldBorderRadius": "5px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "rgba(0, 0, 0, 0.25)", "fieldBorderColor": "rgba(255, 255, 255, 0.25)", "fieldTextColor": "rgba(255, 255, 255, 0.50)", "fieldMenuColor": "#282e2e", "labelSize": "medium", "labelColor": "#ffffff", "labelSublabelColor": "rgba(255, 255, 255, 0.50)", "labelErrorColor": "#ffffff", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "5px", "buttonBorderColor": "#f04970", "buttonBackgroundColor": "#f04970", "buttonTextColor": "#ffffff", "pageBreakColor": "#f04970", "containerShadowSize": "none", "containerPadding": "60px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "0px", "backgroundImage": "stock", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#282e2e", "backgroundUrl": "annie-spratt-6a3nqQ1YwBw-unsplash.jpg"}}, "cottoncandy": {"name": "Cotton Candy", "settings": {"fieldSize": "medium", "fieldBorderRadius": "5px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "rgba(255, 255, 255, 0.05)", "fieldBorderColor": "rgba(255, 255, 255, 0.5)", "fieldTextColor": "rgba(255, 255, 255, 0.75)", "fieldMenuColor": "#b9b9dd", "labelSize": "medium", "labelColor": "#ffffff", "labelSublabelColor": "rgba(255, 255, 255, 0.75)", "labelErrorColor": "#ffffff", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "5px", "buttonBorderColor": "#952653", "buttonBackgroundColor": "#952653", "buttonTextColor": "#ffffff", "pageBreakColor": "#952653", "containerShadowSize": "large", "containerPadding": "60px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "15", "backgroundImage": "stock", "backgroundPosition": "center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#b9b9dd", "backgroundUrl": "adrian-infernus-GLf7bAwCdYg-unsplash.jpg"}}, "craft": {"name": "Craft", "settings": {"fieldSize": "medium", "fieldBorderRadius": "0px", "fieldBorderStyle": "solid", "fieldBorderSize": "3px", "fieldBackgroundColor": "rgba(255, 255, 255, 0.10)", "fieldBorderColor": "rgba(255, 255, 255, 0.50)", "fieldTextColor": "#e0c5c5", "fieldMenuColor": "#6b3026", "labelSize": "medium", "labelColor": "#ffffff", "labelSublabelColor": "#e0c5c5", "labelErrorColor": "#ffffff", "buttonSize": "medium", "buttonBorderStyle": "solid", "buttonBorderSize": "3px", "buttonBorderRadius": "0px", "buttonBorderColor": "rgba(255, 255, 255, 0.50)", "buttonBackgroundColor": "rgba(0 ,0 ,0 ,0)", "buttonTextColor": "#ffffff", "pageBreakColor": "rgba(255, 255, 255, 0.50)", "containerShadowSize": "none", "containerPadding": "60px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "0px", "backgroundImage": "stock", "backgroundPosition": "bottom center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#6b3026", "backgroundUrl": "valentin-salja-CLvkkjb-i3g-unsplash.jpg"}}, "elegance": {"name": "Elegance", "settings": {"fieldSize": "medium", "fieldBorderRadius": "4px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "#ffffff", "fieldBorderColor": "#e6e6e4", "fieldTextColor": "#7d7d7d", "fieldMenuColor": "#ecf0f3", "labelSize": "medium", "labelColor": "#7d7d7d", "labelSublabelColor": "#7d7d7d", "labelErrorColor": "#d63637", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "4px", "buttonBorderColor": "#7d7d7d", "buttonBackgroundColor": "#7d7d7d", "buttonTextColor": "#ffffff", "pageBreakColor": "#7d7d7d", "containerShadowSize": "none", "containerPadding": "60px", "containerBorderStyle": "solid", "containerBorderWidth": "10px", "containerBorderColor": "#e6e6e4", "containerBorderRadius": "30px", "backgroundImage": "stock", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#ecf0f3", "backgroundUrl": "alyssa-hurley-yekIZ4ltv1o-unsplash.jpg"}}, "feathered": {"name": "Feathered", "settings": {"fieldSize": "medium", "fieldBorderRadius": "0px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "rgba(255, 255, 255, 0.75)", "fieldBorderColor": "rgba(0, 0, 0, 0.15)", "fieldTextColor": "#625e53", "fieldMenuColor": "#e2e7e0", "labelSize": "medium", "labelColor": "#46443a", "labelSublabelColor": "#625e53", "labelErrorColor": "#46443a", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "0px", "buttonBorderColor": "#46443a", "buttonBackgroundColor": "#46443a", "buttonTextColor": "#ffffff", "pageBreakColor": "#46443a", "containerShadowSize": "none", "containerPadding": "60px", "containerBorderStyle": "solid", "containerBorderWidth": "1px", "containerBorderColor": "rgba(0, 0, 0, 0.15)", "containerBorderRadius": "0px", "backgroundImage": "stock", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#e2e7e0", "backgroundUrl": "kendall-scott-WFMswWMTa98-unsplash.jpg"}}, "flynn": {"name": "<PERSON>", "settings": {"fieldSize": "medium", "fieldBorderRadius": "0px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "#f0f0ff", "fieldBorderColor": "#4b49e0", "fieldTextColor": "#4b49e0", "fieldMenuColor": "#f0f0ff", "labelSize": "medium", "labelColor": "#4b49e0", "labelSublabelColor": "#4b49e0", "labelErrorColor": "#d80f0f", "buttonSize": "medium", "buttonBorderStyle": "solid", "buttonBorderSize": "1px", "buttonBorderRadius": "100px", "buttonBorderColor": "#4b49e0", "buttonBackgroundColor": "rgba( 0, 0, 0, 0 )", "buttonTextColor": "#4b49e0", "pageBreakColor": "#4b49e0", "containerShadowSize": "none", "containerPadding": "60px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "0px", "backgroundImage": "stock", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#f0f0ff", "backgroundUrl": "url()"}}, "fresh": {"name": "Fresh", "settings": {"fieldSize": "medium", "fieldBorderRadius": "6px", "fieldBorderStyle": "solid", "fieldBorderSize": "2px", "fieldBackgroundColor": "rgba(0, 0, 0, 0.06)", "fieldBorderColor": "rgba(0, 0, 0, 0.30)", "fieldTextColor": "#404040", "fieldMenuColor": "#d6d8d1", "labelSize": "medium", "labelColor": "#000000", "labelSublabelColor": "#404040", "labelErrorColor": "#a10000", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "6px", "buttonBorderColor": "#2da314", "buttonBackgroundColor": "#2da314", "buttonTextColor": "#ffffff", "pageBreakColor": "#2da314", "containerShadowSize": "large", "containerPadding": "60px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "10px", "backgroundImage": "stock", "backgroundPosition": "top right", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#d6d8d1", "backgroundUrl": "lukas-blazek-EWDvHNNfUmQ-unsplash.jpg"}}, "frost": {"name": "<PERSON>", "settings": {"fieldSize": "medium", "fieldBorderRadius": "8px", "fieldBorderStyle": "solid", "fieldBorderSize": "2px", "fieldBackgroundColor": "rgba(222, 240, 252, 0.6)", "fieldBorderColor": "#6e9fbe", "fieldTextColor": "#578bad", "fieldMenuColor": "#99c7e5", "labelSize": "medium", "labelColor": "#09598d", "labelSublabelColor": "#578bad", "labelErrorColor": "#af0000", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "2px", "buttonBorderRadius": "8px", "buttonBorderColor": "rgba(255, 255, 255, 0.30)", "buttonBackgroundColor": "#e03f79", "buttonTextColor": "#ffffff", "pageBreakColor": "#e03f79", "containerShadowSize": "large", "containerPadding": "60px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "20px", "backgroundImage": "stock", "backgroundPosition": "top center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#99c7e5", "backgroundUrl": "fabrizio-conti-aExT3y92x5o-unsplash.jpg"}}, "gloom": {"name": "Gloom", "settings": {"fieldSize": "medium", "fieldBorderRadius": "6px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "#252628", "fieldBorderColor": "#4a4b4f", "fieldTextColor": "rgba(255, 255, 255, .7)", "fieldMenuColor": "#1b1b1b", "labelSize": "medium", "labelColor": "#a9abad", "labelSublabelColor": "rgba(255, 255, 255, .7)", "labelErrorColor": "#ff6565", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "8px", "buttonBorderColor": "#258c60", "buttonBackgroundColor": "#258c60", "buttonTextColor": "#ffffff", "pageBreakColor": "#258c60", "containerShadowSize": "none", "containerPadding": "60px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "24px", "backgroundImage": "none", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#1b1b1b", "backgroundUrl": "url()"}}, "greenery": {"name": "Greenery", "settings": {"fieldSize": "medium", "fieldBorderRadius": "4px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "rgba(255, 255, 255, 0.95)", "fieldBorderColor": "rgba(0, 0, 0, 0.10)", "fieldTextColor": "rgba(38, 44, 30, 0.5)", "fieldMenuColor": "#f4f8fb", "labelSize": "medium", "labelColor": "#262c1e", "labelSublabelColor": "rgba(38, 44, 30, 0.5)", "labelErrorColor": "#d63637", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "4px", "buttonBorderColor": "#1e1f19", "buttonBackgroundColor": "#1e1f19", "buttonTextColor": "#ffffff", "pageBreakColor": "#1e1f19", "containerShadowSize": "large", "containerPadding": "60px", "containerBorderStyle": "solid", "containerBorderWidth": "10px", "containerBorderColor": "#ffffff", "containerBorderRadius": "30px", "backgroundImage": "stock", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#f4f8fb", "backgroundUrl": "annie-spratt-hX_hf2lPpUU-unsplash.jpg"}}, "hallway": {"name": "Hallway", "settings": {"fieldSize": "medium", "fieldBorderRadius": "0px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "#ffffff", "fieldBorderColor": "#cbcbcb", "fieldTextColor": "#000000", "fieldMenuColor": "#c0beb7", "labelSize": "medium", "labelColor": "#000000", "labelSublabelColor": "#000000", "labelErrorColor": "#d80f0f", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "0px", "buttonBorderColor": "#000000", "buttonBackgroundColor": "#000000", "buttonTextColor": "#ffffff", "pageBreakColor": "#000000", "containerShadowSize": "none", "containerPadding": "60px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "0px", "backgroundImage": "stock", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#c0beb7", "backgroundUrl": "joe-woods-4Zaq5xY5M_c-unsplash.jpg"}}, "harvest": {"name": "Harvest", "settings": {"fieldSize": "medium", "fieldBorderRadius": "6px", "fieldBorderStyle": "none", "fieldBorderSize": "1px", "fieldBackgroundColor": "rgba(255, 255, 255, 0.95)", "fieldBorderColor": "rgba( 0, 0, 0, 0.25 )", "fieldTextColor": "#481f10", "fieldMenuColor": "#f9d597", "labelSize": "medium", "labelColor": "#481f10", "labelSublabelColor": "#984628", "labelErrorColor": "#7d0000", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "6px", "buttonBorderColor": "#984628", "buttonBackgroundColor": "#984628", "buttonTextColor": "#ffffff", "pageBreakColor": "#984628", "containerShadowSize": "large", "containerPadding": "60px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "30px", "backgroundImage": "stock", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#f9d597", "backgroundUrl": "simon-berger-QihSgW300qY-unsplash.jpg"}}, "jungle": {"name": "Jungle", "settings": {"fieldSize": "medium", "fieldBorderRadius": "9px", "fieldBorderStyle": "none", "fieldBorderSize": "1px", "fieldBackgroundColor": "rgba(1, 16, 5, 0.7)", "fieldBorderColor": "rgba( 0, 0, 0, 0.25 )", "fieldTextColor": "rgba(255, 255, 255, 0.6)", "fieldMenuColor": "#1e2c2d", "labelSize": "medium", "labelColor": "rgba(255, 255, 255, 0.7)", "labelSublabelColor": "rgba(255, 255, 255, 0.6)", "labelErrorColor": "#ff6565", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "9px", "buttonBorderColor": "rgba(14, 143, 28, 0.5)", "buttonBackgroundColor": "rgba(14, 143, 28, 0.5)", "buttonTextColor": "#ffffff", "pageBreakColor": "rgba(14, 143, 28, 0.5)", "containerShadowSize": "large", "containerPadding": "60px", "containerBorderStyle": "solid", "containerBorderWidth": "10px", "containerBorderColor": "rgba(14, 143, 28, 0.3)", "containerBorderRadius": "24px", "backgroundImage": "stock", "backgroundPosition": "top left", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#1e2c2d", "backgroundUrl": "larm-rmah-5em1lVBmvw8-unsplash.jpg"}}, "lush": {"name": "<PERSON><PERSON>", "settings": {"fieldSize": "medium", "fieldBorderRadius": "6px", "fieldBorderStyle": "solid", "fieldBorderSize": "2px", "fieldBackgroundColor": "rgba(255, 255, 255, 0.95)", "fieldBorderColor": "rgba(0, 0, 0, 0.10)", "fieldTextColor": "rgba(49, 36, 37, 0.5)", "fieldMenuColor": "#f8e0e0", "labelSize": "medium", "labelColor": "#312425", "labelSublabelColor": "rgba(49, 36, 37, 0.5)", "labelErrorColor": "#962d3c", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "6px", "buttonBorderColor": "#962d3c", "buttonBackgroundColor": "#962d3c", "buttonTextColor": "#ffffff", "pageBreakColor": "#962d3c", "containerShadowSize": "large", "containerPadding": "60px", "containerBorderStyle": "solid", "containerBorderWidth": "10px", "containerBorderColor": "#ffffff", "containerBorderRadius": "30px", "backgroundImage": "stock", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#f8e0e0", "backgroundUrl": "annie-spratt-zA7I5BtFbvw-unsplash.jpg"}}, "manhattan": {"name": "Manhattan", "settings": {"fieldSize": "medium", "fieldBorderRadius": "0px", "fieldBorderStyle": "solid", "fieldBorderSize": "2px", "fieldBackgroundColor": "rgba(0, 0, 0, 0.5)", "fieldBorderColor": "rgba(255, 255, 255, 0.15)", "fieldTextColor": "rgba(255, 255, 255, 0.75)", "fieldMenuColor": "#0d0f0e", "labelSize": "medium", "labelColor": "#ffffff", "labelSublabelColor": "rgba(255, 255, 255, 0.75)", "labelErrorColor": "#ffffff", "buttonSize": "medium", "buttonBorderStyle": "solid", "buttonBorderSize": "2px", "buttonBorderRadius": "0px", "buttonBorderColor": "rgba(255, 255, 255, 0.35)", "buttonBackgroundColor": "rgba(0, 0, 0, 0.30)", "buttonTextColor": "#ffffff", "pageBreakColor": "rgba(255, 255, 255, 0.35)", "containerShadowSize": "none", "containerPadding": "60px", "containerBorderStyle": "solid", "containerBorderWidth": "2px", "containerBorderColor": "#070807", "containerBorderRadius": "0px", "backgroundImage": "stock", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#0d0f0e", "backgroundUrl": "and<PERSON><PERSON>-l<PERSON><PERSON>-g3z-CgUiPtg-unsplash.jpg"}}, "matrix": {"name": "Matrix", "settings": {"fieldSize": "medium", "fieldBorderRadius": "0px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "rgba( 0, 0, 0, 0 )", "fieldBorderColor": "#8ee8d6", "fieldTextColor": "#d8fff7", "fieldMenuColor": "#2e4141", "labelSize": "medium", "labelColor": "#d8fff7", "labelSublabelColor": "#d8fff7", "labelErrorColor": "#ffffff", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "0px", "buttonBorderColor": "#8ee8d6", "buttonBackgroundColor": "#8ee8d6", "buttonTextColor": "#111a12", "pageBreakColor": "#8ee8d6", "containerShadowSize": "none", "containerPadding": "60px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "0px", "backgroundImage": "stock", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#2e4141", "backgroundUrl": "annie-spratt-h-LcVG8W1XY-unsplash.jpg"}}, "monstera": {"name": "<PERSON><PERSON>", "settings": {"fieldSize": "medium", "fieldBorderRadius": "6px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "rgba(255, 255, 255, 0.80)", "fieldBorderColor": "#a3a9c2", "fieldTextColor": "#6a6a6a", "fieldMenuColor": "#e5e6ea", "labelSize": "medium", "labelColor": "#000000", "labelSublabelColor": "#6a6a6a", "labelErrorColor": "#d80f0f", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "5px", "buttonBorderColor": "#2f590f", "buttonBackgroundColor": "#2f590f", "buttonTextColor": "#ffffff", "pageBreakColor": "#2f590f", "containerShadowSize": "large", "containerPadding": "60px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "10px", "backgroundImage": "stock", "backgroundPosition": "top right", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#e5e6ea", "backgroundUrl": "invictus-tailoring-sneaker-socks-5OZgVj0YVwQ-unsplash.jpg"}}, "negative": {"name": "Negative", "settings": {"fieldSize": "medium", "fieldBorderRadius": "6px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "#161616", "fieldBorderColor": "#616265", "fieldTextColor": "#999999", "fieldMenuColor": "#161616", "labelSize": "medium", "labelColor": "#ffffff", "labelSublabelColor": "#999999", "labelErrorColor": "#ff6565", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "100px", "buttonBorderColor": "#ff4d24", "buttonBackgroundColor": "#ff4d24", "buttonTextColor": "#ffffff", "pageBreakColor": "#ff4d24", "containerShadowSize": "none", "containerPadding": "60px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "9px", "backgroundImage": "none", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#161616", "backgroundUrl": "url()"}}, "palm": {"name": "Palm", "settings": {"fieldSize": "medium", "fieldBorderRadius": "0px", "fieldBorderStyle": "solid", "fieldBorderSize": "2px", "fieldBackgroundColor": "rgba(0, 0, 0, 0.07)", "fieldBorderColor": "#636363", "fieldTextColor": "#000000", "fieldMenuColor": "#e7e7e5", "labelSize": "medium", "labelColor": "#000000", "labelSublabelColor": "#6a6a6a", "labelErrorColor": "#a10000", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "0px", "buttonBorderColor": "#000000", "buttonBackgroundColor": "#000000", "buttonTextColor": "#ffffff", "pageBreakColor": "#000000", "containerShadowSize": "large", "containerPadding": "60px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "0px", "backgroundImage": "stock", "backgroundPosition": "top left", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#e7e7e5", "backgroundUrl": "augustine-wong-oebTM5wHTfw-unsplash.jpg"}}, "plaster": {"name": "<PERSON><PERSON><PERSON>", "settings": {"fieldSize": "medium", "fieldBorderRadius": "0px", "fieldBorderStyle": "solid", "fieldBorderSize": "2px", "fieldBackgroundColor": "rgba(255, 255, 255, 0.75)", "fieldBorderColor": "#afafaf", "fieldTextColor": "#494848", "fieldMenuColor": "#f1f6fa", "labelSize": "medium", "labelColor": "#494848", "labelSublabelColor": "#afafaf", "labelErrorColor": "#d63637", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "5px", "buttonBorderColor": "#494848", "buttonBackgroundColor": "#494848", "buttonTextColor": "#ffffff", "pageBreakColor": "#494848", "containerShadowSize": "medium", "containerPadding": "60px", "containerBorderStyle": "solid", "containerBorderWidth": "2px", "containerBorderColor": "#afafaf", "containerBorderRadius": "0px", "backgroundImage": "stock", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#f1f6fa", "backgroundUrl": "joan<PERSON>-<PERSON><PERSON><PERSON><PERSON>-mjC9apK53a8-unsplash.jpg"}}, "range": {"name": "Range", "settings": {"fieldSize": "medium", "fieldBorderRadius": "22px", "fieldBorderStyle": "solid", "fieldBorderSize": "3px", "fieldBackgroundColor": "rgba(255, 255, 255, 0.20)", "fieldBorderColor": "rgba(61, 0, 0, 0.15)", "fieldTextColor": "#000000", "fieldMenuColor": "#f9dcd8", "labelSize": "medium", "labelColor": "#000000", "labelSublabelColor": "#6a6a6a", "labelErrorColor": "#e80000", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "50px", "buttonBorderColor": "#ae0120", "buttonBackgroundColor": "#ae0120", "buttonTextColor": "#ffffff", "pageBreakColor": "#ae0120", "containerShadowSize": "large", "containerPadding": "60px", "containerBorderStyle": "solid", "containerBorderWidth": "10px", "containerBorderColor": "rgba(253, 237, 237, 0.70)", "containerBorderRadius": "35px", "backgroundImage": "stock", "backgroundPosition": "bottom center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#f9dcd8", "backgroundUrl": "eber<PERSON>-grossgasteiger-jCL98LGaeoE-unsplash.jpg"}}, "rustic": {"name": "Rustic", "settings": {"fieldSize": "medium", "fieldBorderRadius": "0px", "fieldBorderStyle": "none", "fieldBorderSize": "1px", "fieldBackgroundColor": "rgba(0, 0, 0, 0.5)", "fieldBorderColor": "rgba( 0, 0, 0, 0.25 )", "fieldTextColor": "rgba(255, 255, 255, 0.75)", "fieldMenuColor": "#716955", "labelSize": "medium", "labelColor": "#ffffff", "labelSublabelColor": "rgba(255, 255, 255, 0.75)", "labelErrorColor": "#ffffff", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "0px", "buttonBorderColor": "rgba(0, 0, 0, 0.5)", "buttonBackgroundColor": "rgba(0, 0, 0, 0.5)", "buttonTextColor": "#ffffff", "pageBreakColor": "rgba(0, 0, 0, 0.5)", "containerShadowSize": "none", "containerPadding": "60px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "0px", "backgroundImage": "stock", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#716955", "backgroundUrl": "tim-mossholder-q093cNh_4Q0-unsplash.jpg"}}, "scrap": {"name": "Scrap", "settings": {"fieldSize": "medium", "fieldBorderRadius": "0px", "fieldBorderStyle": "none", "fieldBorderSize": "1px", "fieldBackgroundColor": "#ffffff", "fieldBorderColor": "rgba( 0, 0, 0, 0.25 )", "fieldTextColor": "#2f3133", "fieldMenuColor": "#d3d3d3", "labelSize": "medium", "labelColor": "#2f3133", "labelSublabelColor": "#2f3133", "labelErrorColor": "#d63637", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "0px", "buttonBorderColor": "#2f3133", "buttonBackgroundColor": "#2f3133", "buttonTextColor": "#ffffff", "pageBreakColor": "#2f3133", "containerShadowSize": "medium", "containerPadding": "60px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "0px", "backgroundImage": "stock", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#d3d3d3", "backgroundUrl": "marjan-blan-_kUxT8WkoeY-unsplash.jpg"}}, "solitude": {"name": "Solitude", "settings": {"fieldSize": "medium", "fieldBorderRadius": "0px", "fieldBorderStyle": "none", "fieldBorderSize": "1px", "fieldBackgroundColor": "rgba(14, 15, 50, 0.10)", "fieldBorderColor": "rgba( 0, 0, 0, 0.25 )", "fieldTextColor": "#0e0f32", "fieldMenuColor": "#e1e5ee", "labelSize": "medium", "labelColor": "#0e0f32", "labelSublabelColor": "#64647f", "labelErrorColor": "#de0000", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "0px", "buttonBorderColor": "#0e0f32", "buttonBackgroundColor": "#0e0f32", "buttonTextColor": "#ffffff", "pageBreakColor": "#0e0f32", "containerShadowSize": "none", "containerPadding": "60px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "0px", "backgroundImage": "stock", "backgroundPosition": "bottom center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#e1e5ee", "backgroundUrl": "jean-p<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>-75xPHEQBmvA-unsplash.jpg"}}, "sonic": {"name": "Sonic", "settings": {"fieldSize": "medium", "fieldBorderRadius": "22px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "#ffffff", "fieldBorderColor": "#073eff", "fieldTextColor": "#666666", "fieldMenuColor": "#ffffff", "labelSize": "medium", "labelColor": "#666666", "labelSublabelColor": "#666666", "labelErrorColor": "#da0000", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "100px", "buttonBorderColor": "#073eff", "buttonBackgroundColor": "#073eff", "buttonTextColor": "#ffffff", "pageBreakColor": "#073eff", "containerShadowSize": "large", "containerPadding": "60px", "containerBorderStyle": "solid", "containerBorderWidth": "1px", "containerBorderColor": "#f3f3f3", "containerBorderRadius": "50px", "backgroundImage": "none", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#ffffff", "backgroundUrl": "url()"}}, "spring": {"name": "Spring", "settings": {"fieldSize": "medium", "fieldBorderRadius": "6px", "fieldBorderStyle": "none", "fieldBorderSize": "0px", "fieldBackgroundColor": "rgba(255, 255, 255, 0.75)", "fieldBorderColor": "rgba( 0, 0, 0, 0.25 )", "fieldTextColor": "#606775", "fieldMenuColor": "#d9e5f4", "labelSize": "medium", "labelColor": "#373D4A", "labelSublabelColor": "#606775", "labelErrorColor": "#D63637", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "0px", "buttonBorderRadius": "6px", "buttonBorderColor": "rgba( 0, 0, 0, 0.25 )", "buttonBackgroundColor": "#366CE3", "buttonTextColor": "#ffffff", "pageBreakColor": "#366CE3", "containerShadowSize": "large", "containerPadding": "60px", "containerBorderStyle": "none", "containerBorderWidth": "0px", "containerBorderColor": "rgba( 0, 0, 0, 0.25 )", "containerBorderRadius": "0px", "backgroundImage": "stock", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#d9e5f4", "backgroundUrl": "artiom-vallat-ZYhQXXGxvtQ-unsplash.jpg"}}, "tidal": {"name": "Tidal", "settings": {"fieldSize": "medium", "fieldBorderRadius": "6px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "rgba(0, 2, 1, 0.8)", "fieldBorderColor": "#474e54", "fieldTextColor": "#ffffff", "fieldMenuColor": "#060807", "labelSize": "medium", "labelColor": "#ffffff", "labelSublabelColor": "#a7b3be", "labelErrorColor": "#ffffff", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "6px", "buttonBorderColor": "#474e54", "buttonBackgroundColor": "#474e54", "buttonTextColor": "#ffffff", "pageBreakColor": "#474e54", "containerShadowSize": "none", "containerPadding": "60px", "containerBorderStyle": "solid", "containerBorderWidth": "10px", "containerBorderColor": "#000201", "containerBorderRadius": "30px", "backgroundImage": "stock", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#060807", "backgroundUrl": "mike-<PERSON><PERSON><PERSON><PERSON>-wfh8dDlNFOk-unsplash.jpg"}}, "tranquil": {"name": "Tranquil", "settings": {"fieldSize": "medium", "fieldBorderRadius": "6px", "fieldBorderStyle": "none", "fieldBorderSize": "1px", "fieldBackgroundColor": "rgba(255, 255, 255, 0.75)", "fieldBorderColor": "rgba( 0, 0, 0, 0.25 )", "fieldTextColor": "#606775", "fieldMenuColor": "#ffffff", "labelSize": "medium", "labelColor": "#373d4a", "labelSublabelColor": "#606775", "labelErrorColor": "#d63637", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "6px", "buttonBorderColor": "#366ce3", "buttonBackgroundColor": "#366ce3", "buttonTextColor": "#ffffff", "pageBreakColor": "#366ce3", "containerShadowSize": "large", "containerPadding": "60px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "0px", "backgroundImage": "stock", "backgroundPosition": "bottom center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#ffffff", "backgroundUrl": "artiom-vallat-ZYhQXXGxvtQ-unsplash.jpg"}}, "vintage": {"name": "Vintage", "settings": {"fieldSize": "medium", "fieldBorderRadius": "0px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "#e7e7dd", "fieldBorderColor": "#bababa", "fieldTextColor": "#1a1a1a", "fieldMenuColor": "#e7e7dd", "labelSize": "medium", "labelColor": "#1a1a1a", "labelSublabelColor": "#4d4d4d", "labelErrorColor": "#af0000", "buttonSize": "medium", "buttonBorderStyle": "none", "buttonBorderSize": "1px", "buttonBorderRadius": "0px", "buttonBorderColor": "#f2cd52", "buttonBackgroundColor": "#f2cd52", "buttonTextColor": "#1a1a1a", "pageBreakColor": "#f2cd52", "containerShadowSize": "none", "containerPadding": "60px", "containerBorderStyle": "solid", "containerBorderWidth": "2px", "containerBorderColor": "#000000", "containerBorderRadius": "0px", "backgroundImage": "none", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#e7e7dd", "backgroundUrl": "url()"}}, "western": {"name": "Western", "settings": {"fieldSize": "medium", "fieldBorderRadius": "0px", "fieldBorderStyle": "solid", "fieldBorderSize": "1px", "fieldBackgroundColor": "rgba(255, 255, 255, 0.05)", "fieldBorderColor": "#e0b495", "fieldTextColor": "#ffffff", "fieldMenuColor": "#764320", "labelSize": "medium", "labelColor": "#ffffff", "labelSublabelColor": "#e0b495", "labelErrorColor": "#ffffff", "buttonSize": "medium", "buttonBorderStyle": "solid", "buttonBorderSize": "1px", "buttonBorderRadius": "0px", "buttonBorderColor": "#e0b495", "buttonBackgroundColor": "rgba(255, 255, 255, 0.05)", "buttonTextColor": "#e0b495", "pageBreakColor": "#e0b495", "containerShadowSize": "none", "containerPadding": "60px", "containerBorderStyle": "none", "containerBorderWidth": "1px", "containerBorderColor": "#000000", "containerBorderRadius": "0px", "backgroundImage": "stock", "backgroundPosition": "center center", "backgroundRepeat": "no-repeat", "backgroundSizeMode": "cover", "backgroundSize": "cover", "backgroundWidth": "100px", "backgroundHeight": "100px", "backgroundColor": "#764320", "backgroundUrl": "felipe-santana-xJkTCbtuqAY-unsplash.jpg"}}}