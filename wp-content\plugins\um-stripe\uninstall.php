<?php
/**
 * Uninstall
 *
 * @package none
 */

if ( ! defined( 'WP_UNINSTALL_PLUGIN' ) ) {
	exit;
}

if ( ! defined( 'UM_STRIPE_PATH' ) ) {
	define( 'UM_STRIPE_PATH', plugin_dir_path( __FILE__ ) );
}

if ( ! defined( 'UM_STRIPE_URL' ) ) {
	define( 'UM_STRIPE_URL', plugin_dir_url( __FILE__ ) );
}

if ( ! defined( 'UM_STRIPE_PLUGIN' ) ) {
	define( 'UM_STRIPE_PLUGIN', plugin_basename( __FILE__ ) );
}

$options = get_option( 'um_options', array() );

if ( ! empty( $options['uninstall_on_delete'] ) ) {
	if ( ! class_exists( 'um_ext\um_stripe\Install' ) ) {
		require_once UM_STRIPE_PATH . 'class-install.php';
	}

	$install_class = UM()->call_class( 'um_ext\um_stripe\Install' );

	// Remove settings.
	$options = get_option( 'um_options', array() );
	foreach ( $install_class->settings_defaults as $k => $v ) {
		unset( $options[ $k ] );
	}
	update_option( 'um_options', $options );

	unset( $options['um_stripe_license_key'] );

	// Remove predefined page option value.
	UM()->options()->remove( 'core_payment-successful' );
	UM()->options()->remove( 'core_payment-cancelled' );
	UM()->options()->remove( 'core_already-subscribed' );
	UM()->options()->remove( 'core_stripe' );
	UM()->options()->remove( 'core_checkout' );
	UM()->options()->remove( 'core_customer-portal' );

	// Remove custom templates.
	$templates_directories = array(
		trailingslashit( get_stylesheet_directory() ) . 'ultimate-member/stripe/',
		trailingslashit( get_template_directory() ) . 'ultimate-member/stripe/',
	);
	foreach ( $templates_directories as $templates_dir ) {
		if ( is_dir( $templates_dir ) ) {
			UM()->common()->filesystem()::remove_dir( $templates_dir );
		}
	}

	// Drop activity logs & subscriptions database tables.
	if ( ! class_exists( 'um_ext\um_stripe\db\Init' ) ) {
		require_once UM_STRIPE_PATH . 'includes/db/class-init.php';
	}

	/**
	 * Autoloader. We need it being separate and not using Composer autoloader because of the Stripe libs,
	 * which are huge and not needed for most users.
	 * Inspired by PSR-4 examples: https://github.com/php-fig/fig-standards/blob/master/accepted/PSR-4-autoloader-examples.md
	 *
	 * @since 1.0.0
	 *
	 * @param string $class The fully-qualified class name.
	 */
	$psr4_namespaces = require_once UM_STRIPE_PATH . '/vendor/composer/autoload_psr4.php';

	$psr4_keys = array_keys( $psr4_namespaces );

	spl_autoload_register(
		/**
		 * Closure of the autoloader.
		 *
		 * @param string $class_name The fully-qualified class name.
		 * @return void
		 */
		static function ( $class_name = '' ) use ( $psr4_keys, $psr4_namespaces ) {
			// Project namespace & length.
			$root_namespace    = 'UM_Stripe\\Vendor\\';
			$project_namespace = 'UM_Stripe\\Vendor\\';
			$length            = strlen( $project_namespace );
			$vendor_dir        = '';
			// Bail if class is not in this namespace.
			if ( 0 !== strncmp( $project_namespace, $class_name, $length ) ) {
				return;
			}
			$found_key = '';

			foreach ( $psr4_keys as $i => $ns ) {
				if ( empty( $class_name ) || empty( $ns ) ) {
					continue;
				}
				$class_name = str_replace( 'Symfony\\Component\\Intl\\', 'Symfony\\Intl\\', $class_name );
				$ns         = str_replace( 'Symfony\\Component\\Intl\\', 'Symfony\\Intl\\', $ns );

				if ( strpos( strtolower( $class_name ), strtolower( $ns ) ) > -1 && ! empty( $psr4_namespaces[ $psr4_keys[ $i ] ][0] ) ) {

					$found_key  = $ns;
					$vendor_dir = str_replace( $found_key, '', $psr4_namespaces[ $psr4_keys[ $i ] ][0] );

					$class_name = str_replace( 'Symfony\\Intl\\', '', $class_name );

					$name = str_replace( str_replace( '\\', '', $found_key ) . '/', '\\', str_replace( '\\', \DIRECTORY_SEPARATOR, str_replace( $root_namespace, '', $class_name ) ) );
					// Setup file parts.
					$format = $vendor_dir . '\\%1$s.php';

					// Parse class and namespace to file.
					$file = wp_normalize_path( sprintf( $format, $name ) );
					$file = str_replace( '/', DIRECTORY_SEPARATOR, $file );
					$file = str_replace( $found_key, '', $file );
					// Bail if file does not exist.
					if ( file_exists( $file ) ) {
						require_once $file;
						break;
					}
				}
			}
		}
	);


	$berlindb_class = UM()->call_class( 'um_ext\um_stripe\db\Init' );
	$logs_table     = $berlindb_class->logs()->table();
	if ( $logs_table->exists() ) {
		$logs_table->uninstall();
	}

	$subscriptions_table = $berlindb_class->subscriptions()->table();
	if ( $subscriptions_table->exists() ) {
		$subscriptions_table->uninstall();
	}

	delete_option( 'um_stripe_last_version_upgrade' );
	delete_option( 'um_stripe_version' );
}
