jQuery(document).ready( function($) {

	/* <PERSON><PERSON> "Delete my all albums & photos" */
	$(document.body).on('click', '#um_user_photos_delete_all', function (e) {
		e.preventDefault();
		let btn = $(this);
		if (btn.hasClass('inactive')) {
			return;
		}

		let nonce    = btn.data( 'nonce' );
		let text = btn.data('alert_message');

		if (confirm(text)) {
			btn.addClass('inactive').text( wp.i18n.__( 'Processing...Please wait.', 'um-user-photos' ) );

			wp.ajax.send(
				'um_user_photos_flush_albums',
				{
					data: {
						_wpnonce: nonce,
					},
					success: function (response) {
						btn.parent( 'p' ).append( '<font style="color:green;display:block;text-align:center;">Deleted Successfully !</font>' );
						btn.remove();
						$( '#um_user_photos_download_all' ).remove();
					},
					error: function( data ) {
						console.log( data );
					}
				}
			);
		}
	});
});
