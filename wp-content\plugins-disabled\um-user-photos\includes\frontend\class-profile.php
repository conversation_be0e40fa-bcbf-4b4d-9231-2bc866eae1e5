<?php
namespace um_ext\um_user_photos\frontend;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Profile
 *
 * @package um_ext\um_user_photos\frontend
 */
class Profile {

	/**
	 * Profile constructor.
	 */
	public function __construct() {
		if ( defined( 'UM_DEV_MODE' ) && UM_DEV_MODE && UM()->options()->get( 'enable_new_ui' ) ) {
			// default tab is handled in UM core
		} else {
			add_action( 'um_profile_content_photos_default', array( $this, 'get_gallery_content' ) );
		}
		add_action( 'um_profile_content_photos_albums', array( $this, 'get_gallery_content' ) );

		add_action( 'um_profile_content_photos_photo', array( $this, 'get_gallery_photos_content' ) );

		add_filter( 'um_user_profile_subnav_link', array( $this, 'um_user_profile_subnav_link' ), 20 );
	}

	/**
	 * Galleries Content
	 */
	public function get_gallery_content() {
		if ( defined( 'UM_DEV_MODE' ) && UM_DEV_MODE && UM()->options()->get( 'enable_new_ui' ) ) {
			UM()->User_Photos()->common()->shortcodes()->is_profile = true;
			$content = apply_shortcodes( '[ultimatemember_gallery user_id="' . um_user( 'ID' ) . '" /]' );
			echo wp_kses( $content, UM()->get_allowed_html( 'templates' ) );
			UM()->User_Photos()->common()->shortcodes()->is_profile = false;
		} else {
			$content = apply_shortcodes( '[ultimatemember_gallery user_id="' . um_user( 'ID' ) . '" /]' );
			echo wp_kses( $content, UM()->get_allowed_html( 'templates' ) );
		}
	}

	/**
	 * Gallery Content
	 */
	public function get_gallery_photos_content() {
		if ( defined( 'UM_DEV_MODE' ) && UM_DEV_MODE && UM()->options()->get( 'enable_new_ui' ) ) {
			UM()->User_Photos()->common()->shortcodes()->is_profile = true;
			$content = apply_shortcodes( '[ultimatemember_gallery_photos user_id="' . um_user( 'ID' ) . '" /]' );
			echo wp_kses( $content, UM()->get_allowed_html( 'templates' ) );
			UM()->User_Photos()->common()->shortcodes()->is_profile = false;
		} else {
			$content = apply_shortcodes( '[ultimatemember_gallery_photos user_id="' . um_user( 'ID' ) . '" /]' );
			echo wp_kses( $content, UM()->get_allowed_html( 'templates' ) );
		}
	}

	/**
	 * Change profile subtab.
	 *
	 * @param string $subnav_link
	 *
	 * @return string
	 */
	public function um_user_profile_subnav_link( $subnav_link ) {
		$subnav_link = remove_query_arg( 'album_id', $subnav_link );
		$subnav_link = remove_query_arg( 'photo_id', $subnav_link );

		return $subnav_link;
	}

}
