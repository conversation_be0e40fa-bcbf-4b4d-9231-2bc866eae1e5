=== Ultimate Member - Stripe ===
Author URI: https://ultimatemember.com/
Plugin URI: https://ultimatemember.com/extensions/stripe/
Contributors: ultimatemember, champsupertramp, nsinelnikov
Tags: stripe, networks, community, discussion
Requires at least: 6.2
Tested up to: 6.8
Stable tag: 1.4.11
License: GPLv3
License URI: http://www.gnu.org/licenses/gpl-3.0.txt
Requires UM core at least: 2.10.3

== Description ==

This extension allows you to integrate the Stripe Billing & Checkout with Ultimate Member.

= Key Features: =

*  Create multiple subscription plans for customers to subscribe to.
*  Supports One-time & Recurring Plans.
*  Predefined field to display subscription plans on the Register forms via Form Builder.
*  Available shortcodes to display individual subscription button & a link to the Stripe customer portal.
*  Assign a role to each subscription status via UM Stripe shortcode settings.
*  Supported Stripe subscription statuses: Active, Canceled, Past Due, Unpaid, Pause & Trialing
*  Some action & filters hook and functions are available for customization/integration.

= Documentation & Support =

Got a problem or need help with Ultimate Member? Head over to our [documentation](https://docs.ultimatemember.com/article/1606-introduction-to-ultimate-members-stripe-extension) and perform a search of the knowledge base. If you can’t find a solution to your issue then you can create a topic on the [support forum](https://wordpress.org/support/plugin/ultimate-member).

== Installation ==

1. Activate the plugin
2. That's it. Go to Ultimate Member > Settings > Extensions > Stripe to customize plugin options
3. For more details, please visit the official [Documentation](https://docs.ultimatemember.com/article/1606-introduction-to-ultimate-members-stripe-extension) page.

== Changelog ==

= 1.4.11: April 24, 2025 =

  * Fixed: Displaying button in Billing tab
  * Fixed: Fatal error when switching plans
  * Fixed: Date timezone in Invoice, Subscribed and Logs templates
  * Fixed: Datetime in invoice table
  * Fixed: Checkout process with payment button
  * Tweak: Update vendors

 **Templates required update**

  * checkout/order-details.php

= 1.4.6: February 04, 2025 =

  * Added: PHP session to store Stripe Checkout Session ID for order details in other pages. You may access it with `$_SESSION['UM_STRIPE_CHECKOUT_SID']` and then access the order details with `UM()->Stripe_API()->common()->get_order_details( $sid );`
  * Added: New filter hook [um_stripe_filter__is_subscribed_query](https://ultimatemember.github.io/docs-v3/um-stripe/developer/hooks/filters/um_stripe_filter__is_subscribed_query.html) to modify is_subscribed() query
  * Fixed: Missing subscription when updating profile in WP Admin > Users
  * Fixed: Fatal error with subscription
  * Fixed: Add 'payment_intent.succeeded' Stripe event type to fix one-time payment status
  * Fixed: Fatal error when updating profile via WP Admin > Users
  * Fixed: Displaying button in Account form > Billing tab
  * Fixed: Fatal error when switching plans

= 1.4.4: December 20, 2024 =

  * Added: New filter hook [um_stripe_api_version](https://ultimatemember.github.io/docs-v3/um-stripe/developer/hooks/filters/um_stripe_api_version.html) to modify the Stripe API version:
  * Added: New action hook to capture expired checkout session: [umm_stripe_event_checkout_session_expired](https://ultimatemember.github.io/docs-v3/um-stripe/developer/hooks/actions/umm_stripe_event_checkout_session_expired.html)
  * Added: New filter hook [um_stripe_filter__is_subscribed_query](https://ultimatemember.github.io/docs-v3/um-stripe/developer/hooks/filters/um_stripe_filter__is_subscribed_query.html) to modify is_subscribed() method.
  * Added: PHP session to store Stripe Checkout Session ID for order details in other non-order details pages.
  * Added: [payment_intent.succeeded](https://docs.stripe.com/api/events/types#event_types-payment_intent.succeeded) Stripe event type to fix one-time payment status.
  * Fixed: Invalid checkout session resulting to generating random usernames
  * Fixed: Undefined JS function
  * Fixed: Error notices due to unused PHP variables
  * Fixed: Errors in Stripe API request
  * Fixed: Remove Save Changes button when payment collection method is disabled
  * Fixed: Welcome email to set password or not depending on the password field visibility in register form
  * Fixed: Missing subscription when updating profile in WP Admin > Users
  * Fixed: Fatal error when updating profile via WP Admin > Users

= 1.4.0: November 14, 2024 =

  * Added: Delay to capture the checkout session event
  * Fixed: Invalid checkout session resulting to generating random usernames
  * Fixed: "Load textdomain just in time" issue

= 1.3.7: October 17, 2024 =

  * Tweaked: Redirection after account creation
  * Added: New option to change payment collection method for active subscription
  * Fixed: Exclude Webhook Endpoint URL from Ultimate Member Core's restriction functions
  * Fixed: Cancel at Period Date in Stripe settings
  * Fixed: Re-assignment of roles and plans

**Templates required update**

  * account/billings.php

= 1.3.5: August 29, 2024 =

  * Fixed: Webhook Creation errors
  * Fixed: Uninstall feature
  * Fixed: Exclude Webhook Endpoint URL from Ultimate Member Core's restriction functions

= 1.3.1: August 06, 2024 =

  * Tweaked: Improve text labels and descriptions in Stripe Plan settings for clarity.
  * Added: Stripe Plan option to override subscription cancellation period e.g. cancel_at_period_end, immediately & custom.
  * Added: Stripe Plan option to allow users to switch to another Plans. See our [documentation](https://docs.ultimatemember.com/article/1929-enabling-customers-to-switch-plans-via-stripe-portal)
  * Added: An option to display tax on the Stripe checkout form. See our [documentation](https://docs.ultimatemember.com/article/1892-stripe-plan-settings)
  * Fixed: Return URL in Customer Portal. This returns you back to the previous page of your site where the Customer Portal button is added.
  * Fixed: Error notices in Stripe Plan settings.
  * Fixed: Webhook Creation errors
  * Fixed: Merging of Roles when a user subscribes to a plan
  * Fixed: Date-time in the Activity and Payment Logs tables
  * Fixed: Display Plan(Grouped) Title in Users Info modal and New User Email notification for Admins

= 1.2.1: May 23, 2024 =

  * Fixed: Fatal errors caused by PHP autoloader on plugin update/activation

= 1.2.0: May 22, 2024 =

  * Tweaked: New FontAwesome library. Version 6.5.2
  * Added: Conditional Stripe Plan fields [Documentation](https://docs.ultimatemember.com/article/1932-stripe-conditional-fields-visibility)
  * Added: Stripe Logs interfaces added in WP Admin > Tools > Stripe Activity Logs & Stripe Payment Logs
  * Fixed: Autoloader compatibility issue with Windows OS servers
  * Fixed: String translation of prices in Register forms
  * Fixed: Fatal error with delete user with mismatch API mode
  * Fixed: Error notices in WP Admin > Stripe Plans
  * Fixed: Wrong documentation URL in shortcode reference in Stripe Plan Settings
  * Fixed: Styles issues in Stripe Plan Settings

= 1.1.0: April 17, 2024 =

  * Tweaked: Remove card as default payment methods type to allow stripe to use its global settings
  * Tweaked: Billings tab's default button style
  * Added: Billing tab to the account form
  * Added: class method to retrieve the active subscriptions of a user `UM()->Stripe_API()->common()->subscription()->get_active_subscriptions()`
  * Added: Direct Switch Stripe Plan URL in the Stripe Plan settings under Subscribe URL widget
  * Added: An option that allows customers to switch Prices via Customer Portal added in the Stripe Plan setting
  * Added: Direct permalink URL for customer portal
  * Added: Shortcode to display message when a user subscription is active or inactive
  * Added: Shortcode to Restrict Content by Plans & Prices
  * Added: Disallow logged-out user to view the custom portal predefined page
  * Added: Alert warning when stripe keys are not set on clicking the checkout buttons
  * Added: Show Plan title in Review Registration Details modal
  * Added: Admin web notification for newly submitted registration
  * Added: Create new WP account on Stripe customer creation via stripe.com
  * Fixed: Viewing customer portal page giving critial error
  * Fixed: Issue with shortcodes for Elementor and other page builder loading content via ajax
  * Fixed: Showing of overlay on successful payment
  * Fixed: Update username on successful payment
  * Fixed: Populate names from Stripe Customer name when first and last name in UM fields are not set
  * Fixed: Order details style in firefox browser
  * Fixed: Auto approve for first-time users registered via Stripe button shortcode
  * Fixed: Embedded checkout shortcode
  * Fixed: Webhook response that caused registration process issue
  * Fixed: Create user on Stripe customer creation via Stripe.com

  **Templates required update**

    * checkout/order-details.php

  **Cached and optimized/minified assets(JS/CSS) must be flushed/re-generated after upgrade**

= 1.0.6-beta.7: December 20, 2023 =
  * Added: Promotion Code option in the Stripe Plan setting that enables the Promotion Code field in Stripe Checkout form.
  * Tweak: Make date & time displayed in Checkout Details on Payment Success page translation-ready.
           Updated template: `/um-stripe/templates/checkout/order-details.php
  * Fixed: Checkout process with Direct Checkout URL
  * Fixed: Database tables constraint that prevents from deleting a user

= 1.0.5-beta.6: December 11, 2023 =

  * Tweak: wp-admin scripts refactored
    * Tweak: Added CPT as UM registered post type for define proper UM screen on wp-admin
    * Tweak: `UM.admin.tooltip.init()` function using for admin scripts
  * Tweak: `um-admin-clear` CSS class. It duplicates WordPress native `clear`. Using WordPress native instead.
  * Tweak: Enhancements related to WPCS (using UM_URL).

= 1.0.4-beta.5: November 22, 2023 =

  * Fixed: Issue with maximum characters with Stripe metadata. We've removed the function that saves submitted data to the checkout session. The data is now stored in the transient of your site which expires automatically after an hour.
  * Fixed: Account creation via Subscribe URL with non-logged-in users.

= 1.0.3-beta.4: November 17, 2023 =

  * Added: Show Plan/Product name ( a member subscribed to ) in the WP Admin > Users List.
  * Fixed: Conflict issue with field validation errors displayed in Register forms.
  * Fixed: Invalid type error for checkboxes and multi-select fields on checkout process.

= 1.0.2-beta.3: November 9, 2023 =

  * Added: Embedded Checkout - You can now set the checkout form on your site. Please see the [settings doc](https://ultimatemember.github.io/docs-v3/um-stripe/article/1886-stripe-settings.html#stripe-settings-1).
  * Added: Restrict Content by Stripe Prices. Please see [the updated doc](https://ultimatemember.github.io/docs-v3/um-stripe/article/1805-stripe-content-restriction.html).
  * Added: New filter hook `umm_stripe_checkout_details_date_format` for modifying date format in checkout details.
  * Fixed: Remove decimal for supported Zero-decimal currencies: https://stripe.com/docs/currencies#zero-decimal
  * Fixed: Subscription database table constraint with *_users table that prevents user account from deletion.
  * Fixed: Validate Publishable Key in the Extension settings.

  * Important: Please re-import prices in the UM > Settings > Extensions > Stripe settings to fix the issue with zero-decimal currencies. You must manually update the Stripe title; the new imported price should have the correct price decimals.

= 1.0.1-beta.2: October 18, 2023 =

  * Fixed: Incorrect currency and amount formatting displayed on Register forms & Order details.
  * Fixed: Assigning of role and status after successful Stripe Checkout.
  * Dev: Fixed the beta version's incrementation.

= 1.0.0-beta.1: October 11, 2023 =

  * Officially released the Beta version - Documentation at https://ultimatemember.github.io/docs-v3/um-stripe/
