"use strict";var WPFormsEditEntry=window.WPFormsEditEntry||function(e,i,o){var f={$editForm:o("#wpforms-edit-entry-form"),$submitButton:o("#wpforms-edit-entry-update")},r={},n={init:function(){o(n.ready)},ready:function(){r.nonce=f.$editForm.find('input[name="nonce"]').val(),r.entryId=f.$editForm.find('input[name="wpforms[entry_id]"]').val(),n.initSavedFormData(),n.events(),wpf.initTooltips()},events:function(){f.$submitButton.on("click",n.clickUpdateButton),f.$editForm.on("wpformsAjaxBeforeSubmit",n.validateFields).on("wpformsAjaxSubmitFailed",n.submitFailed).on("wpformsAjaxSubmitSuccess",n.submitSuccess),o(i).on("beforeunload",n.beforeUnload),o(e).on("click",".wpforms-edit-entry-field-file-upload .delete",n.fileDelete)},initSavedFormData:function(){r.savedFormData=f.$editForm.serialize()},beforeUnload:function(e){if(f.$editForm.serialize()!==r.savedFormData)return e.returnValue="Leave site?",e.returnValue},clickUpdateButton:function(e){e.preventDefault(),f.$submitButton.prop("disabled",!0),n.preSubmitActions(),n.hideErrors(),f.$editForm.append(o("<input>",{type:"hidden",name:"_wp_http_referer",value:wpf.updateQueryString("_wp_http_referer",null)})),wpforms.formSubmitAjax(f.$editForm)},preSubmitActions:function(){var r=o("#wpforms-edit-entry-form").data("formid");o(".wpforms-smart-phone-field").trigger("input"),o(".wpforms-edit-entry-field-file-upload a.disabled").each(function(){o(this).parent().remove()}),o(".wpforms-field-file-upload").each(function(){var e=o(this);e.is(":empty")&&(e.closest(".wpforms-edit-entry-field-file-upload").addClass("empty"),e.html(o("<span>",{class:"wpforms-entry-field-value",text:wpforms_admin_edit_entry.strings.entry_empty_file})))}),o(".wpforms-field-richtext").each(function(){var e=o(this).data("field-id"),e=tinyMCE.get("wpforms-"+r+"-field_"+e);e&&e.save()})},submitFailed:function(e,r){n.displayErrors(r),o.alert({title:wpforms_admin.heads_up,content:r.data.errors.general,icon:"fa fa-info-circle",type:"orange",buttons:{confirm:{text:wpforms_admin_edit_entry.strings.continue_editing,btnClass:"btn-confirm",keys:["enter"]},cancel:{text:wpforms_admin_edit_entry.strings.view_entry,action:function(){i.location.href=wpforms_admin_edit_entry.strings.view_entry_url}}}})},submitSuccess:function(e,r){n.initSavedFormData(),void 0!==r.data&&(o("#wpforms-entry-details .wpforms-entry-modified .date-time").text(r.data.modified),o.alert({title:wpforms_admin_edit_entry.strings.success,content:wpforms_admin_edit_entry.strings.msg_saved,icon:"fa fa-info-circle",type:"green",buttons:{confirm:{text:wpforms_admin_edit_entry.strings.continue_editing,btnClass:"btn-confirm",keys:["enter"]},cancel:{text:wpforms_admin_edit_entry.strings.view_entry,action:function(){i.location.href=wpforms_admin_edit_entry.strings.view_entry_url}}}}))},hideErrors:function(){f.$editForm.find(".wpforms-field.wpforms-has-error").removeClass("wpforms-has-error"),f.$editForm.find(".wpforms-error:not(label)").removeClass("wpforms-error"),f.$editForm.find("label.wpforms-error, em.wpforms-error").addClass("wpforms-hidden")},displayErrors:function(e){var r=e.data&&"errors"in e.data?e.data.errors:null;wpf.empty(r)||wpf.empty(r.field)||(r=r.field,Object.keys(r).forEach(function(e){n.displayFieldError(e,r[e]),n.displaySubfieldsErrors(e,r[e])}))},displayFieldError:function(e,r){var t,i,n;"string"!=typeof r||wpf.empty(e)&&"0"!==e||wpf.empty(r)||(t=(e="wpforms-"+f.$editForm.data("formid")+"-field_"+e)+"-error",i=f.$editForm.find("#"+e+"-container"),n=f.$editForm.find("#"+t),i.addClass("wpforms-has-error"),o("#"+e).addClass("wpforms-error"),0<n.length?n.html(r).removeClass("wpforms-hidden"):i.append('<label id="'+t+'" class="wpforms-error">'+r+"</label>"))},displaySubfieldsErrors:function(o,s){var a,d;"object"!=typeof s||wpf.empty(s)||wpf.empty(o)||(a=f.$editForm.data("formid"),d=f.$editForm.find("#"+("wpforms-"+a+"-field_"+o)+"-container"),Object.keys(s).forEach(function(e){var r,t,i,n=s[e];"string"==typeof n&&""!==n&&(r="wpforms[fields]["+o+"]["+e+"]",i=f.$editForm.find("#"+(t="wpforms-"+a+"-field_"+o+"-"+e+"-error")),d.hasClass("wpforms-has-error")||d.addClass("wpforms-has-error"),0<i.length?(d.find('[name="'+r+'"]').addClass("wpforms-error"),i.html(n).removeClass("wpforms-hidden")):(i='<label id="'+t+'" class="wpforms-error">'+n+"</label>",(d.hasClass("wpforms-field-likert_scale")?d.find("tr").eq(e.replace(/r/,"")):d.find('[name="'+r+'"]').addClass("wpforms-error")).after(i)))}))},fileDelete:function(e){e.preventDefault();var r=o(this),t=r.parent().find("a").first();o.confirm({title:!1,content:wpforms_admin_edit_entry.strings.entry_delete_file.replace("{file_name}",t.html()),icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"],action:function(){t.html(t.text().strike()),t.addClass("disabled"),r.parent().find('input[type="hidden"]').remove(),r.remove()}},cancel:{text:wpforms_admin.cancel,keys:["esc"]}}})},validateFields(e){n.validateSmartPhoneFields(e),n.validateNumbersFields(e)},validateSmartPhoneFields(t){o(".wpforms-smart-phone-field").each(function(){var e,r;o(this).val()&&(e=o(this).closest(".wpforms-field").data("field-id"),r=i.intlTelInputGlobals?.getInstance(this),o(this).triggerHandler("validate")||r?.isValidNumberPrecise()||(t.preventDefault(),n.displayFieldError(e,wpforms_settings.val_phone)))})},validateNumbersFields(t){o(".wpforms-field-number").each(function(){var e=o(this),r=e.find('input[type="number"]')[0];r.required=!1,r.checkValidity()||(e=e.data("field-id"),r=r.validity.badInput?wpforms_settings.val_number:r.validationMessage,n.displayFieldError(e,r),t.preventDefault())})}};return n}(document,window,jQuery);WPFormsEditEntry.init();