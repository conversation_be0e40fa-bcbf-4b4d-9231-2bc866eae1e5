<?php

declare (strict_types=1);
namespace WPForms\Vendor\Square\Models;

/**
 * Supported types of an action as a pending change to a subscription.
 */
class SubscriptionActionType
{
    /**
     * The action to execute a scheduled cancellation of a subscription.
     */
    public const CANCEL = 'CANCEL';
    /**
     * The action to execute a scheduled pause of a subscription.
     */
    public const PAUSE = 'PAUSE';
    /**
     * The action to execute a scheduled resumption of a subscription.
     */
    public const RESUME = 'RESUME';
    /**
     * The action to execute a scheduled swap of a subscription plan in a subscription.
     */
    public const SWAP_PLAN = 'SWAP_PLAN';
    /**
     * A billing anchor date change action.
     */
    public const CHANGE_BILLING_ANCHOR_DATE = 'CHANGE_BILLING_ANCHOR_DATE';
}
