<?php

declare (strict_types=1);
namespace WPForms\Vendor\Square\Models;

use stdClass;
class RetrieveBookingResponse implements \JsonSerializable
{
    /**
     * @var Booking|null
     */
    private $booking;
    /**
     * @var Error[]|null
     */
    private $errors;
    /**
     * Returns Booking.
     * Represents a booking as a time-bound service contract for a seller's staff member to provide a
     * specified service
     * at a given location to a requesting customer in one or more appointment segments.
     */
    public function getBooking() : ?Booking
    {
        return $this->booking;
    }
    /**
     * Sets Booking.
     * Represents a booking as a time-bound service contract for a seller's staff member to provide a
     * specified service
     * at a given location to a requesting customer in one or more appointment segments.
     *
     * @maps booking
     */
    public function setBooking(?Booking $booking) : void
    {
        $this->booking = $booking;
    }
    /**
     * Returns Errors.
     * Errors that occurred during the request.
     *
     * @return Error[]|null
     */
    public function getErrors() : ?array
    {
        return $this->errors;
    }
    /**
     * Sets Errors.
     * Errors that occurred during the request.
     *
     * @maps errors
     *
     * @param Error[]|null $errors
     */
    public function setErrors(?array $errors) : void
    {
        $this->errors = $errors;
    }
    /**
     * Encode this object to JSON
     *
     * @param bool $asArrayWhenEmpty Whether to serialize this model as an array whenever no fields
     *        are set. (default: false)
     *
     * @return array|stdClass
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize(bool $asArrayWhenEmpty = \false)
    {
        $json = [];
        if (isset($this->booking)) {
            $json['booking'] = $this->booking;
        }
        if (isset($this->errors)) {
            $json['errors'] = $this->errors;
        }
        $json = \array_filter($json, function ($val) {
            return $val !== null;
        });
        return !$asArrayWhenEmpty && empty($json) ? new stdClass() : $json;
    }
}
