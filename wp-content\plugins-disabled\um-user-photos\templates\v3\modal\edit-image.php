<?php
/**
 * Template for the UM User Photos. The "Edit Image" modal content
 *
 * Call: UM()->User_Photos()->ajax()->load_edit_photo_modal()
 * Page: "Profile", tab "Photos", modal "Edit Image"
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/modal/edit-image.php
 * @var bool   $disable_comment_option
 * @var bool   $disable_comment
 * @var bool   $disable_cover
 * @var string $cover_photo
 * @var object $photo
 * @var object $album
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
?>
<form method="post" action="" id="um-user-photos-edit-image" class="um-form-new">
	<div class="um-photo-form-response um-display-none"></div>
	<div class="um-form-rows _um_row_1">
		<div class="um-form-row">
			<div class="um-form-cols um-form-cols-1">
				<div class="um-form-col um-form-col-1">
					<div class="um-field um-field-text um-field-type_text">
						<label for="image-title-<?php echo esc_attr( $photo->ID ); ?>">
							<?php esc_html_e( 'Image title', 'um-user-photos' ); ?>
							<?php if ( UM()->options()->get( 'form_asterisk' ) ) { ?>
								<span class="um-req" title="<?php esc_attr_e( 'Required', 'um-user-photos' ); ?>">*</span>
							<?php } ?>
						</label>
						<input id="image-title-<?php echo esc_attr( $photo->ID ); ?>" type="text" name="title" value="<?php echo esc_attr( $photo->post_title ); ?>" title="<?php esc_attr_e( 'Image title', 'um-user-photos' ); ?>" required />
					</div>
					<div class="um-field um-field-textarea um-field-type_textarea">
						<label for="image-caption-<?php echo esc_attr( $photo->ID ); ?>"><?php esc_html_e( 'Image caption', 'um-user-photos' ); ?></label>
						<textarea id="image-caption-<?php echo esc_attr( $photo->ID ); ?>" name="caption" title="<?php esc_attr_e( 'Image caption', 'um-user-photos' ); ?>"><?php echo esc_textarea( $photo->post_excerpt ); ?></textarea>
					</div>
					<div class="um-field um-field-url um-field-type_url">
						<label for="image-link-<?php echo esc_attr( $photo->ID ); ?>"><?php esc_html_e( 'Related link', 'um-user-photos' ); ?></label>
						<input id="image-link-<?php echo esc_attr( $photo->ID ); ?>" type="url" name="link" value="<?php echo esc_attr( $photo->_link ); ?>" title="<?php esc_attr_e( 'Related link', 'um-user-photos' ); ?>" />
					</div>
					<?php if ( ! $disable_comment_option ) { ?>
						<div class="um-field um-field-checkbox um-field-type_checkbox">
							<label class="um-checkbox-label um-size-sm">
								<input type="checkbox" name="disable_comments" value="1" <?php checked( $disable_comment ); ?> />
								<?php esc_html_e( 'Disable comments', 'um-user-photos' ); ?>
							</label>
						</div>
					<?php } ?>
					<?php if ( ! $disable_cover ) { ?>
						<div class="um-field um-field-checkbox um-field-type_checkbox">
							<label class="um-checkbox-label um-size-sm">
								<input type="checkbox" name="cover_photo" value="<?php echo absint( $photo->ID ); ?>" <?php checked( absint( $cover_photo ), absint( $photo->ID ) ); ?> />
								<?php esc_html_e( 'Set as album cover', 'um-user-photos' ); ?>
							</label>
						</div>
					<?php } ?>
				</div>
			</div>
		</div>
	</div>
	<div class="um-form-submit">
		<?php
		$loader = UM()->frontend()::layouts()::ajax_loader( 'm', array( 'classes' => array( 'um-user-photos-loader', 'um-display-none' ) ) );
		$submit = UM()->frontend()::layouts()::button(
			esc_html__( 'Update', 'um-user-photos' ),
			array(
				'type'    => 'button',
				'size'    => 'm',
				'design'  => 'primary',
				'id'      => 'um-user-photos-image-update-btn',
				'classes' => array(
					'um-modal-btn',
				),
				'data'    => array(
					'wpnonce' => wp_create_nonce( 'um_user_photos_comment_post' ),
				),
			)
		);
		$cancel = UM()->frontend()::layouts()::button(
			__( 'Cancel', 'um-user-photos' ),
			array(
				'type'    => 'button',
				'size'    => 'm',
				'classes' => array(
					'um-modal-close',
				),
			)
		);
		echo wp_kses( $loader . $submit . $cancel, UM()->get_allowed_html( 'templates' ) );
		?>
	</div>
	<input type="hidden" name="id" value="<?php echo esc_attr( $photo->ID ); ?>"/>
	<input type="hidden" name="album" value="<?php echo esc_attr( $album->ID ); ?>"/>
	<input type="hidden" name="_wpnonce" value="<?php echo esc_attr( wp_create_nonce( 'um_edit_image' . $photo->ID ) ); ?>"/>
</form>
