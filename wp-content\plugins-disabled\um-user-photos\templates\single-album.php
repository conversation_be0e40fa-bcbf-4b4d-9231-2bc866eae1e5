<?php
/**
 * Template for the UM User Photos. The "Album" block
 *
 * Call: UM()->User_Photos()->ajax()->um_user_photos_load_more()
 * Call: UM()->User_Photos()->ajax()->um_user_photos_get_single_album_view()
 * Page: "Profile", tab "Photos"
 * Parent template: photos.php
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/single-album.php
 * @var int  $columns
 * @var bool $is_my_profile
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! empty( $photos ) && is_array( $photos ) ) {
	?>
	<div class="um-user-photos-single-album um-up-grid <?php echo esc_attr( "um-up-grid-col-{$columns}" ); ?>">
		<?php
		$args_t = compact( 'is_my_profile', 'photos' );
		UM()->get_template( 'photos-grid.php', UM_USER_PHOTOS_PLUGIN, $args_t, true );
		?>
	</div>
	<?php
} else {
	?>
	<p class="text-center"><?php esc_html_e( 'Nothing to display', 'um-user-photos' ); ?></p>
	<?php
}
