<?php

namespace UM_Stripe\Vendor\UM_Stripe\Vendor;

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
use UM_Stripe\Vendor\Symfony\Intl\NumberFormatter\NumberFormatter as IntlNumberFormatter;
use UM_Stripe\Vendor\Symfony\Polyfill\Intl\Icu\NumberFormatter as NumberFormatterPolyfill;
if (!\class_exists(NumberFormatterPolyfill::class)) {
    trigger_deprecation('symfony/intl', '5.3', 'Polyfills are deprecated, try running "composer require symfony/polyfill-intl-icu ^1.21" instead.');
    /**
     * Stub implementation for the NumberFormatter class of the intl extension.
     *
     * <AUTHOR> <bschuss<PERSON>@gmail.com>
     *
     * @see IntlNumberFormatter
     */
    class NumberFormatter extends IntlNumberFormatter
    {
    }
    /**
     * Stub implementation for the NumberFormatter class of the intl extension.
     *
     * <AUTHOR> <<EMAIL>>
     *
     * @see IntlNumberFormatter
     */
    \class_alias('UM_Stripe\Vendor\NumberFormatter', 'NumberFormatter', \false);
} else {
    /**
     * Stub implementation for the NumberFormatter class of the intl extension.
     *
     * <AUTHOR> Schussek <<EMAIL>>
     *
     * @see IntlNumberFormatter
     */
    class NumberFormatter extends NumberFormatterPolyfill
    {
    }
    /**
     * Stub implementation for the NumberFormatter class of the intl extension.
     *
     * <AUTHOR> Schussek <<EMAIL>>
     *
     * @see IntlNumberFormatter
     */
    \class_alias('UM_Stripe\Vendor\NumberFormatter', 'NumberFormatter', \false);
}
