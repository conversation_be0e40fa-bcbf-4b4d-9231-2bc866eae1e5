# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.5.5] - 2025-04-28
### Changed
- Internal updates.

## [0.5.4] - 2025-03-21
### Changed
- Internal updates.

## [0.5.3] - 2025-03-12
### Changed
- Internal updates.

## [0.5.2] - 2025-03-05
### Changed
- Internal updates.

## [0.5.1] - 2025-02-24
### Changed
- Update dependencies.

## [0.5.0] - 2025-02-10
### Changed
- Combine vulnerabilities for the same extension into single vulnerable extension threats. [#40863]

## [0.4.2] - 2025-02-03
### Changed
- Internal updates.

## [0.4.1] - 2024-11-25
### Changed
- Updated dependencies. [#40286]

## [0.4.0] - 2024-11-14
### Added
- Added threats property to protect status. [#40097]

### Removed
- General: Update minimum PHP version to 7.2. [#40147]

## [0.3.1] - 2024-11-04
### Added
- Enable test coverage. [#39961]

## [0.3.0] - 2024-09-23
### Changed
- Adds a fixable_threats status property [#39125]

## [0.2.1] - 2024-08-26
### Changed
- Updated package dependencies. [#39004]

## [0.2.0] - 2024-08-09
### Added
- Add Scan History model. [#38117]

## 0.1.0 - 2024-07-15
### Added
- Initial version. [#37864]

[0.5.5]: https://github.com/Automattic/jetpack-protect-models/compare/v0.5.4...v0.5.5
[0.5.4]: https://github.com/Automattic/jetpack-protect-models/compare/v0.5.3...v0.5.4
[0.5.3]: https://github.com/Automattic/jetpack-protect-models/compare/v0.5.2...v0.5.3
[0.5.2]: https://github.com/Automattic/jetpack-protect-models/compare/v0.5.1...v0.5.2
[0.5.1]: https://github.com/Automattic/jetpack-protect-models/compare/v0.5.0...v0.5.1
[0.5.0]: https://github.com/Automattic/jetpack-protect-models/compare/v0.4.2...v0.5.0
[0.4.2]: https://github.com/Automattic/jetpack-protect-models/compare/v0.4.1...v0.4.2
[0.4.1]: https://github.com/Automattic/jetpack-protect-models/compare/v0.4.0...v0.4.1
[0.4.0]: https://github.com/Automattic/jetpack-protect-models/compare/v0.3.1...v0.4.0
[0.3.1]: https://github.com/Automattic/jetpack-protect-models/compare/v0.3.0...v0.3.1
[0.3.0]: https://github.com/Automattic/jetpack-protect-models/compare/v0.2.1...v0.3.0
[0.2.1]: https://github.com/Automattic/jetpack-protect-models/compare/v0.2.0...v0.2.1
[0.2.0]: https://github.com/Automattic/jetpack-protect-models/compare/v0.1.0...v0.2.0
