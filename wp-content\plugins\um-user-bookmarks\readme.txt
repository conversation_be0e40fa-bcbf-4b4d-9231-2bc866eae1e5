﻿=== Ultimate Member - User Bookmarks ===
Author URI: https://ultimatemember.com/
Plugin URI: https://ultimatemember.com/extensions/user-bookmarks/
Contributors: ultimatemember, champsupertramp, nsinelnikov
Tags: private messaging, email, user, community
Requires at least: 5.5
Tested up to: 6.7
Stable tag: 2.1.6
License: GNU Version 2 or Any Later Version
License URI: http://www.gnu.org/licenses/gpl-3.0.txt
Requires UM core at least: 2.7.0

Allow users to bookmark content from your website.

== Description ==

Allow users to bookmark content from your website.

= Key Features: =

* Select which content bookmarks show on e.g pages, posts, CPTs
* Disable bookmarking for individual page/posts
* Bookmark link can appear at top or bottom of page/post content
* Bookmarks can be organized into different user created folders
* Folders can be made public or private by users
* Users can view and manage their bookmark folders and bookmarks from their profiles
* Users can view other users public bookmark folders

= Development * Translations =

Want to add a new language to Ultimate Member? Great! You can contribute via [translate.wordpress.org](https://translate.wordpress.org/projects/wp-plugins/ultimate-member).

If you are a developer and you need to know the list of UM Hooks, make this via our [Hooks Documentation](https://docs.ultimatemember.com/article/1324-hooks-list).

= Documentation & Support =

Got a problem or need help with Ultimate Member? Head over to our [documentation](http://docs.ultimatemember.com/) and perform a search of the knowledge base. If you can’t find a solution to your issue then you can create a topic on the [support forum](https://wordpress.org/support/plugin/um-forumwp).

== Installation ==

1. Activate the plugin
2. That's it. Go to Ultimate Member > Settings > Extensions > User Bookmarks to customize plugin options
3. For more details, please visit the official [Documentation](https://docs.ultimatemember.com/article/1481-user-bookmarks-setup) page.

== Changelog ==

= 2.1.6: November 18, 2024 =

* Fixed: "Load textdomain just in time" issue

= 2.1.5: December 11, 2023 =

* Tweak: wp-admin scripts refactored
* Fixed: 'um_user_permissions_filter' hook attributes
* Tweak: Using enqueue scripts suffix from UM core class. Dependency from UM core 2.7.0
* Tweak: Enhancements related to WPCS

= 2.1.4: October 11, 2023 =

* Updated: Dependencies versions based on the recent changes for `UM()->frontend()->enqueue()::get_suffix();`
* Fixed: Case when extension isn't active based on dependency, but we can provide the license key field

= 2.1.3: September 01, 2023 =

* Added: Ability to bookmark user profiles
* Added: `[um_user_bookmarks_all]` shortcode
* Fixed: `[um_user_bookmarks]` shortcode when folder system is disabled for the bookmarks
* Fixed: Stop displaying bookmarks button if user role capability is set to disable the bookmarks feature
* Fixed: Issue with lack of the nonces

= 2.1.2: June 12, 2023 =

* Tweak: Template overwrite versioning

= 2.1.1: December 14, 2022 =

* Fixed: Security vulnerability related to the 'user_id' parameter in AJAX requests
* Fixed: The "Add Folder" block displaying in the profile tab
* Tweak: Added animation for buttons while waiting for AJAX response

* Templates required update:

  - buttons/add.php
  - buttons/remove.php
  - profile/folder-view.php

* Cached and optimized/minified assets(JS/CSS) must be flushed/re-generated after upgrade

= 2.1.0: August 17, 2022 =

* Added: Disable folders structure setting
* Added: Hooks for exclude bookmarks from query

* Templates required update:

  - buttons/add.php
  - profile/folder-view.php
  - profile/single-folder.php

= 2.0.9: February 9, 2022 =

* Added: "Create default folder" feature
* Fixed: "Add folder" button appears in other users' profiles
* Fixed: Extension settings structure

* Templates required update:

  - profile/folder-view.php
  - profile/folder-view/add-folder.php

= 2.0.8: December 20, 2021 =

* Fixed: Using emoji in the bookmarks folder name and generating the folder key in this case
* Fixed: Bookmarks buttons inside archives

= 2.0.7: March 22, 2021 =

* Fixed: XSS vulnerabilities
* Tweak: UM Dropdown.js scripts updated
* Deprecated: `profile/edit-folder/form.php` template - it's unusable duplicate of `profile/edit-folder.php`

= 2.0.6: December 8, 2020 =

* Added: "Using Page Builder" option to a description if enabled
* Fixed: Issue with page builders showing shortcodes (strip_shortcodes added to bookmark description)
* Fixed: Bookmark issue on product and custom post types
* Fixed: Admin forms rendering in metaboxes

= 2.0.5: August 11, 2020 =

* Added: *.pot translations file
* Changed: The bookmark button templates
* Updated: CSS styles for the button to make it compatible with other UM extensions

= 2.0.4: March 18, 2020 =

* Added: Compatibility with Profile Tabs
* Fixed: Trimmed bookmarks button HTML

= 2.0.3: January 24, 2020 =

* Fixed: Displaying bookmark icon
* Fixed: Displaying bookmarks menu at the profile page when Bookmarks capability is turned off for the user role

= 2.0.2: November 11, 2019 =

* Added: Sanitize functions for request variables
* Added: esc_attr functions to avoid XSS vulnerabilities
* Fixed: [um_bookmarks_button] shortcodes
* Fixed: style dependencies
* Fixed: modal window z-index
* Fixed: privacy fields at account page

= 2.0.1: July 16, 2019 =

* Added: Bookmarks for archive
* Fixed: Texts spelling
* Fixed: Checking if bookmark or post exists
* Fixed: Add to bookmark button arguments

= 2.0: June 10, 2019 =

* Initial release
