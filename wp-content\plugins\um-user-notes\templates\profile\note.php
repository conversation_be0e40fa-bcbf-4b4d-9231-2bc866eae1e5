<?php
/**
 * Template for the note
 *
 * Call: load();
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-notes/note.php
 *
 * @see        https://docs.ultimatemember.com/article/1516-templates-map
 * @package    um_ext\um_user_notes\templates
 * @version    1.1.2
 * @var int    $id
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
?>

<div class="note-block">
	<div class="note-block-container">
		<?php
		$note_status = get_post_status( $id );

		if ( has_post_thumbnail( $id ) ) {
			$image = wp_get_attachment_image_src( get_post_thumbnail_id( $id ), 'um_notes_thumbnail' );
			?>
			<div class="um-notes-note-image">
				<img src="<?php echo esc_url( $image[0] ); ?>" alt="<?php echo esc_attr( get_the_title( $id ) ); ?>"/>
			</div>
		<?php } ?>

		<div>

			<strong>
				<?php
				if ( 'draft' === $note_status ) {
					?>
					<span style="color:#2e93fa;"><em><?php echo esc_html( ucfirst( $note_status ) ); ?> - </em></span>
					<?php
				}

				echo esc_html( get_the_title( $id ) );

				// Only Administrator can see the Note ID for using it as shortcode attributes on the custom pages.
				if ( current_user_can( 'administrator' ) ) {
					// translators: %s: Note ID.
					echo esc_html( sprintf( __( ' (ID #%s)', 'um-user-notes' ), $id ) );
				}
				?>
			</strong>

			<br/>

			<small>
				<?php echo esc_html( substr( wp_strip_all_tags( get_the_content( $id ) ), 0, UM()->Notes()->get_excerpt_length( true ) ) ); ?>..
			</small>
		</div>

		<a href="#" class="um_note_read_more" data-note_id="<?php echo esc_attr( $id ); ?>" data-nonce="<?php echo esc_attr( wp_create_nonce( 'um_user_notes_view' ) ); ?>">
			<?php UM()->Notes()->read_more_text(); ?>
		</a>
	</div>
</div>
