<?php
namespace um_ext\um_user_bookmarks\core;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}


/**
 * Class Bookmark_Profile
 * @package um_ext\um_user_bookmarks\core
 */
class Bookmark_Profile {

	/**
	 * Bookmark_Profile constructor.
	 */
	public function __construct() {
		add_filter( 'um_profile_tabs', array( $this, 'add_profile_tab' ), 801 );
		add_filter( 'um_user_profile_tabs', array( &$this, 'add_user_tab' ), 5, 1 );

		if ( ! UM()->options()->get( 'um_user_bookmarks_disable_folders' ) ) {
			if ( defined( 'UM_DEV_MODE' ) && UM_DEV_MODE && UM()->options()->get( 'enable_new_ui' ) ) {
				// default tab is handled in UM core
			} else {
				add_action( 'um_profile_content_bookmarks_default', array( $this, 'get_bookmarks_content' ) );
			}
			add_action( 'um_profile_content_bookmarks_folders', array( $this, 'get_bookmarks_content' ) );
			add_action( 'um_profile_content_bookmarks_all', array( $this, 'get_bookmarks_content_all' ) );
		} else {
			if ( UM()->options()->get( 'um_user_bookmarks_profile' ) ) {
				if ( defined( 'UM_DEV_MODE' ) && UM_DEV_MODE && UM()->options()->get( 'enable_new_ui' ) ) {
					// default tab is handled in UM core
				} else {
					add_action( 'um_profile_content_bookmarks_default', array( $this, 'get_bookmarks_content_all' ) );
				}
				add_action( 'um_profile_content_bookmarks_all', array( $this, 'get_bookmarks_content_all' ) );
			} else {
				add_action( 'um_profile_content_bookmarks', array( $this, 'get_bookmarks_content_all' ) );
			}
		}

		if ( UM()->options()->get( 'um_user_bookmarks_profile' ) ) {
			add_action( 'um_profile_content_bookmarks_users', array( $this, 'get_bookmarks_content_users' ) );
		}
	}

	/**
	 * @param $tabs
	 *
	 * @return mixed
	 */
	public function add_profile_tab( $tabs ) {
		$tabs['bookmarks'] = array(
			'name' => __( 'Bookmarks', 'um-user-bookmarks' ),
			'icon' => 'um-faicon-bookmark',
		);

		return $tabs;
	}


	/**
	 * Hide tab if there isn't capability
	 *
	 * @param $tabs
	 *
	 * @return mixed
	 */
	public function add_user_tab( $tabs ) {
		if ( empty( $tabs['bookmarks'] ) ) {
			return $tabs;
		}

		if ( ! UM()->User_Bookmarks()->user_can_view_bookmark( um_profile_id() ) ) {
			unset( $tabs['bookmarks'] );
		} else {
			if ( ! UM()->options()->get( 'um_user_bookmarks_disable_folders' ) ) {
				if ( defined( 'UM_DEV_MODE' ) && UM_DEV_MODE && UM()->options()->get( 'enable_new_ui' ) ) {
					$tabs['bookmarks']['subnav'] = array(
						'folders' => array(
							'title' => UM()->User_Bookmarks()->get_folder_text( true ),
						),
						'all'     => array(
							'title' => __( 'All', 'um-user-bookmarks' ),
						),
					);
				} else {
					$tabs['bookmarks']['subnav'] = array(
						'folders' => UM()->User_Bookmarks()->get_folder_text( true ),
						'all'     => __( 'All', 'um-user-bookmarks' ),
					);
				}

				if ( UM()->options()->get( 'um_user_bookmarks_profile' ) ) {
					if ( defined( 'UM_DEV_MODE' ) && UM_DEV_MODE && UM()->options()->get( 'enable_new_ui' ) ) {
						$tabs['bookmarks']['subnav']['users'] = array(
							'title' => UM()->User_Bookmarks()->get_folder_text( true, true ),
						);
						$tabs['bookmarks']['subnav']['all']   = array(
							'title' => __( 'All Posts', 'um-user-bookmarks' ),
						);
					} else {
						$tabs['bookmarks']['subnav']['users'] = UM()->User_Bookmarks()->get_folder_text( true, true );
						$tabs['bookmarks']['subnav']['all']   = __( 'All Posts', 'um-user-bookmarks' );
					}
				}

				$tabs['bookmarks']['subnav_default'] = 'folders';
			} else {
				if ( UM()->options()->get( 'um_user_bookmarks_profile' ) ) {
					if ( defined( 'UM_DEV_MODE' ) && UM_DEV_MODE && UM()->options()->get( 'enable_new_ui' ) ) {
						$tabs['bookmarks']['subnav'] = array(
							'all'   => array(
								'title' => __( 'All Posts', 'um-user-bookmarks' ),
							),
							'users' => array(
								'title' => UM()->User_Bookmarks()->get_folder_text( true, true ),
							),
						);
					} else {
						$tabs['bookmarks']['subnav'] = array(
							'all'   => __( 'All Posts', 'um-user-bookmarks' ),
							'users' => UM()->User_Bookmarks()->get_folder_text( true, true ),
						);
					}

					$tabs['bookmarks']['subnav_default'] = 'all';
				}
			}
		}

		return $tabs;
	}


	public function get_bookmarks_content_users() {
		wp_enqueue_script( 'um-user-bookmarks' );
		wp_enqueue_style( 'um-user-bookmarks' );

		if ( UM()->User_Bookmarks()->user_can_view_bookmark( um_profile_id() ) ) {
			echo $this->get_user_bookmarks_profiles();
		} else {
			esc_html_e( 'You do not have permission to view bookmark', 'um-user-bookmarks' );
		}
	}


	/**
	 *
	 */
	public function get_bookmarks_content() {
		wp_enqueue_script( 'um-user-bookmarks' );
		wp_enqueue_style( 'um-user-bookmarks' );

		if ( UM()->User_Bookmarks()->user_can_view_bookmark( um_profile_id() ) ) {
			echo $this->get_user_profile_bookmarks();
		} else {
			esc_html_e( 'You do not have permission to view bookmark', 'um-user-bookmarks' );
		}
	}


	/**
	 *
	 */
	public function get_bookmarks_content_all() {
		wp_enqueue_script( 'um-user-bookmarks' );
		wp_enqueue_style( 'um-user-bookmarks' );

		if ( UM()->User_Bookmarks()->user_can_view_bookmark( um_profile_id() ) ) {
			echo $this->get_user_profile_bookmarks_all();
		} else {
			esc_html_e( 'You do not have permission to view bookmark', 'um-user-bookmarks' );
		}
	}

	/**
	 * @param $profile_id
	 *
	 * @return false|string
	 */
	public function get_user_bookmarks_profiles( $profile_id = null ) {
		if ( ! $profile_id ) {
			$profile_id = um_profile_id();
		}

		$user_bookmarks = get_user_meta( $profile_id, '_um_user_bookmarks_profiles', true );
		$user_bookmarks = empty( $user_bookmarks ) ? array() : $user_bookmarks;

		foreach ( $user_bookmarks as $user_id => $url ) {
			if ( ! current_user_can( 'administrator' ) && ! um_can_view_profile( $user_id ) ) {
				unset( $user_bookmarks[ $user_id ] );
			}
		}

		wp_enqueue_script( 'um-user-bookmarks' );
		wp_enqueue_style( 'um-user-bookmarks' );

		return UM()->get_template(
			'profile/users-view.php',
			um_user_bookmarks_plugin,
			array(
				'user_bookmarks' => $user_bookmarks,
				'profile_id'     => $profile_id,
			)
		);
	}


	/**
	 * Returns folder view of user bookmarks
	 *
	 * @param null $profile_id
	 *
	 * @return false|string
	 */
	public function get_user_profile_bookmarks( $profile_id = null ) {
		if ( ! $profile_id ) {
			$profile_id = um_profile_id();
		}

		$include_private = false;
		if ( is_user_logged_in() && get_current_user_id() === absint( $profile_id ) ) {
			$include_private = true;
		}

		$user_bookmarks = get_user_meta( $profile_id, '_um_user_bookmarks', true );

		wp_enqueue_script( 'um-user-bookmarks' );
		wp_enqueue_style( 'um-user-bookmarks' );

		ob_start();

		// Load view
		UM()->get_template(
			'profile/folder-view.php',
			um_user_bookmarks_plugin,
			array(
				'user_bookmarks'  => $user_bookmarks,
				'include_private' => $include_private,
				'profile_id'      => $profile_id,
			),
			true
		);

		$html = ob_get_clean();
		return $html;
	}


	/**
	 * Retrieves all bookmark posts for a user
	 *
	 * @param null|int $profile_id
	 *
	 * @return string
	 */
	public function get_user_profile_bookmarks_all( $profile_id = null ) {
		wp_enqueue_script( 'um-user-bookmarks' );
		wp_enqueue_style( 'um-user-bookmarks' );

		if ( ! $profile_id ) {
			$profile_id = um_profile_id();
		}

		$include_private = false;
		if ( is_user_logged_in() ) {
			if ( get_current_user_id() === absint( $profile_id ) ) {
				$include_private = true;
			}
		}

		$user_bookmarks = get_user_meta( $profile_id, '_um_user_bookmarks', true );
		$user_bookmarks = empty( $user_bookmarks ) ? array() : $user_bookmarks;

		if ( empty( $user_bookmarks ) ) {
			return __( 'No bookmarks have been added.', 'um-user-bookmarks' );
		}

		$bookmarks = array();

		if ( $user_bookmarks ) {
			foreach ( $user_bookmarks as $value ) {
				if ( ! $include_private && 'private' === $value['type'] ) {
					continue;
				}

				if ( empty( $value['bookmarks'] ) ) {
					continue;
				}

				$bookmarks = array_merge( $bookmarks, array_keys( $value['bookmarks'] ) );
			}
		}

		if ( ! empty( $bookmarks ) ) {
			$bookmarks = apply_filters( 'um_user_bookmarks_exclude', $bookmarks );
		}

		ob_start();

		if ( ! empty( $bookmarks ) ) {
			UM()->get_template(
				'profile/bookmarks.php',
				um_user_bookmarks_plugin,
				array(
					'bookmarks' => $bookmarks,
					'user_id'   => $profile_id,
				),
				true
			);
		} else {
			echo esc_html__( 'No bookmarks have been added.', 'um-user-bookmarks' );
		}

		$html = ob_get_clean();
		return $html;
	}
}
