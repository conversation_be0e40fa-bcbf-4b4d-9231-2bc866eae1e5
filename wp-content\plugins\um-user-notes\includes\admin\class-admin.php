<?php
namespace um_ext\um_user_notes\admin;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! class_exists( 'um_ext\um_user_notes\admin\Admin' ) ) {

	/**
	 * Class Admin
	 *
	 * @package um_ext\um_user_notes\admin
	 */
	class Admin {

		/**
		 * Admin constructor.
		 */
		public function __construct() {
			add_filter( 'um_settings_structure', array( &$this, 'extend_settings' ) );
			add_filter( 'um_admin_role_metaboxes', array( $this, 'um_user_notes_add_role_metabox' ), 10, 1 );

			add_filter( 'um_override_templates_scan_files', array( $this, 'um_user_notes_extend_scan_files' ), 10, 1 );
			add_filter( 'um_override_templates_get_template_path__um-user-notes', array( $this, 'um_user_notes_get_path_template' ), 10, 2 );
		}

		/**
		 * Additional Settings for Notes
		 *
		 * @param array $settings
		 *
		 * @return array
		 */
		public function extend_settings( $settings ) {

			$settings['licenses']['fields'][] = array(
				'id'        => 'um_user_notes_license_key',
				'label'     => __( 'User Notes License Key', 'um-user-notes' ),
				'item_name' => 'User Notes',
				'author'    => 'ultimatemember',
				'version'   => um_user_notes_version,
			);

			$settings['extensions']['sections']['um-user-notes'] = array(
				'title'  => __( 'User Notes', 'um-user-notes' ),
				'fields' => array(
					array(
						'id'          => 'um_user_notes_per_page',
						'type'        => 'text',
						'placeholder' => '',
						'label'       => __( 'Notes per page', 'um-user-notes' ),
						'size'        => 'medium',
					),
					array(
						'id'          => 'um_user_notes_image_size',
						'type'        => 'text',
						'placeholder' => '400x300',
						'label'       => __( 'Thumbnail image size', 'um-user-notes' ),
						'size'        => 'medium',
					),
					array(
						'id'          => 'um_user_notes_excerpt_length',
						'type'        => 'text',
						'placeholder' => 'Number of charactes to display.',
						'label'       => __( 'Excerpt length', 'um-user-notes' ),
						'size'        => 'medium',
					),
					array(
						'id'          => 'um_user_notes_read_more_text',
						'type'        => 'text',
						'placeholder' => '',
						'label'       => __( 'Read more text', 'um-user-notes' ),
						'size'        => 'medium',
					),
					array(
						'id'          => 'um_user_notes_load_more_text',
						'type'        => 'text',
						'placeholder' => '',
						'label'       => __( 'Load more text', 'um-user-notes' ),
						'size'        => 'medium',
					),
					array(
						'id'    => 'um_user_notes_enable_download',
						'type'  => 'checkbox',
						'label' => __( 'Enable download notes', 'um-user-notes' ),
					),
					array(
						'id'          => 'um_user_notes_download_button_title',
						'type'        => 'text',
						'placeholder' => '',
						'label'       => __( 'Download button title', 'um-user-notes' ),
						'size'        => 'medium',
						'conditional' => array( 'um_user_notes_enable_download', '=', 1 ),
					),
					array(
						'id'    => 'um_user_notes_enable_print',
						'type'  => 'checkbox',
						'label' => __( 'Enable print notes', 'um-user-notes' ),
					),
					array(
						'id'          => 'um_user_notes_print_button_title',
						'type'        => 'text',
						'placeholder' => '',
						'label'       => __( 'Print button title', 'um-user-notes' ),
						'size'        => 'medium',
						'conditional' => array( 'um_user_notes_enable_print', '=', 1 ),
					),
					array(
						'id'          => 'um_user_notes_download_print_guest',
						'type'        => 'checkbox',
						'label'       => __( 'Enable print/download notes for non-logged in users', 'um-user-notes' ),
						'conditional' => array( 'um_user_notes_enable_print||um_user_notes_enable_download', '=', 1 ),
					),
					array(
						'id'          => 'um_user_notes_download_print_position',
						'type'        => 'select',
						'label'       => __( 'Save/print buttons position', 'um-user-notes' ),
						'options'     => array(
							1 => __( 'Top left', 'um-user-notes' ),
							2 => __( 'Top right', 'um-user-notes' ),
							3 => __( 'Bottom left', 'um-user-notes' ),
							4 => __( 'Bottom right', 'um-user-notes' ),
						),
						'conditional' => array( 'um_user_notes_enable_print||um_user_notes_enable_download', '=', 1 ),
						'size'        => 'medium',
					),
					array(
						'id'          => 'um_user_notes_download_print_type',
						'type'        => 'select',
						'label'       => __( 'Set button type', 'um-user-notes' ),
						'options'     => array(
							1 => __( 'Icon', 'um-user-notes' ),
							2 => __( 'Text', 'um-user-notes' ),
							3 => __( 'Icon + text', 'um-user-notes' ),
						),
						'conditional' => array( 'um_user_notes_enable_print||um_user_notes_enable_download', '=', 1 ),
						'size'        => 'medium',
					),
				),
			);

			return $settings;
		}

		/**
		 * @param array $roles_metaboxes
		 *
		 * @return array
		 */
		public function um_user_notes_add_role_metabox( $roles_metaboxes ) {
			$roles_metaboxes[] = array(
				'id'       => 'um-admin-form-notes{' . um_user_notes_path . '}',
				'title'    => __( 'User Notes', 'um-user-notes' ),
				'callback' => array( UM()->metabox(), 'load_metabox_role' ),
				'screen'   => 'um_role_meta',
				'context'  => 'normal',
				'priority' => 'default',
			);

			return $roles_metaboxes;
		}

		/**
		 * Scan templates from extension
		 *
		 * @param $scan_files
		 *
		 * @return array
		 */
		public function um_user_notes_extend_scan_files( $scan_files ) {
			$extension_files['um-user-notes'] = UM()->admin_settings()->scan_template_files( um_user_notes_path . '/templates/' );
			$scan_files                       = array_merge( $scan_files, $extension_files );

			return $scan_files;
		}


		/**
		 * Get template paths
		 *
		 * @param $located
		 * @param $file
		 *
		 * @return array
		 */
		public function um_user_notes_get_path_template( $located, $file ) {
			if ( file_exists( get_stylesheet_directory() . '/ultimate-member/um-user-notes/' . $file ) ) {
				$located = array(
					'theme' => get_stylesheet_directory() . '/ultimate-member/um-user-notes/' . $file,
					'core'  => um_user_notes_path . 'templates/' . $file,
				);
			}

			return $located;
		}
	}
}
