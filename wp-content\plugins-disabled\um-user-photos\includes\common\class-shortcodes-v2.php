<?php
namespace um_ext\um_user_photos\common;

use WP_Query;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Shortcodes_V2
 * Note: It will be deprecated since new UI is live.
 * @todo deprecate since new UI is live.
 * @package um_ext\um_user_photos\common
 */
class Shortcodes_V2 {

	/**
	 * Shortcodes_V2 constructor.
	 */
	public function __construct() {
		add_shortcode( 'ultimatemember_gallery', array( $this, 'get_gallery_content' ) );
		add_shortcode( 'ultimatemember_gallery_photos', array( $this, 'gallery_photos_content' ) );
		add_shortcode( 'ultimatemember_albums', array( $this, 'get_albums_content' ) );
	}

	/**
	 * Display common "Albums" block
	 *
	 * @param array $atts
	 *
	 * @return string
	 */
	public function get_albums_content( $atts = array() ) {
		$atts_def = array(
			'column'   => 2,
			'page'     => 1,
			'per_page' => 12,
		);

		/**
		 * There is possible to use 'shortcode_atts_ultimatemember_albums' filter for getting customized $atts.
		 * @link https://developer.wordpress.org/reference/hooks/shortcode_atts_shortcode/
		 */
		$args = shortcode_atts( $atts_def, $atts, 'ultimatemember_albums' );

		$albums = UM()->User_Photos()->common()->query()->get_albums( $args );
		if ( empty( $albums ) || ! $albums->have_posts() ) {
			return '';
		}

		$sizes         = UM()->options()->get( 'photo_thumb_sizes' );
		$args['size']  = isset( $sizes[2] ) ? absint( $sizes[2] ) : 190;
		$args['pages'] = (int) ceil( $albums->found_posts / $args['per_page'] );

		$args_t = compact( 'albums', 'args' );
		$output = UM()->get_template( 'albums.php', UM_USER_PHOTOS_PLUGIN, $args_t );

		wp_enqueue_script( 'um-user-photos' );
		wp_enqueue_style( 'um-user-photos' );

		return $output;
	}

	/**
	 * Display the "Albums" block
	 *
	 * @param array $atts
	 *
	 * @return string
	 */
	public function get_gallery_content( $atts = array() ) {
		/** There is possible to use 'shortcode_atts_ultimatemember_gallery' filter for getting customized $atts. This filter is documented in wp-includes/shortcodes.php "shortcode_atts_{$shortcode}" */
		$atts = shortcode_atts(
			array(
				'user_id' => um_profile_id(),
			),
			$atts,
			'ultimatemember_gallery'
		);

		// phpcs:ignore WordPress.Security.NonceVerification
		$user_id        = ! empty( $_POST['user_id'] ) ? absint( $_POST['user_id'] ) : absint( $atts['user_id'] );
		$user_role      = UM()->roles()->get_priority_user_role( $user_id );
		$user_role_data = UM()->roles()->role_data( $user_role );

		if ( empty( $user_role_data['enable_user_photos'] ) ) {
			return '<p class="text-center">' . esc_html__( 'Nothing to display', 'um-user-photos' ) . '</p>';
		}

		$is_my_profile = is_user_logged_in() && get_current_user_id() === $user_id;
		if ( ! $is_my_profile && ! um_can_view_profile( $user_id ) ) {
			return '<p class="text-center">' . esc_html__( 'Nothing to display', 'um-user-photos' ) . '</p>';
		}

		$args = array(
			'post_type'      => 'um_user_photos',
			'author__in'     => array( $user_id ),
			'posts_per_page' => -1,
			'post_status'    => 'publish',
		);

		$args = apply_filters( 'um_user_photo_query_args', $args, $user_id );

		$albums = new WP_Query( $args );

		$output = '';
		if ( $is_my_profile || um_is_myprofile() ) {
			$args_t  = compact( 'is_my_profile', 'user_id' );
			$output .= UM()->get_template( 'gallery-head.php', UM_USER_PHOTOS_PLUGIN, $args_t );
			$this->modal_template();
		}

		if ( $albums->have_posts() ) {

			$column = UM()->options()->get( 'um_user_photos_albums_column' );
			if ( ! $column ) {
				$column = 'um-user-photos-col-2';
			}

			$default       = UM_USER_PHOTOS_URL . 'assets/images/dummy_album_cover.png';
			$disable_title = UM()->options()->get( 'um_user_photos_disable_title' );

			$args_t  = compact( 'albums', 'column', 'default', 'disable_title' );
			$output .= UM()->get_template( 'gallery.php', UM_USER_PHOTOS_PLUGIN, $args_t );
		}

		wp_enqueue_script( 'um-user-photos' );
		wp_enqueue_style( 'um-user-photos' );

		return $output;
	}

	/**
	 * Display the "Photos" block
	 *
	 * @param array $atts
	 * @return string
	 */
	public function gallery_photos_content( $atts = array() ) {
		/** There is possible to use 'shortcode_atts_ultimatemember_gallery_photos' filter for getting customized $atts. This filter is documented in wp-includes/shortcodes.php "shortcode_atts_{$shortcode}" */
		$atts = shortcode_atts(
			array(
				'user_id' => um_profile_id(),
			),
			$atts,
			'ultimatemember_gallery_photos'
		);

		// phpcs:ignore WordPress.Security.NonceVerification
		$user_id        = ! empty( $_POST['user_id'] ) ? absint( $_POST['user_id'] ) : absint( $atts['user_id'] );
		$user_role      = UM()->roles()->get_priority_user_role( $user_id );
		$user_role_data = UM()->roles()->role_data( $user_role );

		if ( empty( $user_role_data['enable_user_photos'] ) ) {
			return '<p class="text-center">' . esc_html__( 'Nothing to display', 'um-user-photos' ) . '</p>';
		}

		$is_my_profile = ( is_user_logged_in() && get_current_user_id() === $user_id );
		if ( ! $is_my_profile && ! um_can_view_profile( $user_id ) ) {
			return '<p class="text-center">' . esc_html__( 'Nothing to display', 'um-user-photos' ) . '</p>';
		}

		$images_column = UM()->options()->get( 'um_user_photos_images_column' );
		if ( ! $images_column ) {
			$images_column = 'um-user-photos-col-3';
		}
		$images_row = UM()->options()->get( 'um_user_photos_images_row' );
		if ( ! $images_row ) {
			$images_row = 2;
		}
		$columns  = absint( substr( $images_column, -1 ) );
		$rows     = absint( $images_row );
		$per_page = $columns * $rows;

		$query_args = array(
			'post_type'      => 'attachment',
			'author__in'     => array( $user_id ),
			'post_status'    => 'inherit',
			'post_mime_type' => 'image',
			'posts_per_page' => $per_page,
			'meta_query'     => array(
				array(
					'key'     => '_part_of_gallery',
					'value'   => 'yes',
					'compare' => '=',
				),
			),
			'orderby'        => 'ID',
		);

		$args = array(
			'post_type'      => 'um_user_photos',
			'author__in'     => array( $user_id ),
			'posts_per_page' => -1,
			'post_status'    => 'publish',
			'fields'         => 'ids',
		);
		$args = apply_filters( 'um_user_photo_query_args', $args, $user_id );

		$albums = new WP_Query( $args );

		if ( ! $is_my_profile ) {
			$visible_photos = array();
			if ( ! empty( $albums->posts ) ) {
				foreach ( $albums->posts as $album_id ) {
					$photos_query_args  = array(
						'post_type'      => 'attachment',
						'author__in'     => array( $user_id ),
						'post_status'    => 'inherit',
						'post_mime_type' => 'image',
						'posts_per_page' => -1,
						'meta_query'     => array(
							array(
								'key'     => '_part_of_gallery',
								'value'   => 'yes',
								'compare' => '=',
							),
						),
						'orderby'        => 'ID',
						'post_parent'    => $album_id,
						'fields'         => 'ids',
					);
					$album_photos_query = new WP_Query( $photos_query_args );
					$photos_result      = $album_photos_query->get_posts();

					if ( ! empty( $photos_result ) ) {
						$visible_photos[] = $photos_result;
					}
				}

				$visible_photos = array_merge( ...$visible_photos );
				$visible_photos = array_unique( $visible_photos );
			}

			if ( empty( $visible_photos ) ) {
				return '';
			}

			$query_args['post__in'] = $visible_photos;
		}

		// Disable posts query filter by the taxonomy 'language'. Integration with the plugin 'Polylang'.
		add_action( 'pre_get_posts', array( UM()->User_Photos()->common()->query(), 'remove_language_filter' ), 9 );

		$latest_photos = new WP_Query( $query_args );

		if ( ! $latest_photos->have_posts() ) {
			return '';
		}

		$count = $latest_photos->found_posts;

		$photos = array();
		foreach ( $latest_photos->posts as $photo ) {
			$photos[] = $photo->ID;
		}

		$output = '';
		if ( $is_my_profile || um_is_myprofile() ) {
			$this->modal_template();
		}

		$args_t  = compact( 'columns', 'count', 'is_my_profile', 'per_page', 'photos', 'user_id' );
		$output .= UM()->get_template( 'photos.php', UM_USER_PHOTOS_PLUGIN, $args_t );

		wp_enqueue_script( 'um-user-photos' );
		wp_enqueue_style( 'um-user-photos' );

		return $output;
	}

	/**
	 * Print modal block once
	 *
	 * @since   2.0.6
	 * @version 2.0.9  The modal is moved to the footer to avoid conflict with All in One SEO
	 *
	 * @staticvar boolean $is_printed
	 */
	public function modal_template() {
		static $is_printed = false;
		if ( ! $is_printed ) {
			$is_printed = true;
			add_action(
				'wp_footer',
				function() {
					UM()->get_template( 'modal/modal.php', UM_USER_PHOTOS_PLUGIN, array(), true );
				}
			);
		}
	}
}
