<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="ExpandRulesetReferenceInternalTest" xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/PHPCSStandards/PHP_CodeSniffer/master/phpcs.xsd">

    <config name="installed_paths" value="./tests/Core/Ruleset/Fixtures/Internal/"/>

    <rule ref="Internal.NoCodeFound">
        <severity>0</severity>
    </rule>

    <!-- While this references a valid sniff category, it will be ignored anyway as the ref starts with "Internal". -->
    <rule ref="Internal.Valid"/>

    <!-- Prevent a "no sniff were registered" error. -->
    <rule ref="Generic.PHP.BacktickOperator"/>
</ruleset>
