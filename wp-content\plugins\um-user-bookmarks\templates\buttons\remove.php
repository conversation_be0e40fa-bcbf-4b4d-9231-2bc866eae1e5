<?php
/**
 * Template for displaying the button "Remove bookmark"
 *
 * Used:  Any page with the button "Remove bookmark"
 * Call:  UM()->User_Bookmarks()->get_button( $button_type );
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-bookmarks/buttons/remove.php
 *
 * @see      https://docs.ultimatemember.com/article/1516-templates-map
 * @package  um_ext\um_user_bookmarks\templates
 * @version  2.1.1
 *
 * @var  string $icon
 * @var  int    $post_id
 * @var  int    $profile_id
 * @var  string $text
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
$data  = '';
$class = '';
if ( ! empty( $post_id ) ) {
	$data      = 'data-post="' . esc_attr( $post_id ) . '"';
	$action_id = $post_id;
}
if ( ! empty( $profile_id ) ) {
	$data      = 'data-profile="' . esc_attr( $profile_id ) . '"';
	$class     = 'um-button um-alt';
	$action_id = $profile_id;
}
?>

<div class="um-clear">
	<a href="javascript:void(0);" class="um-user-bookmarks-button um-user-bookmarks-remove-button <?php echo wp_kses( $class, UM()->get_allowed_html( 'templates' ) ); ?>"
		<?php echo wp_kses( $data, UM()->get_allowed_html( 'templates' ) ); ?>
		data-nonce="<?php echo esc_attr( wp_create_nonce( 'um_user_bookmarks_remove_' . $action_id ) ); ?>">
		<i class="<?php echo esc_attr( $icon ); ?>"></i>
		<?php if ( ! empty( $text ) ) { ?>
			<span class="text"><?php echo esc_html( $text ); ?></span>
		<?php } ?>
	</a>
</div>
