div.wpforms-container div.wpforms-uploader {
  border: 1px dashed #ccc;
  border-radius: 2px;
  background: #fcfcfc;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  cursor: pointer;
}

div.wpforms-container div.wpforms-uploader .dz-message {
  display: flex;
  flex-direction: column;
  text-align: center;
  align-items: center;
  font-size: 15px;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.7);
}

div.wpforms-container div.wpforms-uploader .dz-message + .dz-preview {
  padding-top: 20px;
}

div.wpforms-container div.wpforms-uploader .dz-message.hide {
  display: none;
}

div.wpforms-container div.wpforms-uploader .dz-message.hide + .dz-preview {
  padding-top: 0;
}

div.wpforms-container div.wpforms-uploader .dz-message .modern-hint {
  color: rgba(0, 0, 0, 0.4);
}

div.wpforms-container div.wpforms-uploader .dz-message svg {
  width: 40px;
  height: 40px;
  margin-bottom: 5px;
  opacity: 0.5;
}

div.wpforms-container div.wpforms-uploader .dz-preview {
  display: flex;
  position: relative;
  flex-wrap: wrap;
  width: 100%;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f1f1f1;
  cursor: default;
}

div.wpforms-container div.wpforms-uploader .dz-preview:last-of-type {
  margin-bottom: 0;
}

div.wpforms-container div.wpforms-uploader .dz-preview.dz-error .dz-image {
  border-color: #990000;
}

div.wpforms-container div.wpforms-uploader .dz-preview.dz-error .dz-remove {
  position: absolute;
  inset-inline-end: 0;
  text-indent: -9999999px;
  width: 20px;
  height: 20px;
  opacity: 0.5;
}

div.wpforms-container div.wpforms-uploader .dz-preview.dz-error .dz-remove:after, div.wpforms-container div.wpforms-uploader .dz-preview.dz-error .dz-remove:before {
  background-color: #ff0000;
}

div.wpforms-container div.wpforms-uploader .dz-preview.dz-processing .dz-progress {
  display: block;
}

div.wpforms-container div.wpforms-uploader .dz-preview.dz-processing .dz-details .dz-size {
  opacity: 0;
}

div.wpforms-container div.wpforms-uploader .dz-preview.dz-complete .dz-progress {
  display: none;
}

div.wpforms-container div.wpforms-uploader .dz-preview.dz-complete .dz-details .dz-size {
  opacity: 1;
}

div.wpforms-container div.wpforms-uploader .dz-preview .dz-details {
  word-break: break-word;
  margin-inline-end: 25px;
  color: #888;
}

div.wpforms-container div.wpforms-uploader .dz-preview .dz-details .dz-size {
  font-size: 12px;
}

div.wpforms-container div.wpforms-uploader .dz-preview .dz-details .dz-filename {
  font-size: 14px;
}

div.wpforms-container div.wpforms-uploader .dz-preview .dz-image {
  margin-inline-end: 20px;
  width: 50px;
  height: 50px;
  border: 1px solid #ddd;
  overflow: hidden;
  position: relative;
  display: block;
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4gIDwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+IDxzdmcgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiB2aWV3Qm94PSIwIDAgMzIgMzIiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIGZpbGw9IiMwMDAwMDAwZiI+PGc+PHBhdGggZD0iTSA0LDMybCAyNCwwIGMgMS4xMDQsMCwgMi0wLjg5NiwgMi0yTCAzMCwyIGMwLTEuMTA0LTAuODk2LTItMi0yTCA0LDAgQyAyLjg5NiwwLCAyLDAuODk2LCAyLDJsMCwyOCBDIDIsMzEuMTA0LCAyLjg5NiwzMiwgNCwzMnogTSA0LDJsIDI0LDAgbDAsMjggTCA0LDMwIEwgNCwyIHpNIDIzLDZsLTgsMCBDIDE0LjQ0OCw2LCAxNCw2LjQ0OCwgMTQsN0MgMTQsNy41NTIsIDE0LjQ0OCw4LCAxNSw4bCA4LDAgQyAyMy41NTIsOCwgMjQsNy41NTIsIDI0LDcgQyAyNCw2LjQ0OCwgMjMuNTUyLDYsIDIzLDZ6TSAyMywxMmwtMTQsMCBDIDguNDQ4LDEyLCA4LDEyLjQ0OCwgOCwxM0MgOCwxMy41NTIsIDguNDQ4LDE0LCA5LDE0bCAxNCwwIEMgMjMuNTUyLDE0LCAyNCwxMy41NTIsIDI0LDEzIEMgMjQsMTIuNDQ4LCAyMy41NTIsMTIsIDIzLDEyek0gMjMsMThsLTE0LDAgQyA4LjQ0OCwxOCwgOCwxOC40NDgsIDgsMTlDIDgsMTkuNTUyLCA4LjQ0OCwyMCwgOSwyMGwgMTQsMCBjIDAuNTUyLDAsIDEtMC40NDgsIDEtMSBDIDI0LDE4LjQ0OCwgMjMuNTUyLDE4LCAyMywxOHpNIDIzLDI0bC0xNCwwIEMgOC40NDgsMjQsIDgsMjQuNDQ4LCA4LDI1QyA4LDI1LjU1MiwgOC40NDgsMjYsIDksMjZsIDE0LDAgYyAwLjU1MiwwLCAxLTAuNDQ4LCAxLTEgQyAyNCwyNC40NDgsIDIzLjU1MiwyNCwgMjMsMjR6Ij48L3BhdGg+PC9nPjwvc3ZnPg==);
  background-repeat: no-repeat;
  background-position: center;
  background-color: #fff;
}

div.wpforms-container div.wpforms-uploader .dz-preview .dz-image img {
  min-width: 50px;
  max-width: 50px;
  display: block;
}

div.wpforms-container div.wpforms-uploader .dz-preview .dz-success-mark,
div.wpforms-container div.wpforms-uploader .dz-preview .dz-error-mark {
  display: none;
}

div.wpforms-container div.wpforms-uploader .dz-preview .dz-progress {
  display: block;
  position: absolute;
  bottom: 10px;
  width: 100%;
  height: 5px;
  border: 0;
  background-color: #ddd;
}

div.wpforms-container div.wpforms-uploader .dz-preview .dz-progress .dz-upload {
  display: block;
  height: 5px;
  background-color: #999;
}

div.wpforms-container div.wpforms-uploader .dz-preview .dz-error-message {
  flex-basis: 100%;
  font-size: 12px;
  color: #990000;
}

div.wpforms-container div.wpforms-uploader .dz-preview .dz-error-message.dz-error {
  margin-top: 16px;
}

div.wpforms-container div.wpforms-uploader .dz-preview .dz-error-message span:not(:empty) {
  display: block;
  padding-top: 10px;
}

div.wpforms-container div.wpforms-uploader .dz-preview .dz-remove {
  position: absolute;
  inset-inline-end: 0;
  text-indent: -9999999px;
  width: 20px;
  height: 20px;
  opacity: 0.5;
}

div.wpforms-container div.wpforms-uploader .dz-preview .dz-remove:hover {
  opacity: 1;
}

div.wpforms-container div.wpforms-uploader .dz-preview .dz-remove:after, div.wpforms-container div.wpforms-uploader .dz-preview .dz-remove:before {
  content: '';
  height: 14px;
  width: 2px;
  background-color: #666;
  position: absolute;
  display: block;
  top: 0;
  inset-inline-end: 10px;
}

div.wpforms-container div.wpforms-uploader .dz-preview .dz-remove:after {
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

div.wpforms-container div.wpforms-uploader .dz-preview .dz-remove:before {
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

div.wpforms-container .wpforms-field-file-upload.wpforms-has-error .wpforms-uploader {
  border-color: #cc0000;
  border-style: solid;
}

div.wpforms-container .wpforms-submit-overlay-container {
  position: relative;
}

div.wpforms-container .wpforms-submit-overlay-container .wpforms-submit-overlay {
  top: 0;
  background: transparent;
  position: absolute;
  opacity: 0;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader {
  border: 1px dashed #ccc;
  border-radius: 2px;
  background: #fcfcfc;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  cursor: pointer;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-message {
  display: flex;
  flex-direction: column;
  text-align: center;
  align-items: center;
  font-size: 15px;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.7);
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-message + .dz-preview {
  padding-top: 20px;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-message.hide {
  display: none;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-message.hide + .dz-preview {
  padding-top: 0;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-message .modern-hint {
  color: rgba(0, 0, 0, 0.4);
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-message svg {
  width: 40px;
  height: 40px;
  margin-bottom: 5px;
  opacity: 0.5;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview {
  display: flex;
  position: relative;
  flex-wrap: wrap;
  width: 100%;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f1f1f1;
  cursor: default;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview:last-of-type {
  margin-bottom: 0;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview.dz-error .dz-image {
  border-color: #990000;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview.dz-error .dz-remove {
  position: absolute;
  inset-inline-end: 0;
  text-indent: -9999999px;
  width: 20px;
  height: 20px;
  opacity: 0.5;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview.dz-error .dz-remove:after, .et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview.dz-error .dz-remove:before {
  background-color: #ff0000;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview.dz-processing .dz-progress {
  display: block;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview.dz-processing .dz-details .dz-size {
  opacity: 0;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview.dz-complete .dz-progress {
  display: none;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview.dz-complete .dz-details .dz-size {
  opacity: 1;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview .dz-details {
  word-break: break-word;
  margin-inline-end: 25px;
  color: #888;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview .dz-details .dz-size {
  font-size: 12px;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview .dz-details .dz-filename {
  font-size: 14px;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview .dz-image {
  margin-inline-end: 20px;
  width: 50px;
  height: 50px;
  border: 1px solid #ddd;
  overflow: hidden;
  position: relative;
  display: block;
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4gIDwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+IDxzdmcgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiB2aWV3Qm94PSIwIDAgMzIgMzIiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIGZpbGw9IiMwMDAwMDAwZiI+PGc+PHBhdGggZD0iTSA0LDMybCAyNCwwIGMgMS4xMDQsMCwgMi0wLjg5NiwgMi0yTCAzMCwyIGMwLTEuMTA0LTAuODk2LTItMi0yTCA0LDAgQyAyLjg5NiwwLCAyLDAuODk2LCAyLDJsMCwyOCBDIDIsMzEuMTA0LCAyLjg5NiwzMiwgNCwzMnogTSA0LDJsIDI0LDAgbDAsMjggTCA0LDMwIEwgNCwyIHpNIDIzLDZsLTgsMCBDIDE0LjQ0OCw2LCAxNCw2LjQ0OCwgMTQsN0MgMTQsNy41NTIsIDE0LjQ0OCw4LCAxNSw4bCA4LDAgQyAyMy41NTIsOCwgMjQsNy41NTIsIDI0LDcgQyAyNCw2LjQ0OCwgMjMuNTUyLDYsIDIzLDZ6TSAyMywxMmwtMTQsMCBDIDguNDQ4LDEyLCA4LDEyLjQ0OCwgOCwxM0MgOCwxMy41NTIsIDguNDQ4LDE0LCA5LDE0bCAxNCwwIEMgMjMuNTUyLDE0LCAyNCwxMy41NTIsIDI0LDEzIEMgMjQsMTIuNDQ4LCAyMy41NTIsMTIsIDIzLDEyek0gMjMsMThsLTE0LDAgQyA4LjQ0OCwxOCwgOCwxOC40NDgsIDgsMTlDIDgsMTkuNTUyLCA4LjQ0OCwyMCwgOSwyMGwgMTQsMCBjIDAuNTUyLDAsIDEtMC40NDgsIDEtMSBDIDI0LDE4LjQ0OCwgMjMuNTUyLDE4LCAyMywxOHpNIDIzLDI0bC0xNCwwIEMgOC40NDgsMjQsIDgsMjQuNDQ4LCA4LDI1QyA4LDI1LjU1MiwgOC40NDgsMjYsIDksMjZsIDE0LDAgYyAwLjU1MiwwLCAxLTAuNDQ4LCAxLTEgQyAyNCwyNC40NDgsIDIzLjU1MiwyNCwgMjMsMjR6Ij48L3BhdGg+PC9nPjwvc3ZnPg==);
  background-repeat: no-repeat;
  background-position: center;
  background-color: #fff;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview .dz-image img {
  min-width: 50px;
  max-width: 50px;
  display: block;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview .dz-success-mark,
.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview .dz-error-mark {
  display: none;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview .dz-progress {
  display: block;
  position: absolute;
  bottom: 10px;
  width: 100%;
  height: 5px;
  border: 0;
  background-color: #ddd;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview .dz-progress .dz-upload {
  display: block;
  height: 5px;
  background-color: #999;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview .dz-error-message {
  flex-basis: 100%;
  font-size: 12px;
  color: #990000;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview .dz-error-message.dz-error {
  margin-top: 16px;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview .dz-error-message span:not(:empty) {
  display: block;
  padding-top: 10px;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview .dz-remove {
  position: absolute;
  inset-inline-end: 0;
  text-indent: -9999999px;
  width: 20px;
  height: 20px;
  opacity: 0.5;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview .dz-remove:hover {
  opacity: 1;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview .dz-remove:after, .et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview .dz-remove:before {
  content: '';
  height: 14px;
  width: 2px;
  background-color: #666;
  position: absolute;
  display: block;
  top: 0;
  inset-inline-end: 10px;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview .dz-remove:after {
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container div.wpforms-uploader .dz-preview .dz-remove:before {
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container .wpforms-field-file-upload.wpforms-has-error .wpforms-uploader {
  border-color: #cc0000;
  border-style: solid;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container .wpforms-submit-overlay-container {
  position: relative;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container .wpforms-submit-overlay-container .wpforms-submit-overlay {
  top: 0;
  background: transparent;
  position: absolute;
  opacity: 0;
}
