"use strict";var WPMailSMTPAdminNotices=window.WPMailSMTPAdminNotices||function(s){var i={init:function(){s(i.ready)},ready:function(){i.events()},events:function(){s(".wp-mail-smtp-notice.is-dismissible").on("click",".notice-dismiss",i.dismiss)},dismiss:function(i){var n,t=s(this).closest(".wp-mail-smtp-notice");void 0!==t.data("notice")&&(n=s(this),s.ajax({url:ajaxurl,dataType:"json",type:"POST",data:{action:"wp_mail_smtp_ajax",nonce:wp_mail_smtp_admin_notices.nonce,task:"notice_dismiss",notice:t.data("notice")},beforeSend:function(){n.prop("disabled",!0)}}))}};return i}((document,window,jQuery));WPMailSMTPAdminNotices.init();