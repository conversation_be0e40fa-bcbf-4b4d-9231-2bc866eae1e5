<?php
/**
 * Template for the UM User Photos, The "Photos" block
 *
 * Page: "Profile", tab "Photos"
 * Caller: User_Photos_Shortcodes->gallery_photos_content() method
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/photos.php
 * @var int   $columns
 * @var int   $count
 * @var int   $per_page
 * @var array $photos
 * @var int   $user_id
 * @var bool  $is_my_profile
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! empty( $photos ) && is_array( $photos ) ) {
	?>
	<div class="um-user-photos-albums" data-count="<?php echo esc_attr( count( $photos ) ); ?>">
		<div class="photos-container">
			<div class="um-user-photos-single-album um-up-grid um-up-grid-col-<?php echo esc_attr( $columns ); ?>">
				<?php
				$args_t = compact( 'is_my_profile', 'photos' );
				UM()->get_template( 'photos-grid.php', UM_USER_PHOTOS_PLUGIN, $args_t, true );
				?>
			</div>
		</div>

		<?php if ( $count > $per_page ) { ?>
			<div class="um-load-more">
				<div class="um-clear">
					<hr/>
				</div>
				<p class="text-center">
				<button id="um-user-photos-toggle-view-photos-load-more" data-wpnonce="<?php echo esc_attr( wp_create_nonce( 'um_user_photos_load_more' ) ); ?>" class="um-modal-btn alt" data-current_page="1" data-per_page="<?php echo esc_attr( $per_page ); ?>" data-profile="<?php echo esc_attr( $user_id ); ?>" data-count="<?php echo esc_attr( $count ); ?>"><?php esc_html_e( 'Load more', 'um-user-photos' ); ?></button>
				</p>
			</div>
		<?php } ?>

		<div class="um-clear"></div>
	</div>

	<?php
} else {
	?>
	<p class="text-center"><?php esc_html_e( 'Nothing to display', 'um-user-photos' ); ?></p>
	<?php
}
