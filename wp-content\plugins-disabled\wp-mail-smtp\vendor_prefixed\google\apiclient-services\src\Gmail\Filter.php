<?php

/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
namespace WPMailSMTP\Vendor\Google\Service\Gmail;

class Filter extends \WPMailSMTP\Vendor\Google\Model
{
    protected $actionType = \WPMailSMTP\Vendor\Google\Service\Gmail\FilterAction::class;
    protected $actionDataType = '';
    protected $criteriaType = \WPMailSMTP\Vendor\Google\Service\Gmail\FilterCriteria::class;
    protected $criteriaDataType = '';
    /**
     * @var string
     */
    public $id;
    /**
     * @param FilterAction
     */
    public function setAction(\WPMailSMTP\Vendor\Google\Service\Gmail\FilterAction $action)
    {
        $this->action = $action;
    }
    /**
     * @return FilterAction
     */
    public function getAction()
    {
        return $this->action;
    }
    /**
     * @param FilterCriteria
     */
    public function setCriteria(\WPMailSMTP\Vendor\Google\Service\Gmail\FilterCriteria $criteria)
    {
        $this->criteria = $criteria;
    }
    /**
     * @return FilterCriteria
     */
    public function getCriteria()
    {
        return $this->criteria;
    }
    /**
     * @param string
     */
    public function setId($id)
    {
        $this->id = $id;
    }
    /**
     * @return string
     */
    public function getId()
    {
        return $this->id;
    }
}
// Adding a class alias for backwards compatibility with the previous class name.
\class_alias(\WPMailSMTP\Vendor\Google\Service\Gmail\Filter::class, 'WPMailSMTP\\Vendor\\Google_Service_Gmail_Filter');
