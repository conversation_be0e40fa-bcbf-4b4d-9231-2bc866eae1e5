<?php

/*
 * Copyright 2015 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace WPMailSMTP\Vendor\Google\Auth\Middleware;

use WPMailSMTP\Vendor\Google\Auth\FetchAuthTokenInterface;
use W<PERSON>ailSMTP\Vendor\Google\Auth\GetQuotaProjectInterface;
use WPMailSMTP\Vendor\Psr\Http\Message\RequestInterface;
/**
 * ProxyAuthTokenMiddleware is a Guzzle Middleware that adds an Authorization header
 * provided by an object implementing FetchAuthTokenInterface.
 *
 * The FetchAuthTokenInterface#fetchAuthToken is used to obtain a hash; one of
 * the values value in that hash is added as the authorization header.
 *
 * Requests will be accessed with the authorization header:
 *
 * 'proxy-authorization' 'Bearer <value of auth_token>'
 */
class ProxyAuthTokenMiddleware
{
    /**
     * @var callable
     */
    private $httpHandler;
    /**
     * @var FetchAuthTokenInterface
     */
    private $fetcher;
    /**
     * @var ?callable
     */
    private $tokenCallback;
    /**
     * Creates a new ProxyAuthTokenMiddleware.
     *
     * @param FetchAuthTokenInterface $fetcher is used to fetch the auth token
     * @param callable $httpHandler (optional) callback which delivers psr7 request
     * @param callable $tokenCallback (optional) function to be called when a new token is fetched.
     */
    public function __construct(\WPMailSMTP\Vendor\Google\Auth\FetchAuthTokenInterface $fetcher, callable $httpHandler = null, callable $tokenCallback = null)
    {
        $this->fetcher = $fetcher;
        $this->httpHandler = $httpHandler;
        $this->tokenCallback = $tokenCallback;
    }
    /**
     * Updates the request with an Authorization header when auth is 'google_auth'.
     *
     *   use Google\Auth\Middleware\ProxyAuthTokenMiddleware;
     *   use Google\Auth\OAuth2;
     *   use GuzzleHttp\Client;
     *   use GuzzleHttp\HandlerStack;
     *
     *   $config = [..<oauth config param>.];
     *   $oauth2 = new OAuth2($config)
     *   $middleware = new ProxyAuthTokenMiddleware($oauth2);
     *   $stack = HandlerStack::create();
     *   $stack->push($middleware);
     *
     *   $client = new Client([
     *       'handler' => $stack,
     *       'base_uri' => 'https://www.googleapis.com/taskqueue/v1beta2/projects/',
     *       'proxy_auth' => 'google_auth' // authorize all requests
     *   ]);
     *
     *   $res = $client->get('myproject/taskqueues/myqueue');
     *
     * @param callable $handler
     * @return \Closure
     */
    public function __invoke(callable $handler)
    {
        return function (\WPMailSMTP\Vendor\Psr\Http\Message\RequestInterface $request, array $options) use($handler) {
            // Requests using "proxy_auth"="google_auth" will be authorized.
            if (!isset($options['proxy_auth']) || $options['proxy_auth'] !== 'google_auth') {
                return $handler($request, $options);
            }
            $request = $request->withHeader('proxy-authorization', 'Bearer ' . $this->fetchToken());
            if ($quotaProject = $this->getQuotaProject()) {
                $request = $request->withHeader(\WPMailSMTP\Vendor\Google\Auth\GetQuotaProjectInterface::X_GOOG_USER_PROJECT_HEADER, $quotaProject);
            }
            return $handler($request, $options);
        };
    }
    /**
     * Call fetcher to fetch the token.
     *
     * @return string|null
     */
    private function fetchToken()
    {
        $auth_tokens = $this->fetcher->fetchAuthToken($this->httpHandler);
        if (\array_key_exists('access_token', $auth_tokens)) {
            // notify the callback if applicable
            if ($this->tokenCallback) {
                \call_user_func($this->tokenCallback, $this->fetcher->getCacheKey(), $auth_tokens['access_token']);
            }
            return $auth_tokens['access_token'];
        }
        if (\array_key_exists('id_token', $auth_tokens)) {
            return $auth_tokens['id_token'];
        }
        return null;
    }
    /**
     * @return string|null;
     */
    private function getQuotaProject()
    {
        if ($this->fetcher instanceof \WPMailSMTP\Vendor\Google\Auth\GetQuotaProjectInterface) {
            return $this->fetcher->getQuotaProject();
        }
        return null;
    }
}
