<?php
// Test WordPress database connection
echo "<h1>WordPress Database Test</h1>";

// Database configuration from wp-config.php
$db_name = 'raezg';
$db_user = 'root';
$db_password = '';
$db_host = 'localhost';

echo "<h2>1. Testing MySQL Connection</h2>";
try {
    $pdo = new PDO("mysql:host=$db_host", $db_user, $db_password);
    echo "✓ MySQL connection successful<br>";
} catch (PDOException $e) {
    echo "✗ MySQL connection failed: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>2. Testing Database Access</h2>";
try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_password);
    echo "✓ Database '$db_name' connection successful<br>";
} catch (PDOException $e) {
    echo "✗ Database connection failed: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>3. Checking WordPress Tables</h2>";
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'BTx_%'");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    if (count($tables) > 0) {
        echo "✓ Found " . count($tables) . " WordPress tables<br>";
        foreach ($tables as $table) {
            echo "- $table<br>";
        }
    } else {
        echo "✗ No WordPress tables found<br>";
    }
} catch (PDOException $e) {
    echo "✗ Error checking tables: " . $e->getMessage() . "<br>";
}

echo "<h2>4. Checking WordPress Options</h2>";
try {
    $stmt = $pdo->prepare("SELECT option_name, option_value FROM BTx_options WHERE option_name IN ('siteurl', 'home')");
    $stmt->execute();
    $options = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($options) > 0) {
        echo "✓ WordPress URL options:<br>";
        foreach ($options as $option) {
            echo "- {$option['option_name']}: {$option['option_value']}<br>";
        }
    } else {
        echo "✗ No WordPress URL options found<br>";
    }
} catch (PDOException $e) {
    echo "✗ Error checking options: " . $e->getMessage() . "<br>";
}

echo "<h2>5. WordPress Constants</h2>";
echo "WP_HOME: " . (defined('WP_HOME') ? WP_HOME : 'Not defined') . "<br>";
echo "WP_SITEURL: " . (defined('WP_SITEURL') ? WP_SITEURL : 'Not defined') . "<br>";

echo "<h2>6. Current Request Info</h2>";
echo "Request URL: " . (isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : 'Unknown') . "<br>";
echo "HTTP Host: " . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'Unknown') . "<br>";
echo "Server Name: " . (isset($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : 'Unknown') . "<br>";

echo "<h2>Test Complete</h2>";
echo "If you can see this page, the basic PHP and database connections are working.";
?>
