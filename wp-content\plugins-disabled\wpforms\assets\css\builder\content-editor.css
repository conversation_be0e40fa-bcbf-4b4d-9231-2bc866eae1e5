.mce-content-body.wpforms-content-field-editor-body {
  font-family: sans-serif;
  color: #444444;
  background-color: #ffffff;
  word-break: break-word;
  font-size: 16px;
  line-height: 22px;
}

.mce-content-body.wpforms-content-field-editor-body h1, .mce-content-body.wpforms-content-field-editor-body h2, .mce-content-body.wpforms-content-field-editor-body h3, .mce-content-body.wpforms-content-field-editor-body h4, .mce-content-body.wpforms-content-field-editor-body h5, .mce-content-body.wpforms-content-field-editor-body h6 {
  margin: 20px 0;
  padding: 0;
  clear: unset;
}

.mce-content-body.wpforms-content-field-editor-body h1:first-child, .mce-content-body.wpforms-content-field-editor-body h2:first-child, .mce-content-body.wpforms-content-field-editor-body h3:first-child, .mce-content-body.wpforms-content-field-editor-body h4:first-child, .mce-content-body.wpforms-content-field-editor-body h5:first-child, .mce-content-body.wpforms-content-field-editor-body h6:first-child {
  margin-top: 0;
}

.mce-content-body.wpforms-content-field-editor-body h1 {
  font-size: 32px;
  line-height: 40px;
}

.mce-content-body.wpforms-content-field-editor-body h2 {
  font-size: 28px;
  line-height: 36px;
}

.mce-content-body.wpforms-content-field-editor-body h3 {
  font-size: 24px;
  line-height: 32px;
}

.mce-content-body.wpforms-content-field-editor-body h4 {
  font-size: 20px;
  line-height: 28px;
}

.mce-content-body.wpforms-content-field-editor-body h5 {
  font-size: 18px;
  line-height: 26px;
}

.mce-content-body.wpforms-content-field-editor-body h6 {
  font-size: 16px;
  line-height: 24px;
  text-transform: uppercase;
}

.mce-content-body.wpforms-content-field-editor-body p, .mce-content-body.wpforms-content-field-editor-body blockquote, .mce-content-body.wpforms-content-field-editor-body pre, .mce-content-body.wpforms-content-field-editor-body table {
  margin: 0 0 20px 0;
}

.mce-content-body.wpforms-content-field-editor-body li {
  margin: 0 0 10px 0;
}

.mce-content-body.wpforms-content-field-editor-body a {
  text-decoration: underline;
}

.mce-content-body.wpforms-content-field-editor-body a:hover {
  text-decoration: none;
}

.mce-content-body.wpforms-content-field-editor-body code, .mce-content-body.wpforms-content-field-editor-body pre {
  font-family: monospace;
  overflow: auto;
}

.mce-content-body.wpforms-content-field-editor-body del {
  text-decoration: line-through;
}

.mce-content-body.wpforms-content-field-editor-body ins {
  text-decoration: underline;
}

.mce-content-body.wpforms-content-field-editor-body small {
  font-size: smaller;
}

.mce-content-body.wpforms-content-field-editor-body dt {
  margin: 5px 0;
}

.mce-content-body.wpforms-content-field-editor-body dd {
  margin-left: 25px;
}

.mce-content-body.wpforms-content-field-editor-body abbr, .mce-content-body.wpforms-content-field-editor-body acronym {
  text-decoration: underline dotted;
}

.mce-content-body.wpforms-content-field-editor-body ul {
  list-style: disc outside none !important;
  padding-inline-start: 29px !important;
  margin-bottom: 20px !important;
}

.mce-content-body.wpforms-content-field-editor-body ul ul {
  list-style-type: circle !important;
  margin-top: 10px !important;
  margin-bottom: 0 !important;
}

.mce-content-body.wpforms-content-field-editor-body ul ul ul {
  list-style-type: square !important;
}

.mce-content-body.wpforms-content-field-editor-body ul ol {
  margin-top: 10px;
  margin-bottom: 0;
}

.mce-content-body.wpforms-content-field-editor-body ul li {
  list-style: inherit !important;
  margin-bottom: 10px !important;
}

.mce-content-body.wpforms-content-field-editor-body ol {
  list-style: decimal outside none;
  padding-inline-start: 29px;
  margin-bottom: 20px;
}

.mce-content-body.wpforms-content-field-editor-body ol ol {
  margin-top: 10px;
  margin-bottom: 0;
}

.mce-content-body.wpforms-content-field-editor-body ol ul {
  margin-top: 10px !important;
  margin-bottom: 0 !important;
}

.mce-content-body.wpforms-content-field-editor-body ol li {
  list-style: inherit;
}

.mce-content-body.wpforms-content-field-editor-body blockquote {
  border-left: 4px solid rgba(0, 0, 0, 0.15);
  padding-left: 20px;
}

.mce-content-body.wpforms-content-field-editor-body blockquote:before, .mce-content-body.wpforms-content-field-editor-body blockquote:after {
  display: none;
}

.mce-content-body.wpforms-content-field-editor-body table {
  width: 100%;
  border-collapse: collapse;
  word-break: normal;
}

.mce-content-body.wpforms-content-field-editor-body table th, .mce-content-body.wpforms-content-field-editor-body table td {
  padding: 0.5em;
  border: 1px solid;
}

.mce-content-body.wpforms-content-field-editor-body sup, .mce-content-body.wpforms-content-field-editor-body sub {
  font-size: smaller;
  line-height: calc( 100% + 11px);
}

.mce-content-body.wpforms-content-field-editor-body sup {
  vertical-align: super;
}

.mce-content-body.wpforms-content-field-editor-body sub {
  vertical-align: sub;
}

.mce-content-body.wpforms-content-field-editor-body h1, .mce-content-body.wpforms-content-field-editor-body h2, .mce-content-body.wpforms-content-field-editor-body h3, .mce-content-body.wpforms-content-field-editor-body h4, .mce-content-body.wpforms-content-field-editor-body h5, .mce-content-body.wpforms-content-field-editor-body h6 {
  margin: 10px 0;
  font-weight: 600;
}

.mce-content-body.wpforms-content-field-editor-body p {
  line-height: inherit;
}

.mce-content-body.wpforms-content-field-editor-body img {
  vertical-align: top;
}

.mce-content-body.wpforms-content-field-editor-body img {
  max-width: 100%;
  height: auto;
}

.mce-content-body.wpforms-content-field-editor-body .alignleft {
  float: left;
  margin: 0 30px 20px 0;
}

.mce-content-body.wpforms-content-field-editor-body .alignright {
  float: right;
  margin: 0 0 20px 30px;
}

.mce-content-body.wpforms-content-field-editor-body .aligncenter {
  display: block;
  clear: both;
  text-align: center;
  margin: 0 auto 20px;
}

.mce-content-body.wpforms-content-field-editor-body .alignnone {
  display: block;
  clear: both;
  margin: 0 0 20px 0;
}

.mce-content-body.wpforms-content-field-editor-body .wp-caption-dt,
.mce-content-body.wpforms-content-field-editor-body .wp-caption-dd {
  margin: 0;
}

.mce-content-body.wpforms-content-field-editor-body .wp-caption {
  position: relative;
  left: auto;
  right: auto;
  transform: none;
  max-width: 100%;
}

.mce-content-body.wpforms-content-field-editor-body .wp-caption .wp-caption-text,
.mce-content-body.wpforms-content-field-editor-body .wp-caption .wp-caption-dd {
  text-align: center;
  font-size: 14px;
  margin-top: 0.5em;
}
