<?php
namespace um_ext\um_user_photos\frontend;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Init
 *
 * @package um_ext\um_user_photos\frontend
 */
class Init {

	/**
	 * Create classes' instances where __construct isn't empty for hooks init
	 */
	public function includes() {
		$this->account();
		$this->enqueue();
		$this->profile();
	}

	/**
	 * @return Account
	 */
	public function account() {
		if ( empty( UM()->classes['um_ext\um_user_photos\frontend\account'] ) ) {
			UM()->classes['um_ext\um_user_photos\frontend\account'] = new Account();
		}
		return UM()->classes['um_ext\um_user_photos\frontend\account'];
	}

	/**
	 * @return Enqueue
	 */
	public function enqueue() {
		if ( empty( UM()->classes['um_ext\um_user_photos\frontend\enqueue'] ) ) {
			UM()->classes['um_ext\um_user_photos\frontend\enqueue'] = new Enqueue();
		}
		return UM()->classes['um_ext\um_user_photos\frontend\enqueue'];
	}

	/**
	 * @return Profile
	 */
	public function profile() {
		if ( empty( UM()->classes['um_ext\um_user_photos\frontend\profile'] ) ) {
			UM()->classes['um_ext\um_user_photos\frontend\profile'] = new Profile();
		}
		return UM()->classes['um_ext\um_user_photos\frontend\profile'];
	}
}
