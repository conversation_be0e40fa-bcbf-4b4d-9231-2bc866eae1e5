<?php
namespace um_ext\um_user_photos\common;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Profile
 *
 * @package um_ext\um_user_photos\common
 */
class Profile {

	/**
	 * Profile constructor.
	 */
	public function __construct() {
		add_filter( 'um_profile_tabs', array( $this, 'add_profile_tab' ), 800 );
		add_filter( 'um_user_profile_tabs', array( &$this, 'add_user_tab' ), 5 );
		add_filter( 'um_edit_profile_url', array( $this, 'um_edit_profile_url' ), 20 );
	}

	/**
	 * Add tab for Photos
	 *
	 * @param array $tabs
	 *
	 * @return array
	 */
	public function add_profile_tab( $tabs ) {
		$tabs['photos'] = array(
			'name' => __( 'Photos', 'um-user-photos' ),
			'icon' => 'um-icon-images',
		);

		return $tabs;
	}

	/**
	 * @param array $tabs
	 *
	 * @return array
	 */
	public function add_user_tab( $tabs ) {
		if ( empty( $tabs['photos'] ) ) {
			return $tabs;
		}

		if ( ! um_user( 'enable_user_photos' ) ) {
			unset( $tabs['photos'] );
			return $tabs;
		}

		if ( defined( 'UM_DEV_MODE' ) && UM_DEV_MODE && UM()->options()->get( 'enable_new_ui' ) ) {
			$tabs['photos']['subnav'] = array(
				'albums' => array(
					'title' => __( 'Albums', 'um-user-photos' )
				),
				'photo'  => array(
					'title' => __( 'Photos', 'um-user-photos' )
				),
			);
		} else {
			$tabs['photos']['subnav']         = array(
				'albums' => __( 'Albums', 'um-user-photos' ),
				'photo'  => __( 'Photos', 'um-user-photos' ),
			);
		}

		$tabs['photos']['subnav_default'] = 'albums';
		return $tabs;
	}

	/**
	 * Change edit profile URL.
	 *
	 * @param string $url
	 *
	 * @return string
	 */
	public function um_edit_profile_url( $url ) {
		$url = remove_query_arg( 'album_id', $url );
		$url = remove_query_arg( 'photo_id', $url );

		return $url;
	}
}
