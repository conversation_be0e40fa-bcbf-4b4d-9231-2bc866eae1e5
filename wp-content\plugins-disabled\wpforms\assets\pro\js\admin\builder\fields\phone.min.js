var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.FieldPhone=WPForms.Admin.Builder.FieldPhone||function(i){let e={};const o={init(){i(o.ready)},ready(){o.setup(),o.events()},setup(){e={$builder:i("#wpforms-builder")}},events(){e.$builder.on("change",".wpforms-field-option-phone .wpforms-field-option-row-format select",o.handleFormatChange)},handleFormatChange(){var e=i(this),o=e.closest(".wpforms-field-option-row").data("field-id");i(`#wpforms-field-${o} .wpforms-field-phone-input-container`).attr("data-format",e.val())}};return o}((document,window,jQuery)),WPForms.Admin.Builder.FieldPhone.init();