<?php
/**
 * Template for the UM User Photos, The "Photos" block
 *
 * Page: "Profile", tab "Photos"
 * Caller: User_Photos_Shortcodes->gallery_photos_content() method
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/photos.php
 * @var int   $columns
 * @var int   $total
 * @var int   $per_page
 * @var int   $user_id
 * @var array $photos
 * @var bool  $is_my_profile
 * @var bool  $is_profile_tab
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

$wrapper_classes = array( 'um-user-photos-gallery-photos' );
if ( ! $is_profile_tab ) {
	$wrapper_classes[] = 'um';
}
?>
<div class="<?php echo esc_attr( implode( ' ', $wrapper_classes ) ); ?>">
	<?php
	if ( ! empty( $photos ) && is_array( $photos ) ) {
		?>
		<div class="um-grid um-user-photos-grid um-grid-col-<?php echo esc_attr( $columns ); ?>" data-count="<?php echo esc_attr( count( $photos ) ); ?>">
			<?php
			$args_t = compact( 'is_my_profile', 'photos' );
			UM()->get_template( 'v3/photos-grid.php', UM_USER_PHOTOS_PLUGIN, $args_t, true );
			?>
		</div>

		<?php
		if ( $total > $per_page ) {
			$loader = UM()->frontend()::layouts()::ajax_loader(
				'm',
				array(
					'classes' => array(
						'um-user-photos-loader',
						'um-display-none',
					),
				)
			);
			$button = UM()->frontend()::layouts()::button(
				esc_html__( 'Load more', 'um-user-photos' ),
				array(
					'classes' => array( 'um-user-photos-load-more-photos' ),
					'design'  => 'tertiary-gray',
					'type'    => 'button',
					'size'    => 's',
					'data'    => array(
						'wpnonce'      => wp_create_nonce( 'um_user_photos_load_more' ),
						'current_page' => 1,
						'profile'      => $user_id,
						'total'        => $total,
					),
				)
			);
			echo wp_kses( $loader . $button, UM()->get_allowed_html( 'templates' ) );
		}
	} else {
		?>
		<p class="um-supporting-text um-align-center"><?php esc_html_e( 'Nothing to display', 'um-user-photos' ); ?></p>
		<?php
	}
	?>
</div>
