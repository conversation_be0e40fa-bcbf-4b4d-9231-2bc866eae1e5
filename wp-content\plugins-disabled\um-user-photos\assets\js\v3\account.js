jQuery( document ).ready( function( $ ) {

	/* <PERSON><PERSON> "Delete my all albums & photos" */
	$( document.body ).on( 'click', '#um_user_photos_download_all', function ( e ) {
		return ! $(this).hasClass('um-link-disabled');
	});

	$( document.body ).on( 'click', '#um_user_photos_delete_all', function ( e ) {
		e.preventDefault();
		let $btn = $( this );

		let nonce = $btn.data( 'nonce' );
		let text  = $btn.data( 'alert_message' );
		let redirect = $btn.data('redirect');

		if ( confirm( text ) ) {
			$btn.siblings('.um-user-photos-loader').umShow();
			$btn.prop( 'disabled', true );
			$btn.siblings('.um-alert').remove();
			$( '#um_user_photos_download_all' ).addClass('um-link-disabled');
			wp.ajax.send(
				'um_user_photos_flush_albums',
				{
					data: {
						_wpnonce: nonce,
					},
					success: function( response ) {
						$btn.siblings('.um-user-photos-loader').umHide();
						$btn.parents( '.um-user-photos-delete-wrap' ).append( response );
						$btn.remove();
						$( '#um_user_photos_download_all' ).remove();
						setTimeout( function () {
							window.location.assign( redirect );
						}, 3000 );
					},
					error: function( response ) {
						$btn.parents( '.um-user-photos-delete-wrap' ).append( response );
						$btn.prop( 'disabled', false );
						$btn.siblings('.um-user-photos-loader').umHide();
						$( '#um_user_photos_download_all' ).removeClass('um-link-disabled');
						console.log( response );
					}
				}
			);
		}
	});
});
