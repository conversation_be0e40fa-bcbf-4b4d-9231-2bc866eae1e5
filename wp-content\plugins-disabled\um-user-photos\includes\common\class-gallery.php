<?php
namespace um_ext\um_user_photos\common;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Gallery
 *
 * @package um_ext\um_user_photos\common
 */
class Gallery {

	/**
	 * @var int
	 */
	public $photos_per_page = 0;

	/**
	 * @var int
	 */
	public $albums_per_page = 12;

	/**
	 * Gallery constructor.
	 */
	public function __construct() {
	}

	/**
	 *
	 */
	public function hooks() {
		add_action( 'init', array( $this, 'prepare_variables' ) );
	}

	/**
	 *
	 */
	public function prepare_variables() {
		$columns = UM()->options()->get( 'um_user_photos_images_column' );
		if ( empty( $columns ) ) {
			$columns = 3;
		}

		$rows = UM()->options()->get( 'um_user_photos_images_row' );
		if ( empty( $rows ) ) {
			$rows = 2;
		}

		$this->photos_per_page = absint( $columns ) * absint( $rows );
	}
}
