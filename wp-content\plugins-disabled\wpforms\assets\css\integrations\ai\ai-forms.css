#wpforms-setup-templates-list .wpforms-template#wpforms-template-generate:hover, #wpforms-setup-templates-list .wpforms-template#wpforms-template-generate.selected {
  box-shadow: 0 0 0 2px #5c24a9, 0 3px 4px rgba(0, 0, 0, 0.15);
}

#wpforms-setup-templates-list .wpforms-template#wpforms-template-generate .wpforms-template-thumbnail {
  background-color: #faf5fe;
}

#wpforms-setup-templates-list .wpforms-template#wpforms-template-generate .wpforms-template-name-wrap {
  display: flex;
  max-height: 40px;
}

#wpforms-setup-templates-list .wpforms-template#wpforms-template-generate .wpforms-template-name-wrap .wpforms-template-name {
  padding-right: 10px;
}

#wpforms-setup-templates-list .wpforms-template#wpforms-template-generate .wpforms-template-name-wrap .wpforms-badge {
  box-sizing: border-box;
  opacity: 1;
  font-size: 8px;
  padding: 6px 8px;
  height: 18px;
  margin-top: 20px;
}

#wpforms-setup-templates-list .wpforms-template#wpforms-template-generate .wpforms-template-generate.wpforms-inactive {
  color: #ffffff;
  background-color: #7a30e2;
  margin: 0;
  pointer-events: all;
  cursor: default;
}

#wpforms-setup-templates-list .wpforms-template#wpforms-template-generate .wpforms-template-generate.wpforms-inactive:hover {
  background-color: #7a30e2;
  color: #ffffff;
}

#wpforms-setup-templates-list .wpforms-template#wpforms-template-generate .wpforms-template-generate.wpforms-inactive:focus {
  box-shadow: none;
}

#wpforms-panel-ai-form .wpforms-panel-sidebar {
  z-index: 20;
  overflow: hidden;
}

#wpforms-panel-ai-form .wpforms-panel-sidebar-header {
  border-bottom: 1px solid #ced7e0;
}

#wpforms-panel-ai-form .wpforms-btn-back-to-templates {
  background-color: transparent;
  border: none;
  color: #444444;
  width: auto;
  text-align: left;
  padding: 15px 15px 15px 43px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  position: relative;
}

#wpforms-panel-ai-form .wpforms-btn-back-to-templates:hover {
  text-decoration: underline;
}

#wpforms-panel-ai-form .wpforms-btn-back-to-templates:before {
  content: '';
  position: absolute;
  left: 20px;
  background-image: url("../../../images/integrations/ai/back.svg");
  background-size: 13px 11px;
  width: 13px;
  height: 11px;
  top: 50%;
  transform: translateY(-50%);
}

#wpforms-panel-ai-form .wpforms-panel-content-wrap {
  overflow-x: hidden;
}

#wpforms-panel-ai-form .wpforms-panel-content.wpforms-panel-fields {
  width: 100%;
  height: auto;
  padding: 15px;
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-panel-empty-state {
  min-height: 485px;
  height: calc( 100vh - 200px - var( --wpforms-admin-bar-height ));
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-panel-empty-state::before {
  background-image: url("../../../images/integrations/ai/ai-form-empty-state.svg");
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 568px 284px;
  content: "";
  display: block;
  height: 284px;
  width: 100%;
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-panel-empty-state h4 {
  color: #444444;
  font-size: 24px;
  font-weight: 600;
  line-height: 29px;
  margin: 30px 0 10px;
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-panel-empty-state p {
  color: #777777;
  font-size: 16px;
  font-weight: 400;
  line-height: 19px;
  margin: 0;
  text-align: center;
  max-width: 600px;
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-title {
  font-size: 28px;
  line-height: 28px;
  padding: 15px 0 30px 0;
  margin: 0 15px 15px 15px;
  border-bottom: 1px solid #dddddd;
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-field {
  margin: 0;
  min-height: 128px;
  position: relative;
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-field:has(> .placeholder.fade-out) {
  min-height: 0;
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-field .placeholder {
  border-radius: 4px;
  width: calc( 100% - 30px);
  height: 98px;
  margin: 15px;
  background: linear-gradient(92deg, #fcfcfc 50%, #f8f8f8 100%);
  position: absolute;
  top: 0;
  left: 0;
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-field .placeholder.fade-out {
  animation: fade-out .25s ease-in;
  background: transparent;
}

@keyframes fade-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    background: transparent;
  }
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-field .wpforms-field {
  cursor: default;
  opacity: 0;
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-field .wpforms-field.fade-in {
  animation: fade-in .25s ease-in;
  background: transparent;
  opacity: 1;
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-field .wpforms-field:hover {
  border-color: transparent;
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-field .wpforms-field:hover.wpforms-field-divider:not(:empty)::before {
  display: revert;
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-field .wpforms-field:hover.wpforms-pagebreak-normal .wpforms-pagebreak-buttons {
  border-color: transparent;
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-field .wpforms-field:hover.wpforms-pagebreak-normal .wpforms-pagebreak-divider {
  background-color: #626262;
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-field .wpforms-field:hover.wpforms-pagebreak-normal .wpforms-pagebreak-divider span:not(.wpforms-badge) {
  background-color: #626262;
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-field .wpforms-field:empty::before {
  display: none;
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-field label,
#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-field input,
#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-field select,
#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-field textarea,
#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-field button {
  cursor: default;
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-pagebreak-normal .wpforms-pagebreak-divider {
  margin: 0 -15px;
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-submit {
  width: fit-content;
  pointer-events: none;
  background: #999999;
  border: none;
  border-radius: 4px;
  color: #ffffff;
  cursor: pointer;
  font-size: 17px;
  font-weight: 600;
  line-height: 21px;
  padding: 10px 15px;
  margin: 20px 15px 15px 15px;
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-addons-notice {
  display: flex;
  align-items: center;
  margin: 20px 15px 15px 15px;
}

#wpforms-panel-ai-form .wpforms-panel-content .wpforms-ai-form-generator-preview-addons-notice .wpforms-alert-message a {
  color: #444444;
  font-weight: 600;
  text-decoration: underline;
}

wpforms-ai-chat:has(textarea) {
  --wpforms-ai-chat-input-height: 54px;
  --wpforms-ai-chat-message-list-offset: 79px;
}

wpforms-ai-chat:has(textarea) .wpforms-ai-chat-message-list {
  height: calc( 100% - var( --wpforms-ai-chat-input-height, 54px ) - var( --wpforms-ai-chat-message-list-offset, 41px ));
  max-height: calc( 100% - var( --wpforms-ai-chat-input-height, 54px ) - var( --wpforms-ai-chat-message-list-offset, 41px ));
}

wpforms-ai-chat:has(textarea) .wpforms-ai-chat-message-list.wpforms-scrollbar-compact {
  scrollbar-gutter: stable;
}

wpforms-ai-chat:has(textarea) .wpforms-ai-chat-message-list.wpforms-scrollbar-compact .wpforms-chat-item-error .wpforms-chat-item-content {
  width: 400px;
  max-width: 400px;
}

wpforms-ai-chat:has(textarea) .wpforms-ai-chat-message-list .wpforms-chat-item-question {
  width: auto;
  max-width: 410px;
  font-weight: 600;
}

wpforms-ai-chat:has(textarea) .wpforms-ai-chat-message-list .wpforms-chat-item-answer .wpforms-chat-item-content h4,
wpforms-ai-chat:has(textarea) .wpforms-ai-chat-message-list .wpforms-chat-item-warning .wpforms-chat-item-content h4,
wpforms-ai-chat:has(textarea) .wpforms-ai-chat-message-list .wpforms-chat-item-error .wpforms-chat-item-content h4 {
  line-height: 22px;
  margin: 0;
}

wpforms-ai-chat:has(textarea) .wpforms-ai-chat-message-list .wpforms-chat-item-answer .wpforms-chat-item-content .wpforms-ai-chat-answer-buttons,
wpforms-ai-chat:has(textarea) .wpforms-ai-chat-message-list .wpforms-chat-item-warning .wpforms-chat-item-content .wpforms-ai-chat-answer-buttons,
wpforms-ai-chat:has(textarea) .wpforms-ai-chat-message-list .wpforms-chat-item-error .wpforms-chat-item-content .wpforms-ai-chat-answer-buttons {
  margin-top: 10px;
}

wpforms-ai-chat:has(textarea) .wpforms-ai-chat-message-list .wpforms-chat-item-answer:last-child {
  margin-bottom: 20px;
}

wpforms-ai-chat:has(textarea) .wpforms-ai-chat-message-list .wpforms-chat-item-content {
  position: relative;
}

wpforms-ai-chat:has(textarea) .wpforms-ai-chat-inactive .wpforms-chat-item-answer:not(.active) .wpforms-chat-item-content {
  pointer-events: none;
}

wpforms-ai-chat:has(textarea) .wpforms-ai-chat-message-input {
  padding: 35px 40px;
  height: calc( var( --wpforms-ai-chat-input-height, 54px ) + var( --wpforms-ai-chat-message-list-offset, 50px ));
  max-height: calc( var( --wpforms-ai-chat-input-height, 54px ) + var( --wpforms-ai-chat-message-list-offset, 50px ));
}

wpforms-ai-chat:has(textarea) .wpforms-ai-chat-message-input .wpforms-ai-chat-send,
wpforms-ai-chat:has(textarea) .wpforms-ai-chat-message-input .wpforms-ai-chat-stop {
  inset-inline-end: 41px;
  bottom: 38px;
}

wpforms-ai-chat:has(textarea) .wpforms-ai-chat-message-input textarea {
  margin-top: 6px !important;
}

wpforms-ai-chat[mode="forms"] {
  height: calc( 100% - 50px);
  --wpforms-ai-chat-message-list-offset: 40px;
}

wpforms-ai-chat[mode="forms"] .wpforms-ai-chat-message-list {
  padding: 20px 20px 0 19px !important;
}

wpforms-ai-chat[mode="forms"] .wpforms-ai-chat-message-list .wpforms-ai-chat-header h3.wpforms-ai-chat-header-title {
  font-weight: 600;
}

wpforms-ai-chat[mode="forms"] .wpforms-ai-chat-message-list .wpforms-ai-chat-header .wpforms-ai-chat-header-description a {
  display: block;
  line-height: 24px;
  margin-top: 5px;
}

wpforms-ai-chat[mode="forms"] .wpforms-ai-chat-message-list.wpforms-scrollbar-compact {
  padding: 20px 6px 0 19px !important;
}

wpforms-ai-chat[mode="forms"] .wpforms-ai-chat-message-list .wpforms-ai-chat-divider {
  border-top: none;
  margin: 0 0 40px 0;
}

wpforms-ai-chat[mode="forms"] .wpforms-ai-chat-message-input {
  padding: 20px;
}

wpforms-ai-chat[mode="forms"] .wpforms-ai-chat-message-input .wpforms-ai-chat-send,
wpforms-ai-chat[mode="forms"] .wpforms-ai-chat-message-input .wpforms-ai-chat-stop {
  inset-inline-end: 20px;
  bottom: 19px;
}

wpforms-ai-chat[mode="forms"] .wpforms-ai-chat-message-input textarea {
  margin-top: unset !important;
}

wpforms-ai-chat[mode="forms"] .wpforms-ai-chat-welcome-screen-sample-prompts i.wpforms-ai-chat-sample-restaurant {
  background-image: url("../../../images/integrations/ai/icon-restaurant.svg");
}

wpforms-ai-chat[mode="forms"] .wpforms-ai-chat-welcome-screen-sample-prompts i.wpforms-ai-chat-sample-ticket {
  background-image: url("../../../images/integrations/ai/icon-ticket.svg");
}

wpforms-ai-chat[mode="forms"] .wpforms-ai-chat-welcome-screen-sample-prompts i.wpforms-ai-chat-sample-design {
  background-image: url("../../../images/integrations/ai/icon-design.svg");
}

wpforms-ai-chat[mode="forms"] .wpforms-ai-chat-welcome-screen-sample-prompts i.wpforms-ai-chat-sample-stop {
  background-image: url("../../../images/integrations/ai/icon-stop-sign.svg");
}

wpforms-ai-chat[mode="forms"] .wpforms-ai-chat-welcome-screen-sample-prompts i.wpforms-ai-chat-sample-pizza {
  background-image: url("../../../images/integrations/ai/icon-pizza.svg");
}

wpforms-ai-chat[mode="forms"] .wpforms-ai-chat-welcome-screen-sample-prompts i.wpforms-ai-chat-sample-market {
  background-image: url("../../../images/integrations/ai/icon-market.svg");
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-ai-chat-message-item .wpforms-ai-chat-header .wpforms-ai-chat-header-description {
  font-size: 16px;
  color: #444444;
  line-height: 24px;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-ai-chat-message-item .wpforms-ai-chat-header .wpforms-ai-chat-header-description a {
  color: #036aab;
  font-size: 15px;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-ai-chat-message-item .wpforms-ai-chat-header .wpforms-ai-chat-header-description a:hover, wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-ai-chat-message-item .wpforms-ai-chat-header .wpforms-ai-chat-header-description a:focus {
  color: #0399ed;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-question {
  background-color: #0399ed;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-spinner {
  background: #dfe8f2;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-answer .wpforms-chat-item-content,
wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-warning .wpforms-chat-item-content {
  background: #dfe8f2;
  border: 2px solid transparent;
  cursor: pointer;
  transition-property: border-color;
  transition-duration: 0.15s;
  transition-timing-function: ease-out;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-answer .wpforms-chat-item-content:hover,
wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-warning .wpforms-chat-item-content:hover {
  border-color: #86919e;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-answer .wpforms-chat-item-content h4,
wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-warning .wpforms-chat-item-content h4 {
  font-weight: 600;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-answer .wpforms-ai-chat-answer-buttons button span,
wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-warning .wpforms-ai-chat-answer-buttons button span {
  font-weight: 600;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-answer:not(.active) .wpforms-ai-chat-answer-buttons,
wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-warning:not(.active) .wpforms-ai-chat-answer-buttons {
  border-top: 1px solid #ced7e0;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-answer:not(.active) .wpforms-ai-chat-answer-buttons button,
wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-warning:not(.active) .wpforms-ai-chat-answer-buttons button {
  opacity: 0.5;
  pointer-events: none;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-answer:not(.active) .wpforms-ai-chat-answer-buttons .wpforms-ai-chat-answer-buttons-response .wpforms-ai-chat-answer-button.dislike,
wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-warning:not(.active) .wpforms-ai-chat-answer-buttons .wpforms-ai-chat-answer-buttons-response .wpforms-ai-chat-answer-button.dislike {
  background-image: url("../../../images/integrations/ai/thumbs-down-inactive.svg");
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-answer:not(.active) .wpforms-ai-chat-answer-buttons .wpforms-ai-chat-answer-buttons-response button,
wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-warning:not(.active) .wpforms-ai-chat-answer-buttons .wpforms-ai-chat-answer-buttons-response button {
  opacity: 1;
  color: #b0b6bd;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-answer:not(.active) .wpforms-ai-chat-answer-action,
wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-warning:not(.active) .wpforms-ai-chat-answer-action {
  background: #86919e;
  color: #ffffff;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-answer.active .wpforms-chat-item-content,
wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-warning.active .wpforms-chat-item-content {
  background: #ffffff;
  border-color: #ffffff;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-answer.active .wpforms-chat-item-content:hover,
wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-warning.active .wpforms-chat-item-content:hover {
  cursor: default;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-answer.active .wpforms-ai-chat-answer-buttons button,
wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-warning.active .wpforms-ai-chat-answer-buttons button {
  opacity: 1;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-error .wpforms-chat-item-content {
  background: #ffffff;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-error .wpforms-chat-item-content h4 {
  font-weight: 600;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-error .wpforms-chat-item-content span a {
  color: #444444;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-error .wpforms-chat-item-content span a:hover {
  color: #777777;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-list .wpforms-chat-item-answer-waiting .wpforms-chat-item-spinner {
  color: #86919e;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-input {
  border-top: 1px solid #ced7e0;
  background: #dfe8f2;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-input textarea {
  font-size: 15px;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-input textarea::placeholder {
  color: #b0b6bd;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-input textarea:not(:focus) {
  border-color: #b0b6bd;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-message-input textarea:focus {
  border: 1px solid #036aab;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.07), 0 0 0 1px #036aab;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-send,
wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-stop {
  background-color: #036aab;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-send:hover, wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-send:focus,
wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-stop:hover,
wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-stop:focus {
  background-color: #215d8f;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-welcome-screen-sample-prompts li {
  border-bottom: 1px solid #ced7e0;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-welcome-screen-sample-prompts li:last-child {
  border-bottom: none;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-welcome-screen-sample-prompts li::after {
  background-image: url("../../../images/integrations/ai/icon-send-blue.svg");
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-welcome-screen-sample-prompts li:hover a {
  color: #036aab;
}

wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-welcome-screen-sample-prompts li a:hover, wpforms-ai-chat.wpforms-ai-chat-blue .wpforms-ai-chat-welcome-screen-sample-prompts li a:focus {
  color: #036aab;
  text-decoration: underline;
}

.wpforms_page_wpforms-builder .jconfirm .jconfirm-box.wpforms-ai-forms-install-addons-modal .jconfirm-checkbox {
  grid-column: 1 / -1;
  margin: 5px 0 10px 0;
  color: #777777;
  vertical-align: center;
}

.wpforms_page_wpforms-builder .jconfirm .jconfirm-box.wpforms-ai-forms-install-addons-modal .jconfirm-checkbox input[type="checkbox"] {
  border-color: #a6a6a6;
  background: #ffffff;
}

.wpforms_page_wpforms-builder .jconfirm .jconfirm-box.wpforms-ai-forms-addons-installed-modal .jconfirm-buttons {
  margin-top: 0;
}
