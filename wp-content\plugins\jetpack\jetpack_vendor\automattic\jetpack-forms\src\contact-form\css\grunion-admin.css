.add-new-h2, .view-switch, body.no-js .tablenav select[name^="action"], body.no-js #doaction, body.no-js #doaction2 {
	display: none
}

.column-feedback_from img {
	float:left;
	margin-right:10px;
	margin-top:3px;
}

.widefat .column-feedback_from,
.widefat .column-feedback_date,
.widefat .column-feedback_source {
	width: 17%;
}

.widefat .column-feedback_response {
	width: 100%;
}

.widefat .column-feedback_response::before {
	display: none !important;
}

@media screen and (max-width: 782px) {

	.widefat .column-feedback_response {
		padding-left: 8px !important;
	}
}

#export-modal-opener {
	margin-right: 8px;
}

.post-type-feedback .actions {
	display: inline-flex;
	margin-bottom: 4px;
}

.column-feedback_response .feedback_response__item {
	display: grid;
	grid-template-columns: 35% 1fr;
	grid-row-gap: 8px;
}

.column-feedback_response .feedback_response__item-key,
.column-feedback_response .feedback_response__item-value {
	align-items: flex-start;
	display: flex;
	word-break: break-word;
}

.column-feedback_response .feedback_response__item-value {
	font-weight: 700;
}

.column-feedback_response .feedback_response__mobile-separator {
	display: block;
}

@media screen and (min-width: 783px) {

	.column-feedback_response .feedback_response__mobile-separator {
		display: none;
	}
}

.spam a {
	color: #BC0B0B;
}

.untrash a {
	color: #D98500;
}

.unspam a {
	color: #D98500;
}

.post-type-feedback #jetpack-check-feedback-spam {
	margin-top: 0;
}

/* Modal styles */

/* Override Thickbox defaults */
#TB_overlay {
	opacity: 0.5;
}

#TB_title {
	border: none;
	background: none;
}

#TB_ajaxWindowTitle {
	display: none;
}

#TB_ajaxContent {
	display: flex;
}

.feedback-export-modal__wrapper {
	display: flex;
	flex-direction: column;

	/* These strange paddings to add 48px with TB defaults */
	padding: 0 33px 9px 33px;
	width: 100%;
}

.feedback-export-modal__header {
	margin-bottom: 32px;
}

.feedback-export-modal__header-title {
	font-style: normal;
	font-weight: 700;
	font-size: 24px;
	line-height: 32px;
	margin-bottom: 8px;
}

#TB_ajaxContent p.feedback-export-modal__header-subtitle {
	font-style: normal;
	font-weight: 400;
	font-size: 16px;
	line-height: 24px;
	letter-spacing: -0.02em;
	color: #000;
	padding: 0;
	margin-top: 8px;
}

.feedback-export-modal__content {
	display: flex;
	flex-grow: 1;
	flex-direction: column;
	padding: 0;
	gap: 32px;
}

.feedback-export-modal__footer {
	display: flex;
	justify-content: space-between;
	padding: 0 16px;
}

.feedback-export-modal__footer-column {
	display: flex;
	align-items: center;
}

.export-card {
	border-radius: var(--jp-border-radius, 4px);
	box-shadow: 0 0 0 1px var(--jp-gray-10, #c3c4c7) inset;
	display: flex;
	flex-direction: column;
	padding: 24px;
	isolation: isolate;
}

.export-card__body, .export-card__header {
	display: flex;
}

.export-card__header {
	align-items: center;
	margin-bottom: 16px;
	gap: 16px;
}

.export-card__header-title {
	font-style: normal;
	font-weight: 700;
	font-size: 20px;
	line-height: 30px;
}

.export-card__body {
	gap: 32px;
	align-items: center;
	justify-content: space-between;
}

.export-card__body-description {
	flex-grow: 1;
	font-style: normal;
	font-weight: 400;
	font-size: 14px;
	line-height: 24px;
	letter-spacing: -0.02em;
	color: #000;
	max-width: 60%;
}

.export-card__body-description a {
	color: inherit;
}

.export-card__body-cta {
	align-self: flex-end;
}

.export-card__body-cta .button-primary.export-button {
	font-style: normal;
	font-weight: 400;
	letter-spacing: -0.01em;
	color: #FFF;
	background: #000;
	border: 1px solid #FFF;
	border-radius: 4px;
}
