"use strict";var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.Notifications=WPForms.Admin.Builder.Notifications||function(e,n){let l={};const s={removeItemButton:!0,shouldSort:!1};let r;const i=[{toggle:{className:".notifications_enable_file_upload_attachment_toggle",actionElements:["file_upload_attachment_fields"]},choicesJS:{fieldName:"file_upload_attachment_fields",choices:"fileUpload"}},{toggle:{className:".notifications_enable_entry_csv_attachment_toggle",actionElements:["entry_csv_attachment_entry_information","entry_csv_attachment_file_name"]},choicesJS:{fieldName:"entry_csv_attachment_entry_information",choices:"entryInformation"}}],d={init:function(){(r=Object.assign({},s)).noChoicesText=wpforms_builder.notifications_file_upload.no_choices_text,d.setup(),d.bindEvents(),n(d.ready)},ready:function(){"function"==typeof e.Choices&&d.maybeSaveFormState()},setup:function(){l={$builder:n("#wpforms-builder")}},initBlock:function(l,a=!1,c=""){const e=l.data("blockId");"notification"===l.data("blockType")&&e&&i.forEach(function(t){d.initToggle(l,e,t.toggle.className,t.toggle.actionElements);var o,n=l.find("."+t.choicesJS.fieldName).first();if(!(n.length<=0)){let e,i=s;"fileUpload"===t.choicesJS.choices?0===(e=d.choicesJSHelperMethods.fileUploadFields.getAllChoices())[0].choices.length&&(i=r):e=d.choicesJSHelperMethods.entryInformation.getAllChoices(),a&&(o=!c,d.initDynamicallyAddedChoicesJS(l,t.choicesJS.fieldName,o),d.setDynamicallyAddedEntryInformationFileNameFieldValue(l,c)),d.initChoicesJS(n,e,i),"fileUpload"===t.choicesJS.choices&&(d.computeForFileUploadAttachmentSize(n),n.on("change",d.fileUploadFieldChange))}})},initToggle(e,i,t,o){e=e.find(t).first();return!(e.length<=0)&&(e.on("change",function(){var e=n(this),i=d.choicesJSHelperMethods.getFormFields(["file-upload"]);e.trigger("wpformsNotificationsToggleConditionalChange",[i])}),d.setupToggleConditional(e,i,o))},setupToggleConditional:function(e,i,t){var t=t.map(function(e){return`#wpforms-panel-field-notifications-${i}-${e}-wrap`});return!(t.length<=0||(t=t.join(","),e.conditions([{conditions:{element:e,type:"checked",operator:"is",condition:"1"},actions:{if:{element:t,action:"show"},else:{element:t,action:"hide"}},effect:"appear"}]),0))},initChoicesJS:function(e,i,t){var o,n;return!!e.attr("name")&&(o=e.val(),void 0!==(n=e.data("choicesjs"))&&n.destroy(),n=new Choices(e[0],t),e.data("choicesjs",n),e.on("change",d.changeChoicesJS),d.choicesJSHelperMethods.populateInstance(n,i,o),n.passedElement.element.addEventListener("addItem",function(e){l.$builder.trigger("wpformsNotificationFieldAdded",[e.detail.value,e.target])}),n.passedElement.element.addEventListener("removeItem",function(e){l.$builder.trigger("wpformsNotificationFieldRemoved",[e.detail.value,e.target])}),n)},changeChoicesJS:function(){var e=n(this),i=e.data("choicesjs").getValue(),t=e.attr("name");if(t&&i){t=t+"[hidden]",e=e.closest(".wpforms-panel-field").find(`input[name="${t}"]`);if(!(e.length<=0)){var o=[];for(let e=0;e<i.length;e++)o.push(i[e].value);e.val(JSON.stringify(o))}}},fileUploadFieldChange:function(){d.computeForFileUploadAttachmentSize(n(this))},computeForFileUploadAttachmentSize:function(e){var t=e.data("choicesjs").getValue(),e=e.parents(".wpforms-panel-field").find(".notifications-file-upload-attachment-size");if(!(e.length<=0))if(t.length<=0)e.text(0);else{var o=Number(wpforms_builder.notifications_file_upload.wp_max_upload_size);let i=0;for(let e=0;e<t.length;e++){var n,l=wpf.getField(t[e].value);"file-upload"===l.type&&(n=d.utils.convertToNumber(l.max_size,o),l=d.utils.convertToNumber(l.max_file_number,1),i+=n*l)}e.text(+wpf.numberFormat(i,2,".",",").replace(",",""))}},initDynamicallyAddedChoicesJS:function(e,i,t){var o=e.data("blockId"),e=e.find(`#wpforms-panel-field-notifications-${o}-${i}-wrap`);e.length<=0||(o=e.find(".choices")).length<=0||(i=o.find("."+i).first()).length<=0||(i.removeClass("choices__input").removeAttr("hidden").removeAttr("data-choice").removeData("choice").prependTo(e.first()),e.find("label:first").prependTo(e.first()),o.first().remove(),t&&i.val([]))},bindEvents:function(){l.$builder.on("wpformsSettingsBlockAdded",d.notificationBlockAdded).on("wpformsSettingsBlockCloned",d.notificationsBlockCloned).on("wpformsPanelSwitch",d.panelSwitch).on("wpformsPanelSectionSwitch",d.panelSectionSwitch)},notificationBlockAdded:function(e,i){d.initBlock(i,!0)},setDynamicallyAddedEntryInformationFileNameFieldValue:function(e,i){e=e.find(".entry_csv_attachment_file_name");e.length<=0||(i=i?n(`#wpforms-panel-field-notifications-${i}-entry_csv_attachment_file_name`).val():"",e.val(0===i.length?wpforms_builder.entry_information.default_file_name:i))},notificationsBlockCloned:function(e,i,t){d.initBlock(i,!0,t)},panelSwitch:function(e,i){"settings"===i&&"notifications"===n("#wpforms-panel-settings .wpforms-panel-sidebar").find(".wpforms-panel-sidebar-section.active").data("section")&&d.loopAllNotificationsBlock(function(e,i){d.initBlock(e)})},loopAllNotificationsBlock:function(o){n(".wpforms-notification.wpforms-builder-settings-block").each(function(e,i){var i=n(i),t=i.data("blockId");"notification"===i.data("blockType")&&t&&o(i,t)}),d.maybeSaveFormState()},panelSectionSwitch:function(e,i){"notifications"===i&&d.loopAllNotificationsBlock(function(e,i){d.initBlock(e)})},maybeSaveFormState:function(){var e=wpf.getFormState("#wpforms-builder-form");wpf.savedState!==e&&(wpf.savedState=e)},choicesJSHelperMethods:{fileUploadFields:{getAllChoices:function(){return[{label:"hidden",choices:d.choicesJSHelperMethods.getFormFields(["file-upload"])}]}},entryInformation:{getAllChoices:function(){return[{label:"hidden",choices:[{value:"all_fields",label:wpforms_builder.entry_information.localized.all_fields}]},{label:wpforms_builder.fields_available,choices:d.choicesJSHelperMethods.getFormFields(!1,wpforms_builder.entry_information.excluded_field_types)},{label:wpforms_builder.other,choices:d.choicesJSHelperMethods.entryInformation.getOtherChoices()}]},getOtherChoices:function(){var e,i=[];for(const t in wpforms_builder.smart_tags)wpforms_builder.entry_information.excluded_tags.includes(t)||(e=Object.hasOwn(wpforms_builder.entry_information.replacement_tags,t)?wpforms_builder.entry_information.replacement_tags[t]:t,i.push({label:wpf.encodeHTMLEntities(wpforms_builder.smart_tags[t]),value:wpf.sanitizeHTML(e)}));return i}},getFormFields(e,i=[]){var t=[],o=wpf.getFields(e,!0,!0);if(!o)return[];for(const a in o){var n=o[a];if(void 0!==n.label&&!d.choicesJSHelperMethods.isFieldExcluded(n,i)){var l=d.choicesJSHelperMethods.fieldHasRestrictions(n.id);let e=wpf.encodeHTMLEntities(d.choicesJSHelperMethods.getFieldLabel(n));l&&(e+='<span class="wpfroms-notifications-restrictions-enabled">'+wpforms_builder.notifications_file_upload.restrictions_enabled+"</span>"),t.push({label:e,value:wpf.sanitizeHTML(n.id.toString()),disabled:l})}}return t},isFieldExcluded:function(e,i){return Array.isArray(i)&&0<i.length&&i.includes(e.type)},fieldHasRestrictions(e){return Boolean(n(`#wpforms-field-option-${e}-is_restricted`).prop("checked"))},getFieldLabel:function(e){return 0===e.label.length?""+wpforms_builder.empty_label_alternative_text+e.id:e.label},populateInstance:function(i,e,t=[]){i&&(i.clearStore(),i.setChoices(e),Array.isArray(t))&&t.forEach(function(e){i.setChoiceByValue(e)})}},utils:{convertToNumber:function(e,i){e=Number(e);return e<=0?i:e}}};return d}((document,window),jQuery),WPForms.Admin.Builder.Notifications.init();