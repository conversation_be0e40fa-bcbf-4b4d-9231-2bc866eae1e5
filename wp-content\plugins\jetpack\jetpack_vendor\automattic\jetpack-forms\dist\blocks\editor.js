(()=>{var e={4969:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(5573),n=a(790);const o=(0,n.jsx)(r.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)(r.<PERSON>,{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"})})},8248:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(5573),n=a(790);const o=(0,n.jsx)(r.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)(r.<PERSON>,{d:"M6.5 12.4L12 8l5.5 4.4-.9 1.2L12 10l-4.5 3.6-1-1.2z"})})},2663:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(5573),n=a(790);const o=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(r.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M3 7c0-1.1.9-2 2-2h14a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V7Zm2-.5h14c.3 0 .5.2.5.5v1L12 13.5 4.5 7.9V7c0-.3.2-.5.5-.5Zm-.5 3.3V17c0 .*******.5h14c.3 0 .5-.2.5-.5V9.8L12 15.4 4.5 9.8Z"})})},4066:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(5573),n=a(790);const o=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(r.Path,{d:"M12 3.3c-4.8 0-8.8 3.9-8.8 8.8 0 4.8 3.9 8.8 8.8 8.8 4.8 0 8.8-3.9 8.8-8.8s-4-8.8-8.8-8.8zm6.5 5.5h-2.6C15.4 7.3 14.8 6 14 5c2 .6 3.6 2 4.5 3.8zm.7 3.2c0 .6-.1 1.2-.2 1.8h-2.9c.1-.6.1-1.2.1-1.8s-.1-1.2-.1-1.8H19c.2.6.2 1.2.2 1.8zM12 18.7c-1-.7-1.8-1.9-2.3-3.5h4.6c-.5 1.6-1.3 2.9-2.3 3.5zm-2.6-4.9c-.1-.6-.1-1.1-.1-1.8 0-.6.1-1.2.1-1.8h5.2c.1.6.1 1.1.1 1.8s-.1 1.2-.1 1.8H9.4zM4.8 12c0-.6.1-1.2.2-1.8h2.9c-.1.6-.1 1.2-.1 1.8 0 .6.1 1.2.1 1.8H5c-.2-.6-.2-1.2-.2-1.8zM12 5.3c1 .7 1.8 1.9 2.3 3.5H9.7c.5-1.6 1.3-2.9 2.3-3.5zM10 5c-.8 1-1.4 2.3-1.8 3.8H5.5C6.4 7 8 5.6 10 5zM5.5 15.3h2.6c.4 1.5 1 2.8 1.8 3.7-1.8-.6-3.5-2-4.4-3.7zM14 19c.8-1 1.4-2.2 1.8-3.7h2.6C17.6 17 16 18.4 14 19z"})})},2065:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(5573),n=a(790);const o=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(r.Path,{d:"M15 4H9c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h6c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm.5 14c0 .3-.2.5-.5.5H9c-.3 0-.5-.2-.5-.5V6c0-.3.2-.5.5-.5h6c.3 0 .5.2.5.5v12zm-4.5-.5h2V16h-2v1.5z"})})},3407:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(5573),n=a(790);const o=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(r.Path,{d:"M15.5 9.5a1 1 0 100-2 1 1 0 000 2zm0 1.5a2.5 2.5 0 100-5 2.5 2.5 0 000 5zm-2.25 6v-2a2.75 2.75 0 00-2.75-2.75h-4A2.75 2.75 0 003.75 15v2h1.5v-2c0-.69.56-1.25 1.25-1.25h4c.69 0 1.25.56 1.25 1.25v2h1.5zm7-2v2h-1.5v-2c0-.69-.56-1.25-1.25-1.25H15v-1.5h2.5A2.75 2.75 0 0120.25 15zM9.5 8.5a1 1 0 11-2 0 1 1 0 012 0zm1.5 0a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z",fillRule:"evenodd"})})},435:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(5573),n=a(790);const o=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(r.Path,{d:"M10.5 4v4h3V4H15v4h1.5a1 1 0 011 1v4l-3 4v2a1 1 0 01-1 1h-3a1 1 0 01-1-1v-2l-3-4V9a1 1 0 011-1H9V4h1.5zm.5 12.5v2h2v-2l3-4v-3H8v3l3 4z"})})},7326:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(5573),n=a(790);const o=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(r.Path,{d:"M18.5 15v3.5H13V6.7l4.5 4.1 1-1.1-6.2-5.8-5.8 5.8 1 1.1 4-4v11.7h-6V15H4v5h16v-5z"})})},9904:(e,t)=>{"use strict";var a=/^[-!#$%&'*+\/0-9=?A-Z^_a-z{|}~](\.?[-!#$%&'*+\/0-9=?A-Z^_a-z`{|}~])*@[a-zA-Z0-9](-*\.?[a-zA-Z0-9])*\.[a-zA-Z](-?[a-zA-Z0-9])+$/;t.validate=function(e){if(!e)return!1;if(e.length>254)return!1;if(!a.test(e))return!1;var t=e.split("@");return!(t[0].length>64)&&!t[1].split(".").some((function(e){return e.length>63}))}},7842:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});const r={"star-icon":"cuoSlhSNrqf1dozY22Xb",jetpack:"lAIiifeLMmZAPlQ9n9ZR","checkmark-icon":"JLquNpQVlysAamuh5lJO",socialIcon:"cbOwD8Y4tFjwimmtchQI",bluesky:"aLWBKY0yRghEk7tNCgK3",facebook:"aHOlEBGD5EA8NKRw3xTw",twitter:"af4Y_zItXvLAOEoSDPSv",linkedin:"f68aqF3XSD1OBvXR1get",tumblr:"xFI0dt3UiXRlRQdqPWkx",google:"q7JEoyymveP6kF747M43",mastodon:"DKOBOTVmTLbh26gUH_73",nextdoor:"n5XodNsuMfMAAvqHFmbw",instagram:"cL3m0xBYTYhIKI7lCqDB",whatsapp:"fftumuc_lJ6v0tq4UMVR",threads:"inzgC27qxdt7hSdhTWRI"}},7876:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});const r={placeholder:"NisihrgiIKl_knpYJtfg",pulse:"R2i0K45dEF157drbVRPI"}},5196:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});const r={global:"_fUXxnSp5pagKBp9gSN7"}},7378:e=>{var t=1e3,a=60*t,r=60*a,n=24*r,o=7*n,c=365.25*n;function l(e,t,a,r){var n=t>=1.5*a;return Math.round(e/a)+" "+r+(n?"s":"")}e.exports=function(e,s){s=s||{};var i=typeof e;if("string"===i&&e.length>0)return function(e){if((e=String(e)).length>100)return;var l=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!l)return;var s=parseFloat(l[1]);switch((l[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return s*c;case"weeks":case"week":case"w":return s*o;case"days":case"day":case"d":return s*n;case"hours":case"hour":case"hrs":case"hr":case"h":return s*r;case"minutes":case"minute":case"mins":case"min":case"m":return s*a;case"seconds":case"second":case"secs":case"sec":case"s":return s*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return s;default:return}}(e);if("number"===i&&isFinite(e))return s.long?function(e){var o=Math.abs(e);if(o>=n)return l(e,o,n,"day");if(o>=r)return l(e,o,r,"hour");if(o>=a)return l(e,o,a,"minute");if(o>=t)return l(e,o,t,"second");return e+" ms"}(e):function(e){var o=Math.abs(e);if(o>=n)return Math.round(e/n)+"d";if(o>=r)return Math.round(e/r)+"h";if(o>=a)return Math.round(e/a)+"m";if(o>=t)return Math.round(e/t)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},5762:(e,t,a)=>{"use strict";var r=a(3761);function n(){}function o(){}o.resetWarningCache=n,e.exports=function(){function e(e,t,a,n,o,c){if(c!==r){var l=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function t(){return e}e.isRequired=e;var a={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:n};return a.PropTypes=a,a}},8120:(e,t,a)=>{e.exports=a(5762)()},3761:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},4172:(e,t,a)=>{const r=Symbol("SemVer ANY");class n{static get ANY(){return r}constructor(e,t){if(t=o(t),e instanceof n){if(e.loose===!!t.loose)return e;e=e.value}e=e.trim().split(/\s+/).join(" "),i("comparator",e,t),this.options=t,this.loose=!!t.loose,this.parse(e),this.semver===r?this.value="":this.value=this.operator+this.semver.version,i("comp",this)}parse(e){const t=this.options.loose?c[l.COMPARATORLOOSE]:c[l.COMPARATOR],a=e.match(t);if(!a)throw new TypeError(`Invalid comparator: ${e}`);this.operator=void 0!==a[1]?a[1]:"","="===this.operator&&(this.operator=""),a[2]?this.semver=new m(a[2],this.options.loose):this.semver=r}toString(){return this.value}test(e){if(i("Comparator.test",e,this.options.loose),this.semver===r||e===r)return!0;if("string"==typeof e)try{e=new m(e,this.options)}catch(e){return!1}return s(e,this.operator,this.semver,this.options)}intersects(e,t){if(!(e instanceof n))throw new TypeError("a Comparator is required");return""===this.operator?""===this.value||new d(e.value,t).test(this.value):""===e.operator?""===e.value||new d(this.value,t).test(e.semver):(!(t=o(t)).includePrerelease||"<0.0.0-0"!==this.value&&"<0.0.0-0"!==e.value)&&(!(!t.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0")))&&(!(!this.operator.startsWith(">")||!e.operator.startsWith(">"))||(!(!this.operator.startsWith("<")||!e.operator.startsWith("<"))||(!(this.semver.version!==e.semver.version||!this.operator.includes("=")||!e.operator.includes("="))||(!!(s(this.semver,"<",e.semver,t)&&this.operator.startsWith(">")&&e.operator.startsWith("<"))||!!(s(this.semver,">",e.semver,t)&&this.operator.startsWith("<")&&e.operator.startsWith(">")))))))}}e.exports=n;const o=a(879),{safeRe:c,t:l}=a(4746),s=a(9019),i=a(4860),m=a(3296),d=a(2787)},2787:(e,t,a)=>{const r=/\s+/g;class n{constructor(e,t){if(t=c(t),e instanceof n)return e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease?e:new n(e.raw,t);if(e instanceof l)return this.raw=e.value,this.set=[[e]],this.formatted=void 0,this;if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=e.trim().replace(r," "),this.set=this.raw.split("||").map((e=>this.parseRange(e.trim()))).filter((e=>e.length)),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const e=this.set[0];if(this.set=this.set.filter((e=>!k(e[0]))),0===this.set.length)this.set=[e];else if(this.set.length>1)for(const e of this.set)if(1===e.length&&b(e[0])){this.set=[e];break}}this.formatted=void 0}get range(){if(void 0===this.formatted){this.formatted="";for(let e=0;e<this.set.length;e++){e>0&&(this.formatted+="||");const t=this.set[e];for(let e=0;e<t.length;e++)e>0&&(this.formatted+=" "),this.formatted+=t[e].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){const t=((this.options.includePrerelease&&h)|(this.options.loose&&g))+":"+e,a=o.get(t);if(a)return a;const r=this.options.loose,n=r?m[d.HYPHENRANGELOOSE]:m[d.HYPHENRANGE];e=e.replace(n,S(this.options.includePrerelease)),s("hyphen replace",e),e=e.replace(m[d.COMPARATORTRIM],p),s("comparator trim",e),e=e.replace(m[d.TILDETRIM],u),s("tilde trim",e),e=e.replace(m[d.CARETTRIM],f),s("caret trim",e);let c=e.split(" ").map((e=>E(e,this.options))).join(" ").split(/\s+/).map((e=>B(e,this.options)));r&&(c=c.filter((e=>(s("loose invalid filter",e,this.options),!!e.match(m[d.COMPARATORLOOSE]))))),s("range list",c);const i=new Map,b=c.map((e=>new l(e,this.options)));for(const e of b){if(k(e))return[e];i.set(e.value,e)}i.size>1&&i.has("")&&i.delete("");const v=[...i.values()];return o.set(t,v),v}intersects(e,t){if(!(e instanceof n))throw new TypeError("a Range is required");return this.set.some((a=>v(a,t)&&e.set.some((e=>v(e,t)&&a.every((a=>e.every((e=>a.intersects(e,t)))))))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new i(e,this.options)}catch(e){return!1}for(let t=0;t<this.set.length;t++)if(M(this.set[t],e,this.options))return!0;return!1}}e.exports=n;const o=new(a(7582)),c=a(879),l=a(4172),s=a(4860),i=a(3296),{safeRe:m,t:d,comparatorTrimReplace:p,tildeTrimReplace:u,caretTrimReplace:f}=a(4746),{FLAG_INCLUDE_PRERELEASE:h,FLAG_LOOSE:g}=a(246),k=e=>"<0.0.0-0"===e.value,b=e=>""===e.value,v=(e,t)=>{let a=!0;const r=e.slice();let n=r.pop();for(;a&&r.length;)a=r.every((e=>n.intersects(e,t))),n=r.pop();return a},E=(e,t)=>(s("comp",e,t),e=j(e,t),s("caret",e),e=R(e,t),s("tildes",e),e=y(e,t),s("xrange",e),e=_(e,t),s("stars",e),e),w=e=>!e||"x"===e.toLowerCase()||"*"===e,R=(e,t)=>e.trim().split(/\s+/).map((e=>C(e,t))).join(" "),C=(e,t)=>{const a=t.loose?m[d.TILDELOOSE]:m[d.TILDE];return e.replace(a,((t,a,r,n,o)=>{let c;return s("tilde",e,t,a,r,n,o),w(a)?c="":w(r)?c=`>=${a}.0.0 <${+a+1}.0.0-0`:w(n)?c=`>=${a}.${r}.0 <${a}.${+r+1}.0-0`:o?(s("replaceTilde pr",o),c=`>=${a}.${r}.${n}-${o} <${a}.${+r+1}.0-0`):c=`>=${a}.${r}.${n} <${a}.${+r+1}.0-0`,s("tilde return",c),c}))},j=(e,t)=>e.trim().split(/\s+/).map((e=>x(e,t))).join(" "),x=(e,t)=>{s("caret",e,t);const a=t.loose?m[d.CARETLOOSE]:m[d.CARET],r=t.includePrerelease?"-0":"";return e.replace(a,((t,a,n,o,c)=>{let l;return s("caret",e,t,a,n,o,c),w(a)?l="":w(n)?l=`>=${a}.0.0${r} <${+a+1}.0.0-0`:w(o)?l="0"===a?`>=${a}.${n}.0${r} <${a}.${+n+1}.0-0`:`>=${a}.${n}.0${r} <${+a+1}.0.0-0`:c?(s("replaceCaret pr",c),l="0"===a?"0"===n?`>=${a}.${n}.${o}-${c} <${a}.${n}.${+o+1}-0`:`>=${a}.${n}.${o}-${c} <${a}.${+n+1}.0-0`:`>=${a}.${n}.${o}-${c} <${+a+1}.0.0-0`):(s("no pr"),l="0"===a?"0"===n?`>=${a}.${n}.${o}${r} <${a}.${n}.${+o+1}-0`:`>=${a}.${n}.${o}${r} <${a}.${+n+1}.0-0`:`>=${a}.${n}.${o} <${+a+1}.0.0-0`),s("caret return",l),l}))},y=(e,t)=>(s("replaceXRanges",e,t),e.split(/\s+/).map((e=>A(e,t))).join(" ")),A=(e,t)=>{e=e.trim();const a=t.loose?m[d.XRANGELOOSE]:m[d.XRANGE];return e.replace(a,((a,r,n,o,c,l)=>{s("xRange",e,a,r,n,o,c,l);const i=w(n),m=i||w(o),d=m||w(c),p=d;return"="===r&&p&&(r=""),l=t.includePrerelease?"-0":"",i?a=">"===r||"<"===r?"<0.0.0-0":"*":r&&p?(m&&(o=0),c=0,">"===r?(r=">=",m?(n=+n+1,o=0,c=0):(o=+o+1,c=0)):"<="===r&&(r="<",m?n=+n+1:o=+o+1),"<"===r&&(l="-0"),a=`${r+n}.${o}.${c}${l}`):m?a=`>=${n}.0.0${l} <${+n+1}.0.0-0`:d&&(a=`>=${n}.${o}.0${l} <${n}.${+o+1}.0-0`),s("xRange return",a),a}))},_=(e,t)=>(s("replaceStars",e,t),e.trim().replace(m[d.STAR],"")),B=(e,t)=>(s("replaceGTE0",e,t),e.trim().replace(m[t.includePrerelease?d.GTE0PRE:d.GTE0],"")),S=e=>(t,a,r,n,o,c,l,s,i,m,d,p)=>`${a=w(r)?"":w(n)?`>=${r}.0.0${e?"-0":""}`:w(o)?`>=${r}.${n}.0${e?"-0":""}`:c?`>=${a}`:`>=${a}${e?"-0":""}`} ${s=w(i)?"":w(m)?`<${+i+1}.0.0-0`:w(d)?`<${i}.${+m+1}.0-0`:p?`<=${i}.${m}.${d}-${p}`:e?`<${i}.${m}.${+d+1}-0`:`<=${s}`}`.trim(),M=(e,t,a)=>{for(let a=0;a<e.length;a++)if(!e[a].test(t))return!1;if(t.prerelease.length&&!a.includePrerelease){for(let a=0;a<e.length;a++)if(s(e[a].semver),e[a].semver!==l.ANY&&e[a].semver.prerelease.length>0){const r=e[a].semver;if(r.major===t.major&&r.minor===t.minor&&r.patch===t.patch)return!0}return!1}return!0}},3296:(e,t,a)=>{const r=a(4860),{MAX_LENGTH:n,MAX_SAFE_INTEGER:o}=a(246),{safeRe:c,safeSrc:l,t:s}=a(4746),i=a(879),{compareIdentifiers:m}=a(487);class d{constructor(e,t){if(t=i(t),e instanceof d){if(e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw new TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>n)throw new TypeError(`version is longer than ${n} characters`);r("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;const a=e.trim().match(t.loose?c[s.LOOSE]:c[s.FULL]);if(!a)throw new TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+a[1],this.minor=+a[2],this.patch=+a[3],this.major>o||this.major<0)throw new TypeError("Invalid major version");if(this.minor>o||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>o||this.patch<0)throw new TypeError("Invalid patch version");a[4]?this.prerelease=a[4].split(".").map((e=>{if(/^[0-9]+$/.test(e)){const t=+e;if(t>=0&&t<o)return t}return e})):this.prerelease=[],this.build=a[5]?a[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(r("SemVer.compare",this.version,this.options,e),!(e instanceof d)){if("string"==typeof e&&e===this.version)return 0;e=new d(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof d||(e=new d(e,this.options)),m(this.major,e.major)||m(this.minor,e.minor)||m(this.patch,e.patch)}comparePre(e){if(e instanceof d||(e=new d(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{const a=this.prerelease[t],n=e.prerelease[t];if(r("prerelease compare",t,a,n),void 0===a&&void 0===n)return 0;if(void 0===n)return 1;if(void 0===a)return-1;if(a!==n)return m(a,n)}while(++t)}compareBuild(e){e instanceof d||(e=new d(e,this.options));let t=0;do{const a=this.build[t],n=e.build[t];if(r("build compare",t,a,n),void 0===a&&void 0===n)return 0;if(void 0===n)return 1;if(void 0===a)return-1;if(a!==n)return m(a,n)}while(++t)}inc(e,t,a){if(e.startsWith("pre")){if(!t&&!1===a)throw new Error("invalid increment argument: identifier is empty");if(t){const e=new RegExp(`^${this.options.loose?l[s.PRERELEASELOOSE]:l[s.PRERELEASE]}$`),a=`-${t}`.match(e);if(!a||a[1]!==t)throw new Error(`invalid identifier: ${t}`)}}switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t,a);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t,a);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t,a),this.inc("pre",t,a);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t,a),this.inc("pre",t,a);break;case"release":if(0===this.prerelease.length)throw new Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":0===this.minor&&0===this.patch&&0!==this.prerelease.length||this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":0===this.patch&&0!==this.prerelease.length||this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":{const e=Number(a)?1:0;if(0===this.prerelease.length)this.prerelease=[e];else{let r=this.prerelease.length;for(;--r>=0;)"number"==typeof this.prerelease[r]&&(this.prerelease[r]++,r=-2);if(-1===r){if(t===this.prerelease.join(".")&&!1===a)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(e)}}if(t){let r=[t,e];!1===a&&(r=[t]),0===m(this.prerelease[0],t)?isNaN(this.prerelease[1])&&(this.prerelease=r):this.prerelease=r}break}default:throw new Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}e.exports=d},2322:(e,t,a)=>{const r=a(5788);e.exports=(e,t)=>{const a=r(e.trim().replace(/^[=v]+/,""),t);return a?a.version:null}},9019:(e,t,a)=>{const r=a(9250),n=a(6723),o=a(9272),c=a(6453),l=a(3356),s=a(9116);e.exports=(e,t,a,i)=>{switch(t){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof a&&(a=a.version),e===a;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof a&&(a=a.version),e!==a;case"":case"=":case"==":return r(e,a,i);case"!=":return n(e,a,i);case">":return o(e,a,i);case">=":return c(e,a,i);case"<":return l(e,a,i);case"<=":return s(e,a,i);default:throw new TypeError(`Invalid operator: ${t}`)}}},1854:(e,t,a)=>{const r=a(3296),n=a(5788),{safeRe:o,t:c}=a(4746);e.exports=(e,t)=>{if(e instanceof r)return e;if("number"==typeof e&&(e=String(e)),"string"!=typeof e)return null;let a=null;if((t=t||{}).rtl){const r=t.includePrerelease?o[c.COERCERTLFULL]:o[c.COERCERTL];let n;for(;(n=r.exec(e))&&(!a||a.index+a[0].length!==e.length);)a&&n.index+n[0].length===a.index+a[0].length||(a=n),r.lastIndex=n.index+n[1].length+n[2].length;r.lastIndex=-1}else a=e.match(t.includePrerelease?o[c.COERCEFULL]:o[c.COERCE]);if(null===a)return null;const l=a[2],s=a[3]||"0",i=a[4]||"0",m=t.includePrerelease&&a[5]?`-${a[5]}`:"",d=t.includePrerelease&&a[6]?`+${a[6]}`:"";return n(`${l}.${s}.${i}${m}${d}`,t)}},8201:(e,t,a)=>{const r=a(3296);e.exports=(e,t,a)=>{const n=new r(e,a),o=new r(t,a);return n.compare(o)||n.compareBuild(o)}},2471:(e,t,a)=>{const r=a(4940);e.exports=(e,t)=>r(e,t,!0)},4940:(e,t,a)=>{const r=a(3296);e.exports=(e,t,a)=>new r(e,a).compare(new r(t,a))},844:(e,t,a)=>{const r=a(5788);e.exports=(e,t)=>{const a=r(e,null,!0),n=r(t,null,!0),o=a.compare(n);if(0===o)return null;const c=o>0,l=c?a:n,s=c?n:a,i=!!l.prerelease.length;if(!!s.prerelease.length&&!i){if(!s.patch&&!s.minor)return"major";if(0===s.compareMain(l))return s.minor&&!s.patch?"minor":"patch"}const m=i?"pre":"";return a.major!==n.major?m+"major":a.minor!==n.minor?m+"minor":a.patch!==n.patch?m+"patch":"prerelease"}},9250:(e,t,a)=>{const r=a(4940);e.exports=(e,t,a)=>0===r(e,t,a)},9272:(e,t,a)=>{const r=a(4940);e.exports=(e,t,a)=>r(e,t,a)>0},6453:(e,t,a)=>{const r=a(4940);e.exports=(e,t,a)=>r(e,t,a)>=0},6619:(e,t,a)=>{const r=a(3296);e.exports=(e,t,a,n,o)=>{"string"==typeof a&&(o=n,n=a,a=void 0);try{return new r(e instanceof r?e.version:e,a).inc(t,n,o).version}catch(e){return null}}},3356:(e,t,a)=>{const r=a(4940);e.exports=(e,t,a)=>r(e,t,a)<0},9116:(e,t,a)=>{const r=a(4940);e.exports=(e,t,a)=>r(e,t,a)<=0},3566:(e,t,a)=>{const r=a(3296);e.exports=(e,t)=>new r(e,t).major},2186:(e,t,a)=>{const r=a(3296);e.exports=(e,t)=>new r(e,t).minor},6723:(e,t,a)=>{const r=a(4940);e.exports=(e,t,a)=>0!==r(e,t,a)},5788:(e,t,a)=>{const r=a(3296);e.exports=(e,t,a=!1)=>{if(e instanceof r)return e;try{return new r(e,t)}catch(e){if(!a)return null;throw e}}},6313:(e,t,a)=>{const r=a(3296);e.exports=(e,t)=>new r(e,t).patch},965:(e,t,a)=>{const r=a(5788);e.exports=(e,t)=>{const a=r(e,t);return a&&a.prerelease.length?a.prerelease:null}},8998:(e,t,a)=>{const r=a(4940);e.exports=(e,t,a)=>r(t,e,a)},1753:(e,t,a)=>{const r=a(8201);e.exports=(e,t)=>e.sort(((e,a)=>r(a,e,t)))},6146:(e,t,a)=>{const r=a(2787);e.exports=(e,t,a)=>{try{t=new r(t,a)}catch(e){return!1}return t.test(e)}},5123:(e,t,a)=>{const r=a(8201);e.exports=(e,t)=>e.sort(((e,a)=>r(e,a,t)))},9141:(e,t,a)=>{const r=a(5788);e.exports=(e,t)=>{const a=r(e,t);return a?a.version:null}},8265:(e,t,a)=>{const r=a(4746),n=a(246),o=a(3296),c=a(487),l=a(5788),s=a(9141),i=a(2322),m=a(6619),d=a(844),p=a(3566),u=a(2186),f=a(6313),h=a(965),g=a(4940),k=a(8998),b=a(2471),v=a(8201),E=a(5123),w=a(1753),R=a(9272),C=a(3356),j=a(9250),x=a(6723),y=a(6453),A=a(9116),_=a(9019),B=a(1854),S=a(4172),M=a(2787),I=a(6146),N=a(8067),L=a(7064),P=a(3410),T=a(8761),F=a(5966),H=a(6207),V=a(7095),$=a(3250),O=a(6160),z=a(8417),q=a(2620);e.exports={parse:l,valid:s,clean:i,inc:m,diff:d,major:p,minor:u,patch:f,prerelease:h,compare:g,rcompare:k,compareLoose:b,compareBuild:v,sort:E,rsort:w,gt:R,lt:C,eq:j,neq:x,gte:y,lte:A,cmp:_,coerce:B,Comparator:S,Range:M,satisfies:I,toComparators:N,maxSatisfying:L,minSatisfying:P,minVersion:T,validRange:F,outside:H,gtr:V,ltr:$,intersects:O,simplifyRange:z,subset:q,SemVer:o,re:r.re,src:r.src,tokens:r.t,SEMVER_SPEC_VERSION:n.SEMVER_SPEC_VERSION,RELEASE_TYPES:n.RELEASE_TYPES,compareIdentifiers:c.compareIdentifiers,rcompareIdentifiers:c.rcompareIdentifiers}},246:e=>{const t=Number.MAX_SAFE_INTEGER||9007199254740991;e.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:t,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},4860:e=>{const t="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};e.exports=t},487:e=>{const t=/^[0-9]+$/,a=(e,a)=>{const r=t.test(e),n=t.test(a);return r&&n&&(e=+e,a=+a),e===a?0:r&&!n?-1:n&&!r?1:e<a?-1:1};e.exports={compareIdentifiers:a,rcompareIdentifiers:(e,t)=>a(t,e)}},7582:e=>{e.exports=class{constructor(){this.max=1e3,this.map=new Map}get(e){const t=this.map.get(e);return void 0===t?void 0:(this.map.delete(e),this.map.set(e,t),t)}delete(e){return this.map.delete(e)}set(e,t){if(!this.delete(e)&&void 0!==t){if(this.map.size>=this.max){const e=this.map.keys().next().value;this.delete(e)}this.map.set(e,t)}return this}}},879:e=>{const t=Object.freeze({loose:!0}),a=Object.freeze({});e.exports=e=>e?"object"!=typeof e?t:e:a},4746:(e,t,a)=>{const{MAX_SAFE_COMPONENT_LENGTH:r,MAX_SAFE_BUILD_LENGTH:n,MAX_LENGTH:o}=a(246),c=a(4860),l=(t=e.exports={}).re=[],s=t.safeRe=[],i=t.src=[],m=t.safeSrc=[],d=t.t={};let p=0;const u="[a-zA-Z0-9-]",f=[["\\s",1],["\\d",o],[u,n]],h=(e,t,a)=>{const r=(e=>{for(const[t,a]of f)e=e.split(`${t}*`).join(`${t}{0,${a}}`).split(`${t}+`).join(`${t}{1,${a}}`);return e})(t),n=p++;c(e,n,t),d[e]=n,i[n]=t,m[n]=r,l[n]=new RegExp(t,a?"g":void 0),s[n]=new RegExp(r,a?"g":void 0)};h("NUMERICIDENTIFIER","0|[1-9]\\d*"),h("NUMERICIDENTIFIERLOOSE","\\d+"),h("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${u}*`),h("MAINVERSION",`(${i[d.NUMERICIDENTIFIER]})\\.(${i[d.NUMERICIDENTIFIER]})\\.(${i[d.NUMERICIDENTIFIER]})`),h("MAINVERSIONLOOSE",`(${i[d.NUMERICIDENTIFIERLOOSE]})\\.(${i[d.NUMERICIDENTIFIERLOOSE]})\\.(${i[d.NUMERICIDENTIFIERLOOSE]})`),h("PRERELEASEIDENTIFIER",`(?:${i[d.NUMERICIDENTIFIER]}|${i[d.NONNUMERICIDENTIFIER]})`),h("PRERELEASEIDENTIFIERLOOSE",`(?:${i[d.NUMERICIDENTIFIERLOOSE]}|${i[d.NONNUMERICIDENTIFIER]})`),h("PRERELEASE",`(?:-(${i[d.PRERELEASEIDENTIFIER]}(?:\\.${i[d.PRERELEASEIDENTIFIER]})*))`),h("PRERELEASELOOSE",`(?:-?(${i[d.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${i[d.PRERELEASEIDENTIFIERLOOSE]})*))`),h("BUILDIDENTIFIER",`${u}+`),h("BUILD",`(?:\\+(${i[d.BUILDIDENTIFIER]}(?:\\.${i[d.BUILDIDENTIFIER]})*))`),h("FULLPLAIN",`v?${i[d.MAINVERSION]}${i[d.PRERELEASE]}?${i[d.BUILD]}?`),h("FULL",`^${i[d.FULLPLAIN]}$`),h("LOOSEPLAIN",`[v=\\s]*${i[d.MAINVERSIONLOOSE]}${i[d.PRERELEASELOOSE]}?${i[d.BUILD]}?`),h("LOOSE",`^${i[d.LOOSEPLAIN]}$`),h("GTLT","((?:<|>)?=?)"),h("XRANGEIDENTIFIERLOOSE",`${i[d.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),h("XRANGEIDENTIFIER",`${i[d.NUMERICIDENTIFIER]}|x|X|\\*`),h("XRANGEPLAIN",`[v=\\s]*(${i[d.XRANGEIDENTIFIER]})(?:\\.(${i[d.XRANGEIDENTIFIER]})(?:\\.(${i[d.XRANGEIDENTIFIER]})(?:${i[d.PRERELEASE]})?${i[d.BUILD]}?)?)?`),h("XRANGEPLAINLOOSE",`[v=\\s]*(${i[d.XRANGEIDENTIFIERLOOSE]})(?:\\.(${i[d.XRANGEIDENTIFIERLOOSE]})(?:\\.(${i[d.XRANGEIDENTIFIERLOOSE]})(?:${i[d.PRERELEASELOOSE]})?${i[d.BUILD]}?)?)?`),h("XRANGE",`^${i[d.GTLT]}\\s*${i[d.XRANGEPLAIN]}$`),h("XRANGELOOSE",`^${i[d.GTLT]}\\s*${i[d.XRANGEPLAINLOOSE]}$`),h("COERCEPLAIN",`(^|[^\\d])(\\d{1,${r}})(?:\\.(\\d{1,${r}}))?(?:\\.(\\d{1,${r}}))?`),h("COERCE",`${i[d.COERCEPLAIN]}(?:$|[^\\d])`),h("COERCEFULL",i[d.COERCEPLAIN]+`(?:${i[d.PRERELEASE]})?`+`(?:${i[d.BUILD]})?(?:$|[^\\d])`),h("COERCERTL",i[d.COERCE],!0),h("COERCERTLFULL",i[d.COERCEFULL],!0),h("LONETILDE","(?:~>?)"),h("TILDETRIM",`(\\s*)${i[d.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",h("TILDE",`^${i[d.LONETILDE]}${i[d.XRANGEPLAIN]}$`),h("TILDELOOSE",`^${i[d.LONETILDE]}${i[d.XRANGEPLAINLOOSE]}$`),h("LONECARET","(?:\\^)"),h("CARETTRIM",`(\\s*)${i[d.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",h("CARET",`^${i[d.LONECARET]}${i[d.XRANGEPLAIN]}$`),h("CARETLOOSE",`^${i[d.LONECARET]}${i[d.XRANGEPLAINLOOSE]}$`),h("COMPARATORLOOSE",`^${i[d.GTLT]}\\s*(${i[d.LOOSEPLAIN]})$|^$`),h("COMPARATOR",`^${i[d.GTLT]}\\s*(${i[d.FULLPLAIN]})$|^$`),h("COMPARATORTRIM",`(\\s*)${i[d.GTLT]}\\s*(${i[d.LOOSEPLAIN]}|${i[d.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",h("HYPHENRANGE",`^\\s*(${i[d.XRANGEPLAIN]})\\s+-\\s+(${i[d.XRANGEPLAIN]})\\s*$`),h("HYPHENRANGELOOSE",`^\\s*(${i[d.XRANGEPLAINLOOSE]})\\s+-\\s+(${i[d.XRANGEPLAINLOOSE]})\\s*$`),h("STAR","(<|>)?=?\\s*\\*"),h("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),h("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},7095:(e,t,a)=>{const r=a(6207);e.exports=(e,t,a)=>r(e,t,">",a)},6160:(e,t,a)=>{const r=a(2787);e.exports=(e,t,a)=>(e=new r(e,a),t=new r(t,a),e.intersects(t,a))},3250:(e,t,a)=>{const r=a(6207);e.exports=(e,t,a)=>r(e,t,"<",a)},7064:(e,t,a)=>{const r=a(3296),n=a(2787);e.exports=(e,t,a)=>{let o=null,c=null,l=null;try{l=new n(t,a)}catch(e){return null}return e.forEach((e=>{l.test(e)&&(o&&-1!==c.compare(e)||(o=e,c=new r(o,a)))})),o}},3410:(e,t,a)=>{const r=a(3296),n=a(2787);e.exports=(e,t,a)=>{let o=null,c=null,l=null;try{l=new n(t,a)}catch(e){return null}return e.forEach((e=>{l.test(e)&&(o&&1!==c.compare(e)||(o=e,c=new r(o,a)))})),o}},8761:(e,t,a)=>{const r=a(3296),n=a(2787),o=a(9272);e.exports=(e,t)=>{e=new n(e,t);let a=new r("0.0.0");if(e.test(a))return a;if(a=new r("0.0.0-0"),e.test(a))return a;a=null;for(let t=0;t<e.set.length;++t){const n=e.set[t];let c=null;n.forEach((e=>{const t=new r(e.semver.version);switch(e.operator){case">":0===t.prerelease.length?t.patch++:t.prerelease.push(0),t.raw=t.format();case"":case">=":c&&!o(t,c)||(c=t);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${e.operator}`)}})),!c||a&&!o(a,c)||(a=c)}return a&&e.test(a)?a:null}},6207:(e,t,a)=>{const r=a(3296),n=a(4172),{ANY:o}=n,c=a(2787),l=a(6146),s=a(9272),i=a(3356),m=a(9116),d=a(6453);e.exports=(e,t,a,p)=>{let u,f,h,g,k;switch(e=new r(e,p),t=new c(t,p),a){case">":u=s,f=m,h=i,g=">",k=">=";break;case"<":u=i,f=d,h=s,g="<",k="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(l(e,t,p))return!1;for(let a=0;a<t.set.length;++a){const r=t.set[a];let c=null,l=null;if(r.forEach((e=>{e.semver===o&&(e=new n(">=0.0.0")),c=c||e,l=l||e,u(e.semver,c.semver,p)?c=e:h(e.semver,l.semver,p)&&(l=e)})),c.operator===g||c.operator===k)return!1;if((!l.operator||l.operator===g)&&f(e,l.semver))return!1;if(l.operator===k&&h(e,l.semver))return!1}return!0}},8417:(e,t,a)=>{const r=a(6146),n=a(4940);e.exports=(e,t,a)=>{const o=[];let c=null,l=null;const s=e.sort(((e,t)=>n(e,t,a)));for(const e of s){r(e,t,a)?(l=e,c||(c=e)):(l&&o.push([c,l]),l=null,c=null)}c&&o.push([c,null]);const i=[];for(const[e,t]of o)e===t?i.push(e):t||e!==s[0]?t?e===s[0]?i.push(`<=${t}`):i.push(`${e} - ${t}`):i.push(`>=${e}`):i.push("*");const m=i.join(" || "),d="string"==typeof t.raw?t.raw:String(t);return m.length<d.length?m:t}},2620:(e,t,a)=>{const r=a(2787),n=a(4172),{ANY:o}=n,c=a(6146),l=a(4940),s=[new n(">=0.0.0-0")],i=[new n(">=0.0.0")],m=(e,t,a)=>{if(e===t)return!0;if(1===e.length&&e[0].semver===o){if(1===t.length&&t[0].semver===o)return!0;e=a.includePrerelease?s:i}if(1===t.length&&t[0].semver===o){if(a.includePrerelease)return!0;t=i}const r=new Set;let n,m,u,f,h,g,k;for(const t of e)">"===t.operator||">="===t.operator?n=d(n,t,a):"<"===t.operator||"<="===t.operator?m=p(m,t,a):r.add(t.semver);if(r.size>1)return null;if(n&&m){if(u=l(n.semver,m.semver,a),u>0)return null;if(0===u&&(">="!==n.operator||"<="!==m.operator))return null}for(const e of r){if(n&&!c(e,String(n),a))return null;if(m&&!c(e,String(m),a))return null;for(const r of t)if(!c(e,String(r),a))return!1;return!0}let b=!(!m||a.includePrerelease||!m.semver.prerelease.length)&&m.semver,v=!(!n||a.includePrerelease||!n.semver.prerelease.length)&&n.semver;b&&1===b.prerelease.length&&"<"===m.operator&&0===b.prerelease[0]&&(b=!1);for(const e of t){if(k=k||">"===e.operator||">="===e.operator,g=g||"<"===e.operator||"<="===e.operator,n)if(v&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===v.major&&e.semver.minor===v.minor&&e.semver.patch===v.patch&&(v=!1),">"===e.operator||">="===e.operator){if(f=d(n,e,a),f===e&&f!==n)return!1}else if(">="===n.operator&&!c(n.semver,String(e),a))return!1;if(m)if(b&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===b.major&&e.semver.minor===b.minor&&e.semver.patch===b.patch&&(b=!1),"<"===e.operator||"<="===e.operator){if(h=p(m,e,a),h===e&&h!==m)return!1}else if("<="===m.operator&&!c(m.semver,String(e),a))return!1;if(!e.operator&&(m||n)&&0!==u)return!1}return!(n&&g&&!m&&0!==u)&&(!(m&&k&&!n&&0!==u)&&(!v&&!b))},d=(e,t,a)=>{if(!e)return t;const r=l(e.semver,t.semver,a);return r>0?e:r<0||">"===t.operator&&">="===e.operator?t:e},p=(e,t,a)=>{if(!e)return t;const r=l(e.semver,t.semver,a);return r<0?e:r>0||"<"===t.operator&&"<="===e.operator?t:e};e.exports=(e,t,a={})=>{if(e===t)return!0;e=new r(e,a),t=new r(t,a);let n=!1;e:for(const r of e.set){for(const e of t.set){const t=m(r,e,a);if(n=n||null!==t,t)continue e}if(n)return!1}return!0}},8067:(e,t,a)=>{const r=a(2787);e.exports=(e,t)=>new r(e,t).set.map((e=>e.map((e=>e.value)).join(" ").trim().split(" ")))},5966:(e,t,a)=>{const r=a(2787);e.exports=(e,t)=>{try{return new r(e,t).range||"*"}catch(e){return null}}},372:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(67);const n=a.n(r)()("dops:analytics");let o,c;window._tkq=window._tkq||[],window.ga=window.ga||function(){(window.ga.q=window.ga.q||[]).push(arguments)},window.ga.l=+new Date;const l={initialize:function(e,t,a){l.setUser(e,t),l.setSuperProps(a),l.identifyUser()},setGoogleAnalyticsEnabled:function(e,t=null){this.googleAnalyticsEnabled=e,this.googleAnalyticsKey=t},setMcAnalyticsEnabled:function(e){this.mcAnalyticsEnabled=e},setUser:function(e,t){c={ID:e,username:t}},setSuperProps:function(e){o=e},assignSuperProps:function(e){o=Object.assign(o||{},e)},mc:{bumpStat:function(e,t){const a=function(e,t){let a="";if("object"==typeof e){for(const t in e)a+="&x_"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);n("Bumping stats %o",e)}else a="&x_"+encodeURIComponent(e)+"="+encodeURIComponent(t),n('Bumping stat "%s" in group "%s"',t,e);return a}(e,t);l.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom-no-pv"+a+"&t="+Math.random())},bumpStatWithPageView:function(e,t){const a=function(e,t){let a="";if("object"==typeof e){for(const t in e)a+="&"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);n("Built stats %o",e)}else a="&"+encodeURIComponent(e)+"="+encodeURIComponent(t),n('Built stat "%s" in group "%s"',t,e);return a}(e,t);l.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom"+a+"&t="+Math.random())}},pageView:{record:function(e,t){l.tracks.recordPageView(e),l.ga.recordPageView(e,t)}},purchase:{record:function(e,t,a,r,n,o,c){l.ga.recordPurchase(e,t,a,r,n,o,c)}},tracks:{recordEvent:function(e,t){t=t||{},0===e.indexOf("akismet_")||0===e.indexOf("jetpack_")?(o&&(n("- Super Props: %o",o),t=Object.assign(t,o)),n('Record event "%s" called with props %s',e,JSON.stringify(t)),window._tkq.push(["recordEvent",e,t])):n('- Event name must be prefixed by "akismet_" or "jetpack_"')},recordJetpackClick:function(e){const t="object"==typeof e?e:{target:e};l.tracks.recordEvent("jetpack_wpa_click",t)},recordPageView:function(e){l.tracks.recordEvent("akismet_page_view",{path:e})},setOptOut:function(e){n("Pushing setOptOut: %o",e),window._tkq.push(["setOptOut",e])}},ga:{initialized:!1,initialize:function(){let e={};l.ga.initialized||(c&&(e={userId:"u-"+c.ID}),window.ga("create",this.googleAnalyticsKey,"auto",e),l.ga.initialized=!0)},recordPageView:function(e,t){l.ga.initialize(),n("Recording Page View ~ [URL: "+e+"] [Title: "+t+"]"),this.googleAnalyticsEnabled&&(window.ga("set","page",e),window.ga("send",{hitType:"pageview",page:e,title:t}))},recordEvent:function(e,t,a,r){l.ga.initialize();let o="Recording Event ~ [Category: "+e+"] [Action: "+t+"]";void 0!==a&&(o+=" [Option Label: "+a+"]"),void 0!==r&&(o+=" [Option Value: "+r+"]"),n(o),this.googleAnalyticsEnabled&&window.ga("send","event",e,t,a,r)},recordPurchase:function(e,t,a,r,n,o,c){window.ga("require","ecommerce"),window.ga("ecommerce:addTransaction",{id:e,revenue:r,currency:c}),window.ga("ecommerce:addItem",{id:e,name:t,sku:a,price:n,quantity:o}),window.ga("ecommerce:send")}},identifyUser:function(){c&&window._tkq.push(["identifyUser",c.ID,c.username])},setProperties:function(e){window._tkq.push(["setProperties",e])},clearedIdentity:function(){window._tkq.push(["clearIdentity"])}},s=l},8478:(e,t,a)=>{"use strict";a.d(t,{sT:()=>i});var r=a(6072),n=a.n(r),o=a(6427),c=a(3022),l=(a(4705),a(7842));const s=({className:e,size:t=24,viewBox:a="0 0 24 24",opacity:r=1,color:s="#2C3338",children:i})=>{const m={className:(0,c.A)(l.A.iconWrapper,e),width:t,height:t,viewBox:a,opacity:r,fill:void 0};return s&&(m.fill=s),React.createElement(o.SVG,n()({},m,{fillRule:"evenodd",clipRule:"evenodd",xmlns:"http://www.w3.org/2000/svg"}),React.createElement(o.G,{opacity:r},i))},i=({size:e,className:t=l.A.jetpack,color:a})=>React.createElement(s,{className:t,size:e,color:a,viewBox:"0 0 32 32"},React.createElement(o.Path,{className:"jetpack-logo__icon-circle",d:"M16,0C7.2,0,0,7.2,0,16s7.2,16,16,16s16-7.2,16-16S24.8,0,16,0z"}),React.createElement(o.Polygon,{fill:"#fff",points:"15,19 7,19 15,3"}),React.createElement(o.Polygon,{fill:"#fff",points:"17,29 17,13 25,13"}))},1876:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(3022),n=a(1609),o=a.n(n),c=a(7876);const l=({children:e=null,width:t=null,height:a=null,className:n=""})=>o().createElement("div",{className:(0,r.A)(c.A.placeholder,n),style:{width:t,height:a}},e)},723:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>p});var r=a(1609),n=a.n(r),o=a(5196);const c={"--font-headline-medium":"48px","--font-headline-small":"36px","--font-title-medium":"24px","--font-title-small":"20px","--font-body":"16px","--font-body-small":"14px","--font-body-extra-small":"12px","--font-title-large":"var(--font-headline-small)","--font-label":"var(--font-body-extra-small)"},l={"--jp-black":"#000000","--jp-black-80":"#2c3338","--jp-white":"#ffffff","--jp-white-off":"#f9f9f6","--jp-gray":"#dcdcde","--jp-gray-0":"#F6F7F7","--jp-gray-5":"var(--jp-gray)","--jp-gray-10":"#C3C4C7","--jp-gray-20":"#A7AAAD","--jp-gray-40":"#787C82","--jp-gray-50":"#646970","--jp-gray-60":"#50575E","--jp-gray-70":"#3C434A","--jp-gray-80":"#2C3338","--jp-gray-90":"#1d2327","--jp-gray-off":"#e2e2df","--jp-red-0":"#F7EBEC","--jp-red-5":"#FACFD2","--jp-red-40":"#E65054","--jp-red-50":"#D63638","--jp-red-60":"#B32D2E","--jp-red-70":"#8A2424","--jp-red-80":"#691C1C","--jp-red":"#d63639","--jp-yellow-5":"#F5E6B3","--jp-yellow-10":"#F2CF75","--jp-yellow-20":"#F0C930","--jp-yellow-30":"#DEB100","--jp-yellow-40":"#C08C00","--jp-yellow-50":"#9D6E00","--jp-yellow-60":"#7D5600","--jp-blue-20":"#68B3E8","--jp-blue-40":"#1689DB","--jp-pink":"#C9356E","--jp-green-0":"#f0f2eb","--jp-green-5":"#d0e6b8","--jp-green-10":"#9dd977","--jp-green-20":"#64ca43","--jp-green-30":"#2fb41f","--jp-green-40":"#069e08","--jp-green-50":"#008710","--jp-green-60":"#007117","--jp-green-70":"#005b18","--jp-green-80":"#004515","--jp-green-90":"#003010","--jp-green-100":"#001c09","--jp-green":"#069e08","--jp-green-primary":"var( --jp-green-40 )","--jp-green-secondary":"var( --jp-green-30 )"},s={"--jp-border-radius":"4px","--jp-menu-border-height":"1px","--jp-underline-thickness":"2px"},i={"--spacing-base":"8px"},m={},d=(e,t,a)=>{const r={...c,...l,...s,...i};for(const t in r)e.style.setProperty(t,r[t]);a&&e.classList.add(o.A.global),t&&(m[t]={provided:!0,root:e})},p=({children:e=null,targetDom:t,id:a,withGlobalStyles:o=!0})=>{const c=(0,r.useRef)(),l=m?.[a]?.provided;return(0,r.useLayoutEffect)((()=>{if(!l)return t?d(t,a,o):void(c?.current&&d(c.current,a,o))}),[t,c,l,a,o]),t?n().createElement(n().Fragment,null,e):n().createElement("div",{ref:c},e)}},3924:(e,t,a)=>{"use strict";function r(e,t={}){const a={};let r;if("undefined"!=typeof window&&(r=window?.JP_CONNECTION_INITIAL_STATE?.calypsoEnv),0===e.search("https://")){const t=new URL(e);e=`https://${t.host}${t.pathname}`,a.url=encodeURIComponent(e)}else a.source=encodeURIComponent(e);for(const e in t)a[e]=encodeURIComponent(t[e]);!Object.keys(a).includes("site")&&"undefined"!=typeof jetpack_redirects&&Object.hasOwn(jetpack_redirects,"currentSiteRawUrl")&&(a.site=jetpack_redirects.currentBlogID??jetpack_redirects.currentSiteRawUrl),r&&(a.calypso_env=r);return"https://jetpack.com/redirect/?"+Object.keys(a).map((e=>e+"="+a[e])).join("&")}a.d(t,{A:()=>r})},5985:(e,t,a)=>{"use strict";a.d(t,{$i:()=>r.A,BT:()=>c.A,FB:()=>o.A,Sy:()=>n.Sy,_6:()=>m.A,aq:()=>l.aq,d9:()=>n.d9,q7:()=>i.A,st:()=>s.A});a(2810);var r=a(4972),n=(a(4815),a(1409),a(2634)),o=a(703),c=(a(2034),a(5595)),l=a(3265),s=a(3489),i=(a(7119),a(8406),a(6923)),m=(a(335),a(8290),a(9061),a(5929));a(5765)},5765:(e,t,a)=>{"use strict";a(8490)},2810:(e,t,a)=>{"use strict";a(8377).T["Jetpack Green 40"]},2201:(e,t,a)=>{"use strict";a.d(t,{c:()=>r.c});a(5877);var r=a(984)},5877:(e,t,a)=>{"use strict";a(1609)},984:(e,t,a)=>{"use strict";a.d(t,{c:()=>s});var r=a(6427),n=a(7723),o=a(3022),c=a(1609),l=a.n(c);const __=n.__,s=({className:e,description:t,align:a=null,title:n=null,buttonText:c=null,visible:s=!0,context:i=null,checkoutUrl:m=null,goToCheckoutPage:d=null,isRedirecting:p=!1,showButton:u=!0,target:f="_top"})=>{const h=(0,o.A)(e,"jetpack-upgrade-plan-banner",{"wp-block":"editor-canvas"===i,"block-editor-block-list__block":"editor-canvas"===i,"jetpack-upgrade-plan__hidden":!s}),g=__("Redirecting…","jetpack-forms");return l().createElement("div",{className:h,"data-align":a},l().createElement("div",{className:"jetpack-upgrade-plan-banner__wrapper"},n&&l().createElement("strong",{className:(0,o.A)("banner-title",{[`${e}__title`]:e})},n),t&&l().createElement("span",{className:`${e}__description banner-description`},t),u&&l().createElement(r.Button,{href:p?null:m,onClick:d,target:f,className:(0,o.A)("is-primary",{"jetpack-upgrade-plan__hidden":!m}),isBusy:p},p?g:c)))}},335:(e,t,a)=>{"use strict";a(8468)},4972:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const r="Jetpack_Editor_Initial_State";function n(){return"object"==typeof window?window?.[r]??null:null}},703:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(4972);function n(e){const t=(0,r.A)(),a=t?.available_blocks?.[e]?.available??!1,n=t?.available_blocks?.[e]?.unavailable_reason??"unknown";return{available:a,...!a&&{details:t?.available_blocks?.[e]?.details??[],unavailableReason:n}}}},4815:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(7999);function n(){return window&&window.Jetpack_Editor_Initial_State&&window.Jetpack_Editor_Initial_State.siteFragment?window.Jetpack_Editor_Initial_State.siteFragment:(0,r.getScriptData)()?.site?.suffix??null}},3489:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(372),n=a(9384),o=a(8468);const{tracks:c}=r.A,{recordEvent:l}=c,s=({pageViewEventName:e=null,pageViewNamespace:t="jetpack",pageViewSuffix:a="page_view",pageViewEventProperties:s={}}={})=>{const[i,m]=(0,o.useState)(!1),{isUserConnected:d,isRegistered:p,userConnectionData:u={}}=(0,n.useConnection)(),{wpcomUser:{login:f,ID:h}={},blogId:g}=u.currentUser||{},k=(0,o.useCallback)((async(e,t={})=>{d&&h&&f&&l(e,t)}),[d,h,f]);return(0,o.useEffect)((()=>{d&&h&&f&&g&&r.A.initialize(h,f,{blog_id:g})}),[g,h,f,d]),(0,o.useEffect)((()=>{const r=e?`${t}_${e}_${a}`:null;p&&r&&(i||(k(r,s),m(!0)))}),[i,t,e,a,p,s,k]),{recordEvent:k,tracks:c}}},7119:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(7143),n=a(8468),o=a(6087);function c(e=null,t=o.noop){const[a,c]=(0,n.useState)(!1),{isAutosaveablePost:l,isDirtyPost:s,currentPost:i}=(0,r.useSelect)((e=>{const t=e("core/editor");return{isAutosaveablePost:t.isEditedPostAutosaveable(),isDirtyPost:t.isEditedPostDirty(),currentPost:t.getCurrentPost()}}),[]),m=Object.keys(i).length>0,d=(0,r.useSelect)((e=>!!window.wp?.customize||!!e("core/edit-widgets"))),p=(0,r.dispatch)("core/editor").savePost,u=(0,r.useSelect)((e=>e("core").__experimentalGetDirtyEntityRecords())),f=async e=>{e.preventDefault(),m?s&&l&&await p(e):await(async()=>{for(let e=0;e<u.length;e++)await(0,r.dispatch)("core").saveEditedEntityRecord(u[e].kind,u[e].name,u[e].key)})()};return{autosave:f,autosaveAndRedirect:async r=>{r.preventDefault(),a||(c(!0),f(r).then((()=>{e&&function(e,t,a=!1){t&&t(e),a?window.open(e,"_blank"):window.top.location.href=e}(e,t,d)})))},isRedirecting:a}}},6923:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(7143),n=a(8468),o=a(8290);const c=e=>{const{isModuleActive:t,isChangingStatus:a,isLoadingModules:c}=(0,r.useSelect)((t=>{const a=t(o.F);return{isModuleActive:a.isModuleActive(e),isChangingStatus:a.isModuleUpdating(e),isLoadingModules:a.areModulesLoading(e)}}),[e]),{updateJetpackModuleStatus:l}=(0,r.useDispatch)(o.F),s=(0,n.useCallback)((t=>l({name:e,active:t})),[e,l]);return(0,n.useMemo)((()=>({isLoadingModules:c,isChangingStatus:a,isModuleActive:t,changeStatus:s})),[c,a,t,s])}},8406:(e,t,a)=>{"use strict";a(8468)},5929:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(7143),n=a(2619),o=a(3265),c=a(7119);const l=()=>{},s="a8c.wpcom-block-editor.openCheckoutModal";function i(e,t=l){const{checkoutUrl:a,planData:i}=(0,r.useSelect)((t=>{const a=t("core/editor"),r=t("wordpress-com/plans"),{id:n,type:c}=a.getCurrentPost(),l=r&&r.getPlan(e);return{checkoutUrl:(0,o.Q4)({plan:l,planSlug:e,postId:n,postType:c}),planData:l}}),[e]),{autosave:m,autosaveAndRedirect:d,isRedirecting:p}=(0,c.A)(a,t);return[a,async e=>{if(e.preventDefault(),(0,n.hasAction)(s))return e.preventDefault(),m(e),void(0,n.doAction)(s,{products:[i]});d(e)},p,i]}},9520:(e,t,a)=>{"use strict";var r=a(67),n=a.n(r);window,n()("shared-extension-utils:connection")},9061:(e,t,a)=>{"use strict";a(9520)},7105:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>d,E9:()=>m});var r=a(7143),n=a(2634),o=a(4478),c=a(8290);const l="SET_JETPACK_MODULES";function s(e){return m({isLoading:e})}function i(e,t){return{type:"SET_MODULE_UPDATING",name:e,isUpdating:t}}function m(e){return{type:l,options:e}}const d={updateJetpackModuleStatus:function*(e){try{yield i(e.name,!0),yield(0,o.sB)(e);const t=yield(0,o.wz)();return yield m({data:t}),!0}catch{const e=(0,r.select)(c.F).getJetpackModules();return yield m(e),!1}finally{yield i(e.name,!1)}},setJetpackModules:m,fetchModules:function*(){if((0,n.Sy)())return!0;try{yield s(!0);const e=yield(0,o.wz)();return yield m({data:e}),!0}catch{const e=(0,r.select)(c.F).getJetpackModules();return yield m(e),!1}finally{yield s(!1)}}}},4478:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>i,sB:()=>s,wz:()=>l});var r=a(1455),n=a.n(r);const o="FETCH_JETPACK_MODULES",c="UPDATE_JETPACK_MODULE_STATUS",l=()=>({type:o}),s=e=>({type:c,settings:e}),i={[o]:function(){return n()({path:"/jetpack/v4/module/all",method:"GET"})},[c]:function({settings:e}){return n()({path:`/jetpack/v4/module/${e.name}/active`,method:"POST",data:{active:e.active}})}}},8290:(e,t,a)=>{"use strict";a.d(t,{F:()=>i});var r=a(7143),n=a(7105),o=a(4478),c=a(8862),l=a(2701),s=a(1640);const i="jetpack-modules",m=(0,r.createReduxStore)(i,{reducer:c.A,actions:n.Ay,controls:o.Ay,resolvers:l.A,selectors:s.A});(0,r.register)(m);const d=window?.Initial_State?.getModules||window?.Jetpack_Editor_Initial_State?.modules||null;null!==d&&(0,r.dispatch)(i).setJetpackModules({data:{...d}})},8862:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const r={isLoading:!1,isUpdating:{},data:{}},n=(e=r,t)=>{switch(t.type){case"SET_JETPACK_MODULES":return{...e,...t.options};case"SET_MODULE_UPDATING":return{...e,isUpdating:{...e.isUpdating,[t.name]:t.isUpdating}}}return e}},2701:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(7105),n=a(4478);const o={getJetpackModules:function*(){try{const e=yield(0,n.wz)();if(e)return(0,r.E9)({data:e})}catch(e){console.error(e)}}}},1640:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(2634);const n={getJetpackModules:e=>e.data,isModuleActive:(e,t)=>(0,r.Sy)()||(e?.data?.[t]?.activated??!1),areModulesLoading:e=>e.isLoading??!1,isModuleUpdating:(e,t)=>e?.isUpdating?.[t]??!1}},3265:(e,t,a)=>{"use strict";a.d(t,{Q4:()=>s,aq:()=>i});var r=a(7723),n=a(3832),o=a(6087),c=a(4815),l=a(2634);const __=r.__;function s({planSlug:e,plan:t,postId:a,postType:r}){const s=(0,o.startsWith)(e,"jetpack_")?e:(0,o.get)(t,["path_slug"]),i=(void 0===r?()=>{const e=new URLSearchParams(window.location.search);return(0,n.addQueryArgs)(window.location.protocol+`//${(0,c.A)().replace("::","/")}/wp-admin/site-editor.php`,{postId:e.get("postId"),postType:e.get("postType"),plan_upgraded:1})}:()=>{const e=["page","post"].includes(r)?"":"edit";return(0,l.Sy)()?(0,n.addQueryArgs)("/"+(0,o.compact)([e,r,(0,c.A)(),a]).join("/"),{plan_upgraded:1}):(0,n.addQueryArgs)(window.location.protocol+`//${(0,c.A)().replace("::","/")}/wp-admin/post.php`,{action:"edit",post:a,plan_upgraded:1})})();return(0,l.d9)()?(0,n.addQueryArgs)(`https://wordpress.com/plans/${(0,c.A)()}`,{redirect_to:i,customerType:"business"}):s&&(0,n.addQueryArgs)(`https://wordpress.com/checkout/${(0,c.A)()}/${s}`,{redirect_to:i})}function i(e,t){return"missing_plan"===e&&t.required_plan}__("Upgrade your plan to use video covers","jetpack-forms"),__("Upgrade your plan to upload audio","jetpack-forms")},2034:(e,t,a)=>{"use strict";a(2279)},1409:(e,t,a)=>{"use strict";a(7999)},2634:(e,t,a)=>{"use strict";function r(){return"object"==typeof window&&"string"==typeof window._currentSiteType?window._currentSiteType:null}function n(){return"simple"===r()}function o(){return"atomic"===r()}a.d(t,{Sy:()=>n,d9:()=>o})},5595:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(6072),n=a.n(r),o=a(9491);const c=e=>(0,o.createHigherOrderComponent)((t=>a=>React.createElement(t,n()({},a,{className:a.name===e?"has-warning is-interactive":a.className}))),"withHasWarningIsInteractiveClassNames")},4705:(e,t,a)=>{"use strict";a(8992),a(1135)},1135:(e,t,a)=>{"use strict";a.d(t,{$:()=>r});const r=[{name:"amazon",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M13.582 8.182c-1.648.185-3.802.308-5.344.984-1.781.769-3.03 2.337-3.03 4.644 0 2.953 1.86 4.429 4.253 4.429 2.02 0 3.125-.477 4.685-2.065.516.747.685 1.109 1.629 1.894a.59.59 0 0 0 .672-.066l.006.006c.567-.505 1.599-1.401 2.18-1.888.231-.188.19-.496.009-.754-.52-.718-1.072-1.303-1.072-2.634V8.305c0-1.876.133-3.599-1.249-4.891C15.23 2.369 13.422 2 12.04 2 9.336 2 6.318 3.01 5.686 6.351c-.068.355.191.542.423.594l2.754.298c.258-.013.445-.266.494-.523.236-1.151 1.2-1.706 2.284-1.706.584 0 1.249.215 1.595.738.398.584.346 1.384.346 2.061zm-.533 5.906c-.451.8-1.169 1.291-1.967 1.291-1.09 0-1.728-.83-1.728-2.061 0-2.42 2.171-2.86 4.227-2.86v.615c.001 1.108.027 2.031-.532 3.015m7.634 5.251C18.329 21.076 14.917 22 11.979 22c-4.118 0-7.826-1.522-10.632-4.057-.22-.199-.024-.471.241-.317 3.027 1.762 6.771 2.823 10.639 2.823 2.608 0 5.476-.541 8.115-1.66.397-.169.73.262.341.55m.653 1.704c-.194.163-.379.076-.293-.139.284-.71.92-2.298.619-2.684s-1.99-.183-2.749-.092c-.23.027-.266-.173-.059-.319 1.348-.946 3.555-.673 3.811-.356.26.32-.066 2.533-1.329 3.59"})))},{name:"behance",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M7.799 5.698c.589 0 1.12.051 1.606.156q.722.155 1.241.507.516.351.804.938c.188.387.281.871.281 1.443q0 .93-.421 1.551-.424.62-1.255 1.014 1.133.328 1.689 1.146.559.822.557 1.975 0 .935-.359 1.612a3.14 3.14 0 0 1-.973 1.114q-.613.432-1.399.637A6.1 6.1 0 0 1 7.963 18H2V5.698zm-.35 4.97q.721 0 1.192-.345.465-.344.463-1.119 0-.43-.152-.707a1.1 1.1 0 0 0-.416-.427 1.7 1.7 0 0 0-.596-.216 3.6 3.6 0 0 0-.697-.06H4.709v2.874zm.151 5.237q.401.001.759-.077c.243-.053.457-.137.637-.261.182-.12.332-.283.441-.491q.164-.31.163-.798-.002-.948-.533-1.357c-.356-.27-.83-.404-1.413-.404H4.709v3.388zm8.562-.041q.552.538 1.583.538.74 0 1.277-.374c.354-.248.571-.514.654-.79h2.155c-.347 1.072-.872 1.838-1.589 2.299-.708.463-1.572.693-2.58.693q-1.05 0-1.899-.337a4 4 0 0 1-1.439-.958 4.4 4.4 0 0 1-.904-1.484 5.4 5.4 0 0 1-.32-1.899q0-1 .329-1.863a4.4 4.4 0 0 1 .933-1.492q.607-.63 1.444-.994a4.6 4.6 0 0 1 1.857-.363q1.131-.001 1.98.44a3.94 3.94 0 0 1 1.389 1.181 4.8 4.8 0 0 1 .783 1.69q.24.947.171 1.983h-6.428c-.001.706.237 1.372.604 1.73m2.811-4.68c-.291-.321-.783-.496-1.384-.496q-.585 0-.973.2a2 2 0 0 0-.621.491 1.8 1.8 0 0 0-.328.628 2.7 2.7 0 0 0-.111.587h3.98c-.058-.625-.271-1.085-.563-1.41m-3.916-3.446h4.985V6.524h-4.985z"})))},{name:"blogger-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19.779 9.904h-.981l-.021.001a1.163 1.163 0 0 1-1.16-1.079l-.001-.013A5.813 5.813 0 0 0 11.803 3H8.871a5.813 5.813 0 0 0-5.813 5.813v6.375a5.813 5.813 0 0 0 5.813 5.813h6.257a5.814 5.814 0 0 0 5.813-5.813l.002-4.121a1.164 1.164 0 0 0-1.164-1.163M8.726 7.713h3.291a1.117 1.117 0 1 1 0 2.234H8.726a1.117 1.117 0 1 1 0-2.234m6.601 8.657H8.72a1.057 1.057 0 1 1 0-2.114h6.607a1.057 1.057 0 1 1 0 2.114"})))},{name:"blogger",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M14.722 14.019a.654.654 0 0 1-.654.654H9.977a.654.654 0 0 1 0-1.308h4.091c.361 0 .654.293.654.654m-4.741-3.321h2.038a.692.692 0 0 0 0-1.384H9.981a.692.692 0 0 0 0 1.384M21 5v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2m-3.456 6.39a.72.72 0 0 0-.72-.72h-.607l-.013.001a.72.72 0 0 1-.718-.668l-.001-.008a3.6 3.6 0 0 0-3.599-3.599H10.07a3.6 3.6 0 0 0-3.599 3.599v3.947a3.6 3.6 0 0 0 3.599 3.599h3.874a3.6 3.6 0 0 0 3.599-3.599z"})))},{name:"bluesky",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M21.2 3.3c-.5-.2-1.4-.5-3.6 1C15.4 6 12.9 9.2 12 11c-.9-1.8-3.4-5-5.7-6.7-2.2-1.6-3-1.3-3.6-1S2 4.6 2 5.1s.3 4.7.5 5.4c.7 2.3 3.1 3.1 5.3 2.8-3.3.5-6.2 1.7-2.4 5.9 4.2 4.3 5.7-.9 6.5-3.6.8 2.7 1.7 7.7 6.4 3.6 3.6-3.6 1-5.4-2.3-5.9 2.2.2 4.6-.5 5.3-2.8.4-.7.7-4.8.7-5.4 0-.5-.1-1.5-.8-1.8"})))},{name:"codepen",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"m22.016 8.84-.007-.037q-.006-.037-.015-.072-.007-.022-.013-.042l-.023-.062-.02-.042a.4.4 0 0 0-.03-.057l-.025-.038-.035-.052-.03-.037q-.021-.026-.043-.045-.015-.018-.035-.035a.4.4 0 0 0-.048-.04l-.037-.03-.015-.012-9.161-6.096a.86.86 0 0 0-.955 0L2.359 8.237l-.015.012-.038.028-.048.04a.638.638 0 0 0-.078.082q-.018.018-.03.037-.018.026-.035.052l-.025.038q-.016.031-.03.059l-.02.041a1 1 0 0 0-.034.106q-.01.034-.016.071-.003.02-.006.037a1 1 0 0 0-.009.114v6.093q0 .056.008.112l.007.038q.006.035.015.072a.2.2 0 0 0 .013.04q.01.032.022.063l.02.04a.4.4 0 0 0 .055.096l.035.052.03.037.042.045.035.035q.023.02.048.04l.038.03.013.01 9.163 6.095a.858.858 0 0 0 .959.004l9.163-6.095.015-.01q.02-.015.037-.03l.048-.04q.02-.017.035-.035.025-.024.043-.045l.03-.037.035-.052.025-.038a.4.4 0 0 0 .03-.058l.02-.04.023-.063c.003-.013.01-.027.013-.04q.009-.037.015-.072l.007-.037q.006-.062.007-.117V8.954a1 1 0 0 0-.008-.114m-9.154-4.376 6.751 4.49-3.016 2.013-3.735-2.492zm-1.724 0v4.009l-3.735 2.494-3.014-2.013zm-7.439 6.098L5.853 12l-2.155 1.438zm7.439 8.974-6.749-4.491 3.015-2.011 3.735 2.492zM12 14.035 8.953 12 12 9.966 15.047 12zm.862 5.501v-4.009l3.735-2.492 3.016 2.011zm7.441-6.098L18.147 12l2.156-1.438z"})))},{name:"deezer",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M20.129 3.662c.222-1.287.548-2.096.909-2.098h.001c.673.002 1.219 2.809 1.219 6.274s-.546 6.274-1.22 6.274c-.276 0-.531-.477-.736-1.276-.324 2.926-.997 4.937-1.776 4.937-.603 0-1.144-1.208-1.507-3.114-.248 3.624-.872 6.195-1.602 6.195-.458 0-.875-1.019-1.184-2.678C13.861 21.6 13.003 24 12.002 24s-1.861-2.399-2.231-5.824c-.307 1.659-.724 2.678-1.184 2.678-.73 0-1.352-2.571-1.602-6.195-.363 1.905-.903 3.114-1.507 3.114-.778 0-1.452-2.011-1.776-4.937-.204.802-.46 1.276-.736 1.276-.674 0-1.22-2.809-1.22-6.274s.546-6.274 1.22-6.274c.362 0 .685.812.91 2.098.357-2.22.94-3.662 1.6-3.662.784 0 1.463 2.04 1.784 5.002.314-2.156.791-3.53 1.325-3.53.749 0 1.385 2.703 1.621 6.474.443-1.933 1.085-3.146 1.795-3.146s1.352 1.214 1.795 3.146c.237-3.771.872-6.474 1.621-6.474.533 0 1.009 1.374 1.325 3.53.321-2.962 1-5.002 1.784-5.002.658 0 1.244 1.443 1.603 3.662M0 7.221c0-1.549.31-2.805.692-2.805s.692 1.256.692 2.805-.31 2.805-.692 2.805S0 8.77 0 7.221m22.616 0c0-1.549.31-2.805.692-2.805S24 5.672 24 7.221s-.31 2.805-.692 2.805-.692-1.256-.692-2.805"})))},{name:"discord",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M20.33 4.523A20 20 0 0 0 15.379 3a14 14 0 0 0-.634 1.289 18.4 18.4 0 0 0-5.495 0A14 14 0 0 0 8.615 3 20 20 0 0 0 3.66 4.527C.527 9.163-.323 13.684.102 18.141a20 20 0 0 0 6.073 3.049 14.7 14.7 0 0 0 1.301-2.097 13 13 0 0 1-2.048-.978q.258-.189.502-.378a14.27 14.27 0 0 0 12.142 0q.247.202.502.378a13 13 0 0 1-2.052.98 14.5 14.5 0 0 0 1.301 2.095 19.9 19.9 0 0 0 6.076-3.047c.498-5.168-.851-9.648-3.568-13.62M8.013 15.4c-1.183 0-2.161-1.074-2.161-2.395S6.796 10.6 8.01 10.6s2.183 1.083 2.163 2.405S9.22 15.4 8.013 15.4m7.974 0c-1.186 0-2.16-1.074-2.16-2.395s.944-2.405 2.16-2.405 2.178 1.083 2.157 2.405-.951 2.395-2.158 2.395"})))},{name:"dribbble",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10m8.434-8.631c-.292-.092-2.644-.794-5.32-.365 1.117 3.07 1.572 5.57 1.659 6.09a8.56 8.56 0 0 0 3.661-5.725m-5.098 6.507c-.127-.749-.623-3.361-1.822-6.477l-.056.019c-4.818 1.679-6.547 5.02-6.701 5.334A8.5 8.5 0 0 0 12 20.555a8.5 8.5 0 0 0 3.336-.679m-9.682-2.152c.193-.331 2.538-4.213 6.943-5.637q.167-.054.337-.102a29 29 0 0 0-.692-1.45c-4.266 1.277-8.405 1.223-8.778 1.216a8.497 8.497 0 0 0 2.19 5.973m-2.015-7.46c.382.005 3.901.02 7.897-1.041a55 55 0 0 0-3.167-4.94 8.57 8.57 0 0 0-4.73 5.981m6.359-6.555a46 46 0 0 1 3.187 5c3.037-1.138 4.323-2.867 4.477-3.085a8.51 8.51 0 0 0-7.664-1.915m8.614 2.903c-.18.243-1.612 2.078-4.77 3.367a27 27 0 0 1 .751 1.678c2.842-.357 5.666.215 5.948.275a8.5 8.5 0 0 0-1.929-5.32"})))},{name:"dropbox",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 6.134 6.069 9.797 2 6.54l5.883-3.843zm-10 6.92 5.883 3.843L12 13.459 6.069 9.797zm10 .405 4.116 3.439L22 13.054l-4.069-3.257zM22 6.54l-5.884-3.843L12 6.134l5.931 3.663zm-9.989 7.66-4.129 3.426-1.767-1.153v1.291l5.896 3.539 5.897-3.539v-1.291l-1.769 1.153z"})))},{name:"eventbrite",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M18.041 3.931 5.959 3A2.96 2.96 0 0 0 3 5.959v12.083A2.96 2.96 0 0 0 5.959 21l12.083-.931C19.699 19.983 21 18.744 21 17.11V6.89c0-1.634-1.259-2.863-2.959-2.959M16.933 8.17c-.082.215-.192.432-.378.551-.188.122-.489.132-.799.132-1.521 0-3.062-.048-4.607-.048q-.23 1.061-.451 2.128c.932-.004 1.873.005 2.81.005.726 0 1.462-.069 1.586.525.04.189-.001.426-.052.615-.105.38-.258.676-.625.783-.185.054-.408.058-.646.058-1.145 0-2.345.017-3.493.02-.169.772-.328 1.553-.489 2.333 1.57-.005 3.067-.041 4.633-.058.627-.007 1.085.194 1.009.85a2.2 2.2 0 0 1-.211.725c-.102.208-.248.376-.488.452-.237.075-.541.064-.862.078-.304.014-.614.008-.924.016-.309.009-.619.022-.919.022-1.253 0-2.429.08-3.683.073-.603-.004-1.014-.249-1.124-.757-.059-.273-.018-.58.036-.841a3543 3543 0 0 1 1.629-7.763c.056-.265.114-.511.225-.714a1.24 1.24 0 0 1 .79-.62c.368-.099.883-.047 1.344-.047.305 0 .612.008.914.016.925.026 1.817.03 2.747.053.304.007.615.016.915.016.621 0 1.17.073 1.245.614.039.288-.051.567-.132.783"})))},{name:"facebook",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.5 2 2 6.5 2 12c0 5 3.7 9.1 8.4 9.9v-7H7.9V12h2.5V9.8c0-2.5 1.5-3.9 3.8-3.9 1.1 0 2.2.2 2.2.2v2.5h-1.3c-1.2 0-1.6.8-1.6 1.6V12h2.8l-.4 2.9h-2.3v7C18.3 21.1 22 17 22 12c0-5.5-4.5-10-10-10"})))},{name:"fediverse",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 743 743"},React.createElement("g",null,React.createElement("path",{d:"M181.131 275.137a68.9 68.9 0 0 1-29.465 29.328l161.758 162.389 38.998-19.764zm213.363 214.187-38.998 19.764 81.963 82.283a68.9 68.9 0 0 1 29.471-29.332zM581.646 339.391l-91.576 46.41 6.752 43.189 103.616-52.513a68.9 68.9 0 0 1-18.792-37.086m-144.738 73.351L220.383 522.477a68.9 68.9 0 0 1 18.795 37.089L443.66 455.934zM367.275 142.438l-104.48 203.97 30.848 30.967 110.623-215.957a68.9 68.9 0 0 1-36.991-18.98M235.621 399.459l-52.922 103.314a68.9 68.9 0 0 1 36.987 18.979l46.781-91.328zM150.768 304.918a68.9 68.9 0 0 1-34.416 7.195 69 69 0 0 1-6.651-.695l30.903 197.662a68.9 68.9 0 0 1 34.416-7.195 69 69 0 0 1 6.646.695zM239.342 560.545c.707 4.589.949 9.239.72 13.877a68.9 68.9 0 0 1-7.267 27.18l197.629 31.712c-.708-4.59-.95-9.24-.723-13.878a68.9 68.9 0 0 1 7.27-27.178zM601.133 377.199l-91.219 178.082a68.9 68.9 0 0 1 36.994 18.983l91.217-178.08a68.9 68.9 0 0 1-36.992-18.985M476.723 125.33a68.9 68.9 0 0 1-29.471 29.332l141.266 141.811a68.9 68.9 0 0 1 29.468-29.332zM347.787 104.631l-178.576 90.498a68.9 68.9 0 0 1 18.793 37.086l178.574-90.502a68.9 68.9 0 0 1-18.791-37.082M446.926 154.826a68.9 68.9 0 0 1-34.983 7.483 69 69 0 0 1-6.029-.633l15.818 101.291 43.163 6.926zm-16 167.028 37.4 239.482a68.9 68.9 0 0 1 33.914-6.943q3.625.206 7.207.791L474.09 328.777zM188.131 232.975c.734 4.66.988 9.383.758 14.095a68.9 68.9 0 0 1-7.16 26.983l101.369 16.281 19.923-38.908zm173.736 27.9-19.926 38.912 239.514 38.467a69 69 0 0 1-.695-13.719 68.9 68.9 0 0 1 7.349-27.324z"}),React.createElement("path",{fillOpacity:".996",d:"M412.284 156.054c34.538 1.882 64.061-24.592 65.943-59.13s-24.592-64.062-59.131-65.943c-34.538-1.882-64.061 24.592-65.943 59.13s24.593 64.062 59.131 65.943M646.144 390.82c34.538 1.881 64.062-24.593 65.943-59.131s-24.592-64.061-59.13-65.943-64.062 24.593-65.943 59.131 24.592 64.061 59.13 65.943M495.086 685.719c34.538 1.881 64.062-24.592 65.943-59.13s-24.592-64.062-59.13-65.943-64.062 24.592-65.943 59.13 24.592 64.062 59.13 65.943M167.866 633.211c34.538 1.882 64.062-24.592 65.943-59.13s-24.592-64.062-59.13-65.943-64.062 24.592-65.943 59.13 24.592 64.062 59.13 65.943M116.692 305.86c34.538 1.882 64.062-24.592 65.943-59.13s-24.592-64.062-59.131-65.943c-34.538-1.881-64.061 24.592-65.943 59.13s24.593 64.062 59.131 65.943"})))},{name:"feed",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M2 8.667V12c5.515 0 10 4.485 10 10h3.333c0-7.363-5.97-13.333-13.333-13.333M2 2v3.333c9.19 0 16.667 7.477 16.667 16.667H22C22 10.955 13.045 2 2 2m2.5 15a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5"})))},{name:"flickr",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M6.5 7c-2.75 0-5 2.25-5 5s2.25 5 5 5 5-2.25 5-5-2.25-5-5-5m11 0c-2.75 0-5 2.25-5 5s2.25 5 5 5 5-2.25 5-5-2.25-5-5-5"})))},{name:"foursquare",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M17.573 2H6.905C5.434 2 5 3.107 5 3.805v16.948c0 .785.422 1.077.66 1.172.238.097.892.177 1.285-.275 0 0 5.035-5.843 5.122-5.93.132-.132.132-.132.262-.132h3.26c1.368 0 1.588-.977 1.732-1.552.078-.318.692-3.428 1.225-6.122l.675-3.368C19.56 2.893 19.14 2 17.573 2m-1.078 5.22c-.053.252-.372.518-.665.518h-4.157c-.467 0-.802.318-.802.787v.508c0 .467.337.798.805.798h3.528c.331 0 .655.362.583.715s-.407 2.102-.448 2.295c-.04.193-.262.523-.655.523h-2.88c-.523 0-.683.068-1.033.503-.35.437-3.505 4.223-3.505 4.223-.032.035-.063.027-.063-.015V4.852c0-.298.26-.648.648-.648h8.562c.315 0 .61.297.528.683z"})))},{name:"ghost",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M10.203 20.997H3.005v-3.599h7.198zm10.792-3.599h-7.193v3.599h7.193zm.003-7.198H3v3.599h17.998zm-7.195-7.197H3.005v3.599h10.798zm7.197 0h-3.599v3.599H21z"})))},{name:"git",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M23.519 10.947 13.053.482a1.543 1.543 0 0 0-2.183 0L8.696 2.656l2.756 2.756a1.83 1.83 0 0 1 1.886.439 1.84 1.84 0 0 1 .436 1.898l2.656 2.657a1.83 1.83 0 0 1 1.899.436 1.837 1.837 0 0 1 0 2.597 1.84 1.84 0 0 1-2.599 0 1.84 1.84 0 0 1-.4-1.998l-2.478-2.477v6.521a1.837 1.837 0 0 1 .485 2.945 1.837 1.837 0 0 1-2.597 0 1.837 1.837 0 0 1 0-2.598 1.8 1.8 0 0 1 .602-.401V8.85a1.8 1.8 0 0 1-.602-.4 1.84 1.84 0 0 1-.395-2.009L7.628 3.723.452 10.898a1.544 1.544 0 0 0 0 2.184l10.467 10.467a1.544 1.544 0 0 0 2.183 0l10.417-10.418a1.546 1.546 0 0 0 0-2.184"})))},{name:"github",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12c0 4.419 2.865 8.166 6.839 9.489.5.09.682-.218.682-.484 0-.236-.009-.866-.014-1.699-2.782.602-3.369-1.34-3.369-1.34-.455-1.157-1.11-1.465-1.11-1.465-.909-.62.069-.608.069-.608 1.004.071 1.532 1.03 1.532 1.03.891 1.529 2.341 1.089 2.91.833.091-.647.349-1.086.635-1.337-2.22-.251-4.555-1.111-4.555-4.943 0-1.091.39-1.984 1.03-2.682-.103-.254-.447-1.27.097-2.646 0 0 .84-.269 2.75 1.025A9.6 9.6 0 0 1 12 6.836c.85.004 1.705.114 2.504.336 1.909-1.294 2.748-1.025 2.748-1.025.546 1.376.202 2.394.1 2.646.64.699 1.026 1.591 1.026 2.682 0 3.841-2.337 4.687-4.565 4.935.359.307.679.917.679 1.852 0 1.335-.012 2.415-.012 2.741 0 .269.18.579.688.481A10 10 0 0 0 22 12c0-5.523-4.477-10-10-10"})))},{name:"google-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m-.05 16c-3.312 0-6-2.688-6-6s2.688-6 6-6c1.62 0 2.976.594 4.014 1.566L14.26 9.222c-.432-.408-1.188-.888-2.31-.888-1.986 0-3.606 1.65-3.606 3.672s1.62 3.672 3.606 3.672c2.298 0 3.144-1.59 3.3-2.532h-3.306v-2.238h5.616c.084.378.15.732.15 1.23 0 3.426-2.298 5.862-5.76 5.862"})))},{name:"google-plus-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M8 11h6.61c.06.35.11.7.11 1.16 0 4-2.68 6.84-6.72 6.84-3.87 0-7-3.13-7-7s3.13-7 7-7c1.89 0 3.47.69 4.69 1.83l-1.9 1.83c-.52-.5-1.43-1.08-2.79-1.08-2.39 0-4.34 1.98-4.34 4.42S5.61 16.42 8 16.42c2.77 0 3.81-1.99 3.97-3.02H8zm15 0h-2V9h-2v2h-2v2h2v2h2v-2h2"})))},{name:"google-plus",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m-1.919 14.05a4.051 4.051 0 0 1 0-8.1c1.094 0 2.009.401 2.709 1.057l-1.15 1.118a2.23 2.23 0 0 0-1.559-.599c-1.341 0-2.434 1.114-2.434 2.479s1.094 2.479 2.434 2.479c1.551 0 2.122-1.073 2.227-1.709h-2.232v-1.511h3.791c.057.255.101.494.101.83.001 2.312-1.55 3.956-3.887 3.956M19 12.75h-1.25V14h-1.5v-1.25H15v-1.5h1.25V10h1.5v1.25H19z"})))},{name:"google",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12.02 10.18v3.73h5.51c-.26 1.57-1.67 4.22-5.5 4.22-3.31 0-6.01-2.75-6.01-6.12s2.7-6.12 6.01-6.12c1.87 0 3.13.8 3.85 1.48l2.84-2.76C16.99 2.99 14.73 2 12.03 2c-5.52 0-10 4.48-10 10s4.48 10 10 10c5.77 0 9.6-4.06 9.6-9.77 0-.83-.11-1.42-.25-2.05z"})))},{name:"instagram",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 4.622c2.403 0 2.688.009 3.637.052.877.04 1.354.187 1.671.31.42.163.72.358 1.035.673s.51.615.673 1.035c.123.317.27.794.31 1.671.043.949.052 1.234.052 3.637s-.009 2.688-.052 3.637c-.04.877-.187 1.354-.31 1.671-.163.42-.358.72-.673 1.035s-.615.51-1.035.673c-.317.123-.794.27-1.671.31-.949.043-1.233.052-3.637.052s-2.688-.009-3.637-.052c-.877-.04-1.354-.187-1.671-.31a2.8 2.8 0 0 1-1.035-.673 2.8 2.8 0 0 1-.673-1.035c-.123-.317-.27-.794-.31-1.671-.043-.949-.052-1.234-.052-3.637s.009-2.688.052-3.637c.04-.877.187-1.354.31-1.671.163-.42.358-.72.673-1.035s.615-.51 1.035-.673c.317-.123.794-.27 1.671-.31.949-.043 1.234-.052 3.637-.052M12 3c-2.444 0-2.751.01-3.711.054-.958.044-1.612.196-2.184.418a4.4 4.4 0 0 0-1.594 1.039c-.5.5-.808 1.002-1.038 1.594-.223.572-.375 1.226-.419 2.184C3.01 9.249 3 9.556 3 12s.01 2.751.054 3.711c.044.958.196 1.612.418 2.185.23.592.538 1.094 1.038 1.594s1.002.808 1.594 1.038c.572.222 1.227.375 2.185.418.96.044 1.267.054 3.711.054s2.751-.01 3.711-.054c.958-.044 1.612-.196 2.185-.418a4.4 4.4 0 0 0 1.594-1.038c.5-.5.808-1.002 1.038-1.594.222-.572.375-1.227.418-2.185.044-.96.054-1.267.054-3.711s-.01-2.751-.054-3.711c-.044-.958-.196-1.612-.418-2.185A4.4 4.4 0 0 0 19.49 4.51c-.5-.5-1.002-.808-1.594-1.038-.572-.222-1.227-.375-2.185-.418C14.751 3.01 14.444 3 12 3m0 4.378a4.622 4.622 0 1 0 0 9.244 4.622 4.622 0 0 0 0-9.244M12 15a3 3 0 1 1 0-6 3 3 0 0 1 0 6m4.804-8.884a1.08 1.08 0 1 0 .001 2.161 1.08 1.08 0 0 0-.001-2.161"})))},{name:"json-feed",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"m8.522 17.424.027.027c1.076-1.076 1.854-.993 3.154.306l2.053 2.053c2.136 2.136 4.131 2.028 6.515-.356l.729-.728-1.548-1.548-.373.373c-1.349 1.349-2.293 1.366-3.585.075l-2.409-2.409c-1.242-1.242-2.475-1.366-3.659-.381l-.232-.232c1.01-1.225.911-2.368-.29-3.568l-2.16-2.162c-1.317-1.317-1.308-2.236.058-3.602l.372-.372-1.54-1.54-.728.729c-2.393 2.393-2.525 4.346-.439 6.433l1.78 1.78c1.3 1.3 1.383 2.095.315 3.163l.008.008a1.384 1.384 0 0 0 1.952 1.951"}),React.createElement("circle",{cx:"13.089",cy:"10.905",r:"1.383"}),React.createElement("circle",{cx:"16.349",cy:"7.644",r:"1.383"}),React.createElement("circle",{cx:"19.61",cy:"4.383",r:"1.383"})))},{name:"line",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M14.255 9.572v3.333c0 .084-.066.15-.15.15h-.534a.16.16 0 0 1-.122-.061l-1.528-2.063v1.978c0 .084-.066.15-.15.15h-.534a.15.15 0 0 1-.15-.15V9.576c0-.084.066-.15.15-.15h.529a.14.14 0 0 1 .122.066l1.528 2.063V9.577c0-.084.066-.15.15-.15h.534a.15.15 0 0 1 .155.145m-3.844-.15h-.534a.15.15 0 0 0-.15.15v3.333c0 .084.066.15.15.15h.534c.084 0 .15-.066.15-.15V9.572c0-.08-.066-.15-.15-.15m-1.289 2.794H7.664V9.572a.15.15 0 0 0-.15-.15H6.98a.15.15 0 0 0-.15.15v3.333q0 .062.042.103a.16.16 0 0 0 .103.042h2.142c.084 0 .15-.066.15-.15v-.534a.15.15 0 0 0-.145-.15m7.945-2.794h-2.142c-.08 0-.15.066-.15.15v3.333c0 .08.066.15.15.15h2.142c.084 0 .15-.066.15-.15v-.534a.15.15 0 0 0-.15-.15h-1.458v-.563h1.458c.084 0 .15-.066.15-.15v-.539a.15.15 0 0 0-.15-.15h-1.458v-.563h1.458c.084 0 .15-.066.15-.15v-.534c-.005-.08-.07-.15-.15-.15M22.5 5.33v13.373c-.005 2.1-1.725 3.802-3.83 3.797H5.297c-2.1-.005-3.802-1.73-3.797-3.83V5.297c.005-2.1 1.73-3.802 3.83-3.797h13.373c2.1.005 3.802 1.725 3.797 3.83m-2.888 5.747c0-3.422-3.431-6.206-7.645-6.206s-7.645 2.784-7.645 6.206c0 3.066 2.719 5.634 6.394 6.122.895.192.792.52.591 1.725-.033.192-.155.755.661.413s4.402-2.592 6.009-4.439c1.106-1.219 1.636-2.452 1.636-3.82"})))},{name:"link",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M17 13H7v-2h10zm1-6h-1c-1.631 0-3.065.792-3.977 2H18c1.103 0 2 .897 2 2v2c0 1.103-.897 2-2 2h-4.977c.913 1.208 2.347 2 3.977 2h1a4 4 0 0 0 4-4v-2a4 4 0 0 0-4-4M2 11v2a4 4 0 0 0 4 4h1c1.63 0 3.065-.792 3.977-2H6c-1.103 0-2-.897-2-2v-2c0-1.103.897-2 2-2h4.977C10.065 7.792 8.631 7 7 7H6a4 4 0 0 0-4 4"})))},{name:"linkedin",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19.7 3H4.3A1.3 1.3 0 0 0 3 4.3v15.4A1.3 1.3 0 0 0 4.3 21h15.4a1.3 1.3 0 0 0 1.3-1.3V4.3A1.3 1.3 0 0 0 19.7 3M8.339 18.338H5.667v-8.59h2.672zM7.004 8.574a1.548 1.548 0 1 1-.002-3.096 1.548 1.548 0 0 1 .002 3.096m11.335 9.764H15.67v-4.177c0-.996-.017-2.278-1.387-2.278-1.389 0-1.601 1.086-1.601 2.206v4.249h-2.667v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.779 3.203 4.092v4.711z"})))},{name:"mail",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M20 4H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2m0 4.236-8 4.882-8-4.882V6h16z"})))},{name:"mastodon",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M11.973 2.352c-2.468.02-4.842.286-6.225.921 0 0-2.742 1.229-2.742 5.415 0 .958-.018 2.105.012 3.32.1 4.094.75 8.128 4.535 9.129 1.745.462 3.244.56 4.45.494 2.19-.122 3.417-.781 3.417-.781l-.072-1.588s-1.565.491-3.32.431c-1.74-.06-3.576-.188-3.858-2.324a4 4 0 0 1-.04-.598s1.709.416 3.874.516c1.324.06 2.563-.076 3.824-.226 2.418-.29 4.524-1.78 4.79-3.141.416-2.144.38-5.232.38-5.232 0-4.186-2.74-5.415-2.74-5.415-1.383-.635-3.76-.9-6.227-.921zM9.18 5.622c1.028 0 1.804.395 2.318 1.185l.502.84.5-.84c.514-.79 1.292-1.186 2.32-1.186.888 0 1.605.313 2.15.922q.795.915.794 2.469v5.068h-2.008V9.16c0-1.037-.438-1.562-1.31-1.562-.966 0-1.448.622-1.448 1.857v2.693h-1.996V9.455c0-1.235-.484-1.857-1.45-1.857-.872 0-1.308.525-1.308 1.562v4.92H6.236V9.012q-.001-1.554.793-2.469c.547-.609 1.263-.922 2.15-.922"})))},{name:"medium-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{fillRule:"nonzero",d:"M7.423 6c3.27 0 5.922 2.686 5.922 6s-2.651 6-5.922 6S1.5 15.313 1.5 12s2.652-6 5.923-6m9.458.351c1.635 0 2.961 2.53 2.961 5.65 0 3.118-1.325 5.648-2.96 5.648S13.92 15.119 13.92 12s1.325-5.649 2.96-5.649m4.577.589c.576 0 1.042 2.265 1.042 5.06s-.466 5.06-1.042 5.06c-.575 0-1.04-2.265-1.04-5.06s.465-5.06 1.04-5.06"})))},{name:"medium",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M3 3v18h18V3zm15 4.26-1 .93a.28.28 0 0 0-.11.27v6.8a.27.27 0 0 0 .11.27l.94.93v.2h-4.75v-.2l1-1c.09-.1.09-.12.09-.27V9.74l-2.71 6.9h-.37L8 9.74v4.62a.67.67 0 0 0 .17.54l1.27 1.54v.2H5.86v-.2l1.27-1.54a.64.64 0 0 0 .17-.54V9a.5.5 0 0 0-.16-.4L6 7.26v-.2h3.52L12.23 13l2.38-5.94H18z"})))},{name:"messenger",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12.026.375C5.462.375.375 5.172.375 11.652c0 3.389 1.393 6.318 3.66 8.341.391.352.311.556.377 2.73a.934.934 0 0 0 1.307.823c2.48-1.092 2.512-1.178 2.933-1.064 7.185 1.977 14.973-2.621 14.973-10.83 0-6.48-5.035-11.277-11.599-11.277m6.996 8.678L15.6 14.47a1.75 1.75 0 0 1-2.527.465l-2.723-2.038a.7.7 0 0 0-.844 0l-3.674 2.786c-.49.372-1.133-.216-.802-.735l3.422-5.417a1.75 1.75 0 0 1 2.527-.465l2.722 2.037a.7.7 0 0 0 .844 0L18.22 8.32c.489-.374 1.132.213.801.732"})))},{name:"microblog",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19.641 17.086c1.294-1.522 2.067-3.438 2.067-5.521 0-4.957-4.371-8.972-9.763-8.972s-9.763 4.015-9.763 8.972 4.371 8.972 9.763 8.972a10.5 10.5 0 0 0 3.486-.59.315.315 0 0 1 .356.112c.816 1.101 2.09 1.876 3.506 2.191a.194.194 0 0 0 .192-.309 3.82 3.82 0 0 1 .162-4.858zm-3.065-6.575-2.514 1.909.912 3.022a.286.286 0 0 1-.437.317l-2.592-1.802-2.592 1.802a.285.285 0 0 1-.436-.317l.912-3.022-2.515-1.909a.285.285 0 0 1 .167-.513l3.155-.066 1.038-2.981a.285.285 0 0 1 .539 0l1.038 2.981 3.155.066a.285.285 0 0 1 .17.513"})))},{name:"nextdoor",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",strokeMiterlimit:"10",viewBox:"0 0 130 130"},React.createElement("g",null,React.createElement("path",{d:"M64.25 3.531c-31.144.337-57.596 24.22-60.469 55.907-3.064 33.799 21.857 63.685 55.657 66.75s63.685-21.857 66.75-55.657-21.857-63.686-55.657-66.75a62 62 0 0 0-6.281-.25m3.938 34.907C82.468 38.438 93.5 48.58 93.5 61.5v27c0 .685-.565 1.25-1.25 1.25H80.906a1.267 1.267 0 0 1-1.25-1.25V63.375c0-5.58-4.309-11.937-11.469-11.937-7.47 0-11.468 6.357-11.468 11.937V88.5c0 .685-.565 1.25-1.25 1.25H44.125c-.68 0-1.219-.57-1.219-1.25V64.156c0-.74-.529-1.364-1.25-1.531-13.13-2.93-15.115-10.285-15.375-21.125-.005-.332.142-.67.375-.906.233-.237.543-.375.875-.375l11.688.062c.66.01 1.187.529 1.218 1.188.13 4.44.438 9.406 4.438 9.406.83 0 1.443-1.179 1.813-1.719 4.41-6.48 12.28-10.718 21.5-10.718"})))},{name:"patreon",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M20 7.408c-.003-2.299-1.746-4.182-3.79-4.862-2.54-.844-5.888-.722-8.312.453-2.939 1.425-3.862 4.545-3.896 7.656-.028 2.559.22 9.297 3.92 9.345 2.75.036 3.159-3.603 4.43-5.356.906-1.247 2.071-1.599 3.506-1.963 2.465-.627 4.146-2.626 4.142-5.273"})))},{name:"pinterest-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12.289 2C6.617 2 3.606 5.648 3.606 9.622c0 1.846 1.025 4.146 2.666 4.878.25.111.381.063.439-.169.044-.175.267-1.029.365-1.428a.37.37 0 0 0-.091-.362c-.54-.63-.975-1.791-.975-2.873 0-2.777 2.194-5.464 5.933-5.464 3.23 0 5.49 2.108 5.49 5.122 0 3.407-1.794 5.768-4.13 5.768-1.291 0-2.257-1.021-1.948-2.277.372-1.495 1.089-3.112 1.089-4.191 0-.967-.542-1.775-1.663-1.775-1.319 0-2.379 1.309-2.379 3.059 0 1.115.394 1.869.394 1.869s-1.302 5.279-1.54 6.261c-.405 1.666.053 4.368.094 4.604.021.126.167.169.25.063.129-.165 1.699-2.419 2.142-4.051.158-.59.817-2.995.817-2.995.43.784 1.681 1.446 3.013 1.446 3.963 0 6.822-3.494 6.822-7.833C20.394 5.112 16.849 2 12.289 2"})))},{name:"pinterest",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12c0 4.236 2.636 7.855 6.356 9.312-.087-.791-.166-2.005.035-2.869.182-.78 1.173-4.971 1.173-4.971s-.299-.599-.299-1.484c0-1.39.806-2.429 1.809-2.429.853 0 1.265.641 1.265 1.409 0 .858-.546 2.141-.828 3.329-.236.996.499 1.807 1.481 1.807 1.777 0 3.144-1.874 3.144-4.579 0-2.394-1.72-4.068-4.177-4.068-2.845 0-4.515 2.134-4.515 4.34 0 .859.331 1.781.744 2.282a.3.3 0 0 1 .069.287c-.077.316-.246.995-.279 1.134-.044.183-.145.222-.334.134-1.249-.581-2.03-2.407-2.03-3.874 0-3.154 2.292-6.051 6.607-6.051 3.469 0 6.165 2.472 6.165 5.775 0 3.446-2.173 6.22-5.189 6.22-1.013 0-1.966-.526-2.292-1.148l-.623 2.377c-.226.869-.835 1.957-1.243 2.622.936.289 1.93.445 2.961.445 5.523 0 10-4.477 10-10S17.523 2 12 2"})))},{name:"pocket",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M21.927 4.194A1.82 1.82 0 0 0 20.222 3H3.839a1.823 1.823 0 0 0-1.813 1.814v6.035l.069 1.2c.29 2.73 1.707 5.115 3.899 6.778l.119.089.025.018a9.9 9.9 0 0 0 3.91 1.727 10.06 10.06 0 0 0 4.049-.014.3.3 0 0 0 .064-.023 9.9 9.9 0 0 0 3.753-1.691l.025-.018q.06-.043.119-.089c2.192-1.664 3.609-4.049 3.898-6.778l.069-1.2V4.814a1.8 1.8 0 0 0-.098-.62m-4.235 6.287-4.704 4.512a1.37 1.37 0 0 1-1.898 0l-4.705-4.512a1.371 1.371 0 1 1 1.898-1.979l3.756 3.601 3.755-3.601a1.372 1.372 0 0 1 1.898 1.979"})))},{name:"polldaddy",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.487 2 2 6.487 2 12c0 5.514 4.487 10 10 10 5.514 0 10-4.486 10-10 0-5.513-4.486-10-10-10m.991 1.68c2.361.084 4.657 1.251 6.197 3.136.283.334.541.693.774 1.067a7.78 7.78 0 0 0-6.094-2.94 7.76 7.76 0 0 0-5.896 2.703q-.008.006-.016.014l-.152.159-.031.032a6.12 6.12 0 0 0-1.633 4.165 6.15 6.15 0 0 0 6.143 6.143c.57 0 1.123-.081 1.649-.227-1.849.839-4.131.747-5.926-.324-1.841-1.089-3.171-3.111-3.433-5.313A7.39 7.39 0 0 1 6.69 6.137C8.294 4.5 10.634 3.563 12.991 3.68m3.373 8.519c-.049-2.024-1.587-3.889-3.544-4.174-1.927-.343-3.917.857-4.451 2.661a3.67 3.67 0 0 0 .2 2.653c.39.8 1.067 1.451 1.894 1.759 1.664.654 3.63-.27 4.173-1.863.593-1.58-.396-3.423-1.94-3.776-1.52-.407-3.161.757-3.204 2.243a2.36 2.36 0 0 0 .753 1.879c.501.476 1.23.667 1.871.529a2.07 2.07 0 0 0 1.469-1.134 1.91 1.91 0 0 0-.087-1.767c-.297-.513-.859-.863-1.429-.881a1.7 1.7 0 0 0-1.437.679 1.53 1.53 0 0 0-.18 1.489q.006.016.016.03c.193.634.774 1.1 1.467 1.117a1.6 1.6 0 0 1-.97-.183c-.466-.244-.809-.747-.893-1.29a1.8 1.8 0 0 1 .499-1.539 2.02 2.02 0 0 1 1.58-.606c.593.04 1.159.35 1.517.859.364.496.51 1.156.383 1.773-.116.62-.529 1.174-1.093 1.514a2.52 2.52 0 0 1-1.914.286c-.65-.161-1.226-.606-1.584-1.206a2.83 2.83 0 0 1-.341-2.031c.143-.7.573-1.321 1.176-1.753 1.193-.883 3.056-.751 4.106.411 1.106 1.1 1.327 3.027.406 4.371-.877 1.376-2.74 2.086-4.374 1.594-1.639-.449-2.913-2.079-3.031-3.853-.07-.884.13-1.797.583-2.577.445-.777 1.155-1.432 1.972-1.862 1.64-.88 3.816-.743 5.349.424 1.251.924 2.083 2.42 2.236 4.009l.001.03c0 2.9-2.359 5.26-5.26 5.26a5.2 5.2 0 0 1-1.947-.376 5 5 0 0 0 2.613-.079 4.96 4.96 0 0 0 2.514-1.751c.618-.828.95-1.861.901-2.869M12 21.113c-5.024 0-9.111-4.087-9.111-9.113 0-4.789 3.713-8.723 8.411-9.081a7 7 0 0 0-.397.06c-2.644.453-5.017 2.106-6.32 4.409-1.309 2.301-1.391 5.19-.3 7.527 1.056 2.34 3.253 4.156 5.776 4.553 2.497.44 5.133-.483 6.787-2.301 1.719-1.797 2.269-4.529 1.486-6.796-.583-1.81-1.976-3.331-3.7-4.046 3.417.594 6.174 3.221 6.174 6.781 0 1.004-.241 2.02-.657 2.966-1.498 2.984-4.586 5.041-8.149 5.041"})))},{name:"print",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M9 16h6v2H9zm13 1h-3v3a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-3H2V9a2 2 0 0 1 2-2h1V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v2h1a2 2 0 0 1 2 2zM7 7h10V5H7zm10 7H7v6h10zm3-3.5a1.5 1.5 0 1 0-3.001.001A1.5 1.5 0 0 0 20 10.5"})))},{name:"quora",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M47.736 16.521c-.41-.81-.898-1.631-1.846-1.631a1 1 0 0 0-.527.107l-.322-.644a2.93 2.93 0 0 1 1.836-.595c1.26 0 1.914.605 2.431 1.397a6.8 6.8 0 0 0 .449-2.675c0-2.773-.869-4.199-2.929-4.199-1.992 0-2.851 1.465-2.851 4.199s.859 4.17 2.851 4.17a4 4 0 0 0 .869-.107zm.498.966a6 6 0 0 1-1.367.185 5.27 5.27 0 0 1-5.263-5.204c0-3.114 2.558-5.233 5.263-5.233s5.282 2.109 5.282 5.233a5.08 5.08 0 0 1-1.992 4.072c.381.566.781.956 1.319.956.595 0 .839-.459.878-.82h.781c.049.488-.195 2.48-2.373 2.48-1.319 0-2.012-.761-2.529-1.66zm5.624-2.646v-3.563c0-.371-.146-.586-.615-.586h-.498v-.956h3.251v5.048c0 .849.459 1.231 1.161 1.231a1.56 1.56 0 0 0 1.465-.839V11.28c0-.371-.146-.586-.615-.586h-.527v-.957h3.28v5.302c0 .527.195.732.8.732h.107v.976l-2.929.468V16.21h-.057a3.12 3.12 0 0 1-2.509 1.152c-1.28 0-2.304-.644-2.304-2.558zm12.059 1.611c1.152 0 1.592-1.005 1.611-3.027.02-1.982-.459-2.929-1.611-2.929-1.005 0-1.641.956-1.641 2.929 0 2.021.625 3.027 1.641 3.027m0 .956a3.906 3.906 0 0 1-3.974-3.974c0-2.334 1.836-3.886 3.974-3.886 2.226 0 4.004 1.582 4.004 3.886a3.867 3.867 0 0 1-4.004 3.974m4.072-.146v-.956h.312c.781 0 .859-.224.859-.908v-4.121c0-.371-.215-.586-.732-.586h-.42v-.955h2.968l.146 1.553h.108c.371-1.113 1.221-1.699 2.051-1.699.693 0 1.221.39 1.221 1.181 0 .547-.264 1.093-1.005 1.093-.664 0-.8-.449-1.358-.449-.488 0-.869.468-.869 1.152v2.783c0 .673.166.908.937.908h.439v.956h-4.658zm9.901-1.093c.956 0 1.338-.898 1.338-1.797v-1.211c-.732.722-2.304.742-2.304 2.021 0 .625.371.986.966.986m1.387 0c-.39.752-1.191 1.26-2.314 1.26-1.309 0-2.148-.732-2.148-1.914 0-2.451 3.417-1.797 4.423-3.427v-.185c0-1.25-.488-1.445-1.035-1.445-1.524 0-.83 1.631-2.226 1.631-.673 0-.937-.371-.937-.859 0-.927 1.093-1.67 3.173-1.67 1.963 0 3.163.537 3.163 2.49v3.114q-.02.742.595.742a1 1 0 0 0 .449-.127l.254.615c-.205.312-.752.869-1.836.869-.908 0-1.465-.42-1.543-1.113h-.01zm-68.554 2.558c-.83-1.641-1.807-3.3-3.711-3.3a2.9 2.9 0 0 0-1.093.215l-.644-1.299a5.66 5.66 0 0 1 3.662-1.211c2.548 0 3.857 1.231 4.892 2.792q.917-2.012.908-5.38c0-5.585-1.748-8.417-5.829-8.417-4.033 0-5.76 2.87-5.76 8.417s1.738 8.397 5.76 8.397a5.9 5.9 0 0 0 1.748-.224zm.996 1.953a9.8 9.8 0 0 1-2.744.371C5.614 21.041.371 16.764.371 10.545.371 4.277 5.614 0 10.965 0c5.448 0 10.642 4.248 10.642 10.545a10.25 10.25 0 0 1-4.013 8.201c.732 1.152 1.563 1.914 2.665 1.914 1.201 0 1.689-.927 1.768-1.66h1.572c.088.966-.4 4.999-4.775 4.999-2.646 0-4.052-1.543-5.106-3.339z"})))},{name:"reddit",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M22 11.816a2.28 2.28 0 0 0-2.277-2.277c-.593 0-1.122.24-1.526.614-1.481-.965-3.455-1.594-5.647-1.69l1.171-3.702 3.18.748a1.88 1.88 0 0 0 1.876 1.862 1.88 1.88 0 0 0 1.877-1.878 1.88 1.88 0 0 0-1.877-1.877c-.769 0-1.431.466-1.72 1.13l-3.508-.826a.386.386 0 0 0-.46.261l-1.35 4.268c-2.316.038-4.411.67-5.97 1.671a2.24 2.24 0 0 0-1.492-.581A2.28 2.28 0 0 0 2 11.816c0 .814.433 1.523 1.078 1.925a4 4 0 0 0-.061.672c0 3.292 4.011 5.97 8.941 5.97s8.941-2.678 8.941-5.97q-.002-.32-.053-.632A2.26 2.26 0 0 0 22 11.816m-3.224-7.422a1.1 1.1 0 1 1-.001 2.199 1.1 1.1 0 0 1 .001-2.199M2.777 11.816c0-.827.672-1.5 1.499-1.5.313 0 .598.103.838.269-.851.676-1.477 1.479-1.812 2.36a1.48 1.48 0 0 1-.525-1.129m9.182 7.79c-4.501 0-8.164-2.329-8.164-5.193S7.457 9.22 11.959 9.22s8.164 2.329 8.164 5.193-3.663 5.193-8.164 5.193m8.677-6.605c-.326-.89-.948-1.701-1.797-2.384.248-.186.55-.301.883-.301.827 0 1.5.673 1.5 1.5.001.483-.23.911-.586 1.185m-11.64 1.703c-.76 0-1.397-.616-1.397-1.376s.637-1.397 1.397-1.397 1.376.637 1.376 1.397-.616 1.376-1.376 1.376m7.405-1.376c0 .76-.616 1.376-1.376 1.376s-1.399-.616-1.399-1.376.639-1.397 1.399-1.397 1.376.637 1.376 1.397m-1.172 3.38a.39.39 0 0 1 0 .55c-.674.674-1.727 1.002-3.219 1.002l-.011-.002-.011.002c-1.492 0-2.544-.328-3.218-1.002a.389.389 0 1 1 .55-.55c.521.521 1.394.775 2.669.775l.011.002.011-.002c1.275 0 2.148-.253 2.669-.775a.387.387 0 0 1 .549 0"})))},{name:"share",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M18 16c-.788 0-1.499.31-2.034.807L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.048 4.118A3 3 0 0 0 15 19a3 3 0 1 0 3-3"})))},{name:"skype",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"m10.113 2.699.1-.02q.05.025.098.051zM2.72 10.223l-.017.103q.025.048.051.095zm18.555 3.548q.009-.053.018-.106-.025-.047-.052-.095zm-7.712 7.428q.049.027.096.053l.105-.017zM22 16.386a5.55 5.55 0 0 1-1.637 3.953 5.55 5.55 0 0 1-3.953 1.637 5.6 5.6 0 0 1-2.75-.725l.105-.017-.202-.035q.049.027.096.053a9.5 9.5 0 0 1-1.654.147 9.4 9.4 0 0 1-3.676-.743 9.4 9.4 0 0 1-3.002-2.023 9.4 9.4 0 0 1-2.023-3.002 9.4 9.4 0 0 1-.743-3.676c0-.546.049-1.093.142-1.628q.025.048.051.095l-.034-.199-.017.103A5.6 5.6 0 0 1 2 7.615c0-1.493.582-2.898 1.637-3.953A5.56 5.56 0 0 1 7.59 2.024c.915 0 1.818.228 2.622.655l-.1.02.199.031q-.049-.026-.098-.051l.004-.001a9.5 9.5 0 0 1 1.788-.169 9.41 9.41 0 0 1 6.678 2.766 9.4 9.4 0 0 1 2.024 3.002 9.4 9.4 0 0 1 .743 3.676c0 .575-.054 1.15-.157 1.712q-.025-.047-.052-.095l.034.201q.009-.053.018-.106c.461.829.707 1.767.707 2.721m-5.183-2.248c0-1.331-.613-2.743-3.033-3.282l-2.209-.49c-.84-.192-1.807-.444-1.807-1.237s.679-1.348 1.903-1.348c2.468 0 2.243 1.696 3.468 1.696.645 0 1.209-.379 1.209-1.031 0-1.521-2.435-2.663-4.5-2.663-2.242 0-4.63.952-4.63 3.488 0 1.221.436 2.521 2.839 3.123l2.984.745c.903.223 1.129.731 1.129 1.189 0 .762-.758 1.507-2.129 1.507-2.679 0-2.307-2.062-3.743-2.062-.645 0-1.113.444-1.113 1.078 0 1.236 1.501 2.886 4.856 2.886 3.195 0 4.776-1.538 4.776-3.599"})))},{name:"sms",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M17.696 4C20.069 4 22 5.973 22 8.398v4.357c0 2.04-1.368 3.783-3.261 4.266v4.427l-5.234-4.295h-7.2C3.93 17.153 2 15.18 2 12.755V8.398C2 5.973 3.931 4 6.304 4zM7.028 8.515c-.98 0-1.66.562-1.66 1.349-.009.497.322.91.985 1.178l.39.142c.242.097.305.171.305.297 0 .162-.131.251-.442.251s-.76-.135-1.004-.284l-.112.046-.215.868c.359.258.832.364 1.33.364 1.104 0 1.764-.523 1.764-1.333-.008-.574-.305-.956-.954-1.216l-.393-.146c-.266-.108-.341-.181-.341-.287 0-.152.131-.243.387-.243.274 0 .587.093.808.214l.109-.047.214-.837c-.315-.224-.741-.316-1.171-.316m10.302 0c-.98 0-1.66.562-1.66 1.349-.008.497.322.91.985 1.178l.39.142c.243.097.305.171.305.297 0 .162-.13.251-.442.251-.311 0-.76-.135-1.004-.284l-.112.046-.215.868c.359.258.832.364 1.33.364 1.104 0 1.764-.523 1.764-1.333-.008-.574-.305-.956-.954-1.216l-.393-.146c-.266-.108-.341-.181-.341-.287 0-.152.131-.243.387-.243.274 0 .587.093.808.214l.109-.047.214-.837c-.316-.224-.741-.316-1.171-.316m-3.733 0c-.297 0-.55.066-.78.202l-.144.098a2 2 0 0 0-.264.247l-.078.095-.027-.077c-.15-.34-.55-.565-1.033-.565l-.169.007a1.36 1.36 0 0 0-.896.42l-.08.09-.038-.363-.075-.067H8.994l-.075.079.024.634c.005.2.008.397.008.604v2.652l.075.075h1.178l.075-.075v-2.269q-.002-.168.042-.274c.083-.23.262-.392.496-.392.314 0 .483.267.483.753v2.182l.075.075h1.179l.075-.075v-2.277c0-.097.016-.213.043-.285.077-.224.26-.373.486-.373.33 0 .5.272.5.817v2.118l.074.075h1.179l.075-.075v-2.293c0-1.131-.537-1.763-1.39-1.763Z"})))},{name:"snapchat",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M11.989 1.728c3.221.001 5.904 2.683 5.908 5.912q.002 1.133.067 2.094a.737.737 0 0 0 .902.669l1.009-.237a.6.6 0 0 1 .129-.015c.256 0 .492.175.55.434a.74.74 0 0 1-.479.861l-1.532.618a.823.823 0 0 0-.485.98c1.229 4.543 4.661 4.071 4.661 4.662 0 .743-2.587.848-2.821 1.082s-.01 1.368-.532 1.588a1.1 1.1 0 0 1-.409.056c-.393 0-.95-.077-1.536-.077-.509 0-1.04.058-1.507.273-1.239.572-2.433 1.641-3.914 1.641S9.325 21.2 8.086 20.628c-.467-.216-.998-.273-1.507-.273-.586 0-1.143.077-1.536.077-.17 0-.31-.014-.409-.056-.522-.22-.299-1.354-.532-1.588s-2.821-.337-2.821-1.08c0-.592 3.432-.119 4.661-4.662a.824.824 0 0 0-.486-.98l-1.532-.618a.74.74 0 0 1-.479-.861.56.56 0 0 1 .679-.419l1.009.237q.086.02.169.02a.737.737 0 0 0 .733-.689q.065-.961.067-2.094c.004-3.229 2.666-5.91 5.887-5.912m0-1.281c-.961 0-1.898.194-2.784.574A7.2 7.2 0 0 0 6.93 2.572a7.2 7.2 0 0 0-1.539 2.282A7.1 7.1 0 0 0 4.82 7.64a33 33 0 0 1-.029 1.369l-.375-.088a2 2 0 0 0-.421-.049 1.86 1.86 0 0 0-1.135.389 1.84 1.84 0 0 0-.666 1.049 2.024 2.024 0 0 0 1.271 2.335l1.124.454c-.744 2.285-2.117 2.723-3.041 3.018a5 5 0 0 0-.659.246C.087 16.76 0 17.436 0 17.708c0 .521.247.996.694 1.339.223.17.499.311.844.43.47.162 1.016.265 1.459.347.021.164.053.341.106.518.22.738.684 1.069 1.034 1.217.332.14.676.156.905.156.224 0 .462-.018.713-.036.269-.02.548-.041.823-.041.426 0 .743.051.97.155.311.144.64.337.989.542.972.571 2.073 1.217 3.462 1.217s2.49-.647 3.462-1.217c.349-.205.679-.399.989-.542.226-.105.544-.155.97-.155.275 0 .554.021.823.041.251.019.488.036.713.036.229 0 .573-.016.905-.156.35-.147.814-.478 1.034-1.217.053-.178.084-.354.106-.518.443-.082.989-.185 1.459-.347.345-.119.621-.259.844-.43.448-.342.694-.818.694-1.339 0-.272-.087-.948-.891-1.347a5 5 0 0 0-.659-.246c-.924-.295-2.297-.733-3.041-3.018l1.124-.454a2.025 2.025 0 0 0 1.271-2.335 1.83 1.83 0 0 0-.666-1.049 1.86 1.86 0 0 0-1.556-.34l-.375.088a33 33 0 0 1-.029-1.369 7.1 7.1 0 0 0-.575-2.789c-.365-.853-.886-1.62-1.547-2.282s-1.428-1.182-2.28-1.547a7.1 7.1 0 0 0-2.786-.574"})))},{name:"soundcloud",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M23.587 13.923a3.303 3.303 0 0 1-3.344 3.117h-8.037a.674.674 0 0 1-.667-.67V7.717a.74.74 0 0 1 .444-.705s.739-.512 2.296-.512a5.27 5.27 0 0 1 2.702.742 5.35 5.35 0 0 1 2.516 3.485 3.1 3.1 0 0 1 .852-.116 3.217 3.217 0 0 1 3.237 3.312m-13.05-5.659c.242 2.935.419 5.612 0 8.538a.261.261 0 0 1-.519 0c-.39-2.901-.221-5.628 0-8.538a.26.26 0 0 1 .398-.25.26.26 0 0 1 .12.25zm-1.627 8.541a.273.273 0 0 1-.541 0 32.7 32.7 0 0 1 0-7.533.274.274 0 0 1 .544 0 29.4 29.4 0 0 1-.003 7.533m-1.63-7.788c.264 2.69.384 5.099-.003 7.782a.262.262 0 0 1-.522 0c-.374-2.649-.249-5.127 0-7.782a.264.264 0 0 1 .525 0m-1.631 7.792a.268.268 0 0 1-.532 0 27.6 27.6 0 0 1 0-7.034.27.27 0 1 1 .541 0 25.8 25.8 0 0 1-.01 7.034zm-1.63-5.276c.412 1.824.227 3.435-.015 5.294a.255.255 0 0 1-.504 0c-.22-1.834-.402-3.482-.015-5.295a.268.268 0 0 1 .535 0m-1.626-.277c.378 1.869.254 3.451-.01 5.325-.031.277-.506.28-.531 0-.239-1.846-.352-3.476-.01-5.325a.277.277 0 0 1 .551 0m-1.643.907c.396 1.239.261 2.246-.016 3.517a.258.258 0 0 1-.514 0c-.239-1.246-.336-2.274-.021-3.517a.276.276 0 0 1 .55 0z"})))},{name:"spotify",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m4.586 14.424a.62.62 0 0 1-.857.207c-2.348-1.435-5.304-1.76-8.785-.964a.622.622 0 1 1-.277-1.215c3.809-.871 7.077-.496 9.713 1.115a.623.623 0 0 1 .206.857M17.81 13.7a.78.78 0 0 1-1.072.257c-2.687-1.652-6.785-2.131-9.965-1.166A.779.779 0 1 1 6.32 11.3c3.632-1.102 8.147-.568 11.234 1.328a.78.78 0 0 1 .256 1.072m.105-2.835c-3.223-1.914-8.54-2.09-11.618-1.156a.935.935 0 1 1-.542-1.79c3.532-1.072 9.404-.865 13.115 1.338a.936.936 0 1 1-.955 1.608"})))},{name:"squarespace",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M20.87 9.271a3.86 3.86 0 0 0-5.458 0l-6.141 6.141a.964.964 0 1 0 1.365 1.364l6.14-6.14a1.929 1.929 0 1 1 2.729 2.729l-6.022 6.022a1.93 1.93 0 0 0 2.729 0l4.658-4.658a3.86 3.86 0 0 0 0-5.458m-2.047 2.047a.965.965 0 0 0-1.365 0l-6.14 6.14a1.93 1.93 0 0 1-2.729 0 .964.964 0 1 0-1.364 1.364 3.86 3.86 0 0 0 5.458 0l6.14-6.14a.966.966 0 0 0 0-1.364m-2.047-6.141a3.86 3.86 0 0 0-5.458 0l-6.14 6.14a.964.964 0 1 0 1.364 1.364l6.141-6.14a1.93 1.93 0 0 1 2.729 0 .965.965 0 1 0 1.364-1.364m-2.047 2.047a.964.964 0 0 0-1.364 0l-6.14 6.141a1.929 1.929 0 1 1-2.729-2.729l6.022-6.022a1.93 1.93 0 0 0-2.729 0L3.13 9.271a3.86 3.86 0 0 0 5.458 5.458l6.14-6.141a.963.963 0 0 0 .001-1.364"})))},{name:"stackexchange",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M4 11.606h16v3.272H4zM4 7.377h16v3.272H4zM17.514 3H6.55C5.147 3 4 4.169 4 5.614v.848h16v-.85C20 4.167 18.895 3 17.514 3M4 15.813v.85c0 1.445 1.147 2.614 2.55 2.614h6.799v3.463l3.357-3.463h.744c1.402 0 2.55-1.169 2.55-2.614v-.85z"})))},{name:"stackoverflow",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M18.18 20.103V14.78h1.767v7.09H4v-7.09h1.767v5.323z"}),React.createElement("path",{d:"m7.717 14.275 8.673 1.813.367-1.744-8.673-1.813zm1.147-4.13 8.031 3.74.734-1.606-8.031-3.763zm2.226-3.946 6.815 5.667 1.124-1.354-6.815-5.667zM15.495 2l-1.423 1.055 5.277 7.113 1.423-1.055zM7.533 18.314h8.857v-1.767H7.533z"})))},{name:"stumbleupon",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 4.294a4.47 4.47 0 0 0-4.471 4.471v6.353a1.059 1.059 0 1 1-2.118 0v-2.824H2v2.941a4.471 4.471 0 0 0 8.942 0v-6.47a1.059 1.059 0 1 1 2.118 0v1.294l1.412.647 2-.647V8.765A4.473 4.473 0 0 0 12 4.294m1.059 8.059v2.882a4.471 4.471 0 0 0 8.941 0v-2.824h-3.412v2.824a1.059 1.059 0 1 1-2.118 0v-2.882l-2 .647z"})))},{name:"substack",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19.904 9.182H4.095V7.054h15.81v2.127M4.095 11.109V21L12 16.583 19.905 21v-9.891zM19.905 3H4.095v2.127h15.81z"})))},{name:"telegram",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m3.08 14.757s-.25.625-.936.325l-2.541-1.949-1.63 1.486s-.127.096-.266.036c0 0-.12-.011-.27-.486s-.911-2.972-.911-2.972L6 12.349s-.387-.137-.425-.438c-.037-.3.437-.462.437-.462l10.03-3.934s.824-.362.824.238z"})))},{name:"threads",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 192 192"},React.createElement("g",null,React.createElement("path",{d:"M141.537 88.988a67 67 0 0 0-2.518-1.143c-1.482-27.307-16.403-42.94-41.457-43.1h-.34c-14.986 0-27.449 6.396-35.12 18.036l13.779 9.452c5.73-8.695 14.724-10.548 21.348-10.548h.229c8.249.053 14.474 2.452 18.503 7.129 2.932 3.405 4.893 8.111 5.864 14.05-7.314-1.243-15.224-1.626-23.68-1.14-23.82 1.371-39.134 15.264-38.105 34.568.522 9.792 5.4 18.216 13.735 23.719 7.047 4.652 16.124 6.927 25.557 6.412 12.458-.683 22.231-5.436 29.049-14.127 5.178-6.6 8.453-15.153 9.899-25.93 5.937 3.583 10.337 8.298 12.767 13.966 4.132 9.635 4.373 25.468-8.546 38.376-11.319 11.308-24.925 16.2-45.488 16.351-22.809-.169-40.06-7.484-51.275-21.742C35.236 139.966 29.808 120.682 29.605 96c.203-24.682 5.63-43.966 16.133-57.317C56.954 24.425 74.204 17.11 97.013 16.94c22.975.17 40.526 7.52 52.171 21.847 5.71 7.026 10.015 15.86 12.853 26.162l16.147-4.308c-3.44-12.68-8.853-23.606-16.219-32.668C147.036 9.607 125.202.195 97.07 0h-.113C68.882.194 47.292 9.642 32.788 28.08 19.882 44.485 13.224 67.315 13.001 95.932L13 96v.067c.224 28.617 6.882 51.447 19.788 67.854C47.292 182.358 68.882 191.806 96.957 192h.113c24.96-.173 42.554-6.708 57.048-21.189 18.963-18.945 18.392-42.692 12.142-57.27-4.484-10.454-13.033-18.945-24.723-24.553M98.44 129.507c-10.44.588-21.286-4.098-21.82-14.135-.397-7.442 5.296-15.746 22.461-16.735q2.948-.17 5.79-.169c6.235 0 12.068.606 17.371 1.765-1.978 24.702-13.58 28.713-23.802 29.274"})))},{name:"tiktok-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M5 3a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2zm7.531 3h2.053s-.114 2.635 2.85 2.82v2.04s-1.582.099-2.85-.87l.021 4.207a3.804 3.804 0 1 1-3.802-3.802h.533v2.082a1.73 1.73 0 0 0-1.922.648 1.727 1.727 0 0 0 1.947 2.646 1.73 1.73 0 0 0 1.19-1.642z"})))},{name:"tiktok",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12.22 2h3.42s-.19 4.394 4.75 4.702v3.396s-2.636.166-4.75-1.448l.037 7.012a6.338 6.338 0 1 1-6.34-6.339h.89v3.472a2.882 2.882 0 1 0 2.024 2.752z"})))},{name:"tripadvisor",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M21.01 9.859c.236-1.002.985-2.003.985-2.003l-3.341-.002C16.779 6.643 14.502 6 11.979 6 9.363 6 7 6.659 5.135 7.877L2 7.88s.74.988.98 1.983a4.98 4.98 0 0 0-.977 2.961 5.01 5.01 0 0 0 5.009 5.003 5 5 0 0 0 3.904-1.875l1.065 1.592 1.076-1.606a4.96 4.96 0 0 0 1.838 1.448 4.98 4.98 0 0 0 3.831.151 5.01 5.01 0 0 0 2.963-6.431 5 5 0 0 0-.679-1.247m-13.998 6.96a4 4 0 0 1-3.998-3.995 4 4 0 0 1 3.998-3.997 4 4 0 0 1 3.996 3.997 4 4 0 0 1-3.996 3.995m4.987-4.36A5.007 5.007 0 0 0 7.11 7.821c1.434-.613 3.081-.947 4.867-.947 1.798 0 3.421.324 4.853.966a4.984 4.984 0 0 0-4.831 4.619m6.288 4.134a3.97 3.97 0 0 1-3.058-.122 3.96 3.96 0 0 1-2.075-2.245v-.001a3.97 3.97 0 0 1 .118-3.056 3.97 3.97 0 0 1 2.246-2.077 4.005 4.005 0 0 1 5.135 2.366 4.006 4.006 0 0 1-2.366 5.135"}),React.createElement("path",{d:"M6.949 10.307a2.477 2.477 0 0 0-2.475 2.472 2.48 2.48 0 0 0 2.475 2.474 2.474 2.474 0 0 0 0-4.946m0 4.094a1.626 1.626 0 0 1-1.624-1.623 1.621 1.621 0 1 1 1.624 1.623M16.981 10.307a2.477 2.477 0 0 0-2.474 2.472 2.48 2.48 0 0 0 2.474 2.474 2.476 2.476 0 0 0 2.472-2.474 2.475 2.475 0 0 0-2.472-2.472m0 4.094a1.625 1.625 0 0 1-1.622-1.623 1.622 1.622 0 1 1 1.622 1.623"}),React.createElement("path",{d:"M7.778 12.778a.832.832 0 1 1-1.664.002.832.832 0 0 1 1.664-.002M16.981 11.947a.832.832 0 1 0 .002 1.666.832.832 0 0 0-.002-1.666"})))},{name:"tumblr-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M16.749 17.396c-.357.17-1.041.319-1.551.332-1.539.041-1.837-1.081-1.85-1.896V9.847h3.861v-2.91h-3.847V2.039h-2.817c-.046 0-.127.041-.138.144-.165 1.499-.867 4.13-3.783 5.181v2.484h1.945v6.282c0 2.151 1.587 5.206 5.775 5.135 1.413-.024 2.982-.616 3.329-1.126z"})))},{name:"tumblr",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-5.569 14.265c-2.446.042-3.372-1.742-3.372-2.998v-3.668H8.923v-1.45c1.703-.614 2.113-2.15 2.209-3.025.007-.06.054-.084.081-.084h1.645V8.9h2.246v1.7H12.85v3.495c.008.476.182 1.131 1.081 1.107.298-.008.697-.094.906-.194l.54 1.601c-.205.296-1.121.641-1.946.656"})))},{name:"twitch",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M16.499 8.089h-1.636v4.91h1.636zm-4.499 0h-1.637v4.91H12zM4.228 3.178 3 6.451v13.092h4.499V22h2.456l2.454-2.456h3.681L21 14.636V3.178zm15.136 10.638L16.5 16.681H12l-2.453 2.453V16.68H5.863V4.814h13.501z"})))},{name:"twitter-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M22.23 5.924a8.2 8.2 0 0 1-2.357.646 4.12 4.12 0 0 0 1.804-2.27 8.2 8.2 0 0 1-2.606.996 4.103 4.103 0 0 0-6.991 3.742 11.65 11.65 0 0 1-8.457-4.287 4.1 4.1 0 0 0-.556 2.063 4.1 4.1 0 0 0 1.825 3.415 4.1 4.1 0 0 1-1.859-.513v.052a4.104 4.104 0 0 0 3.292 4.023 4.1 4.1 0 0 1-1.853.07 4.11 4.11 0 0 0 3.833 2.85 8.24 8.24 0 0 1-5.096 1.756 8 8 0 0 1-.979-.057 11.6 11.6 0 0 0 6.29 1.843c7.547 0 11.675-6.252 11.675-11.675q0-.267-.012-.531a8.3 8.3 0 0 0 2.047-2.123"})))},{name:"twitter",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-2.534 6.71q.007.148.007.298c0 3.045-2.318 6.556-6.556 6.556a6.5 6.5 0 0 1-3.532-1.035q.27.032.55.032a4.63 4.63 0 0 0 2.862-.986 2.31 2.31 0 0 1-2.152-1.6 2.3 2.3 0 0 0 1.04-.04 2.306 2.306 0 0 1-1.848-2.259v-.029c.311.173.666.276 1.044.288a2.303 2.303 0 0 1-.713-3.076 6.54 6.54 0 0 0 4.749 2.407 2.305 2.305 0 0 1 3.926-2.101 4.6 4.6 0 0 0 1.463-.559 2.3 2.3 0 0 1-1.013 1.275c.466-.056.91-.18 1.323-.363-.31.461-.7.867-1.15 1.192"})))},{name:"untappd",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"m11 13.299-5.824 8.133c-.298.416-.8.635-1.308.572-.578-.072-1.374-.289-2.195-.879S.392 19.849.139 19.323a1.4 1.4 0 0 1 .122-1.425l5.824-8.133a3.1 3.1 0 0 1 1.062-.927l1.146-.604c.23-.121.436-.283.608-.478.556-.631 2.049-2.284 4.696-4.957l.046-.212a.13.13 0 0 1 .096-.1l.146-.037a.135.135 0 0 0 .101-.141l-.015-.18a.13.13 0 0 1 .125-.142c.176-.005.518.046 1.001.393s.64.656.692.824a.13.13 0 0 1-.095.164l-.175.044a.13.13 0 0 0-.101.141l.012.15a.13.13 0 0 1-.063.123l-.186.112c-1.679 3.369-2.764 5.316-3.183 6.046a2.2 2.2 0 0 0-.257.73l-.205 1.281A3.1 3.1 0 0 1 11 13.3zm12.739 4.598-5.824-8.133a3.1 3.1 0 0 0-1.062-.927l-1.146-.605a2.1 2.1 0 0 1-.608-.478 51 51 0 0 0-.587-.654.09.09 0 0 0-.142.018 97 97 0 0 1-1.745 3.223 1.4 1.4 0 0 0-.171.485 3.5 3.5 0 0 0 0 1.103l.01.064c.075.471.259.918.536 1.305l5.824 8.133c.296.413.79.635 1.294.574a4.76 4.76 0 0 0 2.209-.881 4.76 4.76 0 0 0 1.533-1.802 1.4 1.4 0 0 0-.122-1.425zM8.306 3.366l.175.044a.134.134 0 0 1 .101.141l-.012.15a.13.13 0 0 0 .063.123l.186.112q.465.933.869 1.721c.026.051.091.06.129.019q.655-.703 1.585-1.668a.137.137 0 0 0 .003-.19c-.315-.322-.645-.659-1.002-1.02l-.046-.212a.13.13 0 0 0-.096-.099l-.146-.037a.135.135 0 0 1-.101-.141l.015-.18a.13.13 0 0 0-.123-.142c-.175-.005-.518.045-1.002.393-.483.347-.64.656-.692.824a.13.13 0 0 0 .095.164z"})))},{name:"vimeo",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M22.396 7.164q-.139 3.039-4.245 8.32Q13.907 21 10.97 21q-1.82 0-3.079-3.359l-1.68-6.159q-.934-3.36-2.005-3.36-.234.001-1.634.98l-.978-1.261q1.541-1.353 3.037-2.708 2.056-1.774 3.084-1.869 2.429-.234 2.99 3.321.607 3.836.841 4.769.7 3.181 1.542 3.181.653 0 1.963-2.065 1.307-2.063 1.401-3.142.187-1.781-1.401-1.782-.747.001-1.541.341 1.534-5.024 5.862-4.884 3.21.095 3.024 4.161"})))},{name:"vk",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{fillRule:"evenodd",d:"M1.687 1.687C0 3.374 0 6.09 0 11.52v.96c0 5.431 0 8.146 1.687 9.833S6.09 24 11.52 24h.96c5.431 0 8.146 0 9.833-1.687S24 17.91 24 12.48v-.96c0-5.431 0-8.146-1.687-9.833S17.91 0 12.48 0h-.96C6.09 0 3.374 0 1.687 1.687M4.05 7.3c.13 6.24 3.25 9.99 8.72 9.99h.31v-3.57c2.01.2 3.53 1.67 4.14 3.57h2.84c-.78-2.84-2.83-4.41-4.11-5.01 1.28-.74 3.08-2.54 3.51-4.98h-2.58c-.56 1.98-2.22 3.78-3.8 3.95V7.3H10.5v6.92c-1.6-.4-3.62-2.34-3.71-6.92z"})))},{name:"whatsapp",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"m2.048 22 1.406-5.136a9.9 9.9 0 0 1-1.323-4.955C2.133 6.446 6.579 2 12.042 2a9.85 9.85 0 0 1 7.011 2.906 9.85 9.85 0 0 1 2.9 7.011c-.002 5.464-4.448 9.91-9.91 9.91h-.004a9.9 9.9 0 0 1-4.736-1.206zm5.497-3.172.301.179a8.2 8.2 0 0 0 4.193 1.148h.003c4.54 0 8.235-3.695 8.237-8.237a8.2 8.2 0 0 0-2.41-5.828 8.18 8.18 0 0 0-5.824-2.416c-4.544 0-8.239 3.695-8.241 8.237a8.2 8.2 0 0 0 1.259 4.384l.196.312-.832 3.04zm9.49-4.554c-.062-.103-.227-.165-.475-.289s-1.465-.723-1.692-.806-.392-.124-.557.124-.64.806-.784.971-.289.186-.536.062-1.046-.385-1.991-1.229c-.736-.657-1.233-1.468-1.378-1.715s-.015-.382.109-.505c.111-.111.248-.289.371-.434.124-.145.165-.248.248-.413s.041-.31-.021-.434-.557-1.343-.763-1.839c-.202-.483-.407-.417-.559-.425-.144-.007-.31-.009-.475-.009a.9.9 0 0 0-.66.31c-.226.248-.866.847-.866 2.066s.887 2.396 1.011 2.562 1.746 2.666 4.23 3.739c.591.255 1.052.408 1.412.522.593.189 1.133.162 1.56.098.476-.071 1.465-.599 1.671-1.177.206-.58.206-1.075.145-1.179"})))},{name:"woocommerce",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19 2H5C3.3 2 2 3.3 2 5v11c0 1.7 1.3 3 3 3h4l6 3-1-3h5c1.7 0 3-1.3 3-3V5c0-1.7-1.3-3-3-3m-1.6 4.5c-.4.8-.8 2.1-1 3.9-.3 1.8-.4 3.1-.3 4.1 0 .3 0 .5-.1.7s-.3.4-.6.4-.6-.1-.9-.4c-1-1-1.8-2.6-2.4-4.6-.7 1.4-1.2 2.4-1.6 3.1-.6 1.2-1.2 1.8-1.6 1.9-.3 0-.5-.2-.8-.7-.5-1.4-1.1-4.2-1.7-8.2 0-.3 0-.5.2-.7.1-.2.4-.3.7-.4.5 0 .9.2.9.8.3 2.3.7 4.2 1.1 5.7l2.4-4.5c.2-.4.4-.6.8-.6q.75 0 .9.9c.3 1.4.6 2.6 1 3.7.3-2.7.8-4.7 1.4-5.9.2-.3.4-.5.7-.5.2 0 .5.1.7.2q.3.3.3.6c0 .3 0 .4-.1.5"})))},{name:"wordpress",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12.158 12.786 9.46 20.625a9 9 0 0 0 5.526-.144 1 1 0 0 1-.065-.124zM3.009 12a8.99 8.99 0 0 0 5.067 8.092L3.788 8.341A8.95 8.95 0 0 0 3.009 12m15.06-.454c0-1.112-.399-1.881-.741-2.48-.456-.741-.883-1.368-.883-2.109 0-.826.627-1.596 1.51-1.596q.06.002.116.007A8.96 8.96 0 0 0 12 3.009a8.98 8.98 0 0 0-7.512 4.052c.211.007.41.011.579.011.94 0 2.396-.114 2.396-.114.484-.028.541.684.057.741 0 0-.487.057-1.029.085l3.274 9.739 1.968-5.901-1.401-3.838c-.484-.028-.943-.085-.943-.085-.485-.029-.428-.769.057-.741 0 0 1.484.114 2.368.114.94 0 2.397-.114 2.397-.114.485-.028.542.684.057.741 0 0-.488.057-1.029.085l3.249 9.665.897-2.996q.684-1.753.684-2.907m1.82-3.86q.06.428.06.924c0 .912-.171 1.938-.684 3.22l-2.746 7.94a8.98 8.98 0 0 0 4.47-7.771 8.9 8.9 0 0 0-1.1-4.313M12 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10"})))},{name:"x",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M13.982 10.622 20.54 3h-1.554l-5.693 6.618L8.745 3H3.5l6.876 10.007L3.5 21h1.554l6.012-6.989L15.868 21h5.245zm-2.128 2.474-.697-.997-5.543-7.93H8l4.474 6.4.697.996 5.815 8.318h-2.387z"})))},{name:"xanga",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M9 9h6v6H9zM3 9h6V3H3zm12 0h6V3h-6zm0 12h6v-6h-6zM3 21h6v-6H3z"})))},{name:"youtube",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M21.8 8.001s-.195-1.378-.795-1.985c-.76-.797-1.613-.801-2.004-.847-2.799-.202-6.997-.202-6.997-.202h-.009s-4.198 0-6.997.202c-.39.047-1.242.051-2.003.847-.6.607-.795 1.985-.795 1.985S2 9.62 2 11.238v1.517c0 1.618.2 3.237.2 3.237s.195 1.378.795 1.985c.761.797 1.76.771 2.205.855 1.6.153 6.8.201 6.8.201s4.203-.006 7.001-.209c.391-.047 1.243-.051 2.004-.847.6-.607.795-1.985.795-1.985s.2-1.618.2-3.237v-1.517c0-1.618-.2-3.237-.2-3.237M9.935 14.594l-.001-5.62 5.404 2.82z"})))}]},8992:(e,t,a)=>{"use strict";a(6072);var r=a(8120),n=a.n(r),o=a(1609);a(1135);o.PureComponent,n().string.isRequired,n().number,n().func,n().string},8575:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(7723);const __=r.__,n={subject:{type:"string",default:window.jpFormsBlocks?.defaults?.subject||""},to:{type:"string",default:window.jpFormsBlocks?.defaults?.to||""},customThankyou:{type:"string",default:""},customThankyouHeading:{type:"string",default:__("Your message has been sent","jetpack-forms")},customThankyouMessage:{type:"string",default:""},customThankyouRedirect:{type:"string",default:""},jetpackCRM:{type:"boolean",default:!0},formTitle:{type:"string",default:""},salesforceData:{type:"object",default:{organizationId:"",sendToSalesforce:!1}}}},1013:(e,t,a)=>{"use strict";a.d(t,{P:()=>N});var r=a(4715),n=a(4997),o=a(6427),c=a(7723),l=a(2663),s=a(4066),i=a(2065),m=a(7326),d=a(6087),p=a(1978),u=a(9958),f=a(4697),h=a(565),g=a(4506),k=a(630),b=a(3052),v=a(358),E=a(3286),w=a(5700),R=a(5214),C=a(7817),j=a(9787),x=a(1663),y=a(9771),A=a(1097);const __=c.__,_x=c._x,_={apiVersion:3,category:"contact-form",supports:{reusable:!1,html:!1},attributes:{label:{type:"string",default:null,role:"content"},required:{type:"boolean",default:!1},requiredText:{type:"string",role:"content"},options:{type:"array",default:[],role:"content"},defaultValue:{type:"string",default:"",role:"content"},placeholder:{type:"string",default:"",role:"content"},id:{type:"string",default:""},width:{type:"number",default:100},borderRadius:{type:"number",default:""},borderWidth:{type:"number",default:""},labelFontSize:{type:"string"},fieldFontSize:{type:"string"},lineHeight:{type:"number"},labelLineHeight:{type:"number"},inputColor:{type:"string"},labelColor:{type:"string"},fieldBackgroundColor:{type:"string"},buttonBackgroundColor:{type:"string"},buttonBorderRadius:{type:"number",default:""},buttonBorderWidth:{type:"number",default:""},borderColor:{type:"string"},shareFieldAttributes:{type:"boolean",default:!0}},transforms:{to:[{type:"block",blocks:["jetpack/field-number"],transform:e=>(0,n.createBlock)("jetpack/field-number",e)},{type:"block",blocks:["jetpack/field-text"],transform:e=>(0,n.createBlock)("jetpack/field-text",e)},{type:"block",blocks:["jetpack/field-name"],transform:e=>(0,n.createBlock)("jetpack/field-name",e)},{type:"block",blocks:["jetpack/field-email"],transform:e=>(0,n.createBlock)("jetpack/field-email",e)},{type:"block",blocks:["jetpack/field-url"],transform:e=>(0,n.createBlock)("jetpack/field-url",e)},{type:"block",blocks:["jetpack/field-date"],transform:e=>(0,n.createBlock)("jetpack/field-date",e)},{type:"block",blocks:["jetpack/field-telephone"],transform:e=>(0,n.createBlock)("jetpack/field-telephone",e)},{type:"block",blocks:["jetpack/field-textarea"],transform:e=>(0,n.createBlock)("jetpack/field-textarea",e)},{type:"block",blocks:["jetpack/field-checkbox-multiple"],transform:(e,t)=>{let a=[];if((0,d.isEmpty)(t))e.options?.length&&(a=(0,d.map)(e.options,(e=>(0,n.createBlock)("jetpack/field-option-checkbox",{label:e,fieldType:"checkbox"}))));else{const e=(0,d.filter)(t,(({name:e})=>(0,d.startsWith)(e,"jetpack/field-option")));a=(0,d.map)(e,(e=>(0,n.createBlock)("jetpack/field-option-checkbox",{label:e.attributes.label,fieldType:"checkbox"})))}return(0,n.createBlock)("jetpack/field-checkbox-multiple",e,a)}},{type:"block",blocks:["jetpack/field-radio"],transform:(e,t)=>{let a=[];if((0,d.isEmpty)(t))e.options?.length&&(a=(0,d.map)(e.options,(e=>(0,n.createBlock)("jetpack/field-option-radio",{label:e,fieldType:"radio"}))));else{const e=(0,d.filter)(t,(({name:e})=>(0,d.startsWith)(e,"jetpack/field-option")));a=(0,d.map)(e,(e=>(0,n.createBlock)("jetpack/field-option-radio",{label:e.attributes.label,fieldType:"radio"})))}return(0,n.createBlock)("jetpack/field-radio",e,a)}},{type:"block",blocks:["jetpack/field-select"],transform:(e,t)=>{if(!(0,d.isEmpty)(t)){const a=(0,d.filter)(t,(({name:e})=>(0,d.startsWith)(e,"jetpack/field-option")));e.options=(0,d.map)(a,(e=>e.attributes.label))}return e.options=e.options?.length?e.options:[""],(0,n.createBlock)("jetpack/field-select",e)}},{type:"block",blocks:["jetpack/field-consent"],transform:e=>(0,n.createBlock)("jetpack/field-consent",e)},{type:"block",blocks:["jetpack/field-checkbox"],transform:e=>(0,n.createBlock)("jetpack/field-checkbox",e)}]},save:()=>null,example:{}},B=__("Choose several options","jetpack-forms"),S=__("Choose one option","jetpack-forms"),M=e=>({attributes:{..._.attributes,label:{type:"string",default:"checkbox"===e?B:S}},migrate:t=>{const a=`jetpack/field-option-${e}`,r=(0,d.filter)(t.options,(e=>!(0,d.isEmpty)((0,d.trim)(e)))),o=(0,d.map)(r,(e=>(0,n.createBlock)(a,{label:e})));return t.options=[],[t,o]},isEligible:e=>e.options&&(0,d.filter)(e.options,(e=>!(0,d.isEmpty)((0,d.trim)(e)))).length,save:()=>null}),I=e=>t=>((0,x.DR)(t),React.createElement(p.A,{clientId:t.clientId,type:e,label:t.attributes.label,required:t.attributes.required,requiredText:t.attributes.requiredText,setAttributes:t.setAttributes,isSelected:t.isSelected,defaultValue:t.attributes.defaultValue,placeholder:t.attributes.placeholder,id:t.attributes.id,width:t.attributes.width,attributes:t.attributes,insertBlocksAfter:t.insertBlocksAfter})),N=[{name:"field-text",settings:{..._,title:__("Text Input Field","jetpack-forms"),description:__("Collect short text responses from site visitors.","jetpack-forms"),icon:{foreground:(0,j.V)(),src:(0,A.A)(React.createElement(o.Path,{d:"M12 7H4V8.5H12V7ZM19.75 17.25V10.75H4.25V17.25H19.75ZM5.75 15.75V12.25H18.25V15.75H5.75Z"}))},edit:I("text"),attributes:{..._.attributes,label:{type:"string",default:__("Text","jetpack-forms"),role:"content"}}}},{name:"field-number",settings:{..._,title:__("Number Input Field","jetpack-forms"),description:__("Collect numbers from site visitors.","jetpack-forms"),icon:(0,A.A)(React.createElement(o.Path,{fill:(0,j.V)(),d:"M12 7H4V8.5H12V7ZM19.75 17.25V10.75H4.25V17.25H19.75ZM5.75 15.75V12.25H18.25V15.75H5.75Z"})),edit:e=>((0,x.DR)(e),React.createElement(E.A,{clientId:e.clientId,label:e.attributes.label,required:e.attributes.required,requiredText:e.attributes.requiredText,setAttributes:e.setAttributes,isSelected:e.isSelected,defaultValue:e.attributes.defaultValue,placeholder:e.attributes.placeholder,id:e.attributes.id,width:e.attributes.width,attributes:e.attributes,insertBlocksAfter:e.insertBlocksAfter,min:e.attributes.min,max:e.attributes.max})),attributes:{..._.attributes,label:{type:"string",default:__("Number","jetpack-forms")},min:{type:"number",default:""},max:{type:"number",default:""}}}},{name:"field-name",settings:{..._,title:__("Name Field","jetpack-forms"),description:__("Collect the site visitor's name.","jetpack-forms"),icon:{foreground:(0,j.V)(),src:(0,A.A)(React.createElement(o.Path,{d:"M8.25 11.5C9.63071 11.5 10.75 10.3807 10.75 9C10.75 7.61929 9.63071 6.5 8.25 6.5C6.86929 6.5 5.75 7.61929 5.75 9C5.75 10.3807 6.86929 11.5 8.25 11.5ZM8.25 10C8.80228 10 9.25 9.55228 9.25 9C9.25 8.44772 8.80228 8 8.25 8C7.69772 8 7.25 8.44772 7.25 9C7.25 9.55228 7.69772 10 8.25 10ZM13 15.5V17.5H11.5V15.5C11.5 14.8096 10.9404 14.25 10.25 14.25H6.25C5.55964 14.25 5 14.8096 5 15.5V17.5H3.5V15.5C3.5 13.9812 4.73122 12.75 6.25 12.75H10.25C11.7688 12.75 13 13.9812 13 15.5ZM20.5 11H14.5V9.5H20.5V11ZM20.5 14.5H14.5V13H20.5V14.5Z"}))},edit:I("text"),attributes:{..._.attributes,label:{type:"string",default:__("Name","jetpack-forms"),role:"content"}}}},{name:"field-email",settings:{..._,title:__("Email Field","jetpack-forms"),keywords:[__("e-mail","jetpack-forms"),__("mail","jetpack-forms"),"email"],description:__("Collect email addresses from your visitors.","jetpack-forms"),icon:{foreground:(0,j.V)(),src:React.createElement(o.Icon,{icon:l.A})},edit:I("email"),attributes:{..._.attributes,label:{type:"string",default:__("Email","jetpack-forms"),role:"content"}}}},{name:"field-url",settings:{..._,title:__("Website Field","jetpack-forms"),keywords:[__("url","jetpack-forms"),__("internet page","jetpack-forms"),__("link","jetpack-forms"),__("website","jetpack-forms")],description:__("Collect a website address from your site visitors.","jetpack-forms"),icon:{foreground:(0,j.V)(),src:React.createElement(o.Icon,{icon:s.A})},edit:I("url"),attributes:{..._.attributes,label:{type:"string",default:__("Website","jetpack-forms"),role:"content"}}}},{name:"field-date",settings:{..._,title:__("Date Picker","jetpack-forms"),keywords:[__("Calendar","jetpack-forms"),_x("day month year","block search term","jetpack-forms")],description:__("Capture date information with a date picker.","jetpack-forms"),icon:{foreground:(0,j.V)(),src:(0,A.A)(React.createElement(o.Path,{fillRule:"evenodd",d:"M4.5 7H19.5V19C19.5 19.2761 19.2761 19.5 19 19.5H5C4.72386 19.5 4.5 19.2761 4.5 19V7ZM3 5V7V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V7V5C21 3.89543 20.1046 3 19 3H5C3.89543 3 3 3.89543 3 5ZM11 9.25H7V13.25H11V9.25Z"}))},edit:h.A,attributes:{..._.attributes,label:{type:"string",default:__("Date","jetpack-forms"),role:"content"},dateFormat:{type:"string",default:"yy-mm-dd"}}}},{name:"field-telephone",settings:{..._,title:__("Phone Number Field","jetpack-forms"),keywords:[__("Phone","jetpack-forms"),__("Cellular phone","jetpack-forms"),__("Mobile","jetpack-forms")],description:__("Collect phone numbers from site visitors.","jetpack-forms"),icon:{foreground:(0,j.V)(),src:React.createElement(o.Icon,{icon:i.A})},edit:I("tel"),attributes:{..._.attributes,label:{type:"string",default:"Phone",role:"content"}}}},{name:"field-file",settings:{..._,title:__("File Upload Field","jetpack-forms"),keywords:[__("File","jetpack-forms"),__("Upload","jetpack-forms"),__("Attachment","jetpack-forms")],description:__("Allow visitors to upload files through your form.","jetpack-forms"),icon:{foreground:(0,j.V)(),src:React.createElement(o.Icon,{icon:m.A})},edit:k.A,save:()=>{const e=r.useBlockProps.save(),t=r.useInnerBlocksProps.save({className:"jetpack-form-file-field__content-wrap"});return React.createElement("div",e,React.createElement("div",t))},attributes:{..._.attributes,label:{type:"string",default:__("Upload a file","jetpack-forms"),role:"content"},filetype:{type:"string",default:""},maxfiles:{type:"number",default:1}},isBeta:!0}},{name:"field-textarea",settings:{..._,title:__("Multi-line Text Field","jetpack-forms"),keywords:[__("Textarea","jetpack-forms"),"textarea",__("Multiline text","jetpack-forms")],description:__("Capture longform text responses from site visitors.","jetpack-forms"),icon:{foreground:(0,j.V)(),src:(0,A.A)(React.createElement(o.Path,{d:"M20 5H4V6.5H20V5ZM5.5 11.5H18.5V18.5H5.5V11.5ZM20 20V10H4V20H20Z"}))},edit:e=>((0,x.DR)(e),React.createElement(C.A,{clientId:e.clientId,label:e.attributes.label,required:e.attributes.required,requiredText:e.attributes.requiredText,attributes:e.attributes,setAttributes:e.setAttributes,isSelected:e.isSelected,defaultValue:e.attributes.defaultValue,placeholder:e.attributes.placeholder,id:e.attributes.id,width:e.attributes.width})),attributes:{..._.attributes,label:{type:"string",default:__("Message","jetpack-forms"),role:"content"}}}},{name:"field-checkbox",settings:{..._,title:__("Checkbox","jetpack-forms"),keywords:[__("Confirm","jetpack-forms"),__("Accept","jetpack-forms")],description:__("Confirm or select information with a single checkbox.","jetpack-forms"),icon:{foreground:(0,j.V)(),src:(0,A.A)(React.createElement(o.Path,{fillRule:"evenodd",d:"M6.125 6H17.875C17.944 6 18 6.05596 18 6.125V17.875C18 17.944 17.944 18 17.875 18H6.125C6.05596 18 6 17.944 6 17.875V6.125C6 6.05596 6.05596 6 6.125 6ZM4.5 6.125C4.5 5.22754 5.22754 4.5 6.125 4.5H17.875C18.7725 4.5 19.5 5.22754 19.5 6.125V17.875C19.5 18.7725 18.7725 19.5 17.875 19.5H6.125C5.22754 19.5 4.5 18.7725 4.5 17.875V6.125ZM10.5171 16.4421L16.5897 8.71335L15.4103 7.78662L10.4828 14.0579L8.57616 11.7698L7.42383 12.7301L10.5171 16.4421Z"}))},edit:e=>((0,x.DR)(e),React.createElement(u.A,{clientId:e.clientId,label:e.attributes.label,required:e.attributes.required,requiredText:e.attributes.requiredText,setAttributes:e.setAttributes,isSelected:e.isSelected,defaultValue:e.attributes.defaultValue,id:e.attributes.id,width:e.attributes.width,attributes:e.attributes,insertBlocksAfter:e.insertBlocksAfter})),attributes:{..._.attributes,label:{type:"string",default:"",role:"content"}}}},{name:"field-consent",settings:{..._,title:__("Terms Consent","jetpack-forms"),keywords:[__("Consent","jetpack-forms")],description:__("Communicate site terms and offer visitors consent to those terms.","jetpack-forms"),icon:{foreground:(0,j.V)(),src:(0,A.A)(React.createElement(React.Fragment,null,React.createElement(o.Path,{d:"M7 5.5H17C17.2761 5.5 17.5 5.72386 17.5 6V13H19V6C19 4.89543 18.1046 4 17 4H7C5.89543 4 5 4.89543 5 6V18C5 19.1046 5.89543 20 7 20H11.5V18.5H7C6.72386 18.5 6.5 18.2761 6.5 18V6C6.5 5.72386 6.72386 5.5 7 5.5ZM16 7.75H8V9.25H16V7.75ZM8 11H13V12.5H8V11Z"}),React.createElement(o.Path,{d:"M20.1087 15.9382L15.9826 21.6689L12.959 18.5194L14.0411 17.4806L15.8175 19.331L18.8914 15.0618L20.1087 15.9382Z"})))},attributes:{..._.attributes,label:{type:"string",default:__("Consent","jetpack-forms")},consentType:{type:"string",default:"implicit"},implicitConsentMessage:{type:"string",default:__("By submitting your information, you're giving us permission to email you. You may unsubscribe at any time.","jetpack-forms")},explicitConsentMessage:{type:"string",default:__("Can we send you an email from time to time?","jetpack-forms")}},edit:({attributes:e,clientId:t,isSelected:a,name:r,setAttributes:n,insertBlocksAfter:o})=>{(0,x.DR)({attributes:e,clientId:t,name:r});const{id:c,width:l,consentType:s,implicitConsentMessage:i,explicitConsentMessage:m}=e;return React.createElement(f.A,{clientId:t,id:c,isSelected:a,width:l,consentType:s,implicitConsentMessage:i,explicitConsentMessage:m,setAttributes:n,attributes:e,insertBlocksAfter:o})}}},{name:w.A.name,settings:(0,y.A)(_,{...w.A.settings,deprecated:[{save:()=>React.createElement(r.InnerBlocks.Content,null)},M("radio")]})},R.A,{name:b.A.name,settings:(0,y.A)(_,{...b.A.settings,deprecated:[{save:()=>React.createElement(r.InnerBlocks.Content,null)},M("checkbox")]})},v.A,{name:"field-select",settings:{..._,title:__("Dropdown Field","jetpack-forms"),keywords:[__("Choose","jetpack-forms"),__("Dropdown","jetpack-forms"),__("Option","jetpack-forms")],description:__("Add a compact select box, that when expanded, allows visitors to choose one value from the list.","jetpack-forms"),icon:{foreground:(0,j.V)(),src:(0,A.A)(React.createElement(o.Path,{fill:(0,j.V)(),d:"M5 4.5H19C19.2761 4.5 19.5 4.72386 19.5 5V19C19.5 19.2761 19.2761 19.5 19 19.5H5C4.72386 19.5 4.5 19.2761 4.5 19V5C4.5 4.72386 4.72386 4.5 5 4.5ZM19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3ZM8.93582 10.1396L8.06396 11.3602L11.9999 14.1716L15.9358 11.3602L15.064 10.1396L11.9999 12.3283L8.93582 10.1396Z"}))},edit:g.A,attributes:{..._.attributes,toggleLabel:{type:"string",default:null,role:"content"},options:{type:"array",default:[""],role:"content"}}}}]},8799:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(6427),n=a(7723),o=a(1097);const __=n.__,c=({required:e,onClick:t})=>React.createElement(r.ToolbarGroup,null,React.createElement(r.ToolbarButton,{title:__("Required","jetpack-forms"),icon:(0,o.A)(React.createElement(r.Path,{d:"M8.23118 8L16 16M8 16L15.7688 8 M6.5054 11.893L17.6567 11.9415M12.0585 17.6563L12 6.5",stroke:"currentColor"})),onClick:t,className:e?"is-pressed":void 0}))},615:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(6072),n=a.n(r),o=a(3022),c=a(9789);const l=({children:e=null,isError:t=!1,...a})=>{const r=(0,o.A)("help-message",{"help-message-is-error":t});return e&&React.createElement("div",n()({className:r},a),t&&React.createElement(c.A,{size:"24","aria-hidden":"true",role:"img",focusable:"false"}),React.createElement("span",null,e))}},599:(e,t,a)=>{"use strict";function r({children:e}){return React.createElement("p",{style:{color:"rgba( 117, 117, 117, 1 )",marginBottom:"24px"}},e)}a.d(t,{A:()=>r})},3373:(e,t,a)=>{"use strict";a.d(t,{o:()=>c});var r=a(6427),n=a(7723),o=a(5620);const __=n.__,c=({changeStatus:e,isLoading:t,isModuleActive:a})=>React.createElement(r.Placeholder,{icon:o.W.icon.src,instructions:__("You’ll need to activate the Forms feature to use this block.","jetpack-forms"),label:o.W.title},React.createElement(r.Button,{disabled:a||t,isBusy:t,onClick:()=>e(!0),variant:"secondary"},t?__("Activating Forms","jetpack-forms"):__("Activate Forms","jetpack-forms",0)))},1465:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(1876),n=a(6427);const o=function(){return React.createElement(n.Placeholder,null,React.createElement(n.Flex,{gap:2,direction:"column",style:{width:"100%"}},React.createElement(n.Flex,{gap:4},React.createElement(n.FlexItem,null,React.createElement(r.A,{width:30,height:30})),React.createElement(n.FlexBlock,null,React.createElement(r.A,{width:"90%",height:30}))),React.createElement(n.Flex,{style:{marginTop:12}},React.createElement(n.FlexBlock,null,React.createElement(r.A,{height:30,width:"60%"}))),React.createElement(n.Flex,{style:{marginTop:12}},React.createElement(n.FlexBlock,null,React.createElement(r.A,{width:150,height:50}))),React.createElement(n.Flex,{style:{marginTop:18}},React.createElement(n.FlexBlock,null,React.createElement(r.A,{width:"90%",height:16})))))}},7309:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(6427),n=a(8468),o=a(7723),c=a(9904),l=a(615),s=a(599);const __=o.__,i=({emailAddress:e="",emailSubject:t="",instanceId:a,setAttributes:i,postAuthorEmail:m})=>{const[d,p]=(0,n.useState)(!1),u=e=>0!==(e=e.trim()).length&&(!c.validate(e)&&{email:e});return React.createElement(React.Fragment,null,React.createElement(s.A,null,__("Get incoming form responses sent to your email inbox:","jetpack-forms")),React.createElement(r.TextControl,{"aria-describedby":`contact-form-${a}-email-${d&&d.length>0?"error":"help"}`,label:__("Email address to send to","jetpack-forms"),placeholder:__("<EMAIL>","jetpack-forms"),onKeyDown:e=>{"Enter"===event.key&&(e.preventDefault(),e.stopPropagation())},value:e,onBlur:e=>{if(0===e.target.value.length)return p(!1),void i({to:m});const t=e.target.value.split(",").map(u).filter(Boolean);t&&t.length&&p(t)},onChange:e=>{p(!1),i({to:e.trim()})},help:__("You can enter multiple email addresses separated by commas.","jetpack-forms"),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),React.createElement(l.A,{isError:!0,id:`contact-form-${a}-email-error`},(()=>{if(d){if(1===d.length)return d[0]&&d[0].email?(0,o.sprintf)(/* translators: placeholder is an email address. */
__("%s is not a valid email address.","jetpack-forms"),d[0].email):d[0];if(2===d.length)return(0,o.sprintf)(/* translators: placeholders are email addresses. */
__("%1$s and %2$s are not a valid email address.","jetpack-forms"),d[0].email,d[1].email);const e=d.map((e=>e.email));return(0,o.sprintf)(/* translators: placeholder is a list of email addresses. */
__("%s are not a valid email address.","jetpack-forms"),e.join(", "))}return null})()),React.createElement(r.TextControl,{label:__("Email subject line","jetpack-forms"),value:t,placeholder:__("Enter a subject","jetpack-forms"),onChange:e=>i({subject:e}),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}))}},9958:(e,t,a)=>{"use strict";a.d(t,{A:()=>u});var r=a(4715),n=a(6427),o=a(9491),c=a(7723),l=a(8890),s=a(8799),i=a(6869),m=a(975),d=a(1646),p=a(1787);const __=c.__;const u=(0,o.compose)((0,l.H)(["borderRadius","borderWidth","labelFontSize","fieldFontSize","lineHeight","labelLineHeight","inputColor","labelColor","fieldBackgroundColor","borderColor"]),o.withInstanceId)((function(e){const{instanceId:t,required:a,requiredText:o,label:c,setAttributes:l,width:u,defaultValue:f,attributes:h,insertBlocksAfter:g}=e,{blockStyle:k}=(0,p.U)(h),b=(0,r.useBlockProps)({id:`jetpack-field-checkbox-${t}`,className:"jetpack-field jetpack-field-checkbox",style:k});return React.createElement(React.Fragment,null,React.createElement(r.BlockControls,null,React.createElement(s.A,{required:a,onClick:()=>l({required:!a})})),React.createElement("div",b,React.createElement("input",{className:"jetpack-field-checkbox__checkbox",type:"checkbox",disabled:!0,checked:f}),React.createElement(i.A,{required:a,requiredText:o,label:c,setAttributes:l,attributes:h,insertBlocksAfter:g}),React.createElement(r.InspectorControls,null,React.createElement(n.PanelBody,{title:__("Checkbox Settings","jetpack-forms")},React.createElement(n.ToggleControl,{label:__("Checked by default","jetpack-forms"),checked:f,onChange:e=>l({defaultValue:e?"true":""}),__nextHasNoMarginBottom:!0}))),React.createElement(r.InspectorControls,null,React.createElement(n.PanelBody,{title:__("Manage Responses","jetpack-forms")},React.createElement(d.A,{isChildBlock:!0})),React.createElement(n.PanelBody,{title:__("Field Settings","jetpack-forms")},React.createElement(n.ToggleControl,{label:__("Field is required","jetpack-forms"),checked:a,onChange:e=>l({required:e}),help:__('You can edit the "required" label in the editor',"jetpack-forms"),__nextHasNoMarginBottom:!0}),React.createElement(m.A,{setAttributes:l,width:u}),React.createElement(n.ToggleControl,{label:__("Sync fields style","jetpack-forms"),checked:h.shareFieldAttributes,onChange:e=>l({shareFieldAttributes:e}),help:__("Deactivate for individual styling of this block","jetpack-forms"),__nextHasNoMarginBottom:!0})),React.createElement(r.PanelColorSettings,{title:__("Color","jetpack-forms"),initialOpen:!1,colorSettings:[{value:h.labelColor,onChange:e=>l({labelColor:e}),label:__("Label Text","jetpack-forms")}]}),React.createElement(n.PanelBody,{title:__("Label Styles","jetpack-forms"),initialOpen:h.labelFontSize},React.createElement(r.FontSizePicker,{withSlider:!0,withReset:!0,size:"__unstable-large",__nextHasNoMarginBottom:!0,onChange:e=>l({labelFontSize:e}),value:h.labelFontSize,__next40pxDefaultSize:!0})))))}))},6563:(e,t,a)=>{"use strict";a.d(t,{A:()=>f});var r=a(6072),n=a.n(r),o=a(4715),c=a(9491),l=a(7143),s=a(3022),i=a(1663),m=a(8890),d=a(1713),p=a(6869),u=a(1787);const f=(0,c.compose)((0,m.H)(["borderRadius","borderWidth","labelFontSize","fieldFontSize","lineHeight","labelLineHeight","inputColor","labelColor","fieldBackgroundColor","buttonBackgroundColor","buttonBorderRadius","buttonBorderWidth","borderColor"]),c.withInstanceId)((e=>{const{className:t,clientId:a,instanceId:r,setAttributes:c,isSelected:m,attributes:f,type:h}=e,{required:g,requiredText:k,options:b,id:v,width:E}=f;(0,i.DR)(e);const w=(0,l.useSelect)((e=>e("core/block-editor").getBlock(a).innerBlocks),[a]),R=(0,s.A)(t,"jetpack-field jetpack-field-multiple",{"is-selected":m,"has-placeholder":b&&b.length||w.length}),C=(0,i.BV)(a),{blockStyle:j}=(0,u.U)(f),x=(0,o.useBlockProps)({id:`jetpack-field-multiple-${r}`,style:j,className:R}),y=(0,o.useInnerBlocksProps)(x,{defaultBlock:`jetpack/field-option-${h}`,template:[[`jetpack/field-option-${h}`]],templateInsertUpdatesSelection:!0});return React.createElement(React.Fragment,null,React.createElement("div",x,React.createElement(p.A,{required:g,requiredText:k,label:f.label,setAttributes:c,isSelected:m,attributes:f,style:C}),React.createElement("ul",n()({},y,{className:"jetpack-field-multiple__list"}))),React.createElement(d.A,{blockClassNames:R,clientId:a,id:v,required:g,attributes:f,setAttributes:c,type:h,width:E,hidePlaceholder:!0}))}))},9659:(e,t,a)=>{"use strict";a.d(t,{A:()=>h});var r=a(6072),n=a.n(r),o=a(4715),c=a(4997),l=a(9491),s=a(7143),i=a(8468),m=a(7723),d=a(3022),p=a(8882),u=a(2233),f=a(1787);const __=m.__;function h({attributes:e,clientId:t,name:a,onReplace:r,setAttributes:m,type:h}){const{removeBlock:g}=(0,s.useDispatch)(o.store),k=(0,u.F)(t),{optionStyle:b}=(0,f.U)(k),v=(0,s.useSelect)((e=>{const{getBlockCount:a,getBlockRootClientId:r}=e(o.store);return a(r(t))}),[t]),E=(0,o.useBlockProps)(),w=(0,p.B)(),R=(0,d.A)("jetpack-field-option",`field-option-${h}`,E.className),C=(0,o.useInnerBlocksProps)(E,{className:R,style:b}),j=function(e){const{replaceBlocks:t,selectionChange:a}=(0,s.useDispatch)(o.store),{getBlock:r,getBlockRootClientId:n,getBlockIndex:m}=(0,s.useSelect)(o.store),d=(0,i.useRef)(e);return d.current=e,(0,l.useRefEffect)((e=>{function o(e){if(e.defaultPrevented||"Enter"!==e.key)return;const{content:o,clientId:l}=d.current;if(o?.length)return;e.preventDefault();const s=r(n(l)),i=m(l),p=(0,c.cloneBlock)({...s,innerBlocks:s.innerBlocks.slice(0,i)}),u=(0,c.createBlock)((0,c.getDefaultBlockName)()),f=s.innerBlocks.slice(i+1),h=f.length?[(0,c.cloneBlock)({...s,innerBlocks:f})]:[];t(s.clientId,[p,u,...h],1),a(u.clientId)}return e.addEventListener("keydown",o),()=>{e.removeEventListener("keydown",o)}}),[])}({content:e.label,clientId:t});return React.createElement(React.Fragment,null,React.createElement("li",C,React.createElement("input",{type:h,className:"jetpack-option__type",tabIndex:"-1"}),React.createElement(o.RichText,n()({ref:j,identifier:"label",tagName:"div",className:"wp-block",value:e.label,placeholder:__("Add option…","jetpack-forms"),__unstableDisableFormats:!0,onChange:e=>m({label:e}),preserveWhiteSpace:!1,onRemove:()=>{v<=1||g(t)},onReplace:r},w?{}:{onSplit:t=>(0,c.createBlock)(a,{...e,clientId:t&&0===e.label.indexOf(t)?e.clientId:void 0,label:t})}))))}},4414:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});const r={apiVersion:3,category:"contact-form",attributes:{label:{type:"string",role:"content"},fieldType:{enum:["checkbox","radio"],default:"checkbox"}},supports:{reusable:!1,html:!1,splitting:!0}}},8230:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(7723);const __=r.__,n={styles:[{name:"list",label:__("List","jetpack-forms"),isDefault:!0},{name:"button",label:__("Button","jetpack-forms")}]}},4697:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var r=a(4715),n=a(6427),o=a(9491),c=a(7723),l=a(8890),s=a(6869),i=a(975),m=a(1646);const __=c.__,d=(0,o.compose)((0,l.H)(["borderRadius","borderWidth","labelFontSize","fieldFontSize","lineHeight","labelLineHeight","inputColor","labelColor","fieldBackgroundColor","borderColor"]),o.withInstanceId)((({instanceId:e,width:t,consentType:a,implicitConsentMessage:o,explicitConsentMessage:l,setAttributes:d,attributes:p,insertBlocksAfter:u})=>{const f=(0,r.useBlockProps)({id:`jetpack-field-consent-${e}`,className:"jetpack-field jetpack-field-consent"});return React.createElement("div",f,"explicit"===a&&React.createElement("input",{className:"jetpack-field-consent__checkbox",type:"checkbox",disabled:!0}),React.createElement(s.A,{required:!1,label:{implicit:o,explicit:l}[a]??"",attributes:p,setAttributes:d,labelFieldName:`${a}ConsentMessage`,placeholder:(0,c.sprintf)(/* translators: placeholder is a type of consent: implicit or explicit */
__("Add %s consent message…","jetpack-forms"),a),insertBlocksAfter:u}),React.createElement(r.InspectorControls,null,React.createElement(n.PanelBody,{title:__("Manage Responses","jetpack-forms")},React.createElement(m.A,{isChildBlock:!0})),React.createElement(n.PanelBody,{title:__("Field Settings","jetpack-forms")},React.createElement(i.A,{setAttributes:d,width:t}),React.createElement(n.ToggleControl,{label:__("Sync fields style","jetpack-forms"),checked:p.shareFieldAttributes,onChange:e=>d({shareFieldAttributes:e}),help:__("Deactivate for individual styling of this block","jetpack-forms"),__nextHasNoMarginBottom:!0})),React.createElement(r.PanelColorSettings,{title:__("Color","jetpack-forms"),initialOpen:!1,colorSettings:[{value:p.labelColor,onChange:e=>d({labelColor:e}),label:__("Label Text","jetpack-forms")}]}),React.createElement(n.PanelBody,{title:__("Consent Settings","jetpack-forms")},React.createElement(n.BaseControl,{__nextHasNoMarginBottom:!0},React.createElement(n.SelectControl,{label:__("Permission to email","jetpack-forms"),value:a,options:[{label:__("Mention that you can email","jetpack-forms"),value:"implicit"},{label:__("Add a privacy checkbox","jetpack-forms"),value:"explicit"}],onChange:e=>d({consentType:e}),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0})))))}))},1713:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var r=a(4715),n=a(6427),o=a(8468),c=a(7723),l=a(1663),s=a(8799),i=a(975),m=a(1646);const __=c.__,d=({attributes:e,blockClassNames:t,clientId:a,id:c,placeholder:d,placeholderField:p="placeholder",hidePlaceholder:u,required:f,setAttributes:h,type:g,width:k,extraFieldSettings:b=[]})=>{const v=(0,l.BV)(a),E=(0,l.zD)(t),w=["radio","checkbox"].includes(g),R=(e,t=parseInt)=>a=>{const r=t(a,10);h({[e]:isNaN(r)?void 0:r})},C="button"===E?__("Button Text","jetpack-forms"):__("Option Text","jetpack-forms",0),j=w?C:__("Field Text","jetpack-forms",0),x=w?__("Background","jetpack-forms"):__("Field Background","jetpack-forms",0),y=w?__("Options Styles","jetpack-forms"):__("Input Field Styles","jetpack-forms",0),A=[{value:e.labelColor,onChange:e=>h({labelColor:e}),label:__("Label Text","jetpack-forms")},{value:e.inputColor,onChange:e=>h({inputColor:e}),label:j}];w&&"button"===E&&A.push({value:e.buttonBackgroundColor,onChange:e=>h({buttonBackgroundColor:e}),label:__("Button Background","jetpack-forms")}),w&&v!==l.pC.OUTLINED||(A.push({value:e.fieldBackgroundColor,onChange:e=>h({fieldBackgroundColor:e}),label:x}),A.push({value:e.borderColor,onChange:e=>h({borderColor:e}),label:__("Border","jetpack-forms")}));let _=[React.createElement(n.ToggleControl,{key:"required",label:__("Field is required","jetpack-forms"),checked:f,onChange:e=>h({required:e}),help:__('You can edit the "required" label in the editor',"jetpack-forms"),__nextHasNoMarginBottom:!0}),!u&&React.createElement(n.TextControl,{key:"placeholderField",label:__("Placeholder text","jetpack-forms"),value:d||"",onChange:e=>h({[p]:e}),help:__("Show visitors an example of the type of content expected. Otherwise, leave blank.","jetpack-forms"),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),React.createElement(i.A,{key:"width",setAttributes:h,width:k}),React.createElement(n.ToggleControl,{key:"shareFieldAttributes",label:__("Sync fields style","jetpack-forms"),checked:e.shareFieldAttributes,onChange:e=>h({shareFieldAttributes:e}),help:__("Deactivate for individual styling of this block","jetpack-forms"),__nextHasNoMarginBottom:!0})];return b.forEach((({element:e,index:t})=>{(0,o.isValidElement)(e)&&(t>=0&&t<_.length?_=[..._.slice(0,t),e,..._.slice(t)]:_.push(e))})),React.createElement(React.Fragment,null,React.createElement(r.BlockControls,null,React.createElement(s.A,{required:f,onClick:()=>h({required:!f})})),React.createElement(r.InspectorControls,null,React.createElement(n.PanelBody,{title:__("Manage Responses","jetpack-forms")},React.createElement(m.A,{isChildBlock:!0})),React.createElement(n.PanelBody,{title:__("Field Settings","jetpack-forms")},React.createElement(React.Fragment,null,_)),React.createElement(r.PanelColorSettings,{title:__("Color","jetpack-forms"),initialOpen:!1,colorSettings:A}),React.createElement(n.PanelBody,{title:y,initialOpen:!1},React.createElement(n.BaseControl,{__nextHasNoMarginBottom:!0},React.createElement(r.FontSizePicker,{withReset:!0,size:"__unstable-large",__nextHasNoMarginBottom:!0,onChange:e=>h({fieldFontSize:e}),value:e.fieldFontSize})),React.createElement(n.BaseControl,{__nextHasNoMarginBottom:!0},React.createElement(r.LineHeightControl,{__nextHasNoMarginBottom:!0,__unstableInputWidth:"100%",value:e.lineHeight,onChange:R("lineHeight",parseFloat),size:"__unstable-large"})),(w||"button"===E)&&React.createElement(React.Fragment,null,React.createElement(n.RangeControl,{label:__("Button Border Width","jetpack-forms"),value:e.buttonBorderWidth,initialPosition:1,onChange:R("buttonBorderWidth"),min:0,max:100,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),React.createElement(n.RangeControl,{label:__("Button Border Radius","jetpack-forms"),value:e.buttonBorderRadius,initialPosition:0,onChange:R("buttonBorderRadius"),min:0,max:100,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0})),(!w||v===l.pC.OUTLINED)&&React.createElement(React.Fragment,null,React.createElement(n.RangeControl,{label:__("Border Width","jetpack-forms"),value:e.borderWidth,initialPosition:1,onChange:R("borderWidth"),min:0,max:100,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),React.createElement(n.RangeControl,{label:__("Border Radius","jetpack-forms"),value:e.borderRadius,initialPosition:0,onChange:R("borderRadius"),min:0,max:100,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}))),React.createElement(n.PanelBody,{title:__("Label Styles","jetpack-forms"),initialOpen:!1},React.createElement(n.BaseControl,{__nextHasNoMarginBottom:!0},React.createElement(r.FontSizePicker,{withReset:!0,size:"__unstable-large",__nextHasNoMarginBottom:!0,onChange:e=>h({labelFontSize:e}),value:e.labelFontSize})),React.createElement(n.BaseControl,{__nextHasNoMarginBottom:!0},React.createElement(r.LineHeightControl,{__unstableInputWidth:"100%",__nextHasNoMarginBottom:!0,value:e.labelLineHeight,onChange:R("labelLineHeight",parseFloat),size:"__unstable-large"})))),React.createElement(r.InspectorAdvancedControls,null,React.createElement(n.TextControl,{label:__("Name/ID","jetpack-forms"),value:c||"",onChange:e=>{const t=e.replace(/[^a-zA-Z0-9_-]/g,"");h({id:t})},help:__("Customize the input's name/ID. Only alphanumeric, dash and underscore characters are allowed","jetpack-forms"),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0})))}},565:(e,t,a)=>{"use strict";a.d(t,{A:()=>g});var r=a(4715),n=a(4997),o=a(6427),c=a(9491),l=a(7723),s=a(3022),i=a(1663),m=a(8890),d=a(1713),p=a(6869),u=a(1787);const __=l.__,f=(new Date).getFullYear(),h=[{value:"mm/dd/yy",
/* translators: date format. DD is the day of the month, MM the month, and YYYY the year (e.g., 12/31/2023). */
label:__("MM/DD/YYYY","jetpack-forms"),example:`12/31/${f}`},{value:"dd/mm/yy",
/* translators: date format. DD is the day of the month, MM the month, and YYYY the year (e.g., 31/12/2023). */
label:__("DD/MM/YYYY","jetpack-forms"),example:`21/12/${f}`},{value:"yy-mm-dd",
/* translators: date format. DD is the day of the month, MM the month, and YYYY the year (e.g., 2023-12-31). */
label:__("YYYY-MM-DD","jetpack-forms"),example:`${f}-12-31`}],g=(0,c.compose)((0,m.H)(["borderRadius","borderWidth","labelFontSize","fieldFontSize","lineHeight","labelLineHeight","inputColor","labelColor","fieldBackgroundColor","borderColor"]))((e=>{const{attributes:t,clientId:a,isSelected:c,name:l,setAttributes:m,insertBlocksAfter:f}=e,{id:g,label:k,required:b,requiredText:v,width:E,placeholder:w,dateFormat:R}=t;(0,i.DR)({attributes:t,clientId:a,name:l});const{blockStyle:C,fieldStyle:j}=(0,u.U)(t),x=(0,i.BV)(a),y=(0,r.useBlockProps)({className:(0,s.A)("jetpack-field",{"is-selected":c,"has-placeholder":!!w}),style:C});return React.createElement(React.Fragment,null,React.createElement("div",y,React.createElement(p.A,{attributes:t,label:k,suffix:`(${h.find((e=>e.value===R))?.label})`,required:b,requiredText:v,setAttributes:m,style:x}),React.createElement("input",{className:"jetpack-field__input",onChange:e=>m({placeholder:e.target.value}),style:j,type:"text",value:w,onKeyDown:e=>{e.defaultPrevented||"Enter"!==e.key||f((0,n.createBlock)((0,n.getDefaultBlockName)()))}})),React.createElement(d.A,{id:g,required:b,width:E,setAttributes:m,placeholder:w,attributes:t,extraFieldSettings:[{index:1,element:React.createElement(o.SelectControl,{key:"date-format",label:__("Date Format","jetpack-forms"),options:h.map((({value:e,label:t,example:a})=>({value:e,label:`${t} (${a})`}))),onChange:e=>m({dateFormat:e}),value:t.dateFormat,help:__("Select the format in which the date will be displayed.","jetpack-forms"),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0})}]}))}))},4506:(e,t,a)=>{"use strict";a.d(t,{A:()=>g});var r=a(4715),n=a(9491),o=a(8468),c=a(7723),l=a(3022),s=a(6087),i=a(2386),m=a(2453),d=a(1663),p=a(8890),u=a(1713),f=a(6869),h=a(1787);const __=c.__,g=(0,n.compose)((0,p.H)(["borderRadius","borderWidth","labelFontSize","fieldFontSize","lineHeight","labelLineHeight","inputColor","labelColor","fieldBackgroundColor","borderColor"]))((({attributes:e,clientId:t,isSelected:a,name:n,setAttributes:c})=>{const{id:p,label:g,options:k,required:b,requiredText:v,toggleLabel:E,width:w}=e,R=(0,o.useRef)(void 0),C=(0,d.BV)(t),{blockStyle:j}=(0,h.U)(e),x=(0,r.useBlockProps)({className:(0,l.A)("jetpack-field jetpack-field-dropdown",{"is-selected":a,"has-placeholder":!(0,s.isEmpty)(E)}),style:j});(0,d.DR)({attributes:e,clientId:t,name:n});const y=(e,t)=>(0,m.Z)(R.current,"[role=textbox]",e,t),A=(t,a)=>{const r=[...e.options],n=""!==a[a.length-1];r[t]&&(r[t]=a.shift(),t++),r.splice(t,0,...a),c({options:r}),y(t+a.length-1,n)},_=e=>t=>{const a=(0,s.split)(t,"\n").filter((e=>e&&""!==(0,s.trim)(e)));a.length&&(a.length>1?A(e,a):((e,t)=>{const a=[...k];a[e]=t,c({options:a}),y(e)})(e,a.pop()))},B=t=>a=>{if("Enter"!==a.key)return;a.preventDefault();const r=e.options[t];if(!r)return;const n=(0,i.J)(a.target),o=n?r.slice(n):"";A(t,o?[r.slice(0,n),o]:[r,""])},S=t=>()=>{if(1===e.options.length)return;const a=[...e.options];a.splice(t,1),c({options:a}),y(Math.max(t-1,0),!0)};return(0,o.useEffect)((()=>{(0,s.isNil)(g)&&c({label:""}),(0,s.isNil)(E)&&c({toggleLabel:__("Select one option","jetpack-forms")})}),[]),React.createElement("div",x,React.createElement("div",{className:"jetpack-field-dropdown__wrapper"},React.createElement(f.A,{required:b,requiredText:v,label:g,attributes:e,setAttributes:c,isSelected:a,style:C}),React.createElement("div",{className:"jetpack-field-dropdown__toggle"},React.createElement(r.RichText,{value:E,onChange:e=>{c({toggleLabel:e})},allowedFormats:["core/bold","core/italic"],withoutInteractiveFormatting:!0}),React.createElement("span",{className:"jetpack-field-dropdown__icon"}))),a&&React.createElement("div",{className:"jetpack-field-dropdown__popover",ref:R},k.map(((e,t)=>React.createElement(r.RichText,{key:t,value:e,onChange:_(t),onKeyDown:B(t),onRemove:S(t),onReplace:s.noop,placeholder:__("Add option…","jetpack-forms"),__unstableDisableFormats:!0})))),React.createElement(u.A,{id:p,required:b,attributes:e,setAttributes:c,width:w,placeholder:E,placeholderField:"toggleLabel",type:"dropdown"}))}))},630:(e,t,a)=>{"use strict";a.d(t,{A:()=>k});var r=a(5985),n=a(4715),o=a(6427),c=a(9491),l=a(7143),s=a(8468),i=a(7723),m=a(1663),d=a(8890),p=a(1713),u=a(6869),f=a(1149),h=a(1787);const __=i.__,g=[["core/group",{style:{spacing:{padding:{top:"48px",bottom:"48px",left:"48px",right:"48px"},margin:{top:"0",bottom:"0"}},border:{style:"dashed",width:"1px",color:"rgba(125,125,125,0.3)"}}},[["core/image",{url:`${window?.jpFormsBlocks?.defaults?.assetsUrl}/images/upload-icon.svg`,width:"24px",height:"24px",scale:"cover",align:"center",className:"is-style-default",style:{spacing:{margin:{bottom:"20px"}}}}],["core/paragraph",{align:"center",content:__('<strong><a href="#">Select a file</a></strong> or drag and drop your file here',"jetpack-forms"),style:{spacing:{padding:{top:"8px",bottom:"8px"}},typography:{fontSize:"16px"}}}],["core/paragraph",{align:"center",content:__("JPEG, PNG, PDF, and MP4 formats","jetpack-forms"),style:{typography:{fontSize:"14px"}}}]]]],k=(0,c.compose)((0,d.H)(["labelFontSize","labelLineHeight","labelColor"]))((e=>{const{attributes:t,clientId:a,isSelected:c,setAttributes:i}=e,{id:d,label:k,required:b,requiredText:v,width:E}=t,w=(0,r.FB)("field-file");(0,m.DR)({attributes:t,clientId:a});const{blockStyle:R}=(0,h.U)(t),C=(0,n.useBlockProps)({className:"jetpack-field"+(c?" is-selected":""),style:R}),j=(0,l.useSelect)((e=>{const t=e("core/block-editor").getSelectedBlockClientId();return e("core/block-editor").getBlockParents(t).includes(a)})),x=(0,s.useMemo)((()=>(!w||!w.available)&&w?.unavailableReason?.includes("nudge_disabled")),[w]);return React.createElement(React.Fragment,null,React.createElement("div",C,x&&(c||j)&&React.createElement(f.L,{requiredPlan:w?.details?.required_plan}),React.createElement(u.A,{attributes:t,label:k,required:b,requiredText:v,setAttributes:i}),React.createElement("div",{className:"jetpack-form-file-field__dropzone"},React.createElement("div",{className:"jetpack-form-file-field__dropzone-inner"},React.createElement("input",{type:"file",style:{display:"none"},"aria-hidden":"true"}),React.createElement(n.InnerBlocks,{template:g,templateLock:!1})))),React.createElement(p.A,{id:d,required:b,width:E,setAttributes:i,attributes:t,hidePlaceholder:!0,extraFieldSettings:[{index:1,element:React.createElement("p",{key:"max-file-size"},__("Maximum file size is set to 20MB","jetpack-forms"))},{index:2,element:React.createElement(o.__experimentalNumberControl,{key:"maxfiles",label:__("Number of files","jetpack-forms"),value:t.maxfiles,onChange:e=>i({maxfiles:e}),max:10,min:1,step:1,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,help:__("Maximum number of files that the user is able to upload per form submission.","jetpack-forms")})}]}))}))},6869:(e,t,a)=>{"use strict";a.d(t,{A:()=>f});var r=a(6072),n=a.n(r),o=a(4715),c=a(4997),l=a(9491),s=a(8468),i=a(7723),m=a(3022),d=a(1663),p=a(1787);const __=i.__;const u=({attributes:e,className:t,label:a,suffix:r,labelFieldName:n,placeholder:i,resetFocus:d,required:u,requiredText:f,setAttributes:h,insertBlocksAfter:g})=>{const{labelStyle:k}=(0,p.U)(e),b=function(e){const t=(0,s.useRef)(e);return t.current=e,(0,l.useRefEffect)((e=>{const{insertBlocksAfter:a}=t.current;if(a)return e.addEventListener("keydown",r),()=>{e.removeEventListener("keydown",r)};function r(e){e.defaultPrevented||"Enter"!==e.key||e.shiftKey||(e.preventDefault(),a((0,c.createBlock)((0,c.getDefaultBlockName)())))}}),[])}({insertBlocksAfter:g});return React.createElement("div",{className:(0,m.A)(t,"jetpack-field-label"),style:k},React.createElement(o.RichText,{ref:b,tagName:"label",value:a,className:"jetpack-field-label__input",onChange:e=>{d&&d(),h(n?{[n]:e}:{label:e})},placeholder:i??__("Add label…","jetpack-forms"),withoutInteractiveFormatting:!0,allowedFormats:["core/italic"]}),r&&React.createElement("span",{className:"jetpack-field-label__suffix"},r),u&&React.createElement(o.RichText,{tagName:"span",value:f||__("(required)","jetpack-forms"),className:"required",onChange:e=>{h({requiredText:e})},withoutInteractiveFormatting:!0,allowedFormats:["core/bold","core/italic"]}))},f=e=>{const{style:t}=e,a=(0,m.A)({"notched-label__label":t===d.pC.OUTLINED,"animated-label__label":t===d.pC.ANIMATED,"below-label__label":t===d.pC.BELOW});return t===d.pC.OUTLINED?React.createElement("div",{className:"notched-label"},React.createElement("div",{className:"notched-label__leading"}),React.createElement("div",{className:"notched-label__notch"},React.createElement(u,n()({className:a},e))),React.createElement("div",{className:"notched-label__trailing"})):React.createElement(u,n()({className:a},e))}},2438:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(6072),n=a.n(r),o=a(6563);function c(e){return React.createElement(o.A,n()({},e,{type:"checkbox"}))}},3052:(e,t,a)=>{"use strict";a.d(t,{A:()=>m});var r=a(6427),n=a(7723),o=a(9787),c=a(1097),l=a(8230),s=a(2438),i=a(1747);const __=n.__,m={name:"field-checkbox-multiple",settings:{...l.A,title:__("Multiple Choice (Checkbox)","jetpack-forms"),keywords:[__("Choose Multiple","jetpack-forms"),__("Option","jetpack-forms")],description:__("Offer users a list of choices, and allow them to select multiple options.","jetpack-forms"),icon:{foreground:(0,o.V)(),src:(0,c.A)(React.createElement(r.Path,{d:"M7.0812 10.1419L10.6001 5.45005L9.40006 4.55005L6.91891 7.85824L5.53039 6.46972L4.46973 7.53038L7.0812 10.1419ZM12 8.5H20V7H12V8.5ZM12 17H20V15.5H12V17ZM8.5 14.5H5.5V17.5H8.5V14.5ZM5.5 13H8.5C9.32843 13 10 13.6716 10 14.5V17.5C10 18.3284 9.32843 19 8.5 19H5.5C4.67157 19 4 18.3284 4 17.5V14.5C4 13.6716 4.67157 13 5.5 13Z"}))},edit:s.A,save:i.A,allowedBlocks:["jetpack/field-option-checkbox"],attributes:{label:{type:"string",default:"Choose several options"}}}}},2496:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(6072),n=a.n(r),o=a(9659);function c(e){return React.createElement(o.A,n()({},e,{type:"checkbox"}))}},358:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(6427),n=a(7723),o=a(9787),c=a(1097),l=a(4414),s=a(2496);const __=n.__,i={name:"field-option-checkbox",settings:{...l.A,title:__("Multiple Choice Option","jetpack-forms"),parent:["jetpack/field-checkbox-multiple"],icon:{foreground:(0,o.V)(),src:(0,c.A)(React.createElement(r.Path,{d:"M5.5 10.5H8.5V13.5H5.5V10.5ZM8.5 9H5.5C4.67157 9 4 9.67157 4 10.5V13.5C4 14.3284 4.67157 15 5.5 15H8.5C9.32843 15 10 14.3284 10 13.5V10.5C10 9.67157 9.32843 9 8.5 9ZM12 12.75H20V11.25H12V12.75Z"}))},edit:s.A}}},1747:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(4715);const n=()=>React.createElement("div",r.useBlockProps.save(),React.createElement(r.InnerBlocks.Content,null))},3286:(e,t,a)=>{"use strict";a.d(t,{A:()=>h});var r=a(4715),n=a(4997),o=a(6427),c=a(9491),l=a(7723),s=a(3022),i=a(6087),m=a(1663),d=a(8890),p=a(1713),u=a(6869),f=a(1787);const __=l.__,h=(0,c.compose)((0,d.H)(["borderRadius","borderWidth","labelFontSize","fieldFontSize","lineHeight","labelLineHeight","inputColor","labelColor","fieldBackgroundColor","borderColor"]))((e=>{const{attributes:t,clientId:a,id:c,isSelected:l,required:d,requiredText:h,label:g,setAttributes:k,placeholder:b,min:v,max:E,width:w,insertBlocksAfter:R}=e,{blockStyle:C,fieldStyle:j}=(0,f.U)(t),x=(0,m.BV)(a),y=(0,r.useBlockProps)({className:(0,s.A)("jetpack-field",{"is-selected":l,"has-placeholder":!(0,i.isEmpty)(b)}),style:C});return React.createElement(React.Fragment,null,React.createElement("div",y,React.createElement(u.A,{attributes:t,label:g,required:d,requiredText:h,setAttributes:k,style:x}),React.createElement("input",{className:"jetpack-field__input",onChange:e=>k({placeholder:e.target.value}),style:j,type:l?"text":"number",value:l?b:"",placeholder:b,min:v,max:E,onKeyDown:e=>{e.defaultPrevented||"Enter"!==e.key||R((0,n.createBlock)((0,n.getDefaultBlockName)()))}})),React.createElement(p.A,{id:c,required:d,width:w,setAttributes:k,placeholder:b,attributes:t,extraFieldSettings:[{index:1,element:React.createElement(o.__experimentalNumberControl,{key:"min",label:__("Minimum value","jetpack-forms"),value:t.min,onChange:e=>k({min:e}),max:E,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,help:__("The minimum value to accept in the input. Leaving empty allows any negative and positive values.","jetpack-forms")})},{index:2,element:React.createElement(o.__experimentalNumberControl,{key:"max",label:__("Maximum value","jetpack-forms"),value:t.max,onChange:e=>k({max:e}),min:v,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,help:__("The maximum value to accept in the input.","jetpack-forms")})}]}))}))},9678:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(6072),n=a.n(r),o=a(6563);function c(e){return React.createElement(o.A,n()({},e,{type:"radio"}))}},5700:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var r=a(6427),n=a(8468),o=a(7723),c=a(9787),l=a(1097),s=a(8230),i=a(9678),m=a(8891);const __=o.__,d={name:"field-radio",settings:{...s.A,title:__("Single Choice (Radio)","jetpack-forms"),keywords:[__("Choose","jetpack-forms"),__("Select","jetpack-forms"),__("Option","jetpack-forms")],description:__("Offer users a list of choices, and allow them to select a single option.","jetpack-forms"),icon:{foreground:(0,c.V)(),src:(0,l.A)(React.createElement(n.Fragment,null,React.createElement(r.Path,{d:"M4 7.75C4 9.40685 5.34315 10.75 7 10.75C8.65685 10.75 10 9.40685 10 7.75C10 6.09315 8.65685 4.75 7 4.75C5.34315 4.75 4 6.09315 4 7.75ZM20 8.5H12V7H20V8.5ZM20 17H12V15.5H20V17ZM7 17.75C6.17157 17.75 5.5 17.0784 5.5 16.25C5.5 15.4216 6.17157 14.75 7 14.75C7.82843 14.75 8.5 15.4216 8.5 16.25C8.5 17.0784 7.82843 17.75 7 17.75ZM4 16.25C4 17.9069 5.34315 19.25 7 19.25C8.65685 19.25 10 17.9069 10 16.25C10 14.5931 8.65685 13.25 7 13.25C5.34315 13.25 4 14.5931 4 16.25Z"})))},edit:i.A,save:m.A,allowedBlocks:["jetpack/field-option-radio"],attributes:{label:{type:"string",default:"Choose one option"}}}}},9864:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(6072),n=a.n(r),o=a(9659);function c(e){return React.createElement(o.A,n()({},e,{type:"radio"}))}},5214:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(6427),n=a(7723),o=a(9787),c=a(1097),l=a(4414),s=a(9864);const __=n.__,i={name:"field-option-radio",settings:{...l.A,title:__("Single Choice Option","jetpack-forms"),parent:["jetpack/field-radio"],icon:{foreground:(0,o.V)(),src:(0,c.A)(React.createElement(r.Path,{d:"M7.5 13.5C6.67157 13.5 6 12.8284 6 12C6 11.1716 6.67157 10.5 7.5 10.5C8.32843 10.5 9 11.1716 9 12C9 12.8284 8.32843 13.5 7.5 13.5ZM4.5 12C4.5 13.6569 5.84315 15 7.5 15C9.15685 15 10.5 13.6569 10.5 12C10.5 10.3431 9.15685 9 7.5 9C5.84315 9 4.5 10.3431 4.5 12ZM12.5 12.75H20.5V11.25H12.5V12.75Z"}))},edit:s.A}}},8891:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(4715);const n=()=>React.createElement("div",r.useBlockProps.save(),React.createElement(r.InnerBlocks.Content,null))},7817:(e,t,a)=>{"use strict";a.d(t,{A:()=>u});var r=a(4715),n=a(9491),o=a(8468),c=a(3022),l=a(6087),s=a(1663),i=a(8890),m=a(1713),d=a(6869),p=a(1787);const u=(0,n.compose)((0,i.H)(["borderRadius","borderWidth","labelFontSize","fieldFontSize","lineHeight","labelLineHeight","inputColor","labelColor","fieldBackgroundColor","borderColor"]))((e=>{const{attributes:t,clientId:a,id:n,isSelected:i,required:u,requiredText:f,label:h,setAttributes:g,placeholder:k,width:b}=e,v=(0,s.BV)(a),{blockStyle:E,fieldStyle:w}=(0,p.U)(t),R=(0,r.useBlockProps)({className:(0,c.A)("jetpack-field jetpack-field-textarea",{"is-selected":i,"has-placeholder":!(0,l.isEmpty)(k)}),style:E});return(0,o.useEffect)((()=>{(0,l.isNil)(h)&&g({label:""})}),[]),React.createElement(React.Fragment,null,React.createElement("div",R,React.createElement(d.A,{clientId:a,required:u,requiredText:f,label:h,setAttributes:g,attributes:t,style:v}),React.createElement("textarea",{className:"jetpack-field__textarea",value:k,onChange:e=>g({placeholder:e.target.value}),style:w})),React.createElement(m.A,{id:n,required:u,setAttributes:g,width:b,placeholder:k,attributes:t}))}))},975:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(6427),n=a(7723);const __=n.__,o=[25,50,75,100];function c({setAttributes:e,width:t}){return React.createElement(r.BaseControl,{help:__("Adjust the width of the field to include multiple fields on a single line.","jetpack-forms"),__nextHasNoMarginBottom:!0},React.createElement(r.__experimentalToggleGroupControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,"aria-label":__("Width","jetpack-forms"),isBlock:!0,label:__("Width","jetpack-forms"),onChange:t=>e({width:t}),value:t},o.map((e=>React.createElement(r.__experimentalToggleGroupControlOption,{key:e,label:`${e}%`,value:e})))))}},1978:(e,t,a)=>{"use strict";a.d(t,{A:()=>h});var r=a(6072),n=a.n(r),o=a(4715),c=a(4997),l=a(9491),s=a(2619),i=a(3022),m=a(1663),d=a(8890),p=a(1713),u=a(6869),f=a(1787);const h=(0,l.compose)((0,d.H)(["borderRadius","borderWidth","labelFontSize","fieldFontSize","lineHeight","labelLineHeight","inputColor","labelColor","fieldBackgroundColor","borderColor"]))((e=>{const{attributes:t,clientId:a,id:r,isSelected:n,required:l,requiredText:s,label:d,setAttributes:h,placeholder:g="",width:k,insertBlocksAfter:b,type:v}=e,{blockStyle:E,fieldStyle:w}=(0,f.U)(t),R=(0,m.BV)(a),C=(0,o.useBlockProps)({className:(0,i.A)("jetpack-field",{"is-selected":n,"has-placeholder":""!==g}),style:E});return React.createElement(React.Fragment,null,React.createElement("div",C,React.createElement(u.A,{attributes:t,label:d,required:l,requiredText:s,setAttributes:h,style:R}),React.createElement("input",{className:"jetpack-field__input",onChange:e=>h({placeholder:e.target.value}),style:w,type:n?"text":v,value:n?g:"",placeholder:g,onClick:e=>"file"===v&&e.preventDefault(),onKeyDown:e=>{e.defaultPrevented||"Enter"!==e.key||b((0,c.createBlock)((0,c.getDefaultBlockName)()))}})),React.createElement(p.A,{id:r,required:l,width:k,setAttributes:h,placeholder:g,attributes:t}))})),g=(0,l.createHigherOrderComponent)((e=>t=>{if(t.name.indexOf("jetpack/field")>-1){const a=t.attributes.width?"jetpack-field__width-"+t.attributes.width:"";return React.createElement(e,n()({},t,{className:a}))}return React.createElement(e,t)}),"withCustomClassName");(0,s.addFilter)("editor.BlockListBlock","jetpack/contact-form",g)},9415:(e,t,a)=>{"use strict";a.d(t,{A:()=>p});var r=a(5985),n=a(4715),o=a(6427),c=a(8468),l=a(7723),s=a(435),i=a(1684),m=a(8813),d=a(5845);const __=l.__;function p({attributes:e,setAttributes:t}){const[a,l]=(0,c.useState)(!1),{integrations:p,refreshIntegrations:u,isLoading:f}=(0,d.R)(),{tracks:h}=(0,r.st)(),g=e=>{h.recordEvent("jetpack_forms_block_modal_view",{entry_point:e}),l(!0)};return React.createElement(React.Fragment,null,React.createElement(o.PanelBody,{title:__("Integrations","jetpack-forms"),className:"jetpack-contact-form__integrations-panel",initialOpen:!1},React.createElement(m.A,{integrations:p,attributes:e,isLoading:f}),React.createElement(o.Button,{variant:"secondary",onClick:()=>g("block-sidebar"),__next40pxDefaultSize:!0},__("Manage integrations","jetpack-forms"))),React.createElement(n.BlockControls,null,React.createElement(o.ToolbarGroup,null,React.createElement(o.ToolbarButton,{icon:s.A,onClick:()=>g("block-toolbar"),style:{paddingLeft:0}},__("Integrations","jetpack-forms")))),React.createElement(i.A,{isOpen:a,onClose:()=>l(!1),attributes:e,setAttributes:t,integrationsData:p,refreshIntegrations:u}))}},8813:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(8377),n=a(8478),o=a(6427),c=a(7723),l=a(2590);const __=c.__,s=r.T["Jetpack Green 40"];function i({integrations:e,attributes:t,isLoading:a}){const r=e.reduce(((e,a)=>{switch(a.id){case"akismet":a.isConnected&&e.push({...a,icon:React.createElement(l.A,{width:30,height:30}),tooltip:__("Akismet is connected for this form","jetpack-forms")});break;case"zero-bs-crm":a.isActive&&a.details?.hasExtension&&t.jetpackCRM&&e.push({...a,icon:React.createElement(n.sT,{size:30,color:s}),tooltip:__("Jetpack CRM is connected for this form","jetpack-forms")})}return e}),[]);return a?React.createElement("div",{className:"jetpack-forms-active-integrations"},React.createElement(o.Spinner,null)):r?.length?React.createElement("div",{className:"jetpack-forms-active-integrations"},r.map((e=>React.createElement(o.Tooltip,{key:e.id,text:e.tooltip},React.createElement("span",{className:"jetpack-forms-active-integrations__item"},e.icon,React.createElement("span",{className:"jetpack-forms-active-integrations__status"})))))):null}},9593:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(3924),n=a(6427),o=a(8468),c=a(7723),l=a(2590),s=a(4982);const __=c.__,i=({isExpanded:e,onToggle:t,data:a,refreshStatus:c})=>{const i=window?.jpFormsBlocks?.defaults?.formsAdminUrl||"",{isConnected:m=!1,settingsUrl:d=""}=a||{},p={...a,showHeaderToggle:!0,headerToggleValue:m,isHeaderToggleEnabled:!1,isLoading:!a||void 0===a.isInstalled,refreshStatus:c,trackEventName:"jetpack_forms_upsell_akismet_click",notInstalledMessage:(0,o.createInterpolateElement)(__("Add one-click spam protection for your forms with <a>Akismet</a>. Simply install the plugin and you're set.","jetpack-forms"),{a:React.createElement(n.ExternalLink,{href:(0,r.A)("akismet-wordpress-org")})}),notActivatedMessage:__("Akismet is installed. Just activate the plugin to start blocking spam.","jetpack-forms")};return React.createElement(s.A,{title:__("Akismet Spam Protection","jetpack-forms"),description:__("Akismet filters out form spam with 99% accuracy","jetpack-forms"),icon:React.createElement(l.A,null),isExpanded:e,onToggle:t,cardData:p,toggleTooltip:__("We keep your forms protected","jetpack-forms")},m?React.createElement("div",null,React.createElement("p",null,__("Your forms are automatically protected with Akismet.","jetpack-forms")),React.createElement("div",{className:"integration-card__links"},React.createElement(n.Button,{variant:"link",href:i,target:"_blank",rel:"noopener noreferrer"},__("View spam","jetpack-forms")),React.createElement("span",null,"|"),React.createElement(n.Button,{variant:"link",href:d,target:"_blank",rel:"noopener noreferrer"},__("View stats","jetpack-forms")),React.createElement("span",null,"|"),React.createElement(n.ExternalLink,{href:(0,r.A)("akismet-jetpack-forms-docs")},__("Learn about Akismet","jetpack-forms")))):React.createElement("div",null,React.createElement("p",{className:"integration-card__description"},(0,o.createInterpolateElement)(__("Akismet is active. There is one step left. Please add your <a>Akismet key</a>.","jetpack-forms"),{a:React.createElement(n.ExternalLink,{href:d})})),React.createElement(n.Button,{variant:"secondary",href:d,target:"_blank",rel:"noopener noreferrer",__next40pxDefaultSize:!0},__("Add Akismet key","jetpack-forms"))))}},422:(e,t,a)=>{"use strict";a.d(t,{A:()=>m});var r=a(4715),n=a(4997),o=a(6427),c=a(7143),l=a(7723),s=a(8169),i=a(4982);const __=l.__,m=({isExpanded:e,onToggle:t,data:a,refreshStatus:l,borderBottom:m=!0})=>{const{settingsUrl:d=""}=a||{},p=(0,c.useSelect)((e=>e(r.store).getSelectedBlock()),[]),{insertBlock:u,removeBlock:f}=(0,c.useDispatch)(r.store),h=p?.innerBlocks?.some((({name:e})=>"jetpack/field-email"===e)),g=p?.innerBlocks?.find((({name:e})=>"jetpack/field-consent"===e)),k={...a,showHeaderToggle:!1,isLoading:!a||void 0===a.isInstalled,refreshStatus:l,trackEventName:"jetpack_forms_upsell_creative_mail_click",notInstalledMessage:__("To start sending email campaigns, install the Creative Mail plugin.","jetpack-forms"),notActivatedMessage:__("Creative Mail is installed. To start sending email campaigns, simply activate the plugin.","jetpack-forms")};return React.createElement(i.A,{title:__("Creative Mail","jetpack-forms"),description:__("Manage email contacts and campaigns","jetpack-forms"),icon:s.A,isExpanded:e,onToggle:t,cardData:k,borderBottom:m},React.createElement("div",null,React.createElement("p",null,__("You're all setup for email marketing with Creative Mail. Please manage your marketing from Creative Mail panel.","jetpack-forms")),h&&React.createElement(o.ToggleControl,{label:__("Add email permission request before submit button","jetpack-forms"),checked:!!g,onChange:async()=>{if(g)await f(g.clientId,!1);else{const e=p.innerBlocks.findIndex((({name:e})=>"jetpack/button"===e)),t=await(0,n.createBlock)("jetpack/field-consent");await u(t,e,p.clientId,!1)}}}),React.createElement(o.Button,{variant:"link",href:d,target:"_blank",rel:"noopener noreferrer",className:"jetpack-forms-creative-mail-settings-button"},__("Open Creative Mail settings","jetpack-forms"))))}},5845:(e,t,a)=>{"use strict";a.d(t,{R:()=>l});var r=a(1455),n=a.n(r),o=a(8468),c=a(3832);const l=()=>{const[e,t]=(0,o.useState)({isLoading:!0,integrations:[],error:null}),a=(0,o.useCallback)((async()=>{try{const e=await n()({path:(0,c.addQueryArgs)("/wp/v2/feedback/integrations",{version:2})});t({isLoading:!1,integrations:e,error:null})}catch(e){t({isLoading:!1,integrations:[],error:e})}}),[]),r=(0,o.useCallback)((async()=>{t((e=>({...e,isLoading:!0}))),await a()}),[a]);return(0,o.useEffect)((()=>{a()}),[a]),{...e,refreshIntegrations:r}}},8407:(e,t,a)=>{"use strict";a.d(t,{X:()=>c});var r=a(5985),n=a(8468),o=a(1620);const c=(e,t,a,c)=>{const[l,s]=(0,n.useState)(!1),{tracks:i}=(0,r.st)();return{isInstalling:l,installPlugin:(0,n.useCallback)((async()=>{s(!0),c&&i.recordEvent(c,{screen:"block-editor",intent:a?"activate-plugin":"install-plugin"});try{return a?await(0,o.Bf)(t):await(0,o.pj)(e),!0}catch{return!1}finally{s(!1)}}),[e,t,a,i,c])}}},1684:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(6427),n=a(8468),o=a(7723),c=a(9593),l=a(422),s=a(3754);const __=o.__,i=({isOpen:e,onClose:t,attributes:a,setAttributes:o,integrationsData:i,refreshIntegrations:m})=>{const[d,p]=(0,n.useState)({akismet:!1,crm:!1,creativemail:!1});if(!e)return null;const u=e=>{p((t=>({...t,[e]:!t[e]})))},f=e=>i?.find((t=>t.id===e));return React.createElement(r.Modal,{title:__("Manage integrations","jetpack-forms"),onRequestClose:t,style:{width:"700px"},className:"jetpack-forms-integrations-modal"},React.createElement(r.__experimentalVStack,{spacing:"4"},React.createElement(c.A,{isExpanded:d.akismet,onToggle:()=>u("akismet"),data:f("akismet"),refreshStatus:m}),React.createElement(s.A,{isExpanded:d.crm,onToggle:()=>u("crm"),jetpackCRM:a.jetpackCRM,setAttributes:o,data:f("zero-bs-crm"),refreshStatus:m}),React.createElement(l.A,{isExpanded:d.creativemail,onToggle:()=>u("creativemail"),data:f("creative-mail-by-constant-contact"),refreshStatus:m,borderBottom:!1})))}},4982:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(6427),n=a(4704),o=a(493);const c=({title:e,description:t,icon:a="admin-plugins",isExpanded:c,onToggle:l,children:s,cardData:i={},toggleTooltip:m,borderBottom:d=!0})=>React.createElement(r.Card,{className:"integration-card",isBorderless:!0,borderBottom:d,isRounded:!1},React.createElement(o.A,{title:e,description:t,icon:a,isExpanded:c,onToggle:l,cardData:i,toggleTooltip:m}),React.createElement(n.A,{isExpanded:c,cardData:i},s))},4704:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(6427);const n=({isExpanded:e,children:t,cardData:a={}})=>{if(!e)return null;const{notInstalledMessage:n,notActivatedMessage:o,isInstalled:c,isActive:l,isLoading:s,type:i}=a,m="plugin"===i,d=m&&!c,p=m&&c&&!l,u=m&&c&&l||"service"===i;return s?React.createElement(r.CardBody,null,React.createElement(r.Spinner,null)):React.createElement(r.CardBody,null,d&&React.createElement("p",{className:"integration-card__description"},n),p&&React.createElement("p",{className:"integration-card__description"},o),u&&t)}},493:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(6427),n=a(7723),o=a(8248),c=a(4969),l=a(2039);const __=n.__,s=({title:e,description:t,icon:a,isExpanded:n,onToggle:s,cardData:i={},toggleTooltip:m})=>{const{isInstalled:d,isActive:p,isConnected:u,type:f,showHeaderToggle:h,headerToggleValue:g,isHeaderToggleEnabled:k,onHeaderToggleChange:b}=i,v=!("plugin"!==f||d&&p),E=p&&u,w=__("Disable for this form","jetpack-forms"),R=__("Enable for this form","jetpack-forms");return React.createElement(r.CardHeader,{onClick:e=>{e.target.closest(".components-form-toggle")||s(e)},className:"integration-card__header"},React.createElement("div",{className:"integration-card__header-content"},React.createElement("div",{className:"integration-card__header-main"},React.createElement("div",{className:"integration-card__service-icon-container"},React.createElement(r.Icon,{icon:a,className:"integration-card__service-icon",size:30})),React.createElement("div",{className:"integration-card__title-section"},React.createElement("div",{className:"integration-card__title-row"},React.createElement("h3",{className:"integration-card__title"},e),v&&React.createElement("span",{className:"integration-card__plugin-badge"},__("Plugin","jetpack-forms")),E&&React.createElement("span",{className:"integration-card__connected-badge"},React.createElement(r.Icon,{icon:"yes-alt",size:16}),__("Connected","jetpack-forms"))),t&&React.createElement("span",{className:"integration-card__description"},t))),React.createElement(r.__experimentalHStack,{spacing:"3",alignment:"center",justify:"end",expanded:!1},v&&React.createElement(l.A,{slug:i.slug,pluginFile:i.pluginFile,isInstalled:d,refreshStatus:i.refreshStatus,trackEventName:i.trackEventName}),!v&&h&&React.createElement(r.Tooltip,{text:(C=g,m||(C?w:R))},React.createElement("span",{className:"integration-card__toggle-tooltip-wrapper"},React.createElement(r.ToggleControl,{checked:g&&(p||u),onChange:e=>{b&&b(e)},disabled:!k||!(p||u),__nextHasNoMarginBottom:!0}))),React.createElement(r.Icon,{icon:n?o.A:c.A}))));var C}},2039:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(6427),n=a(7723),o=a(8407);const __=n.__,c=({slug:e,pluginFile:t,isInstalled:a,refreshStatus:n,trackEventName:c})=>{const{isInstalling:l,installPlugin:s}=(0,o.X)(e,t,a,c),i=__("Activate this plugin","jetpack-forms"),m=__("Install this plugin","jetpack-forms");return React.createElement(r.Tooltip,{text:a?i:m},React.createElement(r.Button,{variant:"primary",onClick:async e=>{e.stopPropagation();await s()&&n&&n()},disabled:l,icon:l?React.createElement(r.Spinner,null):void 0,__next40pxDefaultSize:!0},l&&a&&__("Activating…","jetpack-forms")||l&&__("Installing…","jetpack-forms")||a&&__("Activate","jetpack-forms")||__("Install","jetpack-forms")))}},3754:(e,t,a)=>{"use strict";a.d(t,{A:()=>p});var r=a(8377),n=a(8478),o=a(6427),c=a(8468),l=a(7723),s=a(8265),i=a.n(s),m=a(4982);const __=l.__,d=r.T["Jetpack Green 40"],p=({isExpanded:e,onToggle:t,jetpackCRM:a,setAttributes:r,data:l,refreshStatus:s})=>{const{settingsUrl:p="",version:u="",details:f={}}=l||{},{hasExtension:h=!1,canActivateExtension:g=!1}=f,k=i().coerce(u),b=k&&i().gte(k,"4.9.1"),v=__("This form is connected to Jetpack CRM.","jetpack-forms"),E=__("To connect this form to Jetpack CRM, enable the toggle above.","jetpack-forms"),w={...l,showHeaderToggle:!0,headerToggleValue:a,isHeaderToggleEnabled:!0,onHeaderToggleChange:e=>r({jetpackCRM:e}),isLoading:!l||void 0===l.isInstalled,refreshStatus:s,trackEventName:"jetpack_forms_upsell_crm_click",notInstalledMessage:__("You can save your form contacts in Jetpack CRM. To get started, please install the plugin.","jetpack-forms"),notActivatedMessage:__("Jetpack CRM is installed. To start saving contacts, simply activate the plugin.","jetpack-forms")};return React.createElement(m.A,{title:__("Jetpack CRM","jetpack-forms"),description:__("Store contact form submissions in your CRM","jetpack-forms"),icon:React.createElement(n.sT,{color:d}),isExpanded:e,onToggle:t,cardData:w},b?h?React.createElement("div",null,React.createElement("p",null,a?v:E),React.createElement(o.Button,{variant:"link",href:p,target:"_blank",rel:"noopener noreferrer"},__("Open Jetpack CRM settings","jetpack-forms"))):React.createElement("div",null,React.createElement("p",null,(0,c.createInterpolateElement)(__("You can integrate this contact form with Jetpack CRM by enabling Jetpack CRM's <a>Jetpack Forms extension</a>.","jetpack-forms"),{a:React.createElement(o.Button,{variant:"link",href:p,target:"_blank",rel:"noopener noreferrer"})})),!g&&React.createElement("p",null,__("A site administrator must enable the CRM Jetpack Forms extension.","jetpack-forms")),g&&React.createElement(o.Button,{variant:"secondary",href:p,target:"_blank",rel:"noopener noreferrer",__next40pxDefaultSize:!0},__("Enable Jetpack Forms extension","jetpack-forms"))):React.createElement("div",null,React.createElement("p",null,__("Please update to the latest version of the Jetpack CRM plugin to integrate your contact form with your CRM.","jetpack-forms")),React.createElement(o.Button,{variant:"secondary",href:p,target:"_blank",rel:"noopener noreferrer",__next40pxDefaultSize:!0},__("Update Jetpack CRM","jetpack-forms"))))}},1646:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(6427),n=a(7723),o=a(3909),c=a(599);const __=n.__,l=()=>React.createElement(React.Fragment,null,React.createElement(c.A,null,__("Manage and export your form responses in WPAdmin:","jetpack-forms")),React.createElement(r.Button,{variant:"secondary",href:o.x7,__next40pxDefaultSize:!0},__("View Form Responses","jetpack-forms"),React.createElement("span",{className:"screen-reader-text"},__("(opens in a new tab)","jetpack-forms"))))},9364:(e,t,a)=>{"use strict";a.d(t,{A:()=>m,F:()=>i});var r=a(6427),n=a(8468),o=a(7723),c=a(615),l=a(9787),s=a(1097);const __=o.__,i={name:"salesforce-web-to-lead-form",title:__("Salesforce Lead Form","jetpack-forms"),description:__("Add a Salesforce Lead form to your site","jetpack-forms"),icon:{foreground:(0,l.V)(),src:(0,s.A)(React.createElement(r.Path,{d:"M10.6778 7.72509C11.1949 7.1895 11.9152 6.8509 12.7094 6.8509C13.7682 6.8509 14.6855 7.4419 15.178 8.31608C15.6028 8.12524 16.0768 8.02059 16.5755 8.02059C18.4839 8.02059 20.0229 9.57811 20.0229 11.4988C20.0229 13.4196 18.4777 14.9771 16.5755 14.9771C16.3415 14.9771 16.1138 14.9525 15.8983 14.9094C15.4674 15.6789 14.6424 16.2022 13.6944 16.2022C13.3004 16.2022 12.9248 16.1099 12.5924 15.9498C12.1553 16.9779 11.1334 17.7043 9.94523 17.7043C8.70783 17.7043 7.64896 16.9225 7.24265 15.8205C7.06412 15.8574 6.87943 15.8759 6.69475 15.8759C5.21725 15.8759 4.02295 14.6693 4.02295 13.1733C4.02295 12.176 4.55854 11.3018 5.35885 10.834C5.19263 10.4584 5.10029 10.0398 5.10029 9.59658C5.09413 7.8913 6.49159 6.5 8.20302 6.5C9.21264 6.5 10.1114 6.98018 10.6778 7.72509Z"}),24,24,"0 0 24 24")},innerBlocks:[["jetpack/field-email",{required:!0,label:__("Business Email","jetpack-forms"),id:"email"}],["jetpack/field-name",{required:!0,label:__("First Name","jetpack-forms"),id:"first_name"}],["jetpack/field-name",{required:!0,label:__("Last Name","jetpack-forms"),id:"last_name"}],["jetpack/field-text",{required:!0,label:__("Job Title","jetpack-forms"),id:"title"}],["jetpack/field-text",{required:!0,label:__("Company","jetpack-forms"),id:"company"}],["jetpack/field-telephone",{required:!0,label:__("Phone Number","jetpack-forms"),id:"phone"}],["jetpack/button",{text:__("Submit","jetpack-forms"),element:"button",lock:{remove:!0}}]],attributes:{subject:__("New lead received from your website","jetpack-forms"),salesforceData:{organizationId:"",sendToSalesforce:!0}},example:{innerBlocks:[{name:"jetpack/field-email",attributes:{required:!0,label:__("Business Email","jetpack-forms")}},{name:"jetpack/field-name",attributes:{required:!0,label:__("First Name","jetpack-forms")}},{name:"jetpack/field-name",attributes:{required:!0,label:__("Last Name","jetpack-forms")}},{name:"jetpack/field-text",attributes:{required:!0,label:__("Job Title","jetpack-forms")}},{name:"jetpack/field-text",attributes:{required:!0,label:__("Company","jetpack-forms")}},{name:"jetpack/field-telephone",attributes:{required:!0,label:__("Phone Number","jetpack-forms")}},{name:"jetpack/button",attributes:{text:__("Submit","jetpack-forms"),element:"button",lock:{remove:!0}}}]}},m=({salesforceData:e,setAttributes:t,instanceId:a})=>{const[o,l]=(0,n.useState)(!1);return React.createElement(n.Fragment,null,React.createElement(r.PanelBody,{title:__("Salesforce","jetpack-forms"),initialOpen:!0},React.createElement(r.BaseControl,{__nextHasNoMarginBottom:!0},React.createElement(r.TextControl,{label:__("Organization ID","jetpack-forms"),value:e.organizationId||"",placeholder:__("Enter your Organization ID","jetpack-forms"),onBlur:e=>{l(!e.target.value.trim().match(/^[a-zA-Z0-9]{15,18}$/))},onChange:a=>{var r;l(!1),r={organizationId:a.trim()},t({salesforceData:{...e,...r}})},help:__("Enter the Salesforce organization ID to send Leads to.","jetpack-forms"),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),o&&React.createElement(c.A,{isError:!0,id:`contact-form-${a}-email-error`},__("Invalid Organization ID. Should be a 15 – 18 characters long alphanumeric string.","jetpack-forms")),React.createElement(r.ExternalLink,{href:"https://help.salesforce.com/s/articleView?id=000325251&type=1"},__("Where to find your Salesforce Organization ID","jetpack-forms")))))}},1149:(e,t,a)=>{"use strict";a.d(t,{L:()=>c});var r=a(5985),n=a(2201),o=a(7723);const __=o.__,c=({requiredPlan:e})=>{const[t,a,o]=(0,r._6)(e);return React.createElement("div",{className:"jetpack-forms-upsell-nudge"},React.createElement(n.c,{className:"",title:__("Upgrade to a paid plan to use this feature.","jetpack-forms"),buttonText:__("Upgrade","jetpack-forms"),checkoutUrl:t,isRedirecting:o,goToCheckoutPage:a}))}},1787:(e,t,a)=>{"use strict";a.d(t,{U:()=>n});var r=a(6087);const n=e=>{const t={"--jetpack--contact-form--border-color":e.borderColor,"--jetpack--contact-form--border-radius":(0,r.isNumber)(e.borderRadius)?`${e.borderRadius}px`:null,"--jetpack--contact-form--border-size":(0,r.isNumber)(e.borderWidth)?`${e.borderWidth}px`:null,"--jetpack--contact-form--input-background":e.fieldBackgroundColor,"--jetpack--contact-form--font-size":e.fieldFontSize,"--jetpack--contact-form--line-height":e.lineHeight,"--jetpack--contact-form--text-color":e.inputColor,"--jetpack--contact-form--button-outline--text-color":e.inputColor,"--jetpack--contact-form--button-outline--background-color":e.buttonBackgroundColor,"--jetpack--contact-form--button-outline--border-radius":(0,r.isNumber)(e.buttonBorderRadius)?`${e.buttonBorderRadius}px`:null,"--jetpack--contact-form--button-outline--border-size":(0,r.isNumber)(e.buttonBorderWidth)?`${e.buttonBorderWidth}px`:null},a={color:e.labelColor,lineHeight:e.labelLineHeight,"--jetpack--contact-form--label--font-size":e.labelFontSize},n={backgroundColor:e.fieldBackgroundColor,borderColor:e.borderColor,borderRadius:(0,r.isNumber)(e.borderRadius)?e.borderRadius:null,borderWidth:(0,r.isNumber)(e.borderWidth)?e.borderWidth:null,color:e.inputColor,fontSize:e.fieldFontSize,lineHeight:e.lineHeight};return{blockStyle:t,fieldStyle:n,labelStyle:a,optionStyle:{color:n.color,fontSize:n.fontSize,lineHeight:n.lineHeight}}}},4351:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(4715),n=a(4997),o=a(7723),c=a(6087),l=a(8575);const __=o.__,s=["submit_button_text","has_form_settings_set","submitButtonText","backgroundButtonColor","textButtonColor","customBackgroundButtonColor","customTextButtonColor","submitButtonClasses","hasFormSettingsSet"],i=[{attributes:{...l.A},supports:{html:!1},save:()=>React.createElement(r.InnerBlocks.Content,null)},{attributes:{submit_button_text:{type:"string",default:__("Submit","jetpack-forms")},has_form_settings_set:{type:"string",default:null},submitButtonText:{type:"string",default:__("Submit","jetpack-forms")},backgroundButtonColor:{type:"string"},textButtonColor:{type:"string"},customBackgroundButtonColor:{type:"string"},customTextButtonColor:{type:"string"},submitButtonClasses:{type:"string"},...l.A},migrate:(e,t)=>{const a=(0,c.omit)(e,s),r={text:e.submitButtonText||e.submit_button_text||__("Submit","jetpack-forms"),backgroundColor:e.backgroundButtonColor,textColor:e.textButtonColor,customBackgroundColor:e.customBackgroundButtonColor,customTextColor:e.customTextButtonColor};return[a,t.concat((0,n.createBlock)("jetpack/button",{element:"button",...r}))]},isEligible:e=>!(!e.has_form_settings_set&&!e.hasFormSettingsSet),save:()=>React.createElement(r.InnerBlocks.Content,null)}]},30:(e,t,a)=>{"use strict";a.d(t,{A:()=>A});var r=a(723),n=a(5985),o=a(4715),c=a(6427),l=a(9491),s=a(3582),i=a(7143),m=a(3656),d=a(8468),p=a(7723),u=a(3022),f=a(6087),h=a(1013),g=a(599),k=a(3373),b=a(1465),v=a(7309),E=a(9415),w=a(1646),R=a(9364),C=a(7268);a(2962);const __=p.__,j=(0,f.filter)(h.P,(({settings:e})=>!e.parent||"jetpack/contact-form"===e.parent||(0,f.isArray)(e.parent)&&e.parent.includes("jetpack/contact-form"))),x=[...(0,f.map)(j,(e=>`jetpack/${e.name}`)),"core/audio","core/columns","core/group","core/heading","core/html","core/image","core/list","core/paragraph","core/row","core/separator","core/spacer","core/stack","core/subhead","core/video"],y=[...(0,f.map)(j,(e=>`jetpack/${e.name}`))];const A=function e({name:t,attributes:a,setAttributes:p,clientId:f,className:h}){const{to:j,subject:A,customThankyou:_,customThankyouHeading:B,customThankyouMessage:S,customThankyouRedirect:M,salesforceData:I,formTitle:N}=a,L=(0,l.useInstanceId)(e),{postTitle:P,canUserInstallPlugins:T,hasInnerBlocks:F,postAuthorEmail:H}=(0,i.useSelect)((e=>{const{getBlocks:t}=e(o.store),{getEditedPostAttribute:a}=e(m.store),{getUser:r,canUser:n}=e(s.store),c=t(f),l=a("title"),i=a("author"),d=i&&r(i)?.email,p=c.find((e=>"jetpack/button"===e.name));if(p&&!p.attributes.lock){const e={move:!1,remove:!0};p.attributes.lock=e}return{postTitle:l,canUserInstallPlugins:n("create","plugins"),hasInnerBlocks:c.length>0,postAuthorEmail:d}}),[f]),V=(0,d.useRef)(),$=(0,d.useRef)(),O=(0,o.useBlockProps)({ref:V}),z=(0,u.A)(h,"jetpack-contact-form"),q=(0,o.useInnerBlocksProps)({ref:$,className:z,style:window.jetpackForms.generateStyleVariables($.current)},{allowedBlocks:x,prioritizedInserterBlocks:y,templateInsertUpdatesSelection:!1}),{isLoadingModules:D,isChangingStatus:U,isModuleActive:G,changeStatus:Z}=(0,n.q7)("contact-form"),W=!!window?.Jetpack_Editor_Initial_State?.available_blocks["contact-form/salesforce-lead-form"];let J;return J=G?F?React.createElement(React.Fragment,null,React.createElement(o.InspectorControls,null,React.createElement(c.PanelBody,{title:__("Manage responses","jetpack-forms"),className:"jetpack-contact-form__manage-responses-panel",initialOpen:!1},React.createElement(w.A,{setAttributes:p})),React.createElement(c.PanelBody,{title:__("Action after submit","jetpack-forms"),initialOpen:!1},React.createElement(g.A,null,__("Customize the view after form submission:","jetpack-forms")),React.createElement(c.SelectControl,{label:__("On Submission","jetpack-forms"),value:_,options:[{label:__("Show a summary of submitted fields","jetpack-forms"),value:""},{label:__("Show a custom text message","jetpack-forms"),value:"message"},{label:__("Redirect to another webpage","jetpack-forms"),value:"redirect"}],onChange:e=>p({customThankyou:e}),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),"redirect"!==_&&React.createElement(c.TextControl,{label:__("Message Heading","jetpack-forms"),value:B,placeholder:__("Your message has been sent","jetpack-forms"),onChange:e=>p({customThankyouHeading:e}),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),"message"===_&&React.createElement(c.TextareaControl,{label:__("Message Text","jetpack-forms"),value:S,placeholder:__("Thank you for your submission!","jetpack-forms"),onChange:e=>p({customThankyouMessage:e}),__nextHasNoMarginBottom:!0}),"redirect"===_&&React.createElement("div",null,React.createElement(o.URLInput,{label:__("Redirect Address","jetpack-forms"),value:M,className:"jetpack-contact-form__thankyou-redirect-url",onChange:e=>p({customThankyouRedirect:e})}))),React.createElement(c.PanelBody,{title:__("Email connection","jetpack-forms"),initialOpen:!1},React.createElement(v.A,{emailAddress:j,emailSubject:A,instanceId:L,postAuthorEmail:H,setAttributes:p})),!(0,n.Sy)()&&T&&React.createElement(E.A,{attributes:a,setAttributes:p}),W&&I?.sendToSalesforce&&React.createElement(R.A,{salesforceData:I,setAttributes:p,instanceId:L})),React.createElement(o.InspectorAdvancedControls,null,React.createElement(c.TextControl,{label:__("Accessible name","jetpack-forms"),value:N,placeholder:P,onChange:e=>p({formTitle:e}),help:__("Add an accessible name to help people using assistive technology identify the form. Defaults to page or post title.","jetpack-forms"),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),React.createElement(c.ExternalLink,{href:"https://developer.mozilla.org/docs/Glossary/Accessible_name"},__("Read more.","jetpack-forms"))),React.createElement("div",q)):React.createElement(C.A,{blockName:t,setAttributes:p,clientId:f,classNames:z}):D?React.createElement(b.A,null):React.createElement(k.o,{changeStatus:Z,isModuleActive:G,isLoading:U}),React.createElement(r.Ay,{targetDom:V.current},React.createElement("div",O,J))}},5620:(e,t,a)=>{"use strict";a.d(t,{U:()=>u,W:()=>h});var r=a(4715),n=a(6427),o=a(7723),c=a(8575),l=a(4351),s=a(30),i=a(6779),m=a(9787),d=a(1097),p=a(8152);const __=o.__,_x=o._x,u="contact-form",f=(0,d.A)(React.createElement(React.Fragment,null,React.createElement(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M18 9H13V7.5H18V9Z"}),React.createElement(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M18 16.5H13V15H18V16.5Z"}),React.createElement(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M9.5 7.5H7.5V9.5H9.5V7.5ZM7.5 6H9.5C10.3284 6 11 6.67157 11 7.5V9.5C11 10.3284 10.3284 11 9.5 11H7.5C6.67157 11 6 10.3284 6 9.5V7.5C6 6.67157 6.67157 6 7.5 6Z"}),React.createElement(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M9.5 14.5H7.5V16.5H9.5V14.5ZM7.5 13H9.5C10.3284 13 11 13.6716 11 14.5V16.5C11 17.3284 10.3284 18 9.5 18H7.5C6.67157 18 6 17.3284 6 16.5V14.5C6 13.6716 6.67157 13 7.5 13Z"}),React.createElement(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M19 4.5H5C4.72386 4.5 4.5 4.72386 4.5 5V19C4.5 19.2761 4.72386 19.5 5 19.5H19C19.2761 19.5 19.5 19.2761 19.5 19V5C19.5 4.72386 19.2761 4.5 19 4.5ZM5 3C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3H5Z"}))),h={apiVersion:3,title:__("Form","jetpack-forms"),description:__("Create forms to collect data from site visitors and manage their responses.","jetpack-forms"),icon:{src:f,foreground:(0,m.V)()},keywords:[_x("email","block search term","jetpack-forms"),_x("feedback","block search term","jetpack-forms"),_x("contact form","block search term","jetpack-forms")],supports:{color:{link:!0,gradients:!0},html:!1,spacing:{padding:!0,margin:!0},align:["wide","full"]},attributes:c.A,edit:s.A,save:()=>{const e=r.useBlockProps.save();return React.createElement("div",e,React.createElement(r.InnerBlocks.Content,null))},example:{innerBlocks:[{name:"jetpack/field-name",attributes:{required:!0,label:__("Name","jetpack-forms")}},{name:"jetpack/field-email",attributes:{required:!0,label:__("Email","jetpack-forms")}},{name:"jetpack/field-textarea",attributes:{label:__("Message","jetpack-forms")}},{name:"jetpack/button",attributes:{text:__("Contact Us","jetpack-forms"),element:"button",lock:{remove:!0}}}]},styles:[{name:"default",label:__("Default","jetpack-forms"),isDefault:!0},{name:"animated",label:__("Animated","jetpack-forms")},{name:"outlined",label:__("Outlined","jetpack-forms")}],variations:p.A,category:"contact-form",transforms:i.A,deprecated:l.A}},6779:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(4997),n=a(7723),o=a(6087);const __=n.__,c=(e,t,a)=>{const r=a.match(new RegExp(`\\[${e}[^\\]]* ${t}="([^"]*)"`,"im"));if(r&&r.length)return r[1];const n=a.match(new RegExp(`\\[${e}[^\\]]* ${t}='([^']*)'`,"im"));if(n&&n.length)return n[1];const o=a.match(new RegExp(`\\[${e}[^\\]]* ${t}=([^\\s]*)\\s`,"im"));return!(!o||!o.length)&&o[1]},l={root:{},innerBlocks:[]},s={from:[{type:"raw",priority:1,isMatch:e=>!("P"!==e.nodeName||!(/\[contact-form(\s.*?)?\](?:([^\[]+)?)?/g.test(e.textContent)||/\[contact-field(\s.*?)?\](?:([^\[]+)?)?/g.test(e.textContent)||/\[\/contact-form]/g.test(e.textContent))),transform:e=>{const t=e.textContent.replace("<br>","");if(t.includes("[contact-form")&&(l.root={},l.innerBlocks=[],l.root=(e=>{const t={to:c("contact-form","to",e),subject:c("contact-form","subject",e),submitButtonText:c("contact-form","submit_button_text",e)};return{blockName:"jetpack/contact-form",attrs:(0,o.pickBy)(t,o.identity)}})(t)),t.includes("[contact-field")){const e=t.match(/(\[contact-field[\s\S]*?\/?])/g);e&&e.length>0&&e.forEach((e=>{l.innerBlocks.push((e=>{const t={label:c("contact-field","label",e),placeholder:c("contact-field","placeholder",e),required:c("contact-field","required",e),options:c("contact-field","options",e)},a=(e=>{const t="jetpack",a={text:`${t}/field-text`,url:`${t}/field-text`,textarea:`${t}/field-textarea`,radio:`${t}/field-radio`,checkbox:`${t}/field-checkbox`,"checkbox-multiple":`${t}/field-checkbox-multiple`,select:`${t}/field-select`,email:`${t}/field-email`,name:`${t}/field-name`,number:`${t}/field-number`,default:`${t}/field-text`};return a[e]?a[e]:a.default})(c("contact-field","type",e));return t.options&&(t.options=t.options.split(",")),(0,r.createBlock)(a,(0,o.pickBy)(t,o.identity))})(e))}))}if(t.includes("[/contact-form]")){l.innerBlocks.push((0,r.createBlock)("jetpack/button",{element:"button",text:l.root.attrs.submitButtonText||__("Contact Us","jetpack-forms")}));return(0,r.createBlock)(l.root.blockName,l.root.attrs,l.innerBlocks)}return!1}}],to:[{type:"block",blocks:["jetpack/subscriptions"],transform:()=>(0,r.createBlock)("jetpack/subscriptions")}]}},9787:(e,t,a)=>{"use strict";a.d(t,{V:()=>c});var r=a(8377),n=a(5985);const o=r.T["Jetpack Green 40"];function c(){return(0,n.d9)()||(0,n.Sy)()?null:o}},8882:(e,t,a)=>{"use strict";a.d(t,{B:()=>n});var r=a(4997);function n(){return(0,r.hasBlockSupport)("core/paragraph","splitting",!1)}},2386:(e,t,a)=>{"use strict";a.d(t,{J:()=>r});const r=e=>{const t=e.ownerDocument.defaultView.getSelection();if(0===t.rangeCount)return 0;const a=t.getRangeAt(0),r=a.cloneRange();return r.selectNodeContents(e),r.setEnd(a.endContainer,a.endOffset),r.toString().length}},2453:(e,t,a)=>{"use strict";a.d(t,{Z:()=>n});var r=a(6087);const n=(e,t,a,n)=>setTimeout((()=>{(0,r.tap)(e.querySelectorAll(t)[a],(e=>{if(e&&(e.focus(),document.createRange&&n)){const t=document.createRange();t.selectNodeContents(e),t.collapse(!1);const a=document.defaultView.getSelection();a.removeAllRanges(),a.addRange(t)}}))}),0)},2962:()=>{window.jetpackForms=window.jetpackForms||{},window.jetpackForms.getBackgroundColor=function(e){let t=window.getComputedStyle(e).backgroundColor;for(;"rgba(0, 0, 0, 0)"===t&&e.parentNode&&e.parentNode.nodeType===window.Node.ELEMENT_NODE;)if("wp-block-cover"!==(e=e.parentNode).className)t=window.getComputedStyle(e).backgroundColor;else{const a=e.querySelector(".wp-block-cover__background");t=window.getComputedStyle(a).backgroundColor}return t},window.jetpackForms.generateStyleVariables=function(e){if(!e)return;const t=window["editor-canvas"]?window["editor-canvas"].document:document,a=t.querySelector("body"),r=t.createElement("div");r.className="contact-form__style-probe",r.style="position: absolute; z-index: -1; width: 1px; height: 1px; visibility: hidden",r.innerHTML='\n\t\t\t<div class="contact-form">\n\t\t\t\t<div class="wp-block-button">\n\t\t\t\t\t<div class="wp-block-button__link btn-primary">Test</div>\n\t\t\t\t</div>\n\t\t\t\t<div class="wp-block-button is-style-outline">\n\t\t\t\t\t<div class="wp-block-button__link btn-outline">Test</div>\n\t\t\t\t</div>\n\t\t\t\t<div class="jetpack-field">\n\t\t\t\t\t<input class="jetpack-field__input" type="text">\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t',e.parentNode.appendChild(r);const n=r.querySelector(".btn-primary"),o=r.querySelector(".btn-outline"),c=r.querySelector('input[type="text"]'),l=window.jetpackForms.getBackgroundColor(a),s=window.jetpackForms.getBackgroundColor(c),i=window.getComputedStyle(c).backgroundColor,{border:m,borderColor:d,backgroundColor:p,color:u}=window.getComputedStyle(n),{backgroundColor:f,border:h,borderWidth:g,borderRadius:k,color:b,padding:v,lineHeight:E}=window.getComputedStyle(o),w=window.jetpackForms.getBackgroundColor(o),{color:R,padding:C,paddingTop:j,paddingLeft:x,border:y,borderColor:A,borderWidth:_,borderStyle:B,borderRadius:S,fontSize:M,fontFamily:I,lineHeight:N}=window.getComputedStyle(c);return r.remove(),{"--jetpack--contact-form--primary-color":p,"--jetpack--contact-form--background-color":l,"--jetpack--contact-form--text-color":R,"--jetpack--contact-form--border":y,"--jetpack--contact-form--border-color":A,"--jetpack--contact-form--border-size":_,"--jetpack--contact-form--border-style":B,"--jetpack--contact-form--border-radius":S,"--jetpack--contact-form--input-background":i,"--jetpack--contact-form--input-background-fallback":s,"--jetpack--contact-form--input-padding":C,"--jetpack--contact-form--input-padding-top":j,"--jetpack--contact-form--input-padding-left":x,"--jetpack--contact-form--font-size":M,"--jetpack--contact-form--font-family":I,"--jetpack--contact-form--line-height":N,"--jetpack--contact-form--button-primary--color":u,"--jetpack--contact-form--button-primary--background-color":p,"--jetpack--contact-form--button-primary--border":m,"--jetpack--contact-form--button-primary--border-color":d,"--jetpack--contact-form--button-outline--padding":v,"--jetpack--contact-form--button-outline--border":h,"--jetpack--contact-form--button-outline--background-color":f,"--jetpack--contact-form--button-outline--background-color-fallback":w,"--jetpack--contact-form--button-outline--border-size":g,"--jetpack--contact-form--button-outline--border-radius":k,"--jetpack--contact-form--button-outline--text-color":b,"--jetpack--contact-form--button-outline--line-height":E}}},1663:(e,t,a)=>{"use strict";a.d(t,{BV:()=>p,DR:()=>m,pC:()=>i,zD:()=>d});var r=a(4715),n=a(4997),o=a(7143),c=a(8468),l=a(7723);const __=l.__,s="jetpack/contact-form",i={ANIMATED:"animated",BELOW:"below",DEFAULT:"default",OUTLINED:"outlined"},m=({attributes:e,clientId:t,name:a})=>{const l={text:__("Submit","jetpack-forms"),element:"button",lock:{remove:!0}},{replaceBlock:i}=(0,o.useDispatch)(r.store),m=(0,o.useSelect)((e=>e(r.store).getBlockParentsByBlockName(t,s)));(0,c.useEffect)((()=>{m?.length||i(t,(0,n.createBlock)(s,{},[(0,n.createBlock)(a,e),(0,n.createBlock)("jetpack/button",l)]))}),[])},d=e=>{const t=e&&e.match(/is-style-([^\s]+)/i);return t?t[1]:""},p=e=>{const t=(0,o.useSelect)((t=>{const[a]=t(r.store).getBlockParentsByBlockName(e,s);return t(r.store).getBlockAttributes(a)}));return d(t?.className)||i.DEFAULT}},9771:(e,t,a)=>{"use strict";function r(e,t){return{...e,...t,supports:{...e.supports,...t.supports},attributes:{...e.attributes,...t.attributes}}}a.d(t,{A:()=>r})},1620:(e,t,a)=>{"use strict";a.d(t,{Bf:()=>l,pj:()=>c});var r=a(5985),n=a(1455),o=a.n(n);async function c(e){if((0,r.Sy)())return Promise.reject();try{return await o()({path:"/jetpack/v4/plugins",method:"POST",data:{slug:e,status:"active",source:"block-editor"}})}catch(e){return Promise.reject(e.message)}}async function l(e){if((0,r.Sy)())return Promise.reject();try{return await o()({path:`/jetpack/v4/plugins/${e}`,method:"POST",data:{status:"active",source:"block-editor"}})}catch(e){return Promise.reject(e.message)}}},2399:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(5985),n=a(4997),o=a(2619);function c(e,t,a=[],c=!0){const{available:l,details:s,unavailableReason:i}=(0,r.FB)(e),m=(0,r.$i)(),d="beta"===m?.blocks_variation,p=(0,r.aq)(i,s),u=c?"jetpack/":"";if(!l&&!p)return!1;const f=(0,n.registerBlockType)(u+e,t);return p&&(0,o.addFilter)("editor.BlockListBlock",`${u+e}-with-has-warning-is-interactive-class-names`,(0,r.BT)(u+e)),a.forEach((e=>{e.settings?.isBeta&&!d||(0,n.registerBlockType)(u+e.name,e.settings)})),f}},1097:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(6427);const n=(e,t=24,a=24,n="0 0 24 24")=>React.createElement(r.SVG,{xmlns:"http://www.w3.org/2000/svg",width:t,height:a,viewBox:n},e)},2233:(e,t,a)=>{"use strict";a.d(t,{F:()=>o});var r=a(7143),n=a(6087);const o=e=>(0,r.useSelect)((t=>{const a=t("core/block-editor");return a.getBlockAttributes((0,n.first)(a.getBlockParents(e,!0)))}))},8890:(e,t,a)=>{"use strict";a.d(t,{H:()=>s});var r=a(6072),n=a.n(r),o=a(7143),c=a(8468),l=a(6087);const s=e=>t=>({attributes:a,clientId:r,setAttributes:s,...i})=>{const m=(({attributes:e,clientId:t,setAttributes:a,sharedAttributes:r})=>{const n=(0,o.useRegistry)(),{updateBlockAttributes:s,__unstableMarkNextChangeAsNotPersistent:i}=(0,o.useDispatch)("core/block-editor"),{getBlockParentsByBlockName:m,getClientIdsOfDescendants:d,getBlocksByClientId:p}=(0,o.useSelect)("core/block-editor"),u=(0,c.useCallback)((()=>{const e=(0,l.first)(m(t,"jetpack/contact-form"));if(!e)return[];const a=d(e);return p(a).filter((e=>e?.name?.includes("jetpack/field")&&e?.attributes?.shareFieldAttributes&&e?.clientId!==t))}),[t,m,d,p]);return(0,c.useEffect)((()=>{const a=u();if(!(0,l.isEmpty)(a)&&e.shareFieldAttributes){const e=(0,l.pick)((0,l.first)(a).attributes,r);n.batch((()=>{i(),s([t],e)}))}}),[]),(0,c.useCallback)((o=>{const c=u();let i=[],m={};e.shareFieldAttributes&&(0,l.isNil)(o.shareFieldAttributes)?(i=(0,l.map)(c,(e=>e.clientId)),m=(0,l.pick)(o,r)):o.shareFieldAttributes&&!(0,l.isEmpty)(c)&&(i=[t],m=(0,l.pick)((0,l.first)(c).attributes,r)),n.batch((()=>{(0,l.isEmpty)(i)||(0,l.isEmpty)(m)||s(i,m),a(o)}))}),[e,t,u,n,a,r,s])})({attributes:a,clientId:r,setAttributes:s,sharedAttributes:e});return React.createElement(t,n()({attributes:a,clientId:r,setAttributes:m},i))}},7268:(e,t,a)=>{"use strict";a.d(t,{A:()=>p});var r=a(4715),n=a(4997),o=a(6427),c=a(7143),l=a(8468),s=a(7723),i=a(3022),m=a(6087);a(2962);const __=s.__,d=e=>(0,m.map)(e,(([e,t,a=[]])=>(0,n.createBlock)(e,t,d(a))));function p({blockName:e,setAttributes:t,clientId:a,classNames:s}){const[p,u]=(0,l.useState)(!1),{replaceInnerBlocks:f,selectBlock:h}=(0,c.useDispatch)(r.store),{blockType:g,defaultVariation:k,variations:b}=(0,c.useSelect)((t=>{const{getBlockType:a,getBlockVariations:r,getDefaultBlockVariation:o}=t(n.store);return{blockType:a(e),defaultVariation:o(e,"block"),variations:r(e,"block")}}),[e]);return(0,l.useEffect)((()=>{p||-1===window.location.search.indexOf("showJetpackFormsPatterns")||u(!0)}),[]),React.createElement("div",{className:(0,i.A)(s,"is-placeholder")},React.createElement(r.__experimentalBlockVariationPicker,{icon:(0,m.get)(g,["icon","src"]),label:(0,m.get)(g,["title"]),instructions:__("Start by selecting one of these templates, or browse patterns.","jetpack-forms"),variations:(0,m.filter)(b,(e=>!e.hiddenFromPicker)),onSelect:(e=k)=>{e.attributes&&t(e.attributes),e.innerBlocks&&f(a,d(e.innerBlocks)),h(a)}}),React.createElement("div",{className:"form-placeholder__footer"},React.createElement(o.Button,{variant:"secondary",onClick:()=>u(!0)},__("Browse form patterns","jetpack-forms"))),p&&React.createElement(o.Modal,{className:"form-placeholder__patterns-modal",title:__("Choose a pattern","jetpack-forms"),closeLabel:__("Cancel","jetpack-forms"),onRequestClose:()=>u(!1)},React.createElement(r.__experimentalBlockPatternSetup,{initialViewMode:"grid",filterPatternsFn:e=>-1!==e.content.indexOf("jetpack/contact-form"),clientId:a})))}},8152:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var r=a(5985),n=a(6427),o=a(7723),c=a(3407),l=a(6087),s=a(9364),i=a(9787),m=a(1097);const __=o.__,_x=o._x,d=(0,l.compact)([{name:"contact-form",title:__("Contact Form","jetpack-forms"),description:__("Add a contact form to your page.","jetpack-forms"),icon:{foreground:(0,i.V)(),src:(0,m.A)(React.createElement(React.Fragment,null,React.createElement(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 5.3203L6.6477 9L12 12.6797L17.3523 9L12 5.3203ZM12 3.5L4 9L12 14.5L20 9L12 3.5Z"}),React.createElement(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M4 18V9H5.5V18C5.5 18.4142 5.83579 18.75 6.25 18.75H17.75C18.1642 18.75 18.5 18.4142 18.5 18V9H20V18C20 19.2426 18.9926 20.25 17.75 20.25H6.25C5.00736 20.25 4 19.2426 4 18Z"})))},innerBlocks:[["jetpack/field-name",{required:!0,label:__("Name","jetpack-forms")}],["jetpack/field-email",{required:!0,label:__("Email","jetpack-forms")}],["jetpack/field-textarea",{label:__("Message","jetpack-forms")}],["jetpack/button",{text:__("Contact Us","jetpack-forms"),element:"button",lock:{remove:!0}}]],attributes:{}},{name:"rsvp-form",title:__("RSVP Form","jetpack-forms"),description:__("Add an RSVP form to your page","jetpack-forms"),icon:{foreground:(0,i.V)(),src:(0,m.A)(React.createElement(React.Fragment,null,React.createElement(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M7.87868 15.5L5.5 17.8787L5.5 6C5.5 5.72386 5.72386 5.5 6 5.5L18 5.5C18.2761 5.5 18.5 5.72386 18.5 6L18.5 15C18.5 15.2761 18.2761 15.5 18 15.5L7.87868 15.5ZM8.5 17L18 17C19.1046 17 20 16.1046 20 15L20 6C20 4.89543 19.1046 4 18 4L6 4C4.89543 4 4 4.89543 4 6L4 18.9393C4 19.5251 4.47487 20 5.06066 20C5.34196 20 5.61175 19.8883 5.81066 19.6893L8.5 17Z"}),React.createElement(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M15.6087 7.93847L11.4826 13.6692L8.45898 10.5196L9.54107 9.48084L11.3175 11.3313L14.3914 7.06201L15.6087 7.93847Z"})))},innerBlocks:[["jetpack/field-name",{required:!0,label:__("Name","jetpack-forms")}],["jetpack/field-email",{required:!0,label:__("Email","jetpack-forms")}],["jetpack/field-radio",{label:__("Attending?","jetpack-forms"),required:!0,options:[__("Yes","jetpack-forms"),__("No","jetpack-forms")]}],["jetpack/field-textarea",{label:__("Other Details","jetpack-forms")}],["jetpack/button",{text:__("Send RSVP","jetpack-forms"),element:"button",lock:{remove:!0}}]],attributes:{subject:__("A new RSVP from your website","jetpack-forms")},example:{innerBlocks:[{name:"jetpack/field-name",attributes:{required:!0,label:__("Name","jetpack-forms")}},{name:"jetpack/field-email",attributes:{required:!0,label:__("Email","jetpack-forms")}},{name:"jetpack/field-radio",attributes:{label:__("Attending?","jetpack-forms"),required:!0,options:[__("Yes","jetpack-forms"),__("No","jetpack-forms")]}},{name:"jetpack/field-textarea",attributes:{label:__("Other Details","jetpack-forms")}},{name:"jetpack/button",attributes:{text:__("Send RSVP","jetpack-forms"),element:"button",lock:{remove:!0}}}]}},{name:"registration-form",title:__("Registration Form","jetpack-forms"),description:__("Add a Registration form to your page","jetpack-forms"),icon:{foreground:(0,i.V)(),src:(0,m.A)(React.createElement(React.Fragment,null,React.createElement(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M18.5 5.5V8H20V5.5H22.5V4H20V1.5H18.5V4H16V5.5H18.5ZM12 4H6C4.89543 4 4 4.89543 4 6V18C4 19.1046 4.89543 20 6 20H18C19.1046 20 20 19.1046 20 18V12H18.5V18C18.5 18.2761 18.2761 18.5 18 18.5H6C5.72386 18.5 5.5 18.2761 5.5 18V6C5.5 5.72386 5.72386 5.5 6 5.5H12V4Z"}),React.createElement(n.Path,{d:"M16.75 17.5V15.5C16.75 13.9812 15.5188 12.75 14 12.75H10C8.48122 12.75 7.25 13.9812 7.25 15.5V17.5H8.75V15.5C8.75 14.8096 9.30964 14.25 10 14.25H14C14.6904 14.25 15.25 14.8096 15.25 15.5V17.5H16.75Z"}),React.createElement(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M14.5 9C14.5 10.3807 13.3807 11.5 12 11.5C10.6193 11.5 9.5 10.3807 9.5 9C9.5 7.61929 10.6193 6.5 12 6.5C13.3807 6.5 14.5 7.61929 14.5 9ZM13 9C13 9.55228 12.5523 10 12 10C11.4477 10 11 9.55228 11 9C11 8.44772 11.4477 8 12 8C12.5523 8 13 8.44772 13 9Z"})))},innerBlocks:[["jetpack/field-name",{required:!0,label:__("Name","jetpack-forms")}],["jetpack/field-email",{required:!0,label:__("Email","jetpack-forms")}],["jetpack/field-telephone",{label:__("Phone","jetpack-forms")}],["jetpack/field-select",{label:__("How did you hear about us?","jetpack-forms"),options:[__("Search Engine","jetpack-forms"),__("Social Media","jetpack-forms"),__("TV","jetpack-forms"),__("Radio","jetpack-forms"),__("Friend or Family","jetpack-forms")]}],["jetpack/field-textarea",{label:__("Other Details","jetpack-forms")}],["jetpack/button",{text:__("Send","jetpack-forms"),element:"button",lock:{remove:!0}}]],attributes:{subject:__("A new registration from your website","jetpack-forms")},example:{innerBlocks:[{name:"jetpack/field-name",attributes:{required:!0,label:__("Name","jetpack-forms")}},{name:"jetpack/field-email",attributes:{required:!0,label:__("Email","jetpack-forms")}},{name:"jetpack/field-telephone",attributes:{required:!0,label:__("Phone","jetpack-forms")}},{name:"jetpack/field-select",attributes:{label:__("How did you hear about us?","jetpack-forms"),options:[__("Search Engine","jetpack-forms"),__("Social Media","jetpack-forms"),__("TV","jetpack-forms"),__("Radio","jetpack-forms"),__("Friend or Family","jetpack-forms")]}},{name:"jetpack/field-textarea",attributes:{label:__("Other Details","jetpack-forms")}},{name:"jetpack/button",attributes:{text:__("Send","jetpack-forms"),element:"button",lock:{remove:!0}}}]}},{name:"appointment-form",title:__("Appointment Form","jetpack-forms"),description:__("Add an Appointment booking form to your page","jetpack-forms"),icon:{foreground:(0,i.V)(),src:(0,m.A)(React.createElement(React.Fragment,null,React.createElement(n.Path,{d:"M4 6C4 4.89543 4.89543 4 6 4H18C19.1046 4 20 4.89543 20 6V8H4V6Z"}),React.createElement(n.Path,{d:"M7 9.25H11V13.25H7V9.25Z"}),React.createElement(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M6 5.5H18C18.2761 5.5 18.5 5.72386 18.5 6V12H20V6C20 4.89543 19.1046 4 18 4H6C4.89543 4 4 4.89543 4 6V18C4 19.1046 4.89543 20 6 20H12V18.5H6C5.72386 18.5 5.5 18.2761 5.5 18V6C5.5 5.72386 5.72386 5.5 6 5.5Z"}),React.createElement(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M17.25 21V15H18.75V21H17.25Z"}),React.createElement(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M15 17.25L21 17.25L21 18.75L15 18.75L15 17.25Z"})))},innerBlocks:[["jetpack/field-name",{required:!0,label:__("Name","jetpack-forms")}],["jetpack/field-email",{required:!0,label:__("Email","jetpack-forms")}],["jetpack/field-telephone",{required:!0,label:__("Phone","jetpack-forms")}],["jetpack/field-date",{label:__("Date","jetpack-forms"),required:!0}],["jetpack/field-radio",{label:__("Time","jetpack-forms"),required:!0,options:[__("Morning","jetpack-forms"),__("Afternoon","jetpack-forms")]}],["jetpack/field-textarea",{label:__("Notes","jetpack-forms")}],["jetpack/button",{text:__("Book Appointment","jetpack-forms"),element:"button",lock:{remove:!0}}]],attributes:{subject:__("A new appointment booked from your website","jetpack-forms")},example:{innerBlocks:[{name:"jetpack/field-name",attributes:{required:!0,label:__("Name","jetpack-forms")}},{name:"jetpack/field-email",attributes:{required:!0,label:__("Email","jetpack-forms")}},{name:"jetpack/field-telephone",attributes:{required:!0,label:__("Phone","jetpack-forms")}},{name:"jetpack/field-date",attributes:{required:!0,label:__("Date","jetpack-forms")}},{name:"jetpack/field-radio",attributes:{label:__("Time","jetpack-forms"),required:!0,options:[__("Morning","jetpack-forms"),__("Afternoon","jetpack-forms")]}},{name:"jetpack/field-textarea",attributes:{label:__("Notes","jetpack-forms")}},{name:"jetpack/button",attributes:{text:__("Book Appointment","jetpack-forms"),element:"button",lock:{remove:!0}}}]}},{name:"feedback-form",title:__("Feedback Form","jetpack-forms"),description:__("Add a Feedback form to your page","jetpack-forms"),icon:{foreground:(0,i.V)(),src:(0,m.A)(React.createElement(React.Fragment,null,React.createElement(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 18.5C15.5899 18.5 18.5 15.5899 18.5 12C18.5 8.41015 15.5899 5.5 12 5.5C8.41015 5.5 5.5 8.41015 5.5 12C5.5 15.5899 8.41015 18.5 12 18.5ZM12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20Z"}),React.createElement(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M9.5 11C10.3284 11 11 10.3284 11 9.5C11 8.67157 10.3284 8 9.5 8C8.67157 8 8 8.67157 8 9.5C8 10.3284 8.67157 11 9.5 11Z"}),React.createElement(n.Path,{d:"M16 9.5C16 10.3284 15.3284 11 14.5 11C13.6716 11 13 10.3284 13 9.5C13 8.67157 13.6716 8 14.5 8C15.3284 8 16 8.67157 16 9.5Z"}),React.createElement(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M14.5 11C15.3284 11 16 10.3284 16 9.5C16 8.67157 15.3284 8 14.5 8C13.6716 8 13 8.67157 13 9.5C13 10.3284 13.6716 11 14.5 11Z"}),React.createElement(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M8.16492 14.6566L7.41431 13.7183L8.58561 12.7812L9.33622 13.7195C9.98358 14.5287 10.9637 14.9998 12 14.9998C13.0362 14.9998 14.0163 14.5287 14.6637 13.7195L15.4143 12.7812L16.5856 13.7183L15.835 14.6566C14.903 15.8216 13.4919 16.4998 12 16.4998C10.508 16.4998 9.09693 15.8216 8.16492 14.6566Z"})))},innerBlocks:[["jetpack/field-name",{required:!0,label:__("Name","jetpack-forms")}],["jetpack/field-email",{required:!0,label:__("Email","jetpack-forms")}],["jetpack/field-radio",{label:__("Please rate our website","jetpack-forms"),required:!0,options:[__("1 - Very Bad","jetpack-forms"),__("2 - Poor","jetpack-forms"),__("3 - Average","jetpack-forms"),__("4 - Good","jetpack-forms"),__("5 - Excellent","jetpack-forms")]}],["jetpack/field-textarea",{label:__("How could we improve?","jetpack-forms")}],["jetpack/button",{text:__("Send Feedback","jetpack-forms"),element:"button",lock:{remove:!0}}]],attributes:{subject:__("New feedback received from your website","jetpack-forms")},example:{innerBlocks:[{name:"jetpack/field-name",attributes:{required:!0,label:__("Name","jetpack-forms")}},{name:"jetpack/field-email",attributes:{required:!0,label:__("Email","jetpack-forms")}},{name:"jetpack/field-radio",attributes:{label:__("Please rate our website","jetpack-forms"),required:!0,options:[__("1 - Very Bad","jetpack-forms"),__("2 - Poor","jetpack-forms"),__("3 - Average","jetpack-forms"),__("4 - Good","jetpack-forms"),__("5 - Excellent","jetpack-forms")]}},{name:"jetpack/field-textarea",attributes:{label:__("How could we improve?","jetpack-forms")}},{name:"jetpack/button",attributes:{text:__("Send Feedback","jetpack-forms"),element:"button",lock:{remove:!0}}}]}},!((0,r.d9)()||(0,r.Sy)())&&{name:"lead-capture-form",title:__("Lead capture","jetpack-forms"),description:__("A simple way to collect leads using forms on your site.","jetpack-forms"),keywords:[_x("subscribe","block search term","jetpack-forms"),_x("email","block search term","jetpack-forms"),_x("signup","block search term","jetpack-forms")],icon:{foreground:(0,i.V)(),src:c.A},innerBlocks:[["jetpack/field-name",{required:!0,label:__("Name","jetpack-forms")}],["jetpack/field-email",{required:!0,label:__("Email","jetpack-forms")}],["jetpack/field-consent",{}],["jetpack/button",{text:__("Subscribe","jetpack-forms"),element:"button",lock:{remove:!0}}]],attributes:{},example:{innerBlocks:[{name:"jetpack/field-name",attributes:{required:!0,label:__("Name","jetpack-forms")}},{name:"jetpack/field-email",attributes:{required:!0,label:__("Email","jetpack-forms")}},{name:"jetpack/field-consent",attributes:{}},{name:"jetpack/button",attributes:{text:__("Subscribe","jetpack-forms"),element:"button",lock:{remove:!0}}}]}},s.F])},2590:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(6072),n=a.n(r),o=a(5573);const c=e=>React.createElement(o.SVG,n()({width:"28",height:"28",viewBox:"0 0 46 46",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),React.createElement("rect",{width:"46",height:"46",rx:"23",fill:"#357b49"}),React.createElement("g",{clipPath:"url(#clip0_3308_44290)"},React.createElement("path",{d:"M24.4162 12.0335L31.0989 29.5939C31.6753 31.0964 32.5607 31.641 34.0748 31.7683C34.1301 31.7708 34.1843 31.7847 34.2341 31.8089C34.2839 31.8332 34.3281 31.8675 34.3641 31.9095C34.4001 31.9515 34.4271 32.0005 34.4435 32.0534C34.4598 32.1063 34.4651 32.162 34.459 32.217C34.4613 32.2738 34.4522 32.3305 34.4324 32.3837C34.4125 32.437 34.3823 32.4858 34.3435 32.5274C34.3046 32.5689 34.2579 32.6023 34.2061 32.6257C34.1542 32.6491 34.0982 32.6619 34.0414 32.6635H26.7218C26.6626 32.665 26.6037 32.6544 26.5488 32.6325C26.4938 32.6106 26.4438 32.5778 26.4019 32.536C26.3599 32.4943 26.3268 32.4445 26.3047 32.3897C26.2825 32.3349 26.2716 32.2761 26.2728 32.217C26.2684 32.1596 26.276 32.1019 26.2951 32.0476C26.3141 31.9933 26.3442 31.9435 26.3834 31.9014C26.4227 31.8593 26.4702 31.8257 26.523 31.8028C26.5758 31.7799 26.6328 31.7681 26.6904 31.7683C27.4255 31.7683 28.2588 31.5763 28.2588 30.6811C28.2523 30.3078 28.1758 29.939 28.0332 29.5939L26.4294 25.3389C26.3646 25.1469 26.3333 25.0822 26.1412 25.0822H20.0328C19.0722 25.0822 17.8881 25.0822 17.3431 26.2988L15.8812 29.5939C15.7326 29.8928 15.6452 30.2185 15.6244 30.5517C15.6244 31.7036 17.13 31.7683 17.9215 31.7683C17.9791 31.7681 18.0361 31.7799 18.0889 31.8028C18.1418 31.8257 18.1893 31.8593 18.2285 31.9014C18.2677 31.9435 18.2978 31.9933 18.3169 32.0476C18.3359 32.1019 18.3435 32.1596 18.3392 32.217C18.3404 32.2762 18.3294 32.335 18.3071 32.3899C18.2848 32.4448 18.2515 32.4945 18.2093 32.5361C18.1671 32.5777 18.1168 32.6103 18.0616 32.6318C18.0064 32.6534 17.9474 32.6635 17.8881 32.6615H11.3934C11.3343 32.6629 11.2756 32.6523 11.2207 32.6304C11.1658 32.6084 11.116 32.5756 11.0742 32.5338C11.0324 32.4921 10.9995 32.4423 10.9776 32.3875C10.9556 32.3326 10.9451 32.2739 10.9465 32.2149C10.9422 32.1575 10.9498 32.0998 10.9688 32.0455C10.9879 31.9912 11.0179 31.9414 11.0572 31.8993C11.0964 31.8572 11.1439 31.8236 11.1967 31.8007C11.2496 31.7778 11.3066 31.7661 11.3642 31.7662C12.9325 31.6702 13.4525 30.9315 14.0518 29.5918L21.7661 12.7033C22.1191 11.9667 22.8876 11.3281 23.5913 11.3281C24.0967 11.3302 24.2575 11.6182 24.4162 12.0335ZM18.5689 23.4837C18.5302 23.5406 18.5078 23.607 18.5042 23.6757C18.5042 23.7404 18.5689 23.7717 18.6963 23.7717H25.0344C25.4813 23.7717 25.6734 23.6757 25.6734 23.3877C25.6728 23.2432 25.64 23.1006 25.5773 22.9704L23.8169 18.2354C23.3992 17.1482 22.7936 15.7104 22.6015 14.5251C22.6015 14.4625 22.5681 14.4291 22.5367 14.4291C22.5054 14.4291 22.4741 14.4625 22.4741 14.5251C22.2337 15.3401 21.9228 16.1326 21.5448 16.8936L18.5689 23.4837Z",fill:"white"}),React.createElement("path",{d:"M36.4557 22.7333C36.4557 23.5555 35.99 24.0209 35.1484 24.0209C34.3068 24.0209 33.9727 23.5931 33.9727 22.9399C33.9727 22.1177 34.4592 21.6523 35.28 21.6523C36.1007 21.6523 36.4557 22.0989 36.4557 22.7333Z",fill:"white"}),React.createElement("path",{d:"M11.3739 22.7588C11.3739 23.5789 10.9061 24.0464 10.0666 24.0464C9.22712 24.0464 8.88672 23.6165 8.88672 22.9633C8.88672 22.1432 9.3733 21.6758 10.194 21.6758C11.0147 21.6758 11.3739 22.1244 11.3739 22.7588Z",fill:"white"})),React.createElement("defs",null,React.createElement("clipPath",{id:"clip0_3308_44290"},React.createElement("rect",{width:"27.5556",height:"21.3333",fill:"white",transform:"translate(8.89062 11.332)"}))))},8169:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(6072),n=a.n(r),o=a(5573);const c=e=>React.createElement(o.SVG,n()({},e,{width:"30",height:"30",viewBox:"0 0 258 258",fill:"none",xmlns:"http://www.w3.org/2000/svg"}),React.createElement(o.G,{clipPath:"url(#clip0)"},React.createElement(o.Rect,{width:"258",height:"258",fill:"#F2EFF8"}),React.createElement(o.Path,{d:"M473.616 242.393C286.085 103.281 -48.1297 232.191 -99.1903 253.521L-170.675 265.578L-322 187.675L-273.725 -72L572.952 -57.1614C602.351 89.0605 623.642 353.682 473.616 242.393Z",fill:"white"}),React.createElement(o.Path,{d:"M516.537 189.11C348.782 86.6334 -48.2135 155.075 -225.742 202.105L-261 -94L540.661 -79.1483C602.517 52.9696 684.292 291.586 516.537 189.11Z",fill:"#7A5CBD"}),React.createElement(o.Path,{d:"M57.1335 113.123L84.543 172.621L249 113.123H57.1335Z",fill:"#663399"}),React.createElement(o.Path,{d:"M49.1116 121.813L83.2063 172.621L249 113.123L49.1116 121.813Z",fill:"#F68909"}),React.createElement(o.Path,{d:"M49.1114 121.813L9 179.975L249 113.123L49.1114 121.813Z",fill:"#FFD66D"}),React.createElement(o.Path,{d:"M57.1335 113.123L86.5486 73.0112L249 113.123H57.1335Z",fill:"#D1B3EE"})),React.createElement("defs",null,React.createElement("clipPath",{id:"clip0"},React.createElement(o.Rect,{width:"258",height:"258",fill:"white"}))))},3909:(e,t,a)=>{"use strict";a.d(t,{x7:()=>c});var r=a(5985);const n=window?.jpFormsBlocks?.defaults?.preferredView,o="classic"===n?"edit.php?post_type=feedback":"admin.php?page=jetpack-forms",c=(0,r.$i)()?.adminUrl+o},67:(e,t,a)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const a="color: "+this.color;t.splice(1,0,a,"color: inherit");let r=0,n=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(r++,"%c"===e&&(n=r))})),t.splice(n,0,a)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=a(8926)(t);const{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},8926:(e,t,a)=>{e.exports=function(e){function t(e){let a,n,o,c=null;function l(...e){if(!l.enabled)return;const r=l,n=Number(new Date),o=n-(a||n);r.diff=o,r.prev=a,r.curr=n,a=n,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let c=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((a,n)=>{if("%%"===a)return"%";c++;const o=t.formatters[n];if("function"==typeof o){const t=e[c];a=o.call(r,t),e.splice(c,1),c--}return a})),t.formatArgs.call(r,e);(r.log||t.log).apply(r,e)}return l.namespace=e,l.useColors=t.useColors(),l.color=t.selectColor(e),l.extend=r,l.destroy=t.destroy,Object.defineProperty(l,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==c?c:(n!==t.namespaces&&(n=t.namespaces,o=t.enabled(e)),o),set:e=>{c=e}}),"function"==typeof t.init&&t.init(l),l}function r(e,a){const r=t(this.namespace+(void 0===a?":":a)+e);return r.log=this.log,r}function n(e,t){let a=0,r=0,n=-1,o=0;for(;a<e.length;)if(r<t.length&&(t[r]===e[a]||"*"===t[r]))"*"===t[r]?(n=r,o=a,r++):(a++,r++);else{if(-1===n)return!1;r=n+1,o++,a=o}for(;r<t.length&&"*"===t[r];)r++;return r===t.length}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names,...t.skips.map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const a=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of a)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const a of t.skips)if(n(e,a))return!1;for(const a of t.names)if(n(e,a))return!0;return!1},t.humanize=a(7378),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((a=>{t[a]=e[a]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let a=0;for(let t=0;t<e.length;t++)a=(a<<5)-a+e.charCodeAt(t),a|=0;return t.colors[Math.abs(a)%t.colors.length]},t.enable(t.load()),t}},9789:(e,t,a)=>{"use strict";t.A=function(e){var t=e.size,a=void 0===t?24:t,r=e.onClick,l=(e.icon,e.className),s=function(e,t){if(null==e)return{};var a,r,n=function(e,t){if(null==e)return{};var a,r,n={},o=Object.keys(e);for(r=0;r<o.length;r++)a=o[r],0<=t.indexOf(a)||(n[a]=e[a]);return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)a=o[r],0<=t.indexOf(a)||Object.prototype.propertyIsEnumerable.call(e,a)&&(n[a]=e[a])}return n}(e,o),i=["gridicon","gridicons-notice-outline",l,!!function(e){return 0==e%18}(a)&&"needs-offset",!1,!1].filter(Boolean).join(" ");return n.default.createElement("svg",c({className:i,height:a,width:a,onClick:r},s,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),n.default.createElement("g",null,n.default.createElement("path",{d:"M12 4c4.411 0 8 3.589 8 8s-3.589 8-8 8-8-3.589-8-8 3.589-8 8-8m0-2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 13h-2v2h2v-2zm-2-2h2l.5-6h-3l.5 6z"})))};var r,n=(r=a(1609))&&r.__esModule?r:{default:r},o=["size","onClick","icon","className"];function c(){return c=Object.assign||function(e){for(var t,a=1;a<arguments.length;a++)for(var r in t=arguments[a])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},c.apply(this,arguments)}},9384:e=>{"use strict";e.exports=window.JetpackConnection},7999:e=>{"use strict";e.exports=window.JetpackScriptDataModule},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},6087:e=>{"use strict";e.exports=window.lodash},1455:e=>{"use strict";e.exports=window.wp.apiFetch},4715:e=>{"use strict";e.exports=window.wp.blockEditor},4997:e=>{"use strict";e.exports=window.wp.blocks},6427:e=>{"use strict";e.exports=window.wp.components},9491:e=>{"use strict";e.exports=window.wp.compose},3582:e=>{"use strict";e.exports=window.wp.coreData},7143:e=>{"use strict";e.exports=window.wp.data},8490:e=>{"use strict";e.exports=window.wp.domReady},3656:e=>{"use strict";e.exports=window.wp.editor},8468:e=>{"use strict";e.exports=window.wp.element},2619:e=>{"use strict";e.exports=window.wp.hooks},7723:e=>{"use strict";e.exports=window.wp.i18n},2279:e=>{"use strict";e.exports=window.wp.plugins},5573:e=>{"use strict";e.exports=window.wp.primitives},3832:e=>{"use strict";e.exports=window.wp.url},6072:e=>{function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},3022:(e,t,a)=>{"use strict";function r(e){var t,a,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(a=r(e[t]))&&(n&&(n+=" "),n+=a)}else for(a in e)e[a]&&(n&&(n+=" "),n+=a);return n}a.d(t,{A:()=>n});const n=function(){for(var e,t,a=0,n="",o=arguments.length;a<o;a++)(e=arguments[a])&&(t=r(e))&&(n&&(n+=" "),n+=t);return n}},8377:e=>{"use strict";e.exports=JSON.parse('{"T":{"White":"#fff","Black":"#000","Gray 0":"#f6f7f7","Gray 5":"#dcdcde","Gray 10":"#c3c4c7","Gray 20":"#a7aaad","Gray 30":"#8c8f94","Gray 40":"#787c82","Gray 50":"#646970","Gray 60":"#50575e","Gray 70":"#3c434a","Gray 80":"#2c3338","Gray 90":"#1d2327","Gray 100":"#101517","Gray":"#646970","Blue 0":"#fbfcfe","Blue 5":"#f7f8fe","Blue 10":"#d6ddf9","Blue 20":"#adbaf3","Blue 30":"#7b90ff","Blue 40":"#546ff3","Blue 50":"#3858e9","Blue 60":"#2a46ce","Blue 70":"#1d35b4","Blue 80":"#1f3286","Blue 90":"#14215a","Blue 100":"#0a112d","Blue":"#3858e9","WordPress Blue 0":"#fbfcfe","WordPress Blue 5":"#f7f8fe","WordPress Blue 10":"#d6ddf9","WordPress Blue 20":"#adbaf3","WordPress Blue 30":"#7b90ff","WordPress Blue 40":"#546ff3","WordPress Blue 50":"#3858e9","WordPress Blue 60":"#2a46ce","WordPress Blue 70":"#1d35b4","WordPress Blue 80":"#1f3286","WordPress Blue 90":"#14215a","WordPress Blue 100":"#0a112d","WordPress Blue":"#3858e9","Purple 0":"#f2e9ed","Purple 5":"#ebcee0","Purple 10":"#e3afd5","Purple 20":"#d48fc8","Purple 30":"#c475bd","Purple 40":"#b35eb1","Purple 50":"#984a9c","Purple 60":"#7c3982","Purple 70":"#662c6e","Purple 80":"#4d2054","Purple 90":"#35163b","Purple 100":"#1e0c21","Purple":"#984a9c","Pink 0":"#f5e9ed","Pink 5":"#f2ceda","Pink 10":"#f7a8c3","Pink 20":"#f283aa","Pink 30":"#eb6594","Pink 40":"#e34c84","Pink 50":"#c9356e","Pink 60":"#ab235a","Pink 70":"#8c1749","Pink 80":"#700f3b","Pink 90":"#4f092a","Pink 100":"#260415","Pink":"#c9356e","Red 0":"#f7ebec","Red 5":"#facfd2","Red 10":"#ffabaf","Red 20":"#ff8085","Red 30":"#f86368","Red 40":"#e65054","Red 50":"#d63638","Red 60":"#b32d2e","Red 70":"#8a2424","Red 80":"#691c1c","Red 90":"#451313","Red 100":"#240a0a","Red":"#d63638","Orange 0":"#f5ece6","Orange 5":"#f7dcc6","Orange 10":"#ffbf86","Orange 20":"#faa754","Orange 30":"#e68b28","Orange 40":"#d67709","Orange 50":"#b26200","Orange 60":"#8a4d00","Orange 70":"#704000","Orange 80":"#543100","Orange 90":"#361f00","Orange 100":"#1f1200","Orange":"#b26200","Yellow 0":"#f5f1e1","Yellow 5":"#f5e6b3","Yellow 10":"#f2d76b","Yellow 20":"#f0c930","Yellow 30":"#deb100","Yellow 40":"#c08c00","Yellow 50":"#9d6e00","Yellow 60":"#7d5600","Yellow 70":"#674600","Yellow 80":"#4f3500","Yellow 90":"#320","Yellow 100":"#1c1300","Yellow":"#9d6e00","Green 0":"#e6f2e8","Green 5":"#b8e6bf","Green 10":"#68de86","Green 20":"#1ed15a","Green 30":"#00ba37","Green 40":"#00a32a","Green 50":"#008a20","Green 60":"#007017","Green 70":"#005c12","Green 80":"#00450c","Green 90":"#003008","Green 100":"#001c05","Green":"#008a20","Celadon 0":"#e4f2ed","Celadon 5":"#a7e8d3","Celadon 10":"#66deb9","Celadon 20":"#31cc9f","Celadon 30":"#09b585","Celadon 40":"#009e73","Celadon 50":"#008763","Celadon 60":"#007053","Celadon 70":"#005c44","Celadon 80":"#004533","Celadon 90":"#003024","Celadon 100":"#001c15","Celadon":"#008763","Automattic Blue 0":"#ebf4fa","Automattic Blue 5":"#c4e2f5","Automattic Blue 10":"#88ccf2","Automattic Blue 20":"#5ab7e8","Automattic Blue 30":"#24a3e0","Automattic Blue 40":"#1490c7","Automattic Blue 50":"#0277a8","Automattic Blue 60":"#036085","Automattic Blue 70":"#02506e","Automattic Blue 80":"#02384d","Automattic Blue 90":"#022836","Automattic Blue 100":"#021b24","Automattic Blue":"#24a3e0","Simplenote Blue 0":"#e9ecf5","Simplenote Blue 5":"#ced9f2","Simplenote Blue 10":"#abc1f5","Simplenote Blue 20":"#84a4f0","Simplenote Blue 30":"#618df2","Simplenote Blue 40":"#4678eb","Simplenote Blue 50":"#3361cc","Simplenote Blue 60":"#1d4fc4","Simplenote Blue 70":"#113ead","Simplenote Blue 80":"#0d2f85","Simplenote Blue 90":"#09205c","Simplenote Blue 100":"#05102e","Simplenote Blue":"#3361cc","WooCommerce Purple 0":"#f2edff","WooCommerce Purple 5":"#e1d7ff","WooCommerce Purple 10":"#d1c1ff","WooCommerce Purple 20":"#b999ff","WooCommerce Purple 30":"#a77eff","WooCommerce Purple 40":"#873eff","WooCommerce Purple 50":"#720eec","WooCommerce Purple 60":"#6108ce","WooCommerce Purple 70":"#5007aa","WooCommerce Purple 80":"#3c087e","WooCommerce Purple 90":"#2c045d","WooCommerce Purple 100":"#1f0342","WooCommerce Purple":"#720eec","Jetpack Green 0":"#f0f2eb","Jetpack Green 5":"#d0e6b8","Jetpack Green 10":"#9dd977","Jetpack Green 20":"#64ca43","Jetpack Green 30":"#2fb41f","Jetpack Green 40":"#069e08","Jetpack Green 50":"#008710","Jetpack Green 60":"#007117","Jetpack Green 70":"#005b18","Jetpack Green 80":"#004515","Jetpack Green 90":"#003010","Jetpack Green 100":"#001c09","Jetpack Green":"#069e08"}}')}},t={};function a(r){var n=t[r];if(void 0!==n)return n.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,a),o.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var r in t)a.o(t,r)&&!a.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=a(1013),t=a(2399),r=a(5620);(0,t.A)(r.U,r.W,e.P)})()})();