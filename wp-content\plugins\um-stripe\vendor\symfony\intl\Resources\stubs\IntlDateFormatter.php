<?php

namespace UM_Stripe\Vendor\UM_Stripe\Vendor;

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
use UM_Stripe\Vendor\Symfony\Intl\DateFormatter\IntlDateFormatter as BaseIntlDateFormatter;
use UM_Stripe\Vendor\Symfony\Polyfill\Intl\Icu\IntlDateFormatter as IntlDateFormatterPolyfill;
if (!\class_exists(IntlDateFormatterPolyfill::class)) {
    trigger_deprecation('symfony/intl', '5.3', 'Polyfills are deprecated, try running "composer require symfony/polyfill-intl-icu ^1.21" instead.');
    /**
     * Stub implementation for the IntlDateFormatter class of the intl extension.
     *
     * <AUTHOR> <<EMAIL>>
     *
     * @see BaseIntlDateFormatter
     */
    class IntlDateFormatter extends BaseIntlDateFormatter
    {
    }
    /**
     * Stub implementation for the IntlDateFormatter class of the intl extension.
     *
     * <AUTHOR> Schussek <<EMAIL>>
     *
     * @see BaseIntlDateFormatter
     */
    \class_alias('UM_Stripe\Vendor\IntlDateFormatter', 'IntlDateFormatter', \false);
} else {
    /**
     * Stub implementation for the IntlDateFormatter class of the intl extension.
     *
     * <AUTHOR> Schussek <<EMAIL>>
     *
     * @see BaseIntlDateFormatter
     */
    class IntlDateFormatter extends IntlDateFormatterPolyfill
    {
    }
    /**
     * Stub implementation for the IntlDateFormatter class of the intl extension.
     *
     * <AUTHOR> Schussek <<EMAIL>>
     *
     * @see BaseIntlDateFormatter
     */
    \class_alias('UM_Stripe\Vendor\IntlDateFormatter', 'IntlDateFormatter', \false);
}
