<?php
namespace um_ext\um_user_photos\common;

use WP_Query;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Query
 *
 * @package um_ext\um_user_photos\common
 */
class Query {

	public function hooks() {
		add_filter( 'um_user_photo_query_args', array( $this, 'change_query' ), 20, 2 );
	}

	/**
	 * Filter um_user_photos query
	 *
	 * @param array $args
	 * @param int   $profile_id
	 *
	 * @return array
	 */
	public function change_query( $args, $profile_id ) {
		if ( empty( $args['meta_query'] ) ) {
			$args['meta_query'] = array( 'relation' => 'AND' );
		}

		$query = array(
			'relation' => 'OR',
			array(
				'key'     => '_privacy',
				'compare' => 'NOT EXISTS',
			),
			array(
				'key'     => '_privacy',
				'value'   => 'everyone',
				'compare' => '=',
			),
		);

		$current_user_id = null;
		if ( is_user_logged_in() ) {
			// user logged in
			$current_user_id = get_current_user_id();

			if ( absint( $profile_id ) === $current_user_id ) {
				$accessible_by_author = apply_filters( 'um_user_photos_accessible_by_author_privacy', array( 'followed', 'follower', 'friends', 'only_me' ) );

				$query[] = array(
					'key'     => '_privacy',
					'value'   => $accessible_by_author,
					'compare' => 'IN',
				);
			}
		}

		$query = apply_filters( 'um_user_photos_change_meta_query', $query, $current_user_id, $profile_id );

		$args['meta_query'][] = $query;

		return $args;
	}

	/**
	 * Disable posts query filter by the taxonomy 'language'. Integration with the plugin 'Polylang'.
	 *
	 * @since 2.0.7
	 *
	 * @param WP_Query $query
	 */
	public static function remove_language_filter( $query ) {
		$qv = &$query->query_vars;
		if ( ! empty( $qv['tax_query'] ) && is_array( $qv['tax_query'] ) ) {
			foreach ( $qv['tax_query'] as $key => $q ) {
				if ( isset( $q['taxonomy'], $q['terms'] ) && 'language' === $q['taxonomy'] ) {
					unset( $qv['tax_query'][ $key ] );
				}
			}
		}
	}

	/**
	 * @param array $args
	 *
	 * @return array|WP_Query
	 */
	public function get_albums( $args ) {
		$args = wp_parse_args(
			$args,
			array(
				'page'     => 1,
				'per_page' => UM()->User_Photos()->common()->gallery()->albums_per_page,
			)
		);

		// Exclude authors without permission to have galleries.
		$roles         = UM()->roles()->get_roles();
		$exclude_roles = array();
		foreach ( $roles as $um_key => $role ) {
			if ( strpos( $um_key, 'um_' ) === 0 ) {
				$key = substr( $um_key, 3 );
			} else {
				$key = $um_key;
			}

			$role_meta = get_option( "um_role_{$key}_meta" );
			if ( ! empty( $role_meta ) ) {
				if ( ! array_key_exists( '_um_enable_user_photos', $role_meta ) || 0 === absint( $role_meta['_um_enable_user_photos'] ) ) {
					$exclude_roles[ $um_key ] = $key;
				}
			}
		}

		$users = array();
		foreach ( $exclude_roles as $role ) {
			$users[] = get_users(
				array(
					'role'   => $role,
					'fields' => 'ID',
				)
			);
		}
		$users = array_merge( ...$users );

		$author_not_in = array();
		foreach ( $users as $user_id ) {
			$user_role      = UM()->roles()->get_priority_user_role( $user_id );
			$user_role_data = UM()->roles()->role_data( $user_role );
			if ( empty( $user_role_data['enable_user_photos'] ) ) {
				$author_not_in[] = $user_id;
			}
		}

		$blocked_users = UM()->User_Photos()->common()->user()->get_blocked_users();
		if ( ! empty( $blocked_users ) ) {
			$author_not_in = array_merge( $author_not_in, $blocked_users );
		}

		// Get public albums with everyone privacy.
		$public_args = array(
			'fields'      => 'ids',
			'post_type'   => 'um_user_photos',
			'post_status' => 'publish',
			'numberposts' => -1,
			'meta_query'  => array(
				'privacy' => array(
					'relation' => 'OR',
					array(
						'key'     => '_privacy',
						'compare' => 'NOT EXISTS',
					),
					array(
						'key'     => '_privacy',
						'value'   => 'everyone',
						'compare' => '=',
					),
				),
			),
		);

		if ( ! empty( $author_not_in ) ) {
			$public_args['author__not_in'] = $author_not_in;
		}

		$public_ids = get_posts( $public_args );

		// Get own albums.
		if ( is_user_logged_in() ) {
			$current_user_id = get_current_user_id();
			$user_role      = UM()->roles()->get_priority_user_role( $current_user_id );
			$user_role_data = UM()->roles()->role_data( $user_role );
			if ( ! empty( $user_role_data['enable_user_photos'] ) ) {
				$own_args = array(
					'author'      => $current_user_id,
					'fields'      => 'ids',
					'post_type'   => 'um_user_photos',
					'post_status' => 'publish',
					'numberposts' => -1,
				);

				$own_ids    = get_posts( $own_args );
				$public_ids = array_merge( $public_ids, $own_ids );
			}
		}

		$post__in = apply_filters( 'um_user_photo_albums_public_ids', array_unique( $public_ids ) );

		if ( empty( $post__in ) ) {
			return array();
		}

		$query_def = array(
			'post_type'      => 'um_user_photos',
			'post_status'    => 'publish',
			'post__in'       => $post__in,
			'paged'          => $args['page'],
			'posts_per_page' => $args['per_page'],
		);

		$query = apply_filters( 'um_user_photo_albums_query', $query_def );

		$albums = new WP_Query( $query );
		if ( ! $albums->have_posts() ) {
			return array();
		}

		return $albums;
	}
}
