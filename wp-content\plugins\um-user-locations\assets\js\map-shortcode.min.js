function UMInitMapShortcode(){jQuery(".um-member-map-shortcode").each(function(){var o=jQuery(this);wp.UM.user_locations.map_shortcode.init(o)})}"object"!=typeof wp.UM&&(wp.UM={}),"object"!=typeof wp.UM.user_locations&&(wp.UM.user_locations={}),wp.UM.user_locations.map_shortcode={infowindow:{customize:function(){var o=jQuery(".gm-style-iw"),a=o.prev(),a=(a.children(":nth-child(2)").css({display:"none"}),a.children(":nth-child(4)").css({display:"none"}),o.parent().parent().css({left:"115px"}),a.children(":nth-child(1)").attr("style",function(o,a){return a+"left: 76px !important;"}),a.children(":nth-child(3)").attr("style",function(o,a){return a+"left: 76px !important;"}),a.children(":nth-child(3)").find("div").children().css({"box-shadow":"rgba(72, 181, 233, 0.6) 0px 1px 6px","z-index":"1"}),o.next());a.css({opacity:"1",right:"48px",top:"20px","border-radius":"50%"}),a.mouseout(function(){jQuery(this).css({opacity:"1"})})},close:function(o){void 0!==wp.UM.user_locations.map_shortcode.blocks.data&&(void 0!==wp.UM.user_locations.map_shortcode.blocks.data.infowindows&&jQuery.each(wp.UM.user_locations.map_shortcode.blocks.data.infowindows,function(o){wp.UM.user_locations.map_shortcode.blocks.data.infowindows[o].close()}),o)&&(wp.UM.user_locations.map_shortcode.blocks.data.infowindows={})}},get_map:function(){return void 0!==wp.UM.user_locations.map_shortcode.blocks.data&&void 0!==wp.UM.user_locations.map_shortcode.blocks.data.map?wp.UM.user_locations.map_shortcode.blocks.data.map:null},init:function(o){var a,e,s;if(!o.hasClass("um-map-inited"))return wp.UM.user_locations.map_shortcode.blocks.data=[],s=new google.maps.LatLng(o.data("lat"),o.data("lng")),a=o.data("zoom"),e=o.data("field"),void 0===a&&(a=12),s=wp.hooks.applyFilters("um_user_locations_map_shortcode_args_init",{center:s,zoom:a,field:e},"data",o),wp.UM.user_locations.map_shortcode.blocks.data.map=new google.maps.Map(o[0],s),wp.UM.user_locations.map_shortcode.ajax.handler(o,e),o.removeClass(wp.UM.user_locations.map_shortcode.classes.hidden).addClass("um-map-inited"),o},ajax:{handler:function(a,o){o={marker_type:a.data("marker_type"),field:o,nonce:um_scripts.nonce};wp.ajax.send("um_get_map_members_data",{data:o,success:function(o){wp.UM.user_locations.map_shortcode.whengoogleloadeddo(wp.UM.user_locations.map_shortcode.ajax.response,[a,o])},error:function(o){console.log(o)}})},response:function(o,t){var a,e,r=o.data("field"),c=o.data("field_title"),i={};Object.getOwnPropertyNames(i).forEach(function(o){delete i[o]}),jQuery.each(t.users,function(o){var a,e,s=t.users[o];void 0===i[s.id]&&(i[s.id]={}),void 0!==s[r+"_lat"]&&void 0!==s[r+"_lng"]&&(e=new google.maps.LatLng(s[r+"_lat"],s[r+"_lng"]),a={content:wp.template("um-user-location-map-shortcode-marker-infowindow")({field:r,userdata:s}),maxWidth:350},a=wp.hooks.applyFilters("um_user_locations_infowindow_data",a,"data",s.id),e={position:e,map:wp.UM.user_locations.map_shortcode.get_map(),title:s.display_name+" "+c,zIndex:o+1,optimized:!1,infowindow:a},""!==s.avatar_url&&(o=-1===s.avatar_url.indexOf("?")?"?um_avatar_marker=1":"&um_avatar_marker=1",e.icon={url:s.avatar_url+o,scaledSize:new google.maps.Size(32,32),size:new google.maps.Size(36,40),anchor:new google.maps.Point(18,18)},e.shape={coords:[18,18,20],type:"circle"}),e=wp.hooks.applyFilters("um_user_locations_marker_data",e,"data",s),i[s.id]=e)}),void 0===wp.UM.user_locations.map_shortcode.blocks.data.markers&&(wp.UM.user_locations.map_shortcode.blocks.data.markers={}),void 0===wp.UM.user_locations.map_shortcode.blocks.data.infowindows&&(wp.UM.user_locations.map_shortcode.blocks.data.infowindows={}),void 0!==wp.UM.user_locations.map_shortcode.blocks.data.marker_clusterer&&wp.UM.user_locations.map_shortcode.blocks.data.marker_clusterer.clearMarkers(),jQuery.each(i,function(a){void 0===wp.UM.user_locations.map_shortcode.blocks.data.markers[a]&&(wp.UM.user_locations.map_shortcode.blocks.data.markers[a]={}),void 0===wp.UM.user_locations.map_shortcode.blocks.data.infowindows[a]&&(wp.UM.user_locations.map_shortcode.blocks.data.infowindows[a]={}),wp.UM.user_locations.map_shortcode.blocks.data.markers[a]=new google.maps.Marker(i[a]),wp.UM.user_locations.map_shortcode.blocks.data.infowindows[a]=new google.maps.InfoWindow(i[a].infowindow),wp.UM.user_locations.map_shortcode.blocks.data.markers[a].addListener("click",function(o){void 0===o?wp.UM.user_locations.map_shortcode.infowindow.close():(wp.UM.user_locations.map_shortcode.infowindow.close(),wp.UM.user_locations.map_shortcode.blocks.data.infowindows[a].status="open",wp.UM.user_locations.map_shortcode.blocks.data.infowindows[a].open(wp.UM.user_locations.map_shortcode.get_map(),wp.UM.user_locations.map_shortcode.blocks.data.markers[a]))}),google.maps.event.addListener(wp.UM.user_locations.map_shortcode.blocks.data.infowindows[a],"closeclick",function(){wp.UM.user_locations.map_shortcode.blocks.data.infowindows[a].status="closed"})}),wp.hooks.doAction("um_member_directory_after_markers_init",!1,i),wp.hooks.applyFilters("um_map_shortcode_disable_spiderfier",!1)||(a=new OverlappingMarkerSpiderfier(wp.UM.user_locations.map_shortcode.get_map(),{keepSpiderfied:!0,event:"mouseover"}),e=[],void 0!==wp.UM.user_locations.map_shortcode.blocks.data.markers&&jQuery.each(wp.UM.user_locations.map_shortcode.blocks.data.markers,function(o){e.push(wp.UM.user_locations.map_shortcode.blocks.data.markers[o]),a.addMarker(wp.UM.user_locations.map_shortcode.blocks.data.markers[o])}),a.addListener("spiderfy",function(o){void 0!==wp.UM.user_locations.map_shortcode.blocks.data.infowindows&&jQuery.each(wp.UM.user_locations.map_shortcode.blocks.data.infowindows,function(o){wp.UM.user_locations.map_shortcode.blocks.data.infowindows[o].close()})}),a.addListener("unspiderfy",function(o){void 0!==wp.UM.user_locations.map_shortcode.blocks.data.infowindows&&jQuery.each(wp.UM.user_locations.map_shortcode.blocks.data.infowindows,function(o){wp.UM.user_locations.map_shortcode.blocks.data.infowindows[o].close()})}));wp.hooks.applyFilters("um_map_shortcode_disable_clustering",!1)||(o=wp.hooks.applyFilters("um_user_locations_marker_clustering_options",{imagePath:um_user_location_map_shortcode.cluster_url},"data",wp.UM.user_locations.map_shortcode.get_map(),e),wp.UM.user_locations.map_shortcode.blocks.data.marker_clusterer=new MarkerClusterer(wp.UM.user_locations.map_shortcode.get_map(),e,o),wp.UM.user_locations.map_shortcode.blocks.data.marker_clusterer.setMaxZoom(20),google.maps.event.addListener(wp.UM.user_locations.map_shortcode.blocks.data.marker_clusterer,"clusterclick",function(o){var a=o.getMarkers()[0];setTimeout(function(){google.maps.event.trigger(a,"click")},1e3)}))}},whengoogleloadeddo:function(o,a){"undefined"!=typeof google?o(a[0],a[1]):setTimeout(function(){wp.UM.user_locations.map_shortcode.whengoogleloadeddo(o,a)},500)},blocks:[],classes:{hidden:"um-user-location-hidden-map"}};var um_ul_script=document.createElement("script");um_ul_script.src="//maps.googleapis.com/maps/api/js?key="+um_user_location_map_shortcode.api_key+"&libraries=places&callback=UMInitMapShortcode",um_user_location_map_shortcode.region&&(um_ul_script.src+="&language="+um_user_location_map_shortcode.region),document.body.appendChild(um_ul_script);