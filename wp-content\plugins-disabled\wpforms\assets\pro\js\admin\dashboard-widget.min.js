const WPFormsDashboardWidget=window.WPFormsDashboardWidget||function(o){const d={$widget:o("#wpforms_reports_widget_pro"),$chartResetBtn:o("#wpforms-dash-widget-reset-chart"),$DaysSelect:o("#wpforms-dash-widget-timespan"),$settingsBtn:o("#wpforms-dash-widget-settings-button"),$canvas:o("#wpforms-dash-widget-chart"),$formsListBlock:o("#wpforms-dash-widget-forms-list-block"),$dismissButton:o(".wpforms-dash-widget-dismiss-icon")},a={backgroundColor:"rgb(226, 119, 48)",hoverBackgroundColor:"#da691f",borderColor:"rgb(226, 119, 48)",hoverBorderColor:"#da691f",pointBackgroundColor:"rgba(255, 255, 255, 1)"},s={backgroundColor:"rgba(34, 113, 177, 1)",hoverBackgroundColor:"#135e96",borderColor:"rgba(34, 113, 177, 1)",hoverBorderColor:"#135e96",pointBackgroundColor:"rgba(255, 255, 255, 1)"};"line"===wpforms_dashboard_widget.chart_type&&(s.backgroundColor="#E2ECF5",a.backgroundColor="rgba(255, 129, 0, 0.135)");var t="wp"===wpforms_dashboard_widget.color_scheme?s:a,e=o("body").hasClass("rtl");const r={instance:null,settings:{type:wpforms_dashboard_widget.chart_type,data:{labels:[],datasets:[{label:wpforms_dashboard_widget.i18n.entries,data:[],borderWidth:2,pointRadius:4,pointBorderWidth:1,...t}]},options:{maintainAspectRatio:!1,scales:{x:{type:"timeseries",time:{tooltipFormat:wpforms_dashboard_widget.date_format},reverse:e,ticks:{source:"labels",padding:0,minRotation:25,maxRotation:25,callback(t,e,a){var s=Math.floor(a.length/7);return s<1||(a.length-e-1)%s==0?moment(t).format("MMM D"):void 0}}},y:{beginAtZero:!0,ticks:{maxTicksLimit:6,padding:0,callback(t){if(Math.floor(t)===t)return t}}}},elements:{line:{tension:0,fill:!0}},animation:!1,plugins:{legend:{display:!1},tooltip:{enabled:!0,displayColors:!1,rtl:e}}}},init(){var t;d.$canvas.length&&(t=d.$canvas[0].getContext("2d"),r.instance=new Chart(t,r.settings),r.updateUI(wpforms_dashboard_widget.chart_data))},ajaxUpdate(t,e){t={_wpnonce:wpforms_dashboard_widget.nonce,action:"wpforms_"+wpforms_dashboard_widget.slug+"_get_chart_data",days:t,form_id:e};i.addOverlay(o(r.instance.canvas)),o.post(ajaxurl,t,function(t){r.updateUI(t)})},updateUI(t){i.removeOverlay(d.$canvas),o.isEmptyObject(t)?(r.updateWithDummyData(),r.showEmptyDataMessage()):(r.updateData(t),r.removeEmptyDataMessage()),r.instance.data.labels=r.settings.data.labels,r.instance.data.datasets[0].data=r.settings.data.datasets[0].data,r.instance.update()},updateData(t){r.settings.data.labels=[],r.settings.data.datasets[0].data=[],r.updateTotal(t)},updateTotal(t){let s=0;o.each(t,function(t,e){s=Number(s)+Number(e.count);var a=moment(e.day);r.settings.data.labels.push(a),r.settings.data.datasets[0].data.push({x:a,y:e.count})}),o("#entry-count-value").text(s)},updateWithDummyData(){r.settings.data.labels=[],r.settings.data.datasets[0].data=[];var t,e=moment().startOf("day"),a=d.$DaysSelect.val()||7;let s;for(s=1;s<=a;s++)t=e.clone().subtract(s,"days"),r.settings.data.labels.push(t),r.settings.data.datasets[0].data.push({x:t,y:Math.floor(16*Math.random())+5})},showEmptyDataMessage(){r.removeEmptyDataMessage(),d.$canvas.after(wpforms_dashboard_widget.empty_chart_html)},removeEmptyDataMessage(){d.$canvas.siblings(".wpforms-error").remove()},events:{daysChanged(){var t=d.$DaysSelect.val(),e=d.$DaysSelect.attr("data-active-form-id")||0;r.ajaxUpdate(t,e),i.saveWidgetMeta("timespan",t)},singleFormView(t){o(".wpforms-dash-widget-single-chart-btn").show();var e=d.$DaysSelect.val(),a=t.closest("tr").attr("data-form-id"),s=t.closest("tr").find(".wpforms-dash-widget-form-title").text();o("#wpforms-dash-widget-chart-title").text(s),d.$DaysSelect.attr("data-active-form-id",a),d.$chartResetBtn.appendTo(t.closest("td")),t.hide(),d.$chartResetBtn.show(),o("#entry-count-text").text(wpforms_dashboard_widget.i18n.form_entries),r.ajaxUpdate(e,a),i.saveWidgetMeta("active_form_id",a)},resetToGeneralView(){var t=d.$DaysSelect.val();d.$DaysSelect.removeAttr("data-active-form-id"),d.$chartResetBtn.hide(),d.$chartResetBtn.closest("td").find(".wpforms-dash-widget-single-chart-btn").show(),o("#entry-count-text").text(wpforms_dashboard_widget.i18n.total_entries),o("#wpforms-dash-widget-chart-title").text(wpforms_dashboard_widget.i18n.total_entries),r.ajaxUpdate(t,0),i.saveWidgetMeta("active_form_id",0)}}},i={chart:r,init(){o(i.ready)},ready(){r.init(),i.events(),i.graphSettings()},graphSettings(){d.$settingsBtn.on("click",function(){o(this).siblings(".wpforms-dash-widget-settings-menu").toggle()}),d.$widget.find(".wpforms-dash-widget-settings-menu-save").on("click",function(){i.saveSettings()})},saveSettings(){var t=d.$widget.find(".wpforms-dash-widget-settings-menu input[name=wpforms-style]:checked").val(),e=d.$widget.find(".wpforms-dash-widget-settings-menu input[name=wpforms-color]:checked").val();t&&(i.saveWidgetMeta("graph_style",t),"2"===t?(r.settings.type="line",s.backgroundColor="rgba(34, 113, 177, 0.135)",a.backgroundColor="rgba(255, 129, 0, 0.135)"):(r.settings.type="bar",s.backgroundColor="rgba(34, 113, 177, 1)",a.backgroundColor="#E27730")),e&&(i.saveWidgetMeta("color_scheme",e),r.settings.data.datasets[0]="2"===e?{...r.settings.data.datasets[0],...s}:{...r.settings.data.datasets[0],...a}),r.instance.update(),d.$widget.find(".wpforms-dash-widget-settings-menu").hide()},events(){i.chartEvents(),i.formsListEvents(),i.miscEvents()},chartEvents(){d.$DaysSelect.on("change",function(){r.events.daysChanged()})},formsListEvents(){d.$DaysSelect.on("change",function(){i.updateFormsList(o(this).val())}),d.$widget.on("click",".wpforms-dash-widget-single-chart-btn",function(){var t=o(this),e=t.closest("tr");r.events.singleFormView(t),e.closest("table").find("tr.wpforms-dash-widget-form-active").removeClass("wpforms-dash-widget-form-active"),e.addClass("wpforms-dash-widget-form-active")}),d.$formsListBlock.on("click",".wpforms-dash-widget-reset-chart",function(){o(".wpforms-dash-widget-reset-chart").hide(),r.events.resetToGeneralView(),d.$formsListBlock.find("tr.wpforms-dash-widget-form-active").removeClass("wpforms-dash-widget-form-active")}),d.$widget.on("click","#wpforms-dash-widget-forms-more",function(){i.toggleCompleteFormsList()})},miscEvents(){d.$dismissButton.on("click",function(){i.dismissWidgetBlock(o(this))})},updateFormsList(e){var t={_wpnonce:wpforms_dashboard_widget.nonce,action:"wpforms_"+wpforms_dashboard_widget.slug+"_get_forms_list",days:e};i.addOverlay(d.$formsListBlock.children().first()),o.post(ajaxurl,t,function(t){d.$formsListBlock.html(t),i.saveWidgetMeta("timespan",e)})},toggleCompleteFormsList(){o("#wpforms-dash-widget-forms-list-table .wpforms-dash-widget-forms-list-hidden-el").toggle(),o("#wpforms-dash-widget-forms-more").html(function(t,e){return e===wpforms_dashboard_widget.show_less_html?wpforms_dashboard_widget.show_more_html:wpforms_dashboard_widget.show_less_html})},saveWidgetMeta(t,e){t={_wpnonce:wpforms_dashboard_widget.nonce,action:"wpforms_"+wpforms_dashboard_widget.slug+"_save_widget_meta",meta:t,value:e};o.post(ajaxurl,t)},addOverlay(t){t.parent().closest(".wpforms-dash-widget-block").length&&(i.removeOverlay(t),t.after('<div class="wpforms-dash-widget-overlay"></div>'))},removeOverlay(t){t.siblings(".wpforms-dash-widget-overlay").remove()},dismissRecommendedBlock(){console.warn("WARNING! WPFormsDashboardWidget.dismissRecommendedBlock() has been deprecated, please use WPFormsDashboardWidget.dismissWidgetBlock() instead."),o(".wpforms-dash-widget-recommended-plugin-block").remove(),i.saveWidgetMeta("hide_recommended_block",1)},dismissWidgetBlock(t){t.closest(".wpforms-dash-widget-block").remove(),i.saveWidgetMeta(t.data("field"),1)}};return i}((document,window,jQuery));WPFormsDashboardWidget.init();