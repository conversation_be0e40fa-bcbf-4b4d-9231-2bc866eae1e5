<?php

namespace UM_Stripe\Vendor\UM_Stripe\Vendor;

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
use UM_Stripe\Vendor\Symfony\Intl\Locale\Locale as IntlLocale;
use UM_Stripe\Vendor\Symfony\Polyfill\Intl\Icu\Locale as LocalePolyfill;
if (!\class_exists(LocalePolyfill::class)) {
    trigger_deprecation('symfony/intl', '5.3', 'Polyfills are deprecated, try running "composer require symfony/polyfill-intl-icu ^1.21" instead.');
    /**
     * Stub implementation for the Locale class of the intl extension.
     *
     * <AUTHOR> <<EMAIL>>
     *
     * @see IntlLocale
     */
    class Locale extends IntlLocale
    {
    }
    /**
     * Stub implementation for the Locale class of the intl extension.
     *
     * <AUTHOR> <<EMAIL>>
     *
     * @see IntlLocale
     */
    \class_alias('UM_Stripe\Vendor\Locale', 'Locale', \false);
} else {
    /**
     * Stub implementation for the Locale class of the intl extension.
     *
     * <AUTHOR> Schussek <<EMAIL>>
     *
     * @see IntlLocale
     */
    class Locale extends LocalePolyfill
    {
    }
    /**
     * Stub implementation for the Locale class of the intl extension.
     *
     * <AUTHOR> Schussek <<EMAIL>>
     *
     * @see IntlLocale
     */
    \class_alias('UM_Stripe\Vendor\Locale', 'Locale', \false);
}
