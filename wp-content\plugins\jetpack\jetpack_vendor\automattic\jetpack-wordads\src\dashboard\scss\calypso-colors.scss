@use 'sass:color';

// Blues
$blue-wordpress:         #0087be;
$blue-light:             #78dcfa;
$blue-medium:            #3582c4;
$blue-dark:              #005082;
$blue-medium-dark:       #2271b1;
$blue-grey-light:        #f6f7f7;
$blue-grey-dark:         #0a4b78;
$light-gray-700:         #c3c4c7;


// Grays
$gray-original:          #87a6bc;
$gray:                   color.adjust( $gray-original, $saturation: -100% ); // Intermediary transform to match dotcom's colors

/**
 * $gray color functions:
 *
 *   color.adjust( $gray, $lightness: 10% )
 *   color.adjust( $gray, $lightness: 20% )
 *   color.adjust( $gray, $lightness: 30% )
 *   color.adjust( $gray, $lightness: -10% )
 *   color.adjust( $gray, $lightness: -20% )
 *   color.adjust( $gray, $lightness: -30% )
 *
 * See wordpress.com/design-handbook/colors/ for more info.
 */

$gray-light:             color.adjust( $gray, $lightness: 33% ); //#f6f6f6
$gray-dark:              color.adjust( $gray, $lightness: -38% ); //#404040

// $gray-text: ideal for standard, non placeholder text
// $gray-text-min: minimum contrast needed for WCAG 2.0 AA on white background
$gray-text:              $gray-dark;
$gray-text-min:          color.adjust( $gray, $lightness: -18% ); //#537994

// Shades of gray
$gray-lighten-10: color.adjust( $gray, $lightness: 10% ); // #a8bece
$gray-lighten-20: color.adjust( $gray, $lightness: 20% ); // #c8d7e1
$gray-lighten-30: color.adjust( $gray, $lightness: 30% ); // #e9eff3
$gray-darken-10:  color.adjust( $gray, $lightness: -10% );  // #668eaa
$gray-darken-20:  color.adjust( $gray, $lightness: -20% );  // #4f748e
$gray-darken-30:  color.adjust( $gray, $lightness: -30% );  // #3d596d

// Oranges
$orange-jazzy:           #f0821e;
$orange-fire:            #d63638;

// Alerts
$alert-yellow:           #f0b849;
$alert-red:              #d94f4f;
$alert-green:            #4ab866;
$alert-purple:           #855DA6;

// Link hovers
$link-highlight:         tint($blue-medium, 20%);

// Essentials
$white:                  rgba(255,255,255,1);
$transparent:            rgba(255,255,255,0);

$border-ultra-light-gray: #e8f0f5;
