#wpforms-builder .wpforms-toolbar {
  background: #ffffff;
  box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.2);
  height: 76px;
  padding: 0;
  position: fixed;
  text-align: center;
  top: var(--wpforms-admin-bar-height);
  width: 100%;
  z-index: 50;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  transition-property: top;
  transition-duration: 0.25s;
  transition-timing-function: ease-out;
}

#wpforms-builder .wpforms-toolbar .wpforms-left {
  align-items: center;
  border-inline-end: 1px solid #dddddd;
  display: flex;
  height: 100%;
  justify-content: center;
  inset-inline-start: 0;
  position: absolute;
  top: 0;
  width: 95px;
}

#wpforms-builder .wpforms-toolbar .wpforms-left img {
  height: 45px;
  width: 57px;
}

#wpforms-builder .wpforms-toolbar .wpforms-center {
  display: flex;
  gap: 5px;
  align-items: center;
  text-align: start;
  font-size: 18px;
  line-height: 1;
  font-weight: 400;
  margin-inline-end: 520px;
  margin-inline-start: 114px;
  max-width: calc( 100% - 500px);
  height: 100%;
}

#wpforms-builder .wpforms-toolbar .wpforms-center-form-name-prefix {
  white-space: nowrap;
}

#wpforms-builder .wpforms-toolbar .wpforms-center-form-name {
  cursor: pointer;
}

#wpforms-builder .wpforms-toolbar .wpforms-center-form-name:hover {
  text-decoration: underline;
}

#wpforms-builder .wpforms-toolbar .wpforms-center-form-template-badge {
  background-color: #fdf2eb;
  border-radius: 3px;
  color: #e79055;
  font-size: 10px;
  line-height: 1;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 8px 10px;
}

#wpforms-builder .wpforms-toolbar .wpforms-form-name {
  font-weight: 600;
}

#wpforms-builder .wpforms-toolbar .wpforms-right {
  align-items: center;
  display: flex;
  height: 100%;
  justify-content: flex-end;
  position: absolute;
  inset-inline-end: 0;
  top: 0;
}

#wpforms-builder .wpforms-toolbar .wpforms-right .wpforms-btn-toolbar {
  font-size: 16px;
  font-weight: 600;
  height: 42px;
  line-height: 40px;
  margin-inline-end: 20px;
  padding: 0 15px;
  text-decoration: none;
  text-transform: capitalize;
}

#wpforms-builder .wpforms-toolbar .wpforms-right .wpforms-btn-toolbar i {
  color: #a6a6a6;
  margin-inline-end: 8px;
}

#wpforms-builder .wpforms-toolbar #wpforms-help,
#wpforms-builder .wpforms-toolbar #wpforms-context-menu-container {
  background-color: transparent;
  border: none;
  padding: 0;
}

#wpforms-builder .wpforms-toolbar #wpforms-help i.fa,
#wpforms-builder .wpforms-toolbar #wpforms-context-menu-container i.fa {
  margin-right: 0 !important;
}

#wpforms-builder .wpforms-toolbar #wpforms-help svg,
#wpforms-builder .wpforms-toolbar #wpforms-context-menu-container svg {
  fill: #a6a6a6;
  margin-top: 13px;
}

#wpforms-builder .wpforms-toolbar #wpforms-help:hover > i.fa,
#wpforms-builder .wpforms-toolbar #wpforms-help:hover > svg, #wpforms-builder .wpforms-toolbar #wpforms-help.wpforms-context-menu-active > i.fa,
#wpforms-builder .wpforms-toolbar #wpforms-help.wpforms-context-menu-active > svg,
#wpforms-builder .wpforms-toolbar #wpforms-context-menu-container:hover > i.fa,
#wpforms-builder .wpforms-toolbar #wpforms-context-menu-container:hover > svg,
#wpforms-builder .wpforms-toolbar #wpforms-context-menu-container.wpforms-context-menu-active > i.fa,
#wpforms-builder .wpforms-toolbar #wpforms-context-menu-container.wpforms-context-menu-active > svg {
  color: #444444;
  fill: #444444;
}

#wpforms-builder .wpforms-toolbar #wpforms-preview-btn:not(.wpforms-alone) {
  border-end-end-radius: 0;
  border-start-end-radius: 0;
  margin-inline-end: -1px;
}

#wpforms-builder .wpforms-toolbar #wpforms-embed {
  border-end-start-radius: 0;
  border-start-start-radius: 0;
}

#wpforms-builder .wpforms-toolbar #wpforms-embed.wpforms-btn-light-grey-disabled {
  cursor: default;
}

#wpforms-builder .wpforms-toolbar #wpforms-embed.wpforms-btn-light-grey-disabled:hover {
  background-color: #f8f8f8;
  border-color: #cccccc;
  color: #777777;
}

#wpforms-builder .wpforms-toolbar #wpforms-embed.wpforms-btn-light-grey-disabled > * {
  opacity: .35;
}

#wpforms-builder .wpforms-toolbar #wpforms-save i {
  color: rgba(255, 255, 255, 0.65);
}

#wpforms-builder .wpforms-toolbar #wpforms-exit {
  align-items: center;
  background-color: #ffffff;
  border-bottom: none;
  border-inline-start: 1px solid #dddddd;
  border-inline-end: none;
  border-top: none;
  color: #a6a6a6;
  cursor: pointer;
  display: flex;
  font-size: 28px;
  height: 76px;
  justify-content: center;
  inset-inline-end: 0;
  text-decoration: none;
  top: 0;
  width: 66px;
}

#wpforms-builder .wpforms-toolbar #wpforms-exit:hover {
  background-color: #f8f8f8;
  color: #777777;
}

#wpforms-builder .wpforms-toolbar.empty .wpforms-center {
  display: none;
}

#wpforms-builder .wpforms-toolbar.empty .wpforms-right #wpforms-embed,
#wpforms-builder .wpforms-toolbar.empty .wpforms-right #wpforms-save,
#wpforms-builder .wpforms-toolbar.empty .wpforms-right div,
#wpforms-builder .wpforms-toolbar.empty .wpforms-right a {
  display: none;
}

.wpforms-panels-toggle {
  display: flex;
  flex-direction: column;
  background: #2d2d2d;
  color: #ffffff;
  min-height: calc( 100vh - 76px - var( --wpforms-admin-bar-height ));
  max-height: calc( 100vh - 76px - var( --wpforms-admin-bar-height ));
  inset-inline-start: 0;
  margin-top: 76px;
  margin-inline-end: -100px;
  overflow-x: hidden;
  overflow-y: auto;
  position: fixed;
  text-align: start;
  top: var(--wpforms-admin-bar-height);
  transform: translateZ(0);
  width: 95px;
  z-index: 15;
  transition-property: top;
  transition-duration: 0.25s;
  transition-timing-function: ease-out;
}

.wpforms-panels-toggle button {
  background: none;
  border: none;
  border-bottom: 1px solid #444444;
  cursor: pointer;
  display: block;
  margin: 0;
  padding: 11px;
  width: 100%;
}

.wpforms-panels-toggle button.active, .wpforms-panels-toggle button.active:hover {
  background-color: #e27730;
}

.wpforms-panels-toggle button.active .fa, .wpforms-panels-toggle button.active:hover .fa {
  color: #ffffff;
}

.wpforms-panels-toggle button:hover {
  background-color: #444444;
}

.wpforms-panels-toggle button .fa {
  color: #999999;
  display: block;
  font-size: 30px;
  margin: 0 auto 5px auto;
  text-align: center !important;
}

.wpforms-panels-toggle button .wpforms-loading-spinner {
  background-size: 26px 26px;
  background-position: 0;
  background-repeat: no-repeat;
  height: 26px;
  width: 26px;
  line-height: 26px;
  vertical-align: 0;
  margin: 2px auto 7px auto;
  display: block;
}

.wpforms-panels-toggle button span {
  color: #ffffff;
  display: block;
  font-size: 14px;
  line-height: 17px;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.wpforms-panel {
  display: none;
}

.wpforms-panel.active {
  display: block;
}

.wpforms-panel-full-content .wpforms-panel-content-wrap,
.wpforms-panel-sidebar-content .wpforms-panel-content-wrap {
  background-color: #6d6d6d;
  bottom: 0;
  inset-inline-start: 95px;
  overflow: auto;
  padding-block-start: 30px;
  padding-block-end: 30px;
  padding-inline-start: 30px;
  padding-inline-end: 30px;
  position: fixed;
  inset-inline-end: 0;
  top: calc( 76px + var( --wpforms-admin-bar-height ));
  z-index: 10;
  transition-property: top, inset-inline-start;
  transition-duration: 0.25s, 0.15s;
  transition-timing-function: ease-out, ease-in-out;
}

.wpforms-panel-full-content .wpforms-panel-content,
.wpforms-panel-sidebar-content .wpforms-panel-content {
  background-color: #ffffff;
  box-shadow: 0 5px 30px rgba(0, 0, 0, 0.2);
  min-height: 100%;
  padding: 30px;
  display: flex;
  flex-direction: column;
}

.wpforms-panel-full-content .wpforms-panel-content .wpforms-bottom,
.wpforms-panel-sidebar-content .wpforms-panel-content .wpforms-bottom {
  margin-bottom: 0;
}

.wpforms-panel-sidebar-content .wpforms-panel-sidebar-toggle-icon {
  align-items: center;
  background-image: url(../../images/builder/toggle-tab-bg.svg);
  background-size: 100% 58px;
  background-repeat: no-repeat;
  border-radius: 5px 5px 0 0;
  color: #86919e;
  display: flex;
  justify-content: left;
  font-size: 20px;
  height: 58px;
  inset-inline-start: 495px;
  cursor: pointer;
  position: absolute;
  top: calc( 50% + 24px);
  width: 15px;
  z-index: 15;
  transition-property: inset-inline-start, width;
  transition-duration: 0.15s;
  transition-timing-function: ease-in-out;
}

.wpforms-panel-sidebar-content .wpforms-panel-sidebar-toggle-icon:hover {
  color: #444444;
}

.wpforms-panel-sidebar-content .wpforms-panel-sidebar-toggle-icon:hover i {
  inset-inline-start: -3px;
}

.rtl .wpforms-panel-sidebar-content .wpforms-panel-sidebar-toggle-icon {
  transform: scale(-1);
}

.wpforms-panel-sidebar-content .wpforms-panel-sidebar-toggle-icon i {
  inset-inline-start: 0;
  position: relative;
  transition-property: transform, inset-inline-start;
  transition-duration: 0.15s;
  transition-timing-function: ease-in-out;
}

.wpforms-panel-sidebar-content .wpforms-panel-content-wrap {
  inset-inline-start: 495px;
}

.wpforms-panel-sidebar-content .wpforms-panel-sidebar {
  background-color: #ebf3fc;
  bottom: 0;
  inset-inline-start: 95px;
  overflow: auto;
  position: fixed;
  top: calc( 76px + var( --wpforms-admin-bar-height ));
  width: 400px;
}

#wpforms-panel-fields .wpforms-panel-sidebar-content.wpforms-panel-sidebar-closed .wpforms-panel-sidebar-toggle-vertical-line {
  position: absolute;
  border-inline-end: 3px solid #ebf3fb;
  top: calc( 76px + var( --wpforms-admin-bar-height ));
  height: 100%;
  inset-inline-start: 95px;
  z-index: 0;
  cursor: pointer;
  transition-property: z-index;
  transition-duration: 0.15s;
  transition-timing-function: ease-in-out;
}

#wpforms-panel-fields .wpforms-panel-sidebar-content.wpforms-panel-sidebar-closed .wpforms-panel-sidebar-toggle-vertical-line:hover {
  z-index: 15;
}

#wpforms-panel-fields .wpforms-panel-sidebar-content.wpforms-panel-sidebar-closed .wpforms-panel-sidebar-toggle-icon {
  justify-content: center;
  inset-inline-start: 95px;
}

#wpforms-panel-fields .wpforms-panel-sidebar-content.wpforms-panel-sidebar-closed .wpforms-panel-sidebar-toggle-icon i {
  transform: rotate(180deg);
}

#wpforms-panel-fields .wpforms-panel-sidebar-content.wpforms-panel-sidebar-closed .wpforms-panel-sidebar,
#wpforms-panel-fields .wpforms-panel-sidebar-content.wpforms-panel-sidebar-closed .wpforms-panel-sidebar .wpforms-tabs {
  inset-inline-start: -305px;
}

#wpforms-panel-fields .wpforms-panel-sidebar-content.wpforms-panel-sidebar-closed .wpforms-panel-content-wrap {
  inset-inline-start: 95px;
}

#wpforms-panel-fields .wpforms-panel-sidebar-content.wpforms-panel-sidebar-closed .wpforms-panel-sidebar-toggle:hover .wpforms-panel-sidebar-toggle-vertical-line {
  z-index: 15;
}

#wpforms-panel-fields .wpforms-panel-sidebar-content.wpforms-panel-sidebar-closed .wpforms-panel-sidebar-toggle:hover .wpforms-panel-sidebar-toggle-icon {
  width: 18px;
}

#wpforms-panel-fields .wpforms-panel-sidebar-content.wpforms-panel-sidebar-closed .wpforms-panel-sidebar-toggle:hover .wpforms-panel-sidebar-toggle-icon i {
  inset-inline-start: 0;
}

.wpforms-panel-field {
  margin-bottom: 20px;
  max-width: 450px;
}

.wpforms-panel-field.wide, .wpforms-panel-field.wpforms-panel-field-tinymce, .wpforms-panel-field.wpforms-panel-field-warning, .wpforms-panel-field[id$="-redirect-wrap"] {
  max-width: 800px;
}

.wpforms-panel-field input[type=text],
.wpforms-panel-field textarea {
  width: 100%;
}

.wpforms-panel-field input[type=number] {
  width: 20%;
}

.wpforms-panel-field select {
  max-width: 100%;
  width: 100%;
}

.wpforms-panel-field input[type=number] {
  height: auto;
}

.wpforms-panel-field .row {
  clear: both;
  display: block;
  margin-bottom: 6px;
  width: 100%;
}

.wpforms-panel-field label {
  display: block;
  font-size: 14px;
  font-weight: 400;
  margin: 0 0 10px 0;
  vertical-align: text-top;
}

.wpforms-panel-field label.inline {
  display: inline-block;
  font-weight: 400;
  margin: 0 0 0 10px;
}

.wpforms-panel-field p {
  font-size: 14px;
  margin: 0;
}

.wpforms-panel-field p.note {
  color: #777777;
  font-size: 12px;
  margin: 10px 0 0 0;
}

.wpforms-panel-field-anti-spam-disallowed-keys textarea {
  height: 200px;
}

.wpforms-panel-field .wpforms-panel-field-after {
  color: #777777;
  margin-left: 10px;
}

.wpforms-panel-field.wpforms-panel-field-select label .wpforms-badge {
  margin-left: 10px;
  vertical-align: middle;
}

.wpforms-panel-fields-group .wpforms-panel-fields-group-title {
  font-size: 16px;
  font-weight: 600;
  line-height: 19px;
  margin-bottom: 5px;
}

.wpforms-panel-fields-group .wpforms-panel-fields-group-description {
  color: #777777;
  font-size: 14px;
  line-height: 17px;
  margin-bottom: 20px;
}

.wpforms-panel-fields-group .wpforms-panel-fields-group-inner {
  margin-bottom: 20px;
}

.wpforms-panel-fields-group .wpforms-panel-fields-group-border-top {
  border-top: 1px solid #dddddd;
  margin-bottom: 20px;
  margin-top: 20px;
}

.wpforms-panel-fields-group .wpforms-panel-fields-group-border-bottom {
  border-top: 1px solid #dddddd;
  margin-bottom: 20px;
  margin-top: 20px;
}

.wpforms-panel-fields-group.wpforms-builder-notifications-advanced .wpforms-panel-fields-group-border-top {
  margin: 0 20px 10px;
}

.wpforms-panel-fields-group.wpforms-builder-notifications-advanced.unfoldable .wpforms-panel-fields-group-title {
  padding: 10px 20px;
}

.wpforms-panel-fields-group.wpforms-builder-notifications-advanced.unfoldable .wpforms-panel-fields-group-title i {
  right: 20px;
}

.wpforms-panel-fields-group.unfoldable .wpforms-panel-fields-group-border-top {
  margin-bottom: 10px;
}

.wpforms-panel-fields-group.unfoldable .wpforms-panel-fields-group-title {
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
  padding: 10px 0;
  position: relative;
}

.wpforms-panel-fields-group.unfoldable .wpforms-panel-fields-group-title i {
  color: #bbbbbb;
  position: absolute;
  right: 0;
  top: calc( 50% - 7px);
  transition-property: transform;
  transition-duration: 0.15s;
  transition-timing-function: ease-in;
}

.wpforms-panel-fields-group.unfoldable.opened .wpforms-panel-fields-group-title i {
  transform: rotate(90deg);
}

.wpforms-panel-content-section-general .wpforms-panel-fields-group.unfoldable .wpforms-panel-fields-group-title {
  margin-bottom: 0;
  padding-bottom: 0;
}

.wpforms-panel-content-section-general .wpforms-panel-fields-group.unfoldable .wpforms-panel-fields-group-title i {
  top: calc( 50% - 2px);
}

.wpforms-panel-content-section-general .wpforms-panel-fields-group.unfoldable.opened .wpforms-panel-fields-group-title {
  margin-bottom: 10px;
  padding: 10px 0;
}

.wpforms-panel-content-section-general .wpforms-panel-fields-group.unfoldable.opened .wpforms-panel-fields-group-title i {
  top: calc( 50% - 7px);
}

.rtl .wpforms-panel-fields-group.wpforms-builder-notifications-advanced .wpforms-panel-fields-group-title i {
  left: 20px;
  right: auto;
}

.rtl .wpforms-panel-fields-group .wpforms-panel-fields-group-title i {
  left: 0;
  right: auto;
  transform: rotate(180deg);
}

.rtl .wpforms-panel-field .wpforms-panel-field-after {
  margin-left: 0;
  margin-right: 10px;
}

.rtl .wpforms-panel-field label.inline {
  margin: 0 10px 0 0;
}

.wpforms-panel-sidebar-section {
  border-bottom: 1px solid #ced7e0;
  color: #444444;
  display: block;
  font-size: 16px;
  height: 75px;
  line-height: 18px;
  margin: 0;
  outline: 0;
  padding: 17px 20px 17px 20px;
  position: relative;
  text-decoration: none;
}

.wpforms-panel-sidebar-section:hover {
  background-color: #e0e8f0;
  color: #444444;
}

.wpforms-panel-sidebar-section.icon {
  padding-block: 28px;
  padding-inline-end: 20px;
  padding-inline-start: 79px;
}

.wpforms-panel-sidebar-section.education-modal {
  opacity: .5;
}

.wpforms-panel-sidebar-section.education-modal .wpforms-panel-sidebar-recommended {
  margin-inline-start: 5px;
}

.wpforms-panel-sidebar-section.default {
  display: none;
}

.wpforms-panel-sidebar-section.active, .wpforms-panel-sidebar-section.active:hover {
  background-color: #036aab;
  color: #ffffff;
}

.wpforms-panel-sidebar-section .fa-angle-right {
  color: #b0b6bd;
  float: inline-end;
  font-size: 16px;
  line-height: 16px;
  text-align: center;
  margin-inline-start: 10px;
}

.rtl .wpforms-panel-sidebar-section .fa-angle-right {
  transform: scale(-1, 1);
}

.wpforms-panel-sidebar-section .fa-check-circle-o {
  color: transparent;
  float: inline-end;
  font-size: 18px;
  margin-inline-end: 10px;
  margin-inline-start: 20px;
}

.wpforms-panel-sidebar-section .fa-check-circle-o:before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23B0B6BD'%3E%3Cdefs/%3E%3Cpath d='M8 .25a7.75 7.75 0 100 15.5A7.75 7.75 0 008 .25zm0 1.5a6.25 6.25 0 110 12.5 6.25 6.25 0 010-12.5zm4.38 4.1l-.72-.72c-.13-.16-.38-.16-.54 0L6.72 9.5 4.84 7.62a.36.36 0 00-.53 0l-.72.7c-.12.15-.12.4 0 .52l2.85 2.88c.15.15.37.15.53 0l5.4-5.35c.13-.15.13-.4 0-.53z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
}

.wpforms-panel-sidebar-section.active .fa-angle-right {
  color: #ffffff;
}

.wpforms-panel-sidebar-section.active .fa-check-circle-o:before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23fff'%3E%3Cdefs/%3E%3Cpath d='M8 .25a7.75 7.75 0 100 15.5A7.75 7.75 0 008 .25zm0 1.5a6.25 6.25 0 110 12.5 6.25 6.25 0 010-12.5zm4.38 4.1l-.72-.72c-.13-.16-.38-.16-.54 0L6.72 9.5 4.84 7.62a.36.36 0 00-.53 0l-.72.7c-.12.15-.12.4 0 .52l2.85 2.88c.15.15.37.15.53 0l5.4-5.35c.13-.15.13-.4 0-.53z'/%3E%3C/svg%3E");
}

.wpforms-panel-sidebar-section.active img {
  border-color: #005387;
}

.wpforms-panel-sidebar-section img {
  border: 1px solid #b0b6bd;
  border-radius: 4px;
  inset-inline-start: 20px;
  position: absolute;
  top: 15px;
  width: 44px;
}

.wpforms-panel-sidebar-section .wpforms-panel-sidebar-recommended {
  background-color: #00a32a;
  border-radius: 4px;
  color: #ffffff;
  font-size: 10px;
  font-weight: 700;
  line-height: 1;
  padding: 4px 5px;
  margin-inline-end: 10px;
  display: inline-block;
  position: relative;
  top: -2px;
  text-transform: uppercase;
}

.wpforms-panel-sidebar-section .wpforms-panel-sidebar-recommended i {
  opacity: 0.8;
}

.wpforms-panel-content-section {
  display: none;
}

.wpforms-panel-content-section .illustration {
  height: 260px;
  margin: 0 auto 30px auto;
  width: 350px;
}

.wpforms-panel-content-section .illustration.illustration-marketing {
  background: url("../../images/builder/illustration-marketing.svg") no-repeat center center;
  background-size: 313px 260px;
}

.wpforms-panel-content-section .illustration.illustration-payments {
  background: url("../../images/builder/illustration-payments.svg") no-repeat center center;
  background-size: 269px 255px;
}

.wpforms-panel-content-section-title {
  border-bottom: 1px solid #dddddd;
  font-size: 28px;
  font-weight: 600;
  line-height: 28px;
  margin-bottom: 30px;
  padding: 0 0 30px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.wpforms-panel-content-section-title button,
.wpforms-panel-content-section-title .button {
  background-color: #036aab;
  border: none;
  border-radius: 4px;
  box-shadow: none;
  color: #ffffff;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  height: auto;
  line-height: 17px;
  padding: 10px 15px;
  text-decoration: none;
  appearance: none;
  -webkit-appearance: none;
}

.wpforms-panel-content-section-title button:hover, .wpforms-panel-content-section-title button:focus,
.wpforms-panel-content-section-title .button:hover,
.wpforms-panel-content-section-title .button:focus {
  background-color: #215d8f;
  color: #ffffff;
  box-shadow: none;
}

.wpforms-panel-content-section-title button i,
.wpforms-panel-content-section-title .button i {
  margin: 0 10px 0 0;
}

.wpforms-panel-content-section-title > a > i:before {
  cursor: pointer;
}

.wpforms-panel-content-section-title .wpforms-help-tooltip {
  font-size: 24px !important;
  vertical-align: 1px;
}

.wpforms-panel-content-section-description {
  border-bottom: 1px solid #dddddd;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  margin-bottom: 20px;
  overflow: hidden;
  padding-bottom: 20px;
  position: relative;
  transition-property: all;
  transition-duration: 0.25s;
  transition-timing-function: ease-in-out;
}

.wpforms-panel-content-section-description.out {
  margin-bottom: 0;
  max-height: 0;
  opacity: 0;
  padding-bottom: 0;
}

.wpforms-panel-content-section-description p {
  line-height: 20px;
  margin-bottom: 0;
  margin-inline-end: 30px;
}

.wpforms-panel-content-section-description p:not(:first-of-type) {
  margin-top: 20px;
}

.wpforms-panel-content-section-description .wpforms-dismiss-button {
  position: absolute;
  inset-inline-end: -3px;
  top: -3px;
}

.wpforms-panel-content-section-default {
  position: relative;
}

.wpforms-panel-content-section-default::after {
  background-image: url("../../images/builder/default-arrow.svg");
  background-repeat: no-repeat;
  background-size: 97px 83px;
  content: "";
  height: 83px;
  inset-inline-start: 0;
  position: absolute;
  top: 0;
  width: 97px;
}

.rtl .wpforms-panel-content-section-default::after {
  transform: scale(-1, 1);
}

.wpforms-panel-content-section-default,
.wpforms-panel-content-section-info {
  align-content: center;
  min-height: calc( 100vh - var( --wpforms-admin-bar-height ) - 195px);
  padding: 50px 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.wpforms-panel-content-section-default h5,
.wpforms-panel-content-section-info h5 {
  color: #444444;
  font-size: 24px;
  font-weight: 600;
  line-height: normal;
  margin: 0 auto 10px auto;
  max-width: 600px;
}

.wpforms-panel-content-section-default p,
.wpforms-panel-content-section-info p {
  color: #777777;
  font-size: 16px;
  margin: 0 auto 20px auto;
  max-width: 600px;
}

.wpforms-panel-content-section-default p:last-of-type,
.wpforms-panel-content-section-info p:last-of-type {
  margin: 0 auto 0 auto;
}

.wpforms-panel-content-section-default .wpforms-btn,
.wpforms-panel-content-section-info .wpforms-btn {
  margin: 20px auto;
}

.wpforms-panel-field:last-child,
.wpforms-builder-settings-block:last-child {
  margin-bottom: 0 !important;
}

.toggle-unfoldable-cont,
.toggle-smart-tag-display {
  color: #86919e;
  float: inline-end;
  font-size: 12px;
  margin-inline-end: 2px;
  text-decoration: none;
  text-align: end;
  margin-inline-start: auto;
  max-width: 150px;
}

.toggle-unfoldable-cont:hover,
.toggle-smart-tag-display:hover {
  color: #777777;
}

.toggle-unfoldable-cont:hover span,
.toggle-smart-tag-display:hover span {
  text-decoration: underline;
}

.toggle-unfoldable-cont i,
.toggle-smart-tag-display i {
  font-size: 14px;
  margin: 0 10px;
}

.unfoldable-cont {
  display: none;
  margin: 0 2px 10px 1px;
}

.unfoldable-cont .heading {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 5px 0;
  display: flex;
  width: 100%;
  align-items: flex-end;
}

.unfoldable-cont .heading a {
  color: #86919e;
  display: inline-block;
  font-size: 12px;
  font-weight: 400;
  text-decoration: none;
  padding-inline-start: 3px;
  margin-inline-start: auto;
  text-align: end;
  line-height: 16px;
  margin-bottom: 3px;
}

.unfoldable-cont .heading a:hover {
  text-decoration: underline;
}

.unfoldable-cont ul {
  background-color: #ffffff;
  border: 1px solid #b0b6bd;
  border-radius: 4px;
  display: none;
  margin: 1px 1px 10px 1px !important;
  overflow: hidden;
  width: 100% !important;
}

.unfoldable-cont li {
  border-bottom: 1px solid #dddddd;
  list-style: none;
  margin: 0 !important;
  padding: 0 !important;
}

.unfoldable-cont li:last-child {
  border-bottom: none;
}

.unfoldable-cont li a {
  color: #215d8f;
  display: block;
  font-size: 14px;
  line-height: 17px;
  padding: 10px;
  text-decoration: none;
}

.unfoldable-cont li a:hover {
  background-color: #f8f8f8;
  color: #036aab;
  text-decoration: none;
}

.unfoldable-cont textarea {
  border-radius: 4px;
  color: #444444;
  display: block;
  font-size: 14px;
  height: 85px;
  line-height: 21px;
  padding: 8px 10px;
  width: 100% !important;
}

.unfoldable-cont button.wpforms-btn {
  margin-inline-start: 1px;
  margin-top: 10px;
}

.wpforms-field-map-table {
  margin: 0 0 10px 0;
  max-width: 1040px;
}

.wpforms-field-map-table h3 {
  color: #444444;
  margin: 0 0 8px 0;
}

.wpforms-field-map-table table {
  margin: 0 -2px;
  width: 100%;
}

.wpforms-field-map-table table td {
  padding: 0 10px 8px 0;
}

.wpforms-field-map-table table td select,
.wpforms-field-map-table table td input:not([type=checkbox]):not([type=radio]) {
  max-width: 100%;
  width: 100% !important;
}

.wpforms-field-map-table table td.key {
  width: 50%;
}

.wpforms-field-map-table table td.field {
  width: calc( 50% - 80px);
}

.wpforms-field-map-table table td.actions {
  width: 40px;
}

.wpforms-field-map-table table tr td:last-child {
  padding-right: 0;
}

.wpforms-field-map-table .actions i {
  display: inline-block;
  font-size: 18px;
}

.wpforms-field-map-table .actions .add {
  margin-right: 5px;
}

.wpforms-field-map-table .actions .remove {
  color: #d63638;
}

.wpforms-field-map-table .actions .remove:hover {
  color: #ee5c5c;
}

.wpforms-field-map-table tr:first-of-type .remove {
  display: none;
}

.wpforms-builder-dropdown-list {
  position: absolute;
  display: flex;
  flex-direction: column;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  z-index: 100;
  width: 370px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #777777;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.15);
  visibility: visible;
  overflow: hidden;
  transition: visibility 0.05s, opacity 0.05s ease-in;
}

.wpforms-builder-dropdown-list.closed {
  opacity: 0;
  visibility: hidden;
}

.wpforms-builder-dropdown-list .title {
  color: #444444;
  background: #f8f8f8;
  border-bottom: 1px solid #dddddd;
  padding: 10px 12px;
  font-weight: 500;
  text-decoration: none;
  cursor: default;
}

.wpforms-builder-dropdown-list ul {
  display: flex;
  flex-direction: column;
  max-height: 170px;
  overflow-x: hidden;
  overflow-y: auto;
}

.wpforms-builder-dropdown-list ul li {
  display: flex;
  flex-direction: row;
  flex-shrink: 0;
  gap: 10px;
  justify-content: space-between;
  padding: 10px 12px;
  background: #ffffff;
  margin: 0;
  border-bottom: 1px solid #eeeeee;
  cursor: pointer;
  color: #777777;
}

.wpforms-builder-dropdown-list ul li:hover {
  color: #ffffff;
  background: #036aab;
}

.wpforms-builder-dropdown-list ul li:hover .grey {
  color: #ffffff;
}

.wpforms-builder-dropdown-list ul li .grey {
  color: #777777;
}

.wpforms-builder-dropdown-list .wpforms-builder-dropdown-list-search-container {
  position: relative;
  padding: 10px;
  border-bottom: 1px solid #dddddd;
}

.wpforms-builder-dropdown-list .wpforms-builder-dropdown-list-search-container input {
  width: 100%;
}

.wpforms-builder-dropdown-list .wpforms-builder-dropdown-list-search-container input::-webkit-search-cancel-button {
  -webkit-appearance: none;
  appearance: none;
}

.wpforms-builder-dropdown-list .wpforms-builder-dropdown-list-search-container .wpforms-builder-dropdown-list-search-close {
  display: none;
  position: absolute;
  inset-inline-end: 20px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #bbbbbb;
  font-size: 16px;
}

.wpforms-builder-dropdown-list .wpforms-builder-dropdown-list-search-container .wpforms-builder-dropdown-list-search-close.active {
  display: block;
}

.wpforms-builder-dropdown-list .wpforms-builder-dropdown-list-search-container .wpforms-builder-dropdown-list-search-close:hover {
  color: #86919e;
}

.wpforms-builder-dropdown-list .wpforms-no-results {
  display: none;
  padding: 10px;
  color: #777777;
}

.wpforms-field-option-row .wpforms-builder-dropdown-list {
  left: -5px;
  width: auto;
  right: -5px;
}
