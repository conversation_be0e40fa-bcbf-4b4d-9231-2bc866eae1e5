/*! For license information please see jetpack-external-media-import-page.js.LICENSE.txt */
(()=>{var e={8137:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n,a=r(2467);function s(){this.intervals={},this.monitorInterval=null,this.windowInstance=null,this.onMessage=e=>{e.source===this.windowInstance&&this.emit("message",e.data)}}n=s.prototype,Object.assign(n,a.EventEmitter.prototype),n.emitChange=function(){this.emit("change")},n.off=n.removeListener,s.prototype.open=function(e,t,r){return t=t||Date.now(),this.windowInstance=window.open(e,t,r),this.startMonitoring(t,this.windowInstance),window.addEventListener("message",this.onMessage,!1),this},s.prototype.getScreenCenterSpecs=function(e,t){const r=void 0!==window.screenTop?window.screenTop:window.screenY,n=void 0!==window.screenLeft?window.screenLeft:window.screenX;return["width="+e,"height="+t,"top="+(r+window.innerHeight/2-t/2),"left="+(n+window.innerWidth/2-e/2)].join()},s.prototype.isOpen=function(e){let t=!1;try{t=this.intervals[e]&&this.intervals[e].closed}catch(e){}return!t},s.prototype.checkStatus=function(){for(const e in this.intervals)this.intervals.hasOwnProperty(e)&&!this.isOpen(e)&&(this.emit("close",e),delete this.intervals[e]);0===Object.keys(this.intervals).length&&(clearInterval(this.monitorInterval),delete this.monitorInterval,window.removeEventListener("message",this.onMessage))},s.prototype.startMonitoring=function(e,t){this.monitorInterval||(this.monitorInterval=setInterval(this.checkStatus.bind(this),100)),this.intervals[e]=t};const i=s},9754:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(8137);const a=(e,t)=>{const r=new n.A;let a;r.open(e,null,"toolbar=0,location=0,status=0,menubar=0,"+r.getScreenCenterSpecs(780,700)),r.once("close",(()=>{const e={};a&&a.keyring_id&&(e.keyring_id=Number(a.keyring_id),e.id_token=a.id_token,e.user=a.user),t(e)})),r.on("message",(e=>a=e))}},7675:(e,t,r)=>{"use strict";r.d(t,{y:()=>c});var n=r(6416),a=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(n=Object.getOwnPropertySymbols(e);a<n.length;a++)t.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]])}return r};const s="text/event-stream",i=1e3,o="last-event-id";function c(e,t){var{signal:r,headers:c,onopen:u,onmessage:d,onclose:p,onerror:h,openWhenHidden:m,fetch:f}=t,g=a(t,["signal","headers","onopen","onmessage","onclose","onerror","openWhenHidden","fetch"]);return new Promise(((t,a)=>{const _=Object.assign({},c);let k;function y(){k.abort(),document.hidden||w()}_.accept||(_.accept=s),m||document.addEventListener("visibilitychange",y);let v=i,E=0;function b(){document.removeEventListener("visibilitychange",y),window.clearTimeout(E),k.abort()}null==r||r.addEventListener("abort",(()=>{b(),t()}));const A=null!=f?f:window.fetch,C=null!=u?u:l;async function w(){var r;k=new AbortController;try{const r=await A(e,Object.assign(Object.assign({},g),{headers:_,signal:k.signal}));await C(r),await(0,n.q5)(r.body,(0,n.iv)((0,n.VL)((e=>{e?_[o]=e:delete _[o]}),(e=>{v=e}),d))),null==p||p(),b(),t()}catch(e){if(!k.signal.aborted)try{const t=null!==(r=null==h?void 0:h(e))&&void 0!==r?r:v;window.clearTimeout(E),E=window.setTimeout(w,t)}catch(e){b(),a(e)}}}w()}))}function l(e){const t=e.headers.get("content-type");if(!(null==t?void 0:t.startsWith(s)))throw new Error(`Expected content-type to be ${s}, Actual: ${t}`)}},6416:(e,t,r)=>{"use strict";async function n(e,t){const r=e.getReader();let n;for(;!(n=await r.read()).done;)t(n.value)}function a(e){let t,r,n,a=!1;return function(s){void 0===t?(t=s,r=0,n=-1):t=function(e,t){const r=new Uint8Array(e.length+t.length);return r.set(e),r.set(t,e.length),r}(t,s);const i=t.length;let o=0;for(;r<i;){a&&(10===t[r]&&(o=++r),a=!1);let s=-1;for(;r<i&&-1===s;++r)switch(t[r]){case 58:-1===n&&(n=r-o);break;case 13:a=!0;case 10:s=r}if(-1===s)break;e(t.subarray(o,s),n),o=r,n=-1}o===i?t=void 0:0!==o&&(t=t.subarray(o),r-=o)}}function s(e,t,r){let n=i();const a=new TextDecoder;return function(s,o){if(0===s.length)null==r||r(n),n=i();else if(o>0){const r=a.decode(s.subarray(0,o)),i=o+(32===s[o+1]?2:1),c=a.decode(s.subarray(i));switch(r){case"data":n.data=n.data?n.data+"\n"+c:c;break;case"event":n.event=c;break;case"id":e(n.id=c);break;case"retry":const r=parseInt(c,10);isNaN(r)||t(n.retry=r)}}}}function i(){return{data:"",event:"",id:"",retry:void 0}}r.d(t,{VL:()=>s,iv:()=>a,q5:()=>n})},1113:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(8468);const a=(0,n.forwardRef)((function({icon:e,size:t=24,...r},a){return(0,n.cloneElement)(e,{width:t,height:t,...r,ref:a})}))},3883:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(5573),a=r(790);const s=(0,a.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(n.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})})},8888:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(5573),a=r(790);const s=(0,a.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(n.Path,{d:"M14.6 7l-1.2-1L8 12l5.4 6 1.2-1-4.6-5z"})})},9115:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(5573),a=r(790);const s=(0,a.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(n.Path,{d:"M10.6 6L9.4 7l4.6 5-4.6 5 1.2 1 5.4-6z"})})},991:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(5573),a=r(790);const s=(0,a.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(n.Path,{d:"m13.06 12 6.47-6.47-1.06-1.06L12 10.94 5.53 4.47 4.47 5.53 10.94 12l-6.47 6.47 1.06 1.06L12 13.06l6.47 6.47 1.06-1.06L13.06 12Z"})})},3512:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(5573),a=r(790);const s=(0,a.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(n.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})})},9783:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(5573),a=r(790);const s=(0,a.jsx)(n.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M5.5 12a6.5 6.5 0 1 0 13 0 6.5 6.5 0 0 0-13 0ZM12 4a8 8 0 1 0 0 16 8 8 0 0 0 0-16Zm.75 4v1.5h-1.5V8h1.5Zm0 8v-5h-1.5v5h1.5Z"})})},347:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(5573),a=r(790);const s=(0,a.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(n.Path,{d:"M19.8 4h-1.5l1 8h1.5l-1-8ZM17 5.8c-.1-1-1-1.8-2-1.8H6.8c-.9 0-1.7.6-1.9 1.4l-1.8 6C2.7 12.7 3.7 14 5 14h4.4l-.8 3.6c-.3 1.3.7 2.4 1.9 2.4h.2c.6 0 1.2-.3 1.6-.8l5-6.6c.3-.4.5-.9.4-1.5L17 5.7Zm-.9 5.9-5 6.6c0 .1-.2.2-.4.2h-.2c-.3 0-.6-.3-.5-.6l.8-3.6c.1-.4 0-.9-.3-1.3s-.7-.6-1.2-.6H4.9c-.3 0-.6-.3-.5-.6l1.8-6c0-.2.3-.4.5-.4h8.2c.3 0 .5.2.5.4l.7 5.4v.4Z"})})},7598:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(5573),a=r(790);const s=(0,a.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(n.Path,{d:"m3 12 1 8h1.5l-1-8H3Zm15.8-2h-4.4l.8-3.6c.3-1.3-.7-2.4-1.9-2.4h-.2c-.6 0-1.2.3-1.6.8l-5 6.6c-.3.4-.4.8-.4 1.2v.2l.7 5.4v.2c.2.9 1 1.5 1.9 1.5h8.2c.9 0 1.7-.6 1.9-1.4l1.8-6c.4-1.3-.6-2.6-1.9-2.6Zm.5 2.1-1.8 6c0 .2-.3.4-.5.4H8.8c-.3 0-.5-.2-.5-.4l-.7-5.4v-.4l5-6.6c0-.1.2-.2.4-.2h.2c.3 0 .6.3.5.6l-.8 3.6c-.1.4 0 .9.3 1.3s.7.6 1.2.6h4.4c.3 0 .6.3.5.6Z"})})},6941:(e,t,r)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const r="color: "+this.color;t.splice(1,0,r,"color: inherit");let n=0,a=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(n++,"%c"===e&&(a=n))})),t.splice(a,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=r(3212)(t);const{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},3212:(e,t,r)=>{e.exports=function(e){function t(e){let r,a,s,i=null;function o(...e){if(!o.enabled)return;const n=o,a=Number(new Date),s=a-(r||a);n.diff=s,n.prev=r,n.curr=a,r=a,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((r,a)=>{if("%%"===r)return"%";i++;const s=t.formatters[a];if("function"==typeof s){const t=e[i];r=s.call(n,t),e.splice(i,1),i--}return r})),t.formatArgs.call(n,e);(n.log||t.log).apply(n,e)}return o.namespace=e,o.useColors=t.useColors(),o.color=t.selectColor(e),o.extend=n,o.destroy=t.destroy,Object.defineProperty(o,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==i?i:(a!==t.namespaces&&(a=t.namespaces,s=t.enabled(e)),s),set:e=>{i=e}}),"function"==typeof t.init&&t.init(o),o}function n(e,r){const n=t(this.namespace+(void 0===r?":":r)+e);return n.log=this.log,n}function a(e,t){let r=0,n=0,a=-1,s=0;for(;r<e.length;)if(n<t.length&&(t[n]===e[r]||"*"===t[n]))"*"===t[n]?(a=n,s=r,n++):(r++,n++);else{if(-1===a)return!1;n=a+1,s++,r=s}for(;n<t.length&&"*"===t[n];)n++;return n===t.length}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names,...t.skips.map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const r=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of r)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const r of t.skips)if(a(e,r))return!1;for(const r of t.names)if(a(e,r))return!0;return!1},t.humanize=r(4997),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((r=>{t[r]=e[r]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t),r|=0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}},2467:e=>{"use strict";var t,r="object"==typeof Reflect?Reflect:null,n=r&&"function"==typeof r.apply?r.apply:function(e,t,r){return Function.prototype.apply.call(e,t,r)};t=r&&"function"==typeof r.ownKeys?r.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var a=Number.isNaN||function(e){return e!=e};function s(){s.init.call(this)}e.exports=s,e.exports.once=function(e,t){return new Promise((function(r,n){function a(r){e.removeListener(t,s),n(r)}function s(){"function"==typeof e.removeListener&&e.removeListener("error",a),r([].slice.call(arguments))}f(e,t,s,{once:!0}),"error"!==t&&function(e,t,r){"function"==typeof e.on&&f(e,"error",t,r)}(e,a,{once:!0})}))},s.EventEmitter=s,s.prototype._events=void 0,s.prototype._eventsCount=0,s.prototype._maxListeners=void 0;var i=10;function o(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function c(e){return void 0===e._maxListeners?s.defaultMaxListeners:e._maxListeners}function l(e,t,r,n){var a,s,i;if(o(r),void 0===(s=e._events)?(s=e._events=Object.create(null),e._eventsCount=0):(void 0!==s.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),s=e._events),i=s[t]),void 0===i)i=s[t]=r,++e._eventsCount;else if("function"==typeof i?i=s[t]=n?[r,i]:[i,r]:n?i.unshift(r):i.push(r),(a=c(e))>0&&i.length>a&&!i.warned){i.warned=!0;var l=new Error("Possible EventEmitter memory leak detected. "+i.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");l.name="MaxListenersExceededWarning",l.emitter=e,l.type=t,l.count=i.length,function(e){console&&console.warn&&console.warn(e)}(l)}return e}function u(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function d(e,t,r){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},a=u.bind(n);return a.listener=r,n.wrapFn=a,a}function p(e,t,r){var n=e._events;if(void 0===n)return[];var a=n[t];return void 0===a?[]:"function"==typeof a?r?[a.listener||a]:[a]:r?function(e){for(var t=new Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(a):m(a,a.length)}function h(e){var t=this._events;if(void 0!==t){var r=t[e];if("function"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function m(e,t){for(var r=new Array(t),n=0;n<t;++n)r[n]=e[n];return r}function f(e,t,r,n){if("function"==typeof e.on)n.once?e.once(t,r):e.on(t,r);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function a(s){n.once&&e.removeEventListener(t,a),r(s)}))}}Object.defineProperty(s,"defaultMaxListeners",{enumerable:!0,get:function(){return i},set:function(e){if("number"!=typeof e||e<0||a(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");i=e}}),s.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},s.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||a(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},s.prototype.getMaxListeners=function(){return c(this)},s.prototype.emit=function(e){for(var t=[],r=1;r<arguments.length;r++)t.push(arguments[r]);var a="error"===e,s=this._events;if(void 0!==s)a=a&&void 0===s.error;else if(!a)return!1;if(a){var i;if(t.length>0&&(i=t[0]),i instanceof Error)throw i;var o=new Error("Unhandled error."+(i?" ("+i.message+")":""));throw o.context=i,o}var c=s[e];if(void 0===c)return!1;if("function"==typeof c)n(c,this,t);else{var l=c.length,u=m(c,l);for(r=0;r<l;++r)n(u[r],this,t)}return!0},s.prototype.addListener=function(e,t){return l(this,e,t,!1)},s.prototype.on=s.prototype.addListener,s.prototype.prependListener=function(e,t){return l(this,e,t,!0)},s.prototype.once=function(e,t){return o(t),this.on(e,d(this,e,t)),this},s.prototype.prependOnceListener=function(e,t){return o(t),this.prependListener(e,d(this,e,t)),this},s.prototype.removeListener=function(e,t){var r,n,a,s,i;if(o(t),void 0===(n=this._events))return this;if(void 0===(r=n[e]))return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete n[e],n.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(a=-1,s=r.length-1;s>=0;s--)if(r[s]===t||r[s].listener===t){i=r[s].listener,a=s;break}if(a<0)return this;0===a?r.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(r,a),1===r.length&&(n[e]=r[0]),void 0!==n.removeListener&&this.emit("removeListener",e,i||t)}return this},s.prototype.off=s.prototype.removeListener,s.prototype.removeAllListeners=function(e){var t,r,n;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0===arguments.length){var a,s=Object.keys(r);for(n=0;n<s.length;++n)"removeListener"!==(a=s[n])&&this.removeAllListeners(a);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(void 0!==t)for(n=t.length-1;n>=0;n--)this.removeListener(e,t[n]);return this},s.prototype.listeners=function(e){return p(this,e,!0)},s.prototype.rawListeners=function(e){return p(this,e,!1)},s.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):h.call(e,t)},s.prototype.listenerCount=h,s.prototype.eventNames=function(){return this._eventsCount>0?t(this._events):[]}},4997:e=>{var t=1e3,r=60*t,n=60*r,a=24*n,s=7*a,i=365.25*a;function o(e,t,r,n){var a=t>=1.5*r;return Math.round(e/r)+" "+n+(a?"s":"")}e.exports=function(e,c){c=c||{};var l=typeof e;if("string"===l&&e.length>0)return function(e){if((e=String(e)).length>100)return;var o=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!o)return;var c=parseFloat(o[1]);switch((o[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return c*i;case"weeks":case"week":case"w":return c*s;case"days":case"day":case"d":return c*a;case"hours":case"hour":case"hrs":case"hr":case"h":return c*n;case"minutes":case"minute":case"mins":case"min":case"m":return c*r;case"seconds":case"second":case"secs":case"sec":case"s":return c*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}(e);if("number"===l&&isFinite(e))return c.long?function(e){var s=Math.abs(e);if(s>=a)return o(e,s,a,"day");if(s>=n)return o(e,s,n,"hour");if(s>=r)return o(e,s,r,"minute");if(s>=t)return o(e,s,t,"second");return e+" ms"}(e):function(e){var s=Math.abs(e);if(s>=a)return Math.round(e/a)+"d";if(s>=n)return Math.round(e/n)+"h";if(s>=r)return Math.round(e/r)+"m";if(s>=t)return Math.round(e/t)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},7072:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>k});const n=2147483647,a=36,s=/^xn--/,i=/[^\0-\x7F]/,o=/[\x2E\u3002\uFF0E\uFF61]/g,c={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},l=Math.floor,u=String.fromCharCode;function d(e){throw new RangeError(c[e])}function p(e,t){const r=e.split("@");let n="";r.length>1&&(n=r[0]+"@",e=r[1]);const a=function(e,t){const r=[];let n=e.length;for(;n--;)r[n]=t(e[n]);return r}((e=e.replace(o,".")).split("."),t).join(".");return n+a}function h(e){const t=[];let r=0;const n=e.length;for(;r<n;){const a=e.charCodeAt(r++);if(a>=55296&&a<=56319&&r<n){const n=e.charCodeAt(r++);56320==(64512&n)?t.push(((1023&a)<<10)+(1023&n)+65536):(t.push(a),r--)}else t.push(a)}return t}const m=function(e,t){return e+22+75*(e<26)-((0!=t)<<5)},f=function(e,t,r){let n=0;for(e=r?l(e/700):e>>1,e+=l(e/t);e>455;n+=a)e=l(e/35);return l(n+36*e/(e+38))},g=function(e){const t=[],r=e.length;let s=0,i=128,o=72,c=e.lastIndexOf("-");c<0&&(c=0);for(let r=0;r<c;++r)e.charCodeAt(r)>=128&&d("not-basic"),t.push(e.charCodeAt(r));for(let p=c>0?c+1:0;p<r;){const c=s;for(let t=1,i=a;;i+=a){p>=r&&d("invalid-input");const c=(u=e.charCodeAt(p++))>=48&&u<58?u-48+26:u>=65&&u<91?u-65:u>=97&&u<123?u-97:a;c>=a&&d("invalid-input"),c>l((n-s)/t)&&d("overflow"),s+=c*t;const h=i<=o?1:i>=o+26?26:i-o;if(c<h)break;const m=a-h;t>l(n/m)&&d("overflow"),t*=m}const h=t.length+1;o=f(s-c,h,0==c),l(s/h)>n-i&&d("overflow"),i+=l(s/h),s%=h,t.splice(s++,0,i)}var u;return String.fromCodePoint(...t)},_=function(e){const t=[],r=(e=h(e)).length;let s=128,i=0,o=72;for(const r of e)r<128&&t.push(u(r));const c=t.length;let p=c;for(c&&t.push("-");p<r;){let r=n;for(const t of e)t>=s&&t<r&&(r=t);const h=p+1;r-s>l((n-i)/h)&&d("overflow"),i+=(r-s)*h,s=r;for(const r of e)if(r<s&&++i>n&&d("overflow"),r===s){let e=i;for(let r=a;;r+=a){const n=r<=o?1:r>=o+26?26:r-o;if(e<n)break;const s=e-n,i=a-n;t.push(u(m(n+s%i,0))),e=l(s/i)}t.push(u(m(e,0))),o=f(i,h,p===c),i=0,++p}++i,++s}return t.join("")},k={version:"2.3.1",ucs2:{decode:h,encode:e=>String.fromCodePoint(...e)},decode:g,encode:_,toASCII:function(e){return p(e,(function(e){return i.test(e)?"xn--"+_(e):e}))},toUnicode:function(e){return p(e,(function(e){return s.test(e)?g(e.slice(4).toLowerCase()):e}))}}},8950:(e,t,r)=>{"use strict";var n=r(5795);t.createRoot=n.createRoot,t.hydrateRoot=n.hydrateRoot},8931:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(1455),a=r.n(n);const s="default"in a()?a().default:a()},7286:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(6941),a=r.n(n),s=r(3664);const i=a()("jetpack-ai-client:ask-question");async function o(e,{postId:t=null,fromCache:r=!1,feature:n,functions:a,model:o}={}){return i("Asking question: %o. options: %o",e,{postId:t,fromCache:r,feature:n,functions:a,model:o}),new s.A({question:e,options:{postId:t,feature:n,fromCache:r,functions:a,model:o}})}},1439:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(6941),a=r.n(n),s=r(1391);const i=a()("jetpack-ai-client:ask-question-sync");async function o(e,t={}){i("Asking question with no streaming: %o. options: %o",e,t);let r=null;try{r=(await(0,s.A)()).token}catch(e){return i("Error getting token: %o",e),Promise.reject(e)}const n={...Array.isArray(e)?{messages:e}:{question:e},...t,stream:!1},a={Authorization:`Bearer ${r}`,"Content-Type":"application/json"};try{const e=await fetch("https://public-api.wordpress.com/wpcom/v2/jetpack-ai-query",{method:"POST",headers:a,body:JSON.stringify(n)}).then((e=>e.json()));return e?.data?.status&&e?.data?.status>200?(i("Error generating prompt: %o",e),Promise.reject(e)):e.choices?.[0]?.message?.content}catch(e){return i("Error asking question: %o",e),Promise.reject(e)}}},7745:(e,t,r)=>{"use strict";var n=r(6941),a=r.n(n);r(1391);a()("jetpack-ai-client:audio-transcription")},4867:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(5985),a=r(3984),s=r(8344);async function i(e){if(!0!==(0,n.FB)("ai-use-chrome-ai-sometimes").available)return!1;const t={content:"",language:""};let r="",i=null,o=null;if(Array.isArray(e))for(let n=0;n<e.length;n++){const a=e[n];if(a.content&&(t.content=a.content),!("context"in a))continue;const s=a.context;s.type&&(r=s.type),s.language&&(t.language=s.language),s.content&&(t.content=s.content),s.tone&&(i=s.tone),s.words&&(o=s.words)}if(r.startsWith("ai-assistant-change-language")){const[e]=t.language.split(" ");if(!("translation"in self)||!self.translation.createTranslator||!self.translation.canTranslate)return!1;const r={sourceLanguage:"en",targetLanguage:e};if("ai"in self&&self.ai.languageDetector){const e=await self.ai.languageDetector.create(),n=await e.detect(t.content);for(const e of n)if(e.confidence>.75){r.sourceLanguage=e.detectedLanguage;break}}if("no"===await self.translation.canTranslate(r))return!1;return new s.A({content:t.content,promptType:a.Gm,options:r})}if(r.startsWith("ai-content-lens")){const e={tone:i,wordCount:o};return new s.A({content:t.content,promptType:a.UA,options:e})}return!1}},5209:(e,t,r)=>{"use strict";r(4867),r(8344)},8344:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(3984),a=r(6219),s=r(5166),i=r(8836);class o extends EventTarget{constructor(e){super(),this.fullMessage="",this.fullFunctionCall={name:"",arguments:""},this.isPromptClear=!1,this.controller=new AbortController,this.initSource(e)}initSource({content:e,promptType:t,options:r={}}){t===n.Gm&&this.translate(e,r.targetLanguage,r.sourceLanguage),t===n.UA&&this.summarize(e,r.tone,r.wordCount)}async initEventSource(){}close(){}checkForUnclearPrompt(){}processEvent(e){let t;try{t=JSON.parse(e.data)}catch(e){return void this.processErrorEvent(e)}"translation"!==e.event&&"summary"!==e.event||this.dispatchEvent(new CustomEvent("suggestion",{detail:t.message})),t.complete&&this.dispatchEvent(new CustomEvent("done",{detail:{message:t.message,source:"chromeAI"}}))}processErrorEvent(e){this.dispatchEvent(new CustomEvent(i.mA,{detail:e})),this.dispatchEvent(new CustomEvent(i.zn,{detail:(0,a.fC)(i.mA)}))}async translate(e,t,r=""){if(!("translation"in self))return;const n=await self.translation.createTranslator({sourceLanguage:r,targetLanguage:t});if(n)try{const t=await n.translate((0,s.Hh)({content:e}));this.processEvent({id:"",event:"translation",data:JSON.stringify({message:(0,s.rh)({content:t}),complete:!0})})}catch(e){this.processErrorEvent(e)}}getSummarizerOptions(e,t){let r=`The summary you write should contain approximately ${t??50} words long. Strive for precision in word count without compromising clarity and significance`;e&&(r+=`\n - Write with a ${e} tone.\n`);return{sharedContext:r,type:"teaser",format:"plain-text",length:"medium"}}async summarize(e,t,r){if(!("ai"in self)||!("summarizer"in self.ai))return;const n=(await self.ai.summarizer.capabilities()).available;if("no"===n)return;const a=this.getSummarizerOptions(t,r),s=await self.ai.summarizer.create(a);"after-download"===n&&await s.ready;try{const r=`Write with a ${t} tone.`,n=await s.summarize(e,{context:r});this.processEvent({id:"",event:"summary",data:JSON.stringify({message:n,complete:!0})})}catch(e){this.processErrorEvent(e)}}}},629:(e,t,r)=>{"use strict";r(4715),r(1609),r(6754)},1417:(e,t,r)=>{"use strict";r(6427),r(9491),r(8468),r(7723);var n=r(6941),a=r.n(n);r(1609),r(3098),r(629);a()("jetpack-ai-client:block-ai-control")},61:(e,t,r)=>{"use strict";r(6427),r(9491),r(8468),r(7723),r(1609),r(3098),r(629)},2299:(e,t,r)=>{"use strict";r(629),r(1417),r(61)},1497:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(5985),a=r(6427),s=r(8468),i=r(7723),o=r(7598),c=r(347),l=r(3022);const __=i.__;function u({disabled:e=!1,iconSize:t=24,ratedItem:r="",feature:i="",savedRatings:u={},options:d={},onRate:p}){if(!function(e){return!0===(0,n.FB)(e).available}("ai-response-feedback"))return null;const[h,m]=(0,s.useState)({}),{tracks:f}=(0,n.st)();(0,s.useEffect)((()=>{const e={...u,...h};JSON.stringify(e)!==JSON.stringify(h)&&m(e)}),[u]);const g=e=>!!h[r]&&h[r]===e,_=e=>{const t=e?"thumbs-up":"thumbs-down";g(t)||(m({...h,[r]:t}),p?.(t),f.recordEvent("jetpack_ai_feedback",{type:i,rating:t,media_library_id:d.mediaLibraryId||null,prompt:d.prompt||null,revised_prompt:d.revisedPrompt||null,block:d.block||null}))};return React.createElement("div",{className:"ai-assistant-feedback__selection"},React.createElement(a.Tooltip,{text:__("I like this","jetpack-external-media")},React.createElement(a.Button,{disabled:e,icon:o.A,onClick:()=>_(!0),iconSize:t,showTooltip:!1,className:(0,l.A)({"ai-assistant-feedback__thumb-selected":g("thumbs-up")})})),React.createElement(a.Tooltip,{text:__("I don't find this useful","jetpack-external-media")},React.createElement(a.Button,{disabled:e,icon:c.A,onClick:()=>_(!1),iconSize:t,showTooltip:!1,className:(0,l.A)({"ai-assistant-feedback__thumb-selected":g("thumbs-down")})})))}},3059:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(6427),a=r(1113),s=r(5573);const i=React.createElement(n.SVG,{width:"42",height:"42",viewBox:"0 0 42 42",fill:"none",xmlns:"http://www.w3.org/2000/svg"},React.createElement(n.G,{clipPath:"url(#clip0_4479_1006)"},React.createElement(n.Path,{d:"M7.87488 0L10.1022 5.64753L15.7498 7.87488L10.1022 10.1022L7.87488 15.7498L5.64753 10.1022L0 7.87488L5.64753 5.64753L7.87488 0Z",fill:"#A7AAAD"}),React.createElement(n.Path,{d:"M31.4998 0L34.4696 7.53004L41.9997 10.4998L34.4696 13.4696L31.4998 20.9997L28.53 13.4696L21 10.4998L28.53 7.53004L31.4998 0Z",fill:"#A7AAAD"}),React.createElement(n.Path,{d:"M18.3748 15.7496L22.0871 25.1621L31.4996 28.8744L22.0871 32.5866L18.3748 41.9992L14.6625 32.5866L5.25 28.8744L14.6625 25.1621L18.3748 15.7496Z",fill:"#A7AAAD"})),React.createElement(s.Defs,null,React.createElement("clipPath",{id:"clip0_4479_1006"},React.createElement(n.Rect,{width:"41.9997",height:"41.9992",fill:"white"}))));function o({className:e,size:t=42}){return React.createElement(a.A,{icon:i,width:t,height:t,className:e})}},6390:(e,t,r)=>{"use strict";r.d(t,{A:()=>k});var n=r(5985),a=r(6427),s=r(8468),i=r(7723),o=r(6941),c=r.n(o),l=r(6223),u=r(3101),d=r(6381),p=r(4016),h=r(7853),m=r(7990),f=r(7560);const __=i.__,g="ai-image-generator",_=c()("jetpack-ai-client:ai-image-modal");function k({title:e,cost:t,open:r,images:i,currentIndex:o=0,onClose:c=null,onTryAgain:k=null,onGenerate:y=null,generating:v=!1,notEnoughRequests:E=!1,requireUpgrade:b=!1,currentLimit:A=null,currentUsage:C=null,isUnlimited:w=!1,upgradeDescription:x=null,hasError:S=!1,handlePreviousImage:D=()=>{},handleNextImage:F=()=>{},acceptButton:P=null,autoStart:R=!1,autoStartAction:L=null,instructionsPlaceholder:I=null,imageStyles:j=[],onGuessStyle:T=null,prompt:N="",setPrompt:M=()=>{},initialStyle:B=null,inputDisabled:O=!1,actionDisabled:q=!1}){const{tracks:z}=(0,n.st)(),{recordEvent:U}=z,G=(0,s.useRef)(!1),[H,V]=(0,s.useState)(!1),[$,Z]=(0,s.useState)(null),[J,W]=(0,s.useState)(j||[]),Y=(0,s.useCallback)((()=>{k?.({userPrompt:N,style:$})}),[k,N,$]),Q=(0,s.useCallback)((async()=>{if($===l.Wp&&T){U("jetpack_ai_general_image_guess_style",{context:"block-editor",tool:"image"});const e=await T(N)||l.Hu;Z(e),_("guessed style",e),y?.({userPrompt:N,style:e})}else y?.({userPrompt:N,style:$})}),[y,N,$,T,U]),K=(0,s.useCallback)((e=>{_("change style",e),Z(e),U("jetpack_ai_image_generator_switch_style",{context:"block-editor",style:e})}),[Z,U]),X=(b||E)&&!v,ee=Boolean(!w&&t&&A),te=__("Generate","jetpack-external-media"),re=__("Try again","jetpack-external-media");return(0,s.useEffect)((()=>{R&&r&&(G.current||(G.current=!0,L?.({})))}),[R,L,r]),(0,s.useEffect)((()=>{j&&j.length>0&&(W([j.find((({value:e})=>e===l.Hu)),j.find((({value:e})=>e===l.Wp)),...j.filter((({value:e})=>![l.Hu,l.Wp].includes(e)))].filter((e=>e))),V(!0),Z(B||l.Hu))}),[j,B]),React.createElement(React.Fragment,null,r&&React.createElement(p.A,{handleClose:c,title:e},React.createElement("div",{className:"ai-image-modal__content"},H&&React.createElement("div",{style:{display:"flex",alignItems:"center",gap:16}},React.createElement("div",{style:{fontWeight:500,flexGrow:1}},__("Generate image","jetpack-external-media")),React.createElement("div",null,React.createElement(a.SelectControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,value:$,options:J,onChange:K}))),React.createElement(u.K,{prompt:N,setPrompt:M,disabled:O,actionDisabled:q,generateHandler:S?Y:Q,placeholder:I,buttonLabel:S?re:te}),X&&React.createElement(h.A,{description:x,placement:g,useLightNudge:!0}),React.createElement("div",{className:"ai-image-modal__actions"},React.createElement("div",{className:"ai-image-modal__actions-left"},ee&&React.createElement(f.A,{cost:t,currentLimit:A,currentUsage:C}))),React.createElement("div",{className:"ai-image-modal__image-canvas"},React.createElement(m.A,{images:i,current:o,handlePreviousImage:D,handleNextImage:F,actions:P}))),React.createElement("div",{className:"ai-image-modal__footer"},React.createElement(d.A,null))))}},7990:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var n=r(6427),a=r(8468),s=r(7723),i=r(1113),o=r(8888),c=r(9115),l=r(3022),u=r(1497),d=r(3059);const __=s.__;function p({children:e,isDotted:t=!1,contentClassName:r=""}){const n=React.createElement("img",{className:"ai-assistant-image__carrousel-image",src:"data:image/svg+xml,<svg viewBox='0 0 1 1' width='1024' height='768' xmlns='http://www.w3.org/2000/svg'><path d='M0 0 L1 0 L1 1 L0 1 L0 0 Z' fill='none' /></svg>",alt:""});return React.createElement("div",{className:"ai-assistant-image__blank"},n,React.createElement("div",{className:(0,l.A)("ai-assistant-image__blank-content",r,{"is-dotted":t})},e))}function h({images:e,current:t,handlePreviousImage:r,handleNextImage:s,actions:h=null}){const[m,f]=(0,a.useState)(!1),g=React.createElement("button",{className:"ai-carrousel__prev",onClick:r},React.createElement(i.A,{icon:o.A,className:(0,l.A)("ai-carrousel__prev-icon",{"is-disabled":0===t})})),_=React.createElement("button",{className:"ai-carrousel__next",onClick:s},React.createElement(i.A,{icon:c.A,className:(0,l.A)("ai-carrousel__next-icon",{"is-disabled":t+1===e.length})})),k=e?.filter?.((e=>e?.generating||Object.hasOwn(e,"image")||Object.hasOwn(e,"libraryId")))?.length,y=0===t&&0===k?0:t+1;return(0,a.useEffect)((()=>{const r=e[t];r||f(!0);const{image:n,generating:a,error:s}=r||{};return n||a||s?a||s?f(!0):void f(!1):f(!0)}),[t,e]),React.createElement("div",{className:"ai-assistant-image__carrousel"},React.createElement("div",{className:"ai-assistant-image__carrousel-images"},e.length>1&&g,e.map((({image:e,generating:r,error:a,revisedPrompt:s,libraryUrl:i},o)=>React.createElement("div",{key:"image:"+o,className:(0,l.A)("ai-assistant-image__carrousel-image-container",{"is-current":t===o,"is-prev":t>o})},r?React.createElement(p,{contentClassName:"ai-assistant-image__loading"},__("Creating image…","jetpack-external-media"),React.createElement(n.Spinner,{style:{width:"50px",height:"50px"}})):React.createElement(React.Fragment,null,a?React.createElement(p,{isDotted:!0},React.createElement("div",{className:"ai-assistant-image__error"},__("An error occurred while generating the image. Please, try again!","jetpack-external-media"),a?.message&&React.createElement("span",{className:"ai-assistant-image__error-message"},a?.message))):React.createElement(React.Fragment,null,r||e||i?React.createElement("img",{className:"ai-assistant-image__carrousel-image",src:e||i,alt:s}):React.createElement(p,null,React.createElement(d.A,null))))))),e.length>1&&_),React.createElement("div",{className:"ai-assistant-image__carrousel-footer"},React.createElement("div",{className:"ai-assistant-image__carrousel-footer-left"},React.createElement("div",{className:"ai-assistant-image__carrousel-counter"},g,y," / ",k,_),React.createElement(u.A,{disabled:m,ratedItem:e[t]?.libraryUrl||"",iconSize:20,options:{mediaLibraryId:Number(e[t].libraryId),prompt:e[t].prompt,revisedPrompt:e[t].revisedPrompt},feature:"image-generator"})),React.createElement("div",{className:"ai-assistant-image__carrousel-actions"},h)))}},7560:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(8468),a=r(7723);const __=a.__;function s({currentLimit:e,currentUsage:t,cost:r}){const s=e-t,i=(0,n.createInterpolateElement)(
// Translators: %d is the cost of one image.
// Translators: %d is the cost of one image.
(0,a.sprintf)(__("Requests needed: <counter>%d</counter>","jetpack-external-media"),r),{counter:React.createElement("span",null)}),o=(0,n.createInterpolateElement)((0,a.sprintf)(
// Translators: %d is the current requests balance.
__("Requests available: <counter>%d</counter>","jetpack-external-media"),s),{counter:s<r?React.createElement("span",{className:"ai-assistant-featured-image__usage-counter-no-limit"}):React.createElement("strong",null)});return React.createElement("div",{className:"ai-assistant-featured-image__usage-counter"},React.createElement("span",null,i),React.createElement("span",null,o))}},9289:(e,t,r)=>{"use strict";r.d(t,{A:()=>v});var n=r(5985),a=r(6427),s=r(7143),i=r(3656),o=r(8468),c=r(7723),l=r(6941),u=r.n(l),d=r(3984),p=r(2448),h=r(1508),m=r(2038),f=r(6390),g=r(3620),_=r(5918),k=r(5988);const __=c.__,y=u()("jetpack-ai-client:featured-image");function v({busy:e,disabled:t,placement:r,onClose:l=()=>{}}){const[u,v]=(0,o.useState)(r===k.hB),E=(0,_.A)(),{getPostContent:b,isEditedPostEmpty:A}=(0,h.A)(),{postTitle:C,postFeaturedMediaId:w,isEditorPanelOpened:x}=(0,s.useSelect)((e=>({postTitle:e(i.store).getEditedPostAttribute("title"),postFeaturedMediaId:e(i.store).getEditedPostAttribute("featured_media"),isEditorPanelOpened:e(i.store).isEditorPanelOpened??e("core/edit-post").isEditorPanelOpened})),[]),{saveToMediaLibrary:S}=(0,m.A)(),{tracks:D}=(0,n.st)(),{recordEvent:F}=D,[P,R]=(0,o.useState)(null),[L,I]=(0,o.useState)(""),{enableComplementaryArea:j}=(0,s.useDispatch)("core/interface"),{clearSelectedBlock:T}=(0,s.useDispatch)("core/block-editor"),{toggleEditorPanelOpened:N}=(0,s.useDispatch)("core/edit-post"),{editPost:M,toggleEditorPanelOpened:B}=(0,s.useDispatch)(i.store),{requireUpgrade:O,requestsCount:q,requestsLimit:z,currentTier:U,costs:G}=(0,p.A)(),H=(0,n.Bd)(U),V=G?.[k.oW]?.activeModel??10,$=V===G?.[k.oW]?.stableDiffusion?k.Dk:k.bb,Z=H===n.Ti,J=z-q<V,W=B??N,{pointer:Y,current:Q,setCurrent:K,processImageGeneration:X,handlePreviousImage:ee,handleNextImage:te,currentImage:re,currentPointer:ne,images:ae,imageStyles:se,guessStyle:ie}=(0,g.A)({autoStart:!1,cost:V,type:"featured-image-generation",feature:k.oW,previousMediaId:w}),oe=(0,o.useCallback)((()=>{v(!1),l?.()}),[l]),ce=(0,o.useCallback)((()=>{v(!0)}),[]),le=(0,o.useCallback)((e=>{const t=C+"\n\n"+b();return ie(e,"featured-image-guess-style",t)}),[C,b,ie]),ue=(0,o.useCallback)((({userPrompt:e,style:t})=>(F("jetpack_ai_featured_image_generation_generate_image",{placement:r,model:$,site_type:E,style:t,userPrompt:e}),v(!0),X({userPrompt:e,postContent:C+"\n\n"+b(),notEnoughRequests:J,style:t}).catch((e=>{F("jetpack_ai_featured_image_generation_error",{placement:r,error:e?.message,model:$,site_type:E,style:t})})))),[F,r,$,E,X,b,J,C]),de=(0,o.useCallback)((async()=>{ne.generating=!0;const e=await le("");R(e);const t=await ue({userPrompt:"",style:e});t&&(y("handleFirstGenerate",t.revisedPrompt),I(t.revisedPrompt||""))}),[ne,ue,le]),pe=(0,o.useCallback)((({userPrompt:e,style:t})=>{F("jetpack_ai_featured_image_generation_generate_another_image",{placement:r,model:$,site_type:E,style:t}),K((()=>ae.length)),X({userPrompt:e,postContent:C+"\n\n"+b(),notEnoughRequests:J,style:t}).catch((n=>{F("jetpack_ai_featured_image_generation_error",{placement:r,error:n?.message,model:$,site_type:E,style:t,userPrompt:e})}))}),[F,r,$,E,K,X,C,b,J,ae]),he=(0,o.useCallback)((({userPrompt:e,style:t})=>{F("jetpack_ai_featured_image_generation_try_again",{placement:r,model:$,site_type:E,style:t}),X({userPrompt:e,postContent:C+"\n\n"+b(),notEnoughRequests:J,style:t}).catch((e=>{F("jetpack_ai_featured_image_generation_error",{placement:r,error:e?.message,model:$,site_type:E,style:t})}))}),[F,r,$,E,X,b,J,C]),me=(0,o.useCallback)((()=>(T(),j("core/edit-post","edit-post/document"))),[T,j]),fe=(0,o.useCallback)((()=>{F("jetpack_ai_featured_image_generation_use_image",{placement:r,model:$,site_type:E});const e=e=>{M({featured_media:e}),oe(),setTimeout((()=>{const e=x("featured-image"),t=x("post-status");me().then((()=>{e||W("featured-image"),t||W("post-status")}))}),500)};re?.libraryId?e(re?.libraryId):S(re?.image).then((t=>{e(t?.id)})).catch((e=>{F("jetpack_ai_featured_image_saving_error",{placement:r,error:e?.message,model:$,site_type:E})}))}),[F,r,$,E,re?.libraryId,re?.image,M,oe,x,me,W,S]),ge=__("Generate another image","jetpack-external-media"),_e=__("Generate","jetpack-external-media"),ke=!(A()&&!C.trim?.()),ye=ke?L.length>=0:L.length>=3,ve=J||ne?.generating||O,Ee=ve||!ke&&!ye,be=J?(0,c.sprintf)(
// Translators: %d is the cost of generating a featured image.
__("Featured image generation costs %d requests per image. You don't have enough requests to generate another image.","jetpack-external-media"),V):null,Ae=React.createElement(a.Button,{onClick:fe,variant:"primary",disabled:!re?.image||re?.generating||re?.libraryId===w},__("Set as featured image","jetpack-external-media"));return React.createElement(React.Fragment,null,(r===d.s6||r===d.wb)&&React.createElement(React.Fragment,null,React.createElement("p",{className:"jetpack-ai-assistant__help-text"},__("Based on your post content.","jetpack-external-media")),React.createElement(a.Button,{onClick:ce,isBusy:e,disabled:t||J,variant:"secondary",__next40pxDefaultSize:!0},__("Generate image","jetpack-external-media"))),React.createElement(f.A,{autoStart:ke&&!w,autoStartAction:de,images:ae,currentIndex:Q,title:__("Generate a featured image with AI","jetpack-external-media"),cost:V,open:u,placement:r,onClose:oe,onTryAgain:he,onGenerate:Y?.current>0||w?pe:ue,generating:ne?.generating,notEnoughRequests:J,requireUpgrade:O,upgradeDescription:be,currentLimit:z,currentUsage:q,isUnlimited:Z,hasError:Boolean(ne?.error),handlePreviousImage:ee,handleNextImage:te,acceptButton:Ae,generateButtonLabel:Y?.current>0?ge:_e,instructionsPlaceholder:__("Describe the featured image you'd like to create and select a style.","jetpack-external-media"),imageStyles:se,onGuessStyle:le,prompt:L,setPrompt:I,initialStyle:P,inputDisabled:ve,actionDisabled:Ee}))}},3158:(e,t,r)=>{"use strict";r.d(t,{A:()=>_});var n=r(5985),a=r(6427),s=r(8468),i=r(7723),o=r(6941),c=r.n(o),l=r(2448),u=r(1508),d=r(2038),p=r(6390),h=r(3620),m=r(5918),f=r(5988);const __=i.__,g=c()("jetpack-ai:general-purpose-image");function _({placement:e,onClose:t=()=>{},onSetImage:r=()=>{}}){const[o,c]=(0,s.useState)(!0),_=(0,m.A)(),{getPostContent:k}=(0,u.A)(),{saveToMediaLibrary:y}=(0,d.A)(),{tracks:v}=(0,n.st)(),{recordEvent:E}=v,[b,A]=(0,s.useState)(""),{requireUpgrade:C,requestsCount:w,requestsLimit:x,currentTier:S,costs:D}=(0,l.A)(),F=(0,n.Bd)(S),P=D?.[f.yP]?.activeModel??10,R=P===D?.[f.yP]?.stableDiffusion?f.Dk:f.bb,L=F===n.Ti,I=x-w<P,{current:j,setCurrent:T,processImageGeneration:N,handlePreviousImage:M,handleNextImage:B,currentImage:O,currentPointer:q,images:z,pointer:U,imageStyles:G,guessStyle:H}=(0,h.A)({cost:P,autoStart:!1,type:"general-image-generation",feature:f.yP}),V=b.length>=3,$=I||q?.generating||C,Z=$||!V,J=(0,s.useCallback)((()=>{c(!1),t?.()}),[t]),W=(0,s.useCallback)((async({userPrompt:t,style:r})=>{g("handleGenerate",t,r),E("jetpack_ai_general_image_generation_generate_image",{placement:e,model:R,site_type:_,style:r}),N({userPrompt:t,postContent:k(),notEnoughRequests:I,style:r}).catch((t=>{E("jetpack_ai_general_image_generation_error",{placement:e,error:t?.message,model:R,site_type:_,style:r})}))}),[E,e,R,_,N,k,I]),Y=(0,s.useCallback)((({userPrompt:t,style:r})=>{g("handleRegenerate",t),E("jetpack_ai_general_image_generation_generate_another_image",{placement:e,model:R,site_type:_,style:r}),T((e=>e+1)),N({userPrompt:t,postContent:k(),notEnoughRequests:I,style:r}).catch((t=>{E("jetpack_ai_general_image_generation_error",{placement:e,error:t?.message,model:R,site_type:_})}))}),[E,e,R,_,N,k,I,T]),Q=(0,s.useCallback)((({userPrompt:t,style:r})=>{g("handleTryAgain",t),E("jetpack_ai_general_image_generation_try_again",{placement:e,model:R,site_type:_,style:r}),N({userPrompt:t,postContent:k(),notEnoughRequests:I,style:r}).catch((t=>{E("jetpack_ai_general_image_generation_error",{placement:e,error:t?.message,model:R,site_type:_})}))}),[E,e,R,_,N,k,I]),K=(0,s.useCallback)((()=>{E("jetpack_ai_general_image_generation_use_image",{placement:e,model:R,site_type:_});const t=e=>{r?.({id:e.id,url:e.url}),J()};O?.libraryId?t({id:O?.libraryId,url:O?.libraryUrl}):y(O?.image).then((e=>{t(e)}))}),[E,e,R,_,O?.libraryId,O?.libraryUrl,O?.image,r,J,y]),X=__("Generate another image","jetpack-external-media"),ee=__("Generate","jetpack-external-media"),te=I?(0,i.sprintf)(
// Translators: %d is the cost of generating a featured image.
__("Image generation costs %d requests per image. You don't have enough requests to generate another image.","jetpack-external-media"),P):null,re=React.createElement(a.Button,{onClick:K,variant:"primary",disabled:!O?.image||O?.generating},__("Insert image","jetpack-external-media"));return React.createElement(p.A,{images:z,currentIndex:j,title:__("Generate an image with AI","jetpack-external-media"),cost:P,open:o,placement:e,onClose:J,onTryAgain:Q,onGenerate:U?.current>0?Y:W,generating:q?.generating,notEnoughRequests:I,requireUpgrade:C,upgradeDescription:te,currentLimit:x,currentUsage:w,isUnlimited:L,hasError:Boolean(q?.error),handlePreviousImage:M,handleNextImage:B,acceptButton:re,generateButtonLabel:U?.current>0?X:ee,instructionsPlaceholder:__("Describe the image you'd like to create and select a style.","jetpack-external-media"),imageStyles:G,onGuessStyle:H,prompt:b,setPrompt:A,inputDisabled:$,actionDisabled:Z})}},3620:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var n=r(7143),a=r(8468),s=r(7723),i=r(3832),o=r(1439),c=r(2448),l=r(2614),u=r(2038),d=r(5988);const __=s.__;function p({feature:e,type:t,cost:r,autoStart:s=!0,previousMediaId:p}){const{generateImageWithParameters:h}=(0,l.Ay)(),{increaseRequestsCount:m,featuresControl:f}=(0,c.A)(),{saveToMediaLibrary:g}=(0,u.A)(),{createNotice:_}=(0,n.useDispatch)("core/notices"),k=(0,a.useRef)(0),[y,v]=(0,a.useState)(0),[E,b]=(0,a.useState)([{generating:s}]),A=e===d.oW?"featured-image":"image",C=f?.[A],w=C?.styles,x=(0,a.useCallback)(((e,t)=>{b((r=>{const n=[...r];return n[t]={...n[t],...e},n}))}),[]),S=(0,n.useSelect)((e=>e("core")?.getMedia?.(p)),[p]);(0,a.useEffect)((()=>{S&&x({image:S.source_url,libraryId:S.id,libraryUrl:S.source_url,generating:!1},k.current)}),[S,x]);const D=(0,a.useCallback)((e=>{_("success",e,{type:"snackbar",isDismissible:!0})}),[_]),F=(0,a.useCallback)((()=>{m(r)}),[m,r]),P=(0,a.useCallback)((e=>{if(!e)return"image.png";const t=e.split(" ").slice(0,10).join(" ");return(0,i.cleanForSlug)(t)+".png"}),[]),R=(0,a.useCallback)((({userPrompt:r,postContent:n,notEnoughRequests:a,style:s=null})=>new Promise(((i,o)=>{if(p&&0===k.current&&k.current++,x({generating:!0,error:null},k.current),a)return x({generating:!1,error:new Error(__("You don't have enough requests to generate another image.","jetpack-external-media"))},k.current),void i({});const c=h({feature:e,size:"1792x1024",responseFormat:"b64_json",messages:[{role:"jetpack-ai",context:{type:t,request:r||null,content:n,style:s}}],style:s||""}),l=P(r);c.then((e=>{if(e.data.length>0){const t="data:image/png;base64,"+e.data[0].b64_json,n=r||null,a=e.data[0].revised_prompt||null;x({image:t,prompt:n,revisedPrompt:a},k.current),F(),g(t,l).then((e=>{D(__("Image saved to media library.","jetpack-external-media")),x({libraryId:e?.id,libraryUrl:e?.url,generating:!1},k.current),k.current+=1,i({image:t,libraryId:e?.id,libraryUrl:e?.url,revisedPrompt:a})})).catch((()=>{x({generating:!1},k.current),k.current+=1,i({image:t})}))}})).catch((e=>{x({generating:!1,error:e},k.current),o(e)}))}))),[x,h,e,t,F,g,D,P,p]),L=(0,a.useCallback)((()=>{v(Math.max(y-1,0))}),[y]),I=(0,a.useCallback)((()=>{v(Math.min(y+1,E.length-1))}),[y,E.length]),j=(0,a.useCallback)((async function(e,t="",r=""){if(!w||!w.length)return null;const n=[{role:"jetpack-ai",context:{type:t||"general-image-guess-style",request:e,content:r}}];try{const e=await(0,o.A)(n,{feature:"jetpack-ai-image-generator"});if(!e)return null;const t=w.find((({value:t})=>t===e));return t?t.value:null}catch(e){Promise.reject(e)}}),[w]);return{current:y,setCurrent:v,processImageGeneration:R,handlePreviousImage:L,handleNextImage:I,currentImage:E[y],currentPointer:E[k.current],images:E,pointer:k,imageStyles:w,guessStyle:j}}},5918:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(5985),a=r(8468);function s(){const[e]=(0,a.useState)((0,n.d9)()?"atomic":(0,n.Sy)()?"simple":"jetpack");return e}},7089:(e,t,r)=>{"use strict";r.d(t,{OE:()=>a.A,dY:()=>s.dY,hB:()=>s.hB,m2:()=>n.A});var n=r(9289),a=r(3158),s=r(5988)},5988:(e,t,r)=>{"use strict";r.d(t,{Dk:()=>s,bb:()=>i,dY:()=>c,hB:()=>o,oW:()=>n,yP:()=>a});const n="featured-post-image",a="general-image",s="stable-diffusion",i="dall-e-3",o="media-source-dropdown",c="block-placeholder-button"},6381:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(6427),a=r(8468),s=r(7723),i=r(1113),o=r(9783);const __=s.__;function c({onGuidelinesClick:e,onFeedbackClick:t}){const r=(0,a.useCallback)((()=>{e?.()}),[e]),s=(0,a.useCallback)((()=>{t?.()}),[t]);return React.createElement(React.Fragment,null,React.createElement("div",{className:"ai-image-modal__footer-disclaimer"},React.createElement(i.A,{icon:o.A}),React.createElement("span",null,__("Generated images could be inaccurate, biased or include text.","jetpack-external-media")),React.createElement(n.Button,{variant:"link",className:"ai-image-modal__guidelines-button",href:"https://jetpack.com/redirect/?source=ai-guidelines",target:"_blank",onClick:r},React.createElement("span",null,__("Guidelines","jetpack-external-media")," ↗"))),React.createElement(n.Button,{variant:"link",className:"ai-image-modal__feedback-button",href:"https://jetpack.com/redirect/?source=jetpack-ai-feedback",target:"_blank",onClick:s},React.createElement("span",null,__("Give feedback","jetpack-external-media")," ↗")))}},6754:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(6427),a=r(3022);function s({state:e}){return React.createElement("div",{className:(0,a.A)("jetpack-ai-status-indicator__icon-wrapper",{[`is-${e}`]:!0})},React.createElement(n.Spinner,null))}},7802:(e,t,r)=>{"use strict";r.d(t,{OE:()=>n.OE,dY:()=>n.dY,hB:()=>n.hB,m2:()=>n.m2});r(2299),r(1497),r(3059);var n=r(7089);r(6754),r(6381),r(3098),r(4016),r(7853)},3098:(e,t,r)=>{"use strict";r(6427),r(8468),r(7723);var n=r(3883),a=r(2154);r(8836),r(1497);const s="warning",i="error",o="success",c="info";a.A,n.A},4016:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(6427),a=r(7723),s=r(991),i=r(6754);const __=a.__,o=({requestingState:e,onClose:t,title:r})=>React.createElement("div",{className:"ai-assistant-modal__header"},React.createElement("div",{className:"ai-assistant-modal__title-wrapper"},React.createElement(i.A,{state:e}),React.createElement("h1",{className:"ai-assistant-modal__title"},r)),React.createElement(n.Button,{icon:s.A,label:__("Close","jetpack-external-media"),onClick:t}));function c({children:e,handleClose:t,hideHeader:r=!0,requestingState:a="init",title:s=__("AI Assistant","jetpack-external-media"),maxWidth:i=720}){return React.createElement(n.Modal,{__experimentalHideHeader:r,className:"ai-assistant-modal",shouldCloseOnClickOutside:!1,onRequestClose:t},React.createElement("div",{className:"ai-assistant-modal__content",style:{maxWidth:i}},React.createElement(o,{requestingState:a,onClose:t,title:s}),React.createElement("hr",{className:"ai-assistant-modal__divider"}),e))}},7853:(e,t,r)=>{"use strict";r.d(t,{A:()=>g});var n=r(3924),a=r(5985),s=r(2201),i=r(6427),o=r(8468),c=r(7723),l=r(6941),u=r.n(l),d=r(2694),p=r(2448),h=r(1567);const __=c.__,m=u()("jetpack-ai-client:upgrade-prompt"),f=({variant:e="error"})=>{const t=(()=>{const{usagePeriod:e}=(0,p.A)(),t=(e=>{const t=__("You've reached this month's request limit, per our <link>fair usage policy</link>.","jetpack-external-media");if(!e)return t;
// Translators: %s is the date when the requests will reset.
const r=__("Requests will reset on %s.","jetpack-external-media");return`${t} ${(0,c.sprintf)(r,e)}`})((e=>{if(!e?.nextStart)return null;const t=new Date(e.nextStart);return t.toLocaleString("default",{month:"long"})+" "+t.getDate()})(e));return(0,o.createInterpolateElement)(t,{link:React.createElement("a",{href:"https://jetpack.com/redirect/?source=ai-assistant-fair-usage-policy",target:"_blank",rel:"noreferrer"})})})();return"muted"===e?React.createElement("span",{className:"jetpack-ai-fair-usage-notice-muted-variant"},t):"error"===e?React.createElement(i.Notice,{status:"error",isDismissible:!1,className:"jetpack-ai-fair-usage-notice"},t):null},g=e=>{const{upgradeType:t,currentTier:r}=(0,p.A)();return 1===r?.value?React.createElement(f,null):"vip"===t?(({description:e=null,useLightNudge:t=!1})=>{const r=t?h.U:s.c,n=(0,o.createInterpolateElement)(__("You've reached the Jetpack AI rate limit. <strong>Please reach out to your VIP account team.</strong>","jetpack-external-media"),{strong:React.createElement("strong",null)});return React.createElement(r,{buttonText:null,checkoutUrl:null,className:"jetpack-ai-upgrade-banner",description:e||n,goToCheckoutPage:null,isRedirecting:null,visible:!0,align:null,title:null,context:null})})({description:e.description,useLightNudge:e?.useLightNudge}):(({placement:e=null,description:t=null,useLightNudge:r=!1})=>{const i=r?h.U:s.c,{checkoutUrl:l}=(0,d.A)(),u=(0,a.CP)(),{nextTier:f,tierPlansEnabled:g,currentTier:_,requestsCount:k}=(0,p.A)(),{tracks:y}=(0,a.st)(),v=(0,o.useCallback)((()=>{m("upgrade",e),y.recordEvent("jetpack_ai_upgrade_button",{current_tier_slug:_?.slug,requests_count:k,placement:e})}),[_,k,y,e]),E=(0,o.useCallback)((()=>{m("contact us",e),y.recordEvent("jetpack_ai_upgrade_contact_us",{placement:e})}),[y,e]);if(!u){const e=(0,o.createInterpolateElement)(__("Congratulations on exploring Jetpack AI and reaching the free requests limit! <strong>Reach out to the site administrator to upgrade and keep using Jetpack AI.</strong>","jetpack-external-media"),{strong:React.createElement("strong",null)});return React.createElement(i,{showButton:!1,className:"jetpack-ai-upgrade-banner",description:t||e,visible:!0,align:null,title:null,context:null})}if(g){if(!f){const e=(0,n.A)("jetpack-ai-tiers-more-requests-contact"),r=__("You have reached the request limit for your current plan.","jetpack-external-media");return React.createElement(i,{buttonText:__("Contact Us","jetpack-external-media"),description:t||r,className:"jetpack-ai-upgrade-banner",checkoutUrl:e,visible:!0,align:null,title:null,context:null,goToCheckoutPage:E,target:"_blank"})}const e=(0,o.createInterpolateElement)((0,c.sprintf)(/* Translators: number of requests */
__("You have reached the requests limit for your current plan. <strong>Upgrade now to increase your requests limit to %d.</strong>","jetpack-external-media"),f.limit),{strong:React.createElement("strong",null)});return React.createElement(i,{buttonText:(0,c.sprintf)(/* Translators: number of requests */
__("Upgrade to %d requests","jetpack-external-media"),f.limit),checkoutUrl:l,className:"jetpack-ai-upgrade-banner",description:t||e,goToCheckoutPage:v,visible:!0,align:"center",title:null,context:null,target:"_blank"})}return React.createElement(i,{buttonText:__("Upgrade","jetpack-external-media"),checkoutUrl:l,className:"jetpack-ai-upgrade-banner",description:(0,o.createInterpolateElement)(__("Congratulations on exploring Jetpack AI and reaching the free requests limit! <strong>Upgrade now to keep using it.</strong>","jetpack-external-media"),{strong:React.createElement("strong",null)}),goToCheckoutPage:v,visible:!0,align:null,title:null,context:null,target:"_blank"})})(e)}},1567:(e,t,r)=>{"use strict";r.d(t,{U:()=>s});var n=r(6427),a=r(7723);const __=a.__,s=({title:e,description:t,buttonText:r=null,checkoutUrl:a=null,goToCheckoutPage:s=null,isRedirecting:i=!1,showButton:o=!0,target:c="_top"})=>{const l=__("Redirecting…","jetpack-external-media");return React.createElement("div",{className:"jetpack-upgrade-plan-banner-light"},React.createElement(n.Notice,{status:"error",isDismissible:!1},React.createElement("p",null,e&&React.createElement("strong",null,e),t," ",o&&React.createElement(n.Button,{href:i?null:a,onClick:s,variant:"link",target:c},i?l:r))))}},3984:(e,t,r)=>{"use strict";r.d(t,{Gm:()=>s,UA:()=>a,s6:()=>i,wb:()=>o});var n=r(7723);const __=n.__,a=(__("English","jetpack-external-media"),__("Spanish","jetpack-external-media"),__("French","jetpack-external-media"),__("German","jetpack-external-media"),__("Italian","jetpack-external-media"),__("Portuguese","jetpack-external-media"),__("Russian","jetpack-external-media"),__("Chinese","jetpack-external-media"),__("Japanese","jetpack-external-media"),__("Arabic","jetpack-external-media"),__("Hindi","jetpack-external-media"),__("Korean","jetpack-external-media"),__("Formal","jetpack-external-media"),__("Informal","jetpack-external-media"),__("Optimistic","jetpack-external-media"),__("Humorous","jetpack-external-media"),__("Serious","jetpack-external-media"),__("Skeptical","jetpack-external-media"),__("Empathetic","jetpack-external-media"),__("Confident","jetpack-external-media"),__("Passionate","jetpack-external-media"),__("Provocative","jetpack-external-media"),"summarize"),s="changeLanguage",i=(__("Translate","jetpack-external-media"),__("Change tone","jetpack-external-media"),__("Correct spelling and grammar","jetpack-external-media"),__("Simplify","jetpack-external-media"),__("Summarize","jetpack-external-media"),__("Make shorter","jetpack-external-media"),__("Expand","jetpack-external-media"),__("Turn list into a table","jetpack-external-media"),__("Write a post from this list","jetpack-external-media"),__("Generate a post title","jetpack-external-media"),__("Summary based on title","jetpack-external-media"),__("Continue writing","jetpack-external-media"),"jetpack-sidebar"),o="document-settings"},2166:(e,t,r)=>{"use strict";r.d(t,{R:()=>i});var n=r(1609),a=r.n(n);const s=(0,n.createContext)({}),i=({value:e,children:t})=>a().createElement(s.Provider,{value:e,children:t})},3965:(e,t,r)=>{"use strict";r.d(t,{Rb:()=>n.R});var n=r(2166);r(9886),r(3471)},3471:(e,t,r)=>{"use strict";r(1609),r(8836),r(3965)},9886:(e,t,r)=>{"use strict";var n=r(9491),a=r(8468),s=r(1609),i=r.n(s),o=r(6219),c=r(3965);(0,n.createHigherOrderComponent)((e=>t=>{const{suggestion:r,error:n,requestingState:s,request:l,stopSuggestion:u,eventSource:d}=(0,o.Ay)(),p=(0,a.useMemo)((()=>({suggestion:r,requestingError:n,requestingState:s,eventSource:d,requestSuggestion:l,stopSuggestion:u})),[r,n,s,d,l,u]);return i().createElement(c.Rb,{value:p},i().createElement(e,t))}),"withAiDataProvider")},2694:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(3924),a=r(5985),s=r(2448);const i=()=>{const e=new URLSearchParams(window.location.search),t=(0,a.GE)();return(0,a.Sy)()&&e.has("post")?`https://wordpress.com/post/${t}/${e.get("post")}`:`https://wordpress.com/home/<USER>"jetpack-ai-yearly-tier-upgrade-nudge",{site:(0,a.GE)(),path:t?`jetpack_ai_yearly:-q-${e?.limit}`:"jetpack_ai_yearly",query:`redirect_to=${encodeURIComponent(r)}`}),c=(0,n.A)("jetpack-ai-upgrade-url-for-jetpack-sites",{site:(0,a.GE)(),path:"jetpack_ai_yearly"}),l=(0,a.d9)()||(0,a.Sy)()?o:c,{autosaveAndRedirect:u,isRedirecting:d}=(0,a.ZR)(l);return{checkoutUrl:l,autosaveAndRedirect:u,isRedirecting:d}}},2448:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(5985),a=r(7143),s=r(8468);function i(){const e=(0,a.useSelect)((e=>e("wordpress-com/plans").getAiAssistantFeature()),[]),t=(0,a.useSelect)((e=>e("wordpress-com/plans").getIsRequestingAiAssistantFeature()),[]),{fetchAiAssistantFeature:r,increaseAiAssistantRequestsCount:i,dequeueAiAssistantFeatureAsyncRequest:o}=(0,a.useDispatch)("wordpress-com/plans");return(0,s.useMemo)((()=>{const a=(0,n.Bd)(e?.currentTier),s=e?.currentTier?.limit||e?.requestsLimit,c=a===n._X?e?.requestsCount:e?.usagePeriod?.requestsCount,l=a===n._X?e?.requestsLimit:s;return{...e,requestsCount:c,requestsLimit:l,loading:t,error:null,refresh:r,increaseRequestsCount:i,dequeueAsyncRequest:o}}),[e,t,r,i,o])}},6219:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>u,fC:()=>c});var n=r(8468),a=r(7723),s=r(7286),i=r(4867),o=r(8836);const __=a.__;function c(e){switch(e){case o.Or:return{code:o.Or,message:__("You have reached the limit of requests for this site.","jetpack-external-media"),severity:"info"};case o.L9:return{code:o.L9,message:__("Your request was unclear. Mind trying again?","jetpack-external-media"),severity:"info"};case o.AZ:return{code:o.AZ,message:__("Jetpack AI services are currently unavailable. Sorry for the inconvenience.","jetpack-external-media"),severity:"info"};case o.ud:return{code:o.ud,message:__("This request has been flagged by our moderation system. Please try to rephrase it and try again.","jetpack-external-media"),severity:"info"};case o.cT:return{code:o.cT,message:__("The content is too large to be processed all at once. Please try to shorten it or divide it into smaller parts.","jetpack-external-media"),severity:"info"};case o.mA:default:return{code:o.mA,message:__("It was not possible to process your request. Mind trying again?","jetpack-external-media"),severity:"info"}}}function l(e){return e.replace(/^<\|start_header_id\|>assistant<\|end_header_id\|>[\n]+/,"")}function u({prompt:e,autoRequest:t=!1,askQuestionOptions:r={},initialRequestingState:a="init",onSuggestion:u,onDone:d,onStop:p,onError:h,onAllErrors:m}={}){const[f,g]=(0,n.useState)(a),[_,k]=(0,n.useState)(""),[y,v]=(0,n.useState)(),E=(0,n.useRef)(void 0),b=(0,n.useCallback)((e=>{const t=l(e?.detail);t&&(k(t),u?.(t))}),[u]),A=(0,n.useCallback)((e=>{I();const t=l(e?.detail?.message??e?.detail);d?.(t,"chromeAI"===e?.detail?.source),g("done")}),[d]),C=(0,n.useCallback)((e=>{m?.(e?.detail)}),[m]),w=(0,n.useCallback)((e=>{E?.current?.close(),g("error"),v(c(e)),h?.(c(e))}),[h]),x=(0,n.useCallback)((()=>w(o.Or)),[]),S=(0,n.useCallback)((()=>w(o.L9)),[]),D=(0,n.useCallback)((()=>w(o.AZ)),[]),F=(0,n.useCallback)((()=>w(o.ud)),[]),P=(0,n.useCallback)((()=>w(o.mA)),[]),R=(0,n.useCallback)((async(e,t={...r})=>{v(void 0),g("requesting");const n=await(0,i.A)(e);if(E.current=!1!==n?n:await(0,s.A)(e,t),!E?.current)return;const a=E.current;g("suggesting"),a.addEventListener("suggestion",b),a.addEventListener(o.Or,x),a.addEventListener(o.L9,S),a.addEventListener(o.AZ,D),a.addEventListener(o.ud,F),a.addEventListener(o.mA,P),a.addEventListener(o.zn,C),a.addEventListener("done",A)}),[A,x,S,D,F,P,b]),L=(0,n.useCallback)((()=>{g("init"),k(""),v(void 0)}),[]),I=(0,n.useCallback)((()=>{if(!E?.current)return;const e=E?.current;e.close(),e.removeEventListener("suggestion",b),e.removeEventListener(o.Or,x),e.removeEventListener(o.L9,S),e.removeEventListener(o.AZ,D),e.removeEventListener(o.ud,F),e.removeEventListener(o.mA,P),e.removeEventListener("done",A)}),[E,b,x,S,D,F,P,A]),j=(0,n.useCallback)((()=>{I(),p?.(),g("done")}),[p]);return(0,n.useEffect)((()=>{if(e?.length)return t&&R(e,r),()=>{j()}}),[t,e,R,j]),{suggestion:_,error:y,requestingState:f,request:R,stopSuggestion:j,reset:L,handleErrorQuotaExceededError:x,eventSource:E.current}}},7804:(e,t,r)=>{"use strict";r(8468),r(7723);var n=r(6941),a=r.n(n);r(7745);a()("jetpack-ai-client:use-audio-transcription")},6859:(e,t,r)=>{"use strict";r(8468),r(7723)},6223:(e,t,r)=>{"use strict";r.d(t,{Hu:()=>a,Wp:()=>n});const n="auto",a="none"},2614:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>u});var n=r(6941),a=r.n(n),s=r(1439),i=r(1391);const o=a()("ai-client:use-image-generator"),c=(e,t)=>{const r=4e3-t;return e.length>r?e.substring(0,r-6)+" [...]":e},l=async(e,t,r)=>{const n=((e,t)=>{if(t){const r=`I need a Stable Diffusion prompt to generate a featured image for a blog post based on this user-provided image description:\n\n${t.length>1e3?t.substring(0,1e3):t}\n\nThe image should be a photo. Make sure you highlight the main suject of the image description, and include brief details about the light and style of the image.\nInclude a request to use high resolution and produce a highly detailed image, with sharp focus.\nReturn just the prompt, without comments.\n\nFor additional context, this is the post content:\n\n`;return r+c(e,r.length)}return"I need a Stable Diffusion prompt to generate a featured image for a blog post with the following content.\nThe image should be a photo. Make sure you highlight the main suject of the content, and include brief details about the light and style of the image.\nInclude a request to use high resolution and produce a highly detailed image, with sharp focus.\nReturn just the prompt, without comments. The content is:\n\n"+c(e,412)})(e,t);return await(0,s.A)(n,{feature:r})},u=()=>{const e=async function(e){let t="";try{t=(await(0,i.A)()).token}catch(e){return o("Error getting token: %o",e),Promise.reject(e)}try{const r="https://public-api.wordpress.com/wpcom/v2/jetpack-ai-image",n={Authorization:`Bearer ${t}`,"Content-Type":"application/json"},a=await fetch(r,{method:"POST",headers:n,body:JSON.stringify(e)}).then((e=>e.json()));return a?.data?.status&&a?.data?.status>200?(o("Error generating image: %o",a),Promise.reject(a)):a}catch(e){return o("Error generating image: %o",e),Promise.reject(e)}};return{generateImage:async function({feature:t,postContent:r,responseFormat:n="url",userPrompt:a}){try{o("Generating image");const s=((e,t)=>{if(t){const r=`I need a cover image for a blog post based on this user prompt:\n\n${t.length>1e3?t.substring(0,1e3):t}\n\nBefore creating the image, identify the main topic of the user prompt and relate it to the post content.\nDo not represent the whole content in one image, keep it simple and just represent one single idea.\nDo not add details, detailed explanations or highlights from the content, just represent the main idea as if it was a photograph.\nDo not use collages or compositions with multiple elements or scenes. Stick to one single scene. Do not compose unrealistic scenes.\nIf the content describes facts, objects or concepts from the real world, represent them on a realistic style and do not make unreal compositions.\nIf the content is more abstract, use a more abstract style to represent the main idea.\nMake sure the light and the style are visually appealing.\nDo not add text to the image.\n\nFor additional context, this is the post content:\n\n`;return r+c(e,r.length)}const r="I need a cover image for a blog post.\nBefore creating the image, identify the main topic of the content and only represent it.\nDo not represent the whole content in one image, keep it simple and just represent one single idea.\nDo not add details, detailed explanations or highlights from the content, just represent the main idea as if it was a photograph.\nDo not use collages or compositions with multiple elements or scenes. Stick to one single scene. Do not compose unrealistic scenes.\nIf the content describes facts, objects or concepts from the real world, represent them on a realistic style and do not make unreal compositions.\nIf the content is more abstract, use a more abstract style to represent the main idea.\nMake sure the light and the style are visually appealing.\nDo not add text to the image.\n\nThis is the post content:\n\n";return r+c(e,838)})(r,a),i={prompt:s,response_format:n,feature:t,size:"1792x1024"};return await e(i)}catch(e){return o("Error generating image: %o",e),Promise.reject(e)}},generateImageWithStableDiffusion:async function({feature:t,postContent:r,userPrompt:n}){try{o("Generating image with Stable Diffusion");const a={prompt:await l(r,n,t),feature:t,model:"stable-diffusion"};return await e(a)}catch(e){return o("Error generating image: %o",e),Promise.reject(e)}},generateImageWithParameters:e}}},6657:(e,t,r)=>{"use strict";r(8468)},1508:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(7378),a=r(7143),s=r(3656),i=r(8468),o=r(5166);const c=()=>{const{getBlocks:e,isEditedPostEmpty:t}=(0,a.useSelect)((e=>{const t=e("core/block-editor"),r=e(s.store);return{getBlocks:t.getBlocks,isEditedPostEmpty:r.isEditedPostEmpty}}),[]),r=(0,i.useCallback)((()=>{const t=e();return 0===t.length?"":(0,n.serialize)(t)}),[e]);return{getPostContent:(0,i.useCallback)((e=>{let t=r();return t?(e&&"function"==typeof e&&(t=e(t)),t?(0,o.rh)({content:t}):""):""}),[e]),isEditedPostEmpty:t,getSerializedPostContent:r}}},2038:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(3162),a=r(7143),s=r(8468),i=r(6941);const o=r.n(i)()("jetpack-ai-client:save-to-media-library");function c(){const[e,t]=(0,s.useState)(!1),{getSettings:r}=(0,a.useSelect)((e=>e("core/block-editor")),[]);return{isLoading:e,saveToMediaLibrary:(e,a=null)=>{const s=r();return new Promise(((r,i)=>{t(!0),o("Fetching image from URL"),fetch(e).then((e=>{o("Transforming response to blob"),e.blob().then((e=>{o("Uploading blob to media library");const c=[];a?c.push(new File([e],a)):c.push(e),s.mediaUpload({allowedTypes:["image"],filesList:c,onFileChange([e]){(0,n.isBlobURL)(e?.url)||(e&&(o("Image uploaded to media library",e),r(e)),t(!1))},onError(e){o("Error uploading image to media library:",e),i(e),t(!1)}})})).catch((e=>{o("Error transforming response to blob:",e?.message),i(e?.message),t(!1)}))})).catch((e=>{o("Error fetching image from URL:",e?.message),i(e?.message),t(!1)}))}))}}}},3869:(e,t,r)=>{"use strict";r(3162),r(7143),r(8468);var n=r(6941);r.n(n)()("ai-client:save-to-media-library")},7784:(e,t,r)=>{"use strict";r(8468);var n=r(6941),a=r.n(n);r(6219);a()("jetpack-ai-client:use-transcription-post-processing")},151:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(6427),a=r(1609),s=r.n(a);const i=s().createElement(n.SVG,{viewBox:"0 0 32 32",width:"32",height:"32",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",className:"ai-assistant-icon"},s().createElement(n.Path,{className:"spark-first",d:"M9.33301 5.33325L10.4644 8.20188L13.333 9.33325L10.4644 10.4646L9.33301 13.3333L8.20164 10.4646L5.33301 9.33325L8.20164 8.20188L9.33301 5.33325Z"}),s().createElement(n.Path,{className:"spark-second",d:"M21.3333 5.33333L22.8418 9.15817L26.6667 10.6667L22.8418 12.1752L21.3333 16L19.8248 12.1752L16 10.6667L19.8248 9.15817L21.3333 5.33333Z"}),s().createElement(n.Path,{className:"spark-third",d:"M14.6667 13.3333L16.5523 18.1144L21.3333 20L16.5523 21.8856L14.6667 26.6667L12.781 21.8856L8 20L12.781 18.1144L14.6667 13.3333Z"}))},2154:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(6427);const a=React.createElement(n.SVG,{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},React.createElement(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M10 3.95833C6.66328 3.95833 3.95833 6.66327 3.95833 9.99999C3.95833 13.3367 6.66328 16.0417 10 16.0417C13.3367 16.0417 16.0417 13.3367 16.0417 9.99999C16.0417 6.66327 13.3367 3.95833 10 3.95833ZM2.70833 9.99999C2.70833 5.97292 5.97292 2.70833 10 2.70833C14.0271 2.70833 17.2917 5.97292 17.2917 9.99999C17.2917 14.0271 14.0271 17.2917 10 17.2917C5.97292 17.2917 2.70833 14.0271 2.70833 9.99999Z"}),React.createElement(n.Path,{d:"M10.8333 5.83333H9.16667V10.8333H10.8333V5.83333Z"}),React.createElement(n.Path,{d:"M10.8333 12.5H9.16667V14.1667H10.8333V12.5Z"}))},1396:(e,t,r)=>{"use strict";r.d(t,{cj:()=>n.A});var n=r(151);r(1353),r(5007),r(6626),r(4416),r(5680),r(7205)},1353:(e,t,r)=>{"use strict";var n=r(6427),a=r(1609),s=r.n(a);n.SVG,n.Path,n.Rect,n.Path,n.Path},5007:(e,t,r)=>{"use strict";var n=r(6427),a=r(1609),s=r.n(a);n.SVG,n.Path,n.Path},5680:(e,t,r)=>{"use strict";var n=r(6427),a=r(1609),s=r.n(a);n.SVG,n.Path,n.Rect,n.Rect},6626:(e,t,r)=>{"use strict";var n=r(6427),a=r(1609),s=r.n(a);n.SVG,n.Path,n.Path},4416:(e,t,r)=>{"use strict";var n=r(6427),a=r(1609),s=r.n(a);n.SVG,n.Path,n.Rect},7205:(e,t,r)=>{"use strict";var n=r(6427),a=r(1609),s=r.n(a);n.SVG,n.Path,n.Path},6865:(e,t,r)=>{"use strict";r.d(t,{OE:()=>a.OE,cj:()=>n.cj,dY:()=>a.dY,hB:()=>a.hB,m2:()=>a.m2});r(1391),r(3664),r(7286),r(1439),r(7745),r(2694),r(2448),r(6219),r(6657),r(7804),r(7784),r(6859),r(2614),r(1508);var n=r(1396),a=r(7802);r(3965),r(8836),r(2586),r(3984),r(3101),r(5209)},1391:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(5985),a=r(6941),s=r.n(a),i=r(8931);const o=s()("jetpack-ai-client:jwt"),c="jetpack-ai-jwt",l=12e4;async function u({apiNonce:e,siteId:t,expirationTime:r}={}){e=e||window.JP_CONNECTION_INITIAL_STATE.apiNonce,t=t||window.JP_CONNECTION_INITIAL_STATE.siteSuffix,r=r||l;const a=localStorage.getItem(c);let s,u=null;if(a)try{u=JSON.parse(a)}catch(e){o("Error parsing token",e)}if(u&&u?.expire>Date.now())return o("Using cached token"),u;const d=(0,n.Sy)();s=d?await(0,i.A)({path:"/wpcom/v2/sites/"+t+"/jetpack-openai-query/jwt",method:"POST"}):await(0,i.A)({path:"/jetpack/v4/jetpack-ai-jwt?_cacheBuster="+Date.now(),credentials:"same-origin",headers:{"X-WP-Nonce":e},method:"POST"});const p={token:s.token,blogId:d?t:s.blog_id,expire:Date.now()+r};return o("Storing new token"),localStorage.setItem(c,JSON.stringify(p)),p}},231:(e,t,r)=>{"use strict";r(7143)},2586:(e,t,r)=>{"use strict";r(5166),r(4861),r(1269),r(1811),r(231)},4861:(e,t,r)=>{"use strict";r(3984)},8641:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(1147);const a={paragraph:e=>e.replaceAll("\n","<br />")},s={emDelimiter:"_",headingStyle:"atx"},i={strikethrough:{filter:["del","s"],replacement:function(e){return"~~"+e+"~~"}}};class o{constructor({options:e={},rules:t={},keep:r=[],remove:a=[],fixes:o=[]}={}){this.fixes=o,this.turndownService=new n.A({...s,...e}),this.turndownService.keep(r),this.turndownService.remove(a);const c={...i,...t};for(const e in c)this.turndownService.addRule(e,c[e])}render({content:e}){const t=this.turndownService.turndown(e);return this.fixes.reduce(((e,t)=>a[t](e)),t)}}},5166:(e,t,r)=>{"use strict";r.d(t,{Hh:()=>i,rh:()=>o});var n=r(8641);const a=new(r(1737).A),s=new n.A,i=({content:e,rules:t,extension:r})=>a.render({content:e,rules:t,extension:r}),o=({content:e})=>s.render({content:e})},1737:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(7158);const a=e=>e.replaceAll("\x3c!-- wp:list-item --\x3e","").replaceAll("\x3c!-- /wp:list-item --\x3e","").replaceAll("\x3c!-- wp:list --\x3e","").replaceAll("\x3c!-- /wp:list --\x3e","").replaceAll("<li>","\x3c!-- wp:list-item --\x3e<li>").replaceAll("</li>","</li>\x3c!-- /wp:list-item --\x3e").replaceAll("<ol>",'\x3c!-- wp:list {"ordered":true} --\x3e<ol>').replaceAll("</ol>","</ol>\x3c!-- /wp:list --\x3e").replaceAll("<ul>","\x3c!-- wp:list --\x3e<ul>").replaceAll("</ul>","</ul>\x3c!-- /wp:list --\x3e"),s={list:(e,t=!1)=>{const r=e.replace(/<li>\s+<p>/g,"<li>").replace(/<\/p>\s+<\/li>/g,"</li>");return t?a(r):r},listItem:(e,t=!1)=>t?a(e.replace(/^<[ou]l>\s*/g,"").replace(/\s*<\/[ou]l>\s*$/g,"")):e,paragraph:(e,t=!1)=>t?e.replaceAll(/\s*&lt;br \/&gt;\s*/g,"<br />"):e,table:(e,t=!1,{hasFixedLayout:r=!1})=>t?e.startsWith("\x3c!-- wp:table")?e:`\x3c!-- wp:table { "hasFixedLayout":${r?"true":"false"} } --\x3e${e}\x3c!-- /wp:table --\x3e`:e},i={breaks:!0},o=["list"];class c{constructor(e=i){this.markdownConverter=new n.A(e)}render({content:e,rules:t=o,extension:r=!1}){const n=this.markdownConverter.render(e);return t.reduce(((e,t)=>s[t](e,r)),n)}}},1269:(e,t,r)=>{"use strict";r(4715),r(7143)},1811:(e,t,r)=>{"use strict";r(7143)},6019:(e,t,r)=>{"use strict";r(6427),r(8867)},5933:(e,t,r)=>{"use strict";r(6427),r(7723)},6194:(e,t,r)=>{"use strict";r(7723),r(1609),r(2669)},4537:(e,t,r)=>{"use strict";r(5985),r(6427),r(7143),r(7723);var n=r(6941),a=r.n(n);r(1609),r(6381),r(674),r(8725),r(4461),r(9362),r(4423),r(5933),r(6194),r(2623),r(4706),r(1114),r(5421),r(8204);a()("jetpack-ai-calypso:generator-modal")},2623:(e,t,r)=>{"use strict";r(5985),r(6427),r(6076),r(8725)},2669:(e,t,r)=>{"use strict";r(6076)},4706:(e,t,r)=>{"use strict";r(5985),r(6427),r(7143),r(7723);var n=r(6941),a=r.n(n);r(1497),r(8725),r(4461),r(9362),r(4423),r(2669);a()("jetpack-ai-calypso:logo-presenter")},1114:(e,t,r)=>{"use strict";r.d(t,{K:()=>c});r(5985);var n=r(6427),a=r(7723),s=r(6941),i=r.n(s),o=r(1609);r(674),r(8725),r(4461),r(6019),r(4024);const __=a.__,c=(i()("jetpack-ai-calypso:prompt-box"),({prompt:e="",setPrompt:t=()=>{},disabled:r=!1,actionDisabled:a=!1,generateHandler:s=()=>{},placeholder:i="",buttonLabel:c=""})=>{const l=(0,o.useRef)(null);(0,o.useEffect)((()=>{l.current&&l.current.textContent!==e&&(l.current.textContent=e)}),[e]);return React.createElement("div",{className:"jetpack-ai-image-generator__prompt-query"},React.createElement("div",{role:"textbox",tabIndex:0,ref:l,contentEditable:!r,suppressContentEditableWarning:!0,className:"prompt-query__input",onInput:e=>{t(e.target.textContent||"")},onPaste:e=>{e.preventDefault();const r=e.currentTarget.ownerDocument.getSelection();if(!r||!r.rangeCount)return;const n=e.clipboardData.getData("text/plain");r.deleteFromDocument();r.getRangeAt(0).insertNode(document.createTextNode(n)),r.collapseToEnd(),t(l.current?.textContent||"")},onKeyDown:e=>{"Enter"===e.key&&(e.preventDefault(),s()),e.stopPropagation()},onKeyUp:()=>{""===l.current?.textContent&&(l.current.innerHTML="")},"data-placeholder":i}),React.createElement(n.Button,{variant:"primary",className:"jetpack-ai-image-generator__prompt-submit",onClick:s,disabled:a},c||__("Generate","jetpack-external-media")))})},4024:(e,t,r)=>{"use strict";r(5985),r(6427),r(8468),r(7723),r(674),r(8725)},5421:(e,t,r)=>{"use strict";r(5985),r(6427),r(7723),r(8725)},8204:(e,t,r)=>{"use strict";r(6427),r(7723),r(6791)},1356:(e,t,r)=>{"use strict";r.d(t,{_S:()=>n});const n=10},674:(e,t,r)=>{"use strict";r(5985),r(7143),r(4423)},8867:(e,t,r)=>{"use strict";r(7143),r(8468),r(7723),r(4423)},8725:(e,t,r)=>{"use strict";r(7143);var n=r(6941),a=r.n(n);r(1609),r(1439),r(2614),r(3869),r(1391),r(9362),r(8229),r(4423),r(4461);a()("jetpack-ai-calypso:use-logo-generator")},4461:(e,t,r)=>{"use strict";r(7143),r(4423)},3101:(e,t,r)=>{"use strict";r.d(t,{K:()=>n.K});r(4537);var n=r(1114)},9362:(e,t,r)=>{"use strict";r.d(t,{$t:()=>a});r(3858);const n=10;function a(e){const t=localStorage.getItem(`logo-history-${e}`);let r=t?JSON.parse(t):[];return Array.isArray(r)||(r=[]),r=r.slice(-n),r=r.filter((e=>"object"==typeof e&&"string"==typeof e.url&&"string"==typeof e.description)).map((e=>({url:e.url,description:e.description,mediaId:e.mediaId,rating:e.rating}))),r}},3858:(e,t,r)=>{"use strict";r(8931)},8229:(e,t,r)=>{"use strict";r(2746)},2746:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(8931);const a=5;let s=0;async function i(e){if(s+=1,s>a)throw s-=1,new Error("Too many requests");return(0,n.A)(e).finally((()=>{s-=1}))}},9624:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(9362),a=r(2746),s=r(9938);const i={storeAiAssistantFeature:e=>({type:s.UU,feature:e}),fetchAiAssistantFeature:()=>async({dispatch:e})=>{e({type:s.ql});try{const t=await(0,a.A)({path:"/wpcom/v2/jetpack-ai/ai-assistant-feature",query:"force=wpcom"});if(t.data)throw new Error("Failed to fetch");e(i.storeAiAssistantFeature(function(e){return{hasFeature:!!e["has-feature"],isOverLimit:!!e["is-over-limit"],requestsCount:e["requests-count"],requestsLimit:e["requests-limit"],requireUpgrade:!!e["site-require-upgrade"],errorMessage:e["error-message"],errorCode:e["error-code"],upgradeType:e["upgrade-type"],usagePeriod:{currentStart:e["usage-period"]?.["current-start"],nextStart:e["usage-period"]?.["next-start"],requestsCount:e["usage-period"]?.["requests-count"]||0},currentTier:e["current-tier"],nextTier:e["next-tier"],tierPlansEnabled:!!e["tier-plans-enabled"],costs:e.costs,featuresControl:e["features-control"]}}(t)))}catch(t){e({type:s.Rx,error:t})}},increaseAiAssistantRequestsCount:(e=1)=>({dispatch:t})=>{t({type:s.Lg,count:e})},setAiAssistantFeatureRequireUpgrade:(e=!0)=>({type:s.Kt,requireUpgrade:e}),setTierPlansEnabled:(e=!0)=>({type:s.c1,tierPlansEnabled:e}),setSiteDetails:e=>({type:s.Bz,siteDetails:e}),setSelectedLogoIndex:e=>({type:s.BG,selectedLogoIndex:e}),addLogoToHistory:e=>({type:s.sG,logo:e}),setIsSavingLogoToLibrary:e=>({type:s.E6,isSavingLogoToLibrary:e}),setIsApplyingLogo:e=>({type:s.Ep,isApplyingLogo:e}),updateSelectedLogo:(e,t)=>({type:s.r_,mediaId:e,url:t}),setIsRequestingImage:e=>({type:s.vk,isRequestingImage:e}),setIsEnhancingPrompt:e=>({type:s.O5,isEnhancingPrompt:e}),loadLogoHistory(e){const t=(0,n.$t)(e);return{type:s.ZI,history:t}},setFeatureFetchError:e=>({type:s.Rx,error:e}),setFirstLogoPromptFetchError:e=>({type:s.MQ,error:e}),setEnhancePromptFetchError:e=>({type:s.gG,error:e}),setLogoFetchError:e=>({type:s.q_,error:e}),setSaveToLibraryError:e=>({type:s.x0,error:e}),setLogoUpdateError:e=>({type:s.FN,error:e}),setContext:e=>({type:s.kZ,context:e}),setIsLoadingHistory:e=>({type:s.hY,isLoadingHistory:e})},o=i},9938:(e,t,r)=>{"use strict";r.d(t,{BG:()=>h,Bz:()=>c,E6:()=>f,Ep:()=>g,FN:()=>S,GB:()=>l,Kt:()=>i,Lg:()=>s,MQ:()=>A,O5:()=>y,Rx:()=>b,UU:()=>n,ZI:()=>v,c1:()=>o,gG:()=>C,hY:()=>E,kZ:()=>p,mx:()=>d,nb:()=>u,q_:()=>w,ql:()=>a,r_:()=>_,sG:()=>m,vk:()=>k,x0:()=>x});const n="STORE_AI_ASSISTANT_FEATURE",a="REQUEST_AI_ASSISTANT_FEATURE",s="INCREASE_AI_ASSISTANT_REQUESTS_COUNT",i="SET_AI_ASSISTANT_FEATURE_REQUIRE_UPGRADE",o="SET_TIER_PLANS_ENABLED",c="SET_SITE_DETAILS",l=20,u=3e3,d=3,p="SET_CONTEXT",h="SET_SELECTED_LOGO_INDEX",m="ADD_LOGO_TO_HISTORY",f="SET_IS_SAVING_LOGO_TO_LIBRARY",g="SET_IS_APPLYING_LOGO",_="SAVE_SELECTED_LOGO",k="SET_IS_REQUESTING_IMAGE",y="SET_IS_ENHANCING_PROMPT",v="SET_SITE_HISTORY",E="SET_IS_LOADING_HISTORY",b="SET_FEATURE_FETCH_ERROR",A="SET_FIRST_LOGO_PROMPT_FETCH_ERROR",C="SET_ENHANCE_PROMPT_FETCH_ERROR",w="SET_LOGO_FETCH_ERROR",x="SET_SAVE_TO_LIBRARY_ERROR",S="SET_LOGO_UPDATE_ERROR"},4423:(e,t,r)=>{"use strict";var n=r(7143),a=r(9624),s=r(3695),i=r(37);const o=(0,n.createReduxStore)("jetpack-ai/logo-generator",{__experimentalUseThunks:!0,actions:a.A,reducer:s.A,selectors:i.A});(0,n.register)(o)},4891:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(9938);const a={siteDetails:{},features:{aiAssistantFeature:{hasFeature:!0,isOverLimit:!1,requestsCount:0,requestsLimit:n.GB,requireUpgrade:!1,errorMessage:"",errorCode:"",upgradeType:"default",currentTier:{slug:"ai-assistant-tier-free",value:0,limit:20},usagePeriod:{currentStart:"",nextStart:"",requestsCount:0},nextTier:null,tierPlansEnabled:!1,_meta:{isRequesting:!1,asyncRequestCountdown:n.mx,asyncRequestTimerId:0,isRequestingImage:!1},featuresControl:{"logo-generator":{enabled:!1,styles:[]}}}},history:[],selectedLogoIndex:0}},3695:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(1356),a=r(9938),s=r(4891);function i(e=s.A,t){switch(t.type){case a.ql:return{...e,_meta:{...e._meta??{},featureFetchError:null},features:{...e.features,aiAssistantFeature:{...e.features.aiAssistantFeature,_meta:{...e?.features?.aiAssistantFeature?._meta,isRequesting:!0,asyncRequestCountdown:a.mx,asyncRequestTimerId:0}}}};case a.UU:{const r={"jetpack-ai-logo-generator":{logo:n._S}};return{...e,features:{...e.features,aiAssistantFeature:{costs:r,...t.feature,requireUpgrade:t.feature?.requireUpgrade||0===t.feature?.currentTier?.value,_meta:{...e?.features?.aiAssistantFeature?._meta,isRequesting:!1}}}}}case a.Lg:{const r=e?.features?.aiAssistantFeature?.usagePeriod||{requestsCount:0},n=(e?.features?.aiAssistantFeature?.requestsCount||0)+(t.count??1);r.requestsCount+=t.count??1;const s=e?.features?.aiAssistantFeature?.currentTier?.value,i=void 0===s&&!e?.features?.aiAssistantFeature?.hasFeature||0===s,o=void 0===s&&e?.features?.aiAssistantFeature?.hasFeature||1===s;let c=e?.features?.aiAssistantFeature?.currentTier?.limit||a.GB;o?c=a.nb:i&&(c=e?.features?.aiAssistantFeature?.requestsLimit);const l=(o||i?n:e?.features?.aiAssistantFeature?.usagePeriod?.requestsCount||0)>=c,u=i||l&&null!==e?.features?.aiAssistantFeature?.nextTier;return{...e,features:{...e.features,aiAssistantFeature:{...e.features.aiAssistantFeature,isOverLimit:l,requestsCount:n,requireUpgrade:u,usagePeriod:{...r}}}}}case a.Kt:return{...e,features:{...e.features,aiAssistantFeature:{...e.features.aiAssistantFeature,requireUpgrade:t.requireUpgrade,...t.requireUpgrade?{isOverLimit:!0}:{}}}};case a.c1:return{...e,features:{...e.features,aiAssistantFeature:{...e.features.aiAssistantFeature,tierPlansEnabled:t.tierPlansEnabled}}};case a.Bz:return{...e,siteDetails:t.siteDetails};case a.BG:return{...e,selectedLogoIndex:t.selectedLogoIndex};case a.sG:{const r=[...e.history,t.logo];return{...e,history:r,selectedLogoIndex:r.length-1}}case a.E6:return{...e,_meta:{...e._meta??{},isSavingLogoToLibrary:t.isSavingLogoToLibrary}};case a.Ep:return{...e,_meta:{...e._meta??{},isApplyingLogo:t.isApplyingLogo}};case a.r_:{const r=e.history?.[e.selectedLogoIndex];return{...e,history:[...e.history.slice(0,e.selectedLogoIndex),{...r,mediaId:t.mediaId,url:t.url},...e.history.slice(e.selectedLogoIndex+1)]}}case a.vk:return{...e,_meta:{...e._meta??{},isRequestingImage:t.isRequestingImage}};case a.O5:return{...e,_meta:{...e._meta??{},isEnhancingPrompt:t.isEnhancingPrompt}};case a.ZI:return{...e,history:t.history,selectedLogoIndex:t.history?.length?t.history.length-1:0};case a.Rx:return{...e,features:{...e.features,aiAssistantFeature:{...e?.features?.aiAssistantFeature,_meta:{...e?.features?.aiAssistantFeature?._meta,isRequesting:!1}}},_meta:{...e._meta??{},featureFetchError:t.error}};case a.MQ:return{...e,_meta:{...e._meta??{},firstLogoPromptFetchError:t.error}};case a.gG:return{...e,_meta:{...e._meta??{},enhancePromptFetchError:t.error}};case a.q_:return{...e,_meta:{...e._meta??{},logoFetchError:t.error}};case a.x0:return{...e,_meta:{...e._meta??{},saveToLibraryError:t.error}};case a.FN:return{...e,_meta:{...e._meta??{},logoUpdateError:t.error}};case a.kZ:return{...e,_meta:{...e._meta??{},context:t.context}};case a.hY:return{...e,_meta:{...e._meta??{},isLoadingHistory:t.isLoadingHistory}}}return e}},37:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(1356);const a={getAiAssistantFeature(e){const t={...e.features.aiAssistantFeature};return delete t._meta,t},getSiteDetails:e=>e.siteDetails,getIsRequestingAiAssistantFeature:e=>e.features.aiAssistantFeature?._meta?.isRequesting??!1,getLogos:e=>e.history??[],getSelectedLogoIndex:e=>e.selectedLogoIndex??null,getSelectedLogo:e=>e.history?.[e.selectedLogoIndex]??null,getIsSavingLogoToLibrary:e=>e._meta?.isSavingLogoToLibrary??!1,getIsApplyingLogo:e=>e._meta?.isApplyingLogo??!1,getIsEnhancingPrompt:e=>e._meta?.isEnhancingPrompt??!1,getIsRequestingImage:e=>e._meta?.isRequestingImage??!1,getIsBusy:e=>a.getIsApplyingLogo(e)||a.getIsSavingLogoToLibrary(e)||a.getIsRequestingImage(e)||a.getIsEnhancingPrompt(e),getRequireUpgrade(e){const t=e.features.aiAssistantFeature;if(!t?.tierPlansEnabled)return t?.requireUpgrade;const r=t?.costs?.["jetpack-ai-logo-generator"]?.logo??n._S,a=t?.currentTier?.value||0,s=t?.usagePeriod?.requestsCount||0,i=1===a,o=!t?.nextTier;return e.features.aiAssistantFeature?.requireUpgrade||!i&&!o&&a-s<r},getFeatureFetchError:e=>e._meta?.featureFetchError??null,getFirstLogoPromptFetchError:e=>e._meta?.firstLogoPromptFetchError??null,getEnhancePromptFetchError:e=>e._meta?.enhancePromptFetchError??null,getLogoFetchError:e=>e._meta?.logoFetchError??null,getSaveToLibraryError:e=>e._meta?.saveToLibraryError??null,getLogoUpdateError:e=>e._meta?.logoUpdateError??null,getContext:e=>e._meta?.context??"",getTierPlansEnabled:e=>e.features.aiAssistantFeature?.tierPlansEnabled??!1,getIsLoadingHistory:e=>e._meta?.isLoadingHistory??!1},s=a},3664:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(7675),a=r(6941),s=r.n(a),i=r(6219),o=r(1391),c=r(8836);const l=s()("jetpack-ai-client:suggestions-event-source");class u extends EventTarget{constructor(e){super(),this.fullMessage="",this.fullFunctionCall={name:"",arguments:""},this.isPromptClear=!1,this.controller=new AbortController,this.initEventSource(e)}async initEventSource({url:e,question:t,token:r,options:a={}}){if(!r)try{l("Token was not provided, requesting one..."),r=(await(0,o.A)()).token}catch(e){return void this.processErrorEvent(e)}const s={};if(Number.isInteger(parseInt(a.postId))&&(s.post_id=+a.postId),!e){const t=new URL("https://public-api.wordpress.com/wpcom/v2/jetpack-ai-query");a?.fromCache&&t.searchParams.append("stream_cache","true"),e=t.toString(),l("URL not provided, using default: %o",e)}Array.isArray(t)?s.messages=t:s.question=t,a?.feature?.length&&(l("Feature: %o",a.feature),s.feature=a.feature),a?.functions?.length&&(l("Functions: %o",a.functions),s.functions=a.functions),a?.model?.length&&(l("Model: %o",a.model),s.model=a.model),this.errorUnclearPromptTriggered=!1,await(0,n.y)(e,{openWhenHidden:!0,method:"POST",headers:{"Content-type":"application/json",Authorization:"Bearer "+r},body:JSON.stringify(s),onclose:()=>{l("Stream closed")},onerror:e=>{throw this.processErrorEvent(e),e},onmessage:e=>{this.processEvent(e)},onopen:async e=>{if(e.ok)return;let t;throw e.status>=400&&e.status<=500&&![413,422,429].includes(e.status)&&(l("Connection error: %o",e),t=c.mA,this.dispatchEvent(new CustomEvent(c.mA,{detail:e}))),503===e.status&&(t=c.AZ,this.dispatchEvent(new CustomEvent(c.AZ))),413===e.status&&(t=c.cT,this.dispatchEvent(new CustomEvent(c.cT))),422===e.status&&(t=c.ud,this.dispatchEvent(new CustomEvent(c.ud))),429===e.status&&(t=c.Or,this.dispatchEvent(new CustomEvent(c.Or))),this.dispatchEvent(new CustomEvent(c.zn,{detail:(0,i.fC)(t)})),new Error},signal:this.controller.signal})}checkForUnclearPrompt(){if(this.isPromptClear)return;const e=this.fullMessage.replace(/__|(\*\*)/g,"");if(e.startsWith("JETPACK_AI_ERROR")){if(this.errorUnclearPromptTriggered)return;this.errorUnclearPromptTriggered=!0,this.dispatchEvent(new CustomEvent(c.L9)),l("Unclear error prompt dispatched"),this.dispatchEvent(new CustomEvent(c.zn,{detail:(0,i.fC)(c.L9)}))}else"JETPACK_AI_ERROR".startsWith(e)?l(this.fullMessage):this.isPromptClear=!0}close(){this.controller.abort()}processEvent(e){if("[DONE]"===e.data){if(this.errorUnclearPromptTriggered)return;if(this.fullMessage.length)return this.dispatchEvent(new CustomEvent("done",{detail:this.fullMessage})),void l("Done: %o",this.fullMessage);if(this.fullFunctionCall.name.length)return this.dispatchEvent(new CustomEvent("function_done",{detail:this.fullFunctionCall})),void l("Done: %o",this.fullFunctionCall)}let t;try{t=JSON.parse(e.data)}catch(t){return void l("Error parsing JSON",e,t)}const{delta:r}=t?.choices?.[0]??{delta:{content:null,function_call:null}},n=r.content,a=r.function_call;n&&(this.fullMessage+=n,this.checkForUnclearPrompt(),this.isPromptClear&&(this.dispatchEvent(new CustomEvent("chunk",{detail:n})),l("suggestion: %o",this.fullMessage),this.dispatchEvent(new CustomEvent("suggestion",{detail:this.fullMessage})))),a&&(null!=a.name&&(this.fullFunctionCall.name+=a.name),null!=a.arguments&&(this.fullFunctionCall.arguments+=a.arguments),this.dispatchEvent(new CustomEvent("functionCallChunk",{detail:this.fullFunctionCall})))}processErrorEvent(e){l("onerror: %o",e),this.dispatchEvent(new CustomEvent(c.mA,{detail:e})),this.dispatchEvent(new CustomEvent(c.zn,{detail:(0,i.fC)(c.mA)}))}}},8836:(e,t,r)=>{"use strict";r.d(t,{AZ:()=>n,L9:()=>c,Or:()=>a,cT:()=>i,mA:()=>o,ud:()=>s,zn:()=>l});r(7784);const n="error_service_unavailable",a="error_quota_exceeded",s="error_moderation",i="error_context_too_large",o="error_network",c="error_unclear_prompt",l="error_response"},372:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(6941);const a=r.n(n)()("dops:analytics");let s,i;window._tkq=window._tkq||[],window.ga=window.ga||function(){(window.ga.q=window.ga.q||[]).push(arguments)},window.ga.l=+new Date;const o={initialize:function(e,t,r){o.setUser(e,t),o.setSuperProps(r),o.identifyUser()},setGoogleAnalyticsEnabled:function(e,t=null){this.googleAnalyticsEnabled=e,this.googleAnalyticsKey=t},setMcAnalyticsEnabled:function(e){this.mcAnalyticsEnabled=e},setUser:function(e,t){i={ID:e,username:t}},setSuperProps:function(e){s=e},assignSuperProps:function(e){s=Object.assign(s||{},e)},mc:{bumpStat:function(e,t){const r=function(e,t){let r="";if("object"==typeof e){for(const t in e)r+="&x_"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);a("Bumping stats %o",e)}else r="&x_"+encodeURIComponent(e)+"="+encodeURIComponent(t),a('Bumping stat "%s" in group "%s"',t,e);return r}(e,t);o.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom-no-pv"+r+"&t="+Math.random())},bumpStatWithPageView:function(e,t){const r=function(e,t){let r="";if("object"==typeof e){for(const t in e)r+="&"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);a("Built stats %o",e)}else r="&"+encodeURIComponent(e)+"="+encodeURIComponent(t),a('Built stat "%s" in group "%s"',t,e);return r}(e,t);o.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom"+r+"&t="+Math.random())}},pageView:{record:function(e,t){o.tracks.recordPageView(e),o.ga.recordPageView(e,t)}},purchase:{record:function(e,t,r,n,a,s,i){o.ga.recordPurchase(e,t,r,n,a,s,i)}},tracks:{recordEvent:function(e,t){t=t||{},0===e.indexOf("akismet_")||0===e.indexOf("jetpack_")?(s&&(a("- Super Props: %o",s),t=Object.assign(t,s)),a('Record event "%s" called with props %s',e,JSON.stringify(t)),window._tkq.push(["recordEvent",e,t])):a('- Event name must be prefixed by "akismet_" or "jetpack_"')},recordJetpackClick:function(e){const t="object"==typeof e?e:{target:e};o.tracks.recordEvent("jetpack_wpa_click",t)},recordPageView:function(e){o.tracks.recordEvent("akismet_page_view",{path:e})},setOptOut:function(e){a("Pushing setOptOut: %o",e),window._tkq.push(["setOptOut",e])}},ga:{initialized:!1,initialize:function(){let e={};o.ga.initialized||(i&&(e={userId:"u-"+i.ID}),window.ga("create",this.googleAnalyticsKey,"auto",e),o.ga.initialized=!0)},recordPageView:function(e,t){o.ga.initialize(),a("Recording Page View ~ [URL: "+e+"] [Title: "+t+"]"),this.googleAnalyticsEnabled&&(window.ga("set","page",e),window.ga("send",{hitType:"pageview",page:e,title:t}))},recordEvent:function(e,t,r,n){o.ga.initialize();let s="Recording Event ~ [Category: "+e+"] [Action: "+t+"]";void 0!==r&&(s+=" [Option Label: "+r+"]"),void 0!==n&&(s+=" [Option Value: "+n+"]"),a(s),this.googleAnalyticsEnabled&&window.ga("send","event",e,t,r,n)},recordPurchase:function(e,t,r,n,a,s,i){window.ga("require","ecommerce"),window.ga("ecommerce:addTransaction",{id:e,revenue:n,currency:i}),window.ga("ecommerce:addItem",{id:e,name:t,sku:r,price:a,quantity:s}),window.ga("ecommerce:send")}},identifyUser:function(){i&&window._tkq.push(["identifyUser",i.ID,i.username])},setProperties:function(e){window._tkq.push(["setProperties",e])},clearedIdentity:function(){window._tkq.push(["clearIdentity"])}},c=o},237:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(6072),a=r.n(n),s=r(6427);const i=s.__experimentalNumberControl||function(e){return React.createElement(s.TextControl,a()({type:"number",inputMode:"numeric"},e,{__next40pxDefaultSize:!0}))}},6281:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(6221);const a=({value:e="https://jetpack.com",size:t=248,bgColor:r,fgColor:a,level:s,includeMargin:i,imageSettings:o,renderAs:c="canvas"})=>{const l={value:e,size:t,bgColor:r,fgColor:a,level:s,includeMargin:i,imageSettings:o};return"svg"===c?React.createElement(n.h,l):React.createElement(n.X,l)}},3924:(e,t,r)=>{"use strict";function n(e,t={}){const r={};let n;if("undefined"!=typeof window&&(n=window?.JP_CONNECTION_INITIAL_STATE?.calypsoEnv),0===e.search("https://")){const t=new URL(e);e=`https://${t.host}${t.pathname}`,r.url=encodeURIComponent(e)}else r.source=encodeURIComponent(e);for(const e in t)r[e]=encodeURIComponent(t[e]);!Object.keys(r).includes("site")&&"undefined"!=typeof jetpack_redirects&&Object.hasOwn(jetpack_redirects,"currentSiteRawUrl")&&(r.site=jetpack_redirects.currentBlogID??jetpack_redirects.currentSiteRawUrl),n&&(r.calypso_env=n);return"https://jetpack.com/redirect/?"+Object.keys(r).map((e=>e+"="+r[e])).join("&")}r.d(t,{A:()=>n})},5985:(e,t,r)=>{"use strict";r.d(t,{Bd:()=>c.Bd,CP:()=>u.C,FB:()=>s.A,GE:()=>n.A,Ql:()=>l.A,Sy:()=>a.Sy,Ti:()=>c.Ti,ZR:()=>o.A,_X:()=>c._X,d9:()=>a.d9,st:()=>i.A});r(2810);var n=r(4815),a=(r(1409),r(2634)),s=r(703),i=(r(2034),r(5595),r(3265),r(3489)),o=r(7119),c=r(710),l=r(8406),u=(r(6923),r(335),r(8290),r(9061));r(5929),r(5765)},5765:(e,t,r)=>{"use strict";r(8490)},2810:(e,t,r)=>{"use strict";r.d(t,{V:()=>i});var n=r(8377),a=r(2634);const s=n.T["Jetpack Green 40"];function i(){return(0,a.d9)()||(0,a.Sy)()?null:s}},2201:(e,t,r)=>{"use strict";r.d(t,{c:()=>n.c});r(5877);var n=r(984)},5877:(e,t,r)=>{"use strict";r(1609)},984:(e,t,r)=>{"use strict";r.d(t,{c:()=>c});var n=r(6427),a=r(7723),s=r(3022),i=r(1609),o=r.n(i);const __=a.__,c=({className:e,description:t,align:r=null,title:a=null,buttonText:i=null,visible:c=!0,context:l=null,checkoutUrl:u=null,goToCheckoutPage:d=null,isRedirecting:p=!1,showButton:h=!0,target:m="_top"})=>{const f=(0,s.A)(e,"jetpack-upgrade-plan-banner",{"wp-block":"editor-canvas"===l,"block-editor-block-list__block":"editor-canvas"===l,"jetpack-upgrade-plan__hidden":!c}),g=__("Redirecting…","jetpack-external-media");return o().createElement("div",{className:f,"data-align":r},o().createElement("div",{className:"jetpack-upgrade-plan-banner__wrapper"},a&&o().createElement("strong",{className:(0,s.A)("banner-title",{[`${e}__title`]:e})},a),t&&o().createElement("span",{className:`${e}__description banner-description`},t),h&&o().createElement(n.Button,{href:p?null:u,onClick:d,target:m,className:(0,s.A)("is-primary",{"jetpack-upgrade-plan__hidden":!u}),isBusy:p},p?g:i)))}},335:(e,t,r)=>{"use strict";r(8468)},4972:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});const n="Jetpack_Editor_Initial_State";function a(){return"object"==typeof window?window?.[n]??null:null}},703:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(4972);function a(e){const t=(0,n.A)(),r=t?.available_blocks?.[e]?.available??!1,a=t?.available_blocks?.[e]?.unavailable_reason??"unknown";return{available:r,...!r&&{details:t?.available_blocks?.[e]?.details??[],unavailableReason:a}}}},4815:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(7999);function a(){return window&&window.Jetpack_Editor_Initial_State&&window.Jetpack_Editor_Initial_State.siteFragment?window.Jetpack_Editor_Initial_State.siteFragment:(0,n.getScriptData)()?.site?.suffix??null}},3489:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(372),a=r(9384),s=r(8468);const{tracks:i}=n.A,{recordEvent:o}=i,c=({pageViewEventName:e=null,pageViewNamespace:t="jetpack",pageViewSuffix:r="page_view",pageViewEventProperties:c={}}={})=>{const[l,u]=(0,s.useState)(!1),{isUserConnected:d,isRegistered:p,userConnectionData:h={}}=(0,a.useConnection)(),{wpcomUser:{login:m,ID:f}={},blogId:g}=h.currentUser||{},_=(0,s.useCallback)((async(e,t={})=>{d&&f&&m&&o(e,t)}),[d,f,m]);return(0,s.useEffect)((()=>{d&&f&&m&&g&&n.A.initialize(f,m,{blog_id:g})}),[g,f,m,d]),(0,s.useEffect)((()=>{const n=e?`${t}_${e}_${r}`:null;p&&n&&(l||(_(n,c),u(!0)))}),[l,t,e,r,p,c,_]),{recordEvent:_,tracks:i}}},7119:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(7143),a=r(8468),s=r(6087);function i(e=null,t=s.noop){const[r,i]=(0,a.useState)(!1),{isAutosaveablePost:o,isDirtyPost:c,currentPost:l}=(0,n.useSelect)((e=>{const t=e("core/editor");return{isAutosaveablePost:t.isEditedPostAutosaveable(),isDirtyPost:t.isEditedPostDirty(),currentPost:t.getCurrentPost()}}),[]),u=Object.keys(l).length>0,d=(0,n.useSelect)((e=>!!window.wp?.customize||!!e("core/edit-widgets"))),p=(0,n.dispatch)("core/editor").savePost,h=(0,n.useSelect)((e=>e("core").__experimentalGetDirtyEntityRecords())),m=async e=>{e.preventDefault(),u?c&&o&&await p(e):await(async()=>{for(let e=0;e<h.length;e++)await(0,n.dispatch)("core").saveEditedEntityRecord(h[e].kind,h[e].name,h[e].key)})()};return{autosave:m,autosaveAndRedirect:async n=>{n.preventDefault(),r||(i(!0),m(n).then((()=>{e&&function(e,t,r=!1){t&&t(e),r?window.open(e,"_blank"):window.top.location.href=e}(e,t,d)})))},isRedirecting:r}}},6923:(e,t,r)=>{"use strict";r(7143),r(8468),r(8290)},710:(e,t,r)=>{"use strict";r.d(t,{Bd:()=>s,Ti:()=>a,_X:()=>n});const n="free",a="unlimited",s=e=>e?0===e?.value?n:1===e?.value?a:"tiered":null},8406:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(8468);const a=e=>{e&&cancelAnimationFrame(e.id)},s=(e,t=0)=>{const r=(0,n.useRef)(),s=(0,n.useRef)(e);s.current=e,(0,n.useEffect)((()=>(r.current=((e,t=0)=>{const r=t<0?0:t,n={id:0};let a=Date.now();const s=()=>{const t=Date.now();t-a>=r&&(a=t,e()),n.id=requestAnimationFrame(s)};return n.id=requestAnimationFrame(s),n})((()=>{s.current()}),t),()=>{a(r.current)})),[t]);return(0,n.useCallback)((()=>{a(r.current)}),[])}},5929:(e,t,r)=>{"use strict";r(7143),r(2619),r(3265),r(7119)},2410:(e,t,r)=>{"use strict";r.d(t,{AR:()=>d,Bl:()=>f,mI:()=>m,mb:()=>u,p3:()=>p,tC:()=>h,v7:()=>g});var n=r(6072),a=r.n(n),s=r(8377),i=r(6427),o=r(1609),c=r.n(o),l=r(2810);s.T["Jetpack Green 40"];const u=e=>c().createElement(i.SVG,a()({height:"24",width:"24",viewBox:"1.2 10.8 169.6 169.6",xmlns:"http://www.w3.org/2000/svg"},e),c().createElement(i.Path,{d:"m43.6 49.4c23.4 0 42.4 19 42.4 42.4v3.9h-80.9c-2.1 0-3.8-1.7-3.9-3.9 0-23.4 19-42.4 42.4-42.4z",fill:"#f6bc04"}),c().createElement(i.Path,{d:"m132.2 53.2c0 23.4-19 42.4-42.4 42.4h-3.8v-80.9c0-2.1 1.7-3.9 3.8-3.9 23.5 0 42.4 19 42.4 42.4z",fill:"#ea4335"}),c().createElement(i.Path,{d:"m128.4 141.9c-23.4 0-42.4-19-42.4-42.4v-3.9h80.9c2.1 0 3.9 1.7 3.9 3.8 0 23.5-19 42.5-42.4 42.5z",fill:"#4285f4"}),c().createElement(i.Path,{d:"m39.8 138c0-23.4 19-42.4 42.4-42.4h3.8v80.9c0 2.1-1.7 3.8-3.9 3.9-23.4 0-42.3-19-42.3-42.4z",fill:"#34a853"})),d=e=>c().createElement(i.SVG,a()({viewBox:"0 0 160 160",enableBackground:"new 0 0 160 160",xmlns:"http://www.w3.org/2000/svg"},e),c().createElement(i.Path,{d:"M25.7 133.8c10.4-.3 20.8-.5 31.2-.6l31.2-.4s20.8-.1 31.2-.1l31.2-.1-1.4 1.4-.3-13.3c-.1-4.4-.3-8.9-.1-13.3v-26.9l.1-53.5 1.3 1.3-62.2-.3-31.1-.3-31.1-.3.6-.6-.1 9.4c0 .3-.2.5-.5.5s-.5-.2-.5-.5l-.1-9.4c0-.3.2-.6.6-.6h.1l31.1-.2h30.9l62.1-.3c.7 0 1.3.6 1.3 1.3l.1 53.5v26.8c0 8.9.3 17.9.4 26.8 0 .8-.6 1.4-1.4 1.4l-31.2-.1c-10.4 0-20.8 0-31.2-.1-10.4-.1-20.8-.1-31.2-.4l-31.2-.6c-.1 0-.2-.1-.3-.3.2-.1.4-.1.5-.2zm1-60.7v-2.4c0-1.6-.1-3.3-.5-4.9 0-.2-.1-.3-.3-.4h-.5c-.1.1-.2.2-.2.3-.4 1.6-.5 3.2-.5 4.9-.1 1.7-.1 3.2-.1 4.9 0 .8 0 1.6.1 2.4.1 2.3.1 4.8.4 7.1 0 .3.3.6.6.6s.5-.2.6-.5c.3-2.3.5-4.5.4-6.8 0-.9 0-1.8.1-2.7l-.1-2.5z",fill:"#a7aaad"}),c().createElement(i.Path,{d:"M17.2 121.2h122s-4.6-28.1-19.1-32.1c-12.8-3.5-17.9 4.3-28.5 4.3-8.5 0-11.8-20-28-20-30.7-.2-46.4 47.8-46.4 47.8z",fill:"none",stroke:"#a7aaad",strokeWidth:"1.2679",strokeLinecap:"round",strokeLinejoin:"round",strokeDasharray:"6.3396,6.3396"}),c().createElement(i.Circle,{cx:"116.8",cy:"58.9",r:"10.9",fill:"none",stroke:"#a7aaad",strokeWidth:"1.2679",strokeLinecap:"round",strokeLinejoin:"round",strokeDasharray:"6.3396,6.3396"}),c().createElement(i.Path,{d:"M7.3 57.8l6.3-.1c.3 0 .5.2.5.5s-.2.5-.5.5l-6.3-.1c-.2 0-.4-.2-.4-.4-.1-.3.1-.4.4-.4zm9.6-3.1c.6-2.1 1.6-4 2.8-5.8.1-.3.5-.3.8-.1.3.1.3.5.1.8-1.2 1.7-2.1 3.6-2.7 5.6-.1.2-.4.4-.6.4-.4-.4-.5-.6-.4-.9zm7.9-10c1-.4 2.2-.6 3.3-.5 1.1 0 2.2.2 3.3.6.3.1.5.5.4.8-.1.3-.5.5-.8.4-1.8-.7-3.9-.8-5.7-.1-.3.1-.6 0-.7-.3v-.1c-.3-.4-.1-.7.2-.8zm10.9 4.7c.3.2.6.2.9.1.5-.3 1-.5 1.5-.6 1.1-.3 2.2-.4 3.3-.4.4 0 .6.3.6.6 0 .4-.3.6-.6.6-1 0-2 .1-2.9.4-.5.2-1 .4-1.4.6-.7.3-1.5.3-2.2-.1-.3-.3-.3-.8 0-1.1.2-.1.5-.2.8-.1zm11.6 2c.6 1 1 2 1.1 3.2.2 1.1.2 2.2.1 3.3 0 .3-.3.5-.6.5s-.5-.2-.5-.5c.2-2-.3-4.1-1.3-5.8-.1-.3-.1-.6.1-.8.4-.2 1-.2 1.1.1zm6.8 6.3h4.7c.2 0 .4.2.4.4s-.1.4-.3.4h-4.8c-.3 0-.5-.2-.5-.5.1 0 .2-.3.5-.3z",fill:"#a7aaad"})),p=e=>c().createElement(i.SVG,a()({style:{fill:"#30272e",background:"#ffe033",padding:"3px",width:"22px",height:"24px"},width:"46",height:"42",viewBox:"0 0 46 42",xmlns:"http://www.w3.org/2000/svg"},e),c().createElement(i.G,null,c().createElement(i.Path,{d:"M0 9.3975C0 14.5687 4.1722 18.795 9.3353 18.795V0C4.1722 0 0 4.2 0 9.3975Z"}),c().createElement(i.Path,{d:"M13.6118 9.3975C13.6118 14.5687 17.784 18.795 22.9471 18.795V0C17.8101 0 13.6118 4.2 13.6118 9.3975Z"}),c().createElement(i.Path,{d:"M36.5589 18.795C41.7147 18.795 45.8942 14.5876 45.8942 9.3975C45.8942 4.2074 41.7147 0 36.5589 0C31.4032 0 27.2236 4.2074 27.2236 9.3975C27.2236 14.5876 31.4032 18.795 36.5589 18.795Z"}),c().createElement(i.Path,{d:"M0 32.6025C0 37.8 4.1722 42 9.3353 42V23.2312C4.1722 23.2312 0 27.4312 0 32.6025Z"}),c().createElement(i.Path,{d:"M13.6118 32.5238C13.6118 37.695 17.784 41.9213 22.9471 41.9213V23.1525C17.8101 23.1525 13.6118 27.3525 13.6118 32.5238Z"}),c().createElement(i.Path,{d:"M36.5589 41.9212C41.7147 41.9212 45.8942 37.7138 45.8942 32.5238C45.8942 27.3337 41.7147 23.1263 36.5589 23.1263C31.4032 23.1263 27.2236 27.3337 27.2236 32.5238C27.2236 37.7138 31.4032 41.9212 36.5589 41.9212Z"}))),h=e=>c().createElement(i.SVG,a()({xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},e),c().createElement(i.Path,{d:"M14 7H9v10h3.9v-3.8H14c1.7 0 3.1-1.4 3.1-3.1C17.2 8.4 15.8 7 14 7z"}),c().createElement(i.Path,{d:"M20.5 2h-17C2.7 2 2 2.7 2 3.5v17c0 .8.7 1.5 1.5 1.5h17c.8 0 1.5-.7 1.5-1.5v-17c0-.8-.7-1.5-1.5-1.5zm-5.6 13.2V19H7V5h7c2.8 0 5.1 2.3 5.1 5.1.1 2.5-1.8 4.7-4.2 5.1z"})),m=()=>{const e="#757575";return c().createElement(i.SVG,{version:"1.1",id:"Layer_1",xmlns:"http://www.w3.org/2000/SVG",x:"0px",y:"0px",viewBox:"0 0 1292 448",style:{enableBackground:"new 0 0 1292 448"}},c().createElement(i.G,{id:"Icon"},c().createElement(i.Path,{class:"st0",fill:"#F6B704",d:"M171.6,177.4c23.4,0,42.4,19,42.4,42.4v3.9h-80.9c-2.1,0-3.8-1.7-3.9-3.9C129.2,196.4,148.2,177.4,171.6,177.4 C171.6,177.4,171.6,177.4,171.6,177.4z"}),c().createElement(i.Path,{class:"st1",fill:"#E54335",d:"M260.2,181.2c0,23.4-19,42.4-42.4,42.4H214v-80.9c0-2.1,1.7-3.9,3.8-3.9h0 C241.3,138.8,260.2,157.8,260.2,181.2C260.2,181.2,260.2,181.2,260.2,181.2z"}),c().createElement(i.Path,{class:"st2",fill:"#4280EF",d:"M256.4,269.9c-23.4,0-42.4-19-42.4-42.4v-3.9h80.9c2.1,0,3.9,1.7,3.9,3.8v0 C298.8,250.9,279.8,269.9,256.4,269.9C256.4,269.9,256.4,269.9,256.4,269.9z"}),c().createElement(i.Path,{class:"st3",fill:"#34A353",d:"M167.8,266c0-23.4,19-42.4,42.4-42.4c0,0,0,0,0,0h3.9v80.9c0,2.1-1.7,3.8-3.9,3.9 C186.7,308.4,167.8,289.4,167.8,266z"})),c().createElement(i.G,{id:"Photos"},c().createElement(i.Path,{class:"st4",fill:e,d:"M796,272v-91.6h31.2c15.2,0,28.7,11.3,28.7,27.3s-13.4,27.3-28.7,27.3h-19.4V272H796z M827.5,223.6 c8.8,0,16-7.2,16-16s-7.2-16-16-16h-19.7v32H827.5z"}),c().createElement(i.Path,{class:"st4",fill:e,d:"M875.8,209.3l-0.5,8.7h0.5c3.3-5.8,11.3-10.8,19.8-10.8c16,0,23.9,10.9,23.9,26.4V272h-11.8v-36.6 c0-13-6.5-17.4-15.5-17.4c-10.2,0-16.5,9.7-16.5,19.3V272H864v-91.6h11.8V209.3z"}),c().createElement(i.Path,{class:"st4",fill:e,d:"M993.8,240.6c0,19.1-13.4,33.4-32.2,33.4s-32.2-14.3-32.2-33.4s13.4-33.4,32.2-33.4S993.8,221.6,993.8,240.6z M982,240.6c0-14.3-10-22.6-20.5-22.6s-20.5,8.3-20.5,22.6s10,22.7,20.5,22.7S982,255,982,240.6L982,240.6z"}),c().createElement(i.Path,{class:"st4",fill:e,d:"M1008,252.9V220h-11v-10.7h11v-19.2h11.8v19.2h15.3V220h-15.3v32c0,7,2.9,10.2,8.4,10.2c1.8,0.1,3.7-0.3,5.4-1 l4.1,10.1c-3,1.2-6.2,1.8-9.5,1.7C1015,273,1008,265.7,1008,252.9z"}),c().createElement(i.Path,{class:"st4",fill:e,d:"M1105.8,240.6c0,19.1-13.4,33.4-32.2,33.4s-32.2-14.3-32.2-33.4s13.4-33.4,32.2-33.4 S1105.8,221.6,1105.8,240.6z M1094,240.6c0-14.3-10-22.6-20.5-22.6s-20.5,8.3-20.5,22.6s10,22.7,20.5,22.7S1094,255,1094,240.6 L1094,240.6z"}),c().createElement(i.Path,{class:"st4",fill:e,d:"M1111,256.1l10.5-4.4c3.3,7.9,9.5,11.8,17,11.8c7.3,0,12.5-3.6,12.5-8.8c0-3.2-1.9-6.6-8.6-8.3l-12.7-3.1 c-5.8-1.4-16.8-6.4-16.8-17.4c0-11.4,11.8-18.7,24.8-18.7c10.9,0,20.3,5,24.2,14.6l-10.2,4.2c-2.4-5.9-8.2-8.3-14.3-8.3 c-6.7,0-12.4,3-12.4,8.1c0,4,3.3,6.1,8.3,7.3l12.4,2.9c12.4,2.9,17.3,10.6,17.3,18.3c0,11.1-10,19.7-24.7,19.7 C1123.2,274,1114.7,265.1,1111,256.1z"})),c().createElement(i.G,{id:"Google"},c().createElement(i.Path,{class:"st4",fill:e,d:"M348.7,221.6c0-28.9,24.3-52.5,53.2-52.5c13.5-0.2,26.4,5,36,14.5l-10.1,10.1c-6.9-6.7-16.2-10.4-25.9-10.2 c-21.1,0-37.6,17-37.6,38.2s16.5,38.1,37.6,38.1c13.7,0,21.5-5.5,26.5-10.5c4.1-4.1,6.8-10,7.8-18H402v-14.4h48.3 c0.5,3,0.8,6,0.8,9c0,10.8-2.9,24.1-12.4,33.5c-9.2,9.6-21,14.7-36.6,14.7C373,274,348.7,250.5,348.7,221.6z"}),c().createElement(i.Path,{class:"st4",fill:e,d:"M490,206.5c-18.7,0-33.9,14.2-33.9,33.8S471.3,274,490,274s33.9-14.3,33.9-33.8S508.7,206.5,490,206.5z M490,260.7c-10.2,0-19.1-8.4-19.1-20.5s8.8-20.5,19.1-20.5s19.1,8.3,19.1,20.5S500.2,260.7,490,260.7z"}),c().createElement(i.Path,{class:"st4",fill:e,d:"M564,206.5c-18.7,0-33.9,14.2-33.9,33.8S545.3,274,564,274s33.9-14.3,33.9-33.8S582.7,206.5,564,206.5z M564,260.7c-10.2,0-19.1-8.4-19.1-20.5s8.8-20.5,19.1-20.5s19.1,8.3,19.1,20.5S574.2,260.7,564,260.7z"}),c().createElement(i.Path,{class:"st4",fill:e,d:"M654.8,208.5v5.5h-0.5c-3.3-4-9.7-7.6-17.8-7.6c-16.9,0-32.4,14.9-32.4,33.9s15.5,33.7,32.4,33.7 c8.1,0,14.5-3.6,17.8-7.7h0.5v4.9c0,12.9-6.9,19.8-18,19.8c-9.1,0-14.7-6.5-17-12l-12.9,5.4c5,12.1,16.8,20.1,30,20 c17.4,0,32.1-10.2,32.1-35.2v-60.7H654.8z M637.8,260.7c-10.2,0-18.8-8.6-18.8-20.4s8.6-20.6,18.8-20.6s18,8.7,18,20.6 S647.9,260.7,637.8,260.7L637.8,260.7z"}),c().createElement(i.Rect,{x:"679.2",y:"172.7",class:"st4",fill:e,width:"14.8",height:"99.3"}),c().createElement(i.Path,{class:"st4",fill:e,d:"M734.7,260.7c-7.6,0-12.9-3.5-16.4-10.2l45.2-18.7L762,228c-2.8-7.6-11.4-21.5-28.9-21.5s-31.9,13.7-31.9,33.8 c0,19,14.3,33.8,33.5,33.8c11.3,0.1,21.9-5.6,28.2-15l-11.5-7.7C747.5,257,742.3,260.7,734.7,260.7z M733.6,219.5 c5.9,0,10.9,2.9,12.5,7.2l-30.2,12.5C715.5,226.2,726,219.5,733.6,219.5L733.6,219.5z"})))},f=e=>c().createElement(i.SVG,a()({width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),c().createElement(i.Rect,{width:"24",height:"24",rx:"5",fill:"#069E08"}),c().createElement(i.G,{clipPath:"url(#clip0_2_1297)"},c().createElement(i.Path,{d:"M11.9829 3C7.02857 3 3 7.02857 3 11.9829C3 16.9371 7.02857 20.9657 11.9829 20.9657C16.9371 20.9657 20.9657 16.9371 20.9657 11.9829C20.9657 7.02857 16.9371 3 11.9829 3ZM11.52 13.4743H7.04571L11.52 4.76571V13.4743ZM12.4286 19.1829V10.4743H16.9029L12.4286 19.1829Z",fill:"white"}))),g=()=>c().createElement(i.SVG,{width:"104",height:"59",viewBox:"0 0 104 59",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"jetpack-app-icon"},c().createElement(i.Path,{d:"M55 29.5C55 14.3475 42.6525 2 27.5 2C12.32 2 0 14.3475 0 29.5C0 44.68 12.32 57 27.5 57C42.6525 57 55 44.68 55 29.5ZM21.395 44.2675L12.0175 19.105C13.53 19.05 15.235 18.885 15.235 18.885C16.61 18.72 16.445 15.7775 15.07 15.8325C15.07 15.8325 11.0825 16.135 8.5525 16.135C8.0575 16.135 7.535 16.135 6.9575 16.1075C11.33 9.3975 18.8925 5.0525 27.5 5.0525C33.9075 5.0525 39.7375 7.445 44.1375 11.4875C42.2675 11.185 39.6 12.56 39.6 15.8325C39.6 17.8675 40.8375 19.5725 42.075 21.6075C43.0375 23.285 43.5875 25.3475 43.5875 28.3725C43.5875 32.47 39.7375 42.1225 39.7375 42.1225L31.405 19.105C32.89 19.05 33.66 18.6375 33.66 18.6375C35.035 18.5 34.87 15.2 33.495 15.2825C33.495 15.2825 29.535 15.6125 26.95 15.6125C24.5575 15.6125 20.5425 15.2825 20.5425 15.2825C19.1675 15.2 19.0025 18.5825 20.3775 18.6375L22.9075 18.8575L26.3725 28.235L21.395 44.2675ZM47.8775 29.5C48.5375 27.74 49.9125 24.3575 49.06 17.8125C50.985 21.36 51.9475 25.265 51.9475 29.5C51.9475 38.5475 47.19 46.66 39.8475 50.895C42.515 43.7725 45.1825 36.595 47.8775 29.5ZM16.775 51.7475C8.58 47.7875 3.0525 39.2075 3.0525 29.5C3.0525 25.925 3.685 22.68 5.0325 19.6275C8.9375 30.325 12.8425 41.05 16.775 51.7475ZM27.8575 33.515L34.9525 52.71C32.5875 53.5075 30.1125 53.9475 27.5 53.9475C25.3275 53.9475 23.1825 53.645 21.2025 53.04C23.43 46.495 25.6575 40.005 27.8575 33.515Z",fill:"#0675C4"}),c().createElement(i.Rect,{x:"46",y:"1",width:"57",height:"57",rx:"28.5",fill:"white"}),c().createElement(i.Path,{d:"M74.5 2C59.3497 2 47 14.316 47 29.5C47 44.6841 59.316 57 74.5 57C89.6841 57 102 44.6841 102 29.5C102 14.316 89.6841 2 74.5 2ZM73.0828 34.0552H59.3834L73.0828 7.39877V34.0552ZM75.8834 51.5337V24.8773H89.5491L75.8834 51.5337Z",fill:"#069E08"}),c().createElement(i.Rect,{x:"46",y:"1",width:"57",height:"57",rx:"28.5",stroke:"white",strokeWidth:"2"}));(0,l.V)(),i.SVG,i.Path,(0,l.V)(),i.SVG,i.Path,(0,l.V)(),i.SVG,i.Path,(0,l.V)(),i.SVG,i.Rect,i.G,i.Path,(0,l.V)(),i.SVG,i.Rect,i.G,i.Path,(0,l.V)(),i.SVG,i.Rect,i.G,i.Path,(0,l.V)(),i.SVG,i.Rect,i.G,i.Path,(0,l.V)(),i.Path,(0,l.V)(),i.Path,(0,l.V)(),(0,l.V)(),i.SVG,i.Rect,i.G,i.Path,i.Path,i.Path,i.SVG,i.Path,i.SVG,i.Path,i.SVG,i.Path,i.SVG,i.Path,i.Path,i.SVG,i.Path,i.SVG,i.Path,i.SVG,i.Path,i.SVG,i.Path,i.Path,i.SVG,i.Path,i.SVG,i.Path,i.SVG,i.Path,i.SVG,i.G,i.Path},9520:(e,t,r)=>{"use strict";r.d(t,{C:()=>o});var n=r(6941),a=r.n(n),s=r(2634);const i=window?.JP_CONNECTION_INITIAL_STATE;a()("shared-extension-utils:connection");function o(){if((0,s.Sy)())return!0;return!1==!(i?.userConnectionData?.currentUser?.permissions??{}).manage_options}},9061:(e,t,r)=>{"use strict";r.d(t,{C:()=>n.C});var n=r(9520)},7105:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>d,E9:()=>u});var n=r(7143),a=r(2634),s=r(4478),i=r(8290);const o="SET_JETPACK_MODULES";function c(e){return u({isLoading:e})}function l(e,t){return{type:"SET_MODULE_UPDATING",name:e,isUpdating:t}}function u(e){return{type:o,options:e}}const d={updateJetpackModuleStatus:function*(e){try{yield l(e.name,!0),yield(0,s.sB)(e);const t=yield(0,s.wz)();return yield u({data:t}),!0}catch{const e=(0,n.select)(i.F).getJetpackModules();return yield u(e),!1}finally{yield l(e.name,!1)}},setJetpackModules:u,fetchModules:function*(){if((0,a.Sy)())return!0;try{yield c(!0);const e=yield(0,s.wz)();return yield u({data:e}),!0}catch{const e=(0,n.select)(i.F).getJetpackModules();return yield u(e),!1}finally{yield c(!1)}}}},4478:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>l,sB:()=>c,wz:()=>o});var n=r(1455),a=r.n(n);const s="FETCH_JETPACK_MODULES",i="UPDATE_JETPACK_MODULE_STATUS",o=()=>({type:s}),c=e=>({type:i,settings:e}),l={[s]:function(){return a()({path:"/jetpack/v4/module/all",method:"GET"})},[i]:function({settings:e}){return a()({path:`/jetpack/v4/module/${e.name}/active`,method:"POST",data:{active:e.active}})}}},8290:(e,t,r)=>{"use strict";r.d(t,{F:()=>l});var n=r(7143),a=r(7105),s=r(4478),i=r(8862),o=r(2701),c=r(1640);const l="jetpack-modules",u=(0,n.createReduxStore)(l,{reducer:i.A,actions:a.Ay,controls:s.Ay,resolvers:o.A,selectors:c.A});(0,n.register)(u);const d=window?.Initial_State?.getModules||window?.Jetpack_Editor_Initial_State?.modules||null;null!==d&&(0,n.dispatch)(l).setJetpackModules({data:{...d}})},8862:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});const n={isLoading:!1,isUpdating:{},data:{}},a=(e=n,t)=>{switch(t.type){case"SET_JETPACK_MODULES":return{...e,...t.options};case"SET_MODULE_UPDATING":return{...e,isUpdating:{...e.isUpdating,[t.name]:t.isUpdating}}}return e}},2701:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(7105),a=r(4478);const s={getJetpackModules:function*(){try{const e=yield(0,a.wz)();if(e)return(0,n.E9)({data:e})}catch(e){console.error(e)}}}},1640:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(2634);const a={getJetpackModules:e=>e.data,isModuleActive:(e,t)=>(0,n.Sy)()||(e?.data?.[t]?.activated??!1),areModulesLoading:e=>e.isLoading??!1,isModuleUpdating:(e,t)=>e?.isUpdating?.[t]??!1}},3265:(e,t,r)=>{"use strict";var n=r(7723);r(3832),r(6087),r(4815);const __=n.__;__("Upgrade your plan to use video covers","jetpack-external-media"),__("Upgrade your plan to upload audio","jetpack-external-media")},2034:(e,t,r)=>{"use strict";r(2279)},1409:(e,t,r)=>{"use strict";r(7999)},2634:(e,t,r)=>{"use strict";function n(){return"object"==typeof window&&"string"==typeof window._currentSiteType?window._currentSiteType:null}function a(){return"simple"===n()}function s(){return"atomic"===n()}r.d(t,{Sy:()=>a,d9:()=>s})},5595:(e,t,r)=>{"use strict";r(6072),r(9491)},2186:(e,t,r)=>{"use strict";r.d(t,{DO:()=>S,FE:()=>d,Ic:()=>x,KS:()=>E,LI:()=>C,OH:()=>y,Pn:()=>u,Q8:()=>o,Q9:()=>w,Sh:()=>i,US:()=>p,_X:()=>l,aD:()=>g,bI:()=>f,fO:()=>m,gr:()=>v,kL:()=>c,o$:()=>h,oc:()=>k,ri:()=>_,xO:()=>b,yS:()=>A});var n=r(8443),a=r(7723),s=r(6087);const __=a.__,i="google_photos",o="openverse",c="pexels",l="jetpack_app_media",u="jetpack_ai_featured_image",d="jetpack_ai_general_purpose_image_for_media_source",p="jetpack_ai_general_purpose_image_for_block",h="recent",m="/",f=[{value:h,label:__("Photos","jetpack-external-media")},{value:m,label:__("Albums","jetpack-external-media")}],g="google_photos_picker_session",_=[{value:"",
/* translators: category of images */
label:__("All categories","jetpack-external-media")},{value:"animals",
/* translators: category of images */
label:__("Animals","jetpack-external-media")},{value:"arts",
/* translators: category of images */
label:__("Arts","jetpack-external-media")},{value:"birthdays",
/* translators: category of images */
label:__("Birthdays","jetpack-external-media")},{value:"cityscapes",
/* translators: category of images */
label:__("Cityscapes","jetpack-external-media")},{value:"crafts",
/* translators: category of images */
label:__("Crafts","jetpack-external-media")},{value:"fashion",
/* translators: category of images */
label:__("Fashion","jetpack-external-media")},{value:"food",
/* translators: category of images */
label:__("Food","jetpack-external-media")},{value:"flowers",
/* translators: category of images */
label:__("Flowers","jetpack-external-media")},{value:"gardens",
/* translators: category of images */
label:__("Gardens","jetpack-external-media")},{value:"holidays",
/* translators: category of images */
label:__("Holidays","jetpack-external-media")},{value:"houses",
/* translators: category of images */
label:__("Houses","jetpack-external-media")},{value:"landmarks",
/* translators: category of images */
label:__("Landmarks","jetpack-external-media")},{value:"landscapes",
/* translators: category of images */
label:__("Landscapes","jetpack-external-media")},{value:"night",
/* translators: category of images */
label:__("Night","jetpack-external-media")},{value:"people",
/* translators: category of images */
label:__("People","jetpack-external-media")},{value:"pets",
/* translators: category of images */
label:__("Pets","jetpack-external-media")},{value:"selfies",
/* translators: category of images */
label:__("Selfies","jetpack-external-media")},{value:"sport",
/* translators: category of images */
label:__("Sport","jetpack-external-media")},{value:"travel",
/* translators: category of images */
label:__("Travel","jetpack-external-media")},{value:"weddings",
/* translators: category of images */
label:__("Weddings","jetpack-external-media")}],k=["mountain","ocean","river","clouds","pattern","abstract","sky"],y="ANY",v="LAST_7_DAYS",E="LAST_30_DAYS",b="LAST_6_MONTHS",A="LAST_12_MONTHS",C="CUSTOM",w=[{value:y,label:__("Any time","jetpack-external-media")},{value:v,label:__("Last 7 days","jetpack-external-media")},{value:E,label:__("Last 30 days","jetpack-external-media")},{value:b,label:__("Last 6 months","jetpack-external-media")},{value:A,label:__("Last 12 months","jetpack-external-media")},{value:C,label:__("Specific Month/Year","jetpack-external-media")}],x=(new Date).getFullYear(),S=[{label:__("Any Month","jetpack-external-media"),value:-1},...(0,s.map)((0,s.range)(0,12),(e=>({label:(0,n.dateI18n)("F",new Date(0,e)),value:e})))]},2119:(e,t,r)=>{"use strict";r.d(t,{JC:()=>n.JC,Pk:()=>n.Pk});r(2186),r(5037),r(3155),r(4328),r(3809);var n=r(7698);r(6501)},5037:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var n=r(5985),a=r(6427),s=r(8468),i=r(7723),o=r(3022),c=r(1609),l=r.n(c),u=r(3682),d=r(3733),p=r(4096);const __=i.__;const h=function({media:e,mediaSource:t,isCopying:r,isLoading:i,imageOnly:c,pageHandle:h,className:m,multiple:f,setPath:g,nextPage:_,onCopy:k,selectButtonText:y,shouldProxyImg:v}){const[E,b]=(0,s.useState)([]),A=(0,s.useRef)(null),{tracks:C}=(0,n.st)(),w=(0,p.A)(),x=(0,s.useCallback)((e=>{let t=[e];"folder"===e.type?g(e.ID):f?(t=E.slice(0,9).concat(e),E.find((t=>e.ID===t.ID))&&(t=E.filter((t=>t.ID!==e.ID)))):1===E.length&&e.ID===E[0].ID&&(t=[]),b(t)}),[E,f,g]),S=(0,s.useCallback)((()=>{C.recordEvent("jetpack_external_media_modal_submit",{page:w,media_source:t,media_count:E.length,multiple:!!f}),k(E)}),[C,w,t,E,f,k]),D=e.filter((e=>"folder"!==e.type)).length>0,F=(e,{item:t})=>{x(t)};return(0,s.useEffect)((()=>{const e=A.current?.lastElementChild;let t;return h&&!i&&e&&(t=new window.IntersectionObserver((e=>{e[0].isIntersecting&&_()})),t.observe(e)),()=>{t?.unobserve(e)}}),[h,i,A]),l().createElement("div",{className:(0,o.A)({"jetpack-external-media-browser":!0,[m]:!0})},l().createElement(a.Composite,{role:"listbox",ref:A,className:(0,o.A)({"jetpack-external-media-browser__media":!0,"jetpack-external-media-browser__media__loading":i}),"aria-label":__("Media list","jetpack-external-media"),render:l().createElement("ul",null)},e.map((e=>l().createElement(d.A,{item:e,imageOnly:c,key:e.ID,onClick:F,isSelected:E.find((t=>t.ID===e.ID)),isCopying:r,shouldProxyImg:v})))),0===e.length&&!i&&l().createElement("div",{className:"jetpack-external-media-browser__empty"},l().createElement("p",null,__("Sorry, but nothing matched your search criteria.","jetpack-external-media"))),i&&l().createElement("div",{className:"jetpack-external-media-browser__loading"},l().createElement(a.Spinner,null)),D&&l().createElement(u.A,{label:(()=>{const e=r?__("Inserting…","jetpack-external-media"):__("Select","jetpack-external-media",0);return y?y(E.length,r):e})(),isLoading:r,disabled:0===E.length||r,onClick:S}))}},3682:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(6427),a=r(1609),s=r.n(a);const i=({label:e,isLoading:t,disabled:r,onClick:a})=>s().createElement("div",{className:"jetpack-external-media-browser__media__toolbar"},s().createElement(n.Button,{variant:"primary",isBusy:t,disabled:r,onClick:a},e))},3733:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(1455),a=r.n(n),s=r(6427),i=r(8468),o=r(7723),c=r(3022),l=r(1609),u=r.n(l);const __=o.__;const d=function({item:e,imageOnly:t,isSelected:r,isCopying:n=!1,shouldProxyImg:l,onClick:d}){const{thumbnails:p,caption:h,name:m,title:f,type:g,children:_=0}=e,{medium:k=null,fmt_hd:y=null,thumbnail:v=null}=p,E=f||h||m||"",[b,A]=(0,i.useState)(null),C=(0,c.A)({"jetpack-external-media-browser__media__item":!0,"jetpack-external-media-browser__media__item__selected":r,"jetpack-external-media-browser__media__folder":"folder"===g,"is-transient":n}),w=r?(0,o.sprintf)(/* translators: %s: item title. */
__("Deselect item: %s","jetpack-external-media"),E):(0,o.sprintf)(/* translators: %s: item title. */
__("Select item: %s","jetpack-external-media"),E),x=r=>{n||"image"!==e.type&&t||d?.(r,{item:e})};return(0,i.useEffect)((()=>{const e=k||y||v;l&&e?!b&&(async e=>{try{const t=await a()({path:"/wpcom/v2/external-media/proxy/google_photos",method:"POST",data:{url:e},parse:!1,responseType:"blob"});let r;r=t instanceof Blob?t:await t.blob();const n=URL.createObjectURL(r);A(n)}catch(e){console.error("Error fetching proxy image:",e)}})(e):A(e)}),[l,b,k,y,v]),u().createElement(s.Composite.Item,{className:C,onClick:n?void 0:x,"aria-checked":!!r,"aria-disabled":!!n,"aria-label":w,render:u().createElement("li",{role:"option"})},b&&u().createElement("img",{src:b,alt:E}),"folder"===g&&u().createElement("div",{className:"jetpack-external-media-browser__media__info"},u().createElement("div",{className:"jetpack-external-media-browser__media__name"},m),u().createElement("div",{className:"jetpack-external-media-browser__media__count"},_)),u().createElement(s.CheckboxControl,{className:"jetpack-external-media-browser__media__checkbox",__nextHasNoMarginBottom:!0,"aria-label":w,"aria-disabled":!!n,checked:r,onChange:()=>x()}))}},4096:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(7143);const a=()=>(0,n.useSelect)((e=>!!e("core/editor")),[])?"editor":"media-library"},3155:(e,t,r)=>{"use strict";r(6072),r(4715),r(8468),r(1609),r(7698),r(7259),r(3843)},7259:(e,t,r)=>{"use strict";r(6427),r(7723),r(1609),r(2186)},3843:(e,t,r)=>{"use strict";r(6427),r(7723),r(1609),r(7348)},7348:(e,t,r)=>{"use strict";r(6427),r(8468),r(1609),r(7698)},3539:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(6427),a=r(9491),s=r(7723),i=r(1609);const __=s.__,o=({defaultValue:e,onSearch:t})=>{const[r,s,o]=(0,a.useDebouncedInput)(e),c=__("Search","jetpack-external-media");return(0,i.useEffect)((()=>{t(o)}),[o,t]),React.createElement(n.SearchControl,{__nextHasNoMarginBottom:!0,className:"jetpack-external-media-search",onChange:s,value:r,label:c,placeholder:c})}},4328:(e,t,r)=>{"use strict";r.d(t,{Be:()=>u,Vu:()=>l,ed:()=>p,jz:()=>c});r(372),r(1455);var n=r(7143),a=r(7723),s=(r(3832),r(2186)),i=r(8703);r(5726),r(3809);const __=a.__;__("Pexels free photos","jetpack-external-media"),__("Search Pexels free photos","jetpack-external-media"),__("Google Photos","jetpack-external-media"),__("Search Google Photos","jetpack-external-media");const o=window.wpCookies,c=(e,t)=>{(0,n.dispatch)(i.t).setAuthenticated(e,t)},l=e=>{d(e?.id||null),(0,n.dispatch)(i.t).mediaPhotosPickerSessionSet(e)},u=()=>(0,n.select)(i.t).mediaPhotosPickerSession(),d=e=>{o.set(s.aD,e,604800,"/",`.${window.location.hostname.split(".").slice(-2).join(".")}`)},p=()=>o.get(s.aD)},3809:(e,t,r)=>{"use strict";r.d(t,{g:()=>n});let n=function(e){return e.Pexels="pexels",e.GooglePhotos="google_photos",e.Openverse="openverse",e.JetpackAppMedia="jetpack_app_media",e.Unknown="unknown",e}({})},4114:(e,t,r)=>{"use strict";r.d(t,{r:()=>i});var n=r(5985),a=r(3832);const s={list:"/wpcom/v2/external-media/list/",copy:(0,n.Sy)()?"/rest/v1.1/external-media-upload?service=":"/wpcom/v2/external-media/copy/",connection:"/wpcom/v2/external-media/connection/"};function i(e,t,r={}){return s[e]?(0,a.addQueryArgs)(s[e]+t,r):null}},233:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(2410),a=r(8468),s=r(7723),i=r(1609),o=r.n(i);const __=s.__;const c=(0,a.memo)((function(){return o().createElement(a.Fragment,null,o().createElement(n.mI,null),o().createElement("p",null,__("To get started, connect your site to your Google Photos library.","jetpack-external-media")),o().createElement("p",null,__("You can remove the connection in either of these places:","jetpack-external-media")),o().createElement("ul",null,o().createElement("li",null,o().createElement("a",{target:"_blank",rel:"noopener noreferrer",href:"https://myaccount.google.com/security"},__("Google Security page","jetpack-external-media"))),o().createElement("li",null,o().createElement("a",{target:"_blank",rel:"noopener noreferrer",href:"https://wordpress.com/marketing/connections/"},__("WordPress.com Connections","jetpack-external-media")))))}))},7564:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(8468),a=r(7723),s=r(1609),i=r.n(s);const __=a.__;const o=(0,n.memo)((function(){return i().createElement("p",null,__("Awaiting authorization","jetpack-external-media"))}))},4752:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(6427),a=r(8468),s=r(7723),i=r(1609),o=r.n(i),c=r(2186);const __=s.__;const l=(0,a.memo)((function({path:e,setPath:t}){return o().createElement(a.Fragment,null,o().createElement(n.Button,{variant:"tertiary",onClick:()=>t(c.fO)},__("Albums","jetpack-external-media")),"→   ",e.name)}))},3718:(e,t,r)=>{"use strict";r.d(t,{A:()=>g});var n=r(237),a=r(6427),s=r(8468),i=r(7723),o=r(6087),c=r(1609),l=r.n(c),u=r(2186);const __=i.__;function d({value:e,updateFilter:t}){return l().createElement(a.SelectControl,{label:__("Category","jetpack-external-media"),value:e,options:u.ri,onChange:t,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0})}function p({value:e,updateFilter:t}){const r=e?.range||u.OH,[i,o]=(0,s.useState)(-1),[c,d]=(0,s.useState)(u.Ic);return l().createElement("div",{className:"jetpack-external-media-date-filter"},l().createElement(a.SelectControl,{label:__("Filter by time period","jetpack-external-media"),value:r,options:u.Q9,onChange:e=>t({range:e}),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),r===u.LI&&l().createElement(s.Fragment,null,l().createElement(a.SelectControl,{label:__("Month","jetpack-external-media"),value:i,options:u.DO,onChange:o,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),l().createElement(n.A,{className:"components-base-control",label:__("Year","jetpack-external-media"),value:c,min:1970,onChange:d}),l().createElement(a.Button,{variant:"secondary",disabled:e?.month===i&&e?.year===c,onClick:()=>t({range:r,month:i,year:c})},__("Apply","jetpack-external-media"))))}function h(){return l().createElement("span",null,__("Favorites","jetpack-external-media"))}function m({value:e,updateFilter:t}){const r=[{label:__("All","jetpack-external-media"),value:""},{label:__("Images","jetpack-external-media"),value:"photo"},{label:__("Videos","jetpack-external-media"),value:"video"}];return l().createElement(a.SelectControl,{label:__("Type","jetpack-external-media"),value:e,options:r,onChange:t,__next40pxDefaultSize:!0})}function f({children:e,removeFilter:t,isRemovable:r=!1}){return l().createElement("div",{className:"jetpack-external-media-googlephotos-filter"},e,!!r&&l().createElement(a.Button,{onClick:t,isSmall:!0},__("Remove Filter","jetpack-external-media")))}const g=function({filters:e,setFilters:t,canChangeMedia:r}){const n=Object.keys(e).filter((e=>r||"mediaType"!==e)).map((r=>{return l().createElement(f,{key:r,removeFilter:()=>t((0,o.omit)(e,r))},(n=r,a=e[r],s=n=>t(function(e,t,r){const n={...e,[t]:r};return"mediaType"===t&&"video"===r?delete n.category:"category"===t&&"video"===n.mediaType&&delete n.mediaType,n}(e,r,n)),"category"===n?l().createElement(d,{value:a,updateFilter:s}):"date"===n?l().createElement(p,{value:a,updateFilter:s}):"favorite"===n?l().createElement(h,{value:a}):"mediaType"===n?l().createElement(m,{value:a,updateFilter:s}):null));var n,a,s}));return 0===n.length?null:n}},5678:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(6154),a=r.n(n),s=r(2186);const i=a()();function o(e){const{mediaType:t,category:r,favorite:n,date:o}=e,c=[];if(t&&c.push("mediaType="+t),r&&"video"!==t&&c.push("categoryInclude="+r),void 0!==n&&c.push("feature=favorite"),o){let e=null,t=null;switch(o.range){case s.gr:e=a()(i).subtract(7,"days"),t=i;break;case s.KS:e=a()(i).subtract(30,"days"),t=i;break;case s.xO:e=a()(i).subtract(6,"months"),t=i;break;case s.yS:e=a()(i).subtract(1,"year"),t=i;break;case s.LI:{const r=parseInt(o.month),n=parseInt(o.year);isNaN(r)||isNaN(n)||(-1===r?(e=a()([n,0]),t=a()(e).endOf("year")):(e=a()([n,r]),t=a()(e).endOf("month")))}}const r=e?e.format("YYYY-MM-DD"):"0000-00-00",n=t?t.format("YYYY-MM-DD"):"0000-00-00";c.push(`dateRange=${r}:${n}`)}return c.length>0?c:null}},3854:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(6427),a=r(8468),s=r(7723),i=r(1609),o=r.n(i);const __=s.__,c=[{label:__("Category","jetpack-external-media"),value:"category"},{label:__("Date","jetpack-external-media"),value:"date"},{label:__("Favorites","jetpack-external-media"),value:"favorite"},{label:__("Media Type","jetpack-external-media"),value:"mediaType"}];function l(e){return c.filter((t=>void 0===e[t.value]))}function u(e){const t=l(e);return t.length>0?t[0].value:""}const d=function(e){const[t,r]=(0,a.useState)(u([])),{isLoading:s,isCopying:i,filters:c,canChangeMedia:d}=e,p=function(e,t){return t?e:e.filter((e=>"mediaType"!==e.value))}(l(c),d);return 0===p.length?null:o().createElement(a.Fragment,null,o().createElement(n.SelectControl,{label:__("Filters","jetpack-external-media"),value:t,disabled:s||i,options:p,onChange:r,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),o().createElement(n.Button,{disabled:s||i,variant:"secondary",isSmall:!0,onClick:()=>{const n=(a=c,s=t,{...a,[s]:"favorite"===s||""});var a,s;e.setFilters(n),r(u(n))}},__("Add Filter","jetpack-external-media")))}},3679:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(2876);const a=({account:e,setAuthenticated:t,disconnectBtnVariant:r,showAccountInfo:a=!0})=>{const{image:s,name:i}=e||{};return React.createElement("div",{className:"jetpack-external-media-header__account"},a&&React.createElement("div",{className:"jetpack-external-media-header__account-info"},s&&React.createElement("img",{className:"jetpack-external-media-header__account-image",src:s,alt:"",height:"18",width:"18"}),i&&React.createElement("div",{className:"jetpack-external-media-header__account-name"},i)),React.createElement(n.A,{setAuthenticated:t,buttonVariant:r}))}},2893:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(2410),a=r(7723),s=r(1609),i=r.n(s),o=r(2876);const __=a.__;function c(e){const{setAuthenticated:t}=e;return i().createElement("div",{className:"jetpack-external-media-auth"},i().createElement(n.mI,null),i().createElement("p",null,__("We've updated our Google Photos service. You will need to disconnect and reconnect to continue accessing your photos.","jetpack-external-media")),i().createElement(o.A,{setAuthenticated:t}))}},3720:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});var n=r(9754),a=r(1455),s=r.n(a),i=r(6427),o=r(8468),c=r(7723),l=r(1609),u=r.n(l),d=r(2186),p=r(4114),h=r(233),m=r(7564);const __=c.__;const f=function(e){const{setAuthenticated:t}=e,[r,a]=(0,o.useState)(!1),c=(0,o.useCallback)((()=>{a(!0),s()({path:(0,p.r)("connection",d.Sh)}).then((e=>{if(e.error)throw e.message;(0,n.A)(e.connect_URL,(()=>{a(!1),t(!0)}))})).catch((()=>{a(!1)}))}),[t]);return u().createElement("div",{className:"jetpack-external-media-auth"},r?u().createElement(m.A,null):u().createElement(h.A,null),u().createElement(i.Button,{variant:"primary",disabled:r,onClick:c},__("Connect to Google Photos","jetpack-external-media")))}},2876:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(1455),a=r.n(n),s=r(6427),i=r(8468),o=r(7723),c=r(2186),l=r(4114);const __=o.__,u=({setAuthenticated:e,buttonVariant:t="secondary"})=>{const[r,n]=(0,i.useState)(!1),o=(0,i.useCallback)((()=>{n(!0),a()({method:"DELETE",path:(0,l.r)("connection",c.Sh)}).then((()=>e(!1))).catch((()=>n(!1)))}),[e]);return React.createElement(s.Button,{variant:t,className:"jetpack-external-media-browser__disconnect",onClick:o,disabled:r,isBusy:r},__("Disconnect from Google Photos","jetpack-external-media"))}},544:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(6427),a=r(3022);const s=({className:e})=>React.createElement("div",{className:(0,a.A)(e,"jetpack-external-media__google-photos-loading")},React.createElement(n.Spinner,null))},2836:(e,t,r)=>{"use strict";r.d(t,{A:()=>k});var n=r(6427),a=r(8468),s=r(7723),i=r(3022),o=r(1609),c=r.n(o),l=r(2186),u=r(5037),d=r(3809),p=r(4114),h=r(4752),m=r(3718),f=r(5678),g=r(3854),_=r(3679);const __=s.__;const k=function(e){const{className:t,account:r,allowedTypes:s,copyMedia:o,getMedia:k,isCopying:y,isLoading:v,media:E,multiple:b,selectButtonText:A,onChangePath:C,pageHandle:w,path:x,setAuthenticated:S,showAdditionalFilters:D=!1,pickerSession:F,pickerFeatureEnabled:P,deletePickerSession:R,createPickerSession:L}=e,I=(j=s)&&1===j.length&&"image"===j[0];var j;const[T,N]=(0,a.useState)(I?{mediaType:"photo",date:{range:l.OH}}:{date:{range:l.OH}}),[M,B]=(0,a.useState)(!1),O=(0,a.useRef)(""),q=(0,a.useRef)(""),z=x.ID===l.o$?(0,f.A)(T):null,U={number:20,path:x.ID};!P&&z&&(U.filter=z),P&&F&&(U.session_id=F.id);const G=(0,p.r)("list",l.Sh,U),H=(0,a.useCallback)(((e,t=!1)=>{k(G,t)}),[k,G]),V=(0,a.useCallback)((e=>{const t=E.find((t=>t.ID===e));q.current=x,C(t||{ID:e})}),[E,C,q,x]),$=(0,a.useCallback)((e=>{o(e,(0,p.r)("copy",l.Sh),l.Sh,P)}),[o,P]),Z=(0,a.useCallback)((()=>{B(!0),F?.id&&R(F.id,!1),L().then((e=>{e?.pickerUri&&window.open(e.pickerUri)}))}),[F,L,R]);return(0,a.useEffect)((()=>{O!==G&&(O.current=G,H("",x!==q.current))}),[O,G,H,x]),c().createElement("div",{className:(0,i.A)(t,"jetpack-external-media-wrapper__google")},c().createElement("div",{className:"jetpack-external-media-header__view"},!P&&c().createElement(c().Fragment,null,c().createElement(n.SelectControl,{className:"jetpack-external-media-header__select",label:__("View","jetpack-external-media"),value:x.ID!==l.o$?l.fO:l.o$,disabled:v||y,options:l.bI,onChange:V,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),D&&x.ID===l.o$&&c().createElement(g.A,{filters:T,isLoading:v,setFilters:N,canChangeMedia:!I}),c().createElement("div",{className:"jetpack-external-media-header__filter"},x.ID===l.o$&&c().createElement(m.A,{filters:T,isLoading:v,setFilters:N,canChangeMedia:!I}),x.ID!==l.o$&&x.ID!==l.fO&&c().createElement(h.A,{path:x,setPath:V}))),P&&!v&&c().createElement("div",{className:"jetpack-external-media-header__change-selection"},c().createElement(n.Button,{variant:"primary",isBusy:M,disabled:M,onClick:Z},__("Change selection","jetpack-external-media"))),(!v||E.length>0)&&c().createElement(_.A,{account:r,setAuthenticated:S})),c().createElement(u.A,{className:"jetpack-external-media-browser__google",key:G,media:E,mediaSource:d.g.GooglePhotos,imageOnly:I,isCopying:y,isLoading:v,nextPage:H,onCopy:$,pageHandle:w,multiple:b,selectButtonText:A,setPath:V,shouldProxyImg:P}))}},3853:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var n=r(2410),a=r(6427),s=r(7723),i=r(1113),o=r(3512),c=r(3022),l=r(1609),u=r.n(l),d=r(3679);const __=s.__;function p(e){const{className:t,pickerSession:r,fetchPickerSession:s,setAuthenticated:p,account:h}=e,m=!r;return(0,l.useEffect)((()=>{const e=setInterval((()=>{r?.id&&s(r.id)}),3e3);return()=>clearInterval(e)}),[s,r?.id]),u().createElement("div",{className:(0,c.A)(t,"jetpack-external-media__google-photos-picker")},u().createElement(n.AR,{width:"150"}),u().createElement("h1",null,__("Google Photos","jetpack-external-media")),u().createElement("p",null,__("Select photos directly from your Google Photos library.","jetpack-external-media")),u().createElement(a.Button,{variant:"primary",isBusy:m,disabled:m,className:"jetpack-external-media__google-photos-picker-button",onClick:()=>{r?.pickerUri&&window.open(r.pickerUri)}},__("Open Google Photos Picker","jetpack-external-media")," ",u().createElement(i.A,{icon:o.A,size:18})),u().createElement(d.A,{account:h,setAuthenticated:p,disconnectBtnVariant:"link",showAccountInfo:!1}))}},3254:(e,t,r)=>{"use strict";r.d(t,{A:()=>_});var n=r(6072),a=r.n(n),s=r(6154),i=r.n(s),o=r(1609),c=r.n(o),l=r(4328),u=r(3809),d=r(6501),p=r(3720),h=r(2893),m=r(544),f=r(2836),g=r(3853);const _=(0,d.A)(u.g.GooglePhotos,{modalSize:"fill"})((function(e){const{isAuthenticated:t,pickerSession:r,createPickerSession:n,fetchPickerSession:s,getPickerStatus:u,setAuthenticated:d}=e,[_]=(0,o.useState)((0,l.ed)()),[k,y]=(0,o.useState)(null),[v,E]=(0,o.useState)(!1),[b,A]=(0,o.useState)(!1),C=null===k,w=null!==r&&!("code"in r),x=r?.expireTime&&i()(r.expireTime).isBefore(new Date);return(0,o.useEffect)((()=>{u().then((e=>{switch(y(e.enabled),e.connection_status){case"ok":d(!0),A(!1);break;case"invalid":d(!0),A(!0);break;case"not_connected":d(!1),A(!1)}}))}),[t,u,d]),(0,o.useEffect)((()=>{k&&t&&!b&&Promise.resolve(_).then((e=>e?s(e):e)).finally((()=>E(!0)))}),[t,k,b,_,s]),(0,o.useEffect)((()=>{k&&v&&t&&!b&&(!w||x)&&n()}),[k,b,v,w,t,x,n,r]),C?c().createElement(m.A,e):t?b?c().createElement(h.A,e):k&&!r?.mediaItemsSet?c().createElement(g.A,e):c().createElement(f.A,a()({pickerFeatureEnabled:k},e)):c().createElement(p.A,e)}))},7698:(e,t,r)=>{"use strict";r.d(t,{JC:()=>y,Pk:()=>k});var n=r(6865),a=r(2410),s=r(7723),i=r(1609),o=r.n(i),c=r(2186),l=r(3254),u=r(4942),d=r(3147),p=r(3638),h=r(792),m=r(2884),f=r(5566);const __=s.__,g=[{id:c._X,label:__("Your Phone","jetpack-external-media"),icon:o().createElement(a.Bl,{className:"components-menu-items__item-icon"}),keyword:"jetpack mobile app"}],_=(c.Pn,__("Generate with AI","jetpack-external-media"),n.cj,c.FE,__("Generate with AI","jetpack-external-media"),n.cj,[{id:c.Sh,label:__("Google Photos","jetpack-external-media"),icon:o().createElement(a.mb,{className:"components-menu-items__item-icon"}),keyword:"google photos"},{id:c.kL,label:__("Pexels free photos","jetpack-external-media"),icon:o().createElement(a.tC,{className:"components-menu-items__item-icon"}),keyword:"pexels"},{id:c.Q8,label:__("Openverse","jetpack-external-media"),icon:o().createElement(a.p3,{className:"components-menu-items__item-icon"}),keyword:"openverse"}].concat(g));function k(e){return e===c.kL?f.A:e===c.Sh?l.A:e===c.Q8?m.A:e===c._X?h.A:e===c.Pn?u.A:e===c.FE?p.A:e===c.US?d.A:null}function y(e){return _.find((t=>t.id===e))}},4942:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(6865),a=r(1609),s=r.n(a);const i=function({onClose:e=()=>{}}){return s().createElement(n.m2,{placement:n.hB,onClose:e})}},3147:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(6865),a=r(1609),s=r.n(a);const i=function({onClose:e=()=>{},onSelect:t,multiple:r=!1}){return s().createElement(n.OE,{placement:n.dY,onClose:e,onSetImage:e=>t(r?[e]:e)})}},3638:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(6865),a=r(1609),s=r.n(a);const i=function({onClose:e=()=>{},onSelect:t,multiple:r=!1}){return s().createElement(n.OE,{placement:n.hB,onClose:e,onSetImage:e=>t(r?[e]:e)})}},792:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});var n=r(6281),a=r(5985),s=r(2410),i=r(7143),o=r(8468),c=r(7723),l=r(3022),u=r(1609),d=r.n(u),p=r(5037),h=r(3809),m=r(6501);const __=c.__,_n=c._n;const f=(0,m.A)(h.g.JetpackAppMedia,{modalSize:"large"})((function(e){const{className:t,media:r,insertMedia:u,isCopying:m,multiple:f,getMedia:g}=e,_=window?.Jetpack_Editor_Initial_State?.wpcomBlogId||window?.JetpackExternalMediaData?.wpcomBlogId||0,k=(()=>{let e="";return window?.Jetpack_Editor_Initial_State?e=window?.Jetpack_Editor_Initial_State?.pluginBasePath:window?.JetpackExternalMediaData&&(e=window?.JetpackExternalMediaData?.pluginBasePath),e+"/images/"})(),y=(0,i.useSelect)((e=>e("core/editor").getCurrentPostId())),[v]=(0,o.useState)(Date.now()/1e3),E=(0,o.useCallback)((()=>{g(`/wpcom/v2/app-media?refresh=true&after=${v}`,!0)}),[g,v]),b=(0,o.useCallback)((()=>{g(`/wpcom/v2/app-media?refresh=true&after=${v}`,!1,!1)}),[g,v]),A=(0,o.useCallback)((e=>{u(e)}),[u]);(0,o.useEffect)((()=>{r.length&&!f&&A(r)}),[r,f,A]),(0,o.useEffect)(E,[]),(0,a.Ql)(b,2e3);const C=!!r.length,w=C?"jetpack-external-media-wrapper__jetpack_app_media-wrapper":"jetpack-external-media-wrapper__jetpack_app_media-wrapper has-no-image-uploaded";return d().createElement("div",{className:(0,l.A)(t,w)},d().createElement(s.v7,null),d().createElement("h2",{className:"jetpack-external-media-wrapper__jetpack_app_media-title"},C&&__("Select images to be added","jetpack-external-media"),!C&&__("Upload from your phone","jetpack-external-media")),d().createElement("p",{className:"jetpack-external-media-wrapper__jetpack_app_media-description"},C&&__("Select the images below to add, or continue adding more from your device.","jetpack-external-media"),!C&&__("Scan the QR code with your iPhone or Android camera to upload from your photos.","jetpack-external-media")),!C&&d().createElement("div",{className:"jetpack-external-media-wrapper__jetpack_app_media-qr-code-wrapper"},d().createElement("div",{className:"jetpack-external-media-wrapper__jetpack_app_media-qr-code"},d().createElement(n.A,{size:"100",value:`https://apps.wordpress.com/get/?campaign=qr-code-media&postId=${y}#%2Fmedia%2F${_}`})),d().createElement("div",{className:"jetpack-external-media-wrapper__jetpack_app_media-instructions"},d().createElement("img",{src:`${k}app-image-upload.png`,srcSet:`${k}app-image-upload.png 1x, ${k}app-image-upload-2x.png 2x`,alt:""}))),C&&d().createElement(p.A,{key:"jetpack-app-media",className:"jetpack-external-media-browser__jetpack_app_media_browser",media:r,mediaSource:h.g.JetpackAppMedia,isCopying:m,isLoading:!1,nextPage:E,onCopy:A,pageHandle:!1,multiple:f,selectButtonText:e=>m?(0,c.sprintf)(/* translators: %1$d is the number of images that were selected. */
_n("Inserting %1$d image…","Inserting %1$d images…",e,"jetpack-external-media"),e):e?(0,c.sprintf)(/* translators: %1$d is the number of images that were selected. */
_n("Add %1$d image","Add %1$d images",e,"jetpack-external-media"),e):__("Add images","jetpack-external-media")}))}))},2884:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var n=r(9491),a=r(8468),s=r(3022),i=r(6087),o=r(1609),c=r.n(o),l=r(2186),u=r(5037),d=r(3539),p=r(3809),h=r(4114);const m=(0,r(6501).A)(p.g.Openverse,{modalSize:"fill"})((function(e){const{className:t,media:r,isCopying:o,isLoading:m,pageHandle:f,multiple:g,selectButtonText:_,copyMedia:k,getMedia:y}=e,[v,E]=(0,a.useState)((0,i.sample)(l.oc)),b=(0,n.usePrevious)(v),A=(0,a.useCallback)((e=>{k(e,(0,h.r)("copy",l.Q8),l.Q8)}),[k]),C=(0,a.useCallback)(((e,t=!1)=>{e&&y((0,h.r)("list",l.Q8,{number:20,search:e}),t)}),[y]);return(0,a.useEffect)((()=>{C(v,v!==b)}),[v]),c().createElement("div",{className:(0,s.A)(t,"jetpack-external-media-wrapper__openverse")},c().createElement(d.A,{defaultValue:v,onSearch:E}),c().createElement(u.A,{className:"jetpack-external-media-browser__openverse",media:r,mediaSource:p.g.Openverse,isCopying:o,isLoading:m,nextPage:()=>C(v),onCopy:A,pageHandle:f,multiple:g,selectButtonText:_}))}))},5566:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var n=r(9491),a=r(8468),s=r(3022),i=r(6087),o=r(1609),c=r.n(o),l=r(2186),u=r(5037),d=r(3539),p=r(3809),h=r(4114);const m=(0,r(6501).A)(p.g.Pexels,{modalSize:"fill"})((function(e){const{className:t,media:r,isCopying:o,isLoading:m,pageHandle:f,multiple:g,selectButtonText:_,copyMedia:k,getMedia:y}=e,[v,E]=(0,a.useState)((0,i.sample)(l.oc)),b=(0,n.usePrevious)(v),A=(0,a.useCallback)((e=>{k(e,(0,h.r)("copy",l.kL),l.kL)}),[k]),C=(0,a.useCallback)(((e,t=!1)=>{e&&y((0,h.r)("list",l.kL,{number:20,path:"recent",search:e}),t)}),[y]);return(0,a.useEffect)((()=>{C(v,v!==b)}),[v]),c().createElement("div",{className:(0,s.A)(t,"jetpack-external-media-wrapper__pexels")},c().createElement(d.A,{defaultValue:v,onSearch:E}),c().createElement(u.A,{className:"jetpack-external-media-browser__pexels",media:r,mediaSource:p.g.Pexels,isCopying:o,isLoading:m,nextPage:()=>C(v),onCopy:A,pageHandle:f,multiple:g,selectButtonText:_}))}))},6501:(e,t,r)=>{"use strict";r.d(t,{A:()=>g});var n=r(1455),a=r.n(n),s=r(6427),i=r(9491),o=r(7143),c=r(8468),l=r(7723),u=r(8558),d=r(3022),p=r(6087),h=r(2186),m=r(4328),f=r(3809);const __=l.__;function g(e=f.g.Unknown,t={}){return(0,i.createHigherOrderComponent)((r=>{class n extends c.Component{constructor(e){super(e),this.defaultAccount={image:"",name:""},this.state={account:this.defaultAccount,media:[],nextHandle:!1,isLoading:!1,isCopying:null,isAuthenticated:!0,path:{ID:h.o$}}}contentRef=e=>{e?(this.contentElement=e,this.modalElement=e.closest(".jetpack-external-media-browser"),this.modalElement&&this.modalElement.addEventListener("keydown",this.stopArrowKeysPropagation)):this.modalElement&&(this.modalElement.removeEventListener("keydown",this.stopArrowKeysPropagation),this.modalElement=null,this.contentElement=null)};stopArrowKeysPropagation=e=>{[u.UP,u.DOWN,u.LEFT,u.RIGHT].includes(e.keyCode)&&!e.target.classList.contains("jetpack-external-media-browser__media__item")&&e.stopPropagation()};setAuthenticated=t=>{this.setState({isAuthenticated:t}),(0,m.jz)(e,t)};mergeMedia(e,t){return(0,p.uniqBy)(e.concat(t),"ID")}getRequestUrl(e){const{nextHandle:t}=this.state;return t?e+"&page_handle="+encodeURIComponent(t):e}getMedia=(e,t=!1,r=!0)=>{this.abortController&&this.abortController.abort(),t&&this.props.noticeOperations.removeAllNotices(),this.setState({account:t?this.defaultAccount:this.state.account,isLoading:r,media:t?[]:this.state.media,nextHandle:!t&&this.state.nextHandle},(()=>this.getMediaRequest(e)))};handleApiError=e=>{if("AbortError"===e.name)return;if("authorization_required"===e.code)return this.setAuthenticated(!1),void this.setState({isLoading:!1,isCopying:!1});e.errors?.length&&(e={code:e.errors[0].error,message:e.errors[0].message});const{noticeOperations:t}=this.props;t.removeAllNotices(),t.createErrorNotice("internal_server_error"===e.code?"Internal server error":e.message),this.setState({isLoading:!1,isCopying:!1})};getMediaRequest=e=>{const{nextHandle:t,media:r}=this.state;if(!1===t&&r.length>0)return void this.setState({isLoading:!1});const n=this.getRequestUrl(e);this.abortController=void 0===window.AbortController?void 0:new window.AbortController,a()({path:n,method:"GET",parse:void 0===window.wpcomFetch,signal:this.abortController?.signal}).then((e=>{if(void 0===e.media)throw{code:"internal_server_error"};this.setState({account:e.meta.account,media:this.mergeMedia(r,e.media),nextHandle:e.meta.next_page,isLoading:!1}),this.setAuthenticated(!0),this.abortController=null})).catch(this.handleApiError)};copyMedia=(e,t,r,n=!1)=>{this.setState({isCopying:e}),this.props.noticeOperations.removeAllNotices(),this.modalElement&&this.modalElement.focus(),a()({path:t,method:"POST",data:{external_ids:e.map((e=>e.guid)),media:e.map((e=>({guid:e.guid,caption:e.caption,title:e.title}))),service:r,post_id:this.props.postId,should_proxy:n}}).then((e=>{e.media&&(e=e.media.map((e=>({alt:e.alt,caption:e.caption,id:e.ID,type:"image",url:e.URL}))));const{value:t,addToGallery:r,multiple:n}=this.props,a=n?e:e[0],s=e.find((e=>e.errors));if(s){const{errors:e}=s,t=Object.keys(e)[0];this.handleApiError({code:t,message:e[t]})}else this.props.onClose(),this.props.onSelect(r?t.concat(e):a)})).catch(this.handleApiError)};createPickerSession=()=>a()({path:"/wpcom/v2/external-media/session/google_photos",method:"POST"}).then((e=>{if("code"in e)throw e;return e})).then((e=>((0,m.Vu)(e),e)));fetchPickerSession=e=>a()({path:`/wpcom/v2/external-media/session/google_photos/${e}`,method:"GET"}).then((e=>{if("code"in e)throw e;return e})).then((e=>((0,m.Vu)(e),e)));deletePickerSession=(e,t=!0)=>a()({path:`/wpcom/v2/external-media/session/google_photos/${e}`,method:"DELETE"}).then((()=>t&&(0,m.Vu)(null)));getPickerStatus=()=>a()({path:"/wpcom/v2/external-media/connection/google_photos/picker_status",method:"GET"});mapImageToResult=e=>({alt:e.name,caption:e.caption,id:e.ID,type:"image",url:e.url,sizes:{thumbnail:{url:e.thumbnails.thumbnail},large:{url:e.thumbnails.large}}});insertMedia=e=>{this.setState({isCopying:e}),this.props.noticeOperations.removeAllNotices(),this.modalElement&&this.modalElement.focus();let t=[];t=0!==e.length?e.map(this.mapImageToResult):[this.mapImageToResult(e)];const{value:r,multiple:n,addToGallery:a}=this.props,s=n?t:t[0];this.props.onClose(),this.props.onSelect(a?r.concat(t):s)};onChangePath=(e,t)=>{this.setState({path:e},t)};getTitle=()=>{const{getTitle:t}=this.props,{isCopying:r}=this.state,n="jetpack_app_media"!==e?__("Select media","jetpack-external-media"):"",a=r?__("Inserting media","jetpack-external-media"):n;return t?t({title:a,isCopying:r}):a};getTexts=()=>{const{externalSource:t,isImport:r}=this.props,{isCopying:n}=this.state;if(r)return{title:(0,l.sprintf)(/* translators: %s is the name of the external media */
__("Import from %s","jetpack-external-media"),t.label),description:(0,l.sprintf)(/* translators: %s is the name of the external media */
__("Import media from %s into the Media Library.","jetpack-external-media"),t.label)};const a="jetpack_app_media"!==e?(0,l.sprintf)(/* translators: %s is the name of the external media */
__("Select media from %s","jetpack-external-media"),t.label):"";return{title:n?__("Inserting media","jetpack-external-media"):a,description:n?__("When the media is finished copying and inserting, you will be returned to the editor.","jetpack-external-media"):__("Select the media you would like to insert into the editor.","jetpack-external-media",0)}};render(){const{account:n,isAuthenticated:a,isCopying:i,isLoading:o,media:c,nextHandle:l,path:u}=this.state,{allowedTypes:p,multiple:h=!1,selectButtonText:m,noticeUI:f,onClose:g}=this.props,{title:_,description:k}=this.getTexts(),y="jetpack-external-media-browser__description",v=(0,d.A)({"jetpack-external-media-browser__modal":!0,"jetpack-external-media-browser__modal--is-copying":i,"is-jetpack-app-media":"jetpack_app_media"===e});return React.createElement(s.Modal,{onRequestClose:g,title:_,aria:{describedby:y},className:v,size:t.modalSize},React.createElement("div",{ref:this.contentRef},f,React.createElement("p",{id:y,className:"jetpack-external-media-browser__modal--visually-hidden"},k),React.createElement(r,{className:"jetpack-external-media-browser__modal-content",account:n,getMedia:this.getMedia,copyMedia:this.copyMedia,insertMedia:this.insertMedia,isCopying:i,isLoading:o,media:c,pageHandle:l,allowedTypes:p,isAuthenticated:a,setAuthenticated:this.setAuthenticated,multiple:h,selectButtonText:m,path:u,onChangePath:this.onChangePath,pickerSession:this.props.pickerSession,createPickerSession:this.createPickerSession,fetchPickerSession:this.fetchPickerSession,deletePickerSession:this.deletePickerSession,getPickerStatus:this.getPickerStatus})))}}return(0,o.withSelect)((e=>{const t=e("core/editor").getCurrentPost();return{postId:("number"==typeof t?.id?t.id:t?.wp_id)??0,pickerSession:(0,m.Be)()}}))((0,s.withNotices)(n))}))}},2496:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>s,MZ:()=>a,lh:()=>n});const n="SET_AUTHENTICATED",a="MEDIA_PHOTOS_PICKER_SESSION_SET",s={setAuthenticated:(e,t)=>({type:n,payload:{isAuthenticated:t,mediaSource:e}}),mediaPhotosPickerSessionSet:e=>({type:a,payload:e})}},8703:(e,t,r)=>{"use strict";r.d(t,{t:()=>c});var n=r(7143),a=r(2496),s=r(3671),i=r(1501);const o={actions:a.Ay,reducer:s.A,selectors:i.A},c=(0,n.createReduxStore)("jetpack-media",o);(0,n.register)(c)},3671:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(2496),a=r(9066);const s=(e=a.i,t)=>{switch(t.type){case n.lh:return{...e,mediaSourceIsAuthenticated:e.mediaSourceIsAuthenticated.set(t.payload.mediaSource,t.payload.isAuthenticated)};case n.MZ:return{...e,mediaPhotosPickerSession:t.payload};default:return e}}},1501:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={isAuthenticated:(e,t)=>e.mediaSourceIsAuthenticated.get(t)??!1,mediaPhotosPickerSession:e=>e.mediaPhotosPickerSession??null}},9066:(e,t,r)=>{"use strict";r.d(t,{i:()=>a});var n=r(3809);const a={mediaPhotosPickerSession:null,mediaSourceIsAuthenticated:new Map([[n.g.Pexels,!1],[n.g.GooglePhotos,!1],[n.g.Openverse,!1],[n.g.Unknown,!1]])}},5726:(e,t,r)=>{"use strict";r(7143)},1147:(e,t,r)=>{"use strict";function n(e,t){return Array(t+1).join(e)}r.d(t,{A:()=>I});var a=["ADDRESS","ARTICLE","ASIDE","AUDIO","BLOCKQUOTE","BODY","CANVAS","CENTER","DD","DIR","DIV","DL","DT","FIELDSET","FIGCAPTION","FIGURE","FOOTER","FORM","FRAMESET","H1","H2","H3","H4","H5","H6","HEADER","HGROUP","HR","HTML","ISINDEX","LI","MAIN","MENU","NAV","NOFRAMES","NOSCRIPT","OL","OUTPUT","P","PRE","SECTION","TABLE","TBODY","TD","TFOOT","TH","THEAD","TR","UL"];function s(e){return l(e,a)}var i=["AREA","BASE","BR","COL","COMMAND","EMBED","HR","IMG","INPUT","KEYGEN","LINK","META","PARAM","SOURCE","TRACK","WBR"];function o(e){return l(e,i)}var c=["A","TABLE","THEAD","TBODY","TFOOT","TH","TD","IFRAME","SCRIPT","AUDIO","VIDEO"];function l(e,t){return t.indexOf(e.nodeName)>=0}function u(e,t){return e.getElementsByTagName&&t.some((function(t){return e.getElementsByTagName(t).length}))}var d={};function p(e){return e?e.replace(/(\n+\s*)+/g,"\n"):""}function h(e){for(var t in this.options=e,this._keep=[],this._remove=[],this.blankRule={replacement:e.blankReplacement},this.keepReplacement=e.keepReplacement,this.defaultRule={replacement:e.defaultReplacement},this.array=[],e.rules)this.array.push(e.rules[t])}function m(e,t,r){for(var n=0;n<e.length;n++){var a=e[n];if(f(a,t,r))return a}}function f(e,t,r){var n=e.filter;if("string"==typeof n){if(n===t.nodeName.toLowerCase())return!0}else if(Array.isArray(n)){if(n.indexOf(t.nodeName.toLowerCase())>-1)return!0}else{if("function"!=typeof n)throw new TypeError("`filter` needs to be a string, array, or function");if(n.call(e,t,r))return!0}}function g(e){var t=e.nextSibling||e.parentNode;return e.parentNode.removeChild(e),t}function _(e,t,r){return e&&e.parentNode===t||r(t)?t.nextSibling||t.parentNode:t.firstChild||t.nextSibling||t.parentNode}d.paragraph={filter:"p",replacement:function(e){return"\n\n"+e+"\n\n"}},d.lineBreak={filter:"br",replacement:function(e,t,r){return r.br+"\n"}},d.heading={filter:["h1","h2","h3","h4","h5","h6"],replacement:function(e,t,r){var a=Number(t.nodeName.charAt(1));return"setext"===r.headingStyle&&a<3?"\n\n"+e+"\n"+n(1===a?"=":"-",e.length)+"\n\n":"\n\n"+n("#",a)+" "+e+"\n\n"}},d.blockquote={filter:"blockquote",replacement:function(e){return"\n\n"+(e=(e=e.replace(/^\n+|\n+$/g,"")).replace(/^/gm,"> "))+"\n\n"}},d.list={filter:["ul","ol"],replacement:function(e,t){var r=t.parentNode;return"LI"===r.nodeName&&r.lastElementChild===t?"\n"+e:"\n\n"+e+"\n\n"}},d.listItem={filter:"li",replacement:function(e,t,r){e=e.replace(/^\n+/,"").replace(/\n+$/,"\n").replace(/\n/gm,"\n    ");var n=r.bulletListMarker+"   ",a=t.parentNode;if("OL"===a.nodeName){var s=a.getAttribute("start"),i=Array.prototype.indexOf.call(a.children,t);n=(s?Number(s)+i:i+1)+".  "}return n+e+(t.nextSibling&&!/\n$/.test(e)?"\n":"")}},d.indentedCodeBlock={filter:function(e,t){return"indented"===t.codeBlockStyle&&"PRE"===e.nodeName&&e.firstChild&&"CODE"===e.firstChild.nodeName},replacement:function(e,t,r){return"\n\n    "+t.firstChild.textContent.replace(/\n/g,"\n    ")+"\n\n"}},d.fencedCodeBlock={filter:function(e,t){return"fenced"===t.codeBlockStyle&&"PRE"===e.nodeName&&e.firstChild&&"CODE"===e.firstChild.nodeName},replacement:function(e,t,r){for(var a,s=((t.firstChild.getAttribute("class")||"").match(/language-(\S+)/)||[null,""])[1],i=t.firstChild.textContent,o=r.fence.charAt(0),c=3,l=new RegExp("^"+o+"{3,}","gm");a=l.exec(i);)a[0].length>=c&&(c=a[0].length+1);var u=n(o,c);return"\n\n"+u+s+"\n"+i.replace(/\n$/,"")+"\n"+u+"\n\n"}},d.horizontalRule={filter:"hr",replacement:function(e,t,r){return"\n\n"+r.hr+"\n\n"}},d.inlineLink={filter:function(e,t){return"inlined"===t.linkStyle&&"A"===e.nodeName&&e.getAttribute("href")},replacement:function(e,t){var r=t.getAttribute("href"),n=p(t.getAttribute("title"));return n&&(n=' "'+n+'"'),"["+e+"]("+r+n+")"}},d.referenceLink={filter:function(e,t){return"referenced"===t.linkStyle&&"A"===e.nodeName&&e.getAttribute("href")},replacement:function(e,t,r){var n,a,s=t.getAttribute("href"),i=p(t.getAttribute("title"));switch(i&&(i=' "'+i+'"'),r.linkReferenceStyle){case"collapsed":n="["+e+"][]",a="["+e+"]: "+s+i;break;case"shortcut":n="["+e+"]",a="["+e+"]: "+s+i;break;default:var o=this.references.length+1;n="["+e+"]["+o+"]",a="["+o+"]: "+s+i}return this.references.push(a),n},references:[],append:function(e){var t="";return this.references.length&&(t="\n\n"+this.references.join("\n")+"\n\n",this.references=[]),t}},d.emphasis={filter:["em","i"],replacement:function(e,t,r){return e.trim()?r.emDelimiter+e+r.emDelimiter:""}},d.strong={filter:["strong","b"],replacement:function(e,t,r){return e.trim()?r.strongDelimiter+e+r.strongDelimiter:""}},d.code={filter:function(e){var t=e.previousSibling||e.nextSibling,r="PRE"===e.parentNode.nodeName&&!t;return"CODE"===e.nodeName&&!r},replacement:function(e){if(!e)return"";e=e.replace(/\r?\n|\r/g," ");for(var t=/^`|^ .*?[^ ].* $|`$/.test(e)?" ":"",r="`",n=e.match(/`+/gm)||[];-1!==n.indexOf(r);)r+="`";return r+t+e+t+r}},d.image={filter:"img",replacement:function(e,t){var r=p(t.getAttribute("alt")),n=t.getAttribute("src")||"",a=p(t.getAttribute("title"));return n?"!["+r+"]("+n+(a?' "'+a+'"':"")+")":""}},h.prototype={add:function(e,t){this.array.unshift(t)},keep:function(e){this._keep.unshift({filter:e,replacement:this.keepReplacement})},remove:function(e){this._remove.unshift({filter:e,replacement:function(){return""}})},forNode:function(e){return e.isBlank?this.blankRule:(t=m(this.array,e,this.options))||(t=m(this._keep,e,this.options))||(t=m(this._remove,e,this.options))?t:this.defaultRule;var t},forEach:function(e){for(var t=0;t<this.array.length;t++)e(this.array[t],t)}};var k="undefined"!=typeof window?window:{};var y,v,E=function(){var e=k.DOMParser,t=!1;try{(new e).parseFromString("","text/html")&&(t=!0)}catch(e){}return t}()?k.DOMParser:(y=function(){},function(){var e=!1;try{document.implementation.createHTMLDocument("").open()}catch(t){window.ActiveXObject&&(e=!0)}return e}()?y.prototype.parseFromString=function(e){var t=new window.ActiveXObject("htmlfile");return t.designMode="on",t.open(),t.write(e),t.close(),t}:y.prototype.parseFromString=function(e){var t=document.implementation.createHTMLDocument("");return t.open(),t.write(e),t.close(),t},y);function b(e,t){var r;"string"==typeof e?r=(v=v||new E).parseFromString('<x-turndown id="turndown-root">'+e+"</x-turndown>","text/html").getElementById("turndown-root"):r=e.cloneNode(!0);return function(e){var t=e.element,r=e.isBlock,n=e.isVoid,a=e.isPre||function(e){return"PRE"===e.nodeName};if(t.firstChild&&!a(t)){for(var s=null,i=!1,o=null,c=_(o,t,a);c!==t;){if(3===c.nodeType||4===c.nodeType){var l=c.data.replace(/[ \r\n\t]+/g," ");if(s&&!/ $/.test(s.data)||i||" "!==l[0]||(l=l.substr(1)),!l){c=g(c);continue}c.data=l,s=c}else{if(1!==c.nodeType){c=g(c);continue}r(c)||"BR"===c.nodeName?(s&&(s.data=s.data.replace(/ $/,"")),s=null,i=!1):n(c)||a(c)?(s=null,i=!0):s&&(i=!1)}var u=_(o,c,a);o=c,c=u}s&&(s.data=s.data.replace(/ $/,""),s.data||g(s))}}({element:r,isBlock:s,isVoid:o,isPre:t.preformattedCode?A:null}),r}function A(e){return"PRE"===e.nodeName||"CODE"===e.nodeName}function C(e,t){return e.isBlock=s(e),e.isCode="CODE"===e.nodeName||e.parentNode.isCode,e.isBlank=function(e){return!o(e)&&!function(e){return l(e,c)}(e)&&/^\s*$/i.test(e.textContent)&&!function(e){return u(e,i)}(e)&&!function(e){return u(e,c)}(e)}(e),e.flankingWhitespace=function(e,t){if(e.isBlock||t.preformattedCode&&e.isCode)return{leading:"",trailing:""};var r=(n=e.textContent,a=n.match(/^(([ \t\r\n]*)(\s*))(?:(?=\S)[\s\S]*\S)?((\s*?)([ \t\r\n]*))$/),{leading:a[1],leadingAscii:a[2],leadingNonAscii:a[3],trailing:a[4],trailingNonAscii:a[5],trailingAscii:a[6]});var n,a;r.leadingAscii&&w("left",e,t)&&(r.leading=r.leadingNonAscii);r.trailingAscii&&w("right",e,t)&&(r.trailing=r.trailingNonAscii);return{leading:r.leading,trailing:r.trailing}}(e,t),e}function w(e,t,r){var n,a,i;return"left"===e?(n=t.previousSibling,a=/ $/):(n=t.nextSibling,a=/^ /),n&&(3===n.nodeType?i=a.test(n.nodeValue):r.preformattedCode&&"CODE"===n.nodeName?i=!1:1!==n.nodeType||s(n)||(i=a.test(n.textContent))),i}var x=Array.prototype.reduce,S=[[/\\/g,"\\\\"],[/\*/g,"\\*"],[/^-/g,"\\-"],[/^\+ /g,"\\+ "],[/^(=+)/g,"\\$1"],[/^(#{1,6}) /g,"\\$1 "],[/`/g,"\\`"],[/^~~~/g,"\\~~~"],[/\[/g,"\\["],[/\]/g,"\\]"],[/^>/g,"\\>"],[/_/g,"\\_"],[/^(\d+)\. /g,"$1\\. "]];function D(e){if(!(this instanceof D))return new D(e);var t={rules:d,headingStyle:"setext",hr:"* * *",bulletListMarker:"*",codeBlockStyle:"indented",fence:"```",emDelimiter:"_",strongDelimiter:"**",linkStyle:"inlined",linkReferenceStyle:"full",br:"  ",preformattedCode:!1,blankReplacement:function(e,t){return t.isBlock?"\n\n":""},keepReplacement:function(e,t){return t.isBlock?"\n\n"+t.outerHTML+"\n\n":t.outerHTML},defaultReplacement:function(e,t){return t.isBlock?"\n\n"+e+"\n\n":e}};this.options=function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)r.hasOwnProperty(n)&&(e[n]=r[n])}return e}({},t,e),this.rules=new h(this.options)}function F(e){var t=this;return x.call(e.childNodes,(function(e,r){var n="";return 3===(r=new C(r,t.options)).nodeType?n=r.isCode?r.nodeValue:t.escape(r.nodeValue):1===r.nodeType&&(n=R.call(t,r)),L(e,n)}),"")}function P(e){var t=this;return this.rules.forEach((function(r){"function"==typeof r.append&&(e=L(e,r.append(t.options)))})),e.replace(/^[\t\r\n]+/,"").replace(/[\t\r\n\s]+$/,"")}function R(e){var t=this.rules.forNode(e),r=F.call(this,e),n=e.flankingWhitespace;return(n.leading||n.trailing)&&(r=r.trim()),n.leading+t.replacement(r,e,this.options)+n.trailing}function L(e,t){var r=function(e){for(var t=e.length;t>0&&"\n"===e[t-1];)t--;return e.substring(0,t)}(e),n=t.replace(/^\n*/,""),a=Math.max(e.length-r.length,t.length-n.length);return r+"\n\n".substring(0,a)+n}D.prototype={turndown:function(e){if(!function(e){return null!=e&&("string"==typeof e||e.nodeType&&(1===e.nodeType||9===e.nodeType||11===e.nodeType))}(e))throw new TypeError(e+" is not a string, or an element/document/fragment node.");if(""===e)return"";var t=F.call(this,new b(e,this.options));return P.call(this,t)},use:function(e){if(Array.isArray(e))for(var t=0;t<e.length;t++)this.use(e[t]);else{if("function"!=typeof e)throw new TypeError("plugin must be a Function or an Array of Functions");e(this)}return this},addRule:function(e,t){return this.rules.add(e,t),this},keep:function(e){return this.rules.keep(e),this},remove:function(e){return this.rules.remove(e),this},escape:function(e){return S.reduce((function(e,t){return e.replace(t[0],t[1])}),e)}};const I=D},6791:(e,t,r)=>{"use strict";e.exports=r.p+"images/jetpack-logo-0869ca6c8490d0aaecca.svg"},6076:(e,t,r)=>{"use strict";e.exports=r.p+"images/loader-0855308317756931a4f5.gif"},9384:e=>{"use strict";e.exports=window.JetpackConnection},7999:e=>{"use strict";e.exports=window.JetpackScriptDataModule},1609:e=>{"use strict";e.exports=window.React},5795:e=>{"use strict";e.exports=window.ReactDOM},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},6087:e=>{"use strict";e.exports=window.lodash},6154:e=>{"use strict";e.exports=window.moment},1455:e=>{"use strict";e.exports=window.wp.apiFetch},3162:e=>{"use strict";e.exports=window.wp.blob},4715:e=>{"use strict";e.exports=window.wp.blockEditor},7378:e=>{"use strict";e.exports=window.wp.blocks},6427:e=>{"use strict";e.exports=window.wp.components},9491:e=>{"use strict";e.exports=window.wp.compose},7143:e=>{"use strict";e.exports=window.wp.data},8443:e=>{"use strict";e.exports=window.wp.date},8490:e=>{"use strict";e.exports=window.wp.domReady},3656:e=>{"use strict";e.exports=window.wp.editor},8468:e=>{"use strict";e.exports=window.wp.element},2619:e=>{"use strict";e.exports=window.wp.hooks},7723:e=>{"use strict";e.exports=window.wp.i18n},8558:e=>{"use strict";e.exports=window.wp.keycodes},2279:e=>{"use strict";e.exports=window.wp.plugins},5573:e=>{"use strict";e.exports=window.wp.primitives},3832:e=>{"use strict";e.exports=window.wp.url},6072:e=>{function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},3022:(e,t,r)=>{"use strict";function n(e){var t,r,a="";if("string"==typeof e||"number"==typeof e)a+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(r=n(e[t]))&&(a&&(a+=" "),a+=r)}else for(r in e)e[r]&&(a&&(a+=" "),a+=r);return a}r.d(t,{A:()=>a});const a=function(){for(var e,t,r=0,a="",s=arguments.length;r<s;r++)(e=arguments[r])&&(t=n(e))&&(a&&(a+=" "),a+=t);return a}},3175:(e,t,r)=>{"use strict";r.d(t,{Gz:()=>g});var n,a=r(5006),s=r(2826),i=r(2485);!function(e){e[e.NUM=35]="NUM",e[e.SEMI=59]="SEMI",e[e.EQUALS=61]="EQUALS",e[e.ZERO=48]="ZERO",e[e.NINE=57]="NINE",e[e.LOWER_A=97]="LOWER_A",e[e.LOWER_F=102]="LOWER_F",e[e.LOWER_X=120]="LOWER_X",e[e.LOWER_Z=122]="LOWER_Z",e[e.UPPER_A=65]="UPPER_A",e[e.UPPER_F=70]="UPPER_F",e[e.UPPER_Z=90]="UPPER_Z"}(n||(n={}));var o,c,l;function u(e){return e>=n.ZERO&&e<=n.NINE}function d(e){return e===n.EQUALS||function(e){return e>=n.UPPER_A&&e<=n.UPPER_Z||e>=n.LOWER_A&&e<=n.LOWER_Z||u(e)}(e)}!function(e){e[e.VALUE_LENGTH=49152]="VALUE_LENGTH",e[e.BRANCH_LENGTH=16256]="BRANCH_LENGTH",e[e.JUMP_TABLE=127]="JUMP_TABLE"}(o||(o={})),function(e){e[e.EntityStart=0]="EntityStart",e[e.NumericStart=1]="NumericStart",e[e.NumericDecimal=2]="NumericDecimal",e[e.NumericHex=3]="NumericHex",e[e.NamedEntity=4]="NamedEntity"}(c||(c={})),function(e){e[e.Legacy=0]="Legacy",e[e.Strict=1]="Strict",e[e.Attribute=2]="Attribute"}(l||(l={}));class p{constructor(e,t,r){this.decodeTree=e,this.emitCodePoint=t,this.errors=r,this.state=c.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=l.Strict}startEntity(e){this.decodeMode=e,this.state=c.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(e,t){switch(this.state){case c.EntityStart:return e.charCodeAt(t)===n.NUM?(this.state=c.NumericStart,this.consumed+=1,this.stateNumericStart(e,t+1)):(this.state=c.NamedEntity,this.stateNamedEntity(e,t));case c.NumericStart:return this.stateNumericStart(e,t);case c.NumericDecimal:return this.stateNumericDecimal(e,t);case c.NumericHex:return this.stateNumericHex(e,t);case c.NamedEntity:return this.stateNamedEntity(e,t)}}stateNumericStart(e,t){return t>=e.length?-1:(32|e.charCodeAt(t))===n.LOWER_X?(this.state=c.NumericHex,this.consumed+=1,this.stateNumericHex(e,t+1)):(this.state=c.NumericDecimal,this.stateNumericDecimal(e,t))}addToNumericResult(e,t,r,n){if(t!==r){const a=r-t;this.result=this.result*Math.pow(n,a)+parseInt(e.substr(t,a),n),this.consumed+=a}}stateNumericHex(e,t){const r=t;for(;t<e.length;){const s=e.charCodeAt(t);if(!(u(s)||(a=s,a>=n.UPPER_A&&a<=n.UPPER_F||a>=n.LOWER_A&&a<=n.LOWER_F)))return this.addToNumericResult(e,r,t,16),this.emitNumericEntity(s,3);t+=1}var a;return this.addToNumericResult(e,r,t,16),-1}stateNumericDecimal(e,t){const r=t;for(;t<e.length;){const n=e.charCodeAt(t);if(!u(n))return this.addToNumericResult(e,r,t,10),this.emitNumericEntity(n,2);t+=1}return this.addToNumericResult(e,r,t,10),-1}emitNumericEntity(e,t){var r;if(this.consumed<=t)return null===(r=this.errors)||void 0===r||r.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(e===n.SEMI)this.consumed+=1;else if(this.decodeMode===l.Strict)return 0;return this.emitCodePoint((0,i.y6)(this.result),this.consumed),this.errors&&(e!==n.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(e,t){const{decodeTree:r}=this;let a=r[this.treeIndex],s=(a&o.VALUE_LENGTH)>>14;for(;t<e.length;t++,this.excess++){const i=e.charCodeAt(t);if(this.treeIndex=m(r,a,this.treeIndex+Math.max(1,s),i),this.treeIndex<0)return 0===this.result||this.decodeMode===l.Attribute&&(0===s||d(i))?0:this.emitNotTerminatedNamedEntity();if(a=r[this.treeIndex],s=(a&o.VALUE_LENGTH)>>14,0!==s){if(i===n.SEMI)return this.emitNamedEntityData(this.treeIndex,s,this.consumed+this.excess);this.decodeMode!==l.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return-1}emitNotTerminatedNamedEntity(){var e;const{result:t,decodeTree:r}=this,n=(r[t]&o.VALUE_LENGTH)>>14;return this.emitNamedEntityData(t,n,this.consumed),null===(e=this.errors)||void 0===e||e.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(e,t,r){const{decodeTree:n}=this;return this.emitCodePoint(1===t?n[e]&~o.VALUE_LENGTH:n[e+1],r),3===t&&this.emitCodePoint(n[e+2],r),r}end(){var e;switch(this.state){case c.NamedEntity:return 0===this.result||this.decodeMode===l.Attribute&&this.result!==this.treeIndex?0:this.emitNotTerminatedNamedEntity();case c.NumericDecimal:return this.emitNumericEntity(0,2);case c.NumericHex:return this.emitNumericEntity(0,3);case c.NumericStart:return null===(e=this.errors)||void 0===e||e.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case c.EntityStart:return 0}}}function h(e){let t="";const r=new p(e,(e=>t+=(0,i.MK)(e)));return function(e,n){let a=0,s=0;for(;(s=e.indexOf("&",s))>=0;){t+=e.slice(a,s),r.startEntity(n);const i=r.write(e,s+1);if(i<0){a=s+r.end();break}a=s+i,s=0===i?a+1:a}const i=t+e.slice(a);return t="",i}}function m(e,t,r,n){const a=(t&o.BRANCH_LENGTH)>>7,s=t&o.JUMP_TABLE;if(0===a)return 0!==s&&n===s?r:-1;if(s){const t=n-s;return t<0||t>=a?-1:e[r+t]-1}let i=r,c=i+a-1;for(;i<=c;){const t=i+c>>>1,r=e[t];if(r<n)i=t+1;else{if(!(r>n))return e[t+a];c=t-1}}return-1}const f=h(a.A);h(s.A);function g(e,t=l.Legacy){return f(e,t)}},2485:(e,t,r)=>{"use strict";var n;r.d(t,{MK:()=>s,y6:()=>i});const a=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),s=null!==(n=String.fromCodePoint)&&void 0!==n?n:function(e){let t="";return e>65535&&(e-=65536,t+=String.fromCharCode(e>>>10&1023|55296),e=56320|1023&e),t+=String.fromCharCode(e),t};function i(e){var t;return e>=55296&&e<=57343||e>1114111?65533:null!==(t=a.get(e))&&void 0!==t?t:e}},7187:(e,t,r)=>{"use strict";r(7431),r(6834)},6834:(e,t,r)=>{"use strict";const n=new Map([[34,"&quot;"],[38,"&amp;"],[39,"&apos;"],[60,"&lt;"],[62,"&gt;"]]);String.prototype.codePointAt;function a(e,t){return function(r){let n,a=0,s="";for(;n=e.exec(r);)a!==n.index&&(s+=r.substring(a,n.index)),s+=t.get(n[0].charCodeAt(0)),a=n.index+1;return s+r.substring(a)}}a(/[&<>'"]/g,n),a(/["&\u00A0]/g,new Map([[34,"&quot;"],[38,"&amp;"],[160,"&nbsp;"]])),a(/[&<>\u00A0]/g,new Map([[38,"&amp;"],[60,"&lt;"],[62,"&gt;"],[160,"&nbsp;"]]))},5006:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n=new Uint16Array('ᵁ<Õıʊҝջאٵ۞ޢߖࠏ੊ઑඡ๭༉༦჊ረዡᐕᒝᓃᓟᔥ\0\0\0\0\0\0ᕫᛍᦍᰒᷝ὾⁠↰⊍⏀⏻⑂⠤⤒ⴈ⹈⿎〖㊺㘹㞬㣾㨨㩱㫠㬮ࠀEMabcfglmnoprstu\\bfms¦³¹ÈÏlig耻Æ䃆P耻&䀦cute耻Á䃁reve;䄂Āiyx}rc耻Â䃂;䐐r;쀀𝔄rave耻À䃀pha;䎑acr;䄀d;橓Āgp¡on;䄄f;쀀𝔸plyFunction;恡ing耻Å䃅Ācs¾Ãr;쀀𝒜ign;扔ilde耻Ã䃃ml耻Ä䃄ЀaceforsuåûþėĜĢħĪĀcrêòkslash;或Ŷöø;櫧ed;挆y;䐑ƀcrtąċĔause;戵noullis;愬a;䎒r;쀀𝔅pf;쀀𝔹eve;䋘còēmpeq;扎܀HOacdefhilorsuōőŖƀƞƢƵƷƺǜȕɳɸɾcy;䐧PY耻©䂩ƀcpyŝŢźute;䄆Ā;iŧŨ拒talDifferentialD;慅leys;愭ȀaeioƉƎƔƘron;䄌dil耻Ç䃇rc;䄈nint;戰ot;䄊ĀdnƧƭilla;䂸terDot;䂷òſi;䎧rcleȀDMPTǇǋǑǖot;抙inus;抖lus;投imes;抗oĀcsǢǸkwiseContourIntegral;戲eCurlyĀDQȃȏoubleQuote;思uote;怙ȀlnpuȞȨɇɕonĀ;eȥȦ户;橴ƀgitȯȶȺruent;扡nt;戯ourIntegral;戮ĀfrɌɎ;愂oduct;成nterClockwiseContourIntegral;戳oss;樯cr;쀀𝒞pĀ;Cʄʅ拓ap;才րDJSZacefiosʠʬʰʴʸˋ˗ˡ˦̳ҍĀ;oŹʥtrahd;椑cy;䐂cy;䐅cy;䐏ƀgrsʿ˄ˇger;怡r;憡hv;櫤Āayː˕ron;䄎;䐔lĀ;t˝˞戇a;䎔r;쀀𝔇Āaf˫̧Ācm˰̢riticalȀADGT̖̜̀̆cute;䂴oŴ̋̍;䋙bleAcute;䋝rave;䁠ilde;䋜ond;拄ferentialD;慆Ѱ̽\0\0\0͔͂\0Ѕf;쀀𝔻ƀ;DE͈͉͍䂨ot;惜qual;扐blèCDLRUVͣͲ΂ϏϢϸontourIntegraìȹoɴ͹\0\0ͻ»͉nArrow;懓Āeo·ΤftƀARTΐΖΡrrow;懐ightArrow;懔eåˊngĀLRΫτeftĀARγιrrow;柸ightArrow;柺ightArrow;柹ightĀATϘϞrrow;懒ee;抨pɁϩ\0\0ϯrrow;懑ownArrow;懕erticalBar;戥ǹABLRTaВЪаўѿͼrrowƀ;BUНОТ憓ar;椓pArrow;懵reve;䌑eft˒к\0ц\0ѐightVector;楐eeVector;楞ectorĀ;Bљњ憽ar;楖ightǔѧ\0ѱeeVector;楟ectorĀ;BѺѻ懁ar;楗eeĀ;A҆҇护rrow;憧ĀctҒҗr;쀀𝒟rok;䄐ࠀNTacdfglmopqstuxҽӀӄӋӞӢӧӮӵԡԯԶՒ՝ՠեG;䅊H耻Ð䃐cute耻É䃉ƀaiyӒӗӜron;䄚rc耻Ê䃊;䐭ot;䄖r;쀀𝔈rave耻È䃈ement;戈ĀapӺӾcr;䄒tyɓԆ\0\0ԒmallSquare;旻erySmallSquare;斫ĀgpԦԪon;䄘f;쀀𝔼silon;䎕uĀaiԼՉlĀ;TՂՃ橵ilde;扂librium;懌Āci՗՚r;愰m;橳a;䎗ml耻Ë䃋Āipժկsts;戃onentialE;慇ʀcfiosօֈ֍ֲ׌y;䐤r;쀀𝔉lledɓ֗\0\0֣mallSquare;旼erySmallSquare;斪Ͱֺ\0ֿ\0\0ׄf;쀀𝔽All;戀riertrf;愱cò׋؀JTabcdfgorstר׬ׯ׺؀ؒؖ؛؝أ٬ٲcy;䐃耻>䀾mmaĀ;d׷׸䎓;䏜reve;䄞ƀeiy؇،ؐdil;䄢rc;䄜;䐓ot;䄠r;쀀𝔊;拙pf;쀀𝔾eater̀EFGLSTصلَٖٛ٦qualĀ;Lؾؿ扥ess;招ullEqual;执reater;檢ess;扷lantEqual;橾ilde;扳cr;쀀𝒢;扫ЀAacfiosuڅڋږڛڞڪھۊRDcy;䐪Āctڐڔek;䋇;䁞irc;䄤r;愌lbertSpace;愋ǰگ\0ڲf;愍izontalLine;攀Āctۃۅòکrok;䄦mpńېۘownHumðįqual;扏܀EJOacdfgmnostuۺ۾܃܇܎ܚܞܡܨ݄ݸދޏޕcy;䐕lig;䄲cy;䐁cute耻Í䃍Āiyܓܘrc耻Î䃎;䐘ot;䄰r;愑rave耻Ì䃌ƀ;apܠܯܿĀcgܴܷr;䄪inaryI;慈lieóϝǴ݉\0ݢĀ;eݍݎ戬Āgrݓݘral;戫section;拂isibleĀCTݬݲomma;恣imes;恢ƀgptݿރވon;䄮f;쀀𝕀a;䎙cr;愐ilde;䄨ǫޚ\0ޞcy;䐆l耻Ï䃏ʀcfosuެ޷޼߂ߐĀiyޱ޵rc;䄴;䐙r;쀀𝔍pf;쀀𝕁ǣ߇\0ߌr;쀀𝒥rcy;䐈kcy;䐄΀HJacfosߤߨ߽߬߱ࠂࠈcy;䐥cy;䐌ppa;䎚Āey߶߻dil;䄶;䐚r;쀀𝔎pf;쀀𝕂cr;쀀𝒦րJTaceflmostࠥࠩࠬࡐࡣ঳সে্਷ੇcy;䐉耻<䀼ʀcmnpr࠷࠼ࡁࡄࡍute;䄹bda;䎛g;柪lacetrf;愒r;憞ƀaeyࡗ࡜ࡡron;䄽dil;䄻;䐛Āfsࡨ॰tԀACDFRTUVarࡾࢩࢱࣦ࣠ࣼयज़ΐ४Ānrࢃ࢏gleBracket;柨rowƀ;BR࢙࢚࢞憐ar;懤ightArrow;懆eiling;挈oǵࢷ\0ࣃbleBracket;柦nǔࣈ\0࣒eeVector;楡ectorĀ;Bࣛࣜ懃ar;楙loor;挊ightĀAV࣯ࣵrrow;憔ector;楎Āerँगeƀ;AVउऊऐ抣rrow;憤ector;楚iangleƀ;BEतथऩ抲ar;槏qual;抴pƀDTVषूौownVector;楑eeVector;楠ectorĀ;Bॖॗ憿ar;楘ectorĀ;B॥०憼ar;楒ightáΜs̀EFGLSTॾঋকঝঢভqualGreater;拚ullEqual;扦reater;扶ess;檡lantEqual;橽ilde;扲r;쀀𝔏Ā;eঽা拘ftarrow;懚idot;䄿ƀnpw৔ਖਛgȀLRlr৞৷ਂਐeftĀAR০৬rrow;柵ightArrow;柷ightArrow;柶eftĀarγਊightáοightáϊf;쀀𝕃erĀLRਢਬeftArrow;憙ightArrow;憘ƀchtਾੀੂòࡌ;憰rok;䅁;扪Ѐacefiosuਗ਼੝੠੷੼અઋ઎p;椅y;䐜Ādl੥੯iumSpace;恟lintrf;愳r;쀀𝔐nusPlus;戓pf;쀀𝕄cò੶;䎜ҀJacefostuણધભીଔଙඑ඗ඞcy;䐊cute;䅃ƀaey઴હાron;䅇dil;䅅;䐝ƀgswે૰଎ativeƀMTV૓૟૨ediumSpace;怋hiĀcn૦૘ë૙eryThiî૙tedĀGL૸ଆreaterGreateòٳessLesóੈLine;䀊r;쀀𝔑ȀBnptଢନଷ଺reak;恠BreakingSpace;䂠f;愕ڀ;CDEGHLNPRSTV୕ୖ୪୼஡௫ఄ౞಄ದ೘ൡඅ櫬Āou୛୤ngruent;扢pCap;扭oubleVerticalBar;戦ƀlqxஃஊ஛ement;戉ualĀ;Tஒஓ扠ilde;쀀≂̸ists;戄reater΀;EFGLSTஶஷ஽௉௓௘௥扯qual;扱ullEqual;쀀≧̸reater;쀀≫̸ess;批lantEqual;쀀⩾̸ilde;扵umpń௲௽ownHump;쀀≎̸qual;쀀≏̸eĀfsఊధtTriangleƀ;BEచఛడ拪ar;쀀⧏̸qual;括s̀;EGLSTవశ఼ౄోౘ扮qual;扰reater;扸ess;쀀≪̸lantEqual;쀀⩽̸ilde;扴estedĀGL౨౹reaterGreater;쀀⪢̸essLess;쀀⪡̸recedesƀ;ESಒಓಛ技qual;쀀⪯̸lantEqual;拠ĀeiಫಹverseElement;戌ghtTriangleƀ;BEೋೌ೒拫ar;쀀⧐̸qual;拭ĀquೝഌuareSuĀbp೨೹setĀ;E೰ೳ쀀⊏̸qual;拢ersetĀ;Eഃആ쀀⊐̸qual;拣ƀbcpഓതൎsetĀ;Eഛഞ쀀⊂⃒qual;抈ceedsȀ;ESTലള഻െ抁qual;쀀⪰̸lantEqual;拡ilde;쀀≿̸ersetĀ;E൘൛쀀⊃⃒qual;抉ildeȀ;EFT൮൯൵ൿ扁qual;扄ullEqual;扇ilde;扉erticalBar;戤cr;쀀𝒩ilde耻Ñ䃑;䎝܀Eacdfgmoprstuvලෂ෉෕ෛ෠෧෼ขภยา฿ไlig;䅒cute耻Ó䃓Āiy෎ීrc耻Ô䃔;䐞blac;䅐r;쀀𝔒rave耻Ò䃒ƀaei෮ෲ෶cr;䅌ga;䎩cron;䎟pf;쀀𝕆enCurlyĀDQฎบoubleQuote;怜uote;怘;橔Āclวฬr;쀀𝒪ash耻Ø䃘iŬื฼de耻Õ䃕es;樷ml耻Ö䃖erĀBP๋๠Āar๐๓r;怾acĀek๚๜;揞et;掴arenthesis;揜Ҁacfhilors๿ງຊຏຒດຝະ໼rtialD;戂y;䐟r;쀀𝔓i;䎦;䎠usMinus;䂱Āipຢອncareplanåڝf;愙Ȁ;eio຺ູ໠໤檻cedesȀ;EST່້໏໚扺qual;檯lantEqual;扼ilde;找me;怳Ādp໩໮uct;戏ortionĀ;aȥ໹l;戝Āci༁༆r;쀀𝒫;䎨ȀUfos༑༖༛༟OT耻"䀢r;쀀𝔔pf;愚cr;쀀𝒬؀BEacefhiorsu༾གྷཇའཱིྦྷྪྭ႖ႩႴႾarr;椐G耻®䂮ƀcnrཎནབute;䅔g;柫rĀ;tཛྷཝ憠l;椖ƀaeyཧཬཱron;䅘dil;䅖;䐠Ā;vླྀཹ愜erseĀEUྂྙĀlq྇ྎement;戋uilibrium;懋pEquilibrium;楯r»ཹo;䎡ghtЀACDFTUVa࿁࿫࿳ဢဨၛႇϘĀnr࿆࿒gleBracket;柩rowƀ;BL࿜࿝࿡憒ar;懥eftArrow;懄eiling;按oǵ࿹\0စbleBracket;柧nǔည\0နeeVector;楝ectorĀ;Bဝသ懂ar;楕loor;挋Āerိ၃eƀ;AVဵံြ抢rrow;憦ector;楛iangleƀ;BEၐၑၕ抳ar;槐qual;抵pƀDTVၣၮၸownVector;楏eeVector;楜ectorĀ;Bႂႃ憾ar;楔ectorĀ;B႑႒懀ar;楓Āpuႛ႞f;愝ndImplies;楰ightarrow;懛ĀchႹႼr;愛;憱leDelayed;槴ڀHOacfhimoqstuფჱჷჽᄙᄞᅑᅖᅡᅧᆵᆻᆿĀCcჩხHcy;䐩y;䐨FTcy;䐬cute;䅚ʀ;aeiyᄈᄉᄎᄓᄗ檼ron;䅠dil;䅞rc;䅜;䐡r;쀀𝔖ortȀDLRUᄪᄴᄾᅉownArrow»ОeftArrow»࢚ightArrow»࿝pArrow;憑gma;䎣allCircle;战pf;쀀𝕊ɲᅭ\0\0ᅰt;戚areȀ;ISUᅻᅼᆉᆯ斡ntersection;抓uĀbpᆏᆞsetĀ;Eᆗᆘ抏qual;抑ersetĀ;Eᆨᆩ抐qual;抒nion;抔cr;쀀𝒮ar;拆ȀbcmpᇈᇛሉላĀ;sᇍᇎ拐etĀ;Eᇍᇕqual;抆ĀchᇠህeedsȀ;ESTᇭᇮᇴᇿ扻qual;檰lantEqual;扽ilde;承Tháྌ;我ƀ;esሒሓሣ拑rsetĀ;Eሜም抃qual;抇et»ሓրHRSacfhiorsሾቄ቉ቕ቞ቱቶኟዂወዑORN耻Þ䃞ADE;愢ĀHc቎ቒcy;䐋y;䐦Ābuቚቜ;䀉;䎤ƀaeyብቪቯron;䅤dil;䅢;䐢r;쀀𝔗Āeiቻ኉ǲኀ\0ኇefore;戴a;䎘Ācn኎ኘkSpace;쀀  Space;怉ldeȀ;EFTካኬኲኼ戼qual;扃ullEqual;扅ilde;扈pf;쀀𝕋ipleDot;惛Āctዖዛr;쀀𝒯rok;䅦ૡዷጎጚጦ\0ጬጱ\0\0\0\0\0ጸጽ፷ᎅ\0᏿ᐄᐊᐐĀcrዻጁute耻Ú䃚rĀ;oጇገ憟cir;楉rǣጓ\0጖y;䐎ve;䅬Āiyጞጣrc耻Û䃛;䐣blac;䅰r;쀀𝔘rave耻Ù䃙acr;䅪Ādiፁ፩erĀBPፈ፝Āarፍፐr;䁟acĀekፗፙ;揟et;掵arenthesis;揝onĀ;P፰፱拃lus;抎Āgp፻፿on;䅲f;쀀𝕌ЀADETadps᎕ᎮᎸᏄϨᏒᏗᏳrrowƀ;BDᅐᎠᎤar;椒ownArrow;懅ownArrow;憕quilibrium;楮eeĀ;AᏋᏌ报rrow;憥ownáϳerĀLRᏞᏨeftArrow;憖ightArrow;憗iĀ;lᏹᏺ䏒on;䎥ing;䅮cr;쀀𝒰ilde;䅨ml耻Ü䃜ҀDbcdefosvᐧᐬᐰᐳᐾᒅᒊᒐᒖash;披ar;櫫y;䐒ashĀ;lᐻᐼ抩;櫦Āerᑃᑅ;拁ƀbtyᑌᑐᑺar;怖Ā;iᑏᑕcalȀBLSTᑡᑥᑪᑴar;戣ine;䁼eparator;杘ilde;所ThinSpace;怊r;쀀𝔙pf;쀀𝕍cr;쀀𝒱dash;抪ʀcefosᒧᒬᒱᒶᒼirc;䅴dge;拀r;쀀𝔚pf;쀀𝕎cr;쀀𝒲Ȁfiosᓋᓐᓒᓘr;쀀𝔛;䎞pf;쀀𝕏cr;쀀𝒳ҀAIUacfosuᓱᓵᓹᓽᔄᔏᔔᔚᔠcy;䐯cy;䐇cy;䐮cute耻Ý䃝Āiyᔉᔍrc;䅶;䐫r;쀀𝔜pf;쀀𝕐cr;쀀𝒴ml;䅸ЀHacdefosᔵᔹᔿᕋᕏᕝᕠᕤcy;䐖cute;䅹Āayᕄᕉron;䅽;䐗ot;䅻ǲᕔ\0ᕛoWidtè૙a;䎖r;愨pf;愤cr;쀀𝒵௡ᖃᖊᖐ\0ᖰᖶᖿ\0\0\0\0ᗆᗛᗫᙟ᙭\0ᚕ᚛ᚲᚹ\0ᚾcute耻á䃡reve;䄃̀;Ediuyᖜᖝᖡᖣᖨᖭ戾;쀀∾̳;房rc耻â䃢te肻´̆;䐰lig耻æ䃦Ā;r²ᖺ;쀀𝔞rave耻à䃠ĀepᗊᗖĀfpᗏᗔsym;愵èᗓha;䎱ĀapᗟcĀclᗤᗧr;䄁g;樿ɤᗰ\0\0ᘊʀ;adsvᗺᗻᗿᘁᘇ戧nd;橕;橜lope;橘;橚΀;elmrszᘘᘙᘛᘞᘿᙏᙙ戠;榤e»ᘙsdĀ;aᘥᘦ戡ѡᘰᘲᘴᘶᘸᘺᘼᘾ;榨;榩;榪;榫;榬;榭;榮;榯tĀ;vᙅᙆ戟bĀ;dᙌᙍ抾;榝Āptᙔᙗh;戢»¹arr;捼Āgpᙣᙧon;䄅f;쀀𝕒΀;Eaeiop዁ᙻᙽᚂᚄᚇᚊ;橰cir;橯;扊d;手s;䀧roxĀ;e዁ᚒñᚃing耻å䃥ƀctyᚡᚦᚨr;쀀𝒶;䀪mpĀ;e዁ᚯñʈilde耻ã䃣ml耻ä䃤Āciᛂᛈoninôɲnt;樑ࠀNabcdefiklnoprsu᛭ᛱᜰ᜼ᝃᝈ᝸᝽០៦ᠹᡐᜍ᤽᥈ᥰot;櫭Ācrᛶ᜞kȀcepsᜀᜅᜍᜓong;扌psilon;䏶rime;怵imĀ;e᜚᜛戽q;拍Ŷᜢᜦee;抽edĀ;gᜬᜭ挅e»ᜭrkĀ;t፜᜷brk;掶Āoyᜁᝁ;䐱quo;怞ʀcmprtᝓ᝛ᝡᝤᝨausĀ;eĊĉptyv;榰séᜌnoõēƀahwᝯ᝱ᝳ;䎲;愶een;扬r;쀀𝔟g΀costuvwឍឝឳេ៕៛៞ƀaiuបពរðݠrc;旯p»፱ƀdptឤឨឭot;樀lus;樁imes;樂ɱឹ\0\0ើcup;樆ar;昅riangleĀdu៍្own;施p;斳plus;樄eåᑄåᒭarow;植ƀako៭ᠦᠵĀcn៲ᠣkƀlst៺֫᠂ozenge;槫riangleȀ;dlr᠒᠓᠘᠝斴own;斾eft;旂ight;斸k;搣Ʊᠫ\0ᠳƲᠯ\0ᠱ;斒;斑4;斓ck;斈ĀeoᠾᡍĀ;qᡃᡆ쀀=⃥uiv;쀀≡⃥t;挐Ȁptwxᡙᡞᡧᡬf;쀀𝕓Ā;tᏋᡣom»Ꮜtie;拈؀DHUVbdhmptuvᢅᢖᢪᢻᣗᣛᣬ᣿ᤅᤊᤐᤡȀLRlrᢎᢐᢒᢔ;敗;敔;敖;敓ʀ;DUduᢡᢢᢤᢦᢨ敐;敦;敩;敤;敧ȀLRlrᢳᢵᢷᢹ;敝;敚;敜;教΀;HLRhlrᣊᣋᣍᣏᣑᣓᣕ救;敬;散;敠;敫;敢;敟ox;槉ȀLRlrᣤᣦᣨᣪ;敕;敒;攐;攌ʀ;DUduڽ᣷᣹᣻᣽;敥;敨;攬;攴inus;抟lus;択imes;抠ȀLRlrᤙᤛᤝ᤟;敛;敘;攘;攔΀;HLRhlrᤰᤱᤳᤵᤷ᤻᤹攂;敪;敡;敞;攼;攤;攜Āevģ᥂bar耻¦䂦Ȁceioᥑᥖᥚᥠr;쀀𝒷mi;恏mĀ;e᜚᜜lƀ;bhᥨᥩᥫ䁜;槅sub;柈Ŭᥴ᥾lĀ;e᥹᥺怢t»᥺pƀ;Eeįᦅᦇ;檮Ā;qۜۛೡᦧ\0᧨ᨑᨕᨲ\0ᨷᩐ\0\0᪴\0\0᫁\0\0ᬡᬮ᭍᭒\0᯽\0ᰌƀcpr᦭ᦲ᧝ute;䄇̀;abcdsᦿᧀᧄ᧊᧕᧙戩nd;橄rcup;橉Āau᧏᧒p;橋p;橇ot;橀;쀀∩︀Āeo᧢᧥t;恁îړȀaeiu᧰᧻ᨁᨅǰ᧵\0᧸s;橍on;䄍dil耻ç䃧rc;䄉psĀ;sᨌᨍ橌m;橐ot;䄋ƀdmnᨛᨠᨦil肻¸ƭptyv;榲t脀¢;eᨭᨮ䂢räƲr;쀀𝔠ƀceiᨽᩀᩍy;䑇ckĀ;mᩇᩈ朓ark»ᩈ;䏇r΀;Ecefms᩟᩠ᩢᩫ᪤᪪᪮旋;槃ƀ;elᩩᩪᩭ䋆q;扗eɡᩴ\0\0᪈rrowĀlr᩼᪁eft;憺ight;憻ʀRSacd᪒᪔᪖᪚᪟»ཇ;擈st;抛irc;抚ash;抝nint;樐id;櫯cir;槂ubsĀ;u᪻᪼晣it»᪼ˬ᫇᫔᫺\0ᬊonĀ;eᫍᫎ䀺Ā;qÇÆɭ᫙\0\0᫢aĀ;t᫞᫟䀬;䁀ƀ;fl᫨᫩᫫戁îᅠeĀmx᫱᫶ent»᫩eóɍǧ᫾\0ᬇĀ;dኻᬂot;橭nôɆƀfryᬐᬔᬗ;쀀𝕔oäɔ脀©;sŕᬝr;愗Āaoᬥᬩrr;憵ss;朗Ācuᬲᬷr;쀀𝒸Ābpᬼ᭄Ā;eᭁᭂ櫏;櫑Ā;eᭉᭊ櫐;櫒dot;拯΀delprvw᭠᭬᭷ᮂᮬᯔ᯹arrĀlr᭨᭪;椸;椵ɰ᭲\0\0᭵r;拞c;拟arrĀ;p᭿ᮀ憶;椽̀;bcdosᮏᮐᮖᮡᮥᮨ截rcap;橈Āauᮛᮞp;橆p;橊ot;抍r;橅;쀀∪︀Ȁalrv᮵ᮿᯞᯣrrĀ;mᮼᮽ憷;椼yƀevwᯇᯔᯘqɰᯎ\0\0ᯒreã᭳uã᭵ee;拎edge;拏en耻¤䂤earrowĀlrᯮ᯳eft»ᮀight»ᮽeäᯝĀciᰁᰇoninôǷnt;戱lcty;挭ঀAHabcdefhijlorstuwz᰸᰻᰿ᱝᱩᱵᲊᲞᲬᲷ᳻᳿ᴍᵻᶑᶫᶻ᷆᷍rò΁ar;楥Ȁglrs᱈ᱍ᱒᱔ger;怠eth;愸òᄳhĀ;vᱚᱛ怐»ऊūᱡᱧarow;椏aã̕Āayᱮᱳron;䄏;䐴ƀ;ao̲ᱼᲄĀgrʿᲁr;懊tseq;橷ƀglmᲑᲔᲘ耻°䂰ta;䎴ptyv;榱ĀirᲣᲨsht;楿;쀀𝔡arĀlrᲳᲵ»ࣜ»သʀaegsv᳂͸᳖᳜᳠mƀ;oș᳊᳔ndĀ;ș᳑uit;晦amma;䏝in;拲ƀ;io᳧᳨᳸䃷de脀÷;o᳧ᳰntimes;拇nø᳷cy;䑒cɯᴆ\0\0ᴊrn;挞op;挍ʀlptuwᴘᴝᴢᵉᵕlar;䀤f;쀀𝕕ʀ;emps̋ᴭᴷᴽᵂqĀ;d͒ᴳot;扑inus;戸lus;戔quare;抡blebarwedgåúnƀadhᄮᵝᵧownarrowóᲃarpoonĀlrᵲᵶefôᲴighôᲶŢᵿᶅkaro÷གɯᶊ\0\0ᶎrn;挟op;挌ƀcotᶘᶣᶦĀryᶝᶡ;쀀𝒹;䑕l;槶rok;䄑Ādrᶰᶴot;拱iĀ;fᶺ᠖斿Āah᷀᷃ròЩaòྦangle;榦Āci᷒ᷕy;䑟grarr;柿ऀDacdefglmnopqrstuxḁḉḙḸոḼṉṡṾấắẽỡἪἷὄ὎὚ĀDoḆᴴoôᲉĀcsḎḔute耻é䃩ter;橮ȀaioyḢḧḱḶron;䄛rĀ;cḭḮ扖耻ê䃪lon;払;䑍ot;䄗ĀDrṁṅot;扒;쀀𝔢ƀ;rsṐṑṗ檚ave耻è䃨Ā;dṜṝ檖ot;檘Ȁ;ilsṪṫṲṴ檙nters;揧;愓Ā;dṹṺ檕ot;檗ƀapsẅẉẗcr;䄓tyƀ;svẒẓẕ戅et»ẓpĀ1;ẝẤĳạả;怄;怅怃ĀgsẪẬ;䅋p;怂ĀgpẴẸon;䄙f;쀀𝕖ƀalsỄỎỒrĀ;sỊị拕l;槣us;橱iƀ;lvỚớở䎵on»ớ;䏵ȀcsuvỪỳἋἣĀioữḱrc»Ḯɩỹ\0\0ỻíՈantĀglἂἆtr»ṝess»Ṻƀaeiἒ἖Ἒls;䀽st;扟vĀ;DȵἠD;橸parsl;槥ĀDaἯἳot;打rr;楱ƀcdiἾὁỸr;愯oô͒ĀahὉὋ;䎷耻ð䃰Āmrὓὗl耻ë䃫o;悬ƀcipὡὤὧl;䀡sôծĀeoὬὴctatioîՙnentialåչৡᾒ\0ᾞ\0ᾡᾧ\0\0ῆῌ\0ΐ\0ῦῪ \0 ⁚llingdotseñṄy;䑄male;晀ƀilrᾭᾳ῁lig;耀ﬃɩᾹ\0\0᾽g;耀ﬀig;耀ﬄ;쀀𝔣lig;耀ﬁlig;쀀fjƀaltῙ῜ῡt;晭ig;耀ﬂns;斱of;䆒ǰ΅\0ῳf;쀀𝕗ĀakֿῷĀ;vῼ´拔;櫙artint;樍Āao‌⁕Ācs‑⁒α‚‰‸⁅⁈\0⁐β•‥‧‪‬\0‮耻½䂽;慓耻¼䂼;慕;慙;慛Ƴ‴\0‶;慔;慖ʴ‾⁁\0\0⁃耻¾䂾;慗;慜5;慘ƶ⁌\0⁎;慚;慝8;慞l;恄wn;挢cr;쀀𝒻ࢀEabcdefgijlnorstv₂₉₟₥₰₴⃰⃵⃺⃿℃ℒℸ̗ℾ⅒↞Ā;lٍ₇;檌ƀcmpₐₕ₝ute;䇵maĀ;dₜ᳚䎳;檆reve;䄟Āiy₪₮rc;䄝;䐳ot;䄡Ȁ;lqsؾق₽⃉ƀ;qsؾٌ⃄lanô٥Ȁ;cdl٥⃒⃥⃕c;檩otĀ;o⃜⃝檀Ā;l⃢⃣檂;檄Ā;e⃪⃭쀀⋛︀s;檔r;쀀𝔤Ā;gٳ؛mel;愷cy;䑓Ȁ;Eajٚℌℎℐ;檒;檥;檤ȀEaesℛℝ℩ℴ;扩pĀ;p℣ℤ檊rox»ℤĀ;q℮ℯ檈Ā;q℮ℛim;拧pf;쀀𝕘Āci⅃ⅆr;愊mƀ;el٫ⅎ⅐;檎;檐茀>;cdlqr׮ⅠⅪⅮⅳⅹĀciⅥⅧ;檧r;橺ot;拗Par;榕uest;橼ʀadelsↄⅪ←ٖ↛ǰ↉\0↎proø₞r;楸qĀlqؿ↖lesó₈ií٫Āen↣↭rtneqq;쀀≩︀Å↪ԀAabcefkosy⇄⇇⇱⇵⇺∘∝∯≨≽ròΠȀilmr⇐⇔⇗⇛rsðᒄf»․ilôکĀdr⇠⇤cy;䑊ƀ;cwࣴ⇫⇯ir;楈;憭ar;意irc;䄥ƀalr∁∎∓rtsĀ;u∉∊晥it»∊lip;怦con;抹r;쀀𝔥sĀew∣∩arow;椥arow;椦ʀamopr∺∾≃≞≣rr;懿tht;戻kĀlr≉≓eftarrow;憩ightarrow;憪f;쀀𝕙bar;怕ƀclt≯≴≸r;쀀𝒽asè⇴rok;䄧Ābp⊂⊇ull;恃hen»ᱛૡ⊣\0⊪\0⊸⋅⋎\0⋕⋳\0\0⋸⌢⍧⍢⍿\0⎆⎪⎴cute耻í䃭ƀ;iyݱ⊰⊵rc耻î䃮;䐸Ācx⊼⊿y;䐵cl耻¡䂡ĀfrΟ⋉;쀀𝔦rave耻ì䃬Ȁ;inoܾ⋝⋩⋮Āin⋢⋦nt;樌t;戭fin;槜ta;愩lig;䄳ƀaop⋾⌚⌝ƀcgt⌅⌈⌗r;䄫ƀelpܟ⌏⌓inåގarôܠh;䄱f;抷ed;䆵ʀ;cfotӴ⌬⌱⌽⍁are;愅inĀ;t⌸⌹戞ie;槝doô⌙ʀ;celpݗ⍌⍐⍛⍡al;抺Āgr⍕⍙eróᕣã⍍arhk;樗rod;樼Ȁcgpt⍯⍲⍶⍻y;䑑on;䄯f;쀀𝕚a;䎹uest耻¿䂿Āci⎊⎏r;쀀𝒾nʀ;EdsvӴ⎛⎝⎡ӳ;拹ot;拵Ā;v⎦⎧拴;拳Ā;iݷ⎮lde;䄩ǫ⎸\0⎼cy;䑖l耻ï䃯̀cfmosu⏌⏗⏜⏡⏧⏵Āiy⏑⏕rc;䄵;䐹r;쀀𝔧ath;䈷pf;쀀𝕛ǣ⏬\0⏱r;쀀𝒿rcy;䑘kcy;䑔Ѐacfghjos␋␖␢␧␭␱␵␻ppaĀ;v␓␔䎺;䏰Āey␛␠dil;䄷;䐺r;쀀𝔨reen;䄸cy;䑅cy;䑜pf;쀀𝕜cr;쀀𝓀஀ABEHabcdefghjlmnoprstuv⑰⒁⒆⒍⒑┎┽╚▀♎♞♥♹♽⚚⚲⛘❝❨➋⟀⠁⠒ƀart⑷⑺⑼rò৆òΕail;椛arr;椎Ā;gঔ⒋;檋ar;楢ॣ⒥\0⒪\0⒱\0\0\0\0\0⒵Ⓔ\0ⓆⓈⓍ\0⓹ute;䄺mptyv;榴raîࡌbda;䎻gƀ;dlࢎⓁⓃ;榑åࢎ;檅uo耻«䂫rЀ;bfhlpst࢙ⓞⓦⓩ⓫⓮⓱⓵Ā;f࢝ⓣs;椟s;椝ë≒p;憫l;椹im;楳l;憢ƀ;ae⓿─┄檫il;椙Ā;s┉┊檭;쀀⪭︀ƀabr┕┙┝rr;椌rk;杲Āak┢┬cĀek┨┪;䁻;䁛Āes┱┳;榋lĀdu┹┻;榏;榍Ȁaeuy╆╋╖╘ron;䄾Ādi═╔il;䄼ìࢰâ┩;䐻Ȁcqrs╣╦╭╽a;椶uoĀ;rนᝆĀdu╲╷har;楧shar;楋h;憲ʀ;fgqs▋▌উ◳◿扤tʀahlrt▘▤▷◂◨rrowĀ;t࢙□aé⓶arpoonĀdu▯▴own»њp»०eftarrows;懇ightƀahs◍◖◞rrowĀ;sࣴࢧarpoonó྘quigarro÷⇰hreetimes;拋ƀ;qs▋ও◺lanôবʀ;cdgsব☊☍☝☨c;檨otĀ;o☔☕橿Ā;r☚☛檁;檃Ā;e☢☥쀀⋚︀s;檓ʀadegs☳☹☽♉♋pproøⓆot;拖qĀgq♃♅ôউgtò⒌ôছiíলƀilr♕࣡♚sht;楼;쀀𝔩Ā;Eজ♣;檑š♩♶rĀdu▲♮Ā;l॥♳;楪lk;斄cy;䑙ʀ;achtੈ⚈⚋⚑⚖rò◁orneòᴈard;楫ri;旺Āio⚟⚤dot;䅀ustĀ;a⚬⚭掰che»⚭ȀEaes⚻⚽⛉⛔;扨pĀ;p⛃⛄檉rox»⛄Ā;q⛎⛏檇Ā;q⛎⚻im;拦Ѐabnoptwz⛩⛴⛷✚✯❁❇❐Ānr⛮⛱g;柬r;懽rëࣁgƀlmr⛿✍✔eftĀar০✇ightá৲apsto;柼ightá৽parrowĀlr✥✩efô⓭ight;憬ƀafl✶✹✽r;榅;쀀𝕝us;樭imes;樴š❋❏st;戗áፎƀ;ef❗❘᠀旊nge»❘arĀ;l❤❥䀨t;榓ʀachmt❳❶❼➅➇ròࢨorneòᶌarĀ;d྘➃;業;怎ri;抿̀achiqt➘➝ੀ➢➮➻quo;怹r;쀀𝓁mƀ;egল➪➬;檍;檏Ābu┪➳oĀ;rฟ➹;怚rok;䅂萀<;cdhilqrࠫ⟒☹⟜⟠⟥⟪⟰Āci⟗⟙;檦r;橹reå◲mes;拉arr;楶uest;橻ĀPi⟵⟹ar;榖ƀ;ef⠀भ᠛旃rĀdu⠇⠍shar;楊har;楦Āen⠗⠡rtneqq;쀀≨︀Å⠞܀Dacdefhilnopsu⡀⡅⢂⢎⢓⢠⢥⢨⣚⣢⣤ઃ⣳⤂Dot;戺Ȁclpr⡎⡒⡣⡽r耻¯䂯Āet⡗⡙;時Ā;e⡞⡟朠se»⡟Ā;sျ⡨toȀ;dluျ⡳⡷⡻owîҌefôएðᏑker;斮Āoy⢇⢌mma;権;䐼ash;怔asuredangle»ᘦr;쀀𝔪o;愧ƀcdn⢯⢴⣉ro耻µ䂵Ȁ;acdᑤ⢽⣀⣄sôᚧir;櫰ot肻·Ƶusƀ;bd⣒ᤃ⣓戒Ā;uᴼ⣘;横ţ⣞⣡p;櫛ò−ðઁĀdp⣩⣮els;抧f;쀀𝕞Āct⣸⣽r;쀀𝓂pos»ᖝƀ;lm⤉⤊⤍䎼timap;抸ఀGLRVabcdefghijlmoprstuvw⥂⥓⥾⦉⦘⧚⧩⨕⨚⩘⩝⪃⪕⪤⪨⬄⬇⭄⭿⮮ⰴⱧⱼ⳩Āgt⥇⥋;쀀⋙̸Ā;v⥐௏쀀≫⃒ƀelt⥚⥲⥶ftĀar⥡⥧rrow;懍ightarrow;懎;쀀⋘̸Ā;v⥻ే쀀≪⃒ightarrow;懏ĀDd⦎⦓ash;抯ash;抮ʀbcnpt⦣⦧⦬⦱⧌la»˞ute;䅄g;쀀∠⃒ʀ;Eiop඄⦼⧀⧅⧈;쀀⩰̸d;쀀≋̸s;䅉roø඄urĀ;a⧓⧔普lĀ;s⧓ସǳ⧟\0⧣p肻 ଷmpĀ;e௹ఀʀaeouy⧴⧾⨃⨐⨓ǰ⧹\0⧻;橃on;䅈dil;䅆ngĀ;dൾ⨊ot;쀀⩭̸p;橂;䐽ash;怓΀;Aadqsxஒ⨩⨭⨻⩁⩅⩐rr;懗rĀhr⨳⨶k;椤Ā;oᏲᏰot;쀀≐̸uiöୣĀei⩊⩎ar;椨í஘istĀ;s஠டr;쀀𝔫ȀEest௅⩦⩹⩼ƀ;qs஼⩭௡ƀ;qs஼௅⩴lanô௢ií௪Ā;rஶ⪁»ஷƀAap⪊⪍⪑rò⥱rr;憮ar;櫲ƀ;svྍ⪜ྌĀ;d⪡⪢拼;拺cy;䑚΀AEadest⪷⪺⪾⫂⫅⫶⫹rò⥦;쀀≦̸rr;憚r;急Ȁ;fqs఻⫎⫣⫯tĀar⫔⫙rro÷⫁ightarro÷⪐ƀ;qs఻⪺⫪lanôౕĀ;sౕ⫴»శiíౝĀ;rవ⫾iĀ;eచథiäඐĀpt⬌⬑f;쀀𝕟膀¬;in⬙⬚⬶䂬nȀ;Edvஉ⬤⬨⬮;쀀⋹̸ot;쀀⋵̸ǡஉ⬳⬵;拷;拶iĀ;vಸ⬼ǡಸ⭁⭃;拾;拽ƀaor⭋⭣⭩rȀ;ast୻⭕⭚⭟lleì୻l;쀀⫽⃥;쀀∂̸lint;樔ƀ;ceಒ⭰⭳uåಥĀ;cಘ⭸Ā;eಒ⭽ñಘȀAait⮈⮋⮝⮧rò⦈rrƀ;cw⮔⮕⮙憛;쀀⤳̸;쀀↝̸ghtarrow»⮕riĀ;eೋೖ΀chimpqu⮽⯍⯙⬄୸⯤⯯Ȁ;cerല⯆ഷ⯉uå൅;쀀𝓃ortɭ⬅\0\0⯖ará⭖mĀ;e൮⯟Ā;q൴൳suĀbp⯫⯭å೸åഋƀbcp⯶ⰑⰙȀ;Ees⯿ⰀഢⰄ抄;쀀⫅̸etĀ;eഛⰋqĀ;qണⰀcĀ;eലⰗñസȀ;EesⰢⰣൟⰧ抅;쀀⫆̸etĀ;e൘ⰮqĀ;qൠⰣȀgilrⰽⰿⱅⱇìௗlde耻ñ䃱çృiangleĀlrⱒⱜeftĀ;eచⱚñదightĀ;eೋⱥñ೗Ā;mⱬⱭ䎽ƀ;esⱴⱵⱹ䀣ro;愖p;怇ҀDHadgilrsⲏⲔⲙⲞⲣⲰⲶⳓⳣash;抭arr;椄p;쀀≍⃒ash;抬ĀetⲨⲬ;쀀≥⃒;쀀>⃒nfin;槞ƀAetⲽⳁⳅrr;椂;쀀≤⃒Ā;rⳊⳍ쀀<⃒ie;쀀⊴⃒ĀAtⳘⳜrr;椃rie;쀀⊵⃒im;쀀∼⃒ƀAan⳰⳴ⴂrr;懖rĀhr⳺⳽k;椣Ā;oᏧᏥear;椧ቓ᪕\0\0\0\0\0\0\0\0\0\0\0\0\0ⴭ\0ⴸⵈⵠⵥ⵲ⶄᬇ\0\0ⶍⶫ\0ⷈⷎ\0ⷜ⸙⸫⸾⹃Ācsⴱ᪗ute耻ó䃳ĀiyⴼⵅrĀ;c᪞ⵂ耻ô䃴;䐾ʀabios᪠ⵒⵗǈⵚlac;䅑v;樸old;榼lig;䅓Ācr⵩⵭ir;榿;쀀𝔬ͯ⵹\0\0⵼\0ⶂn;䋛ave耻ò䃲;槁Ābmⶈ෴ar;榵Ȁacitⶕ⶘ⶥⶨrò᪀Āir⶝ⶠr;榾oss;榻nå๒;槀ƀaeiⶱⶵⶹcr;䅍ga;䏉ƀcdnⷀⷅǍron;䎿;榶pf;쀀𝕠ƀaelⷔ⷗ǒr;榷rp;榹΀;adiosvⷪⷫⷮ⸈⸍⸐⸖戨rò᪆Ȁ;efmⷷⷸ⸂⸅橝rĀ;oⷾⷿ愴f»ⷿ耻ª䂪耻º䂺gof;抶r;橖lope;橗;橛ƀclo⸟⸡⸧ò⸁ash耻ø䃸l;折iŬⸯ⸴de耻õ䃵esĀ;aǛ⸺s;樶ml耻ö䃶bar;挽ૡ⹞\0⹽\0⺀⺝\0⺢⺹\0\0⻋ຜ\0⼓\0\0⼫⾼\0⿈rȀ;astЃ⹧⹲຅脀¶;l⹭⹮䂶leìЃɩ⹸\0\0⹻m;櫳;櫽y;䐿rʀcimpt⺋⺏⺓ᡥ⺗nt;䀥od;䀮il;怰enk;怱r;쀀𝔭ƀimo⺨⺰⺴Ā;v⺭⺮䏆;䏕maô੶ne;明ƀ;tv⺿⻀⻈䏀chfork»´;䏖Āau⻏⻟nĀck⻕⻝kĀ;h⇴⻛;愎ö⇴sҀ;abcdemst⻳⻴ᤈ⻹⻽⼄⼆⼊⼎䀫cir;樣ir;樢Āouᵀ⼂;樥;橲n肻±ຝim;樦wo;樧ƀipu⼙⼠⼥ntint;樕f;쀀𝕡nd耻£䂣Ԁ;Eaceinosu່⼿⽁⽄⽇⾁⾉⾒⽾⾶;檳p;檷uå໙Ā;c໎⽌̀;acens່⽙⽟⽦⽨⽾pproø⽃urlyeñ໙ñ໎ƀaes⽯⽶⽺pprox;檹qq;檵im;拨iíໟmeĀ;s⾈ຮ怲ƀEas⽸⾐⽺ð⽵ƀdfp໬⾙⾯ƀals⾠⾥⾪lar;挮ine;挒urf;挓Ā;t໻⾴ï໻rel;抰Āci⿀⿅r;쀀𝓅;䏈ncsp;怈̀fiopsu⿚⋢⿟⿥⿫⿱r;쀀𝔮pf;쀀𝕢rime;恗cr;쀀𝓆ƀaeo⿸〉〓tĀei⿾々rnionóڰnt;樖stĀ;e【】䀿ñἙô༔઀ABHabcdefhilmnoprstux぀けさすムㄎㄫㅇㅢㅲㆎ㈆㈕㈤㈩㉘㉮㉲㊐㊰㊷ƀartぇおがròႳòϝail;検aròᱥar;楤΀cdenqrtとふへみわゔヌĀeuねぱ;쀀∽̱te;䅕iãᅮmptyv;榳gȀ;del࿑らるろ;榒;榥å࿑uo耻»䂻rր;abcfhlpstw࿜ガクシスゼゾダッデナp;極Ā;f࿠ゴs;椠;椳s;椞ë≝ð✮l;楅im;楴l;憣;憝Āaiパフil;椚oĀ;nホボ戶aló༞ƀabrョリヮrò៥rk;杳ĀakンヽcĀekヹ・;䁽;䁝Āes㄂㄄;榌lĀduㄊㄌ;榎;榐Ȁaeuyㄗㄜㄧㄩron;䅙Ādiㄡㄥil;䅗ì࿲âヺ;䑀Ȁclqsㄴㄷㄽㅄa;椷dhar;楩uoĀ;rȎȍh;憳ƀacgㅎㅟངlȀ;ipsླྀㅘㅛႜnåႻarôྩt;断ƀilrㅩဣㅮsht;楽;쀀𝔯ĀaoㅷㆆrĀduㅽㅿ»ѻĀ;l႑ㆄ;楬Ā;vㆋㆌ䏁;䏱ƀgns㆕ㇹㇼht̀ahlrstㆤㆰ㇂㇘㇤㇮rrowĀ;t࿜ㆭaéトarpoonĀduㆻㆿowîㅾp»႒eftĀah㇊㇐rrowó࿪arpoonóՑightarrows;應quigarro÷ニhreetimes;拌g;䋚ingdotseñἲƀahm㈍㈐㈓rò࿪aòՑ;怏oustĀ;a㈞㈟掱che»㈟mid;櫮Ȁabpt㈲㈽㉀㉒Ānr㈷㈺g;柭r;懾rëဃƀafl㉇㉊㉎r;榆;쀀𝕣us;樮imes;樵Āap㉝㉧rĀ;g㉣㉤䀩t;榔olint;樒arò㇣Ȁachq㉻㊀Ⴜ㊅quo;怺r;쀀𝓇Ābu・㊊oĀ;rȔȓƀhir㊗㊛㊠reåㇸmes;拊iȀ;efl㊪ၙᠡ㊫方tri;槎luhar;楨;愞ൡ㋕㋛㋟㌬㌸㍱\0㍺㎤\0\0㏬㏰\0㐨㑈㑚㒭㒱㓊㓱\0㘖\0\0㘳cute;䅛quï➺Ԁ;Eaceinpsyᇭ㋳㋵㋿㌂㌋㌏㌟㌦㌩;檴ǰ㋺\0㋼;檸on;䅡uåᇾĀ;dᇳ㌇il;䅟rc;䅝ƀEas㌖㌘㌛;檶p;檺im;择olint;樓iíሄ;䑁otƀ;be㌴ᵇ㌵担;橦΀Aacmstx㍆㍊㍗㍛㍞㍣㍭rr;懘rĀhr㍐㍒ë∨Ā;oਸ਼਴t耻§䂧i;䀻war;椩mĀin㍩ðnuóñt;朶rĀ;o㍶⁕쀀𝔰Ȁacoy㎂㎆㎑㎠rp;景Āhy㎋㎏cy;䑉;䑈rtɭ㎙\0\0㎜iäᑤaraì⹯耻­䂭Āgm㎨㎴maƀ;fv㎱㎲㎲䏃;䏂Ѐ;deglnprካ㏅㏉㏎㏖㏞㏡㏦ot;橪Ā;q኱ኰĀ;E㏓㏔檞;檠Ā;E㏛㏜檝;檟e;扆lus;樤arr;楲aròᄽȀaeit㏸㐈㐏㐗Āls㏽㐄lsetmé㍪hp;樳parsl;槤Ādlᑣ㐔e;挣Ā;e㐜㐝檪Ā;s㐢㐣檬;쀀⪬︀ƀflp㐮㐳㑂tcy;䑌Ā;b㐸㐹䀯Ā;a㐾㐿槄r;挿f;쀀𝕤aĀdr㑍ЂesĀ;u㑔㑕晠it»㑕ƀcsu㑠㑹㒟Āau㑥㑯pĀ;sᆈ㑫;쀀⊓︀pĀ;sᆴ㑵;쀀⊔︀uĀbp㑿㒏ƀ;esᆗᆜ㒆etĀ;eᆗ㒍ñᆝƀ;esᆨᆭ㒖etĀ;eᆨ㒝ñᆮƀ;afᅻ㒦ְrť㒫ֱ»ᅼaròᅈȀcemt㒹㒾㓂㓅r;쀀𝓈tmîñiì㐕aræᆾĀar㓎㓕rĀ;f㓔ឿ昆Āan㓚㓭ightĀep㓣㓪psiloîỠhé⺯s»⡒ʀbcmnp㓻㕞ሉ㖋㖎Ҁ;Edemnprs㔎㔏㔑㔕㔞㔣㔬㔱㔶抂;櫅ot;檽Ā;dᇚ㔚ot;櫃ult;櫁ĀEe㔨㔪;櫋;把lus;檿arr;楹ƀeiu㔽㕒㕕tƀ;en㔎㕅㕋qĀ;qᇚ㔏eqĀ;q㔫㔨m;櫇Ābp㕚㕜;櫕;櫓c̀;acensᇭ㕬㕲㕹㕻㌦pproø㋺urlyeñᇾñᇳƀaes㖂㖈㌛pproø㌚qñ㌗g;晪ڀ123;Edehlmnps㖩㖬㖯ሜ㖲㖴㗀㗉㗕㗚㗟㗨㗭耻¹䂹耻²䂲耻³䂳;櫆Āos㖹㖼t;檾ub;櫘Ā;dሢ㗅ot;櫄sĀou㗏㗒l;柉b;櫗arr;楻ult;櫂ĀEe㗤㗦;櫌;抋lus;櫀ƀeiu㗴㘉㘌tƀ;enሜ㗼㘂qĀ;qሢ㖲eqĀ;q㗧㗤m;櫈Ābp㘑㘓;櫔;櫖ƀAan㘜㘠㘭rr;懙rĀhr㘦㘨ë∮Ā;oਫ਩war;椪lig耻ß䃟௡㙑㙝㙠ዎ㙳㙹\0㙾㛂\0\0\0\0\0㛛㜃\0㜉㝬\0\0\0㞇ɲ㙖\0\0㙛get;挖;䏄rë๟ƀaey㙦㙫㙰ron;䅥dil;䅣;䑂lrec;挕r;쀀𝔱Ȁeiko㚆㚝㚵㚼ǲ㚋\0㚑eĀ4fኄኁaƀ;sv㚘㚙㚛䎸ym;䏑Ācn㚢㚲kĀas㚨㚮pproø዁im»ኬsðኞĀas㚺㚮ð዁rn耻þ䃾Ǭ̟㛆⋧es膀×;bd㛏㛐㛘䃗Ā;aᤏ㛕r;樱;樰ƀeps㛡㛣㜀á⩍Ȁ;bcf҆㛬㛰㛴ot;挶ir;櫱Ā;o㛹㛼쀀𝕥rk;櫚á㍢rime;怴ƀaip㜏㜒㝤dåቈ΀adempst㜡㝍㝀㝑㝗㝜㝟ngleʀ;dlqr㜰㜱㜶㝀㝂斵own»ᶻeftĀ;e⠀㜾ñम;扜ightĀ;e㊪㝋ñၚot;旬inus;樺lus;樹b;槍ime;樻ezium;揢ƀcht㝲㝽㞁Āry㝷㝻;쀀𝓉;䑆cy;䑛rok;䅧Āio㞋㞎xô᝷headĀlr㞗㞠eftarro÷ࡏightarrow»ཝऀAHabcdfghlmoprstuw㟐㟓㟗㟤㟰㟼㠎㠜㠣㠴㡑㡝㡫㢩㣌㣒㣪㣶ròϭar;楣Ācr㟜㟢ute耻ú䃺òᅐrǣ㟪\0㟭y;䑞ve;䅭Āiy㟵㟺rc耻û䃻;䑃ƀabh㠃㠆㠋ròᎭlac;䅱aòᏃĀir㠓㠘sht;楾;쀀𝔲rave耻ù䃹š㠧㠱rĀlr㠬㠮»ॗ»ႃlk;斀Āct㠹㡍ɯ㠿\0\0㡊rnĀ;e㡅㡆挜r»㡆op;挏ri;旸Āal㡖㡚cr;䅫肻¨͉Āgp㡢㡦on;䅳f;쀀𝕦̀adhlsuᅋ㡸㡽፲㢑㢠ownáᎳarpoonĀlr㢈㢌efô㠭ighô㠯iƀ;hl㢙㢚㢜䏅»ᏺon»㢚parrows;懈ƀcit㢰㣄㣈ɯ㢶\0\0㣁rnĀ;e㢼㢽挝r»㢽op;挎ng;䅯ri;旹cr;쀀𝓊ƀdir㣙㣝㣢ot;拰lde;䅩iĀ;f㜰㣨»᠓Āam㣯㣲rò㢨l耻ü䃼angle;榧ހABDacdeflnoprsz㤜㤟㤩㤭㦵㦸㦽㧟㧤㧨㧳㧹㧽㨁㨠ròϷarĀ;v㤦㤧櫨;櫩asèϡĀnr㤲㤷grt;榜΀eknprst㓣㥆㥋㥒㥝㥤㦖appá␕othinçẖƀhir㓫⻈㥙opô⾵Ā;hᎷ㥢ïㆍĀiu㥩㥭gmá㎳Ābp㥲㦄setneqĀ;q㥽㦀쀀⊊︀;쀀⫋︀setneqĀ;q㦏㦒쀀⊋︀;쀀⫌︀Āhr㦛㦟etá㚜iangleĀlr㦪㦯eft»थight»ၑy;䐲ash»ံƀelr㧄㧒㧗ƀ;beⷪ㧋㧏ar;抻q;扚lip;拮Ābt㧜ᑨaòᑩr;쀀𝔳tré㦮suĀbp㧯㧱»ജ»൙pf;쀀𝕧roð໻tré㦴Ācu㨆㨋r;쀀𝓋Ābp㨐㨘nĀEe㦀㨖»㥾nĀEe㦒㨞»㦐igzag;榚΀cefoprs㨶㨻㩖㩛㩔㩡㩪irc;䅵Ādi㩀㩑Ābg㩅㩉ar;機eĀ;qᗺ㩏;扙erp;愘r;쀀𝔴pf;쀀𝕨Ā;eᑹ㩦atèᑹcr;쀀𝓌ૣណ㪇\0㪋\0㪐㪛\0\0㪝㪨㪫㪯\0\0㫃㫎\0㫘ៜ៟tré៑r;쀀𝔵ĀAa㪔㪗ròσrò৶;䎾ĀAa㪡㪤ròθrò৫að✓is;拻ƀdptឤ㪵㪾Āfl㪺ឩ;쀀𝕩imåឲĀAa㫇㫊ròώròਁĀcq㫒ីr;쀀𝓍Āpt៖㫜ré។Ѐacefiosu㫰㫽㬈㬌㬑㬕㬛㬡cĀuy㫶㫻te耻ý䃽;䑏Āiy㬂㬆rc;䅷;䑋n耻¥䂥r;쀀𝔶cy;䑗pf;쀀𝕪cr;쀀𝓎Ācm㬦㬩y;䑎l耻ÿ䃿Ԁacdefhiosw㭂㭈㭔㭘㭤㭩㭭㭴㭺㮀cute;䅺Āay㭍㭒ron;䅾;䐷ot;䅼Āet㭝㭡træᕟa;䎶r;쀀𝔷cy;䐶grarr;懝pf;쀀𝕫cr;쀀𝓏Ājn㮅㮇;怍j;怌'.split("").map((e=>e.charCodeAt(0))))},2826:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n=new Uint16Array("Ȁaglq\tɭ\0\0p;䀦os;䀧t;䀾t;䀼uot;䀢".split("").map((e=>e.charCodeAt(0))))},7431:()=>{"use strict";function e(e){for(let t=1;t<e.length;t++)e[t][0]+=e[t-1][0]+1;return e}new Map(e([[9,"&Tab;"],[0,"&NewLine;"],[22,"&excl;"],[0,"&quot;"],[0,"&num;"],[0,"&dollar;"],[0,"&percnt;"],[0,"&amp;"],[0,"&apos;"],[0,"&lpar;"],[0,"&rpar;"],[0,"&ast;"],[0,"&plus;"],[0,"&comma;"],[1,"&period;"],[0,"&sol;"],[10,"&colon;"],[0,"&semi;"],[0,{v:"&lt;",n:8402,o:"&nvlt;"}],[0,{v:"&equals;",n:8421,o:"&bne;"}],[0,{v:"&gt;",n:8402,o:"&nvgt;"}],[0,"&quest;"],[0,"&commat;"],[26,"&lbrack;"],[0,"&bsol;"],[0,"&rbrack;"],[0,"&Hat;"],[0,"&lowbar;"],[0,"&DiacriticalGrave;"],[5,{n:106,o:"&fjlig;"}],[20,"&lbrace;"],[0,"&verbar;"],[0,"&rbrace;"],[34,"&nbsp;"],[0,"&iexcl;"],[0,"&cent;"],[0,"&pound;"],[0,"&curren;"],[0,"&yen;"],[0,"&brvbar;"],[0,"&sect;"],[0,"&die;"],[0,"&copy;"],[0,"&ordf;"],[0,"&laquo;"],[0,"&not;"],[0,"&shy;"],[0,"&circledR;"],[0,"&macr;"],[0,"&deg;"],[0,"&PlusMinus;"],[0,"&sup2;"],[0,"&sup3;"],[0,"&acute;"],[0,"&micro;"],[0,"&para;"],[0,"&centerdot;"],[0,"&cedil;"],[0,"&sup1;"],[0,"&ordm;"],[0,"&raquo;"],[0,"&frac14;"],[0,"&frac12;"],[0,"&frac34;"],[0,"&iquest;"],[0,"&Agrave;"],[0,"&Aacute;"],[0,"&Acirc;"],[0,"&Atilde;"],[0,"&Auml;"],[0,"&angst;"],[0,"&AElig;"],[0,"&Ccedil;"],[0,"&Egrave;"],[0,"&Eacute;"],[0,"&Ecirc;"],[0,"&Euml;"],[0,"&Igrave;"],[0,"&Iacute;"],[0,"&Icirc;"],[0,"&Iuml;"],[0,"&ETH;"],[0,"&Ntilde;"],[0,"&Ograve;"],[0,"&Oacute;"],[0,"&Ocirc;"],[0,"&Otilde;"],[0,"&Ouml;"],[0,"&times;"],[0,"&Oslash;"],[0,"&Ugrave;"],[0,"&Uacute;"],[0,"&Ucirc;"],[0,"&Uuml;"],[0,"&Yacute;"],[0,"&THORN;"],[0,"&szlig;"],[0,"&agrave;"],[0,"&aacute;"],[0,"&acirc;"],[0,"&atilde;"],[0,"&auml;"],[0,"&aring;"],[0,"&aelig;"],[0,"&ccedil;"],[0,"&egrave;"],[0,"&eacute;"],[0,"&ecirc;"],[0,"&euml;"],[0,"&igrave;"],[0,"&iacute;"],[0,"&icirc;"],[0,"&iuml;"],[0,"&eth;"],[0,"&ntilde;"],[0,"&ograve;"],[0,"&oacute;"],[0,"&ocirc;"],[0,"&otilde;"],[0,"&ouml;"],[0,"&div;"],[0,"&oslash;"],[0,"&ugrave;"],[0,"&uacute;"],[0,"&ucirc;"],[0,"&uuml;"],[0,"&yacute;"],[0,"&thorn;"],[0,"&yuml;"],[0,"&Amacr;"],[0,"&amacr;"],[0,"&Abreve;"],[0,"&abreve;"],[0,"&Aogon;"],[0,"&aogon;"],[0,"&Cacute;"],[0,"&cacute;"],[0,"&Ccirc;"],[0,"&ccirc;"],[0,"&Cdot;"],[0,"&cdot;"],[0,"&Ccaron;"],[0,"&ccaron;"],[0,"&Dcaron;"],[0,"&dcaron;"],[0,"&Dstrok;"],[0,"&dstrok;"],[0,"&Emacr;"],[0,"&emacr;"],[2,"&Edot;"],[0,"&edot;"],[0,"&Eogon;"],[0,"&eogon;"],[0,"&Ecaron;"],[0,"&ecaron;"],[0,"&Gcirc;"],[0,"&gcirc;"],[0,"&Gbreve;"],[0,"&gbreve;"],[0,"&Gdot;"],[0,"&gdot;"],[0,"&Gcedil;"],[1,"&Hcirc;"],[0,"&hcirc;"],[0,"&Hstrok;"],[0,"&hstrok;"],[0,"&Itilde;"],[0,"&itilde;"],[0,"&Imacr;"],[0,"&imacr;"],[2,"&Iogon;"],[0,"&iogon;"],[0,"&Idot;"],[0,"&imath;"],[0,"&IJlig;"],[0,"&ijlig;"],[0,"&Jcirc;"],[0,"&jcirc;"],[0,"&Kcedil;"],[0,"&kcedil;"],[0,"&kgreen;"],[0,"&Lacute;"],[0,"&lacute;"],[0,"&Lcedil;"],[0,"&lcedil;"],[0,"&Lcaron;"],[0,"&lcaron;"],[0,"&Lmidot;"],[0,"&lmidot;"],[0,"&Lstrok;"],[0,"&lstrok;"],[0,"&Nacute;"],[0,"&nacute;"],[0,"&Ncedil;"],[0,"&ncedil;"],[0,"&Ncaron;"],[0,"&ncaron;"],[0,"&napos;"],[0,"&ENG;"],[0,"&eng;"],[0,"&Omacr;"],[0,"&omacr;"],[2,"&Odblac;"],[0,"&odblac;"],[0,"&OElig;"],[0,"&oelig;"],[0,"&Racute;"],[0,"&racute;"],[0,"&Rcedil;"],[0,"&rcedil;"],[0,"&Rcaron;"],[0,"&rcaron;"],[0,"&Sacute;"],[0,"&sacute;"],[0,"&Scirc;"],[0,"&scirc;"],[0,"&Scedil;"],[0,"&scedil;"],[0,"&Scaron;"],[0,"&scaron;"],[0,"&Tcedil;"],[0,"&tcedil;"],[0,"&Tcaron;"],[0,"&tcaron;"],[0,"&Tstrok;"],[0,"&tstrok;"],[0,"&Utilde;"],[0,"&utilde;"],[0,"&Umacr;"],[0,"&umacr;"],[0,"&Ubreve;"],[0,"&ubreve;"],[0,"&Uring;"],[0,"&uring;"],[0,"&Udblac;"],[0,"&udblac;"],[0,"&Uogon;"],[0,"&uogon;"],[0,"&Wcirc;"],[0,"&wcirc;"],[0,"&Ycirc;"],[0,"&ycirc;"],[0,"&Yuml;"],[0,"&Zacute;"],[0,"&zacute;"],[0,"&Zdot;"],[0,"&zdot;"],[0,"&Zcaron;"],[0,"&zcaron;"],[19,"&fnof;"],[34,"&imped;"],[63,"&gacute;"],[65,"&jmath;"],[142,"&circ;"],[0,"&caron;"],[16,"&breve;"],[0,"&DiacriticalDot;"],[0,"&ring;"],[0,"&ogon;"],[0,"&DiacriticalTilde;"],[0,"&dblac;"],[51,"&DownBreve;"],[127,"&Alpha;"],[0,"&Beta;"],[0,"&Gamma;"],[0,"&Delta;"],[0,"&Epsilon;"],[0,"&Zeta;"],[0,"&Eta;"],[0,"&Theta;"],[0,"&Iota;"],[0,"&Kappa;"],[0,"&Lambda;"],[0,"&Mu;"],[0,"&Nu;"],[0,"&Xi;"],[0,"&Omicron;"],[0,"&Pi;"],[0,"&Rho;"],[1,"&Sigma;"],[0,"&Tau;"],[0,"&Upsilon;"],[0,"&Phi;"],[0,"&Chi;"],[0,"&Psi;"],[0,"&ohm;"],[7,"&alpha;"],[0,"&beta;"],[0,"&gamma;"],[0,"&delta;"],[0,"&epsi;"],[0,"&zeta;"],[0,"&eta;"],[0,"&theta;"],[0,"&iota;"],[0,"&kappa;"],[0,"&lambda;"],[0,"&mu;"],[0,"&nu;"],[0,"&xi;"],[0,"&omicron;"],[0,"&pi;"],[0,"&rho;"],[0,"&sigmaf;"],[0,"&sigma;"],[0,"&tau;"],[0,"&upsi;"],[0,"&phi;"],[0,"&chi;"],[0,"&psi;"],[0,"&omega;"],[7,"&thetasym;"],[0,"&Upsi;"],[2,"&phiv;"],[0,"&piv;"],[5,"&Gammad;"],[0,"&digamma;"],[18,"&kappav;"],[0,"&rhov;"],[3,"&epsiv;"],[0,"&backepsilon;"],[10,"&IOcy;"],[0,"&DJcy;"],[0,"&GJcy;"],[0,"&Jukcy;"],[0,"&DScy;"],[0,"&Iukcy;"],[0,"&YIcy;"],[0,"&Jsercy;"],[0,"&LJcy;"],[0,"&NJcy;"],[0,"&TSHcy;"],[0,"&KJcy;"],[1,"&Ubrcy;"],[0,"&DZcy;"],[0,"&Acy;"],[0,"&Bcy;"],[0,"&Vcy;"],[0,"&Gcy;"],[0,"&Dcy;"],[0,"&IEcy;"],[0,"&ZHcy;"],[0,"&Zcy;"],[0,"&Icy;"],[0,"&Jcy;"],[0,"&Kcy;"],[0,"&Lcy;"],[0,"&Mcy;"],[0,"&Ncy;"],[0,"&Ocy;"],[0,"&Pcy;"],[0,"&Rcy;"],[0,"&Scy;"],[0,"&Tcy;"],[0,"&Ucy;"],[0,"&Fcy;"],[0,"&KHcy;"],[0,"&TScy;"],[0,"&CHcy;"],[0,"&SHcy;"],[0,"&SHCHcy;"],[0,"&HARDcy;"],[0,"&Ycy;"],[0,"&SOFTcy;"],[0,"&Ecy;"],[0,"&YUcy;"],[0,"&YAcy;"],[0,"&acy;"],[0,"&bcy;"],[0,"&vcy;"],[0,"&gcy;"],[0,"&dcy;"],[0,"&iecy;"],[0,"&zhcy;"],[0,"&zcy;"],[0,"&icy;"],[0,"&jcy;"],[0,"&kcy;"],[0,"&lcy;"],[0,"&mcy;"],[0,"&ncy;"],[0,"&ocy;"],[0,"&pcy;"],[0,"&rcy;"],[0,"&scy;"],[0,"&tcy;"],[0,"&ucy;"],[0,"&fcy;"],[0,"&khcy;"],[0,"&tscy;"],[0,"&chcy;"],[0,"&shcy;"],[0,"&shchcy;"],[0,"&hardcy;"],[0,"&ycy;"],[0,"&softcy;"],[0,"&ecy;"],[0,"&yucy;"],[0,"&yacy;"],[1,"&iocy;"],[0,"&djcy;"],[0,"&gjcy;"],[0,"&jukcy;"],[0,"&dscy;"],[0,"&iukcy;"],[0,"&yicy;"],[0,"&jsercy;"],[0,"&ljcy;"],[0,"&njcy;"],[0,"&tshcy;"],[0,"&kjcy;"],[1,"&ubrcy;"],[0,"&dzcy;"],[7074,"&ensp;"],[0,"&emsp;"],[0,"&emsp13;"],[0,"&emsp14;"],[1,"&numsp;"],[0,"&puncsp;"],[0,"&ThinSpace;"],[0,"&hairsp;"],[0,"&NegativeMediumSpace;"],[0,"&zwnj;"],[0,"&zwj;"],[0,"&lrm;"],[0,"&rlm;"],[0,"&dash;"],[2,"&ndash;"],[0,"&mdash;"],[0,"&horbar;"],[0,"&Verbar;"],[1,"&lsquo;"],[0,"&CloseCurlyQuote;"],[0,"&lsquor;"],[1,"&ldquo;"],[0,"&CloseCurlyDoubleQuote;"],[0,"&bdquo;"],[1,"&dagger;"],[0,"&Dagger;"],[0,"&bull;"],[2,"&nldr;"],[0,"&hellip;"],[9,"&permil;"],[0,"&pertenk;"],[0,"&prime;"],[0,"&Prime;"],[0,"&tprime;"],[0,"&backprime;"],[3,"&lsaquo;"],[0,"&rsaquo;"],[3,"&oline;"],[2,"&caret;"],[1,"&hybull;"],[0,"&frasl;"],[10,"&bsemi;"],[7,"&qprime;"],[7,{v:"&MediumSpace;",n:8202,o:"&ThickSpace;"}],[0,"&NoBreak;"],[0,"&af;"],[0,"&InvisibleTimes;"],[0,"&ic;"],[72,"&euro;"],[46,"&tdot;"],[0,"&DotDot;"],[37,"&complexes;"],[2,"&incare;"],[4,"&gscr;"],[0,"&hamilt;"],[0,"&Hfr;"],[0,"&Hopf;"],[0,"&planckh;"],[0,"&hbar;"],[0,"&imagline;"],[0,"&Ifr;"],[0,"&lagran;"],[0,"&ell;"],[1,"&naturals;"],[0,"&numero;"],[0,"&copysr;"],[0,"&weierp;"],[0,"&Popf;"],[0,"&Qopf;"],[0,"&realine;"],[0,"&real;"],[0,"&reals;"],[0,"&rx;"],[3,"&trade;"],[1,"&integers;"],[2,"&mho;"],[0,"&zeetrf;"],[0,"&iiota;"],[2,"&bernou;"],[0,"&Cayleys;"],[1,"&escr;"],[0,"&Escr;"],[0,"&Fouriertrf;"],[1,"&Mellintrf;"],[0,"&order;"],[0,"&alefsym;"],[0,"&beth;"],[0,"&gimel;"],[0,"&daleth;"],[12,"&CapitalDifferentialD;"],[0,"&dd;"],[0,"&ee;"],[0,"&ii;"],[10,"&frac13;"],[0,"&frac23;"],[0,"&frac15;"],[0,"&frac25;"],[0,"&frac35;"],[0,"&frac45;"],[0,"&frac16;"],[0,"&frac56;"],[0,"&frac18;"],[0,"&frac38;"],[0,"&frac58;"],[0,"&frac78;"],[49,"&larr;"],[0,"&ShortUpArrow;"],[0,"&rarr;"],[0,"&darr;"],[0,"&harr;"],[0,"&updownarrow;"],[0,"&nwarr;"],[0,"&nearr;"],[0,"&LowerRightArrow;"],[0,"&LowerLeftArrow;"],[0,"&nlarr;"],[0,"&nrarr;"],[1,{v:"&rarrw;",n:824,o:"&nrarrw;"}],[0,"&Larr;"],[0,"&Uarr;"],[0,"&Rarr;"],[0,"&Darr;"],[0,"&larrtl;"],[0,"&rarrtl;"],[0,"&LeftTeeArrow;"],[0,"&mapstoup;"],[0,"&map;"],[0,"&DownTeeArrow;"],[1,"&hookleftarrow;"],[0,"&hookrightarrow;"],[0,"&larrlp;"],[0,"&looparrowright;"],[0,"&harrw;"],[0,"&nharr;"],[1,"&lsh;"],[0,"&rsh;"],[0,"&ldsh;"],[0,"&rdsh;"],[1,"&crarr;"],[0,"&cularr;"],[0,"&curarr;"],[2,"&circlearrowleft;"],[0,"&circlearrowright;"],[0,"&leftharpoonup;"],[0,"&DownLeftVector;"],[0,"&RightUpVector;"],[0,"&LeftUpVector;"],[0,"&rharu;"],[0,"&DownRightVector;"],[0,"&dharr;"],[0,"&dharl;"],[0,"&RightArrowLeftArrow;"],[0,"&udarr;"],[0,"&LeftArrowRightArrow;"],[0,"&leftleftarrows;"],[0,"&upuparrows;"],[0,"&rightrightarrows;"],[0,"&ddarr;"],[0,"&leftrightharpoons;"],[0,"&Equilibrium;"],[0,"&nlArr;"],[0,"&nhArr;"],[0,"&nrArr;"],[0,"&DoubleLeftArrow;"],[0,"&DoubleUpArrow;"],[0,"&DoubleRightArrow;"],[0,"&dArr;"],[0,"&DoubleLeftRightArrow;"],[0,"&DoubleUpDownArrow;"],[0,"&nwArr;"],[0,"&neArr;"],[0,"&seArr;"],[0,"&swArr;"],[0,"&lAarr;"],[0,"&rAarr;"],[1,"&zigrarr;"],[6,"&larrb;"],[0,"&rarrb;"],[15,"&DownArrowUpArrow;"],[7,"&loarr;"],[0,"&roarr;"],[0,"&hoarr;"],[0,"&forall;"],[0,"&comp;"],[0,{v:"&part;",n:824,o:"&npart;"}],[0,"&exist;"],[0,"&nexist;"],[0,"&empty;"],[1,"&Del;"],[0,"&Element;"],[0,"&NotElement;"],[1,"&ni;"],[0,"&notni;"],[2,"&prod;"],[0,"&coprod;"],[0,"&sum;"],[0,"&minus;"],[0,"&MinusPlus;"],[0,"&dotplus;"],[1,"&Backslash;"],[0,"&lowast;"],[0,"&compfn;"],[1,"&radic;"],[2,"&prop;"],[0,"&infin;"],[0,"&angrt;"],[0,{v:"&ang;",n:8402,o:"&nang;"}],[0,"&angmsd;"],[0,"&angsph;"],[0,"&mid;"],[0,"&nmid;"],[0,"&DoubleVerticalBar;"],[0,"&NotDoubleVerticalBar;"],[0,"&and;"],[0,"&or;"],[0,{v:"&cap;",n:65024,o:"&caps;"}],[0,{v:"&cup;",n:65024,o:"&cups;"}],[0,"&int;"],[0,"&Int;"],[0,"&iiint;"],[0,"&conint;"],[0,"&Conint;"],[0,"&Cconint;"],[0,"&cwint;"],[0,"&ClockwiseContourIntegral;"],[0,"&awconint;"],[0,"&there4;"],[0,"&becaus;"],[0,"&ratio;"],[0,"&Colon;"],[0,"&dotminus;"],[1,"&mDDot;"],[0,"&homtht;"],[0,{v:"&sim;",n:8402,o:"&nvsim;"}],[0,{v:"&backsim;",n:817,o:"&race;"}],[0,{v:"&ac;",n:819,o:"&acE;"}],[0,"&acd;"],[0,"&VerticalTilde;"],[0,"&NotTilde;"],[0,{v:"&eqsim;",n:824,o:"&nesim;"}],[0,"&sime;"],[0,"&NotTildeEqual;"],[0,"&cong;"],[0,"&simne;"],[0,"&ncong;"],[0,"&ap;"],[0,"&nap;"],[0,"&ape;"],[0,{v:"&apid;",n:824,o:"&napid;"}],[0,"&backcong;"],[0,{v:"&asympeq;",n:8402,o:"&nvap;"}],[0,{v:"&bump;",n:824,o:"&nbump;"}],[0,{v:"&bumpe;",n:824,o:"&nbumpe;"}],[0,{v:"&doteq;",n:824,o:"&nedot;"}],[0,"&doteqdot;"],[0,"&efDot;"],[0,"&erDot;"],[0,"&Assign;"],[0,"&ecolon;"],[0,"&ecir;"],[0,"&circeq;"],[1,"&wedgeq;"],[0,"&veeeq;"],[1,"&triangleq;"],[2,"&equest;"],[0,"&ne;"],[0,{v:"&Congruent;",n:8421,o:"&bnequiv;"}],[0,"&nequiv;"],[1,{v:"&le;",n:8402,o:"&nvle;"}],[0,{v:"&ge;",n:8402,o:"&nvge;"}],[0,{v:"&lE;",n:824,o:"&nlE;"}],[0,{v:"&gE;",n:824,o:"&ngE;"}],[0,{v:"&lnE;",n:65024,o:"&lvertneqq;"}],[0,{v:"&gnE;",n:65024,o:"&gvertneqq;"}],[0,{v:"&ll;",n:new Map(e([[824,"&nLtv;"],[7577,"&nLt;"]]))}],[0,{v:"&gg;",n:new Map(e([[824,"&nGtv;"],[7577,"&nGt;"]]))}],[0,"&between;"],[0,"&NotCupCap;"],[0,"&nless;"],[0,"&ngt;"],[0,"&nle;"],[0,"&nge;"],[0,"&lesssim;"],[0,"&GreaterTilde;"],[0,"&nlsim;"],[0,"&ngsim;"],[0,"&LessGreater;"],[0,"&gl;"],[0,"&NotLessGreater;"],[0,"&NotGreaterLess;"],[0,"&pr;"],[0,"&sc;"],[0,"&prcue;"],[0,"&sccue;"],[0,"&PrecedesTilde;"],[0,{v:"&scsim;",n:824,o:"&NotSucceedsTilde;"}],[0,"&NotPrecedes;"],[0,"&NotSucceeds;"],[0,{v:"&sub;",n:8402,o:"&NotSubset;"}],[0,{v:"&sup;",n:8402,o:"&NotSuperset;"}],[0,"&nsub;"],[0,"&nsup;"],[0,"&sube;"],[0,"&supe;"],[0,"&NotSubsetEqual;"],[0,"&NotSupersetEqual;"],[0,{v:"&subne;",n:65024,o:"&varsubsetneq;"}],[0,{v:"&supne;",n:65024,o:"&varsupsetneq;"}],[1,"&cupdot;"],[0,"&UnionPlus;"],[0,{v:"&sqsub;",n:824,o:"&NotSquareSubset;"}],[0,{v:"&sqsup;",n:824,o:"&NotSquareSuperset;"}],[0,"&sqsube;"],[0,"&sqsupe;"],[0,{v:"&sqcap;",n:65024,o:"&sqcaps;"}],[0,{v:"&sqcup;",n:65024,o:"&sqcups;"}],[0,"&CirclePlus;"],[0,"&CircleMinus;"],[0,"&CircleTimes;"],[0,"&osol;"],[0,"&CircleDot;"],[0,"&circledcirc;"],[0,"&circledast;"],[1,"&circleddash;"],[0,"&boxplus;"],[0,"&boxminus;"],[0,"&boxtimes;"],[0,"&dotsquare;"],[0,"&RightTee;"],[0,"&dashv;"],[0,"&DownTee;"],[0,"&bot;"],[1,"&models;"],[0,"&DoubleRightTee;"],[0,"&Vdash;"],[0,"&Vvdash;"],[0,"&VDash;"],[0,"&nvdash;"],[0,"&nvDash;"],[0,"&nVdash;"],[0,"&nVDash;"],[0,"&prurel;"],[1,"&LeftTriangle;"],[0,"&RightTriangle;"],[0,{v:"&LeftTriangleEqual;",n:8402,o:"&nvltrie;"}],[0,{v:"&RightTriangleEqual;",n:8402,o:"&nvrtrie;"}],[0,"&origof;"],[0,"&imof;"],[0,"&multimap;"],[0,"&hercon;"],[0,"&intcal;"],[0,"&veebar;"],[1,"&barvee;"],[0,"&angrtvb;"],[0,"&lrtri;"],[0,"&bigwedge;"],[0,"&bigvee;"],[0,"&bigcap;"],[0,"&bigcup;"],[0,"&diam;"],[0,"&sdot;"],[0,"&sstarf;"],[0,"&divideontimes;"],[0,"&bowtie;"],[0,"&ltimes;"],[0,"&rtimes;"],[0,"&leftthreetimes;"],[0,"&rightthreetimes;"],[0,"&backsimeq;"],[0,"&curlyvee;"],[0,"&curlywedge;"],[0,"&Sub;"],[0,"&Sup;"],[0,"&Cap;"],[0,"&Cup;"],[0,"&fork;"],[0,"&epar;"],[0,"&lessdot;"],[0,"&gtdot;"],[0,{v:"&Ll;",n:824,o:"&nLl;"}],[0,{v:"&Gg;",n:824,o:"&nGg;"}],[0,{v:"&leg;",n:65024,o:"&lesg;"}],[0,{v:"&gel;",n:65024,o:"&gesl;"}],[2,"&cuepr;"],[0,"&cuesc;"],[0,"&NotPrecedesSlantEqual;"],[0,"&NotSucceedsSlantEqual;"],[0,"&NotSquareSubsetEqual;"],[0,"&NotSquareSupersetEqual;"],[2,"&lnsim;"],[0,"&gnsim;"],[0,"&precnsim;"],[0,"&scnsim;"],[0,"&nltri;"],[0,"&NotRightTriangle;"],[0,"&nltrie;"],[0,"&NotRightTriangleEqual;"],[0,"&vellip;"],[0,"&ctdot;"],[0,"&utdot;"],[0,"&dtdot;"],[0,"&disin;"],[0,"&isinsv;"],[0,"&isins;"],[0,{v:"&isindot;",n:824,o:"&notindot;"}],[0,"&notinvc;"],[0,"&notinvb;"],[1,{v:"&isinE;",n:824,o:"&notinE;"}],[0,"&nisd;"],[0,"&xnis;"],[0,"&nis;"],[0,"&notnivc;"],[0,"&notnivb;"],[6,"&barwed;"],[0,"&Barwed;"],[1,"&lceil;"],[0,"&rceil;"],[0,"&LeftFloor;"],[0,"&rfloor;"],[0,"&drcrop;"],[0,"&dlcrop;"],[0,"&urcrop;"],[0,"&ulcrop;"],[0,"&bnot;"],[1,"&profline;"],[0,"&profsurf;"],[1,"&telrec;"],[0,"&target;"],[5,"&ulcorn;"],[0,"&urcorn;"],[0,"&dlcorn;"],[0,"&drcorn;"],[2,"&frown;"],[0,"&smile;"],[9,"&cylcty;"],[0,"&profalar;"],[7,"&topbot;"],[6,"&ovbar;"],[1,"&solbar;"],[60,"&angzarr;"],[51,"&lmoustache;"],[0,"&rmoustache;"],[2,"&OverBracket;"],[0,"&bbrk;"],[0,"&bbrktbrk;"],[37,"&OverParenthesis;"],[0,"&UnderParenthesis;"],[0,"&OverBrace;"],[0,"&UnderBrace;"],[2,"&trpezium;"],[4,"&elinters;"],[59,"&blank;"],[164,"&circledS;"],[55,"&boxh;"],[1,"&boxv;"],[9,"&boxdr;"],[3,"&boxdl;"],[3,"&boxur;"],[3,"&boxul;"],[3,"&boxvr;"],[7,"&boxvl;"],[7,"&boxhd;"],[7,"&boxhu;"],[7,"&boxvh;"],[19,"&boxH;"],[0,"&boxV;"],[0,"&boxdR;"],[0,"&boxDr;"],[0,"&boxDR;"],[0,"&boxdL;"],[0,"&boxDl;"],[0,"&boxDL;"],[0,"&boxuR;"],[0,"&boxUr;"],[0,"&boxUR;"],[0,"&boxuL;"],[0,"&boxUl;"],[0,"&boxUL;"],[0,"&boxvR;"],[0,"&boxVr;"],[0,"&boxVR;"],[0,"&boxvL;"],[0,"&boxVl;"],[0,"&boxVL;"],[0,"&boxHd;"],[0,"&boxhD;"],[0,"&boxHD;"],[0,"&boxHu;"],[0,"&boxhU;"],[0,"&boxHU;"],[0,"&boxvH;"],[0,"&boxVh;"],[0,"&boxVH;"],[19,"&uhblk;"],[3,"&lhblk;"],[3,"&block;"],[8,"&blk14;"],[0,"&blk12;"],[0,"&blk34;"],[13,"&square;"],[8,"&blacksquare;"],[0,"&EmptyVerySmallSquare;"],[1,"&rect;"],[0,"&marker;"],[2,"&fltns;"],[1,"&bigtriangleup;"],[0,"&blacktriangle;"],[0,"&triangle;"],[2,"&blacktriangleright;"],[0,"&rtri;"],[3,"&bigtriangledown;"],[0,"&blacktriangledown;"],[0,"&dtri;"],[2,"&blacktriangleleft;"],[0,"&ltri;"],[6,"&loz;"],[0,"&cir;"],[32,"&tridot;"],[2,"&bigcirc;"],[8,"&ultri;"],[0,"&urtri;"],[0,"&lltri;"],[0,"&EmptySmallSquare;"],[0,"&FilledSmallSquare;"],[8,"&bigstar;"],[0,"&star;"],[7,"&phone;"],[49,"&female;"],[1,"&male;"],[29,"&spades;"],[2,"&clubs;"],[1,"&hearts;"],[0,"&diamondsuit;"],[3,"&sung;"],[2,"&flat;"],[0,"&natural;"],[0,"&sharp;"],[163,"&check;"],[3,"&cross;"],[8,"&malt;"],[21,"&sext;"],[33,"&VerticalSeparator;"],[25,"&lbbrk;"],[0,"&rbbrk;"],[84,"&bsolhsub;"],[0,"&suphsol;"],[28,"&LeftDoubleBracket;"],[0,"&RightDoubleBracket;"],[0,"&lang;"],[0,"&rang;"],[0,"&Lang;"],[0,"&Rang;"],[0,"&loang;"],[0,"&roang;"],[7,"&longleftarrow;"],[0,"&longrightarrow;"],[0,"&longleftrightarrow;"],[0,"&DoubleLongLeftArrow;"],[0,"&DoubleLongRightArrow;"],[0,"&DoubleLongLeftRightArrow;"],[1,"&longmapsto;"],[2,"&dzigrarr;"],[258,"&nvlArr;"],[0,"&nvrArr;"],[0,"&nvHarr;"],[0,"&Map;"],[6,"&lbarr;"],[0,"&bkarow;"],[0,"&lBarr;"],[0,"&dbkarow;"],[0,"&drbkarow;"],[0,"&DDotrahd;"],[0,"&UpArrowBar;"],[0,"&DownArrowBar;"],[2,"&Rarrtl;"],[2,"&latail;"],[0,"&ratail;"],[0,"&lAtail;"],[0,"&rAtail;"],[0,"&larrfs;"],[0,"&rarrfs;"],[0,"&larrbfs;"],[0,"&rarrbfs;"],[2,"&nwarhk;"],[0,"&nearhk;"],[0,"&hksearow;"],[0,"&hkswarow;"],[0,"&nwnear;"],[0,"&nesear;"],[0,"&seswar;"],[0,"&swnwar;"],[8,{v:"&rarrc;",n:824,o:"&nrarrc;"}],[1,"&cudarrr;"],[0,"&ldca;"],[0,"&rdca;"],[0,"&cudarrl;"],[0,"&larrpl;"],[2,"&curarrm;"],[0,"&cularrp;"],[7,"&rarrpl;"],[2,"&harrcir;"],[0,"&Uarrocir;"],[0,"&lurdshar;"],[0,"&ldrushar;"],[2,"&LeftRightVector;"],[0,"&RightUpDownVector;"],[0,"&DownLeftRightVector;"],[0,"&LeftUpDownVector;"],[0,"&LeftVectorBar;"],[0,"&RightVectorBar;"],[0,"&RightUpVectorBar;"],[0,"&RightDownVectorBar;"],[0,"&DownLeftVectorBar;"],[0,"&DownRightVectorBar;"],[0,"&LeftUpVectorBar;"],[0,"&LeftDownVectorBar;"],[0,"&LeftTeeVector;"],[0,"&RightTeeVector;"],[0,"&RightUpTeeVector;"],[0,"&RightDownTeeVector;"],[0,"&DownLeftTeeVector;"],[0,"&DownRightTeeVector;"],[0,"&LeftUpTeeVector;"],[0,"&LeftDownTeeVector;"],[0,"&lHar;"],[0,"&uHar;"],[0,"&rHar;"],[0,"&dHar;"],[0,"&luruhar;"],[0,"&ldrdhar;"],[0,"&ruluhar;"],[0,"&rdldhar;"],[0,"&lharul;"],[0,"&llhard;"],[0,"&rharul;"],[0,"&lrhard;"],[0,"&udhar;"],[0,"&duhar;"],[0,"&RoundImplies;"],[0,"&erarr;"],[0,"&simrarr;"],[0,"&larrsim;"],[0,"&rarrsim;"],[0,"&rarrap;"],[0,"&ltlarr;"],[1,"&gtrarr;"],[0,"&subrarr;"],[1,"&suplarr;"],[0,"&lfisht;"],[0,"&rfisht;"],[0,"&ufisht;"],[0,"&dfisht;"],[5,"&lopar;"],[0,"&ropar;"],[4,"&lbrke;"],[0,"&rbrke;"],[0,"&lbrkslu;"],[0,"&rbrksld;"],[0,"&lbrksld;"],[0,"&rbrkslu;"],[0,"&langd;"],[0,"&rangd;"],[0,"&lparlt;"],[0,"&rpargt;"],[0,"&gtlPar;"],[0,"&ltrPar;"],[3,"&vzigzag;"],[1,"&vangrt;"],[0,"&angrtvbd;"],[6,"&ange;"],[0,"&range;"],[0,"&dwangle;"],[0,"&uwangle;"],[0,"&angmsdaa;"],[0,"&angmsdab;"],[0,"&angmsdac;"],[0,"&angmsdad;"],[0,"&angmsdae;"],[0,"&angmsdaf;"],[0,"&angmsdag;"],[0,"&angmsdah;"],[0,"&bemptyv;"],[0,"&demptyv;"],[0,"&cemptyv;"],[0,"&raemptyv;"],[0,"&laemptyv;"],[0,"&ohbar;"],[0,"&omid;"],[0,"&opar;"],[1,"&operp;"],[1,"&olcross;"],[0,"&odsold;"],[1,"&olcir;"],[0,"&ofcir;"],[0,"&olt;"],[0,"&ogt;"],[0,"&cirscir;"],[0,"&cirE;"],[0,"&solb;"],[0,"&bsolb;"],[3,"&boxbox;"],[3,"&trisb;"],[0,"&rtriltri;"],[0,{v:"&LeftTriangleBar;",n:824,o:"&NotLeftTriangleBar;"}],[0,{v:"&RightTriangleBar;",n:824,o:"&NotRightTriangleBar;"}],[11,"&iinfin;"],[0,"&infintie;"],[0,"&nvinfin;"],[4,"&eparsl;"],[0,"&smeparsl;"],[0,"&eqvparsl;"],[5,"&blacklozenge;"],[8,"&RuleDelayed;"],[1,"&dsol;"],[9,"&bigodot;"],[0,"&bigoplus;"],[0,"&bigotimes;"],[1,"&biguplus;"],[1,"&bigsqcup;"],[5,"&iiiint;"],[0,"&fpartint;"],[2,"&cirfnint;"],[0,"&awint;"],[0,"&rppolint;"],[0,"&scpolint;"],[0,"&npolint;"],[0,"&pointint;"],[0,"&quatint;"],[0,"&intlarhk;"],[10,"&pluscir;"],[0,"&plusacir;"],[0,"&simplus;"],[0,"&plusdu;"],[0,"&plussim;"],[0,"&plustwo;"],[1,"&mcomma;"],[0,"&minusdu;"],[2,"&loplus;"],[0,"&roplus;"],[0,"&Cross;"],[0,"&timesd;"],[0,"&timesbar;"],[1,"&smashp;"],[0,"&lotimes;"],[0,"&rotimes;"],[0,"&otimesas;"],[0,"&Otimes;"],[0,"&odiv;"],[0,"&triplus;"],[0,"&triminus;"],[0,"&tritime;"],[0,"&intprod;"],[2,"&amalg;"],[0,"&capdot;"],[1,"&ncup;"],[0,"&ncap;"],[0,"&capand;"],[0,"&cupor;"],[0,"&cupcap;"],[0,"&capcup;"],[0,"&cupbrcap;"],[0,"&capbrcup;"],[0,"&cupcup;"],[0,"&capcap;"],[0,"&ccups;"],[0,"&ccaps;"],[2,"&ccupssm;"],[2,"&And;"],[0,"&Or;"],[0,"&andand;"],[0,"&oror;"],[0,"&orslope;"],[0,"&andslope;"],[1,"&andv;"],[0,"&orv;"],[0,"&andd;"],[0,"&ord;"],[1,"&wedbar;"],[6,"&sdote;"],[3,"&simdot;"],[2,{v:"&congdot;",n:824,o:"&ncongdot;"}],[0,"&easter;"],[0,"&apacir;"],[0,{v:"&apE;",n:824,o:"&napE;"}],[0,"&eplus;"],[0,"&pluse;"],[0,"&Esim;"],[0,"&Colone;"],[0,"&Equal;"],[1,"&ddotseq;"],[0,"&equivDD;"],[0,"&ltcir;"],[0,"&gtcir;"],[0,"&ltquest;"],[0,"&gtquest;"],[0,{v:"&leqslant;",n:824,o:"&nleqslant;"}],[0,{v:"&geqslant;",n:824,o:"&ngeqslant;"}],[0,"&lesdot;"],[0,"&gesdot;"],[0,"&lesdoto;"],[0,"&gesdoto;"],[0,"&lesdotor;"],[0,"&gesdotol;"],[0,"&lap;"],[0,"&gap;"],[0,"&lne;"],[0,"&gne;"],[0,"&lnap;"],[0,"&gnap;"],[0,"&lEg;"],[0,"&gEl;"],[0,"&lsime;"],[0,"&gsime;"],[0,"&lsimg;"],[0,"&gsiml;"],[0,"&lgE;"],[0,"&glE;"],[0,"&lesges;"],[0,"&gesles;"],[0,"&els;"],[0,"&egs;"],[0,"&elsdot;"],[0,"&egsdot;"],[0,"&el;"],[0,"&eg;"],[2,"&siml;"],[0,"&simg;"],[0,"&simlE;"],[0,"&simgE;"],[0,{v:"&LessLess;",n:824,o:"&NotNestedLessLess;"}],[0,{v:"&GreaterGreater;",n:824,o:"&NotNestedGreaterGreater;"}],[1,"&glj;"],[0,"&gla;"],[0,"&ltcc;"],[0,"&gtcc;"],[0,"&lescc;"],[0,"&gescc;"],[0,"&smt;"],[0,"&lat;"],[0,{v:"&smte;",n:65024,o:"&smtes;"}],[0,{v:"&late;",n:65024,o:"&lates;"}],[0,"&bumpE;"],[0,{v:"&PrecedesEqual;",n:824,o:"&NotPrecedesEqual;"}],[0,{v:"&sce;",n:824,o:"&NotSucceedsEqual;"}],[2,"&prE;"],[0,"&scE;"],[0,"&precneqq;"],[0,"&scnE;"],[0,"&prap;"],[0,"&scap;"],[0,"&precnapprox;"],[0,"&scnap;"],[0,"&Pr;"],[0,"&Sc;"],[0,"&subdot;"],[0,"&supdot;"],[0,"&subplus;"],[0,"&supplus;"],[0,"&submult;"],[0,"&supmult;"],[0,"&subedot;"],[0,"&supedot;"],[0,{v:"&subE;",n:824,o:"&nsubE;"}],[0,{v:"&supE;",n:824,o:"&nsupE;"}],[0,"&subsim;"],[0,"&supsim;"],[2,{v:"&subnE;",n:65024,o:"&varsubsetneqq;"}],[0,{v:"&supnE;",n:65024,o:"&varsupsetneqq;"}],[2,"&csub;"],[0,"&csup;"],[0,"&csube;"],[0,"&csupe;"],[0,"&subsup;"],[0,"&supsub;"],[0,"&subsub;"],[0,"&supsup;"],[0,"&suphsub;"],[0,"&supdsub;"],[0,"&forkv;"],[0,"&topfork;"],[0,"&mlcp;"],[8,"&Dashv;"],[1,"&Vdashl;"],[0,"&Barv;"],[0,"&vBar;"],[0,"&vBarv;"],[1,"&Vbar;"],[0,"&Not;"],[0,"&bNot;"],[0,"&rnmid;"],[0,"&cirmid;"],[0,"&midcir;"],[0,"&topcir;"],[0,"&nhpar;"],[0,"&parsim;"],[9,{v:"&parsl;",n:8421,o:"&nparsl;"}],[44343,{n:new Map(e([[56476,"&Ascr;"],[1,"&Cscr;"],[0,"&Dscr;"],[2,"&Gscr;"],[2,"&Jscr;"],[0,"&Kscr;"],[2,"&Nscr;"],[0,"&Oscr;"],[0,"&Pscr;"],[0,"&Qscr;"],[1,"&Sscr;"],[0,"&Tscr;"],[0,"&Uscr;"],[0,"&Vscr;"],[0,"&Wscr;"],[0,"&Xscr;"],[0,"&Yscr;"],[0,"&Zscr;"],[0,"&ascr;"],[0,"&bscr;"],[0,"&cscr;"],[0,"&dscr;"],[1,"&fscr;"],[1,"&hscr;"],[0,"&iscr;"],[0,"&jscr;"],[0,"&kscr;"],[0,"&lscr;"],[0,"&mscr;"],[0,"&nscr;"],[1,"&pscr;"],[0,"&qscr;"],[0,"&rscr;"],[0,"&sscr;"],[0,"&tscr;"],[0,"&uscr;"],[0,"&vscr;"],[0,"&wscr;"],[0,"&xscr;"],[0,"&yscr;"],[0,"&zscr;"],[52,"&Afr;"],[0,"&Bfr;"],[1,"&Dfr;"],[0,"&Efr;"],[0,"&Ffr;"],[0,"&Gfr;"],[2,"&Jfr;"],[0,"&Kfr;"],[0,"&Lfr;"],[0,"&Mfr;"],[0,"&Nfr;"],[0,"&Ofr;"],[0,"&Pfr;"],[0,"&Qfr;"],[1,"&Sfr;"],[0,"&Tfr;"],[0,"&Ufr;"],[0,"&Vfr;"],[0,"&Wfr;"],[0,"&Xfr;"],[0,"&Yfr;"],[1,"&afr;"],[0,"&bfr;"],[0,"&cfr;"],[0,"&dfr;"],[0,"&efr;"],[0,"&ffr;"],[0,"&gfr;"],[0,"&hfr;"],[0,"&ifr;"],[0,"&jfr;"],[0,"&kfr;"],[0,"&lfr;"],[0,"&mfr;"],[0,"&nfr;"],[0,"&ofr;"],[0,"&pfr;"],[0,"&qfr;"],[0,"&rfr;"],[0,"&sfr;"],[0,"&tfr;"],[0,"&ufr;"],[0,"&vfr;"],[0,"&wfr;"],[0,"&xfr;"],[0,"&yfr;"],[0,"&zfr;"],[0,"&Aopf;"],[0,"&Bopf;"],[1,"&Dopf;"],[0,"&Eopf;"],[0,"&Fopf;"],[0,"&Gopf;"],[1,"&Iopf;"],[0,"&Jopf;"],[0,"&Kopf;"],[0,"&Lopf;"],[0,"&Mopf;"],[1,"&Oopf;"],[3,"&Sopf;"],[0,"&Topf;"],[0,"&Uopf;"],[0,"&Vopf;"],[0,"&Wopf;"],[0,"&Xopf;"],[0,"&Yopf;"],[1,"&aopf;"],[0,"&bopf;"],[0,"&copf;"],[0,"&dopf;"],[0,"&eopf;"],[0,"&fopf;"],[0,"&gopf;"],[0,"&hopf;"],[0,"&iopf;"],[0,"&jopf;"],[0,"&kopf;"],[0,"&lopf;"],[0,"&mopf;"],[0,"&nopf;"],[0,"&oopf;"],[0,"&popf;"],[0,"&qopf;"],[0,"&ropf;"],[0,"&sopf;"],[0,"&topf;"],[0,"&uopf;"],[0,"&vopf;"],[0,"&wopf;"],[0,"&xopf;"],[0,"&yopf;"],[0,"&zopf;"]]))}],[8906,"&fflig;"],[0,"&filig;"],[0,"&fllig;"],[0,"&ffilig;"],[0,"&ffllig;"]]))},9405:(e,t,r)=>{"use strict";r.d(t,{Gz:()=>s.Gz});var n,a,s=r(3175);r(7187),r(6834);!function(e){e[e.XML=0]="XML",e[e.HTML=1]="HTML"}(n||(n={})),function(e){e[e.UTF8=0]="UTF8",e[e.ASCII=1]="ASCII",e[e.Extensive=2]="Extensive",e[e.Attribute=3]="Attribute",e[e.Text=4]="Text"}(a||(a={}))},7237:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});var n=r(724);function a(e){return Array.prototype.slice.call(arguments,1).forEach((function(t){t&&Object.keys(t).forEach((function(r){e[r]=t[r]}))})),e}function s(e){return Object.prototype.toString.call(e)}function i(e){return"[object Function]"===s(e)}function o(e){return e.replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}const c={fuzzyLink:!0,fuzzyEmail:!0,fuzzyIP:!1};const l={"http:":{validate:function(e,t,r){const n=e.slice(t);return r.re.http||(r.re.http=new RegExp("^\\/\\/"+r.re.src_auth+r.re.src_host_port_strict+r.re.src_path,"i")),r.re.http.test(n)?n.match(r.re.http)[0].length:0}},"https:":"http:","ftp:":"http:","//":{validate:function(e,t,r){const n=e.slice(t);return r.re.no_http||(r.re.no_http=new RegExp("^"+r.re.src_auth+"(?:localhost|(?:(?:"+r.re.src_domain+")\\.)+"+r.re.src_domain_root+")"+r.re.src_port+r.re.src_host_terminator+r.re.src_path,"i")),r.re.no_http.test(n)?t>=3&&":"===e[t-3]||t>=3&&"/"===e[t-3]?0:n.match(r.re.no_http)[0].length:0}},"mailto:":{validate:function(e,t,r){const n=e.slice(t);return r.re.mailto||(r.re.mailto=new RegExp("^"+r.re.src_email_name+"@"+r.re.src_host_strict,"i")),r.re.mailto.test(n)?n.match(r.re.mailto)[0].length:0}}},u="biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф".split("|");function d(e){const t=e.re=(0,n.A)(e.__opts__),r=e.__tlds__.slice();function a(e){return e.replace("%TLDS%",t.src_tlds)}e.onCompile(),e.__tlds_replaced__||r.push("a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]"),r.push(t.src_xn),t.src_tlds=r.join("|"),t.email_fuzzy=RegExp(a(t.tpl_email_fuzzy),"i"),t.link_fuzzy=RegExp(a(t.tpl_link_fuzzy),"i"),t.link_no_ip_fuzzy=RegExp(a(t.tpl_link_no_ip_fuzzy),"i"),t.host_fuzzy_test=RegExp(a(t.tpl_host_fuzzy_test),"i");const c=[];function l(e,t){throw new Error('(LinkifyIt) Invalid schema "'+e+'": '+t)}e.__compiled__={},Object.keys(e.__schemas__).forEach((function(t){const r=e.__schemas__[t];if(null===r)return;const n={validate:null,link:null};if(e.__compiled__[t]=n,"[object Object]"===s(r))return!function(e){return"[object RegExp]"===s(e)}(r.validate)?i(r.validate)?n.validate=r.validate:l(t,r):n.validate=function(e){return function(t,r){const n=t.slice(r);return e.test(n)?n.match(e)[0].length:0}}(r.validate),void(i(r.normalize)?n.normalize=r.normalize:r.normalize?l(t,r):n.normalize=function(e,t){t.normalize(e)});!function(e){return"[object String]"===s(e)}(r)?l(t,r):c.push(t)})),c.forEach((function(t){e.__compiled__[e.__schemas__[t]]&&(e.__compiled__[t].validate=e.__compiled__[e.__schemas__[t]].validate,e.__compiled__[t].normalize=e.__compiled__[e.__schemas__[t]].normalize)})),e.__compiled__[""]={validate:null,normalize:function(e,t){t.normalize(e)}};const u=Object.keys(e.__compiled__).filter((function(t){return t.length>0&&e.__compiled__[t]})).map(o).join("|");e.re.schema_test=RegExp("(^|(?!_)(?:[><｜]|"+t.src_ZPCc+"))("+u+")","i"),e.re.schema_search=RegExp("(^|(?!_)(?:[><｜]|"+t.src_ZPCc+"))("+u+")","ig"),e.re.schema_at_start=RegExp("^"+e.re.schema_search.source,"i"),e.re.pretest=RegExp("("+e.re.schema_test.source+")|("+e.re.host_fuzzy_test.source+")|@","i"),function(e){e.__index__=-1,e.__text_cache__=""}(e)}function p(e,t){const r=e.__index__,n=e.__last_index__,a=e.__text_cache__.slice(r,n);this.schema=e.__schema__.toLowerCase(),this.index=r+t,this.lastIndex=n+t,this.raw=a,this.text=a,this.url=a}function h(e,t){const r=new p(e,t);return e.__compiled__[r.schema].normalize(r,e),r}function m(e,t){if(!(this instanceof m))return new m(e,t);var r;t||(r=e,Object.keys(r||{}).reduce((function(e,t){return e||c.hasOwnProperty(t)}),!1)&&(t=e,e={})),this.__opts__=a({},c,t),this.__index__=-1,this.__last_index__=-1,this.__schema__="",this.__text_cache__="",this.__schemas__=a({},l,e),this.__compiled__={},this.__tlds__=u,this.__tlds_replaced__=!1,this.re={},d(this)}m.prototype.add=function(e,t){return this.__schemas__[e]=t,d(this),this},m.prototype.set=function(e){return this.__opts__=a(this.__opts__,e),this},m.prototype.test=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return!1;let t,r,n,a,s,i,o,c,l;if(this.re.schema_test.test(e))for(o=this.re.schema_search,o.lastIndex=0;null!==(t=o.exec(e));)if(a=this.testSchemaAt(e,t[2],o.lastIndex),a){this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+a;break}return this.__opts__.fuzzyLink&&this.__compiled__["http:"]&&(c=e.search(this.re.host_fuzzy_test),c>=0&&(this.__index__<0||c<this.__index__)&&null!==(r=e.match(this.__opts__.fuzzyIP?this.re.link_fuzzy:this.re.link_no_ip_fuzzy))&&(s=r.index+r[1].length,(this.__index__<0||s<this.__index__)&&(this.__schema__="",this.__index__=s,this.__last_index__=r.index+r[0].length))),this.__opts__.fuzzyEmail&&this.__compiled__["mailto:"]&&(l=e.indexOf("@"),l>=0&&null!==(n=e.match(this.re.email_fuzzy))&&(s=n.index+n[1].length,i=n.index+n[0].length,(this.__index__<0||s<this.__index__||s===this.__index__&&i>this.__last_index__)&&(this.__schema__="mailto:",this.__index__=s,this.__last_index__=i))),this.__index__>=0},m.prototype.pretest=function(e){return this.re.pretest.test(e)},m.prototype.testSchemaAt=function(e,t,r){return this.__compiled__[t.toLowerCase()]?this.__compiled__[t.toLowerCase()].validate(e,r,this):0},m.prototype.match=function(e){const t=[];let r=0;this.__index__>=0&&this.__text_cache__===e&&(t.push(h(this,r)),r=this.__last_index__);let n=r?e.slice(r):e;for(;this.test(n);)t.push(h(this,r)),n=n.slice(this.__last_index__),r+=this.__last_index__;return t.length?t:null},m.prototype.matchAtStart=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return null;const t=this.re.schema_at_start.exec(e);if(!t)return null;const r=this.testSchemaAt(e,t[2],t[0].length);return r?(this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+r,h(this,0)):null},m.prototype.tlds=function(e,t){return e=Array.isArray(e)?e:[e],t?(this.__tlds__=this.__tlds__.concat(e).sort().filter((function(e,t,r){return e!==r[t-1]})).reverse(),d(this),this):(this.__tlds__=e.slice(),this.__tlds_replaced__=!0,d(this),this)},m.prototype.normalize=function(e){e.schema||(e.url="http://"+e.url),"mailto:"!==e.schema||/^mailto:/i.test(e.url)||(e.url="mailto:"+e.url)},m.prototype.onCompile=function(){};const f=m},724:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(905),a=r(786),s=r(3506),i=r(804);function o(e){const t={};e=e||{},t.src_Any=n.A.source,t.src_Cc=a.A.source,t.src_Z=s.A.source,t.src_P=i.A.source,t.src_ZPCc=[t.src_Z,t.src_P,t.src_Cc].join("|"),t.src_ZCc=[t.src_Z,t.src_Cc].join("|");const r="[><｜]";return t.src_pseudo_letter="(?:(?![><｜]|"+t.src_ZPCc+")"+t.src_Any+")",t.src_ip4="(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)",t.src_auth="(?:(?:(?!"+t.src_ZCc+"|[@/\\[\\]()]).)+@)?",t.src_port="(?::(?:6(?:[0-4]\\d{3}|5(?:[0-4]\\d{2}|5(?:[0-2]\\d|3[0-5])))|[1-5]?\\d{1,4}))?",t.src_host_terminator="(?=$|[><｜]|"+t.src_ZPCc+")(?!"+(e["---"]?"-(?!--)|":"-|")+"_|:\\d|\\.-|\\.(?!$|"+t.src_ZPCc+"))",t.src_path="(?:[/?#](?:(?!"+t.src_ZCc+"|"+r+"|[()[\\]{}.,\"'?!\\-;]).|\\[(?:(?!"+t.src_ZCc+"|\\]).)*\\]|\\((?:(?!"+t.src_ZCc+"|[)]).)*\\)|\\{(?:(?!"+t.src_ZCc+'|[}]).)*\\}|\\"(?:(?!'+t.src_ZCc+'|["]).)+\\"|\\\'(?:(?!'+t.src_ZCc+"|[']).)+\\'|\\'(?="+t.src_pseudo_letter+"|[-])|\\.{2,}[a-zA-Z0-9%/&]|\\.(?!"+t.src_ZCc+"|[.]|$)|"+(e["---"]?"\\-(?!--(?:[^-]|$))(?:-*)|":"\\-+|")+",(?!"+t.src_ZCc+"|$)|;(?!"+t.src_ZCc+"|$)|\\!+(?!"+t.src_ZCc+"|[!]|$)|\\?(?!"+t.src_ZCc+"|[?]|$))+|\\/)?",t.src_email_name='[\\-;:&=\\+\\$,\\.a-zA-Z0-9_][\\-;:&=\\+\\$,\\"\\.a-zA-Z0-9_]*',t.src_xn="xn--[a-z0-9\\-]{1,59}",t.src_domain_root="(?:"+t.src_xn+"|"+t.src_pseudo_letter+"{1,63})",t.src_domain="(?:"+t.src_xn+"|(?:"+t.src_pseudo_letter+")|(?:"+t.src_pseudo_letter+"(?:-|"+t.src_pseudo_letter+"){0,61}"+t.src_pseudo_letter+"))",t.src_host="(?:(?:(?:(?:"+t.src_domain+")\\.)*"+t.src_domain+"))",t.tpl_host_fuzzy="(?:"+t.src_ip4+"|(?:(?:(?:"+t.src_domain+")\\.)+(?:%TLDS%)))",t.tpl_host_no_ip_fuzzy="(?:(?:(?:"+t.src_domain+")\\.)+(?:%TLDS%))",t.src_host_strict=t.src_host+t.src_host_terminator,t.tpl_host_fuzzy_strict=t.tpl_host_fuzzy+t.src_host_terminator,t.src_host_port_strict=t.src_host+t.src_port+t.src_host_terminator,t.tpl_host_port_fuzzy_strict=t.tpl_host_fuzzy+t.src_port+t.src_host_terminator,t.tpl_host_port_no_ip_fuzzy_strict=t.tpl_host_no_ip_fuzzy+t.src_port+t.src_host_terminator,t.tpl_host_fuzzy_test="localhost|www\\.|\\.\\d{1,3}\\.|(?:\\.(?:%TLDS%)(?:"+t.src_ZPCc+"|>|$))",t.tpl_email_fuzzy='(^|[><｜]|"|\\(|'+t.src_ZCc+")("+t.src_email_name+"@"+t.tpl_host_fuzzy_strict+")",t.tpl_link_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+t.src_ZPCc+"))((?![$+<=>^`|｜])"+t.tpl_host_port_fuzzy_strict+t.src_path+")",t.tpl_link_no_ip_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+t.src_ZPCc+"))((?![$+<=>^`|｜])"+t.tpl_host_port_no_ip_fuzzy_strict+t.src_path+")",t}},7158:(e,t,r)=>{"use strict";r.d(t,{A:()=>n.A});var n=r(6838)},4384:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"]},8105:(e,t,r)=>{"use strict";r.d(t,{l:()=>s,p:()=>i});const n="<[A-Za-z][A-Za-z0-9\\-]*(?:\\s+[a-zA-Z_:][a-zA-Z0-9:._-]*(?:\\s*=\\s*(?:[^\"'=<>`\\x00-\\x20]+|'[^']*'|\"[^\"]*\"))?)*\\s*\\/?>",a="<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>",s=new RegExp("^(?:"+n+"|"+a+"|\x3c!---?>|\x3c!--(?:[^-]|-[^-]|--[^>])*--\x3e|<[?][\\s\\S]*?[?]>|<![A-Za-z][^>]*>|<!\\[CDATA\\[[\\s\\S]*?\\]\\]>)"),i=new RegExp("^(?:"+n+"|"+a+")")},4439:(e,t,r)=>{"use strict";r.r(t),r.d(t,{arrayReplaceAt:()=>p,assign:()=>d,escapeHtml:()=>C,escapeRE:()=>x,fromCodePoint:()=>m,has:()=>u,isMdAsciiPunct:()=>P,isPunctChar:()=>F,isSpace:()=>S,isString:()=>c,isValidEntityCode:()=>h,isWhiteSpace:()=>D,lib:()=>L,normalizeReference:()=>R,unescapeAll:()=>y,unescapeMd:()=>k});var n=r(5315),a=r(804),s=r(7795),i=r(8407),o=r(9405);function c(e){return"[object String]"===function(e){return Object.prototype.toString.call(e)}(e)}const l=Object.prototype.hasOwnProperty;function u(e,t){return l.call(e,t)}function d(e){return Array.prototype.slice.call(arguments,1).forEach((function(t){if(t){if("object"!=typeof t)throw new TypeError(t+"must be object");Object.keys(t).forEach((function(r){e[r]=t[r]}))}})),e}function p(e,t,r){return[].concat(e.slice(0,t),r,e.slice(t+1))}function h(e){return!(e>=55296&&e<=57343)&&(!(e>=64976&&e<=65007)&&(!!(65535&~e&&65534!=(65535&e))&&(!(e>=0&&e<=8)&&(11!==e&&(!(e>=14&&e<=31)&&(!(e>=127&&e<=159)&&!(e>1114111)))))))}function m(e){if(e>65535){const t=55296+((e-=65536)>>10),r=56320+(1023&e);return String.fromCharCode(t,r)}return String.fromCharCode(e)}const f=/\\([!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~])/g,g=new RegExp(f.source+"|"+/&([a-z#][a-z0-9]{1,31});/gi.source,"gi"),_=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))$/i;function k(e){return e.indexOf("\\")<0?e:e.replace(f,"$1")}function y(e){return e.indexOf("\\")<0&&e.indexOf("&")<0?e:e.replace(g,(function(e,t,r){return t||function(e,t){if(35===t.charCodeAt(0)&&_.test(t)){const r="x"===t[1].toLowerCase()?parseInt(t.slice(2),16):parseInt(t.slice(1),10);return h(r)?m(r):e}const r=(0,o.Gz)(e);return r!==e?r:e}(e,r)}))}const v=/[&<>"]/,E=/[&<>"]/g,b={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function A(e){return b[e]}function C(e){return v.test(e)?e.replace(E,A):e}const w=/[.?*+^$[\]\\(){}|-]/g;function x(e){return e.replace(w,"\\$&")}function S(e){switch(e){case 9:case 32:return!0}return!1}function D(e){if(e>=8192&&e<=8202)return!0;switch(e){case 9:case 10:case 11:case 12:case 13:case 32:case 160:case 5760:case 8239:case 8287:case 12288:return!0}return!1}function F(e){return a.A.test(e)||s.A.test(e)}function P(e){switch(e){case 33:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:case 58:case 59:case 60:case 61:case 62:case 63:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 124:case 125:case 126:return!0;default:return!1}}function R(e){return e=e.trim().replace(/\s+/g," "),"Ṿ"==="ẞ".toLowerCase()&&(e=e.replace(/ẞ/g,"ß")),e.toLowerCase().toUpperCase()}const L={mdurl:n,ucmicro:i}},16:(e,t,r)=>{"use strict";r.r(t),r.d(t,{parseLinkDestination:()=>a.A,parseLinkLabel:()=>n.A,parseLinkTitle:()=>s.A});var n=r(5739),a=r(9945),s=r(8859)},9945:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(4439);function a(e,t,r){let a,s=t;const i={ok:!1,pos:0,str:""};if(60===e.charCodeAt(s)){for(s++;s<r;){if(a=e.charCodeAt(s),10===a)return i;if(60===a)return i;if(62===a)return i.pos=s+1,i.str=(0,n.unescapeAll)(e.slice(t+1,s)),i.ok=!0,i;92===a&&s+1<r?s+=2:s++}return i}let o=0;for(;s<r&&(a=e.charCodeAt(s),32!==a)&&!(a<32||127===a);)if(92===a&&s+1<r){if(32===e.charCodeAt(s+1))break;s+=2}else{if(40===a&&(o++,o>32))return i;if(41===a){if(0===o)break;o--}s++}return t===s||0!==o||(i.str=(0,n.unescapeAll)(e.slice(t,s)),i.pos=s,i.ok=!0),i}},5739:(e,t,r)=>{"use strict";function n(e,t,r){let n,a,s,i;const o=e.posMax,c=e.pos;for(e.pos=t+1,n=1;e.pos<o;){if(s=e.src.charCodeAt(e.pos),93===s&&(n--,0===n)){a=!0;break}if(i=e.pos,e.md.inline.skipToken(e),91===s)if(i===e.pos-1)n++;else if(r)return e.pos=c,-1}let l=-1;return a&&(l=e.pos),e.pos=c,l}r.d(t,{A:()=>n})},8859:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(4439);function a(e,t,r,a){let s,i=t;const o={ok:!1,can_continue:!1,pos:0,str:"",marker:0};if(a)o.str=a.str,o.marker=a.marker;else{if(i>=r)return o;let n=e.charCodeAt(i);if(34!==n&&39!==n&&40!==n)return o;t++,i++,40===n&&(n=41),o.marker=n}for(;i<r;){if(s=e.charCodeAt(i),s===o.marker)return o.pos=i+1,o.str+=(0,n.unescapeAll)(e.slice(t,i)),o.ok=!0,o;if(40===s&&41===o.marker)return o;92===s&&i+1<r&&i++,i++}return o.can_continue=!0,o.str+=(0,n.unescapeAll)(e.slice(t,i)),o}},6838:(e,t,r)=>{"use strict";r.d(t,{A:()=>A});var n=r(4439),a=r(16),s=r(3836),i=r(5439),o=r(737),c=r(2813),l=r(7237),u=r(5315),d=r(7072),p=r(9404),h=r(8079),m=r(6881);const f={default:p.A,zero:h.A,commonmark:m.A},g=/^(vbscript|javascript|file|data):/,_=/^data:image\/(gif|png|jpeg|webp);/;function k(e){const t=e.trim().toLowerCase();return!g.test(t)||_.test(t)}const y=["http:","https:","mailto:"];function v(e){const t=u.parse(e,!0);if(t.hostname&&(!t.protocol||y.indexOf(t.protocol)>=0))try{t.hostname=d.Ay.toASCII(t.hostname)}catch(e){}return u.encode(u.format(t))}function E(e){const t=u.parse(e,!0);if(t.hostname&&(!t.protocol||y.indexOf(t.protocol)>=0))try{t.hostname=d.Ay.toUnicode(t.hostname)}catch(e){}return u.decode(u.format(t),u.decode.defaultChars+"%")}function b(e,t){if(!(this instanceof b))return new b(e,t);t||n.isString(e)||(t=e||{},e="default"),this.inline=new c.A,this.block=new o.A,this.core=new i.A,this.renderer=new s.A,this.linkify=new l.A,this.validateLink=k,this.normalizeLink=v,this.normalizeLinkText=E,this.utils=n,this.helpers=n.assign({},a),this.options={},this.configure(e),t&&this.set(t)}b.prototype.set=function(e){return n.assign(this.options,e),this},b.prototype.configure=function(e){const t=this;if(n.isString(e)){const t=e;if(!(e=f[t]))throw new Error('Wrong `markdown-it` preset "'+t+'", check name')}if(!e)throw new Error("Wrong `markdown-it` preset, can't be empty");return e.options&&t.set(e.options),e.components&&Object.keys(e.components).forEach((function(r){e.components[r].rules&&t[r].ruler.enableOnly(e.components[r].rules),e.components[r].rules2&&t[r].ruler2.enableOnly(e.components[r].rules2)})),this},b.prototype.enable=function(e,t){let r=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach((function(t){r=r.concat(this[t].ruler.enable(e,!0))}),this),r=r.concat(this.inline.ruler2.enable(e,!0));const n=e.filter((function(e){return r.indexOf(e)<0}));if(n.length&&!t)throw new Error("MarkdownIt. Failed to enable unknown rule(s): "+n);return this},b.prototype.disable=function(e,t){let r=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach((function(t){r=r.concat(this[t].ruler.disable(e,!0))}),this),r=r.concat(this.inline.ruler2.disable(e,!0));const n=e.filter((function(e){return r.indexOf(e)<0}));if(n.length&&!t)throw new Error("MarkdownIt. Failed to disable unknown rule(s): "+n);return this},b.prototype.use=function(e){const t=[this].concat(Array.prototype.slice.call(arguments,1));return e.apply(e,t),this},b.prototype.parse=function(e,t){if("string"!=typeof e)throw new Error("Input data should be a String");const r=new this.core.State(e,this,t);return this.core.process(r),r.tokens},b.prototype.render=function(e,t){return t=t||{},this.renderer.render(this.parse(e,t),this.options,t)},b.prototype.parseInline=function(e,t){const r=new this.core.State(e,this,t);return r.inlineMode=!0,this.core.process(r),r.tokens},b.prototype.renderInline=function(e,t){return t=t||{},this.renderer.render(this.parseInline(e,t),this.options,t)};const A=b},737:(e,t,r)=>{"use strict";r.d(t,{A:()=>k});var n=r(9358),a=r(2961),s=r(712),i=r(7103),o=r(6283),c=r(7537),l=r(9062),u=r(5792),d=r(3863),p=r(2577),h=r(3318),m=r(4206),f=r(4970);const g=[["table",s.A,["paragraph","reference"]],["code",i.A],["fence",o.A,["paragraph","reference","blockquote","list"]],["blockquote",c.A,["paragraph","reference","blockquote","list"]],["hr",l.A,["paragraph","reference","blockquote","list"]],["list",u.A,["paragraph","reference","blockquote"]],["reference",d.A],["html_block",p.A,["paragraph","reference","blockquote"]],["heading",h.A,["paragraph","reference","blockquote"]],["lheading",m.A],["paragraph",f.A]];function _(){this.ruler=new n.A;for(let e=0;e<g.length;e++)this.ruler.push(g[e][0],g[e][1],{alt:(g[e][2]||[]).slice()})}_.prototype.tokenize=function(e,t,r){const n=this.ruler.getRules(""),a=n.length,s=e.md.options.maxNesting;let i=t,o=!1;for(;i<r&&(e.line=i=e.skipEmptyLines(i),!(i>=r))&&!(e.sCount[i]<e.blkIndent);){if(e.level>=s){e.line=r;break}const t=e.line;let c=!1;for(let s=0;s<a;s++)if(c=n[s](e,i,r,!1),c){if(t>=e.line)throw new Error("block rule didn't increment state.line");break}if(!c)throw new Error("none of the block rules matched");e.tight=!o,e.isEmpty(e.line-1)&&(o=!0),i=e.line,i<r&&e.isEmpty(i)&&(o=!0,i++,e.line=i)}},_.prototype.parse=function(e,t,r,n){if(!e)return;const a=new this.State(e,t,r,n);this.tokenize(a,a.line,a.lineMax)},_.prototype.State=a.A;const k=_},5439:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var n=r(9358),a=r(7569),s=r(2699),i=r(9417),o=r(5189),c=r(6084),l=r(8541),u=r(5834),d=r(1242);const p=[["normalize",s.A],["block",i.A],["inline",o.A],["linkify",c.A],["replacements",l.A],["smartquotes",u.A],["text_join",d.A]];function h(){this.ruler=new n.A;for(let e=0;e<p.length;e++)this.ruler.push(p[e][0],p[e][1])}h.prototype.process=function(e){const t=this.ruler.getRules("");for(let r=0,n=t.length;r<n;r++)t[r](e)},h.prototype.State=a.A;const m=h},2813:(e,t,r)=>{"use strict";r.d(t,{A:()=>b});var n=r(9358),a=r(57),s=r(8597),i=r(4938),o=r(4076),c=r(7785),l=r(8797),u=r(925),d=r(2206),p=r(2128),h=r(4427),m=r(5469),f=r(9486),g=r(1517),_=r(6014),k=r(322);const y=[["text",s.A],["linkify",i.A],["newline",o.A],["escape",c.A],["backticks",l.A],["strikethrough",u.A.tokenize],["emphasis",d.A.tokenize],["link",p.A],["image",h.A],["autolink",m.A],["html_inline",f.A],["entity",g.A]],v=[["balance_pairs",_.A],["strikethrough",u.A.postProcess],["emphasis",d.A.postProcess],["fragments_join",k.A]];function E(){this.ruler=new n.A;for(let e=0;e<y.length;e++)this.ruler.push(y[e][0],y[e][1]);this.ruler2=new n.A;for(let e=0;e<v.length;e++)this.ruler2.push(v[e][0],v[e][1])}E.prototype.skipToken=function(e){const t=e.pos,r=this.ruler.getRules(""),n=r.length,a=e.md.options.maxNesting,s=e.cache;if(void 0!==s[t])return void(e.pos=s[t]);let i=!1;if(e.level<a){for(let a=0;a<n;a++)if(e.level++,i=r[a](e,!0),e.level--,i){if(t>=e.pos)throw new Error("inline rule didn't increment state.pos");break}}else e.pos=e.posMax;i||e.pos++,s[t]=e.pos},E.prototype.tokenize=function(e){const t=this.ruler.getRules(""),r=t.length,n=e.posMax,a=e.md.options.maxNesting;for(;e.pos<n;){const s=e.pos;let i=!1;if(e.level<a)for(let n=0;n<r;n++)if(i=t[n](e,!1),i){if(s>=e.pos)throw new Error("inline rule didn't increment state.pos");break}if(i){if(e.pos>=n)break}else e.pending+=e.src[e.pos++]}e.pending&&e.pushPending()},E.prototype.parse=function(e,t,r,n){const a=new this.State(e,t,r,n);this.tokenize(a);const s=this.ruler2.getRules(""),i=s.length;for(let e=0;e<i;e++)s[e](a)},E.prototype.State=a.A;const b=E},6881:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["blockquote","code","fence","heading","hr","html_block","lheading","list","reference","paragraph"]},inline:{rules:["autolink","backticks","emphasis","entity","escape","html_inline","image","link","newline","text"],rules2:["balance_pairs","emphasis","fragments_join"]}}}},9404:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:100},components:{core:{},block:{},inline:{}}}},8079:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["paragraph"]},inline:{rules:["text"],rules2:["balance_pairs","fragments_join"]}}}},3836:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(4439);const a={};function s(){this.rules=(0,n.assign)({},a)}a.code_inline=function(e,t,r,a,s){const i=e[t];return"<code"+s.renderAttrs(i)+">"+(0,n.escapeHtml)(i.content)+"</code>"},a.code_block=function(e,t,r,a,s){const i=e[t];return"<pre"+s.renderAttrs(i)+"><code>"+(0,n.escapeHtml)(e[t].content)+"</code></pre>\n"},a.fence=function(e,t,r,a,s){const i=e[t],o=i.info?(0,n.unescapeAll)(i.info).trim():"";let c,l="",u="";if(o){const e=o.split(/(\s+)/g);l=e[0],u=e.slice(2).join("")}if(c=r.highlight&&r.highlight(i.content,l,u)||(0,n.escapeHtml)(i.content),0===c.indexOf("<pre"))return c+"\n";if(o){const e=i.attrIndex("class"),t=i.attrs?i.attrs.slice():[];e<0?t.push(["class",r.langPrefix+l]):(t[e]=t[e].slice(),t[e][1]+=" "+r.langPrefix+l);const n={attrs:t};return`<pre><code${s.renderAttrs(n)}>${c}</code></pre>\n`}return`<pre><code${s.renderAttrs(i)}>${c}</code></pre>\n`},a.image=function(e,t,r,n,a){const s=e[t];return s.attrs[s.attrIndex("alt")][1]=a.renderInlineAsText(s.children,r,n),a.renderToken(e,t,r)},a.hardbreak=function(e,t,r){return r.xhtmlOut?"<br />\n":"<br>\n"},a.softbreak=function(e,t,r){return r.breaks?r.xhtmlOut?"<br />\n":"<br>\n":"\n"},a.text=function(e,t){return(0,n.escapeHtml)(e[t].content)},a.html_block=function(e,t){return e[t].content},a.html_inline=function(e,t){return e[t].content},s.prototype.renderAttrs=function(e){let t,r,a;if(!e.attrs)return"";for(a="",t=0,r=e.attrs.length;t<r;t++)a+=" "+(0,n.escapeHtml)(e.attrs[t][0])+'="'+(0,n.escapeHtml)(e.attrs[t][1])+'"';return a},s.prototype.renderToken=function(e,t,r){const n=e[t];let a="";if(n.hidden)return"";n.block&&-1!==n.nesting&&t&&e[t-1].hidden&&(a+="\n"),a+=(-1===n.nesting?"</":"<")+n.tag,a+=this.renderAttrs(n),0===n.nesting&&r.xhtmlOut&&(a+=" /");let s=!1;if(n.block&&(s=!0,1===n.nesting&&t+1<e.length)){const r=e[t+1];("inline"===r.type||r.hidden||-1===r.nesting&&r.tag===n.tag)&&(s=!1)}return a+=s?">\n":">",a},s.prototype.renderInline=function(e,t,r){let n="";const a=this.rules;for(let s=0,i=e.length;s<i;s++){const i=e[s].type;void 0!==a[i]?n+=a[i](e,s,t,r,this):n+=this.renderToken(e,s,t)}return n},s.prototype.renderInlineAsText=function(e,t,r){let n="";for(let a=0,s=e.length;a<s;a++)switch(e[a].type){case"text":case"html_inline":case"html_block":n+=e[a].content;break;case"image":n+=this.renderInlineAsText(e[a].children,t,r);break;case"softbreak":case"hardbreak":n+="\n"}return n},s.prototype.render=function(e,t,r){let n="";const a=this.rules;for(let s=0,i=e.length;s<i;s++){const i=e[s].type;"inline"===i?n+=this.renderInline(e[s].children,t,r):void 0!==a[i]?n+=a[i](e,s,t,r,this):n+=this.renderToken(e,s,t,r)}return n};const i=s},9358:(e,t,r)=>{"use strict";function n(){this.__rules__=[],this.__cache__=null}r.d(t,{A:()=>a}),n.prototype.__find__=function(e){for(let t=0;t<this.__rules__.length;t++)if(this.__rules__[t].name===e)return t;return-1},n.prototype.__compile__=function(){const e=this,t=[""];e.__rules__.forEach((function(e){e.enabled&&e.alt.forEach((function(e){t.indexOf(e)<0&&t.push(e)}))})),e.__cache__={},t.forEach((function(t){e.__cache__[t]=[],e.__rules__.forEach((function(r){r.enabled&&(t&&r.alt.indexOf(t)<0||e.__cache__[t].push(r.fn))}))}))},n.prototype.at=function(e,t,r){const n=this.__find__(e),a=r||{};if(-1===n)throw new Error("Parser rule not found: "+e);this.__rules__[n].fn=t,this.__rules__[n].alt=a.alt||[],this.__cache__=null},n.prototype.before=function(e,t,r,n){const a=this.__find__(e),s=n||{};if(-1===a)throw new Error("Parser rule not found: "+e);this.__rules__.splice(a,0,{name:t,enabled:!0,fn:r,alt:s.alt||[]}),this.__cache__=null},n.prototype.after=function(e,t,r,n){const a=this.__find__(e),s=n||{};if(-1===a)throw new Error("Parser rule not found: "+e);this.__rules__.splice(a+1,0,{name:t,enabled:!0,fn:r,alt:s.alt||[]}),this.__cache__=null},n.prototype.push=function(e,t,r){const n=r||{};this.__rules__.push({name:e,enabled:!0,fn:t,alt:n.alt||[]}),this.__cache__=null},n.prototype.enable=function(e,t){Array.isArray(e)||(e=[e]);const r=[];return e.forEach((function(e){const n=this.__find__(e);if(n<0){if(t)return;throw new Error("Rules manager: invalid rule name "+e)}this.__rules__[n].enabled=!0,r.push(e)}),this),this.__cache__=null,r},n.prototype.enableOnly=function(e,t){Array.isArray(e)||(e=[e]),this.__rules__.forEach((function(e){e.enabled=!1})),this.enable(e,t)},n.prototype.disable=function(e,t){Array.isArray(e)||(e=[e]);const r=[];return e.forEach((function(e){const n=this.__find__(e);if(n<0){if(t)return;throw new Error("Rules manager: invalid rule name "+e)}this.__rules__[n].enabled=!1,r.push(e)}),this),this.__cache__=null,r},n.prototype.getRules=function(e){return null===this.__cache__&&this.__compile__(),this.__cache__[e]||[]};const a=n},7537:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(4439);function a(e,t,r,a){let s=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];const o=e.lineMax;if(e.sCount[t]-e.blkIndent>=4)return!1;if(62!==e.src.charCodeAt(s))return!1;if(a)return!0;const c=[],l=[],u=[],d=[],p=e.md.block.ruler.getRules("blockquote"),h=e.parentType;e.parentType="blockquote";let m,f=!1;for(m=t;m<r;m++){const t=e.sCount[m]<e.blkIndent;if(s=e.bMarks[m]+e.tShift[m],i=e.eMarks[m],s>=i)break;if(62===e.src.charCodeAt(s++)&&!t){let t,r,a=e.sCount[m]+1;32===e.src.charCodeAt(s)?(s++,a++,r=!1,t=!0):9===e.src.charCodeAt(s)?(t=!0,(e.bsCount[m]+a)%4==3?(s++,a++,r=!1):r=!0):t=!1;let o=a;for(c.push(e.bMarks[m]),e.bMarks[m]=s;s<i;){const t=e.src.charCodeAt(s);if(!(0,n.isSpace)(t))break;9===t?o+=4-(o+e.bsCount[m]+(r?1:0))%4:o++,s++}f=s>=i,l.push(e.bsCount[m]),e.bsCount[m]=e.sCount[m]+1+(t?1:0),u.push(e.sCount[m]),e.sCount[m]=o-a,d.push(e.tShift[m]),e.tShift[m]=s-e.bMarks[m];continue}if(f)break;let a=!1;for(let t=0,n=p.length;t<n;t++)if(p[t](e,m,r,!0)){a=!0;break}if(a){e.lineMax=m,0!==e.blkIndent&&(c.push(e.bMarks[m]),l.push(e.bsCount[m]),d.push(e.tShift[m]),u.push(e.sCount[m]),e.sCount[m]-=e.blkIndent);break}c.push(e.bMarks[m]),l.push(e.bsCount[m]),d.push(e.tShift[m]),u.push(e.sCount[m]),e.sCount[m]=-1}const g=e.blkIndent;e.blkIndent=0;const _=e.push("blockquote_open","blockquote",1);_.markup=">";const k=[t,0];_.map=k,e.md.block.tokenize(e,t,m);e.push("blockquote_close","blockquote",-1).markup=">",e.lineMax=o,e.parentType=h,k[1]=e.line;for(let r=0;r<d.length;r++)e.bMarks[r+t]=c[r],e.tShift[r+t]=d[r],e.sCount[r+t]=u[r],e.bsCount[r+t]=l[r];return e.blkIndent=g,!0}},7103:(e,t,r)=>{"use strict";function n(e,t,r){if(e.sCount[t]-e.blkIndent<4)return!1;let n=t+1,a=n;for(;n<r;)if(e.isEmpty(n))n++;else{if(!(e.sCount[n]-e.blkIndent>=4))break;n++,a=n}e.line=a;const s=e.push("code_block","code",0);return s.content=e.getLines(t,a,4+e.blkIndent,!1)+"\n",s.map=[t,e.line],!0}r.d(t,{A:()=>n})},6283:(e,t,r)=>{"use strict";function n(e,t,r,n){let a=e.bMarks[t]+e.tShift[t],s=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(a+3>s)return!1;const i=e.src.charCodeAt(a);if(126!==i&&96!==i)return!1;let o=a;a=e.skipChars(a,i);let c=a-o;if(c<3)return!1;const l=e.src.slice(o,a),u=e.src.slice(a,s);if(96===i&&u.indexOf(String.fromCharCode(i))>=0)return!1;if(n)return!0;let d=t,p=!1;for(;(d++,!(d>=r))&&(a=o=e.bMarks[d]+e.tShift[d],s=e.eMarks[d],!(a<s&&e.sCount[d]<e.blkIndent));)if(e.src.charCodeAt(a)===i&&!(e.sCount[d]-e.blkIndent>=4||(a=e.skipChars(a,i),a-o<c||(a=e.skipSpaces(a),a<s)))){p=!0;break}c=e.sCount[t],e.line=d+(p?1:0);const h=e.push("fence","code",0);return h.info=u,h.content=e.getLines(t+1,d,c,!0),h.markup=l,h.map=[t,e.line],!0}r.d(t,{A:()=>n})},3318:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(4439);function a(e,t,r,a){let s=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;let o=e.src.charCodeAt(s);if(35!==o||s>=i)return!1;let c=1;for(o=e.src.charCodeAt(++s);35===o&&s<i&&c<=6;)c++,o=e.src.charCodeAt(++s);if(c>6||s<i&&!(0,n.isSpace)(o))return!1;if(a)return!0;i=e.skipSpacesBack(i,s);const l=e.skipCharsBack(i,35,s);l>s&&(0,n.isSpace)(e.src.charCodeAt(l-1))&&(i=l),e.line=t+1;const u=e.push("heading_open","h"+String(c),1);u.markup="########".slice(0,c),u.map=[t,e.line];const d=e.push("inline","",0);d.content=e.src.slice(s,i).trim(),d.map=[t,e.line],d.children=[];return e.push("heading_close","h"+String(c),-1).markup="########".slice(0,c),!0}},9062:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(4439);function a(e,t,r,a){const s=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;let i=e.bMarks[t]+e.tShift[t];const o=e.src.charCodeAt(i++);if(42!==o&&45!==o&&95!==o)return!1;let c=1;for(;i<s;){const t=e.src.charCodeAt(i++);if(t!==o&&!(0,n.isSpace)(t))return!1;t===o&&c++}if(c<3)return!1;if(a)return!0;e.line=t+1;const l=e.push("hr","hr",0);return l.map=[t,e.line],l.markup=Array(c+1).join(String.fromCharCode(o)),!0}},2577:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(4384),a=r(8105);const s=[[/^<(script|pre|style|textarea)(?=(\s|>|$))/i,/<\/(script|pre|style|textarea)>/i,!0],[/^<!--/,/-->/,!0],[/^<\?/,/\?>/,!0],[/^<![A-Z]/,/>/,!0],[/^<!\[CDATA\[/,/\]\]>/,!0],[new RegExp("^</?("+n.A.join("|")+")(?=(\\s|/?>|$))","i"),/^$/,!0],[new RegExp(a.p.source+"\\s*$"),/^$/,!1]];function i(e,t,r,n){let a=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(!e.md.options.html)return!1;if(60!==e.src.charCodeAt(a))return!1;let o=e.src.slice(a,i),c=0;for(;c<s.length&&!s[c][0].test(o);c++);if(c===s.length)return!1;if(n)return s[c][2];let l=t+1;if(!s[c][1].test(o))for(;l<r&&!(e.sCount[l]<e.blkIndent);l++)if(a=e.bMarks[l]+e.tShift[l],i=e.eMarks[l],o=e.src.slice(a,i),s[c][1].test(o)){0!==o.length&&l++;break}e.line=l;const u=e.push("html_block","",0);return u.map=[t,l],u.content=e.getLines(t,l,e.blkIndent,!0),!0}},4206:(e,t,r)=>{"use strict";function n(e,t,r){const n=e.md.block.ruler.getRules("paragraph");if(e.sCount[t]-e.blkIndent>=4)return!1;const a=e.parentType;e.parentType="paragraph";let s,i=0,o=t+1;for(;o<r&&!e.isEmpty(o);o++){if(e.sCount[o]-e.blkIndent>3)continue;if(e.sCount[o]>=e.blkIndent){let t=e.bMarks[o]+e.tShift[o];const r=e.eMarks[o];if(t<r&&(s=e.src.charCodeAt(t),(45===s||61===s)&&(t=e.skipChars(t,s),t=e.skipSpaces(t),t>=r))){i=61===s?1:2;break}}if(e.sCount[o]<0)continue;let t=!1;for(let a=0,s=n.length;a<s;a++)if(n[a](e,o,r,!0)){t=!0;break}if(t)break}if(!i)return!1;const c=e.getLines(t,o,e.blkIndent,!1).trim();e.line=o+1;const l=e.push("heading_open","h"+String(i),1);l.markup=String.fromCharCode(s),l.map=[t,e.line];const u=e.push("inline","",0);u.content=c,u.map=[t,e.line-1],u.children=[];return e.push("heading_close","h"+String(i),-1).markup=String.fromCharCode(s),e.parentType=a,!0}r.d(t,{A:()=>n})},5792:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(4439);function a(e,t){const r=e.eMarks[t];let a=e.bMarks[t]+e.tShift[t];const s=e.src.charCodeAt(a++);if(42!==s&&45!==s&&43!==s)return-1;if(a<r){const t=e.src.charCodeAt(a);if(!(0,n.isSpace)(t))return-1}return a}function s(e,t){const r=e.bMarks[t]+e.tShift[t],a=e.eMarks[t];let s=r;if(s+1>=a)return-1;let i=e.src.charCodeAt(s++);if(i<48||i>57)return-1;for(;;){if(s>=a)return-1;if(i=e.src.charCodeAt(s++),!(i>=48&&i<=57)){if(41===i||46===i)break;return-1}if(s-r>=10)return-1}return s<a&&(i=e.src.charCodeAt(s),!(0,n.isSpace)(i))?-1:s}function i(e,t,r,n){let i,o,c,l,u=t,d=!0;if(e.sCount[u]-e.blkIndent>=4)return!1;if(e.listIndent>=0&&e.sCount[u]-e.listIndent>=4&&e.sCount[u]<e.blkIndent)return!1;let p,h,m,f=!1;if(n&&"paragraph"===e.parentType&&e.sCount[u]>=e.blkIndent&&(f=!0),(m=s(e,u))>=0){if(p=!0,c=e.bMarks[u]+e.tShift[u],h=Number(e.src.slice(c,m-1)),f&&1!==h)return!1}else{if(!((m=a(e,u))>=0))return!1;p=!1}if(f&&e.skipSpaces(m)>=e.eMarks[u])return!1;if(n)return!0;const g=e.src.charCodeAt(m-1),_=e.tokens.length;p?(l=e.push("ordered_list_open","ol",1),1!==h&&(l.attrs=[["start",h]])):l=e.push("bullet_list_open","ul",1);const k=[u,0];l.map=k,l.markup=String.fromCharCode(g);let y=!1;const v=e.md.block.ruler.getRules("list"),E=e.parentType;for(e.parentType="list";u<r;){o=m,i=e.eMarks[u];const t=e.sCount[u]+m-(e.bMarks[u]+e.tShift[u]);let n=t;for(;o<i;){const t=e.src.charCodeAt(o);if(9===t)n+=4-(n+e.bsCount[u])%4;else{if(32!==t)break;n++}o++}const h=o;let f;f=h>=i?1:n-t,f>4&&(f=1);const _=t+f;l=e.push("list_item_open","li",1),l.markup=String.fromCharCode(g);const k=[u,0];l.map=k,p&&(l.info=e.src.slice(c,m-1));const E=e.tight,b=e.tShift[u],A=e.sCount[u],C=e.listIndent;if(e.listIndent=e.blkIndent,e.blkIndent=_,e.tight=!0,e.tShift[u]=h-e.bMarks[u],e.sCount[u]=n,h>=i&&e.isEmpty(u+1)?e.line=Math.min(e.line+2,r):e.md.block.tokenize(e,u,r,!0),e.tight&&!y||(d=!1),y=e.line-u>1&&e.isEmpty(e.line-1),e.blkIndent=e.listIndent,e.listIndent=C,e.tShift[u]=b,e.sCount[u]=A,e.tight=E,l=e.push("list_item_close","li",-1),l.markup=String.fromCharCode(g),u=e.line,k[1]=u,u>=r)break;if(e.sCount[u]<e.blkIndent)break;if(e.sCount[u]-e.blkIndent>=4)break;let w=!1;for(let t=0,n=v.length;t<n;t++)if(v[t](e,u,r,!0)){w=!0;break}if(w)break;if(p){if(m=s(e,u),m<0)break;c=e.bMarks[u]+e.tShift[u]}else if(m=a(e,u),m<0)break;if(g!==e.src.charCodeAt(m-1))break}return l=p?e.push("ordered_list_close","ol",-1):e.push("bullet_list_close","ul",-1),l.markup=String.fromCharCode(g),k[1]=u,e.line=u,e.parentType=E,d&&function(e,t){const r=e.level+2;for(let n=t+2,a=e.tokens.length-2;n<a;n++)e.tokens[n].level===r&&"paragraph_open"===e.tokens[n].type&&(e.tokens[n+2].hidden=!0,e.tokens[n].hidden=!0,n+=2)}(e,_),!0}},4970:(e,t,r)=>{"use strict";function n(e,t,r){const n=e.md.block.ruler.getRules("paragraph"),a=e.parentType;let s=t+1;for(e.parentType="paragraph";s<r&&!e.isEmpty(s);s++){if(e.sCount[s]-e.blkIndent>3)continue;if(e.sCount[s]<0)continue;let t=!1;for(let a=0,i=n.length;a<i;a++)if(n[a](e,s,r,!0)){t=!0;break}if(t)break}const i=e.getLines(t,s,e.blkIndent,!1).trim();e.line=s;e.push("paragraph_open","p",1).map=[t,e.line];const o=e.push("inline","",0);return o.content=i,o.map=[t,e.line],o.children=[],e.push("paragraph_close","p",-1),e.parentType=a,!0}r.d(t,{A:()=>n})},3863:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(4439);function a(e,t,r,a){let s=e.bMarks[t]+e.tShift[t],i=e.eMarks[t],o=t+1;if(e.sCount[t]-e.blkIndent>=4)return!1;if(91!==e.src.charCodeAt(s))return!1;function c(t){const r=e.lineMax;if(t>=r||e.isEmpty(t))return null;let n=!1;if(e.sCount[t]-e.blkIndent>3&&(n=!0),e.sCount[t]<0&&(n=!0),!n){const n=e.md.block.ruler.getRules("reference"),a=e.parentType;e.parentType="reference";let s=!1;for(let a=0,i=n.length;a<i;a++)if(n[a](e,t,r,!0)){s=!0;break}if(e.parentType=a,s)return null}const a=e.bMarks[t]+e.tShift[t],s=e.eMarks[t];return e.src.slice(a,s+1)}let l=e.src.slice(s,i+1);i=l.length;let u=-1;for(s=1;s<i;s++){const e=l.charCodeAt(s);if(91===e)return!1;if(93===e){u=s;break}if(10===e){const e=c(o);null!==e&&(l+=e,i=l.length,o++)}else if(92===e&&(s++,s<i&&10===l.charCodeAt(s))){const e=c(o);null!==e&&(l+=e,i=l.length,o++)}}if(u<0||58!==l.charCodeAt(u+1))return!1;for(s=u+2;s<i;s++){const e=l.charCodeAt(s);if(10===e){const e=c(o);null!==e&&(l+=e,i=l.length,o++)}else if(!(0,n.isSpace)(e))break}const d=e.md.helpers.parseLinkDestination(l,s,i);if(!d.ok)return!1;const p=e.md.normalizeLink(d.str);if(!e.md.validateLink(p))return!1;s=d.pos;const h=s,m=o,f=s;for(;s<i;s++){const e=l.charCodeAt(s);if(10===e){const e=c(o);null!==e&&(l+=e,i=l.length,o++)}else if(!(0,n.isSpace)(e))break}let g,_=e.md.helpers.parseLinkTitle(l,s,i);for(;_.can_continue;){const t=c(o);if(null===t)break;l+=t,s=i,i=l.length,o++,_=e.md.helpers.parseLinkTitle(l,s,i,_)}for(s<i&&f!==s&&_.ok?(g=_.str,s=_.pos):(g="",s=h,o=m);s<i;){const e=l.charCodeAt(s);if(!(0,n.isSpace)(e))break;s++}if(s<i&&10!==l.charCodeAt(s)&&g)for(g="",s=h,o=m;s<i;){const e=l.charCodeAt(s);if(!(0,n.isSpace)(e))break;s++}if(s<i&&10!==l.charCodeAt(s))return!1;const k=(0,n.normalizeReference)(l.slice(1,u));return!!k&&(a||(void 0===e.env.references&&(e.env.references={}),void 0===e.env.references[k]&&(e.env.references[k]={title:g,href:p}),e.line=o),!0)}},2961:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(7997),a=r(4439);function s(e,t,r,n){this.src=e,this.md=t,this.env=r,this.tokens=n,this.bMarks=[],this.eMarks=[],this.tShift=[],this.sCount=[],this.bsCount=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.ddIndent=-1,this.listIndent=-1,this.parentType="root",this.level=0;const s=this.src;for(let e=0,t=0,r=0,n=0,i=s.length,o=!1;t<i;t++){const c=s.charCodeAt(t);if(!o){if((0,a.isSpace)(c)){r++,9===c?n+=4-n%4:n++;continue}o=!0}10!==c&&t!==i-1||(10!==c&&t++,this.bMarks.push(e),this.eMarks.push(t),this.tShift.push(r),this.sCount.push(n),this.bsCount.push(0),o=!1,r=0,n=0,e=t+1)}this.bMarks.push(s.length),this.eMarks.push(s.length),this.tShift.push(0),this.sCount.push(0),this.bsCount.push(0),this.lineMax=this.bMarks.length-1}s.prototype.push=function(e,t,r){const a=new n.A(e,t,r);return a.block=!0,r<0&&this.level--,a.level=this.level,r>0&&this.level++,this.tokens.push(a),a},s.prototype.isEmpty=function(e){return this.bMarks[e]+this.tShift[e]>=this.eMarks[e]},s.prototype.skipEmptyLines=function(e){for(let t=this.lineMax;e<t&&!(this.bMarks[e]+this.tShift[e]<this.eMarks[e]);e++);return e},s.prototype.skipSpaces=function(e){for(let t=this.src.length;e<t;e++){const t=this.src.charCodeAt(e);if(!(0,a.isSpace)(t))break}return e},s.prototype.skipSpacesBack=function(e,t){if(e<=t)return e;for(;e>t;)if(!(0,a.isSpace)(this.src.charCodeAt(--e)))return e+1;return e},s.prototype.skipChars=function(e,t){for(let r=this.src.length;e<r&&this.src.charCodeAt(e)===t;e++);return e},s.prototype.skipCharsBack=function(e,t,r){if(e<=r)return e;for(;e>r;)if(t!==this.src.charCodeAt(--e))return e+1;return e},s.prototype.getLines=function(e,t,r,n){if(e>=t)return"";const s=new Array(t-e);for(let i=0,o=e;o<t;o++,i++){let e=0;const c=this.bMarks[o];let l,u=c;for(l=o+1<t||n?this.eMarks[o]+1:this.eMarks[o];u<l&&e<r;){const t=this.src.charCodeAt(u);if((0,a.isSpace)(t))9===t?e+=4-(e+this.bsCount[o])%4:e++;else{if(!(u-c<this.tShift[o]))break;e++}u++}s[i]=e>r?new Array(e-r+1).join(" ")+this.src.slice(u,l):this.src.slice(u,l)}return s.join("")},s.prototype.Token=n.A;const i=s},712:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(4439);const a=65536;function s(e,t){const r=e.bMarks[t]+e.tShift[t],n=e.eMarks[t];return e.src.slice(r,n)}function i(e){const t=[],r=e.length;let n=0,a=e.charCodeAt(n),s=!1,i=0,o="";for(;n<r;)124===a&&(s?(o+=e.substring(i,n-1),i=n):(t.push(o+e.substring(i,n)),o="",i=n+1)),s=92===a,n++,a=e.charCodeAt(n);return t.push(o+e.substring(i)),t}function o(e,t,r,o){if(t+2>r)return!1;let c=t+1;if(e.sCount[c]<e.blkIndent)return!1;if(e.sCount[c]-e.blkIndent>=4)return!1;let l=e.bMarks[c]+e.tShift[c];if(l>=e.eMarks[c])return!1;const u=e.src.charCodeAt(l++);if(124!==u&&45!==u&&58!==u)return!1;if(l>=e.eMarks[c])return!1;const d=e.src.charCodeAt(l++);if(124!==d&&45!==d&&58!==d&&!(0,n.isSpace)(d))return!1;if(45===u&&(0,n.isSpace)(d))return!1;for(;l<e.eMarks[c];){const t=e.src.charCodeAt(l);if(124!==t&&45!==t&&58!==t&&!(0,n.isSpace)(t))return!1;l++}let p=s(e,t+1),h=p.split("|");const m=[];for(let e=0;e<h.length;e++){const t=h[e].trim();if(!t){if(0===e||e===h.length-1)continue;return!1}if(!/^:?-+:?$/.test(t))return!1;58===t.charCodeAt(t.length-1)?m.push(58===t.charCodeAt(0)?"center":"right"):58===t.charCodeAt(0)?m.push("left"):m.push("")}if(p=s(e,t).trim(),-1===p.indexOf("|"))return!1;if(e.sCount[t]-e.blkIndent>=4)return!1;h=i(p),h.length&&""===h[0]&&h.shift(),h.length&&""===h[h.length-1]&&h.pop();const f=h.length;if(0===f||f!==m.length)return!1;if(o)return!0;const g=e.parentType;e.parentType="table";const _=e.md.block.ruler.getRules("blockquote"),k=[t,0];e.push("table_open","table",1).map=k;e.push("thead_open","thead",1).map=[t,t+1];e.push("tr_open","tr",1).map=[t,t+1];for(let t=0;t<h.length;t++){const r=e.push("th_open","th",1);m[t]&&(r.attrs=[["style","text-align:"+m[t]]]);const n=e.push("inline","",0);n.content=h[t].trim(),n.children=[],e.push("th_close","th",-1)}let y;e.push("tr_close","tr",-1),e.push("thead_close","thead",-1);let v=0;for(c=t+2;c<r&&!(e.sCount[c]<e.blkIndent);c++){let n=!1;for(let t=0,a=_.length;t<a;t++)if(_[t](e,c,r,!0)){n=!0;break}if(n)break;if(p=s(e,c).trim(),!p)break;if(e.sCount[c]-e.blkIndent>=4)break;if(h=i(p),h.length&&""===h[0]&&h.shift(),h.length&&""===h[h.length-1]&&h.pop(),v+=f-h.length,v>a)break;if(c===t+2){e.push("tbody_open","tbody",1).map=y=[t+2,0]}e.push("tr_open","tr",1).map=[c,c+1];for(let t=0;t<f;t++){const r=e.push("td_open","td",1);m[t]&&(r.attrs=[["style","text-align:"+m[t]]]);const n=e.push("inline","",0);n.content=h[t]?h[t].trim():"",n.children=[],e.push("td_close","td",-1)}e.push("tr_close","tr",-1)}return y&&(e.push("tbody_close","tbody",-1),y[1]=c),e.push("table_close","table",-1),k[1]=c,e.parentType=g,e.line=c,!0}},9417:(e,t,r)=>{"use strict";function n(e){let t;e.inlineMode?(t=new e.Token("inline","",0),t.content=e.src,t.map=[0,1],t.children=[],e.tokens.push(t)):e.md.block.parse(e.src,e.md,e.env,e.tokens)}r.d(t,{A:()=>n})},5189:(e,t,r)=>{"use strict";function n(e){const t=e.tokens;for(let r=0,n=t.length;r<n;r++){const n=t[r];"inline"===n.type&&e.md.inline.parse(n.content,e.md,e.env,n.children)}}r.d(t,{A:()=>n})},6084:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(4439);function a(e){return/^<\/a\s*>/i.test(e)}function s(e){const t=e.tokens;var r;if(e.md.options.linkify)for(let s=0,i=t.length;s<i;s++){if("inline"!==t[s].type||!e.md.linkify.pretest(t[s].content))continue;let i=t[s].children,o=0;for(let c=i.length-1;c>=0;c--){const l=i[c];if("link_close"!==l.type){if("html_inline"===l.type&&(r=l.content,/^<a[>\s]/i.test(r)&&o>0&&o--,a(l.content)&&o++),!(o>0)&&"text"===l.type&&e.md.linkify.test(l.content)){const r=l.content;let a=e.md.linkify.match(r);const o=[];let u=l.level,d=0;a.length>0&&0===a[0].index&&c>0&&"text_special"===i[c-1].type&&(a=a.slice(1));for(let t=0;t<a.length;t++){const n=a[t].url,s=e.md.normalizeLink(n);if(!e.md.validateLink(s))continue;let i=a[t].text;i=a[t].schema?"mailto:"!==a[t].schema||/^mailto:/i.test(i)?e.md.normalizeLinkText(i):e.md.normalizeLinkText("mailto:"+i).replace(/^mailto:/,""):e.md.normalizeLinkText("http://"+i).replace(/^http:\/\//,"");const c=a[t].index;if(c>d){const t=new e.Token("text","",0);t.content=r.slice(d,c),t.level=u,o.push(t)}const l=new e.Token("link_open","a",1);l.attrs=[["href",s]],l.level=u++,l.markup="linkify",l.info="auto",o.push(l);const p=new e.Token("text","",0);p.content=i,p.level=u,o.push(p);const h=new e.Token("link_close","a",-1);h.level=--u,h.markup="linkify",h.info="auto",o.push(h),d=a[t].lastIndex}if(d<r.length){const t=new e.Token("text","",0);t.content=r.slice(d),t.level=u,o.push(t)}t[s].children=i=(0,n.arrayReplaceAt)(i,c,o)}}else for(c--;i[c].level!==l.level&&"link_open"!==i[c].type;)c--}}}},2699:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});const n=/\r\n?|\n/g,a=/\0/g;function s(e){let t;t=e.src.replace(n,"\n"),t=t.replace(a,"�"),e.src=t}},8541:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});const n=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,a=/\((c|tm|r)\)/i,s=/\((c|tm|r)\)/gi,i={c:"©",r:"®",tm:"™"};function o(e,t){return i[t.toLowerCase()]}function c(e){let t=0;for(let r=e.length-1;r>=0;r--){const n=e[r];"text"!==n.type||t||(n.content=n.content.replace(s,o)),"link_open"===n.type&&"auto"===n.info&&t--,"link_close"===n.type&&"auto"===n.info&&t++}}function l(e){let t=0;for(let r=e.length-1;r>=0;r--){const a=e[r];"text"!==a.type||t||n.test(a.content)&&(a.content=a.content.replace(/\+-/g,"±").replace(/\.{2,}/g,"…").replace(/([?!])…/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---(?=[^-]|$)/gm,"$1—").replace(/(^|\s)--(?=\s|$)/gm,"$1–").replace(/(^|[^-\s])--(?=[^-\s]|$)/gm,"$1–")),"link_open"===a.type&&"auto"===a.info&&t--,"link_close"===a.type&&"auto"===a.info&&t++}}function u(e){let t;if(e.md.options.typographer)for(t=e.tokens.length-1;t>=0;t--)"inline"===e.tokens[t].type&&(a.test(e.tokens[t].content)&&c(e.tokens[t].children),n.test(e.tokens[t].content)&&l(e.tokens[t].children))}},5834:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(4439);const a=/['"]/,s=/['"]/g,i="’";function o(e,t,r){return e.slice(0,t)+r+e.slice(t+1)}function c(e,t){let r;const a=[];for(let c=0;c<e.length;c++){const l=e[c],u=e[c].level;for(r=a.length-1;r>=0&&!(a[r].level<=u);r--);if(a.length=r+1,"text"!==l.type)continue;let d=l.content,p=0,h=d.length;e:for(;p<h;){s.lastIndex=p;const m=s.exec(d);if(!m)break;let f=!0,g=!0;p=m.index+1;const _="'"===m[0];let k=32;if(m.index-1>=0)k=d.charCodeAt(m.index-1);else for(r=c-1;r>=0&&("softbreak"!==e[r].type&&"hardbreak"!==e[r].type);r--)if(e[r].content){k=e[r].content.charCodeAt(e[r].content.length-1);break}let y=32;if(p<h)y=d.charCodeAt(p);else for(r=c+1;r<e.length&&("softbreak"!==e[r].type&&"hardbreak"!==e[r].type);r++)if(e[r].content){y=e[r].content.charCodeAt(0);break}const v=(0,n.isMdAsciiPunct)(k)||(0,n.isPunctChar)(String.fromCharCode(k)),E=(0,n.isMdAsciiPunct)(y)||(0,n.isPunctChar)(String.fromCharCode(y)),b=(0,n.isWhiteSpace)(k),A=(0,n.isWhiteSpace)(y);if(A?f=!1:E&&(b||v||(f=!1)),b?g=!1:v&&(A||E||(g=!1)),34===y&&'"'===m[0]&&k>=48&&k<=57&&(g=f=!1),f&&g&&(f=v,g=E),f||g){if(g)for(r=a.length-1;r>=0;r--){let n=a[r];if(a[r].level<u)break;if(n.single===_&&a[r].level===u){let s,i;n=a[r],_?(s=t.md.options.quotes[2],i=t.md.options.quotes[3]):(s=t.md.options.quotes[0],i=t.md.options.quotes[1]),l.content=o(l.content,m.index,i),e[n.token].content=o(e[n.token].content,n.pos,s),p+=i.length-1,n.token===c&&(p+=s.length-1),d=l.content,h=d.length,a.length=r;continue e}}f?a.push({token:c,pos:m.index,single:_,level:u}):g&&_&&(l.content=o(l.content,m.index,i))}else _&&(l.content=o(l.content,m.index,i))}}}function l(e){if(e.md.options.typographer)for(let t=e.tokens.length-1;t>=0;t--)"inline"===e.tokens[t].type&&a.test(e.tokens[t].content)&&c(e.tokens[t].children,e)}},7569:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(7997);function a(e,t,r){this.src=e,this.env=r,this.tokens=[],this.inlineMode=!1,this.md=t}a.prototype.Token=n.A;const s=a},1242:(e,t,r)=>{"use strict";function n(e){let t,r;const n=e.tokens,a=n.length;for(let e=0;e<a;e++){if("inline"!==n[e].type)continue;const a=n[e].children,s=a.length;for(t=0;t<s;t++)"text_special"===a[t].type&&(a[t].type="text");for(t=r=0;t<s;t++)"text"===a[t].type&&t+1<s&&"text"===a[t+1].type?a[t+1].content=a[t].content+a[t+1].content:(t!==r&&(a[r]=a[t]),r++);t!==r&&(a.length=r)}}r.d(t,{A:()=>n})},5469:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});const n=/^([a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/,a=/^([a-zA-Z][a-zA-Z0-9+.-]{1,31}):([^<>\x00-\x20]*)$/;function s(e,t){let r=e.pos;if(60!==e.src.charCodeAt(r))return!1;const s=e.pos,i=e.posMax;for(;;){if(++r>=i)return!1;const t=e.src.charCodeAt(r);if(60===t)return!1;if(62===t)break}const o=e.src.slice(s+1,r);if(a.test(o)){const r=e.md.normalizeLink(o);if(!e.md.validateLink(r))return!1;if(!t){const t=e.push("link_open","a",1);t.attrs=[["href",r]],t.markup="autolink",t.info="auto";e.push("text","",0).content=e.md.normalizeLinkText(o);const n=e.push("link_close","a",-1);n.markup="autolink",n.info="auto"}return e.pos+=o.length+2,!0}if(n.test(o)){const r=e.md.normalizeLink("mailto:"+o);if(!e.md.validateLink(r))return!1;if(!t){const t=e.push("link_open","a",1);t.attrs=[["href",r]],t.markup="autolink",t.info="auto";e.push("text","",0).content=e.md.normalizeLinkText(o);const n=e.push("link_close","a",-1);n.markup="autolink",n.info="auto"}return e.pos+=o.length+2,!0}return!1}},8797:(e,t,r)=>{"use strict";function n(e,t){let r=e.pos;if(96!==e.src.charCodeAt(r))return!1;const n=r;r++;const a=e.posMax;for(;r<a&&96===e.src.charCodeAt(r);)r++;const s=e.src.slice(n,r),i=s.length;if(e.backticksScanned&&(e.backticks[i]||0)<=n)return t||(e.pending+=s),e.pos+=i,!0;let o,c=r;for(;-1!==(o=e.src.indexOf("`",c));){for(c=o+1;c<a&&96===e.src.charCodeAt(c);)c++;const n=c-o;if(n===i){if(!t){const t=e.push("code_inline","code",0);t.markup=s,t.content=e.src.slice(r,o).replace(/\n/g," ").replace(/^ (.+) $/,"$1")}return e.pos=c,!0}e.backticks[n]=o}return e.backticksScanned=!0,t||(e.pending+=s),e.pos+=i,!0}r.d(t,{A:()=>n})},6014:(e,t,r)=>{"use strict";function n(e){const t={},r=e.length;if(!r)return;let n=0,a=-2;const s=[];for(let i=0;i<r;i++){const r=e[i];if(s.push(0),e[n].marker===r.marker&&a===r.token-1||(n=i),a=r.token,r.length=r.length||0,!r.close)continue;t.hasOwnProperty(r.marker)||(t[r.marker]=[-1,-1,-1,-1,-1,-1]);const o=t[r.marker][(r.open?3:0)+r.length%3];let c=n-s[n]-1,l=c;for(;c>o;c-=s[c]+1){const t=e[c];if(t.marker===r.marker&&(t.open&&t.end<0)){let n=!1;if((t.close||r.open)&&(t.length+r.length)%3==0&&(t.length%3==0&&r.length%3==0||(n=!0)),!n){const n=c>0&&!e[c-1].open?s[c-1]+1:0;s[i]=i-c+n,s[c]=n,r.open=!1,t.end=i,t.close=!1,l=-1,a=-2;break}}}-1!==l&&(t[r.marker][(r.open?3:0)+(r.length||0)%3]=l)}}function a(e){const t=e.tokens_meta,r=e.tokens_meta.length;n(e.delimiters);for(let e=0;e<r;e++)t[e]&&t[e].delimiters&&n(t[e].delimiters)}r.d(t,{A:()=>a})},2206:(e,t,r)=>{"use strict";function n(e,t){for(let r=t.length-1;r>=0;r--){const n=t[r];if(95!==n.marker&&42!==n.marker)continue;if(-1===n.end)continue;const a=t[n.end],s=r>0&&t[r-1].end===n.end+1&&t[r-1].marker===n.marker&&t[r-1].token===n.token-1&&t[n.end+1].token===a.token+1,i=String.fromCharCode(n.marker),o=e.tokens[n.token];o.type=s?"strong_open":"em_open",o.tag=s?"strong":"em",o.nesting=1,o.markup=s?i+i:i,o.content="";const c=e.tokens[a.token];c.type=s?"strong_close":"em_close",c.tag=s?"strong":"em",c.nesting=-1,c.markup=s?i+i:i,c.content="",s&&(e.tokens[t[r-1].token].content="",e.tokens[t[n.end+1].token].content="",r--)}}r.d(t,{A:()=>a});const a={tokenize:function(e,t){const r=e.pos,n=e.src.charCodeAt(r);if(t)return!1;if(95!==n&&42!==n)return!1;const a=e.scanDelims(e.pos,42===n);for(let t=0;t<a.length;t++){e.push("text","",0).content=String.fromCharCode(n),e.delimiters.push({marker:n,length:a.length,token:e.tokens.length-1,end:-1,open:a.can_open,close:a.can_close})}return e.pos+=a.length,!0},postProcess:function(e){const t=e.tokens_meta,r=e.tokens_meta.length;n(e,e.delimiters);for(let a=0;a<r;a++)t[a]&&t[a].delimiters&&n(e,t[a].delimiters)}}},1517:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(9405),a=r(4439);const s=/^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i,i=/^&([a-z][a-z0-9]{1,31});/i;function o(e,t){const r=e.pos,o=e.posMax;if(38!==e.src.charCodeAt(r))return!1;if(r+1>=o)return!1;if(35===e.src.charCodeAt(r+1)){const n=e.src.slice(r).match(s);if(n){if(!t){const t="x"===n[1][0].toLowerCase()?parseInt(n[1].slice(1),16):parseInt(n[1],10),r=e.push("text_special","",0);r.content=(0,a.isValidEntityCode)(t)?(0,a.fromCodePoint)(t):(0,a.fromCodePoint)(65533),r.markup=n[0],r.info="entity"}return e.pos+=n[0].length,!0}}else{const a=e.src.slice(r).match(i);if(a){const r=(0,n.Gz)(a[0]);if(r!==a[0]){if(!t){const t=e.push("text_special","",0);t.content=r,t.markup=a[0],t.info="entity"}return e.pos+=a[0].length,!0}}}return!1}},7785:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(4439);const a=[];for(let e=0;e<256;e++)a.push(0);function s(e,t){let r=e.pos;const s=e.posMax;if(92!==e.src.charCodeAt(r))return!1;if(r++,r>=s)return!1;let i=e.src.charCodeAt(r);if(10===i){for(t||e.push("hardbreak","br",0),r++;r<s&&(i=e.src.charCodeAt(r),(0,n.isSpace)(i));)r++;return e.pos=r,!0}let o=e.src[r];if(i>=55296&&i<=56319&&r+1<s){const t=e.src.charCodeAt(r+1);t>=56320&&t<=57343&&(o+=e.src[r+1],r++)}const c="\\"+o;if(!t){const t=e.push("text_special","",0);i<256&&0!==a[i]?t.content=o:t.content=c,t.markup=c,t.info="escape"}return e.pos=r+1,!0}"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach((function(e){a[e.charCodeAt(0)]=1}))},322:(e,t,r)=>{"use strict";function n(e){let t,r,n=0;const a=e.tokens,s=e.tokens.length;for(t=r=0;t<s;t++)a[t].nesting<0&&n--,a[t].level=n,a[t].nesting>0&&n++,"text"===a[t].type&&t+1<s&&"text"===a[t+1].type?a[t+1].content=a[t].content+a[t+1].content:(t!==r&&(a[r]=a[t]),r++);t!==r&&(a.length=r)}r.d(t,{A:()=>n})},9486:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(8105);function a(e,t){if(!e.md.options.html)return!1;const r=e.posMax,a=e.pos;if(60!==e.src.charCodeAt(a)||a+2>=r)return!1;const s=e.src.charCodeAt(a+1);if(33!==s&&63!==s&&47!==s&&!function(e){const t=32|e;return t>=97&&t<=122}(s))return!1;const i=e.src.slice(a).match(n.l);if(!i)return!1;if(!t){const t=e.push("html_inline","",0);t.content=i[0],o=t.content,/^<a[>\s]/i.test(o)&&e.linkLevel++,function(e){return/^<\/a\s*>/i.test(e)}(t.content)&&e.linkLevel--}var o;return e.pos+=i[0].length,!0}},4427:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(4439);function a(e,t){let r,a,s,i,o,c,l,u,d="";const p=e.pos,h=e.posMax;if(33!==e.src.charCodeAt(e.pos))return!1;if(91!==e.src.charCodeAt(e.pos+1))return!1;const m=e.pos+2,f=e.md.helpers.parseLinkLabel(e,e.pos+1,!1);if(f<0)return!1;if(i=f+1,i<h&&40===e.src.charCodeAt(i)){for(i++;i<h&&(r=e.src.charCodeAt(i),(0,n.isSpace)(r)||10===r);i++);if(i>=h)return!1;for(u=i,c=e.md.helpers.parseLinkDestination(e.src,i,e.posMax),c.ok&&(d=e.md.normalizeLink(c.str),e.md.validateLink(d)?i=c.pos:d=""),u=i;i<h&&(r=e.src.charCodeAt(i),(0,n.isSpace)(r)||10===r);i++);if(c=e.md.helpers.parseLinkTitle(e.src,i,e.posMax),i<h&&u!==i&&c.ok)for(l=c.str,i=c.pos;i<h&&(r=e.src.charCodeAt(i),(0,n.isSpace)(r)||10===r);i++);else l="";if(i>=h||41!==e.src.charCodeAt(i))return e.pos=p,!1;i++}else{if(void 0===e.env.references)return!1;if(i<h&&91===e.src.charCodeAt(i)?(u=i+1,i=e.md.helpers.parseLinkLabel(e,i),i>=0?s=e.src.slice(u,i++):i=f+1):i=f+1,s||(s=e.src.slice(m,f)),o=e.env.references[(0,n.normalizeReference)(s)],!o)return e.pos=p,!1;d=o.href,l=o.title}if(!t){a=e.src.slice(m,f);const t=[];e.md.inline.parse(a,e.md,e.env,t);const r=e.push("image","img",0),n=[["src",d],["alt",""]];r.attrs=n,r.children=t,r.content=a,l&&n.push(["title",l])}return e.pos=i,e.posMax=h,!0}},2128:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(4439);function a(e,t){let r,a,s,i,o="",c="",l=e.pos,u=!0;if(91!==e.src.charCodeAt(e.pos))return!1;const d=e.pos,p=e.posMax,h=e.pos+1,m=e.md.helpers.parseLinkLabel(e,e.pos,!0);if(m<0)return!1;let f=m+1;if(f<p&&40===e.src.charCodeAt(f)){for(u=!1,f++;f<p&&(r=e.src.charCodeAt(f),(0,n.isSpace)(r)||10===r);f++);if(f>=p)return!1;if(l=f,s=e.md.helpers.parseLinkDestination(e.src,f,e.posMax),s.ok){for(o=e.md.normalizeLink(s.str),e.md.validateLink(o)?f=s.pos:o="",l=f;f<p&&(r=e.src.charCodeAt(f),(0,n.isSpace)(r)||10===r);f++);if(s=e.md.helpers.parseLinkTitle(e.src,f,e.posMax),f<p&&l!==f&&s.ok)for(c=s.str,f=s.pos;f<p&&(r=e.src.charCodeAt(f),(0,n.isSpace)(r)||10===r);f++);}(f>=p||41!==e.src.charCodeAt(f))&&(u=!0),f++}if(u){if(void 0===e.env.references)return!1;if(f<p&&91===e.src.charCodeAt(f)?(l=f+1,f=e.md.helpers.parseLinkLabel(e,f),f>=0?a=e.src.slice(l,f++):f=m+1):f=m+1,a||(a=e.src.slice(h,m)),i=e.env.references[(0,n.normalizeReference)(a)],!i)return e.pos=d,!1;o=i.href,c=i.title}if(!t){e.pos=h,e.posMax=m;const t=[["href",o]];e.push("link_open","a",1).attrs=t,c&&t.push(["title",c]),e.linkLevel++,e.md.inline.tokenize(e),e.linkLevel--,e.push("link_close","a",-1)}return e.pos=f,e.posMax=p,!0}},4938:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});const n=/(?:^|[^a-z0-9.+-])([a-z][a-z0-9.+-]*)$/i;function a(e,t){if(!e.md.options.linkify)return!1;if(e.linkLevel>0)return!1;const r=e.pos;if(r+3>e.posMax)return!1;if(58!==e.src.charCodeAt(r))return!1;if(47!==e.src.charCodeAt(r+1))return!1;if(47!==e.src.charCodeAt(r+2))return!1;const a=e.pending.match(n);if(!a)return!1;const s=a[1],i=e.md.linkify.matchAtStart(e.src.slice(r-s.length));if(!i)return!1;let o=i.url;if(o.length<=s.length)return!1;o=o.replace(/\*+$/,"");const c=e.md.normalizeLink(o);if(!e.md.validateLink(c))return!1;if(!t){e.pending=e.pending.slice(0,-s.length);const t=e.push("link_open","a",1);t.attrs=[["href",c]],t.markup="linkify",t.info="auto";e.push("text","",0).content=e.md.normalizeLinkText(o);const r=e.push("link_close","a",-1);r.markup="linkify",r.info="auto"}return e.pos+=o.length-s.length,!0}},4076:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(4439);function a(e,t){let r=e.pos;if(10!==e.src.charCodeAt(r))return!1;const a=e.pending.length-1,s=e.posMax;if(!t)if(a>=0&&32===e.pending.charCodeAt(a))if(a>=1&&32===e.pending.charCodeAt(a-1)){let t=a-1;for(;t>=1&&32===e.pending.charCodeAt(t-1);)t--;e.pending=e.pending.slice(0,t),e.push("hardbreak","br",0)}else e.pending=e.pending.slice(0,-1),e.push("softbreak","br",0);else e.push("softbreak","br",0);for(r++;r<s&&(0,n.isSpace)(e.src.charCodeAt(r));)r++;return e.pos=r,!0}},57:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(7997),a=r(4439);function s(e,t,r,n){this.src=e,this.env=r,this.md=t,this.tokens=n,this.tokens_meta=Array(n.length),this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache={},this.delimiters=[],this._prev_delimiters=[],this.backticks={},this.backticksScanned=!1,this.linkLevel=0}s.prototype.pushPending=function(){const e=new n.A("text","",0);return e.content=this.pending,e.level=this.pendingLevel,this.tokens.push(e),this.pending="",e},s.prototype.push=function(e,t,r){this.pending&&this.pushPending();const a=new n.A(e,t,r);let s=null;return r<0&&(this.level--,this.delimiters=this._prev_delimiters.pop()),a.level=this.level,r>0&&(this.level++,this._prev_delimiters.push(this.delimiters),this.delimiters=[],s={delimiters:this.delimiters}),this.pendingLevel=this.level,this.tokens.push(a),this.tokens_meta.push(s),a},s.prototype.scanDelims=function(e,t){const r=this.posMax,n=this.src.charCodeAt(e),s=e>0?this.src.charCodeAt(e-1):32;let i=e;for(;i<r&&this.src.charCodeAt(i)===n;)i++;const o=i-e,c=i<r?this.src.charCodeAt(i):32,l=(0,a.isMdAsciiPunct)(s)||(0,a.isPunctChar)(String.fromCharCode(s)),u=(0,a.isMdAsciiPunct)(c)||(0,a.isPunctChar)(String.fromCharCode(c)),d=(0,a.isWhiteSpace)(s),p=(0,a.isWhiteSpace)(c),h=!p&&(!u||d||l),m=!d&&(!l||p||u);return{can_open:h&&(t||!m||l),can_close:m&&(t||!h||u),length:o}},s.prototype.Token=n.A;const i=s},925:(e,t,r)=>{"use strict";function n(e,t){let r;const n=[],a=t.length;for(let s=0;s<a;s++){const a=t[s];if(126!==a.marker)continue;if(-1===a.end)continue;const i=t[a.end];r=e.tokens[a.token],r.type="s_open",r.tag="s",r.nesting=1,r.markup="~~",r.content="",r=e.tokens[i.token],r.type="s_close",r.tag="s",r.nesting=-1,r.markup="~~",r.content="","text"===e.tokens[i.token-1].type&&"~"===e.tokens[i.token-1].content&&n.push(i.token-1)}for(;n.length;){const t=n.pop();let a=t+1;for(;a<e.tokens.length&&"s_close"===e.tokens[a].type;)a++;a--,t!==a&&(r=e.tokens[a],e.tokens[a]=e.tokens[t],e.tokens[t]=r)}}r.d(t,{A:()=>a});const a={tokenize:function(e,t){const r=e.pos,n=e.src.charCodeAt(r);if(t)return!1;if(126!==n)return!1;const a=e.scanDelims(e.pos,!0);let s=a.length;const i=String.fromCharCode(n);if(s<2)return!1;let o;s%2&&(o=e.push("text","",0),o.content=i,s--);for(let t=0;t<s;t+=2)o=e.push("text","",0),o.content=i+i,e.delimiters.push({marker:n,length:0,token:e.tokens.length-1,end:-1,open:a.can_open,close:a.can_close});return e.pos+=a.length,!0},postProcess:function(e){const t=e.tokens_meta,r=e.tokens_meta.length;n(e,e.delimiters);for(let a=0;a<r;a++)t[a]&&t[a].delimiters&&n(e,t[a].delimiters)}}},8597:(e,t,r)=>{"use strict";function n(e){switch(e){case 10:case 33:case 35:case 36:case 37:case 38:case 42:case 43:case 45:case 58:case 60:case 61:case 62:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 125:case 126:return!0;default:return!1}}function a(e,t){let r=e.pos;for(;r<e.posMax&&!n(e.src.charCodeAt(r));)r++;return r!==e.pos&&(t||(e.pending+=e.src.slice(e.pos,r)),e.pos=r,!0)}r.d(t,{A:()=>a})},7997:(e,t,r)=>{"use strict";function n(e,t,r){this.type=e,this.tag=t,this.attrs=null,this.map=null,this.nesting=r,this.level=0,this.children=null,this.content="",this.markup="",this.info="",this.meta=null,this.block=!1,this.hidden=!1}r.d(t,{A:()=>a}),n.prototype.attrIndex=function(e){if(!this.attrs)return-1;const t=this.attrs;for(let r=0,n=t.length;r<n;r++)if(t[r][0]===e)return r;return-1},n.prototype.attrPush=function(e){this.attrs?this.attrs.push(e):this.attrs=[e]},n.prototype.attrSet=function(e,t){const r=this.attrIndex(e),n=[e,t];r<0?this.attrPush(n):this.attrs[r]=n},n.prototype.attrGet=function(e){const t=this.attrIndex(e);let r=null;return t>=0&&(r=this.attrs[t][1]),r},n.prototype.attrJoin=function(e,t){const r=this.attrIndex(e);r<0?this.attrPush([e,t]):this.attrs[r][1]=this.attrs[r][1]+" "+t};const a=n},5315:(e,t,r)=>{"use strict";r.r(t),r.d(t,{decode:()=>n.A,encode:()=>a.A,format:()=>s.A,parse:()=>i.A});var n=r(1227),a=r(963),s=r(5840),i=r(6786)},1227:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});const n={};function a(e,t){"string"!=typeof t&&(t=a.defaultChars);const r=function(e){let t=n[e];if(t)return t;t=n[e]=[];for(let e=0;e<128;e++){const r=String.fromCharCode(e);t.push(r)}for(let r=0;r<e.length;r++){const n=e.charCodeAt(r);t[n]="%"+("0"+n.toString(16).toUpperCase()).slice(-2)}return t}(t);return e.replace(/(%[a-f0-9]{2})+/gi,(function(e){let t="";for(let n=0,a=e.length;n<a;n+=3){const s=parseInt(e.slice(n+1,n+3),16);if(s<128)t+=r[s];else{if(192==(224&s)&&n+3<a){const r=parseInt(e.slice(n+4,n+6),16);if(128==(192&r)){const e=s<<6&1984|63&r;t+=e<128?"��":String.fromCharCode(e),n+=3;continue}}if(224==(240&s)&&n+6<a){const r=parseInt(e.slice(n+4,n+6),16),a=parseInt(e.slice(n+7,n+9),16);if(128==(192&r)&&128==(192&a)){const e=s<<12&61440|r<<6&4032|63&a;t+=e<2048||e>=55296&&e<=57343?"���":String.fromCharCode(e),n+=6;continue}}if(240==(248&s)&&n+9<a){const r=parseInt(e.slice(n+4,n+6),16),a=parseInt(e.slice(n+7,n+9),16),i=parseInt(e.slice(n+10,n+12),16);if(128==(192&r)&&128==(192&a)&&128==(192&i)){let e=s<<18&1835008|r<<12&258048|a<<6&4032|63&i;e<65536||e>1114111?t+="����":(e-=65536,t+=String.fromCharCode(55296+(e>>10),56320+(1023&e))),n+=9;continue}}t+="�"}}return t}))}a.defaultChars=";/?:@&=+$,#",a.componentChars="";const s=a},963:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});const n={};function a(e,t,r){"string"!=typeof t&&(r=t,t=a.defaultChars),void 0===r&&(r=!0);const s=function(e){let t=n[e];if(t)return t;t=n[e]=[];for(let e=0;e<128;e++){const r=String.fromCharCode(e);/^[0-9a-z]$/i.test(r)?t.push(r):t.push("%"+("0"+e.toString(16).toUpperCase()).slice(-2))}for(let r=0;r<e.length;r++)t[e.charCodeAt(r)]=e[r];return t}(t);let i="";for(let t=0,n=e.length;t<n;t++){const a=e.charCodeAt(t);if(r&&37===a&&t+2<n&&/^[0-9a-f]{2}$/i.test(e.slice(t+1,t+3)))i+=e.slice(t,t+3),t+=2;else if(a<128)i+=s[a];else if(a>=55296&&a<=57343){if(a>=55296&&a<=56319&&t+1<n){const r=e.charCodeAt(t+1);if(r>=56320&&r<=57343){i+=encodeURIComponent(e[t]+e[t+1]),t++;continue}}i+="%EF%BF%BD"}else i+=encodeURIComponent(e[t])}return i}a.defaultChars=";/?:@&=+$,-_.!~*'()#",a.componentChars="-_.!~*'()";const s=a},5840:(e,t,r)=>{"use strict";function n(e){let t="";return t+=e.protocol||"",t+=e.slashes?"//":"",t+=e.auth?e.auth+"@":"",e.hostname&&-1!==e.hostname.indexOf(":")?t+="["+e.hostname+"]":t+=e.hostname||"",t+=e.port?":"+e.port:"",t+=e.pathname||"",t+=e.search||"",t+=e.hash||"",t}r.d(t,{A:()=>n})},6786:(e,t,r)=>{"use strict";function n(){this.protocol=null,this.slashes=null,this.auth=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.pathname=null}r.d(t,{A:()=>f});const a=/^([a-z0-9.+-]+:)/i,s=/:[0-9]*$/,i=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,o=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),c=["'"].concat(o),l=["%","/","?",";","#"].concat(c),u=["/","?","#"],d=/^[+a-z0-9A-Z_-]{0,63}$/,p=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,h={javascript:!0,"javascript:":!0},m={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0};n.prototype.parse=function(e,t){let r,n,s,o=e;if(o=o.trim(),!t&&1===e.split("#").length){const e=i.exec(o);if(e)return this.pathname=e[1],e[2]&&(this.search=e[2]),this}let c=a.exec(o);if(c&&(c=c[0],r=c.toLowerCase(),this.protocol=c,o=o.substr(c.length)),(t||c||o.match(/^\/\/[^@\/]+@[^@\/]+/))&&(s="//"===o.substr(0,2),!s||c&&h[c]||(o=o.substr(2),this.slashes=!0)),!h[c]&&(s||c&&!m[c])){let e,t,r=-1;for(let e=0;e<u.length;e++)n=o.indexOf(u[e]),-1!==n&&(-1===r||n<r)&&(r=n);t=-1===r?o.lastIndexOf("@"):o.lastIndexOf("@",r),-1!==t&&(e=o.slice(0,t),o=o.slice(t+1),this.auth=e),r=-1;for(let e=0;e<l.length;e++)n=o.indexOf(l[e]),-1!==n&&(-1===r||n<r)&&(r=n);-1===r&&(r=o.length),":"===o[r-1]&&r--;const a=o.slice(0,r);o=o.slice(r),this.parseHost(a),this.hostname=this.hostname||"";const s="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!s){const e=this.hostname.split(/\./);for(let t=0,r=e.length;t<r;t++){const r=e[t];if(r&&!r.match(d)){let n="";for(let e=0,t=r.length;e<t;e++)r.charCodeAt(e)>127?n+="x":n+=r[e];if(!n.match(d)){const n=e.slice(0,t),a=e.slice(t+1),s=r.match(p);s&&(n.push(s[1]),a.unshift(s[2])),a.length&&(o=a.join(".")+o),this.hostname=n.join(".");break}}}}this.hostname.length>255&&(this.hostname=""),s&&(this.hostname=this.hostname.substr(1,this.hostname.length-2))}const f=o.indexOf("#");-1!==f&&(this.hash=o.substr(f),o=o.slice(0,f));const g=o.indexOf("?");return-1!==g&&(this.search=o.substr(g),o=o.slice(0,g)),o&&(this.pathname=o),m[r]&&this.hostname&&!this.pathname&&(this.pathname=""),this},n.prototype.parseHost=function(e){let t=s.exec(e);t&&(t=t[0],":"!==t&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)};const f=function(e,t){if(e&&e instanceof n)return e;const r=new n;return r.parse(e,t),r}},6221:(e,t,r)=>{"use strict";r.d(t,{X:()=>C,h:()=>w});var n,a=r(1609),s=Object.defineProperty,i=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,l=(e,t,r)=>t in e?s(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,u=(e,t)=>{for(var r in t||(t={}))o.call(t,r)&&l(e,r,t[r]);if(i)for(var r of i(t))c.call(t,r)&&l(e,r,t[r]);return e},d=(e,t)=>{var r={};for(var n in e)o.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&i)for(var n of i(e))t.indexOf(n)<0&&c.call(e,n)&&(r[n]=e[n]);return r};(e=>{const t=class t{constructor(e,r,n,s){if(this.version=e,this.errorCorrectionLevel=r,this.modules=[],this.isFunction=[],e<t.MIN_VERSION||e>t.MAX_VERSION)throw new RangeError("Version value out of range");if(s<-1||s>7)throw new RangeError("Mask value out of range");this.size=4*e+17;let i=[];for(let e=0;e<this.size;e++)i.push(!1);for(let e=0;e<this.size;e++)this.modules.push(i.slice()),this.isFunction.push(i.slice());this.drawFunctionPatterns();const o=this.addEccAndInterleave(n);if(this.drawCodewords(o),-1==s){let e=1e9;for(let t=0;t<8;t++){this.applyMask(t),this.drawFormatBits(t);const r=this.getPenaltyScore();r<e&&(s=t,e=r),this.applyMask(t)}}a(0<=s&&s<=7),this.mask=s,this.applyMask(s),this.drawFormatBits(s),this.isFunction=[]}static encodeText(r,n){const a=e.QrSegment.makeSegments(r);return t.encodeSegments(a,n)}static encodeBinary(r,n){const a=e.QrSegment.makeBytes(r);return t.encodeSegments([a],n)}static encodeSegments(e,n,s=1,o=40,c=-1,l=!0){if(!(t.MIN_VERSION<=s&&s<=o&&o<=t.MAX_VERSION)||c<-1||c>7)throw new RangeError("Invalid value");let u,d;for(u=s;;u++){const r=8*t.getNumDataCodewords(u,n),a=i.getTotalBits(e,u);if(a<=r){d=a;break}if(u>=o)throw new RangeError("Data too long")}for(const e of[t.Ecc.MEDIUM,t.Ecc.QUARTILE,t.Ecc.HIGH])l&&d<=8*t.getNumDataCodewords(u,e)&&(n=e);let p=[];for(const t of e){r(t.mode.modeBits,4,p),r(t.numChars,t.mode.numCharCountBits(u),p);for(const e of t.getData())p.push(e)}a(p.length==d);const h=8*t.getNumDataCodewords(u,n);a(p.length<=h),r(0,Math.min(4,h-p.length),p),r(0,(8-p.length%8)%8,p),a(p.length%8==0);for(let e=236;p.length<h;e^=253)r(e,8,p);let m=[];for(;8*m.length<p.length;)m.push(0);return p.forEach(((e,t)=>m[t>>>3]|=e<<7-(7&t))),new t(u,n,m,c)}getModule(e,t){return 0<=e&&e<this.size&&0<=t&&t<this.size&&this.modules[t][e]}getModules(){return this.modules}drawFunctionPatterns(){for(let e=0;e<this.size;e++)this.setFunctionModule(6,e,e%2==0),this.setFunctionModule(e,6,e%2==0);this.drawFinderPattern(3,3),this.drawFinderPattern(this.size-4,3),this.drawFinderPattern(3,this.size-4);const e=this.getAlignmentPatternPositions(),t=e.length;for(let r=0;r<t;r++)for(let n=0;n<t;n++)0==r&&0==n||0==r&&n==t-1||r==t-1&&0==n||this.drawAlignmentPattern(e[r],e[n]);this.drawFormatBits(0),this.drawVersion()}drawFormatBits(e){const t=this.errorCorrectionLevel.formatBits<<3|e;let r=t;for(let e=0;e<10;e++)r=r<<1^1335*(r>>>9);const s=21522^(t<<10|r);a(s>>>15==0);for(let e=0;e<=5;e++)this.setFunctionModule(8,e,n(s,e));this.setFunctionModule(8,7,n(s,6)),this.setFunctionModule(8,8,n(s,7)),this.setFunctionModule(7,8,n(s,8));for(let e=9;e<15;e++)this.setFunctionModule(14-e,8,n(s,e));for(let e=0;e<8;e++)this.setFunctionModule(this.size-1-e,8,n(s,e));for(let e=8;e<15;e++)this.setFunctionModule(8,this.size-15+e,n(s,e));this.setFunctionModule(8,this.size-8,!0)}drawVersion(){if(this.version<7)return;let e=this.version;for(let t=0;t<12;t++)e=e<<1^7973*(e>>>11);const t=this.version<<12|e;a(t>>>18==0);for(let e=0;e<18;e++){const r=n(t,e),a=this.size-11+e%3,s=Math.floor(e/3);this.setFunctionModule(a,s,r),this.setFunctionModule(s,a,r)}}drawFinderPattern(e,t){for(let r=-4;r<=4;r++)for(let n=-4;n<=4;n++){const a=Math.max(Math.abs(n),Math.abs(r)),s=e+n,i=t+r;0<=s&&s<this.size&&0<=i&&i<this.size&&this.setFunctionModule(s,i,2!=a&&4!=a)}}drawAlignmentPattern(e,t){for(let r=-2;r<=2;r++)for(let n=-2;n<=2;n++)this.setFunctionModule(e+n,t+r,1!=Math.max(Math.abs(n),Math.abs(r)))}setFunctionModule(e,t,r){this.modules[t][e]=r,this.isFunction[t][e]=!0}addEccAndInterleave(e){const r=this.version,n=this.errorCorrectionLevel;if(e.length!=t.getNumDataCodewords(r,n))throw new RangeError("Invalid argument");const s=t.NUM_ERROR_CORRECTION_BLOCKS[n.ordinal][r],i=t.ECC_CODEWORDS_PER_BLOCK[n.ordinal][r],o=Math.floor(t.getNumRawDataModules(r)/8),c=s-o%s,l=Math.floor(o/s);let u=[];const d=t.reedSolomonComputeDivisor(i);for(let r=0,n=0;r<s;r++){let a=e.slice(n,n+l-i+(r<c?0:1));n+=a.length;const s=t.reedSolomonComputeRemainder(a,d);r<c&&a.push(0),u.push(a.concat(s))}let p=[];for(let e=0;e<u[0].length;e++)u.forEach(((t,r)=>{(e!=l-i||r>=c)&&p.push(t[e])}));return a(p.length==o),p}drawCodewords(e){if(e.length!=Math.floor(t.getNumRawDataModules(this.version)/8))throw new RangeError("Invalid argument");let r=0;for(let t=this.size-1;t>=1;t-=2){6==t&&(t=5);for(let a=0;a<this.size;a++)for(let s=0;s<2;s++){const i=t-s,o=!(t+1&2)?this.size-1-a:a;!this.isFunction[o][i]&&r<8*e.length&&(this.modules[o][i]=n(e[r>>>3],7-(7&r)),r++)}}a(r==8*e.length)}applyMask(e){if(e<0||e>7)throw new RangeError("Mask value out of range");for(let t=0;t<this.size;t++)for(let r=0;r<this.size;r++){let n;switch(e){case 0:n=(r+t)%2==0;break;case 1:n=t%2==0;break;case 2:n=r%3==0;break;case 3:n=(r+t)%3==0;break;case 4:n=(Math.floor(r/3)+Math.floor(t/2))%2==0;break;case 5:n=r*t%2+r*t%3==0;break;case 6:n=(r*t%2+r*t%3)%2==0;break;case 7:n=((r+t)%2+r*t%3)%2==0;break;default:throw new Error("Unreachable")}!this.isFunction[t][r]&&n&&(this.modules[t][r]=!this.modules[t][r])}}getPenaltyScore(){let e=0;for(let r=0;r<this.size;r++){let n=!1,a=0,s=[0,0,0,0,0,0,0];for(let i=0;i<this.size;i++)this.modules[r][i]==n?(a++,5==a?e+=t.PENALTY_N1:a>5&&e++):(this.finderPenaltyAddHistory(a,s),n||(e+=this.finderPenaltyCountPatterns(s)*t.PENALTY_N3),n=this.modules[r][i],a=1);e+=this.finderPenaltyTerminateAndCount(n,a,s)*t.PENALTY_N3}for(let r=0;r<this.size;r++){let n=!1,a=0,s=[0,0,0,0,0,0,0];for(let i=0;i<this.size;i++)this.modules[i][r]==n?(a++,5==a?e+=t.PENALTY_N1:a>5&&e++):(this.finderPenaltyAddHistory(a,s),n||(e+=this.finderPenaltyCountPatterns(s)*t.PENALTY_N3),n=this.modules[i][r],a=1);e+=this.finderPenaltyTerminateAndCount(n,a,s)*t.PENALTY_N3}for(let r=0;r<this.size-1;r++)for(let n=0;n<this.size-1;n++){const a=this.modules[r][n];a==this.modules[r][n+1]&&a==this.modules[r+1][n]&&a==this.modules[r+1][n+1]&&(e+=t.PENALTY_N2)}let r=0;for(const e of this.modules)r=e.reduce(((e,t)=>e+(t?1:0)),r);const n=this.size*this.size,s=Math.ceil(Math.abs(20*r-10*n)/n)-1;return a(0<=s&&s<=9),e+=s*t.PENALTY_N4,a(0<=e&&e<=2568888),e}getAlignmentPatternPositions(){if(1==this.version)return[];{const e=Math.floor(this.version/7)+2,t=32==this.version?26:2*Math.ceil((4*this.version+4)/(2*e-2));let r=[6];for(let n=this.size-7;r.length<e;n-=t)r.splice(1,0,n);return r}}static getNumRawDataModules(e){if(e<t.MIN_VERSION||e>t.MAX_VERSION)throw new RangeError("Version number out of range");let r=(16*e+128)*e+64;if(e>=2){const t=Math.floor(e/7)+2;r-=(25*t-10)*t-55,e>=7&&(r-=36)}return a(208<=r&&r<=29648),r}static getNumDataCodewords(e,r){return Math.floor(t.getNumRawDataModules(e)/8)-t.ECC_CODEWORDS_PER_BLOCK[r.ordinal][e]*t.NUM_ERROR_CORRECTION_BLOCKS[r.ordinal][e]}static reedSolomonComputeDivisor(e){if(e<1||e>255)throw new RangeError("Degree out of range");let r=[];for(let t=0;t<e-1;t++)r.push(0);r.push(1);let n=1;for(let a=0;a<e;a++){for(let e=0;e<r.length;e++)r[e]=t.reedSolomonMultiply(r[e],n),e+1<r.length&&(r[e]^=r[e+1]);n=t.reedSolomonMultiply(n,2)}return r}static reedSolomonComputeRemainder(e,r){let n=r.map((e=>0));for(const a of e){const e=a^n.shift();n.push(0),r.forEach(((r,a)=>n[a]^=t.reedSolomonMultiply(r,e)))}return n}static reedSolomonMultiply(e,t){if(e>>>8!=0||t>>>8!=0)throw new RangeError("Byte out of range");let r=0;for(let n=7;n>=0;n--)r=r<<1^285*(r>>>7),r^=(t>>>n&1)*e;return a(r>>>8==0),r}finderPenaltyCountPatterns(e){const t=e[1];a(t<=3*this.size);const r=t>0&&e[2]==t&&e[3]==3*t&&e[4]==t&&e[5]==t;return(r&&e[0]>=4*t&&e[6]>=t?1:0)+(r&&e[6]>=4*t&&e[0]>=t?1:0)}finderPenaltyTerminateAndCount(e,t,r){return e&&(this.finderPenaltyAddHistory(t,r),t=0),t+=this.size,this.finderPenaltyAddHistory(t,r),this.finderPenaltyCountPatterns(r)}finderPenaltyAddHistory(e,t){0==t[0]&&(e+=this.size),t.pop(),t.unshift(e)}};t.MIN_VERSION=1,t.MAX_VERSION=40,t.PENALTY_N1=3,t.PENALTY_N2=3,t.PENALTY_N3=40,t.PENALTY_N4=10,t.ECC_CODEWORDS_PER_BLOCK=[[-1,7,10,15,20,26,18,20,24,30,18,20,24,26,30,22,24,28,30,28,28,28,28,30,30,26,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,10,16,26,18,24,16,18,22,22,26,30,22,22,24,24,28,28,26,26,26,26,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28],[-1,13,22,18,26,18,24,18,22,20,24,28,26,24,20,30,24,28,28,26,30,28,30,30,30,30,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,17,28,22,16,22,28,26,26,24,28,24,28,22,24,24,30,28,28,26,28,30,24,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30]],t.NUM_ERROR_CORRECTION_BLOCKS=[[-1,1,1,1,1,1,2,2,2,2,4,4,4,4,4,6,6,6,6,7,8,8,9,9,10,12,12,12,13,14,15,16,17,18,19,19,20,21,22,24,25],[-1,1,1,1,2,2,4,4,4,5,5,5,8,9,9,10,10,11,13,14,16,17,17,18,20,21,23,25,26,28,29,31,33,35,37,38,40,43,45,47,49],[-1,1,1,2,2,4,4,6,6,8,8,8,10,12,16,12,17,16,18,21,20,23,23,25,27,29,34,34,35,38,40,43,45,48,51,53,56,59,62,65,68],[-1,1,1,2,4,4,4,5,6,8,8,11,11,16,16,18,16,19,21,25,25,25,34,30,32,35,37,40,42,45,48,51,54,57,60,63,66,70,74,77,81]];function r(e,t,r){if(t<0||t>31||e>>>t!=0)throw new RangeError("Value out of range");for(let n=t-1;n>=0;n--)r.push(e>>>n&1)}function n(e,t){return!!(e>>>t&1)}function a(e){if(!e)throw new Error("Assertion error")}e.QrCode=t;const s=class e{constructor(e,t,r){if(this.mode=e,this.numChars=t,this.bitData=r,t<0)throw new RangeError("Invalid argument");this.bitData=r.slice()}static makeBytes(t){let n=[];for(const e of t)r(e,8,n);return new e(e.Mode.BYTE,t.length,n)}static makeNumeric(t){if(!e.isNumeric(t))throw new RangeError("String contains non-numeric characters");let n=[];for(let e=0;e<t.length;){const a=Math.min(t.length-e,3);r(parseInt(t.substring(e,e+a),10),3*a+1,n),e+=a}return new e(e.Mode.NUMERIC,t.length,n)}static makeAlphanumeric(t){if(!e.isAlphanumeric(t))throw new RangeError("String contains unencodable characters in alphanumeric mode");let n,a=[];for(n=0;n+2<=t.length;n+=2){let s=45*e.ALPHANUMERIC_CHARSET.indexOf(t.charAt(n));s+=e.ALPHANUMERIC_CHARSET.indexOf(t.charAt(n+1)),r(s,11,a)}return n<t.length&&r(e.ALPHANUMERIC_CHARSET.indexOf(t.charAt(n)),6,a),new e(e.Mode.ALPHANUMERIC,t.length,a)}static makeSegments(t){return""==t?[]:e.isNumeric(t)?[e.makeNumeric(t)]:e.isAlphanumeric(t)?[e.makeAlphanumeric(t)]:[e.makeBytes(e.toUtf8ByteArray(t))]}static makeEci(t){let n=[];if(t<0)throw new RangeError("ECI assignment value out of range");if(t<128)r(t,8,n);else if(t<16384)r(2,2,n),r(t,14,n);else{if(!(t<1e6))throw new RangeError("ECI assignment value out of range");r(6,3,n),r(t,21,n)}return new e(e.Mode.ECI,0,n)}static isNumeric(t){return e.NUMERIC_REGEX.test(t)}static isAlphanumeric(t){return e.ALPHANUMERIC_REGEX.test(t)}getData(){return this.bitData.slice()}static getTotalBits(e,t){let r=0;for(const n of e){const e=n.mode.numCharCountBits(t);if(n.numChars>=1<<e)return 1/0;r+=4+e+n.bitData.length}return r}static toUtf8ByteArray(e){e=encodeURI(e);let t=[];for(let r=0;r<e.length;r++)"%"!=e.charAt(r)?t.push(e.charCodeAt(r)):(t.push(parseInt(e.substring(r+1,r+3),16)),r+=2);return t}};s.NUMERIC_REGEX=/^[0-9]*$/,s.ALPHANUMERIC_REGEX=/^[A-Z0-9 $%*+.\/:-]*$/,s.ALPHANUMERIC_CHARSET="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:";let i=s;e.QrSegment=s})(n||(n={})),(e=>{let t;(e=>{const t=class{constructor(e,t){this.ordinal=e,this.formatBits=t}};t.LOW=new t(0,1),t.MEDIUM=new t(1,0),t.QUARTILE=new t(2,3),t.HIGH=new t(3,2);e.Ecc=t})(t=e.QrCode||(e.QrCode={}))})(n||(n={})),(e=>{let t;(e=>{const t=class{constructor(e,t){this.modeBits=e,this.numBitsCharCount=t}numCharCountBits(e){return this.numBitsCharCount[Math.floor((e+7)/17)]}};t.NUMERIC=new t(1,[10,12,14]),t.ALPHANUMERIC=new t(2,[9,11,13]),t.BYTE=new t(4,[8,16,16]),t.KANJI=new t(8,[8,10,12]),t.ECI=new t(7,[0,0,0]);e.Mode=t})(t=e.QrSegment||(e.QrSegment={}))})(n||(n={}));var p=n,h={L:p.QrCode.Ecc.LOW,M:p.QrCode.Ecc.MEDIUM,Q:p.QrCode.Ecc.QUARTILE,H:p.QrCode.Ecc.HIGH},m=128,f="L",g="#FFFFFF",_="#000000",k=!1,y=1;function v(e,t=0){const r=[];return e.forEach((function(e,n){let a=null;e.forEach((function(s,i){if(!s&&null!==a)return r.push(`M${a+t} ${n+t}h${i-a}v1H${a+t}z`),void(a=null);if(i!==e.length-1)s&&null===a&&(a=i);else{if(!s)return;null===a?r.push(`M${i+t},${n+t} h1v1H${i+t}z`):r.push(`M${a+t},${n+t} h${i+1-a}v1H${a+t}z`)}}))})),r.join("")}function E(e,t){return e.slice().map(((e,r)=>r<t.y||r>=t.y+t.h?e:e.map(((e,r)=>(r<t.x||r>=t.x+t.w)&&e))))}function b({value:e,level:t,minVersion:r,includeMargin:n,marginSize:s,imageSettings:i,size:o,boostLevel:c}){let l=a.useMemo((()=>{const n=(Array.isArray(e)?e:[e]).reduce(((e,t)=>(e.push(...p.QrSegment.makeSegments(t)),e)),[]);return p.QrCode.encodeSegments(n,h[t],r,void 0,void 0,c)}),[e,t,r,c]);const{cells:u,margin:d,numCells:m,calculatedImageSettings:f}=a.useMemo((()=>{let e=l.getModules();const t=function(e,t){return null!=t?Math.max(Math.floor(t),0):e?4:0}(n,s),r=e.length+2*t,a=function(e,t,r,n){if(null==n)return null;const a=e.length+2*r,s=Math.floor(.1*t),i=a/t,o=(n.width||s)*i,c=(n.height||s)*i,l=null==n.x?e.length/2-o/2:n.x*i,u=null==n.y?e.length/2-c/2:n.y*i,d=null==n.opacity?1:n.opacity;let p=null;if(n.excavate){let e=Math.floor(l),t=Math.floor(u);p={x:e,y:t,w:Math.ceil(o+l-e),h:Math.ceil(c+u-t)}}return{x:l,y:u,h:c,w:o,excavation:p,opacity:d,crossOrigin:n.crossOrigin}}(e,o,t,i);return{cells:e,margin:t,numCells:r,calculatedImageSettings:a}}),[l,o,i,n,s]);return{qrcode:l,margin:d,cells:u,numCells:m,calculatedImageSettings:f}}var A=function(){try{(new Path2D).addPath(new Path2D)}catch(e){return!1}return!0}(),C=a.forwardRef((function(e,t){const r=e,{value:n,size:s=m,level:i=f,bgColor:o=g,fgColor:c=_,includeMargin:l=k,minVersion:p=y,boostLevel:h,marginSize:C,imageSettings:w}=r,x=d(r,["value","size","level","bgColor","fgColor","includeMargin","minVersion","boostLevel","marginSize","imageSettings"]),{style:S}=x,D=d(x,["style"]),F=null==w?void 0:w.src,P=a.useRef(null),R=a.useRef(null),L=a.useCallback((e=>{P.current=e,"function"==typeof t?t(e):t&&(t.current=e)}),[t]),[I,j]=a.useState(!1),{margin:T,cells:N,numCells:M,calculatedImageSettings:B}=b({value:n,level:i,minVersion:p,boostLevel:h,includeMargin:l,marginSize:C,imageSettings:w,size:s});a.useEffect((()=>{if(null!=P.current){const e=P.current,t=e.getContext("2d");if(!t)return;let r=N;const n=R.current,a=null!=B&&null!==n&&n.complete&&0!==n.naturalHeight&&0!==n.naturalWidth;a&&null!=B.excavation&&(r=E(N,B.excavation));const i=window.devicePixelRatio||1;e.height=e.width=s*i;const l=s/M*i;t.scale(l,l),t.fillStyle=o,t.fillRect(0,0,M,M),t.fillStyle=c,A?t.fill(new Path2D(v(r,T))):N.forEach((function(e,r){e.forEach((function(e,n){e&&t.fillRect(n+T,r+T,1,1)}))})),B&&(t.globalAlpha=B.opacity),a&&t.drawImage(n,B.x+T,B.y+T,B.w,B.h)}})),a.useEffect((()=>{j(!1)}),[F]);const O=u({height:s,width:s},S);let q=null;return null!=F&&(q=a.createElement("img",{src:F,key:F,style:{display:"none"},onLoad:()=>{j(!0)},ref:R,crossOrigin:null==B?void 0:B.crossOrigin})),a.createElement(a.Fragment,null,a.createElement("canvas",u({style:O,height:s,width:s,ref:L,role:"img"},D)),q)}));C.displayName="QRCodeCanvas";var w=a.forwardRef((function(e,t){const r=e,{value:n,size:s=m,level:i=f,bgColor:o=g,fgColor:c=_,includeMargin:l=k,minVersion:p=y,boostLevel:h,title:A,marginSize:C,imageSettings:w}=r,x=d(r,["value","size","level","bgColor","fgColor","includeMargin","minVersion","boostLevel","title","marginSize","imageSettings"]),{margin:S,cells:D,numCells:F,calculatedImageSettings:P}=b({value:n,level:i,minVersion:p,boostLevel:h,includeMargin:l,marginSize:C,imageSettings:w,size:s});let R=D,L=null;null!=w&&null!=P&&(null!=P.excavation&&(R=E(D,P.excavation)),L=a.createElement("image",{href:w.src,height:P.h,width:P.w,x:P.x+S,y:P.y+S,preserveAspectRatio:"none",opacity:P.opacity,crossOrigin:P.crossOrigin}));const I=v(R,S);return a.createElement("svg",u({height:s,width:s,viewBox:`0 0 ${F} ${F}`,ref:t,role:"img"},x),!!A&&a.createElement("title",null,A),a.createElement("path",{fill:o,d:`M0,0 h${F}v${F}H0z`,shapeRendering:"crispEdges"}),a.createElement("path",{fill:c,d:I,shapeRendering:"crispEdges"}),L)}));w.displayName="QRCodeSVG"},786:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n=/[\0-\x1F\x7F-\x9F]/},4935:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n=/[\xAD\u0600-\u0605\u061C\u06DD\u070F\u0890\u0891\u08E2\u180E\u200B-\u200F\u202A-\u202E\u2060-\u2064\u2066-\u206F\uFEFF\uFFF9-\uFFFB]|\uD804[\uDCBD\uDCCD]|\uD80D[\uDC30-\uDC3F]|\uD82F[\uDCA0-\uDCA3]|\uD834[\uDD73-\uDD7A]|\uDB40[\uDC01\uDC20-\uDC7F]/},804:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n=/[!-#%-\*,-\/:;\?@\[-\]_\{\}\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD803[\uDEAD\uDF55-\uDF59\uDF86-\uDF89]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDC4B-\uDC4F\uDC5A\uDC5B\uDC5D\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDE60-\uDE6C\uDEB9\uDF3C-\uDF3E]|\uD806[\uDC3B\uDD44-\uDD46\uDDE2\uDE3F-\uDE46\uDE9A-\uDE9C\uDE9E-\uDEA2\uDF00-\uDF09]|\uD807[\uDC41-\uDC45\uDC70\uDC71\uDEF7\uDEF8\uDF43-\uDF4F\uDFFF]|\uD809[\uDC70-\uDC74]|\uD80B[\uDFF1\uDFF2]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD81B[\uDE97-\uDE9A\uDFE2]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]|\uD83A[\uDD5E\uDD5F]/},7795:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n=/[\$\+<->\^`\|~\xA2-\xA6\xA8\xA9\xAC\xAE-\xB1\xB4\xB8\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0384\u0385\u03F6\u0482\u058D-\u058F\u0606-\u0608\u060B\u060E\u060F\u06DE\u06E9\u06FD\u06FE\u07F6\u07FE\u07FF\u0888\u09F2\u09F3\u09FA\u09FB\u0AF1\u0B70\u0BF3-\u0BFA\u0C7F\u0D4F\u0D79\u0E3F\u0F01-\u0F03\u0F13\u0F15-\u0F17\u0F1A-\u0F1F\u0F34\u0F36\u0F38\u0FBE-\u0FC5\u0FC7-\u0FCC\u0FCE\u0FCF\u0FD5-\u0FD8\u109E\u109F\u1390-\u1399\u166D\u17DB\u1940\u19DE-\u19FF\u1B61-\u1B6A\u1B74-\u1B7C\u1FBD\u1FBF-\u1FC1\u1FCD-\u1FCF\u1FDD-\u1FDF\u1FED-\u1FEF\u1FFD\u1FFE\u2044\u2052\u207A-\u207C\u208A-\u208C\u20A0-\u20C0\u2100\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F\u218A\u218B\u2190-\u2307\u230C-\u2328\u232B-\u2426\u2440-\u244A\u249C-\u24E9\u2500-\u2767\u2794-\u27C4\u27C7-\u27E5\u27F0-\u2982\u2999-\u29D7\u29DC-\u29FB\u29FE-\u2B73\u2B76-\u2B95\u2B97-\u2BFF\u2CE5-\u2CEA\u2E50\u2E51\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u2FF0-\u2FFF\u3004\u3012\u3013\u3020\u3036\u3037\u303E\u303F\u309B\u309C\u3190\u3191\u3196-\u319F\u31C0-\u31E3\u31EF\u3200-\u321E\u322A-\u3247\u3250\u3260-\u327F\u328A-\u32B0\u32C0-\u33FF\u4DC0-\u4DFF\uA490-\uA4C6\uA700-\uA716\uA720\uA721\uA789\uA78A\uA828-\uA82B\uA836-\uA839\uAA77-\uAA79\uAB5B\uAB6A\uAB6B\uFB29\uFBB2-\uFBC2\uFD40-\uFD4F\uFDCF\uFDFC-\uFDFF\uFE62\uFE64-\uFE66\uFE69\uFF04\uFF0B\uFF1C-\uFF1E\uFF3E\uFF40\uFF5C\uFF5E\uFFE0-\uFFE6\uFFE8-\uFFEE\uFFFC\uFFFD]|\uD800[\uDD37-\uDD3F\uDD79-\uDD89\uDD8C-\uDD8E\uDD90-\uDD9C\uDDA0\uDDD0-\uDDFC]|\uD802[\uDC77\uDC78\uDEC8]|\uD805\uDF3F|\uD807[\uDFD5-\uDFF1]|\uD81A[\uDF3C-\uDF3F\uDF45]|\uD82F\uDC9C|\uD833[\uDF50-\uDFC3]|\uD834[\uDC00-\uDCF5\uDD00-\uDD26\uDD29-\uDD64\uDD6A-\uDD6C\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDDEA\uDE00-\uDE41\uDE45\uDF00-\uDF56]|\uD835[\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85\uDE86]|\uD838[\uDD4F\uDEFF]|\uD83B[\uDCAC\uDCB0\uDD2E\uDEF0\uDEF1]|\uD83C[\uDC00-\uDC2B\uDC30-\uDC93\uDCA0-\uDCAE\uDCB1-\uDCBF\uDCC1-\uDCCF\uDCD1-\uDCF5\uDD0D-\uDDAD\uDDE6-\uDE02\uDE10-\uDE3B\uDE40-\uDE48\uDE50\uDE51\uDE60-\uDE65\uDF00-\uDFFF]|\uD83D[\uDC00-\uDED7\uDEDC-\uDEEC\uDEF0-\uDEFC\uDF00-\uDF76\uDF7B-\uDFD9\uDFE0-\uDFEB\uDFF0]|\uD83E[\uDC00-\uDC0B\uDC10-\uDC47\uDC50-\uDC59\uDC60-\uDC87\uDC90-\uDCAD\uDCB0\uDCB1\uDD00-\uDE53\uDE60-\uDE6D\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC5\uDECE-\uDEDB\uDEE0-\uDEE8\uDEF0-\uDEF8\uDF00-\uDF92\uDF94-\uDFCA]/},3506:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n=/[ \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/},8407:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Any:()=>n.A,Cc:()=>a.A,Cf:()=>s.A,P:()=>i.A,S:()=>o.A,Z:()=>c.A});var n=r(905),a=r(786),s=r(4935),i=r(804),o=r(7795),c=r(3506)},905:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n=/[\0-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/},8377:e=>{"use strict";e.exports=JSON.parse('{"T":{"White":"#fff","Black":"#000","Gray 0":"#f6f7f7","Gray 5":"#dcdcde","Gray 10":"#c3c4c7","Gray 20":"#a7aaad","Gray 30":"#8c8f94","Gray 40":"#787c82","Gray 50":"#646970","Gray 60":"#50575e","Gray 70":"#3c434a","Gray 80":"#2c3338","Gray 90":"#1d2327","Gray 100":"#101517","Gray":"#646970","Blue 0":"#fbfcfe","Blue 5":"#f7f8fe","Blue 10":"#d6ddf9","Blue 20":"#adbaf3","Blue 30":"#7b90ff","Blue 40":"#546ff3","Blue 50":"#3858e9","Blue 60":"#2a46ce","Blue 70":"#1d35b4","Blue 80":"#1f3286","Blue 90":"#14215a","Blue 100":"#0a112d","Blue":"#3858e9","WordPress Blue 0":"#fbfcfe","WordPress Blue 5":"#f7f8fe","WordPress Blue 10":"#d6ddf9","WordPress Blue 20":"#adbaf3","WordPress Blue 30":"#7b90ff","WordPress Blue 40":"#546ff3","WordPress Blue 50":"#3858e9","WordPress Blue 60":"#2a46ce","WordPress Blue 70":"#1d35b4","WordPress Blue 80":"#1f3286","WordPress Blue 90":"#14215a","WordPress Blue 100":"#0a112d","WordPress Blue":"#3858e9","Purple 0":"#f2e9ed","Purple 5":"#ebcee0","Purple 10":"#e3afd5","Purple 20":"#d48fc8","Purple 30":"#c475bd","Purple 40":"#b35eb1","Purple 50":"#984a9c","Purple 60":"#7c3982","Purple 70":"#662c6e","Purple 80":"#4d2054","Purple 90":"#35163b","Purple 100":"#1e0c21","Purple":"#984a9c","Pink 0":"#f5e9ed","Pink 5":"#f2ceda","Pink 10":"#f7a8c3","Pink 20":"#f283aa","Pink 30":"#eb6594","Pink 40":"#e34c84","Pink 50":"#c9356e","Pink 60":"#ab235a","Pink 70":"#8c1749","Pink 80":"#700f3b","Pink 90":"#4f092a","Pink 100":"#260415","Pink":"#c9356e","Red 0":"#f7ebec","Red 5":"#facfd2","Red 10":"#ffabaf","Red 20":"#ff8085","Red 30":"#f86368","Red 40":"#e65054","Red 50":"#d63638","Red 60":"#b32d2e","Red 70":"#8a2424","Red 80":"#691c1c","Red 90":"#451313","Red 100":"#240a0a","Red":"#d63638","Orange 0":"#f5ece6","Orange 5":"#f7dcc6","Orange 10":"#ffbf86","Orange 20":"#faa754","Orange 30":"#e68b28","Orange 40":"#d67709","Orange 50":"#b26200","Orange 60":"#8a4d00","Orange 70":"#704000","Orange 80":"#543100","Orange 90":"#361f00","Orange 100":"#1f1200","Orange":"#b26200","Yellow 0":"#f5f1e1","Yellow 5":"#f5e6b3","Yellow 10":"#f2d76b","Yellow 20":"#f0c930","Yellow 30":"#deb100","Yellow 40":"#c08c00","Yellow 50":"#9d6e00","Yellow 60":"#7d5600","Yellow 70":"#674600","Yellow 80":"#4f3500","Yellow 90":"#320","Yellow 100":"#1c1300","Yellow":"#9d6e00","Green 0":"#e6f2e8","Green 5":"#b8e6bf","Green 10":"#68de86","Green 20":"#1ed15a","Green 30":"#00ba37","Green 40":"#00a32a","Green 50":"#008a20","Green 60":"#007017","Green 70":"#005c12","Green 80":"#00450c","Green 90":"#003008","Green 100":"#001c05","Green":"#008a20","Celadon 0":"#e4f2ed","Celadon 5":"#a7e8d3","Celadon 10":"#66deb9","Celadon 20":"#31cc9f","Celadon 30":"#09b585","Celadon 40":"#009e73","Celadon 50":"#008763","Celadon 60":"#007053","Celadon 70":"#005c44","Celadon 80":"#004533","Celadon 90":"#003024","Celadon 100":"#001c15","Celadon":"#008763","Automattic Blue 0":"#ebf4fa","Automattic Blue 5":"#c4e2f5","Automattic Blue 10":"#88ccf2","Automattic Blue 20":"#5ab7e8","Automattic Blue 30":"#24a3e0","Automattic Blue 40":"#1490c7","Automattic Blue 50":"#0277a8","Automattic Blue 60":"#036085","Automattic Blue 70":"#02506e","Automattic Blue 80":"#02384d","Automattic Blue 90":"#022836","Automattic Blue 100":"#021b24","Automattic Blue":"#24a3e0","Simplenote Blue 0":"#e9ecf5","Simplenote Blue 5":"#ced9f2","Simplenote Blue 10":"#abc1f5","Simplenote Blue 20":"#84a4f0","Simplenote Blue 30":"#618df2","Simplenote Blue 40":"#4678eb","Simplenote Blue 50":"#3361cc","Simplenote Blue 60":"#1d4fc4","Simplenote Blue 70":"#113ead","Simplenote Blue 80":"#0d2f85","Simplenote Blue 90":"#09205c","Simplenote Blue 100":"#05102e","Simplenote Blue":"#3361cc","WooCommerce Purple 0":"#f2edff","WooCommerce Purple 5":"#e1d7ff","WooCommerce Purple 10":"#d1c1ff","WooCommerce Purple 20":"#b999ff","WooCommerce Purple 30":"#a77eff","WooCommerce Purple 40":"#873eff","WooCommerce Purple 50":"#720eec","WooCommerce Purple 60":"#6108ce","WooCommerce Purple 70":"#5007aa","WooCommerce Purple 80":"#3c087e","WooCommerce Purple 90":"#2c045d","WooCommerce Purple 100":"#1f0342","WooCommerce Purple":"#720eec","Jetpack Green 0":"#f0f2eb","Jetpack Green 5":"#d0e6b8","Jetpack Green 10":"#9dd977","Jetpack Green 20":"#64ca43","Jetpack Green 30":"#2fb41f","Jetpack Green 40":"#069e08","Jetpack Green 50":"#008710","Jetpack Green 60":"#007117","Jetpack Green 70":"#005b18","Jetpack Green 80":"#004515","Jetpack Green 90":"#003010","Jetpack Green 100":"#001c09","Jetpack Green":"#069e08"}}')}},t={};function r(n){var a=t[n];if(void 0!==a)return a.exports;var s=t[n]={exports:{}};return e[n](s,s.exports,r),s.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;r.g.importScripts&&(e=r.g.location+"");var t=r.g.document;if(!e&&t&&(t.currentScript&&"SCRIPT"===t.currentScript.tagName.toUpperCase()&&(e=t.currentScript.src),!e)){var n=t.getElementsByTagName("script");if(n.length)for(var a=n.length-1;a>-1&&(!e||!/^http(s?):/.test(e));)e=n[a--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),r.p=e+"../"})(),(()=>{"use strict";var e=r(5985),t=r(7723),n=r(1609),a=r(5795),s=r(8950),i=r(2119);const __=t.__,o=({message:e,onDismiss:t})=>React.createElement("div",{className:"notice notice-success is-dismissible"},React.createElement("p",null,e),React.createElement("button",{type:"button",className:"notice-dismiss",onClick:t},React.createElement("span",{className:"screen-reader-text"},__("Dismiss this notice.","jetpack-external-media")))),c=()=>{const[r,s]=(0,n.useState)(null),[c,l]=(0,n.useState)(""),{tracks:u}=(0,e.st)(),d=(0,i.Pk)(r);return(0,n.useEffect)((()=>{const e=document.getElementById("jetpack-external-media-import"),t=e=>{const t=e.target.dataset.slug;t&&(s(t),u.recordEvent("jetpack_external_media_import_media_page_import_click",{media_source:t}))};return e&&e.addEventListener("click",t),()=>{e&&e.removeEventListener("click",t)}}),[u]),React.createElement(React.Fragment,null,d&&React.createElement(d,{externalSource:(0,i.JC)(r),multiple:!0,isImport:!0,selectButtonText:(e,r)=>r?(0,t.sprintf)(/* translators: %1$d is the number of media that were selected. */
__("Importing %1$d media…","jetpack-external-media"),e):e?(0,t.sprintf)(/* translators: %1$d is the number of media that were selected. */
__("Import %1$d media","jetpack-external-media"),e):__("Import media","jetpack-external-media"),onSelect:e=>{e&&0!==e.length&&l((0,t.sprintf)(/* translators: %d is the number of the media */
__("%d media imported successfully.","jetpack-external-media"),e.length))},onClose:e=>{e&&(e.stopPropagation(),e.target.closest(".jetpack-external-media-header__dropdown"))||s(null)}}),c&&(0,a.createPortal)(React.createElement(o,{message:c,onDismiss:()=>l("")}),document.getElementById("jetpack-external-media-import-notice")))},l=document.getElementById("jetpack-external-media-import-modal");if(l){s.createRoot(l).render(React.createElement(c,null))}})()})();