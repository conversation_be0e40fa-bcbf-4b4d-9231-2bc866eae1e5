{"name": "um-user-photos", "version": "2.1.9", "author": {"name": "Ultimate Member", "email": "<EMAIL>", "url": "https://ultimatemember.com"}, "homepage": "http://roots.io", "repository": {"type": "git", "url": "git://github.com/roots/roots.git"}, "bugs": {"url": "https://github.com/roots/roots/issues"}, "licenses": [{"type": "MIT", "url": "http://opensource.org/licenses/MIT"}], "scripts": {"build:docs": "rm -rf docs/hooks/ && jsdoc -c hookdoc-conf.json"}, "engines": {"node": ">= 0.10.0"}, "devDependencies": {"gulp": "^4.0.2", "gulp-cli": "^2.3.0", "gulp-concat": "2.6.1", "gulp-rename": "^2.0.0", "sass": "1.38.0", "gulp-sass": "^5.1.0", "gulp-uglify": "^3.0.2", "gulp-clean-css": "^4.3.0"}, "dependencies": {"react": "^18.2.0", "taffydb": "^2.7.3"}}