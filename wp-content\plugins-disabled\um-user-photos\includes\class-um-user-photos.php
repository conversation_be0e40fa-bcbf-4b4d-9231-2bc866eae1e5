<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class UM_User_Photos
 */
class UM_User_Photos {

	/**
	 * @var
	 */
	private static $instance;

	/**
	 * @return UM_User_Photos
	 */
	public static function instance() {
		if ( is_null( self::$instance ) ) {
			self::$instance = new self();
		}
		return self::$instance;
	}

	/**
	 * UM_User_Photos constructor.
	 */
	public function __construct() {
		add_filter( 'um_call_object_User_Photos', array( &$this, 'get_this' ) );
		add_filter( 'um_settings_default_values', array( &$this, 'default_settings' ) );

		$this->includes();
	}

	/**
	 * @return $this
	 */
	function get_this() {
		return $this;
	}

	/**
	 * @param $defaults
	 *
	 * @return array
	 */
	public function default_settings( $defaults ) {
		return array_merge( $defaults, $this->setup()->settings_defaults );
	}

	public function includes() {
		$this->common()->includes();

		if ( UM()->is_request( 'ajax' ) ) {
			if ( defined( 'UM_DEV_MODE' ) && UM_DEV_MODE && UM()->options()->get( 'enable_new_ui' ) ) {
				$this->ajax()->includes();
			} else {
				$this->ajax_v2();
			}
		} elseif ( UM()->is_request( 'admin' ) ) {
			$this->admin()->includes();
		} elseif ( UM()->is_request( 'frontend' ) ) {
			$this->frontend()->includes();
		}
	}

	/**
	 * @return um_ext\um_user_photos\common\Init
	 */
	public function common() {
		if ( empty( UM()->classes['um_ext\um_user_photos\common\init'] ) ) {
			UM()->classes['um_ext\um_user_photos\common\init'] = new um_ext\um_user_photos\common\Init();
		}
		return UM()->classes['um_ext\um_user_photos\common\init'];
	}

	/**
	 * @return um_ext\um_user_photos\ajax\Init
	 */
	public function ajax() {
		if ( empty( UM()->classes['um_ext\um_user_photos\ajax\init'] ) ) {
			UM()->classes['um_ext\um_user_photos\ajax\init'] = new um_ext\um_user_photos\ajax\Init();
		}
		return UM()->classes['um_ext\um_user_photos\ajax\init'];
	}

	/**
	 * Note: It will be deprecated since new UI is live.
	 * @todo deprecate since new UI is live.
	 *
	 * @return um_ext\um_user_photos\ajax\Ajax_V2
	 */
	public function ajax_v2() {
		if ( empty( UM()->classes['um_photos_ajax'] ) ) {
			UM()->classes['um_photos_ajax'] = new um_ext\um_user_photos\ajax\Ajax_V2();
		}
		return UM()->classes['um_photos_ajax'];
	}

	/**
	 * @return um_ext\um_user_photos\admin\Init
	 */
	public function admin() {
		if ( empty( UM()->classes['um_ext\um_user_photos\admin\init'] ) ) {
			UM()->classes['um_ext\um_user_photos\admin\init'] = new um_ext\um_user_photos\admin\Init();
		}
		return UM()->classes['um_ext\um_user_photos\admin\init'];
	}

	/**
	 * @return um_ext\um_user_photos\frontend\Init
	 */
	public function frontend() {
		if ( empty( UM()->classes['um_ext\um_user_photos\frontend\init'] ) ) {
			UM()->classes['um_ext\um_user_photos\frontend\init'] = new um_ext\um_user_photos\frontend\Init();
		}
		return UM()->classes['um_ext\um_user_photos\frontend\init'];
	}

	/**
	 * @return um_ext\um_user_photos\common\Setup
	 */
	public function setup() {
		if ( empty( UM()->classes['um_photos_setup'] ) ) {
			UM()->classes['um_photos_setup'] = new um_ext\um_user_photos\common\Setup();
		}
		return UM()->classes['um_photos_setup'];
	}

	/**
	 * @deprecated 2.2.0
	 *
	 * @param $comment_id
	 * @param $user_id
	 *
	 * @return bool
	 */
	public function can_edit_comment( $comment_id , $user_id ) {
		_deprecated_function( __METHOD__, '2.2.0', 'UM()->User_Photos()->common()->user()->can_edit_comment()' );
		return $this->common()->user()->can_edit_comment( $comment_id, $user_id );
	}

	/**
	 * @deprecated 2.2.0
	 *
	 * @param $comment_id
	 * @param $user_id
	 *
	 * @return bool
	 */
	public function is_comment_author( $comment_id, $user_id ) {
		_deprecated_function( __METHOD__, '2.2.0', 'UM()->User_Photos()->common()->user()->is_comment_author()' );
		return $this->common()->user()->is_comment_author( $comment_id, $user_id );
	}

	/**
	 * @deprecated 2.2.0
	 *
	 * @return true
	 */
	public function can_comment() {
		_deprecated_function( __METHOD__, '2.2.0', 'UM()->User_Photos()->common()->user()->can_comment()' );
		return $this->common()->user()->can_comment();
	}
}

//create class var
add_action( 'plugins_loaded', 'um_init_photos', -10 );
function um_init_photos() {
	if ( function_exists( 'UM' ) ) {
		UM()->set_class( 'User_Photos', true );
	}
}
