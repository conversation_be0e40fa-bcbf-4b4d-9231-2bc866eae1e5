<?php
/**
 * Template for the UM User Photos. Display members, who like this photo or comment.
 *
 * Page: "Profile", tab "Photos", the image popup likes or comment's likes
 * Call: UM()->User_Photos()->ajax()->get_um_user_photo_likes()
 * Call: UM()->User_Photos()->ajax()->get_um_user_photos_comment_likes()
 * @version 2.2.0
 *
 * @var string $context
 * @var array  $likes
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/modal/likes.php
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( empty( $likes ) ) {
	if ( isset( $context ) ) {
		if ( 'photo' === $context ) {
			esc_html_e( 'Nobody has liked this photo yet.', 'um-user-photos' );
		} elseif ( 'comment' === $context ) {
			esc_html_e( 'Nobody has liked this comment yet.', 'um-user-photos' );
		} else {
			esc_html_e( 'Invalid template context.', 'um-user-photos' );
		}
	}
} else {
	echo wp_kses(
		UM()->frontend()::layouts()::users_list(
			$likes,
			array(
				'avatar_size'   => 'm',
				'wrapper_class' => array( 'um-grid', 'um-grid-col-2' ),
			)
		),
		UM()->get_allowed_html( 'templates' )
	);
}
