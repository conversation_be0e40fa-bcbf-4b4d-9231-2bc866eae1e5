jQuery(document).ready(function(l){l(document.body).on("click","#um_user_photos_download_all",function(e){return!l(this).hasClass("um-link-disabled")}),l(document.body).on("click","#um_user_photos_delete_all",function(e){e.preventDefault();let o=l(this);var e=o.data("nonce"),s=o.data("alert_message");let a=o.data("redirect");confirm(s)&&(o.siblings(".um-user-photos-loader").umShow(),o.prop("disabled",!0),o.siblings(".um-alert").remove(),l("#um_user_photos_download_all").addClass("um-link-disabled"),wp.ajax.send("um_user_photos_flush_albums",{data:{_wpnonce:e},success:function(e){o.siblings(".um-user-photos-loader").umHide(),o.parents(".um-user-photos-delete-wrap").append(e),o.remove(),l("#um_user_photos_download_all").remove(),setTimeout(function(){window.location.assign(a)},3e3)},error:function(e){o.parents(".um-user-photos-delete-wrap").append(e),o.prop("disabled",!1),o.siblings(".um-user-photos-loader").umHide(),l("#um_user_photos_download_all").removeClass("um-link-disabled"),console.log(e)}}))})});