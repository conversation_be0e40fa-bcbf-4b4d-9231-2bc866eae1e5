.um_current_user_location {
  width: 40px;
  height: 36px;
  background: #fff;
  line-height: 36px;
  font-size: 24px;
  position: absolute;
  top: 0;
  right: 0;
  text-align: center;
  display: inline-block;
  text-decoration: none !important;
  box-shadow: none !important;
  margin: 2px;
  color: #aaaaaa; }
  .um_current_user_location:hover {
    color: #3ba1da; }

.um_user_location_g_autocomplete_map {
  height: 300px;
  width: 100%; }

.um-user-locations-map-field-view {
  height: 300px; }

.um-user-locations-map-field-text {
  margin: 0 0 10px 0; }

.um-directory .um-member-directory-map {
  margin: 0 0 10px 0;
  width: 100%; }
  .um-directory .um-member-directory-map.um-member-directory-hidden-map {
    display: none; }
  .um-directory .um-member-directory-map.um-map-inited img[src$="um_avatar_marker=1"] {
    border: 2px solid #fff !important;
    border-radius: 100%;
    -moz-border-radius: 100%;
    box-shadow: 0 2px 0 0 rgba(50, 50, 93, 0.1), 0 1px 0 0 rgba(0, 0, 0, 0.07) !important;
    -webkit-box-shadow: 0 2px 0 0 rgba(50, 50, 93, 0.1), 0 1px 0 0 rgba(0, 0, 0, 0.07) !important; }

.um-directory .um-member-directory-map-controls {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: baseline;
  flex-wrap: nowrap;
  margin: 0 0 10px 0; }
  .um-directory .um-member-directory-map-controls .um-member-directory-map-controls-half {
    width: calc( 50% - 5px); }
    .um-directory .um-member-directory-map-controls .um-member-directory-map-controls-half .um-field-area {
      margin: 10px 0 0 0; }
    .um-directory .um-member-directory-map-controls .um-member-directory-map-controls-half .um-field {
      padding: 0; }

.um-directory .um-member-directory-header .um-member-directory-header-row .um-search .um-search-filter.um-user_location-filter-type {
  position: relative; }

.um-directory .um-members-wrapper .um-members-map {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  flex-wrap: nowrap;
  width: 100%; }
  .um-directory .um-members-wrapper .um-members-map .um-members-map-wrapper {
    width: 100%;
    margin: 0 0 10px 0;
    height: 300px; }
  .um-directory .um-members-wrapper .um-members-map .um-members-map-grid {
    width: 100%;
    margin: 0 0 10px 0;
    display: grid;
    grid-template-rows: auto 1fr;
    grid-template-columns: repeat(1, 1fr);
    grid-gap: 10px;
    grid-auto-rows: minmax(max-content, auto);
    -ms-grid-template-rows: auto 1fr;
    -ms-grid-template-columns: repeat(1, 1fr);
    -ms-grid-gap: 10px;
    -ms-grid-auto-rows: minmax(max-content, auto);
    align-items: center; }
    .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: stretch;
      flex-wrap: nowrap;
      width: 100%;
      padding: 15px;
      margin: 0;
      background: #fff;
      box-sizing: border-box;
      -moz-box-sizing: border-box;
      -webkit-box-sizing: border-box; }
      .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-status {
        display: none;
        background: #999; }
        .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-status.awaiting_admin_review, .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-status.inactive, .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-status.rejected {
          display: block;
          width: 100%;
          padding: 7px 15px;
          margin-bottom: 10px;
          color: #fff;
          font-size: 13px;
          box-sizing: border-box;
          -moz-box-sizing: border-box;
          -webkit-box-sizing: border-box; }
        .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-status.awaiting_admin_review {
          background: #c74a4a; }
      .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: flex-start;
        flex-wrap: nowrap;
        width: 100%;
        margin: 0 0 10px 0;
        padding: 0; }
        .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-photo {
          width: 100px;
          margin: 0 15px 0 0;
          padding: 0; }
          .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-photo a {
            width: 100px;
            height: 100px;
            display: block; }
            .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-photo a img {
              width: 100px;
              height: 100px;
              position: relative;
              top: 0;
              margin: 0;
              margin-bottom: 0;
              border: none; }
        .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-card {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: flex-start;
          flex-wrap: nowrap;
          width: calc( 100% - 115px);
          padding: 0;
          margin: 0;
          box-sizing: border-box;
          -moz-box-sizing: border-box;
          -webkit-box-sizing: border-box; }
          .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-card.no-photo {
            width: 100%; }
          .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-card .um-member-card-content {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: stretch;
            flex-wrap: nowrap;
            width: calc( 100% - 30px);
            margin: 0 10px 0 0; }
            .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-card .um-member-card-content .um-member-card-header {
              width: 100%; }
              .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-card .um-member-card-content .um-member-card-header .um-member-name {
                margin: 0 0 4px 0; }
                .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-card .um-member-card-content .um-member-card-header .um-member-name a {
                  font-size: 16px;
                  line-height: 26px;
                  color: #444;
                  font-weight: 700; }
            .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-card .um-member-card-content .um-member-tagline {
              display: flex;
              flex-direction: row;
              justify-content: flex-start;
              align-items: baseline;
              flex-wrap: wrap;
              width: 100%;
              font-size: 13px;
              color: #999;
              padding: 0;
              box-sizing: border-box; }
            .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-card .um-member-card-content .um-member-meta-main {
              width: 100%;
              padding: 0;
              box-sizing: border-box;
              display: none; }
              .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-card .um-member-card-content .um-member-meta-main.no-animate {
                display: block; }
              .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-card .um-member-card-content .um-member-meta-main .um-member-meta {
                float: left;
                width: 100%;
                display: block;
                margin: 10px 0 0 0;
                box-sizing: border-box;
                border: none; }
                .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-card .um-member-card-content .um-member-meta-main .um-member-meta .um-member-metaline {
                  display: flex;
                  flex-direction: row;
                  justify-content: flex-start;
                  align-items: center;
                  flex-wrap: wrap;
                  font-size: 13px;
                  padding: 12px 0 0 0;
                  line-height: 16px;
                  width: 100%; }
                .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-card .um-member-card-content .um-member-meta-main .um-member-meta .um-member-connect {
                  padding-top: 10px; }
                  .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-card .um-member-card-content .um-member-meta-main .um-member-meta .um-member-connect a {
                    display: inline-block;
                    width: 40px;
                    line-height: 40px;
                    height: 40px;
                    -moz-border-radius: 999px;
                    -webkit-border-radius: 999px;
                    border-radius: 999px;
                    color: #fff !important;
                    opacity: 0.85;
                    margin: 0 1px;
                    font-size: 22px;
                    transition: 0.25s;
                    text-align: center; }
                    .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-card .um-member-card-content .um-member-meta-main .um-member-meta .um-member-connect a:hover {
                      opacity: 1;
                      color: #fff; }
          .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-card .um-member-card-actions {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-end;
            flex-wrap: nowrap;
            width: 20px;
            text-align: right;
            padding: 0; }
            .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-card .um-member-card-actions .um-member-cog {
              position: relative; }
              .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-card .um-member-card-actions .um-member-cog .um-member-actions-a {
                line-height: 1;
                display: block;
                color: #666; }
                .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-card .um-member-card-actions .um-member-cog .um-member-actions-a i {
                  display: block;
                  font-size: 20px;
                  line-height: 1; }
              .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-card .um-member-card-actions .um-member-cog .um-new-dropdown {
                width: 180px;
                right: 0;
                text-align: left; }
            .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-container .um-member-card .um-member-card-actions a {
              box-sizing: border-box; }
      .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-footer {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        flex-wrap: nowrap;
        width: 100%; }
        .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-footer .um-member-card-footer-buttons {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: baseline;
          flex-wrap: nowrap;
          width: calc( 100% - 145px);
          margin: 0 10px 0 0; }
          .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-footer .um-member-card-footer-buttons .um-members-list-footer-button-wrapper {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: baseline;
            flex-wrap: nowrap;
            margin: 0; }
          .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-footer .um-member-card-footer-buttons > :not(:last-child) {
            margin: 0 10px 0 0; }
        .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-footer .um-member-card-reveal-buttons {
          width: 20px;
          text-align: right; }
          .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-footer .um-member-card-reveal-buttons .um-member-more {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: baseline;
            flex-wrap: nowrap; }
            .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-footer .um-member-card-reveal-buttons .um-member-more a {
              color: #666;
              display: inline-block; }
              .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-footer .um-member-card-reveal-buttons .um-member-more a i {
                display: block;
                font-size: 28px;
                height: 28px;
                line-height: 28px; }
          .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-footer .um-member-card-reveal-buttons .um-member-less {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: baseline;
            flex-wrap: nowrap;
            display: none; }
            .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-footer .um-member-card-reveal-buttons .um-member-less a {
              color: #666;
              display: inline-block; }
              .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-footer .um-member-card-reveal-buttons .um-member-less a i {
                display: block;
                font-size: 28px;
                height: 28px;
                line-height: 28px; }
        .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-footer.no-photo.no-reveal .um-member-card-footer-buttons {
          width: 100%; }
        .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-footer.no-photo:not(.no-reveal) .um-member-card-footer-buttons {
          width: calc( 100% - 30px); }
        .um-directory .um-members-wrapper .um-members-map .um-members-map-grid .um-member .um-member-card-footer.no-reveal:not(.no-photo) .um-member-card-footer-buttons {
          width: calc( 100% - 125px); }

.um-directory.uimob340 .um-member-directory-map-controls .um-member-directory-map-controls-half {
  width: 100%; }

.um-directory.uimob500 .um-member-directory-map-controls .um-member-directory-map-controls-half {
  width: 100%; }

.um-directory.uimob800 .um-member-directory-map-controls .um-member-directory-map-controls-half {
  width: 100%; }

.um_user_location_gmap_infowindow {
  text-align: center; }
  .um_user_location_gmap_infowindow .um_user_location_infowindow_avatar {
    display: inline-block;
    text-align: center;
    margin: 0 0 5px 0; }
    .um_user_location_gmap_infowindow .um_user_location_infowindow_avatar img {
      margin: 0 auto;
      border-radius: 100%;
      display: block;
      height: calc(3.75 * 1rem);
      min-height: inherit;
      width: calc(3.75 * 1rem); }
  .um_user_location_gmap_infowindow .um_user_location_infowindow_title {
    margin: 0 0 5px 0;
    font-weight: bold; }
  .um_user_location_gmap_infowindow .um_user_location_infowindow_content .um-member-infowindow-line {
    margin: 0 0 5px 0; }
