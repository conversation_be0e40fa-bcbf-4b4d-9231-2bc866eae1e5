<?php

namespace UM_Stripe\Vendor\UM_Stripe\Vendor;

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
if (\PHP_VERSION_ID < 80000) {
    class UnhandledMatchError extends \Error
    {
    }
    \class_alias('UM_Stripe\Vendor\UnhandledMatchError', 'UnhandledMatchError', \false);
}
