{"translation-revision-date": "2024-10-02 16:14:23+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "field": ["field"], "Password strength": ["Password strength"], "Very strong": ["Very strong"], "Strong": ["Strong"], "Weak": ["Weak"], "Too weak": ["Too weak"], "Please select a %s": ["Please select a %s"], "+ Add %s": ["+ Add %s"], "Please select a valid option": ["Please select a valid option"], "Medium": ["Medium"], "Remove %s from cart": ["Remove %s from basket"], "Polo": ["Polo"], "We are experiencing difficulties with this payment method. Please contact us for assistance.": ["We are experiencing difficulties with this payment method. Please contact us for assistance."], "%s has been removed from your cart.": ["%s has been removed from your basket."], "Sorry, this order requires a shipping option.": ["Sorry, this order requires a shipping option."], "Total price for %1$d %2$s item: %3$s": ["Total price for %1$d %2$s item: %3$s", "Total price for %1$d %2$s items: %3$s"], "%d item": ["%d item", "%d items"], "Express Checkout": ["Express Checkout"], "No registered Payment Methods": ["No registered Payment Methods"], "Price between %1$s and %2$s": ["Price between %1$s and %2$s"], "The type returned by checkout filters must be the same as the type they receive. The function received %1$s but returned %2$s.": ["The type returned by checkout filters must be the same as the type they receive. The function received %1$s but returned %2$s."], "%s (optional)": ["%s (optional)"], "Returned value must include %1$s, you passed \"%2$s\"": ["Returned value must include %1$s, you passed \"%2$s\""], "Other available payment methods": ["Other available payment methods"], "Use another payment method.": ["Use another payment method."], "%1$s (%2$d unit)": ["%1$s (%2$d unit)", "%1$s (%2$d units)"], "Remove \"%s\"": ["Remove \"%s\""], "Remove item": ["Remove item"], "Details": ["Details"], "Orange": ["Orange"], "Lightweight baseball cap": ["Lightweight baseball cap"], "Cap": ["Cap"], "Yellow": ["Yellow"], "Warm hat for winter": ["Warm hat for winter"], "Beanie": ["<PERSON><PERSON>"], "example product in Cart Block\u0004Beanie": ["<PERSON><PERSON>"], "example product in Cart Block\u0004Beanie with Logo": ["Beanie with <PERSON><PERSON>"], "Remove coupon \"%s\"": ["Remove coupon \"%s\""], "Quantity increased to %s.": ["Quantity increased to %s."], "Quantity reduced to %s.": ["Quantity reduced to %s."], "Quantity of %s in your cart.": ["Quantity of %s in your basket."], "Loading shipping rates…": ["Loading shipping rates…"], "%d shipping option was found": ["%d shipping option was found", "%d shipping options were found"], "Shipping option searched for %d package.": ["Shipping option searched for %d package.", "Shipping options searched for %d packages."], "%d shipping option was found.": ["%d shipping option was found.", "%d shipping options were found."], "Removing coupon…": ["Removing coupon…"], "There was an error with this payment method. Please verify it's configured correctly.": ["There was an error with this payment method. Please verify it's configured correctly."], "%1$s ending in %2$s (expires %3$s)": ["%1$s ending in %2$s (expires %3$s)"], "There are no payment methods available. This may be an error on our side. Please contact us if you need any help placing your order.": ["There are no payment methods available. This may be an error on our side. Please contact us if you need any help placing your order."], "Save payment information to my account for future purchases.": ["Save payment information to my account for future purchases."], "Saved token for %s": ["Saved token for %s"], "Something went wrong. Please contact us to get assistance.": ["Something went wrong. Please contact us to get assistance."], "%d left in stock": ["%d left in stock"], "Discounted price:": ["Discounted price:"], "Previous price:": ["Previous price:"], "Including <TaxAmount/> in taxes": ["Including <TaxAmount/> in taxes"], "Enter code": ["Enter code"], "Applying coupon…": ["Applying coupon…"], "Taxes:": ["Taxes:"], "Coupon code \"%s\" has been removed from your cart.": ["Coupon code \"%s\" has been removed from your basket."], "Coupon code \"%s\" has been applied to your cart.": ["Coupon code \"%s\" has been applied to your basket."], "Loading…": ["Loading…"], "Or continue below": ["Or continue below"], "There was a problem with your shipping option.": ["There was a problem with your shipping option."], "There was a problem with your payment option.": ["There was a problem with your payment option."], "Unable to get cart data from the API.": ["Unable to get basket data from the API."], "Sales tax": ["Sales tax"], "Dismiss this notice": ["Dismiss this notice"], "Fees:": ["Fees:"], "Color": ["Colour"], "Small": ["Small"], "Size": ["Size"], "Show less": ["Show less"], "Show less options": ["Show less options"], "Show %s more": ["Show %s more", "Show %s more"], "Show %s more option": ["Show %s more option", "Show %s more options"], "Or": ["Or"], "There was an error loading the content.": ["There was an error loading the content."], "Oops!": ["Oops!"], "Read less": ["Read less"], "Please enter a valid email address": ["Please enter a valid email address"], "%1$s ending in %2$s": ["%1$s ending in %2$s"], "Coupon: %s": ["Coupon: %s"], "Free shipping": ["Free shipping"], "Error:": ["Error:"], "Subtotal": ["Subtotal"], "Free": ["Free"], "Discount": ["Discount"], "Close": ["Close"], "Apply": ["Apply"], "Save %s": ["Save %s"], "Shipping": ["Shipping"], "Total": ["Total"], "Taxes": ["Taxes"], "Remove": ["Remove"], "Coupons": ["Coupons"], "Product": ["Product"], "Fee": ["Fee"], "Subtotal:": ["Subtotal:"], "Discount:": ["Discount:"], "Shipping:": ["Shipping:"], "Read more": ["Read more"], "Available on backorder": ["Available on back-order"], "Local pickup": ["Local pickup"]}}, "comment": {"reference": "assets/client/blocks/wc-cart-checkout-base-frontend.js"}}