{"translation-revision-date": "2024-10-02 16:14:23+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "Remove %s from cart": ["Remove %s from basket"], "Polo": ["Polo"], "Start shopping": ["Start shopping"], "View my cart": ["View my basket"], "Go to checkout": ["Go to checkout"], "Shipping, taxes, and discounts calculated at checkout.": ["Shipping, taxes, and discounts calculated at checkout."], "Your cart": ["Your basket"], "%s has been removed from your cart.": ["%s has been removed from your basket."], "Price between %1$s and %2$s": ["Price between %1$s and %2$s"], "%s (optional)": ["%s (optional)"], "Remove item": ["Remove item"], "Details": ["Details"], "Orange": ["Orange"], "Lightweight baseball cap": ["Lightweight baseball cap"], "Cap": ["Cap"], "Yellow": ["Yellow"], "Warm hat for winter": ["Warm hat for winter"], "Beanie": ["<PERSON><PERSON>"], "example product in Cart Block\u0004Beanie": ["<PERSON><PERSON>"], "example product in Cart Block\u0004Beanie with Logo": ["Beanie with <PERSON><PERSON>"], "Quantity increased to %s.": ["Quantity increased to %s."], "Quantity reduced to %s.": ["Quantity reduced to %s."], "Quantity of %s in your cart.": ["Quantity of %s in your basket."], "%d left in stock": ["%d left in stock"], "Discounted price:": ["Discounted price:"], "Previous price:": ["Previous price:"], "Sales tax": ["Sales tax"], "Color": ["Colour"], "Small": ["Small"], "Size": ["Size"], "Free shipping": ["Free shipping"], "Subtotal": ["Subtotal"], "Dimensions": ["Dimensions"], "Save %s": ["Save %s"], "Shipping": ["Shipping"], "Total": ["Total"], "WooCommerce": ["WooCommerce"], "Product": ["Product"], "Fee": ["Fee"], "Available on backorder": ["Available on back-order"], "Local pickup": ["Local pickup"]}}, "comment": {"reference": "assets/client/blocks/mini-cart-contents.js"}}