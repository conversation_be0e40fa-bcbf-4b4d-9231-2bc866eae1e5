var um_locations_maps={},um_locations_maps_view={},um_locations_map_markers={},um_locations_map_view_markers={};function UMLocationSelectOnEnter(t){var l=t.addEventListener||t.attachEvent;function a(a,e){var o;"keydown"===a&&(o=e,e=function(a){var e=0<jQuery(".pac-item-selected").length;13!==a.which||e||(e=jQuery.Event("keydown",{keyCode:40,which:40}),o.apply(t,[e]),$selected_autocomplete=jQuery(t)),o.apply(t,[a])}),l.apply(t,[a,e])}t.addEventListener?t.addEventListener=a:t.attachEvent&&(t.attachEvent=a)}function UMplaceMarker(o,a,e){um_locations_map_markers[e]||(t={map:um_locations_maps[e],draggable:!1},um_locations_map_markers[e]=new google.maps.Marker(t)),um_locations_map_markers[e].setPosition({lat:o.lat(),lng:o.lng()});var t,l=jQuery(".um-form-field[data-key="+e+"]");a.panTo(o),(geocoder=new google.maps.Geocoder).geocode({latLng:new google.maps.LatLng(o.lat(),o.lng()),region:um_user_location_var.region},function(a,e){e==google.maps.GeocoderStatus.OK&&void 0!==a[0].formatted_address&&(e=wp.hooks.applyFilters("um_user_location_formatted_address_set_by_geocode",a[0].formatted_address,a,o),l.val(e),l.siblings(".um_lat_param").val(o.lat()),l.siblings(".um_lng_param").val(o.lng()),l.siblings(".um_url_param").val("https://www.google.com/maps/search/?api=1&query="+o.lat()+","+o.lng()),wp.hooks.doAction("um_user_location_after_location_field_set_by_geocode",a,o))})}function UMInitUserLocationAutocomplete(){var s;jQuery(".um-user-locations-map-field-view").length&&jQuery(".um-user-locations-map-field-view").each(function(){var a,e;""!==jQuery(this).data("lat")&&""!==jQuery(this).data("lng")&&(a=jQuery(this).parents(".um-field").data("key"),e=wp.hooks.applyFilters("um_user_locations_map_field_args_init",{center:{lat:parseFloat(jQuery(this).data("lat")),lng:parseFloat(jQuery(this).data("lng"))},zoom:parseInt(um_user_location_var.start_coords.zoom),fullscreenControl:!1,streetViewControl:!1,mapTypeControl:!1},a),um_locations_maps_view[a]=new google.maps.Map(jQuery(this).get(0),e),um_locations_map_view_markers[a]=new google.maps.Marker({position:{lat:parseFloat(jQuery(this).data("lat")),lng:parseFloat(jQuery(this).data("lng"))},map:um_locations_maps_view[a],draggable:!1}))}),jQuery(document.body).on("keyup",".um_user_location_g_autocomplete",function(){""===jQuery(this).val()&&(jQuery(this).siblings(".um_lat_param").val(""),jQuery(this).siblings(".um_lng_param").val(""),jQuery(this).siblings(".um_url_param").val(""),jQuery(this).siblings(".um_user_location_g_autocomplete_map").length&&um_locations_map_markers[jQuery(this).data("key")]&&(um_locations_map_markers[jQuery(this).data("key")].setMap(null),delete um_locations_map_markers[jQuery(this).data("key")]),wp.hooks.doAction("um_user_location_after_location_field_empty",jQuery(this)))}),jQuery(".um_user_location_g_autocomplete").each(function(){var a,e,o,t,l;UMLocationSelectOnEnter(jQuery(this).get(0)),jQuery(this).parents(".um-directory").length||(a=wp.hooks.applyFilters("um_user_locations_autocomplete_args",{types:["geocode"]},!1,jQuery(this)),a=new google.maps.places.Autocomplete(jQuery(this).get(0),a),jQuery(this).siblings(".um_user_location_g_autocomplete_map").length&&(e=!1,o={lat:parseFloat(um_user_location_var.start_coords.lat),lng:parseFloat(um_user_location_var.start_coords.lng)},""!==jQuery(this).siblings(".um_lat_param").val()&&""!==jQuery(this).siblings(".um_lng_param").val()&&(e=!0,o={lat:parseFloat(jQuery(this).siblings(".um_lat_param").val()),lng:parseFloat(jQuery(this).siblings(".um_lng_param").val())}),um_locations_maps[jQuery(this).data("key")]=new google.maps.Map(jQuery(this).siblings(".um_user_location_g_autocomplete_map").get(0),{center:o,zoom:parseInt(um_user_location_var.start_coords.zoom),fullscreenControl:!1,streetViewControl:!1,mapTypeControl:!1}),o={map:um_locations_maps[jQuery(this).data("key")],draggable:!1},e&&(o.position={lat:parseFloat(jQuery(this).siblings(".um_lat_param").val()),lng:parseFloat(jQuery(this).siblings(".um_lng_param").val())}),um_locations_map_markers[jQuery(this).data("key")]=new google.maps.Marker(o),t=um_locations_maps[jQuery(this).data("key")],l=jQuery(this).data("key"),google.maps.event.addListener(t,"click",function(a){UMplaceMarker(a.latLng,t,l)})),a.addListener("place_changed",function(a){var e,o,t=this.getPlace();void 0===t||void 0===t.geometry||void 0===t.geometry.location?void 0!==s&&(s.siblings(".um_lat_param").val(""),s.siblings(".um_lng_param").val(""),s.siblings(".um_url_param").val(""),wp.hooks.doAction("um_user_location_after_location_field_empty",s)):(e=t.geometry.location.lat(),o=t.geometry.location.lng(),void 0!==s&&(s.siblings(".um_lat_param").val(e),s.siblings(".um_lng_param").val(o),s.siblings(".um_url_param").val(t.url),wp.hooks.doAction("um_user_location_after_location_field_set_by_autocomplete",t,t.geometry.location),s.siblings(".um_user_location_g_autocomplete_map").length)&&(um_locations_maps[s.data("key")].setCenter({lat:parseFloat(e),lng:parseFloat(o)}),um_locations_maps[s.data("key")].fitBounds(t.geometry.viewport),um_locations_map_markers[s.data("key")]||(t={map:um_locations_maps[s.data("key")],draggable:!1},um_locations_map_markers[s.data("key")]=new google.maps.Marker(t)),um_locations_map_markers[s.data("key")].setPosition({lat:parseFloat(e),lng:parseFloat(o)})))}))}).on("click",function(){s=jQuery(this)}),wp.hooks.doAction("um_member_directory_init_autocomplete")}!function(s){function r(a,e){um_locations_maps[a].setCenter({lat:parseFloat(e.lat),lng:parseFloat(e.lng)}),um_locations_map_markers[a].setPosition({lat:parseFloat(e.lat),lng:parseFloat(e.lng)})}s(document).ready(function(){jQuery(document.body).on("keypress, keyup, keydown",".um-form",function(a){jQuery(a.target).parents(".um-field").hasClass("um-field-textarea")||13===a.which&&a.preventDefault()}),s(document.body).on("click",".um_current_user_location",function(){var t=s(this),l=(0<t.parents(".um-member-directory-map-controls").length&&t.parents(".um-directory"),t.siblings(".um_user_location_g_autocomplete"));return!navigator.geolocation&&um_user_location_var.is_ssl||navigator.geolocation.getCurrentPosition(function(o){var a;void 0!==o.coords&&(geocoder=new google.maps.Geocoder,a=wp.hooks.applyFilters("um_user_locations_geocode_args_init",{latLng:new google.maps.LatLng(o.coords.latitude,o.coords.longitude),region:um_user_location_var.region},o),geocoder.geocode(a,function(a,e){e==google.maps.GeocoderStatus.OK?void 0!==a[0].formatted_address&&(e=wp.hooks.applyFilters("um_user_location_formatted_address_set_by_geocode",a[0].formatted_address,a,o.coords),l.val(e),t.siblings(".um_lat_param").val(o.coords.latitude),t.siblings(".um_lng_param").val(o.coords.longitude),t.siblings(".um_url_param").val("https://www.google.com/maps/search/?api=1&query="+o.coords.latitude+","+o.coords.longitude),t.siblings(".um_user_location_g_autocomplete_map").length&&r(l.data("key"),{lat:o.coords.latitude,lng:o.coords.longitude}),wp.hooks.doAction("um_user_location_after_location_field_set_by_geocode",a,o.coords)):(s.support.cors=!0,s.ajax({url:"https://freegeoip.app/json/",type:"GET",crossDomain:!0,dataType:"jsonp",success:function(a){var e;void 0!==a.country_name?(e=wp.hooks.applyFilters("um_user_location_formatted_address_set_by_freegeoip",a.country_name+","+a.region_name+","+a.city,a),t.prev().val(e),t.siblings(".um_lat_param").val(a.latitude),t.siblings(".um_lng_param").val(a.longitude),t.siblings(".um_url_param").val("https://www.google.com/maps/search/?api=1&query="+a.latitude+","+a.longitude),t.siblings(".um_user_location_g_autocomplete_map").length&&r(l.data("key"),{lat:a.latitude,lng:a.longitude}),wp.hooks.doAction("um_user_location_after_location_field_set_by_freegeoip",a)):alert(wp.i18n.__("Can not get your current location","um-user-locations"))},error:function(a){alert(wp.i18n.__("Can not get your current location","um-user-locations"))}}))}))}),!1})})}(jQuery);var um_ul_script=document.createElement("script");um_ul_script.src="//maps.googleapis.com/maps/api/js?key="+um_user_location_var.api_key+"&libraries=places&callback=UMInitUserLocationAutocomplete",um_user_location_var.region&&(um_ul_script.src+="&language="+um_user_location_var.region),document.body.appendChild(um_ul_script),wp.hooks.addAction("um_profile_completeness_popup_loaded","um_user_locations",function(a){UMInitUserLocationAutocomplete()}),wp.hooks.addFilter("um_profile_completeness_save_field_value","um_user_locations",function(a,e,o,t){return t.find('input[data-key="'+o+'_lat"]').length&&((a={})[o]=t.find('input[data-key="'+o+'"]').val(),a[o+"_lat"]=t.find('input[data-key="'+o+'_lat"]').val(),a[o+"_lng"]=t.find('input[data-key="'+o+'_lng"]').val(),a[o+"_url"]=t.find('input[data-key="'+o+'_url"]').val()),a},10);