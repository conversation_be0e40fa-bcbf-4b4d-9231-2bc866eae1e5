<?php

declare (strict_types=1);
namespace WPForms\Vendor\Square\Models;

use stdClass;
/**
 * Describes save-card action fields.
 */
class SaveCardOptions implements \JsonSerializable
{
    /**
     * @var string
     */
    private $customerId;
    /**
     * @var string|null
     */
    private $cardId;
    /**
     * @var array
     */
    private $referenceId = [];
    /**
     * @param string $customerId
     */
    public function __construct(string $customerId)
    {
        $this->customerId = $customerId;
    }
    /**
     * Returns Customer Id.
     * The square-assigned ID of the customer linked to the saved card.
     */
    public function getCustomerId() : string
    {
        return $this->customerId;
    }
    /**
     * Sets Customer Id.
     * The square-assigned ID of the customer linked to the saved card.
     *
     * @required
     * @maps customer_id
     */
    public function setCustomerId(string $customerId) : void
    {
        $this->customerId = $customerId;
    }
    /**
     * Returns Card Id.
     * The id of the created card-on-file.
     */
    public function getCardId() : ?string
    {
        return $this->cardId;
    }
    /**
     * Sets Card Id.
     * The id of the created card-on-file.
     *
     * @maps card_id
     */
    public function setCardId(?string $cardId) : void
    {
        $this->cardId = $cardId;
    }
    /**
     * Returns Reference Id.
     * An optional user-defined reference ID that can be used to associate
     * this `Card` to another entity in an external system. For example, a customer
     * ID generated by a third-party system.
     */
    public function getReferenceId() : ?string
    {
        if (\count($this->referenceId) == 0) {
            return null;
        }
        return $this->referenceId['value'];
    }
    /**
     * Sets Reference Id.
     * An optional user-defined reference ID that can be used to associate
     * this `Card` to another entity in an external system. For example, a customer
     * ID generated by a third-party system.
     *
     * @maps reference_id
     */
    public function setReferenceId(?string $referenceId) : void
    {
        $this->referenceId['value'] = $referenceId;
    }
    /**
     * Unsets Reference Id.
     * An optional user-defined reference ID that can be used to associate
     * this `Card` to another entity in an external system. For example, a customer
     * ID generated by a third-party system.
     */
    public function unsetReferenceId() : void
    {
        $this->referenceId = [];
    }
    /**
     * Encode this object to JSON
     *
     * @param bool $asArrayWhenEmpty Whether to serialize this model as an array whenever no fields
     *        are set. (default: false)
     *
     * @return array|stdClass
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize(bool $asArrayWhenEmpty = \false)
    {
        $json = [];
        $json['customer_id'] = $this->customerId;
        if (isset($this->cardId)) {
            $json['card_id'] = $this->cardId;
        }
        if (!empty($this->referenceId)) {
            $json['reference_id'] = $this->referenceId['value'];
        }
        $json = \array_filter($json, function ($val) {
            return $val !== null;
        });
        return !$asArrayWhenEmpty && empty($json) ? new stdClass() : $json;
    }
}
