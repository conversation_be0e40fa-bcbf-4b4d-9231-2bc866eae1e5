<?php
/**
 * Template for the UM User Photos, common "Albums" block
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/albums.php
 *
 * Use via shortcode: [ultimatemember_albums]
 *
 * @version 2.2.0
 *
 * @var array $args
 * @var array $albums
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( empty( $albums ) || ! $albums->have_posts() ) {
	return;
}
$disable_title = UM()->options()->get( 'um_user_photos_disable_title' );
?>

<div class="um ultimatemember_albums" data-um-pagi-action="um_user_photos_get_albums_content" data-um-pagi-column="<?php echo esc_attr( $args['column'] ); ?>" data-um-pagi-per_page="<?php echo esc_attr( $args['per_page'] ); ?>">

	<?php do_action( 'ultimatemember_albums_before', $albums, $args ); ?>

	<div class="grid-row grid-row-<?php echo esc_attr( $args['column'] ); ?>">

		<?php
		foreach ( $albums->posts as $i => $album ) {
			$user = get_userdata( $album->post_author );
			if ( ! $user ) {
				continue;
			}
			$img         = UM()->User_Photos()->common()->album()->get_cover( $album->ID );
			$photos      = $album->_photos;
			$profile_url = um_user_profile_url( $album->post_author );
			$albums_url  = add_query_arg( 'profiletab', 'photos', $profile_url );
			$album_url   = add_query_arg( 'album_id', $album->ID, $albums_url );
			?>

			<div class="grid-item">
				<div class="um-user-photos-album">

					<?php do_action( 'ultimatemember_albums_item_before', $album, $args ); ?>

					<a href="<?php echo esc_url( $album_url ); ?>" class="um-user-photos-album-link" title="<?php echo esc_attr( $album->post_title ); ?>">
						<p class="album-title">
							<?php if ( empty( $disable_title ) && $album->post_title ) { ?>
								<strong><?php echo esc_html( $album->post_title ); ?></strong>
								<br />
							<?php } ?>
							<?php // translators: %s is a photos count ?>
							<small><?php echo $photos ? esc_html( sprintf( _n( '%s Photo', '%s Photos', count( $photos ), 'um-user-photos' ), number_format_i18n( count( $photos ) ) ) ) : esc_html__( 'No photos', 'um-user-photos' ); ?></small>
						</p>
						<img src="<?php echo esc_url( $img ); ?>" alt="<?php echo esc_attr( $album->post_title ); ?>"/>
					</a>

					<div class="um-member-card">
						<div class="um-member-photo">
							<a href="<?php echo esc_url( $profile_url ); ?>" title="<?php echo esc_attr( $user->display_name ); ?>"><?php echo get_avatar( $album->post_author, $args['size'] ); ?></a>
						</div>
						<div class="um-member-name">
							<a href="<?php echo esc_url( $profile_url ); ?>" title="<?php echo esc_attr( $user->display_name ); ?>"><?php echo esc_html( $user->display_name ); ?></a>
						</div>
					</div>

					<?php do_action( 'ultimatemember_albums_item_after', $album, $args ); ?>

				</div>
			</div>
			<?php
		}
		?>
	</div>

	<?php
	if ( $albums->found_posts > $args['per_page'] ) {
		UM()->get_template( 'pagination.php', UM_USER_PHOTOS_PLUGIN, $args, true );
	}
	?>

	<?php do_action( 'ultimatemember_albums_after', $albums, $args ); ?>
</div>
