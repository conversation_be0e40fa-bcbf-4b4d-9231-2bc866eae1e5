jQuery(document).ready(function(d){var s='<p style="text-align:center;">'+wp.i18n.__("Loading...","um-unsplash")+"</p>",r='<p style="text-align:center;">'+wp.i18n.__("No result was found.","um-unsplash")+"</p>";d(document.body).on("click",".um-unsplash-trigger",function(){return d("body").addClass("um_unsplash_overlay").trigger("um_unsplash_overlay_show"),UM.dropdown.hideAll(),!1}),d(document.body).on("click","#um_unsplash_remove_modal",function(){d("body").removeClass("um_unsplash_overlay").trigger("um_unsplash_overlay_hide")}),d("body").on("um_unsplash_overlay_show",function(){var e=d(".um-unsplash-modal"),a=(e.find(".um-unsplash-modal-body-ajax-content").html(s),um_unsplash_settings.proxy_url+"?action=um_unsplash_search&license="+um_unsplash_settings.license+"&per_page="+um_unsplash_settings.number_of_photos+"&query="+um_unsplash_settings.default_keyword+"&page=1&_wp_http_referer="+um_unsplash_settings.wp_http_referer);d.post(a,function(a){var s,u="",a=JSON.parse(JSON.stringify(a));1==a.success?(s=JSON.parse(a.data.body),d(s.results).each(function(a,s){var e=s.id,n=s.user.name,o=(s.user.username,s.links.download),l=s.links.download_location,t=s.user.links.html,r=s.urls.thumb,s=s.urls.full+"&w=1000&h=370&fit=crop";u+='<input class="unsplash_img_radio" type="radio" id="'+e+'" name="unsplash_img" data-id="'+e+'" value="'+s+'" data-user="'+n+'" data-download="'+o+'" data-user_profile="'+t+'" data-download_location="'+l+'"/><label for="'+e+'" style="background-image:url('+r+');"></label>'}),u+='<div class="selected-image"></div>'):(console.warn("Can not get Unsplash images",a),u+="<p>"+wp.i18n.__("Unexpected Error!","um-unsplash")+"</p>"),e.find(".um-unsplash-modal-body-ajax-content").html(u)})}).on("um_unsplash_overlay_hide",function(){var a=d(".um-unsplash-modal");a.find(".um-unsplash-modal-body-ajax-content").html(""),a.find("form").trigger("reset")}),d(document.body).on("click","#um-unsplash-submit",function(){var s,e,n,a,o,l,t,r,u=d(this);u.hasClass("disabled")||(s=u.parents(".um-unsplash-modal").find("form.um-unsplash-form"),e=d("body").find(".um-cover-e"),n=e.find("img"),(r=d("#um-unsplash-cropable")).length&&r.attr("src")&&d(".unsplash_img_radio:checked").val(r.attr("src")),d(".unsplash_img_radio:checked").length&&(r=d(".unsplash_img_radio:checked").attr("data-user"),a=d(".unsplash_img_radio:checked").attr("id"),o=d(".unsplash_img_radio:checked").attr("data-user_profile"),l=d(".unsplash_img_radio:checked").attr("data-download"),t=d(".unsplash_img_radio:checked").attr("data-download_location"),s.find('[name="photo_author"]').val(r),s.find('[name="photo_author_url"]').val(o),s.find('[name="photo_download_url"]').val(l),s.find('[name="photo_download_location"]').val(t),s.find('[name="photo_id"]').val(a)),u.addClass("disabled"),r=s.serialize(),wp.ajax.send({data:r,success:function(a){var s=a.image.replace("&amp;","&");n.length?n.attr("src",s):e.html(a.img_html),d("#um_unsplash_remove_modal").trigger("click"),u.removeClass("disabled")},error:function(a){s.find(".um-unsplash-form-response").html(a),u.removeClass("disabled")}}))}),d(document.body).on("click","#um-unsplash-photo-search-btn",function(a){a.preventDefault();var e,n,o,a=d("#um-unsplash-photo-search-field");""!==a.val()&&((e=d(this).parents(".um-unsplash-modal").find(".um-unsplash-modal-body-ajax-content")).html(s),a=a.val(),n=um_unsplash_settings.number_of_photos,o=um_unsplash_settings.proxy_url+"?action=um_unsplash_search&license="+um_unsplash_settings.license+"&per_page="+n+"&query="+a+"&_wp_http_referer="+um_unsplash_settings.wp_http_referer,d.post(o+"&page=1",function(a){var s,u="",a=JSON.parse(JSON.stringify(a));1==a.success?(0===(s=JSON.parse(a.data.body)).total?u+=r:(d(s.results).each(function(a,s){var e=s.id,n=s.user.name,o=(s.user.username,s.links.download),l=s.links.download_location,t=s.user.links.html,r=s.urls.thumb,s=s.urls.full+"&w=1000&h=370&fit=crop";u+='<input class="unsplash_img_radio" type="radio" id="'+e+'" name="unsplash_img" data-id="'+e+'" value="'+s+'" data-user="'+n+'" data-download="'+o+'" data-user_profile="'+t+'" data-download_location="'+l+'"/><label for="'+e+'" style="background-image:url('+r+');"></label>'}),s.total>n&&(u+='<p class="um-unsplash-ajax-load-more-holder"><a class="um-unsplash-ajax-load-more" href="javascript:void(0);" data-more="'+s.total+'" data-per_page="'+n+'" data-url="'+o+'" data-page="2">'+wp.i18n.__("Load more","um-unsplash")+" &raquo;</a></p>")),u+='<div class="selected-image"></div>'):(console.warn("Can not search Unsplash images",a),u+="<p>"+wp.i18n.__("Unexpected Error!","um-unsplash")+"</p>"),e.html(u)}))}),d(document.body).on("click",".um-unsplash-ajax-load-more",function(){var e=d(this),n=e.parents(".um-unsplash-modal").find(".um-unsplash-modal-body-ajax-content"),o=e.data("url"),l=e.data("page"),t=e.data("per_page");d.post(o+"&page="+l,function(a){var s,u="",a=JSON.parse(JSON.stringify(a));1==a.success?(0===(s=JSON.parse(a.data.body)).total?u+=r:d(s.results).each(function(a,s){var e=s.id,n=s.user.name,o=(s.user.username,s.links.download),l=s.links.download_location,t=s.user.links.html,r=s.urls.thumb,s=s.urls.full+"&w=1000&h=370&fit=crop";u+='<input class="unsplash_img_radio" type="radio" id="'+e+'" name="unsplash_img" data-id="'+e+'" value="'+s+'" data-user="'+n+'" data-download="'+o+'" data-user_profile="'+t+'" data-download_location="'+l+'"/><label for="'+e+'" style="background-image:url('+r+');"></label>'}),s.results.length&&(u+='<p class="um-unsplash-ajax-load-more-holder"><a class="um-unsplash-ajax-load-more" href="javascript:void(0);" data-more="'+s.total+'" data-per_page="'+t+'" data-url="'+o+'"" data-page="'+(+l+1)+'">'+wp.i18n.__("Load more","um-unsplash")+" &raquo;</a></p>")):(console.warn("Can not load Unsplash images",a),u+="<p>"+wp.i18n.__("Unexpected Error!","um-unsplash")+"</p>"),e.parent(".um-unsplash-ajax-load-more-holder").remove(),n.find(".selected-image").before(u)})}),d(document.body).on("change",".unsplash_img_radio",function(){d(".unsplash_img_radio:checked").length&&(d("#um_unsplash_view_image").css("display","inline-block"),d(".um-unsplash-modal-body").find(".selected-image").html(""),d("body").find(".um-unsplash-modal-footer").find(".um-modal-btn.disabled").removeClass("disabled"))}),d(document.body).on("click","#um_unsplash_view_image",function(){var a=d(".unsplash_img_radio:checked");a.length&&(a=a.val(),d(".um-unsplash-modal-body").find(".selected-image").html('<img id="um-unsplash-cropable" src="'+a+'" alt="" /><span class="um-unsplash-crop-adjustment-buttons"><a id="fp-y-up-button" class="button" href="javascript:void(0);"><i class="um-faicon-chevron-up"></i></a><a id="fp-y-down-button" class="button" href="javascript:void(0);"><i class="um-faicon-chevron-down"></i></a></span>')),d(this).css("display","none")}),d(document.body).on("click","#fp-y-up-button",function(){var a,s,e,n,o;d(this).hasClass("disabled")||(a=d("#um-unsplash-cropable")).length&&(o=(o=(o=a.attr("src")).replace("&crop=entropy","")).replace("&crop=focalpoint",""),-1<(o+="&crop=focalpoint").indexOf("&fp-y=")?0<(s=parseInt(a.data("fpy")))&&(e="0."+s,o=o.replace("&fp-y="+(e=(n=0)===s?0:e),"&fp-y="+(n=1<=(e=s-1)?"0."+e:n)),a.data("fpy",e)):(o+="&fp-y=0.1",a.data("fpy","1")),a.attr("src",o),d(document).trigger("um_unsplash_cover_position_change"))}),d(document.body).on("click","#fp-y-down-button",function(){var a,s,e,n,o;d(this).hasClass("disabled")||(a=d("#um-unsplash-cropable")).length&&(o=(o=(o=a.attr("src")).replace("&crop=entropy","")).replace("&crop=focalpoint",""),-1<(o+="&crop=focalpoint").indexOf("&fp-y=")?(s=parseInt(a.data("fpy")))<9&&(a.data("fpy",e=s+1),n="0."+s,o=o.replace("&fp-y="+(n=0===s?0:n),"&fp-y=0."+e)):(o+="&fp-y=0.5",a.data("fpy","5")),a.attr("src",o),d(document).trigger("um_unsplash_cover_position_change"))})});