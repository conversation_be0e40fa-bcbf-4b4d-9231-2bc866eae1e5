<?php
namespace um_ext\um_user_photos\admin;

use WP_Query;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Posts
 * @package um_ext\um_user_photos\admin
 */
class Posts {

	/**
	 * Posts constructor.
	 */
	public function __construct() {
		// Media Library.
		add_action( 'pre_get_posts', array( $this, 'media_filter' ), 20 );
	}

	/**
	 * Shows photos uploaded to albums on the Media Library screen.
	 * Hides photos uploaded to albums on other screens.
	 *
	 * @param WP_Query $query The WP_Query instance (passed by reference).
	 */
	public function media_filter( $query ) {
		if ( $query->is_main_query() ) {
			$screen = get_current_screen();
			if ( $screen && 'upload' === $screen->id && 'attachment' === $screen->post_type
			     && ! empty( $_GET['um_user_album_photos'] ) && UM()->options()->get( 'um_user_photos_media_library' ) ) { // phpcs:ignore WordPress.Security.NonceVerification -- No submitted input here.

				$meta_query   = (array) $query->get( 'meta_query' );
				$meta_query[] = array(
					'key'     => '_part_of_gallery',
					'compare' => 'EXISTS',
				);
				$query->set( 'meta_query', $meta_query );
				return;
			}
		}

		if ( $query->is_attachment || 'attachment' === $query->get( 'post_type' ) ) {
			$meta_query   = (array) $query->get( 'meta_query' );
			$meta_query[] = array(
				'key'     => '_part_of_gallery',
				'compare' => 'NOT EXISTS',
			);
			$query->set( 'meta_query', $meta_query );
		}
	}
}
