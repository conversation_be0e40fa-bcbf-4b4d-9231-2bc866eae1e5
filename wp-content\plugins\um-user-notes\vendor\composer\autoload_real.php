<?php

// autoload_real.php @generated by Composer

class ComposerAutoloaderInitbcab9c3ff15d6b2fa5ead75cecc1141c
{
    private static $loader;

    public static function loadClassLoader($class)
    {
        if ('Composer\Autoload\ClassLoader' === $class) {
            require __DIR__ . '/ClassLoader.php';
        }
    }

    /**
     * @return \Composer\Autoload\ClassLoader
     */
    public static function getLoader()
    {
        if (null !== self::$loader) {
            return self::$loader;
        }

        require __DIR__ . '/platform_check.php';

        spl_autoload_register(array('ComposerAutoloaderInitbcab9c3ff15d6b2fa5ead75cecc1141c', 'loadClassLoader'), true, true);
        self::$loader = $loader = new \Composer\Autoload\ClassLoader(\dirname(__DIR__));
        spl_autoload_unregister(array('ComposerAutoloaderInitbcab9c3ff15d6b2fa5ead75cecc1141c', 'loadClassLoader'));

        require __DIR__ . '/autoload_static.php';
        call_user_func(\Composer\Autoload\ComposerStaticInitbcab9c3ff15d6b2fa5ead75cecc1141c::getInitializer($loader));

        $loader->register(true);

        return $loader;
    }
}
