{"translation-revision-date": "2025-05-21 12:54:18+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=6; plural=(n == 0) ? 0 : ((n == 1) ? 1 : ((n == 2) ? 2 : ((n % 100 >= 3 && n % 100 <= 10) ? 3 : ((n % 100 >= 11 && n % 100 <= 99) ? 4 : 5))));", "lang": "ar"}, "A plugin was successfully installed and activated.": ["تم تثبيت الإضافة وتفعيلها بنجاح."], "%1$s (%2$s) was successfully installed and activated.": ["تم تثبيت ⁦%1$s⁩ (⁦%2$s⁩) وتفعيله بنجاح."], "Google for WooCommerce": ["Google for WooCommerce"], "Omnichannel for WooCommerce": ["قناة متعددة الاتجاهات لـ WooCommerce"], "TikTok for WooCommerce": ["TikTok لـ WooCommerce"], "Pinterest for WooCommerce": ["Pinterest لـ WooCommerce"], "Mercado Pago payments for WooCommerce": ["مدفوعات Mercado Pago الخاصة بـ WooCommerce"], "MailPoet": ["MailPoet"], "Could not %(actionType)s %(pluginName)s plugin, %(error)s": ["يتعذر %(actionType)s %(pluginName)s الإضافة، و%(error)s", "يتعذر %(actionType)s الإضافات الآتية: %(pluginName)s التي تتضمن هذه الأخطاء: %(error)s", "يتعذر %(actionType)s الإضافات الآتية: %(pluginName)s التي تتضمن هذه الأخطاء: %(error)s", "يتعذر %(actionType)s الإضافات الآتية: %(pluginName)s التي تتضمن هذه الأخطاء: %(error)s", "يتعذر %(actionType)s الإضافات الآتية: %(pluginName)s التي تتضمن هذه الأخطاء: %(error)s", "يتعذر %(actionType)s الإضافات الآتية: %(pluginName)s التي تتضمن هذه الأخطاء: %(error)s"], "Razorpay": ["Razorpay"], "Creative Mail for WooCommerce": ["البريد الإبداعي لـ WooCommerce"], "WooCommerce Shipping & Tax": ["الشحن والضريبة عبر WooCommerce"], "WooCommerce Payfast": ["إضافة Payfast في WooCommerce"], "WooCommerce Stripe": ["WooCommerce Stripe"], "WooCommerce PayPal": ["WooCommerce PayPal"], "WooCommerce ShipStation Gateway": ["WooCommerce ShipStation Gateway"], "There was a problem updating your settings.": ["حدثت هناك مشكلة أثناء تحديث إعداداتك."], "Plugins were successfully installed and activated.": ["تم تثبيت الإضافات وتفعيلها بنجاح."], "MM/DD/YYYY": ["MM/DD/YYYY"], "Facebook for WooCommerce": ["فيسبوك مقابل WooCommerce"], "Mailchimp for WooCommerce": ["إضافة MailChimp لـ WooCommerce"], "Klarna Payments for WooCommerce": ["إضافة عمليات الدفع Klarna لـ ووكومرس WooCommerce"], "Klarna Checkout for WooCommerce": ["إضافة عملية إتمام الطلب Klarna لـ ووكومرس WooCommerce"], "Jetpack": ["Jetpack"]}}, "comment": {"reference": "assets/client/admin/data/index.js"}}