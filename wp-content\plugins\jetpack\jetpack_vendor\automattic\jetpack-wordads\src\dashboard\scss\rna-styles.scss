@import './variables';

.jp-wordads-dashboard-wrap {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-flow: column nowrap;
	width: 100%;
	margin: 0 auto;
}

.jp-wordads-dashboard-row {
	display: grid;
	grid-gap: 24px;
	grid-template-columns: repeat( 4, 1fr );
	width: calc( 100% - 32px );
	margin: 0 16px;

	@include for-phone-up {
		grid-template-columns: repeat( 8, 1fr );
		width: calc( 100% - 36px );
		margin: 0 18px;
	}

	@include for-tablet-up {
		grid-template-columns: repeat( 12, 1fr );
		max-width: 1128px;
		width: calc( 100% - 48px );
		margin: 0 24px;
	}

	@for $i from 1 through 4 {
		.sm-col-span-#{$i} {
			grid-column-end: span #{$i};
		}
	}

	@include for-phone-up {

		@for $i from 1 through 8 {
			.md-col-span-#{$i} {
				grid-column-end: span #{$i};
			}
		}
	}

	@include for-tablet-up {

		@for $i from 1 through 12 {
			.lg-col-span-#{$i} {
				grid-column-end: span #{$i};
			}
		}
	}

	@include for-tablet-up {

		.lg-col-span-0 {
			display: none;
		}
	}

	@include for-tablet-down {

		.md-col-span-0 {
			display: none;
		}
	}

	@include for-phone-down {

		.sm-col-span-0 {
			display: none;
		}

		.sm-col-span-1 {
			display: block;
		}
	}
}
