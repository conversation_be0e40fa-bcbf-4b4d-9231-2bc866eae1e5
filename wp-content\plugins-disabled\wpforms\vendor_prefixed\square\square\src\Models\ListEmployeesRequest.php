<?php

declare (strict_types=1);
namespace WPForms\Vendor\Square\Models;

use stdClass;
class ListEmployeesRequest implements \JsonSerializable
{
    /**
     * @var array
     */
    private $locationId = [];
    /**
     * @var string|null
     */
    private $status;
    /**
     * @var array
     */
    private $limit = [];
    /**
     * @var array
     */
    private $cursor = [];
    /**
     * Returns Location Id.
     */
    public function getLocationId() : ?string
    {
        if (\count($this->locationId) == 0) {
            return null;
        }
        return $this->locationId['value'];
    }
    /**
     * Sets Location Id.
     *
     * @maps location_id
     */
    public function setLocationId(?string $locationId) : void
    {
        $this->locationId['value'] = $locationId;
    }
    /**
     * Unsets Location Id.
     */
    public function unsetLocationId() : void
    {
        $this->locationId = [];
    }
    /**
     * Returns Status.
     * The status of the Employee being retrieved.
     *
     * DEPRECATED at version 2020-08-26. Replaced by [TeamMemberStatus](entity:TeamMemberStatus).
     */
    public function getStatus() : ?string
    {
        return $this->status;
    }
    /**
     * Sets Status.
     * The status of the Employee being retrieved.
     *
     * DEPRECATED at version 2020-08-26. Replaced by [TeamMemberStatus](entity:TeamMemberStatus).
     *
     * @maps status
     */
    public function setStatus(?string $status) : void
    {
        $this->status = $status;
    }
    /**
     * Returns Limit.
     * The number of employees to be returned on each page.
     */
    public function getLimit() : ?int
    {
        if (\count($this->limit) == 0) {
            return null;
        }
        return $this->limit['value'];
    }
    /**
     * Sets Limit.
     * The number of employees to be returned on each page.
     *
     * @maps limit
     */
    public function setLimit(?int $limit) : void
    {
        $this->limit['value'] = $limit;
    }
    /**
     * Unsets Limit.
     * The number of employees to be returned on each page.
     */
    public function unsetLimit() : void
    {
        $this->limit = [];
    }
    /**
     * Returns Cursor.
     * The token required to retrieve the specified page of results.
     */
    public function getCursor() : ?string
    {
        if (\count($this->cursor) == 0) {
            return null;
        }
        return $this->cursor['value'];
    }
    /**
     * Sets Cursor.
     * The token required to retrieve the specified page of results.
     *
     * @maps cursor
     */
    public function setCursor(?string $cursor) : void
    {
        $this->cursor['value'] = $cursor;
    }
    /**
     * Unsets Cursor.
     * The token required to retrieve the specified page of results.
     */
    public function unsetCursor() : void
    {
        $this->cursor = [];
    }
    /**
     * Encode this object to JSON
     *
     * @param bool $asArrayWhenEmpty Whether to serialize this model as an array whenever no fields
     *        are set. (default: false)
     *
     * @return array|stdClass
     */
    #[\ReturnTypeWillChange] // @phan-suppress-current-line PhanUndeclaredClassAttribute for (php < 8.1)
    public function jsonSerialize(bool $asArrayWhenEmpty = \false)
    {
        $json = [];
        if (!empty($this->locationId)) {
            $json['location_id'] = $this->locationId['value'];
        }
        if (isset($this->status)) {
            $json['status'] = $this->status;
        }
        if (!empty($this->limit)) {
            $json['limit'] = $this->limit['value'];
        }
        if (!empty($this->cursor)) {
            $json['cursor'] = $this->cursor['value'];
        }
        $json = \array_filter($json, function ($val) {
            return $val !== null;
        });
        return !$asArrayWhenEmpty && empty($json) ? new stdClass() : $json;
    }
}
