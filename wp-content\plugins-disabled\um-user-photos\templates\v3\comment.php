<?php
/**
 * Template for the UM User Photos. The comment block
 *
 * Call:  UM()->User_Photos()->ajax()->um_user_photos_post_comment()
 * Page: "Profile", tab "Photos", the image popup
 * Parent template: comments.php
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/comment.php
 * @var int        $user_id
 * @var int        $id
 * @var string     $content
 * @var WP_Comment $photo_comment
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

$comment_date     = get_comment_time( get_option( 'date_format', 'F j, Y' ) . ' ' . get_option( 'time_format', 'g:i a' ), false, true, $id );
$comment_date_gmt = get_comment_time( get_option( 'date_format', 'F j, Y' ) . ' ' . get_option( 'time_format', 'g:i a' ), true, true, $id );

um_fetch_user( $user_id );

$has_liked = false;
$likes     = UM()->User_Photos()->common()->photo()->get_comment_likes( $id );
if ( ! $likes ) {
	$likes = array();
}

if ( in_array( get_current_user_id(), $likes, true ) ) {
	$has_liked = true;
}
$likes_count = count( $likes );
?>
<div class="um-user-photos-comment-wrap" data-comment_id="<?php echo esc_attr( $id ); ?>">
	<?php
	$post_date = UM()->datetime()->time_diff( strtotime( $comment_date_gmt ) );

	$supporting  = '<span class="um-user-photos-comment-time" title="' . esc_attr( $comment_date ) . '">' . esc_html( $post_date ) . '</span>';
	$supporting .= UM()->frontend()::layouts()::badge(
		__( 'Comment has been updated.', 'um-user-photos' ),
		array(
			'size'    => 's',
			'color'   => 'success',
			'classes' => array(
				'um-user-photos-comment-updated',
				'um-display-none',
			),
		)
	);

	echo wp_kses(
		UM()->frontend()::layouts()::small_data(
			um_user( 'ID' ),
			array(
				'avatar_size' => 'm',
				'clickable'   => true,
				'url'         => um_user_profile_url(),
				'supporting'  => $supporting,
			)
		),
		UM()->get_allowed_html( 'templates' )
	);

	if ( is_user_logged_in() && UM()->User_Photos()->common()->user()->can_edit_comment( $id, get_current_user_id() ) ) {
		?>
		<span class="um-user-photos-comment-actions">
			<?php
			$edit = '';
			if ( UM()->User_Photos()->common()->user()->is_comment_author( $id, get_current_user_id() ) ) {
				$icon  = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icons-tabler-outline icon-tabler-pencil"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4" /><path d="M13.5 6.5l4 4" /></svg>';
				$edit .= UM()->frontend()::layouts()::button(
					$icon,
					array(
						'size'          => 's',
						'design'        => 'link-gray',
						'icon_position' => 'content',
						'title'         => __( 'Edit', 'um-user-photos' ),
						'classes'       => array(
							'um-user-photos-edit-comment',
						),
						'data'          => array(
							'wpnonce'   => wp_create_nonce( 'um_user_photos_comment_edit' ),
							'commentid' => $id,
						),
					)
				);
			}
			$icon   = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icons-tabler-outline icon-tabler-trash"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M4 7l16 0" /><path d="M10 11l0 6" /><path d="M14 11l0 6" /><path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12" /><path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3" /></svg>';
			$delete = UM()->frontend()::layouts()::button(
				$icon,
				array(
					'size'          => 's',
					'design'        => 'link-gray',
					'icon_position' => 'content',
					'title'         => __( 'Delete', 'um-user-photos' ),
					'classes'       => array(
						'um-user-photos-delete-comment',
					),
					'data'          => array(
						'wpnonce'   => wp_create_nonce( 'um_user_photos_comment_delete' ),
						'confirm'   => __( 'Are you sure you want to delete this comment?', 'um-user-photos' ),
						'commentid' => $id,
					),
				)
			);
			echo wp_kses( $edit . $delete, UM()->get_allowed_html( 'templates' ) );
			?>
		</span>
	<?php } ?>
	<div class="um-user-photos-comment-info">
		<!-- comment meta-->
		<div class="um-user-photos-comment-text">
			<?php echo wp_kses( $content, UM()->get_allowed_html( 'templates' ) ); ?>
		</div>
		<?php if ( is_user_logged_in() && UM()->User_Photos()->common()->user()->is_comment_author( $id, get_current_user_id() ) ) { ?>
			<div class="um-user-photos-comment-edit-section um-display-none">
				<textarea class="um-user-photos-comment-textarea" placeholder="<?php esc_attr_e( 'Write a comment...', 'um-user-photos' ); ?>"><?php echo esc_textarea( $content ); ?></textarea>
				<div class="um-user-photos-comment-textarea-action">
					<?php
					$response = UM()->frontend()::layouts()::badge(
						'',
						array(
							'size'    => 's',
							'color'   => 'error',
							'classes' => array(
								'um-user-photos-edit-comment-response',
								'um-display-none',
							),
						)
					);
					$loader   = UM()->frontend()::layouts()::ajax_loader( 's', array( 'classes' => array( 'um-user-photos-loader', 'um-display-none' ) ) );
					$update   = UM()->frontend()::layouts()::button(
						__( 'Update', 'um-user-photos' ),
						array(
							'type'    => 'submit',
							'size'    => 's',
							'design'  => 'primary',
							'classes' => array( 'um-user-photos-comment-update-btn' ),
							'data'    => array(
								'commentid' => $id,
								'wpnonce'   => wp_create_nonce( 'um_user_photos_comment_update' ),
							),
						)
					);
					$cancel   = UM()->frontend()::layouts()::button(
						__( 'Cancel', 'um-user-photos' ),
						array(
							'type'    => 'button',
							'size'    => 's',
							'classes' => array( 'um-user-photos-comment-cancel-btn' ),
						)
					);
					echo wp_kses( $response . $loader . $update . $cancel, UM()->get_allowed_html( 'templates' ) );
					?>
				</div>
			</div>
		<?php } ?>
		<div class="um-user-photos-comment-meta">
			<?php
			$like_classes = array( 'um-user-photos-comment-like' );
			if ( $has_liked ) {
				$like_classes[] = 'active';
			}

			echo wp_kses(
				UM()->frontend()::layouts()::button(
					'<span class="um-like-icon"></span>',
					array(
						'size'     => 's',
						'design'   => 'link-gray',
						'title'    => $has_liked ? __( 'Unlike', 'um-user-photos' ) : __( 'Like', 'um-user-photos' ),
						'data'     => array(
							'id'           => $id,
							'likenonce'    => wp_create_nonce( 'um_user_photos_comment_like' ),
							'unlikenonce'  => wp_create_nonce( 'um_user_photos_comment_unlike' ),
							'like_title'   => __( 'Like', 'um-user-photos' ),
							'unlike_title' => __( 'Unlike', 'um-user-photos' ),
						),
						'classes'  => $like_classes,
						'disabled' => ! is_user_logged_in(),
					)
				),
				UM()->get_allowed_html( 'templates' )
			);

			$users_avatars = get_comment_meta( $id, '_likes', true );
			if ( empty( $users_avatars ) ) {
				$users_avatars = array();
			}

			$avatars = UM()->frontend()::layouts()::avatars_list(
				$users_avatars,
				array(
					'wrapper' => 'span',
					'size'    => 'xs',
					'count'   => 5,
				)
			);

			$likes_count_badge = UM()->frontend()::layouts()::badge(
				$likes_count,
				array(
					'size' => 's',
				)
			);

			echo wp_kses(
				UM()->frontend()::layouts()::button(
					'<span class="um-user-photos-comment-likes"><span class="um-user-photos-comment-likes-avatars' . ( 0 >= absint( $likes_count ) ? ' um-display-none' : '' ) . '">' . wp_kses( $avatars, UM()->get_allowed_html( 'templates' ) ) . '</span><span class="um-user-photos-comment-likes-count">' . wp_kses( $likes_count_badge, UM()->get_allowed_html( 'templates' ) ) . '</span></span>',
					array(
						'size'     => 'xs',
						'design'   => 'link-gray',
						'data'     => array(
							'id'      => $id,
							'wpnonce' => wp_create_nonce( 'um_user_photos_get_comment_likes' ),
						),
						'title'    => __( 'Click to show all likes', 'um-user-photos' ),
						'classes'  => array( 'um-user-photos-show-comment-likes' ),
						'disabled' => 0 >= absint( $likes_count ),
					)
				),
				UM()->get_allowed_html( 'templates' )
			);
			?>
		</div>
		<!-- comment meta-->
	</div>
</div>
