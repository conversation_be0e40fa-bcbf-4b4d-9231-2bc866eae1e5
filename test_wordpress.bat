@echo off
echo ===== TESTING WORDPRESS RAEZG.LOCAL =====
echo.

echo 1. Testing simple HTML file access...
echo ^<html^>^<body^>^<h1^>Test Page Works!^</h1^>^</body^>^</html^> > C:\xampp\htdocs\raezg\test-simple.html
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://raezg.local/test-simple.html' -TimeoutSec 5; Write-Host 'HTML Test: SUCCESS - Status' $response.StatusCode } catch { Write-Host 'HTML Test: FAILED -' $_.Exception.Message }"

echo.
echo 2. Testing PHP file access...
echo ^<?php echo "PHP Test Works! Time: " . date('Y-m-d H:i:s'); ?^> > C:\xampp\htdocs\raezg\test-simple.php
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://raezg.local/test-simple.php' -TimeoutSec 5; Write-Host 'PHP Test: SUCCESS - Status' $response.StatusCode } catch { Write-Host 'PHP Test: FAILED -' $_.Exception.Message }"

echo.
echo 3. Testing WordPress index.php directly...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://raezg.local/index.php' -TimeoutSec 10; Write-Host 'WordPress Index: SUCCESS - Status' $response.StatusCode } catch { Write-Host 'WordPress Index: FAILED -' $_.Exception.Message }"

echo.
echo 4. Testing WordPress wp-admin...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://raezg.local/wp-admin/' -TimeoutSec 10; Write-Host 'WordPress Admin: SUCCESS - Status' $response.StatusCode } catch { Write-Host 'WordPress Admin: FAILED -' $_.Exception.Message }"

echo.
echo 5. Checking WordPress database connection...
powershell -Command "try { $mysql = mysql -u root -h localhost -e 'USE raezg; SELECT COUNT(*) FROM wp_posts;' 2>&1; if ($LASTEXITCODE -eq 0) { Write-Host 'Database: SUCCESS - Connection works' } else { Write-Host 'Database: FAILED - Connection error' } } catch { Write-Host 'Database: FAILED -' $_.Exception.Message }"

echo.
echo 6. Checking for WordPress errors...
if exist "C:\xampp\htdocs\raezg\wp-content\debug.log" (
    echo WordPress debug log found:
    powershell -Command "Get-Content 'C:\xampp\htdocs\raezg\wp-content\debug.log' -Tail 5"
) else (
    echo No WordPress debug log found.
)

echo.
echo 7. Testing with different browsers...
echo Opening raezg.local in different ways...
start http://raezg.local
timeout /t 2 > nul
start http://127.0.0.1/raezg/
timeout /t 2 > nul

echo.
echo 8. Checking Apache access log for raezg.local requests...
powershell -Command "Get-Content 'C:\xampp\apache\logs\access.log' -Tail 10 | Where-Object { $_ -match 'raezg' }"

echo.
echo ===== WORDPRESS TEST COMPLETE =====
echo.
echo Please check the browser windows that opened.
echo If the simple HTML/PHP tests work but WordPress doesn't,
echo the issue is with the WordPress configuration.
echo.
pause
