<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

$role             = $object['data'];
$enable_download = true === (bool) UM()->options()->get( 'um_user_notes_enable_download' ) ? 1 : 0;
$enable_print     = true === (bool) UM()->options()->get( 'um_user_notes_enable_print' ) ? 1 : 0;
?>

<div class="um-admin-metabox">
	<?php
	UM()->admin_forms(
		array(
			'class'     => 'um-role-notes um-half-column',
			'prefix_id' => 'role',
			'fields'    => array(
				array(
					'id'      => '_um_disable_notes',
					'type'    => 'checkbox',
					'label'   => __( 'Disable notes feature?', 'um-user-notes' ),
					'tooltip' => __( 'Can this role have notes feature?', 'um-user-notes' ),
					'value'   => isset( $role['_um_disable_notes'] ) ? $role['_um_disable_notes'] : 0,
				),
				array(
					'id'          => '_um_user_notes_enable_download',
					'type'        => 'checkbox',
					'label'       => __( 'Enable download notes', 'um-user-notes' ),
					'value'       => isset( $role['_um_user_notes_enable_download'] ) ? $role['_um_user_notes_enable_download'] : $enable_download,
					'conditional' => array( '_um_disable_notes', '!=', 1 ),
				),
				array(
					'id'          => '_um_user_notes_enable_print',
					'type'        => 'checkbox',
					'label'       => __( 'Enable print notes', 'um-user-notes' ),
					'value'       => isset( $role['_um_user_notes_enable_print'] ) ? $role['_um_user_notes_enable_print'] : $enable_print,
					'conditional' => array( '_um_disable_notes', '!=', 1 ),
				),
			),
		)
	)->render_form();
	?>
	<div class="clear"></div>
</div>
