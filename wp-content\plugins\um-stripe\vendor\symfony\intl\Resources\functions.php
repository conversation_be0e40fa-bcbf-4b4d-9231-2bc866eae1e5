<?php

namespace UM_Stripe\Vendor\UM_Stripe\Vendor;

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
use UM_Stripe\Vendor\Symfony\Intl\Globals\IntlGlobals;
if (!\function_exists('intl_is_failure') && !\function_exists('UM_Stripe\Vendor\intl_is_failure') && !\function_exists('UM_Stripe\Vendor\intl_is_failure')) {
    function intl_is_failure($error_code)
    {
        return IntlGlobals::isFailure($error_code);
    }
}
if (!\function_exists('intl_get_error_code') && !\function_exists('UM_Stripe\Vendor\intl_get_error_code') && !\function_exists('UM_Stripe\Vendor\intl_get_error_code')) {
    function intl_get_error_code()
    {
        return IntlGlobals::getErrorCode();
    }
}
if (!\function_exists('intl_get_error_message') && !\function_exists('UM_Stripe\Vendor\intl_get_error_message') && !\function_exists('UM_Stripe\Vendor\intl_get_error_message')) {
    function intl_get_error_message()
    {
        return IntlGlobals::getErrorMessage();
    }
}
if (!\function_exists('intl_error_name') && !\function_exists('UM_Stripe\Vendor\intl_error_name') && !\function_exists('UM_Stripe\Vendor\intl_error_name')) {
    function intl_error_name($error_code)
    {
        return IntlGlobals::getErrorName($error_code);
    }
}
