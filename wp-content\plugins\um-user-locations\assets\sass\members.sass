@import "selectors"
@import "mixins"
@import "colors"

.um_current_user_location
  width: 40px
  height: 36px
  background: #fff
  line-height: 36px
  font-size: 24px
  position: absolute
  top: 0
  right: 0
  text-align: center
  display: inline-block
  text-decoration: none !important
  box-shadow: none !important
  margin: 2px
  color: #aaaaaa
  &:hover
    color: #3ba1da

.um_user_location_g_autocomplete_map
  height: 300px
  width: 100%

.um-user-locations-map-field-view
  height: 300px
.um-user-locations-map-field-text
  margin: 0 0 10px 0


.#{$prefix}directory
  .um-member-directory-map
    margin: 0 0 10px 0
    width: 100%
    &.um-member-directory-hidden-map
      display: none
    &.um-map-inited img[src$="um_avatar_marker=1"]
      border: 2px solid #fff !important
      border-radius: 100%
      -moz-border-radius: 100%
      box-shadow: 0 2px 0 0 rgba(50, 50, 93, 0.1), 0 1px 0 0 rgba(0, 0, 0, 0.07) !important
      -webkit-box-shadow: 0 2px 0 0 rgba(50, 50, 93, 0.1), 0 1px 0 0 rgba(0, 0, 0, 0.07) !important
  .um-member-directory-map-controls
    +flex( row, space-between, baseline, nowrap )
    margin: 0 0 10px 0
    .um-member-directory-map-controls-half
      width: calc( 50% - 5px )
      .um-field-area
        margin: 10px 0 0 0
      .um-field
        padding: 0

  .um-member-directory-header
    .um-member-directory-header-row
      .um-search
        .um-search-filter
          &.um-user_location-filter-type
            position: relative

  .um-members-wrapper
    .um-members-map
      +flex( column, flex-start, stretch, nowrap )
      width: 100%
      .um-members-map-wrapper
        width: 100%
        margin: 0 0 10px 0
        height: 300px
      .um-members-map-grid
        width: 100%
        margin: 0 0 10px 0
        display: grid

        grid-template-rows: auto 1fr
        grid-template-columns: repeat(1, 1fr)
        grid-gap: 10px
        grid-auto-rows: minmax(max-content, auto)
        -ms-grid-template-rows: auto 1fr
        -ms-grid-template-columns: repeat(1, 1fr)
        -ms-grid-gap: 10px
        -ms-grid-auto-rows: minmax(max-content, auto)
        align-items: center

        .um-member
          +flex( column, flex-start, stretch, nowrap )
          width: 100%
          padding: 15px
          margin: 0
          background: #fff
          +border-box
          .um-member-status
            display: none
            background: #999
            &.awaiting_admin_review,
            &.inactive,
            &.rejected
              display: block
              width: 100%
              padding: 7px 15px
              margin-bottom: 10px
              color: #fff
              font-size: 13px
              +border-box
            &.awaiting_admin_review
              background: #c74a4a
          .um-member-card-container
            +flex( row, flex-start, flex-start, nowrap )
            width: 100%
            margin: 0 0 10px 0
            padding: 0
            .um-member-photo
              width: 100px
              margin: 0 15px 0 0
              padding: 0
              a
                width: 100px
                height: 100px
                display: block
                img
                  width: 100px
                  height: 100px
                  position: relative
                  top: 0
                  margin: 0
                  margin-bottom: 0
                  border: none

            .um-member-card
              +flex( row, flex-start, flex-start, nowrap )
              width: calc( 100% - 115px )
              padding: 0
              margin: 0
              +border-box
              &.no-photo
                width: 100%

              .um-member-card-content
                +flex( column, flex-start, stretch, nowrap )
                width: calc( 100% - 30px )
                margin: 0 10px 0 0
                .um-member-card-header
                  width: 100%
                  .um-member-name
                    margin: 0 0 4px 0
                    a
                      font-size: 16px
                      line-height: 26px
                      color: #444
                      font-weight: 700

                .um-member-tagline
                  +flex( row, flex-start, baseline, wrap )
                  width: 100%
                  font-size: 13px
                  //line-height: 22px
                  color: #999
                  padding: 0
                  box-sizing: border-box
                .um-member-meta-main
                  width: 100%
                  padding: 0
                  box-sizing: border-box
                  display: none
                  &.no-animate
                    display: block
                  .um-member-meta
                    float: left
                    width: 100%
                    display: block
                    margin: 10px 0 0 0
                    box-sizing: border-box
                    border: none

                    .um-member-metaline
                      +flex( row, flex-start, center, wrap )
                      font-size: 13px
                      padding: 12px 0 0 0
                      line-height: 16px
                      width: 100%

                    .um-member-connect
                      padding-top: 10px
                      a
                        display: inline-block
                        width: 40px
                        line-height: 40px
                        height: 40px
                        -moz-border-radius: 999px
                        -webkit-border-radius: 999px
                        border-radius: 999px
                        color: #fff !important
                        opacity: 0.85
                        margin: 0 1px
                        font-size: 22px
                        transition: 0.25s
                        text-align: center

                        &:hover
                          opacity: 1
                          color: #fff

              .um-member-card-actions
                +flex( column, flex-start, flex-end, nowrap )
                width: 20px
                text-align: right
                padding: 0
                .um-member-cog
                  position: relative
                  .um-member-actions-a
                    line-height: 1
                    display: block
                    color: #666
                    i
                      display: block
                      font-size: 20px
                      line-height: 1
                  .um-new-dropdown
                    width: 180px
                    right: 0
                    text-align: left

                a
                  box-sizing: border-box

          .um-member-card-footer
            +flex( row, flex-end, center, nowrap )
            width: 100%

            .um-member-card-footer-buttons
              +flex( row, flex-start, baseline, nowrap )
              width: calc( 100% - 145px )
              margin: 0 10px 0 0

              .um-members-list-footer-button-wrapper
                +flex( row, flex-start, baseline, nowrap )
                margin: 0
              //flex: 1 0 calc( ( 100% - 20px ) / 3 )
              & > :not(:last-child)
                margin: 0 10px 0 0
            .um-member-card-reveal-buttons
              width: 20px
              text-align: right
              .um-member-more
                +flex( column, flex-start, baseline, nowrap )
                a
                  color: #666
                  display: inline-block
                  i
                    display: block
                    font-size: 28px
                    height: 28px
                    line-height: 28px
              .um-member-less
                +flex( column, flex-start, baseline, nowrap )
                display: none
                a
                  color: #666
                  display: inline-block
                  i
                    display: block
                    font-size: 28px
                    height: 28px
                    line-height: 28px
            &.no-photo.no-reveal
              .um-member-card-footer-buttons
                width: 100%
            &.no-photo:not(.no-reveal)
              .um-member-card-footer-buttons
                width: calc( 100% - 30px )
            &.no-reveal:not(.no-photo)
              .um-member-card-footer-buttons
                width: calc( 100% - 125px )

  &.uimob340
    .um-member-directory-map-controls
      .um-member-directory-map-controls-half
        width: 100%
  &.uimob500
    .um-member-directory-map-controls
      .um-member-directory-map-controls-half
        width: 100%
  &.uimob800
    .um-member-directory-map-controls
      .um-member-directory-map-controls-half
        width: 100%


.um_user_location_gmap_infowindow
  text-align: center
  .um_user_location_infowindow_avatar
    display: inline-block
    text-align: center
    margin: 0 0 5px 0
    img
      margin: 0 auto
      border-radius: 100%
      display: block
      height: calc(3.75 * 1rem)
      min-height: inherit
      width: calc(3.75 * 1rem)
  .um_user_location_infowindow_title
    margin: 0 0 5px 0
    font-weight: bold
  .um_user_location_infowindow_content
    .um-member-infowindow-line
      margin: 0 0 5px 0