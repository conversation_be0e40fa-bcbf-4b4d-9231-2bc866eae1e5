<?php

declare (strict_types=1);
namespace WPMailSMTP\Vendor\ParagonIE\ConstantTime;

/**
 *  Copyright (c) 2016 - 2022 Paragon Initiative Enterprises.
 *  Copyright (c) 2014 <PERSON> "Sc00bz" <PERSON> (steve at tobtu dot com)
 *
 *  Permission is hereby granted, free of charge, to any person obtaining a copy
 *  of this software and associated documentation files (the "Software"), to deal
 *  in the Software without restriction, including without limitation the rights
 *  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 *  copies of the Software, and to permit persons to whom the Software is
 *  furnished to do so, subject to the following conditions:
 *
 *  The above copyright notice and this permission notice shall be included in all
 *  copies or substantial portions of the Software.
 *
 *  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 *  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 *  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 *  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 *  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 *  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 *  SOFTWARE.
 */
/**
 * Class Base64UrlSafe
 * [A-Z][a-z][0-9]\-_
 *
 * @package ParagonIE\ConstantTime
 */
abstract class Base64UrlSafe extends \WPMailSMTP\Vendor\ParagonIE\ConstantTime\Base64
{
    /**
     * Uses bitwise operators instead of table-lookups to turn 6-bit integers
     * into 8-bit integers.
     *
     * Base64 character set:
     * [A-Z]      [a-z]      [0-9]      -     _
     * 0x41-0x5a, 0x61-0x7a, 0x30-0x39, 0x2d, 0x5f
     *
     * @param int $src
     * @return int
     */
    protected static function decode6Bits(int $src) : int
    {
        $ret = -1;
        // if ($src > 0x40 && $src < 0x5b) $ret += $src - 0x41 + 1; // -64
        $ret += (0x40 - $src & $src - 0x5b) >> 8 & $src - 64;
        // if ($src > 0x60 && $src < 0x7b) $ret += $src - 0x61 + 26 + 1; // -70
        $ret += (0x60 - $src & $src - 0x7b) >> 8 & $src - 70;
        // if ($src > 0x2f && $src < 0x3a) $ret += $src - 0x30 + 52 + 1; // 5
        $ret += (0x2f - $src & $src - 0x3a) >> 8 & $src + 5;
        // if ($src == 0x2c) $ret += 62 + 1;
        $ret += (0x2c - $src & $src - 0x2e) >> 8 & 63;
        // if ($src == 0x5f) ret += 63 + 1;
        $ret += (0x5e - $src & $src - 0x60) >> 8 & 64;
        return $ret;
    }
    /**
     * Uses bitwise operators instead of table-lookups to turn 8-bit integers
     * into 6-bit integers.
     *
     * @param int $src
     * @return string
     */
    protected static function encode6Bits(int $src) : string
    {
        $diff = 0x41;
        // if ($src > 25) $diff += 0x61 - 0x41 - 26; // 6
        $diff += 25 - $src >> 8 & 6;
        // if ($src > 51) $diff += 0x30 - 0x61 - 26; // -75
        $diff -= 51 - $src >> 8 & 75;
        // if ($src > 61) $diff += 0x2d - 0x30 - 10; // -13
        $diff -= 61 - $src >> 8 & 13;
        // if ($src > 62) $diff += 0x5f - 0x2b - 1; // 3
        $diff += 62 - $src >> 8 & 49;
        return \pack('C', $src + $diff);
    }
}
