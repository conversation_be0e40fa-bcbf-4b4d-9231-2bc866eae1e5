<?php

declare( strict_types = 1 );

namespace Automattic\WooCommerce\Admin\API\AI;

use Automattic\WooCommerce\Blocks\AI\Connection;
use Automattic\WooCommerce\Blocks\AIContent\PatternsHelper;
use Automattic\WooCommerce\Blocks\AIContent\UpdatePatterns;
use WP_Error;
use WP_REST_Request;
use WP_REST_Response;

defined( 'ABSPATH' ) || exit;

/**
 * Patterns controller
 *
 * @internal
 */
class Patterns extends AIEndpoint {
	/**
	 * Endpoint.
	 *
	 * @var string
	 */
	protected $endpoint = 'patterns';

	/**
	 * Register routes.
	 */
	public function register_routes() {
		$this->register(
			array(
				array(
					'methods'             => \WP_REST_Server::CREATABLE,
					'callback'            => array( $this, 'update_patterns' ),
					'permission_callback' => array( Middleware::class, 'is_authorized' ),
					'args'                => array(
						'business_description' => array(
							'description' => __( 'The business description for a given store.', 'woocommerce' ),
							'type'        => 'string',
						),
						'images'               => array(
							'description' => __( 'The images for a given store.', 'woocommerce' ),
							'type'        => 'object',
						),
					),
				),
				array(
					'methods'             => \WP_REST_Server::DELETABLE,
					'callback'            => array( $this, 'delete_patterns' ),
					'permission_callback' => array( Middleware::class, 'is_authorized' ),
				),
			)
		);
	}

	/**
	 * Update patterns with the content and images powered by AI.
	 *
	 * @param  WP_REST_Request $request Request object.
	 *
	 * @return WP_Error|WP_REST_Response
	 */
	public function update_patterns( WP_REST_Request $request ) {
		$business_description = sanitize_text_field( wp_unslash( $request['business_description'] ) );

		$ai_connection = new Connection();

		$site_id = $ai_connection->get_site_id();

		if ( is_wp_error( $site_id ) ) {
			return $site_id;
		}

		$token = $ai_connection->get_jwt_token( $site_id );

		$images = $request['images'];

		try {
			( new UpdatePatterns() )->generate_content( $ai_connection, $token, $images, $business_description );
			return rest_ensure_response( array( 'ai_content_generated' => true ) );
		} catch ( \Exception $e ) {
			return rest_ensure_response( array( 'ai_content_generated' => false ) );
		}
	}

	/**
	 * Remove patterns generated by AI.
	 *
	 * @return WP_Error|WP_REST_Response
	 */
	public function delete_patterns() {
		PatternsHelper::delete_patterns_ai_data_post();
		return rest_ensure_response( array( 'removed' => true ) );
	}
}
