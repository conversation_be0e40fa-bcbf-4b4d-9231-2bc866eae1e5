# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.4.9] - 2025-04-28
### Changed
- Internal updates.

## [0.4.8] - 2025-04-14
### Changed
- Internal updates.

## [0.4.7] - 2025-03-31
### Changed
- Internal updates.

## [0.4.6] - 2025-03-21
### Changed
- Internal updates.

## [0.4.5] - 2025-03-17
### Changed
- Internal updates.

## [0.4.4] - 2025-03-12
### Changed
- Internal updates.

## [0.4.3] - 2025-03-05
### Changed
- Internal updates.

## [0.4.2] - 2025-02-24
### Changed
- Update dependencies.

## [0.4.1] - 2024-11-25
### Changed
- Update package dependencies. [#40258]

## [0.4.0] - 2024-11-14
### Removed
- General: Update minimum PHP version to 7.2. [#40147]

## [0.3.1] - 2024-11-04
### Added
- Enable test coverage. [#39961]

### Fixed
- Fix PHPUnit coverage warnings. [#39989]

## [0.3.0] - 2024-09-23
### Added
- IP Utils: added support for CIDR ranges. [#39425]

## [0.2.3] - 2024-08-23
### Changed
- Updated package dependencies. [#39004]

## [0.2.2] - 2024-03-12
### Changed
- Internal updates.

## [0.2.1] - 2023-11-21
### Changed
- Added a note of non-usage of PHP8+ functions yet. [#34137]

## [0.2.0] - 2023-11-20
### Changed
- Updated required PHP version to >= 7.0. [#34192]

## [0.1.6] - 2023-09-19

- Minor internal updates.

## [0.1.5] - 2023-08-23
### Changed
- Updated package dependencies. [#32605]

## [0.1.4] - 2023-05-29
### Changed
- Internal updates.

## [0.1.3] - 2023-05-11

- Updated package dependencies

## [0.1.2] - 2023-04-10
### Added
- Add Jetpack Autoloader package suggestion. [#29988]

## [0.1.1] - 2023-03-28
### Changed
- Update README.md [#28401]

## 0.1.0 - 2023-02-28
### Added
- Added a utility function to extract an array of IP addresses from a given string. [#29131]
- Add jetpack-ip package functionality [#28846]
- Initialized the package. [#28765]

[0.4.9]: https://github.com/automattic/jetpack-ip/compare/v0.4.8...v0.4.9
[0.4.8]: https://github.com/automattic/jetpack-ip/compare/v0.4.7...v0.4.8
[0.4.7]: https://github.com/automattic/jetpack-ip/compare/v0.4.6...v0.4.7
[0.4.6]: https://github.com/automattic/jetpack-ip/compare/v0.4.5...v0.4.6
[0.4.5]: https://github.com/automattic/jetpack-ip/compare/v0.4.4...v0.4.5
[0.4.4]: https://github.com/automattic/jetpack-ip/compare/v0.4.3...v0.4.4
[0.4.3]: https://github.com/automattic/jetpack-ip/compare/v0.4.2...v0.4.3
[0.4.2]: https://github.com/automattic/jetpack-ip/compare/v0.4.1...v0.4.2
[0.4.1]: https://github.com/automattic/jetpack-ip/compare/v0.4.0...v0.4.1
[0.4.0]: https://github.com/automattic/jetpack-ip/compare/v0.3.1...v0.4.0
[0.3.1]: https://github.com/automattic/jetpack-ip/compare/v0.3.0...v0.3.1
[0.3.0]: https://github.com/automattic/jetpack-ip/compare/v0.2.3...v0.3.0
[0.2.3]: https://github.com/automattic/jetpack-ip/compare/v0.2.2...v0.2.3
[0.2.2]: https://github.com/automattic/jetpack-ip/compare/v0.2.1...v0.2.2
[0.2.1]: https://github.com/automattic/jetpack-ip/compare/v0.2.0...v0.2.1
[0.2.0]: https://github.com/automattic/jetpack-ip/compare/v0.1.6...v0.2.0
[0.1.6]: https://github.com/automattic/jetpack-ip/compare/v0.1.5...v0.1.6
[0.1.5]: https://github.com/automattic/jetpack-ip/compare/v0.1.4...v0.1.5
[0.1.4]: https://github.com/automattic/jetpack-ip/compare/v0.1.3...v0.1.4
[0.1.3]: https://github.com/automattic/jetpack-ip/compare/v0.1.2...v0.1.3
[0.1.2]: https://github.com/automattic/jetpack-ip/compare/v0.1.1...v0.1.2
[0.1.1]: https://github.com/automattic/jetpack-ip/compare/v0.1.0...v0.1.1
