.jetpack-form-file-field__dropzone {
	position: relative;
}

.jetpack-form-file-field__dropzone.is-hidden {
	display: none;
}

.jetpack-form-file-field__dropzone-inner {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100%;
	height: 100%;
	z-index: 2;
	cursor: pointer;
}

.jetpack-form-file-field__dropzone input[type="file"] {
	display: none;
}

.jetpack-form-file-field__dropzone.is-dropping {
	cursor: pointer;
	transition: .2s;
	opacity: 1;
	filter: invert(0.1);
}

.jetpack-form-file-field__dropzone:hover {
	outline: var(--b) solid var(--c);
	outline-offset: var(--g);
}

.jetpack-form-file-field__content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: var(--jetpack--contact-form--input-padding, 16px);
}

.jetpack-form-file-field__icon {
	color: currentColor;
}

.jetpack-form-file-field__text {
	display: flex;
	flex-direction: column;
	gap: 8px;
	font-size: min(var(--jetpack--contact-form--font-size, inherit), 16px);
}

.jetpack-form-file-field__select-link {
	text-decoration: underline;
	font-weight: 700;
}

.jetpack-form-file-field__formats {
	opacity: 0.4;
	font-size: min(var(--jetpack--contact-form--font-size, inherit), 14px);
}

.jetpack-form-file-field__preview-wrap {
	padding: 0;
	visibility: hidden;
	display: flex;
	flex-direction: column;
	gap: 8px;
	transition: visibility 0.3s ease-in-out;
}

.jetpack-form-file-field__preview-wrap.is-active {
	visibility: visible;

}

.jetpack-form-file-field__preview {
	align-items: center;
	animation: jpShowFileField 0.35s cubic-bezier(.21,1.02,.73,1) forwards;
	display: flex;
	gap: 1em;
	opacity: 0;
	padding: 16px;
	position: relative;
	justify-content:start;
	border-radius: var( --jetpack--contact-form--border-radius, 0 );
	background-color: var(--jetpack--contact-form--input-background, #fff);
	color: var(--jetpack--contact-form--text-color, #333);
	border: var(--jetpack--contact-form--border-size, 1px) var( --jetpack--contact-form--border-style, solid) var(--jetpack--contact-form--border-color, #333 );
	font-size: 12px;
	line-height: 18px;
}

@media (prefers-reduced-motion) {

	.jetpack-form-file-field__preview {
		animation: none;
	}
}

.jetpack-form-file-field__preview::before {
	content: '';
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 1;
}

.jetpack-form-file-field__preview > * {
	z-index: 2;
	transition: height 0.3s ease;
}

@media (prefers-reduced-motion) {

	.jetpack-form-file-field__preview > *,
	.jetpack-form-file-field__preview:hover::before {
		transition: none;
	}
}


.jetpack-form-file-field__preview.fade-out {
	animation: jpFadeOutFileField 0.3s forwards;
}

@media (prefers-reduced-motion) {

	.jetpack-form-file-field__preview.fade-out {
		animation: none;
	}
}

.grunion-field-file-wrap .wp-block-jetpack-contact-form {
	gap: 8px;
}

.jetpack-form-file-field__file-wrap {
	align-items: baseline;
	display: flex;
	flex-direction: column;
	font-size: 0.9em;
	flex-grow: 2;
}

.jetpack-form-file-field__file-name {
	text-align: left;
}

.jetpack-form-file-field__remove {
	position: relative;
	min-width: 33px;
	height: 33px;
	margin-right: 10px;
}

.jetpack-form-file-field__remove:hover {
	cursor: pointer;
	border-radius: 50%;
	background-color: var(--jetpack--contact-form--input-background, #fff );
	filter: invert(0.1);
	transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
}

.jetpack-form-file-field__remove::before,
.jetpack-form-file-field__remove::after {
	position: absolute;
	left: 16px;
	content: ' ';
	height: 18px;
	width: 1px;
	top: 7px;
	background-color: var(--jetpack--contact-form--text-color, #333 );
}

.jetpack-form-file-field__remove::before {
	transform: rotate(45deg);
}

.jetpack-form-file-field__remove::after {
	transform: rotate(-45deg);
}

.jetpack-form-file-field__image-wrap {
	min-width: 46px;
	height: 46px;
	border-radius: calc(infinity * 1px);
	position: relative;
}

.jetpack-form-file-field__image {
	content: '';
	aspect-ratio: 1 / 1;
	background-position: center;
	background-size: cover;
	outline: 1 solid  var( --jetpack--contact-form--button-primary--text-color, #FFF );
	background-color: #333;
	border-radius: 50%;
	outline-offset:0;
	width: 40px;
	min-width: 40px;
	left: 3px;
	position: absolute;
	top: 3px;
	z-index: 2;
}

.jetpack-form-file-field__preview.is-complete .jetpack-form-file-field__image {
	width: 46px;
	min-width: 46px;
	height: 46px;
	left: 0;
	top: 0;
	outline: 1 solid  var( --jetpack--contact-form--button-primary--text-color, #FFF );
	transition: width 0.5s ease-in-out;
}

.jetpack-form-file-field__progress-bar {
	content:'';
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	border-radius: 50%;
	background: conic-gradient(
		var(--jetpack--contact-form--border-color, #333 )
		calc(var(--progress, 3) * 1%),
		transparent calc(var(--progress, 3) * 1%)
	);

	z-index: -1;
	animation: spin 1.2s ease infinite;
	opacity: 0.8;
	width: 46px;
	height: 46px;
}

.jetpack-form-file-field__preview.is-error .jetpack-form-file-field__progress-bar,
.jetpack-form-file-field__preview.is-complete .jetpack-form-file-field__progress-bar {
	animation: none;
	opacity: 0;
}

@keyframes spin {

    from {transform:rotate(0deg);}

    to {transform:rotate(360deg);}
}

.jetpack-form-file-field__file-info {
	opacity: 0.8;
	display: inline-flex;
	gap: 4px;
}

.jetpack-form-file-field__file-info .jetpack-form-file-field__file-size {
	white-space: nowrap;
}

.jetpack-form-file-field__success {
	color: #008A20;
	display: none;
}

.jetpack-form-file-field__error {
	color: #b32d2e;
}

.jetpack-form-file-field__preview.is-error .jetpack-form-file-field__uploading,
.jetpack-form-file-field__preview.is-complete .jetpack-form-file-field__uploading {
	display: none;
}

.jetpack-form-file-field__preview.is-complete .jetpack-form-file-field__success {
	display: inline;
}

@keyframes jpShowFileField {

	0% {transform: translate3d(0,-10px, 0) scale(.90); opacity:.5;}

	100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
}

@keyframes jpFadeOutFileField {

	0% {
		opacity: 1;
		transform: scale(1);
	}

	100% {
		opacity: 0;
		transform: scale(0.98);
	}
}
