<?php
namespace um_ext\um_user_photos\ajax;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Init
 *
 * @package um_ext\um_user_photos\ajax
 */
class Init {

	/**
	 * Create classes' instances where __construct isn't empty for hooks init
	 */
	public function includes() {
		$this->albums();
		$this->comments()->hooks();
		$this->gallery();
		$this->photos();
		$this->uploader();
	}

	/**
	 * @return Albums
	 */
	public function albums() {
		if ( empty( UM()->classes['um_ext\um_user_photos\ajax\albums'] ) ) {
			UM()->classes['um_ext\um_user_photos\ajax\albums'] = new Albums();
		}
		return UM()->classes['um_ext\um_user_photos\ajax\albums'];
	}

	/**
	 * @return Comments
	 */
	public function comments() {
		if ( empty( UM()->classes['um_ext\um_user_photos\ajax\comments'] ) ) {
			UM()->classes['um_ext\um_user_photos\ajax\comments'] = new Comments();
		}
		return UM()->classes['um_ext\um_user_photos\ajax\comments'];
	}

	/**
	 * @return Gallery
	 */
	public function gallery() {
		if ( empty( UM()->classes['um_ext\um_user_photos\ajax\gallery'] ) ) {
			UM()->classes['um_ext\um_user_photos\ajax\gallery'] = new Gallery();
		}
		return UM()->classes['um_ext\um_user_photos\ajax\gallery'];
	}

	/**
	 * @return Photos
	 */
	public function photos() {
		if ( empty( UM()->classes['um_ext\um_user_photos\ajax\photos'] ) ) {
			UM()->classes['um_ext\um_user_photos\ajax\photos'] = new Photos();
		}
		return UM()->classes['um_ext\um_user_photos\ajax\photos'];
	}

	/**
	 * @return Uploader
	 */
	public function uploader() {
		if ( empty( UM()->classes['um_ext\um_user_photos\ajax\uploader'] ) ) {
			UM()->classes['um_ext\um_user_photos\ajax\uploader'] = new Uploader();
		}
		return UM()->classes['um_ext\um_user_photos\ajax\uploader'];
	}
}
