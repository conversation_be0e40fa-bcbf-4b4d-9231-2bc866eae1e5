<?php
/**
 * Template for the UM User Photos, The "Albums" block
 *
 * Page: "Profile", tab "Photos"
 * Hook: 'ultimatemember_gallery'
 * Caller: User_Photos_Shortcodes->get_gallery_content() method
 * @version 2.1.9
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/gallery.php
 * @var int       $user_id
 * @var bool      $is_my_profile
 * @var WP_Post[] $albums
 * @var int       $total
 * @var int       $last_id
 * @var int       $per_page
 * @var int       $columns
 * @var string    $default
 * @var bool      $disable_title
 * @var bool      $is_profile_tab
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

$wrapper_classes = array( 'um-user-photos-gallery' );
if ( ! $is_profile_tab ) {
	$wrapper_classes[] = 'um';
}
?>

<div class="<?php echo esc_attr( implode( ' ', $wrapper_classes ) ); ?>">
	<?php
	if ( $is_my_profile ) {
		UM()->get_template(
			'v3/gallery-head.php',
			UM_USER_PHOTOS_PLUGIN,
			compact( 'is_my_profile', 'user_id' ),
			true
		);
	}

	echo wp_kses( UM()->frontend()::layouts()::ajax_loader( 'l', array( 'classes' => array( 'um-user-photos-albums-loader', 'um-display-none' ) ) ), UM()->get_allowed_html( 'templates' ) );

	if ( ! empty( $albums ) ) {
		?>
		<div class="um-user-photos-albums um-grid um-grid-col-<?php echo esc_attr( $columns ); ?>">
			<?php
			foreach ( $albums as $album ) {
				$photos = get_post_meta( $album->ID, '_photos', true );
				if ( $photos ) {
					$count = count( $photos );
					// translators: %s is a photos count.
					$count_msg = sprintf( _n( '%s Photo', '%s Photos', $count, 'um-user-photos' ), number_format_i18n( $count ) );
				} else {
					$count_msg = false;
				}

				$img = UM()->User_Photos()->common()->album()->get_cover( $album->ID );

				$data_t = array(
					'id'            => $album->ID,
					'title'         => get_the_title( $album->ID ),
					'author'        => $album->post_author,
					'count_msg'     => $count_msg,
					'img'           => $img,
					'disable_title' => $disable_title,
					'default'       => $default,
					'context'       => 'gallery',
					'url'           => '',
				);
				UM()->get_template( 'v3/album-block.php', UM_USER_PHOTOS_PLUGIN, $data_t, true );
			}
			?>
		</div>
		<?php
		if ( $total > $per_page ) {
			$loader = UM()->frontend()::layouts()::ajax_loader(
				'm',
				array(
					'classes' => array(
						'um-user-photos-loader',
						'um-display-none',
					),
				)
			);
			$button = UM()->frontend()::layouts()::button(
				esc_html__( 'Load more', 'um-user-photos' ),
				array(
					'classes' => array( 'um-user-photos-load-more-albums' ),
					'design'  => 'tertiary-gray',
					'type'    => 'button',
					'size'    => 's',
					'data'    => array(
						'wpnonce'  => wp_create_nonce( 'um_user_photos_load_more_albums' ),
						'profile'  => $user_id,
						'per_page' => $per_page,
						'page'     => 1,
						'pages'    => ceil( $total / $per_page ),
						'last_id'  => $last_id,
					),
				)
			);
			echo wp_kses( $loader . $button, UM()->get_allowed_html( 'templates' ) );
		}
	} else {
		?>
		<p class="um-supporting-text um-align-center"><?php esc_html_e( 'Nothing to display', 'um-user-photos' ); ?></p>
		<?php
	}
	?>
</div>
