(()=>{var e={1113:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(6087);const o=(0,n.forwardRef)((function({icon:e,size:t=24,...r},o){return(0,n.cloneElement)(e,{width:t,height:t,...r,ref:o})}))},3512:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(5573),o=r(790);const s=(0,o.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,o.jsx)(n.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})})},6941:(e,t,r)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const r="color: "+this.color;t.splice(1,0,r,"color: inherit");let n=0,o=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(n++,"%c"===e&&(o=n))})),t.splice(o,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=r(3212)(t);const{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},3212:(e,t,r)=>{e.exports=function(e){function t(e){let r,o,s,a=null;function c(...e){if(!c.enabled)return;const n=c,o=Number(new Date),s=o-(r||o);n.diff=s,n.prev=r,n.curr=o,r=o,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let a=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((r,o)=>{if("%%"===r)return"%";a++;const s=t.formatters[o];if("function"==typeof s){const t=e[a];r=s.call(n,t),e.splice(a,1),a--}return r})),t.formatArgs.call(n,e);(n.log||t.log).apply(n,e)}return c.namespace=e,c.useColors=t.useColors(),c.color=t.selectColor(e),c.extend=n,c.destroy=t.destroy,Object.defineProperty(c,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==a?a:(o!==t.namespaces&&(o=t.namespaces,s=t.enabled(e)),s),set:e=>{a=e}}),"function"==typeof t.init&&t.init(c),c}function n(e,r){const n=t(this.namespace+(void 0===r?":":r)+e);return n.log=this.log,n}function o(e,t){let r=0,n=0,o=-1,s=0;for(;r<e.length;)if(n<t.length&&(t[n]===e[r]||"*"===t[n]))"*"===t[n]?(o=n,s=r,n++):(r++,n++);else{if(-1===o)return!1;n=o+1,s++,r=s}for(;n<t.length&&"*"===t[n];)n++;return n===t.length}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names,...t.skips.map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const r=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of r)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const r of t.skips)if(o(e,r))return!1;for(const r of t.names)if(o(e,r))return!0;return!1},t.humanize=r(4997),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((r=>{t[r]=e[r]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t),r|=0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}},7792:(e,t,r)=>{var n=r(426)(r(1665),"DataView");e.exports=n},8985:(e,t,r)=>{var n=r(8276),o=r(5986),s=r(7549),a=r(9297),c=r(1033);function i(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}i.prototype.clear=n,i.prototype.delete=o,i.prototype.get=s,i.prototype.has=a,i.prototype.set=c,e.exports=i},8603:(e,t,r)=>{var n=r(4346),o=r(876),s=r(4783),a=r(851),c=r(9643);function i(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}i.prototype.clear=n,i.prototype.delete=o,i.prototype.get=s,i.prototype.has=a,i.prototype.set=c,e.exports=i},3579:(e,t,r)=>{var n=r(426)(r(1665),"Map");e.exports=n},3017:(e,t,r)=>{var n=r(5444),o=r(9634),s=r(3725),a=r(2273),c=r(729);function i(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}i.prototype.clear=n,i.prototype.delete=o,i.prototype.get=s,i.prototype.has=a,i.prototype.set=c,e.exports=i},1232:(e,t,r)=>{var n=r(426)(r(1665),"Promise");e.exports=n},7749:(e,t,r)=>{var n=r(426)(r(1665),"Set");e.exports=n},941:(e,t,r)=>{var n=r(8603),o=r(1696),s=r(4838),a=r(1025),c=r(4205),i=r(7333);function l(e){var t=this.__data__=new n(e);this.size=t.size}l.prototype.clear=o,l.prototype.delete=s,l.prototype.get=a,l.prototype.has=c,l.prototype.set=i,e.exports=l},8693:(e,t,r)=>{var n=r(1665).Symbol;e.exports=n},8216:(e,t,r)=>{var n=r(1665).Uint8Array;e.exports=n},7483:(e,t,r)=>{var n=r(426)(r(1665),"WeakMap");e.exports=n},3605:e=>{e.exports=function(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}},237:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n&&!1!==t(e[r],r,e););return e}},4046:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=0,s=[];++r<n;){var a=e[r];t(a,r,e)&&(s[o++]=a)}return s}},1171:(e,t,r)=>{var n=r(6628),o=r(8960),s=r(5413),a=r(52),c=r(101),i=r(2163),l=Object.prototype.hasOwnProperty;e.exports=function(e,t){var r=s(e),u=!r&&o(e),p=!r&&!u&&a(e),d=!r&&!u&&!p&&i(e),f=r||u||p||d,h=f?n(e.length,String):[],g=h.length;for(var m in e)!t&&!l.call(e,m)||f&&("length"==m||p&&("offset"==m||"parent"==m)||d&&("buffer"==m||"byteLength"==m||"byteOffset"==m)||c(m,g))||h.push(m);return h}},600:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=Array(n);++r<n;)o[r]=t(e[r],r,e);return o}},8812:e=>{e.exports=function(e,t){for(var r=-1,n=t.length,o=e.length;++r<n;)e[o+r]=t[r];return e}},9431:(e,t,r)=>{var n=r(7772),o=r(2604),s=Object.prototype.hasOwnProperty;e.exports=function(e,t,r){var a=e[t];s.call(e,t)&&o(a,r)&&(void 0!==r||t in e)||n(e,t,r)}},8853:(e,t,r)=>{var n=r(2604);e.exports=function(e,t){for(var r=e.length;r--;)if(n(e[r][0],t))return r;return-1}},3121:(e,t,r)=>{var n=r(3771),o=r(2610);e.exports=function(e,t){return e&&n(t,o(t),e)}},2282:(e,t,r)=>{var n=r(3771),o=r(9701);e.exports=function(e,t){return e&&n(t,o(t),e)}},7772:(e,t,r)=>{var n=r(5255);e.exports=function(e,t,r){"__proto__"==t&&n?n(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}},7563:(e,t,r)=>{var n=r(941),o=r(237),s=r(9431),a=r(3121),c=r(2282),i=r(8158),l=r(5483),u=r(3139),p=r(3472),d=r(7550),f=r(2025),h=r(4713),g=r(3625),m=r(1707),v=r(5541),y=r(5413),b=r(52),w=r(4574),j=r(9169),x=r(4076),k=r(2610),A=r(9701),C="[object Arguments]",E="[object Function]",_="[object Object]",S={};S[C]=S["[object Array]"]=S["[object ArrayBuffer]"]=S["[object DataView]"]=S["[object Boolean]"]=S["[object Date]"]=S["[object Float32Array]"]=S["[object Float64Array]"]=S["[object Int8Array]"]=S["[object Int16Array]"]=S["[object Int32Array]"]=S["[object Map]"]=S["[object Number]"]=S[_]=S["[object RegExp]"]=S["[object Set]"]=S["[object String]"]=S["[object Symbol]"]=S["[object Uint8Array]"]=S["[object Uint8ClampedArray]"]=S["[object Uint16Array]"]=S["[object Uint32Array]"]=!0,S["[object Error]"]=S[E]=S["[object WeakMap]"]=!1,e.exports=function e(t,r,O,N,P,R){var M,z=1&r,I=2&r,F=4&r;if(O&&(M=P?O(t,N,P,R):O(t)),void 0!==M)return M;if(!j(t))return t;var T=y(t);if(T){if(M=g(t),!z)return l(t,M)}else{var D=h(t),L=D==E||"[object GeneratorFunction]"==D;if(b(t))return i(t,z);if(D==_||D==C||L&&!P){if(M=I||L?{}:v(t),!z)return I?p(t,c(M,t)):u(t,a(M,t))}else{if(!S[D])return P?t:{};M=m(t,D,z)}}R||(R=new n);var V=R.get(t);if(V)return V;R.set(t,M),x(t)?t.forEach((function(n){M.add(e(n,r,O,n,t,R))})):w(t)&&t.forEach((function(n,o){M.set(o,e(n,r,O,o,t,R))}));var U=T?void 0:(F?I?f:d:I?A:k)(t);return o(U||t,(function(n,o){U&&(n=t[o=n]),s(M,o,e(n,r,O,o,t,R))})),M}},6764:(e,t,r)=>{var n=r(9169),o=Object.create,s=function(){function e(){}return function(t){if(!n(t))return{};if(o)return o(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}();e.exports=s},5292:(e,t,r)=>{var n=r(8812),o=r(7655);e.exports=function e(t,r,s,a,c){var i=-1,l=t.length;for(s||(s=o),c||(c=[]);++i<l;){var u=t[i];r>0&&s(u)?r>1?e(u,r-1,s,a,c):n(c,u):a||(c[c.length]=u)}return c}},2530:(e,t,r)=>{var n=r(7141),o=r(7489);e.exports=function(e,t){for(var r=0,s=(t=n(t,e)).length;null!=e&&r<s;)e=e[o(t[r++])];return r&&r==s?e:void 0}},2443:(e,t,r)=>{var n=r(8812),o=r(5413);e.exports=function(e,t,r){var s=t(e);return o(e)?s:n(s,r(e))}},740:(e,t,r)=>{var n=r(8693),o=r(9079),s=r(9170),a=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?o(e):s(e)}},4497:e=>{e.exports=function(e,t){return null!=e&&t in Object(e)}},1210:(e,t,r)=>{var n=r(740),o=r(1726);e.exports=function(e){return o(e)&&"[object Arguments]"==n(e)}},8856:(e,t,r)=>{var n=r(4713),o=r(1726);e.exports=function(e){return o(e)&&"[object Map]"==n(e)}},431:(e,t,r)=>{var n=r(4406),o=r(9924),s=r(9169),a=r(8709),c=/^\[object .+?Constructor\]$/,i=Function.prototype,l=Object.prototype,u=i.toString,p=l.hasOwnProperty,d=RegExp("^"+u.call(p).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!s(e)||o(e))&&(n(e)?d:c).test(a(e))}},6514:(e,t,r)=>{var n=r(4713),o=r(1726);e.exports=function(e){return o(e)&&"[object Set]"==n(e)}},4897:(e,t,r)=>{var n=r(740),o=r(8762),s=r(1726),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,e.exports=function(e){return s(e)&&o(e.length)&&!!a[n(e)]}},5860:(e,t,r)=>{var n=r(5043),o=r(1958),s=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return o(e);var t=[];for(var r in Object(e))s.call(e,r)&&"constructor"!=r&&t.push(r);return t}},5259:(e,t,r)=>{var n=r(9169),o=r(5043),s=r(7249),a=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return s(e);var t=o(e),r=[];for(var c in e)("constructor"!=c||!t&&a.call(e,c))&&r.push(c);return r}},7378:(e,t,r)=>{var n=r(5312),o=r(2051);e.exports=function(e,t){return n(e,t,(function(t,r){return o(e,r)}))}},5312:(e,t,r)=>{var n=r(2530),o=r(1502),s=r(7141);e.exports=function(e,t,r){for(var a=-1,c=t.length,i={};++a<c;){var l=t[a],u=n(e,l);r(u,l)&&o(i,s(l,e),u)}return i}},9514:(e,t,r)=>{var n=r(3428),o=r(6905),s=r(5261);e.exports=function(e,t){return s(o(e,t,n),e+"")}},1502:(e,t,r)=>{var n=r(9431),o=r(7141),s=r(101),a=r(9169),c=r(7489);e.exports=function(e,t,r,i){if(!a(e))return e;for(var l=-1,u=(t=o(t,e)).length,p=u-1,d=e;null!=d&&++l<u;){var f=c(t[l]),h=r;if("__proto__"===f||"constructor"===f||"prototype"===f)return e;if(l!=p){var g=d[f];void 0===(h=i?i(g,f,d):void 0)&&(h=a(g)?g:s(t[l+1])?[]:{})}n(d,f,h),d=d[f]}return e}},9334:(e,t,r)=>{var n=r(9274),o=r(5255),s=r(3428),a=o?function(e,t){return o(e,"toString",{configurable:!0,enumerable:!1,value:n(t),writable:!0})}:s;e.exports=a},4580:e=>{e.exports=function(e,t,r){var n=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(r=r>o?o:r)<0&&(r+=o),o=t>r?0:r-t>>>0,t>>>=0;for(var s=Array(o);++n<o;)s[n]=e[n+t];return s}},6628:e=>{e.exports=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}},1856:(e,t,r)=>{var n=r(8693),o=r(600),s=r(5413),a=r(7614),c=n?n.prototype:void 0,i=c?c.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(s(t))return o(t,e)+"";if(a(t))return i?i.call(t):"";var r=t+"";return"0"==r&&1/t==-1/0?"-0":r}},8321:e=>{e.exports=function(e){return function(t){return e(t)}}},2439:(e,t,r)=>{var n=r(7141),o=r(6214),s=r(2109),a=r(7489);e.exports=function(e,t){return t=n(t,e),null==(e=s(e,t))||delete e[a(o(t))]}},7141:(e,t,r)=>{var n=r(5413),o=r(6022),s=r(3894),a=r(6938);e.exports=function(e,t){return n(e)?e:o(e,t)?[e]:s(a(e))}},529:(e,t,r)=>{var n=r(8216);e.exports=function(e){var t=new e.constructor(e.byteLength);return new n(t).set(new n(e)),t}},8158:(e,t,r)=>{e=r.nmd(e);var n=r(1665),o=t&&!t.nodeType&&t,s=o&&e&&!e.nodeType&&e,a=s&&s.exports===o?n.Buffer:void 0,c=a?a.allocUnsafe:void 0;e.exports=function(e,t){if(t)return e.slice();var r=e.length,n=c?c(r):new e.constructor(r);return e.copy(n),n}},8533:(e,t,r)=>{var n=r(529);e.exports=function(e,t){var r=t?n(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.byteLength)}},4029:e=>{var t=/\w*$/;e.exports=function(e){var r=new e.constructor(e.source,t.exec(e));return r.lastIndex=e.lastIndex,r}},4612:(e,t,r)=>{var n=r(8693),o=n?n.prototype:void 0,s=o?o.valueOf:void 0;e.exports=function(e){return s?Object(s.call(e)):{}}},7317:(e,t,r)=>{var n=r(529);e.exports=function(e,t){var r=t?n(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}},5483:e=>{e.exports=function(e,t){var r=-1,n=e.length;for(t||(t=Array(n));++r<n;)t[r]=e[r];return t}},3771:(e,t,r)=>{var n=r(9431),o=r(7772);e.exports=function(e,t,r,s){var a=!r;r||(r={});for(var c=-1,i=t.length;++c<i;){var l=t[c],u=s?s(r[l],e[l],l,r,e):void 0;void 0===u&&(u=e[l]),a?o(r,l,u):n(r,l,u)}return r}},3139:(e,t,r)=>{var n=r(3771),o=r(5332);e.exports=function(e,t){return n(e,o(e),t)}},3472:(e,t,r)=>{var n=r(3771),o=r(3899);e.exports=function(e,t){return n(e,o(e),t)}},1893:(e,t,r)=>{var n=r(1665)["__core-js_shared__"];e.exports=n},6579:(e,t,r)=>{var n=r(9514),o=r(84);e.exports=function(e){return n((function(t,r){var n=-1,s=r.length,a=s>1?r[s-1]:void 0,c=s>2?r[2]:void 0;for(a=e.length>3&&"function"==typeof a?(s--,a):void 0,c&&o(r[0],r[1],c)&&(a=s<3?void 0:a,s=1),t=Object(t);++n<s;){var i=r[n];i&&e(t,i,n,a)}return t}))}},9214:(e,t,r)=>{var n=r(3663);e.exports=function(e){return n(e)?void 0:e}},5255:(e,t,r)=>{var n=r(426),o=function(){try{var e=n(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();e.exports=o},3092:(e,t,r)=>{var n=r(8502),o=r(6905),s=r(5261);e.exports=function(e){return s(o(e,void 0,n),e+"")}},9324:e=>{var t="object"==typeof window&&window&&window.Object===Object&&window;e.exports=t},7550:(e,t,r)=>{var n=r(2443),o=r(5332),s=r(2610);e.exports=function(e){return n(e,s,o)}},2025:(e,t,r)=>{var n=r(2443),o=r(3899),s=r(9701);e.exports=function(e){return n(e,s,o)}},1639:(e,t,r)=>{var n=r(2950);e.exports=function(e,t){var r=e.__data__;return n(t)?r["string"==typeof t?"string":"hash"]:r.map}},426:(e,t,r)=>{var n=r(431),o=r(3404);e.exports=function(e,t){var r=o(e,t);return n(r)?r:void 0}},603:(e,t,r)=>{var n=r(1427)(Object.getPrototypeOf,Object);e.exports=n},9079:(e,t,r)=>{var n=r(8693),o=Object.prototype,s=o.hasOwnProperty,a=o.toString,c=n?n.toStringTag:void 0;e.exports=function(e){var t=s.call(e,c),r=e[c];try{e[c]=void 0;var n=!0}catch(e){}var o=a.call(e);return n&&(t?e[c]=r:delete e[c]),o}},5332:(e,t,r)=>{var n=r(4046),o=r(565),s=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,c=a?function(e){return null==e?[]:(e=Object(e),n(a(e),(function(t){return s.call(e,t)})))}:o;e.exports=c},3899:(e,t,r)=>{var n=r(8812),o=r(603),s=r(5332),a=r(565),c=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)n(t,s(e)),e=o(e);return t}:a;e.exports=c},4713:(e,t,r)=>{var n=r(7792),o=r(3579),s=r(1232),a=r(7749),c=r(7483),i=r(740),l=r(8709),u="[object Map]",p="[object Promise]",d="[object Set]",f="[object WeakMap]",h="[object DataView]",g=l(n),m=l(o),v=l(s),y=l(a),b=l(c),w=i;(n&&w(new n(new ArrayBuffer(1)))!=h||o&&w(new o)!=u||s&&w(s.resolve())!=p||a&&w(new a)!=d||c&&w(new c)!=f)&&(w=function(e){var t=i(e),r="[object Object]"==t?e.constructor:void 0,n=r?l(r):"";if(n)switch(n){case g:return h;case m:return u;case v:return p;case y:return d;case b:return f}return t}),e.exports=w},3404:e=>{e.exports=function(e,t){return null==e?void 0:e[t]}},138:(e,t,r)=>{var n=r(7141),o=r(8960),s=r(5413),a=r(101),c=r(8762),i=r(7489);e.exports=function(e,t,r){for(var l=-1,u=(t=n(t,e)).length,p=!1;++l<u;){var d=i(t[l]);if(!(p=null!=e&&r(e,d)))break;e=e[d]}return p||++l!=u?p:!!(u=null==e?0:e.length)&&c(u)&&a(d,u)&&(s(e)||o(e))}},8276:(e,t,r)=>{var n=r(6310);e.exports=function(){this.__data__=n?n(null):{},this.size=0}},5986:e=>{e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},7549:(e,t,r)=>{var n=r(6310),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(n){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(t,e)?t[e]:void 0}},9297:(e,t,r)=>{var n=r(6310),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return n?void 0!==t[e]:o.call(t,e)}},1033:(e,t,r)=>{var n=r(6310);e.exports=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=n&&void 0===t?"__lodash_hash_undefined__":t,this}},3625:e=>{var t=Object.prototype.hasOwnProperty;e.exports=function(e){var r=e.length,n=new e.constructor(r);return r&&"string"==typeof e[0]&&t.call(e,"index")&&(n.index=e.index,n.input=e.input),n}},1707:(e,t,r)=>{var n=r(529),o=r(8533),s=r(4029),a=r(4612),c=r(7317);e.exports=function(e,t,r){var i=e.constructor;switch(t){case"[object ArrayBuffer]":return n(e);case"[object Boolean]":case"[object Date]":return new i(+e);case"[object DataView]":return o(e,r);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return c(e,r);case"[object Map]":case"[object Set]":return new i;case"[object Number]":case"[object String]":return new i(e);case"[object RegExp]":return s(e);case"[object Symbol]":return a(e)}}},5541:(e,t,r)=>{var n=r(6764),o=r(603),s=r(5043);e.exports=function(e){return"function"!=typeof e.constructor||s(e)?{}:n(o(e))}},7655:(e,t,r)=>{var n=r(8693),o=r(8960),s=r(5413),a=n?n.isConcatSpreadable:void 0;e.exports=function(e){return s(e)||o(e)||!!(a&&e&&e[a])}},101:e=>{var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,r){var n=typeof e;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&t.test(e))&&e>-1&&e%1==0&&e<r}},84:(e,t,r)=>{var n=r(2604),o=r(3266),s=r(101),a=r(9169);e.exports=function(e,t,r){if(!a(r))return!1;var c=typeof t;return!!("number"==c?o(r)&&s(t,r.length):"string"==c&&t in r)&&n(r[t],e)}},6022:(e,t,r)=>{var n=r(5413),o=r(7614),s=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;e.exports=function(e,t){if(n(e))return!1;var r=typeof e;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=e&&!o(e))||(a.test(e)||!s.test(e)||null!=t&&e in Object(t))}},2950:e=>{e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},9924:(e,t,r)=>{var n,o=r(1893),s=(n=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"";e.exports=function(e){return!!s&&s in e}},5043:e=>{var t=Object.prototype;e.exports=function(e){var r=e&&e.constructor;return e===("function"==typeof r&&r.prototype||t)}},4346:e=>{e.exports=function(){this.__data__=[],this.size=0}},876:(e,t,r)=>{var n=r(8853),o=Array.prototype.splice;e.exports=function(e){var t=this.__data__,r=n(t,e);return!(r<0)&&(r==t.length-1?t.pop():o.call(t,r,1),--this.size,!0)}},4783:(e,t,r)=>{var n=r(8853);e.exports=function(e){var t=this.__data__,r=n(t,e);return r<0?void 0:t[r][1]}},851:(e,t,r)=>{var n=r(8853);e.exports=function(e){return n(this.__data__,e)>-1}},9643:(e,t,r)=>{var n=r(8853);e.exports=function(e,t){var r=this.__data__,o=n(r,e);return o<0?(++this.size,r.push([e,t])):r[o][1]=t,this}},5444:(e,t,r)=>{var n=r(8985),o=r(8603),s=r(3579);e.exports=function(){this.size=0,this.__data__={hash:new n,map:new(s||o),string:new n}}},9634:(e,t,r)=>{var n=r(1639);e.exports=function(e){var t=n(this,e).delete(e);return this.size-=t?1:0,t}},3725:(e,t,r)=>{var n=r(1639);e.exports=function(e){return n(this,e).get(e)}},2273:(e,t,r)=>{var n=r(1639);e.exports=function(e){return n(this,e).has(e)}},729:(e,t,r)=>{var n=r(1639);e.exports=function(e,t){var r=n(this,e),o=r.size;return r.set(e,t),this.size+=r.size==o?0:1,this}},9580:(e,t,r)=>{var n=r(9284);e.exports=function(e){var t=n(e,(function(e){return 500===r.size&&r.clear(),e})),r=t.cache;return t}},6310:(e,t,r)=>{var n=r(426)(Object,"create");e.exports=n},1958:(e,t,r)=>{var n=r(1427)(Object.keys,Object);e.exports=n},7249:e=>{e.exports=function(e){var t=[];if(null!=e)for(var r in Object(e))t.push(r);return t}},1629:(e,t,r)=>{e=r.nmd(e);var n=r(9324),o=t&&!t.nodeType&&t,s=o&&e&&!e.nodeType&&e,a=s&&s.exports===o&&n.process,c=function(){try{var e=s&&s.require&&s.require("util").types;return e||a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=c},9170:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},1427:e=>{e.exports=function(e,t){return function(r){return e(t(r))}}},6905:(e,t,r)=>{var n=r(3605),o=Math.max;e.exports=function(e,t,r){return t=o(void 0===t?e.length-1:t,0),function(){for(var s=arguments,a=-1,c=o(s.length-t,0),i=Array(c);++a<c;)i[a]=s[t+a];a=-1;for(var l=Array(t+1);++a<t;)l[a]=s[a];return l[t]=r(i),n(e,this,l)}}},2109:(e,t,r)=>{var n=r(2530),o=r(4580);e.exports=function(e,t){return t.length<2?e:n(e,o(t,0,-1))}},1665:(e,t,r)=>{var n=r(9324),o="object"==typeof self&&self&&self.Object===Object&&self,s=n||o||Function("return this")();e.exports=s},5261:(e,t,r)=>{var n=r(9334),o=r(8935)(n);e.exports=o},8935:e=>{var t=Date.now;e.exports=function(e){var r=0,n=0;return function(){var o=t(),s=16-(o-n);if(n=o,s>0){if(++r>=800)return arguments[0]}else r=0;return e.apply(void 0,arguments)}}},1696:(e,t,r)=>{var n=r(8603);e.exports=function(){this.__data__=new n,this.size=0}},4838:e=>{e.exports=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}},1025:e=>{e.exports=function(e){return this.__data__.get(e)}},4205:e=>{e.exports=function(e){return this.__data__.has(e)}},7333:(e,t,r)=>{var n=r(8603),o=r(3579),s=r(3017);e.exports=function(e,t){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([e,t]),this.size=++r.size,this;r=this.__data__=new s(a)}return r.set(e,t),this.size=r.size,this}},3894:(e,t,r)=>{var n=r(9580),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,s=/\\(\\)?/g,a=n((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(o,(function(e,r,n,o){t.push(n?o.replace(s,"$1"):r||e)})),t}));e.exports=a},7489:(e,t,r)=>{var n=r(7614);e.exports=function(e){if("string"==typeof e||n(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}},8709:e=>{var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},9367:(e,t,r)=>{var n=r(9431),o=r(3771),s=r(6579),a=r(3266),c=r(5043),i=r(2610),l=Object.prototype.hasOwnProperty,u=s((function(e,t){if(c(t)||a(t))o(t,i(t),e);else for(var r in t)l.call(t,r)&&n(e,r,t[r])}));e.exports=u},9274:e=>{e.exports=function(e){return function(){return e}}},2604:e=>{e.exports=function(e,t){return e===t||e!=e&&t!=t}},8502:(e,t,r)=>{var n=r(5292);e.exports=function(e){return(null==e?0:e.length)?n(e,1):[]}},2051:(e,t,r)=>{var n=r(4497),o=r(138);e.exports=function(e,t){return null!=e&&o(e,t,n)}},3428:e=>{e.exports=function(e){return e}},8960:(e,t,r)=>{var n=r(1210),o=r(1726),s=Object.prototype,a=s.hasOwnProperty,c=s.propertyIsEnumerable,i=n(function(){return arguments}())?n:function(e){return o(e)&&a.call(e,"callee")&&!c.call(e,"callee")};e.exports=i},5413:e=>{var t=Array.isArray;e.exports=t},3266:(e,t,r)=>{var n=r(4406),o=r(8762);e.exports=function(e){return null!=e&&o(e.length)&&!n(e)}},52:(e,t,r)=>{e=r.nmd(e);var n=r(1665),o=r(4267),s=t&&!t.nodeType&&t,a=s&&e&&!e.nodeType&&e,c=a&&a.exports===s?n.Buffer:void 0,i=(c?c.isBuffer:void 0)||o;e.exports=i},4406:(e,t,r)=>{var n=r(740),o=r(9169);e.exports=function(e){if(!o(e))return!1;var t=n(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},8762:e=>{e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},4574:(e,t,r)=>{var n=r(8856),o=r(8321),s=r(1629),a=s&&s.isMap,c=a?o(a):n;e.exports=c},9169:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},1726:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},3663:(e,t,r)=>{var n=r(740),o=r(603),s=r(1726),a=Function.prototype,c=Object.prototype,i=a.toString,l=c.hasOwnProperty,u=i.call(Object);e.exports=function(e){if(!s(e)||"[object Object]"!=n(e))return!1;var t=o(e);if(null===t)return!0;var r=l.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&i.call(r)==u}},4076:(e,t,r)=>{var n=r(6514),o=r(8321),s=r(1629),a=s&&s.isSet,c=a?o(a):n;e.exports=c},7614:(e,t,r)=>{var n=r(740),o=r(1726);e.exports=function(e){return"symbol"==typeof e||o(e)&&"[object Symbol]"==n(e)}},2163:(e,t,r)=>{var n=r(4897),o=r(8321),s=r(1629),a=s&&s.isTypedArray,c=a?o(a):n;e.exports=c},2610:(e,t,r)=>{var n=r(1171),o=r(5860),s=r(3266);e.exports=function(e){return s(e)?n(e):o(e)}},9701:(e,t,r)=>{var n=r(1171),o=r(5259),s=r(3266);e.exports=function(e){return s(e)?n(e,!0):o(e)}},6214:e=>{e.exports=function(e){var t=null==e?0:e.length;return t?e[t-1]:void 0}},9284:(e,t,r)=>{var n=r(3017);function o(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var r=function(){var n=arguments,o=t?t.apply(this,n):n[0],s=r.cache;if(s.has(o))return s.get(o);var a=e.apply(this,n);return r.cache=s.set(o,a)||s,a};return r.cache=new(o.Cache||n),r}o.Cache=n,e.exports=o},6418:e=>{e.exports=function(){}},2903:(e,t,r)=>{var n=r(600),o=r(7563),s=r(2439),a=r(7141),c=r(3771),i=r(9214),l=r(3092),u=r(2025),p=l((function(e,t){var r={};if(null==e)return r;var l=!1;t=n(t,(function(t){return t=a(t,e),l||(l=t.length>1),t})),c(e,u(e),r),l&&(r=o(r,7,i));for(var p=t.length;p--;)s(r,t[p]);return r}));e.exports=p},8795:(e,t,r)=>{var n=r(7378),o=r(3092)((function(e,t){return null==e?{}:n(e,t)}));e.exports=o},565:e=>{e.exports=function(){return[]}},4267:e=>{e.exports=function(){return!1}},6938:(e,t,r)=>{var n=r(1856);e.exports=function(e){return null==e?"":n(e)}},4436:(e,t,r)=>{var n=r(6938),o=0;e.exports=function(e){var t=++o;return n(e)+t}},2021:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={"admin-page":"sexr0jUxC1jVixdKiDnC",background:"vKQ11sLeAM45M04P1ccj","admin-page-header":"iWGAhN9gOB48g0jEO1OQ","sandbox-domain-badge":"JOYmuxQjG4FArIIUxJfA"}},654:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={section:"cAbGtJDGgLubucBnz7vM"}},3689:()=>{},4206:()=>{},8403:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={sm:"(max-width: 599px)",md:"(min-width: 600px) and (max-width: 959px)",lg:"(min-width: 960px)"}},7371:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={sm:"(max-width: 599px)",md:"(min-width: 600px) and (max-width: 959px)",lg:"(min-width: 960px)",smCols:"4",mdCols:"8",lgCols:"12","col-sm-1":"RuVLl3q4lxTQa3wbhBJB","col-sm-1-start":"f9LZTRG4MMK42rS89afW","col-sm-1-end":"bHe_zKxjjpUwHw_MdYE1","col-sm-2":"QZbNrOqE2aNSn50xVhpU","col-sm-2-start":"ev7W3z7zVYPeHAlYqZjf","col-sm-2-end":"NJWd1m_e7lOiPYru2ZMP","col-sm-3":"Xc6nt1Qc1DI0Z2A3gt1r","col-sm-3-start":"UIcN_GXiPRoIsin8Kohg","col-sm-3-end":"GRKCyqb5LufCSCgykKFc","col-sm-4":"i_qTq8gqhhC3vIUepVRB","col-sm-4-start":"G3qaZ3Jpbvam_1XvGxgc","col-sm-4-end":"VRCNYKZtO9zukEwmgP1y","col-md-1":"tRm008K_WJL79WoNZTNL","col-md-1-start":"l5T2P_bgKts4tdaRkS1d","col-md-1-end":"zOCxfLZpF6BlgC7a_Yq1","col-md-2":"F80DdgVn0m5OpvtSQWka","col-md-2-start":"oI1c7JYfiJtMQHbhngtU","col-md-2-end":"pMQtA_4jh1_1lVknqEP5","col-md-3":"VenqMpdgyKQVUNNQcfqd","col-md-3-start":"seNYL99uoczf9V4MxBxT","col-md-3-end":"YKfF1HFhI9KygA5l3b2J","col-md-4":"yAi0Cv1xDWkoqsaUhvhR","col-md-4-start":"ubhnyZOnkgxNhh6XtVWv","col-md-4-end":"RGOPGQbWMJ9Ei5oFxS7X","col-md-5":"Sz1E2aWbX483ijdi6yge","col-md-5-start":"tku6_bRYrX9tMbgYGmIl","col-md-5-end":"b5JHttOhSEcI1WBlqAjk","col-md-6":"FboSx5MoKTAWbxXyYlCw","col-md-6-start":"Jhs8yEEmodG30edbJvag","col-md-6-end":"IpzbbKVqEqPcfIGkXkwt","col-md-7":"mhCPwfAZ4Kmm_empzJAq","col-md-7-start":"x034ilrJF7rO9UJB2rI1","col-md-7-end":"Wt8t2e16viRrOJ1lLA5v","col-md-8":"S6pIrEy9AMLKx9bgh_Ae","col-md-8-start":"kEfI4tGyuWfHTlRnvIab","col-md-8-end":"PUzX4RRsKq1dnsz3gebS","col-lg-1":"X_pdcLJikd8LS_YAdJlB","col-lg-1-start":"tl936d14Huby4khYp05X","col-lg-1-end":"hnge0LnR69d3NXEtEE1t","col-lg-2":"fj0NUMuyZQcPNgKcjp5Z","col-lg-2-start":"R2ncBX7a2NigdYCcV1OX","col-lg-2-end":"t8vMSDVYno9k9itRwnXb","col-lg-3":"wsDuEN2GqHx6qzo8dUdk","col-lg-3-start":"cIEVPUweWtLBy3xaXnMx","col-lg-3-end":"fajUWBwu1m2B479j3jmz","col-lg-4":"YR0c7fQTgMkDdWzwSyLp","col-lg-4-start":"xlwp8BmplxkKNMI7gamo","col-lg-4-end":"_C4O1w9DUqx1m3gPf8aA","col-lg-5":"Z54F1hAErckAIrKlxnXW","col-lg-5-start":"ezSDWkRHmKSxDJXxuiOH","col-lg-5-end":"T0ChoeAjGJjkkNrYhD4g","col-lg-6":"qtMoMPF6yHvGJnWHSsde","col-lg-6-start":"gdoywN5VPiWERfIBqkph","col-lg-6-end":"wUev_VH5uf_pwFFlbnAU","col-lg-7":"egIPDFJsOpownTClq9XP","col-lg-7-start":"yGhp9yoAW7k0kQik9AB7","col-lg-7-end":"SJ43U9mR5wUg5V2qBeQA","col-lg-8":"cTuyHfMwSUJxN_HdIEgd","col-lg-8-start":"smCr8DaIagcumdvdldiK","col-lg-8-end":"T03NHzQJvzwL6wAfIiTL","col-lg-9":"pMvxM3RJGjqyNdf9qg1Y","col-lg-9-start":"iIVpNRwEnQ_JI5gpp9EN","col-lg-9-end":"ZbQ4u4vGSX5rJOje4uGL","col-lg-10":"gKb5wuIDAlKGbrjK2vxy","col-lg-10-start":"Z7pINdImE2WJiYnZBTqm","col-lg-10-end":"ZTxp6qpvwurMdOnLLSz1","col-lg-11":"NnQTlbfnxPDR6cQ7rygg","col-lg-11-start":"O137wZd6Yl0olSA9PsXR","col-lg-11-end":"zf2OJtQ2MPz6SDoh6CB0","col-lg-12":"U3H6UHW6HqRt9hdzVg3O","col-lg-12-start":"zynnNeS_ZBTxABcVpUQH","col-lg-12-end":"vI8tltFZtFUNAy9Iag9s"}},2420:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={sm:"(max-width: 599px)",md:"(min-width: 600px) and (max-width: 959px)",lg:"(min-width: 960px)",container:"SqdhUZkXCRuIpErj1B3z",fluid:"OZC_9a1LhpWF9dv15Gdh"}},8325:()=>{},9422:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={global:"_fUXxnSp5pagKBp9gSN7"}},4997:e=>{var t=1e3,r=60*t,n=60*r,o=24*n,s=7*o,a=365.25*o;function c(e,t,r,n){var o=t>=1.5*r;return Math.round(e/r)+" "+n+(o?"s":"")}e.exports=function(e,i){i=i||{};var l=typeof e;if("string"===l&&e.length>0)return function(e){if((e=String(e)).length>100)return;var c=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!c)return;var i=parseFloat(c[1]);switch((c[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return i*a;case"weeks":case"week":case"w":return i*s;case"days":case"day":case"d":return i*o;case"hours":case"hour":case"hrs":case"hr":case"h":return i*n;case"minutes":case"minute":case"mins":case"min":case"m":return i*r;case"seconds":case"second":case"secs":case"sec":case"s":return i*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return i;default:return}}(e);if("number"===l&&isFinite(e))return i.long?function(e){var s=Math.abs(e);if(s>=o)return c(e,s,o,"day");if(s>=n)return c(e,s,n,"hour");if(s>=r)return c(e,s,r,"minute");if(s>=t)return c(e,s,t,"second");return e+" ms"}(e):function(e){var s=Math.abs(e);if(s>=o)return Math.round(e/o)+"d";if(s>=n)return Math.round(e/n)+"h";if(s>=r)return Math.round(e/r)+"m";if(s>=t)return Math.round(e/t)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},3381:(e,t,r)=>{"use strict";var n=r(3761);function o(){}function s(){}s.resetWarningCache=o,e.exports=function(){function e(e,t,r,o,s,a){if(a!==n){var c=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:s,resetWarningCache:o};return r.PropTypes=r,r}},8120:(e,t,r)=>{e.exports=r(3381)()},3761:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},372:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});r(9060);var n=r(6941);const o=r.n(n)()("dops:analytics");let s,a;window._tkq=window._tkq||[],window.ga=window.ga||function(){(window.ga.q=window.ga.q||[]).push(arguments)},window.ga.l=+new Date;const c={initialize:function(e,t,r){c.setUser(e,t),c.setSuperProps(r),c.identifyUser()},setGoogleAnalyticsEnabled:function(e,t=null){this.googleAnalyticsEnabled=e,this.googleAnalyticsKey=t},setMcAnalyticsEnabled:function(e){this.mcAnalyticsEnabled=e},setUser:function(e,t){a={ID:e,username:t}},setSuperProps:function(e){s=e},assignSuperProps:function(e){s=Object.assign(s||{},e)},mc:{bumpStat:function(e,t){const r=function(e,t){let r="";if("object"==typeof e){for(const t in e)r+="&x_"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);o("Bumping stats %o",e)}else r="&x_"+encodeURIComponent(e)+"="+encodeURIComponent(t),o('Bumping stat "%s" in group "%s"',t,e);return r}(e,t);c.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom-no-pv"+r+"&t="+Math.random())},bumpStatWithPageView:function(e,t){const r=function(e,t){let r="";if("object"==typeof e){for(const t in e)r+="&"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);o("Built stats %o",e)}else r="&"+encodeURIComponent(e)+"="+encodeURIComponent(t),o('Built stat "%s" in group "%s"',t,e);return r}(e,t);c.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom"+r+"&t="+Math.random())}},pageView:{record:function(e,t){c.tracks.recordPageView(e),c.ga.recordPageView(e,t)}},purchase:{record:function(e,t,r,n,o,s,a){c.ga.recordPurchase(e,t,r,n,o,s,a)}},tracks:{recordEvent:function(e,t){t=t||{},0===e.indexOf("akismet_")||0===e.indexOf("jetpack_")?(s&&(o("- Super Props: %o",s),t=Object.assign(t,s)),o('Record event "%s" called with props %s',e,JSON.stringify(t)),window._tkq.push(["recordEvent",e,t])):o('- Event name must be prefixed by "akismet_" or "jetpack_"')},recordJetpackClick:function(e){const t="object"==typeof e?e:{target:e};c.tracks.recordEvent("jetpack_wpa_click",t)},recordPageView:function(e){c.tracks.recordEvent("akismet_page_view",{path:e})},setOptOut:function(e){o("Pushing setOptOut: %o",e),window._tkq.push(["setOptOut",e])}},ga:{initialized:!1,initialize:function(){let e={};c.ga.initialized||(a&&(e={userId:"u-"+a.ID}),window.ga("create",this.googleAnalyticsKey,"auto",e),c.ga.initialized=!0)},recordPageView:function(e,t){c.ga.initialize(),o("Recording Page View ~ [URL: "+e+"] [Title: "+t+"]"),this.googleAnalyticsEnabled&&(window.ga("set","page",e),window.ga("send",{hitType:"pageview",page:e,title:t}))},recordEvent:function(e,t,r,n){c.ga.initialize();let s="Recording Event ~ [Category: "+e+"] [Action: "+t+"]";void 0!==r&&(s+=" [Option Label: "+r+"]"),void 0!==n&&(s+=" [Option Value: "+n+"]"),o(s),this.googleAnalyticsEnabled&&window.ga("send","event",e,t,r,n)},recordPurchase:function(e,t,r,n,o,s,a){window.ga("require","ecommerce"),window.ga("ecommerce:addTransaction",{id:e,revenue:n,currency:a}),window.ga("ecommerce:addItem",{id:e,name:t,sku:r,price:o,quantity:s}),window.ga("ecommerce:send")}},identifyUser:function(){a&&window._tkq.push(["identifyUser",a.ID,a.username])},setProperties:function(e){window._tkq.push(["setProperties",e])},clearedIdentity:function(){window._tkq.push(["clearIdentity"])}},i=c},5932:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>p});r(9060);var n=r(6439),o=r(3832);function s(e){class t extends Error{constructor(...t){super(...t),this.name=e}}return t}const a=s("JsonParseError"),c=s("JsonParseAfterRedirectError"),i=s("Api404Error"),l=s("Api404AfterRedirectError"),u=s("FetchNetworkError");const p=new function(e,t){let r=e,s=e,a={"X-WP-Nonce":t},c={credentials:"same-origin",headers:a},i={method:"post",credentials:"same-origin",headers:Object.assign({},a,{"Content-type":"application/json"})},l=function(e){const t=e.split("?"),r=t.length>1?t[1]:"",n=r.length?r.split("&"):[];return n.push("_cacheBuster="+(new Date).getTime()),t[0]+"?"+n.join("&")};const u={setApiRoot(e){r=e},setWpcomOriginApiUrl(e){s=e},setApiNonce(e){a={"X-WP-Nonce":e},c={credentials:"same-origin",headers:a},i={method:"post",credentials:"same-origin",headers:Object.assign({},a,{"Content-type":"application/json"})}},setCacheBusterCallback:e=>{l=e},registerSite:(e,t,o)=>{const s={};return(0,n.jetpackConfigHas)("consumer_slug")&&(s.plugin_slug=(0,n.jetpackConfigGet)("consumer_slug")),null!==t&&(s.redirect_uri=t),o&&(s.from=o),h(`${r}jetpack/v4/connection/register`,i,{body:JSON.stringify(s)}).then(d).then(f)},fetchAuthorizationUrl:e=>p((0,o.addQueryArgs)(`${r}jetpack/v4/connection/authorize_url`,{no_iframe:"1",redirect_uri:e}),c).then(d).then(f),fetchSiteConnectionData:()=>p(`${r}jetpack/v4/connection/data`,c).then(f),fetchSiteConnectionStatus:()=>p(`${r}jetpack/v4/connection`,c).then(f),fetchSiteConnectionTest:()=>p(`${r}jetpack/v4/connection/test`,c).then(d).then(f),fetchUserConnectionData:()=>p(`${r}jetpack/v4/connection/data`,c).then(f),fetchUserTrackingSettings:()=>p(`${r}jetpack/v4/tracking/settings`,c).then(d).then(f),updateUserTrackingSettings:e=>h(`${r}jetpack/v4/tracking/settings`,i,{body:JSON.stringify(e)}).then(d).then(f),disconnectSite:()=>h(`${r}jetpack/v4/connection`,i,{body:JSON.stringify({isActive:!1})}).then(d).then(f),fetchConnectUrl:()=>p(`${r}jetpack/v4/connection/url`,c).then(d).then(f),unlinkUser:(e=!1,t={})=>{const n={linked:!1,force:!!e};return t.disconnectAllUsers&&(n["disconnect-all-users"]=!0),h(`${r}jetpack/v4/connection/user`,i,{body:JSON.stringify(n)}).then(d).then(f)},reconnect:()=>h(`${r}jetpack/v4/connection/reconnect`,i).then(d).then(f),fetchConnectedPlugins:()=>p(`${r}jetpack/v4/connection/plugins`,c).then(d).then(f),setHasSeenWCConnectionModal:()=>h(`${r}jetpack/v4/seen-wc-connection-modal`,i).then(d).then(f),fetchModules:()=>p(`${r}jetpack/v4/module/all`,c).then(d).then(f),fetchModule:e=>p(`${r}jetpack/v4/module/${e}`,c).then(d).then(f),activateModule:e=>h(`${r}jetpack/v4/module/${e}/active`,i,{body:JSON.stringify({active:!0})}).then(d).then(f),deactivateModule:e=>h(`${r}jetpack/v4/module/${e}/active`,i,{body:JSON.stringify({active:!1})}),updateModuleOptions:(e,t)=>h(`${r}jetpack/v4/module/${e}`,i,{body:JSON.stringify(t)}).then(d).then(f),updateSettings:e=>h(`${r}jetpack/v4/settings`,i,{body:JSON.stringify(e)}).then(d).then(f),getProtectCount:()=>p(`${r}jetpack/v4/module/protect/data`,c).then(d).then(f),resetOptions:e=>h(`${r}jetpack/v4/options/${e}`,i,{body:JSON.stringify({reset:!0})}).then(d).then(f),activateVaultPress:()=>h(`${r}jetpack/v4/plugins`,i,{body:JSON.stringify({slug:"vaultpress",status:"active"})}).then(d).then(f),getVaultPressData:()=>p(`${r}jetpack/v4/module/vaultpress/data`,c).then(d).then(f),installPlugin:(e,t)=>{const n={slug:e,status:"active"};return t&&(n.source=t),h(`${r}jetpack/v4/plugins`,i,{body:JSON.stringify(n)}).then(d).then(f)},activateAkismet:()=>h(`${r}jetpack/v4/plugins`,i,{body:JSON.stringify({slug:"akismet",status:"active"})}).then(d).then(f),getAkismetData:()=>p(`${r}jetpack/v4/module/akismet/data`,c).then(d).then(f),checkAkismetKey:()=>p(`${r}jetpack/v4/module/akismet/key/check`,c).then(d).then(f),checkAkismetKeyTyped:e=>h(`${r}jetpack/v4/module/akismet/key/check`,i,{body:JSON.stringify({api_key:e})}).then(d).then(f),getFeatureTypeStatus:e=>p(`${r}jetpack/v4/feature/${e}`,c).then(d).then(f),fetchStatsData:e=>p(function(e){let t=`${r}jetpack/v4/module/stats/data`;-1!==t.indexOf("?")?t+=`&range=${encodeURIComponent(e)}`:t+=`?range=${encodeURIComponent(e)}`;return t}(e),c).then(d).then(f).then(m),getPluginUpdates:()=>p(`${r}jetpack/v4/updates/plugins`,c).then(d).then(f),getPlans:()=>p(`${r}jetpack/v4/plans`,c).then(d).then(f),fetchSettings:()=>p(`${r}jetpack/v4/settings`,c).then(d).then(f),updateSetting:e=>h(`${r}jetpack/v4/settings`,i,{body:JSON.stringify(e)}).then(d).then(f),fetchSiteData:()=>p(`${r}jetpack/v4/site`,c).then(d).then(f).then((e=>JSON.parse(e.data))),fetchSiteFeatures:()=>p(`${r}jetpack/v4/site/features`,c).then(d).then(f).then((e=>JSON.parse(e.data))),fetchSiteProducts:()=>p(`${r}jetpack/v4/site/products`,c).then(d).then(f),fetchSitePurchases:()=>p(`${r}jetpack/v4/site/purchases`,c).then(d).then(f).then((e=>JSON.parse(e.data))),fetchSiteBenefits:()=>p(`${r}jetpack/v4/site/benefits`,c).then(d).then(f).then((e=>JSON.parse(e.data))),fetchSiteDiscount:()=>p(`${r}jetpack/v4/site/discount`,c).then(d).then(f).then((e=>e.data)),fetchSetupQuestionnaire:()=>p(`${r}jetpack/v4/setup/questionnaire`,c).then(d).then(f),fetchRecommendationsData:()=>p(`${r}jetpack/v4/recommendations/data`,c).then(d).then(f),fetchRecommendationsProductSuggestions:()=>p(`${r}jetpack/v4/recommendations/product-suggestions`,c).then(d).then(f),fetchRecommendationsUpsell:()=>p(`${r}jetpack/v4/recommendations/upsell`,c).then(d).then(f),fetchRecommendationsConditional:()=>p(`${r}jetpack/v4/recommendations/conditional`,c).then(d).then(f),saveRecommendationsData:e=>h(`${r}jetpack/v4/recommendations/data`,i,{body:JSON.stringify({data:e})}).then(d),fetchProducts:()=>p(`${r}jetpack/v4/products`,c).then(d).then(f),fetchRewindStatus:()=>p(`${r}jetpack/v4/rewind`,c).then(d).then(f).then((e=>JSON.parse(e.data))),fetchScanStatus:()=>p(`${r}jetpack/v4/scan`,c).then(d).then(f).then((e=>JSON.parse(e.data))),dismissJetpackNotice:e=>h(`${r}jetpack/v4/notice/${e}`,i,{body:JSON.stringify({dismissed:!0})}).then(d).then(f),fetchPluginsData:()=>p(`${r}jetpack/v4/plugins`,c).then(d).then(f),fetchIntroOffers:()=>p(`${r}jetpack/v4/intro-offers`,c).then(d).then(f),fetchVerifySiteGoogleStatus:e=>p(null!==e?`${r}jetpack/v4/verify-site/google/${e}`:`${r}jetpack/v4/verify-site/google`,c).then(d).then(f),verifySiteGoogle:e=>h(`${r}jetpack/v4/verify-site/google`,i,{body:JSON.stringify({keyring_id:e})}).then(d).then(f),submitSurvey:e=>h(`${r}jetpack/v4/marketing/survey`,i,{body:JSON.stringify(e)}).then(d).then(f),saveSetupQuestionnaire:e=>h(`${r}jetpack/v4/setup/questionnaire`,i,{body:JSON.stringify(e)}).then(d).then(f),updateLicensingError:e=>h(`${r}jetpack/v4/licensing/error`,i,{body:JSON.stringify(e)}).then(d).then(f),updateLicenseKey:e=>h(`${r}jetpack/v4/licensing/set-license`,i,{body:JSON.stringify({license:e})}).then(d).then(f),getUserLicensesCounts:()=>p(`${r}jetpack/v4/licensing/user/counts`,c).then(d).then(f),getUserLicenses:()=>p(`${r}jetpack/v4/licensing/user/licenses`,c).then(d).then(f),updateLicensingActivationNoticeDismiss:e=>h(`${r}jetpack/v4/licensing/user/activation-notice-dismiss`,i,{body:JSON.stringify({last_detached_count:e})}).then(d).then(f),updateRecommendationsStep:e=>h(`${r}jetpack/v4/recommendations/step`,i,{body:JSON.stringify({step:e})}).then(d),confirmIDCSafeMode:()=>h(`${r}jetpack/v4/identity-crisis/confirm-safe-mode`,i).then(d),startIDCFresh:e=>h(`${r}jetpack/v4/identity-crisis/start-fresh`,i,{body:JSON.stringify({redirect_uri:e})}).then(d).then(f),migrateIDC:()=>h(`${r}jetpack/v4/identity-crisis/migrate`,i).then(d),attachLicenses:e=>h(`${r}jetpack/v4/licensing/attach-licenses`,i,{body:JSON.stringify({licenses:e})}).then(d).then(f),fetchSearchPlanInfo:()=>p(`${s}jetpack/v4/search/plan`,c).then(d).then(f),fetchSearchSettings:()=>p(`${s}jetpack/v4/search/settings`,c).then(d).then(f),updateSearchSettings:e=>h(`${s}jetpack/v4/search/settings`,i,{body:JSON.stringify(e)}).then(d).then(f),fetchSearchStats:()=>p(`${s}jetpack/v4/search/stats`,c).then(d).then(f),fetchWafSettings:()=>p(`${r}jetpack/v4/waf`,c).then(d).then(f),updateWafSettings:e=>h(`${r}jetpack/v4/waf`,i,{body:JSON.stringify(e)}).then(d).then(f),fetchWordAdsSettings:()=>p(`${r}jetpack/v4/wordads/settings`,c).then(d).then(f),updateWordAdsSettings:e=>h(`${r}jetpack/v4/wordads/settings`,i,{body:JSON.stringify(e)}),fetchSearchPricing:()=>p(`${s}jetpack/v4/search/pricing`,c).then(d).then(f),fetchMigrationStatus:()=>p(`${r}jetpack/v4/migration/status`,c).then(d).then(f),fetchBackupUndoEvent:()=>p(`${r}jetpack/v4/site/backup/undo-event`,c).then(d).then(f),fetchBackupPreflightStatus:()=>p(`${r}jetpack/v4/site/backup/preflight`,c).then(d).then(f)};function p(e,t){return fetch(l(e),t)}function h(e,t,r){return fetch(e,Object.assign({},t,r)).catch(g)}function m(e){return e.general&&void 0===e.general.response||e.week&&void 0===e.week.response||e.month&&void 0===e.month.response?e:{}}Object.assign(this,u)};function d(e){return e.status>=200&&e.status<300?e:404===e.status?new Promise((()=>{throw e.redirected?new l(e.redirected):new i})):e.json().catch((e=>h(e))).then((t=>{const r=new Error(`${t.message} (Status ${e.status})`);throw r.response=t,r.name="ApiError",r}))}function f(e){return e.json().catch((t=>h(t,e.redirected,e.url)))}function h(e,t,r){throw t?new c(r):new a}function g(){throw new u}},2947:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(5932),o=r(7723),s=r(3022),a=r(1609),c=r(8250),i=r(7142),l=r(8509),u=r(5918),p=r(2021);const __=o.__,d=({children:e,moduleName:t=__("Jetpack","jetpack-wordads"),moduleNameHref:r,showHeader:d=!0,showFooter:f=!0,useInternalLinks:h=!1,showBackground:g=!0,sandboxedDomain:m="",apiRoot:v="",apiNonce:y="",optionalMenuItems:b,header:w})=>{(0,a.useEffect)((()=>{n.Ay.setApiRoot(v),n.Ay.setApiNonce(y)}),[v,y]);const j=(0,s.A)(p.A["admin-page"],{[p.A.background]:g}),x=(0,a.useCallback)((async()=>{try{const e=await n.Ay.fetchSiteConnectionTest();window.alert(e.message)}catch(e){window.alert((0,o.sprintf)(/* translators: placeholder is an error message. */
__("There was an error testing Jetpack. Error: %s","jetpack-wordads"),e.message))}}),[]);return React.createElement("div",{className:j},d&&React.createElement(u.A,{horizontalSpacing:5},React.createElement(l.A,{className:(0,s.A)(p.A["admin-page-header"],"jp-admin-page-header")},w||React.createElement(i.A,null),m&&React.createElement("code",{className:p.A["sandbox-domain-badge"],onClick:x,onKeyDown:x,role:"button",tabIndex:0,title:`Sandboxing via ${m}. Click to test connection.`},"API Sandboxed"))),React.createElement(u.A,{fluid:!0,horizontalSpacing:0},React.createElement(l.A,null,e)),f&&React.createElement(u.A,{horizontalSpacing:5},React.createElement(l.A,null,React.createElement(c.A,{moduleName:t,moduleNameHref:r,menu:b,useInternalLinks:h}))))}},5640:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(1609),o=r.n(n),s=r(654);const a=({children:e})=>o().createElement("div",{className:s.A.section},e)},8907:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(6072),o=r.n(n),s=r(7723),a=r(3022),c=r(1609),i=r.n(c);const __=s.__,l=({title:e=__("An Automattic Airline","jetpack-wordads"),height:t=7,className:r,...n})=>i().createElement("svg",o()({role:"img",x:"0",y:"0",viewBox:"0 0 935 38.2",enableBackground:"new 0 0 935 38.2","aria-labelledby":"jp-automattic-byline-logo-title",height:t,className:(0,a.A)("jp-automattic-byline-logo",r)},n),i().createElement("desc",{id:"jp-automattic-byline-logo-title"},e),i().createElement("path",{d:"M317.1 38.2c-12.6 0-20.7-9.1-20.7-18.5v-1.2c0-9.6 8.2-18.5 20.7-18.5 12.6 0 20.8 8.9 20.8 18.5v1.2C337.9 29.1 329.7 38.2 317.1 38.2zM331.2 18.6c0-6.9-5-13-14.1-13s-14 6.1-14 13v0.9c0 6.9 5 13.1 14 13.1s14.1-6.2 14.1-13.1V18.6zM175 36.8l-4.7-8.8h-20.9l-4.5 8.8h-7L157 1.3h5.5L182 36.8H175zM159.7 8.2L152 23.1h15.7L159.7 8.2zM212.4 38.2c-12.7 0-18.7-6.9-18.7-16.2V1.3h6.6v20.9c0 6.6 4.3 10.5 12.5 10.5 8.4 0 11.9-3.9 11.9-10.5V1.3h6.7V22C231.4 30.8 225.8 38.2 212.4 38.2zM268.6 6.8v30h-6.7v-30h-15.5V1.3h37.7v5.5H268.6zM397.3 36.8V8.7l-1.8 3.1 -14.9 25h-3.3l-14.7-25 -1.8-3.1v28.1h-6.5V1.3h9.2l14 24.4 1.7 3 1.7-3 13.9-24.4h9.1v35.5H397.3zM454.4 36.8l-4.7-8.8h-20.9l-4.5 8.8h-7l19.2-35.5h5.5l19.5 35.5H454.4zM439.1 8.2l-7.7 14.9h15.7L439.1 8.2zM488.4 6.8v30h-6.7v-30h-15.5V1.3h37.7v5.5H488.4zM537.3 6.8v30h-6.7v-30h-15.5V1.3h37.7v5.5H537.3zM569.3 36.8V4.6c2.7 0 3.7-1.4 3.7-3.4h2.8v35.5L569.3 36.8 569.3 36.8zM628 11.3c-3.2-2.9-7.9-5.7-14.2-5.7 -9.5 0-14.8 6.5-14.8 13.3v0.7c0 6.7 5.4 13 15.3 13 5.9 0 10.8-2.8 13.9-5.7l4 4.2c-3.9 3.8-10.5 7.1-18.3 7.1 -13.4 0-21.6-8.7-21.6-18.3v-1.2c0-9.6 8.9-18.7 21.9-18.7 7.5 0 14.3 3.1 18 7.1L628 11.3zM321.5 12.4c1.2 0.8 1.5 2.4 0.8 3.6l-6.1 9.4c-0.8 1.2-2.4 1.6-3.6 0.8l0 0c-1.2-0.8-1.5-2.4-0.8-3.6l6.1-9.4C318.7 11.9 320.3 11.6 321.5 12.4L321.5 12.4z"}),i().createElement("path",{d:"M37.5 36.7l-4.7-8.9H11.7l-4.6 8.9H0L19.4 0.8H25l19.7 35.9H37.5zM22 7.8l-7.8 15.1h15.9L22 7.8zM82.8 36.7l-23.3-24 -2.3-2.5v26.6h-6.7v-36H57l22.6 24 2.3 2.6V0.8h6.7v35.9H82.8z"}),i().createElement("path",{d:"M719.9 37l-4.8-8.9H694l-4.6 8.9h-7.1l19.5-36h5.6l19.8 36H719.9zM704.4 8l-7.8 15.1h15.9L704.4 8zM733 37V1h6.8v36H733zM781 37c-1.8 0-2.6-2.5-2.9-5.8l-0.2-3.7c-0.2-3.6-1.7-5.1-8.4-5.1h-12.8V37H750V1h19.6c10.8 0 15.7 4.3 15.7 9.9 0 3.9-2 7.7-9 9 7 0.5 8.5 3.7 8.6 7.9l0.1 3c0.1 2.5 0.5 4.3 2.2 6.1V37H781zM778.5 11.8c0-2.6-2.1-5.1-7.9-5.1h-13.8v10.8h14.4c5 0 7.3-2.4 7.3-5.2V11.8zM794.8 37V1h6.8v30.4h28.2V37H794.8zM836.7 37V1h6.8v36H836.7zM886.2 37l-23.4-24.1 -2.3-2.5V37h-6.8V1h6.5l22.7 24.1 2.3 2.6V1h6.8v36H886.2zM902.3 37V1H935v5.6h-26v9.2h20v5.5h-20v10.1h26V37H902.3z"}))},1883:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(7723),o=r(3022),s=r(1609);r(3689);const __=n.__;class a extends s.Component{static defaultProps={"aria-hidden":"false",focusable:"true"};needsOffset(e,t){return["gridicons-arrow-left","gridicons-arrow-right","gridicons-calendar","gridicons-cart","gridicons-folder","gridicons-help-outline","gridicons-info","gridicons-info-outline","gridicons-posts","gridicons-star-outline","gridicons-star"].indexOf(e)>=0&&t%18==0}getSVGDescription(e){if("description"in this.props)return this.props.description;switch(e){default:return"";case"gridicons-audio":return __("Has audio.","jetpack-wordads");case"gridicons-arrow-left":return __("Arrow left","jetpack-wordads");case"gridicons-arrow-right":return __("Arrow right","jetpack-wordads");case"gridicons-calendar":return __("Is an event.","jetpack-wordads");case"gridicons-cart":return __("Is a product.","jetpack-wordads");case"chevron-down":return __("Show filters","jetpack-wordads");case"gridicons-comment":return __("Matching comment.","jetpack-wordads");case"gridicons-cross":return __("Close.","jetpack-wordads");case"gridicons-filter":return __("Toggle search filters.","jetpack-wordads");case"gridicons-folder":return __("Category","jetpack-wordads");case"gridicons-help-outline":return __("Help","jetpack-wordads");case"gridicons-info":case"gridicons-info-outline":return __("Information.","jetpack-wordads");case"gridicons-image-multiple":return __("Has multiple images.","jetpack-wordads");case"gridicons-image":return __("Has an image.","jetpack-wordads");case"gridicons-page":return __("Page","jetpack-wordads");case"gridicons-post":return __("Post","jetpack-wordads");case"gridicons-jetpack-search":case"gridicons-search":return __("Magnifying Glass","jetpack-wordads");case"gridicons-tag":return __("Tag","jetpack-wordads");case"gridicons-video":return __("Has a video.","jetpack-wordads")}}renderIcon(e){switch(e){default:return null;case"gridicons-audio":return React.createElement("g",null,React.createElement("path",{d:"M8 4v10.184C7.686 14.072 7.353 14 7 14c-1.657 0-3 1.343-3 3s1.343 3 3 3 3-1.343 3-3V7h7v4.184c-.314-.112-.647-.184-1-.184-1.657 0-3 1.343-3 3s1.343 3 3 3 3-1.343 3-3V4H8z"}));case"gridicons-arrow-left":return React.createElement("g",null,React.createElement("path",{d:"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"}));case"gridicons-arrow-right":return React.createElement("g",null,React.createElement("path",{d:"M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8-8-8z"}));case"gridicons-block":return React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zM4 12c0-4.418 3.582-8 8-8 1.848 0 3.545.633 4.9 1.686L5.686 16.9C4.633 15.545 4 13.848 4 12zm8 8c-1.848 0-3.546-.633-4.9-1.686L18.314 7.1C19.367 8.455 20 10.152 20 12c0 4.418-3.582 8-8 8z"}));case"gridicons-calendar":return React.createElement("g",null,React.createElement("path",{d:"M19 4h-1V2h-2v2H8V2H6v2H5c-1.105 0-2 .896-2 2v13c0 1.104.895 2 2 2h14c1.104 0 2-.896 2-2V6c0-1.104-.896-2-2-2zm0 15H5V8h14v11z"}));case"gridicons-cart":return React.createElement("g",null,React.createElement("path",{d:"M9 20c0 1.1-.9 2-2 2s-1.99-.9-1.99-2S5.9 18 7 18s2 .9 2 2zm8-2c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2zm.396-5c.937 0 1.75-.65 1.952-1.566L21 5H7V4c0-1.105-.895-2-2-2H3v2h2v11c0 1.105.895 2 2 2h12c0-1.105-.895-2-2-2H7v-2h10.396z"}));case"gridicons-checkmark":return React.createElement("g",null,React.createElement("path",{d:"M11 17.768l-4.884-4.884 1.768-1.768L11 14.232l8.658-8.658C17.823 3.39 15.075 2 12 2 6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10c0-1.528-.353-2.97-.966-4.266L11 17.768z"}));case"gridicons-chevron-left":return React.createElement("g",null,React.createElement("path",{d:"M16.443 7.41L15.0399 6L9.06934 12L15.0399 18L16.443 16.59L11.8855 12L16.443 7.41Z"}));case"gridicons-chevron-right":return React.createElement("g",null,React.createElement("path",{d:"M10.2366 6L8.8335 7.41L13.391 12L8.8335 16.59L10.2366 18L16.2072 12L10.2366 6Z"}));case"gridicons-chevron-down":return React.createElement("g",null,React.createElement("path",{d:"M20 9l-8 8-8-8 1.414-1.414L12 14.172l6.586-6.586"}));case"gridicons-comment":return React.createElement("g",null,React.createElement("path",{d:"M3 6v9c0 1.105.895 2 2 2h9v5l5.325-3.804c1.05-.75 1.675-1.963 1.675-3.254V6c0-1.105-.895-2-2-2H5c-1.105 0-2 .895-2 2z"}));case"gridicons-computer":return React.createElement("g",null,React.createElement("path",{d:"M20 2H4c-1.104 0-2 .896-2 2v12c0 1.104.896 2 2 2h6v2H7v2h10v-2h-3v-2h6c1.104 0 2-.896 2-2V4c0-1.104-.896-2-2-2zm0 14H4V4h16v12z"}));case"gridicons-cross":return React.createElement("g",null,React.createElement("path",{d:"M18.36 19.78L12 13.41l-6.36 6.37-1.42-1.42L10.59 12 4.22 5.64l1.42-1.42L12 10.59l6.36-6.36 1.41 1.41L13.41 12l6.36 6.36z"}));case"gridicons-filter":return React.createElement("g",null,React.createElement("path",{d:"M10 19h4v-2h-4v2zm-4-6h12v-2H6v2zM3 5v2h18V5H3z"}));case"gridicons-folder":return React.createElement("g",null,React.createElement("path",{d:"M18 19H6c-1.1 0-2-.9-2-2V7c0-1.1.9-2 2-2h3c1.1 0 2 .9 2 2h7c1.1 0 2 .9 2 2v8c0 1.1-.9 2-2 2z"}));case"gridicons-help-outline":return React.createElement("g",null,React.createElement("path",{d:"M12 4c4.41 0 8 3.59 8 8s-3.59 8-8 8-8-3.59-8-8 3.59-8 8-8m0-2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 13h-2v2h2v-2zm-1.962-2v-.528c0-.4.082-.74.246-1.017.163-.276.454-.546.87-.808.333-.21.572-.397.717-.565.146-.168.22-.36.22-.577 0-.172-.078-.308-.234-.41-.156-.1-.358-.15-.608-.15-.62 0-1.34.22-2.168.658l-.854-1.67c1.02-.58 2.084-.872 3.194-.872.913 0 1.63.202 2.15.603.52.4.78.948.78 1.64 0 .495-.116.924-.347 1.287-.23.362-.6.705-1.11 1.03-.43.278-.7.48-.807.61-.108.13-.163.282-.163.458V13h-1.885z"}));case"gridicons-image":return React.createElement("g",null,React.createElement("path",{d:"M13 9.5c0-.828.672-1.5 1.5-1.5s1.5.672 1.5 1.5-.672 1.5-1.5 1.5-1.5-.672-1.5-1.5zM22 6v12c0 1.105-.895 2-2 2H4c-1.105 0-2-.895-2-2V6c0-1.105.895-2 2-2h16c1.105 0 2 .895 2 2zm-2 0H4v7.444L8 9l5.895 6.55 1.587-1.85c.798-.932 2.24-.932 3.037 0L20 15.426V6z"}));case"gridicons-image-multiple":return React.createElement("g",null,React.createElement("path",{d:"M15 7.5c0-.828.672-1.5 1.5-1.5s1.5.672 1.5 1.5S17.328 9 16.5 9 15 8.328 15 7.5zM4 20h14c0 1.105-.895 2-2 2H4c-1.1 0-2-.9-2-2V8c0-1.105.895-2 2-2v14zM22 4v12c0 1.105-.895 2-2 2H8c-1.105 0-2-.895-2-2V4c0-1.105.895-2 2-2h12c1.105 0 2 .895 2 2zM8 4v6.333L11 7l4.855 5.395.656-.73c.796-.886 2.183-.886 2.977 0l.513.57V4H8z"}));case"gridicons-info":return React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"}));case"gridicons-info-outline":return React.createElement("g",null,React.createElement("path",{d:"M13 9h-2V7h2v2zm0 2h-2v6h2v-6zm-1-7c-4.411 0-8 3.589-8 8s3.589 8 8 8 8-3.589 8-8-3.589-8-8-8m0-2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2z"}));case"gridicons-jetpack-search":return React.createElement("g",null,React.createElement("path",{d:"M0 9.257C0 4.15 4.151 0 9.257 0c5.105 0 9.256 4.151 9.256 9.257a9.218 9.218 0 01-2.251 6.045l.034.033h1.053L24 22.01l-1.986 1.989-6.664-6.662v-1.055l-.033-.033a9.218 9.218 0 01-6.06 2.264C4.15 18.513 0 14.362 0 9.257zm4.169 1.537h4.61V1.82l-4.61 8.973zm5.547-3.092v8.974l4.61-8.974h-4.61z"}));case"gridicons-phone":return React.createElement("g",null,React.createElement("path",{d:"M16 2H8c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h8c1.104 0 2-.896 2-2V4c0-1.104-.896-2-2-2zm-3 19h-2v-1h2v1zm3-2H8V5h8v14z"}));case"gridicons-pages":return React.createElement("g",null,React.createElement("path",{d:"M16 8H8V6h8v2zm0 2H8v2h8v-2zm4-6v12l-6 6H6c-1.105 0-2-.895-2-2V4c0-1.105.895-2 2-2h12c1.105 0 2 .895 2 2zm-2 10V4H6v16h6v-4c0-1.105.895-2 2-2h4z"}));case"gridicons-posts":return React.createElement("g",null,React.createElement("path",{d:"M16 19H3v-2h13v2zm5-10H3v2h18V9zM3 5v2h11V5H3zm14 0v2h4V5h-4zm-6 8v2h10v-2H11zm-8 0v2h5v-2H3z"}));case"gridicons-search":return React.createElement("g",null,React.createElement("path",{d:"M21 19l-5.154-5.154C16.574 12.742 17 11.42 17 10c0-3.866-3.134-7-7-7s-7 3.134-7 7 3.134 7 7 7c1.42 0 2.742-.426 3.846-1.154L19 21l2-2zM5 10c0-2.757 2.243-5 5-5s5 2.243 5 5-2.243 5-5 5-5-2.243-5-5z"}));case"gridicons-star-outline":return React.createElement("g",null,React.createElement("path",{d:"M12 6.308l1.176 3.167.347.936.997.042 3.374.14-2.647 2.09-.784.62.27.963.91 3.25-2.813-1.872-.83-.553-.83.552-2.814 1.87.91-3.248.27-.962-.783-.62-2.648-2.092 3.374-.14.996-.04.347-.936L12 6.308M12 2L9.418 8.953 2 9.257l5.822 4.602L5.82 21 12 16.89 18.18 21l-2.002-7.14L22 9.256l-7.418-.305L12 2z"}));case"gridicons-star":return React.createElement("g",null,React.createElement("path",{d:"M12 2l2.582 6.953L22 9.257l-5.822 4.602L18.18 21 12 16.89 5.82 21l2.002-7.14L2 9.256l7.418-.304"}));case"gridicons-tag":return React.createElement("g",null,React.createElement("path",{d:"M20 2.007h-7.087c-.53 0-1.04.21-1.414.586L2.592 11.5c-.78.78-.78 2.046 0 2.827l7.086 7.086c.78.78 2.046.78 2.827 0l8.906-8.906c.376-.374.587-.883.587-1.413V4.007c0-1.105-.895-2-2-2zM17.007 9c-1.105 0-2-.895-2-2s.895-2 2-2 2 .895 2 2-.895 2-2 2z"}));case"gridicons-video":return React.createElement("g",null,React.createElement("path",{d:"M20 4v2h-2V4H6v2H4V4c-1.105 0-2 .895-2 2v12c0 1.105.895 2 2 2v-2h2v2h12v-2h2v2c1.105 0 2-.895 2-2V6c0-1.105-.895-2-2-2zM6 16H4v-3h2v3zm0-5H4V8h2v3zm4 4V9l4.5 3-4.5 3zm10 1h-2v-3h2v3zm0-5h-2V8h2v3z"}));case"gridicons-lock":return React.createElement(React.Fragment,null,React.createElement("g",{id:"lock"},React.createElement("path",{d:"M18,8h-1V7c0-2.757-2.243-5-5-5S7,4.243,7,7v1H6c-1.105,0-2,0.895-2,2v10c0,1.105,0.895,2,2,2h12c1.105,0,2-0.895,2-2V10 C20,8.895,19.105,8,18,8z M9,7c0-1.654,1.346-3,3-3s3,1.346,3,3v1H9V7z M13,15.723V18h-2v-2.277c-0.595-0.346-1-0.984-1-1.723 c0-1.105,0.895-2,2-2s2,0.895,2,2C14,14.738,13.595,15.376,13,15.723z"})),React.createElement("g",{id:"Layer_1"}));case"gridicons-external":return React.createElement("g",null,React.createElement("path",{d:"M19 13v6c0 1.105-.895 2-2 2H5c-1.105 0-2-.895-2-2V7c0-1.105.895-2 2-2h6v2H5v12h12v-6h2zM13 3v2h4.586l-7.793 7.793 1.414 1.414L19 6.414V11h2V3h-8z"}))}}render(){const{size:e=24,className:t=""}=this.props,r=this.props.height||e,n=this.props.width||e,s=this.props.style||{height:r,width:n},a="gridicons-"+this.props.icon,c=(0,o.A)("gridicon",a,t,{"needs-offset":this.needsOffset(a,e)}),i=this.getSVGDescription(a);return React.createElement("svg",{className:c,focusable:this.props.focusable,height:r,onClick:this.props.onClick,style:s,viewBox:"0 0 24 24",width:n,xmlns:"http://www.w3.org/2000/svg","aria-hidden":this.props["aria-hidden"]},i?React.createElement("desc",null,i):null,this.renderIcon(a))}}const c=a},8250:(e,t,r)=>{"use strict";r.d(t,{A:()=>y});var n=r(6072),o=r.n(n),s=(r(4006),r(9588),r(7723)),a=r(1113),c=r(3512),i=r(3022),l=r(1609),u=r.n(l),p=r(3924),d=r(1069),f=r(8907),h=(r(4206),r(7142)),g=r(442);const __=s.__,_x=s._x,m=()=>u().createElement(h.A,{logoColor:"#000",showText:!1,height:16,"aria-hidden":"true"}),v=()=>u().createElement(u().Fragment,null,u().createElement(a.A,{icon:c.A,size:16}),u().createElement("span",{className:"jp-dashboard-footer__accessible-external-link"},/* translators: accessibility text */
__("(opens in a new tab)","jetpack-wordads"))),y=({moduleName:e=__("Jetpack","jetpack-wordads"),className:t,moduleNameHref:r="https://jetpack.com",menu:n,useInternalLinks:s,onAboutClick:a,onPrivacyClick:c,onTermsClick:l,...h})=>{const[y]=(0,g.A)("sm","<="),[b]=(0,g.A)("md","<="),[w]=(0,g.A)("lg",">"),j=(0,d.A)();let x=[{label:_x("About","Link to learn more about Jetpack.","jetpack-wordads"),title:__("About Jetpack","jetpack-wordads"),href:s?new URL("admin.php?page=jetpack_about",j).href:(0,p.A)("jetpack-about"),target:s?"_self":"_blank",onClick:a},{label:_x("Privacy","Shorthand for Privacy Policy.","jetpack-wordads"),title:__("Automattic's Privacy Policy","jetpack-wordads"),href:s?new URL("admin.php?page=jetpack#/privacy",j).href:(0,p.A)("a8c-privacy"),target:s?"_self":"_blank",onClick:c},{label:_x("Terms","Shorthand for Terms of Service.","jetpack-wordads"),title:__("WordPress.com Terms of Service","jetpack-wordads"),href:(0,p.A)("wpcom-tos"),target:"_blank",onClick:l}];n&&(x=[...x,...n]);const k=u().createElement(u().Fragment,null,u().createElement(m,null),e);return u().createElement("footer",o()({className:(0,i.A)("jp-dashboard-footer",{"is-sm":y,"is-md":b,"is-lg":w},t),"aria-label":__("Jetpack","jetpack-wordads"),role:"contentinfo"},h),u().createElement("ul",null,u().createElement("li",{className:"jp-dashboard-footer__jp-item"},r?u().createElement("a",{href:r},k):k),x.map((e=>{const t="button"===e.role,r=!t&&"_blank"===e.target;return u().createElement("li",{key:e.label},u().createElement("a",{href:e.href,title:e.title,target:e.target,onClick:e.onClick,onKeyDown:e.onKeyDown,className:(0,i.A)("jp-dashboard-footer__menu-item",{"is-external":r}),role:e.role,rel:r?"noopener noreferrer":void 0,tabIndex:t?0:void 0},e.label,r&&u().createElement(v,null)))})),u().createElement("li",{className:"jp-dashboard-footer__a8c-item"},u().createElement("a",{href:s?new URL("admin.php?page=jetpack_about",j).href:(0,p.A)("a8c-about"),"aria-label":__("An Automattic Airline","jetpack-wordads")},u().createElement(f.A,{"aria-hidden":"true"})))))}},7142:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(6072),o=r.n(n),s=r(7723),a=r(3022),c=r(1609),i=r.n(c);const __=s.__,l=({logoColor:e="#069e08",showText:t=!0,className:r,height:n=32,...s})=>{const c=t?"0 0 118 32":"0 0 32 32";return i().createElement("svg",o()({xmlns:"http://www.w3.org/2000/svg",x:"0px",y:"0px",viewBox:c,className:(0,a.A)("jetpack-logo",r),"aria-labelledby":"jetpack-logo-title",height:n},s,{role:"img"}),i().createElement("title",{id:"jetpack-logo-title"},__("Jetpack Logo","jetpack-wordads")),i().createElement("path",{fill:e,d:"M16,0C7.2,0,0,7.2,0,16s7.2,16,16,16s16-7.2,16-16S24.8,0,16,0z M15,19H7l8-16V19z M17,29V13h8L17,29z"}),t&&i().createElement(i().Fragment,null,i().createElement("path",{d:"M41.3,26.6c-0.5-0.7-0.9-1.4-1.3-2.1c2.3-1.4,3-2.5,3-4.6V8h-3V6h6v13.4C46,22.8,45,24.8,41.3,26.6z"}),i().createElement("path",{d:"M65,18.4c0,1.1,0.8,1.3,1.4,1.3c0.5,0,2-0.2,2.6-0.4v2.1c-0.9,0.3-2.5,0.5-3.7,0.5c-1.5,0-3.2-0.5-3.2-3.1V12H60v-2h2.1V7.1 H65V10h4v2h-4V18.4z"}),i().createElement("path",{d:"M71,10h3v1.3c1.1-0.8,1.9-1.3,3.3-1.3c2.5,0,4.5,1.8,4.5,5.6s-2.2,6.3-5.8,6.3c-0.9,0-1.3-0.1-2-0.3V28h-3V10z M76.5,12.3 c-0.8,0-1.6,0.4-2.5,1.2v5.9c0.6,0.1,0.9,0.2,1.8,0.2c2,0,3.2-1.3,3.2-3.9C79,13.4,78.1,12.3,76.5,12.3z"}),i().createElement("path",{d:"M93,22h-3v-1.5c-0.9,0.7-1.9,1.5-3.5,1.5c-1.5,0-3.1-1.1-3.1-3.2c0-2.9,2.5-3.4,4.2-3.7l2.4-0.3v-0.3c0-1.5-0.5-2.3-2-2.3 c-0.7,0-2.3,0.5-3.7,1.1L84,11c1.2-0.4,3-1,4.4-1c2.7,0,4.6,1.4,4.6,4.7L93,22z M90,16.4l-2.2,0.4c-0.7,0.1-1.4,0.5-1.4,1.6 c0,0.9,0.5,1.4,1.3,1.4s1.5-0.5,2.3-1V16.4z"}),i().createElement("path",{d:"M104.5,21.3c-1.1,0.4-2.2,0.6-3.5,0.6c-4.2,0-5.9-2.4-5.9-5.9c0-3.7,2.3-6,6.1-6c1.4,0,2.3,0.2,3.2,0.5V13 c-0.8-0.3-2-0.6-3.2-0.6c-1.7,0-3.2,0.9-3.2,3.6c0,2.9,1.5,3.8,3.3,3.8c0.9,0,1.9-0.2,3.2-0.7V21.3z"}),i().createElement("path",{d:"M110,15.2c0.2-0.3,0.2-0.8,3.8-5.2h3.7l-4.6,5.7l5,6.3h-3.7l-4.2-5.8V22h-3V6h3V15.2z"}),i().createElement("path",{d:"M58.5,21.3c-1.5,0.5-2.7,0.6-4.2,0.6c-3.6,0-5.8-1.8-5.8-6c0-3.1,1.9-5.9,5.5-5.9s4.9,2.5,4.9,4.9c0,0.8,0,1.5-0.1,2h-7.3 c0.1,2.5,1.5,2.8,3.6,2.8c1.1,0,2.2-0.3,3.4-0.7C58.5,19,58.5,21.3,58.5,21.3z M56,15c0-1.4-0.5-2.9-2-2.9c-1.4,0-2.3,1.3-2.4,2.9 C51.6,15,56,15,56,15z"})))}},8509:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(3022),o=r(1609),s=r(7371);const a=Number(s.A.smCols),c=Number(s.A.mdCols),i=Number(s.A.lgCols),l=e=>{const{children:t,tagName:r="div",className:l}=e,u=Math.min(a,"number"==typeof e.sm?e.sm:a),p=Math.min(a,"object"==typeof e.sm?e.sm.start:0),d=Math.min(a,"object"==typeof e.sm?e.sm.end:0),f=Math.min(c,"number"==typeof e.md?e.md:c),h=Math.min(c,"object"==typeof e.md?e.md.start:0),g=Math.min(c,"object"==typeof e.md?e.md.end:0),m=Math.min(i,"number"==typeof e.lg?e.lg:i),v=Math.min(i,"object"==typeof e.lg?e.lg.start:0),y=Math.min(i,"object"==typeof e.lg?e.lg.end:0),b=(0,n.A)(l,{[s.A[`col-sm-${u}`]]:!(p&&d),[s.A[`col-sm-${p}-start`]]:p>0,[s.A[`col-sm-${d}-end`]]:d>0,[s.A[`col-md-${f}`]]:!(h&&g),[s.A[`col-md-${h}-start`]]:h>0,[s.A[`col-md-${g}-end`]]:g>0,[s.A[`col-lg-${m}`]]:!(v&&y),[s.A[`col-lg-${v}-start`]]:v>0,[s.A[`col-lg-${y}-end`]]:y>0});return(0,o.createElement)(r,{className:b},t)}},5918:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(3022),o=r(1609),s=r(2420);const a=({children:e,fluid:t=!1,tagName:r="div",className:a,horizontalGap:c=1,horizontalSpacing:i=1},l)=>{const u=(0,o.useMemo)((()=>{const e=`calc( var(--horizontal-spacing) * ${i} )`;return{paddingTop:e,paddingBottom:e,rowGap:`calc( var(--horizontal-spacing) * ${c} )`}}),[c,i]),p=(0,n.A)(a,s.A.container,{[s.A.fluid]:t});return(0,o.createElement)(r,{className:p,style:u,ref:l},e)},c=(0,o.forwardRef)(a)},442:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});r(4006),r(9588),r(2920);var n=r(9491),o=r(8403);const s=["sm","md","lg"],a=(e,t)=>{const r=Array.isArray(e)?e:[e],a=Array.isArray(t)?t:[t],[c,i,l]=s,u={sm:(0,n.useMediaQuery)(o.A[c]),md:(0,n.useMediaQuery)(o.A[i]),lg:(0,n.useMediaQuery)(o.A[l])};return r.map(((e,t)=>{const r=a[t];return r?((e,t,r)=>{const n=s.indexOf(e),o=n+1,a=t.includes("=");let c=[];return t.startsWith("<")&&(c=s.slice(0,a?o:n)),t.startsWith(">")&&(c=s.slice(a?n:o)),c?.length?c.some((e=>r[e])):r[e]})(e,r,u):u[e]}))}},6461:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(8120),o=r.n(n),s=r(1609),a=r.n(s);r(8325);const c=({color:e="#FFFFFF",className:t="",size:r=20})=>{const n=t+" jp-components-spinner",o={width:r,height:r,fontSize:r,borderTopColor:e},s={borderTopColor:e,borderRightColor:e};return a().createElement("div",{className:n},a().createElement("div",{className:"jp-components-spinner__outer",style:o},a().createElement("div",{className:"jp-components-spinner__inner",style:s})))};c.propTypes={color:o().string,className:o().string,size:o().number};const i=c},723:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>d});var n=r(1609),o=r.n(n),s=r(9422);const a={"--font-headline-medium":"48px","--font-headline-small":"36px","--font-title-medium":"24px","--font-title-small":"20px","--font-body":"16px","--font-body-small":"14px","--font-body-extra-small":"12px","--font-title-large":"var(--font-headline-small)","--font-label":"var(--font-body-extra-small)"},c={"--jp-black":"#000000","--jp-black-80":"#2c3338","--jp-white":"#ffffff","--jp-white-off":"#f9f9f6","--jp-gray":"#dcdcde","--jp-gray-0":"#F6F7F7","--jp-gray-5":"var(--jp-gray)","--jp-gray-10":"#C3C4C7","--jp-gray-20":"#A7AAAD","--jp-gray-40":"#787C82","--jp-gray-50":"#646970","--jp-gray-60":"#50575E","--jp-gray-70":"#3C434A","--jp-gray-80":"#2C3338","--jp-gray-90":"#1d2327","--jp-gray-off":"#e2e2df","--jp-red-0":"#F7EBEC","--jp-red-5":"#FACFD2","--jp-red-40":"#E65054","--jp-red-50":"#D63638","--jp-red-60":"#B32D2E","--jp-red-70":"#8A2424","--jp-red-80":"#691C1C","--jp-red":"#d63639","--jp-yellow-5":"#F5E6B3","--jp-yellow-10":"#F2CF75","--jp-yellow-20":"#F0C930","--jp-yellow-30":"#DEB100","--jp-yellow-40":"#C08C00","--jp-yellow-50":"#9D6E00","--jp-yellow-60":"#7D5600","--jp-blue-20":"#68B3E8","--jp-blue-40":"#1689DB","--jp-pink":"#C9356E","--jp-green-0":"#f0f2eb","--jp-green-5":"#d0e6b8","--jp-green-10":"#9dd977","--jp-green-20":"#64ca43","--jp-green-30":"#2fb41f","--jp-green-40":"#069e08","--jp-green-50":"#008710","--jp-green-60":"#007117","--jp-green-70":"#005b18","--jp-green-80":"#004515","--jp-green-90":"#003010","--jp-green-100":"#001c09","--jp-green":"#069e08","--jp-green-primary":"var( --jp-green-40 )","--jp-green-secondary":"var( --jp-green-30 )"},i={"--jp-border-radius":"4px","--jp-menu-border-height":"1px","--jp-underline-thickness":"2px"},l={"--spacing-base":"8px"},u={},p=(e,t,r)=>{const n={...a,...c,...i,...l};for(const t in n)e.style.setProperty(t,n[t]);r&&e.classList.add(s.A.global),t&&(u[t]={provided:!0,root:e})},d=({children:e=null,targetDom:t,id:r,withGlobalStyles:s=!0})=>{const a=(0,n.useRef)(),c=u?.[r]?.provided;return(0,n.useLayoutEffect)((()=>{if(!c)return t?p(t,r,s):void(a?.current&&p(a.current,r,s))}),[t,a,c,r,s]),t?o().createElement(o().Fragment,null,e):o().createElement("div",{ref:a},e)}},1069:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(7999);function o(){return(0,n.getScriptData)()?.site?.admin_url||window.Initial_State?.adminUrl||window.Jetpack_Editor_Initial_State?.adminUrl||window?.myJetpackInitialState?.adminUrl||null}},3924:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});r(4006),r(9588);function n(e,t={}){const r={};let n;if("undefined"!=typeof window&&(n=window?.JP_CONNECTION_INITIAL_STATE?.calypsoEnv),0===e.search("https://")){const t=new URL(e);e=`https://${t.host}${t.pathname}`,r.url=encodeURIComponent(e)}else r.source=encodeURIComponent(e);for(const e in t)r[e]=encodeURIComponent(t[e]);!Object.keys(r).includes("site")&&"undefined"!=typeof jetpack_redirects&&Object.hasOwn(jetpack_redirects,"currentSiteRawUrl")&&(r.site=jetpack_redirects.currentBlogID??jetpack_redirects.currentSiteRawUrl),n&&(r.calypso_env=n);return"https://jetpack.com/redirect/?"+Object.keys(r).map((e=>e+"="+r[e])).join("&")}},6439:(e,t,r)=>{let n={};try{n=r(1403)}catch{console.error("jetpackConfig is missing in your webpack config file. See @automattic/jetpack-config"),n={missingConfig:!0}}const o=e=>Object.hasOwn(n,e);e.exports={jetpackConfigHas:o,jetpackConfigGet:e=>{if(!o(e))throw'This app requires the "'+e+'" Jetpack Config to be defined in your webpack configuration file. See details in @automattic/jetpack-config package docs.';return n[e]}}},5985:(e,t,r)=>{"use strict";r.d(t,{pg:()=>n.A});r(2810),r(4815);var n=r(1409);r(2034),r(5595),r(3265),r(3489),r(7119),r(8406),r(6923),r(335),r(8290),r(9061),r(5929),r(5765)},5765:(e,t,r)=>{"use strict";r(8490)},2810:(e,t,r)=>{"use strict";r(8377).T["Jetpack Green 40"]},335:(e,t,r)=>{"use strict";r(4006),r(8991),r(6087)},4815:(e,t,r)=>{"use strict";r(7999)},3489:(e,t,r)=>{"use strict";var n=r(372);r(9384),r(6087);const{tracks:o}=n.A,{recordEvent:s}=o},7119:(e,t,r)=>{"use strict";r(7143),r(6087),r(8468)},6923:(e,t,r)=>{"use strict";r(7143),r(6087),r(8290)},8406:(e,t,r)=>{"use strict";r(6087)},5929:(e,t,r)=>{"use strict";r(7143),r(2619),r(3265),r(7119)},9520:(e,t,r)=>{"use strict";var n=r(6941),o=r.n(n);window,o()("shared-extension-utils:connection")},9061:(e,t,r)=>{"use strict";r(9520)},7105:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>p,E9:()=>u});var n=r(7143),o=r(2634),s=r(4478),a=r(8290);const c="SET_JETPACK_MODULES";function i(e){return u({isLoading:e})}function l(e,t){return{type:"SET_MODULE_UPDATING",name:e,isUpdating:t}}function u(e){return{type:c,options:e}}const p={updateJetpackModuleStatus:function*(e){try{yield l(e.name,!0),yield(0,s.sB)(e);const t=yield(0,s.wz)();return yield u({data:t}),!0}catch{const e=(0,n.select)(a.F).getJetpackModules();return yield u(e),!1}finally{yield l(e.name,!1)}},setJetpackModules:u,fetchModules:function*(){if((0,o.Sy)())return!0;try{yield i(!0);const e=yield(0,s.wz)();return yield u({data:e}),!0}catch{const e=(0,n.select)(a.F).getJetpackModules();return yield u(e),!1}finally{yield i(!1)}}}},4478:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>l,sB:()=>i,wz:()=>c});var n=r(1455),o=r.n(n);const s="FETCH_JETPACK_MODULES",a="UPDATE_JETPACK_MODULE_STATUS",c=()=>({type:s}),i=e=>({type:a,settings:e}),l={[s]:function(){return o()({path:"/jetpack/v4/module/all",method:"GET"})},[a]:function({settings:e}){return o()({path:`/jetpack/v4/module/${e.name}/active`,method:"POST",data:{active:e.active}})}}},8290:(e,t,r)=>{"use strict";r.d(t,{F:()=>l});var n=r(7143),o=r(7105),s=r(4478),a=r(8862),c=r(2701),i=r(1640);const l="jetpack-modules",u=(0,n.createReduxStore)(l,{reducer:a.A,actions:o.Ay,controls:s.Ay,resolvers:c.A,selectors:i.A});(0,n.register)(u);const p=window?.Initial_State?.getModules||window?.Jetpack_Editor_Initial_State?.modules||null;null!==p&&(0,n.dispatch)(l).setJetpackModules({data:{...p}})},8862:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});const n={isLoading:!1,isUpdating:{},data:{}},o=(e=n,t)=>{switch(t.type){case"SET_JETPACK_MODULES":return{...e,...t.options};case"SET_MODULE_UPDATING":return{...e,isUpdating:{...e.isUpdating,[t.name]:t.isUpdating}}}return e}},2701:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(7105),o=r(4478);const s={getJetpackModules:function*(){try{const e=yield(0,o.wz)();if(e)return(0,n.E9)({data:e})}catch(e){console.error(e)}}}},1640:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(2634);const o={getJetpackModules:e=>e.data,isModuleActive:(e,t)=>(0,n.Sy)()||(e?.data?.[t]?.activated??!1),areModulesLoading:e=>e.isLoading??!1,isModuleUpdating:(e,t)=>e?.isUpdating?.[t]??!1}},3265:(e,t,r)=>{"use strict";var n=r(7723);r(3832),r(8468),r(4815);const __=n.__;__("Upgrade your plan to use video covers","jetpack-wordads"),__("Upgrade your plan to upload audio","jetpack-wordads")},2034:(e,t,r)=>{"use strict";r(2279)},1409:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});r(4006),r(2920);var n=r(7999);function o(){const{connectedPlugins:e,connectionStatus:t}=(0,n.getScriptData)()?.connection??{};return t?.isActive&&e?.some((({slug:e})=>"jetpack"===e))}},2634:(e,t,r)=>{"use strict";function n(){return"object"==typeof window&&"string"==typeof window._currentSiteType?window._currentSiteType:null}function o(){return"simple"===n()}r.d(t,{Sy:()=>o})},5595:(e,t,r)=>{"use strict";r(6072),r(9491)},7982:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});r(9060);var n=r(1883),o=r(3022),s=r(9367),a=r.n(s),c=r(2903),i=r.n(c),l=r(8120),u=r.n(l),p=r(1609),d=r.n(p);class f extends d().Component{static propTypes={title:u().any,vertical:u().any,style:u().object,className:u().string,device:u().oneOf(["desktop","tablet","phone"])};static defaultProps={vertical:null};render(){return d().createElement("div",{className:(0,o.A)("dops-card-section",this.props.className),style:this.props.style},this.props.title?this._renderWithTitle():this.props.children)}_renderWithTitle=()=>{const e="dops-card-section-orient-"+(this.props.vertical?"vertical":"horizontal");return d().createElement("div",{className:e},d().createElement("h4",{className:"dops-card-section-label"},this.props.title),d().createElement("div",{className:"dops-card-section-content"},this.props.children))}}class h extends d().Component{render(){return d().createElement("div",{className:"dops-card-footer"},this.props.children)}}class g extends d().Component{static propTypes={meta:u().any,icon:u().string,iconLabel:u().any,iconColor:u().string,style:u().object,className:u().string,href:u().string,onClick:u().func,title:u().string,tagName:u().string,target:u().string,compact:u().bool,children:u().node};static defaultProps={iconColor:"#787878",className:"",tagName:"div",onClick:()=>{}};render(){const e=(0,o.A)("dops-card",this.props.className,{"is-card-link":!!this.props.href,"is-compact":this.props.compact}),t=["compact","tagName","meta","iconColor"];let r,s;return this.props.href?r=d().createElement(n.A,{className:"dops-card__link-indicator",icon:this.props.target?"external":"chevron-right"}):t.push("href","target"),this.props.title&&(s=d().createElement("h2",{className:"dops-card-title"},this.props.title,this.props.meta&&d().createElement("span",{className:"dops-card-meta"},this.props.meta),(this.props.icon||this.props.iconLabel)&&this._renderIcon())),d().createElement(this.props.href?"a":this.props.tagName,a()(i()(this.props,t),{className:e}),r,s,this.props.children)}_renderIcon=()=>d().createElement("span",{className:"dops-card-icon",style:{color:this.props.iconColor}},this.props.icon&&d().createElement(n.A,{icon:this.props.icon,style:{backgroundColor:this.props.iconColor}}),this.props.iconLabel)}g.Section=f,g.Footer=h;const m=g},252:(e,t,r)=>{"use strict";r.d(t,{A:()=>y});var n=r(372),o=r(5932),s=r(6461),a=r(2947),c=r(5640),i=r(5918),l=r(8509),u=r(5985),p=r(7143),d=r(7723),f=r(2121),h=r(9624),g=r(1609),m=r.n(g),v=r(8228);const __=d.__;function y(){(0,p.useSelect)((e=>e(v.a).getWordAdsModuleStatus()),[]);const e=(0,p.useDispatch)(v.a).updateJetpackSettings,t=(0,p.useSelect)((e=>e(v.a).isModuleEnabled())),r=(0,p.useSelect)((e=>e(v.a).isUpdatingJetpackSettings())),d=(0,p.useSelect)((e=>e(v.a).isTogglingModule())),y=(0,p.useSelect)((e=>e(v.a).isResolving("getWordAdsModuleStatus")||!e(v.a).hasStartedResolution("getWordAdsModuleStatus"))),b=(0,p.useDispatch)(v.a).removeNotice,w=(0,p.useSelect)((e=>e(v.a).getNotices()),[]);return(0,g.useMemo)((()=>{const e=(0,p.select)(v.a).getAPIRootUrl(),t=(0,p.select)(v.a).getAPINonce();e&&o.Ay.setApiRoot(e),t&&o.Ay.setApiNonce(t),(()=>{const e=(0,p.select)(v.a).getWpcomUser(),t=(0,p.select)(v.a).getBlogId();e&&n.A.initialize(e.ID,e.login,{blog_id:t})})(),n.A.tracks.recordEvent("jetpack_wordads_admin_page_view",{current_version:(0,p.select)(v.a).getVersion()})}),[]),m().createElement(g.Fragment,null,y&&m().createElement(s.A,{className:"jp-wordads-dashboard-page-loading-spinner",color:"#000",size:32}),!y&&m().createElement(a.A,{moduleName:__("WordAds","jetpack-wordads"),useInternalLinks:(0,u.pg)()},m().createElement(c.A,null,m().createElement(i.A,{horizontalSpacing:5},m().createElement(l.A,{sm:4},m().createElement(h.A,{updateOptions:e,isModuleEnabled:t,isSavingOptions:r,isTogglingModule:d}))))),m().createElement(f.A,{notices:w,handleLocalNoticeDismissClick:b}))}},4092:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var n=r(6072),o=r.n(n),s=r(3022),a=r(1363),c=r(2903),i=r.n(c),l=r(1609),u=r.n(l);class p extends u().Component{static displayName="CompactFormToggle";render(){return u().createElement(a.A,o()({},i()(this.props,"className"),{className:(0,s.A)(this.props.className,"is-compact")}),this.props.children)}}},1363:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(3022),o=r(8120),s=r.n(o),a=r(1609),c=r.n(a);class i extends a.Component{static propTypes={onChange:s().func,onKeyDown:s().func,checked:s().bool,disabled:s().bool,id:s().string,className:s().string,toggling:s().bool,"aria-label":s().string,children:s().node,disabledReason:s().node,switchClassNames:s().string,labelClassNames:s().string};static defaultProps={checked:!1,disabled:!1,onKeyDown:()=>{},onChange:()=>{},disabledReason:""};state={};static idNum=0;constructor(){super(...arguments),this.onKeyDown=this.onKeyDown.bind(this),this.onClick=this.onClick.bind(this),this.onLabelClick=this.onLabelClick.bind(this)}UNSAFE_componentWillMount(){this.id=this.constructor.idNum++}onKeyDown(e){this.props.disabled||("Enter"!==e.key&&" "!==e.key||(e.preventDefault(),this.props.onChange()),this.props.onKeyDown(e))}onClick(){this.props.disabled||this.props.onChange()}onLabelClick(e){if(this.props.disabled)return;const t=e.target.nodeName.toLowerCase();"a"!==t&&"input"!==t&&"select"!==t&&(e.preventDefault(),this.props.onChange())}render(){const e=this.props.id||"toggle-"+this.id,t=(0,n.A)("form-toggle",this.props.className,{"is-toggling":this.props.toggling});return c().createElement(a.Fragment,null,c().createElement("div",{className:(0,n.A)("form-toggle__switch-container",this.props.switchClassNames)},c().createElement("input",{className:t,type:"checkbox",checked:this.props.checked,readOnly:!0,disabled:this.props.disabled}),c().createElement("span",{className:(0,n.A)("form-toggle__switch",this.props.switchClassNames),disabled:this.props.disabled,id:e,onClick:this.onClick,onKeyDown:this.onKeyDown,role:"checkbox","aria-checked":this.props.checked,"aria-label":this.props["aria-label"],tabIndex:this.props.disabled?-1:0})),c().createElement("label",{className:(0,n.A)("form-toggle__label",this.props.labelClassNames),htmlFor:e},c().createElement("span",{className:(0,n.A)("form-toggle__label-content",this.props.labelClassNames),onClick:this.onLabelClick},this.props.children)))}}},2121:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});r(4006),r(9588);var n=r(8420),o=r(8423),s=r(1609),a=r.n(s);function c(e={handleLocalNoticeDismissClick:null,notices:Object.freeze([])}){const t=e.notices.map((function(t){const r=t=>()=>{t&&e.handleLocalNoticeDismissClick(t.id)};return a().createElement(n.A,{key:"notice-"+t.id,status:t.status,duration:t.duration||null,text:t.text,isCompact:t.isCompact,onDismissClick:r(t),showDismiss:t.showDismiss},t.button&&a().createElement(o.A,{href:t.href,onClick:r(t)},t.button))}));return t.length?a().createElement("div",{id:e.id,className:"global-notices"},t):null}},6450:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>g,Er:()=>u,HI:()=>f,I4:()=>c,Op:()=>a,hn:()=>h,tg:()=>p});var n=r(7723),o=r(4436),s=r.n(o);const __=n.__,a="CREATE_NOTICE",c="REMOVE_NOTICE";function i(e,t,r={}){const n={id:r.id||s()(),duration:r.duration??2e3,showDismiss:"boolean"!=typeof r.showDismiss||r.showDismiss,isPersistent:r.isPersistent||!1,displayOnNextPage:r.displayOnNextPage||!1,status:e,text:t};return{type:a,notice:n}}function l(e){return{type:c,notice:{id:e}}}const u=i.bind(null,"is-success"),p=i.bind(null,"is-error"),d=(i.bind(null,"is-info"),i.bind(null,"is-warning")),f=(e=__("Updating settings…","jetpack-wordads"))=>i("is-info",e,{duration:3e4,id:"search-updating-settings"}),h=()=>l("search-updating-settings"),g={createNotice:i,removeNotice:l,successNotice:u,errorNotice:p,warningNotice:d,updatingNotice:f,removeUpdatingNotice:h}},1473:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});r(4006),r(7210);var n=r(6450);const o=(e={notices:[]},t)=>{switch(t.type){case n.Op:return{...e,notices:[...e.notices,t.notice]};case n.I4:return{...e,notices:e.notices.filter((e=>e.id!==t.notice.id))}}return e}},8179:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={getNotices:e=>e.notices.notices??[]}},9624:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var n=r(372),o=r(7723),s=r(3022),a=r(7982),c=r(4092),i=r(1609),l=r.n(i);const __=o.__,u=__("Earn income by allowing Jetpack to display high quality ads.","jetpack-wordads");function p({updateOptions:e,isSavingOptions:t,isModuleEnabled:r,isTogglingModule:o}){const p=(0,i.useCallback)((()=>{const t={module_active:!r};e(t),n.A.tracks.recordEvent("jetpack_wordads_module_toggle",t)}),[r,e]);return l().createElement("div",{className:"jp-form-settings-group jp-form-wordads-settings-group"},l().createElement(a.A,{className:(0,s.A)({"jp-form-has-child":!0,"jp-form-settings-disable":!1})},l().createElement("div",{className:"jp-form-wordads-settings-group-inside"},l().createElement("div",{className:"jp-form-wordads-settings-group__toggle is-search jp-wordads-dashboard-wrap"},l().createElement("div",{className:"jp-wordads-dashboard-row"},l().createElement("div",{className:"lg-col-span-2 md-col-span-1 sm-col-span-0"}),l().createElement(c.A,{checked:r,disabled:t,onChange:p,toggling:o,className:"is-wordads-admin",switchClassNames:"lg-col-span-1 md-col-span-1 sm-col-span-1",labelClassNames:" lg-col-span-7 md-col-span-5 sm-col-span-3","aria-label":__("Enable WordAds","jetpack-wordads")},__("Enable WordAds","jetpack-wordads")),l().createElement("div",{className:"lg-col-span-2 md-col-span-1 sm-col-span-0"})),l().createElement("div",{className:"jp-wordads-dashboard-row"},l().createElement("div",{className:"lg-col-span-3 md-col-span-2 sm-col-span-1"}),l().createElement("div",{className:"jp-form-wordads-settings-group__toggle-description lg-col-span-7 md-col-span-5 sm-col-span-3"},l().createElement("p",{className:"jp-form-wordads-settings-group__toggle-explanation"},u)),l().createElement("div",{className:"lg-col-span-2 md-col-span-1 sm-col-span-0"}))))))}},8420:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var n=r(1883),o=r(3022),s=r(6418),a=r.n(s),c=r(8120),i=r.n(c),l=r(1609),u=r.n(l);class p extends u().Component{static displayName="SimpleNotice";static defaultProps={duration:0,status:null,showDismiss:!0,className:"",onDismissClick:a()};static propTypes={status:i().string,showDismiss:i().bool,isCompact:i().bool,duration:i().number,text:i().oneOfType([i().oneOfType([i().string,i().node]),i().arrayOf(i().oneOfType([i().string,i().node]))]),icon:i().string,onDismissClick:i().func,className:i().string};dismissTimeout=null;componentDidMount(){this.props.duration>0&&(this.dismissTimeout=setTimeout(this.props.onDismissClick,this.props.duration))}componentWillUnmount(){this.dismissTimeout&&clearTimeout(this.dismissTimeout)}getIcon=()=>{let e;switch(this.props.status){case"is-info":default:e="info";break;case"is-success":e="checkmark";break;case"is-error":case"is-warning":e="notice"}return e};clearText=e=>"string"==typeof e?e.replace(/(<([^>]+)>)/gi,""):e;onKeyDownCallback=e=>t=>{13!==t.which&&32!==t.which||e&&e(t)};render(){const{children:e,className:t,icon:r,isCompact:s,onDismissClick:a,showDismiss:c=!s,status:i,text:l,dismissText:p}=this.props,d=(0,o.A)("dops-notice",i,t,{"is-compact":s,"is-dismissable":c});return u().createElement("div",{className:d},u().createElement("span",{className:"dops-notice__icon-wrapper"},u().createElement(n.A,{className:"dops-notice__icon",icon:r||this.getIcon(),size:24})),u().createElement("span",{className:"dops-notice__content"},u().createElement("span",{className:"dops-notice__text"},l?this.clearText(l):e)),l?e:null,c&&u().createElement("span",{role:"button",onKeyDown:this.onKeyDownCallback(a),tabIndex:"0",className:"dops-notice__dismiss",onClick:a},u().createElement(n.A,{icon:"cross",size:24}),u().createElement("span",{className:"dops-notice__screen-reader-text screen-reader-text"},p)))}}},8423:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(1883),o=r(8120),s=r.n(o),a=r(1609),c=r.n(a);class i extends c().Component{static displayName="NoticeAction";static propTypes={href:s().string,onClick:s().func,external:s().bool,icon:s().string};static defaultProps={external:!1};render(){const e={className:"dops-notice__action",href:this.props.href,onClick:this.props.onClick};return this.props.external&&(e.target="_blank"),c().createElement("a",e,c().createElement("span",null,this.props.children),this.props.icon&&c().createElement(n.A,{icon:this.props.icon,size:24}),this.props.external&&c().createElement(n.A,{icon:"external",size:24}))}}},4568:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(6450);const o={...r(5694).Ay,...n.Ay}},5694:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>d,aV:()=>u,iJ:()=>p});var n=r(7143),o=r(7723),s=r(6450),a=r(8795),c=r.n(a),i=r(8228),l=r(1464);const __=o.__,u="SET_WORDADS_SETTINGS";function p(e){return{type:u,options:e}}const d={updateJetpackSettings:function*(e){try{yield(0,s.HI)(),yield p({is_updating:!0}),yield p(e),yield(0,l.Dw)(e);const t=yield(0,l.E_)();return yield p(t),(0,s.Er)(__("Updated settings.","jetpack-wordads"))}catch{const e=c()((0,n.select)(i.a).getWordAdsModuleStatus(),["module_active"]);return yield p(e),(0,s.tg)(__("Error Update settings…","jetpack-wordads"))}finally{yield(0,s.hn)(),yield p({is_updating:!1})}},setJetpackSettings:p}},1464:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>i,Dw:()=>c,E_:()=>a});var n=r(5932);const o="FETCH_WORDADS_SETTINGS",s="UPDATE_WORDADS_SETTINGS",a=()=>({type:o}),c=e=>({type:s,settings:e}),i={[o]:function(){return n.Ay.fetchWordAdsSettings()},[s]:function(e){return n.Ay.updateWordAdsSettings(e.settings)}}},8228:(e,t,r)=>{"use strict";r.d(t,{a:()=>i,i:()=>l});var n=r(4568),o=r(1464),s=r(8295),a=r(5267),c=r(193);const i="jetpack-wordads-plugin",l={reducer:s.A,actions:n.A,selectors:c.A,resolvers:a.A,controls:o.Ay,initialState:window.WORDADS_DASHBOARD_INITIAL_STATE||{}}},9597:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n=(e=[])=>e},8295:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(7143),o=r(1473),s=r(9597),a=r(4795),c=r(5513),i=r(9585);const l=(0,n.combineReducers)({siteData:c.A,jetpackSettings:a.A,userData:i.A,features:s.A,notices:o.A})},4795:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(5694);const o=(e={},t)=>t.type===n.aV?{...e,...t.options,is_toggling_module:e.module_active!==t.options.module_active&&!!t.options.is_updating,is_toggling_instant_search:e.instant_search_enabled!==t.options.instant_search_enabled&&!!t.options.is_updating}:e},5513:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n=(e={})=>e},9585:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n=(e={})=>e},5267:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(7723),o=r(6450),s=r(5694),a=r(1464);const __=n.__;const c={getWordAdsModuleStatus:function*(){try{const e=yield(0,a.E_)();if(e)return(0,s.iJ)(e)}catch{return(0,o.tg)(__("Error fetching settings…","jetpack-wordads"))}}}},6155:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={isFeatureEnabled:(e,t)=>Array.isArray(e.features)&&e.features.includes(t)}},193:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(8179),o=r(6155),s=r(577),a=r(3491),c=r(8159);const i={...a.A,...s.A,...c.A,...n.A,...o.A}},577:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={getWordAdsModuleStatus:e=>e.jetpackSettings,isModuleEnabled:e=>e.jetpackSettings.module_active,isInstantSearchEnabled:e=>e.jetpackSettings.instant_search_enabled,isUpdatingJetpackSettings:e=>e.jetpackSettings.is_updating,isTogglingModule:e=>e.jetpackSettings.is_toggling_module,isTogglingInstantSearch:e=>e.jetpackSettings.is_toggling_instant_search}},3491:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={getAPIRootUrl:e=>e.siteData?.WP_API_root??null,getAPINonce:e=>e.siteData?.WP_API_nonce??null,getRegistrationNonce:e=>e.siteData?.registrationNonce??null,getSiteAdminUrl:e=>e.siteData?.adminUrl??null,getBlogId:e=>e.siteData?.blogId??0,getVersion:e=>e.siteData?.version??"development",getCalypsoSlug:e=>e.siteData?.calypsoSlug}},8159:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={getWpcomUser:e=>e.userData?.currentUser?.wpcomUser}},1403:e=>{"use strict";e.exports={consumer_slug:"jetpack-wordads-package"}},9384:e=>{"use strict";e.exports=window.JetpackConnection},7999:e=>{"use strict";e.exports=window.JetpackScriptDataModule},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},8468:e=>{"use strict";e.exports=window.lodash},1455:e=>{"use strict";e.exports=window.wp.apiFetch},9491:e=>{"use strict";e.exports=window.wp.compose},7143:e=>{"use strict";e.exports=window.wp.data},8490:e=>{"use strict";e.exports=window.wp.domReady},6087:e=>{"use strict";e.exports=window.wp.element},2619:e=>{"use strict";e.exports=window.wp.hooks},7723:e=>{"use strict";e.exports=window.wp.i18n},2279:e=>{"use strict";e.exports=window.wp.plugins},5573:e=>{"use strict";e.exports=window.wp.primitives},3832:e=>{"use strict";e.exports=window.wp.url},6072:e=>{function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},3136:(e,t,r)=>{"use strict";var n=r(2003),o=r(561),s=TypeError;e.exports=function(e){if(n(e))return e;throw new s(o(e)+" is not a function")}},3501:(e,t,r)=>{"use strict";var n=r(8599),o=TypeError;e.exports=function(e,t){if(n(t,e))return e;throw new o("Incorrect invocation")}},3773:(e,t,r)=>{"use strict";var n=r(2480),o=String,s=TypeError;e.exports=function(e){if(n(e))return e;throw new s(o(e)+" is not an object")}},8419:(e,t,r)=>{"use strict";var n=r(9351),o=r(1512),s=r(9496),a=function(e){return function(t,r,a){var c=n(t),i=s(c);if(0===i)return!e&&-1;var l,u=o(a,i);if(e&&r!=r){for(;i>u;)if((l=c[u++])!=l)return!0}else for(;i>u;u++)if((e||u in c)&&c[u]===r)return e||u||0;return!e&&-1}};e.exports={includes:a(!0),indexOf:a(!1)}},2625:(e,t,r)=>{"use strict";var n=r(7910),o=r(5866),s=TypeError,a=Object.getOwnPropertyDescriptor,c=n&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}();e.exports=c?function(e,t){if(o(e)&&!a(e,"length").writable)throw new s("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t}},5229:(e,t,r)=>{"use strict";var n=r(3773),o=r(6105);e.exports=function(e,t,r,s){try{return s?t(n(r)[0],r[1]):t(r)}catch(t){o(e,"throw",t)}}},8278:(e,t,r)=>{"use strict";var n=r(2322),o=n({}.toString),s=n("".slice);e.exports=function(e){return s(o(e),8,-1)}},1289:(e,t,r)=>{"use strict";var n=r(3514),o=r(2003),s=r(8278),a=r(7369)("toStringTag"),c=Object,i="Arguments"===s(function(){return arguments}());e.exports=n?s:function(e){var t,r,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(e){}}(t=c(e),a))?r:i?s(t):"Object"===(n=s(t))&&o(t.callee)?"Arguments":n}},1038:(e,t,r)=>{"use strict";var n=r(867),o=r(8337),s=r(2457),a=r(2931);e.exports=function(e,t,r){for(var c=o(t),i=a.f,l=s.f,u=0;u<c.length;u++){var p=c[u];n(e,p)||r&&n(r,p)||i(e,p,l(t,p))}}},7737:(e,t,r)=>{"use strict";var n=r(6977);e.exports=!n((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}))},3623:e=>{"use strict";e.exports=function(e,t){return{value:e,done:t}}},1781:(e,t,r)=>{"use strict";var n=r(7910),o=r(2931),s=r(5762);e.exports=n?function(e,t,r){return o.f(e,t,s(1,r))}:function(e,t,r){return e[t]=r,e}},5762:e=>{"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},2470:(e,t,r)=>{"use strict";var n=r(7910),o=r(2931),s=r(5762);e.exports=function(e,t,r){n?o.f(e,t,s(0,r)):e[t]=r}},1552:(e,t,r)=>{"use strict";var n=r(2609),o=r(2931);e.exports=function(e,t,r){return r.get&&n(r.get,t,{getter:!0}),r.set&&n(r.set,t,{setter:!0}),o.f(e,t,r)}},4386:(e,t,r)=>{"use strict";var n=r(2003),o=r(2931),s=r(2609),a=r(9447);e.exports=function(e,t,r,c){c||(c={});var i=c.enumerable,l=void 0!==c.name?c.name:t;if(n(r)&&s(r,l,c),c.global)i?e[t]=r:a(t,r);else{try{c.unsafe?e[t]&&(i=!0):delete e[t]}catch(e){}i?e[t]=r:o.f(e,t,{value:r,enumerable:!1,configurable:!c.nonConfigurable,writable:!c.nonWritable})}return e}},753:(e,t,r)=>{"use strict";var n=r(4386);e.exports=function(e,t,r){for(var o in t)n(e,o,t[o],r);return e}},9447:(e,t,r)=>{"use strict";var n=r(642),o=Object.defineProperty;e.exports=function(e,t){try{o(n,e,{value:t,configurable:!0,writable:!0})}catch(r){n[e]=t}return t}},7910:(e,t,r)=>{"use strict";var n=r(6977);e.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},6337:(e,t,r)=>{"use strict";var n=r(642),o=r(2480),s=n.document,a=o(s)&&o(s.createElement);e.exports=function(e){return a?s.createElement(e):{}}},3163:e=>{"use strict";var t=TypeError;e.exports=function(e){if(e>9007199254740991)throw t("Maximum allowed index exceeded");return e}},2589:e=>{"use strict";e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},8093:(e,t,r)=>{"use strict";var n=r(642).navigator,o=n&&n.userAgent;e.exports=o?String(o):""},4965:(e,t,r)=>{"use strict";var n,o,s=r(642),a=r(8093),c=s.process,i=s.Deno,l=c&&c.versions||i&&i.version,u=l&&l.v8;u&&(o=(n=u.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!o&&a&&(!(n=a.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=a.match(/Chrome\/(\d+)/))&&(o=+n[1]),e.exports=o},9948:(e,t,r)=>{"use strict";var n=r(642),o=r(2457).f,s=r(1781),a=r(4386),c=r(9447),i=r(1038),l=r(4866);e.exports=function(e,t){var r,u,p,d,f,h=e.target,g=e.global,m=e.stat;if(r=g?n:m?n[h]||c(h,{}):n[h]&&n[h].prototype)for(u in t){if(d=t[u],p=e.dontCallGetSet?(f=o(r,u))&&f.value:r[u],!l(g?u:h+(m?".":"#")+u,e.forced)&&void 0!==p){if(typeof d==typeof p)continue;i(d,p)}(e.sham||p&&p.sham)&&s(d,"sham",!0),a(r,u,d,e)}}},6977:e=>{"use strict";e.exports=function(e){try{return!!e()}catch(e){return!0}}},8170:(e,t,r)=>{"use strict";var n=r(2258),o=r(3136),s=r(1658),a=n(n.bind);e.exports=function(e,t){return o(e),void 0===t?e:s?a(e,t):function(){return e.apply(t,arguments)}}},1658:(e,t,r)=>{"use strict";var n=r(6977);e.exports=!n((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},7623:(e,t,r)=>{"use strict";var n=r(1658),o=Function.prototype.call;e.exports=n?o.bind(o):function(){return o.apply(o,arguments)}},7016:(e,t,r)=>{"use strict";var n=r(7910),o=r(867),s=Function.prototype,a=n&&Object.getOwnPropertyDescriptor,c=o(s,"name"),i=c&&"something"===function(){}.name,l=c&&(!n||n&&a(s,"name").configurable);e.exports={EXISTS:c,PROPER:i,CONFIGURABLE:l}},2258:(e,t,r)=>{"use strict";var n=r(8278),o=r(2322);e.exports=function(e){if("Function"===n(e))return o(e)}},2322:(e,t,r)=>{"use strict";var n=r(1658),o=Function.prototype,s=o.call,a=n&&o.bind.bind(s,s);e.exports=n?a:function(e){return function(){return s.apply(e,arguments)}}},6297:(e,t,r)=>{"use strict";var n=r(642),o=r(2003);e.exports=function(e,t){return arguments.length<2?(r=n[e],o(r)?r:void 0):n[e]&&n[e][t];var r}},4641:e=>{"use strict";e.exports=function(e){return{iterator:e,next:e.next,done:!1}}},8353:(e,t,r)=>{"use strict";var n=r(1289),o=r(8396),s=r(9943),a=r(4175),c=r(7369)("iterator");e.exports=function(e){if(!s(e))return o(e,c)||o(e,"@@iterator")||a[n(e)]}},1471:(e,t,r)=>{"use strict";var n=r(7623),o=r(3136),s=r(3773),a=r(561),c=r(8353),i=TypeError;e.exports=function(e,t){var r=arguments.length<2?c(e):t;if(o(r))return s(n(r,e));throw new i(a(e)+" is not iterable")}},8396:(e,t,r)=>{"use strict";var n=r(3136),o=r(9943);e.exports=function(e,t){var r=e[t];return o(r)?void 0:n(r)}},642:function(e){"use strict";var t=function(e){return e&&e.Math===Math&&e};e.exports=t("object"==typeof globalThis&&globalThis)||t("object"==typeof window&&window)||t("object"==typeof self&&self)||t("object"==typeof window&&window)||t("object"==typeof this&&this)||function(){return this}()||Function("return this")()},867:(e,t,r)=>{"use strict";var n=r(2322),o=r(4707),s=n({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return s(o(e),t)}},8555:e=>{"use strict";e.exports={}},979:(e,t,r)=>{"use strict";var n=r(6297);e.exports=n("document","documentElement")},2159:(e,t,r)=>{"use strict";var n=r(7910),o=r(6977),s=r(6337);e.exports=!n&&!o((function(){return 7!==Object.defineProperty(s("div"),"a",{get:function(){return 7}}).a}))},9233:(e,t,r)=>{"use strict";var n=r(2322),o=r(6977),s=r(8278),a=Object,c=n("".split);e.exports=o((function(){return!a("z").propertyIsEnumerable(0)}))?function(e){return"String"===s(e)?c(e,""):a(e)}:a},2716:(e,t,r)=>{"use strict";var n=r(2322),o=r(2003),s=r(9487),a=n(Function.toString);o(s.inspectSource)||(s.inspectSource=function(e){return a(e)}),e.exports=s.inspectSource},5147:(e,t,r)=>{"use strict";var n,o,s,a=r(204),c=r(642),i=r(2480),l=r(1781),u=r(867),p=r(9487),d=r(8777),f=r(8555),h="Object already initialized",g=c.TypeError,m=c.WeakMap;if(a||p.state){var v=p.state||(p.state=new m);v.get=v.get,v.has=v.has,v.set=v.set,n=function(e,t){if(v.has(e))throw new g(h);return t.facade=e,v.set(e,t),t},o=function(e){return v.get(e)||{}},s=function(e){return v.has(e)}}else{var y=d("state");f[y]=!0,n=function(e,t){if(u(e,y))throw new g(h);return t.facade=e,l(e,y,t),t},o=function(e){return u(e,y)?e[y]:{}},s=function(e){return u(e,y)}}e.exports={set:n,get:o,has:s,enforce:function(e){return s(e)?o(e):n(e,{})},getterFor:function(e){return function(t){var r;if(!i(t)||(r=o(t)).type!==e)throw new g("Incompatible receiver, "+e+" required");return r}}}},779:(e,t,r)=>{"use strict";var n=r(7369),o=r(4175),s=n("iterator"),a=Array.prototype;e.exports=function(e){return void 0!==e&&(o.Array===e||a[s]===e)}},5866:(e,t,r)=>{"use strict";var n=r(8278);e.exports=Array.isArray||function(e){return"Array"===n(e)}},2003:e=>{"use strict";var t="object"==typeof document&&document.all;e.exports=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},4866:(e,t,r)=>{"use strict";var n=r(6977),o=r(2003),s=/#|\.prototype\./,a=function(e,t){var r=i[c(e)];return r===u||r!==l&&(o(t)?n(t):!!t)},c=a.normalize=function(e){return String(e).replace(s,".").toLowerCase()},i=a.data={},l=a.NATIVE="N",u=a.POLYFILL="P";e.exports=a},9943:e=>{"use strict";e.exports=function(e){return null==e}},2480:(e,t,r)=>{"use strict";var n=r(2003);e.exports=function(e){return"object"==typeof e?null!==e:n(e)}},957:e=>{"use strict";e.exports=!1},9895:(e,t,r)=>{"use strict";var n=r(6297),o=r(2003),s=r(8599),a=r(4150),c=Object;e.exports=a?function(e){return"symbol"==typeof e}:function(e){var t=n("Symbol");return o(t)&&s(t.prototype,c(e))}},1738:(e,t,r)=>{"use strict";var n=r(8170),o=r(7623),s=r(3773),a=r(561),c=r(779),i=r(9496),l=r(8599),u=r(1471),p=r(8353),d=r(6105),f=TypeError,h=function(e,t){this.stopped=e,this.result=t},g=h.prototype;e.exports=function(e,t,r){var m,v,y,b,w,j,x,k=r&&r.that,A=!(!r||!r.AS_ENTRIES),C=!(!r||!r.IS_RECORD),E=!(!r||!r.IS_ITERATOR),_=!(!r||!r.INTERRUPTED),S=n(t,k),O=function(e){return m&&d(m,"normal",e),new h(!0,e)},N=function(e){return A?(s(e),_?S(e[0],e[1],O):S(e[0],e[1])):_?S(e,O):S(e)};if(C)m=e.iterator;else if(E)m=e;else{if(!(v=p(e)))throw new f(a(e)+" is not iterable");if(c(v)){for(y=0,b=i(e);b>y;y++)if((w=N(e[y]))&&l(g,w))return w;return new h(!1)}m=u(e,v)}for(j=C?e.next:m.next;!(x=o(j,m)).done;){try{w=N(x.value)}catch(e){d(m,"throw",e)}if("object"==typeof w&&w&&l(g,w))return w}return new h(!1)}},6105:(e,t,r)=>{"use strict";var n=r(7623),o=r(3773),s=r(8396);e.exports=function(e,t,r){var a,c;o(e);try{if(!(a=s(e,"return"))){if("throw"===t)throw r;return r}a=n(a,e)}catch(e){c=!0,a=e}if("throw"===t)throw r;if(c)throw a;return o(a),r}},3772:(e,t,r)=>{"use strict";var n=r(7623),o=r(3666),s=r(1781),a=r(753),c=r(7369),i=r(5147),l=r(8396),u=r(4387).IteratorPrototype,p=r(3623),d=r(6105),f=c("toStringTag"),h="IteratorHelper",g="WrapForValidIterator",m=i.set,v=function(e){var t=i.getterFor(e?g:h);return a(o(u),{next:function(){var r=t(this);if(e)return r.nextHandler();try{var n=r.done?void 0:r.nextHandler();return p(n,r.done)}catch(e){throw r.done=!0,e}},return:function(){var r=t(this),o=r.iterator;if(r.done=!0,e){var s=l(o,"return");return s?n(s,o):p(void 0,!0)}if(r.inner)try{d(r.inner.iterator,"normal")}catch(e){return d(o,"throw",e)}return d(o,"normal"),p(void 0,!0)}})},y=v(!0),b=v(!1);s(b,f,"Iterator Helper"),e.exports=function(e,t){var r=function(r,n){n?(n.iterator=r.iterator,n.next=r.next):n=r,n.type=t?g:h,n.nextHandler=e,n.counter=0,n.done=!1,m(this,n)};return r.prototype=t?y:b,r}},3235:(e,t,r)=>{"use strict";var n=r(7623),o=r(3136),s=r(3773),a=r(4641),c=r(3772),i=r(5229),l=c((function(){var e=this.iterator,t=s(n(this.next,e));if(!(this.done=!!t.done))return i(e,this.mapper,[t.value,this.counter++],!0)}));e.exports=function(e){return s(this),o(e),new l(a(this),{mapper:e})}},4387:(e,t,r)=>{"use strict";var n,o,s,a=r(6977),c=r(2003),i=r(2480),l=r(3666),u=r(6597),p=r(4386),d=r(7369),f=r(957),h=d("iterator"),g=!1;[].keys&&("next"in(s=[].keys())?(o=u(u(s)))!==Object.prototype&&(n=o):g=!0),!i(n)||a((function(){var e={};return n[h].call(e)!==e}))?n={}:f&&(n=l(n)),c(n[h])||p(n,h,(function(){return this})),e.exports={IteratorPrototype:n,BUGGY_SAFARI_ITERATORS:g}},4175:e=>{"use strict";e.exports={}},9496:(e,t,r)=>{"use strict";var n=r(2748);e.exports=function(e){return n(e.length)}},2609:(e,t,r)=>{"use strict";var n=r(2322),o=r(6977),s=r(2003),a=r(867),c=r(7910),i=r(7016).CONFIGURABLE,l=r(2716),u=r(5147),p=u.enforce,d=u.get,f=String,h=Object.defineProperty,g=n("".slice),m=n("".replace),v=n([].join),y=c&&!o((function(){return 8!==h((function(){}),"length",{value:8}).length})),b=String(String).split("String"),w=e.exports=function(e,t,r){"Symbol("===g(f(t),0,7)&&(t="["+m(f(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(t="get "+t),r&&r.setter&&(t="set "+t),(!a(e,"name")||i&&e.name!==t)&&(c?h(e,"name",{value:t,configurable:!0}):e.name=t),y&&r&&a(r,"arity")&&e.length!==r.arity&&h(e,"length",{value:r.arity});try{r&&a(r,"constructor")&&r.constructor?c&&h(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var n=p(e);return a(n,"source")||(n.source=v(b,"string"==typeof t?t:"")),e};Function.prototype.toString=w((function(){return s(this)&&d(this).source||l(this)}),"toString")},5983:e=>{"use strict";var t=Math.ceil,r=Math.floor;e.exports=Math.trunc||function(e){var n=+e;return(n>0?r:t)(n)}},3666:(e,t,r)=>{"use strict";var n,o=r(3773),s=r(3711),a=r(2589),c=r(8555),i=r(979),l=r(6337),u=r(8777),p="prototype",d="script",f=u("IE_PROTO"),h=function(){},g=function(e){return"<"+d+">"+e+"</"+d+">"},m=function(e){e.write(g("")),e.close();var t=e.parentWindow.Object;return e=null,t},v=function(){try{n=new ActiveXObject("htmlfile")}catch(e){}var e,t,r;v="undefined"!=typeof document?document.domain&&n?m(n):(t=l("iframe"),r="java"+d+":",t.style.display="none",i.appendChild(t),t.src=String(r),(e=t.contentWindow.document).open(),e.write(g("document.F=Object")),e.close(),e.F):m(n);for(var o=a.length;o--;)delete v[p][a[o]];return v()};c[f]=!0,e.exports=Object.create||function(e,t){var r;return null!==e?(h[p]=o(e),r=new h,h[p]=null,r[f]=e):r=v(),void 0===t?r:s.f(r,t)}},3711:(e,t,r)=>{"use strict";var n=r(7910),o=r(8576),s=r(2931),a=r(3773),c=r(9351),i=r(6146);t.f=n&&!o?Object.defineProperties:function(e,t){a(e);for(var r,n=c(t),o=i(t),l=o.length,u=0;l>u;)s.f(e,r=o[u++],n[r]);return e}},2931:(e,t,r)=>{"use strict";var n=r(7910),o=r(2159),s=r(8576),a=r(3773),c=r(8543),i=TypeError,l=Object.defineProperty,u=Object.getOwnPropertyDescriptor,p="enumerable",d="configurable",f="writable";t.f=n?s?function(e,t,r){if(a(e),t=c(t),a(r),"function"==typeof e&&"prototype"===t&&"value"in r&&f in r&&!r[f]){var n=u(e,t);n&&n[f]&&(e[t]=r.value,r={configurable:d in r?r[d]:n[d],enumerable:p in r?r[p]:n[p],writable:!1})}return l(e,t,r)}:l:function(e,t,r){if(a(e),t=c(t),a(r),o)try{return l(e,t,r)}catch(e){}if("get"in r||"set"in r)throw new i("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},2457:(e,t,r)=>{"use strict";var n=r(7910),o=r(7623),s=r(6691),a=r(5762),c=r(9351),i=r(8543),l=r(867),u=r(2159),p=Object.getOwnPropertyDescriptor;t.f=n?p:function(e,t){if(e=c(e),t=i(t),u)try{return p(e,t)}catch(e){}if(l(e,t))return a(!o(s.f,e,t),e[t])}},9038:(e,t,r)=>{"use strict";var n=r(2846),o=r(2589).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return n(e,o)}},3683:(e,t)=>{"use strict";t.f=Object.getOwnPropertySymbols},6597:(e,t,r)=>{"use strict";var n=r(867),o=r(2003),s=r(4707),a=r(8777),c=r(7737),i=a("IE_PROTO"),l=Object,u=l.prototype;e.exports=c?l.getPrototypeOf:function(e){var t=s(e);if(n(t,i))return t[i];var r=t.constructor;return o(r)&&t instanceof r?r.prototype:t instanceof l?u:null}},8599:(e,t,r)=>{"use strict";var n=r(2322);e.exports=n({}.isPrototypeOf)},2846:(e,t,r)=>{"use strict";var n=r(2322),o=r(867),s=r(9351),a=r(8419).indexOf,c=r(8555),i=n([].push);e.exports=function(e,t){var r,n=s(e),l=0,u=[];for(r in n)!o(c,r)&&o(n,r)&&i(u,r);for(;t.length>l;)o(n,r=t[l++])&&(~a(u,r)||i(u,r));return u}},6146:(e,t,r)=>{"use strict";var n=r(2846),o=r(2589);e.exports=Object.keys||function(e){return n(e,o)}},6691:(e,t)=>{"use strict";var r={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,o=n&&!r.call({1:2},1);t.f=o?function(e){var t=n(this,e);return!!t&&t.enumerable}:r},6772:(e,t,r)=>{"use strict";var n=r(7623),o=r(2003),s=r(2480),a=TypeError;e.exports=function(e,t){var r,c;if("string"===t&&o(r=e.toString)&&!s(c=n(r,e)))return c;if(o(r=e.valueOf)&&!s(c=n(r,e)))return c;if("string"!==t&&o(r=e.toString)&&!s(c=n(r,e)))return c;throw new a("Can't convert object to primitive value")}},8337:(e,t,r)=>{"use strict";var n=r(6297),o=r(2322),s=r(9038),a=r(3683),c=r(3773),i=o([].concat);e.exports=n("Reflect","ownKeys")||function(e){var t=s.f(c(e)),r=a.f;return r?i(t,r(e)):t}},3384:(e,t,r)=>{"use strict";var n=r(9943),o=TypeError;e.exports=function(e){if(n(e))throw new o("Can't call method on "+e);return e}},8777:(e,t,r)=>{"use strict";var n=r(4335),o=r(1026),s=n("keys");e.exports=function(e){return s[e]||(s[e]=o(e))}},9487:(e,t,r)=>{"use strict";var n=r(957),o=r(642),s=r(9447),a="__core-js_shared__",c=e.exports=o[a]||s(a,{});(c.versions||(c.versions=[])).push({version:"3.38.1",mode:n?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.38.1/LICENSE",source:"https://github.com/zloirock/core-js"})},4335:(e,t,r)=>{"use strict";var n=r(9487);e.exports=function(e,t){return n[e]||(n[e]=t||{})}},789:(e,t,r)=>{"use strict";var n=r(4965),o=r(6977),s=r(642).String;e.exports=!!Object.getOwnPropertySymbols&&!o((function(){var e=Symbol("symbol detection");return!s(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},1512:(e,t,r)=>{"use strict";var n=r(6709),o=Math.max,s=Math.min;e.exports=function(e,t){var r=n(e);return r<0?o(r+t,0):s(r,t)}},9351:(e,t,r)=>{"use strict";var n=r(9233),o=r(3384);e.exports=function(e){return n(o(e))}},6709:(e,t,r)=>{"use strict";var n=r(5983);e.exports=function(e){var t=+e;return t!=t||0===t?0:n(t)}},2748:(e,t,r)=>{"use strict";var n=r(6709),o=Math.min;e.exports=function(e){var t=n(e);return t>0?o(t,9007199254740991):0}},4707:(e,t,r)=>{"use strict";var n=r(3384),o=Object;e.exports=function(e){return o(n(e))}},4603:(e,t,r)=>{"use strict";var n=r(7623),o=r(2480),s=r(9895),a=r(8396),c=r(6772),i=r(7369),l=TypeError,u=i("toPrimitive");e.exports=function(e,t){if(!o(e)||s(e))return e;var r,i=a(e,u);if(i){if(void 0===t&&(t="default"),r=n(i,e,t),!o(r)||s(r))return r;throw new l("Can't convert object to primitive value")}return void 0===t&&(t="number"),c(e,t)}},8543:(e,t,r)=>{"use strict";var n=r(4603),o=r(9895);e.exports=function(e){var t=n(e,"string");return o(t)?t:t+""}},3514:(e,t,r)=>{"use strict";var n={};n[r(7369)("toStringTag")]="z",e.exports="[object z]"===String(n)},561:e=>{"use strict";var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},1026:(e,t,r)=>{"use strict";var n=r(2322),o=0,s=Math.random(),a=n(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+a(++o+s,36)}},4150:(e,t,r)=>{"use strict";var n=r(789);e.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},8576:(e,t,r)=>{"use strict";var n=r(7910),o=r(6977);e.exports=n&&o((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},204:(e,t,r)=>{"use strict";var n=r(642),o=r(2003),s=n.WeakMap;e.exports=o(s)&&/native code/.test(String(s))},7369:(e,t,r)=>{"use strict";var n=r(642),o=r(4335),s=r(867),a=r(1026),c=r(789),i=r(4150),l=n.Symbol,u=o("wks"),p=i?l.for||l:l&&l.withoutSetter||a;e.exports=function(e){return s(u,e)||(u[e]=c&&s(l,e)?l[e]:p("Symbol."+e)),u[e]}},9060:(e,t,r)=>{"use strict";var n=r(9948),o=r(4707),s=r(9496),a=r(2625),c=r(3163);n({target:"Array",proto:!0,arity:1,forced:r(6977)((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}}()},{push:function(e){var t=o(this),r=s(t),n=arguments.length;c(r+n);for(var i=0;i<n;i++)t[r]=arguments[i],r++;return a(t,r),r}})},4006:(e,t,r)=>{"use strict";var n=r(9948),o=r(642),s=r(3501),a=r(3773),c=r(2003),i=r(6597),l=r(1552),u=r(2470),p=r(6977),d=r(867),f=r(7369),h=r(4387).IteratorPrototype,g=r(7910),m=r(957),v="constructor",y="Iterator",b=f("toStringTag"),w=TypeError,j=o[y],x=m||!c(j)||j.prototype!==h||!p((function(){j({})})),k=function(){if(s(this,h),i(this)===h)throw new w("Abstract class Iterator not directly constructable")},A=function(e,t){g?l(h,e,{configurable:!0,get:function(){return t},set:function(t){if(a(this),this===h)throw new w("You can't redefine this property");d(this,e)?this[e]=t:u(this,e,t)}}):h[e]=t};d(h,b)||A(b,y),!x&&d(h,v)&&h[v]!==Object||A(v,k),k.prototype=h,n({global:!0,constructor:!0,forced:x},{Iterator:k})},7210:(e,t,r)=>{"use strict";var n=r(9948),o=r(7623),s=r(3136),a=r(3773),c=r(4641),i=r(3772),l=r(5229),u=r(957),p=i((function(){for(var e,t,r=this.iterator,n=this.predicate,s=this.next;;){if(e=a(o(s,r)),this.done=!!e.done)return;if(t=e.value,l(r,n,[t,this.counter++],!0))return t}}));n({target:"Iterator",proto:!0,real:!0,forced:u},{filter:function(e){return a(this),s(e),new p(c(this),{predicate:e})}})},8991:(e,t,r)=>{"use strict";var n=r(9948),o=r(1738),s=r(3136),a=r(3773),c=r(4641);n({target:"Iterator",proto:!0,real:!0},{forEach:function(e){a(this),s(e);var t=c(this),r=0;o(t,(function(t){e(t,r++)}),{IS_RECORD:!0})}})},9588:(e,t,r)=>{"use strict";var n=r(9948),o=r(3235);n({target:"Iterator",proto:!0,real:!0,forced:r(957)},{map:o})},2920:(e,t,r)=>{"use strict";var n=r(9948),o=r(1738),s=r(3136),a=r(3773),c=r(4641);n({target:"Iterator",proto:!0,real:!0},{some:function(e){a(this),s(e);var t=c(this),r=0;return o(t,(function(t,n){if(e(t,r++))return n()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},3022:(e,t,r)=>{"use strict";function n(e){var t,r,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(r=n(e[t]))&&(o&&(o+=" "),o+=r)}else for(r in e)e[r]&&(o&&(o+=" "),o+=r);return o}r.d(t,{A:()=>o});const o=function(){for(var e,t,r=0,o="",s=arguments.length;r<s;r++)(e=arguments[r])&&(t=n(e))&&(o&&(o+=" "),o+=t);return o}},8377:e=>{"use strict";e.exports=JSON.parse('{"T":{"White":"#fff","Black":"#000","Gray 0":"#f6f7f7","Gray 5":"#dcdcde","Gray 10":"#c3c4c7","Gray 20":"#a7aaad","Gray 30":"#8c8f94","Gray 40":"#787c82","Gray 50":"#646970","Gray 60":"#50575e","Gray 70":"#3c434a","Gray 80":"#2c3338","Gray 90":"#1d2327","Gray 100":"#101517","Gray":"#646970","Blue 0":"#fbfcfe","Blue 5":"#f7f8fe","Blue 10":"#d6ddf9","Blue 20":"#adbaf3","Blue 30":"#7b90ff","Blue 40":"#546ff3","Blue 50":"#3858e9","Blue 60":"#2a46ce","Blue 70":"#1d35b4","Blue 80":"#1f3286","Blue 90":"#14215a","Blue 100":"#0a112d","Blue":"#3858e9","WordPress Blue 0":"#fbfcfe","WordPress Blue 5":"#f7f8fe","WordPress Blue 10":"#d6ddf9","WordPress Blue 20":"#adbaf3","WordPress Blue 30":"#7b90ff","WordPress Blue 40":"#546ff3","WordPress Blue 50":"#3858e9","WordPress Blue 60":"#2a46ce","WordPress Blue 70":"#1d35b4","WordPress Blue 80":"#1f3286","WordPress Blue 90":"#14215a","WordPress Blue 100":"#0a112d","WordPress Blue":"#3858e9","Purple 0":"#f2e9ed","Purple 5":"#ebcee0","Purple 10":"#e3afd5","Purple 20":"#d48fc8","Purple 30":"#c475bd","Purple 40":"#b35eb1","Purple 50":"#984a9c","Purple 60":"#7c3982","Purple 70":"#662c6e","Purple 80":"#4d2054","Purple 90":"#35163b","Purple 100":"#1e0c21","Purple":"#984a9c","Pink 0":"#f5e9ed","Pink 5":"#f2ceda","Pink 10":"#f7a8c3","Pink 20":"#f283aa","Pink 30":"#eb6594","Pink 40":"#e34c84","Pink 50":"#c9356e","Pink 60":"#ab235a","Pink 70":"#8c1749","Pink 80":"#700f3b","Pink 90":"#4f092a","Pink 100":"#260415","Pink":"#c9356e","Red 0":"#f7ebec","Red 5":"#facfd2","Red 10":"#ffabaf","Red 20":"#ff8085","Red 30":"#f86368","Red 40":"#e65054","Red 50":"#d63638","Red 60":"#b32d2e","Red 70":"#8a2424","Red 80":"#691c1c","Red 90":"#451313","Red 100":"#240a0a","Red":"#d63638","Orange 0":"#f5ece6","Orange 5":"#f7dcc6","Orange 10":"#ffbf86","Orange 20":"#faa754","Orange 30":"#e68b28","Orange 40":"#d67709","Orange 50":"#b26200","Orange 60":"#8a4d00","Orange 70":"#704000","Orange 80":"#543100","Orange 90":"#361f00","Orange 100":"#1f1200","Orange":"#b26200","Yellow 0":"#f5f1e1","Yellow 5":"#f5e6b3","Yellow 10":"#f2d76b","Yellow 20":"#f0c930","Yellow 30":"#deb100","Yellow 40":"#c08c00","Yellow 50":"#9d6e00","Yellow 60":"#7d5600","Yellow 70":"#674600","Yellow 80":"#4f3500","Yellow 90":"#320","Yellow 100":"#1c1300","Yellow":"#9d6e00","Green 0":"#e6f2e8","Green 5":"#b8e6bf","Green 10":"#68de86","Green 20":"#1ed15a","Green 30":"#00ba37","Green 40":"#00a32a","Green 50":"#008a20","Green 60":"#007017","Green 70":"#005c12","Green 80":"#00450c","Green 90":"#003008","Green 100":"#001c05","Green":"#008a20","Celadon 0":"#e4f2ed","Celadon 5":"#a7e8d3","Celadon 10":"#66deb9","Celadon 20":"#31cc9f","Celadon 30":"#09b585","Celadon 40":"#009e73","Celadon 50":"#008763","Celadon 60":"#007053","Celadon 70":"#005c44","Celadon 80":"#004533","Celadon 90":"#003024","Celadon 100":"#001c15","Celadon":"#008763","Automattic Blue 0":"#ebf4fa","Automattic Blue 5":"#c4e2f5","Automattic Blue 10":"#88ccf2","Automattic Blue 20":"#5ab7e8","Automattic Blue 30":"#24a3e0","Automattic Blue 40":"#1490c7","Automattic Blue 50":"#0277a8","Automattic Blue 60":"#036085","Automattic Blue 70":"#02506e","Automattic Blue 80":"#02384d","Automattic Blue 90":"#022836","Automattic Blue 100":"#021b24","Automattic Blue":"#24a3e0","Simplenote Blue 0":"#e9ecf5","Simplenote Blue 5":"#ced9f2","Simplenote Blue 10":"#abc1f5","Simplenote Blue 20":"#84a4f0","Simplenote Blue 30":"#618df2","Simplenote Blue 40":"#4678eb","Simplenote Blue 50":"#3361cc","Simplenote Blue 60":"#1d4fc4","Simplenote Blue 70":"#113ead","Simplenote Blue 80":"#0d2f85","Simplenote Blue 90":"#09205c","Simplenote Blue 100":"#05102e","Simplenote Blue":"#3361cc","WooCommerce Purple 0":"#f2edff","WooCommerce Purple 5":"#e1d7ff","WooCommerce Purple 10":"#d1c1ff","WooCommerce Purple 20":"#b999ff","WooCommerce Purple 30":"#a77eff","WooCommerce Purple 40":"#873eff","WooCommerce Purple 50":"#720eec","WooCommerce Purple 60":"#6108ce","WooCommerce Purple 70":"#5007aa","WooCommerce Purple 80":"#3c087e","WooCommerce Purple 90":"#2c045d","WooCommerce Purple 100":"#1f0342","WooCommerce Purple":"#720eec","Jetpack Green 0":"#f0f2eb","Jetpack Green 5":"#d0e6b8","Jetpack Green 10":"#9dd977","Jetpack Green 20":"#64ca43","Jetpack Green 30":"#2fb41f","Jetpack Green 40":"#069e08","Jetpack Green 50":"#008710","Jetpack Green 60":"#007117","Jetpack Green 70":"#005b18","Jetpack Green 80":"#004515","Jetpack Green 90":"#003010","Jetpack Green 100":"#001c09","Jetpack Green":"#069e08"}}')}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var s=t[n]={id:n,loaded:!1,exports:{}};return e[n].call(s.exports,s,s.exports,r),s.loaded=!0,s.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{"use strict";var e=r(723),t=r(7143),n=r(6087),o=r(1609),s=r.n(o),a=r(252),c=r(8228);const i=(0,t.createReduxStore)(c.a,c.i);function l(){const t=document.getElementById("jp-wordads-dashboard");if(null===t)return;const r=s().createElement(e.Ay,null,s().createElement(a.A,null));n.createRoot(t).render(r)}(0,t.register)(i),"loading"!==document.readyState?l():document.addEventListener("DOMContentLoaded",l)})()})();