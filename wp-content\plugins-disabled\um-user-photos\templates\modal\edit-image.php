<?php
/**
 * Template for the UM User Photos. The "Edit Image" modal content
 *
 * Call: UM()->User_Photos()->ajax()->load_edit_photo_modal()
 * Page: "Profile", tab "Photos", modal "Edit Image"
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/modal/edit-image.php
 * @var object $photo
 * @var object $album
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

$disable_comment_option = UM()->options()->get( 'um_user_photos_disable_comments' );
$disable_comment        = get_post_meta( $photo->ID, '_disable_comment', true );
$cover_photo            = get_post_meta( $album->ID, '_thumbnail_id', true );
$disable_cover          = UM()->options()->get( 'um_user_photos_disable_cover' );
?>

<div class="um-form">
	<form method="post" action="" id="um_user_photos_edit_image">

		<div class="um-galley-form-response"></div>

		<div class="um-field">
			<label for="image-title-<?php echo esc_attr( $photo->ID ); ?>"><?php esc_html_e( 'Image title', 'um-user-photos' ); ?></label>
			<input id="image-title-<?php echo esc_attr( $photo->ID ); ?>" type="text" name="title" value="<?php echo esc_attr( $photo->post_title ); ?>" placeholder="<?php esc_attr_e( 'Image title', 'um-user-photos' ); ?>" title="<?php esc_attr_e( 'Image title', 'um-user-photos' ); ?>" required="required" />
		</div>

		<div class="um-field">
			<label for="image-caption-<?php echo esc_attr( $photo->ID ); ?>"><?php esc_html_e( 'Image caption', 'um-user-photos' ); ?></label>
			<textarea id="image-caption-<?php echo esc_attr( $photo->ID ); ?>" name="caption" placeholder="<?php esc_attr_e( 'Image caption', 'um-user-photos' ); ?>" title="<?php esc_attr_e( 'Image caption', 'um-user-photos' ); ?>"><?php echo esc_html( $photo->post_excerpt ); ?></textarea>
		</div>

		<div class="um-field">
			<label for="image-link-<?php echo esc_attr( $photo->ID ); ?>"><?php esc_html_e( 'Related link', 'um-user-photos' ); ?></label>
			<input id="image-link-<?php echo esc_attr( $photo->ID ); ?>" type="text" name="link" value="<?php echo esc_attr( $photo->_link ); ?>" placeholder="<?php esc_attr_e( 'Related link', 'um-user-photos' ); ?>" title="<?php esc_attr_e( 'Related link', 'um-user-photos' ); ?>" />
		</div>

		<?php if ( ! $disable_comment_option ) { ?>
			<div class="um-field">
				<label>
					<input type="checkbox" name="disable_comments" value="1" <?php checked( $disable_comment ); ?> />
					<?php esc_html_e( 'Disable comments', 'um-user-photos' ); ?>
				</label>
			</div>
		<?php } ?>
		<?php if ( ! $disable_cover ) { ?>
		<div class="um-field">
			<label>
				<input type="checkbox" name="cover_photo" value="<?php echo absint( $photo->ID ); ?>" <?php checked( absint( $cover_photo ), absint( $photo->ID ) ); ?> />
				<?php esc_html_e( 'Set as album cover', 'um-user-photos' ); ?>
			</label>
		</div>
		<?php } ?>

		<div class="um-field um-user-photos-modal-footer text-right">
			<button type="button" id="um-user-photos-image-update-btn" class="um-modal-btn"><?php esc_html_e( 'Update', 'um-user-photos' ); ?></button>
			<a href="javascript:void(0);" class="um-modal-btn alt um-user-photos-modal-close-link"><?php esc_html_e( 'Cancel', 'um-user-photos' ); ?></a>
		</div>

		<input type="hidden" name="id" value="<?php echo esc_attr( $photo->ID ); ?>"/>
		<input type="hidden" name="album" value="<?php echo esc_attr( $album->ID ); ?>"/>
		<input type="hidden" name="_wpnonce" value="<?php echo esc_attr( wp_create_nonce( 'um_edit_image' . $photo->ID ) ); ?>"/>
	</form>
</div>
