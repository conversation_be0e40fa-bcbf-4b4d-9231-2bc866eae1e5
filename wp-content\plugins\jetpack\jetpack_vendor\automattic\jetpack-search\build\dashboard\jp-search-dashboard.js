/*! For license information please see jp-search-dashboard.js.LICENSE.txt */
(()=>{var e={9826:(e,t,r)=>{"use strict";r.d(t,{i:()=>a});const n={AED:{symbol:"د.إ.‏",grouping:",",decimal:".",precision:2},AFN:{symbol:"؋",grouping:",",decimal:".",precision:2},ALL:{symbol:"Lek",grouping:".",decimal:",",precision:2},AMD:{symbol:"֏",grouping:",",decimal:".",precision:2},ANG:{symbol:"ƒ",grouping:",",decimal:".",precision:2},AOA:{symbol:"Kz",grouping:",",decimal:".",precision:2},ARS:{symbol:"$",grouping:".",decimal:",",precision:2},AUD:{symbol:"A$",grouping:",",decimal:".",precision:2},AWG:{symbol:"ƒ",grouping:",",decimal:".",precision:2},AZN:{symbol:"₼",grouping:" ",decimal:",",precision:2},BAM:{symbol:"КМ",grouping:".",decimal:",",precision:2},BBD:{symbol:"Bds$",grouping:",",decimal:".",precision:2},BDT:{symbol:"৳",grouping:",",decimal:".",precision:0},BGN:{symbol:"лв.",grouping:" ",decimal:",",precision:2},BHD:{symbol:"د.ب.‏",grouping:",",decimal:".",precision:3},BIF:{symbol:"FBu",grouping:",",decimal:".",precision:0},BMD:{symbol:"$",grouping:",",decimal:".",precision:2},BND:{symbol:"$",grouping:".",decimal:",",precision:0},BOB:{symbol:"Bs",grouping:".",decimal:",",precision:2},BRL:{symbol:"R$",grouping:".",decimal:",",precision:2},BSD:{symbol:"$",grouping:",",decimal:".",precision:2},BTC:{symbol:"Ƀ",grouping:",",decimal:".",precision:2},BTN:{symbol:"Nu.",grouping:",",decimal:".",precision:1},BWP:{symbol:"P",grouping:",",decimal:".",precision:2},BYR:{symbol:"р.",grouping:" ",decimal:",",precision:2},BZD:{symbol:"BZ$",grouping:",",decimal:".",precision:2},CAD:{symbol:"C$",grouping:",",decimal:".",precision:2},CDF:{symbol:"FC",grouping:",",decimal:".",precision:2},CHF:{symbol:"CHF",grouping:"'",decimal:".",precision:2},CLP:{symbol:"$",grouping:".",decimal:",",precision:2},CNY:{symbol:"¥",grouping:",",decimal:".",precision:2},COP:{symbol:"$",grouping:".",decimal:",",precision:2},CRC:{symbol:"₡",grouping:".",decimal:",",precision:2},CUC:{symbol:"CUC",grouping:",",decimal:".",precision:2},CUP:{symbol:"$MN",grouping:",",decimal:".",precision:2},CVE:{symbol:"$",grouping:",",decimal:".",precision:2},CZK:{symbol:"Kč",grouping:" ",decimal:",",precision:2},DJF:{symbol:"Fdj",grouping:",",decimal:".",precision:0},DKK:{symbol:"kr.",grouping:"",decimal:",",precision:2},DOP:{symbol:"RD$",grouping:",",decimal:".",precision:2},DZD:{symbol:"د.ج.‏",grouping:",",decimal:".",precision:2},EGP:{symbol:"ج.م.‏",grouping:",",decimal:".",precision:2},ERN:{symbol:"Nfk",grouping:",",decimal:".",precision:2},ETB:{symbol:"ETB",grouping:",",decimal:".",precision:2},EUR:{symbol:"€",grouping:".",decimal:",",precision:2},FJD:{symbol:"FJ$",grouping:",",decimal:".",precision:2},FKP:{symbol:"£",grouping:",",decimal:".",precision:2},GBP:{symbol:"£",grouping:",",decimal:".",precision:2},GEL:{symbol:"Lari",grouping:" ",decimal:",",precision:2},GHS:{symbol:"₵",grouping:",",decimal:".",precision:2},GIP:{symbol:"£",grouping:",",decimal:".",precision:2},GMD:{symbol:"D",grouping:",",decimal:".",precision:2},GNF:{symbol:"FG",grouping:",",decimal:".",precision:0},GTQ:{symbol:"Q",grouping:",",decimal:".",precision:2},GYD:{symbol:"G$",grouping:",",decimal:".",precision:2},HKD:{symbol:"HK$",grouping:",",decimal:".",precision:2},HNL:{symbol:"L.",grouping:",",decimal:".",precision:2},HRK:{symbol:"kn",grouping:".",decimal:",",precision:2},HTG:{symbol:"G",grouping:",",decimal:".",precision:2},HUF:{symbol:"Ft",grouping:".",decimal:",",precision:0},IDR:{symbol:"Rp",grouping:".",decimal:",",precision:0},ILS:{symbol:"₪",grouping:",",decimal:".",precision:2},INR:{symbol:"₹",grouping:",",decimal:".",precision:2},IQD:{symbol:"د.ع.‏",grouping:",",decimal:".",precision:2},IRR:{symbol:"﷼",grouping:",",decimal:"/",precision:2},ISK:{symbol:"kr.",grouping:".",decimal:",",precision:0},JMD:{symbol:"J$",grouping:",",decimal:".",precision:2},JOD:{symbol:"د.ا.‏",grouping:",",decimal:".",precision:3},JPY:{symbol:"¥",grouping:",",decimal:".",precision:0},KES:{symbol:"S",grouping:",",decimal:".",precision:2},KGS:{symbol:"сом",grouping:" ",decimal:"-",precision:2},KHR:{symbol:"៛",grouping:",",decimal:".",precision:0},KMF:{symbol:"CF",grouping:",",decimal:".",precision:2},KPW:{symbol:"₩",grouping:",",decimal:".",precision:0},KRW:{symbol:"₩",grouping:",",decimal:".",precision:0},KWD:{symbol:"د.ك.‏",grouping:",",decimal:".",precision:3},KYD:{symbol:"$",grouping:",",decimal:".",precision:2},KZT:{symbol:"₸",grouping:" ",decimal:"-",precision:2},LAK:{symbol:"₭",grouping:",",decimal:".",precision:0},LBP:{symbol:"ل.ل.‏",grouping:",",decimal:".",precision:2},LKR:{symbol:"₨",grouping:",",decimal:".",precision:0},LRD:{symbol:"L$",grouping:",",decimal:".",precision:2},LSL:{symbol:"M",grouping:",",decimal:".",precision:2},LYD:{symbol:"د.ل.‏",grouping:",",decimal:".",precision:3},MAD:{symbol:"د.م.‏",grouping:",",decimal:".",precision:2},MDL:{symbol:"lei",grouping:",",decimal:".",precision:2},MGA:{symbol:"Ar",grouping:",",decimal:".",precision:0},MKD:{symbol:"ден.",grouping:".",decimal:",",precision:2},MMK:{symbol:"K",grouping:",",decimal:".",precision:2},MNT:{symbol:"₮",grouping:" ",decimal:",",precision:2},MOP:{symbol:"MOP$",grouping:",",decimal:".",precision:2},MRO:{symbol:"UM",grouping:",",decimal:".",precision:2},MTL:{symbol:"₤",grouping:",",decimal:".",precision:2},MUR:{symbol:"₨",grouping:",",decimal:".",precision:2},MVR:{symbol:"MVR",grouping:",",decimal:".",precision:1},MWK:{symbol:"MK",grouping:",",decimal:".",precision:2},MXN:{symbol:"MX$",grouping:",",decimal:".",precision:2},MYR:{symbol:"RM",grouping:",",decimal:".",precision:2},MZN:{symbol:"MT",grouping:",",decimal:".",precision:0},NAD:{symbol:"N$",grouping:",",decimal:".",precision:2},NGN:{symbol:"₦",grouping:",",decimal:".",precision:2},NIO:{symbol:"C$",grouping:",",decimal:".",precision:2},NOK:{symbol:"kr",grouping:" ",decimal:",",precision:2},NPR:{symbol:"₨",grouping:",",decimal:".",precision:2},NZD:{symbol:"NZ$",grouping:",",decimal:".",precision:2},OMR:{symbol:"﷼",grouping:",",decimal:".",precision:3},PAB:{symbol:"B/.",grouping:",",decimal:".",precision:2},PEN:{symbol:"S/.",grouping:",",decimal:".",precision:2},PGK:{symbol:"K",grouping:",",decimal:".",precision:2},PHP:{symbol:"₱",grouping:",",decimal:".",precision:2},PKR:{symbol:"₨",grouping:",",decimal:".",precision:2},PLN:{symbol:"zł",grouping:" ",decimal:",",precision:2},PYG:{symbol:"₲",grouping:".",decimal:",",precision:2},QAR:{symbol:"﷼",grouping:",",decimal:".",precision:2},RON:{symbol:"lei",grouping:".",decimal:",",precision:2},RSD:{symbol:"Дин.",grouping:".",decimal:",",precision:2},RUB:{symbol:"₽",grouping:" ",decimal:",",precision:2},RWF:{symbol:"RWF",grouping:" ",decimal:",",precision:2},SAR:{symbol:"﷼",grouping:",",decimal:".",precision:2},SBD:{symbol:"S$",grouping:",",decimal:".",precision:2},SCR:{symbol:"₨",grouping:",",decimal:".",precision:2},SDD:{symbol:"LSd",grouping:",",decimal:".",precision:2},SDG:{symbol:"£‏",grouping:",",decimal:".",precision:2},SEK:{symbol:"kr",grouping:",",decimal:".",precision:2},SGD:{symbol:"S$",grouping:",",decimal:".",precision:2},SHP:{symbol:"£",grouping:",",decimal:".",precision:2},SLL:{symbol:"Le",grouping:",",decimal:".",precision:2},SOS:{symbol:"S",grouping:",",decimal:".",precision:2},SRD:{symbol:"$",grouping:",",decimal:".",precision:2},STD:{symbol:"Db",grouping:",",decimal:".",precision:2},SVC:{symbol:"₡",grouping:",",decimal:".",precision:2},SYP:{symbol:"£",grouping:",",decimal:".",precision:2},SZL:{symbol:"E",grouping:",",decimal:".",precision:2},THB:{symbol:"฿",grouping:",",decimal:".",precision:2},TJS:{symbol:"TJS",grouping:" ",decimal:";",precision:2},TMT:{symbol:"m",grouping:" ",decimal:",",precision:0},TND:{symbol:"د.ت.‏",grouping:",",decimal:".",precision:3},TOP:{symbol:"T$",grouping:",",decimal:".",precision:2},TRY:{symbol:"TL",grouping:".",decimal:",",precision:2},TTD:{symbol:"TT$",grouping:",",decimal:".",precision:2},TVD:{symbol:"$T",grouping:",",decimal:".",precision:2},TWD:{symbol:"NT$",grouping:",",decimal:".",precision:2},TZS:{symbol:"TSh",grouping:",",decimal:".",precision:2},UAH:{symbol:"₴",grouping:" ",decimal:",",precision:2},UGX:{symbol:"USh",grouping:",",decimal:".",precision:2},USD:{symbol:"$",grouping:",",decimal:".",precision:2},UYU:{symbol:"$U",grouping:".",decimal:",",precision:2},UZS:{symbol:"сўм",grouping:" ",decimal:",",precision:2},VEB:{symbol:"Bs.",grouping:",",decimal:".",precision:2},VEF:{symbol:"Bs. F.",grouping:".",decimal:",",precision:2},VND:{symbol:"₫",grouping:".",decimal:",",precision:1},VUV:{symbol:"VT",grouping:",",decimal:".",precision:0},WST:{symbol:"WS$",grouping:",",decimal:".",precision:2},XAF:{symbol:"F",grouping:",",decimal:".",precision:2},XCD:{symbol:"$",grouping:",",decimal:".",precision:2},XOF:{symbol:"F",grouping:" ",decimal:",",precision:2},XPF:{symbol:"F",grouping:",",decimal:".",precision:2},YER:{symbol:"﷼",grouping:",",decimal:".",precision:2},ZAR:{symbol:"R",grouping:" ",decimal:",",precision:2},ZMW:{symbol:"ZK",grouping:",",decimal:".",precision:2},WON:{symbol:"₩",grouping:",",decimal:".",precision:2}};function a(e){return n[e]||{symbol:"$",grouping:",",decimal:".",precision:2}}},7397:(e,t,r)=>{"use strict";r.d(t,{vA:()=>s});var n=r(9826),a=r(8506);function s(e,t,r={}){const s=(0,n.i)(t);if(!s||isNaN(e))return null;const{decimal:c,grouping:o,precision:i,symbol:l}={...s,...r},p=e<0?"-":"",u=Math.abs(e),d=Math.floor(u);return{sign:p,symbol:l,integer:(0,a.A)(u,i,c,o).split(c)[0],fraction:i>0?(0,a.A)(u-d,i,c,o).slice(1):""}}},8506:(e,t,r)=>{"use strict";function n(e,t=0,r=".",n=","){const a=(e+"").replace(/[^0-9+\-Ee.]/g,""),s=isFinite(+a)?+a:0,c=isFinite(+t)?Math.abs(t):0,o=(c?function(e,t){const r=Math.pow(10,t);return""+(Math.round(e*r)/r).toFixed(t)}(s,c):""+Math.round(s)).split(".");return o[0].length>3&&(o[0]=o[0].replace(/\B(?=(?:\d{3})+(?!\d))/g,n)),(o[1]||"").length<c&&(o[1]=o[1]||"",o[1]+=new Array(c-o[1].length+1).join("0")),o.join(r)}r.d(t,{A:()=>n})},1113:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(6087);const a=(0,n.forwardRef)((function({icon:e,size:t=24,...r},a){return(0,n.cloneElement)(e,{width:t,height:t,...r,ref:a})}))},1797:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(5573),a=r(790);const s=(0,a.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(n.Path,{d:"m14.5 6.5-1 1 3.7 3.7H4v1.6h13.2l-3.7 3.7 1 1 5.6-5.5z"})})},3883:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(5573),a=r(790);const s=(0,a.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(n.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})})},1249:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(5573),a=r(790);const s=(0,a.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(n.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})})},3512:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(5573),a=r(790);const s=(0,a.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(n.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})})},6941:(e,t,r)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const r="color: "+this.color;t.splice(1,0,r,"color: inherit");let n=0,a=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(n++,"%c"===e&&(a=n))})),t.splice(a,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=r(3212)(t);const{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},3212:(e,t,r)=>{e.exports=function(e){function t(e){let r,a,s,c=null;function o(...e){if(!o.enabled)return;const n=o,a=Number(new Date),s=a-(r||a);n.diff=s,n.prev=r,n.curr=a,r=a,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let c=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((r,a)=>{if("%%"===r)return"%";c++;const s=t.formatters[a];if("function"==typeof s){const t=e[c];r=s.call(n,t),e.splice(c,1),c--}return r})),t.formatArgs.call(n,e);(n.log||t.log).apply(n,e)}return o.namespace=e,o.useColors=t.useColors(),o.color=t.selectColor(e),o.extend=n,o.destroy=t.destroy,Object.defineProperty(o,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==c?c:(a!==t.namespaces&&(a=t.namespaces,s=t.enabled(e)),s),set:e=>{c=e}}),"function"==typeof t.init&&t.init(o),o}function n(e,r){const n=t(this.namespace+(void 0===r?":":r)+e);return n.log=this.log,n}function a(e,t){let r=0,n=0,a=-1,s=0;for(;r<e.length;)if(n<t.length&&(t[n]===e[r]||"*"===t[n]))"*"===t[n]?(a=n,s=r,n++):(r++,n++);else{if(-1===a)return!1;n=a+1,s++,r=s}for(;n<t.length&&"*"===t[n];)n++;return n===t.length}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names,...t.skips.map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const r=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of r)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const r of t.skips)if(a(e,r))return!1;for(const r of t.names)if(a(e,r))return!0;return!1},t.humanize=r(4997),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((r=>{t[r]=e[r]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t),r|=0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}},7792:(e,t,r)=>{var n=r(426)(r(1665),"DataView");e.exports=n},8985:(e,t,r)=>{var n=r(8276),a=r(5986),s=r(7549),c=r(9297),o=r(1033);function i(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}i.prototype.clear=n,i.prototype.delete=a,i.prototype.get=s,i.prototype.has=c,i.prototype.set=o,e.exports=i},8603:(e,t,r)=>{var n=r(4346),a=r(876),s=r(4783),c=r(851),o=r(9643);function i(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}i.prototype.clear=n,i.prototype.delete=a,i.prototype.get=s,i.prototype.has=c,i.prototype.set=o,e.exports=i},3579:(e,t,r)=>{var n=r(426)(r(1665),"Map");e.exports=n},3017:(e,t,r)=>{var n=r(5444),a=r(9634),s=r(3725),c=r(2273),o=r(729);function i(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}i.prototype.clear=n,i.prototype.delete=a,i.prototype.get=s,i.prototype.has=c,i.prototype.set=o,e.exports=i},1232:(e,t,r)=>{var n=r(426)(r(1665),"Promise");e.exports=n},7749:(e,t,r)=>{var n=r(426)(r(1665),"Set");e.exports=n},941:(e,t,r)=>{var n=r(8603),a=r(1696),s=r(4838),c=r(1025),o=r(4205),i=r(7333);function l(e){var t=this.__data__=new n(e);this.size=t.size}l.prototype.clear=a,l.prototype.delete=s,l.prototype.get=c,l.prototype.has=o,l.prototype.set=i,e.exports=l},8693:(e,t,r)=>{var n=r(1665).Symbol;e.exports=n},8216:(e,t,r)=>{var n=r(1665).Uint8Array;e.exports=n},7483:(e,t,r)=>{var n=r(426)(r(1665),"WeakMap");e.exports=n},3605:e=>{e.exports=function(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}},237:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n&&!1!==t(e[r],r,e););return e}},4046:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,a=0,s=[];++r<n;){var c=e[r];t(c,r,e)&&(s[a++]=c)}return s}},1171:(e,t,r)=>{var n=r(6628),a=r(8960),s=r(5413),c=r(52),o=r(101),i=r(2163),l=Object.prototype.hasOwnProperty;e.exports=function(e,t){var r=s(e),p=!r&&a(e),u=!r&&!p&&c(e),d=!r&&!p&&!u&&i(e),m=r||p||u||d,g=m?n(e.length,String):[],h=g.length;for(var f in e)!t&&!l.call(e,f)||m&&("length"==f||u&&("offset"==f||"parent"==f)||d&&("buffer"==f||"byteLength"==f||"byteOffset"==f)||o(f,h))||g.push(f);return g}},600:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,a=Array(n);++r<n;)a[r]=t(e[r],r,e);return a}},8812:e=>{e.exports=function(e,t){for(var r=-1,n=t.length,a=e.length;++r<n;)e[a+r]=t[r];return e}},9431:(e,t,r)=>{var n=r(7772),a=r(2604),s=Object.prototype.hasOwnProperty;e.exports=function(e,t,r){var c=e[t];s.call(e,t)&&a(c,r)&&(void 0!==r||t in e)||n(e,t,r)}},8853:(e,t,r)=>{var n=r(2604);e.exports=function(e,t){for(var r=e.length;r--;)if(n(e[r][0],t))return r;return-1}},3121:(e,t,r)=>{var n=r(3771),a=r(2610);e.exports=function(e,t){return e&&n(t,a(t),e)}},2282:(e,t,r)=>{var n=r(3771),a=r(9701);e.exports=function(e,t){return e&&n(t,a(t),e)}},7772:(e,t,r)=>{var n=r(5255);e.exports=function(e,t,r){"__proto__"==t&&n?n(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}},7563:(e,t,r)=>{var n=r(941),a=r(237),s=r(9431),c=r(3121),o=r(2282),i=r(8158),l=r(5483),p=r(3139),u=r(3472),d=r(7550),m=r(2025),g=r(4713),h=r(3625),f=r(1707),v=r(5541),y=r(5413),b=r(52),k=r(4574),E=r(9169),C=r(4076),j=r(2610),_=r(9701),A="[object Arguments]",w="[object Function]",S="[object Object]",x={};x[A]=x["[object Array]"]=x["[object ArrayBuffer]"]=x["[object DataView]"]=x["[object Boolean]"]=x["[object Date]"]=x["[object Float32Array]"]=x["[object Float64Array]"]=x["[object Int8Array]"]=x["[object Int16Array]"]=x["[object Int32Array]"]=x["[object Map]"]=x["[object Number]"]=x[S]=x["[object RegExp]"]=x["[object Set]"]=x["[object String]"]=x["[object Symbol]"]=x["[object Uint8Array]"]=x["[object Uint8ClampedArray]"]=x["[object Uint16Array]"]=x["[object Uint32Array]"]=!0,x["[object Error]"]=x[w]=x["[object WeakMap]"]=!1,e.exports=function e(t,r,N,P,R,I){var O,M=1&r,T=2&r,D=4&r;if(N&&(O=R?N(t,P,R,I):N(t)),void 0!==O)return O;if(!E(t))return t;var L=y(t);if(L){if(O=h(t),!M)return l(t,O)}else{var z=g(t),F=z==w||"[object GeneratorFunction]"==z;if(b(t))return i(t,M);if(z==S||z==A||F&&!R){if(O=T||F?{}:v(t),!M)return T?u(t,o(O,t)):p(t,c(O,t))}else{if(!x[z])return R?t:{};O=f(t,z,M)}}I||(I=new n);var B=I.get(t);if(B)return B;I.set(t,O),C(t)?t.forEach((function(n){O.add(e(n,r,N,n,t,I))})):k(t)&&t.forEach((function(n,a){O.set(a,e(n,r,N,a,t,I))}));var U=L?void 0:(D?T?m:d:T?_:j)(t);return a(U||t,(function(n,a){U&&(n=t[a=n]),s(O,a,e(n,r,N,a,t,I))})),O}},6764:(e,t,r)=>{var n=r(9169),a=Object.create,s=function(){function e(){}return function(t){if(!n(t))return{};if(a)return a(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}();e.exports=s},5292:(e,t,r)=>{var n=r(8812),a=r(7655);e.exports=function e(t,r,s,c,o){var i=-1,l=t.length;for(s||(s=a),o||(o=[]);++i<l;){var p=t[i];r>0&&s(p)?r>1?e(p,r-1,s,c,o):n(o,p):c||(o[o.length]=p)}return o}},2530:(e,t,r)=>{var n=r(7141),a=r(7489);e.exports=function(e,t){for(var r=0,s=(t=n(t,e)).length;null!=e&&r<s;)e=e[a(t[r++])];return r&&r==s?e:void 0}},2443:(e,t,r)=>{var n=r(8812),a=r(5413);e.exports=function(e,t,r){var s=t(e);return a(e)?s:n(s,r(e))}},740:(e,t,r)=>{var n=r(8693),a=r(9079),s=r(9170),c=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":c&&c in Object(e)?a(e):s(e)}},4497:e=>{e.exports=function(e,t){return null!=e&&t in Object(e)}},1210:(e,t,r)=>{var n=r(740),a=r(1726);e.exports=function(e){return a(e)&&"[object Arguments]"==n(e)}},8856:(e,t,r)=>{var n=r(4713),a=r(1726);e.exports=function(e){return a(e)&&"[object Map]"==n(e)}},431:(e,t,r)=>{var n=r(4406),a=r(9924),s=r(9169),c=r(8709),o=/^\[object .+?Constructor\]$/,i=Function.prototype,l=Object.prototype,p=i.toString,u=l.hasOwnProperty,d=RegExp("^"+p.call(u).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!s(e)||a(e))&&(n(e)?d:o).test(c(e))}},6514:(e,t,r)=>{var n=r(4713),a=r(1726);e.exports=function(e){return a(e)&&"[object Set]"==n(e)}},4897:(e,t,r)=>{var n=r(740),a=r(8762),s=r(1726),c={};c["[object Float32Array]"]=c["[object Float64Array]"]=c["[object Int8Array]"]=c["[object Int16Array]"]=c["[object Int32Array]"]=c["[object Uint8Array]"]=c["[object Uint8ClampedArray]"]=c["[object Uint16Array]"]=c["[object Uint32Array]"]=!0,c["[object Arguments]"]=c["[object Array]"]=c["[object ArrayBuffer]"]=c["[object Boolean]"]=c["[object DataView]"]=c["[object Date]"]=c["[object Error]"]=c["[object Function]"]=c["[object Map]"]=c["[object Number]"]=c["[object Object]"]=c["[object RegExp]"]=c["[object Set]"]=c["[object String]"]=c["[object WeakMap]"]=!1,e.exports=function(e){return s(e)&&a(e.length)&&!!c[n(e)]}},5860:(e,t,r)=>{var n=r(5043),a=r(1958),s=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return a(e);var t=[];for(var r in Object(e))s.call(e,r)&&"constructor"!=r&&t.push(r);return t}},5259:(e,t,r)=>{var n=r(9169),a=r(5043),s=r(7249),c=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return s(e);var t=a(e),r=[];for(var o in e)("constructor"!=o||!t&&c.call(e,o))&&r.push(o);return r}},7378:(e,t,r)=>{var n=r(5312),a=r(2051);e.exports=function(e,t){return n(e,t,(function(t,r){return a(e,r)}))}},5312:(e,t,r)=>{var n=r(2530),a=r(1502),s=r(7141);e.exports=function(e,t,r){for(var c=-1,o=t.length,i={};++c<o;){var l=t[c],p=n(e,l);r(p,l)&&a(i,s(l,e),p)}return i}},9514:(e,t,r)=>{var n=r(3428),a=r(6905),s=r(5261);e.exports=function(e,t){return s(a(e,t,n),e+"")}},1502:(e,t,r)=>{var n=r(9431),a=r(7141),s=r(101),c=r(9169),o=r(7489);e.exports=function(e,t,r,i){if(!c(e))return e;for(var l=-1,p=(t=a(t,e)).length,u=p-1,d=e;null!=d&&++l<p;){var m=o(t[l]),g=r;if("__proto__"===m||"constructor"===m||"prototype"===m)return e;if(l!=u){var h=d[m];void 0===(g=i?i(h,m,d):void 0)&&(g=c(h)?h:s(t[l+1])?[]:{})}n(d,m,g),d=d[m]}return e}},9334:(e,t,r)=>{var n=r(9274),a=r(5255),s=r(3428),c=a?function(e,t){return a(e,"toString",{configurable:!0,enumerable:!1,value:n(t),writable:!0})}:s;e.exports=c},4580:e=>{e.exports=function(e,t,r){var n=-1,a=e.length;t<0&&(t=-t>a?0:a+t),(r=r>a?a:r)<0&&(r+=a),a=t>r?0:r-t>>>0,t>>>=0;for(var s=Array(a);++n<a;)s[n]=e[n+t];return s}},6628:e=>{e.exports=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}},1856:(e,t,r)=>{var n=r(8693),a=r(600),s=r(5413),c=r(7614),o=n?n.prototype:void 0,i=o?o.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(s(t))return a(t,e)+"";if(c(t))return i?i.call(t):"";var r=t+"";return"0"==r&&1/t==-1/0?"-0":r}},8321:e=>{e.exports=function(e){return function(t){return e(t)}}},2439:(e,t,r)=>{var n=r(7141),a=r(6214),s=r(2109),c=r(7489);e.exports=function(e,t){return t=n(t,e),null==(e=s(e,t))||delete e[c(a(t))]}},7141:(e,t,r)=>{var n=r(5413),a=r(6022),s=r(3894),c=r(6938);e.exports=function(e,t){return n(e)?e:a(e,t)?[e]:s(c(e))}},529:(e,t,r)=>{var n=r(8216);e.exports=function(e){var t=new e.constructor(e.byteLength);return new n(t).set(new n(e)),t}},8158:(e,t,r)=>{e=r.nmd(e);var n=r(1665),a=t&&!t.nodeType&&t,s=a&&e&&!e.nodeType&&e,c=s&&s.exports===a?n.Buffer:void 0,o=c?c.allocUnsafe:void 0;e.exports=function(e,t){if(t)return e.slice();var r=e.length,n=o?o(r):new e.constructor(r);return e.copy(n),n}},8533:(e,t,r)=>{var n=r(529);e.exports=function(e,t){var r=t?n(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.byteLength)}},4029:e=>{var t=/\w*$/;e.exports=function(e){var r=new e.constructor(e.source,t.exec(e));return r.lastIndex=e.lastIndex,r}},4612:(e,t,r)=>{var n=r(8693),a=n?n.prototype:void 0,s=a?a.valueOf:void 0;e.exports=function(e){return s?Object(s.call(e)):{}}},7317:(e,t,r)=>{var n=r(529);e.exports=function(e,t){var r=t?n(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}},5483:e=>{e.exports=function(e,t){var r=-1,n=e.length;for(t||(t=Array(n));++r<n;)t[r]=e[r];return t}},3771:(e,t,r)=>{var n=r(9431),a=r(7772);e.exports=function(e,t,r,s){var c=!r;r||(r={});for(var o=-1,i=t.length;++o<i;){var l=t[o],p=s?s(r[l],e[l],l,r,e):void 0;void 0===p&&(p=e[l]),c?a(r,l,p):n(r,l,p)}return r}},3139:(e,t,r)=>{var n=r(3771),a=r(5332);e.exports=function(e,t){return n(e,a(e),t)}},3472:(e,t,r)=>{var n=r(3771),a=r(3899);e.exports=function(e,t){return n(e,a(e),t)}},1893:(e,t,r)=>{var n=r(1665)["__core-js_shared__"];e.exports=n},6579:(e,t,r)=>{var n=r(9514),a=r(84);e.exports=function(e){return n((function(t,r){var n=-1,s=r.length,c=s>1?r[s-1]:void 0,o=s>2?r[2]:void 0;for(c=e.length>3&&"function"==typeof c?(s--,c):void 0,o&&a(r[0],r[1],o)&&(c=s<3?void 0:c,s=1),t=Object(t);++n<s;){var i=r[n];i&&e(t,i,n,c)}return t}))}},9214:(e,t,r)=>{var n=r(3663);e.exports=function(e){return n(e)?void 0:e}},5255:(e,t,r)=>{var n=r(426),a=function(){try{var e=n(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();e.exports=a},3092:(e,t,r)=>{var n=r(8502),a=r(6905),s=r(5261);e.exports=function(e){return s(a(e,void 0,n),e+"")}},9324:e=>{var t="object"==typeof window&&window&&window.Object===Object&&window;e.exports=t},7550:(e,t,r)=>{var n=r(2443),a=r(5332),s=r(2610);e.exports=function(e){return n(e,s,a)}},2025:(e,t,r)=>{var n=r(2443),a=r(3899),s=r(9701);e.exports=function(e){return n(e,s,a)}},1639:(e,t,r)=>{var n=r(2950);e.exports=function(e,t){var r=e.__data__;return n(t)?r["string"==typeof t?"string":"hash"]:r.map}},426:(e,t,r)=>{var n=r(431),a=r(3404);e.exports=function(e,t){var r=a(e,t);return n(r)?r:void 0}},603:(e,t,r)=>{var n=r(1427)(Object.getPrototypeOf,Object);e.exports=n},9079:(e,t,r)=>{var n=r(8693),a=Object.prototype,s=a.hasOwnProperty,c=a.toString,o=n?n.toStringTag:void 0;e.exports=function(e){var t=s.call(e,o),r=e[o];try{e[o]=void 0;var n=!0}catch(e){}var a=c.call(e);return n&&(t?e[o]=r:delete e[o]),a}},5332:(e,t,r)=>{var n=r(4046),a=r(565),s=Object.prototype.propertyIsEnumerable,c=Object.getOwnPropertySymbols,o=c?function(e){return null==e?[]:(e=Object(e),n(c(e),(function(t){return s.call(e,t)})))}:a;e.exports=o},3899:(e,t,r)=>{var n=r(8812),a=r(603),s=r(5332),c=r(565),o=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)n(t,s(e)),e=a(e);return t}:c;e.exports=o},4713:(e,t,r)=>{var n=r(7792),a=r(3579),s=r(1232),c=r(7749),o=r(7483),i=r(740),l=r(8709),p="[object Map]",u="[object Promise]",d="[object Set]",m="[object WeakMap]",g="[object DataView]",h=l(n),f=l(a),v=l(s),y=l(c),b=l(o),k=i;(n&&k(new n(new ArrayBuffer(1)))!=g||a&&k(new a)!=p||s&&k(s.resolve())!=u||c&&k(new c)!=d||o&&k(new o)!=m)&&(k=function(e){var t=i(e),r="[object Object]"==t?e.constructor:void 0,n=r?l(r):"";if(n)switch(n){case h:return g;case f:return p;case v:return u;case y:return d;case b:return m}return t}),e.exports=k},3404:e=>{e.exports=function(e,t){return null==e?void 0:e[t]}},138:(e,t,r)=>{var n=r(7141),a=r(8960),s=r(5413),c=r(101),o=r(8762),i=r(7489);e.exports=function(e,t,r){for(var l=-1,p=(t=n(t,e)).length,u=!1;++l<p;){var d=i(t[l]);if(!(u=null!=e&&r(e,d)))break;e=e[d]}return u||++l!=p?u:!!(p=null==e?0:e.length)&&o(p)&&c(d,p)&&(s(e)||a(e))}},8276:(e,t,r)=>{var n=r(6310);e.exports=function(){this.__data__=n?n(null):{},this.size=0}},5986:e=>{e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},7549:(e,t,r)=>{var n=r(6310),a=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(n){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return a.call(t,e)?t[e]:void 0}},9297:(e,t,r)=>{var n=r(6310),a=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return n?void 0!==t[e]:a.call(t,e)}},1033:(e,t,r)=>{var n=r(6310);e.exports=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=n&&void 0===t?"__lodash_hash_undefined__":t,this}},3625:e=>{var t=Object.prototype.hasOwnProperty;e.exports=function(e){var r=e.length,n=new e.constructor(r);return r&&"string"==typeof e[0]&&t.call(e,"index")&&(n.index=e.index,n.input=e.input),n}},1707:(e,t,r)=>{var n=r(529),a=r(8533),s=r(4029),c=r(4612),o=r(7317);e.exports=function(e,t,r){var i=e.constructor;switch(t){case"[object ArrayBuffer]":return n(e);case"[object Boolean]":case"[object Date]":return new i(+e);case"[object DataView]":return a(e,r);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return o(e,r);case"[object Map]":case"[object Set]":return new i;case"[object Number]":case"[object String]":return new i(e);case"[object RegExp]":return s(e);case"[object Symbol]":return c(e)}}},5541:(e,t,r)=>{var n=r(6764),a=r(603),s=r(5043);e.exports=function(e){return"function"!=typeof e.constructor||s(e)?{}:n(a(e))}},7655:(e,t,r)=>{var n=r(8693),a=r(8960),s=r(5413),c=n?n.isConcatSpreadable:void 0;e.exports=function(e){return s(e)||a(e)||!!(c&&e&&e[c])}},101:e=>{var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,r){var n=typeof e;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&t.test(e))&&e>-1&&e%1==0&&e<r}},84:(e,t,r)=>{var n=r(2604),a=r(3266),s=r(101),c=r(9169);e.exports=function(e,t,r){if(!c(r))return!1;var o=typeof t;return!!("number"==o?a(r)&&s(t,r.length):"string"==o&&t in r)&&n(r[t],e)}},6022:(e,t,r)=>{var n=r(5413),a=r(7614),s=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,c=/^\w*$/;e.exports=function(e,t){if(n(e))return!1;var r=typeof e;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=e&&!a(e))||(c.test(e)||!s.test(e)||null!=t&&e in Object(t))}},2950:e=>{e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},9924:(e,t,r)=>{var n,a=r(1893),s=(n=/[^.]+$/.exec(a&&a.keys&&a.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"";e.exports=function(e){return!!s&&s in e}},5043:e=>{var t=Object.prototype;e.exports=function(e){var r=e&&e.constructor;return e===("function"==typeof r&&r.prototype||t)}},4346:e=>{e.exports=function(){this.__data__=[],this.size=0}},876:(e,t,r)=>{var n=r(8853),a=Array.prototype.splice;e.exports=function(e){var t=this.__data__,r=n(t,e);return!(r<0)&&(r==t.length-1?t.pop():a.call(t,r,1),--this.size,!0)}},4783:(e,t,r)=>{var n=r(8853);e.exports=function(e){var t=this.__data__,r=n(t,e);return r<0?void 0:t[r][1]}},851:(e,t,r)=>{var n=r(8853);e.exports=function(e){return n(this.__data__,e)>-1}},9643:(e,t,r)=>{var n=r(8853);e.exports=function(e,t){var r=this.__data__,a=n(r,e);return a<0?(++this.size,r.push([e,t])):r[a][1]=t,this}},5444:(e,t,r)=>{var n=r(8985),a=r(8603),s=r(3579);e.exports=function(){this.size=0,this.__data__={hash:new n,map:new(s||a),string:new n}}},9634:(e,t,r)=>{var n=r(1639);e.exports=function(e){var t=n(this,e).delete(e);return this.size-=t?1:0,t}},3725:(e,t,r)=>{var n=r(1639);e.exports=function(e){return n(this,e).get(e)}},2273:(e,t,r)=>{var n=r(1639);e.exports=function(e){return n(this,e).has(e)}},729:(e,t,r)=>{var n=r(1639);e.exports=function(e,t){var r=n(this,e),a=r.size;return r.set(e,t),this.size+=r.size==a?0:1,this}},9580:(e,t,r)=>{var n=r(9284);e.exports=function(e){var t=n(e,(function(e){return 500===r.size&&r.clear(),e})),r=t.cache;return t}},6310:(e,t,r)=>{var n=r(426)(Object,"create");e.exports=n},1958:(e,t,r)=>{var n=r(1427)(Object.keys,Object);e.exports=n},7249:e=>{e.exports=function(e){var t=[];if(null!=e)for(var r in Object(e))t.push(r);return t}},1629:(e,t,r)=>{e=r.nmd(e);var n=r(9324),a=t&&!t.nodeType&&t,s=a&&e&&!e.nodeType&&e,c=s&&s.exports===a&&n.process,o=function(){try{var e=s&&s.require&&s.require("util").types;return e||c&&c.binding&&c.binding("util")}catch(e){}}();e.exports=o},9170:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},1427:e=>{e.exports=function(e,t){return function(r){return e(t(r))}}},6905:(e,t,r)=>{var n=r(3605),a=Math.max;e.exports=function(e,t,r){return t=a(void 0===t?e.length-1:t,0),function(){for(var s=arguments,c=-1,o=a(s.length-t,0),i=Array(o);++c<o;)i[c]=s[t+c];c=-1;for(var l=Array(t+1);++c<t;)l[c]=s[c];return l[t]=r(i),n(e,this,l)}}},2109:(e,t,r)=>{var n=r(2530),a=r(4580);e.exports=function(e,t){return t.length<2?e:n(e,a(t,0,-1))}},1665:(e,t,r)=>{var n=r(9324),a="object"==typeof self&&self&&self.Object===Object&&self,s=n||a||Function("return this")();e.exports=s},5261:(e,t,r)=>{var n=r(9334),a=r(8935)(n);e.exports=a},8935:e=>{var t=Date.now;e.exports=function(e){var r=0,n=0;return function(){var a=t(),s=16-(a-n);if(n=a,s>0){if(++r>=800)return arguments[0]}else r=0;return e.apply(void 0,arguments)}}},1696:(e,t,r)=>{var n=r(8603);e.exports=function(){this.__data__=new n,this.size=0}},4838:e=>{e.exports=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}},1025:e=>{e.exports=function(e){return this.__data__.get(e)}},4205:e=>{e.exports=function(e){return this.__data__.has(e)}},7333:(e,t,r)=>{var n=r(8603),a=r(3579),s=r(3017);e.exports=function(e,t){var r=this.__data__;if(r instanceof n){var c=r.__data__;if(!a||c.length<199)return c.push([e,t]),this.size=++r.size,this;r=this.__data__=new s(c)}return r.set(e,t),this.size=r.size,this}},3894:(e,t,r)=>{var n=r(9580),a=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,s=/\\(\\)?/g,c=n((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(a,(function(e,r,n,a){t.push(n?a.replace(s,"$1"):r||e)})),t}));e.exports=c},7489:(e,t,r)=>{var n=r(7614);e.exports=function(e){if("string"==typeof e||n(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}},8709:e=>{var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},9367:(e,t,r)=>{var n=r(9431),a=r(3771),s=r(6579),c=r(3266),o=r(5043),i=r(2610),l=Object.prototype.hasOwnProperty,p=s((function(e,t){if(o(t)||c(t))a(t,i(t),e);else for(var r in t)l.call(t,r)&&n(e,r,t[r])}));e.exports=p},9274:e=>{e.exports=function(e){return function(){return e}}},2604:e=>{e.exports=function(e,t){return e===t||e!=e&&t!=t}},8502:(e,t,r)=>{var n=r(5292);e.exports=function(e){return(null==e?0:e.length)?n(e,1):[]}},2051:(e,t,r)=>{var n=r(4497),a=r(138);e.exports=function(e,t){return null!=e&&a(e,t,n)}},3428:e=>{e.exports=function(e){return e}},8960:(e,t,r)=>{var n=r(1210),a=r(1726),s=Object.prototype,c=s.hasOwnProperty,o=s.propertyIsEnumerable,i=n(function(){return arguments}())?n:function(e){return a(e)&&c.call(e,"callee")&&!o.call(e,"callee")};e.exports=i},5413:e=>{var t=Array.isArray;e.exports=t},3266:(e,t,r)=>{var n=r(4406),a=r(8762);e.exports=function(e){return null!=e&&a(e.length)&&!n(e)}},52:(e,t,r)=>{e=r.nmd(e);var n=r(1665),a=r(4267),s=t&&!t.nodeType&&t,c=s&&e&&!e.nodeType&&e,o=c&&c.exports===s?n.Buffer:void 0,i=(o?o.isBuffer:void 0)||a;e.exports=i},4406:(e,t,r)=>{var n=r(740),a=r(9169);e.exports=function(e){if(!a(e))return!1;var t=n(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},8762:e=>{e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},4574:(e,t,r)=>{var n=r(8856),a=r(8321),s=r(1629),c=s&&s.isMap,o=c?a(c):n;e.exports=o},9169:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},1726:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},3663:(e,t,r)=>{var n=r(740),a=r(603),s=r(1726),c=Function.prototype,o=Object.prototype,i=c.toString,l=o.hasOwnProperty,p=i.call(Object);e.exports=function(e){if(!s(e)||"[object Object]"!=n(e))return!1;var t=a(e);if(null===t)return!0;var r=l.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&i.call(r)==p}},4076:(e,t,r)=>{var n=r(6514),a=r(8321),s=r(1629),c=s&&s.isSet,o=c?a(c):n;e.exports=o},7614:(e,t,r)=>{var n=r(740),a=r(1726);e.exports=function(e){return"symbol"==typeof e||a(e)&&"[object Symbol]"==n(e)}},2163:(e,t,r)=>{var n=r(4897),a=r(8321),s=r(1629),c=s&&s.isTypedArray,o=c?a(c):n;e.exports=o},2610:(e,t,r)=>{var n=r(1171),a=r(5860),s=r(3266);e.exports=function(e){return s(e)?n(e):a(e)}},9701:(e,t,r)=>{var n=r(1171),a=r(5259),s=r(3266);e.exports=function(e){return s(e)?n(e,!0):a(e)}},6214:e=>{e.exports=function(e){var t=null==e?0:e.length;return t?e[t-1]:void 0}},9284:(e,t,r)=>{var n=r(3017);function a(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var r=function(){var n=arguments,a=t?t.apply(this,n):n[0],s=r.cache;if(s.has(a))return s.get(a);var c=e.apply(this,n);return r.cache=s.set(a,c)||s,c};return r.cache=new(a.Cache||n),r}a.Cache=n,e.exports=a},6418:e=>{e.exports=function(){}},2903:(e,t,r)=>{var n=r(600),a=r(7563),s=r(2439),c=r(7141),o=r(3771),i=r(9214),l=r(3092),p=r(2025),u=l((function(e,t){var r={};if(null==e)return r;var l=!1;t=n(t,(function(t){return t=c(t,e),l||(l=t.length>1),t})),o(e,p(e),r),l&&(r=a(r,7,i));for(var u=t.length;u--;)s(r,t[u]);return r}));e.exports=u},8795:(e,t,r)=>{var n=r(7378),a=r(3092)((function(e,t){return null==e?{}:n(e,t)}));e.exports=a},565:e=>{e.exports=function(){return[]}},4267:e=>{e.exports=function(){return!1}},6938:(e,t,r)=>{var n=r(1856);e.exports=function(e){return null==e?"":n(e)}},4436:(e,t,r)=>{var n=r(6938),a=0;e.exports=function(e){var t=++a;return n(e)+t}},2021:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={"admin-page":"sexr0jUxC1jVixdKiDnC",background:"vKQ11sLeAM45M04P1ccj","admin-page-header":"iWGAhN9gOB48g0jEO1OQ","sandbox-domain-badge":"JOYmuxQjG4FArIIUxJfA"}},6888:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={"section-hero":"vMa4i_Dza2t5Zi_Bw9Nf"}},2258:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={button:"zI5tJ_qhWE6Oe6Lk75GY","is-icon-button":"tuBt2DLqimiImoqVzPqo",small:"Na39I683LAaSA99REg14",normal:"ipS7tKy9GntCS4R3vekF",icon:"paGLQwtPEaJmtArCcmyK",regular:"lZAo6_oGfclXOO9CC6Rd","full-width":"xJDOiJxTt0R_wSl8Ipz_",loading:"q_tVWqMjl39RcY6WtQA6","external-icon":"CDuBjJp_8jxzx5j6Nept"}},2127:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={cut:"msOlyh2T7D6uhbM6AROg",icon:"cPN7USVqSBpxUswfDtUZ",cta:"EmnJAyEzzn1QpA8HtypY",iconContainer:"vV7YZikAz0oHYsuvtxMq",description:"T1YaMupeZmBIpXZHY9EZ"}},9768:()=>{},3689:()=>{},4803:()=>{},6445:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={"indeterminate-progress-bar":"lGBcNzDlFSVmny5xh3AH",indeterminate_progress_bar__animation:"Otu8DduoZIRmZk0FB77u"}},4206:()=>{},8403:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={sm:"(max-width: 599px)",md:"(min-width: 600px) and (max-width: 959px)",lg:"(min-width: 960px)"}},7371:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={sm:"(max-width: 599px)",md:"(min-width: 600px) and (max-width: 959px)",lg:"(min-width: 960px)",smCols:"4",mdCols:"8",lgCols:"12","col-sm-1":"RuVLl3q4lxTQa3wbhBJB","col-sm-1-start":"f9LZTRG4MMK42rS89afW","col-sm-1-end":"bHe_zKxjjpUwHw_MdYE1","col-sm-2":"QZbNrOqE2aNSn50xVhpU","col-sm-2-start":"ev7W3z7zVYPeHAlYqZjf","col-sm-2-end":"NJWd1m_e7lOiPYru2ZMP","col-sm-3":"Xc6nt1Qc1DI0Z2A3gt1r","col-sm-3-start":"UIcN_GXiPRoIsin8Kohg","col-sm-3-end":"GRKCyqb5LufCSCgykKFc","col-sm-4":"i_qTq8gqhhC3vIUepVRB","col-sm-4-start":"G3qaZ3Jpbvam_1XvGxgc","col-sm-4-end":"VRCNYKZtO9zukEwmgP1y","col-md-1":"tRm008K_WJL79WoNZTNL","col-md-1-start":"l5T2P_bgKts4tdaRkS1d","col-md-1-end":"zOCxfLZpF6BlgC7a_Yq1","col-md-2":"F80DdgVn0m5OpvtSQWka","col-md-2-start":"oI1c7JYfiJtMQHbhngtU","col-md-2-end":"pMQtA_4jh1_1lVknqEP5","col-md-3":"VenqMpdgyKQVUNNQcfqd","col-md-3-start":"seNYL99uoczf9V4MxBxT","col-md-3-end":"YKfF1HFhI9KygA5l3b2J","col-md-4":"yAi0Cv1xDWkoqsaUhvhR","col-md-4-start":"ubhnyZOnkgxNhh6XtVWv","col-md-4-end":"RGOPGQbWMJ9Ei5oFxS7X","col-md-5":"Sz1E2aWbX483ijdi6yge","col-md-5-start":"tku6_bRYrX9tMbgYGmIl","col-md-5-end":"b5JHttOhSEcI1WBlqAjk","col-md-6":"FboSx5MoKTAWbxXyYlCw","col-md-6-start":"Jhs8yEEmodG30edbJvag","col-md-6-end":"IpzbbKVqEqPcfIGkXkwt","col-md-7":"mhCPwfAZ4Kmm_empzJAq","col-md-7-start":"x034ilrJF7rO9UJB2rI1","col-md-7-end":"Wt8t2e16viRrOJ1lLA5v","col-md-8":"S6pIrEy9AMLKx9bgh_Ae","col-md-8-start":"kEfI4tGyuWfHTlRnvIab","col-md-8-end":"PUzX4RRsKq1dnsz3gebS","col-lg-1":"X_pdcLJikd8LS_YAdJlB","col-lg-1-start":"tl936d14Huby4khYp05X","col-lg-1-end":"hnge0LnR69d3NXEtEE1t","col-lg-2":"fj0NUMuyZQcPNgKcjp5Z","col-lg-2-start":"R2ncBX7a2NigdYCcV1OX","col-lg-2-end":"t8vMSDVYno9k9itRwnXb","col-lg-3":"wsDuEN2GqHx6qzo8dUdk","col-lg-3-start":"cIEVPUweWtLBy3xaXnMx","col-lg-3-end":"fajUWBwu1m2B479j3jmz","col-lg-4":"YR0c7fQTgMkDdWzwSyLp","col-lg-4-start":"xlwp8BmplxkKNMI7gamo","col-lg-4-end":"_C4O1w9DUqx1m3gPf8aA","col-lg-5":"Z54F1hAErckAIrKlxnXW","col-lg-5-start":"ezSDWkRHmKSxDJXxuiOH","col-lg-5-end":"T0ChoeAjGJjkkNrYhD4g","col-lg-6":"qtMoMPF6yHvGJnWHSsde","col-lg-6-start":"gdoywN5VPiWERfIBqkph","col-lg-6-end":"wUev_VH5uf_pwFFlbnAU","col-lg-7":"egIPDFJsOpownTClq9XP","col-lg-7-start":"yGhp9yoAW7k0kQik9AB7","col-lg-7-end":"SJ43U9mR5wUg5V2qBeQA","col-lg-8":"cTuyHfMwSUJxN_HdIEgd","col-lg-8-start":"smCr8DaIagcumdvdldiK","col-lg-8-end":"T03NHzQJvzwL6wAfIiTL","col-lg-9":"pMvxM3RJGjqyNdf9qg1Y","col-lg-9-start":"iIVpNRwEnQ_JI5gpp9EN","col-lg-9-end":"ZbQ4u4vGSX5rJOje4uGL","col-lg-10":"gKb5wuIDAlKGbrjK2vxy","col-lg-10-start":"Z7pINdImE2WJiYnZBTqm","col-lg-10-end":"ZTxp6qpvwurMdOnLLSz1","col-lg-11":"NnQTlbfnxPDR6cQ7rygg","col-lg-11-start":"O137wZd6Yl0olSA9PsXR","col-lg-11-end":"zf2OJtQ2MPz6SDoh6CB0","col-lg-12":"U3H6UHW6HqRt9hdzVg3O","col-lg-12-start":"zynnNeS_ZBTxABcVpUQH","col-lg-12-end":"vI8tltFZtFUNAy9Iag9s"}},2420:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={sm:"(max-width: 599px)",md:"(min-width: 600px) and (max-width: 959px)",lg:"(min-width: 960px)",container:"SqdhUZkXCRuIpErj1B3z",fluid:"OZC_9a1LhpWF9dv15Gdh"}},6406:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={placeholder:"NisihrgiIKl_knpYJtfg",pulse:"R2i0K45dEF157drbVRPI"}},4319:()=>{},3142:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={container:"p4qz2tkq0p9hxucJ6Qk2",table:"lbNDyXioOwvyvbALtCBm","is-viewport-large":"s2Lsn4kbm6BrS3DSndRB",card:"cLaNK_XcbTGlRQ4Tp43Q","is-primary":"CYt1X0eH1icRjhtJ28jx",header:"DAkZc1P9A3K12fjEliMg",item:"WUBuYABl8nymjs9NnCEL","last-feature":"ANtCFeb41NhA8PA3H7ZN",value:"Ql2gy_148yW8Vw5vhaKD",icon:"EAQrAnQEW1z1BfdY5gbC","icon-check":"JDSTlLoOC_4aUoH2oNM2","icon-cross":"zNdQRJ1w7BvaQOYyqzHK",popover:"lr7vbX95SKtoe7DarJcZ","popover-icon":"KRrGp2xdkeBOxLZeuQ6X",tos:"H_ZJiRVJg0LiMXPGOcmt","tos-container":"x21z_DixObRDsDaWotP1"}},3407:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={container:"dovianZYLKhnbnh9I06o","price-container":"lljtQMhW7lq5tE5SDJEf","promo-label":"NubApIV1vQCRUNprfm6b",price:"dhFQXpZfMwVI8vuYHnwC","is-not-off-price":"eD7hzxFmdtG_MgmBtl_k",footer:"C64ZjjUAqJC1T2Sa7apS",legend:"UpZDGew6Ay1hPoP6eI7b",symbol:"TDiiPbuW1Z0_05u_pvcK"}},8249:()=>{},8325:()=>{},7253:()=>{},4495:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={reset:"WQVtrU6q0L1Igcj7wCrQ","headline-medium":"UujoBFTnQNY2cWU2SIsH","headline-small":"TeGO5V_thHw5lDAm1_2M","headline-small-regular":"WolQzb2MsSgiNmLtc7_j","title-medium":"hUB0JT8p1T2Hw28N6qC8","title-medium-semi-bold":"gKZWDv5chz3_O3Syp74H","title-small":"zY2No8Ga4b8shbOQGhnv",body:"tIj0D1t8Cc892ikmgFPZ","body-small":"KdcN0BnOaVeVhyLRKqhS","body-extra-small":"dso3Rh3tl3Xv1GumBktz","body-extra-small-bold":"mQ1UlbN9u4Mg9byO8m7v",label:"PItlW5vRExLnTj4a8eLE","m-0":"TwRpPlktzxhmFVeua7P5","mx-0":"zVfqx7gyb3o9mxfGynn1","my-0":"iSHVzNiB9iVleGljaQxy","mt-0":"xqDIp6cNVr_E6RXaiPyD","mr-0":"S8EwaXk1kyPizt6x4WH2","mb-0":"ODX5Vr1TARoLFkDDFooD","ml-0":"cphJ8dCpfimnky7P2FHg","m-1":"PFgIhNxIyiSuNvQjAIYj","mx-1":"M2jKmUzDxvJjjVEPU3zn","my-1":"io15gAh8tMTNbSEfwJKk","mt-1":"rcTN5uw9xIEeMEGL3Xi_","mr-1":"CQSkybjq2TcRM1Xo9COV","mb-1":"hfqOWgq6_MEGdFE82eOY","ml-1":"I8MxZQYTbuu595yfesWA","m-2":"kQkc6rmdpvLKPkyoJtVQ","mx-2":"j6vFPxWuu4Jan2ldoxpp","my-2":"hqr39dC4H_AbactPAkCG","mt-2":"c3dQnMi16C6J6Ecy4283","mr-2":"YNZmHOuRo6hU7zzKfPdP","mb-2":"Db8lbak1_wunpPk8NwKU","ml-2":"ftsYE5J9hLzquQ0tA5dY","m-3":"Det4MHzLUW7EeDnafPzq","mx-3":"h_8EEAztC29Vve1datb5","my-3":"YXIXJ0h1k47u6hzK8KcM","mt-3":"soADBBkcIKCBXzCTuV9_","mr-3":"zSX59ziEaEWGjnpZa4uV","mb-3":"yrVTnq_WBMbejg89c2ZQ","ml-3":"UKtHPJnI2cXBWtPDm5hM","m-4":"guexok_Tqd5Tf52hRlbT","mx-4":"oS1E2KfTBZkJ3F0tN7T6","my-4":"DN1OhhXi6AoBgEdDSbGd","mt-4":"ot2kkMcYHv53hLZ4LSn0","mr-4":"A1krOZZhlQ6Sp8Cy4bly","mb-4":"pkDbXXXL32237M0hokEh","ml-4":"XXv4kDTGvEnQeuGKOPU3","m-5":"yGqHk1a57gaISwkXwXe6","mx-5":"X8cghM358X3DkXLc9aNK","my-5":"GdfSmGwHlFnN2S6xBn1f","mt-5":"yqeuzwyGQ7zG0avrGqi_","mr-5":"g9emeCkuHvYhveiJbfXO","mb-5":"Lvk3dqcyHbZ07QCRlrUQ","ml-5":"r3yQECDQ9qX0XZzXlVAg","m-6":"aQhlPwht2Cz1X_63Miw0","mx-6":"JyHb0vK3wJgpblL9s5j8","my-6":"cY2gULL1lAv6WPNIRuf3","mt-6":"NBWQ9Lwhh_fnry3lg_p7","mr-6":"yIOniNe5E40C8fWvBm5V","mb-6":"t30usboNSyqfQWIwHvT3","ml-6":"Nm_TyFkYCMhOoghoToKJ","m-7":"C4qJKoBXpgKtpmrqtEKB","mx-7":"S93Srbu6NQ_PBr7DmTiD","my-7":"fJj8k6gGJDks3crUZxOS","mt-7":"cW6D6djs7Ppm7fD7TeoV","mr-7":"DuCnqNfcxcP3Z__Yo5Ro","mb-7":"im8407m2fw5vOg7O2zsw","ml-7":"G0fbeBgvz2sh3uTP9gNl","m-8":"kvW3sBCxRxUqz1jrVMJl","mx-8":"tOjEqjLONQdkiYx_XRnw","my-8":"op5hFSx318zgxsoZZNLN","mt-8":"c9WfNHP6TFKWIfLxv52J","mr-8":"sBA75QqcqRwwYSHJh2wc","mb-8":"GpL6idrXmSOM6jB8Ohsf","ml-8":"HbtWJoQwpgGycz8dGzeT","p-0":"uxX3khU88VQ_Ah49Ejsa","px-0":"KX0FhpBKwKzs9fOUdbNz","py-0":"PfK8vKDyN32dnimlzYjz","pt-0":"emxLHRjQuJsImnPbQIzE","pr-0":"kJ8WzlpTVgdViXt8ukP9","pb-0":"tg_UIUI11VBzrTAn2AzJ","pl-0":"uczvl8kaz84oPQJ2DB2R","p-1":"o7UHPcdVK3lt7q3lqV4o","px-1":"IDqEOxvDoYrFYxELPmtX","py-1":"DdywPW2qSYlu2pt8tpO2","pt-1":"npy3hw4A5QSkDicb2CJJ","pr-1":"LgbptTApNY5NwLQvEFAt","pb-1":"WZQy2SZuZso59bUsXXyl","pl-1":"o331apInxNunbYB3SfPE","p-2":"fMPIyD9Vqki1Lrc_yJnG","px-2":"i2pMcTcdrr10IQoiSm_L","py-2":"eA702gn32kwptiI1obXH","pt-2":"o9bGieUKcYc8o0Ij9oZX","pr-2":"SwZcFez1RDqWsOFjB5iG","pb-2":"eHpLc_idmuEqeqCTvqkN","pl-2":"vU39i2B4P1fUTMB2l6Vo","p-3":"JHWNzBnE29awhdu5BEh1","px-3":"X72lGbb56L3KFzC2xQ9N","py-3":"BzfNhRG8wXdCEB5ocQ6e","pt-3":"srV0KSDC83a2fiimSMMQ","pr-3":"lUWfkmbQjCskhcNwkyCm","pb-3":"Ts0dIlc3aTSL7V4cIHis","pl-3":"CzlqQXXhX6MvorArFZ8B","p-4":"TqMPkQtR_DdZuKb5vBoV","px-4":"a7UrjhI69Vetlcj9ZVzz","py-4":"StEhBzGs2Gi5dDEkjhAv","pt-4":"FGneZfZyvYrt1dG0zcnm","pr-4":"APEH216rpdlJWgD2fHc8","pb-4":"oGwXC3ohCic9XnAj6x69","pl-4":"U6gnT9y42ViPNOcNzBwb","p-5":"IpdRLBwnHqbqFrixgbYC","px-5":"HgNeXvkBa9o3bQ5fvFZm","py-5":"tJtFZM3XfPG9v9TSDfN1","pt-5":"PdifHW45QeXYfK568uD8","pr-5":"mbLkWTTZ0Za_BBbFZ5b2","pb-5":"vVWpZpLlWrkTt0hMk8XU","pl-5":"RxfaJj5a1Nt6IavEo5Zl","p-6":"SppJULDGdnOGcjZNCYBy","px-6":"palY2nLwdoyooPUm9Hhk","py-6":"WYw1JvZC0ppLdvSAPhr_","pt-6":"YEEJ9b90ueQaPfiU8aeN","pr-6":"QE0ssnsKvWJMqlhPbY5u","pb-6":"n8yA3jHlMRyLd5UIfoND","pl-6":"tXHmxYnHzbwtfxEaG51n","p-7":"kBTsPKkO_3g_tLkj77Um","px-7":"RyhrFx6Y1FGDrGAAyaxm","py-7":"CBwRpB0bDN3iEdQPPMJO","pt-7":"vQVSq6SvWKbOMu6r4H6b","pr-7":"oBy5__aEADMsH46mrgFX","pb-7":"KVEXoJqf1s92j0JMdNmN","pl-7":"ZMXGNrNaKW3k_3TLz0Fq","p-8":"tuiR9PhkHXhGyEgzRZRI","px-8":"U7454qyWkQNa2iaSJziu","py-8":"VLYIv2GVocjuN93e8HC8","pt-8":"X1rm9DQ1zLGLfogja5Gn","pr-8":"JS7G6kAuqJo5GIuF8S5t","pb-8":"Y8F9ga1TDCMbM1lj4gUz","pl-8":"AJuyNGrI63BOWql719H8"}},9422:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={global:"_fUXxnSp5pagKBp9gSN7"}},4997:e=>{var t=1e3,r=60*t,n=60*r,a=24*n,s=7*a,c=365.25*a;function o(e,t,r,n){var a=t>=1.5*r;return Math.round(e/r)+" "+n+(a?"s":"")}e.exports=function(e,i){i=i||{};var l=typeof e;if("string"===l&&e.length>0)return function(e){if((e=String(e)).length>100)return;var o=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!o)return;var i=parseFloat(o[1]);switch((o[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return i*c;case"weeks":case"week":case"w":return i*s;case"days":case"day":case"d":return i*a;case"hours":case"hour":case"hrs":case"hr":case"h":return i*n;case"minutes":case"minute":case"mins":case"min":case"m":return i*r;case"seconds":case"second":case"secs":case"sec":case"s":return i*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return i;default:return}}(e);if("number"===l&&isFinite(e))return i.long?function(e){var s=Math.abs(e);if(s>=a)return o(e,s,a,"day");if(s>=n)return o(e,s,n,"hour");if(s>=r)return o(e,s,r,"minute");if(s>=t)return o(e,s,t,"second");return e+" ms"}(e):function(e){var s=Math.abs(e);if(s>=a)return Math.round(e/a)+"d";if(s>=n)return Math.round(e/n)+"h";if(s>=r)return Math.round(e/r)+"m";if(s>=t)return Math.round(e/t)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},3381:(e,t,r)=>{"use strict";var n=r(3761);function a(){}function s(){}s.resetWarningCache=a,e.exports=function(){function e(e,t,r,a,s,c){if(c!==n){var o=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:s,resetWarningCache:a};return r.PropTypes=r,r}},8120:(e,t,r)=>{e.exports=r(3381)()},3761:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},372:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});r(9060);var n=r(6941);const a=r.n(n)()("dops:analytics");let s,c;window._tkq=window._tkq||[],window.ga=window.ga||function(){(window.ga.q=window.ga.q||[]).push(arguments)},window.ga.l=+new Date;const o={initialize:function(e,t,r){o.setUser(e,t),o.setSuperProps(r),o.identifyUser()},setGoogleAnalyticsEnabled:function(e,t=null){this.googleAnalyticsEnabled=e,this.googleAnalyticsKey=t},setMcAnalyticsEnabled:function(e){this.mcAnalyticsEnabled=e},setUser:function(e,t){c={ID:e,username:t}},setSuperProps:function(e){s=e},assignSuperProps:function(e){s=Object.assign(s||{},e)},mc:{bumpStat:function(e,t){const r=function(e,t){let r="";if("object"==typeof e){for(const t in e)r+="&x_"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);a("Bumping stats %o",e)}else r="&x_"+encodeURIComponent(e)+"="+encodeURIComponent(t),a('Bumping stat "%s" in group "%s"',t,e);return r}(e,t);o.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom-no-pv"+r+"&t="+Math.random())},bumpStatWithPageView:function(e,t){const r=function(e,t){let r="";if("object"==typeof e){for(const t in e)r+="&"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);a("Built stats %o",e)}else r="&"+encodeURIComponent(e)+"="+encodeURIComponent(t),a('Built stat "%s" in group "%s"',t,e);return r}(e,t);o.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom"+r+"&t="+Math.random())}},pageView:{record:function(e,t){o.tracks.recordPageView(e),o.ga.recordPageView(e,t)}},purchase:{record:function(e,t,r,n,a,s,c){o.ga.recordPurchase(e,t,r,n,a,s,c)}},tracks:{recordEvent:function(e,t){t=t||{},0===e.indexOf("akismet_")||0===e.indexOf("jetpack_")?(s&&(a("- Super Props: %o",s),t=Object.assign(t,s)),a('Record event "%s" called with props %s',e,JSON.stringify(t)),window._tkq.push(["recordEvent",e,t])):a('- Event name must be prefixed by "akismet_" or "jetpack_"')},recordJetpackClick:function(e){const t="object"==typeof e?e:{target:e};o.tracks.recordEvent("jetpack_wpa_click",t)},recordPageView:function(e){o.tracks.recordEvent("akismet_page_view",{path:e})},setOptOut:function(e){a("Pushing setOptOut: %o",e),window._tkq.push(["setOptOut",e])}},ga:{initialized:!1,initialize:function(){let e={};o.ga.initialized||(c&&(e={userId:"u-"+c.ID}),window.ga("create",this.googleAnalyticsKey,"auto",e),o.ga.initialized=!0)},recordPageView:function(e,t){o.ga.initialize(),a("Recording Page View ~ [URL: "+e+"] [Title: "+t+"]"),this.googleAnalyticsEnabled&&(window.ga("set","page",e),window.ga("send",{hitType:"pageview",page:e,title:t}))},recordEvent:function(e,t,r,n){o.ga.initialize();let s="Recording Event ~ [Category: "+e+"] [Action: "+t+"]";void 0!==r&&(s+=" [Option Label: "+r+"]"),void 0!==n&&(s+=" [Option Value: "+n+"]"),a(s),this.googleAnalyticsEnabled&&window.ga("send","event",e,t,r,n)},recordPurchase:function(e,t,r,n,a,s,c){window.ga("require","ecommerce"),window.ga("ecommerce:addTransaction",{id:e,revenue:n,currency:c}),window.ga("ecommerce:addItem",{id:e,name:t,sku:r,price:a,quantity:s}),window.ga("ecommerce:send")}},identifyUser:function(){c&&window._tkq.push(["identifyUser",c.ID,c.username])},setProperties:function(e){window._tkq.push(["setProperties",e])},clearedIdentity:function(){window._tkq.push(["clearIdentity"])}},i=o},5932:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>u});r(9060);var n=r(6439),a=r(3832);function s(e){class t extends Error{constructor(...t){super(...t),this.name=e}}return t}const c=s("JsonParseError"),o=s("JsonParseAfterRedirectError"),i=s("Api404Error"),l=s("Api404AfterRedirectError"),p=s("FetchNetworkError");const u=new function(e,t){let r=e,s=e,c={"X-WP-Nonce":t},o={credentials:"same-origin",headers:c},i={method:"post",credentials:"same-origin",headers:Object.assign({},c,{"Content-type":"application/json"})},l=function(e){const t=e.split("?"),r=t.length>1?t[1]:"",n=r.length?r.split("&"):[];return n.push("_cacheBuster="+(new Date).getTime()),t[0]+"?"+n.join("&")};const p={setApiRoot(e){r=e},setWpcomOriginApiUrl(e){s=e},setApiNonce(e){c={"X-WP-Nonce":e},o={credentials:"same-origin",headers:c},i={method:"post",credentials:"same-origin",headers:Object.assign({},c,{"Content-type":"application/json"})}},setCacheBusterCallback:e=>{l=e},registerSite:(e,t,a)=>{const s={};return(0,n.jetpackConfigHas)("consumer_slug")&&(s.plugin_slug=(0,n.jetpackConfigGet)("consumer_slug")),null!==t&&(s.redirect_uri=t),a&&(s.from=a),g(`${r}jetpack/v4/connection/register`,i,{body:JSON.stringify(s)}).then(d).then(m)},fetchAuthorizationUrl:e=>u((0,a.addQueryArgs)(`${r}jetpack/v4/connection/authorize_url`,{no_iframe:"1",redirect_uri:e}),o).then(d).then(m),fetchSiteConnectionData:()=>u(`${r}jetpack/v4/connection/data`,o).then(m),fetchSiteConnectionStatus:()=>u(`${r}jetpack/v4/connection`,o).then(m),fetchSiteConnectionTest:()=>u(`${r}jetpack/v4/connection/test`,o).then(d).then(m),fetchUserConnectionData:()=>u(`${r}jetpack/v4/connection/data`,o).then(m),fetchUserTrackingSettings:()=>u(`${r}jetpack/v4/tracking/settings`,o).then(d).then(m),updateUserTrackingSettings:e=>g(`${r}jetpack/v4/tracking/settings`,i,{body:JSON.stringify(e)}).then(d).then(m),disconnectSite:()=>g(`${r}jetpack/v4/connection`,i,{body:JSON.stringify({isActive:!1})}).then(d).then(m),fetchConnectUrl:()=>u(`${r}jetpack/v4/connection/url`,o).then(d).then(m),unlinkUser:(e=!1,t={})=>{const n={linked:!1,force:!!e};return t.disconnectAllUsers&&(n["disconnect-all-users"]=!0),g(`${r}jetpack/v4/connection/user`,i,{body:JSON.stringify(n)}).then(d).then(m)},reconnect:()=>g(`${r}jetpack/v4/connection/reconnect`,i).then(d).then(m),fetchConnectedPlugins:()=>u(`${r}jetpack/v4/connection/plugins`,o).then(d).then(m),setHasSeenWCConnectionModal:()=>g(`${r}jetpack/v4/seen-wc-connection-modal`,i).then(d).then(m),fetchModules:()=>u(`${r}jetpack/v4/module/all`,o).then(d).then(m),fetchModule:e=>u(`${r}jetpack/v4/module/${e}`,o).then(d).then(m),activateModule:e=>g(`${r}jetpack/v4/module/${e}/active`,i,{body:JSON.stringify({active:!0})}).then(d).then(m),deactivateModule:e=>g(`${r}jetpack/v4/module/${e}/active`,i,{body:JSON.stringify({active:!1})}),updateModuleOptions:(e,t)=>g(`${r}jetpack/v4/module/${e}`,i,{body:JSON.stringify(t)}).then(d).then(m),updateSettings:e=>g(`${r}jetpack/v4/settings`,i,{body:JSON.stringify(e)}).then(d).then(m),getProtectCount:()=>u(`${r}jetpack/v4/module/protect/data`,o).then(d).then(m),resetOptions:e=>g(`${r}jetpack/v4/options/${e}`,i,{body:JSON.stringify({reset:!0})}).then(d).then(m),activateVaultPress:()=>g(`${r}jetpack/v4/plugins`,i,{body:JSON.stringify({slug:"vaultpress",status:"active"})}).then(d).then(m),getVaultPressData:()=>u(`${r}jetpack/v4/module/vaultpress/data`,o).then(d).then(m),installPlugin:(e,t)=>{const n={slug:e,status:"active"};return t&&(n.source=t),g(`${r}jetpack/v4/plugins`,i,{body:JSON.stringify(n)}).then(d).then(m)},activateAkismet:()=>g(`${r}jetpack/v4/plugins`,i,{body:JSON.stringify({slug:"akismet",status:"active"})}).then(d).then(m),getAkismetData:()=>u(`${r}jetpack/v4/module/akismet/data`,o).then(d).then(m),checkAkismetKey:()=>u(`${r}jetpack/v4/module/akismet/key/check`,o).then(d).then(m),checkAkismetKeyTyped:e=>g(`${r}jetpack/v4/module/akismet/key/check`,i,{body:JSON.stringify({api_key:e})}).then(d).then(m),getFeatureTypeStatus:e=>u(`${r}jetpack/v4/feature/${e}`,o).then(d).then(m),fetchStatsData:e=>u(function(e){let t=`${r}jetpack/v4/module/stats/data`;-1!==t.indexOf("?")?t+=`&range=${encodeURIComponent(e)}`:t+=`?range=${encodeURIComponent(e)}`;return t}(e),o).then(d).then(m).then(f),getPluginUpdates:()=>u(`${r}jetpack/v4/updates/plugins`,o).then(d).then(m),getPlans:()=>u(`${r}jetpack/v4/plans`,o).then(d).then(m),fetchSettings:()=>u(`${r}jetpack/v4/settings`,o).then(d).then(m),updateSetting:e=>g(`${r}jetpack/v4/settings`,i,{body:JSON.stringify(e)}).then(d).then(m),fetchSiteData:()=>u(`${r}jetpack/v4/site`,o).then(d).then(m).then((e=>JSON.parse(e.data))),fetchSiteFeatures:()=>u(`${r}jetpack/v4/site/features`,o).then(d).then(m).then((e=>JSON.parse(e.data))),fetchSiteProducts:()=>u(`${r}jetpack/v4/site/products`,o).then(d).then(m),fetchSitePurchases:()=>u(`${r}jetpack/v4/site/purchases`,o).then(d).then(m).then((e=>JSON.parse(e.data))),fetchSiteBenefits:()=>u(`${r}jetpack/v4/site/benefits`,o).then(d).then(m).then((e=>JSON.parse(e.data))),fetchSiteDiscount:()=>u(`${r}jetpack/v4/site/discount`,o).then(d).then(m).then((e=>e.data)),fetchSetupQuestionnaire:()=>u(`${r}jetpack/v4/setup/questionnaire`,o).then(d).then(m),fetchRecommendationsData:()=>u(`${r}jetpack/v4/recommendations/data`,o).then(d).then(m),fetchRecommendationsProductSuggestions:()=>u(`${r}jetpack/v4/recommendations/product-suggestions`,o).then(d).then(m),fetchRecommendationsUpsell:()=>u(`${r}jetpack/v4/recommendations/upsell`,o).then(d).then(m),fetchRecommendationsConditional:()=>u(`${r}jetpack/v4/recommendations/conditional`,o).then(d).then(m),saveRecommendationsData:e=>g(`${r}jetpack/v4/recommendations/data`,i,{body:JSON.stringify({data:e})}).then(d),fetchProducts:()=>u(`${r}jetpack/v4/products`,o).then(d).then(m),fetchRewindStatus:()=>u(`${r}jetpack/v4/rewind`,o).then(d).then(m).then((e=>JSON.parse(e.data))),fetchScanStatus:()=>u(`${r}jetpack/v4/scan`,o).then(d).then(m).then((e=>JSON.parse(e.data))),dismissJetpackNotice:e=>g(`${r}jetpack/v4/notice/${e}`,i,{body:JSON.stringify({dismissed:!0})}).then(d).then(m),fetchPluginsData:()=>u(`${r}jetpack/v4/plugins`,o).then(d).then(m),fetchIntroOffers:()=>u(`${r}jetpack/v4/intro-offers`,o).then(d).then(m),fetchVerifySiteGoogleStatus:e=>u(null!==e?`${r}jetpack/v4/verify-site/google/${e}`:`${r}jetpack/v4/verify-site/google`,o).then(d).then(m),verifySiteGoogle:e=>g(`${r}jetpack/v4/verify-site/google`,i,{body:JSON.stringify({keyring_id:e})}).then(d).then(m),submitSurvey:e=>g(`${r}jetpack/v4/marketing/survey`,i,{body:JSON.stringify(e)}).then(d).then(m),saveSetupQuestionnaire:e=>g(`${r}jetpack/v4/setup/questionnaire`,i,{body:JSON.stringify(e)}).then(d).then(m),updateLicensingError:e=>g(`${r}jetpack/v4/licensing/error`,i,{body:JSON.stringify(e)}).then(d).then(m),updateLicenseKey:e=>g(`${r}jetpack/v4/licensing/set-license`,i,{body:JSON.stringify({license:e})}).then(d).then(m),getUserLicensesCounts:()=>u(`${r}jetpack/v4/licensing/user/counts`,o).then(d).then(m),getUserLicenses:()=>u(`${r}jetpack/v4/licensing/user/licenses`,o).then(d).then(m),updateLicensingActivationNoticeDismiss:e=>g(`${r}jetpack/v4/licensing/user/activation-notice-dismiss`,i,{body:JSON.stringify({last_detached_count:e})}).then(d).then(m),updateRecommendationsStep:e=>g(`${r}jetpack/v4/recommendations/step`,i,{body:JSON.stringify({step:e})}).then(d),confirmIDCSafeMode:()=>g(`${r}jetpack/v4/identity-crisis/confirm-safe-mode`,i).then(d),startIDCFresh:e=>g(`${r}jetpack/v4/identity-crisis/start-fresh`,i,{body:JSON.stringify({redirect_uri:e})}).then(d).then(m),migrateIDC:()=>g(`${r}jetpack/v4/identity-crisis/migrate`,i).then(d),attachLicenses:e=>g(`${r}jetpack/v4/licensing/attach-licenses`,i,{body:JSON.stringify({licenses:e})}).then(d).then(m),fetchSearchPlanInfo:()=>u(`${s}jetpack/v4/search/plan`,o).then(d).then(m),fetchSearchSettings:()=>u(`${s}jetpack/v4/search/settings`,o).then(d).then(m),updateSearchSettings:e=>g(`${s}jetpack/v4/search/settings`,i,{body:JSON.stringify(e)}).then(d).then(m),fetchSearchStats:()=>u(`${s}jetpack/v4/search/stats`,o).then(d).then(m),fetchWafSettings:()=>u(`${r}jetpack/v4/waf`,o).then(d).then(m),updateWafSettings:e=>g(`${r}jetpack/v4/waf`,i,{body:JSON.stringify(e)}).then(d).then(m),fetchWordAdsSettings:()=>u(`${r}jetpack/v4/wordads/settings`,o).then(d).then(m),updateWordAdsSettings:e=>g(`${r}jetpack/v4/wordads/settings`,i,{body:JSON.stringify(e)}),fetchSearchPricing:()=>u(`${s}jetpack/v4/search/pricing`,o).then(d).then(m),fetchMigrationStatus:()=>u(`${r}jetpack/v4/migration/status`,o).then(d).then(m),fetchBackupUndoEvent:()=>u(`${r}jetpack/v4/site/backup/undo-event`,o).then(d).then(m),fetchBackupPreflightStatus:()=>u(`${r}jetpack/v4/site/backup/preflight`,o).then(d).then(m)};function u(e,t){return fetch(l(e),t)}function g(e,t,r){return fetch(e,Object.assign({},t,r)).catch(h)}function f(e){return e.general&&void 0===e.general.response||e.week&&void 0===e.week.response||e.month&&void 0===e.month.response?e:{}}Object.assign(this,p)};function d(e){return e.status>=200&&e.status<300?e:404===e.status?new Promise((()=>{throw e.redirected?new l(e.redirected):new i})):e.json().catch((e=>g(e))).then((t=>{const r=new Error(`${t.message} (Status ${e.status})`);throw r.response=t,r.name="ApiError",r}))}function m(e){return e.json().catch((t=>g(t,e.redirected,e.url)))}function g(e,t,r){throw t?new o(r):new c}function h(){throw new p}},2947:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(5932),a=r(7723),s=r(3022),c=r(1609),o=r(8250),i=r(7142),l=r(8509),p=r(5918),u=r(2021);const __=a.__,d=({children:e,moduleName:t=__("Jetpack","jetpack-search-pkg"),moduleNameHref:r,showHeader:d=!0,showFooter:m=!0,useInternalLinks:g=!1,showBackground:h=!0,sandboxedDomain:f="",apiRoot:v="",apiNonce:y="",optionalMenuItems:b,header:k})=>{(0,c.useEffect)((()=>{n.Ay.setApiRoot(v),n.Ay.setApiNonce(y)}),[v,y]);const E=(0,s.A)(u.A["admin-page"],{[u.A.background]:h}),C=(0,c.useCallback)((async()=>{try{const e=await n.Ay.fetchSiteConnectionTest();window.alert(e.message)}catch(e){window.alert((0,a.sprintf)(/* translators: placeholder is an error message. */
__("There was an error testing Jetpack. Error: %s","jetpack-search-pkg"),e.message))}}),[]);return React.createElement("div",{className:E},d&&React.createElement(p.A,{horizontalSpacing:5},React.createElement(l.A,{className:(0,s.A)(u.A["admin-page-header"],"jp-admin-page-header")},k||React.createElement(i.A,null),f&&React.createElement("code",{className:u.A["sandbox-domain-badge"],onClick:C,onKeyDown:C,role:"button",tabIndex:0,title:`Sandboxing via ${f}. Click to test connection.`},"API Sandboxed"))),React.createElement(p.A,{fluid:!0,horizontalSpacing:0},React.createElement(l.A,null,e)),m&&React.createElement(p.A,{horizontalSpacing:5},React.createElement(l.A,null,React.createElement(o.A,{moduleName:t,moduleNameHref:r,menu:b,useInternalLinks:g}))))}},766:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(1609),a=r.n(n),s=r(6888);const c=({children:e})=>a().createElement("div",{className:s.A["section-hero"]},e)},8907:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(6072),a=r.n(n),s=r(7723),c=r(3022),o=r(1609),i=r.n(o);const __=s.__,l=({title:e=__("An Automattic Airline","jetpack-search-pkg"),height:t=7,className:r,...n})=>i().createElement("svg",a()({role:"img",x:"0",y:"0",viewBox:"0 0 935 38.2",enableBackground:"new 0 0 935 38.2","aria-labelledby":"jp-automattic-byline-logo-title",height:t,className:(0,c.A)("jp-automattic-byline-logo",r)},n),i().createElement("desc",{id:"jp-automattic-byline-logo-title"},e),i().createElement("path",{d:"M317.1 38.2c-12.6 0-20.7-9.1-20.7-18.5v-1.2c0-9.6 8.2-18.5 20.7-18.5 12.6 0 20.8 8.9 20.8 18.5v1.2C337.9 29.1 329.7 38.2 317.1 38.2zM331.2 18.6c0-6.9-5-13-14.1-13s-14 6.1-14 13v0.9c0 6.9 5 13.1 14 13.1s14.1-6.2 14.1-13.1V18.6zM175 36.8l-4.7-8.8h-20.9l-4.5 8.8h-7L157 1.3h5.5L182 36.8H175zM159.7 8.2L152 23.1h15.7L159.7 8.2zM212.4 38.2c-12.7 0-18.7-6.9-18.7-16.2V1.3h6.6v20.9c0 6.6 4.3 10.5 12.5 10.5 8.4 0 11.9-3.9 11.9-10.5V1.3h6.7V22C231.4 30.8 225.8 38.2 212.4 38.2zM268.6 6.8v30h-6.7v-30h-15.5V1.3h37.7v5.5H268.6zM397.3 36.8V8.7l-1.8 3.1 -14.9 25h-3.3l-14.7-25 -1.8-3.1v28.1h-6.5V1.3h9.2l14 24.4 1.7 3 1.7-3 13.9-24.4h9.1v35.5H397.3zM454.4 36.8l-4.7-8.8h-20.9l-4.5 8.8h-7l19.2-35.5h5.5l19.5 35.5H454.4zM439.1 8.2l-7.7 14.9h15.7L439.1 8.2zM488.4 6.8v30h-6.7v-30h-15.5V1.3h37.7v5.5H488.4zM537.3 6.8v30h-6.7v-30h-15.5V1.3h37.7v5.5H537.3zM569.3 36.8V4.6c2.7 0 3.7-1.4 3.7-3.4h2.8v35.5L569.3 36.8 569.3 36.8zM628 11.3c-3.2-2.9-7.9-5.7-14.2-5.7 -9.5 0-14.8 6.5-14.8 13.3v0.7c0 6.7 5.4 13 15.3 13 5.9 0 10.8-2.8 13.9-5.7l4 4.2c-3.9 3.8-10.5 7.1-18.3 7.1 -13.4 0-21.6-8.7-21.6-18.3v-1.2c0-9.6 8.9-18.7 21.9-18.7 7.5 0 14.3 3.1 18 7.1L628 11.3zM321.5 12.4c1.2 0.8 1.5 2.4 0.8 3.6l-6.1 9.4c-0.8 1.2-2.4 1.6-3.6 0.8l0 0c-1.2-0.8-1.5-2.4-0.8-3.6l6.1-9.4C318.7 11.9 320.3 11.6 321.5 12.4L321.5 12.4z"}),i().createElement("path",{d:"M37.5 36.7l-4.7-8.9H11.7l-4.6 8.9H0L19.4 0.8H25l19.7 35.9H37.5zM22 7.8l-7.8 15.1h15.9L22 7.8zM82.8 36.7l-23.3-24 -2.3-2.5v26.6h-6.7v-36H57l22.6 24 2.3 2.6V0.8h6.7v35.9H82.8z"}),i().createElement("path",{d:"M719.9 37l-4.8-8.9H694l-4.6 8.9h-7.1l19.5-36h5.6l19.8 36H719.9zM704.4 8l-7.8 15.1h15.9L704.4 8zM733 37V1h6.8v36H733zM781 37c-1.8 0-2.6-2.5-2.9-5.8l-0.2-3.7c-0.2-3.6-1.7-5.1-8.4-5.1h-12.8V37H750V1h19.6c10.8 0 15.7 4.3 15.7 9.9 0 3.9-2 7.7-9 9 7 0.5 8.5 3.7 8.6 7.9l0.1 3c0.1 2.5 0.5 4.3 2.2 6.1V37H781zM778.5 11.8c0-2.6-2.1-5.1-7.9-5.1h-13.8v10.8h14.4c5 0 7.3-2.4 7.3-5.2V11.8zM794.8 37V1h6.8v30.4h28.2V37H794.8zM836.7 37V1h6.8v36H836.7zM886.2 37l-23.4-24.1 -2.3-2.5V37h-6.8V1h6.5l22.7 24.1 2.3 2.6V1h6.8v36H886.2zM902.3 37V1H935v5.6h-26v9.2h20v5.5h-20v10.1h26V37H902.3z"}))},1112:(e,t,r)=>{"use strict";r.d(t,{A:()=>g});var n=r(6072),a=r.n(n),s=r(6427),c=r(7723),o=r(1113),i=r(3512),l=r(3022),p=r(1609),u=r.n(p),d=r(2258);const __=c.__,m=(0,p.forwardRef)(((e,t)=>{const{children:r,variant:n="primary",size:c="normal",weight:p="bold",icon:m,iconSize:g,disabled:h,isDestructive:f,isLoading:v,isExternalLink:y,className:b,text:k,fullWidth:E,...C}=e,j=(0,l.A)(d.A.button,b,{[d.A.normal]:"normal"===c,[d.A.small]:"small"===c,[d.A.icon]:Boolean(m),[d.A.loading]:v,[d.A.regular]:"regular"===p,[d.A["full-width"]]:E,[d.A["is-icon-button"]]:Boolean(m)&&!r});C.ref=t;const _="normal"===c?20:16,A=y&&u().createElement(u().Fragment,null,u().createElement(o.A,{size:_,icon:i.A,className:d.A["external-icon"]}),u().createElement(s.VisuallyHidden,{as:"span"},/* translators: accessibility text */
__("(opens in a new tab)","jetpack-search-pkg"))),w=y?"_blank":void 0,S=r?.[0]&&null!==r[0]&&"components-tooltip"!==r?.[0]?.props?.className;return u().createElement(s.Button,a()({target:w,variant:n,className:(0,l.A)(j,{"has-text":!!m&&S}),icon:y?void 0:m,iconSize:g,disabled:h,"aria-disabled":h,isDestructive:f,text:k},C),v&&u().createElement(s.Spinner,null),u().createElement("span",null,r),A)}));m.displayName="Button";const g=m},4437:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(1113),a=r(1797),s=r(3022),c=r(597),o=r(7425),i=r(2127);const l=({description:e,cta:t,onClick:r,href:l,openInNewTab:p=!1,className:u,tooltipText:d=""})=>{const m=void 0!==l?"a":"button",g="a"===m?{href:l,...p&&{target:"_blank"}}:{onClick:r};return React.createElement("div",{className:(0,s.A)(i.A.cut,u)},React.createElement("div",null,React.createElement("div",null,React.createElement(o.Ay,{className:i.A.description},e),d&&React.createElement(c.A,{className:i.A.iconContainer,iconSize:16,offset:4},React.createElement(o.Ay,{variant:"body-small"},d))),React.createElement("div",null,React.createElement(m,g,React.createElement(o.Ay,{className:i.A.cta},t)))),React.createElement(n.A,{icon:a.A,className:i.A.icon,size:30}))}},4976:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(1609),a=r.n(n);r(9768);const s=({className:e="",description:t="",donutWidth:r="64px",segmentCount:n,thickness:s="3.5",title:c="",totalCount:o,type:i,useAdaptiveColors:l})=>{const p=n/(0===o?1:o)*100,u="string"==typeof c&&0===c.length&&"string"==typeof t&&0===t.length?"true":"false",d=`donut-meter ${e?e+" ":""}${i?"is-"+i+" ":""} ${!i&&l?"is-"+(e=>e<70?"success":e<100?"warning":"danger")(p)+" ":""}`.trim();return a().createElement("div",{className:d,"aria-hidden":u,"data-testid":"donut-meter"},a().createElement("svg",{width:r,height:r,viewBox:"0 0 40 40",className:"donut-meter_svg",role:"img"},a().createElement("title",{id:"donut-meter-title"},c),a().createElement("desc",{id:"donut-meter-description"},t),a().createElement("circle",{className:"donut-meter-hole",cx:"20",cy:"20",r:"15.91549430918954",fill:"transparent"}),a().createElement("circle",{className:"donut-meter-ring",cx:"20",cy:"20",r:"15.91549430918954",fill:"transparent",strokeWidth:s,stroke:"#ebebeb"}),a().createElement("circle",{className:"donut-meter-segment",cx:"20",cy:"20",r:"15.91549430918954",fill:"transparent","transform-origin":"center",strokeWidth:s,strokeDasharray:`${p} ${100-p}`,strokeDashoffset:"-25"})))}},1883:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(7723),a=r(3022),s=r(1609);r(3689);const __=n.__;class c extends s.Component{static defaultProps={"aria-hidden":"false",focusable:"true"};needsOffset(e,t){return["gridicons-arrow-left","gridicons-arrow-right","gridicons-calendar","gridicons-cart","gridicons-folder","gridicons-help-outline","gridicons-info","gridicons-info-outline","gridicons-posts","gridicons-star-outline","gridicons-star"].indexOf(e)>=0&&t%18==0}getSVGDescription(e){if("description"in this.props)return this.props.description;switch(e){default:return"";case"gridicons-audio":return __("Has audio.","jetpack-search-pkg");case"gridicons-arrow-left":return __("Arrow left","jetpack-search-pkg");case"gridicons-arrow-right":return __("Arrow right","jetpack-search-pkg");case"gridicons-calendar":return __("Is an event.","jetpack-search-pkg");case"gridicons-cart":return __("Is a product.","jetpack-search-pkg");case"chevron-down":return __("Show filters","jetpack-search-pkg");case"gridicons-comment":return __("Matching comment.","jetpack-search-pkg");case"gridicons-cross":return __("Close.","jetpack-search-pkg");case"gridicons-filter":return __("Toggle search filters.","jetpack-search-pkg");case"gridicons-folder":return __("Category","jetpack-search-pkg");case"gridicons-help-outline":return __("Help","jetpack-search-pkg");case"gridicons-info":case"gridicons-info-outline":return __("Information.","jetpack-search-pkg");case"gridicons-image-multiple":return __("Has multiple images.","jetpack-search-pkg");case"gridicons-image":return __("Has an image.","jetpack-search-pkg");case"gridicons-page":return __("Page","jetpack-search-pkg");case"gridicons-post":return __("Post","jetpack-search-pkg");case"gridicons-jetpack-search":case"gridicons-search":return __("Magnifying Glass","jetpack-search-pkg");case"gridicons-tag":return __("Tag","jetpack-search-pkg");case"gridicons-video":return __("Has a video.","jetpack-search-pkg")}}renderIcon(e){switch(e){default:return null;case"gridicons-audio":return React.createElement("g",null,React.createElement("path",{d:"M8 4v10.184C7.686 14.072 7.353 14 7 14c-1.657 0-3 1.343-3 3s1.343 3 3 3 3-1.343 3-3V7h7v4.184c-.314-.112-.647-.184-1-.184-1.657 0-3 1.343-3 3s1.343 3 3 3 3-1.343 3-3V4H8z"}));case"gridicons-arrow-left":return React.createElement("g",null,React.createElement("path",{d:"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"}));case"gridicons-arrow-right":return React.createElement("g",null,React.createElement("path",{d:"M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8-8-8z"}));case"gridicons-block":return React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zM4 12c0-4.418 3.582-8 8-8 1.848 0 3.545.633 4.9 1.686L5.686 16.9C4.633 15.545 4 13.848 4 12zm8 8c-1.848 0-3.546-.633-4.9-1.686L18.314 7.1C19.367 8.455 20 10.152 20 12c0 4.418-3.582 8-8 8z"}));case"gridicons-calendar":return React.createElement("g",null,React.createElement("path",{d:"M19 4h-1V2h-2v2H8V2H6v2H5c-1.105 0-2 .896-2 2v13c0 1.104.895 2 2 2h14c1.104 0 2-.896 2-2V6c0-1.104-.896-2-2-2zm0 15H5V8h14v11z"}));case"gridicons-cart":return React.createElement("g",null,React.createElement("path",{d:"M9 20c0 1.1-.9 2-2 2s-1.99-.9-1.99-2S5.9 18 7 18s2 .9 2 2zm8-2c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2zm.396-5c.937 0 1.75-.65 1.952-1.566L21 5H7V4c0-1.105-.895-2-2-2H3v2h2v11c0 1.105.895 2 2 2h12c0-1.105-.895-2-2-2H7v-2h10.396z"}));case"gridicons-checkmark":return React.createElement("g",null,React.createElement("path",{d:"M11 17.768l-4.884-4.884 1.768-1.768L11 14.232l8.658-8.658C17.823 3.39 15.075 2 12 2 6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10c0-1.528-.353-2.97-.966-4.266L11 17.768z"}));case"gridicons-chevron-left":return React.createElement("g",null,React.createElement("path",{d:"M16.443 7.41L15.0399 6L9.06934 12L15.0399 18L16.443 16.59L11.8855 12L16.443 7.41Z"}));case"gridicons-chevron-right":return React.createElement("g",null,React.createElement("path",{d:"M10.2366 6L8.8335 7.41L13.391 12L8.8335 16.59L10.2366 18L16.2072 12L10.2366 6Z"}));case"gridicons-chevron-down":return React.createElement("g",null,React.createElement("path",{d:"M20 9l-8 8-8-8 1.414-1.414L12 14.172l6.586-6.586"}));case"gridicons-comment":return React.createElement("g",null,React.createElement("path",{d:"M3 6v9c0 1.105.895 2 2 2h9v5l5.325-3.804c1.05-.75 1.675-1.963 1.675-3.254V6c0-1.105-.895-2-2-2H5c-1.105 0-2 .895-2 2z"}));case"gridicons-computer":return React.createElement("g",null,React.createElement("path",{d:"M20 2H4c-1.104 0-2 .896-2 2v12c0 1.104.896 2 2 2h6v2H7v2h10v-2h-3v-2h6c1.104 0 2-.896 2-2V4c0-1.104-.896-2-2-2zm0 14H4V4h16v12z"}));case"gridicons-cross":return React.createElement("g",null,React.createElement("path",{d:"M18.36 19.78L12 13.41l-6.36 6.37-1.42-1.42L10.59 12 4.22 5.64l1.42-1.42L12 10.59l6.36-6.36 1.41 1.41L13.41 12l6.36 6.36z"}));case"gridicons-filter":return React.createElement("g",null,React.createElement("path",{d:"M10 19h4v-2h-4v2zm-4-6h12v-2H6v2zM3 5v2h18V5H3z"}));case"gridicons-folder":return React.createElement("g",null,React.createElement("path",{d:"M18 19H6c-1.1 0-2-.9-2-2V7c0-1.1.9-2 2-2h3c1.1 0 2 .9 2 2h7c1.1 0 2 .9 2 2v8c0 1.1-.9 2-2 2z"}));case"gridicons-help-outline":return React.createElement("g",null,React.createElement("path",{d:"M12 4c4.41 0 8 3.59 8 8s-3.59 8-8 8-8-3.59-8-8 3.59-8 8-8m0-2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 13h-2v2h2v-2zm-1.962-2v-.528c0-.4.082-.74.246-1.017.163-.276.454-.546.87-.808.333-.21.572-.397.717-.565.146-.168.22-.36.22-.577 0-.172-.078-.308-.234-.41-.156-.1-.358-.15-.608-.15-.62 0-1.34.22-2.168.658l-.854-1.67c1.02-.58 2.084-.872 3.194-.872.913 0 1.63.202 2.15.603.52.4.78.948.78 1.64 0 .495-.116.924-.347 1.287-.23.362-.6.705-1.11 1.03-.43.278-.7.48-.807.61-.108.13-.163.282-.163.458V13h-1.885z"}));case"gridicons-image":return React.createElement("g",null,React.createElement("path",{d:"M13 9.5c0-.828.672-1.5 1.5-1.5s1.5.672 1.5 1.5-.672 1.5-1.5 1.5-1.5-.672-1.5-1.5zM22 6v12c0 1.105-.895 2-2 2H4c-1.105 0-2-.895-2-2V6c0-1.105.895-2 2-2h16c1.105 0 2 .895 2 2zm-2 0H4v7.444L8 9l5.895 6.55 1.587-1.85c.798-.932 2.24-.932 3.037 0L20 15.426V6z"}));case"gridicons-image-multiple":return React.createElement("g",null,React.createElement("path",{d:"M15 7.5c0-.828.672-1.5 1.5-1.5s1.5.672 1.5 1.5S17.328 9 16.5 9 15 8.328 15 7.5zM4 20h14c0 1.105-.895 2-2 2H4c-1.1 0-2-.9-2-2V8c0-1.105.895-2 2-2v14zM22 4v12c0 1.105-.895 2-2 2H8c-1.105 0-2-.895-2-2V4c0-1.105.895-2 2-2h12c1.105 0 2 .895 2 2zM8 4v6.333L11 7l4.855 5.395.656-.73c.796-.886 2.183-.886 2.977 0l.513.57V4H8z"}));case"gridicons-info":return React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"}));case"gridicons-info-outline":return React.createElement("g",null,React.createElement("path",{d:"M13 9h-2V7h2v2zm0 2h-2v6h2v-6zm-1-7c-4.411 0-8 3.589-8 8s3.589 8 8 8 8-3.589 8-8-3.589-8-8-8m0-2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2z"}));case"gridicons-jetpack-search":return React.createElement("g",null,React.createElement("path",{d:"M0 9.257C0 4.15 4.151 0 9.257 0c5.105 0 9.256 4.151 9.256 9.257a9.218 9.218 0 01-2.251 6.045l.034.033h1.053L24 22.01l-1.986 1.989-6.664-6.662v-1.055l-.033-.033a9.218 9.218 0 01-6.06 2.264C4.15 18.513 0 14.362 0 9.257zm4.169 1.537h4.61V1.82l-4.61 8.973zm5.547-3.092v8.974l4.61-8.974h-4.61z"}));case"gridicons-phone":return React.createElement("g",null,React.createElement("path",{d:"M16 2H8c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h8c1.104 0 2-.896 2-2V4c0-1.104-.896-2-2-2zm-3 19h-2v-1h2v1zm3-2H8V5h8v14z"}));case"gridicons-pages":return React.createElement("g",null,React.createElement("path",{d:"M16 8H8V6h8v2zm0 2H8v2h8v-2zm4-6v12l-6 6H6c-1.105 0-2-.895-2-2V4c0-1.105.895-2 2-2h12c1.105 0 2 .895 2 2zm-2 10V4H6v16h6v-4c0-1.105.895-2 2-2h4z"}));case"gridicons-posts":return React.createElement("g",null,React.createElement("path",{d:"M16 19H3v-2h13v2zm5-10H3v2h18V9zM3 5v2h11V5H3zm14 0v2h4V5h-4zm-6 8v2h10v-2H11zm-8 0v2h5v-2H3z"}));case"gridicons-search":return React.createElement("g",null,React.createElement("path",{d:"M21 19l-5.154-5.154C16.574 12.742 17 11.42 17 10c0-3.866-3.134-7-7-7s-7 3.134-7 7 3.134 7 7 7c1.42 0 2.742-.426 3.846-1.154L19 21l2-2zM5 10c0-2.757 2.243-5 5-5s5 2.243 5 5-2.243 5-5 5-5-2.243-5-5z"}));case"gridicons-star-outline":return React.createElement("g",null,React.createElement("path",{d:"M12 6.308l1.176 3.167.347.936.997.042 3.374.14-2.647 2.09-.784.62.27.963.91 3.25-2.813-1.872-.83-.553-.83.552-2.814 1.87.91-3.248.27-.962-.783-.62-2.648-2.092 3.374-.14.996-.04.347-.936L12 6.308M12 2L9.418 8.953 2 9.257l5.822 4.602L5.82 21 12 16.89 18.18 21l-2.002-7.14L22 9.256l-7.418-.305L12 2z"}));case"gridicons-star":return React.createElement("g",null,React.createElement("path",{d:"M12 2l2.582 6.953L22 9.257l-5.822 4.602L18.18 21 12 16.89 5.82 21l2.002-7.14L2 9.256l7.418-.304"}));case"gridicons-tag":return React.createElement("g",null,React.createElement("path",{d:"M20 2.007h-7.087c-.53 0-1.04.21-1.414.586L2.592 11.5c-.78.78-.78 2.046 0 2.827l7.086 7.086c.78.78 2.046.78 2.827 0l8.906-8.906c.376-.374.587-.883.587-1.413V4.007c0-1.105-.895-2-2-2zM17.007 9c-1.105 0-2-.895-2-2s.895-2 2-2 2 .895 2 2-.895 2-2 2z"}));case"gridicons-video":return React.createElement("g",null,React.createElement("path",{d:"M20 4v2h-2V4H6v2H4V4c-1.105 0-2 .895-2 2v12c0 1.105.895 2 2 2v-2h2v2h12v-2h2v2c1.105 0 2-.895 2-2V6c0-1.105-.895-2-2-2zM6 16H4v-3h2v3zm0-5H4V8h2v3zm4 4V9l4.5 3-4.5 3zm10 1h-2v-3h2v3zm0-5h-2V8h2v3z"}));case"gridicons-lock":return React.createElement(React.Fragment,null,React.createElement("g",{id:"lock"},React.createElement("path",{d:"M18,8h-1V7c0-2.757-2.243-5-5-5S7,4.243,7,7v1H6c-1.105,0-2,0.895-2,2v10c0,1.105,0.895,2,2,2h12c1.105,0,2-0.895,2-2V10 C20,8.895,19.105,8,18,8z M9,7c0-1.654,1.346-3,3-3s3,1.346,3,3v1H9V7z M13,15.723V18h-2v-2.277c-0.595-0.346-1-0.984-1-1.723 c0-1.105,0.895-2,2-2s2,0.895,2,2C14,14.738,13.595,15.376,13,15.723z"})),React.createElement("g",{id:"Layer_1"}));case"gridicons-external":return React.createElement("g",null,React.createElement("path",{d:"M19 13v6c0 1.105-.895 2-2 2H5c-1.105 0-2-.895-2-2V7c0-1.105.895-2 2-2h6v2H5v12h12v-6h2zM13 3v2h4.586l-7.793 7.793 1.414 1.414L19 6.414V11h2V3h-8z"}))}}render(){const{size:e=24,className:t=""}=this.props,r=this.props.height||e,n=this.props.width||e,s=this.props.style||{height:r,width:n},c="gridicons-"+this.props.icon,o=(0,a.A)("gridicon",c,t,{"needs-offset":this.needsOffset(c,e)}),i=this.getSVGDescription(c);return React.createElement("svg",{className:o,focusable:this.props.focusable,height:r,onClick:this.props.onClick,style:s,viewBox:"0 0 24 24",width:n,xmlns:"http://www.w3.org/2000/svg","aria-hidden":this.props["aria-hidden"]},i?React.createElement("desc",null,i):null,this.renderIcon(c))}}const o=c},597:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var n=r(6427),a=r(3022),s=r(1609),c=r.n(s),o=r(1112),i=r(1883);r(4803);const l=e=>({"top-end":"top left",top:"top center","top-start":"top right","bottom-end":"bottom left",bottom:"bottom center","bottom-start":"bottom right"}[e]),p=({className:e="",iconClassName:t="",placement:r="bottom-end",animate:p=!0,iconCode:u="info-outline",iconSize:d=18,offset:m=10,title:g,children:h,popoverAnchorStyle:f="icon",forceShow:v=!1,hoverShow:y=!1,wide:b=!1,inline:k=!0,shift:E=!1})=>{const[C,j]=(0,s.useState)(!1),[_,A]=(0,s.useState)(null),w=(0,s.useCallback)((()=>j(!1)),[j]),S=(0,s.useCallback)((e=>{e.preventDefault(),j(!C)}),[C,j]),x={position:l(r),placement:r,animate:p,noArrow:!1,resize:!1,flip:!1,offset:m,focusOnMount:"container",onClose:w,className:"icon-tooltip-container",inline:k,shift:E},N="wrapper"===f,P=(0,a.A)("icon-tooltip-wrapper",e),R={left:N?0:-(62-d/2)+"px"},I=N&&v,O=(0,s.useCallback)((()=>{y&&(_&&(clearTimeout(_),A(null)),j(!0))}),[y,_]),M=(0,s.useCallback)((()=>{if(y){const e=setTimeout((()=>{j(!1),A(null)}),100);A(e)}}),[y]);return c().createElement("div",{className:P,"data-testid":"icon-tooltip_wrapper",onMouseEnter:O,onMouseLeave:M},!N&&c().createElement(o.A,{variant:"link",onMouseDown:S},c().createElement(i.A,{className:t,icon:u,size:d})),c().createElement("div",{className:(0,a.A)("icon-tooltip-helper",{"is-wide":b}),style:R},(I||C)&&c().createElement(n.Popover,x,c().createElement("div",null,g&&c().createElement("div",{className:"icon-tooltip-title"},g),c().createElement("div",{className:"icon-tooltip-content"},h)))))}},7319:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(7723),a=r(3022),s=r(6445);const __=n.__,c=({className:e})=>React.createElement("div",{className:(0,a.A)(e,s.A["indeterminate-progress-bar"]),"aria-label":__("Indeterminate Progress Bar","jetpack-search-pkg")})},8250:(e,t,r)=>{"use strict";r.d(t,{A:()=>y});var n=r(6072),a=r.n(n),s=r(7723),c=r(1113),o=r(3512),i=r(3022),l=r(1609),p=r.n(l),u=r(3924),d=r(1069),m=r(8907),g=(r(4206),r(7142)),h=r(442);const __=s.__,_x=s._x,f=()=>p().createElement(g.A,{logoColor:"#000",showText:!1,height:16,"aria-hidden":"true"}),v=()=>p().createElement(p().Fragment,null,p().createElement(c.A,{icon:o.A,size:16}),p().createElement("span",{className:"jp-dashboard-footer__accessible-external-link"},/* translators: accessibility text */
__("(opens in a new tab)","jetpack-search-pkg"))),y=({moduleName:e=__("Jetpack","jetpack-search-pkg"),className:t,moduleNameHref:r="https://jetpack.com",menu:n,useInternalLinks:s,onAboutClick:c,onPrivacyClick:o,onTermsClick:l,...g})=>{const[y]=(0,h.A)("sm","<="),[b]=(0,h.A)("md","<="),[k]=(0,h.A)("lg",">"),E=(0,d.A)();let C=[{label:_x("About","Link to learn more about Jetpack.","jetpack-search-pkg"),title:__("About Jetpack","jetpack-search-pkg"),href:s?new URL("admin.php?page=jetpack_about",E).href:(0,u.A)("jetpack-about"),target:s?"_self":"_blank",onClick:c},{label:_x("Privacy","Shorthand for Privacy Policy.","jetpack-search-pkg"),title:__("Automattic's Privacy Policy","jetpack-search-pkg"),href:s?new URL("admin.php?page=jetpack#/privacy",E).href:(0,u.A)("a8c-privacy"),target:s?"_self":"_blank",onClick:o},{label:_x("Terms","Shorthand for Terms of Service.","jetpack-search-pkg"),title:__("WordPress.com Terms of Service","jetpack-search-pkg"),href:(0,u.A)("wpcom-tos"),target:"_blank",onClick:l}];n&&(C=[...C,...n]);const j=p().createElement(p().Fragment,null,p().createElement(f,null),e);return p().createElement("footer",a()({className:(0,i.A)("jp-dashboard-footer",{"is-sm":y,"is-md":b,"is-lg":k},t),"aria-label":__("Jetpack","jetpack-search-pkg"),role:"contentinfo"},g),p().createElement("ul",null,p().createElement("li",{className:"jp-dashboard-footer__jp-item"},r?p().createElement("a",{href:r},j):j),C.map((e=>{const t="button"===e.role,r=!t&&"_blank"===e.target;return p().createElement("li",{key:e.label},p().createElement("a",{href:e.href,title:e.title,target:e.target,onClick:e.onClick,onKeyDown:e.onKeyDown,className:(0,i.A)("jp-dashboard-footer__menu-item",{"is-external":r}),role:e.role,rel:r?"noopener noreferrer":void 0,tabIndex:t?0:void 0},e.label,r&&p().createElement(v,null)))})),p().createElement("li",{className:"jp-dashboard-footer__a8c-item"},p().createElement("a",{href:s?new URL("admin.php?page=jetpack_about",E).href:(0,u.A)("a8c-about"),"aria-label":__("An Automattic Airline","jetpack-search-pkg")},p().createElement(m.A,{"aria-hidden":"true"})))))}},7142:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(6072),a=r.n(n),s=r(7723),c=r(3022),o=r(1609),i=r.n(o);const __=s.__,l=({logoColor:e="#069e08",showText:t=!0,className:r,height:n=32,...s})=>{const o=t?"0 0 118 32":"0 0 32 32";return i().createElement("svg",a()({xmlns:"http://www.w3.org/2000/svg",x:"0px",y:"0px",viewBox:o,className:(0,c.A)("jetpack-logo",r),"aria-labelledby":"jetpack-logo-title",height:n},s,{role:"img"}),i().createElement("title",{id:"jetpack-logo-title"},__("Jetpack Logo","jetpack-search-pkg")),i().createElement("path",{fill:e,d:"M16,0C7.2,0,0,7.2,0,16s7.2,16,16,16s16-7.2,16-16S24.8,0,16,0z M15,19H7l8-16V19z M17,29V13h8L17,29z"}),t&&i().createElement(i().Fragment,null,i().createElement("path",{d:"M41.3,26.6c-0.5-0.7-0.9-1.4-1.3-2.1c2.3-1.4,3-2.5,3-4.6V8h-3V6h6v13.4C46,22.8,45,24.8,41.3,26.6z"}),i().createElement("path",{d:"M65,18.4c0,1.1,0.8,1.3,1.4,1.3c0.5,0,2-0.2,2.6-0.4v2.1c-0.9,0.3-2.5,0.5-3.7,0.5c-1.5,0-3.2-0.5-3.2-3.1V12H60v-2h2.1V7.1 H65V10h4v2h-4V18.4z"}),i().createElement("path",{d:"M71,10h3v1.3c1.1-0.8,1.9-1.3,3.3-1.3c2.5,0,4.5,1.8,4.5,5.6s-2.2,6.3-5.8,6.3c-0.9,0-1.3-0.1-2-0.3V28h-3V10z M76.5,12.3 c-0.8,0-1.6,0.4-2.5,1.2v5.9c0.6,0.1,0.9,0.2,1.8,0.2c2,0,3.2-1.3,3.2-3.9C79,13.4,78.1,12.3,76.5,12.3z"}),i().createElement("path",{d:"M93,22h-3v-1.5c-0.9,0.7-1.9,1.5-3.5,1.5c-1.5,0-3.1-1.1-3.1-3.2c0-2.9,2.5-3.4,4.2-3.7l2.4-0.3v-0.3c0-1.5-0.5-2.3-2-2.3 c-0.7,0-2.3,0.5-3.7,1.1L84,11c1.2-0.4,3-1,4.4-1c2.7,0,4.6,1.4,4.6,4.7L93,22z M90,16.4l-2.2,0.4c-0.7,0.1-1.4,0.5-1.4,1.6 c0,0.9,0.5,1.4,1.3,1.4s1.5-0.5,2.3-1V16.4z"}),i().createElement("path",{d:"M104.5,21.3c-1.1,0.4-2.2,0.6-3.5,0.6c-4.2,0-5.9-2.4-5.9-5.9c0-3.7,2.3-6,6.1-6c1.4,0,2.3,0.2,3.2,0.5V13 c-0.8-0.3-2-0.6-3.2-0.6c-1.7,0-3.2,0.9-3.2,3.6c0,2.9,1.5,3.8,3.3,3.8c0.9,0,1.9-0.2,3.2-0.7V21.3z"}),i().createElement("path",{d:"M110,15.2c0.2-0.3,0.2-0.8,3.8-5.2h3.7l-4.6,5.7l5,6.3h-3.7l-4.2-5.8V22h-3V6h3V15.2z"}),i().createElement("path",{d:"M58.5,21.3c-1.5,0.5-2.7,0.6-4.2,0.6c-3.6,0-5.8-1.8-5.8-6c0-3.1,1.9-5.9,5.5-5.9s4.9,2.5,4.9,4.9c0,0.8,0,1.5-0.1,2h-7.3 c0.1,2.5,1.5,2.8,3.6,2.8c1.1,0,2.2-0.3,3.4-0.7C58.5,19,58.5,21.3,58.5,21.3z M56,15c0-1.4-0.5-2.9-2-2.9c-1.4,0-2.3,1.3-2.4,2.9 C51.6,15,56,15,56,15z"})))}},4149:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(6072),a=r.n(n),s=r(7723),c=r(3022),o=r(1609),i=r.n(o);const __=s.__,l=({logoColor:e="#069e08",showText:t=!0,className:r,height:n=42,width:s=330,...o})=>i().createElement("svg",a()({width:s,height:n,xmlns:"http://www.w3.org/2000/svg",x:"0px",y:"0px",viewBox:"0 0 330 42",className:(0,c.A)("jetpack-logo",r),"aria-labelledby":"jetpack-logo-title"},o),i().createElement("desc",{id:"jetpack-logo-title"},__("Jetpack Search Logo","jetpack-search-pkg")),t&&i().createElement(i().Fragment,null,i().createElement("path",{d:"M165.947 13.2919C165.947 13.9029 166.071 14.4103 166.319 14.8142C166.568 15.2078 166.899 15.534 167.314 15.7929C167.738 16.0518 168.225 16.2641 168.774 16.4298C169.333 16.5955 169.913 16.7508 170.514 16.8958C171.322 17.0926 172.114 17.3101 172.89 17.5483C173.678 17.7865 174.377 18.1179 174.988 18.5425C175.599 18.9567 176.091 19.5004 176.463 20.1736C176.847 20.8467 177.038 21.7115 177.038 22.7678C177.038 23.8449 176.836 24.7769 176.432 25.564C176.039 26.3407 175.485 26.9828 174.77 27.4902C174.066 27.9873 173.222 28.355 172.238 28.5932C171.254 28.8417 170.177 28.966 169.007 28.966C168.468 28.966 167.914 28.9298 167.345 28.8573C166.785 28.7951 166.247 28.7071 165.729 28.5932C165.222 28.4793 164.761 28.3498 164.347 28.2048C163.932 28.0598 163.606 27.9148 163.368 27.7699V25.4552C163.741 25.6417 164.16 25.8125 164.626 25.9679C165.092 26.1232 165.574 26.2579 166.071 26.3718C166.568 26.4753 167.07 26.5582 167.578 26.6203C168.085 26.6825 168.562 26.7135 169.007 26.7135C169.732 26.7135 170.421 26.6514 171.073 26.5271C171.736 26.4028 172.316 26.1957 172.813 25.9057C173.31 25.6158 173.703 25.2222 173.993 24.7251C174.283 24.2177 174.428 23.5859 174.428 22.8299C174.428 22.2086 174.299 21.6959 174.04 21.292C173.791 20.8881 173.455 20.5567 173.03 20.2978C172.606 20.0286 172.114 19.8111 171.555 19.6454C171.006 19.4797 170.426 19.3244 169.815 19.1794C169.007 18.993 168.215 18.7858 167.438 18.558C166.672 18.3198 165.983 17.9936 165.372 17.5793C164.771 17.1651 164.284 16.6317 163.912 15.9793C163.549 15.3269 163.368 14.4932 163.368 13.4783C163.368 12.4219 163.554 11.5158 163.927 10.7598C164.3 9.9934 164.807 9.36167 165.45 8.86457C166.092 8.36747 166.842 7.99983 167.702 7.76163C168.562 7.52344 169.478 7.40434 170.452 7.40434C171.56 7.40434 172.59 7.51308 173.543 7.73056C174.506 7.93769 175.386 8.21213 176.184 8.55388V10.8996C175.324 10.5371 174.434 10.2419 173.512 10.0141C172.6 9.78627 171.622 9.66718 170.576 9.65682C169.789 9.65682 169.105 9.74485 168.525 9.92091C167.945 10.0866 167.464 10.33 167.081 10.651C166.697 10.9617 166.413 11.3449 166.226 11.8006C166.04 12.2459 165.947 12.743 165.947 13.2919Z",fill:"black"}),i().createElement("path",{d:"M186.565 15.4977C186.016 15.4977 185.504 15.6065 185.027 15.824C184.561 16.0414 184.152 16.3366 183.8 16.7094C183.448 17.0822 183.163 17.5224 182.946 18.0298C182.728 18.5269 182.593 19.0603 182.542 19.6299H189.998C189.998 19.0499 189.92 18.5114 189.765 18.0143C189.62 17.5068 189.403 17.0667 189.113 16.6939C188.823 16.3211 188.465 16.0311 188.041 15.824C187.616 15.6065 187.124 15.4977 186.565 15.4977ZM187.885 26.8689C188.683 26.8689 189.408 26.8067 190.06 26.6825C190.723 26.5478 191.381 26.3614 192.033 26.1232V28.1271C191.505 28.3861 190.853 28.588 190.076 28.733C189.299 28.8883 188.465 28.966 187.575 28.966C186.518 28.966 185.524 28.8314 184.592 28.5621C183.67 28.2928 182.863 27.8579 182.169 27.2572C181.475 26.6566 180.926 25.8747 180.522 24.9115C180.129 23.9381 179.932 22.7678 179.932 21.4008C179.932 20.0545 180.118 18.8894 180.491 17.9056C180.864 16.9114 181.361 16.0829 181.982 15.4201C182.604 14.7573 183.318 14.2602 184.126 13.9288C184.934 13.5974 185.778 13.4317 186.658 13.4317C187.487 13.4317 188.258 13.5715 188.973 13.8511C189.698 14.1204 190.324 14.5501 190.853 15.1404C191.381 15.7307 191.795 16.4919 192.095 17.424C192.406 18.3561 192.561 19.4745 192.561 20.7794C192.561 20.9037 192.561 21.0124 192.561 21.1056C192.561 21.1885 192.556 21.3852 192.546 21.6959H182.417C182.417 22.6384 182.557 23.441 182.837 24.1038C183.127 24.7562 183.515 25.2895 184.002 25.7038C184.499 26.1077 185.079 26.4028 185.742 26.5893C186.405 26.7757 187.119 26.8689 187.885 26.8689Z",fill:"black"}),i().createElement("path",{d:"M204.791 26.9465H204.729C204.532 27.1951 204.284 27.4436 203.983 27.6922C203.683 27.9304 203.336 28.1479 202.943 28.3446C202.549 28.531 202.119 28.6812 201.653 28.7951C201.187 28.909 200.69 28.966 200.162 28.966C199.447 28.966 198.79 28.8573 198.189 28.6398C197.599 28.4327 197.086 28.1271 196.651 27.7233C196.227 27.309 195.895 26.8067 195.657 26.2164C195.419 25.6261 195.3 24.9581 195.3 24.2125C195.3 23.4772 195.429 22.8196 195.688 22.2396C195.957 21.6597 196.33 21.1678 196.807 20.7639C197.283 20.36 197.852 20.0493 198.515 19.8318C199.188 19.6143 199.929 19.5056 200.737 19.5056C201.503 19.5159 202.212 19.5833 202.865 19.7075C203.517 19.8318 204.097 19.9872 204.605 20.1736H204.667V19.1483C204.667 18.7444 204.641 18.3871 204.589 18.0764C204.537 17.7657 204.449 17.4913 204.325 17.2531C204.056 16.756 203.626 16.3469 203.036 16.0259C202.445 15.6945 201.648 15.5288 200.643 15.5288C199.898 15.5288 199.194 15.6013 198.531 15.7463C197.878 15.8809 197.226 16.0725 196.573 16.3211V14.3016C196.822 14.1773 197.112 14.0634 197.443 13.9598C197.785 13.8459 198.142 13.7527 198.515 13.6802C198.888 13.5974 199.282 13.5352 199.696 13.4938C200.11 13.4524 200.524 13.4317 200.939 13.4317C202.43 13.4317 203.647 13.6957 204.589 14.2239C205.542 14.7521 206.225 15.4511 206.64 16.3211C206.795 16.6525 206.904 17.0201 206.966 17.424C207.038 17.8175 207.075 18.2525 207.075 18.7289V28.5776H205.055L204.791 26.9465ZM204.667 22.0998C204.222 21.9756 203.693 21.8565 203.082 21.7425C202.471 21.6286 201.798 21.5665 201.063 21.5561C200.038 21.5561 199.235 21.7736 198.655 22.2086C198.075 22.6435 197.785 23.3115 197.785 24.2125C197.785 24.6682 197.858 25.0669 198.003 25.4086C198.148 25.7504 198.344 26.0352 198.593 26.263C198.852 26.4805 199.152 26.6462 199.494 26.7601C199.846 26.8637 200.219 26.9155 200.612 26.9155C201.13 26.9155 201.612 26.843 202.057 26.698C202.502 26.5426 202.896 26.3614 203.238 26.1543C203.59 25.9472 203.885 25.74 204.123 25.5329C204.372 25.3258 204.553 25.1653 204.667 25.0514V22.0998Z",fill:"black"}),i().createElement("path",{d:"M219.367 15.7774H219.18C218.611 15.7774 218.051 15.8291 217.503 15.9327C216.954 16.0363 216.436 16.1968 215.949 16.4143C215.473 16.6214 215.038 16.8803 214.644 17.191C214.261 17.5017 213.94 17.8693 213.681 18.2939V28.5776H211.273V13.82H213.137L213.603 16.1657H213.65C213.888 15.7825 214.178 15.4252 214.52 15.0938C214.872 14.7624 215.266 14.4725 215.701 14.2239C216.136 13.9754 216.612 13.7838 217.13 13.6491C217.648 13.5042 218.191 13.4317 218.761 13.4317C218.864 13.4317 218.968 13.4368 219.072 13.4472C219.185 13.4472 219.284 13.4524 219.367 13.4627V15.7774Z",fill:"black"}),i().createElement("path",{d:"M227.791 13.4317C228.505 13.4317 229.189 13.5093 229.841 13.6647C230.494 13.8097 231.053 14.0168 231.519 14.2861V16.29C230.866 16.0414 230.266 15.855 229.717 15.7307C229.168 15.6065 228.609 15.5443 228.039 15.5443C227.439 15.5443 226.843 15.6427 226.253 15.8395C225.662 16.0363 225.134 16.3625 224.668 16.8182C224.202 17.2635 223.824 17.859 223.534 18.6046C223.255 19.3399 223.115 20.2564 223.115 21.3542C223.115 22.1516 223.218 22.8869 223.426 23.5601C223.643 24.2332 223.959 24.8183 224.373 25.3154C224.798 25.8022 225.326 26.1854 225.958 26.465C226.589 26.7342 227.319 26.8689 228.148 26.8689C228.749 26.8689 229.354 26.8067 229.965 26.6825C230.577 26.5478 231.172 26.3614 231.752 26.1232V28.1271C231.576 28.2307 231.343 28.3343 231.053 28.4378C230.773 28.5414 230.463 28.6294 230.121 28.7019C229.779 28.7848 229.417 28.8469 229.033 28.8883C228.65 28.9401 228.267 28.966 227.884 28.966C226.89 28.966 225.947 28.8158 225.057 28.5155C224.176 28.2152 223.405 27.7543 222.742 27.133C222.079 26.5116 221.556 25.7245 221.173 24.7717C220.79 23.819 220.598 22.6901 220.598 21.3852C220.598 20.4118 220.697 19.5367 220.893 18.7599C221.101 17.9832 221.38 17.2997 221.732 16.7094C222.084 16.1191 222.493 15.6168 222.96 15.2026C223.436 14.7883 223.938 14.4518 224.466 14.1928C225.005 13.9236 225.559 13.732 226.129 13.6181C226.698 13.4938 227.252 13.4317 227.791 13.4317Z",fill:"black"}),i().createElement("path",{d:"M237.488 15.8395H237.535C237.804 15.4977 238.105 15.1819 238.436 14.8919C238.767 14.5916 239.135 14.3378 239.539 14.1307C239.943 13.9132 240.378 13.7424 240.844 13.6181C241.31 13.4938 241.812 13.4317 242.351 13.4317C242.91 13.4317 243.469 13.5093 244.028 13.6647C244.588 13.8097 245.105 14.0738 245.582 14.4569C246.058 14.8401 246.447 15.3735 246.747 16.057C247.047 16.7405 247.197 17.6208 247.197 18.6978V28.5776H244.79V19.0862C244.79 17.8952 244.562 16.9994 244.106 16.3987C243.661 15.7981 242.925 15.4977 241.9 15.4977C241.051 15.4977 240.243 15.7307 239.477 16.1968C238.71 16.6525 238.048 17.2842 237.488 18.092V28.5776H235.081V6.08392H237.488V15.8395Z",fill:"black"})),i().createElement("path",{d:"M21.0008 42C32.5991 42 42.0016 32.5975 42.0016 20.9992C42.0016 9.40089 32.5991 0 21.0008 0C9.40245 0 0 9.40245 0 21.0008C0 32.5991 9.40245 42 21.0008 42Z",fill:e}),i().createElement("path",{d:"M22.0427 17.4737V37.8322L32.5431 17.4737H22.0427V17.4737Z",fill:"white"}),i().createElement("path",{d:"M19.9185 24.4874V4.16779L9.4585 24.4874H19.9185Z",fill:"white"}),i().createElement("path",{d:"M54.2236 34.8665C53.6218 33.9443 53.0619 33.0237 52.5005 32.1419C55.4662 30.3379 56.4677 28.8963 56.4677 26.1701V10.4195H52.981V7.41498H60.396V25.3692C60.396 29.9383 59.0741 32.5027 54.2236 34.8665Z",fill:"black"}),i().createElement("path",{d:"M85.2846 24.1266C85.2846 25.6491 86.367 25.8093 87.0885 25.8093C87.8101 25.8093 88.8521 25.5683 89.653 25.3288V28.1343C88.5302 28.4951 87.3685 28.775 85.7651 28.775C83.8414 28.775 81.5973 28.0534 81.5973 24.6865V16.4302H79.5538V13.5843H81.5973V9.37756H85.2846V13.5858H89.9329V16.4317H85.2846V24.1266Z",fill:"black"}),i().createElement("path",{d:"M92.9795 36.2693V13.5454H96.5066V14.9077C97.9093 13.8253 99.4723 13.1442 101.396 13.1442C104.722 13.1442 107.368 15.4691 107.368 20.4783C107.368 25.4485 104.483 28.7346 99.7133 28.7346C98.5516 28.7346 97.6294 28.5744 96.6668 28.3738V36.2288H92.9795V36.2693V36.2693ZM100.433 16.1907C99.351 16.1907 97.9886 16.7117 96.7056 17.8345V25.5698C97.5065 25.73 98.3494 25.8498 99.4707 25.8498C102.076 25.8498 103.559 24.206 103.559 20.7597C103.559 17.5935 102.477 16.1907 100.433 16.1907Z",fill:"black"}),i().createElement("path",{d:"M121.874 28.4142H118.428V26.7704H118.347C117.145 27.6926 115.661 28.6941 113.458 28.6941C111.534 28.6941 109.45 27.2914 109.45 24.4454C109.45 20.6384 112.696 19.9168 114.98 19.5965L118.226 19.1564V18.7162C118.226 16.7117 117.425 16.0709 115.54 16.0709C114.618 16.0709 112.455 16.3509 110.691 17.0725L110.371 14.1068C111.974 13.5454 114.178 13.1457 116.022 13.1457C119.629 13.1457 121.954 14.5889 121.954 18.8764V28.4142H121.874V28.4142ZM118.187 21.4813L115.14 21.9619C114.218 22.0816 113.257 22.643 113.257 24.0053C113.257 25.2075 113.938 25.8886 114.94 25.8886C116.022 25.8886 117.184 25.2479 118.185 24.5263V21.4813H118.187Z",fill:"black"}),i().createElement("path",{d:"M137.104 27.9336C135.581 28.4546 134.219 28.775 132.494 28.775C126.964 28.775 124.759 25.6087 124.759 21.0008C124.759 16.1518 127.805 13.1457 132.734 13.1457C134.577 13.1457 135.699 13.4661 136.942 13.8673V16.9931C135.86 16.5919 134.297 16.1518 132.774 16.1518C130.53 16.1518 128.606 17.3539 128.606 20.8002C128.606 24.6072 130.53 25.7704 132.975 25.7704C134.136 25.7704 135.419 25.5294 137.143 24.8482V27.9336H137.104Z",fill:"black"}),i().createElement("path",{d:"M144.077 19.9588C144.398 19.598 144.639 19.2372 149.287 13.5858H154.096L148.083 20.64L154.655 28.4546H149.847L144.116 21.4005V28.4546H140.43V7.41498H144.118V19.9588H144.077Z",fill:"black"}),i().createElement("path",{d:"M76.7873 27.9337C74.8636 28.5355 73.2198 28.775 71.2961 28.775C66.5668 28.775 63.6416 26.4112 63.6416 20.8795C63.6416 16.8314 66.1267 13.1442 70.8948 13.1442C75.6241 13.1442 77.2678 16.4302 77.2678 19.5561C77.2678 20.598 77.187 21.1594 77.1481 21.7597H67.6103C67.6912 25.0053 69.5341 25.7673 72.2991 25.7673C73.8216 25.7673 75.1839 25.4065 76.7484 24.8451V27.9306H76.7873V27.9337ZM73.422 19.3166C73.422 17.5126 72.8201 15.9497 70.8575 15.9497C69.0147 15.9497 67.8918 17.2715 67.6508 19.3166H73.422V19.3166Z",fill:"black"}))},8509:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(3022),a=r(1609),s=r(7371);const c=Number(s.A.smCols),o=Number(s.A.mdCols),i=Number(s.A.lgCols),l=e=>{const{children:t,tagName:r="div",className:l}=e,p=Math.min(c,"number"==typeof e.sm?e.sm:c),u=Math.min(c,"object"==typeof e.sm?e.sm.start:0),d=Math.min(c,"object"==typeof e.sm?e.sm.end:0),m=Math.min(o,"number"==typeof e.md?e.md:o),g=Math.min(o,"object"==typeof e.md?e.md.start:0),h=Math.min(o,"object"==typeof e.md?e.md.end:0),f=Math.min(i,"number"==typeof e.lg?e.lg:i),v=Math.min(i,"object"==typeof e.lg?e.lg.start:0),y=Math.min(i,"object"==typeof e.lg?e.lg.end:0),b=(0,n.A)(l,{[s.A[`col-sm-${p}`]]:!(u&&d),[s.A[`col-sm-${u}-start`]]:u>0,[s.A[`col-sm-${d}-end`]]:d>0,[s.A[`col-md-${m}`]]:!(g&&h),[s.A[`col-md-${g}-start`]]:g>0,[s.A[`col-md-${h}-end`]]:h>0,[s.A[`col-lg-${f}`]]:!(v&&y),[s.A[`col-lg-${v}-start`]]:v>0,[s.A[`col-lg-${y}-end`]]:y>0});return(0,a.createElement)(r,{className:b},t)}},5918:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(3022),a=r(1609),s=r(2420);const c=({children:e,fluid:t=!1,tagName:r="div",className:c,horizontalGap:o=1,horizontalSpacing:i=1},l)=>{const p=(0,a.useMemo)((()=>{const e=`calc( var(--horizontal-spacing) * ${i} )`;return{paddingTop:e,paddingBottom:e,rowGap:`calc( var(--horizontal-spacing) * ${o} )`}}),[o,i]),u=(0,n.A)(c,s.A.container,{[s.A.fluid]:t});return(0,a.createElement)(r,{className:u,style:p,ref:l},e)},o=(0,a.forwardRef)(c)},442:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(9491),a=r(8403);const s=["sm","md","lg"],c=(e,t)=>{const r=Array.isArray(e)?e:[e],c=Array.isArray(t)?t:[t],[o,i,l]=s,p={sm:(0,n.useMediaQuery)(a.A[o]),md:(0,n.useMediaQuery)(a.A[i]),lg:(0,n.useMediaQuery)(a.A[l])};return r.map(((e,t)=>{const r=c[t];return r?((e,t,r)=>{const n=s.indexOf(e),a=n+1,c=t.includes("=");let o=[];return t.startsWith("<")&&(o=s.slice(0,c?a:n)),t.startsWith(">")&&(o=s.slice(c?n:a)),o?.length?o.some((e=>r[e])):r[e]})(e,r,p):p[e]}))}},1876:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(3022),a=r(1609),s=r.n(a),c=r(6406);const o=({children:e=null,width:t=null,height:r=null,className:a=""})=>s().createElement("div",{className:(0,n.A)(c.A.placeholder,a),style:{width:t,height:r}},e)},7975:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(1437);const a=(e,t={})=>{const r=(0,n.Y)();return new Intl.NumberFormat(r,t).format(e)}},9957:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(7397),a=r(6427),s=r(7723),c=r(1876),o=r(5879);r(4319);const __=s.__,i=e=>-1===e.fraction.indexOf("00"),l=({currencyCode:e="USD",priceDetails:t=__("/month, paid yearly","jetpack-search-pkg"),...r})=>{const l=(0,n.vA)(r.priceBefore,e),p=(0,n.vA)(r.priceAfter,e);return React.createElement("div",{className:"jp-components__pricing-card"},r.icon&&React.createElement("div",{className:"jp-components__pricing-card__icon"},"string"==typeof r.icon?React.createElement("img",{src:r.icon,alt:(0,s.sprintf)(/* translators: placeholder is a product name */
__("Icon for the product %s","jetpack-search-pkg"),r.title)}):r.icon),React.createElement("h1",{className:"jp-components__pricing-card__title"},r.title),React.createElement("div",{className:"jp-components__pricing-card__pricing"},0===r.priceAfter&&React.createElement(c.A,{width:"100%",height:48}),r.priceBefore!==r.priceAfter&&r.priceAfter>0&&React.createElement("div",{className:"jp-components__pricing-card__price-before"},React.createElement("span",{className:"jp-components__pricing-card__currency"},l.symbol),React.createElement("span",{className:"jp-components__pricing-card__price"},l.integer),i(l)&&React.createElement("span",{className:"jp-components__pricing-card__price-decimal"}," ",l.fraction),React.createElement("div",{className:"jp-components__pricing-card__price-strikethrough"})),r.priceAfter>0&&React.createElement(React.Fragment,null,React.createElement("div",{className:"jp-components__pricing-card__price-after"},React.createElement("span",{className:"jp-components__pricing-card__currency"},p.symbol),React.createElement("span",{className:"jp-components__pricing-card__price"},p.integer),i(p)&&React.createElement("span",{className:"jp-components__pricing-card__price-decimal"},p.fraction)),React.createElement("span",{className:"jp-components__pricing-card__price-details"},t))),r.children&&React.createElement("div",{className:"jp-components__pricing-card__extra-content-wrapper"},r.children),r.tosText&&React.createElement("div",{className:"jp-components__pricing-card__tos"},r.tosText),r.ctaText&&React.createElement(React.Fragment,null,!r.tosText&&React.createElement("div",{className:"jp-components__pricing-card__tos"},React.createElement(o.A,{agreeButtonLabel:r.ctaText})),React.createElement("div",{className:"jp-components__pricing-card__cta"},React.createElement(a.Button,{className:"jp-components__pricing-card__button",label:r.ctaText,onClick:r.onCtaClick},r.ctaText))),r.infoText&&React.createElement("div",{className:"jp-components__pricing-card__info"},r.infoText))}},9245:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>C,N0:()=>E,eY:()=>b,i7:()=>k});var n=r(7723),a=r(1113),s=r(3883),c=r(1249),o=r(3022),i=r(1609),l=r.n(i),p=r(597),u=r(442),d=r(5879),m=r(7425),g=r(3142);const __=n.__,h=__("Included","jetpack-search-pkg"),f=__("Not included","jetpack-search-pkg"),v=__("Coming soon","jetpack-search-pkg"),y=(0,i.createContext)(void 0),b=({isIncluded:e=!1,isComingSoon:t=!1,index:r=0,label:d=null,tooltipInfo:b,tooltipTitle:k,tooltipClassName:E=""})=>{const[C]=(0,u.A)("lg"),j=(0,i.useContext)(y)[r],_=t||e,A=j.name,w=j.tooltipInfo,S=j.tooltipTitle,x=b||!C&&w,N=((e,t,r)=>e?{lg:v,
// translators: Name of the current feature
default:(0,n.sprintf)(__("%s coming soon","jetpack-search-pkg"),r)}:{lg:t?h:f,default:t?r:(0,n.sprintf)(/* translators: Name of the current feature */
__("%s not included","jetpack-search-pkg"),r)})(t,e,A),P=C?N.lg:N.default;return l().createElement("div",{className:(0,o.A)(g.A.item,g.A.value)},l().createElement(a.A,{className:(0,o.A)(g.A.icon,_?g.A["icon-check"]:g.A["icon-cross"]),size:32,icon:_?s.A:c.A}),l().createElement(m.Ay,{variant:"body-small"},d||P),x&&l().createElement(p.A,{title:k||S,iconClassName:g.A["popover-icon"],className:(0,o.A)(g.A.popover,E),placement:"bottom-end",iconSize:14,offset:4,wide:Boolean(k&&b)},l().createElement(m.Ay,{variant:"body-small",component:"div"},b||w)))},k=({children:e})=>l().createElement("div",{className:g.A.header},e),E=({primary:e=!1,children:t})=>{let r=0;return l().createElement("div",{className:(0,o.A)(g.A.card,{[g.A["is-primary"]]:e})},i.Children.map(t,(e=>{const t=e;return t.type===b?(r++,(0,i.cloneElement)(t,{index:r-1})):t})))},C=({title:e,items:t,children:r,showIntroOfferDisclaimer:n=!1})=>{const[a]=(0,u.A)("lg");return l().createElement(y.Provider,{value:t},l().createElement("div",{className:(0,o.A)(g.A.container,{[g.A["is-viewport-large"]]:a}),style:{"--rows":t.length+1,"--columns":i.Children.toArray(r).length+1}},l().createElement("div",{className:g.A.table},l().createElement(m.Ay,{variant:"headline-small"},e),a&&t.map(((e,r)=>l().createElement("div",{className:(0,o.A)(g.A.item,{[g.A["last-feature"]]:r===t.length-1}),key:r},l().createElement(m.Ay,{variant:"body-small"},l().createElement("strong",null,e.name)),e.tooltipInfo&&l().createElement(p.A,{title:e.tooltipTitle,iconClassName:g.A["popover-icon"],className:g.A.popover,placement:e.tooltipPlacement?e.tooltipPlacement:"bottom-end",iconSize:14,offset:4,wide:Boolean(e.tooltipTitle&&e.tooltipInfo)},l().createElement(m.Ay,{variant:"body-small"},e.tooltipInfo))))),r)),l().createElement("div",{className:g.A["tos-container"]},l().createElement("div",{className:g.A.tos},n&&l().createElement(m.Ay,{variant:"body-small"},__("Reduced pricing is a limited offer for the first year and renews at regular price.","jetpack-search-pkg")),l().createElement(d.A,{multipleButtons:!0}))))}},489:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(7723),a=r(3022),s=r(7425),c=r(2746),o=r(3407);const __=n.__,i=({price:e,offPrice:t,currency:r="",showNotOffPrice:n=!0,hideDiscountLabel:i=!0,promoLabel:l="",legend:p=__("/month, paid yearly","jetpack-search-pkg"),isNotConvenientPrice:u=!1,hidePriceFraction:d=!1,children:m})=>{if(null==e&&null==t||!r)return null;n=n&&null!=t;const g="number"==typeof e&&"number"==typeof t?Math.floor((e-t)/e*100):0,h=!i&&g&&g>0?g+__("% off","jetpack-search-pkg"):null;return React.createElement(React.Fragment,null,React.createElement("div",{className:o.A.container},React.createElement("div",{className:(0,a.A)(o.A["price-container"],"product-price_container")},React.createElement(c.g,{value:t??e,currency:r,isOff:!u,hidePriceFraction:d}),n&&React.createElement(c.g,{value:e,currency:r,isOff:!1,hidePriceFraction:d}),h&&React.createElement(s.Ay,{className:(0,a.A)(o.A["promo-label"],"product-price_promo_label")},h))),React.createElement("div",{className:o.A.footer},m||React.createElement(s.Ay,{className:(0,a.A)(o.A.legend,"product-price_legend")},p),l&&React.createElement(s.Ay,{className:(0,a.A)(o.A["promo-label"],"product-price_promo_label")},l)))}},2746:(e,t,r)=>{"use strict";r.d(t,{g:()=>o});var n=r(7397),a=r(3022),s=r(7425),c=r(3407);const o=({value:e,currency:t,isOff:r,hidePriceFraction:o})=>{const i=(0,a.A)(c.A.price,"product-price_price",{[c.A["is-not-off-price"]]:!r}),{symbol:l,integer:p,fraction:u}=(0,n.vA)(e,t),d=!o||!u.endsWith("00");return React.createElement(s.Ay,{className:i,variant:"headline-medium",component:"p"},React.createElement(s.Ay,{className:c.A.symbol,component:"sup",variant:"title-medium"},l),p,d&&React.createElement(s.Ay,{component:"sup",variant:"body-small","data-testid":"PriceFraction"},React.createElement("strong",null,u)))}},6051:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(7723),a=r(3022),s=r(1609),c=r.n(s),o=r(7975);r(8249);const __=n.__,i=({totalCount:e,items:t=[],showLegendLabelBeforeCount:r=!1,sortByCount:n,className:i,tableCaption:l,legendTitle:p,recordTypeLabel:u,recordCountLabel:d})=>{const m=(0,s.useMemo)((()=>e||t.reduce(((e,{count:t})=>e+t),0)),[t,e]),g=(0,s.useMemo)((()=>n?[...t].sort(((e,t)=>"ascending"===n?e.count-t.count:t.count-e.count)):t),[t,n]);return c().createElement("div",{className:(0,a.A)("record-meter-bar",i)},c().createElement("div",{className:"record-meter-bar__items","aria-hidden":"true"},g.map((({count:e,label:t,backgroundColor:r})=>{const n=(e/m*100).toPrecision(2);return c().createElement("div",{key:t,style:{backgroundColor:r,flexBasis:`${n}%`}})}))),c().createElement("div",{className:"record-meter-bar__legend","aria-hidden":"true"},p&&c().createElement("div",{className:"record-meter-bar__legend--title"},p),c().createElement("ul",{className:"record-meter-bar__legend--items"},g.map((({count:e,label:t,backgroundColor:n})=>{const a=(0,o.A)(e);return c().createElement("li",{key:t,className:"record-meter-bar__legend--item"},c().createElement("div",{className:"record-meter-bar__legend--item-circle",style:{backgroundColor:n}}),!r&&c().createElement("span",null,c().createElement("span",{className:"record-meter-bar__legend--item-count"},a),c().createElement("span",{className:"record-meter-bar__legend--item-label"},t)),r&&c().createElement("span",null,c().createElement("span",{className:"record-meter-bar__legend--item-label record-meter-bar__legend--item-label-first"},t),c().createElement("span",{className:"record-meter-bar__legend--item-count"},"(",a,")")))})))),c().createElement("table",{className:"screen-reader-text"},c().createElement("caption",null,l||__("Summary of the records","jetpack-search-pkg")),c().createElement("tbody",null,c().createElement("tr",null,c().createElement("th",{scope:"col"},u||__("Record type","jetpack-search-pkg")),c().createElement("th",{scope:"col"},d||__("Record count","jetpack-search-pkg"))),g.map((({label:e,count:t})=>c().createElement("tr",{key:e},c().createElement("td",null,e),c().createElement("td",null,t)))))))}},6461:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(8120),a=r.n(n),s=r(1609),c=r.n(s);r(8325);const o=({color:e="#FFFFFF",className:t="",size:r=20})=>{const n=t+" jp-components-spinner",a={width:r,height:r,fontSize:r,borderTopColor:e},s={borderTopColor:e,borderRightColor:e};return c().createElement("div",{className:n},c().createElement("div",{className:"jp-components-spinner__outer",style:a},c().createElement("div",{className:"jp-components-spinner__inner",style:s})))};o.propTypes={color:a().string,className:a().string,size:a().number};const i=o},5879:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var n=r(6072),a=r.n(n),s=r(6427),c=r(6087),o=r(7723),i=r(3022),l=r(3924),p=r(7425);r(7253);const __=o.__,u=({multipleButtonsLabels:e})=>Array.isArray(e)&&e.length>1?(0,c.createInterpolateElement)((0,o.sprintf)(/* translators: %1$s is button label 1 and %2$s is button label 2 */
__("By clicking <strong>%1$s</strong> or <strong>%2$s</strong>, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-search-pkg"),e[0],e[1]),{strong:React.createElement("strong",null),tosLink:React.createElement(g,{slug:"wpcom-tos"}),shareDetailsLink:React.createElement(g,{slug:"jetpack-support-what-data-does-jetpack-sync"})}):(0,c.createInterpolateElement)(__("By clicking the buttons above, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-search-pkg"),{tosLink:React.createElement(g,{slug:"wpcom-tos"}),shareDetailsLink:React.createElement(g,{slug:"jetpack-support-what-data-does-jetpack-sync"})}),d=({agreeButtonLabel:e})=>(0,c.createInterpolateElement)((0,o.sprintf)(/* translators: %s is a button label */
__("By clicking <strong>%s</strong>, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-search-pkg"),e),{strong:React.createElement("strong",null),tosLink:React.createElement(g,{slug:"wpcom-tos"}),shareDetailsLink:React.createElement(g,{slug:"jetpack-support-what-data-does-jetpack-sync"})}),m=()=>(0,c.createInterpolateElement)(__("By continuing you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site’s data</shareDetailsLink> with us. We’ll check if that email is linked to an existing WordPress.com account or create a new one instantly.","jetpack-search-pkg"),{tosLink:React.createElement(g,{slug:"wpcom-tos"}),shareDetailsLink:React.createElement(g,{slug:"jetpack-support-what-data-does-jetpack-sync"})}),g=({slug:e,children:t})=>React.createElement(s.ExternalLink,{className:"terms-of-service__link",href:(0,l.A)(e)},t),h=({className:e,multipleButtons:t,agreeButtonLabel:r,isTextOnly:n,...s})=>React.createElement(p.Ay,a()({className:(0,i.A)(e,"terms-of-service")},s),n?React.createElement(m,null):t?React.createElement(u,{multipleButtonsLabels:t}):React.createElement(d,{agreeButtonLabel:r}))},110:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n,Z:()=>a});const n={"headline-medium":"h1","headline-small":"h2","headline-small-regular":"h2","title-medium":"h3","title-medium-semi-bold":"h3","title-small":"h4",body:"p","body-small":"p","body-extra-small":"p","body-extra-small-bold":"p",label:"p"},a=["mt","mr","mb","ml","mx","my","m","pt","pr","pb","pl","px","py","p"]},7425:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>u});var n=r(6072),a=r.n(n),s=r(3022),c=r(1609),o=r.n(c),i=r(110),l=r(4495);const p=(0,c.forwardRef)((({variant:e="body",children:t,component:r,className:n,...p},u)=>{const d=r||i.Q[e]||"span",m=(0,c.useMemo)((()=>i.Z.reduce(((e,t)=>(void 0!==p[t]&&(e+=l.A[`${t}-${p[t]}`]+" ",delete p[t]),e)),"")),[p]);return o().createElement(d,a()({className:(0,s.A)(l.A.reset,l.A[e],n,m)},p,{ref:u}),t)}));p.displayName="Text";const u=p},723:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>d});var n=r(1609),a=r.n(n),s=r(9422);const c={"--font-headline-medium":"48px","--font-headline-small":"36px","--font-title-medium":"24px","--font-title-small":"20px","--font-body":"16px","--font-body-small":"14px","--font-body-extra-small":"12px","--font-title-large":"var(--font-headline-small)","--font-label":"var(--font-body-extra-small)"},o={"--jp-black":"#000000","--jp-black-80":"#2c3338","--jp-white":"#ffffff","--jp-white-off":"#f9f9f6","--jp-gray":"#dcdcde","--jp-gray-0":"#F6F7F7","--jp-gray-5":"var(--jp-gray)","--jp-gray-10":"#C3C4C7","--jp-gray-20":"#A7AAAD","--jp-gray-40":"#787C82","--jp-gray-50":"#646970","--jp-gray-60":"#50575E","--jp-gray-70":"#3C434A","--jp-gray-80":"#2C3338","--jp-gray-90":"#1d2327","--jp-gray-off":"#e2e2df","--jp-red-0":"#F7EBEC","--jp-red-5":"#FACFD2","--jp-red-40":"#E65054","--jp-red-50":"#D63638","--jp-red-60":"#B32D2E","--jp-red-70":"#8A2424","--jp-red-80":"#691C1C","--jp-red":"#d63639","--jp-yellow-5":"#F5E6B3","--jp-yellow-10":"#F2CF75","--jp-yellow-20":"#F0C930","--jp-yellow-30":"#DEB100","--jp-yellow-40":"#C08C00","--jp-yellow-50":"#9D6E00","--jp-yellow-60":"#7D5600","--jp-blue-20":"#68B3E8","--jp-blue-40":"#1689DB","--jp-pink":"#C9356E","--jp-green-0":"#f0f2eb","--jp-green-5":"#d0e6b8","--jp-green-10":"#9dd977","--jp-green-20":"#64ca43","--jp-green-30":"#2fb41f","--jp-green-40":"#069e08","--jp-green-50":"#008710","--jp-green-60":"#007117","--jp-green-70":"#005b18","--jp-green-80":"#004515","--jp-green-90":"#003010","--jp-green-100":"#001c09","--jp-green":"#069e08","--jp-green-primary":"var( --jp-green-40 )","--jp-green-secondary":"var( --jp-green-30 )"},i={"--jp-border-radius":"4px","--jp-menu-border-height":"1px","--jp-underline-thickness":"2px"},l={"--spacing-base":"8px"},p={},u=(e,t,r)=>{const n={...c,...o,...i,...l};for(const t in n)e.style.setProperty(t,n[t]);r&&e.classList.add(s.A.global),t&&(p[t]={provided:!0,root:e})},d=({children:e=null,targetDom:t,id:r,withGlobalStyles:s=!0})=>{const c=(0,n.useRef)(),o=p?.[r]?.provided;return(0,n.useLayoutEffect)((()=>{if(!o)return t?u(t,r,s):void(c?.current&&u(c.current,r,s))}),[t,c,o,r,s]),t?a().createElement(a().Fragment,null,e):a().createElement("div",{ref:c},e)}},1437:(e,t,r)=>{"use strict";r.d(t,{Y:()=>a});var n=r(8443);const a=()=>{const{l10n:{locale:e}}=(0,n.getSettings)();if(e)return(e=>{const t=e.match(/^([a-z]{2,3})(_[a-z]{2}|_[a-z][a-z0-9]{4,7})?(?:_.*)?$/i);return t?`${t[1]}${t[2]?t[2]:""}`.replace("_","-"):"en-US"})(e);return window?.window?.navigator?.language??"en-US"}},8214:(e,t,r)=>{"use strict";function n(e,t,r,n){const a=new URL("https://wordpress.com/checkout/"),s=new URL(`${a}${t}/${e}`);return s.searchParams.set("redirect_to",r),n||s.searchParams.set("unlinked","1"),s.searchParams.set("site",t),s.toString()}r.d(t,{A:()=>n})},1069:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(7999);function a(){return(0,n.getScriptData)()?.site?.admin_url||window.Initial_State?.adminUrl||window.Jetpack_Editor_Initial_State?.adminUrl||window?.myJetpackInitialState?.adminUrl||null}},3924:(e,t,r)=>{"use strict";function n(e,t={}){const r={};let n;if("undefined"!=typeof window&&(n=window?.JP_CONNECTION_INITIAL_STATE?.calypsoEnv),0===e.search("https://")){const t=new URL(e);e=`https://${t.host}${t.pathname}`,r.url=encodeURIComponent(e)}else r.source=encodeURIComponent(e);for(const e in t)r[e]=encodeURIComponent(t[e]);!Object.keys(r).includes("site")&&"undefined"!=typeof jetpack_redirects&&Object.hasOwn(jetpack_redirects,"currentSiteRawUrl")&&(r.site=jetpack_redirects.currentBlogID??jetpack_redirects.currentSiteRawUrl),n&&(r.calypso_env=n);return"https://jetpack.com/redirect/?"+Object.keys(r).map((e=>e+"="+r[e])).join("&")}r.d(t,{A:()=>n})},6439:(e,t,r)=>{let n={};try{n=r(516)}catch{console.error("jetpackConfig is missing in your webpack config file. See @automattic/jetpack-config"),n={missingConfig:!0}}const a=e=>Object.hasOwn(n,e);e.exports={jetpackConfigHas:a,jetpackConfigGet:e=>{if(!a(e))throw'This app requires the "'+e+'" Jetpack Config to be defined in your webpack configuration file. See details in @automattic/jetpack-config package docs.';return n[e]}}},5985:(e,t,r)=>{"use strict";r.d(t,{pg:()=>n.A});r(2810),r(4815);var n=r(1409);r(2034),r(5595),r(3265),r(3489),r(7119),r(8406),r(6923),r(335),r(8290),r(9061),r(5929),r(5765)},5765:(e,t,r)=>{"use strict";r(8490)},2810:(e,t,r)=>{"use strict";r(8377).colors["Jetpack Green 40"]},335:(e,t,r)=>{"use strict";r(6087)},4815:(e,t,r)=>{"use strict";r(7999)},3489:(e,t,r)=>{"use strict";var n=r(372);r(9384),r(6087);const{tracks:a}=n.A,{recordEvent:s}=a},7119:(e,t,r)=>{"use strict";r(7143),r(6087),r(8468)},6923:(e,t,r)=>{"use strict";r(7143),r(6087),r(8290)},8406:(e,t,r)=>{"use strict";r(6087)},5929:(e,t,r)=>{"use strict";r(7143),r(2619),r(3265),r(7119)},9520:(e,t,r)=>{"use strict";var n=r(6941),a=r.n(n);window,a()("shared-extension-utils:connection")},9061:(e,t,r)=>{"use strict";r(9520)},7105:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>u,E9:()=>p});var n=r(7143),a=r(2634),s=r(4478),c=r(8290);const o="SET_JETPACK_MODULES";function i(e){return p({isLoading:e})}function l(e,t){return{type:"SET_MODULE_UPDATING",name:e,isUpdating:t}}function p(e){return{type:o,options:e}}const u={updateJetpackModuleStatus:function*(e){try{yield l(e.name,!0),yield(0,s.sB)(e);const t=yield(0,s.wz)();return yield p({data:t}),!0}catch{const e=(0,n.select)(c.F).getJetpackModules();return yield p(e),!1}finally{yield l(e.name,!1)}},setJetpackModules:p,fetchModules:function*(){if((0,a.Sy)())return!0;try{yield i(!0);const e=yield(0,s.wz)();return yield p({data:e}),!0}catch{const e=(0,n.select)(c.F).getJetpackModules();return yield p(e),!1}finally{yield i(!1)}}}},4478:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>l,sB:()=>i,wz:()=>o});var n=r(1455),a=r.n(n);const s="FETCH_JETPACK_MODULES",c="UPDATE_JETPACK_MODULE_STATUS",o=()=>({type:s}),i=e=>({type:c,settings:e}),l={[s]:function(){return a()({path:"/jetpack/v4/module/all",method:"GET"})},[c]:function({settings:e}){return a()({path:`/jetpack/v4/module/${e.name}/active`,method:"POST",data:{active:e.active}})}}},8290:(e,t,r)=>{"use strict";r.d(t,{F:()=>l});var n=r(7143),a=r(7105),s=r(4478),c=r(8862),o=r(2701),i=r(1640);const l="jetpack-modules",p=(0,n.createReduxStore)(l,{reducer:c.A,actions:a.Ay,controls:s.Ay,resolvers:o.A,selectors:i.A});(0,n.register)(p);const u=window?.Initial_State?.getModules||window?.Jetpack_Editor_Initial_State?.modules||null;null!==u&&(0,n.dispatch)(l).setJetpackModules({data:{...u}})},8862:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});const n={isLoading:!1,isUpdating:{},data:{}},a=(e=n,t)=>{switch(t.type){case"SET_JETPACK_MODULES":return{...e,...t.options};case"SET_MODULE_UPDATING":return{...e,isUpdating:{...e.isUpdating,[t.name]:t.isUpdating}}}return e}},2701:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(7105),a=r(4478);const s={getJetpackModules:function*(){try{const e=yield(0,a.wz)();if(e)return(0,n.E9)({data:e})}catch(e){console.error(e)}}}},1640:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(2634);const a={getJetpackModules:e=>e.data,isModuleActive:(e,t)=>(0,n.Sy)()||(e?.data?.[t]?.activated??!1),areModulesLoading:e=>e.isLoading??!1,isModuleUpdating:(e,t)=>e?.isUpdating?.[t]??!1}},3265:(e,t,r)=>{"use strict";var n=r(7723);r(3832),r(8468),r(4815);const __=n.__;__("Upgrade your plan to use video covers","jetpack-search-pkg"),__("Upgrade your plan to upload audio","jetpack-search-pkg")},2034:(e,t,r)=>{"use strict";r(2279)},1409:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(7999);function a(){const{connectedPlugins:e,connectionStatus:t}=(0,n.getScriptData)()?.connection??{};return t?.isActive&&e?.some((({slug:e})=>"jetpack"===e))}},2634:(e,t,r)=>{"use strict";function n(){return"object"==typeof window&&"string"==typeof window._currentSiteType?window._currentSiteType:null}function a(){return"simple"===n()}r.d(t,{Sy:()=>a})},5595:(e,t,r)=>{"use strict";r(6072),r(9491)},468:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var n=r(3022),a=r(6418),s=r.n(a),c=r(8120),o=r.n(c),i=r(1609),l=r.n(i);class p extends l().Component{static displayName="Button";static propTypes={disabled:o().bool,compact:o().bool,primary:o().bool,scary:o().bool,type:o().string,href:o().string,onClick:o().func,borderless:o().bool,className:o().string};static defaultProps={disabled:!1,type:"button",onClick:s(),borderless:!1};render(){const e=this.props.href?"a":"button",{primary:t,compact:r,scary:a,borderless:s,className:c,...o}=this.props,i=(0,n.A)({"dops-button":!0,"is-compact":r,"is-primary":t,"is-scary":a,"is-borderless":s});return o.className=(0,n.A)(c,i),l().createElement(e,o,this.props.children)}}},7982:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});r(9060);var n=r(1883),a=r(3022),s=r(9367),c=r.n(s),o=r(2903),i=r.n(o),l=r(8120),p=r.n(l),u=r(1609),d=r.n(u);class m extends d().Component{static propTypes={title:p().any,vertical:p().any,style:p().object,className:p().string,device:p().oneOf(["desktop","tablet","phone"])};static defaultProps={vertical:null};render(){return d().createElement("div",{className:(0,a.A)("dops-card-section",this.props.className),style:this.props.style},this.props.title?this._renderWithTitle():this.props.children)}_renderWithTitle=()=>{const e="dops-card-section-orient-"+(this.props.vertical?"vertical":"horizontal");return d().createElement("div",{className:e},d().createElement("h4",{className:"dops-card-section-label"},this.props.title),d().createElement("div",{className:"dops-card-section-content"},this.props.children))}}class g extends d().Component{render(){return d().createElement("div",{className:"dops-card-footer"},this.props.children)}}class h extends d().Component{static propTypes={meta:p().any,icon:p().string,iconLabel:p().any,iconColor:p().string,style:p().object,className:p().string,href:p().string,onClick:p().func,title:p().string,tagName:p().string,target:p().string,compact:p().bool,children:p().node};static defaultProps={iconColor:"#787878",className:"",tagName:"div",onClick:()=>{}};render(){const e=(0,a.A)("dops-card",this.props.className,{"is-card-link":!!this.props.href,"is-compact":this.props.compact}),t=["compact","tagName","meta","iconColor"];let r,s;return this.props.href?r=d().createElement(n.A,{className:"dops-card__link-indicator",icon:this.props.target?"external":"chevron-right"}):t.push("href","target"),this.props.title&&(s=d().createElement("h2",{className:"dops-card-title"},this.props.title,this.props.meta&&d().createElement("span",{className:"dops-card-meta"},this.props.meta),(this.props.icon||this.props.iconLabel)&&this._renderIcon())),d().createElement(this.props.href?"a":this.props.tagName,c()(i()(this.props,t),{className:e}),r,s,this.props.children)}_renderIcon=()=>d().createElement("span",{className:"dops-card-icon",style:{color:this.props.iconColor}},this.props.icon&&d().createElement(n.A,{icon:this.props.icon,style:{backgroundColor:this.props.iconColor}}),this.props.iconLabel)}h.Section=m,h.Footer=g;const f=h},496:(e,t,r)=>{"use strict";r.d(t,{A:()=>g});var n=r(372),a=r(5932),s=r(5985),c=r(7143),o=r(684),i=r(5214),l=r(5432),p=r(665),u=r(1609),d=r.n(u),m=r(8228);function g(){const{isFullyConnected:e}=(0,p.A)(),t=(0,c.useSelect)((e=>e(m.a).isNewPricing202208()),[]);return(0,u.useMemo)((()=>{const e=(0,c.select)(m.a).getAPIRootUrl(),t=(0,c.select)(m.a).getWpcomOriginApiUrl(),r=(0,c.select)(m.a).getAPINonce();e&&a.Ay.setApiRoot(e),t&&a.Ay.setWpcomOriginApiUrl(t),r&&a.Ay.setApiNonce(r),(()=>{const e=(0,c.select)(m.a).getWpcomUser(),t=(0,c.select)(m.a).getBlogId();e&&n.A.initialize(e.ID,e.login,{blog_id:t})})(),n.A.tracks.recordEvent("jetpack_search_admin_page_view",{current_version:(0,c.select)(m.a).getVersion()})}),[]),d().createElement(d().Fragment,null,!e&&!t&&d().createElement(o.A,null),(e||t)&&d().createElement(h,null))}function h(){const{isFullyConnected:e}=(0,p.A)();return d().createElement(d().Fragment,null,e&&d().createElement(f,null),!e&&d().createElement(l.A,null))}function f(){(0,c.useSelect)((e=>e(m.a).getSearchPlanInfo()),[]);const e=(0,c.useSelect)((e=>e(m.a).supportsSearch())),t=(0,c.useSelect)((e=>e(m.a).isResolving("getSearchPlanInfo")||!e(m.a).hasStartedResolution("getSearchPlanInfo")));return d().createElement(d().Fragment,null,e&&d().createElement(i.A,{isLoading:t,useInternalLinks:(0,s.pg)()}),!e&&d().createElement(l.A,{isLoading:t}))}},2090:(e,t,r)=>{"use strict";r.d(t,{A:()=>v,Z:()=>m});var n=r(7975),a=r(723),s=r(4976),c=r(597),o=r(1112),i=r(1883),l=r(7723),p=r(1609),u=r.n(p);const __=l.__,d=__("Unlimited","jetpack-search-pkg"),m=e=>e>1e18?d:(0,n.A)(e),g=(e,t)=>{e.preventDefault(),t()},h=({localizedMessage:e,iconClickedCallback:t})=>{const r="function"==typeof t;return u().createElement("div",{className:"donut-info-primary"},e," ",r&&u().createElement("a",{href:"#",className:"info-icon-wrapper",onClick:e=>{g(e,t)}},u().createElement(i.A,{className:"",icon:"info-outline",size:16})))},f=({localizedMessage:e,linkClickedCallback:t})=>{const r="function"==typeof t;return u().createElement("div",{className:"donut-info-secondary"},e," ",r&&u().createElement("a",{href:"#",className:"info-link",onClick:e=>{g(e,t)}},__("View details","jetpack-search-pkg")))},v=({current:e=0,limit:t=1,title:r,tooltip:n,iconClickedCallback:i,linkClickedCallback:l})=>{const p=t>1e18,g=p?1:e,v=p?1:t,y=((e,t)=>t>1e18?0===e?d:`${m(e)} / ${d}`:`${m(e)} / ${m(t)}`)(e,t),b={popoverAnchorStyle:"wrapper",title:n.title,placement:"top",forceShow:n.forceShow};return u().createElement(a.Ay,null,u().createElement("div",{className:"donut-meter-container"},u().createElement("div",{className:"donut-meter-wrapper"},u().createElement(s.A,{segmentCount:g,totalCount:v,useAdaptiveColors:!p}),u().createElement("div",{className:"upgrade-tooltip-shadow-anchor"},u().createElement(c.A,b,u().createElement(u().Fragment,null,u().createElement("div",null,n.content),u().createElement("div",{className:"upgrade-tooltip-actions"},u().createElement("span",null,n.section),u().createElement(o.A,{onClick:n.goToNext},n.next)))))),u().createElement("div",{className:"donut-info-wrapper"},u().createElement(h,{localizedMessage:r,iconClickedCallback:i}),u().createElement(f,{localizedMessage:y,linkClickedCallback:l}))))}},4092:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(6072),a=r.n(n),s=r(3022),c=r(1363),o=r(2903),i=r.n(o),l=r(1609),p=r.n(l);class u extends p().Component{static displayName="CompactFormToggle";render(){return p().createElement(c.A,a()({},i()(this.props,"className"),{className:(0,s.A)(this.props.className,"is-compact")}),this.props.children)}}},1363:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(3022),a=r(8120),s=r.n(a),c=r(1609),o=r.n(c);class i extends c.Component{static propTypes={onChange:s().func,onKeyDown:s().func,checked:s().bool,disabled:s().bool,id:s().string,className:s().string,toggling:s().bool,"aria-label":s().string,children:s().node,disabledReason:s().node,switchClassNames:s().string,labelClassNames:s().string};static defaultProps={checked:!1,disabled:!1,onKeyDown:()=>{},onChange:()=>{},disabledReason:""};state={};static idNum=0;constructor(){super(...arguments),this.onKeyDown=this.onKeyDown.bind(this),this.onClick=this.onClick.bind(this),this.onLabelClick=this.onLabelClick.bind(this)}UNSAFE_componentWillMount(){this.id=this.constructor.idNum++}onKeyDown(e){this.props.disabled||("Enter"!==e.key&&" "!==e.key||(e.preventDefault(),this.props.onChange()),this.props.onKeyDown(e))}onClick(){this.props.disabled||this.props.onChange()}onLabelClick(e){if(this.props.disabled)return;const t=e.target.nodeName.toLowerCase();"a"!==t&&"input"!==t&&"select"!==t&&(e.preventDefault(),this.props.onChange())}render(){const e=this.props.id||"toggle-"+this.id,t=(0,n.A)("form-toggle",this.props.className,{"is-toggling":this.props.toggling});return o().createElement(c.Fragment,null,o().createElement("div",{className:(0,n.A)("form-toggle__switch-container",this.props.switchClassNames)},o().createElement("input",{className:t,type:"checkbox",checked:this.props.checked,readOnly:!0,disabled:this.props.disabled}),o().createElement("span",{className:(0,n.A)("form-toggle__switch",this.props.switchClassNames),disabled:this.props.disabled,id:e,onClick:this.onClick,onKeyDown:this.onKeyDown,role:"checkbox","aria-checked":this.props.checked,"aria-label":this.props["aria-label"],tabIndex:this.props.disabled?-1:0})),o().createElement("label",{className:(0,n.A)("form-toggle__label",this.props.labelClassNames),htmlFor:e},o().createElement("span",{className:(0,n.A)("form-toggle__label-content",this.props.labelClassNames),onClick:this.onLabelClick},this.props.children)))}}},2121:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(8420),a=r(8423),s=r(1609),c=r.n(s);function o(e={handleLocalNoticeDismissClick:null,notices:Object.freeze([])}){const t=e.notices.map((function(t){const r=t=>()=>{t&&e.handleLocalNoticeDismissClick(t.id)};return c().createElement(n.A,{key:"notice-"+t.id,status:t.status,duration:t.duration||null,text:t.text,isCompact:t.isCompact,onDismissClick:r(t),showDismiss:t.showDismiss},t.button&&c().createElement(a.A,{href:t.href,onClick:r(t)},t.button))}));return t.length?c().createElement("div",{id:e.id,className:"global-notices"},t):null}},6450:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>h,Er:()=>p,HI:()=>m,I4:()=>o,Op:()=>c,hn:()=>g,tg:()=>u});var n=r(7723),a=r(4436),s=r.n(a);const __=n.__,c="CREATE_NOTICE",o="REMOVE_NOTICE";function i(e,t,r={}){const n={id:r.id||s()(),duration:r.duration??2e3,showDismiss:"boolean"!=typeof r.showDismiss||r.showDismiss,isPersistent:r.isPersistent||!1,displayOnNextPage:r.displayOnNextPage||!1,status:e,text:t};return{type:c,notice:n}}function l(e){return{type:o,notice:{id:e}}}const p=i.bind(null,"is-success"),u=i.bind(null,"is-error"),d=(i.bind(null,"is-info"),i.bind(null,"is-warning")),m=(e=__("Updating settings…","jetpack-search-pkg"))=>i("is-info",e,{duration:3e4,id:"search-updating-settings"}),g=()=>l("search-updating-settings"),h={createNotice:i,removeNotice:l,successNotice:p,errorNotice:u,warningNotice:d,updatingNotice:m,removeUpdatingNotice:g}},1473:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(6450);const a=(e={notices:[]},t)=>{switch(t.type){case n.Op:return{...e,notices:[...e.notices,t.notice]};case n.I4:return{...e,notices:e.notices.filter((e=>e.id!==t.notice.id))}}return e}},8179:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={getNotices:e=>e.notices.notices??[]}},2948:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(6461),a=r(1609),s=r.n(a);function c(){return s().createElement(n.A,{className:"jp-search-dashboard-page-loading-spinner",color:"#000",size:32})}},3030:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(1609),a=r.n(n),s=r(7543),c=r(8160);function o({supportsInstantSearch:e=!0,supportsOnlyClassicSearch:t=!1}){const r=t&&!e;return a().createElement(n.Fragment,null,r&&a().createElement(c.A,null),!r&&a().createElement(s.A,null))}},7543:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(1883),a=r(7723),s=r(1609),c=r.n(s),o=r(7717);const __=a.__;function i(){return c().createElement("div",{className:"jp-mocked-instant-search","aria-hidden":"true"},c().createElement("div",{className:"jp-mocked-instant-search__search-controls"},c().createElement("div",{className:"jp-mocked-instant-search__search-icon"},c().createElement(n.A,{icon:"search",size:24})),c().createElement("div",{className:"jp-mocked-instant-search__search-mock-input"},c().createElement(o.A,{style:{height:"50px",width:"80%",maxWidth:"212px"}})),c().createElement("div",{className:"jp-mocked-instant-search__close-button"},c().createElement(n.A,{icon:"cross",size:24}))),c().createElement("div",{className:"jp-mocked-instant-search__search-results"},c().createElement("div",{className:"jp-mocked-instant-search__search-results-primary"},c().createElement("div",{className:"jp-mocked-instant-search__search-results-header"},c().createElement("div",{className:"jp-mocked-instant-search__result-statistics"},/* translators: %s is replaced with the number of search results */ /* translators: %s is replaced with the number of search results */
(0,a.sprintf)(__("Found %s results","jetpack-search-pkg"),"27")),c().createElement("div",{className:"jp-mocked-instant-search__result-sort-list"},c().createElement("span",{className:"jp-mocked-instant-search__result-sort-selected"},__("Relevance","jetpack-search-pkg")),c().createElement("span",null,"·"),c().createElement("span",null,__("Newest","jetpack-search-pkg")),c().createElement("span",null,"·"),c().createElement("span",null,__("Oldest","jetpack-search-pkg")))),c().createElement("div",{className:"jp-mocked-instant-search__search-results-content"},c().createElement(p,null),c().createElement(p,null),c().createElement(p,null))),c().createElement("div",{className:"jp-mocked-instant-search__search-results-secondary"},c().createElement("div",{className:"jp-mocked-instant-search__search-filter-header"},__("Filter options","jetpack-search-pkg")),c().createElement("div",{className:"jp-mocked-instant-search__search-filter-list"},c().createElement(l,null),c().createElement(l,null)))))}const l=()=>{const e=(0,s.useId)();return c().createElement("div",{className:"jp-mocked-instant-search__search-filter"},c().createElement("label",{htmlFor:e},c().createElement("input",{id:e,type:"checkbox",disabled:"disabled"})," ",c().createElement(o.A,{style:{width:"30%"}})))},p=()=>c().createElement("div",{className:"jp-mocked-instant-search__search-result"},c().createElement(o.A,{style:{height:"2.5em",width:"50%",maxWidth:"200px",margin:"0.1em 0.1em 1em 0.1em"}}),c().createElement(o.A,{style:{height:"1em",width:"90%",margin:"0.1em"}}),c().createElement(o.A,{style:{height:"1em",width:"70%",margin:"0.1em"}}))},8160:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(1883),a=r(1609),s=r.n(a),c=r(7717);function o(){return s().createElement("div",{className:"jp-mocked-legacy-search","aria-hidden":"true"},s().createElement("div",{className:"jp-mocked-legacy-search__search-controls"},s().createElement("div",{className:"jp-mocked-legacy-search__search-icon"},s().createElement(n.A,{icon:"search",size:24})),s().createElement("div",{className:"jp-mocked-legacy-search__search-input"},s().createElement(c.A,{style:{height:"50px",width:"80%",maxWidth:"212px"}}))))}},7717:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(1609),a=r.n(n);const s=e=>a().createElement("div",{className:"jp-search-dashboard__text-row-placeholder",style:{display:"inline-block",borderRadius:"10px",maxHeight:"1.5em",width:"100%",height:"1em",backgroundColor:"#E9EFF3",...e.style}})},9624:(e,t,r)=>{"use strict";r.d(t,{A:()=>k});var n=r(372),a=r(8214),s=r(9384),c=r(7143),o=r(6087),i=r(7723),l=r(3022),p=r(468),u=r(7982),d=r(4092),m=r(1549),g=r(1609),h=r.n(g),f=r(8228);const __=i.__,v=__("Jetpack Search is an incredibly powerful and customizable replacement for the search capability built into WordPress that helps your visitors find the right content.","jetpack-search-pkg"),y=__("Instant search uses a dynamic overlay for lightning-fast searching, sorting, and filtering without reloading the page.","jetpack-search-pkg"),b="admin.php?page=jetpack-search";function k({siteAdminUrl:e,updateOptions:t,domain:r,isDisabledFromOverLimit:o,isSavingEitherOption:i,isModuleEnabled:p,isInstantSearchEnabled:d,isInstantSearchPromotionActive:m,supportsOnlyClassicSearch:v,supportsSearch:y,supportsInstantSearch:k,isTogglingModule:C,isTogglingInstantSearch:_}){const{isUserConnected:A}=(0,s.useConnection)({redirectUri:"admin.php?page=jetpack-search",from:"jetpack-search"}),w=(0,c.useSelect)((e=>e(f.a).isWpcom()),[]),S=(0,a.A)("jetpack_search_free",r,"admin.php?page=jetpack-search",A||w),x=(0,g.useCallback)((()=>{if(o)return;const e={module_active:!p};d!==!p&&(e.instant_search_enabled=!p&&k),t(e),n.A.tracks.recordEvent("jetpack_search_module_toggle",e)}),[k,p,t,d,o]),N=(0,g.useCallback)((()=>{if(o)return;const e={instant_search_enabled:k&&!d};e.instant_search_enabled&&(e.module_active=!0),t(e),n.A.tracks.recordEvent("jetpack_search_instant_toggle",e)}),[k,d,t,o]);return h().createElement("div",{className:(0,l.A)({"jp-form-settings-group jp-form-search-settings-group":!0,"jp-form-search-settings-group--disabled":o})},h().createElement(u.A,{className:(0,l.A)({"jp-form-has-child":!0})},h().createElement("div",{className:"jp-form-search-settings-group-inside"},h().createElement(j,{isModuleEnabled:p,isSavingEitherOption:i,isTogglingModule:C,supportsSearch:y,toggleSearchModule:x,isDisabledFromOverLimit:o}),h().createElement(E,{isInstantSearchEnabled:d,isInstantSearchPromotionActive:m,isModuleEnabled:p,isSavingEitherOption:i,isTogglingInstantSearch:_,returnUrl:e+b,supportsInstantSearch:k,supportsOnlyClassicSearch:v,toggleInstantSearch:N,upgradeUrl:S,isDisabledFromOverLimit:o}))))}const E=({isInstantSearchEnabled:e,isInstantSearchPromotionActive:t,isSavingEitherOption:r,isModuleEnabled:n,isTogglingInstantSearch:a,returnUrl:s,supportsInstantSearch:c,supportsOnlyClassicSearch:i,toggleInstantSearch:l,upgradeUrl:p,isDisabledFromOverLimit:u})=>{const f=n&&e&&c&&!u,v=r||!c||u,b=r||!n||!e||!c||u,k=r||!n||u;return h().createElement("div",{className:"jp-form-search-settings-group__toggle is-instant-search jp-search-dashboard-wrap"},h().createElement("div",{className:"jp-search-dashboard-row"},h().createElement("div",{className:"lg-col-span-2 md-col-span-1 sm-col-span-0"}),h().createElement(d.A,{checked:f,disabled:v,onChange:l,toggling:a,className:"is-search-admin",switchClassNames:"lg-col-span-1 md-col-span-1 sm-col-span-1",labelClassNames:" lg-col-span-7 md-col-span-5 sm-col-span-3","aria-label":__("Enable instant search experience (recommended)","jetpack-search-pkg")},(0,o.createInterpolateElement)(__("Enable instant search experience <span>(recommended)</span>","jetpack-search-pkg"),{span:h().createElement("span",null)}))),h().createElement("div",{className:"jp-search-dashboard-row"},h().createElement("div",{className:"lg-col-span-3 md-col-span-2 sm-col-span-1"}),h().createElement("div",{className:"jp-form-search-settings-group__toggle-description lg-col-span-7 md-col-span-5 sm-col-span-3"},c&&h().createElement(g.Fragment,null,h().createElement("p",{className:"jp-form-search-settings-group__toggle-explanation"},y)),!c&&t&&h().createElement(m.A,{href:p,upgrade:i})),h().createElement("div",{className:"lg-col-span-2 md-col-span-1 sm-col-span-0"})),c&&h().createElement(C,{isInstantSearchCustomizeButtonDisabled:b,isWidgetsEditorButtonDisabled:k,returnUrl:s}))},C=({isInstantSearchCustomizeButtonDisabled:e,isWidgetsEditorButtonDisabled:t,returnUrl:r})=>h().createElement("div",{className:"jp-form-search-settings-group-buttons jp-search-dashboard-row"},h().createElement("div",{className:"lg-col-span-3 md-col-span-2 sm-col-span-1"}),h().createElement(p.A,{className:"jp-form-search-settings-group-buttons__button is-customize-search lg-col-span-4 md-col-span-5 sm-col-span-3",href:e?void 0:(0,i.sprintf)("admin.php?page=jetpack-search-configure",encodeURIComponent(r)),disabled:e},h().createElement("span",null,__("Customize search results","jetpack-search-pkg"))),h().createElement("div",{className:"lg-col-span-0 md-col-span-1 sm-col-span-0"}),h().createElement("div",{className:"lg-col-span-0 md-col-span-2 sm-col-span-1"}),h().createElement(p.A,{className:"jp-form-search-settings-group-buttons__button is-widgets-editor lg-col-span-3 md-col-span-5 sm-col-span-3",href:t?void 0:(0,i.sprintf)("widgets.php",encodeURIComponent(r)),disabled:t},h().createElement("span",null,__("Edit sidebar widgets","jetpack-search-pkg"))),h().createElement("div",{className:"lg-col-span-2 md-col-span-1 sm-col-span-0"})),j=({isModuleEnabled:e,isSavingEitherOption:t,isTogglingModule:r,supportsSearch:n,toggleSearchModule:a,isDisabledFromOverLimit:s})=>{const o=e&&n&&!s,i=t||!n||s,l=(0,c.useSelect)((e=>e(f.a).isWpcom()),[]);return h().createElement("div",{className:"jp-form-search-settings-group__toggle is-search jp-search-dashboard-wrap"},!l&&h().createElement("div",{className:"jp-search-dashboard-row"},h().createElement("div",{className:"lg-col-span-2 md-col-span-1 sm-col-span-0"}),h().createElement(d.A,{checked:o,disabled:i,onChange:a,toggling:r,className:"is-search-admin",switchClassNames:"lg-col-span-1 md-col-span-1 sm-col-span-1",labelClassNames:" lg-col-span-7 md-col-span-5 sm-col-span-3","aria-label":__("Enable Jetpack Search","jetpack-search-pkg")},__("Enable Jetpack Search","jetpack-search-pkg")),h().createElement("div",{className:"lg-col-span-2 md-col-span-1 sm-col-span-0"})),h().createElement("div",{className:"jp-search-dashboard-row"},h().createElement("div",{className:"lg-col-span-3 md-col-span-2 sm-col-span-1"}),h().createElement("div",{className:"jp-form-search-settings-group__toggle-description lg-col-span-7 md-col-span-5 sm-col-span-3"},h().createElement("p",{className:"jp-form-search-settings-group__toggle-explanation"},v)),h().createElement("div",{className:"lg-col-span-2 md-col-span-1 sm-col-span-0"})))}},8420:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(1883),a=r(3022),s=r(6418),c=r.n(s),o=r(8120),i=r.n(o),l=r(1609),p=r.n(l);class u extends p().Component{static displayName="SimpleNotice";static defaultProps={duration:0,status:null,showDismiss:!0,className:"",onDismissClick:c()};static propTypes={status:i().string,showDismiss:i().bool,isCompact:i().bool,duration:i().number,text:i().oneOfType([i().oneOfType([i().string,i().node]),i().arrayOf(i().oneOfType([i().string,i().node]))]),icon:i().string,onDismissClick:i().func,className:i().string};dismissTimeout=null;componentDidMount(){this.props.duration>0&&(this.dismissTimeout=setTimeout(this.props.onDismissClick,this.props.duration))}componentWillUnmount(){this.dismissTimeout&&clearTimeout(this.dismissTimeout)}getIcon=()=>{let e;switch(this.props.status){case"is-info":default:e="info";break;case"is-success":e="checkmark";break;case"is-error":case"is-warning":e="notice"}return e};clearText=e=>"string"==typeof e?e.replace(/(<([^>]+)>)/gi,""):e;onKeyDownCallback=e=>t=>{13!==t.which&&32!==t.which||e&&e(t)};render(){const{children:e,className:t,icon:r,isCompact:s,onDismissClick:c,showDismiss:o=!s,status:i,text:l,dismissText:u}=this.props,d=(0,a.A)("dops-notice",i,t,{"is-compact":s,"is-dismissable":o});return p().createElement("div",{className:d},p().createElement("span",{className:"dops-notice__icon-wrapper"},p().createElement(n.A,{className:"dops-notice__icon",icon:r||this.getIcon(),size:24})),p().createElement("span",{className:"dops-notice__content"},p().createElement("span",{className:"dops-notice__text"},l?this.clearText(l):e)),l?e:null,o&&p().createElement("span",{role:"button",onKeyDown:this.onKeyDownCallback(c),tabIndex:"0",className:"dops-notice__dismiss",onClick:c},p().createElement(n.A,{icon:"cross",size:24}),p().createElement("span",{className:"dops-notice__screen-reader-text screen-reader-text"},u)))}}},8423:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(1883),a=r(8120),s=r.n(a),c=r(1609),o=r.n(c);class i extends o().Component{static displayName="NoticeAction";static propTypes={href:s().string,onClick:s().func,external:s().bool,icon:s().string};static defaultProps={external:!1};render(){const e={className:"dops-notice__action",href:this.props.href,onClick:this.props.onClick};return this.props.external&&(e.target="_blank"),o().createElement("a",e,o().createElement("span",null,this.props.children),this.props.icon&&o().createElement(n.A,{icon:this.props.icon,size:24}),this.props.external&&o().createElement(n.A,{icon:"external",size:24}))}}},684:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var n=r(766),a=r(5918),s=r(8509),c=r(3924),o=r(9384),i=r(7143),l=r(7723),p=r(2948),u=r(8232),d=r(1609),m=r.n(d),g=r(8228);const __=l.__;function h({isLoading:e=!1}){(0,i.useSelect)((e=>e(g.a).getSearchPricing()),[]);const t=(0,i.useSelect)((t=>t(g.a).isResolving("getSearchPricing")||!t(g.a).hasStartedResolution("getSearchPricing")||e),[e]);return m().createElement(m().Fragment,null,t&&m().createElement(p.A,null),!t&&m().createElement("div",{className:"jp-search-dashboard-connection-screen"},m().createElement(n.A,null,m().createElement(a.A,{horizontalSpacing:3,horizontalGap:3},m().createElement(s.A,{lg:12,md:8,sm:4},m().createElement(v,null)),m().createElement(s.A,{lg:12,md:8,sm:4},m().createElement(f,null))))))}const f=()=>m().createElement("div",{className:"jp-search-dashboard-connection-footer"},m().createElement("p",{className:"jp-search-dashboard-connection-footer__text"},__("Special introductory pricing, all renewals are at full price. 14 day money back guarantee.","jetpack-search-pkg")),m().createElement("p",{className:"jp-search-dashboard-connection-footer__text"},"*"," ",__("Pricing will automatically adjust based on the number of records in your search index.","jetpack-search-pkg")," ",m().createElement("a",{href:(0,c.A)("search-product-pricing"),className:"jp-search-dashboard-connection-footer__link"},__("Learn more","jetpack-search-pkg")))),v=()=>{const e=(0,i.useSelect)((e=>e(g.a).getAPINonce()),[]),t=(0,i.useSelect)((e=>e(g.a).getAPIRootUrl()),[]),r=(0,i.useSelect)((e=>e(g.a).getPriceBefore()),[]),n=(0,i.useSelect)((e=>e(g.a).getPriceAfter()),[]),a=(0,i.useSelect)((e=>e(g.a).getPriceCurrencyCode()),[]),s=(0,i.useSelect)((e=>e(g.a).getRegistrationNonce()),[]),{fetchSearchPlanInfo:c}=(0,i.useDispatch)(g.a),l=(0,d.useCallback)((()=>c().then((e=>e?.supports_search))),[c]);return m().createElement(o.ConnectScreenRequiredPlan,{buttonLabel:__("Get Jetpack Search","jetpack-search-pkg"),priceAfter:n/12,priceBefore:r/12,pricingCurrencyCode:a,pricingIcon:"data:image/svg+xml,%3Csvg width='32' height='32' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M21 19l-5.154-5.154C16.574 12.742 17 11.42 17 10c0-3.866-3.134-7-7-7s-7 3.134-7 7 3.134 7 7 7c1.42 0 2.742-.426 3.846-1.154L19 21l2-2zM5 10c0-2.757 2.243-5 5-5s5 2.243 5 5-2.243 5-5 5-5-2.243-5-5z' fill='%23000'/%3E%3C/svg%3E",pricingTitle:__("Jetpack Search","jetpack-search-pkg"),title:__("The best WordPress search experience","jetpack-search-pkg"),apiRoot:t,apiNonce:e,registrationNonce:s,from:"jetpack-search",redirectUri:"admin.php?page=jetpack-search",wpcomProductSlug:"jetpack_search",siteProductAvailabilityHandler:l},m().createElement(u.A,null))}},5214:(e,t,r)=>{"use strict";r.d(t,{A:()=>j});var n=r(8214),a=r(5918),s=r(8509),c=r(8250),o=r(4149),i=r(1112),l=r(9384),p=r(5985),u=r(7143),d=r(7723),m=r(2121),g=r(2948),h=r(3030),f=r(9624),v=r(7011),y=r(1609),b=r.n(y),k=r(8228),E=r(3795),C=r(5998);const __=d.__;function j({isLoading:e=!1}){(0,u.useSelect)((e=>e(k.a).getSearchPlanInfo()),[]),(0,u.useSelect)((e=>e(k.a).getSearchModuleStatus()),[]),(0,u.useSelect)((e=>e(k.a).getSearchStats()),[]);const t=(0,u.useSelect)((e=>e(k.a).getCalypsoSlug())),r=(0,u.useSelect)((e=>e(k.a).getBlogId())),c=(0,u.useSelect)((e=>e(k.a).getSiteAdminUrl())),{hasConnectionError:o}=(0,l.useConnectionErrorNotice)(),i=()=>{const e=(0,n.A)("jetpack_search",r||t,"admin.php?page=jetpack-search&just_upgraded=1",!0);window.location.href=e},p=(0,u.useSelect)((t=>t(k.a).isResolving("getSearchModuleStatus")||!t(k.a).hasStartedResolution("getSearchModuleStatus")||t(k.a).isResolving("getSearchStats")||!t(k.a).hasStartedResolution("getSearchStats")||t(k.a).isResolving("getSearchPlanInfo")||!t(k.a).hasStartedResolution("getSearchPlanInfo")||e),[e]),d=(0,u.useSelect)((e=>e(k.a).isNewPricing202208()),[]),h=(0,u.useSelect)((e=>e(k.a).isFreePlan())),y=(0,u.useSelect)((e=>e(k.a).isOverLimit())),E=(0,u.useDispatch)(k.a).updateJetpackSettings,C=(0,u.useSelect)((e=>e(k.a).isInstantSearchPromotionActive())),j=(0,u.useSelect)((e=>e(k.a).supportsOnlyClassicSearch())),x=(0,u.useSelect)((e=>e(k.a).supportsSearch())),N=(0,u.useSelect)((e=>e(k.a).supportsInstantSearch())),P=(0,u.useSelect)((e=>e(k.a).isModuleEnabled())),R=(0,u.useSelect)((e=>e(k.a).isInstantSearchEnabled())),I=(0,u.useSelect)((e=>e(k.a).isUpdatingJetpackSettings())),O=(0,u.useSelect)((e=>e(k.a).isTogglingModule())),M=(0,u.useSelect)((e=>e(k.a).isTogglingInstantSearch())),T=(0,u.useSelect)((e=>e(k.a).getTierMaximumRecords())),D=(0,u.useSelect)((e=>e(k.a).getPostCount())),L=(0,u.useSelect)((e=>e(k.a).getPostTypeBreakdown())),z=(0,u.useSelect)((e=>e(k.a).getLastIndexedDate())),F=(0,u.useSelect)((e=>e(k.a).getPostTypes())),B=(0,u.useDispatch)(k.a).removeNotice,U=(0,u.useSelect)((e=>e(k.a).getNotices()),[]),V={lastIndexedDate:z,postCount:D,postTypeBreakdown:L,postTypes:F,tierMaximumRecords:T};return b().createElement(b().Fragment,null,b().createElement(a.A,{horizontalSpacing:0},b().createElement(s.A,null,b().createElement("div",{id:"jp-admin-notices",className:"jetpack-search-jitm-card"}))),p&&b().createElement(g.A,null),!p&&b().createElement("div",{className:"jp-search-dashboard-page"},b().createElement(S,{isUpgradable:d&&h||!N,sendPaidPlanToCart:i}),o&&b().createElement(a.A,{horizontalSpacing:3,horizontalGap:3},b().createElement(s.A,{lg:12,md:12,sm:12},b().createElement(l.ConnectionError,null))),b().createElement(A,{supportsInstantSearch:N,supportsOnlyClassicSearch:j}),d&&N&&b().createElement(_,{hasIndex:0!==D,recordMeterInfo:V,isFreePlan:h,sendPaidPlanToCart:i}),!d&&N&&b().createElement(v.A,{postCount:D,postTypeBreakdown:L,tierMaximumRecords:T,lastIndexedDate:z,postTypes:F}),b().createElement("div",{className:"jp-search-dashboard-bottom"},b().createElement(f.A,{siteAdminUrl:c,updateOptions:E,domain:t,isDisabledFromOverLimit:y,isInstantSearchPromotionActive:C,supportsOnlyClassicSearch:j,supportsSearch:x,supportsInstantSearch:N,isModuleEnabled:P,isInstantSearchEnabled:R,isSavingEitherOption:I,isTogglingModule:O,isTogglingInstantSearch:M})),b().createElement(w,null),b().createElement(m.A,{notices:U,handleLocalNoticeDismissClick:B})))}const _=({hasIndex:e,recordMeterInfo:t,isFreePlan:r,sendPaidPlanToCart:n})=>{const a=(0,u.useSelect)((e=>e(k.a).getSiteTitle()))||"your site",s={currentPlan:(0,u.useSelect)((e=>e(k.a).getCurrentPlan())),currentUsage:(0,u.useSelect)((e=>e(k.a).getCurrentUsage())),latestMonthRequests:(0,u.useSelect)((e=>e(k.a).getLatestMonthRequests())),isFreePlan:r},c=(0,u.useSelect)((e=>e(k.a).isPlanJustUpgraded()),[]);return b().createElement(b().Fragment,null,!e&&b().createElement(E.A,{siteTitle:a,planInfo:s}),e&&b().createElement(b().Fragment,null,b().createElement(C.A,{isFreePlan:r,isPlanJustUpgraded:c,planInfo:s,sendPaidPlanToCart:n}),b().createElement(v.A,{postCount:t.postCount,postTypeBreakdown:t.postTypeBreakdown,tierMaximumRecords:t.tierMaximumRecords,lastIndexedDate:t.lastIndexedDate,postTypes:t.postTypes})))},A=({supportsInstantSearch:e,supportsOnlyClassicSearch:t})=>b().createElement("div",{className:"jp-search-dashboard-top jp-search-dashboard-wrap"},b().createElement("div",{className:"jp-search-dashboard-row"},b().createElement("div",{className:"jp-search-dashboard-top__title lg-col-span-6 md-col-span-7 sm-col-span-4"},b().createElement("h1",null,__("Help your visitors find exactly what they're looking for, fast","jetpack-search-pkg"))),b().createElement("div",{className:" lg-col-span-6 md-col-span-1 sm-col-span-0"})),b().createElement("div",{className:"jp-search-dashboard-row","aria-hidden":"true"},b().createElement("div",{className:"lg-col-span-1 md-col-span-1 sm-col-span-0"}),b().createElement("div",{className:"jp-search-dashboard-top__mocked-search-interface lg-col-span-10 md-col-span-6 sm-col-span-4"},b().createElement(h.A,{supportsInstantSearch:e,supportsOnlyClassicSearch:t})),b().createElement("div",{className:"lg-col-span-1 md-col-span-1 sm-col-span-0"}))),w=()=>b().createElement("div",{className:"jp-search-dashboard-footer jp-search-dashboard-wrap"},b().createElement("div",{className:"jp-search-dashboard-row"},b().createElement(c.A,{moduleName:__("Jetpack Search","jetpack-search-pkg"),className:"lg-col-span-12 md-col-span-8 sm-col-span-4",useInternalLinks:(0,p.pg)()}))),S=({isUpgradable:e,sendPaidPlanToCart:t})=>{const r={children:__("Upgrade Jetpack Search","jetpack-search-pkg"),variant:"link",onClick:t};return b().createElement("div",{className:"jp-search-dashboard-header jp-search-dashboard-wrap"},b().createElement("div",{className:"jp-search-dashboard-row"},b().createElement("div",{className:"lg-col-span-12 md-col-span-8 sm-col-span-4"},b().createElement("div",{className:"jp-search-dashboard-header__logo-container"},b().createElement(o.A,{className:"jp-search-dashboard-header__masthead"}),e&&b().createElement(i.A,r)))))}},3795:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(723),a=r(7319),s=r(7723),c=r(8420),o=r(1609),i=r.n(o),l=r(6893);const __=s.__,p=({siteTitle:e})=>i().createElement("div",null,i().createElement("h3",null,(0,s.sprintf)(
// translators: %1$s: site name (not translated)
__("Indexing %1$s","jetpack-search-pkg"),e)),i().createElement(n.Ay,null,i().createElement(a.A,null))),u=()=>{const e=__("We're gathering your usage data.","jetpack-search-pkg"),t=__("If you have recently set up Search, please allow a little time for indexing to complete.","jetpack-search-pkg");return i().createElement(c.A,{isCompact:!1,status:"is-info",className:"jp-search-notice-box",icon:"info-outline",showDismiss:!1},i().createElement("h3",{className:"dops-notice__header"},e),i().createElement("span",{className:"dops-notice__body"},t))},d=({planInfo:e,siteTitle:t})=>i().createElement("div",{className:"jp-search-dashboard-wrap jp-search-dashboard-meter-wrap"},i().createElement("div",{className:"jp-search-dashboard-row"},i().createElement("div",{className:"lg-col-span-2 md-col-span-1 sm-col-span-0"}),i().createElement("div",{className:"jp-search-dashboard-meter-wrap__content lg-col-span-8 md-col-span-6 sm-col-span-4"},i().createElement(l.A,{planInfo:e}),i().createElement(p,{siteTitle:t}),i().createElement(u,null)),i().createElement("div",{className:"lg-col-span-2 md-col-span-1 sm-col-span-0"})))},6893:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(7723),a=r(1609),s=r.n(a);const __=n.__,c=({isFreePlan:e,planInfo:t})=>s().createElement("h2",null,
// translators: Header for section showing search records and requests usage.
__("Your usage","jetpack-search-pkg")," ",s().createElement("span",null,(e=>{const t=new Date(e.latestMonthRequests.start_date),r=new Date(e.latestMonthRequests.end_date),n={month:"short",day:"2-digit"};return`${t.toLocaleDateString(void 0,n)} - ${r.toLocaleDateString(void 0,n)}`})(t)," (",(e=>{const t=__("Upgraded","jetpack-search-pkg"),r=__("Free plan","jetpack-search-pkg");return e?r:t})(e),")"))},5998:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});var n=r(723),a=r(4437),s=r(6427),c=r(6087),o=r(7723),i=r(1609),l=r.n(i),p=r(2090),u=r(6893);const __=o.__,d=e=>{if(e.currentUsage.must_upgrade)return{description:__("You’ve exceeded the limits available for your search plan.","jetpack-search-pkg"),cta:__("Upgrade now to restore Jetpack Search functionality.","jetpack-search-pkg")};if(!e.currentUsage.should_upgrade)return null;let t=(e=>{if(!e.currentUsage.upgrade_reason.records||!e.currentUsage.upgrade_reason.requests)return null;const t=__("You’re close to exceeding the number of site records and search requests available for the free plan.","jetpack-search-pkg");let r=__("You’ve exceeded the number of site records and search requests available for the free plan.","jetpack-search-pkg");return 0===e.currentUsage.months_over_plan_records_limit&&(r=t),{description:r,cta:__("Upgrade now to increase your limits and avoid interruption!","jetpack-search-pkg")}})(e);return t||(t=(e=>{if(!e.currentUsage.upgrade_reason.records||e.currentUsage.upgrade_reason.requests)return null;const t=__("You’re close to exceeding the number of site records available on the free plan.","jetpack-search-pkg"),r=__("You’ve exceeded the number of site records available on the free plan.","jetpack-search-pkg"),n=__("You’ve exceeded the number of site records available on the free plan for two consecutive months.","jetpack-search-pkg"),a=__("You’ve exceeded the number of site records available on the free plan for three consecutive months.","jetpack-search-pkg");let s=e.currentUsage.months_over_plan_records_limit;s<0?s=0:s>3&&(s=3);const c=[t,r,n,a][s],o=__("Upgrade now to increase your monthly record limit and avoid interruption!","jetpack-search-pkg"),i=__("Upgrade now to continue using Jetpack Search.","jetpack-search-pkg");let l=o;return e.currentUsage.months_over_plan_records_limit>=3&&(l=i),{description:c,cta:l}})(e),t||(t=(e=>{if(e.currentUsage.upgrade_reason.records||!e.currentUsage.upgrade_reason.requests)return null;const t=__("You’re close to exceeding the number of search requests available on the free plan.","jetpack-search-pkg"),r=__("You’re close to exceeding the number of search requests available on the free plan for two consecutive months.","jetpack-search-pkg"),n=__("You’re close to exceeding the number of search requests available on the free plan for three consecutive months.","jetpack-search-pkg"),a=__("You’ve exceeded the number of search requests available on the free plan.","jetpack-search-pkg"),s=__("You’ve exceeded the number of search requests available on the free plan for two consecutive months.","jetpack-search-pkg"),c=__("You’ve exceeded the number of search requests available on the free plan for three consecutive months.","jetpack-search-pkg");let o=e.currentUsage.months_over_plan_requests_limit;o<0?o=0:o>3&&(o=3);const i=[t,a,s,c,r,n][o],l=__("Upgrade now to increase your monthly request limit and avoid interruption.","jetpack-search-pkg"),p=__("Upgrade now to continue using Jetpack Search.","jetpack-search-pkg");let u=l;return e.currentUsage.months_over_plan_requests_limit>=3&&(u=p),{description:i,cta:u}})(e),t||{description:__("Do you want to increase your site records and search requests?.","jetpack-search-pkg"),cta:__("Upgrade now and avoid any future interruption!","jetpack-search-pkg")}))},m=({upgradeMessage:e,ctaCallback:t})=>{const r=e&&{...e,onClick:t};return l().createElement(l().Fragment,null,r&&l().createElement(n.Ay,null,l().createElement(a.A,r)))},g=({usageInfo:e,isPlanJustUpgraded:t})=>{const[r,n]=(0,i.useState)(0),a=window.localStorage;(0,i.useMemo)((()=>n(a.getItem("upgrade_tooltip_finished")?0:+t)),[n,a,t]);const s=(0,i.useCallback)((()=>a.setItem("upgrade_tooltip_finished",1)),[a]),c=(0,i.useCallback)((()=>{n((e=>(e>=2&&s(),e+1)))}),[n,s]),u={record:{index:1,title:__("Site records increased","jetpack-search-pkg"),content:(0,o.sprintf)(
// translators: %1$s: records limit
__("Thank you for upgrading! Now your visitors can search up to %1$s records.","jetpack-search-pkg"),(0,p.Z)(e.recordMax)),section:__("1 of 2","jetpack-search-pkg"),next:__("Next","jetpack-search-pkg"),forceShow:1===r,goToNext:c},request:{index:2,title:__("More search requests","jetpack-search-pkg"),content:(0,o.sprintf)(
// translators: %1$s: requests limit
__("Your search plugin now supports up to %1$s search requests per month.","jetpack-search-pkg"),(0,p.Z)(e.requestMax)),section:__("2 of 2","jetpack-search-pkg"),next:__("Finish","jetpack-search-pkg"),forceShow:2===r,goToNext:c}};return l().createElement("div",{className:"usage-meter-group"},l().createElement(p.A,{title:__("Site records","jetpack-search-pkg"),current:e.recordCount,limit:e.recordMax,tooltip:u.record}),l().createElement(p.A,{title:__("Search requests","jetpack-search-pkg"),current:e.requestCount,limit:e.requestMax,tooltip:u.request}))},h=()=>l().createElement("div",{className:"usage-meter-about"},(0,c.createInterpolateElement)(__("Tell me more about <jpPlanLimits>record indexing and request limits</jpPlanLimits>","jetpack-search-pkg"),{jpPlanLimits:l().createElement(s.ExternalLink,{href:"https://jetpack.com/support/search/",rel:"noopener noreferrer",target:"_blank",className:"support-link"})})),f=({isFreePlan:e,planInfo:t,sendPaidPlanToCart:r,isPlanJustUpgraded:n})=>{const a=e||t.currentUsage.must_upgrade?d(t):null,s=(c=t,{recordCount:c?.currentUsage?.num_records||0,recordMax:c?.currentPlan?.record_limit||0,requestCount:c?.latestMonthRequests?.num_requests||0,requestMax:c?.currentPlan.monthly_search_request_limit||0});var c;return l().createElement("div",{className:"jp-search-dashboard-wrap jp-search-dashboard-meter-wrap"},l().createElement("div",{className:"jp-search-dashboard-row"},l().createElement("div",{className:"lg-col-span-2 md-col-span-1 sm-col-span-0"}),l().createElement("div",{className:"jp-search-dashboard-meter-wrap__content lg-col-span-8 md-col-span-6 sm-col-span-4"},l().createElement(u.A,{isFreePlan:e,planInfo:t}),l().createElement(g,{usageInfo:s,isPlanJustUpgraded:n}),l().createElement(m,{upgradeMessage:a,ctaCallback:r}),l().createElement(h,null)),l().createElement("div",{className:"lg-col-span-2 md-col-span-1 sm-col-span-0"})))}},3981:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(4149),a=r(7143),s=r(6087),c=r(7723),o=r(8228);const __=c.__,i=()=>{const e=(0,a.useSelect)((e=>`${e(o.a).getSiteAdminUrl()}admin.php?page=my-jetpack#/add-license`)),t=(0,a.useSelect)((e=>e(o.a).isWpcom()),[]);return React.createElement("div",{className:"jp-search-dashboard-upsell-page__header"},React.createElement("span",{className:"jp-search-dashboard-upsell-page__logo"},React.createElement(n.A,null)),!t&&React.createElement("p",null,(0,s.createInterpolateElement)(__("Already have an existing plan or license key? <a>Click here to get started</a>","jetpack-search-pkg"),{a:React.createElement("a",{href:e})})))}},5432:(e,t,r)=>{"use strict";r.d(t,{A:()=>N});var n=r(5932),a=r(2947),s=r(766),c=r(5918),o=r(8509),i=r(9957),l=r(1437),p=r(723),u=r(9245),d=r(489),m=r(597),g=r(1112),h=r(9384),f=r(5985),v=r(7143),y=r(6087),b=r(7723),k=r(2948),E=r(3655),C=r(8232),j=r(7033),_=r(1609),A=r.n(_),w=r(8228),S=r(3981);const __=b.__,x="https://jetpack.com/upgrade/search";function N({isLoading:e=!1}){const t=(0,v.useSelect)((e=>e(w.a).getAPINonce()),[]),r=(0,v.useSelect)((e=>e(w.a).isNewPricing202208()),[]);(0,v.useSelect)((e=>e(w.a).getSearchPricing()),[]);const c=(0,v.useSelect)((e=>e(w.a).getCalypsoSlug()),[]),o=(0,v.useSelect)((e=>e(w.a).getBlogId()),[]),i=(0,v.useSelect)((e=>e(w.a).getSiteAdminUrl()),[]),l=(0,v.useSelect)((e=>e(w.a).isWpcom()),[]),{fetchSearchPlanInfo:p}=(0,v.useDispatch)(w.a),u=(0,_.useCallback)((()=>{n.Ay.setApiNonce(t),p().then((e=>e?.supports_search))}),[t,p]),{run:d,hasCheckoutStarted:m}=(0,j.A)({productSlug:"jetpack_search",adminUrl:i,redirectUri:"admin.php?page=jetpack-search&just_upgraded=1",siteProductAvailabilityHandler:u,from:"jetpack-search",siteSuffix:c,blogID:o,isWpcom:l}),{run:g,hasCheckoutStarted:h}=(0,j.A)({productSlug:"jetpack_search_free",adminUrl:i,redirectUri:"admin.php?page=jetpack-search",siteProductAvailabilityHandler:u,from:"jetpack-search",siteSuffix:c,blogID:o,isWpcom:l}),y=(0,v.useSelect)((t=>t(w.a).isResolving("getSearchPricing")||!t(w.a).hasStartedResolution("getSearchPricing")||m||h||e),[e,m,h]);return A().createElement(A().Fragment,null,y&&A().createElement(k.A,null),!y&&A().createElement("div",{className:"jp-search-dashboard-upsell-page"},A().createElement(a.A,{moduleName:__("Jetpack Search","jetpack-search-pkg"),header:A().createElement(S.A,null),moduleNameHref:x,useInternalLinks:(0,f.pg)()},A().createElement(s.A,null,r?A().createElement(R,{sendToCartPaid:d,sendToCartFree:g}):A().createElement(P,{sendToCart:d})))))}const P=({sendToCart:e})=>{const t=(0,v.useSelect)((e=>e(w.a).getPriceBefore()/12),[]),r=(0,v.useSelect)((e=>e(w.a).getPriceAfter()/12),[]),n=(0,v.useSelect)((e=>e(w.a).getPriceCurrencyCode()),[]),a=__("14 day money back guarantee.","jetpack-search-pkg"),s=__("Special introductory pricing, all renewals are at full price. 14 day money back guarantee.","jetpack-search-pkg"),{hasConnectionError:l}=(0,h.useConnectionErrorNotice)();return A().createElement(c.A,{horizontalSpacing:3,horizontalGap:3},l&&A().createElement(o.A,{lg:12,md:12,sm:12},A().createElement(h.ConnectionError,null)),A().createElement(o.A,{lg:6,md:6,sm:4},A().createElement("h1",null,__("The best WordPress search experience","jetpack-search-pkg")),A().createElement(C.A,null)),A().createElement(o.A,{lg:1,md:1,sm:0}),A().createElement(o.A,{lg:5,md:6,sm:4},A().createElement(i.A,{ctaText:__("Get Jetpack Search","jetpack-search-pkg"),icon:"data:image/svg+xml,%3Csvg width='32' height='32' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M21 19l-5.154-5.154C16.574 12.742 17 11.42 17 10c0-3.866-3.134-7-7-7s-7 3.134-7 7 3.134 7 7 7c1.42 0 2.742-.426 3.846-1.154L19 21l2-2zM5 10c0-2.757 2.243-5 5-5s5 2.243 5 5-2.243 5-5 5-5-2.243-5-5z' fill='%23000'/%3E%3C/svg%3E",infoText:r===t?a:s,onCtaClick:e,priceAfter:r,priceBefore:t,currencyCode:n,title:__("Jetpack Search","jetpack-search-pkg")})))},R=({sendToCartPaid:e,sendToCartFree:t})=>{const r=(0,v.useSelect)((e=>e(w.a).getCalypsoSlug()),[]),n=(0,l.Y)(),a=(0,v.useSelect)((e=>e(w.a).getPriceBefore()/12),[]),s=(0,v.useSelect)((e=>e(w.a).getPriceAfter()/12),[]),i=(0,v.useSelect)((e=>e(w.a).getPriceCurrencyCode()),[]),f=(0,v.useSelect)((e=>e(w.a).getPricingDiscountPercentage()),[]),{hasConnectionError:k}=(0,h.useConnectionErrorNotice)(),C=(0,v.useSelect)((e=>e(w.a).getPaidRecordsLimit()),[]),j=new Intl.NumberFormat(n,{notation:"compact",compactDisplay:"short"}).format(C),_=(0,v.useSelect)((e=>e(w.a).getPaidRequestsLimit()),[]),S=_>1e18,x=__("Unlimited","jetpack-search-pkg"),N=S?x:new Intl.NumberFormat(n,{notation:"compact",compactDisplay:"short"}).format(_),P=(0,v.useSelect)((e=>e(w.a).getAdditionalUnitPrice()),[]),R=(0,v.useSelect)((e=>e(w.a).getAdditionalUnitQuantity()),[]),O=new Intl.NumberFormat(n,{notation:"compact",compactDisplay:"short"}).format(R);return A().createElement(c.A,{horizontalSpacing:8},k&&A().createElement(o.A,{lg:12,md:12,sm:12},A().createElement(h.ConnectionError,null)),A().createElement(o.A,{lg:12,md:12,sm:12},A().createElement(p.Ay,null,A().createElement(u.Ay,I,A().createElement(u.N0,{primary:!0},A().createElement(u.i7,null,A().createElement(d.A,{price:a,offPrice:f>0?s:null,currency:i,legend:"",promoLabel:f>0&&(0,b.sprintf)(
// translators: Discount percentage (e.g. 50%).
__("%s off","jetpack-search-pkg"),`${f}%`)},A().createElement("div",{className:"price-tip"},A().createElement("span",{className:"price-tip-text"},__("Starting price per month, billed yearly","jetpack-search-pkg")),A().createElement(m.A,{placement:"bottom-end",iconSize:14,iconClassName:"price-tip-icon",offset:4},(0,y.createInterpolateElement)((0,b.sprintf)(
// translators: %1$s: site domain
__("Starting price based on the number of records for <b>%1$s</b>.","jetpack-search-pkg"),r),{b:A().createElement("b",null)})," ",!S&&(0,y.createInterpolateElement)((0,b.sprintf)(
// translators: %1$s: Records or requests count, <Price>: Additional charge.
__("For every additional %1$s records or requests, an additional <Price></Price> per month will be charged.","jetpack-search-pkg"),O),{Price:A().createElement(E.A,{amount:P,currency:i})})))),A().createElement(g.A,{onClick:e,fullWidth:!0},__("Get Search","jetpack-search-pkg"))),A().createElement(u.eY,{isIncluded:!0,label:A().createElement("strong",null,(0,b.sprintf)(
// translators: %1$s: Number of records allowed before being charged extra.
__("%1$s records","jetpack-search-pkg"),j))}),A().createElement(u.eY,{isIncluded:!0,label:A().createElement("strong",null,(0,b.sprintf)(
// translators: %1$s: Number of requests allowed before being charged extra.
__("%1$s requests","jetpack-search-pkg"),N))}),A().createElement(u.eY,{isIncluded:!0,label:__("Branding removed","jetpack-search-pkg")}),A().createElement(u.eY,{isIncluded:!0}),A().createElement(u.eY,{isIncluded:!0}),A().createElement(u.eY,{isIncluded:!0}),A().createElement(u.eY,{isIncluded:!0}),A().createElement(u.eY,{isIncluded:!0})),A().createElement(u.N0,null,A().createElement(u.i7,null,A().createElement(d.A,{price:0,legend:"",currency:i,hidePriceFraction:!0}),A().createElement(g.A,{onClick:t,variant:"secondary",fullWidth:!0},__("Start for free","jetpack-search-pkg"))),A().createElement(u.eY,{isIncluded:!0,label:A().createElement("strong",null,
// translators: Record count for calculating Jetpack Search tier
__("5k records","jetpack-search-pkg")),tooltipInfo:A().createElement(A().Fragment,null,__("In the free plan, you can continue using the plugin even if you have more than 5k records for three months.","jetpack-search-pkg")," ",A().createElement("a",{href:"https://jetpack.com/search/",rel:"external noopener noreferrer nofollow",target:"_blank"},__("More about indexing and query limits","jetpack-search-pkg")))}),A().createElement(u.eY,{isIncluded:!0,label:A().createElement("strong",null,
// translators: Request count for calculating Jetpack Search tier
__("500 requests","jetpack-search-pkg")),tooltipInfo:A().createElement(A().Fragment,null,__("In the free plan, you can continue using the plugin even if you have more than 500 requests for three consecutive months.","jetpack-search-pkg")," ",A().createElement("a",{href:"https://jetpack.com/search/",rel:"external noopener noreferrer nofollow",target:"_blank"},__("More about indexing and query limits","jetpack-search-pkg")))}),A().createElement(u.eY,{isIncluded:!1,label:__("Shows Jetpack logo","jetpack-search-pkg")}),A().createElement(u.eY,{isIncluded:!1}),A().createElement(u.eY,{isIncluded:!0}),A().createElement(u.eY,{isIncluded:!0}),A().createElement(u.eY,{isIncluded:!0}),A().createElement(u.eY,{isIncluded:!0}))))))},I={title:__("The best WordPress search experience","jetpack-search-pkg"),items:[{name:__("Number of records","jetpack-search-pkg"),tooltipInfo:__("Records are all posts, pages, custom post types and other types of content indexed by Jetpack Search.","jetpack-search-pkg")},{name:__("Monthly requests","jetpack-search-pkg"),tooltipInfo:__("A search request is when someone visiting your site searches for something.","jetpack-search-pkg")},{name:__("Unbranded search","jetpack-search-pkg"),tooltipInfo:__("Paid customers can remove branding from the search tool.","jetpack-search-pkg")},{name:__("Priority support","jetpack-search-pkg"),tooltipInfo:A().createElement(A().Fragment,null,__("Paid customers get dedicated email support from our world-class Happiness Engineers to help with any issue.","jetpack-search-pkg"),A().createElement("br",null),A().createElement("br",null),__("All other questions are handled by our team as quickly as we are able to through the WordPress support forum.","jetpack-search-pkg"))},{name:__("Instant search and indexing","jetpack-search-pkg"),tooltipInfo:A().createElement(A().Fragment,null,__("Instant search and filtering without reloading the page.","jetpack-search-pkg"),A().createElement("br",null),A().createElement("br",null),__("Real-time indexing, so your search index will update within minutes of changes to your site.","jetpack-search-pkg"))},{name:__("Powerful filtering","jetpack-search-pkg"),tooltipInfo:__("Filtered and faceted searches by tags, categories, dates, custom taxonomies, and post types.","jetpack-search-pkg")},{name:__("Supports 38 languages","jetpack-search-pkg"),tooltipInfo:A().createElement(A().Fragment,null,__("Language support for English, Spanish, French, Portuguese, Hindi, Japanese, among others.","jetpack-search-pkg")," ",A().createElement("a",{href:"https://jetpack.com/support/search/jetpack-search-plugin/#faq",rel:"external noopener noreferrer nofollow",target:"_blank"},__("See all supported languanges","jetpack-search-pkg")))},{name:__("Spelling correction","jetpack-search-pkg"),tooltipInfo:__("Quick and accurate spelling correction for when your site visitors mistype their search.","jetpack-search-pkg")}]}},3655:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(7397),a=r(7425),s=r(1609),c=r.n(s);const o=({amount:e,currency:t,hidePriceFraction:r,tag:s="span"})=>{const{symbol:o,integer:i,fraction:l}=(0,n.vA)(e,t),p=!r||!l.endsWith("00");return c().createElement(a.Ay,{component:s},o,i,p&&l)}},7011:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var n=r(6051),a=r(7723),s=r(1609),c=r.n(s),o=r(9993),i=r(4919),l=r(6330);const __=a.__;function p({postCount:e,postTypeBreakdown:t,tierMaximumRecords:r,lastIndexedDate:a,postTypes:s}){const p=(0,o.Ay)(e,t,a,s);return c().createElement("div",{className:"jp-search-record-meter jp-search-dashboard-wrap","data-testid":"record-meter"},c().createElement("div",{className:"jp-search-dashboard-row"},c().createElement("div",{className:"lg-col-span-2 md-col-span-1 sm-col-span-0"}),c().createElement("div",{className:"jp-search-record-meter__content lg-col-span-8 md-col-span-6 sm-col-span-4"},c().createElement("h2",null,
/* translators: 'Your search index' is a breakdown of the site's indexed post type content,
  such as the number of indexed posts, pages etc. */
__("Your search index","jetpack-search-pkg")),c().createElement("div",null,c().createElement(l.$,{recordCount:p.recordCount,tierMaximumRecords:r}),c().createElement(n.A,{items:p.data,showLegendLabelBeforeCount:!0,totalCount:r}),c().createElement(i.I,{recordCount:p.recordCount,tierMaximumRecords:r,hasBeenIndexed:p.hasBeenIndexed,hasValidData:p.hasValidData,hasItems:p.hasItems}))),c().createElement("div",{className:"lg-col-span-2 md-col-span-1 sm-col-span-0"})))}},9993:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>s});r(9060);var n=r(7723);const __=n.__,a=r(8377);function s(e,t,r,n){const s=[],o=[];let i=0,l=!0,p=!0,u=!0;"object"!=typeof t&&(l=!1),"number"!=typeof e&&(p=!1);const d=l&&p?Object.keys(t).length:0;0===d&&(u=!1);const m=[a.colors["Blue 30"],a.colors["Orange 30"],a.colors["WooCommerce Purple 30"],a.colors["Green 30"],a.colors["Yellow 30"]];if(d>0&&l&&p){const e=function(e,t){const r=e.map((e=>{const r=t[e.slug],n=r?r.label:e.slug;return{...e,label:n}}));return r}(t,n);for(let t=0;t<d;t++){const r=Object.values(e)[t],{count:n,label:a}=r;o.push({data:c(n,m[t],a)}),i+=n}const r=function(e,t,r){const n=r<=t?r:t;return{includedItems:e.slice(0,n),otherItems:e.slice(n,t)}}(o,d,5);for(const e in r.includedItems)s.push(c(r.includedItems[e].data.count,m[e],r.includedItems[e].data.label));r.otherItems.length>0&&s.push(c(function(e){let t=0;for(const r in e)t=e[r].data.count+t;return t}(r.otherItems),a.colors["Gray 30"],__("other","jetpack-search-pkg")))}return{data:s,recordCount:i,hasBeenIndexed:p,hasValidData:l,hasItems:u,isValid:p&&l&&u}}function c(e,t,r){return{count:e,label:r,backgroundColor:t}}},4919:(e,t,r)=>{"use strict";r.d(t,{I:()=>u});r(9060);var n=r(7975),a=r(7723),s=r(8420),c=r(8423),o=r(1609),i=r.n(o);const __=a.__,l=.8,p=(e=null)=>{const t="number"==typeof e?(0,n.A)(e):e;return{1:{id:1,header:__("We were unable to index your content","jetpack-search-pkg"),message:__("Jetpack's servers ran into a problem when trying to communicate with your site.","jetpack-search-pkg"),isImportant:!0},2:{id:2,header:__("We're gathering your usage data","jetpack-search-pkg"),message:__("If you have recently set up Jetpack Search, please allow a little time for indexing to complete.","jetpack-search-pkg")},3:{id:3,header:__("You're close to the maximum records for this billing tier","jetpack-search-pkg"),message:(0,a.sprintf)(
// translators: %s: site's current plan record limit
__("Once you hit %s indexed records, you'll be upgraded to the next tier. You won't be charged for the new tier until your next billing date.","jetpack-search-pkg"),t),link:{text:__("Learn more","jetpack-search-pkg"),url:"https://jetpack.com/support/search/product-pricing/"}}}};function u(e){const t=[];if(!1===e.hasValidData&&t.push("1"),!1===e.hasBeenIndexed&&t.push("2"),!1===e.hasItems&&t.push("2"),"number"==typeof e.tierMaximumRecords&&e.recordCount>e.tierMaximumRecords*l&&e.recordCount<e.tierMaximumRecords&&t.push("3"),t.length<1)return null;const r=p(e.tierMaximumRecords)[t[0]],n=r.isImportant?"jp-search-notice-box jp-search-notice-box__important":"jp-search-notice-box";return i().createElement(s.A,{isCompact:!1,status:"is-info",className:n,icon:"info-outline",showDismiss:!1},r.header&&i().createElement("h3",{className:"dops-notice__header"},r.header),r.message&&i().createElement("span",{className:"dops-notice__body"},r.message),r.link&&i().createElement(c.A,{href:r.link.url,external:!0},r.link.text))}},6330:(e,t,r)=>{"use strict";r.d(t,{$:()=>p});var n=r(7975),a=r(1883),s=r(6087),c=r(7723),o=r(1609),i=r.n(o);const __=c.__,l="https://jetpack.com/support/search/jetpack-search-record-meter/";function p(e){if(!e.recordCount)return null;const t="number"==typeof e.recordCount?(0,n.A)(e.recordCount):e.recordCount,r="number"==typeof e.tierMaximumRecords?(0,n.A)(e.tierMaximumRecords):e.tierMaximumRecords;let o;return o=r?(0,s.createInterpolateElement)((0,c.sprintf)(
// translators: %1$s: site's current record count, %2$s: record limit of the current plan
__("<s>%1$s</s> records indexed out of the <s>%2$s</s> allotted for your current plan","jetpack-search-pkg"),t,r),{s:i().createElement("strong",null)}):(0,s.createInterpolateElement)((0,c.sprintf)(
// translators: %1$s: site's current record count, %2$s: record limit of the current plan
__("<s>%1$s</s> records indexed","jetpack-search-pkg"),t),{s:i().createElement("strong",null)}),i().createElement("div",{"data-testid":"record-count",className:"jp-search-record-count"},i().createElement("p",{className:"jp-search-record-count__message"},o,l&&i().createElement("a",{href:l,className:"jp-search-record-count__info",title:__("More info","jetpack-search-pkg")},i().createElement(a.A,{className:"jp-search-record-count__info-icon",icon:"info-outline",size:18}),i().createElement("span",{className:"screen-reader-text"},__("More info","jetpack-search-pkg")))))}},8232:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(7723),a=r(1609),s=r.n(a);const __=n.__;function c(){return s().createElement("div",{className:"jp-search-dashboard-promotion"},s().createElement("h3",null,__("Allow viewers to search through your site's records, lightning fast.","jetpack-search-pkg")),s().createElement("ul",{className:"jp-product-promote"},s().createElement("li",null,__("Customizable filtering","jetpack-search-pkg")),s().createElement("li",null,__("Support for 38 languages","jetpack-search-pkg")),s().createElement("li",null,__("Content displayed within results is updated in real-time","jetpack-search-pkg")),s().createElement("li",null,__("If you grow into a new pricing tier, we'll let you know before your next billing cycle","jetpack-search-pkg")),s().createElement("li",null,__("Best-in-class support","jetpack-search-pkg"))))}},1549:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(7723),a=r(1609),s=r.n(a);const __=n.__;function c(e={upgrade:!0}){return s().createElement("a",{className:"jp-instant-search-upsell-nudge jp-search-dashboard-cut",href:e.href},s().createElement("span",null,__("Offer instant search results to your visitors as soon as they start typing.","jetpack-search-pkg"))," ",s().createElement("span",null,s().createElement("b",null,__("Try Jetpack Instant Search for free now","jetpack-search-pkg"))))}},665:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(9384),a=r(7143),s=r(8228);function c(){const e=(0,a.useSelect)((e=>({siteIsRegistering:e(n.CONNECTION_STORE_ID).getSiteIsRegistering(),userIsConnecting:e(n.CONNECTION_STORE_ID).getUserIsConnecting(),userConnectionData:e(n.CONNECTION_STORE_ID).getUserConnectionData(),connectedPlugins:e(n.CONNECTION_STORE_ID).getConnectedPlugins(),connectionErrors:e(n.CONNECTION_STORE_ID).getConnectionErrors(),...e(n.CONNECTION_STORE_ID).getConnectionStatus()}))),t=(0,a.useSelect)((e=>e(s.a).isWpcom()),[]);return{connectionStatus:e,isFullyConnected:Object.keys(e).length&&e.hasConnectedOwner&&e.isRegistered||t,isSiteConnected:Object.keys(e).length&&e.isRegistered||t}}},7033:(e,t,r)=>{"use strict";r.d(t,{A:()=>g});var n=r(372),a=r(5932),s=r(8214),c=r(9384),o=r(7143),i=r(1609),l=r(8228);const{registrationNonce:p,apiRoot:u,apiNonce:d,siteSuffix:m}=window?.JP_CONNECTION_INITIAL_STATE?window.JP_CONNECTION_INITIAL_STATE:{};function g({productSlug:e,redirectUri:t,siteSuffix:r=m,blogID:g=null,siteProductAvailabilityHandler:h=null,from:f,isWpcom:v=!1}={}){const[y,b]=(0,i.useState)(!1),{registerSite:k}=(0,o.useDispatch)(c.CONNECTION_STORE_ID),{isUserConnected:E,isRegistered:C,handleConnectUser:j}=(0,c.useConnection)({redirectUri:t,from:f}),_=(0,s.A)(e,g||r,t,E||v),A=()=>Promise.resolve(h&&h()).then((e=>{if(e)return j();window.location.href=_}));return(0,i.useEffect)((()=>{a.Ay.setApiRoot(u),a.Ay.setApiNonce(d)}),[]),{run:r=>{if(r&&r.preventDefault(),b(!0),(()=>{const e=(0,o.select)(l.a).getWpcomUser(),t=(0,o.select)(l.a).getBlogId();e&&n.A.initialize(e.ID,e.login,{blog_id:t})})(),n.A.tracks.recordEvent(e+"_purchase_button_click",{isWpcom:v,current_version:(0,o.select)(l.a).getVersion()}),C||v)return A();k({registrationNonce:p,redirectUri:t}).then(A)},isRegistered:C||v,hasCheckoutStarted:y}}},4568:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(6450),a=r(5694),s=r(5477),c=r(6585),o=r(1703);const i={...a.Ay,...c.Ay,...o.Ay,...n.Ay,...s.Ay}},5694:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>d,iJ:()=>u,ql:()=>p});var n=r(7143),a=r(7723),s=r(6450),c=r(8795),o=r.n(c),i=r(8228),l=r(1464);const __=a.__,p="SET_JETPACK_SETTINGS";function u(e){return{type:p,options:e}}const d={updateJetpackSettings:function*(e){try{yield(0,s.HI)(),yield u({is_updating:!0}),yield u(e),yield(0,l.He)(e);const t=yield(0,l._r)();return yield u(t),(0,s.Er)(__("Updated settings.","jetpack-search-pkg"))}catch{const e=o()((0,n.select)(i.a).getSearchModuleStatus(),["module_active","instant_search_enabled"]);return yield u(e),(0,s.tg)(__("Error Update settings…","jetpack-search-pkg"))}finally{yield(0,s.hn)(),yield u({is_updating:!1})}},setJetpackSettings:u}},5477:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>s,dJ:()=>a,vJ:()=>n});const n="SET_SEARCH_PRICING";function a(e){return{type:"SET_SEARCH_PRICING",options:e}}const s={setSearchPricing:a}},6585:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>c,Rn:()=>a,uo:()=>s});var n=r(1464);const a="SET_SEARCH_PLAN_INFO";function s(e){return{type:"SET_SEARCH_PLAN_INFO",options:e}}const c={setSearchPlanInfo:s,fetchSearchPlanInfo:n.mk}},1703:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>s,Q3:()=>n,_L:()=>a});const n="SET_SEARCH_STATS";function a(e){return{type:"SET_SEARCH_STATS",options:e}}const s={setSearchStats:a}},1464:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>g,D8:()=>m,He:()=>p,_r:()=>l,cq:()=>d,mk:()=>u});var n=r(5932);const a="FETCH_JETPACK_SETTINGS",s="UPDATE_JETPACK_SETTINGS",c="FETCH_SEARCH_PLAN_INFO",o="FETCH_SEARCH_STATS",i="FETCH_SEARCH_PRICING",l=()=>({type:a}),p=e=>({type:s,settings:e});function*u(){return yield{type:c}}const d=()=>({type:o}),m=()=>({type:i}),g={[a]:function(){return n.Ay.fetchSearchSettings()},[s]:function(e){return n.Ay.updateSearchSettings(e.settings)},[c]:function(){return n.Ay.fetchSearchPlanInfo()},[o]:function(){return n.Ay.fetchSearchStats()},[i]:function(){return n.Ay.fetchSearchPricing()}}},8228:(e,t,r)=>{"use strict";r.d(t,{a:()=>i,i:()=>l});var n=r(4568),a=r(1464),s=r(8295),c=r(5267),o=r(193);const i="jetpack-search-plugin",l={reducer:s.A,actions:n.A,selectors:o.A,resolvers:c.Ay,controls:a.Ay,initialState:window.JETPACK_SEARCH_DASHBOARD_INITIAL_STATE||{}}},9597:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n=(e=[])=>e},8295:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(7143),a=r(1473),s=r(9597),c=r(4795),o=r(9696),i=r(5513),l=r(3414),p=r(6110),u=r(9585);const d=(0,n.combineReducers)({siteData:i.A,jetpackSettings:c.A,sitePlan:l.A,siteStats:p.A,userData:u.A,features:s.A,notices:a.A,searchPricing:o.A})},4795:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(5694);const a=(e={},t)=>t.type===n.ql?{...e,...t.options,is_toggling_module:e.module_active!==t.options.module_active&&!!t.options.is_updating,is_toggling_instant_search:e.instant_search_enabled!==t.options.instant_search_enabled&&!!t.options.is_updating}:e},9696:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(5477);const a=(e={},t)=>t.type===n.vJ?{...e,...t.options}:e},5513:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n=(e={})=>e},3414:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(6585);const a=(e={},t)=>t.type===n.Rn?{...e,...t.options}:e},6110:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(1703);const a=(e={},t)=>t.type===n.Q3?{...e,...t.options}:e},9585:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n=(e={})=>e},5267:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>p});var n=r(7723),a=r(6450),s=r(5694),c=r(5477),o=r(6585),i=r(1703),l=r(1464);const __=n.__;const p={getSearchModuleStatus:function*(){try{const e=yield(0,l._r)();if(e)return(0,s.iJ)(e)}catch{return(0,a.tg)(__("Error fetching settings…","jetpack-search-pkg"))}},getSearchPlanInfo:function*(){try{const e=yield(0,l.mk)();if(e)return(0,o.uo)(e)}catch{return(0,a.tg)(__("Error fetching search plan…","jetpack-search-pkg"))}},getSearchStats:function*(){try{const e=yield(0,l.cq)();if(e)return(0,i._L)(e)}catch{return(0,a.tg)(__("Error fetching search stats","jetpack-search-pkg"))}},getSearchPricing:function*(){try{const e=yield(0,l.D8)();if(e)return(0,c.dJ)(e)}catch{}}}},6155:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={isFeatureEnabled:(e,t)=>Array.isArray(e.features)&&e.features.includes(t)}},193:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(8179),a=r(6155),s=r(577),c=r(4126),o=r(3491),i=r(644),l=r(7972),p=r(8159);const u={...o.A,...s.A,...i.A,...p.A,...n.A,...a.A,...l.A,...c.A}},577:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={getSearchModuleStatus:e=>e.jetpackSettings,isModuleEnabled:e=>e.jetpackSettings.module_active,isInstantSearchEnabled:e=>e.jetpackSettings.instant_search_enabled,isUpdatingJetpackSettings:e=>e.jetpackSettings.is_updating,isTogglingModule:e=>e.jetpackSettings.is_toggling_module,isTogglingInstantSearch:e=>e.jetpackSettings.is_toggling_instant_search}},4126:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});const n=e=>e.searchPricing.full_price??0,a=e=>Number.isFinite(e.searchPricing.discount_price)?e.searchPricing.discount_price:n(e),s={getSearchPricing:e=>e.searchPricing,getPriceBefore:n,getPriceAfter:a,getPricingDiscountPercentage:e=>{const t=n(e),r=a(e);return t<=r?0:Math.round((t-r)/t*100)},getPriceCurrencyCode:e=>e.searchPricing.currency_code??"USD",isNewPricing202208:e=>e.searchPricing.pricing_version>="202208",getPaidRequestsLimit:e=>e.searchPricing.monthly_search_request_limit??1e4,getPaidRecordsLimit:e=>e.searchPricing.record_limit??1e4,getAdditionalUnitQuantity:e=>e.searchPricing.quantity_per_unit,getAdditionalUnitPrice:e=>Number.isFinite(+e.searchPricing.per_unit_fee)?+e.searchPricing.per_unit_fee/12:null}},3491:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={getAPIRootUrl:e=>e.siteData?.WP_API_root??null,getWpcomOriginApiUrl:e=>e.siteData?.wpcomOriginApiUrl??null,getAPINonce:e=>e.siteData?.WP_API_nonce??null,getRegistrationNonce:e=>e.siteData?.registrationNonce??null,getSiteAdminUrl:e=>e.siteData?.adminUrl??null,isInstantSearchPromotionActive:e=>e.siteData?.showPromotions??!0,getBlogId:e=>e.siteData?.blogId??0,getVersion:e=>e.siteData?.version??"development",getCalypsoSlug:e=>e.siteData?.calypsoSlug,getPostTypes:e=>e.siteData?.postTypes,getSiteTitle:e=>e.siteData?.title||"",isWpcom:e=>e.siteData?.isWpcom??!1,isPlanJustUpgraded:e=>e.siteData?.isPlanJustUpgraded??!1}},644:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={getSearchPlanInfo:e=>e.sitePlan,hasBusinessPlan:e=>e.sitePlan.supports_only_classic_search,isOverLimit:e=>e.sitePlan.plan_usage?.must_upgrade,supportsInstantSearch:e=>e.sitePlan.supports_instant_search,supportsOnlyClassicSearch:e=>e.sitePlan.supports_only_classic_search,getUpgradeBillPeriod:e=>e.sitePlan?.default_upgrade_bill_period,supportsSearch:e=>e.sitePlan.supports_instant_search||e.sitePlan.supports_only_classic_search,getTierMaximumRecords:e=>e.sitePlan.tier_maximum_records,isFreePlan:e=>"jetpack_search_free"===e.sitePlan.effective_subscription?.product_slug,getLatestMonthRequests:e=>e.sitePlan.plan_usage?.num_requests_3m[0],getCurrentPlan:e=>e.sitePlan.plan_current,getCurrentUsage:e=>e.sitePlan.plan_usage}},7972:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={getSearchStats:e=>e.siteStats,getPostCount:e=>e.siteStats?.post_count,getPostTypeBreakdown:e=>e.siteStats?.post_type_breakdown,getLastIndexedDate:e=>e.siteStats?.last_indexed_date}},8159:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n={getWpcomUser:e=>e.userData?.currentUser?.wpcomUser}},516:e=>{"use strict";e.exports={consumer_slug:"jetpack-search"}},9384:e=>{"use strict";e.exports=window.JetpackConnection},7999:e=>{"use strict";e.exports=window.JetpackScriptDataModule},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},8468:e=>{"use strict";e.exports=window.lodash},1455:e=>{"use strict";e.exports=window.wp.apiFetch},6427:e=>{"use strict";e.exports=window.wp.components},9491:e=>{"use strict";e.exports=window.wp.compose},7143:e=>{"use strict";e.exports=window.wp.data},8443:e=>{"use strict";e.exports=window.wp.date},8490:e=>{"use strict";e.exports=window.wp.domReady},6087:e=>{"use strict";e.exports=window.wp.element},2619:e=>{"use strict";e.exports=window.wp.hooks},7723:e=>{"use strict";e.exports=window.wp.i18n},2279:e=>{"use strict";e.exports=window.wp.plugins},5573:e=>{"use strict";e.exports=window.wp.primitives},3832:e=>{"use strict";e.exports=window.wp.url},6072:e=>{function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},3136:(e,t,r)=>{"use strict";var n=r(2003),a=r(561),s=TypeError;e.exports=function(e){if(n(e))return e;throw new s(a(e)+" is not a function")}},3773:(e,t,r)=>{"use strict";var n=r(2480),a=String,s=TypeError;e.exports=function(e){if(n(e))return e;throw new s(a(e)+" is not an object")}},8419:(e,t,r)=>{"use strict";var n=r(9351),a=r(1512),s=r(9496),c=function(e){return function(t,r,c){var o=n(t),i=s(o);if(0===i)return!e&&-1;var l,p=a(c,i);if(e&&r!=r){for(;i>p;)if((l=o[p++])!=l)return!0}else for(;i>p;p++)if((e||p in o)&&o[p]===r)return e||p||0;return!e&&-1}};e.exports={includes:c(!0),indexOf:c(!1)}},2625:(e,t,r)=>{"use strict";var n=r(7910),a=r(5866),s=TypeError,c=Object.getOwnPropertyDescriptor,o=n&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}();e.exports=o?function(e,t){if(a(e)&&!c(e,"length").writable)throw new s("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t}},8278:(e,t,r)=>{"use strict";var n=r(2322),a=n({}.toString),s=n("".slice);e.exports=function(e){return s(a(e),8,-1)}},1038:(e,t,r)=>{"use strict";var n=r(867),a=r(8337),s=r(2457),c=r(2931);e.exports=function(e,t,r){for(var o=a(t),i=c.f,l=s.f,p=0;p<o.length;p++){var u=o[p];n(e,u)||r&&n(r,u)||i(e,u,l(t,u))}}},1781:(e,t,r)=>{"use strict";var n=r(7910),a=r(2931),s=r(5762);e.exports=n?function(e,t,r){return a.f(e,t,s(1,r))}:function(e,t,r){return e[t]=r,e}},5762:e=>{"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},4386:(e,t,r)=>{"use strict";var n=r(2003),a=r(2931),s=r(2609),c=r(9447);e.exports=function(e,t,r,o){o||(o={});var i=o.enumerable,l=void 0!==o.name?o.name:t;if(n(r)&&s(r,l,o),o.global)i?e[t]=r:c(t,r);else{try{o.unsafe?e[t]&&(i=!0):delete e[t]}catch(e){}i?e[t]=r:a.f(e,t,{value:r,enumerable:!1,configurable:!o.nonConfigurable,writable:!o.nonWritable})}return e}},9447:(e,t,r)=>{"use strict";var n=r(642),a=Object.defineProperty;e.exports=function(e,t){try{a(n,e,{value:t,configurable:!0,writable:!0})}catch(r){n[e]=t}return t}},7910:(e,t,r)=>{"use strict";var n=r(6977);e.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},6337:(e,t,r)=>{"use strict";var n=r(642),a=r(2480),s=n.document,c=a(s)&&a(s.createElement);e.exports=function(e){return c?s.createElement(e):{}}},3163:e=>{"use strict";var t=TypeError;e.exports=function(e){if(e>9007199254740991)throw t("Maximum allowed index exceeded");return e}},2589:e=>{"use strict";e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},8093:(e,t,r)=>{"use strict";var n=r(642).navigator,a=n&&n.userAgent;e.exports=a?String(a):""},4965:(e,t,r)=>{"use strict";var n,a,s=r(642),c=r(8093),o=s.process,i=s.Deno,l=o&&o.versions||i&&i.version,p=l&&l.v8;p&&(a=(n=p.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!a&&c&&(!(n=c.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=c.match(/Chrome\/(\d+)/))&&(a=+n[1]),e.exports=a},9948:(e,t,r)=>{"use strict";var n=r(642),a=r(2457).f,s=r(1781),c=r(4386),o=r(9447),i=r(1038),l=r(4866);e.exports=function(e,t){var r,p,u,d,m,g=e.target,h=e.global,f=e.stat;if(r=h?n:f?n[g]||o(g,{}):n[g]&&n[g].prototype)for(p in t){if(d=t[p],u=e.dontCallGetSet?(m=a(r,p))&&m.value:r[p],!l(h?p:g+(f?".":"#")+p,e.forced)&&void 0!==u){if(typeof d==typeof u)continue;i(d,u)}(e.sham||u&&u.sham)&&s(d,"sham",!0),c(r,p,d,e)}}},6977:e=>{"use strict";e.exports=function(e){try{return!!e()}catch(e){return!0}}},1658:(e,t,r)=>{"use strict";var n=r(6977);e.exports=!n((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},7623:(e,t,r)=>{"use strict";var n=r(1658),a=Function.prototype.call;e.exports=n?a.bind(a):function(){return a.apply(a,arguments)}},7016:(e,t,r)=>{"use strict";var n=r(7910),a=r(867),s=Function.prototype,c=n&&Object.getOwnPropertyDescriptor,o=a(s,"name"),i=o&&"something"===function(){}.name,l=o&&(!n||n&&c(s,"name").configurable);e.exports={EXISTS:o,PROPER:i,CONFIGURABLE:l}},2322:(e,t,r)=>{"use strict";var n=r(1658),a=Function.prototype,s=a.call,c=n&&a.bind.bind(s,s);e.exports=n?c:function(e){return function(){return s.apply(e,arguments)}}},6297:(e,t,r)=>{"use strict";var n=r(642),a=r(2003);e.exports=function(e,t){return arguments.length<2?(r=n[e],a(r)?r:void 0):n[e]&&n[e][t];var r}},8396:(e,t,r)=>{"use strict";var n=r(3136),a=r(9943);e.exports=function(e,t){var r=e[t];return a(r)?void 0:n(r)}},642:function(e){"use strict";var t=function(e){return e&&e.Math===Math&&e};e.exports=t("object"==typeof globalThis&&globalThis)||t("object"==typeof window&&window)||t("object"==typeof self&&self)||t("object"==typeof window&&window)||t("object"==typeof this&&this)||function(){return this}()||Function("return this")()},867:(e,t,r)=>{"use strict";var n=r(2322),a=r(4707),s=n({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return s(a(e),t)}},8555:e=>{"use strict";e.exports={}},2159:(e,t,r)=>{"use strict";var n=r(7910),a=r(6977),s=r(6337);e.exports=!n&&!a((function(){return 7!==Object.defineProperty(s("div"),"a",{get:function(){return 7}}).a}))},9233:(e,t,r)=>{"use strict";var n=r(2322),a=r(6977),s=r(8278),c=Object,o=n("".split);e.exports=a((function(){return!c("z").propertyIsEnumerable(0)}))?function(e){return"String"===s(e)?o(e,""):c(e)}:c},2716:(e,t,r)=>{"use strict";var n=r(2322),a=r(2003),s=r(9487),c=n(Function.toString);a(s.inspectSource)||(s.inspectSource=function(e){return c(e)}),e.exports=s.inspectSource},5147:(e,t,r)=>{"use strict";var n,a,s,c=r(204),o=r(642),i=r(2480),l=r(1781),p=r(867),u=r(9487),d=r(8777),m=r(8555),g="Object already initialized",h=o.TypeError,f=o.WeakMap;if(c||u.state){var v=u.state||(u.state=new f);v.get=v.get,v.has=v.has,v.set=v.set,n=function(e,t){if(v.has(e))throw new h(g);return t.facade=e,v.set(e,t),t},a=function(e){return v.get(e)||{}},s=function(e){return v.has(e)}}else{var y=d("state");m[y]=!0,n=function(e,t){if(p(e,y))throw new h(g);return t.facade=e,l(e,y,t),t},a=function(e){return p(e,y)?e[y]:{}},s=function(e){return p(e,y)}}e.exports={set:n,get:a,has:s,enforce:function(e){return s(e)?a(e):n(e,{})},getterFor:function(e){return function(t){var r;if(!i(t)||(r=a(t)).type!==e)throw new h("Incompatible receiver, "+e+" required");return r}}}},5866:(e,t,r)=>{"use strict";var n=r(8278);e.exports=Array.isArray||function(e){return"Array"===n(e)}},2003:e=>{"use strict";var t="object"==typeof document&&document.all;e.exports=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},4866:(e,t,r)=>{"use strict";var n=r(6977),a=r(2003),s=/#|\.prototype\./,c=function(e,t){var r=i[o(e)];return r===p||r!==l&&(a(t)?n(t):!!t)},o=c.normalize=function(e){return String(e).replace(s,".").toLowerCase()},i=c.data={},l=c.NATIVE="N",p=c.POLYFILL="P";e.exports=c},9943:e=>{"use strict";e.exports=function(e){return null==e}},2480:(e,t,r)=>{"use strict";var n=r(2003);e.exports=function(e){return"object"==typeof e?null!==e:n(e)}},957:e=>{"use strict";e.exports=!1},9895:(e,t,r)=>{"use strict";var n=r(6297),a=r(2003),s=r(8599),c=r(4150),o=Object;e.exports=c?function(e){return"symbol"==typeof e}:function(e){var t=n("Symbol");return a(t)&&s(t.prototype,o(e))}},9496:(e,t,r)=>{"use strict";var n=r(2748);e.exports=function(e){return n(e.length)}},2609:(e,t,r)=>{"use strict";var n=r(2322),a=r(6977),s=r(2003),c=r(867),o=r(7910),i=r(7016).CONFIGURABLE,l=r(2716),p=r(5147),u=p.enforce,d=p.get,m=String,g=Object.defineProperty,h=n("".slice),f=n("".replace),v=n([].join),y=o&&!a((function(){return 8!==g((function(){}),"length",{value:8}).length})),b=String(String).split("String"),k=e.exports=function(e,t,r){"Symbol("===h(m(t),0,7)&&(t="["+f(m(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(t="get "+t),r&&r.setter&&(t="set "+t),(!c(e,"name")||i&&e.name!==t)&&(o?g(e,"name",{value:t,configurable:!0}):e.name=t),y&&r&&c(r,"arity")&&e.length!==r.arity&&g(e,"length",{value:r.arity});try{r&&c(r,"constructor")&&r.constructor?o&&g(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var n=u(e);return c(n,"source")||(n.source=v(b,"string"==typeof t?t:"")),e};Function.prototype.toString=k((function(){return s(this)&&d(this).source||l(this)}),"toString")},5983:e=>{"use strict";var t=Math.ceil,r=Math.floor;e.exports=Math.trunc||function(e){var n=+e;return(n>0?r:t)(n)}},2931:(e,t,r)=>{"use strict";var n=r(7910),a=r(2159),s=r(8576),c=r(3773),o=r(8543),i=TypeError,l=Object.defineProperty,p=Object.getOwnPropertyDescriptor,u="enumerable",d="configurable",m="writable";t.f=n?s?function(e,t,r){if(c(e),t=o(t),c(r),"function"==typeof e&&"prototype"===t&&"value"in r&&m in r&&!r[m]){var n=p(e,t);n&&n[m]&&(e[t]=r.value,r={configurable:d in r?r[d]:n[d],enumerable:u in r?r[u]:n[u],writable:!1})}return l(e,t,r)}:l:function(e,t,r){if(c(e),t=o(t),c(r),a)try{return l(e,t,r)}catch(e){}if("get"in r||"set"in r)throw new i("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},2457:(e,t,r)=>{"use strict";var n=r(7910),a=r(7623),s=r(6691),c=r(5762),o=r(9351),i=r(8543),l=r(867),p=r(2159),u=Object.getOwnPropertyDescriptor;t.f=n?u:function(e,t){if(e=o(e),t=i(t),p)try{return u(e,t)}catch(e){}if(l(e,t))return c(!a(s.f,e,t),e[t])}},9038:(e,t,r)=>{"use strict";var n=r(2846),a=r(2589).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return n(e,a)}},3683:(e,t)=>{"use strict";t.f=Object.getOwnPropertySymbols},8599:(e,t,r)=>{"use strict";var n=r(2322);e.exports=n({}.isPrototypeOf)},2846:(e,t,r)=>{"use strict";var n=r(2322),a=r(867),s=r(9351),c=r(8419).indexOf,o=r(8555),i=n([].push);e.exports=function(e,t){var r,n=s(e),l=0,p=[];for(r in n)!a(o,r)&&a(n,r)&&i(p,r);for(;t.length>l;)a(n,r=t[l++])&&(~c(p,r)||i(p,r));return p}},6691:(e,t)=>{"use strict";var r={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,a=n&&!r.call({1:2},1);t.f=a?function(e){var t=n(this,e);return!!t&&t.enumerable}:r},6772:(e,t,r)=>{"use strict";var n=r(7623),a=r(2003),s=r(2480),c=TypeError;e.exports=function(e,t){var r,o;if("string"===t&&a(r=e.toString)&&!s(o=n(r,e)))return o;if(a(r=e.valueOf)&&!s(o=n(r,e)))return o;if("string"!==t&&a(r=e.toString)&&!s(o=n(r,e)))return o;throw new c("Can't convert object to primitive value")}},8337:(e,t,r)=>{"use strict";var n=r(6297),a=r(2322),s=r(9038),c=r(3683),o=r(3773),i=a([].concat);e.exports=n("Reflect","ownKeys")||function(e){var t=s.f(o(e)),r=c.f;return r?i(t,r(e)):t}},3384:(e,t,r)=>{"use strict";var n=r(9943),a=TypeError;e.exports=function(e){if(n(e))throw new a("Can't call method on "+e);return e}},8777:(e,t,r)=>{"use strict";var n=r(4335),a=r(1026),s=n("keys");e.exports=function(e){return s[e]||(s[e]=a(e))}},9487:(e,t,r)=>{"use strict";var n=r(957),a=r(642),s=r(9447),c="__core-js_shared__",o=e.exports=a[c]||s(c,{});(o.versions||(o.versions=[])).push({version:"3.38.1",mode:n?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.38.1/LICENSE",source:"https://github.com/zloirock/core-js"})},4335:(e,t,r)=>{"use strict";var n=r(9487);e.exports=function(e,t){return n[e]||(n[e]=t||{})}},789:(e,t,r)=>{"use strict";var n=r(4965),a=r(6977),s=r(642).String;e.exports=!!Object.getOwnPropertySymbols&&!a((function(){var e=Symbol("symbol detection");return!s(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},1512:(e,t,r)=>{"use strict";var n=r(6709),a=Math.max,s=Math.min;e.exports=function(e,t){var r=n(e);return r<0?a(r+t,0):s(r,t)}},9351:(e,t,r)=>{"use strict";var n=r(9233),a=r(3384);e.exports=function(e){return n(a(e))}},6709:(e,t,r)=>{"use strict";var n=r(5983);e.exports=function(e){var t=+e;return t!=t||0===t?0:n(t)}},2748:(e,t,r)=>{"use strict";var n=r(6709),a=Math.min;e.exports=function(e){var t=n(e);return t>0?a(t,9007199254740991):0}},4707:(e,t,r)=>{"use strict";var n=r(3384),a=Object;e.exports=function(e){return a(n(e))}},4603:(e,t,r)=>{"use strict";var n=r(7623),a=r(2480),s=r(9895),c=r(8396),o=r(6772),i=r(7369),l=TypeError,p=i("toPrimitive");e.exports=function(e,t){if(!a(e)||s(e))return e;var r,i=c(e,p);if(i){if(void 0===t&&(t="default"),r=n(i,e,t),!a(r)||s(r))return r;throw new l("Can't convert object to primitive value")}return void 0===t&&(t="number"),o(e,t)}},8543:(e,t,r)=>{"use strict";var n=r(4603),a=r(9895);e.exports=function(e){var t=n(e,"string");return a(t)?t:t+""}},561:e=>{"use strict";var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},1026:(e,t,r)=>{"use strict";var n=r(2322),a=0,s=Math.random(),c=n(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+c(++a+s,36)}},4150:(e,t,r)=>{"use strict";var n=r(789);e.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},8576:(e,t,r)=>{"use strict";var n=r(7910),a=r(6977);e.exports=n&&a((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},204:(e,t,r)=>{"use strict";var n=r(642),a=r(2003),s=n.WeakMap;e.exports=a(s)&&/native code/.test(String(s))},7369:(e,t,r)=>{"use strict";var n=r(642),a=r(4335),s=r(867),c=r(1026),o=r(789),i=r(4150),l=n.Symbol,p=a("wks"),u=i?l.for||l:l&&l.withoutSetter||c;e.exports=function(e){return s(p,e)||(p[e]=o&&s(l,e)?l[e]:u("Symbol."+e)),p[e]}},9060:(e,t,r)=>{"use strict";var n=r(9948),a=r(4707),s=r(9496),c=r(2625),o=r(3163);n({target:"Array",proto:!0,arity:1,forced:r(6977)((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}}()},{push:function(e){var t=a(this),r=s(t),n=arguments.length;o(r+n);for(var i=0;i<n;i++)t[r]=arguments[i],r++;return c(t,r),r}})},3022:(e,t,r)=>{"use strict";function n(e){var t,r,a="";if("string"==typeof e||"number"==typeof e)a+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(r=n(e[t]))&&(a&&(a+=" "),a+=r)}else for(r in e)e[r]&&(a&&(a+=" "),a+=r);return a}r.d(t,{A:()=>a});const a=function(){for(var e,t,r=0,a="",s=arguments.length;r<s;r++)(e=arguments[r])&&(t=n(e))&&(a&&(a+=" "),a+=t);return a}},8377:e=>{"use strict";e.exports=JSON.parse('{"version":"4.1.0","colors":{"White":"#fff","Black":"#000","Gray 0":"#f6f7f7","Gray 5":"#dcdcde","Gray 10":"#c3c4c7","Gray 20":"#a7aaad","Gray 30":"#8c8f94","Gray 40":"#787c82","Gray 50":"#646970","Gray 60":"#50575e","Gray 70":"#3c434a","Gray 80":"#2c3338","Gray 90":"#1d2327","Gray 100":"#101517","Gray":"#646970","Blue 0":"#fbfcfe","Blue 5":"#f7f8fe","Blue 10":"#d6ddf9","Blue 20":"#adbaf3","Blue 30":"#7b90ff","Blue 40":"#546ff3","Blue 50":"#3858e9","Blue 60":"#2a46ce","Blue 70":"#1d35b4","Blue 80":"#1f3286","Blue 90":"#14215a","Blue 100":"#0a112d","Blue":"#3858e9","WordPress Blue 0":"#fbfcfe","WordPress Blue 5":"#f7f8fe","WordPress Blue 10":"#d6ddf9","WordPress Blue 20":"#adbaf3","WordPress Blue 30":"#7b90ff","WordPress Blue 40":"#546ff3","WordPress Blue 50":"#3858e9","WordPress Blue 60":"#2a46ce","WordPress Blue 70":"#1d35b4","WordPress Blue 80":"#1f3286","WordPress Blue 90":"#14215a","WordPress Blue 100":"#0a112d","WordPress Blue":"#3858e9","Purple 0":"#f2e9ed","Purple 5":"#ebcee0","Purple 10":"#e3afd5","Purple 20":"#d48fc8","Purple 30":"#c475bd","Purple 40":"#b35eb1","Purple 50":"#984a9c","Purple 60":"#7c3982","Purple 70":"#662c6e","Purple 80":"#4d2054","Purple 90":"#35163b","Purple 100":"#1e0c21","Purple":"#984a9c","Pink 0":"#f5e9ed","Pink 5":"#f2ceda","Pink 10":"#f7a8c3","Pink 20":"#f283aa","Pink 30":"#eb6594","Pink 40":"#e34c84","Pink 50":"#c9356e","Pink 60":"#ab235a","Pink 70":"#8c1749","Pink 80":"#700f3b","Pink 90":"#4f092a","Pink 100":"#260415","Pink":"#c9356e","Red 0":"#f7ebec","Red 5":"#facfd2","Red 10":"#ffabaf","Red 20":"#ff8085","Red 30":"#f86368","Red 40":"#e65054","Red 50":"#d63638","Red 60":"#b32d2e","Red 70":"#8a2424","Red 80":"#691c1c","Red 90":"#451313","Red 100":"#240a0a","Red":"#d63638","Orange 0":"#f5ece6","Orange 5":"#f7dcc6","Orange 10":"#ffbf86","Orange 20":"#faa754","Orange 30":"#e68b28","Orange 40":"#d67709","Orange 50":"#b26200","Orange 60":"#8a4d00","Orange 70":"#704000","Orange 80":"#543100","Orange 90":"#361f00","Orange 100":"#1f1200","Orange":"#b26200","Yellow 0":"#f5f1e1","Yellow 5":"#f5e6b3","Yellow 10":"#f2d76b","Yellow 20":"#f0c930","Yellow 30":"#deb100","Yellow 40":"#c08c00","Yellow 50":"#9d6e00","Yellow 60":"#7d5600","Yellow 70":"#674600","Yellow 80":"#4f3500","Yellow 90":"#320","Yellow 100":"#1c1300","Yellow":"#9d6e00","Green 0":"#e6f2e8","Green 5":"#b8e6bf","Green 10":"#68de86","Green 20":"#1ed15a","Green 30":"#00ba37","Green 40":"#00a32a","Green 50":"#008a20","Green 60":"#007017","Green 70":"#005c12","Green 80":"#00450c","Green 90":"#003008","Green 100":"#001c05","Green":"#008a20","Celadon 0":"#e4f2ed","Celadon 5":"#a7e8d3","Celadon 10":"#66deb9","Celadon 20":"#31cc9f","Celadon 30":"#09b585","Celadon 40":"#009e73","Celadon 50":"#008763","Celadon 60":"#007053","Celadon 70":"#005c44","Celadon 80":"#004533","Celadon 90":"#003024","Celadon 100":"#001c15","Celadon":"#008763","Automattic Blue 0":"#ebf4fa","Automattic Blue 5":"#c4e2f5","Automattic Blue 10":"#88ccf2","Automattic Blue 20":"#5ab7e8","Automattic Blue 30":"#24a3e0","Automattic Blue 40":"#1490c7","Automattic Blue 50":"#0277a8","Automattic Blue 60":"#036085","Automattic Blue 70":"#02506e","Automattic Blue 80":"#02384d","Automattic Blue 90":"#022836","Automattic Blue 100":"#021b24","Automattic Blue":"#24a3e0","Simplenote Blue 0":"#e9ecf5","Simplenote Blue 5":"#ced9f2","Simplenote Blue 10":"#abc1f5","Simplenote Blue 20":"#84a4f0","Simplenote Blue 30":"#618df2","Simplenote Blue 40":"#4678eb","Simplenote Blue 50":"#3361cc","Simplenote Blue 60":"#1d4fc4","Simplenote Blue 70":"#113ead","Simplenote Blue 80":"#0d2f85","Simplenote Blue 90":"#09205c","Simplenote Blue 100":"#05102e","Simplenote Blue":"#3361cc","WooCommerce Purple 0":"#f2edff","WooCommerce Purple 5":"#e1d7ff","WooCommerce Purple 10":"#d1c1ff","WooCommerce Purple 20":"#b999ff","WooCommerce Purple 30":"#a77eff","WooCommerce Purple 40":"#873eff","WooCommerce Purple 50":"#720eec","WooCommerce Purple 60":"#6108ce","WooCommerce Purple 70":"#5007aa","WooCommerce Purple 80":"#3c087e","WooCommerce Purple 90":"#2c045d","WooCommerce Purple 100":"#1f0342","WooCommerce Purple":"#720eec","Jetpack Green 0":"#f0f2eb","Jetpack Green 5":"#d0e6b8","Jetpack Green 10":"#9dd977","Jetpack Green 20":"#64ca43","Jetpack Green 30":"#2fb41f","Jetpack Green 40":"#069e08","Jetpack Green 50":"#008710","Jetpack Green 60":"#007117","Jetpack Green 70":"#005b18","Jetpack Green 80":"#004515","Jetpack Green 90":"#003010","Jetpack Green 100":"#001c09","Jetpack Green":"#069e08"}}')}},t={};function r(n){var a=t[n];if(void 0!==a)return a.exports;var s=t[n]={id:n,loaded:!1,exports:{}};return e[n].call(s.exports,s,s.exports,r),s.loaded=!0,s.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{"use strict";var e=r(7143),t=r(6087),n=r(1609),a=r.n(n),s=r(496),c=r(8228);const o=(0,e.createReduxStore)(c.a,c.i);function i(){const e=document.getElementById("jp-search-dashboard");null!==e&&t.createRoot(e).render(a().createElement(s.A,null))}(0,e.register)(o),"loading"!==document.readyState?i():document.addEventListener("DOMContentLoaded",i)})()})();