<?php
/**
 * Class Main Init
 *
 * @package um_ext\um_stripe\UM_Stripe_API
 */

use um_ext\um_stripe\Functions;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class  UM_Stripe_API
 *
 * @package um_ext\um_stripe
 */
final class UM_Stripe_API extends Functions {


	/**
	 * Stripe extension slug
	 *
	 * @since 1.0.0
	 * @var string
	 */
	private $slug = 'stripe';


	/**
	 * Instance variable
	 *
	 * @since 1.0.0
	 * @var object
	 */
	private static $instance;


	/**
	 * Instance function
	 *
	 * @since 1.0.0
	 * @return Init
	 */
	public static function instance() {

		if ( is_null( self::$instance ) ) {
			self::$instance = new self();
		}
		return self::$instance;
	}


	/**
	 * Init constructor.
	 *
	 * @since 1.0.0
	 */
	public function __construct() {

		if ( empty( self::$price_option ) ) {
			self::$price_option = get_option( 'um_stripe_prices_cache' );
		}

		$this->berlindb();

		$this->common()->includes();

		if ( UM()->is_request( 'ajax' ) ) {
			$this->ajax()->includes();
			$this->frontend()->includes();
		} elseif ( UM()->is_request( 'admin' ) ) {
			$this->admin()->includes();
		} elseif ( UM()->is_request( 'frontend' ) ) {
			$this->frontend()->includes();
		}
	}


	/**
	 * Common class
	 *
	 * @since 1.0.0
	 * @return includes\common\Init()
	 */
	public function common() {
		if ( empty( UM()->classes['um_ext\um_stripe\includes\common\init'] ) ) {
			UM()->classes['um_ext\um_stripe\includes\common\init'] = new um_ext\um_stripe\common\Init();
		}
		return UM()->classes['um_ext\um_stripe\includes\common\init'];
	}


	/**
	 * Admin Init class
	 *
	 * @since 1.0.0
	 * @return includes\admin\Init()
	 */
	public function admin() {
		if ( empty( UM()->classes['um_ext\um_stripe\includes\admin\init'] ) ) {
			UM()->classes['um_ext\um_stripe\includes\admin\init'] = new um_ext\um_stripe\admin\Init();
		}
		return UM()->classes['um_ext\um_stripe\includes\admin\init'];
	}


	/**
	 * Ajax class
	 *
	 * @since 1.0.0
	 * @return includes\ajax\Init()
	 */
	public function ajax() {
		if ( empty( UM()->classes['um_ext\um_stripe\includes\ajax\init'] ) ) {
			UM()->classes['um_ext\um_stripe\includes\ajax\init'] = new um_ext\um_stripe\ajax\Init();
		}
		return UM()->classes['um_ext\um_stripe\includes\ajax\init'];
	}


	/**
	 * Frontend class
	 *
	 * @since 1.0.0
	 * @return includes\frontend\Init()
	 */
	public function frontend() {
		if ( empty( UM()->classes['um_ext\um_stripe\includes\frontend\init'] ) ) {
			UM()->classes['um_ext\um_stripe\includes\frontend\init'] = new um_ext\um_stripe\frontend\Init();
		}
		return UM()->classes['um_ext\um_stripe\includes\frontend\init'];
	}

	/**
	 *  Initialize BerlinDB
	 *
	 * @since 1.0.0
	 * @return Init()
	 */
	public function berlindb() {
		if ( empty( UM()->classes['um_ext\um_stripe\includes\db\init'] ) ) {
			UM()->classes['um_ext\um_stripe\includes\db\init'] = new um_ext\um_stripe\db\Init();
		}
		return UM()->classes['um_ext\um_stripe\includes\db\init'];
	}
}

/**
 * Create class variable
 *
 * Initialize the Stripe API class.
 *
 * @since 1.0
 */
function um_stripe_init() {

	if ( function_exists( 'UM' ) ) {
		UM()->set_class( 'Stripe_API', true );
	}
}
add_action( 'plugins_loaded', 'um_stripe_init', -10, 1 );
