# Copyright (C) 2024 Ultimate Member
# This file is distributed under the same license as the Ultimate Member - User Notes plugin.
msgid ""
msgstr ""
"Project-Id-Version: Ultimate Member - User Notes 1.1.5\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/notes\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-11-05T09:35:40+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: um-user-notes\n"

#. Plugin Name of the plugin
#: um-user-notes.php
msgid "Ultimate Member - User Notes"
msgstr ""

#. Plugin URI of the plugin
#: um-user-notes.php
msgid "http://ultimatemember.com/extensions/user-notes"
msgstr ""

#. Description of the plugin
#: um-user-notes.php
msgid "Let users create notes via their profile."
msgstr ""

#. Author of the plugin
#: um-user-notes.php
msgid "Ultimate Member"
msgstr ""

#. Author URI of the plugin
#: um-user-notes.php
msgid "http://ultimatemember.com/"
msgstr ""

#: includes/admin/class-admin.php:39
#: um-user-notes.php:82
msgid "User Notes License Key"
msgstr ""

#: includes/admin/class-admin.php:46
#: includes/admin/class-admin.php:154
msgid "User Notes"
msgstr ""

#: includes/admin/class-admin.php:52
msgid "Notes per page"
msgstr ""

#: includes/admin/class-admin.php:59
msgid "Thumbnail image size"
msgstr ""

#: includes/admin/class-admin.php:66
msgid "Excerpt length"
msgstr ""

#: includes/admin/class-admin.php:73
msgid "Read more text"
msgstr ""

#: includes/admin/class-admin.php:80
msgid "Load more text"
msgstr ""

#: includes/admin/class-admin.php:86
#: includes/admin/templates/role/notes.php:28
msgid "Enable download notes"
msgstr ""

#: includes/admin/class-admin.php:92
msgid "Download button title"
msgstr ""

#: includes/admin/class-admin.php:99
#: includes/admin/templates/role/notes.php:35
msgid "Enable print notes"
msgstr ""

#: includes/admin/class-admin.php:105
msgid "Print button title"
msgstr ""

#: includes/admin/class-admin.php:112
msgid "Enable print/download notes for non-logged in users"
msgstr ""

#: includes/admin/class-admin.php:118
msgid "Save/print buttons position"
msgstr ""

#: includes/admin/class-admin.php:120
msgid "Top left"
msgstr ""

#: includes/admin/class-admin.php:121
msgid "Top right"
msgstr ""

#: includes/admin/class-admin.php:122
msgid "Bottom left"
msgstr ""

#: includes/admin/class-admin.php:123
msgid "Bottom right"
msgstr ""

#: includes/admin/class-admin.php:131
msgid "Set button type"
msgstr ""

#: includes/admin/class-admin.php:133
msgid "Icon"
msgstr ""

#: includes/admin/class-admin.php:134
msgid "Text"
msgstr ""

#: includes/admin/class-admin.php:135
msgid "Icon + text"
msgstr ""

#: includes/admin/templates/role/notes.php:21
msgid "Disable notes feature?"
msgstr ""

#: includes/admin/templates/role/notes.php:22
msgid "Can this role have notes feature?"
msgstr ""

#: includes/core/class-activity.php:342
msgid "New note"
msgstr ""

#: includes/core/class-ajax.php:73
#: includes/core/class-ajax.php:178
#: includes/core/class-ajax.php:192
#: includes/core/class-ajax.php:269
#: includes/core/class-ajax.php:291
#: includes/core/class-ajax.php:319
#: includes/core/class-ajax.php:438
#: includes/core/class-ajax.php:494
#: includes/core/class-ajax.php:572
msgid "Invalid request"
msgstr ""

#: includes/core/class-ajax.php:77
#: includes/core/class-ajax.php:196
#: includes/core/class-ajax.php:295
#: includes/core/class-ajax.php:442
msgid "Invalid user"
msgstr ""

#: includes/core/class-ajax.php:81
#: includes/core/class-ajax.php:200
msgid "Title is required"
msgstr ""

#: includes/core/class-ajax.php:85
#: includes/core/class-ajax.php:204
msgid "Content is required"
msgstr ""

#: includes/core/class-ajax.php:89
#: includes/core/class-ajax.php:208
msgid "Please select visibility option."
msgstr ""

#: includes/core/class-ajax.php:94
msgid "You are not authorized to update this note."
msgstr ""

#. translators: %s is a file type
#: includes/core/class-ajax.php:99
#: includes/core/class-ajax.php:212
msgid "%s files are not allowed"
msgstr ""

#: includes/core/class-ajax.php:172
#: includes/core/class-ajax.php:263
msgid "Problem uploading file."
msgstr ""

#: includes/core/class-ajax.php:300
msgid "You are not authorized to delete this note."
msgstr ""

#: includes/core/class-ajax.php:327
msgid "Invalid nonce."
msgstr ""

#: includes/core/class-ajax.php:333
msgid "You are not authorized to view this note."
msgstr ""

#: includes/core/class-ajax.php:380
#: includes/core/class-ajax.php:383
#: includes/core/class-setup.php:34
msgid "Download"
msgstr ""

#: includes/core/class-ajax.php:391
#: includes/core/class-ajax.php:394
#: includes/core/class-setup.php:36
msgid "Print"
msgstr ""

#: includes/core/class-ajax.php:447
#: includes/core/class-ajax.php:452
msgid "You are not authorized to edit this note."
msgstr ""

#: includes/core/class-ajax.php:576
#: includes/core/class-ajax.php:583
msgid "Invalid ID"
msgstr ""

#: includes/core/class-ajax.php:587
msgid "You are not authorized to download this note."
msgstr ""

#: includes/core/class-posttype.php:39
msgctxt "Post Type General Name"
msgid "Notes"
msgstr ""

#: includes/core/class-posttype.php:40
msgctxt "Post Type Singular Name"
msgid "Note"
msgstr ""

#: includes/core/class-posttype.php:41
#: includes/core/class-posttype.php:42
#: includes/core/class-profile.php:64
msgid "Notes"
msgstr ""

#: includes/core/class-posttype.php:43
msgid "Item Archives"
msgstr ""

#: includes/core/class-posttype.php:44
msgid "Item Attributes"
msgstr ""

#: includes/core/class-posttype.php:45
msgid "Parent Item:"
msgstr ""

#: includes/core/class-posttype.php:46
msgid "All Items"
msgstr ""

#: includes/core/class-posttype.php:47
msgid "Add New Item"
msgstr ""

#: includes/core/class-posttype.php:48
msgid "Add New"
msgstr ""

#: includes/core/class-posttype.php:49
msgid "New Item"
msgstr ""

#: includes/core/class-posttype.php:50
msgid "Edit Item"
msgstr ""

#: includes/core/class-posttype.php:51
msgid "Update Item"
msgstr ""

#: includes/core/class-posttype.php:52
msgid "View Item"
msgstr ""

#: includes/core/class-posttype.php:53
msgid "View Items"
msgstr ""

#: includes/core/class-posttype.php:54
msgid "Search Item"
msgstr ""

#: includes/core/class-posttype.php:55
msgid "Not found"
msgstr ""

#: includes/core/class-posttype.php:56
msgid "Not found in Trash"
msgstr ""

#: includes/core/class-posttype.php:57
msgid "Featured Image"
msgstr ""

#: includes/core/class-posttype.php:58
msgid "Set featured image"
msgstr ""

#: includes/core/class-posttype.php:59
msgid "Remove featured image"
msgstr ""

#: includes/core/class-posttype.php:60
msgid "Use as featured image"
msgstr ""

#: includes/core/class-posttype.php:62
msgid "Uploaded to this item"
msgstr ""

#: includes/core/class-posttype.php:63
msgid "Items list"
msgstr ""

#: includes/core/class-posttype.php:64
msgid "Items list navigation"
msgstr ""

#: includes/core/class-posttype.php:65
msgid "Filter items list"
msgstr ""

#: includes/core/class-posttype.php:71
msgid "Note"
msgstr ""

#: includes/core/class-posttype.php:72
msgid "User notes"
msgstr ""

#: includes/core/class-profile.php:84
#: includes/core/class-profile.php:92
msgid "View notes"
msgstr ""

#: includes/core/class-profile.php:87
#: includes/core/class-profile.php:93
msgid "Add note"
msgstr ""

#: includes/core/class-setup.php:31
msgid "Read more"
msgstr ""

#: includes/core/class-setup.php:32
msgid "Load more"
msgstr ""

#: includes/core/class-shortcodes.php:142
#: includes/core/class-shortcodes.php:154
msgid "Invalid Note ID."
msgstr ""

#: templates/activity/new-note.php:15
msgid "just created a new"
msgstr ""

#: templates/activity/new-note.php:15
msgid "note"
msgstr ""

#: templates/profile/add.php:22
#: templates/profile/edit.php:43
msgid "Remove Photo"
msgstr ""

#: templates/profile/add.php:25
#: templates/profile/add.php:27
#: templates/profile/edit.php:48
#: templates/profile/edit.php:50
msgid "Add Photo"
msgstr ""

#: templates/profile/add.php:26
#: templates/profile/edit.php:49
#: templates/profile/edit.php:50
msgid "Edit Photo"
msgstr ""

#: templates/profile/add.php:34
msgid "Title"
msgstr ""

#: templates/profile/add.php:56
#: templates/profile/add.php:71
#: templates/profile/edit.php:94
msgid "Publish"
msgstr ""

#: templates/profile/add.php:57
#: templates/profile/edit.php:95
msgid "Draft"
msgstr ""

#: templates/profile/add.php:61
#: templates/profile/edit.php:102
msgid "Only me"
msgstr ""

#: templates/profile/add.php:62
#: templates/profile/edit.php:103
msgid "Everyone"
msgstr ""

#: templates/profile/edit.php:115
msgid "Update"
msgstr ""

#: templates/profile/edit.php:119
msgid "Cancel"
msgstr ""

#: templates/profile/empty.php:15
msgid "No notes to display"
msgstr ""

#: templates/profile/modal.php:23
msgid "Close"
msgstr ""

#. translators: %s: Note ID.
#: templates/profile/note.php:47
msgid " (ID #%s)"
msgstr ""

#: templates/profile/single-note.php:25
msgid "You do not have permission to view this note."
msgstr ""

#: templates/profile/single-note.php:28
msgid "Back to notes"
msgstr ""

#: templates/profile/view.php:87
msgid "Edit"
msgstr ""

#: templates/profile/view.php:93
msgid "Delete"
msgstr ""

#. translators: %s is the User Notes extension name.
#: um-user-notes.php:50
#: um-user-notes.php:66
msgid "The <strong>%s</strong> extension requires the Ultimate Member plugin to be activated to work properly. You can download it <a href=\"https://wordpress.org/plugins/ultimate-member\">here</a>"
msgstr ""

#: assets/js/um-user-notes.js:183
msgid "Note successfully updated."
msgstr ""

#: assets/js/um-user-notes.js:268
msgid "Want to delete note?"
msgstr ""
