<?php
namespace um_ext\um_user_photos\frontend;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Enqueue
 * @package um_ext\um_user_photos\frontend
 */
class Enqueue {

	/**
	 * Enqueue constructor.
	 */
	public function __construct() {
		add_action( 'wp_enqueue_scripts', array( &$this, 'wp_enqueue_scripts' ), 99 );
	}

	public function wp_enqueue_scripts() {
		$suffix = UM()->frontend()->enqueue()::get_suffix();

		if ( defined( 'UM_DEV_MODE' ) && UM_DEV_MODE && UM()->options()->get( 'enable_new_ui' ) ) {
			wp_register_style( 'um-user-photos', UM_USER_PHOTOS_URL . 'assets/css/v3/user-photos' . $suffix . '.css', array( 'um_new_design' ), UM_USER_PHOTOS_VERSION );

			wp_register_script( 'um-user-photos', UM_USER_PHOTOS_URL . 'assets/js/v3/gallery' . $suffix . '.js', array( 'wp-util', 'jquery-ui-sortable', 'um_new_design', 'wp-i18n', 'um_confirm', 'um_modal' ), UM_USER_PHOTOS_VERSION, true );
			wp_register_script( 'um-user-photos-account', UM_USER_PHOTOS_URL . 'assets/js/v3/account' . $suffix . '.js', array( 'wp-util', 'wp-i18n', 'um_new_design', 'um_confirm' ), UM_USER_PHOTOS_VERSION, true );

			wp_set_script_translations( 'um-user-photos', 'um-user-photos' );
			wp_set_script_translations( 'um-user-photos-account', 'um-user-photos' );
		} else {
			wp_register_style( 'um-images-grid', UM_USER_PHOTOS_URL . 'assets/css/images-grid' . $suffix . '.css', array(), UM_USER_PHOTOS_VERSION );
			wp_register_style( 'um-user-photos', UM_USER_PHOTOS_URL . 'assets/css/um-user-photos' . $suffix . '.css', array( 'um-images-grid', 'um_ui', 'um_confirm' ), UM_USER_PHOTOS_VERSION );
			wp_register_script( 'um-images-grid', UM_USER_PHOTOS_URL . 'assets/js/images-grid' . $suffix . '.js', array( 'jquery', 'um_scripts' ), UM_USER_PHOTOS_VERSION, true );
			wp_register_script( 'um-user-photos', UM_USER_PHOTOS_URL . 'assets/js/um-user-photos' . $suffix . '.js', array( 'wp-util', 'um-images-grid', 'jquery-ui-sortable', 'um_scripts', 'wp-i18n', 'um_confirm' ), UM_USER_PHOTOS_VERSION, true );

			wp_register_script( 'um-user-photos-account', UM_USER_PHOTOS_URL . 'assets/js/account' . $suffix . '.js', array( 'wp-util', 'wp-i18n', 'um_scripts', 'um_confirm' ), UM_USER_PHOTOS_VERSION, true );

			wp_set_script_translations( 'um-user-photos', 'um-user-photos' );
			wp_set_script_translations( 'um-user-photos-account', 'um-user-photos' );

			wp_localize_script(
				'um-images-grid',
				'user_photos_settings',
				array(
					'disabled_comments' => UM()->options()->get( 'um_user_photos_disable_comments' ),
				)
			);

			wp_enqueue_script( 'um-images-grid' );
			wp_enqueue_script( 'um-user-photos' );
			wp_enqueue_style( 'um-user-photos' );
			wp_enqueue_style( 'um-images-grid' );
		}
	}
}
