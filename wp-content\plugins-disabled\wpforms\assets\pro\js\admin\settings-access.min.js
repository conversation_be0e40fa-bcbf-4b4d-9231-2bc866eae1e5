"use strict";var WPFormsSettingsAccess=window.WPFormsSettingsAccess||function(r){var n={capsCache:{},parentCaps:{wpforms_create_forms:["wpforms_edit_own_forms","wpforms_view_own_forms"],wpforms_edit_own_forms:["wpforms_view_own_forms"],wpforms_edit_others_forms:["wpforms_view_others_forms"],wpforms_delete_own_forms:["wpforms_view_own_forms"],wpforms_delete_others_forms:["wpforms_view_others_forms"],wpforms_view_entries_own_forms:["wpforms_view_own_forms"],wpforms_view_entries_others_forms:["wpforms_view_others_forms"],wpforms_edit_entries_own_forms:["wpforms_view_own_forms","wpforms_view_entries_own_forms"],wpforms_edit_entries_others_forms:["wpforms_view_others_forms","wpforms_view_entries_others_forms"],wpforms_delete_entries_own_forms:["wpforms_view_own_forms","wpforms_view_entries_own_forms"],wpforms_delete_entries_others_forms:["wpforms_view_others_forms","wpforms_view_entries_others_forms"]},childCaps:{wpforms_view_own_forms:["wpforms_edit_own_forms","wpforms_delete_own_forms","wpforms_view_entries_own_forms","wpforms_edit_entries_own_forms","wpforms_delete_entries_own_forms"],wpforms_view_others_forms:["wpforms_edit_others_forms","wpforms_delete_others_forms","wpforms_view_entries_others_forms","wpforms_edit_entries_others_forms","wpforms_delete_entries_others_forms"],wpforms_view_entries_own_forms:["wpforms_edit_entries_own_forms","wpforms_delete_entries_own_forms"],wpforms_view_entries_others_forms:["wpforms_edit_entries_others_forms","wpforms_delete_entries_others_forms"]},init:function(){r(n.ready)},ready:function(){n.updateAllCapsCache(),n.events()},events:function(){r(".wpforms-admin-settings-access select").on("change",n.selectChangeEvent)},selectChangeEvent:function(){var e,s,o,t=r(this);t.length&&(e=t.data("cap"))&&(o=t.val(),s=_.difference(o,n.getCapCache(e)).toString(),o=_.difference(n.getCapCache(e),o).toString(),n.updateCapCache(t),s.length&&n.processRoleAdded(e,s),o.length)&&n.processRoleRemoved(e,o)},processRoleAdded:function(e,s){var o=n.getParentCapsMissing(e,s);o.length&&n.displayModal({cap:e,caps:o,role:s,template:wpforms_settings_access.l10n.grant_caps,confirmAction:function(){n.populateRoles(o,s)},cancelAction:function(){n.removeRoles([e],s)}})},processRoleRemoved:function(e,s){var o=n.getChildCapsPresent(e,s);o.length&&n.displayModal({cap:e,caps:o,role:s,template:wpforms_settings_access.l10n.remove_caps,confirmAction:function(){n.removeRoles(o,s)},cancelAction:function(){n.populateRoles([e],s)}})},getCapLabel:function(e){return wpforms_settings_access.labels.caps[e]||e},getCapLabels:function(e){return void 0!==e&&e.length?e.map(n.getCapLabel):[]},getRoleLabel:function(e){return wpforms_settings_access.labels.roles[e]||e},getCapCache:function(e){return n.capsCache[e]||[]},updateCapCache:function(e,s){var o;e.length&&(o=e.data("cap"))&&(s=s||e.val(),n.capsCache[o]=s)},updateAllCapsCache:function(){r(".wpforms-admin-settings-access select").each(function(){n.updateCapCache(r(this))})},getParentCapsMissing:function(e,s){e=n.parentCaps[e];return e?e.filter(function(e){e=r("#wpforms-setting-"+e).val();return!e||-1===e.indexOf(s)}):[]},getChildCapsPresent:function(e,s){e=n.childCaps[e];return e?e.filter(function(e){e=r("#wpforms-setting-"+e).val();return!!e&&-1!==e.indexOf(s)}):[]},displayModal:function(e){var s=e.template.replace("%1$s","<b>"+n.getCapLabel(e.cap)+"</b>").replace(/%2\$s/g,"<b>"+n.getCapLabels(e.caps).join(", ")+"</b>").replace("%3$s","<i>"+n.getRoleLabel(e.role)+"</i>");r.alert({title:wpforms_admin.heads_up,content:s,icon:"fa fa-exclamation-circle",type:"orange",boxWidth:"500px",buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"],action:e.confirmAction},cancel:{text:wpforms_admin.cancel,action:e.cancelAction}}})},populateRoles:function(e,t){e.map(function(e){var s,o=r("#wpforms-setting-"+e);return!o.length||!(s=o.data("choicesjs"))||(s.setChoiceByValue(t),n.updateCapCache(o),e)})},removeRoles:function(e,o){e.map(function(e){var s,e=r("#wpforms-setting-"+e);return!e.length||!(s=e.data("choicesjs"))||(s.removeActiveItemsByValue(o),void n.updateCapCache(e))})}};return n}((document,window,jQuery));WPFormsSettingsAccess.init();