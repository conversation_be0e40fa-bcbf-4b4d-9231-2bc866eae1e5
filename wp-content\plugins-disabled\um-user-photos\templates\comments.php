<?php
/**
 * Template for the UM User Photos. The comments block
 *
 * Page: "Profile", tab "Photos", the image popup
 * Parent template: caption.php
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/comments.php
 * @var int   $image_id
 * @var array $comments
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
?>

<div class="um-user-photos-comments-loop">
	<?php
	if ( ! empty( $comments ) ) {
		foreach ( $comments as $photo_comment ) {
			$is_url  = filter_var( $photo_comment->comment_content, FILTER_VALIDATE_URL );
			$content = $is_url ? '<a href="' . esc_url( $photo_comment->comment_content ) . '" target="_blank">' . esc_html( $photo_comment->comment_content ) . '</a>' : esc_html( $photo_comment->comment_content );

			UM()->get_template(
				'comment.php',
				UM_USER_PHOTOS_PLUGIN,
				array(
					'user_id'       => $photo_comment->user_id,
					'content'       => $content,
					'id'            => $photo_comment->comment_ID,
					'image_id'      => $image_id,
					'photo_comment' => $photo_comment,
				),
				true
			);
		}
	}
	?>
</div>
