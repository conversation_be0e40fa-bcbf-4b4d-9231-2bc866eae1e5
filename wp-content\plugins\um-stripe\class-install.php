<?php
/**
 * Install class
 *
 * @package um_ext\um_stripe\Install
 */

namespace um_ext\um_stripe;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Install
 *
 * @package umm\stripe
 */
class Install {

	/**
	 * Default settings
	 *
	 * @since 1.0.0
	 * @var array Default module settings
	 */
	public $settings_defaults;

	/**
	 * Init
	 *
	 * @since 1.0.0
	 */
	public function __construct() {
		// settings defaults.
		$this->settings_defaults = array(
			'stripe_live_publish_key'    => '',
			'stripe_live_secret_key'     => '',
			'stripe_live_webhook_secret' => '',
			'stripe_sandbox_mode'        => false,
			'stripe_test_publish_key'    => '',
			'stripe_test_secret_key'     => '',
			'stripe_test_webhook_secret' => '',
			'stripe_merge_role'          => true,
		);
	}

	/**
	 * Save default settings on start
	 *
	 * @since 1.0.0
	 */
	private function set_default_settings() {
		$options = get_option( 'um_options', array() );
		foreach ( $this->settings_defaults as $key => $value ) {
			// Set new options to default.
			if ( ! isset( $options[ $key ] ) ) {
				$options[ $key ] = $value;
			}
		}

		update_option( 'um_options', $options );
	}

	/**
	 * Show create pages admin notice if it was hidden.
	 *
	 * @since 1.0.0
	 */
	private function force_show_create_pages() {
		$version = get_option( 'um_stripe_version' );

		if ( ! $version ) {
			$hidden_notices = get_option( 'um_hidden_admin_notices', array() );
			$hidden_notices = array_flip( $hidden_notices );
			unset( $hidden_notices['wrong_pages'] );
			$hidden_notices = array_flip( $hidden_notices );

			update_option( 'um_hidden_admin_notices', $hidden_notices );
		}
	}

	/**
	 * Start installation process.
	 *
	 * @since 1.0.0
	 */
	public function start() {
		$this->set_default_settings();
		$this->force_show_create_pages();
	}
}
