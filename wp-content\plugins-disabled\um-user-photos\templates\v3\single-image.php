<?php
/**
 * Template for the UM User Photos. The "Album" block
 *
 * Call: UM()->User_Photos()->ajax()->um_user_photos_load_more()
 * Call: UM()->User_Photos()->ajax()->get_single_album_view()
 * Page: "Profile", tab "Photos"
 * Parent template: photos.php
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/single-image.php
 * @var bool   $is_my_profile
 * @var int    $photo_id
 * @var string $thumbnail_image
 * @var string $img_link
 * @var array  $full_image
 * @var string $caption
 * @var string $img_title
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! $is_my_profile ) {
	?>
	<div id="um-user-photos-image-block-<?php echo esc_attr( $photo_id ); ?>" class="um-user-photos-image-block um-up-cell">
		<div class="um-user-photos-image-block-buttons">
			<?php
			$link_args = array(
				'type'          => 'button',
				'size'          => 's',
				'icon_position' => 'content',
				'url'           => $img_link,
				'target'        => '_blank',
				'title'         => __( 'Related link', 'um-user-photos' ),
				'classes'       => array( 'um-user-photo-related-link' ),
			);

			if ( empty( $img_link ) ) {
				$link_args['classes'][] = 'um-hidden';
			}

			$svg_html = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icons-tabler-outline icon-tabler-link"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M9 15l6 -6" /><path d="M11 6l.463 -.536a5 5 0 0 1 7.071 7.072l-.534 .464" /><path d="M13 18l-.397 .534a5.068 5.068 0 0 1 -7.127 0a4.972 4.972 0 0 1 0 -7.071l.524 -.463" /></svg>';
			echo wp_kses(
				UM()->frontend()::layouts()::link( $svg_html, $link_args ),
				UM()->get_allowed_html( 'templates' )
			);
			?>
		</div>
		<div class="um-user-photos-image">
			<a data-caption="<?php echo esc_attr( $caption ); ?>" title="<?php echo esc_attr( $img_title ); ?>" href="<?php echo esc_url( $full_image[0] ); ?>" class="um-user-photos-image um-user-photos-open-single-photo-modal" data-id="<?php echo esc_attr( $photo_id ); ?>" data-wpnonce="<?php echo esc_attr( wp_create_nonce( 'um_user_photos_get_comment_section' ) ); ?>" data-umaction="open_modal">
				<?php
				echo wp_kses(
					UM()->frontend()::layouts()::lazy_image(
						$thumbnail_image[0],
						array(
							'width' => '100%',
							'alt'   => $img_title,
						)
					),
					UM()->get_allowed_html( 'templates' )
				);
				?>
			</a>
		</div>
	</div>
<?php } else { ?>
	<div id="um-user-photos-image-block-<?php echo esc_attr( $photo_id ); ?>" class="um-user-photos-image-block um-user-photos-image-block-editable um-up-cell">
		<div class="um-user-photos-image-block-buttons">
			<?php
			$link_args = array(
				'type'          => 'button',
				'size'          => 's',
				'icon_position' => 'content',
				'url'           => $img_link,
				'target'        => '_blank',
				'title'         => __( 'Related link', 'um-user-photos' ),
				'classes'       => array( 'um-user-photo-related-link' ),
			);
			if ( empty( $img_link ) ) {
				$link_args['classes'][] = 'um-hidden';
			}

			$svg_html = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icons-tabler-outline icon-tabler-link"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M9 15l6 -6" /><path d="M11 6l.463 -.536a5 5 0 0 1 7.071 7.072l-.534 .464" /><path d="M13 18l-.397 .534a5.068 5.068 0 0 1 -7.127 0a4.972 4.972 0 0 1 0 -7.071l.524 -.463" /></svg>';
			echo wp_kses(
				UM()->frontend()::layouts()::link(
					$svg_html,
					$link_args
				),
				UM()->get_allowed_html( 'templates' )
			);

			$svg_html = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icons-tabler-outline icon-tabler-pencil"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4" /><path d="M13.5 6.5l4 4" /></svg>';
			echo wp_kses(
				UM()->frontend()::layouts()::link(
					$svg_html,
					array(
						'type'          => 'button',
						'size'          => 's',
						'icon_position' => 'content',
						'title'         => __( 'Edit Image', 'um-user-photos' ),
						'data'          => array(
							'nonce'    => wp_create_nonce( 'um_edit_image_modal' . $photo_id ),
							'photo-id' => $photo_id,
						),
						'classes'       => array(
							'um-user-photos-edit-image',
						),
					)
				),
				UM()->get_allowed_html( 'templates' )
			);
			?>
		</div>
		<div class="um-user-photos-image">
			<a data-caption="<?php echo esc_attr( $caption ); ?>"
				data-id="<?php echo esc_attr( $photo_id ); ?>"
				title="<?php echo esc_attr( $img_title ); ?>"
				href="<?php echo esc_url( $full_image[0] ); ?>"
				class="um-user-photos-image um-user-photos-open-single-photo-modal"
				data-wpnonce="<?php echo esc_attr( wp_create_nonce( 'um_user_photos_get_comment_section' ) ); ?>"
				data-umaction="open_modal">
				<?php
				echo wp_kses(
					UM()->frontend()::layouts()::lazy_image(
						$thumbnail_image[0],
						array(
							'width' => '100%',
							'alt'   => $img_title,
						)
					),
					UM()->get_allowed_html( 'templates' )
				);
				?>
			</a>
		</div>
	</div>
	<?php
}
