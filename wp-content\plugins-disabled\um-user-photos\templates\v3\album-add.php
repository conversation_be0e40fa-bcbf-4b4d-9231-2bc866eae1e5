<?php
/**
 * Template for the UM User Photos, the "New Album" modal content
 *
 * Page: "Profile", tab "Photos"
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/modal/add-album.php
 * @var int        $count_user_photos
 * @var int        $limit_user_photos
 * @var int|string $limit_per_albums
 * @var array      $wrapper_classes
 * @var bool       $disable_title
 * @var bool       $disable_cover
 * @var bool       $enable_upload
 * @var bool       $album_limit_error
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
?>

<div class="<?php echo esc_attr( implode( ' ', $wrapper_classes ) ); ?>">
	<h4 class="um-user-photos-form-title"><?php esc_html_e( 'New album', 'um-user-photos' ); ?></h4>
	<?php echo wp_kses( UM()->frontend()::layouts()::ajax_loader( 'l', array( 'classes' => array( 'um-user-photos-albums-loader', 'um-display-none' ) ) ), UM()->get_allowed_html( 'templates' ) ); ?>
	<form id="um_user_photos_create_album" class="um-form-new um-user-photos-album-form">
		<div class="um-form-rows _um_row_1">
			<div class="um-form-row">
				<div class="um-form-cols um-form-cols-1">
					<div class="um-form-col um-form-col-1">
						<?php if ( ! $disable_title ) { ?>
							<div class="um-field um-field-text um-field-type_text">
								<label for="um-album-title">
									<?php esc_html_e( 'Album title', 'um-user-photos' ); ?>
									<?php if ( UM()->options()->get( 'form_asterisk' ) ) { ?>
										<span class="um-req" title="<?php esc_attr_e( 'Required', 'um-user-photos' ); ?>">*</span>
									<?php } ?>
								</label>
								<input type="text" name="album_title" id="um-album-title" required />
							</div>
						<?php } ?>

						<div class="um-field um-field-select um-field-type_select">
							<label for="um-album-privacy"><?php esc_html_e( 'Album privacy', 'um-user-photos' ); ?></label>
							<select id="um-album-privacy" class="um-form-field js-choice um-no-search" name="album_privacy">
								<?php foreach ( UM()->User_Photos()->common()->album()->privacy_options as $key => $label ) { ?>
									<option value="<?php echo esc_attr( $key ); ?>" <?php selected( 'everyone', $key ); ?>><?php echo esc_html( $label ); ?></option>
								<?php } ?>
							</select>
						</div>

						<div class="um-field um-field-uploader um-field-type_uploader">
							<?php
							if ( $enable_upload ) {
								?>
								<label><?php esc_html_e( 'Photos', 'um-user-photos' ); ?></label>
								<?php
								$dropzone_error = UM()->frontend()::layouts()::alert(
									__( 'You cannot upload more photos, you have reached the limit of uploads. Delete old photos to add new ones.', 'um-user-photos' ),
									array(
										'type'      => 'warning',
										'underline' => false,
										'classes'   => array( 'um-user-photos-error' ),
									)
								);

								$uploader_args = array(
									'id'             => 'um_user_photos_uploader',
									'handler'        => 'um-user-photos-upload',
									'async'          => false,
									'field_id'       => 'um_user_photos_upload',
									'name'           => 'album_photos',
									'multiple'       => true,
									'sortable_files' => true,
									'types'          => UM()->User_Photos()->common()->uploader()->allowed_extensions,
									'dropzone_error' => $dropzone_error,
								);

								$max_files = false;
								if ( ! empty( $limit_per_albums ) ) {
									$max_files = $limit_per_albums;
								}

								if ( ! empty( $limit_user_photos ) ) {
									$user_photos_left = absint( $limit_user_photos ) - absint( $count_user_photos );
									if ( empty( $max_files ) || $max_files > $user_photos_left ) {
										$max_files = $user_photos_left;
									}
								}

								// Set files limit or remove multiple attribute if max files = 1.
								if ( ! empty( $max_files ) ) {
									if ( 1 < $max_files ) {
										$uploader_args['max_files'] = $max_files;
									} else {
										$uploader_args['multiple'] = false;
									}
								}

								echo wp_kses(
									UM()->frontend()::layouts()::uploader( $uploader_args ),
									UM()->get_allowed_html( 'templates' )
								);
							} else {
								$error_message = empty( $album_limit_error ) ? __( 'You cannot upload more photos, you have reached the limit of uploads. Delete old photos to add new ones.', 'um-user-photos' ) : __( 'You cannot upload more photos, you have reached the limit of uploads for this album. Delete old photos or upload few photos.', 'um-user-photos' );
								echo wp_kses(
									UM()->frontend()::layouts()::alert(
										$error_message,
										array(
											'type'      => 'warning',
											'underline' => false,
											'classes'   => array( 'um-user-photos-error' ),
										)
									),
									UM()->get_allowed_html( 'templates' )
								);
							}
							?>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="um-form-submit">
			<?php
			$loader = UM()->frontend()::layouts()::ajax_loader( 'm', array( 'classes' => array( 'um-user-photos-loader', 'um-display-none' ) ) );
			$submit = UM()->frontend()::layouts()::button(
				__( 'Publish', 'um-user-photos' ),
				array(
					'design'  => 'primary',
					'type'    => 'submit',
					'size'    => 'm',
					'id'      => 'um-album-add',
					'classes' => array(
						'um-gallery-album-update',
					),
				)
			);
			$cancel = UM()->frontend()::layouts()::button(
				__( 'Cancel', 'um-user-photos' ),
				array(
					'type'    => 'button',
					'size'    => 'm',
					'classes' => array(
						'um-user-photos-back-to-gallery',
					),
					'data'    => array(
						'nonce'   => wp_create_nonce( 'um_get_gallery' ),
						'profile' => um_profile_id(),
					),
				)
			);
			echo wp_kses( $loader . $submit . $cancel, UM()->get_allowed_html( 'templates' ) );
			?>
		</div>
		<input type="hidden" name="_wpnonce" value="<?php echo esc_attr( wp_create_nonce( 'um_add_album' ) ); ?>" />
	</form>
</div>
