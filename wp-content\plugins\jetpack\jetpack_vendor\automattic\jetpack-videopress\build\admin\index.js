/*! For license information please see index.js.LICENSE.txt */
(()=>{var e={9826:(e,t,a)=>{"use strict";a.d(t,{i:()=>r});const n={AED:{symbol:"د.إ.‏",grouping:",",decimal:".",precision:2},AFN:{symbol:"؋",grouping:",",decimal:".",precision:2},ALL:{symbol:"Lek",grouping:".",decimal:",",precision:2},AMD:{symbol:"֏",grouping:",",decimal:".",precision:2},ANG:{symbol:"ƒ",grouping:",",decimal:".",precision:2},AOA:{symbol:"Kz",grouping:",",decimal:".",precision:2},ARS:{symbol:"$",grouping:".",decimal:",",precision:2},AUD:{symbol:"A$",grouping:",",decimal:".",precision:2},AWG:{symbol:"ƒ",grouping:",",decimal:".",precision:2},AZN:{symbol:"₼",grouping:" ",decimal:",",precision:2},BAM:{symbol:"КМ",grouping:".",decimal:",",precision:2},BBD:{symbol:"Bds$",grouping:",",decimal:".",precision:2},BDT:{symbol:"৳",grouping:",",decimal:".",precision:0},BGN:{symbol:"лв.",grouping:" ",decimal:",",precision:2},BHD:{symbol:"د.ب.‏",grouping:",",decimal:".",precision:3},BIF:{symbol:"FBu",grouping:",",decimal:".",precision:0},BMD:{symbol:"$",grouping:",",decimal:".",precision:2},BND:{symbol:"$",grouping:".",decimal:",",precision:0},BOB:{symbol:"Bs",grouping:".",decimal:",",precision:2},BRL:{symbol:"R$",grouping:".",decimal:",",precision:2},BSD:{symbol:"$",grouping:",",decimal:".",precision:2},BTC:{symbol:"Ƀ",grouping:",",decimal:".",precision:2},BTN:{symbol:"Nu.",grouping:",",decimal:".",precision:1},BWP:{symbol:"P",grouping:",",decimal:".",precision:2},BYR:{symbol:"р.",grouping:" ",decimal:",",precision:2},BZD:{symbol:"BZ$",grouping:",",decimal:".",precision:2},CAD:{symbol:"C$",grouping:",",decimal:".",precision:2},CDF:{symbol:"FC",grouping:",",decimal:".",precision:2},CHF:{symbol:"CHF",grouping:"'",decimal:".",precision:2},CLP:{symbol:"$",grouping:".",decimal:",",precision:2},CNY:{symbol:"¥",grouping:",",decimal:".",precision:2},COP:{symbol:"$",grouping:".",decimal:",",precision:2},CRC:{symbol:"₡",grouping:".",decimal:",",precision:2},CUC:{symbol:"CUC",grouping:",",decimal:".",precision:2},CUP:{symbol:"$MN",grouping:",",decimal:".",precision:2},CVE:{symbol:"$",grouping:",",decimal:".",precision:2},CZK:{symbol:"Kč",grouping:" ",decimal:",",precision:2},DJF:{symbol:"Fdj",grouping:",",decimal:".",precision:0},DKK:{symbol:"kr.",grouping:"",decimal:",",precision:2},DOP:{symbol:"RD$",grouping:",",decimal:".",precision:2},DZD:{symbol:"د.ج.‏",grouping:",",decimal:".",precision:2},EGP:{symbol:"ج.م.‏",grouping:",",decimal:".",precision:2},ERN:{symbol:"Nfk",grouping:",",decimal:".",precision:2},ETB:{symbol:"ETB",grouping:",",decimal:".",precision:2},EUR:{symbol:"€",grouping:".",decimal:",",precision:2},FJD:{symbol:"FJ$",grouping:",",decimal:".",precision:2},FKP:{symbol:"£",grouping:",",decimal:".",precision:2},GBP:{symbol:"£",grouping:",",decimal:".",precision:2},GEL:{symbol:"Lari",grouping:" ",decimal:",",precision:2},GHS:{symbol:"₵",grouping:",",decimal:".",precision:2},GIP:{symbol:"£",grouping:",",decimal:".",precision:2},GMD:{symbol:"D",grouping:",",decimal:".",precision:2},GNF:{symbol:"FG",grouping:",",decimal:".",precision:0},GTQ:{symbol:"Q",grouping:",",decimal:".",precision:2},GYD:{symbol:"G$",grouping:",",decimal:".",precision:2},HKD:{symbol:"HK$",grouping:",",decimal:".",precision:2},HNL:{symbol:"L.",grouping:",",decimal:".",precision:2},HRK:{symbol:"kn",grouping:".",decimal:",",precision:2},HTG:{symbol:"G",grouping:",",decimal:".",precision:2},HUF:{symbol:"Ft",grouping:".",decimal:",",precision:0},IDR:{symbol:"Rp",grouping:".",decimal:",",precision:0},ILS:{symbol:"₪",grouping:",",decimal:".",precision:2},INR:{symbol:"₹",grouping:",",decimal:".",precision:2},IQD:{symbol:"د.ع.‏",grouping:",",decimal:".",precision:2},IRR:{symbol:"﷼",grouping:",",decimal:"/",precision:2},ISK:{symbol:"kr.",grouping:".",decimal:",",precision:0},JMD:{symbol:"J$",grouping:",",decimal:".",precision:2},JOD:{symbol:"د.ا.‏",grouping:",",decimal:".",precision:3},JPY:{symbol:"¥",grouping:",",decimal:".",precision:0},KES:{symbol:"S",grouping:",",decimal:".",precision:2},KGS:{symbol:"сом",grouping:" ",decimal:"-",precision:2},KHR:{symbol:"៛",grouping:",",decimal:".",precision:0},KMF:{symbol:"CF",grouping:",",decimal:".",precision:2},KPW:{symbol:"₩",grouping:",",decimal:".",precision:0},KRW:{symbol:"₩",grouping:",",decimal:".",precision:0},KWD:{symbol:"د.ك.‏",grouping:",",decimal:".",precision:3},KYD:{symbol:"$",grouping:",",decimal:".",precision:2},KZT:{symbol:"₸",grouping:" ",decimal:"-",precision:2},LAK:{symbol:"₭",grouping:",",decimal:".",precision:0},LBP:{symbol:"ل.ل.‏",grouping:",",decimal:".",precision:2},LKR:{symbol:"₨",grouping:",",decimal:".",precision:0},LRD:{symbol:"L$",grouping:",",decimal:".",precision:2},LSL:{symbol:"M",grouping:",",decimal:".",precision:2},LYD:{symbol:"د.ل.‏",grouping:",",decimal:".",precision:3},MAD:{symbol:"د.م.‏",grouping:",",decimal:".",precision:2},MDL:{symbol:"lei",grouping:",",decimal:".",precision:2},MGA:{symbol:"Ar",grouping:",",decimal:".",precision:0},MKD:{symbol:"ден.",grouping:".",decimal:",",precision:2},MMK:{symbol:"K",grouping:",",decimal:".",precision:2},MNT:{symbol:"₮",grouping:" ",decimal:",",precision:2},MOP:{symbol:"MOP$",grouping:",",decimal:".",precision:2},MRO:{symbol:"UM",grouping:",",decimal:".",precision:2},MTL:{symbol:"₤",grouping:",",decimal:".",precision:2},MUR:{symbol:"₨",grouping:",",decimal:".",precision:2},MVR:{symbol:"MVR",grouping:",",decimal:".",precision:1},MWK:{symbol:"MK",grouping:",",decimal:".",precision:2},MXN:{symbol:"MX$",grouping:",",decimal:".",precision:2},MYR:{symbol:"RM",grouping:",",decimal:".",precision:2},MZN:{symbol:"MT",grouping:",",decimal:".",precision:0},NAD:{symbol:"N$",grouping:",",decimal:".",precision:2},NGN:{symbol:"₦",grouping:",",decimal:".",precision:2},NIO:{symbol:"C$",grouping:",",decimal:".",precision:2},NOK:{symbol:"kr",grouping:" ",decimal:",",precision:2},NPR:{symbol:"₨",grouping:",",decimal:".",precision:2},NZD:{symbol:"NZ$",grouping:",",decimal:".",precision:2},OMR:{symbol:"﷼",grouping:",",decimal:".",precision:3},PAB:{symbol:"B/.",grouping:",",decimal:".",precision:2},PEN:{symbol:"S/.",grouping:",",decimal:".",precision:2},PGK:{symbol:"K",grouping:",",decimal:".",precision:2},PHP:{symbol:"₱",grouping:",",decimal:".",precision:2},PKR:{symbol:"₨",grouping:",",decimal:".",precision:2},PLN:{symbol:"zł",grouping:" ",decimal:",",precision:2},PYG:{symbol:"₲",grouping:".",decimal:",",precision:2},QAR:{symbol:"﷼",grouping:",",decimal:".",precision:2},RON:{symbol:"lei",grouping:".",decimal:",",precision:2},RSD:{symbol:"Дин.",grouping:".",decimal:",",precision:2},RUB:{symbol:"₽",grouping:" ",decimal:",",precision:2},RWF:{symbol:"RWF",grouping:" ",decimal:",",precision:2},SAR:{symbol:"﷼",grouping:",",decimal:".",precision:2},SBD:{symbol:"S$",grouping:",",decimal:".",precision:2},SCR:{symbol:"₨",grouping:",",decimal:".",precision:2},SDD:{symbol:"LSd",grouping:",",decimal:".",precision:2},SDG:{symbol:"£‏",grouping:",",decimal:".",precision:2},SEK:{symbol:"kr",grouping:",",decimal:".",precision:2},SGD:{symbol:"S$",grouping:",",decimal:".",precision:2},SHP:{symbol:"£",grouping:",",decimal:".",precision:2},SLL:{symbol:"Le",grouping:",",decimal:".",precision:2},SOS:{symbol:"S",grouping:",",decimal:".",precision:2},SRD:{symbol:"$",grouping:",",decimal:".",precision:2},STD:{symbol:"Db",grouping:",",decimal:".",precision:2},SVC:{symbol:"₡",grouping:",",decimal:".",precision:2},SYP:{symbol:"£",grouping:",",decimal:".",precision:2},SZL:{symbol:"E",grouping:",",decimal:".",precision:2},THB:{symbol:"฿",grouping:",",decimal:".",precision:2},TJS:{symbol:"TJS",grouping:" ",decimal:";",precision:2},TMT:{symbol:"m",grouping:" ",decimal:",",precision:0},TND:{symbol:"د.ت.‏",grouping:",",decimal:".",precision:3},TOP:{symbol:"T$",grouping:",",decimal:".",precision:2},TRY:{symbol:"TL",grouping:".",decimal:",",precision:2},TTD:{symbol:"TT$",grouping:",",decimal:".",precision:2},TVD:{symbol:"$T",grouping:",",decimal:".",precision:2},TWD:{symbol:"NT$",grouping:",",decimal:".",precision:2},TZS:{symbol:"TSh",grouping:",",decimal:".",precision:2},UAH:{symbol:"₴",grouping:" ",decimal:",",precision:2},UGX:{symbol:"USh",grouping:",",decimal:".",precision:2},USD:{symbol:"$",grouping:",",decimal:".",precision:2},UYU:{symbol:"$U",grouping:".",decimal:",",precision:2},UZS:{symbol:"сўм",grouping:" ",decimal:",",precision:2},VEB:{symbol:"Bs.",grouping:",",decimal:".",precision:2},VEF:{symbol:"Bs. F.",grouping:".",decimal:",",precision:2},VND:{symbol:"₫",grouping:".",decimal:",",precision:1},VUV:{symbol:"VT",grouping:",",decimal:".",precision:0},WST:{symbol:"WS$",grouping:",",decimal:".",precision:2},XAF:{symbol:"F",grouping:",",decimal:".",precision:2},XCD:{symbol:"$",grouping:",",decimal:".",precision:2},XOF:{symbol:"F",grouping:" ",decimal:",",precision:2},XPF:{symbol:"F",grouping:",",decimal:".",precision:2},YER:{symbol:"﷼",grouping:",",decimal:".",precision:2},ZAR:{symbol:"R",grouping:" ",decimal:",",precision:2},ZMW:{symbol:"ZK",grouping:",",decimal:".",precision:2},WON:{symbol:"₩",grouping:",",decimal:".",precision:2}};function r(e){return n[e]||{symbol:"$",grouping:",",decimal:".",precision:2}}},7397:(e,t,a)=>{"use strict";a.d(t,{vA:()=>o});var n=a(9826),r=a(8506);function o(e,t,a={}){const o=(0,n.i)(t);if(!o||isNaN(e))return null;const{decimal:i,grouping:s,precision:c,symbol:l}={...o,...a},d=e<0?"-":"",u=Math.abs(e),p=Math.floor(u);return{sign:d,symbol:l,integer:(0,r.A)(u,c,i,s).split(i)[0],fraction:c>0?(0,r.A)(u-p,c,i,s).slice(1):""}}},8506:(e,t,a)=>{"use strict";function n(e,t=0,a=".",n=","){const r=(e+"").replace(/[^0-9+\-Ee.]/g,""),o=isFinite(+r)?+r:0,i=isFinite(+t)?Math.abs(t):0,s=(i?function(e,t){const a=Math.pow(10,t);return""+(Math.round(e*a)/a).toFixed(t)}(o,i):""+Math.round(o)).split(".");return s[0].length>3&&(s[0]=s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g,n)),(s[1]||"").length<i&&(s[1]=s[1]||"",s[1]+=new Array(i-s[1].length+1).join("0")),s.join(a)}a.d(t,{A:()=>n})},6992:(e,t,a)=>{"use strict";function n(){return n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},n.apply(this,arguments)}var r;a.d(t,{AO:()=>u,Gh:()=>U,HS:()=>O,Oi:()=>s,Rr:()=>p,TM:()=>i,pX:()=>D,pb:()=>j,rc:()=>r,tH:()=>T,ue:()=>g,yD:()=>z}),function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(r||(r={}));const o="popstate";function i(e){return void 0===e&&(e={}),m((function(e,t){let{pathname:a="/",search:n="",hash:r=""}=p(e.location.hash.substr(1));return a.startsWith("/")||a.startsWith(".")||(a="/"+a),d("",{pathname:a,search:n,hash:r},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){let a=e.document.querySelector("base"),n="";if(a&&a.getAttribute("href")){let t=e.location.href,a=t.indexOf("#");n=-1===a?t:t.slice(0,a)}return n+"#"+("string"==typeof t?t:u(t))}),(function(e,t){c("/"===e.pathname.charAt(0),"relative pathnames are not supported in hash history.push("+JSON.stringify(t)+")")}),e)}function s(e,t){if(!1===e||null==e)throw new Error(t)}function c(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(e){}}}function l(e,t){return{usr:e.state,key:e.key,idx:t}}function d(e,t,a,r){return void 0===a&&(a=null),n({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?p(t):t,{state:a,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function u(e){let{pathname:t="/",search:a="",hash:n=""}=e;return a&&"?"!==a&&(t+="?"===a.charAt(0)?a:"?"+a),n&&"#"!==n&&(t+="#"===n.charAt(0)?n:"#"+n),t}function p(e){let t={};if(e){let a=e.indexOf("#");a>=0&&(t.hash=e.substr(a),e=e.substr(0,a));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}function m(e,t,a,i){void 0===i&&(i={});let{window:c=document.defaultView,v5Compat:p=!1}=i,m=c.history,h=r.Pop,g=null,v=f();function f(){return(m.state||{idx:null}).idx}function y(){h=r.Pop;let e=f(),t=null==e?null:e-v;v=e,g&&g({action:h,location:w.location,delta:t})}function b(e){let t="null"!==c.location.origin?c.location.origin:c.location.href,a="string"==typeof e?e:u(e);return a=a.replace(/ $/,"%20"),s(t,"No window.location.(origin|href) available to create URL for href: "+a),new URL(a,t)}null==v&&(v=0,m.replaceState(n({},m.state,{idx:v}),""));let w={get action(){return h},get location(){return e(c,m)},listen(e){if(g)throw new Error("A history only accepts one active listener");return c.addEventListener(o,y),g=e,()=>{c.removeEventListener(o,y),g=null}},createHref:e=>t(c,e),createURL:b,encodeLocation(e){let t=b(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){h=r.Push;let n=d(w.location,e,t);a&&a(n,e),v=f()+1;let o=l(n,v),i=w.createHref(n);try{m.pushState(o,"",i)}catch(e){if(e instanceof DOMException&&"DataCloneError"===e.name)throw e;c.location.assign(i)}p&&g&&g({action:h,location:w.location,delta:1})},replace:function(e,t){h=r.Replace;let n=d(w.location,e,t);a&&a(n,e),v=f();let o=l(n,v),i=w.createHref(n);m.replaceState(o,"",i),p&&g&&g({action:h,location:w.location,delta:0})},go:e=>m.go(e)};return w}var h;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(h||(h={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function g(e,t,a){return void 0===a&&(a="/"),v(e,t,a,!1)}function v(e,t,a,n){let r=j(("string"==typeof t?p(t):t).pathname||"/",a);if(null==r)return null;let o=f(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let a=e.length===t.length&&e.slice(0,-1).every(((e,a)=>e===t[a]));return a?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(o);let i=null;for(let e=0;null==i&&e<o.length;++e){let t=P(r);i=_(o[e],t,n)}return i}function f(e,t,a,n){void 0===t&&(t=[]),void 0===a&&(a=[]),void 0===n&&(n="");let r=(e,r,o)=>{let i={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:r,route:e};i.relativePath.startsWith("/")&&(s(i.relativePath.startsWith(n),'Absolute route path "'+i.relativePath+'" nested under path "'+n+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),i.relativePath=i.relativePath.slice(n.length));let c=O([n,i.relativePath]),l=a.concat(i);e.children&&e.children.length>0&&(s(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+c+'".'),f(e.children,t,l,c)),(null!=e.path||e.index)&&t.push({path:c,score:S(c,e.index),routesMeta:l})};return e.forEach(((e,t)=>{var a;if(""!==e.path&&null!=(a=e.path)&&a.includes("?"))for(let a of y(e.path))r(e,t,a);else r(e,t)})),t}function y(e){let t=e.split("/");if(0===t.length)return[];let[a,...n]=t,r=a.endsWith("?"),o=a.replace(/\?$/,"");if(0===n.length)return r?[o,""]:[o];let i=y(n.join("/")),s=[];return s.push(...i.map((e=>""===e?o:[o,e].join("/")))),r&&s.push(...i),s.map((t=>e.startsWith("/")&&""===t?"/":t))}const b=/^:[\w-]+$/,w=3,E=2,A=1,k=10,R=-2,C=e=>"*"===e;function S(e,t){let a=e.split("/"),n=a.length;return a.some(C)&&(n+=R),t&&(n+=E),a.filter((e=>!C(e))).reduce(((e,t)=>e+(b.test(t)?w:""===t?A:k)),n)}function _(e,t,a){void 0===a&&(a=!1);let{routesMeta:n}=e,r={},o="/",i=[];for(let e=0;e<n.length;++e){let s=n[e],c=e===n.length-1,l="/"===o?t:t.slice(o.length)||"/",d=x({path:s.relativePath,caseSensitive:s.caseSensitive,end:c},l),u=s.route;if(!d&&c&&a&&!n[n.length-1].route.index&&(d=x({path:s.relativePath,caseSensitive:s.caseSensitive,end:!1},l)),!d)return null;Object.assign(r,d.params),i.push({params:r,pathname:O([o,d.pathname]),pathnameBase:L(O([o,d.pathnameBase])),route:u}),"/"!==d.pathnameBase&&(o=O([o,d.pathnameBase]))}return i}function x(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[a,n]=function(e,t,a){void 0===t&&(t=!1);void 0===a&&(a=!0);c("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let n=[],r="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,a)=>(n.push({paramName:t,isOptional:null!=a}),a?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(n.push({paramName:"*"}),r+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):a?r+="\\/*$":""!==e&&"/"!==e&&(r+="(?:(?=\\/|$))");let o=new RegExp(r,t?void 0:"i");return[o,n]}(e.path,e.caseSensitive,e.end),r=t.match(a);if(!r)return null;let o=r[0],i=o.replace(/(.)\/+$/,"$1"),s=r.slice(1);return{params:n.reduce(((e,t,a)=>{let{paramName:n,isOptional:r}=t;if("*"===n){let e=s[a]||"";i=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const c=s[a];return e[n]=r&&!c?void 0:(c||"").replace(/%2F/g,"/"),e}),{}),pathname:o,pathnameBase:i,pattern:e}}function P(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return c(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function j(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let a=t.endsWith("/")?t.length-1:t.length,n=e.charAt(a);return n&&"/"!==n?null:e.slice(a)||"/"}function N(e,t,a,n){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(n)+"].  Please separate it out to the `to."+a+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function V(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function z(e,t){let a=V(e);return t?a.map(((e,t)=>t===a.length-1?e.pathname:e.pathnameBase)):a.map((e=>e.pathnameBase))}function U(e,t,a,r){let o;void 0===r&&(r=!1),"string"==typeof e?o=p(e):(o=n({},e),s(!o.pathname||!o.pathname.includes("?"),N("?","pathname","search",o)),s(!o.pathname||!o.pathname.includes("#"),N("#","pathname","hash",o)),s(!o.search||!o.search.includes("#"),N("#","search","hash",o)));let i,c=""===e||""===o.pathname,l=c?"/":o.pathname;if(null==l)i=a;else{let e=t.length-1;if(!r&&l.startsWith("..")){let t=l.split("/");for(;".."===t[0];)t.shift(),e-=1;o.pathname=t.join("/")}i=e>=0?t[e]:"/"}let d=function(e,t){void 0===t&&(t="/");let{pathname:a,search:n="",hash:r=""}="string"==typeof e?p(e):e,o=a?a.startsWith("/")?a:function(e,t){let a=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?a.length>1&&a.pop():"."!==e&&a.push(e)})),a.length>1?a.join("/"):"/"}(a,t):t;return{pathname:o,search:M(n),hash:F(r)}}(o,i),u=l&&"/"!==l&&l.endsWith("/"),m=(c||"."===l)&&a.endsWith("/");return d.pathname.endsWith("/")||!u&&!m||(d.pathname+="/"),d}const O=e=>e.join("/").replace(/\/\/+/g,"/"),L=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),M=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",F=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";class T extends Error{}function D(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}const I=["post","put","patch","delete"],B=(new Set(I),["get",...I]);new Set(B),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred")},1113:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var n=a(6087);const r=(0,n.forwardRef)((function({icon:e,size:t=24,...a},r){return(0,n.cloneElement)(e,{width:t,height:t,...a,ref:r})}))},7474:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(n.Path,{d:"M20 11.2H6.8l3.7-3.7-1-1L3.9 12l5.6 5.5 1-1-3.7-3.7H20z"})})},1797:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(n.Path,{d:"m14.5 6.5-1 1 3.7 3.7H4v1.6h13.2l-3.7 3.7 1 1 5.6-5.5z"})})},7711:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)(n.Path,{d:"M14 5H4c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm.5 12c0 .3-.2.5-.5.5H4c-.3 0-.5-.2-.5-.5V7c0-.3.2-.5.5-.5h10c.3 0 .5.2.5.5v10zm2.5-7v4l5 3V7l-5 3zm3.5 4.4l-2-1.2v-2.3l2-1.2v4.7z"})})},3751:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(n.Path,{d:"M12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4ZM12.75 8V13H11.25V8H12.75ZM12.75 14.5V16H11.25V14.5H12.75Z"})})},7267:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(n.Path,{fillRule:"evenodd",d:"M11.25 5h1.5v15h-1.5V5zM6 10h1.5v10H6V10zm12 4h-1.5v6H18v-6z",clipRule:"evenodd"})})},3883:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(n.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})})},4969:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)(n.Path,{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"})})},8888:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(n.Path,{d:"M14.6 7l-1.2-1L8 12l5.4 6 1.2-1-4.6-5z"})})},5437:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(n.Path,{d:"M10.8622 8.04053L14.2805 12.0286L10.8622 16.0167L9.72327 15.0405L12.3049 12.0286L9.72327 9.01672L10.8622 8.04053Z"})})},9115:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(n.Path,{d:"M10.6 6L9.4 7l4.6 5-4.6 5 1.2 1 5.4-6z"})})},8248:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)(n.Path,{d:"M6.5 12.4L12 8l5.5 4.4-.9 1.2L12 10l-4.5 3.6-1-1.2z"})})},1249:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(n.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})})},991:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(n.Path,{d:"m13.06 12 6.47-6.47-1.06-1.06L12 10.94 5.53 4.47 4.47 5.53 10.94 12l-6.47 6.47 1.06 1.06L12 13.06l6.47 6.47 1.06-1.06L13.06 12Z"})})},7534:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(n.Path,{d:"M17.3 10.1c0-2.5-2.1-4.4-4.8-4.4-2.2 0-4.1 1.4-4.6 3.3h-.2C5.7 9 4 10.7 4 12.8c0 2.1 1.7 3.8 3.7 3.8h9c1.8 0 3.2-1.5 3.2-3.3.1-1.6-1.1-2.9-2.6-3.2zm-.5 5.1h-9c-1.2 0-2.2-1.1-2.2-2.3s1-2.4 2.2-2.4h1.3l.3-1.1c.4-1.3 1.7-2.2 3.2-2.2 1.8 0 3.3 1.3 3.3 2.9v1.3l1.3.2c.8.1 1.4.9 1.4 1.8-.1 1-.9 1.8-1.8 1.8z"})})},6307:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(n.Path,{d:"M18 11.3l-1-1.1-4 4V3h-1.5v11.3L7 10.2l-1 1.1 6.2 5.8 5.8-5.8zm.5 3.7v3.5h-13V15H4v5h16v-5h-1.5z"})})},2687:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n=a(8938).A},3512:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(n.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})})},9761:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(n.Path,{d:"M11.1 15.8H20v-1.5h-8.9v1.5zm0-8.6v1.5H20V7.2h-8.9zM6 13c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0-7c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"})})},4066:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(n.Path,{d:"M12 3.3c-4.8 0-8.8 3.9-8.8 8.8 0 4.8 3.9 8.8 8.8 8.8 4.8 0 8.8-3.9 8.8-8.8s-4-8.8-8.8-8.8zm6.5 5.5h-2.6C15.4 7.3 14.8 6 14 5c2 .6 3.6 2 4.5 3.8zm.7 3.2c0 .6-.1 1.2-.2 1.8h-2.9c.1-.6.1-1.2.1-1.8s-.1-1.2-.1-1.8H19c.2.6.2 1.2.2 1.8zM12 18.7c-1-.7-1.8-1.9-2.3-3.5h4.6c-.5 1.6-1.3 2.9-2.3 3.5zm-2.6-4.9c-.1-.6-.1-1.1-.1-1.8 0-.6.1-1.2.1-1.8h5.2c.1.6.1 1.1.1 1.8s-.1 1.2-.1 1.8H9.4zM4.8 12c0-.6.1-1.2.2-1.8h2.9c-.1.6-.1 1.2-.1 1.8 0 .6.1 1.2.1 1.8H5c-.2-.6-.2-1.2-.2-1.8zM12 5.3c1 .7 1.8 1.9 2.3 3.5H9.7c.5-1.6 1.3-2.9 2.3-3.5zM10 5c-.8 1-1.4 2.3-1.8 3.8H5.5C6.4 7 8 5.6 10 5zM5.5 15.3h2.6c.4 1.5 1 2.8 1.8 3.7-1.8-.6-3.5-2-4.4-3.7zM14 19c.8-1 1.4-2.2 1.8-3.7h2.6C17.6 17 16 18.4 14 19z"})})},1651:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(n.Path,{d:"m3 5c0-1.10457.89543-2 2-2h13.5c1.1046 0 2 .89543 2 2v13.5c0 1.1046-.8954 2-2 2h-13.5c-1.10457 0-2-.8954-2-2zm2-.5h6v6.5h-6.5v-6c0-.27614.22386-.5.5-.5zm-.5 8v6c0 .2761.22386.5.5.5h6v-6.5zm8 0v6.5h6c.2761 0 .5-.2239.5-.5v-6zm0-8v6.5h6.5v-6c0-.27614-.2239-.5-.5-.5z",fillRule:"evenodd",clipRule:"evenodd"})})},5938:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)(n.Path,{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM5 4.5h14c.3 0 .5.2.5.5v8.4l-3-2.9c-.3-.3-.8-.3-1 0L11.9 14 9 12c-.3-.2-.6-.2-.8 0l-3.6 2.6V5c-.1-.3.1-.5.4-.5zm14 15H5c-.3 0-.5-.2-.5-.5v-2.4l4.1-3 3 1.9c.*******.9-.1L16 12l3.5 3.4V19c0 .3-.2.5-.5.5z"})})},9783:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M5.5 12a6.5 6.5 0 1 0 13 0 6.5 6.5 0 0 0-13 0ZM12 4a8 8 0 1 0 0 16 8 8 0 0 0 0-16Zm.75 4v1.5h-1.5V8h1.5Zm0 8v-5h-1.5v5h1.5Z"})})},1679:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsxs)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,r.jsx)(n.Path,{d:"m7 6.5 4 2.5-4 2.5z"}),(0,r.jsx)(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"m5 3c-1.10457 0-2 .89543-2 2v14c0 1.1046.89543 2 2 2h14c1.1046 0 2-.8954 2-2v-14c0-1.10457-.8954-2-2-2zm14 1.5h-14c-.27614 0-.5.22386-.5.5v10.7072l3.62953-2.6465c.25108-.1831.58905-.1924.84981-.0234l2.92666 1.8969 3.5712-3.4719c.2911-.2831.7545-.2831 1.0456 0l2.9772 2.8945v-9.3568c0-.27614-.2239-.5-.5-.5zm-14.5 14.5v-1.4364l4.09643-2.987 2.99567 1.9417c.2936.1903.6798.1523.9307-.0917l3.4772-3.3806 3.4772 3.3806.0228-.0234v2.5968c0 .2761-.2239.5-.5.5h-14c-.27614 0-.5-.2239-.5-.5z"})]})},1597:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(n.Path,{d:"M13 19h-2v-2h2v2zm0-6h-2v-2h2v2zm0-6h-2V5h2v2z"})})},8938:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(n.Path,{d:"m19 7-3-3-8.5 8.5-1 4 4-1L19 7Zm-7 11.5H5V20h7v-1.5Z"})})},6673:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 5.5A2.25 2.25 0 0 0 9.878 7h4.244A2.251 2.251 0 0 0 12 5.5ZM12 4a3.751 3.751 0 0 0-3.675 3H5v1.5h1.27l.818 8.997a2.75 2.75 0 0 0 2.739 2.501h4.347a2.75 2.75 0 0 0 2.738-2.5L17.73 8.5H19V7h-3.325A3.751 3.751 0 0 0 12 4Zm4.224 4.5H7.776l.806 8.861a1.25 1.25 0 0 0 1.245 1.137h4.347a1.25 1.25 0 0 0 1.245-1.137l.805-8.861Z"})})},8015:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(5573),r=a(790);const o=(0,r.jsx)(n.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)(n.Path,{d:"M18.7 3H5.3C4 3 3 4 3 5.3v13.4C3 20 4 21 5.3 21h13.4c1.3 0 2.3-1 2.3-2.3V5.3C21 4 20 3 18.7 3zm.8 15.7c0 .4-.4.8-.8.8H5.3c-.4 0-.8-.4-.8-.8V5.3c0-.4.4-.8.8-.8h13.4c.4 0 .8.4.8.8v13.4zM10 15l5-3-5-3v6z"})})},6941:(e,t,a)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const a="color: "+this.color;t.splice(1,0,a,"color: inherit");let n=0,r=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(n++,"%c"===e&&(r=n))})),t.splice(r,0,a)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=a(3212)(t);const{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},3212:(e,t,a)=>{e.exports=function(e){function t(e){let a,r,o,i=null;function s(...e){if(!s.enabled)return;const n=s,r=Number(new Date),o=r-(a||r);n.diff=o,n.prev=a,n.curr=r,a=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((a,r)=>{if("%%"===a)return"%";i++;const o=t.formatters[r];if("function"==typeof o){const t=e[i];a=o.call(n,t),e.splice(i,1),i--}return a})),t.formatArgs.call(n,e);(n.log||t.log).apply(n,e)}return s.namespace=e,s.useColors=t.useColors(),s.color=t.selectColor(e),s.extend=n,s.destroy=t.destroy,Object.defineProperty(s,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==i?i:(r!==t.namespaces&&(r=t.namespaces,o=t.enabled(e)),o),set:e=>{i=e}}),"function"==typeof t.init&&t.init(s),s}function n(e,a){const n=t(this.namespace+(void 0===a?":":a)+e);return n.log=this.log,n}function r(e,t){let a=0,n=0,r=-1,o=0;for(;a<e.length;)if(n<t.length&&(t[n]===e[a]||"*"===t[n]))"*"===t[n]?(r=n,o=a,n++):(a++,n++);else{if(-1===r)return!1;n=r+1,o++,a=o}for(;n<t.length&&"*"===t[n];)n++;return n===t.length}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names,...t.skips.map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const a=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of a)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const a of t.skips)if(r(e,a))return!1;for(const a of t.names)if(r(e,a))return!0;return!1},t.humanize=a(7378),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((a=>{t[a]=e[a]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let a=0;for(let t=0;t<e.length;t++)a=(a<<5)-a+e.charCodeAt(t),a|=0;return t.colors[Math.abs(a)%t.colors.length]},t.enable(t.load()),t}},2241:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={wrapper:"GqFcAwJvIrg1v7f6QUfw",header:"OENx8kmm62tkWGukzP2S",title:"KnqJLKwSceJTwFJrPGHq","close-button":"PJU0_yA9jNf7ao0jhHut",footer:"rrORM3zqHfGvqiPduEXY",steps:"Q7fUcDUGhteXEr18rZPC","action-button":"S5LAeskUiEQ0JlPhr0Ze"}},1631:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={"admin-page":"sexr0jUxC1jVixdKiDnC",background:"vKQ11sLeAM45M04P1ccj","admin-page-header":"iWGAhN9gOB48g0jEO1OQ","sandbox-domain-badge":"JOYmuxQjG4FArIIUxJfA"}},3496:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={section:"cAbGtJDGgLubucBnz7vM"}},1842:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={"section-hero":"vMa4i_Dza2t5Zi_Bw9Nf"}},7560:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={button:"zI5tJ_qhWE6Oe6Lk75GY","is-icon-button":"tuBt2DLqimiImoqVzPqo",small:"Na39I683LAaSA99REg14",normal:"ipS7tKy9GntCS4R3vekF",icon:"paGLQwtPEaJmtArCcmyK",regular:"lZAo6_oGfclXOO9CC6Rd","full-width":"xJDOiJxTt0R_wSl8Ipz_",loading:"q_tVWqMjl39RcY6WtQA6","external-icon":"CDuBjJp_8jxzx5j6Nept"}},1626:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={cut:"msOlyh2T7D6uhbM6AROg",icon:"cPN7USVqSBpxUswfDtUZ",cta:"EmnJAyEzzn1QpA8HtypY",iconContainer:"vV7YZikAz0oHYsuvtxMq",description:"T1YaMupeZmBIpXZHY9EZ"}},4459:()=>{},4813:()=>{},7842:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={"star-icon":"cuoSlhSNrqf1dozY22Xb",jetpack:"lAIiifeLMmZAPlQ9n9ZR","checkmark-icon":"JLquNpQVlysAamuh5lJO",socialIcon:"cbOwD8Y4tFjwimmtchQI",bluesky:"aLWBKY0yRghEk7tNCgK3",facebook:"aHOlEBGD5EA8NKRw3xTw",twitter:"af4Y_zItXvLAOEoSDPSv",linkedin:"f68aqF3XSD1OBvXR1get",tumblr:"xFI0dt3UiXRlRQdqPWkx",google:"q7JEoyymveP6kF747M43",mastodon:"DKOBOTVmTLbh26gUH_73",nextdoor:"n5XodNsuMfMAAvqHFmbw",instagram:"cL3m0xBYTYhIKI7lCqDB",whatsapp:"fftumuc_lJ6v0tq4UMVR",threads:"inzgC27qxdt7hSdhTWRI"}},9128:()=>{},9053:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={sm:"(max-width: 599px)",md:"(min-width: 600px) and (max-width: 959px)",lg:"(min-width: 960px)"}},1157:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={sm:"(max-width: 599px)",md:"(min-width: 600px) and (max-width: 959px)",lg:"(min-width: 960px)",smCols:"4",mdCols:"8",lgCols:"12","col-sm-1":"RuVLl3q4lxTQa3wbhBJB","col-sm-1-start":"f9LZTRG4MMK42rS89afW","col-sm-1-end":"bHe_zKxjjpUwHw_MdYE1","col-sm-2":"QZbNrOqE2aNSn50xVhpU","col-sm-2-start":"ev7W3z7zVYPeHAlYqZjf","col-sm-2-end":"NJWd1m_e7lOiPYru2ZMP","col-sm-3":"Xc6nt1Qc1DI0Z2A3gt1r","col-sm-3-start":"UIcN_GXiPRoIsin8Kohg","col-sm-3-end":"GRKCyqb5LufCSCgykKFc","col-sm-4":"i_qTq8gqhhC3vIUepVRB","col-sm-4-start":"G3qaZ3Jpbvam_1XvGxgc","col-sm-4-end":"VRCNYKZtO9zukEwmgP1y","col-md-1":"tRm008K_WJL79WoNZTNL","col-md-1-start":"l5T2P_bgKts4tdaRkS1d","col-md-1-end":"zOCxfLZpF6BlgC7a_Yq1","col-md-2":"F80DdgVn0m5OpvtSQWka","col-md-2-start":"oI1c7JYfiJtMQHbhngtU","col-md-2-end":"pMQtA_4jh1_1lVknqEP5","col-md-3":"VenqMpdgyKQVUNNQcfqd","col-md-3-start":"seNYL99uoczf9V4MxBxT","col-md-3-end":"YKfF1HFhI9KygA5l3b2J","col-md-4":"yAi0Cv1xDWkoqsaUhvhR","col-md-4-start":"ubhnyZOnkgxNhh6XtVWv","col-md-4-end":"RGOPGQbWMJ9Ei5oFxS7X","col-md-5":"Sz1E2aWbX483ijdi6yge","col-md-5-start":"tku6_bRYrX9tMbgYGmIl","col-md-5-end":"b5JHttOhSEcI1WBlqAjk","col-md-6":"FboSx5MoKTAWbxXyYlCw","col-md-6-start":"Jhs8yEEmodG30edbJvag","col-md-6-end":"IpzbbKVqEqPcfIGkXkwt","col-md-7":"mhCPwfAZ4Kmm_empzJAq","col-md-7-start":"x034ilrJF7rO9UJB2rI1","col-md-7-end":"Wt8t2e16viRrOJ1lLA5v","col-md-8":"S6pIrEy9AMLKx9bgh_Ae","col-md-8-start":"kEfI4tGyuWfHTlRnvIab","col-md-8-end":"PUzX4RRsKq1dnsz3gebS","col-lg-1":"X_pdcLJikd8LS_YAdJlB","col-lg-1-start":"tl936d14Huby4khYp05X","col-lg-1-end":"hnge0LnR69d3NXEtEE1t","col-lg-2":"fj0NUMuyZQcPNgKcjp5Z","col-lg-2-start":"R2ncBX7a2NigdYCcV1OX","col-lg-2-end":"t8vMSDVYno9k9itRwnXb","col-lg-3":"wsDuEN2GqHx6qzo8dUdk","col-lg-3-start":"cIEVPUweWtLBy3xaXnMx","col-lg-3-end":"fajUWBwu1m2B479j3jmz","col-lg-4":"YR0c7fQTgMkDdWzwSyLp","col-lg-4-start":"xlwp8BmplxkKNMI7gamo","col-lg-4-end":"_C4O1w9DUqx1m3gPf8aA","col-lg-5":"Z54F1hAErckAIrKlxnXW","col-lg-5-start":"ezSDWkRHmKSxDJXxuiOH","col-lg-5-end":"T0ChoeAjGJjkkNrYhD4g","col-lg-6":"qtMoMPF6yHvGJnWHSsde","col-lg-6-start":"gdoywN5VPiWERfIBqkph","col-lg-6-end":"wUev_VH5uf_pwFFlbnAU","col-lg-7":"egIPDFJsOpownTClq9XP","col-lg-7-start":"yGhp9yoAW7k0kQik9AB7","col-lg-7-end":"SJ43U9mR5wUg5V2qBeQA","col-lg-8":"cTuyHfMwSUJxN_HdIEgd","col-lg-8-start":"smCr8DaIagcumdvdldiK","col-lg-8-end":"T03NHzQJvzwL6wAfIiTL","col-lg-9":"pMvxM3RJGjqyNdf9qg1Y","col-lg-9-start":"iIVpNRwEnQ_JI5gpp9EN","col-lg-9-end":"ZbQ4u4vGSX5rJOje4uGL","col-lg-10":"gKb5wuIDAlKGbrjK2vxy","col-lg-10-start":"Z7pINdImE2WJiYnZBTqm","col-lg-10-end":"ZTxp6qpvwurMdOnLLSz1","col-lg-11":"NnQTlbfnxPDR6cQ7rygg","col-lg-11-start":"O137wZd6Yl0olSA9PsXR","col-lg-11-end":"zf2OJtQ2MPz6SDoh6CB0","col-lg-12":"U3H6UHW6HqRt9hdzVg3O","col-lg-12-start":"zynnNeS_ZBTxABcVpUQH","col-lg-12-end":"vI8tltFZtFUNAy9Iag9s"}},2498:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={sm:"(max-width: 599px)",md:"(min-width: 600px) and (max-width: 959px)",lg:"(min-width: 960px)",container:"SqdhUZkXCRuIpErj1B3z",fluid:"OZC_9a1LhpWF9dv15Gdh"}},7876:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={placeholder:"NisihrgiIKl_knpYJtfg",pulse:"R2i0K45dEF157drbVRPI"}},3128:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={container:"p4qz2tkq0p9hxucJ6Qk2",table:"lbNDyXioOwvyvbALtCBm","is-viewport-large":"s2Lsn4kbm6BrS3DSndRB",card:"cLaNK_XcbTGlRQ4Tp43Q","is-primary":"CYt1X0eH1icRjhtJ28jx",header:"DAkZc1P9A3K12fjEliMg",item:"WUBuYABl8nymjs9NnCEL","last-feature":"ANtCFeb41NhA8PA3H7ZN",value:"Ql2gy_148yW8Vw5vhaKD",icon:"EAQrAnQEW1z1BfdY5gbC","icon-check":"JDSTlLoOC_4aUoH2oNM2","icon-cross":"zNdQRJ1w7BvaQOYyqzHK",popover:"lr7vbX95SKtoe7DarJcZ","popover-icon":"KRrGp2xdkeBOxLZeuQ6X",tos:"H_ZJiRVJg0LiMXPGOcmt","tos-container":"x21z_DixObRDsDaWotP1"}},9921:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={container:"dovianZYLKhnbnh9I06o","price-container":"lljtQMhW7lq5tE5SDJEf","promo-label":"NubApIV1vQCRUNprfm6b",price:"dhFQXpZfMwVI8vuYHnwC","is-not-off-price":"eD7hzxFmdtG_MgmBtl_k",footer:"C64ZjjUAqJC1T2Sa7apS",legend:"UpZDGew6Ay1hPoP6eI7b",symbol:"TDiiPbuW1Z0_05u_pvcK"}},3941:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={wrapper:"McCYJ2ErFiztX2NeMRou",small:"BzRvZWnrBa4JjQY0uluD",progress:"DKqdDLOgkrxCkDzKGT87"}},6392:()=>{},2073:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={reset:"WQVtrU6q0L1Igcj7wCrQ","headline-medium":"UujoBFTnQNY2cWU2SIsH","headline-small":"TeGO5V_thHw5lDAm1_2M","headline-small-regular":"WolQzb2MsSgiNmLtc7_j","title-medium":"hUB0JT8p1T2Hw28N6qC8","title-medium-semi-bold":"gKZWDv5chz3_O3Syp74H","title-small":"zY2No8Ga4b8shbOQGhnv",body:"tIj0D1t8Cc892ikmgFPZ","body-small":"KdcN0BnOaVeVhyLRKqhS","body-extra-small":"dso3Rh3tl3Xv1GumBktz","body-extra-small-bold":"mQ1UlbN9u4Mg9byO8m7v",label:"PItlW5vRExLnTj4a8eLE","m-0":"TwRpPlktzxhmFVeua7P5","mx-0":"zVfqx7gyb3o9mxfGynn1","my-0":"iSHVzNiB9iVleGljaQxy","mt-0":"xqDIp6cNVr_E6RXaiPyD","mr-0":"S8EwaXk1kyPizt6x4WH2","mb-0":"ODX5Vr1TARoLFkDDFooD","ml-0":"cphJ8dCpfimnky7P2FHg","m-1":"PFgIhNxIyiSuNvQjAIYj","mx-1":"M2jKmUzDxvJjjVEPU3zn","my-1":"io15gAh8tMTNbSEfwJKk","mt-1":"rcTN5uw9xIEeMEGL3Xi_","mr-1":"CQSkybjq2TcRM1Xo9COV","mb-1":"hfqOWgq6_MEGdFE82eOY","ml-1":"I8MxZQYTbuu595yfesWA","m-2":"kQkc6rmdpvLKPkyoJtVQ","mx-2":"j6vFPxWuu4Jan2ldoxpp","my-2":"hqr39dC4H_AbactPAkCG","mt-2":"c3dQnMi16C6J6Ecy4283","mr-2":"YNZmHOuRo6hU7zzKfPdP","mb-2":"Db8lbak1_wunpPk8NwKU","ml-2":"ftsYE5J9hLzquQ0tA5dY","m-3":"Det4MHzLUW7EeDnafPzq","mx-3":"h_8EEAztC29Vve1datb5","my-3":"YXIXJ0h1k47u6hzK8KcM","mt-3":"soADBBkcIKCBXzCTuV9_","mr-3":"zSX59ziEaEWGjnpZa4uV","mb-3":"yrVTnq_WBMbejg89c2ZQ","ml-3":"UKtHPJnI2cXBWtPDm5hM","m-4":"guexok_Tqd5Tf52hRlbT","mx-4":"oS1E2KfTBZkJ3F0tN7T6","my-4":"DN1OhhXi6AoBgEdDSbGd","mt-4":"ot2kkMcYHv53hLZ4LSn0","mr-4":"A1krOZZhlQ6Sp8Cy4bly","mb-4":"pkDbXXXL32237M0hokEh","ml-4":"XXv4kDTGvEnQeuGKOPU3","m-5":"yGqHk1a57gaISwkXwXe6","mx-5":"X8cghM358X3DkXLc9aNK","my-5":"GdfSmGwHlFnN2S6xBn1f","mt-5":"yqeuzwyGQ7zG0avrGqi_","mr-5":"g9emeCkuHvYhveiJbfXO","mb-5":"Lvk3dqcyHbZ07QCRlrUQ","ml-5":"r3yQECDQ9qX0XZzXlVAg","m-6":"aQhlPwht2Cz1X_63Miw0","mx-6":"JyHb0vK3wJgpblL9s5j8","my-6":"cY2gULL1lAv6WPNIRuf3","mt-6":"NBWQ9Lwhh_fnry3lg_p7","mr-6":"yIOniNe5E40C8fWvBm5V","mb-6":"t30usboNSyqfQWIwHvT3","ml-6":"Nm_TyFkYCMhOoghoToKJ","m-7":"C4qJKoBXpgKtpmrqtEKB","mx-7":"S93Srbu6NQ_PBr7DmTiD","my-7":"fJj8k6gGJDks3crUZxOS","mt-7":"cW6D6djs7Ppm7fD7TeoV","mr-7":"DuCnqNfcxcP3Z__Yo5Ro","mb-7":"im8407m2fw5vOg7O2zsw","ml-7":"G0fbeBgvz2sh3uTP9gNl","m-8":"kvW3sBCxRxUqz1jrVMJl","mx-8":"tOjEqjLONQdkiYx_XRnw","my-8":"op5hFSx318zgxsoZZNLN","mt-8":"c9WfNHP6TFKWIfLxv52J","mr-8":"sBA75QqcqRwwYSHJh2wc","mb-8":"GpL6idrXmSOM6jB8Ohsf","ml-8":"HbtWJoQwpgGycz8dGzeT","p-0":"uxX3khU88VQ_Ah49Ejsa","px-0":"KX0FhpBKwKzs9fOUdbNz","py-0":"PfK8vKDyN32dnimlzYjz","pt-0":"emxLHRjQuJsImnPbQIzE","pr-0":"kJ8WzlpTVgdViXt8ukP9","pb-0":"tg_UIUI11VBzrTAn2AzJ","pl-0":"uczvl8kaz84oPQJ2DB2R","p-1":"o7UHPcdVK3lt7q3lqV4o","px-1":"IDqEOxvDoYrFYxELPmtX","py-1":"DdywPW2qSYlu2pt8tpO2","pt-1":"npy3hw4A5QSkDicb2CJJ","pr-1":"LgbptTApNY5NwLQvEFAt","pb-1":"WZQy2SZuZso59bUsXXyl","pl-1":"o331apInxNunbYB3SfPE","p-2":"fMPIyD9Vqki1Lrc_yJnG","px-2":"i2pMcTcdrr10IQoiSm_L","py-2":"eA702gn32kwptiI1obXH","pt-2":"o9bGieUKcYc8o0Ij9oZX","pr-2":"SwZcFez1RDqWsOFjB5iG","pb-2":"eHpLc_idmuEqeqCTvqkN","pl-2":"vU39i2B4P1fUTMB2l6Vo","p-3":"JHWNzBnE29awhdu5BEh1","px-3":"X72lGbb56L3KFzC2xQ9N","py-3":"BzfNhRG8wXdCEB5ocQ6e","pt-3":"srV0KSDC83a2fiimSMMQ","pr-3":"lUWfkmbQjCskhcNwkyCm","pb-3":"Ts0dIlc3aTSL7V4cIHis","pl-3":"CzlqQXXhX6MvorArFZ8B","p-4":"TqMPkQtR_DdZuKb5vBoV","px-4":"a7UrjhI69Vetlcj9ZVzz","py-4":"StEhBzGs2Gi5dDEkjhAv","pt-4":"FGneZfZyvYrt1dG0zcnm","pr-4":"APEH216rpdlJWgD2fHc8","pb-4":"oGwXC3ohCic9XnAj6x69","pl-4":"U6gnT9y42ViPNOcNzBwb","p-5":"IpdRLBwnHqbqFrixgbYC","px-5":"HgNeXvkBa9o3bQ5fvFZm","py-5":"tJtFZM3XfPG9v9TSDfN1","pt-5":"PdifHW45QeXYfK568uD8","pr-5":"mbLkWTTZ0Za_BBbFZ5b2","pb-5":"vVWpZpLlWrkTt0hMk8XU","pl-5":"RxfaJj5a1Nt6IavEo5Zl","p-6":"SppJULDGdnOGcjZNCYBy","px-6":"palY2nLwdoyooPUm9Hhk","py-6":"WYw1JvZC0ppLdvSAPhr_","pt-6":"YEEJ9b90ueQaPfiU8aeN","pr-6":"QE0ssnsKvWJMqlhPbY5u","pb-6":"n8yA3jHlMRyLd5UIfoND","pl-6":"tXHmxYnHzbwtfxEaG51n","p-7":"kBTsPKkO_3g_tLkj77Um","px-7":"RyhrFx6Y1FGDrGAAyaxm","py-7":"CBwRpB0bDN3iEdQPPMJO","pt-7":"vQVSq6SvWKbOMu6r4H6b","pr-7":"oBy5__aEADMsH46mrgFX","pb-7":"KVEXoJqf1s92j0JMdNmN","pl-7":"ZMXGNrNaKW3k_3TLz0Fq","p-8":"tuiR9PhkHXhGyEgzRZRI","px-8":"U7454qyWkQNa2iaSJziu","py-8":"VLYIv2GVocjuN93e8HC8","pt-8":"X1rm9DQ1zLGLfogja5Gn","pr-8":"JS7G6kAuqJo5GIuF8S5t","pb-8":"Y8F9ga1TDCMbM1lj4gUz","pl-8":"AJuyNGrI63BOWql719H8"}},5196:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={global:"_fUXxnSp5pagKBp9gSN7"}},1795:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={"library-wrapper":"ngVehANvo95yL8SR3vCM","total-filter-wrapper":"u5Bm2lMdl_RF_PwsJFu3","total-sm":"tq4tsG0QhYtikiEyOjPd","filter-wrapper":"nQrPF6Z_QvFU70OoPfzr","search-input":"ysRC_YNbmBw3A8QXNwgV",small:"tpQusFkmJMjL7DDy8Sfh","filter-section":"A4BwZl_cA9ZSyyzrYVRF",pagination:"HK8MNR2nXOqy39lYbQce","first-video-wrapper":"gCc0d1v6OLwLRMO2Q_Yg","upload-area":"zupVSeSUypdTZypfXAF7","upgrade-trigger":"CKlPOzBAXYHPGa4yptcw","storage-meter":"Yi3NM4F2ed0DsZdQPtAu","storage-meter__progress-bar":"U4pZHTAM5On7WDEjxDCq","query-no-results":"gnESFSYJXvVJfa4rVUeQ","files-overlay":"D572veCt9epT8TLGAkLI","file-input":"xhfD9funJfBObVH1nuQ_","drop-text":"Ydu8V8fGJfNHNE6oqG1U",hover:"Ep7FMzH1uLJae7gMk2pW","jetpack-videopress-jitm-card":"Qh0kXwNK5UY0FloNd7Tr"}},6652:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={"checkbox-container":"tHO55M6cue1xUoXFhgFA",checkbox:"bLteOo5cdwnUeHakckCm","checkbox-checkmark":"YtnIvXvFOKbXCblFMT8a"}},8823:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={wrapper:"KuS14qLJxLxK5wN6vSKC","button-wrapper":"imdKI_xQT7pjFOh_ZDwf"}},8838:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={"modal-actions":"NPsvl2MHUyhI4EKr8__a","modal-action-button":"HWIcFAu9FWNqkEFxTerT"}},192:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={"header-wrapper":"Qu3FMU6c_M0vLWhYYmwk",small:"kt8OpzafeWQnLbMWHV2n","header-content":"Xt29nIclMutY8TDLgLKv","logo-button":"uUIUmI2IAFy3dYHmtXYP",breadcrumb:"SA0xAJg8n1xMUGyD9TGp",input:"SkJoFmh5ZtF0NPW5MVhd","back-link":"CVYImSXNGONXVapSYCYV",icon:"hTuLdSZ2UkxQSSGKgMCK",link:"eeRf0yn8FgAuPnCFTZJS","side-fields":"PFSfTWhz3OHCeRoDTkyQ",field:"Im2xyb0iQS6hRHW86HA0","privacy-icon":"_p3jOiy2XnKofiPPbcrm",rating:"GbfLzJi6zjrnQjQNa7hZ",checkboxTitle:"OaQIAT7wE4RxYgmX5xCV","chapters-help-container":"sBh98QDjL5Z0Hq1y6aHV","learn-more":"p2oUuL_36x5zb2QlgmZj","incomplete-chapters-notice":"_svkjLixYlAaWnbcC5sq",buttons:"ZYAdanIct9xbXeP0QvvS","jetpack-videopress-jitm-card":"m_KL4QLkUZaisROpd7D8"}},4870:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={notice:"dJlDReKx7kLk8SlNRx9S","is-warning":"yIBA1ClKVZ8LzhL6fCcF",icon:"oMaxgfhG0zEOdy3QEsBx","is-info":"dI4NmXhDJRWR5S35LQzf","is-success":"Hlh96OxtSayBmWyKmqUg",message:"sUrXs7n0h85zG6glOmcC"}},5341:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={label:"UPMQl2lcMrIHVFqbO9k1","input-wrapper":"lxkk4eVTUu28RhmSBtbd",disabled:"vhy9tG9TVoL1kHuVbDFR",large:"BWkYz6YLixxwG6T3K6nA","is-textarea":"nbQEVqPlwegYvvvppkPj",input:"CPNtC1daS23cmYvgI2j6","with-icon":"aG0o7XvHhQHoxWiKWIYV","icon-wrapper":"kQkWlMcIUkMbI3EUetn0","clear-icon":"jvMupIZA8TRJzHnLtYqo",loader:"a59hNmaVOdJiRC9h7DCQ"}},3003:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={wrapper:"x1Tbu0P622OAKsVqM8r_",navigation:"kduXj2UYHfYPY2OWzDA5",button:"UUeY0t_E1boOuwrOosKA",selected:"GTeOAT8BJioiAcaoPLq8","pagination-placeholder":"CvZfzPc3BoZ7jWYUPs3M"}},662:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={"action-popover":"rAlHvmHvhThRpglwvWH4"}},8727:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={"video-card__wrapper":"CmIhD5KlC34dY5elqkHt","is-blank":"iv8MHMl5EqDX4TdVdtGc","video-card__background":"C2tUqf95cgt6mGFwrEgw","video-card__quick-actions-section":"PRiNxpiEmTiaydAizI4Q","video-card__title-section":"pjN9m5JS8gKTbawlYvKE",disabled:"oaQ8fJmnujfHJ9aOsjbf","video-card__thumbnail":"XjRcYJK2E4Ygy8UBlbDT","video-card__title":"fl5NWyI0vT0C1iFLPcTU","video-card__video-plays-counter":"Iy6ctLwDonPHblHktl6g",small:"Ovu42BdFSE6hVQ7_C3pQ",chevron:"DW2zO1qn8P3F4tPfGrU6"}},9907:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={dropdown:"EwhvbK4YjGyJUcAzpCM6",separator:"j9HLeLBpqn4E0yd1iw6E",delete:"ppbkVbfFx5ta7Pn63SYE"}},7722:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={details:"oPqyKPaqt7IG2l6GCZOQ",filename:"pR2xpODRH2SI0rKbVoxv"}},6455:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={"filter-button":"xOV0028X57DzKXWT1f4X","is-active":"yXNNgoFym1vCBROVVqWz","filters-section":"sMQIPPsbpU8JxRsPawaD","checkbox-container":"p_R1YzEfKnHhHMNibHUa","title-adornment":"CeGpKAfDTC3iHVXguub0"}},4761:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={wrapper:"HW4AqaoibTciaVbN9s8v"}},6217:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={list:"kUGHOcMJnACgVE341N6g",header:"GEBB90uPGpY_ehZQMjim","title-wrapper":"jKNO2YYZiIwBdZp9M0HB",row:"Oq1JxuR5Cr62vXgn6Mgg","title-adornment":"DQF_YYNIWAoXWO7JTkAk",error:"VI5HvRC93oGOX4Y7F4I2"}},9284:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={actions:"YNmnd00qmYMNP9wYvIK4",popover:"wjFKvEG47iknEU90iDq9",trash:"HFyA3ZR7LoTwT_UZ0LlO","dropdown-content":"Voun5vQOD5fVqz8YCPb5"}},299:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={"video-row":"azFrwCqBXcsFKzkEUwzW",disabled:"IFercqk6TQnGkD13ZfED",pressed:"fNy2aT5H1blTtbBv61rR",stats:"XmlxDm3JYE1kpeltGrhR","mobile-stats":"gD0CFomXUHpNq7zku2iw",actions:"s6sgQ5Mw5HiW2AAKIlIA","checkbox-wrapper-small":"r4np4KDbgusNFdvlmReF","video-data-wrapper":"PoMJURXGuCh0iMK0XxyA",small:"wQakTXf3KoqgsnHbQORQ","info-wrapper":"k6ZZinbzyl2FbrjBHsSV",poster:"NfmyA6T82BR5NQtF8b8D","title-wrapper":"dr12n7MKBYGiJ1mIxUFw",title:"Nw6vQSQF5AEj2lyQk1BW",label:"To4eiepnWQxfy24AfQWE","meta-wrapper":"M1mY7AP3E1wcURRkvFJ7",upload:"yy8gb2USc3qp5hD4a_Mn","mobile-actions":"SNI76CTvLKNBSNVcHXXa","storybook-wrapper":"P7wAzijvMBzexy4_D2TF"}},7364:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={"percentage-description":"nXXmYwqehVvKJSvOVsq0","progress-bar":"qNQwG3YGaH9dgnw2XC3F"}},7059:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={selector:"qTNpT0cIqk0mi29aoqKX","is-small":"aWCfvJbDbrXzDB9iMUgD",actions:"YQG0v1bY6ogTc1WEjsN8"}},7959:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={thumbnail:"HRtyHR7FwkF0z4v6iwoT","thumbnail-placeholder":"dLUHJkXbduCtupYel_tE","thumbnail-blank":"XxMuxhqo7_HXAJa8faua","thumbnail-error":"KnlSTZxk7Kk4A_SSNYx4","video-thumbnail-duration":"egaF3YQ5W_2m1Wu2YdkB","is-small":"BmdRfRYMGO0Ow6jS592k","video-thumbnail-edit":"nwG_0RVAw2qSSdaxnBwc","thumbnail__edit-button":"uGbLU3t2V9Lu1MZJC8eC","custom-thumbnail":"Yloc5OpF90Gv15yyCxXi",pulse:"pkDi4axzixXDF7EraLze","is-row":"CPsBhWjdAs58PRPecnXw","progress-bar":"_ZJ4eTLW8oCAjPJMuXpa","upload-text":"h4cm8AdnkM6qMJDmuOXZ",disabled:"FsV3eqT7w52MtiGic31w"}},4900:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={wrapper:"VBg9MDIwwjh9niPRLqHv",small:"eeomA8xgTxZBCvvaqHBf","file-input":"rUUpPeXFVXo383lEdV2g",icon:"IN92PVoVWZ70ZOQWEog2"}},4237:()=>{"use strict"},8766:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n={container:"ianknO6MB_ng2EieMQdE","play-icon":"no33D8j7N4nzdafpETZy","video-player-wrapper":"VFzg5w6i04b05mkee2SA","video-player-spinner-wrapper":"PHuqvGT1Z0IVZgjAtUZw",spinner:"xwBn5mUH0mhSBqas1dog",video:"k5rpcKdHL3HKU39CrUhe",range:"sgykjydBL76DT5yDw_ka"}},7378:e=>{var t=1e3,a=60*t,n=60*a,r=24*n,o=7*r,i=365.25*r;function s(e,t,a,n){var r=t>=1.5*a;return Math.round(e/a)+" "+n+(r?"s":"")}e.exports=function(e,c){c=c||{};var l=typeof e;if("string"===l&&e.length>0)return function(e){if((e=String(e)).length>100)return;var s=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!s)return;var c=parseFloat(s[1]);switch((s[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return c*i;case"weeks":case"week":case"w":return c*o;case"days":case"day":case"d":return c*r;case"hours":case"hour":case"hrs":case"hr":case"h":return c*n;case"minutes":case"minute":case"mins":case"min":case"m":return c*a;case"seconds":case"second":case"secs":case"sec":case"s":return c*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}(e);if("number"===l&&isFinite(e))return c.long?function(e){var o=Math.abs(e);if(o>=r)return s(e,o,r,"day");if(o>=n)return s(e,o,n,"hour");if(o>=a)return s(e,o,a,"minute");if(o>=t)return s(e,o,t,"second");return e+" ms"}(e):function(e){var o=Math.abs(e);if(o>=r)return Math.round(e/r)+"d";if(o>=n)return Math.round(e/n)+"h";if(o>=a)return Math.round(e/a)+"m";if(o>=t)return Math.round(e/t)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},5762:(e,t,a)=>{"use strict";var n=a(3761);function r(){}function o(){}o.resetWarningCache=r,e.exports=function(){function e(e,t,a,r,o,i){if(i!==n){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var a={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:r};return a.PropTypes=a,a}},8120:(e,t,a)=>{e.exports=a(5762)()},3761:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},4588:(e,t)=>{"use strict";var a=Object.prototype.hasOwnProperty;function n(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch(e){return null}}function r(e){try{return encodeURIComponent(e)}catch(e){return null}}t.stringify=function(e,t){t=t||"";var n,o,i=[];for(o in"string"!=typeof t&&(t="?"),e)if(a.call(e,o)){if((n=e[o])||null!=n&&!isNaN(n)||(n=""),o=r(o),n=r(n),null===o||null===n)continue;i.push(o+"="+n)}return i.length?t+i.join("&"):""},t.parse=function(e){for(var t,a=/([^=?#&]+)=?([^&]*)/g,r={};t=a.exec(e);){var o=n(t[1]),i=n(t[2]);null===o||null===i||o in r||(r[o]=i)}return r}},28:(e,t,a)=>{"use strict";a.d(t,{I9:()=>u,N_:()=>h});var n=a(1609),r=a(5795),o=a(9539),i=a(6992);function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},s.apply(this,arguments)}function c(e,t){if(null==e)return{};var a,n,r={},o=Object.keys(e);for(n=0;n<o.length;n++)a=o[n],t.indexOf(a)>=0||(r[a]=e[a]);return r}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);const l=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(e){}new Map;const d=n.startTransition;r.flushSync,n.useId;function u(e){let{basename:t,children:a,future:r,window:s}=e,c=n.useRef();null==c.current&&(c.current=(0,i.TM)({window:s,v5Compat:!0}));let l=c.current,[u,p]=n.useState({action:l.action,location:l.location}),{v7_startTransition:m}=r||{},h=n.useCallback((e=>{m&&d?d((()=>p(e))):p(e)}),[p,m]);return n.useLayoutEffect((()=>l.listen(h)),[l,h]),n.useEffect((()=>(0,o.V8)(r)),[r]),n.createElement(o.Ix,{basename:t,children:a,location:u.location,navigationType:u.action,navigator:l,future:r})}const p="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,m=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,h=n.forwardRef((function(e,t){let a,{onClick:r,relative:d,reloadDocument:u,replace:h,state:g,target:v,to:f,preventScrollReset:y,viewTransition:b}=e,w=c(e,l),{basename:E}=n.useContext(o.jb),A=!1;if("string"==typeof f&&m.test(f)&&(a=f,p))try{let e=new URL(window.location.href),t=f.startsWith("//")?new URL(e.protocol+f):new URL(f),a=(0,i.pb)(t.pathname,E);t.origin===e.origin&&null!=a?f=a+t.search+t.hash:A=!0}catch(e){}let k=(0,o.$P)(f,{relative:d}),R=function(e,t){let{target:a,replace:r,state:s,preventScrollReset:c,relative:l,viewTransition:d}=void 0===t?{}:t,u=(0,o.Zp)(),p=(0,o.zy)(),m=(0,o.x$)(e,{relative:l});return n.useCallback((t=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(t,a)){t.preventDefault();let a=void 0!==r?r:(0,i.AO)(p)===(0,i.AO)(m);u(e,{replace:a,state:s,preventScrollReset:c,relative:l,viewTransition:d})}}),[p,u,m,r,s,a,e,c,l,d])}(f,{replace:h,state:g,target:v,preventScrollReset:y,relative:d,viewTransition:b});return n.createElement("a",s({},w,{href:a||k,onClick:A||u?r:function(e){r&&r(e),e.defaultPrevented||R(e)},ref:t,target:v}))}));var g,v;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(g||(g={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(v||(v={}))},9539:(e,t,a)=>{"use strict";a.d(t,{$P:()=>p,BV:()=>O,Ix:()=>U,V8:()=>V,Zp:()=>v,g:()=>f,jb:()=>c,qh:()=>z,x$:()=>y,zy:()=>h});var n=a(1609),r=a(6992);function o(){return o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},o.apply(this,arguments)}const i=n.createContext(null);const s=n.createContext(null);const c=n.createContext(null);const l=n.createContext(null);const d=n.createContext({outlet:null,matches:[],isDataRoute:!1});const u=n.createContext(null);function p(e,t){let{relative:a}=void 0===t?{}:t;m()||(0,r.Oi)(!1);let{basename:o,navigator:i}=n.useContext(c),{hash:s,pathname:l,search:d}=y(e,{relative:a}),u=l;return"/"!==o&&(u="/"===l?o:(0,r.HS)([o,l])),i.createHref({pathname:u,search:d,hash:s})}function m(){return null!=n.useContext(l)}function h(){return m()||(0,r.Oi)(!1),n.useContext(l).location}function g(e){n.useContext(c).static||n.useLayoutEffect(e)}function v(){let{isDataRoute:e}=n.useContext(d);return e?function(){let{router:e}=_(C.UseNavigateStable),t=P(S.UseNavigateStable),a=n.useRef(!1);return g((()=>{a.current=!0})),n.useCallback((function(n,r){void 0===r&&(r={}),a.current&&("number"==typeof n?e.navigate(n):e.navigate(n,o({fromRouteId:t},r)))}),[e,t])}():function(){m()||(0,r.Oi)(!1);let e=n.useContext(i),{basename:t,future:a,navigator:o}=n.useContext(c),{matches:s}=n.useContext(d),{pathname:l}=h(),u=JSON.stringify((0,r.yD)(s,a.v7_relativeSplatPath)),p=n.useRef(!1);return g((()=>{p.current=!0})),n.useCallback((function(a,n){if(void 0===n&&(n={}),!p.current)return;if("number"==typeof a)return void o.go(a);let i=(0,r.Gh)(a,JSON.parse(u),l,"path"===n.relative);null==e&&"/"!==t&&(i.pathname="/"===i.pathname?t:(0,r.HS)([t,i.pathname])),(n.replace?o.replace:o.push)(i,n.state,n)}),[t,o,u,l,e])}()}function f(){let{matches:e}=n.useContext(d),t=e[e.length-1];return t?t.params:{}}function y(e,t){let{relative:a}=void 0===t?{}:t,{future:o}=n.useContext(c),{matches:i}=n.useContext(d),{pathname:s}=h(),l=JSON.stringify((0,r.yD)(i,o.v7_relativeSplatPath));return n.useMemo((()=>(0,r.Gh)(e,JSON.parse(l),s,"path"===a)),[e,l,s,a])}function b(e,t,a,i){m()||(0,r.Oi)(!1);let{navigator:s,static:u}=n.useContext(c),{matches:p}=n.useContext(d),g=p[p.length-1],v=g?g.params:{},f=(g&&g.pathname,g?g.pathnameBase:"/");g&&g.route;let y,b=h();if(t){var w;let e="string"==typeof t?(0,r.Rr)(t):t;"/"===f||(null==(w=e.pathname)?void 0:w.startsWith(f))||(0,r.Oi)(!1),y=e}else y=b;let E=y.pathname||"/",A=E;if("/"!==f){let e=f.replace(/^\//,"").split("/");A="/"+E.replace(/^\//,"").split("/").slice(e.length).join("/")}let k=!u&&a&&a.matches&&a.matches.length>0?a.matches:(0,r.ue)(e,{pathname:A});let C=R(k&&k.map((e=>Object.assign({},e,{params:Object.assign({},v,e.params),pathname:(0,r.HS)([f,s.encodeLocation?s.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?f:(0,r.HS)([f,s.encodeLocation?s.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),p,a,i);return t&&C?n.createElement(l.Provider,{value:{location:o({pathname:"/",search:"",hash:"",state:null,key:"default"},y),navigationType:r.rc.Pop}},C):C}function w(){let e=function(){var e;let t=n.useContext(u),a=x(S.UseRouteError),r=P(S.UseRouteError);if(void 0!==t)return t;return null==(e=a.errors)?void 0:e[r]}(),t=(0,r.pX)(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),a=e instanceof Error?e.stack:null,o="rgba(200,200,200, 0.5)",i={padding:"0.5rem",backgroundColor:o};return n.createElement(n.Fragment,null,n.createElement("h2",null,"Unexpected Application Error!"),n.createElement("h3",{style:{fontStyle:"italic"}},t),a?n.createElement("pre",{style:i},a):null,null)}const E=n.createElement(w,null);class A extends n.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?n.createElement(d.Provider,{value:this.props.routeContext},n.createElement(u.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function k(e){let{routeContext:t,match:a,children:r}=e,o=n.useContext(i);return o&&o.static&&o.staticContext&&(a.route.errorElement||a.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=a.route.id),n.createElement(d.Provider,{value:t},r)}function R(e,t,a,o){var i;if(void 0===t&&(t=[]),void 0===a&&(a=null),void 0===o&&(o=null),null==e){var s;if(!a)return null;if(a.errors)e=a.matches;else{if(!(null!=(s=o)&&s.v7_partialHydration&&0===t.length&&!a.initialized&&a.matches.length>0))return null;e=a.matches}}let c=e,l=null==(i=a)?void 0:i.errors;if(null!=l){let e=c.findIndex((e=>e.route.id&&void 0!==(null==l?void 0:l[e.route.id])));e>=0||(0,r.Oi)(!1),c=c.slice(0,Math.min(c.length,e+1))}let d=!1,u=-1;if(a&&o&&o.v7_partialHydration)for(let e=0;e<c.length;e++){let t=c[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(u=e),t.route.id){let{loaderData:e,errors:n}=a,r=t.route.loader&&void 0===e[t.route.id]&&(!n||void 0===n[t.route.id]);if(t.route.lazy||r){d=!0,c=u>=0?c.slice(0,u+1):[c[0]];break}}}return c.reduceRight(((e,r,o)=>{let i,s=!1,p=null,m=null;var h;a&&(i=l&&r.route.id?l[r.route.id]:void 0,p=r.route.errorElement||E,d&&(u<0&&0===o?(h="route-fallback",!1||j[h]||(j[h]=!0),s=!0,m=null):u===o&&(s=!0,m=r.route.hydrateFallbackElement||null)));let g=t.concat(c.slice(0,o+1)),v=()=>{let t;return t=i?p:s?m:r.route.Component?n.createElement(r.route.Component,null):r.route.element?r.route.element:e,n.createElement(k,{match:r,routeContext:{outlet:e,matches:g,isDataRoute:null!=a},children:t})};return a&&(r.route.ErrorBoundary||r.route.errorElement||0===o)?n.createElement(A,{location:a.location,revalidation:a.revalidation,component:p,error:i,children:v(),routeContext:{outlet:null,matches:g,isDataRoute:!0}}):v()}),null)}var C=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(C||{}),S=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(S||{});function _(e){let t=n.useContext(i);return t||(0,r.Oi)(!1),t}function x(e){let t=n.useContext(s);return t||(0,r.Oi)(!1),t}function P(e){let t=function(){let e=n.useContext(d);return e||(0,r.Oi)(!1),e}(),a=t.matches[t.matches.length-1];return a.route.id||(0,r.Oi)(!1),a.route.id}const j={};const N=(e,t,a)=>{};function V(e,t){void 0===(null==e?void 0:e.v7_startTransition)&&N("v7_startTransition","React Router will begin wrapping state updates in `React.startTransition` in v7","https://reactrouter.com/v6/upgrading/future#v7_starttransition"),void 0!==(null==e?void 0:e.v7_relativeSplatPath)||t&&t.v7_relativeSplatPath||N("v7_relativeSplatPath","Relative route resolution within Splat routes is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath"),t&&(void 0===t.v7_fetcherPersist&&N("v7_fetcherPersist","The persistence behavior of fetchers is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_fetcherpersist"),void 0===t.v7_normalizeFormMethod&&N("v7_normalizeFormMethod","Casing of `formMethod` fields is being normalized to uppercase in v7","https://reactrouter.com/v6/upgrading/future#v7_normalizeformmethod"),void 0===t.v7_partialHydration&&N("v7_partialHydration","`RouterProvider` hydration behavior is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_partialhydration"),void 0===t.v7_skipActionErrorRevalidation&&N("v7_skipActionErrorRevalidation","The revalidation behavior after 4xx/5xx `action` responses is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_skipactionerrorrevalidation"))}n.startTransition;function z(e){(0,r.Oi)(!1)}function U(e){let{basename:t="/",children:a=null,location:i,navigationType:s=r.rc.Pop,navigator:d,static:u=!1,future:p}=e;m()&&(0,r.Oi)(!1);let h=t.replace(/^\/*/,"/"),g=n.useMemo((()=>({basename:h,navigator:d,static:u,future:o({v7_relativeSplatPath:!1},p)})),[h,p,d,u]);"string"==typeof i&&(i=(0,r.Rr)(i));let{pathname:v="/",search:f="",hash:y="",state:b=null,key:w="default"}=i,E=n.useMemo((()=>{let e=(0,r.pb)(v,h);return null==e?null:{location:{pathname:e,search:f,hash:y,state:b,key:w},navigationType:s}}),[h,v,f,y,b,w,s]);return null==E?null:n.createElement(c.Provider,{value:g},n.createElement(l.Provider,{children:a,value:E}))}function O(e){let{children:t,location:a}=e;return b(L(t),a)}new Promise((()=>{}));n.Component;function L(e,t){void 0===t&&(t=[]);let a=[];return n.Children.forEach(e,((e,o)=>{if(!n.isValidElement(e))return;let i=[...t,o];if(e.type===n.Fragment)return void a.push.apply(a,L(e.props.children,i));e.type!==z&&(0,r.Oi)(!1),e.props.index&&e.props.children&&(0,r.Oi)(!1);let s={id:e.props.id||i.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(s.children=L(e.props.children,i)),a.push(s)})),a}},6811:e=>{"use strict";e.exports=function(e,t){if(t=t.split(":")[0],!(e=+e))return!1;switch(t){case"http":case"ws":return 80!==e;case"https":case"wss":return 443!==e;case"ftp":return 21!==e;case"gopher":return 70!==e;case"file":return!1}return 0!==e}},372:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var n=a(6941);const r=a.n(n)()("dops:analytics");let o,i;window._tkq=window._tkq||[],window.ga=window.ga||function(){(window.ga.q=window.ga.q||[]).push(arguments)},window.ga.l=+new Date;const s={initialize:function(e,t,a){s.setUser(e,t),s.setSuperProps(a),s.identifyUser()},setGoogleAnalyticsEnabled:function(e,t=null){this.googleAnalyticsEnabled=e,this.googleAnalyticsKey=t},setMcAnalyticsEnabled:function(e){this.mcAnalyticsEnabled=e},setUser:function(e,t){i={ID:e,username:t}},setSuperProps:function(e){o=e},assignSuperProps:function(e){o=Object.assign(o||{},e)},mc:{bumpStat:function(e,t){const a=function(e,t){let a="";if("object"==typeof e){for(const t in e)a+="&x_"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);r("Bumping stats %o",e)}else a="&x_"+encodeURIComponent(e)+"="+encodeURIComponent(t),r('Bumping stat "%s" in group "%s"',t,e);return a}(e,t);s.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom-no-pv"+a+"&t="+Math.random())},bumpStatWithPageView:function(e,t){const a=function(e,t){let a="";if("object"==typeof e){for(const t in e)a+="&"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);r("Built stats %o",e)}else a="&"+encodeURIComponent(e)+"="+encodeURIComponent(t),r('Built stat "%s" in group "%s"',t,e);return a}(e,t);s.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom"+a+"&t="+Math.random())}},pageView:{record:function(e,t){s.tracks.recordPageView(e),s.ga.recordPageView(e,t)}},purchase:{record:function(e,t,a,n,r,o,i){s.ga.recordPurchase(e,t,a,n,r,o,i)}},tracks:{recordEvent:function(e,t){t=t||{},0===e.indexOf("akismet_")||0===e.indexOf("jetpack_")?(o&&(r("- Super Props: %o",o),t=Object.assign(t,o)),r('Record event "%s" called with props %s',e,JSON.stringify(t)),window._tkq.push(["recordEvent",e,t])):r('- Event name must be prefixed by "akismet_" or "jetpack_"')},recordJetpackClick:function(e){const t="object"==typeof e?e:{target:e};s.tracks.recordEvent("jetpack_wpa_click",t)},recordPageView:function(e){s.tracks.recordEvent("akismet_page_view",{path:e})},setOptOut:function(e){r("Pushing setOptOut: %o",e),window._tkq.push(["setOptOut",e])}},ga:{initialized:!1,initialize:function(){let e={};s.ga.initialized||(i&&(e={userId:"u-"+i.ID}),window.ga("create",this.googleAnalyticsKey,"auto",e),s.ga.initialized=!0)},recordPageView:function(e,t){s.ga.initialize(),r("Recording Page View ~ [URL: "+e+"] [Title: "+t+"]"),this.googleAnalyticsEnabled&&(window.ga("set","page",e),window.ga("send",{hitType:"pageview",page:e,title:t}))},recordEvent:function(e,t,a,n){s.ga.initialize();let o="Recording Event ~ [Category: "+e+"] [Action: "+t+"]";void 0!==a&&(o+=" [Option Label: "+a+"]"),void 0!==n&&(o+=" [Option Value: "+n+"]"),r(o),this.googleAnalyticsEnabled&&window.ga("send","event",e,t,a,n)},recordPurchase:function(e,t,a,n,r,o,i){window.ga("require","ecommerce"),window.ga("ecommerce:addTransaction",{id:e,revenue:n,currency:i}),window.ga("ecommerce:addItem",{id:e,name:t,sku:a,price:r,quantity:o}),window.ga("ecommerce:send")}},identifyUser:function(){i&&window._tkq.push(["identifyUser",i.ID,i.username])},setProperties:function(e){window._tkq.push(["setProperties",e])},clearedIdentity:function(){window._tkq.push(["clearIdentity"])}},c=s},5932:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>u});var n=a(6439),r=a(3832);function o(e){class t extends Error{constructor(...t){super(...t),this.name=e}}return t}const i=o("JsonParseError"),s=o("JsonParseAfterRedirectError"),c=o("Api404Error"),l=o("Api404AfterRedirectError"),d=o("FetchNetworkError");const u=new function(e,t){let a=e,o=e,i={"X-WP-Nonce":t},s={credentials:"same-origin",headers:i},c={method:"post",credentials:"same-origin",headers:Object.assign({},i,{"Content-type":"application/json"})},l=function(e){const t=e.split("?"),a=t.length>1?t[1]:"",n=a.length?a.split("&"):[];return n.push("_cacheBuster="+(new Date).getTime()),t[0]+"?"+n.join("&")};const d={setApiRoot(e){a=e},setWpcomOriginApiUrl(e){o=e},setApiNonce(e){i={"X-WP-Nonce":e},s={credentials:"same-origin",headers:i},c={method:"post",credentials:"same-origin",headers:Object.assign({},i,{"Content-type":"application/json"})}},setCacheBusterCallback:e=>{l=e},registerSite:(e,t,r)=>{const o={};return(0,n.jetpackConfigHas)("consumer_slug")&&(o.plugin_slug=(0,n.jetpackConfigGet)("consumer_slug")),null!==t&&(o.redirect_uri=t),r&&(o.from=r),h(`${a}jetpack/v4/connection/register`,c,{body:JSON.stringify(o)}).then(p).then(m)},fetchAuthorizationUrl:e=>u((0,r.addQueryArgs)(`${a}jetpack/v4/connection/authorize_url`,{no_iframe:"1",redirect_uri:e}),s).then(p).then(m),fetchSiteConnectionData:()=>u(`${a}jetpack/v4/connection/data`,s).then(m),fetchSiteConnectionStatus:()=>u(`${a}jetpack/v4/connection`,s).then(m),fetchSiteConnectionTest:()=>u(`${a}jetpack/v4/connection/test`,s).then(p).then(m),fetchUserConnectionData:()=>u(`${a}jetpack/v4/connection/data`,s).then(m),fetchUserTrackingSettings:()=>u(`${a}jetpack/v4/tracking/settings`,s).then(p).then(m),updateUserTrackingSettings:e=>h(`${a}jetpack/v4/tracking/settings`,c,{body:JSON.stringify(e)}).then(p).then(m),disconnectSite:()=>h(`${a}jetpack/v4/connection`,c,{body:JSON.stringify({isActive:!1})}).then(p).then(m),fetchConnectUrl:()=>u(`${a}jetpack/v4/connection/url`,s).then(p).then(m),unlinkUser:(e=!1,t={})=>{const n={linked:!1,force:!!e};return t.disconnectAllUsers&&(n["disconnect-all-users"]=!0),h(`${a}jetpack/v4/connection/user`,c,{body:JSON.stringify(n)}).then(p).then(m)},reconnect:()=>h(`${a}jetpack/v4/connection/reconnect`,c).then(p).then(m),fetchConnectedPlugins:()=>u(`${a}jetpack/v4/connection/plugins`,s).then(p).then(m),setHasSeenWCConnectionModal:()=>h(`${a}jetpack/v4/seen-wc-connection-modal`,c).then(p).then(m),fetchModules:()=>u(`${a}jetpack/v4/module/all`,s).then(p).then(m),fetchModule:e=>u(`${a}jetpack/v4/module/${e}`,s).then(p).then(m),activateModule:e=>h(`${a}jetpack/v4/module/${e}/active`,c,{body:JSON.stringify({active:!0})}).then(p).then(m),deactivateModule:e=>h(`${a}jetpack/v4/module/${e}/active`,c,{body:JSON.stringify({active:!1})}),updateModuleOptions:(e,t)=>h(`${a}jetpack/v4/module/${e}`,c,{body:JSON.stringify(t)}).then(p).then(m),updateSettings:e=>h(`${a}jetpack/v4/settings`,c,{body:JSON.stringify(e)}).then(p).then(m),getProtectCount:()=>u(`${a}jetpack/v4/module/protect/data`,s).then(p).then(m),resetOptions:e=>h(`${a}jetpack/v4/options/${e}`,c,{body:JSON.stringify({reset:!0})}).then(p).then(m),activateVaultPress:()=>h(`${a}jetpack/v4/plugins`,c,{body:JSON.stringify({slug:"vaultpress",status:"active"})}).then(p).then(m),getVaultPressData:()=>u(`${a}jetpack/v4/module/vaultpress/data`,s).then(p).then(m),installPlugin:(e,t)=>{const n={slug:e,status:"active"};return t&&(n.source=t),h(`${a}jetpack/v4/plugins`,c,{body:JSON.stringify(n)}).then(p).then(m)},activateAkismet:()=>h(`${a}jetpack/v4/plugins`,c,{body:JSON.stringify({slug:"akismet",status:"active"})}).then(p).then(m),getAkismetData:()=>u(`${a}jetpack/v4/module/akismet/data`,s).then(p).then(m),checkAkismetKey:()=>u(`${a}jetpack/v4/module/akismet/key/check`,s).then(p).then(m),checkAkismetKeyTyped:e=>h(`${a}jetpack/v4/module/akismet/key/check`,c,{body:JSON.stringify({api_key:e})}).then(p).then(m),getFeatureTypeStatus:e=>u(`${a}jetpack/v4/feature/${e}`,s).then(p).then(m),fetchStatsData:e=>u(function(e){let t=`${a}jetpack/v4/module/stats/data`;-1!==t.indexOf("?")?t+=`&range=${encodeURIComponent(e)}`:t+=`?range=${encodeURIComponent(e)}`;return t}(e),s).then(p).then(m).then(v),getPluginUpdates:()=>u(`${a}jetpack/v4/updates/plugins`,s).then(p).then(m),getPlans:()=>u(`${a}jetpack/v4/plans`,s).then(p).then(m),fetchSettings:()=>u(`${a}jetpack/v4/settings`,s).then(p).then(m),updateSetting:e=>h(`${a}jetpack/v4/settings`,c,{body:JSON.stringify(e)}).then(p).then(m),fetchSiteData:()=>u(`${a}jetpack/v4/site`,s).then(p).then(m).then((e=>JSON.parse(e.data))),fetchSiteFeatures:()=>u(`${a}jetpack/v4/site/features`,s).then(p).then(m).then((e=>JSON.parse(e.data))),fetchSiteProducts:()=>u(`${a}jetpack/v4/site/products`,s).then(p).then(m),fetchSitePurchases:()=>u(`${a}jetpack/v4/site/purchases`,s).then(p).then(m).then((e=>JSON.parse(e.data))),fetchSiteBenefits:()=>u(`${a}jetpack/v4/site/benefits`,s).then(p).then(m).then((e=>JSON.parse(e.data))),fetchSiteDiscount:()=>u(`${a}jetpack/v4/site/discount`,s).then(p).then(m).then((e=>e.data)),fetchSetupQuestionnaire:()=>u(`${a}jetpack/v4/setup/questionnaire`,s).then(p).then(m),fetchRecommendationsData:()=>u(`${a}jetpack/v4/recommendations/data`,s).then(p).then(m),fetchRecommendationsProductSuggestions:()=>u(`${a}jetpack/v4/recommendations/product-suggestions`,s).then(p).then(m),fetchRecommendationsUpsell:()=>u(`${a}jetpack/v4/recommendations/upsell`,s).then(p).then(m),fetchRecommendationsConditional:()=>u(`${a}jetpack/v4/recommendations/conditional`,s).then(p).then(m),saveRecommendationsData:e=>h(`${a}jetpack/v4/recommendations/data`,c,{body:JSON.stringify({data:e})}).then(p),fetchProducts:()=>u(`${a}jetpack/v4/products`,s).then(p).then(m),fetchRewindStatus:()=>u(`${a}jetpack/v4/rewind`,s).then(p).then(m).then((e=>JSON.parse(e.data))),fetchScanStatus:()=>u(`${a}jetpack/v4/scan`,s).then(p).then(m).then((e=>JSON.parse(e.data))),dismissJetpackNotice:e=>h(`${a}jetpack/v4/notice/${e}`,c,{body:JSON.stringify({dismissed:!0})}).then(p).then(m),fetchPluginsData:()=>u(`${a}jetpack/v4/plugins`,s).then(p).then(m),fetchIntroOffers:()=>u(`${a}jetpack/v4/intro-offers`,s).then(p).then(m),fetchVerifySiteGoogleStatus:e=>u(null!==e?`${a}jetpack/v4/verify-site/google/${e}`:`${a}jetpack/v4/verify-site/google`,s).then(p).then(m),verifySiteGoogle:e=>h(`${a}jetpack/v4/verify-site/google`,c,{body:JSON.stringify({keyring_id:e})}).then(p).then(m),submitSurvey:e=>h(`${a}jetpack/v4/marketing/survey`,c,{body:JSON.stringify(e)}).then(p).then(m),saveSetupQuestionnaire:e=>h(`${a}jetpack/v4/setup/questionnaire`,c,{body:JSON.stringify(e)}).then(p).then(m),updateLicensingError:e=>h(`${a}jetpack/v4/licensing/error`,c,{body:JSON.stringify(e)}).then(p).then(m),updateLicenseKey:e=>h(`${a}jetpack/v4/licensing/set-license`,c,{body:JSON.stringify({license:e})}).then(p).then(m),getUserLicensesCounts:()=>u(`${a}jetpack/v4/licensing/user/counts`,s).then(p).then(m),getUserLicenses:()=>u(`${a}jetpack/v4/licensing/user/licenses`,s).then(p).then(m),updateLicensingActivationNoticeDismiss:e=>h(`${a}jetpack/v4/licensing/user/activation-notice-dismiss`,c,{body:JSON.stringify({last_detached_count:e})}).then(p).then(m),updateRecommendationsStep:e=>h(`${a}jetpack/v4/recommendations/step`,c,{body:JSON.stringify({step:e})}).then(p),confirmIDCSafeMode:()=>h(`${a}jetpack/v4/identity-crisis/confirm-safe-mode`,c).then(p),startIDCFresh:e=>h(`${a}jetpack/v4/identity-crisis/start-fresh`,c,{body:JSON.stringify({redirect_uri:e})}).then(p).then(m),migrateIDC:()=>h(`${a}jetpack/v4/identity-crisis/migrate`,c).then(p),attachLicenses:e=>h(`${a}jetpack/v4/licensing/attach-licenses`,c,{body:JSON.stringify({licenses:e})}).then(p).then(m),fetchSearchPlanInfo:()=>u(`${o}jetpack/v4/search/plan`,s).then(p).then(m),fetchSearchSettings:()=>u(`${o}jetpack/v4/search/settings`,s).then(p).then(m),updateSearchSettings:e=>h(`${o}jetpack/v4/search/settings`,c,{body:JSON.stringify(e)}).then(p).then(m),fetchSearchStats:()=>u(`${o}jetpack/v4/search/stats`,s).then(p).then(m),fetchWafSettings:()=>u(`${a}jetpack/v4/waf`,s).then(p).then(m),updateWafSettings:e=>h(`${a}jetpack/v4/waf`,c,{body:JSON.stringify(e)}).then(p).then(m),fetchWordAdsSettings:()=>u(`${a}jetpack/v4/wordads/settings`,s).then(p).then(m),updateWordAdsSettings:e=>h(`${a}jetpack/v4/wordads/settings`,c,{body:JSON.stringify(e)}),fetchSearchPricing:()=>u(`${o}jetpack/v4/search/pricing`,s).then(p).then(m),fetchMigrationStatus:()=>u(`${a}jetpack/v4/migration/status`,s).then(p).then(m),fetchBackupUndoEvent:()=>u(`${a}jetpack/v4/site/backup/undo-event`,s).then(p).then(m),fetchBackupPreflightStatus:()=>u(`${a}jetpack/v4/site/backup/preflight`,s).then(p).then(m)};function u(e,t){return fetch(l(e),t)}function h(e,t,a){return fetch(e,Object.assign({},t,a)).catch(g)}function v(e){return e.general&&void 0===e.general.response||e.week&&void 0===e.week.response||e.month&&void 0===e.month.response?e:{}}Object.assign(this,d)};function p(e){return e.status>=200&&e.status<300?e:404===e.status?new Promise((()=>{throw e.redirected?new l(e.redirected):new c})):e.json().catch((e=>h(e))).then((t=>{const a=new Error(`${t.message} (Status ${e.status})`);throw a.response=t,a.name="ApiError",a}))}function m(e){return e.json().catch((t=>h(t,e.redirected,e.url)))}function h(e,t,a){throw t?new s(a):new i}function g(){throw new d}},1330:(e,t,a)=>{"use strict";a.d(t,{A:()=>u});var n=a(6427),r=a(7723),o=a(991),i=a(1112),s=a(442),c=a(7425),l=a(723),d=a(2241);const __=r.__,u=({hideCloseButton:e=!1,title:t,children:a,step:u=null,totalSteps:p=null,buttonContent:m=null,buttonDisabled:h=!1,buttonHref:g=null,buttonExternalLink:v=!1,offset:f=32,onClose:y,onClick:b,...w})=>{const[E]=(0,s.A)("sm");if(!t||!a||!m)return null;w.position||(w.position=E?"top center":"middle right");const A={...w,offset:f,onClose:y},k=Number.isFinite(u)&&Number.isFinite(p);let R=null;return k&&(R=(0,r.sprintf)(/* translators: 1 Current step, 2 Total steps */
__("%1$d of %2$d","jetpack-videopress-pkg"),u,p)),React.createElement(n.Popover,A,React.createElement(l.Ay,null,React.createElement("div",{className:d.A.wrapper},React.createElement("div",{className:d.A.header},React.createElement(c.Ay,{variant:"title-small",className:d.A.title},t),!e&&React.createElement(React.Fragment,null,React.createElement(i.A,{size:"small",variant:"tertiary","aria-label":"close",className:d.A["close-button"],icon:o.A,iconSize:16,onClick:y}))),a,React.createElement("div",{className:d.A.footer},k&&React.createElement(c.Ay,{variant:"body",className:d.A.steps},R),React.createElement(i.A,{variant:"primary",className:d.A["action-button"],disabled:h,onClick:b,isExternalLink:v,href:g},m)))))}},2947:(e,t,a)=>{"use strict";a.d(t,{A:()=>p});var n=a(5932),r=a(7723),o=a(3022),i=a(1609),s=a(8250),c=a(7142),l=a(8509),d=a(5918),u=a(1631);const __=r.__,p=({children:e,moduleName:t=__("Jetpack","jetpack-videopress-pkg"),moduleNameHref:a,showHeader:p=!0,showFooter:m=!0,useInternalLinks:h=!1,showBackground:g=!0,sandboxedDomain:v="",apiRoot:f="",apiNonce:y="",optionalMenuItems:b,header:w})=>{(0,i.useEffect)((()=>{n.Ay.setApiRoot(f),n.Ay.setApiNonce(y)}),[f,y]);const E=(0,o.A)(u.A["admin-page"],{[u.A.background]:g}),A=(0,i.useCallback)((async()=>{try{const e=await n.Ay.fetchSiteConnectionTest();window.alert(e.message)}catch(e){window.alert((0,r.sprintf)(/* translators: placeholder is an error message. */
__("There was an error testing Jetpack. Error: %s","jetpack-videopress-pkg"),e.message))}}),[]);return React.createElement("div",{className:E},p&&React.createElement(d.A,{horizontalSpacing:5},React.createElement(l.A,{className:(0,o.A)(u.A["admin-page-header"],"jp-admin-page-header")},w||React.createElement(c.A,null),v&&React.createElement("code",{className:u.A["sandbox-domain-badge"],onClick:A,onKeyDown:A,role:"button",tabIndex:0,title:`Sandboxing via ${v}. Click to test connection.`},"API Sandboxed"))),React.createElement(d.A,{fluid:!0,horizontalSpacing:0},React.createElement(l.A,null,e)),m&&React.createElement(d.A,{horizontalSpacing:5},React.createElement(l.A,null,React.createElement(s.A,{moduleName:t,moduleNameHref:a,menu:b,useInternalLinks:h}))))}},5640:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n=a(1609),r=a.n(n),o=a(3496);const i=({children:e})=>r().createElement("div",{className:o.A.section},e)},766:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n=a(1609),r=a.n(n),o=a(1842);const i=({children:e})=>r().createElement("div",{className:o.A["section-hero"]},e)},8907:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(6072),r=a.n(n),o=a(7723),i=a(3022),s=a(1609),c=a.n(s);const __=o.__,l=({title:e=__("An Automattic Airline","jetpack-videopress-pkg"),height:t=7,className:a,...n})=>c().createElement("svg",r()({role:"img",x:"0",y:"0",viewBox:"0 0 935 38.2",enableBackground:"new 0 0 935 38.2","aria-labelledby":"jp-automattic-byline-logo-title",height:t,className:(0,i.A)("jp-automattic-byline-logo",a)},n),c().createElement("desc",{id:"jp-automattic-byline-logo-title"},e),c().createElement("path",{d:"M317.1 38.2c-12.6 0-20.7-9.1-20.7-18.5v-1.2c0-9.6 8.2-18.5 20.7-18.5 12.6 0 20.8 8.9 20.8 18.5v1.2C337.9 29.1 329.7 38.2 317.1 38.2zM331.2 18.6c0-6.9-5-13-14.1-13s-14 6.1-14 13v0.9c0 6.9 5 13.1 14 13.1s14.1-6.2 14.1-13.1V18.6zM175 36.8l-4.7-8.8h-20.9l-4.5 8.8h-7L157 1.3h5.5L182 36.8H175zM159.7 8.2L152 23.1h15.7L159.7 8.2zM212.4 38.2c-12.7 0-18.7-6.9-18.7-16.2V1.3h6.6v20.9c0 6.6 4.3 10.5 12.5 10.5 8.4 0 11.9-3.9 11.9-10.5V1.3h6.7V22C231.4 30.8 225.8 38.2 212.4 38.2zM268.6 6.8v30h-6.7v-30h-15.5V1.3h37.7v5.5H268.6zM397.3 36.8V8.7l-1.8 3.1 -14.9 25h-3.3l-14.7-25 -1.8-3.1v28.1h-6.5V1.3h9.2l14 24.4 1.7 3 1.7-3 13.9-24.4h9.1v35.5H397.3zM454.4 36.8l-4.7-8.8h-20.9l-4.5 8.8h-7l19.2-35.5h5.5l19.5 35.5H454.4zM439.1 8.2l-7.7 14.9h15.7L439.1 8.2zM488.4 6.8v30h-6.7v-30h-15.5V1.3h37.7v5.5H488.4zM537.3 6.8v30h-6.7v-30h-15.5V1.3h37.7v5.5H537.3zM569.3 36.8V4.6c2.7 0 3.7-1.4 3.7-3.4h2.8v35.5L569.3 36.8 569.3 36.8zM628 11.3c-3.2-2.9-7.9-5.7-14.2-5.7 -9.5 0-14.8 6.5-14.8 13.3v0.7c0 6.7 5.4 13 15.3 13 5.9 0 10.8-2.8 13.9-5.7l4 4.2c-3.9 3.8-10.5 7.1-18.3 7.1 -13.4 0-21.6-8.7-21.6-18.3v-1.2c0-9.6 8.9-18.7 21.9-18.7 7.5 0 14.3 3.1 18 7.1L628 11.3zM321.5 12.4c1.2 0.8 1.5 2.4 0.8 3.6l-6.1 9.4c-0.8 1.2-2.4 1.6-3.6 0.8l0 0c-1.2-0.8-1.5-2.4-0.8-3.6l6.1-9.4C318.7 11.9 320.3 11.6 321.5 12.4L321.5 12.4z"}),c().createElement("path",{d:"M37.5 36.7l-4.7-8.9H11.7l-4.6 8.9H0L19.4 0.8H25l19.7 35.9H37.5zM22 7.8l-7.8 15.1h15.9L22 7.8zM82.8 36.7l-23.3-24 -2.3-2.5v26.6h-6.7v-36H57l22.6 24 2.3 2.6V0.8h6.7v35.9H82.8z"}),c().createElement("path",{d:"M719.9 37l-4.8-8.9H694l-4.6 8.9h-7.1l19.5-36h5.6l19.8 36H719.9zM704.4 8l-7.8 15.1h15.9L704.4 8zM733 37V1h6.8v36H733zM781 37c-1.8 0-2.6-2.5-2.9-5.8l-0.2-3.7c-0.2-3.6-1.7-5.1-8.4-5.1h-12.8V37H750V1h19.6c10.8 0 15.7 4.3 15.7 9.9 0 3.9-2 7.7-9 9 7 0.5 8.5 3.7 8.6 7.9l0.1 3c0.1 2.5 0.5 4.3 2.2 6.1V37H781zM778.5 11.8c0-2.6-2.1-5.1-7.9-5.1h-13.8v10.8h14.4c5 0 7.3-2.4 7.3-5.2V11.8zM794.8 37V1h6.8v30.4h28.2V37H794.8zM836.7 37V1h6.8v36H836.7zM886.2 37l-23.4-24.1 -2.3-2.5V37h-6.8V1h6.5l22.7 24.1 2.3 2.6V1h6.8v36H886.2zM902.3 37V1H935v5.6h-26v9.2h20v5.5h-20v10.1h26V37H902.3z"}))},1112:(e,t,a)=>{"use strict";a.d(t,{A:()=>h});var n=a(6072),r=a.n(n),o=a(6427),i=a(7723),s=a(1113),c=a(3512),l=a(3022),d=a(1609),u=a.n(d),p=a(7560);const __=i.__,m=(0,d.forwardRef)(((e,t)=>{const{children:a,variant:n="primary",size:i="normal",weight:d="bold",icon:m,iconSize:h,disabled:g,isDestructive:v,isLoading:f,isExternalLink:y,className:b,text:w,fullWidth:E,...A}=e,k=(0,l.A)(p.A.button,b,{[p.A.normal]:"normal"===i,[p.A.small]:"small"===i,[p.A.icon]:Boolean(m),[p.A.loading]:f,[p.A.regular]:"regular"===d,[p.A["full-width"]]:E,[p.A["is-icon-button"]]:Boolean(m)&&!a});A.ref=t;const R="normal"===i?20:16,C=y&&u().createElement(u().Fragment,null,u().createElement(s.A,{size:R,icon:c.A,className:p.A["external-icon"]}),u().createElement(o.VisuallyHidden,{as:"span"},/* translators: accessibility text */
__("(opens in a new tab)","jetpack-videopress-pkg"))),S=y?"_blank":void 0,_=a?.[0]&&null!==a[0]&&"components-tooltip"!==a?.[0]?.props?.className;return u().createElement(o.Button,r()({target:S,variant:n,className:(0,l.A)(k,{"has-text":!!m&&_}),icon:y?void 0:m,iconSize:h,disabled:g,"aria-disabled":g,isDestructive:v,text:w},A),f&&u().createElement(o.Spinner,null),u().createElement("span",null,a),C)}));m.displayName="Button";const h=m},4437:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(1113),r=a(1797),o=a(3022),i=a(597),s=a(7425),c=a(1626);const l=({description:e,cta:t,onClick:a,href:l,openInNewTab:d=!1,className:u,tooltipText:p=""})=>{const m=void 0!==l?"a":"button",h="a"===m?{href:l,...d&&{target:"_blank"}}:{onClick:a};return React.createElement("div",{className:(0,o.A)(c.A.cut,u)},React.createElement("div",null,React.createElement("div",null,React.createElement(s.Ay,{className:c.A.description},e),p&&React.createElement(i.A,{className:c.A.iconContainer,iconSize:16,offset:4},React.createElement(s.Ay,{variant:"body-small"},p))),React.createElement("div",null,React.createElement(m,h,React.createElement(s.Ay,{className:c.A.cta},t)))),React.createElement(n.A,{icon:r.A,className:c.A.icon,size:30}))}},1883:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(7723),r=a(3022),o=a(1609);a(4459);const __=n.__;class i extends o.Component{static defaultProps={"aria-hidden":"false",focusable:"true"};needsOffset(e,t){return["gridicons-arrow-left","gridicons-arrow-right","gridicons-calendar","gridicons-cart","gridicons-folder","gridicons-help-outline","gridicons-info","gridicons-info-outline","gridicons-posts","gridicons-star-outline","gridicons-star"].indexOf(e)>=0&&t%18==0}getSVGDescription(e){if("description"in this.props)return this.props.description;switch(e){default:return"";case"gridicons-audio":return __("Has audio.","jetpack-videopress-pkg");case"gridicons-arrow-left":return __("Arrow left","jetpack-videopress-pkg");case"gridicons-arrow-right":return __("Arrow right","jetpack-videopress-pkg");case"gridicons-calendar":return __("Is an event.","jetpack-videopress-pkg");case"gridicons-cart":return __("Is a product.","jetpack-videopress-pkg");case"chevron-down":return __("Show filters","jetpack-videopress-pkg");case"gridicons-comment":return __("Matching comment.","jetpack-videopress-pkg");case"gridicons-cross":return __("Close.","jetpack-videopress-pkg");case"gridicons-filter":return __("Toggle search filters.","jetpack-videopress-pkg");case"gridicons-folder":return __("Category","jetpack-videopress-pkg");case"gridicons-help-outline":return __("Help","jetpack-videopress-pkg");case"gridicons-info":case"gridicons-info-outline":return __("Information.","jetpack-videopress-pkg");case"gridicons-image-multiple":return __("Has multiple images.","jetpack-videopress-pkg");case"gridicons-image":return __("Has an image.","jetpack-videopress-pkg");case"gridicons-page":return __("Page","jetpack-videopress-pkg");case"gridicons-post":return __("Post","jetpack-videopress-pkg");case"gridicons-jetpack-search":case"gridicons-search":return __("Magnifying Glass","jetpack-videopress-pkg");case"gridicons-tag":return __("Tag","jetpack-videopress-pkg");case"gridicons-video":return __("Has a video.","jetpack-videopress-pkg")}}renderIcon(e){switch(e){default:return null;case"gridicons-audio":return React.createElement("g",null,React.createElement("path",{d:"M8 4v10.184C7.686 14.072 7.353 14 7 14c-1.657 0-3 1.343-3 3s1.343 3 3 3 3-1.343 3-3V7h7v4.184c-.314-.112-.647-.184-1-.184-1.657 0-3 1.343-3 3s1.343 3 3 3 3-1.343 3-3V4H8z"}));case"gridicons-arrow-left":return React.createElement("g",null,React.createElement("path",{d:"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"}));case"gridicons-arrow-right":return React.createElement("g",null,React.createElement("path",{d:"M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8-8-8z"}));case"gridicons-block":return React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zM4 12c0-4.418 3.582-8 8-8 1.848 0 3.545.633 4.9 1.686L5.686 16.9C4.633 15.545 4 13.848 4 12zm8 8c-1.848 0-3.546-.633-4.9-1.686L18.314 7.1C19.367 8.455 20 10.152 20 12c0 4.418-3.582 8-8 8z"}));case"gridicons-calendar":return React.createElement("g",null,React.createElement("path",{d:"M19 4h-1V2h-2v2H8V2H6v2H5c-1.105 0-2 .896-2 2v13c0 1.104.895 2 2 2h14c1.104 0 2-.896 2-2V6c0-1.104-.896-2-2-2zm0 15H5V8h14v11z"}));case"gridicons-cart":return React.createElement("g",null,React.createElement("path",{d:"M9 20c0 1.1-.9 2-2 2s-1.99-.9-1.99-2S5.9 18 7 18s2 .9 2 2zm8-2c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2zm.396-5c.937 0 1.75-.65 1.952-1.566L21 5H7V4c0-1.105-.895-2-2-2H3v2h2v11c0 1.105.895 2 2 2h12c0-1.105-.895-2-2-2H7v-2h10.396z"}));case"gridicons-checkmark":return React.createElement("g",null,React.createElement("path",{d:"M11 17.768l-4.884-4.884 1.768-1.768L11 14.232l8.658-8.658C17.823 3.39 15.075 2 12 2 6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10c0-1.528-.353-2.97-.966-4.266L11 17.768z"}));case"gridicons-chevron-left":return React.createElement("g",null,React.createElement("path",{d:"M16.443 7.41L15.0399 6L9.06934 12L15.0399 18L16.443 16.59L11.8855 12L16.443 7.41Z"}));case"gridicons-chevron-right":return React.createElement("g",null,React.createElement("path",{d:"M10.2366 6L8.8335 7.41L13.391 12L8.8335 16.59L10.2366 18L16.2072 12L10.2366 6Z"}));case"gridicons-chevron-down":return React.createElement("g",null,React.createElement("path",{d:"M20 9l-8 8-8-8 1.414-1.414L12 14.172l6.586-6.586"}));case"gridicons-comment":return React.createElement("g",null,React.createElement("path",{d:"M3 6v9c0 1.105.895 2 2 2h9v5l5.325-3.804c1.05-.75 1.675-1.963 1.675-3.254V6c0-1.105-.895-2-2-2H5c-1.105 0-2 .895-2 2z"}));case"gridicons-computer":return React.createElement("g",null,React.createElement("path",{d:"M20 2H4c-1.104 0-2 .896-2 2v12c0 1.104.896 2 2 2h6v2H7v2h10v-2h-3v-2h6c1.104 0 2-.896 2-2V4c0-1.104-.896-2-2-2zm0 14H4V4h16v12z"}));case"gridicons-cross":return React.createElement("g",null,React.createElement("path",{d:"M18.36 19.78L12 13.41l-6.36 6.37-1.42-1.42L10.59 12 4.22 5.64l1.42-1.42L12 10.59l6.36-6.36 1.41 1.41L13.41 12l6.36 6.36z"}));case"gridicons-filter":return React.createElement("g",null,React.createElement("path",{d:"M10 19h4v-2h-4v2zm-4-6h12v-2H6v2zM3 5v2h18V5H3z"}));case"gridicons-folder":return React.createElement("g",null,React.createElement("path",{d:"M18 19H6c-1.1 0-2-.9-2-2V7c0-1.1.9-2 2-2h3c1.1 0 2 .9 2 2h7c1.1 0 2 .9 2 2v8c0 1.1-.9 2-2 2z"}));case"gridicons-help-outline":return React.createElement("g",null,React.createElement("path",{d:"M12 4c4.41 0 8 3.59 8 8s-3.59 8-8 8-8-3.59-8-8 3.59-8 8-8m0-2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 13h-2v2h2v-2zm-1.962-2v-.528c0-.4.082-.74.246-1.017.163-.276.454-.546.87-.808.333-.21.572-.397.717-.565.146-.168.22-.36.22-.577 0-.172-.078-.308-.234-.41-.156-.1-.358-.15-.608-.15-.62 0-1.34.22-2.168.658l-.854-1.67c1.02-.58 2.084-.872 3.194-.872.913 0 1.63.202 2.15.603.52.4.78.948.78 1.64 0 .495-.116.924-.347 1.287-.23.362-.6.705-1.11 1.03-.43.278-.7.48-.807.61-.108.13-.163.282-.163.458V13h-1.885z"}));case"gridicons-image":return React.createElement("g",null,React.createElement("path",{d:"M13 9.5c0-.828.672-1.5 1.5-1.5s1.5.672 1.5 1.5-.672 1.5-1.5 1.5-1.5-.672-1.5-1.5zM22 6v12c0 1.105-.895 2-2 2H4c-1.105 0-2-.895-2-2V6c0-1.105.895-2 2-2h16c1.105 0 2 .895 2 2zm-2 0H4v7.444L8 9l5.895 6.55 1.587-1.85c.798-.932 2.24-.932 3.037 0L20 15.426V6z"}));case"gridicons-image-multiple":return React.createElement("g",null,React.createElement("path",{d:"M15 7.5c0-.828.672-1.5 1.5-1.5s1.5.672 1.5 1.5S17.328 9 16.5 9 15 8.328 15 7.5zM4 20h14c0 1.105-.895 2-2 2H4c-1.1 0-2-.9-2-2V8c0-1.105.895-2 2-2v14zM22 4v12c0 1.105-.895 2-2 2H8c-1.105 0-2-.895-2-2V4c0-1.105.895-2 2-2h12c1.105 0 2 .895 2 2zM8 4v6.333L11 7l4.855 5.395.656-.73c.796-.886 2.183-.886 2.977 0l.513.57V4H8z"}));case"gridicons-info":return React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"}));case"gridicons-info-outline":return React.createElement("g",null,React.createElement("path",{d:"M13 9h-2V7h2v2zm0 2h-2v6h2v-6zm-1-7c-4.411 0-8 3.589-8 8s3.589 8 8 8 8-3.589 8-8-3.589-8-8-8m0-2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2z"}));case"gridicons-jetpack-search":return React.createElement("g",null,React.createElement("path",{d:"M0 9.257C0 4.15 4.151 0 9.257 0c5.105 0 9.256 4.151 9.256 9.257a9.218 9.218 0 01-2.251 6.045l.034.033h1.053L24 22.01l-1.986 1.989-6.664-6.662v-1.055l-.033-.033a9.218 9.218 0 01-6.06 2.264C4.15 18.513 0 14.362 0 9.257zm4.169 1.537h4.61V1.82l-4.61 8.973zm5.547-3.092v8.974l4.61-8.974h-4.61z"}));case"gridicons-phone":return React.createElement("g",null,React.createElement("path",{d:"M16 2H8c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h8c1.104 0 2-.896 2-2V4c0-1.104-.896-2-2-2zm-3 19h-2v-1h2v1zm3-2H8V5h8v14z"}));case"gridicons-pages":return React.createElement("g",null,React.createElement("path",{d:"M16 8H8V6h8v2zm0 2H8v2h8v-2zm4-6v12l-6 6H6c-1.105 0-2-.895-2-2V4c0-1.105.895-2 2-2h12c1.105 0 2 .895 2 2zm-2 10V4H6v16h6v-4c0-1.105.895-2 2-2h4z"}));case"gridicons-posts":return React.createElement("g",null,React.createElement("path",{d:"M16 19H3v-2h13v2zm5-10H3v2h18V9zM3 5v2h11V5H3zm14 0v2h4V5h-4zm-6 8v2h10v-2H11zm-8 0v2h5v-2H3z"}));case"gridicons-search":return React.createElement("g",null,React.createElement("path",{d:"M21 19l-5.154-5.154C16.574 12.742 17 11.42 17 10c0-3.866-3.134-7-7-7s-7 3.134-7 7 3.134 7 7 7c1.42 0 2.742-.426 3.846-1.154L19 21l2-2zM5 10c0-2.757 2.243-5 5-5s5 2.243 5 5-2.243 5-5 5-5-2.243-5-5z"}));case"gridicons-star-outline":return React.createElement("g",null,React.createElement("path",{d:"M12 6.308l1.176 3.167.347.936.997.042 3.374.14-2.647 2.09-.784.62.27.963.91 3.25-2.813-1.872-.83-.553-.83.552-2.814 1.87.91-3.248.27-.962-.783-.62-2.648-2.092 3.374-.14.996-.04.347-.936L12 6.308M12 2L9.418 8.953 2 9.257l5.822 4.602L5.82 21 12 16.89 18.18 21l-2.002-7.14L22 9.256l-7.418-.305L12 2z"}));case"gridicons-star":return React.createElement("g",null,React.createElement("path",{d:"M12 2l2.582 6.953L22 9.257l-5.822 4.602L18.18 21 12 16.89 5.82 21l2.002-7.14L2 9.256l7.418-.304"}));case"gridicons-tag":return React.createElement("g",null,React.createElement("path",{d:"M20 2.007h-7.087c-.53 0-1.04.21-1.414.586L2.592 11.5c-.78.78-.78 2.046 0 2.827l7.086 7.086c.78.78 2.046.78 2.827 0l8.906-8.906c.376-.374.587-.883.587-1.413V4.007c0-1.105-.895-2-2-2zM17.007 9c-1.105 0-2-.895-2-2s.895-2 2-2 2 .895 2 2-.895 2-2 2z"}));case"gridicons-video":return React.createElement("g",null,React.createElement("path",{d:"M20 4v2h-2V4H6v2H4V4c-1.105 0-2 .895-2 2v12c0 1.105.895 2 2 2v-2h2v2h12v-2h2v2c1.105 0 2-.895 2-2V6c0-1.105-.895-2-2-2zM6 16H4v-3h2v3zm0-5H4V8h2v3zm4 4V9l4.5 3-4.5 3zm10 1h-2v-3h2v3zm0-5h-2V8h2v3z"}));case"gridicons-lock":return React.createElement(React.Fragment,null,React.createElement("g",{id:"lock"},React.createElement("path",{d:"M18,8h-1V7c0-2.757-2.243-5-5-5S7,4.243,7,7v1H6c-1.105,0-2,0.895-2,2v10c0,1.105,0.895,2,2,2h12c1.105,0,2-0.895,2-2V10 C20,8.895,19.105,8,18,8z M9,7c0-1.654,1.346-3,3-3s3,1.346,3,3v1H9V7z M13,15.723V18h-2v-2.277c-0.595-0.346-1-0.984-1-1.723 c0-1.105,0.895-2,2-2s2,0.895,2,2C14,14.738,13.595,15.376,13,15.723z"})),React.createElement("g",{id:"Layer_1"}));case"gridicons-external":return React.createElement("g",null,React.createElement("path",{d:"M19 13v6c0 1.105-.895 2-2 2H5c-1.105 0-2-.895-2-2V7c0-1.105.895-2 2-2h6v2H5v12h12v-6h2zM13 3v2h4.586l-7.793 7.793 1.414 1.414L19 6.414V11h2V3h-8z"}))}}render(){const{size:e=24,className:t=""}=this.props,a=this.props.height||e,n=this.props.width||e,o=this.props.style||{height:a,width:n},i="gridicons-"+this.props.icon,s=(0,r.A)("gridicon",i,t,{"needs-offset":this.needsOffset(i,e)}),c=this.getSVGDescription(i);return React.createElement("svg",{className:s,focusable:this.props.focusable,height:a,onClick:this.props.onClick,style:o,viewBox:"0 0 24 24",width:n,xmlns:"http://www.w3.org/2000/svg","aria-hidden":this.props["aria-hidden"]},c?React.createElement("desc",null,c):null,this.renderIcon(i))}}const s=i},597:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var n=a(6427),r=a(3022),o=a(1609),i=a.n(o),s=a(1112),c=a(1883);a(4813);const l=e=>({"top-end":"top left",top:"top center","top-start":"top right","bottom-end":"bottom left",bottom:"bottom center","bottom-start":"bottom right"}[e]),d=({className:e="",iconClassName:t="",placement:a="bottom-end",animate:d=!0,iconCode:u="info-outline",iconSize:p=18,offset:m=10,title:h,children:g,popoverAnchorStyle:v="icon",forceShow:f=!1,hoverShow:y=!1,wide:b=!1,inline:w=!0,shift:E=!1})=>{const[A,k]=(0,o.useState)(!1),[R,C]=(0,o.useState)(null),S=(0,o.useCallback)((()=>k(!1)),[k]),_=(0,o.useCallback)((e=>{e.preventDefault(),k(!A)}),[A,k]),x={position:l(a),placement:a,animate:d,noArrow:!1,resize:!1,flip:!1,offset:m,focusOnMount:"container",onClose:S,className:"icon-tooltip-container",inline:w,shift:E},P="wrapper"===v,j=(0,r.A)("icon-tooltip-wrapper",e),N={left:P?0:-(62-p/2)+"px"},V=P&&f,z=(0,o.useCallback)((()=>{y&&(R&&(clearTimeout(R),C(null)),k(!0))}),[y,R]),U=(0,o.useCallback)((()=>{if(y){const e=setTimeout((()=>{k(!1),C(null)}),100);C(e)}}),[y]);return i().createElement("div",{className:j,"data-testid":"icon-tooltip_wrapper",onMouseEnter:z,onMouseLeave:U},!P&&i().createElement(s.A,{variant:"link",onMouseDown:_},i().createElement(c.A,{className:t,icon:u,size:p})),i().createElement("div",{className:(0,r.A)("icon-tooltip-helper",{"is-wide":b}),style:N},(V||A)&&i().createElement(n.Popover,x,i().createElement("div",null,h&&i().createElement("div",{className:"icon-tooltip-title"},h),i().createElement("div",{className:"icon-tooltip-content"},g)))))}},8478:(e,t,a)=>{"use strict";a.d(t,{WI:()=>l});var n=a(6072),r=a.n(n),o=a(6427),i=a(3022),s=(a(4705),a(7842));const c=({className:e,size:t=24,viewBox:a="0 0 24 24",opacity:n=1,color:c="#2C3338",children:l})=>{const d={className:(0,i.A)(s.A.iconWrapper,e),width:t,height:t,viewBox:a,opacity:n,fill:void 0};return c&&(d.fill=c),React.createElement(o.SVG,r()({},d,{fillRule:"evenodd",clipRule:"evenodd",xmlns:"http://www.w3.org/2000/svg"}),React.createElement(o.G,{opacity:n},l))},l=({opacity:e=1,size:t,color:a})=>React.createElement(c,{size:t,opacity:e,color:a},React.createElement(o.Path,{d:"M17.5 11.5a4 4 0 1 1-8 0 4 4 0 0 1 8 0Zm1.5 0a5.5 5.5 0 0 1-9.142 4.121l-3.364 2.943-.988-1.128 3.373-2.952A5.5 5.5 0 1 1 19 11.5Z"}))},8250:(e,t,a)=>{"use strict";a.d(t,{A:()=>y});var n=a(6072),r=a.n(n),o=a(7723),i=a(1113),s=a(3512),c=a(3022),l=a(1609),d=a.n(l),u=a(3924),p=a(1069),m=a(8907),h=(a(9128),a(7142)),g=a(442);const __=o.__,_x=o._x,v=()=>d().createElement(h.A,{logoColor:"#000",showText:!1,height:16,"aria-hidden":"true"}),f=()=>d().createElement(d().Fragment,null,d().createElement(i.A,{icon:s.A,size:16}),d().createElement("span",{className:"jp-dashboard-footer__accessible-external-link"},/* translators: accessibility text */
__("(opens in a new tab)","jetpack-videopress-pkg"))),y=({moduleName:e=__("Jetpack","jetpack-videopress-pkg"),className:t,moduleNameHref:a="https://jetpack.com",menu:n,useInternalLinks:o,onAboutClick:i,onPrivacyClick:s,onTermsClick:l,...h})=>{const[y]=(0,g.A)("sm","<="),[b]=(0,g.A)("md","<="),[w]=(0,g.A)("lg",">"),E=(0,p.A)();let A=[{label:_x("About","Link to learn more about Jetpack.","jetpack-videopress-pkg"),title:__("About Jetpack","jetpack-videopress-pkg"),href:o?new URL("admin.php?page=jetpack_about",E).href:(0,u.A)("jetpack-about"),target:o?"_self":"_blank",onClick:i},{label:_x("Privacy","Shorthand for Privacy Policy.","jetpack-videopress-pkg"),title:__("Automattic's Privacy Policy","jetpack-videopress-pkg"),href:o?new URL("admin.php?page=jetpack#/privacy",E).href:(0,u.A)("a8c-privacy"),target:o?"_self":"_blank",onClick:s},{label:_x("Terms","Shorthand for Terms of Service.","jetpack-videopress-pkg"),title:__("WordPress.com Terms of Service","jetpack-videopress-pkg"),href:(0,u.A)("wpcom-tos"),target:"_blank",onClick:l}];n&&(A=[...A,...n]);const k=d().createElement(d().Fragment,null,d().createElement(v,null),e);return d().createElement("footer",r()({className:(0,c.A)("jp-dashboard-footer",{"is-sm":y,"is-md":b,"is-lg":w},t),"aria-label":__("Jetpack","jetpack-videopress-pkg"),role:"contentinfo"},h),d().createElement("ul",null,d().createElement("li",{className:"jp-dashboard-footer__jp-item"},a?d().createElement("a",{href:a},k):k),A.map((e=>{const t="button"===e.role,a=!t&&"_blank"===e.target;return d().createElement("li",{key:e.label},d().createElement("a",{href:e.href,title:e.title,target:e.target,onClick:e.onClick,onKeyDown:e.onKeyDown,className:(0,c.A)("jp-dashboard-footer__menu-item",{"is-external":a}),role:e.role,rel:a?"noopener noreferrer":void 0,tabIndex:t?0:void 0},e.label,a&&d().createElement(f,null)))})),d().createElement("li",{className:"jp-dashboard-footer__a8c-item"},d().createElement("a",{href:o?new URL("admin.php?page=jetpack_about",E).href:(0,u.A)("a8c-about"),"aria-label":__("An Automattic Airline","jetpack-videopress-pkg")},d().createElement(m.A,{"aria-hidden":"true"})))))}},7142:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(6072),r=a.n(n),o=a(7723),i=a(3022),s=a(1609),c=a.n(s);const __=o.__,l=({logoColor:e="#069e08",showText:t=!0,className:a,height:n=32,...o})=>{const s=t?"0 0 118 32":"0 0 32 32";return c().createElement("svg",r()({xmlns:"http://www.w3.org/2000/svg",x:"0px",y:"0px",viewBox:s,className:(0,i.A)("jetpack-logo",a),"aria-labelledby":"jetpack-logo-title",height:n},o,{role:"img"}),c().createElement("title",{id:"jetpack-logo-title"},__("Jetpack Logo","jetpack-videopress-pkg")),c().createElement("path",{fill:e,d:"M16,0C7.2,0,0,7.2,0,16s7.2,16,16,16s16-7.2,16-16S24.8,0,16,0z M15,19H7l8-16V19z M17,29V13h8L17,29z"}),t&&c().createElement(c().Fragment,null,c().createElement("path",{d:"M41.3,26.6c-0.5-0.7-0.9-1.4-1.3-2.1c2.3-1.4,3-2.5,3-4.6V8h-3V6h6v13.4C46,22.8,45,24.8,41.3,26.6z"}),c().createElement("path",{d:"M65,18.4c0,1.1,0.8,1.3,1.4,1.3c0.5,0,2-0.2,2.6-0.4v2.1c-0.9,0.3-2.5,0.5-3.7,0.5c-1.5,0-3.2-0.5-3.2-3.1V12H60v-2h2.1V7.1 H65V10h4v2h-4V18.4z"}),c().createElement("path",{d:"M71,10h3v1.3c1.1-0.8,1.9-1.3,3.3-1.3c2.5,0,4.5,1.8,4.5,5.6s-2.2,6.3-5.8,6.3c-0.9,0-1.3-0.1-2-0.3V28h-3V10z M76.5,12.3 c-0.8,0-1.6,0.4-2.5,1.2v5.9c0.6,0.1,0.9,0.2,1.8,0.2c2,0,3.2-1.3,3.2-3.9C79,13.4,78.1,12.3,76.5,12.3z"}),c().createElement("path",{d:"M93,22h-3v-1.5c-0.9,0.7-1.9,1.5-3.5,1.5c-1.5,0-3.1-1.1-3.1-3.2c0-2.9,2.5-3.4,4.2-3.7l2.4-0.3v-0.3c0-1.5-0.5-2.3-2-2.3 c-0.7,0-2.3,0.5-3.7,1.1L84,11c1.2-0.4,3-1,4.4-1c2.7,0,4.6,1.4,4.6,4.7L93,22z M90,16.4l-2.2,0.4c-0.7,0.1-1.4,0.5-1.4,1.6 c0,0.9,0.5,1.4,1.3,1.4s1.5-0.5,2.3-1V16.4z"}),c().createElement("path",{d:"M104.5,21.3c-1.1,0.4-2.2,0.6-3.5,0.6c-4.2,0-5.9-2.4-5.9-5.9c0-3.7,2.3-6,6.1-6c1.4,0,2.3,0.2,3.2,0.5V13 c-0.8-0.3-2-0.6-3.2-0.6c-1.7,0-3.2,0.9-3.2,3.6c0,2.9,1.5,3.8,3.3,3.8c0.9,0,1.9-0.2,3.2-0.7V21.3z"}),c().createElement("path",{d:"M110,15.2c0.2-0.3,0.2-0.8,3.8-5.2h3.7l-4.6,5.7l5,6.3h-3.7l-4.2-5.8V22h-3V6h3V15.2z"}),c().createElement("path",{d:"M58.5,21.3c-1.5,0.5-2.7,0.6-4.2,0.6c-3.6,0-5.8-1.8-5.8-6c0-3.1,1.9-5.9,5.5-5.9s4.9,2.5,4.9,4.9c0,0.8,0,1.5-0.1,2h-7.3 c0.1,2.5,1.5,2.8,3.6,2.8c1.1,0,2.2-0.3,3.4-0.7C58.5,19,58.5,21.3,58.5,21.3z M56,15c0-1.4-0.5-2.9-2-2.9c-1.4,0-2.3,1.3-2.4,2.9 C51.6,15,56,15,56,15z"})))}},8176:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(6072),r=a.n(n),o=a(7723),i=a(3022),s=a(1609),c=a.n(s);const __=o.__,l=({showText:e=!0,className:t,height:a=32,...n})=>{const o=e?"0 0 184 32":"0 0 32 32";return c().createElement("svg",r()({xmlns:"http://www.w3.org/2000/svg",x:"0px",y:"0px",viewBox:o,className:(0,i.A)("jetpack-videopress-logo",t),"aria-labelledby":"jetpack-videopress-logo-title",height:a},n),c().createElement("desc",{id:"jetpack-videopress-logo-title"},__("VideoPress Logo","jetpack-videopress-pkg")),e?c().createElement(c().Fragment,null,c().createElement("path",{d:"M16.0006 32C24.8374 32 32.0012 24.8362 32.0012 15.9994C32.0012 7.16259 24.8374 0 16.0006 0C7.16377 0 0 7.16377 0 16.0006C0 24.8374 7.16377 32 16.0006 32Z",fill:"#069E08"}),c().createElement("path",{d:"M16.7945 13.3133V28.8245L24.7948 13.3133H16.7945V13.3133Z",fill:"white"}),c().createElement("path",{d:"M15.176 18.6571V3.17546L7.20647 18.6571H15.176Z",fill:"white"}),c().createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M38.0203 6.0536C39.279 6.0536 40.385 6.8849 40.7297 8.09007L44.1374 20.0064C44.1408 20.0185 44.1464 20.0295 44.1536 20.0391L48.1907 6.0536H50.4492H56.6922C61.068 6.0536 64.0012 8.88765 64.0012 13.1781C64.0012 17.5177 60.991 20.2238 56.4999 20.2238H53.5378H49.7209L48.9794 22.5087C48.3144 24.5579 46.3979 25.9464 44.2347 25.9464C42.0715 25.9464 40.1551 24.5579 39.49 22.5087L35.3397 9.71996C34.7519 7.90897 36.1085 6.0536 38.0203 6.0536ZM51.1005 15.9727H55.4228C57.2981 15.9727 58.4233 14.9001 58.4233 13.1781C58.4233 11.4462 57.2981 10.4227 55.4228 10.4227H52.9017L51.1005 15.9727Z",fill:"black"}),c().createElement("path",{d:"M79.5277 20.4681C79.8196 19.608 80.0287 18.9965 80.155 18.6335C80.2891 18.2627 80.5653 17.5091 80.9835 16.3728L83.9188 8.50195H86.9843L80.7941 24.3384H78.1902L72 8.50195H75.0892L78.0245 16.3728C78.4427 17.5091 78.7149 18.2627 78.8412 18.6335C78.9753 18.9965 79.1884 19.608 79.4803 20.4681H79.5277Z",fill:"black"}),c().createElement("path",{d:"M88.3459 12.8694H91.151V24.3384H88.3459V12.8694ZM88.2749 7.2H91.222V10.2418H88.2749V7.2Z",fill:"black"}),c().createElement("path",{d:"M98.762 22.3973C99.0382 22.3973 99.2986 22.3618 99.5432 22.2908C99.7957 22.2198 100.032 22.1251 100.253 22.0068C100.474 21.8884 100.668 21.7582 100.833 21.6162C101.007 21.4741 101.153 21.3361 101.271 21.2019V15.7574C100.94 15.4891 100.569 15.2682 100.159 15.0946C99.7483 14.9131 99.3143 14.8223 98.8567 14.8223C98.5647 14.8223 98.257 14.8815 97.9335 14.9999C97.6179 15.1182 97.3259 15.3194 97.0576 15.6035C96.7972 15.8876 96.5763 16.2821 96.3948 16.7871C96.2212 17.2921 96.1344 17.9352 96.1344 18.7164C96.1344 19.8684 96.3554 20.7679 96.7972 21.415C97.247 22.062 97.9019 22.3894 98.762 22.3973ZM93.2346 18.8347C93.2346 17.8326 93.3727 16.941 93.6489 16.1598C93.9251 15.3707 94.2999 14.7119 94.7733 14.1832C95.2546 13.6466 95.803 13.2482 96.4185 12.9878C97.034 12.7195 97.6731 12.5853 98.3359 12.5853C98.6436 12.5853 98.9356 12.613 99.2118 12.6682C99.4879 12.7234 99.7483 12.7984 99.9929 12.8931C100.238 12.9878 100.466 13.0943 100.679 13.2127C100.892 13.331 101.09 13.4533 101.271 13.5796H101.295C101.287 13.4218 101.279 13.2008 101.271 12.9168C101.271 12.6248 101.271 12.3092 101.271 11.9699V7.2H104.076V20.9297C104.076 21.6872 104.084 22.3382 104.1 22.8826C104.116 23.4271 104.132 23.9123 104.147 24.3384H101.614L101.496 23.1193H101.449C101.283 23.3166 101.082 23.5099 100.845 23.6993C100.616 23.8887 100.36 24.0544 100.076 24.1964C99.7996 24.3305 99.4919 24.4371 99.1526 24.516C98.8212 24.5949 98.4582 24.6343 98.0637 24.6343C97.4403 24.6343 96.8406 24.5239 96.2646 24.3029C95.6965 24.082 95.1836 23.7427 94.726 23.285C94.2762 22.8195 93.9132 22.2159 93.637 21.4741C93.3688 20.7245 93.2346 19.8447 93.2346 18.8347Z",fill:"black"}),c().createElement("path",{d:"M112.764 22.3737C113.309 22.3737 113.877 22.3184 114.469 22.208C115.061 22.0975 115.66 21.9397 116.268 21.7345V23.9715C115.905 24.1293 115.349 24.2793 114.599 24.4213C113.849 24.5633 113.072 24.6343 112.267 24.6343C111.455 24.6343 110.681 24.5318 109.947 24.3266C109.222 24.1136 108.586 23.7743 108.042 23.3087C107.497 22.8432 107.063 22.2395 106.74 21.4978C106.424 20.7561 106.266 19.8566 106.266 18.7992C106.266 17.7577 106.416 16.8463 106.716 16.0651C107.016 15.2839 107.418 14.6369 107.923 14.124C108.428 13.6111 109.005 13.2284 109.652 12.9759C110.306 12.7155 110.981 12.5853 111.675 12.5853C112.401 12.5853 113.068 12.6958 113.676 12.9168C114.291 13.1377 114.82 13.4849 115.262 13.9583C115.712 14.4318 116.059 15.0433 116.303 15.7929C116.556 16.5346 116.682 17.4144 116.682 18.4323C116.674 18.8268 116.662 19.1622 116.647 19.4384H109.131C109.17 19.967 109.289 20.4207 109.486 20.7995C109.683 21.1703 109.94 21.4741 110.255 21.7109C110.579 21.9397 110.953 22.1093 111.38 22.2198C111.806 22.3224 112.267 22.3737 112.764 22.3737ZM111.64 14.775C111.285 14.775 110.969 14.8421 110.693 14.9762C110.417 15.1025 110.176 15.28 109.971 15.5088C109.774 15.7298 109.616 15.9902 109.498 16.29C109.379 16.5898 109.3 16.9094 109.261 17.2487H113.77C113.77 16.9094 113.723 16.5898 113.628 16.29C113.542 15.9902 113.407 15.7298 113.226 15.5088C113.052 15.28 112.831 15.1025 112.563 14.9762C112.303 14.8421 111.995 14.775 111.64 14.775Z",fill:"black"}),c().createElement("path",{d:"M126.838 18.6098C126.838 18.0023 126.767 17.4657 126.625 17.0002C126.483 16.5346 126.285 16.144 126.033 15.8284C125.78 15.5128 125.477 15.2761 125.122 15.1182C124.767 14.9604 124.38 14.8815 123.962 14.8815C123.551 14.8815 123.169 14.9604 122.814 15.1182C122.466 15.2761 122.167 15.5128 121.914 15.8284C121.662 16.144 121.464 16.5346 121.322 17.0002C121.18 17.4657 121.109 18.0023 121.109 18.6098C121.109 19.2174 121.18 19.754 121.322 20.2195C121.464 20.6851 121.662 21.0757 121.914 21.3913C122.174 21.699 122.478 21.9357 122.825 22.1014C123.18 22.2593 123.567 22.3382 123.985 22.3382C124.404 22.3382 124.786 22.2593 125.133 22.1014C125.481 21.9357 125.78 21.699 126.033 21.3913C126.285 21.0757 126.483 20.6851 126.625 20.2195C126.767 19.754 126.838 19.2174 126.838 18.6098ZM129.738 18.6098C129.738 19.5094 129.603 20.33 129.335 21.0717C129.067 21.8134 128.68 22.4486 128.175 22.9773C127.678 23.506 127.075 23.9163 126.364 24.2082C125.654 24.4923 124.861 24.6343 123.985 24.6343C123.086 24.6343 122.277 24.4923 121.559 24.2082C120.849 23.9163 120.245 23.506 119.748 22.9773C119.251 22.4486 118.868 21.8134 118.6 21.0717C118.34 20.33 118.209 19.5094 118.209 18.6098C118.209 17.7103 118.344 16.8897 118.612 16.148C118.88 15.4062 119.263 14.7711 119.76 14.2424C120.265 13.7137 120.872 13.3073 121.583 13.0233C122.293 12.7313 123.086 12.5853 123.962 12.5853C124.861 12.5853 125.666 12.7313 126.376 13.0233C127.094 13.3073 127.702 13.7137 128.199 14.2424C128.696 14.7711 129.075 15.4062 129.335 16.148C129.603 16.8897 129.738 17.7103 129.738 18.6098Z",fill:"black"}),c().createElement("path",{d:"M136.473 18.2074C136.212 18.2074 135.956 18.2035 135.703 18.1956C135.451 18.1877 135.214 18.1719 134.993 18.1482V24.3384H132.093V8.50195H136.662C137.427 8.50195 138.094 8.53746 138.662 8.60847C139.238 8.6716 139.751 8.77023 140.201 8.90437C141.282 9.23578 142.115 9.76445 142.698 10.4904C143.282 11.2163 143.574 12.1395 143.574 13.26C143.574 14.0254 143.416 14.7158 143.101 15.3313C142.785 15.9389 142.324 16.4557 141.716 16.8818C141.108 17.3079 140.367 17.6353 139.491 17.8642C138.615 18.093 137.609 18.2074 136.473 18.2074ZM134.993 15.71C135.159 15.7337 135.368 15.7534 135.621 15.7692C135.881 15.7771 136.149 15.7811 136.425 15.7811C137.207 15.7811 137.861 15.7179 138.39 15.5917C138.919 15.4654 139.345 15.2918 139.668 15.0709C139.992 14.8421 140.225 14.5698 140.367 14.2542C140.509 13.9307 140.58 13.5796 140.58 13.2008C140.58 12.7353 140.473 12.3328 140.26 11.9936C140.047 11.6543 139.676 11.3899 139.148 11.2005C138.871 11.1059 138.528 11.0388 138.118 10.9993C137.715 10.952 137.218 10.9283 136.627 10.9283H134.993V15.71Z",fill:"black"}),c().createElement("path",{d:"M152.629 15.0946H152.393C151.532 15.0946 150.775 15.2248 150.12 15.4852C149.473 15.7377 148.972 16.1282 148.617 16.6569V24.3384H145.812V12.8694H148.132L148.392 14.4673H148.439C148.771 13.8834 149.224 13.4257 149.8 13.0943C150.384 12.755 151.083 12.5853 151.895 12.5853C152.179 12.5853 152.424 12.6011 152.629 12.6327V15.0946Z",fill:"black"}),c().createElement("path",{d:"M159.708 22.3737C160.252 22.3737 160.82 22.3184 161.412 22.208C162.004 22.0975 162.603 21.9397 163.211 21.7345V23.9715C162.848 24.1293 162.292 24.2793 161.542 24.4213C160.793 24.5633 160.015 24.6343 159.211 24.6343C158.398 24.6343 157.624 24.5318 156.891 24.3266C156.165 24.1136 155.53 23.7743 154.985 23.3087C154.441 22.8432 154.007 22.2395 153.683 21.4978C153.367 20.7561 153.21 19.8566 153.21 18.7992C153.21 17.7577 153.36 16.8463 153.659 16.0651C153.959 15.2839 154.362 14.6369 154.867 14.124C155.372 13.6111 155.948 13.2284 156.595 12.9759C157.25 12.7155 157.924 12.5853 158.619 12.5853C159.345 12.5853 160.011 12.6958 160.619 12.9168C161.234 13.1377 161.763 13.4849 162.205 13.9583C162.655 14.4318 163.002 15.0433 163.247 15.7929C163.499 16.5346 163.625 17.4144 163.625 18.4323C163.617 18.8268 163.606 19.1622 163.59 19.4384H156.074C156.113 19.967 156.232 20.4207 156.429 20.7995C156.626 21.1703 156.883 21.4741 157.198 21.7109C157.522 21.9397 157.897 22.1093 158.323 22.2198C158.749 22.3224 159.211 22.3737 159.708 22.3737ZM158.583 14.775C158.228 14.775 157.912 14.8421 157.636 14.9762C157.36 15.1025 157.119 15.28 156.914 15.5088C156.717 15.7298 156.559 15.9902 156.441 16.29C156.323 16.5898 156.244 16.9094 156.204 17.2487H160.714C160.714 16.9094 160.666 16.5898 160.572 16.29C160.485 15.9902 160.351 15.7298 160.169 15.5088C159.996 15.28 159.775 15.1025 159.506 14.9762C159.246 14.8421 158.938 14.775 158.583 14.775Z",fill:"black"}),c().createElement("path",{d:"M170.822 21.0125C170.822 20.7679 170.751 20.5667 170.609 20.4089C170.475 20.2511 170.293 20.1209 170.065 20.0183C169.836 19.9078 169.571 19.8132 169.272 19.7343C168.98 19.6553 168.672 19.5764 168.348 19.4975C167.938 19.3871 167.543 19.2608 167.165 19.1188C166.786 18.9689 166.451 18.7755 166.159 18.5388C165.875 18.3021 165.646 18.0062 165.472 17.6511C165.306 17.2882 165.224 16.8463 165.224 16.3255C165.224 15.6864 165.338 15.134 165.567 14.6685C165.796 14.195 166.103 13.8045 166.49 13.4967C166.885 13.189 167.338 12.9602 167.851 12.8102C168.372 12.6603 168.92 12.5853 169.496 12.5853C170.199 12.5853 170.858 12.6445 171.473 12.7629C172.088 12.8734 172.645 13.0193 173.142 13.2008V15.4852C172.881 15.3984 172.605 15.3195 172.313 15.2484C172.029 15.1695 171.737 15.1025 171.437 15.0472C171.146 14.992 170.854 14.9486 170.562 14.917C170.27 14.8776 169.994 14.8578 169.733 14.8578C169.402 14.8578 169.122 14.8894 168.893 14.9525C168.664 15.0157 168.479 15.1025 168.336 15.2129C168.194 15.3155 168.092 15.4378 168.029 15.5798C167.966 15.714 167.934 15.856 167.934 16.0059C167.934 16.2663 168.001 16.4794 168.135 16.6451C168.277 16.8108 168.471 16.9449 168.715 17.0475C168.96 17.1501 169.216 17.2369 169.485 17.3079C169.753 17.371 170.013 17.4341 170.266 17.4973C170.66 17.592 171.055 17.7064 171.449 17.8405C171.844 17.9746 172.199 18.164 172.515 18.4086C172.83 18.6453 173.087 18.957 173.284 19.3437C173.489 19.7224 173.592 20.2077 173.592 20.7995C173.592 21.4465 173.469 22.0107 173.225 22.492C172.98 22.9734 172.637 23.3758 172.195 23.6993C171.761 24.0149 171.236 24.2477 170.621 24.3976C170.013 24.5554 169.343 24.6343 168.609 24.6343C167.883 24.6343 167.228 24.5752 166.644 24.4568C166.068 24.3463 165.591 24.2043 165.212 24.0307V21.77C165.827 21.9989 166.395 22.1567 166.916 22.2435C167.437 22.3224 167.922 22.3618 168.372 22.3618C168.719 22.3618 169.043 22.3382 169.343 22.2908C169.642 22.2356 169.899 22.1527 170.112 22.0423C170.333 21.9318 170.506 21.7937 170.633 21.628C170.759 21.4544 170.822 21.2493 170.822 21.0125Z",fill:"black"}),c().createElement("path",{d:"M180.611 21.0125C180.611 20.7679 180.54 20.5667 180.398 20.4089C180.264 20.2511 180.082 20.1209 179.853 20.0183C179.624 19.9078 179.36 19.8132 179.06 19.7343C178.768 19.6553 178.461 19.5764 178.137 19.4975C177.727 19.3871 177.332 19.2608 176.954 19.1188C176.575 18.9689 176.239 18.7755 175.947 18.5388C175.663 18.3021 175.435 18.0062 175.261 17.6511C175.095 17.2882 175.012 16.8463 175.012 16.3255C175.012 15.6864 175.127 15.134 175.356 14.6685C175.584 14.195 175.892 13.8045 176.279 13.4967C176.673 13.189 177.127 12.9602 177.64 12.8102C178.161 12.6603 178.709 12.5853 179.285 12.5853C179.987 12.5853 180.646 12.6445 181.262 12.7629C181.877 12.8734 182.434 13.0193 182.931 13.2008V15.4852C182.67 15.3984 182.394 15.3195 182.102 15.2484C181.818 15.1695 181.526 15.1025 181.226 15.0472C180.934 14.992 180.642 14.9486 180.35 14.917C180.058 14.8776 179.782 14.8578 179.522 14.8578C179.191 14.8578 178.91 14.8894 178.682 14.9525C178.453 15.0157 178.267 15.1025 178.125 15.2129C177.983 15.3155 177.881 15.4378 177.818 15.5798C177.754 15.714 177.723 15.856 177.723 16.0059C177.723 16.2663 177.79 16.4794 177.924 16.6451C178.066 16.8108 178.259 16.9449 178.504 17.0475C178.749 17.1501 179.005 17.2369 179.273 17.3079C179.542 17.371 179.802 17.4341 180.055 17.4973C180.449 17.592 180.844 17.7064 181.238 17.8405C181.633 17.9746 181.988 18.164 182.303 18.4086C182.619 18.6453 182.875 18.957 183.073 19.3437C183.278 19.7224 183.38 20.2077 183.38 20.7995C183.38 21.4465 183.258 22.0107 183.014 22.492C182.769 22.9734 182.426 23.3758 181.984 23.6993C181.55 24.0149 181.025 24.2477 180.41 24.3976C179.802 24.5554 179.131 24.6343 178.398 24.6343C177.672 24.6343 177.017 24.5752 176.433 24.4568C175.857 24.3463 175.379 24.2043 175.001 24.0307V21.77C175.616 21.9989 176.184 22.1567 176.705 22.2435C177.226 22.3224 177.711 22.3618 178.161 22.3618C178.508 22.3618 178.831 22.3382 179.131 22.2908C179.431 22.2356 179.688 22.1527 179.901 22.0423C180.122 21.9318 180.295 21.7937 180.421 21.628C180.548 21.4544 180.611 21.2493 180.611 21.0125Z",fill:"black"})):c().createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.70585 8C6.82474 8 7.80784 8.73893 8.11419 9.81019L11.1433 20.4025C11.1464 20.4132 11.1513 20.423 11.1577 20.4315L14.7463 8H16.7538H22.3031C26.1927 8 28.8 10.5191 28.8 14.3329C28.8 18.1903 26.1243 20.5957 22.1322 20.5957H19.4992H16.1064L15.4473 22.6268C14.8562 24.4482 13.1527 25.6825 11.2298 25.6825C9.30695 25.6825 7.60344 24.4482 7.01232 22.6268L3.32309 11.259C2.80067 9.64921 4.00647 8 5.70585 8ZM17.3328 16.817H21.1747C22.8417 16.817 23.8419 15.8636 23.8419 14.3329C23.8419 12.7934 22.8417 11.8837 21.1747 11.8837H18.9338L17.3328 16.817Z",fill:"black"}))}},8509:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(3022),r=a(1609),o=a(1157);const i=Number(o.A.smCols),s=Number(o.A.mdCols),c=Number(o.A.lgCols),l=e=>{const{children:t,tagName:a="div",className:l}=e,d=Math.min(i,"number"==typeof e.sm?e.sm:i),u=Math.min(i,"object"==typeof e.sm?e.sm.start:0),p=Math.min(i,"object"==typeof e.sm?e.sm.end:0),m=Math.min(s,"number"==typeof e.md?e.md:s),h=Math.min(s,"object"==typeof e.md?e.md.start:0),g=Math.min(s,"object"==typeof e.md?e.md.end:0),v=Math.min(c,"number"==typeof e.lg?e.lg:c),f=Math.min(c,"object"==typeof e.lg?e.lg.start:0),y=Math.min(c,"object"==typeof e.lg?e.lg.end:0),b=(0,n.A)(l,{[o.A[`col-sm-${d}`]]:!(u&&p),[o.A[`col-sm-${u}-start`]]:u>0,[o.A[`col-sm-${p}-end`]]:p>0,[o.A[`col-md-${m}`]]:!(h&&g),[o.A[`col-md-${h}-start`]]:h>0,[o.A[`col-md-${g}-end`]]:g>0,[o.A[`col-lg-${v}`]]:!(f&&y),[o.A[`col-lg-${f}-start`]]:f>0,[o.A[`col-lg-${y}-end`]]:y>0});return(0,r.createElement)(a,{className:b},t)}},5918:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(3022),r=a(1609),o=a(2498);const i=({children:e,fluid:t=!1,tagName:a="div",className:i,horizontalGap:s=1,horizontalSpacing:c=1},l)=>{const d=(0,r.useMemo)((()=>{const e=`calc( var(--horizontal-spacing) * ${c} )`;return{paddingTop:e,paddingBottom:e,rowGap:`calc( var(--horizontal-spacing) * ${s} )`}}),[s,c]),u=(0,n.A)(i,o.A.container,{[o.A.fluid]:t});return(0,r.createElement)(a,{className:u,style:d,ref:l},e)},s=(0,r.forwardRef)(i)},442:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n=a(9491),r=a(9053);const o=["sm","md","lg"],i=(e,t)=>{const a=Array.isArray(e)?e:[e],i=Array.isArray(t)?t:[t],[s,c,l]=o,d={sm:(0,n.useMediaQuery)(r.A[s]),md:(0,n.useMediaQuery)(r.A[c]),lg:(0,n.useMediaQuery)(r.A[l])};return a.map(((e,t)=>{const a=i[t];return a?((e,t,a)=>{const n=o.indexOf(e),r=n+1,i=t.includes("=");let s=[];return t.startsWith("<")&&(s=o.slice(0,i?r:n)),t.startsWith(">")&&(s=o.slice(i?n:r)),s?.length?s.some((e=>a[e])):a[e]})(e,a,d):d[e]}))}},1876:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(3022),r=a(1609),o=a.n(r),i=a(7876);const s=({children:e=null,width:t=null,height:a=null,className:r=""})=>o().createElement("div",{className:(0,n.A)(i.A.placeholder,r),style:{width:t,height:a}},e)},356:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var n=a(1437);const r=(e,t={})=>{const a=(0,n.Y)();return new Intl.NumberFormat(a,t).format(e)}},9245:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>A,N0:()=>E,eY:()=>b,i7:()=>w});var n=a(7723),r=a(1113),o=a(3883),i=a(1249),s=a(3022),c=a(1609),l=a.n(c),d=a(597),u=a(442),p=a(5879),m=a(7425),h=a(3128);const __=n.__,g=__("Included","jetpack-videopress-pkg"),v=__("Not included","jetpack-videopress-pkg"),f=__("Coming soon","jetpack-videopress-pkg"),y=(0,c.createContext)(void 0),b=({isIncluded:e=!1,isComingSoon:t=!1,index:a=0,label:p=null,tooltipInfo:b,tooltipTitle:w,tooltipClassName:E=""})=>{const[A]=(0,u.A)("lg"),k=(0,c.useContext)(y)[a],R=t||e,C=k.name,S=k.tooltipInfo,_=k.tooltipTitle,x=b||!A&&S,P=((e,t,a)=>e?{lg:f,
// translators: Name of the current feature
default:(0,n.sprintf)(__("%s coming soon","jetpack-videopress-pkg"),a)}:{lg:t?g:v,default:t?a:(0,n.sprintf)(/* translators: Name of the current feature */
__("%s not included","jetpack-videopress-pkg"),a)})(t,e,C),j=A?P.lg:P.default;return l().createElement("div",{className:(0,s.A)(h.A.item,h.A.value)},l().createElement(r.A,{className:(0,s.A)(h.A.icon,R?h.A["icon-check"]:h.A["icon-cross"]),size:32,icon:R?o.A:i.A}),l().createElement(m.Ay,{variant:"body-small"},p||j),x&&l().createElement(d.A,{title:w||_,iconClassName:h.A["popover-icon"],className:(0,s.A)(h.A.popover,E),placement:"bottom-end",iconSize:14,offset:4,wide:Boolean(w&&b)},l().createElement(m.Ay,{variant:"body-small",component:"div"},b||S)))},w=({children:e})=>l().createElement("div",{className:h.A.header},e),E=({primary:e=!1,children:t})=>{let a=0;return l().createElement("div",{className:(0,s.A)(h.A.card,{[h.A["is-primary"]]:e})},c.Children.map(t,(e=>{const t=e;return t.type===b?(a++,(0,c.cloneElement)(t,{index:a-1})):t})))},A=({title:e,items:t,children:a,showIntroOfferDisclaimer:n=!1})=>{const[r]=(0,u.A)("lg");return l().createElement(y.Provider,{value:t},l().createElement("div",{className:(0,s.A)(h.A.container,{[h.A["is-viewport-large"]]:r}),style:{"--rows":t.length+1,"--columns":c.Children.toArray(a).length+1}},l().createElement("div",{className:h.A.table},l().createElement(m.Ay,{variant:"headline-small"},e),r&&t.map(((e,a)=>l().createElement("div",{className:(0,s.A)(h.A.item,{[h.A["last-feature"]]:a===t.length-1}),key:a},l().createElement(m.Ay,{variant:"body-small"},l().createElement("strong",null,e.name)),e.tooltipInfo&&l().createElement(d.A,{title:e.tooltipTitle,iconClassName:h.A["popover-icon"],className:h.A.popover,placement:e.tooltipPlacement?e.tooltipPlacement:"bottom-end",iconSize:14,offset:4,wide:Boolean(e.tooltipTitle&&e.tooltipInfo)},l().createElement(m.Ay,{variant:"body-small"},e.tooltipInfo))))),a)),l().createElement("div",{className:h.A["tos-container"]},l().createElement("div",{className:h.A.tos},n&&l().createElement(m.Ay,{variant:"body-small"},__("Reduced pricing is a limited offer for the first year and renews at regular price.","jetpack-videopress-pkg")),l().createElement(p.A,{multipleButtons:!0}))))}},489:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var n=a(7723),r=a(3022),o=a(7425),i=a(2746),s=a(9921);const __=n.__,c=({price:e,offPrice:t,currency:a="",showNotOffPrice:n=!0,hideDiscountLabel:c=!0,promoLabel:l="",legend:d=__("/month, paid yearly","jetpack-videopress-pkg"),isNotConvenientPrice:u=!1,hidePriceFraction:p=!1,children:m})=>{if(null==e&&null==t||!a)return null;n=n&&null!=t;const h="number"==typeof e&&"number"==typeof t?Math.floor((e-t)/e*100):0,g=!c&&h&&h>0?h+__("% off","jetpack-videopress-pkg"):null;return React.createElement(React.Fragment,null,React.createElement("div",{className:s.A.container},React.createElement("div",{className:(0,r.A)(s.A["price-container"],"product-price_container")},React.createElement(i.g,{value:t??e,currency:a,isOff:!u,hidePriceFraction:p}),n&&React.createElement(i.g,{value:e,currency:a,isOff:!1,hidePriceFraction:p}),g&&React.createElement(o.Ay,{className:(0,r.A)(s.A["promo-label"],"product-price_promo_label")},g))),React.createElement("div",{className:s.A.footer},m||React.createElement(o.Ay,{className:(0,r.A)(s.A.legend,"product-price_legend")},d),l&&React.createElement(o.Ay,{className:(0,r.A)(s.A["promo-label"],"product-price_promo_label")},l)))}},2746:(e,t,a)=>{"use strict";a.d(t,{g:()=>s});var n=a(7397),r=a(3022),o=a(7425),i=a(9921);const s=({value:e,currency:t,isOff:a,hidePriceFraction:s})=>{const c=(0,r.A)(i.A.price,"product-price_price",{[i.A["is-not-off-price"]]:!a}),{symbol:l,integer:d,fraction:u}=(0,n.vA)(e,t),p=!s||!u.endsWith("00");return React.createElement(o.Ay,{className:c,variant:"headline-medium",component:"p"},React.createElement(o.Ay,{className:i.A.symbol,component:"sup",variant:"title-medium"},l),d,p&&React.createElement(o.Ay,{component:"sup",variant:"body-small","data-testid":"PriceFraction"},React.createElement("strong",null,u)))}},2989:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(3022),r=a(3941);const o=({className:e,progressClassName:t,progress:a,size:o="normal"})=>{if(null==a)return null;const i={width:100*Math.max(Math.min(a,1),0)+"%"};return React.createElement("div",{className:(0,n.A)(e,r.A.wrapper,{[r.A.small]:"small"===o})},React.createElement("div",{className:(0,n.A)(t,r.A.progress),style:i}))}},5879:(e,t,a)=>{"use strict";a.d(t,{A:()=>g});var n=a(6072),r=a.n(n),o=a(6427),i=a(6087),s=a(7723),c=a(3022),l=a(3924),d=a(7425);a(6392);const __=s.__,u=({multipleButtonsLabels:e})=>Array.isArray(e)&&e.length>1?(0,i.createInterpolateElement)((0,s.sprintf)(/* translators: %1$s is button label 1 and %2$s is button label 2 */
__("By clicking <strong>%1$s</strong> or <strong>%2$s</strong>, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-videopress-pkg"),e[0],e[1]),{strong:React.createElement("strong",null),tosLink:React.createElement(h,{slug:"wpcom-tos"}),shareDetailsLink:React.createElement(h,{slug:"jetpack-support-what-data-does-jetpack-sync"})}):(0,i.createInterpolateElement)(__("By clicking the buttons above, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-videopress-pkg"),{tosLink:React.createElement(h,{slug:"wpcom-tos"}),shareDetailsLink:React.createElement(h,{slug:"jetpack-support-what-data-does-jetpack-sync"})}),p=({agreeButtonLabel:e})=>(0,i.createInterpolateElement)((0,s.sprintf)(/* translators: %s is a button label */
__("By clicking <strong>%s</strong>, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-videopress-pkg"),e),{strong:React.createElement("strong",null),tosLink:React.createElement(h,{slug:"wpcom-tos"}),shareDetailsLink:React.createElement(h,{slug:"jetpack-support-what-data-does-jetpack-sync"})}),m=()=>(0,i.createInterpolateElement)(__("By continuing you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site’s data</shareDetailsLink> with us. We’ll check if that email is linked to an existing WordPress.com account or create a new one instantly.","jetpack-videopress-pkg"),{tosLink:React.createElement(h,{slug:"wpcom-tos"}),shareDetailsLink:React.createElement(h,{slug:"jetpack-support-what-data-does-jetpack-sync"})}),h=({slug:e,children:t})=>React.createElement(o.ExternalLink,{className:"terms-of-service__link",href:(0,l.A)(e)},t),g=({className:e,multipleButtons:t,agreeButtonLabel:a,isTextOnly:n,...o})=>React.createElement(d.Ay,r()({className:(0,c.A)(e,"terms-of-service")},o),n?React.createElement(m,null):t?React.createElement(u,{multipleButtonsLabels:t}):React.createElement(p,{agreeButtonLabel:a}))},110:(e,t,a)=>{"use strict";a.d(t,{Q:()=>n,Z:()=>r});const n={"headline-medium":"h1","headline-small":"h2","headline-small-regular":"h2","title-medium":"h3","title-medium-semi-bold":"h3","title-small":"h4",body:"p","body-small":"p","body-extra-small":"p","body-extra-small-bold":"p",label:"p"},r=["mt","mr","mb","ml","mx","my","m","pt","pr","pb","pl","px","py","p"]},7425:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>u,hE:()=>p});var n=a(6072),r=a.n(n),o=a(3022),i=a(1609),s=a.n(i),c=a(110),l=a(2073);const d=(0,i.forwardRef)((({variant:e="body",children:t,component:a,className:n,...d},u)=>{const p=a||c.Q[e]||"span",m=(0,i.useMemo)((()=>c.Z.reduce(((e,t)=>(void 0!==d[t]&&(e+=l.A[`${t}-${d[t]}`]+" ",delete d[t]),e)),"")),[d]);return s().createElement(p,r()({className:(0,o.A)(l.A.reset,l.A[e],n,m)},d,{ref:u}),t)}));d.displayName="Text";const u=d,p=({children:e,size:t="medium",...a})=>s().createElement(d,r()({variant:`title-${t}`,mb:1},a),e)},723:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>p});var n=a(1609),r=a.n(n),o=a(5196);const i={"--font-headline-medium":"48px","--font-headline-small":"36px","--font-title-medium":"24px","--font-title-small":"20px","--font-body":"16px","--font-body-small":"14px","--font-body-extra-small":"12px","--font-title-large":"var(--font-headline-small)","--font-label":"var(--font-body-extra-small)"},s={"--jp-black":"#000000","--jp-black-80":"#2c3338","--jp-white":"#ffffff","--jp-white-off":"#f9f9f6","--jp-gray":"#dcdcde","--jp-gray-0":"#F6F7F7","--jp-gray-5":"var(--jp-gray)","--jp-gray-10":"#C3C4C7","--jp-gray-20":"#A7AAAD","--jp-gray-40":"#787C82","--jp-gray-50":"#646970","--jp-gray-60":"#50575E","--jp-gray-70":"#3C434A","--jp-gray-80":"#2C3338","--jp-gray-90":"#1d2327","--jp-gray-off":"#e2e2df","--jp-red-0":"#F7EBEC","--jp-red-5":"#FACFD2","--jp-red-40":"#E65054","--jp-red-50":"#D63638","--jp-red-60":"#B32D2E","--jp-red-70":"#8A2424","--jp-red-80":"#691C1C","--jp-red":"#d63639","--jp-yellow-5":"#F5E6B3","--jp-yellow-10":"#F2CF75","--jp-yellow-20":"#F0C930","--jp-yellow-30":"#DEB100","--jp-yellow-40":"#C08C00","--jp-yellow-50":"#9D6E00","--jp-yellow-60":"#7D5600","--jp-blue-20":"#68B3E8","--jp-blue-40":"#1689DB","--jp-pink":"#C9356E","--jp-green-0":"#f0f2eb","--jp-green-5":"#d0e6b8","--jp-green-10":"#9dd977","--jp-green-20":"#64ca43","--jp-green-30":"#2fb41f","--jp-green-40":"#069e08","--jp-green-50":"#008710","--jp-green-60":"#007117","--jp-green-70":"#005b18","--jp-green-80":"#004515","--jp-green-90":"#003010","--jp-green-100":"#001c09","--jp-green":"#069e08","--jp-green-primary":"var( --jp-green-40 )","--jp-green-secondary":"var( --jp-green-30 )"},c={"--jp-border-radius":"4px","--jp-menu-border-height":"1px","--jp-underline-thickness":"2px"},l={"--spacing-base":"8px"},d={},u=(e,t,a)=>{const n={...i,...s,...c,...l};for(const t in n)e.style.setProperty(t,n[t]);a&&e.classList.add(o.A.global),t&&(d[t]={provided:!0,root:e})},p=({children:e=null,targetDom:t,id:a,withGlobalStyles:o=!0})=>{const i=(0,n.useRef)(),s=d?.[a]?.provided;return(0,n.useLayoutEffect)((()=>{if(!s)return t?u(t,a,o):void(i?.current&&u(i.current,a,o))}),[t,i,s,a,o]),t?r().createElement(r().Fragment,null,e):r().createElement("div",{ref:i},e)}},1437:(e,t,a)=>{"use strict";a.d(t,{Y:()=>r});var n=a(8443);const r=()=>{const{l10n:{locale:e}}=(0,n.getSettings)();if(e)return(e=>{const t=e.match(/^([a-z]{2,3})(_[a-z]{2}|_[a-z][a-z0-9]{4,7})?(?:_.*)?$/i);return t?`${t[1]}${t[2]?t[2]:""}`.replace("_","-"):"en-US"})(e);return window?.window?.navigator?.language??"en-US"}},1069:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var n=a(7999);function r(){return(0,n.getScriptData)()?.site?.admin_url||window.Initial_State?.adminUrl||window.Jetpack_Editor_Initial_State?.adminUrl||window?.myJetpackInitialState?.adminUrl||null}},3924:(e,t,a)=>{"use strict";function n(e,t={}){const a={};let n;if("undefined"!=typeof window&&(n=window?.JP_CONNECTION_INITIAL_STATE?.calypsoEnv),0===e.search("https://")){const t=new URL(e);e=`https://${t.host}${t.pathname}`,a.url=encodeURIComponent(e)}else a.source=encodeURIComponent(e);for(const e in t)a[e]=encodeURIComponent(t[e]);!Object.keys(a).includes("site")&&"undefined"!=typeof jetpack_redirects&&Object.hasOwn(jetpack_redirects,"currentSiteRawUrl")&&(a.site=jetpack_redirects.currentBlogID??jetpack_redirects.currentSiteRawUrl),n&&(a.calypso_env=n);return"https://jetpack.com/redirect/?"+Object.keys(a).map((e=>e+"="+a[e])).join("&")}a.d(t,{A:()=>n})},6439:(e,t,a)=>{let n={};try{n=a(3018)}catch{console.error("jetpackConfig is missing in your webpack config file. See @automattic/jetpack-config"),n={missingConfig:!0}}const r=e=>Object.hasOwn(n,e);e.exports={jetpackConfigHas:r,jetpackConfigGet:e=>{if(!r(e))throw'This app requires the "'+e+'" Jetpack Config to be defined in your webpack configuration file. See details in @automattic/jetpack-config package docs.';return n[e]}}},5985:(e,t,a)=>{"use strict";a.d(t,{pg:()=>n.A});a(2810),a(4815);var n=a(1409);a(2034),a(5595),a(3265),a(3489),a(7119),a(8406),a(6923),a(335),a(8290),a(9061),a(5929),a(5765)},5765:(e,t,a)=>{"use strict";a(8490)},2810:(e,t,a)=>{"use strict";a(8377).T["Jetpack Green 40"]},335:(e,t,a)=>{"use strict";a(6087)},4815:(e,t,a)=>{"use strict";a(7999)},3489:(e,t,a)=>{"use strict";var n=a(372);a(9384),a(6087);const{tracks:r}=n.A,{recordEvent:o}=r},7119:(e,t,a)=>{"use strict";a(7143),a(6087),a(8468)},6923:(e,t,a)=>{"use strict";a(7143),a(6087),a(8290)},8406:(e,t,a)=>{"use strict";a(6087)},5929:(e,t,a)=>{"use strict";a(7143),a(2619),a(3265),a(7119)},9520:(e,t,a)=>{"use strict";var n=a(6941),r=a.n(n);window,r()("shared-extension-utils:connection")},9061:(e,t,a)=>{"use strict";a(9520)},7105:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>u,E9:()=>d});var n=a(7143),r=a(2634),o=a(4478),i=a(8290);const s="SET_JETPACK_MODULES";function c(e){return d({isLoading:e})}function l(e,t){return{type:"SET_MODULE_UPDATING",name:e,isUpdating:t}}function d(e){return{type:s,options:e}}const u={updateJetpackModuleStatus:function*(e){try{yield l(e.name,!0),yield(0,o.sB)(e);const t=yield(0,o.wz)();return yield d({data:t}),!0}catch{const e=(0,n.select)(i.F).getJetpackModules();return yield d(e),!1}finally{yield l(e.name,!1)}},setJetpackModules:d,fetchModules:function*(){if((0,r.Sy)())return!0;try{yield c(!0);const e=yield(0,o.wz)();return yield d({data:e}),!0}catch{const e=(0,n.select)(i.F).getJetpackModules();return yield d(e),!1}finally{yield c(!1)}}}},4478:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>l,sB:()=>c,wz:()=>s});var n=a(1455),r=a.n(n);const o="FETCH_JETPACK_MODULES",i="UPDATE_JETPACK_MODULE_STATUS",s=()=>({type:o}),c=e=>({type:i,settings:e}),l={[o]:function(){return r()({path:"/jetpack/v4/module/all",method:"GET"})},[i]:function({settings:e}){return r()({path:`/jetpack/v4/module/${e.name}/active`,method:"POST",data:{active:e.active}})}}},8290:(e,t,a)=>{"use strict";a.d(t,{F:()=>l});var n=a(7143),r=a(7105),o=a(4478),i=a(8862),s=a(2701),c=a(1640);const l="jetpack-modules",d=(0,n.createReduxStore)(l,{reducer:i.A,actions:r.Ay,controls:o.Ay,resolvers:s.A,selectors:c.A});(0,n.register)(d);const u=window?.Initial_State?.getModules||window?.Jetpack_Editor_Initial_State?.modules||null;null!==u&&(0,n.dispatch)(l).setJetpackModules({data:{...u}})},8862:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});const n={isLoading:!1,isUpdating:{},data:{}},r=(e=n,t)=>{switch(t.type){case"SET_JETPACK_MODULES":return{...e,...t.options};case"SET_MODULE_UPDATING":return{...e,isUpdating:{...e.isUpdating,[t.name]:t.isUpdating}}}return e}},2701:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(7105),r=a(4478);const o={getJetpackModules:function*(){try{const e=yield(0,r.wz)();if(e)return(0,n.E9)({data:e})}catch(e){console.error(e)}}}},1640:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var n=a(2634);const r={getJetpackModules:e=>e.data,isModuleActive:(e,t)=>(0,n.Sy)()||(e?.data?.[t]?.activated??!1),areModulesLoading:e=>e.isLoading??!1,isModuleUpdating:(e,t)=>e?.isUpdating?.[t]??!1}},3265:(e,t,a)=>{"use strict";var n=a(7723);a(3832),a(8468),a(4815);const __=n.__;__("Upgrade your plan to use video covers","jetpack-videopress-pkg"),__("Upgrade your plan to upload audio","jetpack-videopress-pkg")},2034:(e,t,a)=>{"use strict";a(2279)},1409:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var n=a(7999);function r(){const{connectedPlugins:e,connectionStatus:t}=(0,n.getScriptData)()?.connection??{};return t?.isActive&&e?.some((({slug:e})=>"jetpack"===e))}},2634:(e,t,a)=>{"use strict";function n(){return"object"==typeof window&&"string"==typeof window._currentSiteType?window._currentSiteType:null}function r(){return"simple"===n()}a.d(t,{Sy:()=>r})},5595:(e,t,a)=>{"use strict";a(6072),a(9491)},4705:(e,t,a)=>{"use strict";a(8992),a(1135)},1135:(e,t,a)=>{"use strict";a.d(t,{$:()=>n});const n=[{name:"amazon",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M13.582 8.182c-1.648.185-3.802.308-5.344.984-1.781.769-3.03 2.337-3.03 4.644 0 2.953 1.86 4.429 4.253 4.429 2.02 0 3.125-.477 4.685-2.065.516.747.685 1.109 1.629 1.894a.59.59 0 0 0 .672-.066l.006.006c.567-.505 1.599-1.401 2.18-1.888.231-.188.19-.496.009-.754-.52-.718-1.072-1.303-1.072-2.634V8.305c0-1.876.133-3.599-1.249-4.891C15.23 2.369 13.422 2 12.04 2 9.336 2 6.318 3.01 5.686 6.351c-.068.355.191.542.423.594l2.754.298c.258-.013.445-.266.494-.523.236-1.151 1.2-1.706 2.284-1.706.584 0 1.249.215 1.595.738.398.584.346 1.384.346 2.061zm-.533 5.906c-.451.8-1.169 1.291-1.967 1.291-1.09 0-1.728-.83-1.728-2.061 0-2.42 2.171-2.86 4.227-2.86v.615c.001 1.108.027 2.031-.532 3.015m7.634 5.251C18.329 21.076 14.917 22 11.979 22c-4.118 0-7.826-1.522-10.632-4.057-.22-.199-.024-.471.241-.317 3.027 1.762 6.771 2.823 10.639 2.823 2.608 0 5.476-.541 8.115-1.66.397-.169.73.262.341.55m.653 1.704c-.194.163-.379.076-.293-.139.284-.71.92-2.298.619-2.684s-1.99-.183-2.749-.092c-.23.027-.266-.173-.059-.319 1.348-.946 3.555-.673 3.811-.356.26.32-.066 2.533-1.329 3.59"})))},{name:"behance",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M7.799 5.698c.589 0 1.12.051 1.606.156q.722.155 1.241.507.516.351.804.938c.188.387.281.871.281 1.443q0 .93-.421 1.551-.424.62-1.255 1.014 1.133.328 1.689 1.146.559.822.557 1.975 0 .935-.359 1.612a3.14 3.14 0 0 1-.973 1.114q-.613.432-1.399.637A6.1 6.1 0 0 1 7.963 18H2V5.698zm-.35 4.97q.721 0 1.192-.345.465-.344.463-1.119 0-.43-.152-.707a1.1 1.1 0 0 0-.416-.427 1.7 1.7 0 0 0-.596-.216 3.6 3.6 0 0 0-.697-.06H4.709v2.874zm.151 5.237q.401.001.759-.077c.243-.053.457-.137.637-.261.182-.12.332-.283.441-.491q.164-.31.163-.798-.002-.948-.533-1.357c-.356-.27-.83-.404-1.413-.404H4.709v3.388zm8.562-.041q.552.538 1.583.538.74 0 1.277-.374c.354-.248.571-.514.654-.79h2.155c-.347 1.072-.872 1.838-1.589 2.299-.708.463-1.572.693-2.58.693q-1.05 0-1.899-.337a4 4 0 0 1-1.439-.958 4.4 4.4 0 0 1-.904-1.484 5.4 5.4 0 0 1-.32-1.899q0-1 .329-1.863a4.4 4.4 0 0 1 .933-1.492q.607-.63 1.444-.994a4.6 4.6 0 0 1 1.857-.363q1.131-.001 1.98.44a3.94 3.94 0 0 1 1.389 1.181 4.8 4.8 0 0 1 .783 1.69q.24.947.171 1.983h-6.428c-.001.706.237 1.372.604 1.73m2.811-4.68c-.291-.321-.783-.496-1.384-.496q-.585 0-.973.2a2 2 0 0 0-.621.491 1.8 1.8 0 0 0-.328.628 2.7 2.7 0 0 0-.111.587h3.98c-.058-.625-.271-1.085-.563-1.41m-3.916-3.446h4.985V6.524h-4.985z"})))},{name:"blogger-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19.779 9.904h-.981l-.021.001a1.163 1.163 0 0 1-1.16-1.079l-.001-.013A5.813 5.813 0 0 0 11.803 3H8.871a5.813 5.813 0 0 0-5.813 5.813v6.375a5.813 5.813 0 0 0 5.813 5.813h6.257a5.814 5.814 0 0 0 5.813-5.813l.002-4.121a1.164 1.164 0 0 0-1.164-1.163M8.726 7.713h3.291a1.117 1.117 0 1 1 0 2.234H8.726a1.117 1.117 0 1 1 0-2.234m6.601 8.657H8.72a1.057 1.057 0 1 1 0-2.114h6.607a1.057 1.057 0 1 1 0 2.114"})))},{name:"blogger",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M14.722 14.019a.654.654 0 0 1-.654.654H9.977a.654.654 0 0 1 0-1.308h4.091c.361 0 .654.293.654.654m-4.741-3.321h2.038a.692.692 0 0 0 0-1.384H9.981a.692.692 0 0 0 0 1.384M21 5v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2m-3.456 6.39a.72.72 0 0 0-.72-.72h-.607l-.013.001a.72.72 0 0 1-.718-.668l-.001-.008a3.6 3.6 0 0 0-3.599-3.599H10.07a3.6 3.6 0 0 0-3.599 3.599v3.947a3.6 3.6 0 0 0 3.599 3.599h3.874a3.6 3.6 0 0 0 3.599-3.599z"})))},{name:"bluesky",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M21.2 3.3c-.5-.2-1.4-.5-3.6 1C15.4 6 12.9 9.2 12 11c-.9-1.8-3.4-5-5.7-6.7-2.2-1.6-3-1.3-3.6-1S2 4.6 2 5.1s.3 4.7.5 5.4c.7 2.3 3.1 3.1 5.3 2.8-3.3.5-6.2 1.7-2.4 5.9 4.2 4.3 5.7-.9 6.5-3.6.8 2.7 1.7 7.7 6.4 3.6 3.6-3.6 1-5.4-2.3-5.9 2.2.2 4.6-.5 5.3-2.8.4-.7.7-4.8.7-5.4 0-.5-.1-1.5-.8-1.8"})))},{name:"codepen",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"m22.016 8.84-.007-.037q-.006-.037-.015-.072-.007-.022-.013-.042l-.023-.062-.02-.042a.4.4 0 0 0-.03-.057l-.025-.038-.035-.052-.03-.037q-.021-.026-.043-.045-.015-.018-.035-.035a.4.4 0 0 0-.048-.04l-.037-.03-.015-.012-9.161-6.096a.86.86 0 0 0-.955 0L2.359 8.237l-.015.012-.038.028-.048.04a.638.638 0 0 0-.078.082q-.018.018-.03.037-.018.026-.035.052l-.025.038q-.016.031-.03.059l-.02.041a1 1 0 0 0-.034.106q-.01.034-.016.071-.003.02-.006.037a1 1 0 0 0-.009.114v6.093q0 .056.008.112l.007.038q.006.035.015.072a.2.2 0 0 0 .013.04q.01.032.022.063l.02.04a.4.4 0 0 0 .055.096l.035.052.03.037.042.045.035.035q.023.02.048.04l.038.03.013.01 9.163 6.095a.858.858 0 0 0 .959.004l9.163-6.095.015-.01q.02-.015.037-.03l.048-.04q.02-.017.035-.035.025-.024.043-.045l.03-.037.035-.052.025-.038a.4.4 0 0 0 .03-.058l.02-.04.023-.063c.003-.013.01-.027.013-.04q.009-.037.015-.072l.007-.037q.006-.062.007-.117V8.954a1 1 0 0 0-.008-.114m-9.154-4.376 6.751 4.49-3.016 2.013-3.735-2.492zm-1.724 0v4.009l-3.735 2.494-3.014-2.013zm-7.439 6.098L5.853 12l-2.155 1.438zm7.439 8.974-6.749-4.491 3.015-2.011 3.735 2.492zM12 14.035 8.953 12 12 9.966 15.047 12zm.862 5.501v-4.009l3.735-2.492 3.016 2.011zm7.441-6.098L18.147 12l2.156-1.438z"})))},{name:"deezer",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M20.129 3.662c.222-1.287.548-2.096.909-2.098h.001c.673.002 1.219 2.809 1.219 6.274s-.546 6.274-1.22 6.274c-.276 0-.531-.477-.736-1.276-.324 2.926-.997 4.937-1.776 4.937-.603 0-1.144-1.208-1.507-3.114-.248 3.624-.872 6.195-1.602 6.195-.458 0-.875-1.019-1.184-2.678C13.861 21.6 13.003 24 12.002 24s-1.861-2.399-2.231-5.824c-.307 1.659-.724 2.678-1.184 2.678-.73 0-1.352-2.571-1.602-6.195-.363 1.905-.903 3.114-1.507 3.114-.778 0-1.452-2.011-1.776-4.937-.204.802-.46 1.276-.736 1.276-.674 0-1.22-2.809-1.22-6.274s.546-6.274 1.22-6.274c.362 0 .685.812.91 2.098.357-2.22.94-3.662 1.6-3.662.784 0 1.463 2.04 1.784 5.002.314-2.156.791-3.53 1.325-3.53.749 0 1.385 2.703 1.621 6.474.443-1.933 1.085-3.146 1.795-3.146s1.352 1.214 1.795 3.146c.237-3.771.872-6.474 1.621-6.474.533 0 1.009 1.374 1.325 3.53.321-2.962 1-5.002 1.784-5.002.658 0 1.244 1.443 1.603 3.662M0 7.221c0-1.549.31-2.805.692-2.805s.692 1.256.692 2.805-.31 2.805-.692 2.805S0 8.77 0 7.221m22.616 0c0-1.549.31-2.805.692-2.805S24 5.672 24 7.221s-.31 2.805-.692 2.805-.692-1.256-.692-2.805"})))},{name:"discord",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M20.33 4.523A20 20 0 0 0 15.379 3a14 14 0 0 0-.634 1.289 18.4 18.4 0 0 0-5.495 0A14 14 0 0 0 8.615 3 20 20 0 0 0 3.66 4.527C.527 9.163-.323 13.684.102 18.141a20 20 0 0 0 6.073 3.049 14.7 14.7 0 0 0 1.301-2.097 13 13 0 0 1-2.048-.978q.258-.189.502-.378a14.27 14.27 0 0 0 12.142 0q.247.202.502.378a13 13 0 0 1-2.052.98 14.5 14.5 0 0 0 1.301 2.095 19.9 19.9 0 0 0 6.076-3.047c.498-5.168-.851-9.648-3.568-13.62M8.013 15.4c-1.183 0-2.161-1.074-2.161-2.395S6.796 10.6 8.01 10.6s2.183 1.083 2.163 2.405S9.22 15.4 8.013 15.4m7.974 0c-1.186 0-2.16-1.074-2.16-2.395s.944-2.405 2.16-2.405 2.178 1.083 2.157 2.405-.951 2.395-2.158 2.395"})))},{name:"dribbble",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10m8.434-8.631c-.292-.092-2.644-.794-5.32-.365 1.117 3.07 1.572 5.57 1.659 6.09a8.56 8.56 0 0 0 3.661-5.725m-5.098 6.507c-.127-.749-.623-3.361-1.822-6.477l-.056.019c-4.818 1.679-6.547 5.02-6.701 5.334A8.5 8.5 0 0 0 12 20.555a8.5 8.5 0 0 0 3.336-.679m-9.682-2.152c.193-.331 2.538-4.213 6.943-5.637q.167-.054.337-.102a29 29 0 0 0-.692-1.45c-4.266 1.277-8.405 1.223-8.778 1.216a8.497 8.497 0 0 0 2.19 5.973m-2.015-7.46c.382.005 3.901.02 7.897-1.041a55 55 0 0 0-3.167-4.94 8.57 8.57 0 0 0-4.73 5.981m6.359-6.555a46 46 0 0 1 3.187 5c3.037-1.138 4.323-2.867 4.477-3.085a8.51 8.51 0 0 0-7.664-1.915m8.614 2.903c-.18.243-1.612 2.078-4.77 3.367a27 27 0 0 1 .751 1.678c2.842-.357 5.666.215 5.948.275a8.5 8.5 0 0 0-1.929-5.32"})))},{name:"dropbox",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 6.134 6.069 9.797 2 6.54l5.883-3.843zm-10 6.92 5.883 3.843L12 13.459 6.069 9.797zm10 .405 4.116 3.439L22 13.054l-4.069-3.257zM22 6.54l-5.884-3.843L12 6.134l5.931 3.663zm-9.989 7.66-4.129 3.426-1.767-1.153v1.291l5.896 3.539 5.897-3.539v-1.291l-1.769 1.153z"})))},{name:"eventbrite",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M18.041 3.931 5.959 3A2.96 2.96 0 0 0 3 5.959v12.083A2.96 2.96 0 0 0 5.959 21l12.083-.931C19.699 19.983 21 18.744 21 17.11V6.89c0-1.634-1.259-2.863-2.959-2.959M16.933 8.17c-.082.215-.192.432-.378.551-.188.122-.489.132-.799.132-1.521 0-3.062-.048-4.607-.048q-.23 1.061-.451 2.128c.932-.004 1.873.005 2.81.005.726 0 1.462-.069 1.586.525.04.189-.001.426-.052.615-.105.38-.258.676-.625.783-.185.054-.408.058-.646.058-1.145 0-2.345.017-3.493.02-.169.772-.328 1.553-.489 2.333 1.57-.005 3.067-.041 4.633-.058.627-.007 1.085.194 1.009.85a2.2 2.2 0 0 1-.211.725c-.102.208-.248.376-.488.452-.237.075-.541.064-.862.078-.304.014-.614.008-.924.016-.309.009-.619.022-.919.022-1.253 0-2.429.08-3.683.073-.603-.004-1.014-.249-1.124-.757-.059-.273-.018-.58.036-.841a3543 3543 0 0 1 1.629-7.763c.056-.265.114-.511.225-.714a1.24 1.24 0 0 1 .79-.62c.368-.099.883-.047 1.344-.047.305 0 .612.008.914.016.925.026 1.817.03 2.747.053.304.007.615.016.915.016.621 0 1.17.073 1.245.614.039.288-.051.567-.132.783"})))},{name:"facebook",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.5 2 2 6.5 2 12c0 5 3.7 9.1 8.4 9.9v-7H7.9V12h2.5V9.8c0-2.5 1.5-3.9 3.8-3.9 1.1 0 2.2.2 2.2.2v2.5h-1.3c-1.2 0-1.6.8-1.6 1.6V12h2.8l-.4 2.9h-2.3v7C18.3 21.1 22 17 22 12c0-5.5-4.5-10-10-10"})))},{name:"fediverse",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 743 743"},React.createElement("g",null,React.createElement("path",{d:"M181.131 275.137a68.9 68.9 0 0 1-29.465 29.328l161.758 162.389 38.998-19.764zm213.363 214.187-38.998 19.764 81.963 82.283a68.9 68.9 0 0 1 29.471-29.332zM581.646 339.391l-91.576 46.41 6.752 43.189 103.616-52.513a68.9 68.9 0 0 1-18.792-37.086m-144.738 73.351L220.383 522.477a68.9 68.9 0 0 1 18.795 37.089L443.66 455.934zM367.275 142.438l-104.48 203.97 30.848 30.967 110.623-215.957a68.9 68.9 0 0 1-36.991-18.98M235.621 399.459l-52.922 103.314a68.9 68.9 0 0 1 36.987 18.979l46.781-91.328zM150.768 304.918a68.9 68.9 0 0 1-34.416 7.195 69 69 0 0 1-6.651-.695l30.903 197.662a68.9 68.9 0 0 1 34.416-7.195 69 69 0 0 1 6.646.695zM239.342 560.545c.707 4.589.949 9.239.72 13.877a68.9 68.9 0 0 1-7.267 27.18l197.629 31.712c-.708-4.59-.95-9.24-.723-13.878a68.9 68.9 0 0 1 7.27-27.178zM601.133 377.199l-91.219 178.082a68.9 68.9 0 0 1 36.994 18.983l91.217-178.08a68.9 68.9 0 0 1-36.992-18.985M476.723 125.33a68.9 68.9 0 0 1-29.471 29.332l141.266 141.811a68.9 68.9 0 0 1 29.468-29.332zM347.787 104.631l-178.576 90.498a68.9 68.9 0 0 1 18.793 37.086l178.574-90.502a68.9 68.9 0 0 1-18.791-37.082M446.926 154.826a68.9 68.9 0 0 1-34.983 7.483 69 69 0 0 1-6.029-.633l15.818 101.291 43.163 6.926zm-16 167.028 37.4 239.482a68.9 68.9 0 0 1 33.914-6.943q3.625.206 7.207.791L474.09 328.777zM188.131 232.975c.734 4.66.988 9.383.758 14.095a68.9 68.9 0 0 1-7.16 26.983l101.369 16.281 19.923-38.908zm173.736 27.9-19.926 38.912 239.514 38.467a69 69 0 0 1-.695-13.719 68.9 68.9 0 0 1 7.349-27.324z"}),React.createElement("path",{fillOpacity:".996",d:"M412.284 156.054c34.538 1.882 64.061-24.592 65.943-59.13s-24.592-64.062-59.131-65.943c-34.538-1.882-64.061 24.592-65.943 59.13s24.593 64.062 59.131 65.943M646.144 390.82c34.538 1.881 64.062-24.593 65.943-59.131s-24.592-64.061-59.13-65.943-64.062 24.593-65.943 59.131 24.592 64.061 59.13 65.943M495.086 685.719c34.538 1.881 64.062-24.592 65.943-59.13s-24.592-64.062-59.13-65.943-64.062 24.592-65.943 59.13 24.592 64.062 59.13 65.943M167.866 633.211c34.538 1.882 64.062-24.592 65.943-59.13s-24.592-64.062-59.13-65.943-64.062 24.592-65.943 59.13 24.592 64.062 59.13 65.943M116.692 305.86c34.538 1.882 64.062-24.592 65.943-59.13s-24.592-64.062-59.131-65.943c-34.538-1.881-64.061 24.592-65.943 59.13s24.593 64.062 59.131 65.943"})))},{name:"feed",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M2 8.667V12c5.515 0 10 4.485 10 10h3.333c0-7.363-5.97-13.333-13.333-13.333M2 2v3.333c9.19 0 16.667 7.477 16.667 16.667H22C22 10.955 13.045 2 2 2m2.5 15a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5"})))},{name:"flickr",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M6.5 7c-2.75 0-5 2.25-5 5s2.25 5 5 5 5-2.25 5-5-2.25-5-5-5m11 0c-2.75 0-5 2.25-5 5s2.25 5 5 5 5-2.25 5-5-2.25-5-5-5"})))},{name:"foursquare",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M17.573 2H6.905C5.434 2 5 3.107 5 3.805v16.948c0 .785.422 1.077.66 1.172.238.097.892.177 1.285-.275 0 0 5.035-5.843 5.122-5.93.132-.132.132-.132.262-.132h3.26c1.368 0 1.588-.977 1.732-1.552.078-.318.692-3.428 1.225-6.122l.675-3.368C19.56 2.893 19.14 2 17.573 2m-1.078 5.22c-.053.252-.372.518-.665.518h-4.157c-.467 0-.802.318-.802.787v.508c0 .467.337.798.805.798h3.528c.331 0 .655.362.583.715s-.407 2.102-.448 2.295c-.04.193-.262.523-.655.523h-2.88c-.523 0-.683.068-1.033.503-.35.437-3.505 4.223-3.505 4.223-.032.035-.063.027-.063-.015V4.852c0-.298.26-.648.648-.648h8.562c.315 0 .61.297.528.683z"})))},{name:"ghost",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M10.203 20.997H3.005v-3.599h7.198zm10.792-3.599h-7.193v3.599h7.193zm.003-7.198H3v3.599h17.998zm-7.195-7.197H3.005v3.599h10.798zm7.197 0h-3.599v3.599H21z"})))},{name:"git",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M23.519 10.947 13.053.482a1.543 1.543 0 0 0-2.183 0L8.696 2.656l2.756 2.756a1.83 1.83 0 0 1 1.886.439 1.84 1.84 0 0 1 .436 1.898l2.656 2.657a1.83 1.83 0 0 1 1.899.436 1.837 1.837 0 0 1 0 2.597 1.84 1.84 0 0 1-2.599 0 1.84 1.84 0 0 1-.4-1.998l-2.478-2.477v6.521a1.837 1.837 0 0 1 .485 2.945 1.837 1.837 0 0 1-2.597 0 1.837 1.837 0 0 1 0-2.598 1.8 1.8 0 0 1 .602-.401V8.85a1.8 1.8 0 0 1-.602-.4 1.84 1.84 0 0 1-.395-2.009L7.628 3.723.452 10.898a1.544 1.544 0 0 0 0 2.184l10.467 10.467a1.544 1.544 0 0 0 2.183 0l10.417-10.418a1.546 1.546 0 0 0 0-2.184"})))},{name:"github",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12c0 4.419 2.865 8.166 6.839 9.489.5.09.682-.218.682-.484 0-.236-.009-.866-.014-1.699-2.782.602-3.369-1.34-3.369-1.34-.455-1.157-1.11-1.465-1.11-1.465-.909-.62.069-.608.069-.608 1.004.071 1.532 1.03 1.532 1.03.891 1.529 2.341 1.089 2.91.833.091-.647.349-1.086.635-1.337-2.22-.251-4.555-1.111-4.555-4.943 0-1.091.39-1.984 1.03-2.682-.103-.254-.447-1.27.097-2.646 0 0 .84-.269 2.75 1.025A9.6 9.6 0 0 1 12 6.836c.85.004 1.705.114 2.504.336 1.909-1.294 2.748-1.025 2.748-1.025.546 1.376.202 2.394.1 2.646.64.699 1.026 1.591 1.026 2.682 0 3.841-2.337 4.687-4.565 4.935.359.307.679.917.679 1.852 0 1.335-.012 2.415-.012 2.741 0 .269.18.579.688.481A10 10 0 0 0 22 12c0-5.523-4.477-10-10-10"})))},{name:"google-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m-.05 16c-3.312 0-6-2.688-6-6s2.688-6 6-6c1.62 0 2.976.594 4.014 1.566L14.26 9.222c-.432-.408-1.188-.888-2.31-.888-1.986 0-3.606 1.65-3.606 3.672s1.62 3.672 3.606 3.672c2.298 0 3.144-1.59 3.3-2.532h-3.306v-2.238h5.616c.084.378.15.732.15 1.23 0 3.426-2.298 5.862-5.76 5.862"})))},{name:"google-plus-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M8 11h6.61c.06.35.11.7.11 1.16 0 4-2.68 6.84-6.72 6.84-3.87 0-7-3.13-7-7s3.13-7 7-7c1.89 0 3.47.69 4.69 1.83l-1.9 1.83c-.52-.5-1.43-1.08-2.79-1.08-2.39 0-4.34 1.98-4.34 4.42S5.61 16.42 8 16.42c2.77 0 3.81-1.99 3.97-3.02H8zm15 0h-2V9h-2v2h-2v2h2v2h2v-2h2"})))},{name:"google-plus",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m-1.919 14.05a4.051 4.051 0 0 1 0-8.1c1.094 0 2.009.401 2.709 1.057l-1.15 1.118a2.23 2.23 0 0 0-1.559-.599c-1.341 0-2.434 1.114-2.434 2.479s1.094 2.479 2.434 2.479c1.551 0 2.122-1.073 2.227-1.709h-2.232v-1.511h3.791c.057.255.101.494.101.83.001 2.312-1.55 3.956-3.887 3.956M19 12.75h-1.25V14h-1.5v-1.25H15v-1.5h1.25V10h1.5v1.25H19z"})))},{name:"google",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12.02 10.18v3.73h5.51c-.26 1.57-1.67 4.22-5.5 4.22-3.31 0-6.01-2.75-6.01-6.12s2.7-6.12 6.01-6.12c1.87 0 3.13.8 3.85 1.48l2.84-2.76C16.99 2.99 14.73 2 12.03 2c-5.52 0-10 4.48-10 10s4.48 10 10 10c5.77 0 9.6-4.06 9.6-9.77 0-.83-.11-1.42-.25-2.05z"})))},{name:"instagram",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 4.622c2.403 0 2.688.009 3.637.052.877.04 1.354.187 1.671.31.42.163.72.358 1.035.673s.51.615.673 1.035c.123.317.27.794.31 1.671.043.949.052 1.234.052 3.637s-.009 2.688-.052 3.637c-.04.877-.187 1.354-.31 1.671-.163.42-.358.72-.673 1.035s-.615.51-1.035.673c-.317.123-.794.27-1.671.31-.949.043-1.233.052-3.637.052s-2.688-.009-3.637-.052c-.877-.04-1.354-.187-1.671-.31a2.8 2.8 0 0 1-1.035-.673 2.8 2.8 0 0 1-.673-1.035c-.123-.317-.27-.794-.31-1.671-.043-.949-.052-1.234-.052-3.637s.009-2.688.052-3.637c.04-.877.187-1.354.31-1.671.163-.42.358-.72.673-1.035s.615-.51 1.035-.673c.317-.123.794-.27 1.671-.31.949-.043 1.234-.052 3.637-.052M12 3c-2.444 0-2.751.01-3.711.054-.958.044-1.612.196-2.184.418a4.4 4.4 0 0 0-1.594 1.039c-.5.5-.808 1.002-1.038 1.594-.223.572-.375 1.226-.419 2.184C3.01 9.249 3 9.556 3 12s.01 2.751.054 3.711c.044.958.196 1.612.418 2.185.23.592.538 1.094 1.038 1.594s1.002.808 1.594 1.038c.572.222 1.227.375 2.185.418.96.044 1.267.054 3.711.054s2.751-.01 3.711-.054c.958-.044 1.612-.196 2.185-.418a4.4 4.4 0 0 0 1.594-1.038c.5-.5.808-1.002 1.038-1.594.222-.572.375-1.227.418-2.185.044-.96.054-1.267.054-3.711s-.01-2.751-.054-3.711c-.044-.958-.196-1.612-.418-2.185A4.4 4.4 0 0 0 19.49 4.51c-.5-.5-1.002-.808-1.594-1.038-.572-.222-1.227-.375-2.185-.418C14.751 3.01 14.444 3 12 3m0 4.378a4.622 4.622 0 1 0 0 9.244 4.622 4.622 0 0 0 0-9.244M12 15a3 3 0 1 1 0-6 3 3 0 0 1 0 6m4.804-8.884a1.08 1.08 0 1 0 .001 2.161 1.08 1.08 0 0 0-.001-2.161"})))},{name:"json-feed",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"m8.522 17.424.027.027c1.076-1.076 1.854-.993 3.154.306l2.053 2.053c2.136 2.136 4.131 2.028 6.515-.356l.729-.728-1.548-1.548-.373.373c-1.349 1.349-2.293 1.366-3.585.075l-2.409-2.409c-1.242-1.242-2.475-1.366-3.659-.381l-.232-.232c1.01-1.225.911-2.368-.29-3.568l-2.16-2.162c-1.317-1.317-1.308-2.236.058-3.602l.372-.372-1.54-1.54-.728.729c-2.393 2.393-2.525 4.346-.439 6.433l1.78 1.78c1.3 1.3 1.383 2.095.315 3.163l.008.008a1.384 1.384 0 0 0 1.952 1.951"}),React.createElement("circle",{cx:"13.089",cy:"10.905",r:"1.383"}),React.createElement("circle",{cx:"16.349",cy:"7.644",r:"1.383"}),React.createElement("circle",{cx:"19.61",cy:"4.383",r:"1.383"})))},{name:"line",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M14.255 9.572v3.333c0 .084-.066.15-.15.15h-.534a.16.16 0 0 1-.122-.061l-1.528-2.063v1.978c0 .084-.066.15-.15.15h-.534a.15.15 0 0 1-.15-.15V9.576c0-.084.066-.15.15-.15h.529a.14.14 0 0 1 .122.066l1.528 2.063V9.577c0-.084.066-.15.15-.15h.534a.15.15 0 0 1 .155.145m-3.844-.15h-.534a.15.15 0 0 0-.15.15v3.333c0 .084.066.15.15.15h.534c.084 0 .15-.066.15-.15V9.572c0-.08-.066-.15-.15-.15m-1.289 2.794H7.664V9.572a.15.15 0 0 0-.15-.15H6.98a.15.15 0 0 0-.15.15v3.333q0 .062.042.103a.16.16 0 0 0 .103.042h2.142c.084 0 .15-.066.15-.15v-.534a.15.15 0 0 0-.145-.15m7.945-2.794h-2.142c-.08 0-.15.066-.15.15v3.333c0 .08.066.15.15.15h2.142c.084 0 .15-.066.15-.15v-.534a.15.15 0 0 0-.15-.15h-1.458v-.563h1.458c.084 0 .15-.066.15-.15v-.539a.15.15 0 0 0-.15-.15h-1.458v-.563h1.458c.084 0 .15-.066.15-.15v-.534c-.005-.08-.07-.15-.15-.15M22.5 5.33v13.373c-.005 2.1-1.725 3.802-3.83 3.797H5.297c-2.1-.005-3.802-1.73-3.797-3.83V5.297c.005-2.1 1.73-3.802 3.83-3.797h13.373c2.1.005 3.802 1.725 3.797 3.83m-2.888 5.747c0-3.422-3.431-6.206-7.645-6.206s-7.645 2.784-7.645 6.206c0 3.066 2.719 5.634 6.394 6.122.895.192.792.52.591 1.725-.033.192-.155.755.661.413s4.402-2.592 6.009-4.439c1.106-1.219 1.636-2.452 1.636-3.82"})))},{name:"link",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M17 13H7v-2h10zm1-6h-1c-1.631 0-3.065.792-3.977 2H18c1.103 0 2 .897 2 2v2c0 1.103-.897 2-2 2h-4.977c.913 1.208 2.347 2 3.977 2h1a4 4 0 0 0 4-4v-2a4 4 0 0 0-4-4M2 11v2a4 4 0 0 0 4 4h1c1.63 0 3.065-.792 3.977-2H6c-1.103 0-2-.897-2-2v-2c0-1.103.897-2 2-2h4.977C10.065 7.792 8.631 7 7 7H6a4 4 0 0 0-4 4"})))},{name:"linkedin",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19.7 3H4.3A1.3 1.3 0 0 0 3 4.3v15.4A1.3 1.3 0 0 0 4.3 21h15.4a1.3 1.3 0 0 0 1.3-1.3V4.3A1.3 1.3 0 0 0 19.7 3M8.339 18.338H5.667v-8.59h2.672zM7.004 8.574a1.548 1.548 0 1 1-.002-3.096 1.548 1.548 0 0 1 .002 3.096m11.335 9.764H15.67v-4.177c0-.996-.017-2.278-1.387-2.278-1.389 0-1.601 1.086-1.601 2.206v4.249h-2.667v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.779 3.203 4.092v4.711z"})))},{name:"mail",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M20 4H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2m0 4.236-8 4.882-8-4.882V6h16z"})))},{name:"mastodon",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M11.973 2.352c-2.468.02-4.842.286-6.225.921 0 0-2.742 1.229-2.742 5.415 0 .958-.018 2.105.012 3.32.1 4.094.75 8.128 4.535 9.129 1.745.462 3.244.56 4.45.494 2.19-.122 3.417-.781 3.417-.781l-.072-1.588s-1.565.491-3.32.431c-1.74-.06-3.576-.188-3.858-2.324a4 4 0 0 1-.04-.598s1.709.416 3.874.516c1.324.06 2.563-.076 3.824-.226 2.418-.29 4.524-1.78 4.79-3.141.416-2.144.38-5.232.38-5.232 0-4.186-2.74-5.415-2.74-5.415-1.383-.635-3.76-.9-6.227-.921zM9.18 5.622c1.028 0 1.804.395 2.318 1.185l.502.84.5-.84c.514-.79 1.292-1.186 2.32-1.186.888 0 1.605.313 2.15.922q.795.915.794 2.469v5.068h-2.008V9.16c0-1.037-.438-1.562-1.31-1.562-.966 0-1.448.622-1.448 1.857v2.693h-1.996V9.455c0-1.235-.484-1.857-1.45-1.857-.872 0-1.308.525-1.308 1.562v4.92H6.236V9.012q-.001-1.554.793-2.469c.547-.609 1.263-.922 2.15-.922"})))},{name:"medium-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{fillRule:"nonzero",d:"M7.423 6c3.27 0 5.922 2.686 5.922 6s-2.651 6-5.922 6S1.5 15.313 1.5 12s2.652-6 5.923-6m9.458.351c1.635 0 2.961 2.53 2.961 5.65 0 3.118-1.325 5.648-2.96 5.648S13.92 15.119 13.92 12s1.325-5.649 2.96-5.649m4.577.589c.576 0 1.042 2.265 1.042 5.06s-.466 5.06-1.042 5.06c-.575 0-1.04-2.265-1.04-5.06s.465-5.06 1.04-5.06"})))},{name:"medium",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M3 3v18h18V3zm15 4.26-1 .93a.28.28 0 0 0-.11.27v6.8a.27.27 0 0 0 .11.27l.94.93v.2h-4.75v-.2l1-1c.09-.1.09-.12.09-.27V9.74l-2.71 6.9h-.37L8 9.74v4.62a.67.67 0 0 0 .17.54l1.27 1.54v.2H5.86v-.2l1.27-1.54a.64.64 0 0 0 .17-.54V9a.5.5 0 0 0-.16-.4L6 7.26v-.2h3.52L12.23 13l2.38-5.94H18z"})))},{name:"messenger",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12.026.375C5.462.375.375 5.172.375 11.652c0 3.389 1.393 6.318 3.66 8.341.391.352.311.556.377 2.73a.934.934 0 0 0 1.307.823c2.48-1.092 2.512-1.178 2.933-1.064 7.185 1.977 14.973-2.621 14.973-10.83 0-6.48-5.035-11.277-11.599-11.277m6.996 8.678L15.6 14.47a1.75 1.75 0 0 1-2.527.465l-2.723-2.038a.7.7 0 0 0-.844 0l-3.674 2.786c-.49.372-1.133-.216-.802-.735l3.422-5.417a1.75 1.75 0 0 1 2.527-.465l2.722 2.037a.7.7 0 0 0 .844 0L18.22 8.32c.489-.374 1.132.213.801.732"})))},{name:"microblog",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19.641 17.086c1.294-1.522 2.067-3.438 2.067-5.521 0-4.957-4.371-8.972-9.763-8.972s-9.763 4.015-9.763 8.972 4.371 8.972 9.763 8.972a10.5 10.5 0 0 0 3.486-.59.315.315 0 0 1 .356.112c.816 1.101 2.09 1.876 3.506 2.191a.194.194 0 0 0 .192-.309 3.82 3.82 0 0 1 .162-4.858zm-3.065-6.575-2.514 1.909.912 3.022a.286.286 0 0 1-.437.317l-2.592-1.802-2.592 1.802a.285.285 0 0 1-.436-.317l.912-3.022-2.515-1.909a.285.285 0 0 1 .167-.513l3.155-.066 1.038-2.981a.285.285 0 0 1 .539 0l1.038 2.981 3.155.066a.285.285 0 0 1 .17.513"})))},{name:"nextdoor",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",strokeMiterlimit:"10",viewBox:"0 0 130 130"},React.createElement("g",null,React.createElement("path",{d:"M64.25 3.531c-31.144.337-57.596 24.22-60.469 55.907-3.064 33.799 21.857 63.685 55.657 66.75s63.685-21.857 66.75-55.657-21.857-63.686-55.657-66.75a62 62 0 0 0-6.281-.25m3.938 34.907C82.468 38.438 93.5 48.58 93.5 61.5v27c0 .685-.565 1.25-1.25 1.25H80.906a1.267 1.267 0 0 1-1.25-1.25V63.375c0-5.58-4.309-11.937-11.469-11.937-7.47 0-11.468 6.357-11.468 11.937V88.5c0 .685-.565 1.25-1.25 1.25H44.125c-.68 0-1.219-.57-1.219-1.25V64.156c0-.74-.529-1.364-1.25-1.531-13.13-2.93-15.115-10.285-15.375-21.125-.005-.332.142-.67.375-.906.233-.237.543-.375.875-.375l11.688.062c.66.01 1.187.529 1.218 1.188.13 4.44.438 9.406 4.438 9.406.83 0 1.443-1.179 1.813-1.719 4.41-6.48 12.28-10.718 21.5-10.718"})))},{name:"patreon",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M20 7.408c-.003-2.299-1.746-4.182-3.79-4.862-2.54-.844-5.888-.722-8.312.453-2.939 1.425-3.862 4.545-3.896 7.656-.028 2.559.22 9.297 3.92 9.345 2.75.036 3.159-3.603 4.43-5.356.906-1.247 2.071-1.599 3.506-1.963 2.465-.627 4.146-2.626 4.142-5.273"})))},{name:"pinterest-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12.289 2C6.617 2 3.606 5.648 3.606 9.622c0 1.846 1.025 4.146 2.666 4.878.25.111.381.063.439-.169.044-.175.267-1.029.365-1.428a.37.37 0 0 0-.091-.362c-.54-.63-.975-1.791-.975-2.873 0-2.777 2.194-5.464 5.933-5.464 3.23 0 5.49 2.108 5.49 5.122 0 3.407-1.794 5.768-4.13 5.768-1.291 0-2.257-1.021-1.948-2.277.372-1.495 1.089-3.112 1.089-4.191 0-.967-.542-1.775-1.663-1.775-1.319 0-2.379 1.309-2.379 3.059 0 1.115.394 1.869.394 1.869s-1.302 5.279-1.54 6.261c-.405 1.666.053 4.368.094 4.604.021.126.167.169.25.063.129-.165 1.699-2.419 2.142-4.051.158-.59.817-2.995.817-2.995.43.784 1.681 1.446 3.013 1.446 3.963 0 6.822-3.494 6.822-7.833C20.394 5.112 16.849 2 12.289 2"})))},{name:"pinterest",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12c0 4.236 2.636 7.855 6.356 9.312-.087-.791-.166-2.005.035-2.869.182-.78 1.173-4.971 1.173-4.971s-.299-.599-.299-1.484c0-1.39.806-2.429 1.809-2.429.853 0 1.265.641 1.265 1.409 0 .858-.546 2.141-.828 3.329-.236.996.499 1.807 1.481 1.807 1.777 0 3.144-1.874 3.144-4.579 0-2.394-1.72-4.068-4.177-4.068-2.845 0-4.515 2.134-4.515 4.34 0 .859.331 1.781.744 2.282a.3.3 0 0 1 .069.287c-.077.316-.246.995-.279 1.134-.044.183-.145.222-.334.134-1.249-.581-2.03-2.407-2.03-3.874 0-3.154 2.292-6.051 6.607-6.051 3.469 0 6.165 2.472 6.165 5.775 0 3.446-2.173 6.22-5.189 6.22-1.013 0-1.966-.526-2.292-1.148l-.623 2.377c-.226.869-.835 1.957-1.243 2.622.936.289 1.93.445 2.961.445 5.523 0 10-4.477 10-10S17.523 2 12 2"})))},{name:"pocket",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M21.927 4.194A1.82 1.82 0 0 0 20.222 3H3.839a1.823 1.823 0 0 0-1.813 1.814v6.035l.069 1.2c.29 2.73 1.707 5.115 3.899 6.778l.119.089.025.018a9.9 9.9 0 0 0 3.91 1.727 10.06 10.06 0 0 0 4.049-.014.3.3 0 0 0 .064-.023 9.9 9.9 0 0 0 3.753-1.691l.025-.018q.06-.043.119-.089c2.192-1.664 3.609-4.049 3.898-6.778l.069-1.2V4.814a1.8 1.8 0 0 0-.098-.62m-4.235 6.287-4.704 4.512a1.37 1.37 0 0 1-1.898 0l-4.705-4.512a1.371 1.371 0 1 1 1.898-1.979l3.756 3.601 3.755-3.601a1.372 1.372 0 0 1 1.898 1.979"})))},{name:"polldaddy",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.487 2 2 6.487 2 12c0 5.514 4.487 10 10 10 5.514 0 10-4.486 10-10 0-5.513-4.486-10-10-10m.991 1.68c2.361.084 4.657 1.251 6.197 3.136.283.334.541.693.774 1.067a7.78 7.78 0 0 0-6.094-2.94 7.76 7.76 0 0 0-5.896 2.703q-.008.006-.016.014l-.152.159-.031.032a6.12 6.12 0 0 0-1.633 4.165 6.15 6.15 0 0 0 6.143 6.143c.57 0 1.123-.081 1.649-.227-1.849.839-4.131.747-5.926-.324-1.841-1.089-3.171-3.111-3.433-5.313A7.39 7.39 0 0 1 6.69 6.137C8.294 4.5 10.634 3.563 12.991 3.68m3.373 8.519c-.049-2.024-1.587-3.889-3.544-4.174-1.927-.343-3.917.857-4.451 2.661a3.67 3.67 0 0 0 .2 2.653c.39.8 1.067 1.451 1.894 1.759 1.664.654 3.63-.27 4.173-1.863.593-1.58-.396-3.423-1.94-3.776-1.52-.407-3.161.757-3.204 2.243a2.36 2.36 0 0 0 .753 1.879c.501.476 1.23.667 1.871.529a2.07 2.07 0 0 0 1.469-1.134 1.91 1.91 0 0 0-.087-1.767c-.297-.513-.859-.863-1.429-.881a1.7 1.7 0 0 0-1.437.679 1.53 1.53 0 0 0-.18 1.489q.006.016.016.03c.193.634.774 1.1 1.467 1.117a1.6 1.6 0 0 1-.97-.183c-.466-.244-.809-.747-.893-1.29a1.8 1.8 0 0 1 .499-1.539 2.02 2.02 0 0 1 1.58-.606c.593.04 1.159.35 1.517.859.364.496.51 1.156.383 1.773-.116.62-.529 1.174-1.093 1.514a2.52 2.52 0 0 1-1.914.286c-.65-.161-1.226-.606-1.584-1.206a2.83 2.83 0 0 1-.341-2.031c.143-.7.573-1.321 1.176-1.753 1.193-.883 3.056-.751 4.106.411 1.106 1.1 1.327 3.027.406 4.371-.877 1.376-2.74 2.086-4.374 1.594-1.639-.449-2.913-2.079-3.031-3.853-.07-.884.13-1.797.583-2.577.445-.777 1.155-1.432 1.972-1.862 1.64-.88 3.816-.743 5.349.424 1.251.924 2.083 2.42 2.236 4.009l.001.03c0 2.9-2.359 5.26-5.26 5.26a5.2 5.2 0 0 1-1.947-.376 5 5 0 0 0 2.613-.079 4.96 4.96 0 0 0 2.514-1.751c.618-.828.95-1.861.901-2.869M12 21.113c-5.024 0-9.111-4.087-9.111-9.113 0-4.789 3.713-8.723 8.411-9.081a7 7 0 0 0-.397.06c-2.644.453-5.017 2.106-6.32 4.409-1.309 2.301-1.391 5.19-.3 7.527 1.056 2.34 3.253 4.156 5.776 4.553 2.497.44 5.133-.483 6.787-2.301 1.719-1.797 2.269-4.529 1.486-6.796-.583-1.81-1.976-3.331-3.7-4.046 3.417.594 6.174 3.221 6.174 6.781 0 1.004-.241 2.02-.657 2.966-1.498 2.984-4.586 5.041-8.149 5.041"})))},{name:"print",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M9 16h6v2H9zm13 1h-3v3a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-3H2V9a2 2 0 0 1 2-2h1V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v2h1a2 2 0 0 1 2 2zM7 7h10V5H7zm10 7H7v6h10zm3-3.5a1.5 1.5 0 1 0-3.001.001A1.5 1.5 0 0 0 20 10.5"})))},{name:"quora",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M47.736 16.521c-.41-.81-.898-1.631-1.846-1.631a1 1 0 0 0-.527.107l-.322-.644a2.93 2.93 0 0 1 1.836-.595c1.26 0 1.914.605 2.431 1.397a6.8 6.8 0 0 0 .449-2.675c0-2.773-.869-4.199-2.929-4.199-1.992 0-2.851 1.465-2.851 4.199s.859 4.17 2.851 4.17a4 4 0 0 0 .869-.107zm.498.966a6 6 0 0 1-1.367.185 5.27 5.27 0 0 1-5.263-5.204c0-3.114 2.558-5.233 5.263-5.233s5.282 2.109 5.282 5.233a5.08 5.08 0 0 1-1.992 4.072c.381.566.781.956 1.319.956.595 0 .839-.459.878-.82h.781c.049.488-.195 2.48-2.373 2.48-1.319 0-2.012-.761-2.529-1.66zm5.624-2.646v-3.563c0-.371-.146-.586-.615-.586h-.498v-.956h3.251v5.048c0 .849.459 1.231 1.161 1.231a1.56 1.56 0 0 0 1.465-.839V11.28c0-.371-.146-.586-.615-.586h-.527v-.957h3.28v5.302c0 .527.195.732.8.732h.107v.976l-2.929.468V16.21h-.057a3.12 3.12 0 0 1-2.509 1.152c-1.28 0-2.304-.644-2.304-2.558zm12.059 1.611c1.152 0 1.592-1.005 1.611-3.027.02-1.982-.459-2.929-1.611-2.929-1.005 0-1.641.956-1.641 2.929 0 2.021.625 3.027 1.641 3.027m0 .956a3.906 3.906 0 0 1-3.974-3.974c0-2.334 1.836-3.886 3.974-3.886 2.226 0 4.004 1.582 4.004 3.886a3.867 3.867 0 0 1-4.004 3.974m4.072-.146v-.956h.312c.781 0 .859-.224.859-.908v-4.121c0-.371-.215-.586-.732-.586h-.42v-.955h2.968l.146 1.553h.108c.371-1.113 1.221-1.699 2.051-1.699.693 0 1.221.39 1.221 1.181 0 .547-.264 1.093-1.005 1.093-.664 0-.8-.449-1.358-.449-.488 0-.869.468-.869 1.152v2.783c0 .673.166.908.937.908h.439v.956h-4.658zm9.901-1.093c.956 0 1.338-.898 1.338-1.797v-1.211c-.732.722-2.304.742-2.304 2.021 0 .625.371.986.966.986m1.387 0c-.39.752-1.191 1.26-2.314 1.26-1.309 0-2.148-.732-2.148-1.914 0-2.451 3.417-1.797 4.423-3.427v-.185c0-1.25-.488-1.445-1.035-1.445-1.524 0-.83 1.631-2.226 1.631-.673 0-.937-.371-.937-.859 0-.927 1.093-1.67 3.173-1.67 1.963 0 3.163.537 3.163 2.49v3.114q-.02.742.595.742a1 1 0 0 0 .449-.127l.254.615c-.205.312-.752.869-1.836.869-.908 0-1.465-.42-1.543-1.113h-.01zm-68.554 2.558c-.83-1.641-1.807-3.3-3.711-3.3a2.9 2.9 0 0 0-1.093.215l-.644-1.299a5.66 5.66 0 0 1 3.662-1.211c2.548 0 3.857 1.231 4.892 2.792q.917-2.012.908-5.38c0-5.585-1.748-8.417-5.829-8.417-4.033 0-5.76 2.87-5.76 8.417s1.738 8.397 5.76 8.397a5.9 5.9 0 0 0 1.748-.224zm.996 1.953a9.8 9.8 0 0 1-2.744.371C5.614 21.041.371 16.764.371 10.545.371 4.277 5.614 0 10.965 0c5.448 0 10.642 4.248 10.642 10.545a10.25 10.25 0 0 1-4.013 8.201c.732 1.152 1.563 1.914 2.665 1.914 1.201 0 1.689-.927 1.768-1.66h1.572c.088.966-.4 4.999-4.775 4.999-2.646 0-4.052-1.543-5.106-3.339z"})))},{name:"reddit",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M22 11.816a2.28 2.28 0 0 0-2.277-2.277c-.593 0-1.122.24-1.526.614-1.481-.965-3.455-1.594-5.647-1.69l1.171-3.702 3.18.748a1.88 1.88 0 0 0 1.876 1.862 1.88 1.88 0 0 0 1.877-1.878 1.88 1.88 0 0 0-1.877-1.877c-.769 0-1.431.466-1.72 1.13l-3.508-.826a.386.386 0 0 0-.46.261l-1.35 4.268c-2.316.038-4.411.67-5.97 1.671a2.24 2.24 0 0 0-1.492-.581A2.28 2.28 0 0 0 2 11.816c0 .814.433 1.523 1.078 1.925a4 4 0 0 0-.061.672c0 3.292 4.011 5.97 8.941 5.97s8.941-2.678 8.941-5.97q-.002-.32-.053-.632A2.26 2.26 0 0 0 22 11.816m-3.224-7.422a1.1 1.1 0 1 1-.001 2.199 1.1 1.1 0 0 1 .001-2.199M2.777 11.816c0-.827.672-1.5 1.499-1.5.313 0 .598.103.838.269-.851.676-1.477 1.479-1.812 2.36a1.48 1.48 0 0 1-.525-1.129m9.182 7.79c-4.501 0-8.164-2.329-8.164-5.193S7.457 9.22 11.959 9.22s8.164 2.329 8.164 5.193-3.663 5.193-8.164 5.193m8.677-6.605c-.326-.89-.948-1.701-1.797-2.384.248-.186.55-.301.883-.301.827 0 1.5.673 1.5 1.5.001.483-.23.911-.586 1.185m-11.64 1.703c-.76 0-1.397-.616-1.397-1.376s.637-1.397 1.397-1.397 1.376.637 1.376 1.397-.616 1.376-1.376 1.376m7.405-1.376c0 .76-.616 1.376-1.376 1.376s-1.399-.616-1.399-1.376.639-1.397 1.399-1.397 1.376.637 1.376 1.397m-1.172 3.38a.39.39 0 0 1 0 .55c-.674.674-1.727 1.002-3.219 1.002l-.011-.002-.011.002c-1.492 0-2.544-.328-3.218-1.002a.389.389 0 1 1 .55-.55c.521.521 1.394.775 2.669.775l.011.002.011-.002c1.275 0 2.148-.253 2.669-.775a.387.387 0 0 1 .549 0"})))},{name:"share",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M18 16c-.788 0-1.499.31-2.034.807L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.048 4.118A3 3 0 0 0 15 19a3 3 0 1 0 3-3"})))},{name:"skype",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"m10.113 2.699.1-.02q.05.025.098.051zM2.72 10.223l-.017.103q.025.048.051.095zm18.555 3.548q.009-.053.018-.106-.025-.047-.052-.095zm-7.712 7.428q.049.027.096.053l.105-.017zM22 16.386a5.55 5.55 0 0 1-1.637 3.953 5.55 5.55 0 0 1-3.953 1.637 5.6 5.6 0 0 1-2.75-.725l.105-.017-.202-.035q.049.027.096.053a9.5 9.5 0 0 1-1.654.147 9.4 9.4 0 0 1-3.676-.743 9.4 9.4 0 0 1-3.002-2.023 9.4 9.4 0 0 1-2.023-3.002 9.4 9.4 0 0 1-.743-3.676c0-.546.049-1.093.142-1.628q.025.048.051.095l-.034-.199-.017.103A5.6 5.6 0 0 1 2 7.615c0-1.493.582-2.898 1.637-3.953A5.56 5.56 0 0 1 7.59 2.024c.915 0 1.818.228 2.622.655l-.1.02.199.031q-.049-.026-.098-.051l.004-.001a9.5 9.5 0 0 1 1.788-.169 9.41 9.41 0 0 1 6.678 2.766 9.4 9.4 0 0 1 2.024 3.002 9.4 9.4 0 0 1 .743 3.676c0 .575-.054 1.15-.157 1.712q-.025-.047-.052-.095l.034.201q.009-.053.018-.106c.461.829.707 1.767.707 2.721m-5.183-2.248c0-1.331-.613-2.743-3.033-3.282l-2.209-.49c-.84-.192-1.807-.444-1.807-1.237s.679-1.348 1.903-1.348c2.468 0 2.243 1.696 3.468 1.696.645 0 1.209-.379 1.209-1.031 0-1.521-2.435-2.663-4.5-2.663-2.242 0-4.63.952-4.63 3.488 0 1.221.436 2.521 2.839 3.123l2.984.745c.903.223 1.129.731 1.129 1.189 0 .762-.758 1.507-2.129 1.507-2.679 0-2.307-2.062-3.743-2.062-.645 0-1.113.444-1.113 1.078 0 1.236 1.501 2.886 4.856 2.886 3.195 0 4.776-1.538 4.776-3.599"})))},{name:"sms",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M17.696 4C20.069 4 22 5.973 22 8.398v4.357c0 2.04-1.368 3.783-3.261 4.266v4.427l-5.234-4.295h-7.2C3.93 17.153 2 15.18 2 12.755V8.398C2 5.973 3.931 4 6.304 4zM7.028 8.515c-.98 0-1.66.562-1.66 1.349-.009.497.322.91.985 1.178l.39.142c.242.097.305.171.305.297 0 .162-.131.251-.442.251s-.76-.135-1.004-.284l-.112.046-.215.868c.359.258.832.364 1.33.364 1.104 0 1.764-.523 1.764-1.333-.008-.574-.305-.956-.954-1.216l-.393-.146c-.266-.108-.341-.181-.341-.287 0-.152.131-.243.387-.243.274 0 .587.093.808.214l.109-.047.214-.837c-.315-.224-.741-.316-1.171-.316m10.302 0c-.98 0-1.66.562-1.66 1.349-.008.497.322.91.985 1.178l.39.142c.243.097.305.171.305.297 0 .162-.13.251-.442.251-.311 0-.76-.135-1.004-.284l-.112.046-.215.868c.359.258.832.364 1.33.364 1.104 0 1.764-.523 1.764-1.333-.008-.574-.305-.956-.954-1.216l-.393-.146c-.266-.108-.341-.181-.341-.287 0-.152.131-.243.387-.243.274 0 .587.093.808.214l.109-.047.214-.837c-.316-.224-.741-.316-1.171-.316m-3.733 0c-.297 0-.55.066-.78.202l-.144.098a2 2 0 0 0-.264.247l-.078.095-.027-.077c-.15-.34-.55-.565-1.033-.565l-.169.007a1.36 1.36 0 0 0-.896.42l-.08.09-.038-.363-.075-.067H8.994l-.075.079.024.634c.005.2.008.397.008.604v2.652l.075.075h1.178l.075-.075v-2.269q-.002-.168.042-.274c.083-.23.262-.392.496-.392.314 0 .483.267.483.753v2.182l.075.075h1.179l.075-.075v-2.277c0-.097.016-.213.043-.285.077-.224.26-.373.486-.373.33 0 .5.272.5.817v2.118l.074.075h1.179l.075-.075v-2.293c0-1.131-.537-1.763-1.39-1.763Z"})))},{name:"snapchat",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M11.989 1.728c3.221.001 5.904 2.683 5.908 5.912q.002 1.133.067 2.094a.737.737 0 0 0 .902.669l1.009-.237a.6.6 0 0 1 .129-.015c.256 0 .492.175.55.434a.74.74 0 0 1-.479.861l-1.532.618a.823.823 0 0 0-.485.98c1.229 4.543 4.661 4.071 4.661 4.662 0 .743-2.587.848-2.821 1.082s-.01 1.368-.532 1.588a1.1 1.1 0 0 1-.409.056c-.393 0-.95-.077-1.536-.077-.509 0-1.04.058-1.507.273-1.239.572-2.433 1.641-3.914 1.641S9.325 21.2 8.086 20.628c-.467-.216-.998-.273-1.507-.273-.586 0-1.143.077-1.536.077-.17 0-.31-.014-.409-.056-.522-.22-.299-1.354-.532-1.588s-2.821-.337-2.821-1.08c0-.592 3.432-.119 4.661-4.662a.824.824 0 0 0-.486-.98l-1.532-.618a.74.74 0 0 1-.479-.861.56.56 0 0 1 .679-.419l1.009.237q.086.02.169.02a.737.737 0 0 0 .733-.689q.065-.961.067-2.094c.004-3.229 2.666-5.91 5.887-5.912m0-1.281c-.961 0-1.898.194-2.784.574A7.2 7.2 0 0 0 6.93 2.572a7.2 7.2 0 0 0-1.539 2.282A7.1 7.1 0 0 0 4.82 7.64a33 33 0 0 1-.029 1.369l-.375-.088a2 2 0 0 0-.421-.049 1.86 1.86 0 0 0-1.135.389 1.84 1.84 0 0 0-.666 1.049 2.024 2.024 0 0 0 1.271 2.335l1.124.454c-.744 2.285-2.117 2.723-3.041 3.018a5 5 0 0 0-.659.246C.087 16.76 0 17.436 0 17.708c0 .521.247.996.694 1.339.223.17.499.311.844.43.47.162 1.016.265 1.459.347.021.164.053.341.106.518.22.738.684 1.069 1.034 1.217.332.14.676.156.905.156.224 0 .462-.018.713-.036.269-.02.548-.041.823-.041.426 0 .743.051.97.155.311.144.64.337.989.542.972.571 2.073 1.217 3.462 1.217s2.49-.647 3.462-1.217c.349-.205.679-.399.989-.542.226-.105.544-.155.97-.155.275 0 .554.021.823.041.251.019.488.036.713.036.229 0 .573-.016.905-.156.35-.147.814-.478 1.034-1.217.053-.178.084-.354.106-.518.443-.082.989-.185 1.459-.347.345-.119.621-.259.844-.43.448-.342.694-.818.694-1.339 0-.272-.087-.948-.891-1.347a5 5 0 0 0-.659-.246c-.924-.295-2.297-.733-3.041-3.018l1.124-.454a2.025 2.025 0 0 0 1.271-2.335 1.83 1.83 0 0 0-.666-1.049 1.86 1.86 0 0 0-1.556-.34l-.375.088a33 33 0 0 1-.029-1.369 7.1 7.1 0 0 0-.575-2.789c-.365-.853-.886-1.62-1.547-2.282s-1.428-1.182-2.28-1.547a7.1 7.1 0 0 0-2.786-.574"})))},{name:"soundcloud",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M23.587 13.923a3.303 3.303 0 0 1-3.344 3.117h-8.037a.674.674 0 0 1-.667-.67V7.717a.74.74 0 0 1 .444-.705s.739-.512 2.296-.512a5.27 5.27 0 0 1 2.702.742 5.35 5.35 0 0 1 2.516 3.485 3.1 3.1 0 0 1 .852-.116 3.217 3.217 0 0 1 3.237 3.312m-13.05-5.659c.242 2.935.419 5.612 0 8.538a.261.261 0 0 1-.519 0c-.39-2.901-.221-5.628 0-8.538a.26.26 0 0 1 .398-.25.26.26 0 0 1 .12.25zm-1.627 8.541a.273.273 0 0 1-.541 0 32.7 32.7 0 0 1 0-7.533.274.274 0 0 1 .544 0 29.4 29.4 0 0 1-.003 7.533m-1.63-7.788c.264 2.69.384 5.099-.003 7.782a.262.262 0 0 1-.522 0c-.374-2.649-.249-5.127 0-7.782a.264.264 0 0 1 .525 0m-1.631 7.792a.268.268 0 0 1-.532 0 27.6 27.6 0 0 1 0-7.034.27.27 0 1 1 .541 0 25.8 25.8 0 0 1-.01 7.034zm-1.63-5.276c.412 1.824.227 3.435-.015 5.294a.255.255 0 0 1-.504 0c-.22-1.834-.402-3.482-.015-5.295a.268.268 0 0 1 .535 0m-1.626-.277c.378 1.869.254 3.451-.01 5.325-.031.277-.506.28-.531 0-.239-1.846-.352-3.476-.01-5.325a.277.277 0 0 1 .551 0m-1.643.907c.396 1.239.261 2.246-.016 3.517a.258.258 0 0 1-.514 0c-.239-1.246-.336-2.274-.021-3.517a.276.276 0 0 1 .55 0z"})))},{name:"spotify",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m4.586 14.424a.62.62 0 0 1-.857.207c-2.348-1.435-5.304-1.76-8.785-.964a.622.622 0 1 1-.277-1.215c3.809-.871 7.077-.496 9.713 1.115a.623.623 0 0 1 .206.857M17.81 13.7a.78.78 0 0 1-1.072.257c-2.687-1.652-6.785-2.131-9.965-1.166A.779.779 0 1 1 6.32 11.3c3.632-1.102 8.147-.568 11.234 1.328a.78.78 0 0 1 .256 1.072m.105-2.835c-3.223-1.914-8.54-2.09-11.618-1.156a.935.935 0 1 1-.542-1.79c3.532-1.072 9.404-.865 13.115 1.338a.936.936 0 1 1-.955 1.608"})))},{name:"squarespace",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M20.87 9.271a3.86 3.86 0 0 0-5.458 0l-6.141 6.141a.964.964 0 1 0 1.365 1.364l6.14-6.14a1.929 1.929 0 1 1 2.729 2.729l-6.022 6.022a1.93 1.93 0 0 0 2.729 0l4.658-4.658a3.86 3.86 0 0 0 0-5.458m-2.047 2.047a.965.965 0 0 0-1.365 0l-6.14 6.14a1.93 1.93 0 0 1-2.729 0 .964.964 0 1 0-1.364 1.364 3.86 3.86 0 0 0 5.458 0l6.14-6.14a.966.966 0 0 0 0-1.364m-2.047-6.141a3.86 3.86 0 0 0-5.458 0l-6.14 6.14a.964.964 0 1 0 1.364 1.364l6.141-6.14a1.93 1.93 0 0 1 2.729 0 .965.965 0 1 0 1.364-1.364m-2.047 2.047a.964.964 0 0 0-1.364 0l-6.14 6.141a1.929 1.929 0 1 1-2.729-2.729l6.022-6.022a1.93 1.93 0 0 0-2.729 0L3.13 9.271a3.86 3.86 0 0 0 5.458 5.458l6.14-6.141a.963.963 0 0 0 .001-1.364"})))},{name:"stackexchange",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M4 11.606h16v3.272H4zM4 7.377h16v3.272H4zM17.514 3H6.55C5.147 3 4 4.169 4 5.614v.848h16v-.85C20 4.167 18.895 3 17.514 3M4 15.813v.85c0 1.445 1.147 2.614 2.55 2.614h6.799v3.463l3.357-3.463h.744c1.402 0 2.55-1.169 2.55-2.614v-.85z"})))},{name:"stackoverflow",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M18.18 20.103V14.78h1.767v7.09H4v-7.09h1.767v5.323z"}),React.createElement("path",{d:"m7.717 14.275 8.673 1.813.367-1.744-8.673-1.813zm1.147-4.13 8.031 3.74.734-1.606-8.031-3.763zm2.226-3.946 6.815 5.667 1.124-1.354-6.815-5.667zM15.495 2l-1.423 1.055 5.277 7.113 1.423-1.055zM7.533 18.314h8.857v-1.767H7.533z"})))},{name:"stumbleupon",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 4.294a4.47 4.47 0 0 0-4.471 4.471v6.353a1.059 1.059 0 1 1-2.118 0v-2.824H2v2.941a4.471 4.471 0 0 0 8.942 0v-6.47a1.059 1.059 0 1 1 2.118 0v1.294l1.412.647 2-.647V8.765A4.473 4.473 0 0 0 12 4.294m1.059 8.059v2.882a4.471 4.471 0 0 0 8.941 0v-2.824h-3.412v2.824a1.059 1.059 0 1 1-2.118 0v-2.882l-2 .647z"})))},{name:"substack",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19.904 9.182H4.095V7.054h15.81v2.127M4.095 11.109V21L12 16.583 19.905 21v-9.891zM19.905 3H4.095v2.127h15.81z"})))},{name:"telegram",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m3.08 14.757s-.25.625-.936.325l-2.541-1.949-1.63 1.486s-.127.096-.266.036c0 0-.12-.011-.27-.486s-.911-2.972-.911-2.972L6 12.349s-.387-.137-.425-.438c-.037-.3.437-.462.437-.462l10.03-3.934s.824-.362.824.238z"})))},{name:"threads",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 192 192"},React.createElement("g",null,React.createElement("path",{d:"M141.537 88.988a67 67 0 0 0-2.518-1.143c-1.482-27.307-16.403-42.94-41.457-43.1h-.34c-14.986 0-27.449 6.396-35.12 18.036l13.779 9.452c5.73-8.695 14.724-10.548 21.348-10.548h.229c8.249.053 14.474 2.452 18.503 7.129 2.932 3.405 4.893 8.111 5.864 14.05-7.314-1.243-15.224-1.626-23.68-1.14-23.82 1.371-39.134 15.264-38.105 34.568.522 9.792 5.4 18.216 13.735 23.719 7.047 4.652 16.124 6.927 25.557 6.412 12.458-.683 22.231-5.436 29.049-14.127 5.178-6.6 8.453-15.153 9.899-25.93 5.937 3.583 10.337 8.298 12.767 13.966 4.132 9.635 4.373 25.468-8.546 38.376-11.319 11.308-24.925 16.2-45.488 16.351-22.809-.169-40.06-7.484-51.275-21.742C35.236 139.966 29.808 120.682 29.605 96c.203-24.682 5.63-43.966 16.133-57.317C56.954 24.425 74.204 17.11 97.013 16.94c22.975.17 40.526 7.52 52.171 21.847 5.71 7.026 10.015 15.86 12.853 26.162l16.147-4.308c-3.44-12.68-8.853-23.606-16.219-32.668C147.036 9.607 125.202.195 97.07 0h-.113C68.882.194 47.292 9.642 32.788 28.08 19.882 44.485 13.224 67.315 13.001 95.932L13 96v.067c.224 28.617 6.882 51.447 19.788 67.854C47.292 182.358 68.882 191.806 96.957 192h.113c24.96-.173 42.554-6.708 57.048-21.189 18.963-18.945 18.392-42.692 12.142-57.27-4.484-10.454-13.033-18.945-24.723-24.553M98.44 129.507c-10.44.588-21.286-4.098-21.82-14.135-.397-7.442 5.296-15.746 22.461-16.735q2.948-.17 5.79-.169c6.235 0 12.068.606 17.371 1.765-1.978 24.702-13.58 28.713-23.802 29.274"})))},{name:"tiktok-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M5 3a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2zm7.531 3h2.053s-.114 2.635 2.85 2.82v2.04s-1.582.099-2.85-.87l.021 4.207a3.804 3.804 0 1 1-3.802-3.802h.533v2.082a1.73 1.73 0 0 0-1.922.648 1.727 1.727 0 0 0 1.947 2.646 1.73 1.73 0 0 0 1.19-1.642z"})))},{name:"tiktok",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12.22 2h3.42s-.19 4.394 4.75 4.702v3.396s-2.636.166-4.75-1.448l.037 7.012a6.338 6.338 0 1 1-6.34-6.339h.89v3.472a2.882 2.882 0 1 0 2.024 2.752z"})))},{name:"tripadvisor",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M21.01 9.859c.236-1.002.985-2.003.985-2.003l-3.341-.002C16.779 6.643 14.502 6 11.979 6 9.363 6 7 6.659 5.135 7.877L2 7.88s.74.988.98 1.983a4.98 4.98 0 0 0-.977 2.961 5.01 5.01 0 0 0 5.009 5.003 5 5 0 0 0 3.904-1.875l1.065 1.592 1.076-1.606a4.96 4.96 0 0 0 1.838 1.448 4.98 4.98 0 0 0 3.831.151 5.01 5.01 0 0 0 2.963-6.431 5 5 0 0 0-.679-1.247m-13.998 6.96a4 4 0 0 1-3.998-3.995 4 4 0 0 1 3.998-3.997 4 4 0 0 1 3.996 3.997 4 4 0 0 1-3.996 3.995m4.987-4.36A5.007 5.007 0 0 0 7.11 7.821c1.434-.613 3.081-.947 4.867-.947 1.798 0 3.421.324 4.853.966a4.984 4.984 0 0 0-4.831 4.619m6.288 4.134a3.97 3.97 0 0 1-3.058-.122 3.96 3.96 0 0 1-2.075-2.245v-.001a3.97 3.97 0 0 1 .118-3.056 3.97 3.97 0 0 1 2.246-2.077 4.005 4.005 0 0 1 5.135 2.366 4.006 4.006 0 0 1-2.366 5.135"}),React.createElement("path",{d:"M6.949 10.307a2.477 2.477 0 0 0-2.475 2.472 2.48 2.48 0 0 0 2.475 2.474 2.474 2.474 0 0 0 0-4.946m0 4.094a1.626 1.626 0 0 1-1.624-1.623 1.621 1.621 0 1 1 1.624 1.623M16.981 10.307a2.477 2.477 0 0 0-2.474 2.472 2.48 2.48 0 0 0 2.474 2.474 2.476 2.476 0 0 0 2.472-2.474 2.475 2.475 0 0 0-2.472-2.472m0 4.094a1.625 1.625 0 0 1-1.622-1.623 1.622 1.622 0 1 1 1.622 1.623"}),React.createElement("path",{d:"M7.778 12.778a.832.832 0 1 1-1.664.002.832.832 0 0 1 1.664-.002M16.981 11.947a.832.832 0 1 0 .002 1.666.832.832 0 0 0-.002-1.666"})))},{name:"tumblr-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M16.749 17.396c-.357.17-1.041.319-1.551.332-1.539.041-1.837-1.081-1.85-1.896V9.847h3.861v-2.91h-3.847V2.039h-2.817c-.046 0-.127.041-.138.144-.165 1.499-.867 4.13-3.783 5.181v2.484h1.945v6.282c0 2.151 1.587 5.206 5.775 5.135 1.413-.024 2.982-.616 3.329-1.126z"})))},{name:"tumblr",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-5.569 14.265c-2.446.042-3.372-1.742-3.372-2.998v-3.668H8.923v-1.45c1.703-.614 2.113-2.15 2.209-3.025.007-.06.054-.084.081-.084h1.645V8.9h2.246v1.7H12.85v3.495c.008.476.182 1.131 1.081 1.107.298-.008.697-.094.906-.194l.54 1.601c-.205.296-1.121.641-1.946.656"})))},{name:"twitch",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M16.499 8.089h-1.636v4.91h1.636zm-4.499 0h-1.637v4.91H12zM4.228 3.178 3 6.451v13.092h4.499V22h2.456l2.454-2.456h3.681L21 14.636V3.178zm15.136 10.638L16.5 16.681H12l-2.453 2.453V16.68H5.863V4.814h13.501z"})))},{name:"twitter-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M22.23 5.924a8.2 8.2 0 0 1-2.357.646 4.12 4.12 0 0 0 1.804-2.27 8.2 8.2 0 0 1-2.606.996 4.103 4.103 0 0 0-6.991 3.742 11.65 11.65 0 0 1-8.457-4.287 4.1 4.1 0 0 0-.556 2.063 4.1 4.1 0 0 0 1.825 3.415 4.1 4.1 0 0 1-1.859-.513v.052a4.104 4.104 0 0 0 3.292 4.023 4.1 4.1 0 0 1-1.853.07 4.11 4.11 0 0 0 3.833 2.85 8.24 8.24 0 0 1-5.096 1.756 8 8 0 0 1-.979-.057 11.6 11.6 0 0 0 6.29 1.843c7.547 0 11.675-6.252 11.675-11.675q0-.267-.012-.531a8.3 8.3 0 0 0 2.047-2.123"})))},{name:"twitter",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-2.534 6.71q.007.148.007.298c0 3.045-2.318 6.556-6.556 6.556a6.5 6.5 0 0 1-3.532-1.035q.27.032.55.032a4.63 4.63 0 0 0 2.862-.986 2.31 2.31 0 0 1-2.152-1.6 2.3 2.3 0 0 0 1.04-.04 2.306 2.306 0 0 1-1.848-2.259v-.029c.311.173.666.276 1.044.288a2.303 2.303 0 0 1-.713-3.076 6.54 6.54 0 0 0 4.749 2.407 2.305 2.305 0 0 1 3.926-2.101 4.6 4.6 0 0 0 1.463-.559 2.3 2.3 0 0 1-1.013 1.275c.466-.056.91-.18 1.323-.363-.31.461-.7.867-1.15 1.192"})))},{name:"untappd",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"m11 13.299-5.824 8.133c-.298.416-.8.635-1.308.572-.578-.072-1.374-.289-2.195-.879S.392 19.849.139 19.323a1.4 1.4 0 0 1 .122-1.425l5.824-8.133a3.1 3.1 0 0 1 1.062-.927l1.146-.604c.23-.121.436-.283.608-.478.556-.631 2.049-2.284 4.696-4.957l.046-.212a.13.13 0 0 1 .096-.1l.146-.037a.135.135 0 0 0 .101-.141l-.015-.18a.13.13 0 0 1 .125-.142c.176-.005.518.046 1.001.393s.64.656.692.824a.13.13 0 0 1-.095.164l-.175.044a.13.13 0 0 0-.101.141l.012.15a.13.13 0 0 1-.063.123l-.186.112c-1.679 3.369-2.764 5.316-3.183 6.046a2.2 2.2 0 0 0-.257.73l-.205 1.281A3.1 3.1 0 0 1 11 13.3zm12.739 4.598-5.824-8.133a3.1 3.1 0 0 0-1.062-.927l-1.146-.605a2.1 2.1 0 0 1-.608-.478 51 51 0 0 0-.587-.654.09.09 0 0 0-.142.018 97 97 0 0 1-1.745 3.223 1.4 1.4 0 0 0-.171.485 3.5 3.5 0 0 0 0 1.103l.01.064c.075.471.259.918.536 1.305l5.824 8.133c.296.413.79.635 1.294.574a4.76 4.76 0 0 0 2.209-.881 4.76 4.76 0 0 0 1.533-1.802 1.4 1.4 0 0 0-.122-1.425zM8.306 3.366l.175.044a.134.134 0 0 1 .101.141l-.012.15a.13.13 0 0 0 .063.123l.186.112q.465.933.869 1.721c.026.051.091.06.129.019q.655-.703 1.585-1.668a.137.137 0 0 0 .003-.19c-.315-.322-.645-.659-1.002-1.02l-.046-.212a.13.13 0 0 0-.096-.099l-.146-.037a.135.135 0 0 1-.101-.141l.015-.18a.13.13 0 0 0-.123-.142c-.175-.005-.518.045-1.002.393-.483.347-.64.656-.692.824a.13.13 0 0 0 .095.164z"})))},{name:"vimeo",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M22.396 7.164q-.139 3.039-4.245 8.32Q13.907 21 10.97 21q-1.82 0-3.079-3.359l-1.68-6.159q-.934-3.36-2.005-3.36-.234.001-1.634.98l-.978-1.261q1.541-1.353 3.037-2.708 2.056-1.774 3.084-1.869 2.429-.234 2.99 3.321.607 3.836.841 4.769.7 3.181 1.542 3.181.653 0 1.963-2.065 1.307-2.063 1.401-3.142.187-1.781-1.401-1.782-.747.001-1.541.341 1.534-5.024 5.862-4.884 3.21.095 3.024 4.161"})))},{name:"vk",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{fillRule:"evenodd",d:"M1.687 1.687C0 3.374 0 6.09 0 11.52v.96c0 5.431 0 8.146 1.687 9.833S6.09 24 11.52 24h.96c5.431 0 8.146 0 9.833-1.687S24 17.91 24 12.48v-.96c0-5.431 0-8.146-1.687-9.833S17.91 0 12.48 0h-.96C6.09 0 3.374 0 1.687 1.687M4.05 7.3c.13 6.24 3.25 9.99 8.72 9.99h.31v-3.57c2.01.2 3.53 1.67 4.14 3.57h2.84c-.78-2.84-2.83-4.41-4.11-5.01 1.28-.74 3.08-2.54 3.51-4.98h-2.58c-.56 1.98-2.22 3.78-3.8 3.95V7.3H10.5v6.92c-1.6-.4-3.62-2.34-3.71-6.92z"})))},{name:"whatsapp",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"m2.048 22 1.406-5.136a9.9 9.9 0 0 1-1.323-4.955C2.133 6.446 6.579 2 12.042 2a9.85 9.85 0 0 1 7.011 2.906 9.85 9.85 0 0 1 2.9 7.011c-.002 5.464-4.448 9.91-9.91 9.91h-.004a9.9 9.9 0 0 1-4.736-1.206zm5.497-3.172.301.179a8.2 8.2 0 0 0 4.193 1.148h.003c4.54 0 8.235-3.695 8.237-8.237a8.2 8.2 0 0 0-2.41-5.828 8.18 8.18 0 0 0-5.824-2.416c-4.544 0-8.239 3.695-8.241 8.237a8.2 8.2 0 0 0 1.259 4.384l.196.312-.832 3.04zm9.49-4.554c-.062-.103-.227-.165-.475-.289s-1.465-.723-1.692-.806-.392-.124-.557.124-.64.806-.784.971-.289.186-.536.062-1.046-.385-1.991-1.229c-.736-.657-1.233-1.468-1.378-1.715s-.015-.382.109-.505c.111-.111.248-.289.371-.434.124-.145.165-.248.248-.413s.041-.31-.021-.434-.557-1.343-.763-1.839c-.202-.483-.407-.417-.559-.425-.144-.007-.31-.009-.475-.009a.9.9 0 0 0-.66.31c-.226.248-.866.847-.866 2.066s.887 2.396 1.011 2.562 1.746 2.666 4.23 3.739c.591.255 1.052.408 1.412.522.593.189 1.133.162 1.56.098.476-.071 1.465-.599 1.671-1.177.206-.58.206-1.075.145-1.179"})))},{name:"woocommerce",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19 2H5C3.3 2 2 3.3 2 5v11c0 1.7 1.3 3 3 3h4l6 3-1-3h5c1.7 0 3-1.3 3-3V5c0-1.7-1.3-3-3-3m-1.6 4.5c-.4.8-.8 2.1-1 3.9-.3 1.8-.4 3.1-.3 4.1 0 .3 0 .5-.1.7s-.3.4-.6.4-.6-.1-.9-.4c-1-1-1.8-2.6-2.4-4.6-.7 1.4-1.2 2.4-1.6 3.1-.6 1.2-1.2 1.8-1.6 1.9-.3 0-.5-.2-.8-.7-.5-1.4-1.1-4.2-1.7-8.2 0-.3 0-.5.2-.7.1-.2.4-.3.7-.4.5 0 .9.2.9.8.3 2.3.7 4.2 1.1 5.7l2.4-4.5c.2-.4.4-.6.8-.6q.75 0 .9.9c.3 1.4.6 2.6 1 3.7.3-2.7.8-4.7 1.4-5.9.2-.3.4-.5.7-.5.2 0 .5.1.7.2q.3.3.3.6c0 .3 0 .4-.1.5"})))},{name:"wordpress",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12.158 12.786 9.46 20.625a9 9 0 0 0 5.526-.144 1 1 0 0 1-.065-.124zM3.009 12a8.99 8.99 0 0 0 5.067 8.092L3.788 8.341A8.95 8.95 0 0 0 3.009 12m15.06-.454c0-1.112-.399-1.881-.741-2.48-.456-.741-.883-1.368-.883-2.109 0-.826.627-1.596 1.51-1.596q.06.002.116.007A8.96 8.96 0 0 0 12 3.009a8.98 8.98 0 0 0-7.512 4.052c.211.007.41.011.579.011.94 0 2.396-.114 2.396-.114.484-.028.541.684.057.741 0 0-.487.057-1.029.085l3.274 9.739 1.968-5.901-1.401-3.838c-.484-.028-.943-.085-.943-.085-.485-.029-.428-.769.057-.741 0 0 1.484.114 2.368.114.94 0 2.397-.114 2.397-.114.485-.028.542.684.057.741 0 0-.488.057-1.029.085l3.249 9.665.897-2.996q.684-1.753.684-2.907m1.82-3.86q.06.428.06.924c0 .912-.171 1.938-.684 3.22l-2.746 7.94a8.98 8.98 0 0 0 4.47-7.771 8.9 8.9 0 0 0-1.1-4.313M12 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10"})))},{name:"x",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M13.982 10.622 20.54 3h-1.554l-5.693 6.618L8.745 3H3.5l6.876 10.007L3.5 21h1.554l6.012-6.989L15.868 21h5.245zm-2.128 2.474-.697-.997-5.543-7.93H8l4.474 6.4.697.996 5.815 8.318h-2.387z"})))},{name:"xanga",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M9 9h6v6H9zM3 9h6V3H3zm12 0h6V3h-6zm0 12h6v-6h-6zM3 21h6v-6H3z"})))},{name:"youtube",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M21.8 8.001s-.195-1.378-.795-1.985c-.76-.797-1.613-.801-2.004-.847-2.799-.202-6.997-.202-6.997-.202h-.009s-4.198 0-6.997.202c-.39.047-1.242.051-2.003.847-.6.607-.795 1.985-.795 1.985S2 9.62 2 11.238v1.517c0 1.618.2 3.237.2 3.237s.195 1.378.795 1.985c.761.797 1.76.771 2.205.855 1.6.153 6.8.201 6.8.201s4.203-.006 7.001-.209c.391-.047 1.243-.051 2.004-.847.6-.607.795-1.985.795-1.985s.2-1.618.2-3.237v-1.517c0-1.618-.2-3.237-.2-3.237M9.935 14.594l-.001-5.62 5.404 2.82z"})))}]},8992:(e,t,a)=>{"use strict";a(6072);var n=a(8120),r=a.n(n),o=a(1609);a(1135);o.PureComponent,r().string.isRequired,r().number,r().func,r().string},3536:(e,t,a)=>{"use strict";a.d(t,{A:()=>L});var n=a(442),r=a(2947),o=a(8176),i=a(7425),s=a(766),c=a(5918),l=a(8509),d=a(1112),u=a(5640),p=a(4437),m=a(9384),h=a(5985),g=a(6427),v=a(7143),f=a(7723),y=a(3022),b=a(1609),w=a(6287),E=a(3356),A=a(7770),k=a(6764),R=a(9072),C=a(7520),S=a(8728),_=a(3463),x=a(957),P=a(8575),j=a(2301),N=a(3322),V=a(7812),z=a(9668),U=a(9525),O=a(1795);const __=f.__,L=()=>{const{videos:e,uploadedVideoCount:t,localVideos:a,uploadedLocalVideoCount:p,hasVideos:f,hasLocalVideos:L,handleFilesUpload:F,handleLocalVideoUpload:T,loading:D,uploading:I,hasVideoPressPurchase:B}=(()=>{const{uploadVideo:e,uploadVideoFromLibrary:t,setVideosQuery:a}=(0,v.useDispatch)(w.ax),{items:n,uploadErrors:r,uploading:o,uploadedVideoCount:i,isFetching:s,search:c,page:l,itemsPerPage:d,total:u}=(0,x.Ay)(),{items:p,uploadedLocalVideoCount:m}=(0,x.Ds)(),{hasVideoPressPurchase:h}=(0,C.j)(),g=(0,b.useRef)(l),f=(0,S.o)(),y=parseInt(f.getParam("page","1")),A=f.getParam("q",""),k=Math.ceil(u/d);(0,b.useEffect)((()=>0===u&&1!==y||u>0&&(y<1||y>k)?(f.deleteParam("page"),void f.update()):void(l===y?c!==A&&a({search:A}):l!==g.current?(g.current=l,f.setParam("page",l),f.update()):(g.current=y,a({page:y})))),[k,l,y,c,A,g.current]);let R=l>1||Boolean(c)?n:[...r,...o,...n];const _=i>0||s||o?.length>0||r?.length>0,P=m>0;if(s){const e=Math.max(1,Math.min(d,i-d*(l-1)));R=new Array(e).fill({}).map((()=>({id:(0,E.A)()})))}return{videos:R,localVideos:p,uploadedVideoCount:i,uploadedLocalVideoCount:m,hasVideos:_,hasLocalVideos:P,handleFilesUpload:t=>{h?t.forEach((t=>{e(t)})):t.length>0&&e(t[0])},handleLocalVideoUpload:e=>{t(e)},loading:s,uploading:o?.length>0||r?.length>0,hasVideoPressPurchase:h}})(),{canPerformAction:H,isRegistered:q,hasConnectedOwner:G,isUserConnected:$}=(0,R.J)(),{hasConnectionError:J}=(0,m.useConnectionErrorNotice)(),[W,K]=(0,b.useState)(!q),[Z]=(0,n.A)("sm"),Y=(B||!f)&&H,{isDraggingOver:X,inputRef:Q,handleFileInputChangeEvent:ee,filterVideoFiles:te}=(0,_.A)({canDrop:Y&&!D,dropElement:document,onSelectFiles:F});return(0,k.A)({pageViewEventName:"jetpack_videopress_admin_page_view"}),React.createElement(r.A,{moduleName:__("Jetpack VideoPress","jetpack-videopress-pkg"),header:React.createElement(o.A,null),useInternalLinks:(0,h.pg)()},React.createElement("div",{className:(0,y.A)(O.A["files-overlay"],{[O.A.hover]:X&&Y&&!D})},React.createElement(i.Ay,{className:O.A["drop-text"],variant:"headline-medium"},__("Drop files to upload","jetpack-videopress-pkg")),React.createElement("input",{ref:Q,type:"file",accept:A.Zd,className:O.A["file-input"],onChange:ee})),W?React.createElement(s.A,null,React.createElement(c.A,{horizontalSpacing:3,horizontalGap:3},React.createElement(l.A,{sm:4,md:8,lg:12},React.createElement(j.A,{onRedirecting:()=>K(!0)})))):React.createElement(React.Fragment,null,React.createElement(s.A,null,React.createElement(c.A,{horizontalSpacing:0},React.createElement(l.A,null,React.createElement("div",{id:"jp-admin-notices",className:O.A["jetpack-videopress-jitm-card"]}))),React.createElement(c.A,{horizontalSpacing:6,horizontalGap:3},J&&React.createElement(l.A,null,React.createElement(m.ConnectionError,null)),(!G||!$)&&React.createElement(l.A,{sm:4,md:8,lg:12},React.createElement(P.X,null)),React.createElement(l.A,{sm:4,md:4,lg:8},React.createElement(i.Ay,{variant:"headline-small",mb:3},__("High quality, ad-free video","jetpack-videopress-pkg")),B&&React.createElement(V.i,{className:O.A["storage-meter"],progressBarClassName:O.A["storage-meter__progress-bar"]}),f?React.createElement(g.FormFileUpload,{onChange:e=>F(te(e.currentTarget.files)),accept:A.Zd,multiple:B,render:({openFileDialog:e})=>React.createElement(d.A,{fullWidth:Z,onClick:e,isLoading:D,disabled:!Y},__("Add new video","jetpack-videopress-pkg")),__next40pxDefaultSize:!0}):React.createElement(i.Ay,{variant:"title-medium"},__("Let's add your first video below!","jetpack-videopress-pkg")),!B&&React.createElement(M,{hasUsedVideo:f})))),React.createElement(u.A,null,React.createElement(c.A,{horizontalSpacing:6,horizontalGap:10},f?React.createElement(l.A,{sm:4,md:6,lg:12},React.createElement(U.ad,{videos:e,totalVideos:t,loading:D})):React.createElement(l.A,{sm:4,md:6,lg:12,className:O.A["first-video-wrapper"]},React.createElement(z.A,{className:O.A["upload-area"],onSelectFiles:F})),L&&React.createElement(l.A,{sm:4,md:6,lg:12},React.createElement(U.DI,{videos:a,totalVideos:p,onUploadClick:T,uploading:I})))),React.createElement(u.A,null,React.createElement(N.M,null))))},M=({hasUsedVideo:e=!1})=>{const{adminUri:t,siteSuffix:a}=window.jetpackVideoPressInitialState,{product:n,hasVideoPressPurchase:r,isFetchingPurchases:o}=(0,C.j)(),{run:i}=(0,m.useProductCheckoutWorkflow)({siteSuffix:a,productSlug:n.productSlug,redirectUrl:t,isFetchingPurchases:o,useBlogIdSuffix:!0}),{recordEventHandler:s}=(0,k.A)({});if(r||o)return null;const c=s("jetpack_videopress_upgrade_trigger_link_click",i),l=e?__("You have used your free video upload","jetpack-videopress-pkg"):"",d=__("Upgrade now to unlock unlimited videos, 1TB of storage, and more!","jetpack-videopress-pkg");return React.createElement(p.A,{description:l,cta:d,className:O.A["upgrade-trigger"],onClick:c})}},9525:(e,t,a)=>{"use strict";a.d(t,{DI:()=>_,ad:()=>S});var n=a(442),r=a(7425),o=a(1112),i=a(6087),s=a(7723),c=a(1651),l=a(9761),d=a(3022),u=a(1609),p=a.n(u),m=a(9539),h=a(8728),g=a(957),v=a(5333),f=a(8175),y=a(1755),b=a(6145),w=a(3793),E=a(1795);const __=s.__,A="videopress-library-type",k="list",R="grid",C=({children:e,totalVideos:t=0,libraryType:a=k,onChangeType:i,hideFilter:m=!1,title:f,disabled:b})=>{const{isFetching:w,search:A}=(0,g.Ay)(),R=(0,h.o)(),C=(e,t=!0)=>{t&&R.deleteParam("page"),e?R.setParam("q",e):R.deleteParam("q"),R.update()},[S,_]=(0,u.useState)(R.getParam("q",""));(0,u.useEffect)((()=>{A&&A!==S&&(C(A,!1),_(A))}),[A]);const[x]=(0,n.A)("lg"),[P,j]=(0,u.useState)(!1),N=__("1 Video","jetpack-videopress-pkg"),V=(0,s.sprintf)(/* translators: placeholder is the number of videos */
__("%s Videos","jetpack-videopress-pkg"),t),z=1===t?N:V;return p().createElement("div",{className:E.A["library-wrapper"]},p().createElement(r.Ay,{variant:"headline-small",mb:1},f),!x&&p().createElement(r.Ay,{className:E.A["total-sm"]},z),p().createElement("div",{className:E.A["total-filter-wrapper"]},x&&p().createElement(r.Ay,null,z),m?null:p().createElement("div",{className:E.A["filter-wrapper"]},p().createElement(v.DO,{className:(0,d.A)(E.A["search-input"],{[E.A.small]:!x}),onSearch:C,value:S,loading:w,onChange:_,disabled:b}),p().createElement(y.Fr,{onClick:()=>j((e=>!e)),isActive:P,disabled:b}),p().createElement(o.A,{variant:"tertiary",size:"small",icon:a===k?c.A:l.A,onClick:i}))),P&&p().createElement(y.M2,{className:E.A["filter-section"]}),e)},S=({videos:e,totalVideos:t,loading:a})=>{const n=(0,m.Zp)(),{search:o}=(0,g.Ay)(),c=[...e.filter((e=>e.error)),...e.filter((e=>e.uploading)),...e.filter((e=>!e.uploading&&!e.error)).slice(0,6)],l=localStorage.getItem(A),[d,h]=(0,u.useState)(l??R),v=e?.some?.((e=>e.uploading)),y=e=>{n(`/video/${e?.id}/edit`)},S=d===R?p().createElement(b.A,{videos:c,onVideoDetailsClick:y,loading:a,count:v?c.length:6}):p().createElement(w.A,{videos:c,onVideoDetailsClick:y,hidePlays:!0,loading:a});return p().createElement(C,{totalVideos:t,onChangeType:()=>{h((e=>{const t=e===R?k:R;return localStorage.setItem(A,t),t}))},libraryType:d,title:__("Your VideoPress library","jetpack-videopress-pkg")},c.length>0||a?S:p().createElement(r.Ay,null,o.trim()?(0,i.createInterpolateElement)((0,s.sprintf)(/* translators: placeholder is the search term */
__("No videos match your search for <em>%s</em>.","jetpack-videopress-pkg"),o),{em:p().createElement("em",{className:E.A["query-no-results"]})}):__("No videos match your filtering criteria.","jetpack-videopress-pkg")),p().createElement(f.XL,{className:E.A.pagination}))},_=({videos:e,totalVideos:t,loading:a,uploading:n,onUploadClick:r})=>p().createElement(C,{totalVideos:t,hideFilter:!0,title:__("Local videos","jetpack-videopress-pkg")},p().createElement(w.R,{videos:e,loading:a,onActionClick:r,uploading:n}),p().createElement(f.O6,{className:E.A.pagination}))},8876:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(6072),r=a.n(n),o=a(3022),i=a(1609),s=a(6652);const c=(0,i.forwardRef)((({checked:e,onChange:t,className:a,children:n,htmlFor:i,dataTestId:c,...l},d)=>React.createElement("label",{htmlFor:i,className:s.A["checkbox-container"],"data-testid":c},React.createElement("input",r()({},l,{ref:d,type:"checkbox",checked:e,className:(0,o.A)(a,s.A.checkbox),onChange:e=>{t?.(e.target.checked)}})),React.createElement("span",{className:s.A["checkbox-checkmark"]}),n)));c.displayName="Checkbox";const l=c},6955:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var n=a(1112),r=a(9491),o=a(7723),i=a(1609),s=a(8823);const __=o.__,c=({text:e,value:t,onCopy:a,resetTime:o=3e3})=>{const c=__("Copied!","jetpack-videopress-pkg"),l=__("Copy","jetpack-videopress-pkg"),[d,u]=(0,i.useState)(!1),p=t||e,m=(0,r.useCopyToClipboard)(p,(()=>{const e=setTimeout((()=>{u(!1),clearTimeout(e)}),o);u(!0),a?.()}));return React.createElement("div",{className:s.A.wrapper},React.createElement("input",{type:"text",value:e||t,onClick:e=>{e.currentTarget.select()},readOnly:!0}),React.createElement(n.A,{weight:"regular",variant:"secondary",size:"small",ref:m},d?c:l))}},3378:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(723),r=a(7425),o=a(1112),i=a(6427),s=a(7723),c=a(8838);const __=s.__,l=({onClose:e,onDelete:t})=>React.createElement(i.Modal,{title:__("Delete video","jetpack-videopress-pkg"),onRequestClose:e,className:c.A["delete-video-modal"]},React.createElement(n.Ay,null,React.createElement("div",null,React.createElement(r.Ay,null,__("This action cannot be undone.","jetpack-videopress-pkg")),React.createElement("div",{className:c.A["modal-actions"]},React.createElement(o.A,{className:c.A["modal-action-button"],variant:"secondary",weight:"bold",onClick:e},__("Cancel","jetpack-videopress-pkg")),React.createElement(o.A,{className:c.A["modal-action-button"],isDestructive:!0,variant:"primary",weight:"bold",onClick:t},__("Delete","jetpack-videopress-pkg"))))))},2784:(e,t,a)=>{"use strict";a.d(t,{A:()=>q});var n=a(442),r=a(8176),o=a(7425),i=a(1112),s=a(1876),c=a(2947),l=a(5640),d=a(5918),u=a(8509),p=a(5985),m=a(6427),h=a(7723),g=a(1113),v=a(5437),f=a(7474),y=a(4066),b=a(3022),w=a(1609),E=a(9539),A=a(28),k=a(6151),R=a(7726),C=a(7121),S=a(2843),_=a(6720),x=a(2439),P=a(8962),j=a(9072),N=a(4951),V=a(957),z=a(5333),U=a(4565),O=a(3287),L=a(5419),M=a(4231),F=a(192),T=a(2267);const __=h.__,D=()=>{},I=({saveDisabled:e=!0,disabled:t=!1,onSaveChanges:a,onDelete:s,videoId:c})=>{const[l]=(0,n.A)("sm"),d=(0,E.Zp)();return React.createElement("div",{className:(0,b.A)(F.A["header-wrapper"],{[F.A.small]:l})},React.createElement("button",{onClick:()=>d("/"),className:F.A["logo-button"]},React.createElement(r.A,null)),React.createElement("div",{className:F.A["header-content"]},React.createElement("div",{className:F.A.breadcrumb},!l&&React.createElement(g.A,{icon:v.A}),React.createElement(o.Ay,null,__("Edit video details","jetpack-videopress-pkg"))),React.createElement("div",{className:F.A.buttons},React.createElement(i.A,{disabled:e||t,onClick:a,isLoading:t},__("Save changes","jetpack-videopress-pkg")),React.createElement(O.A,{videoId:c,disabled:t,onDelete:s}))))},B=()=>{const{page:e}=(0,V.mo)(),t=e>1?`/?page=${e}`:"/";return React.createElement("div",{className:F.A["back-link"]},React.createElement(A.N_,{to:t,className:F.A.link},React.createElement(g.A,{icon:f.A,className:F.A.icon}),__("Go back","jetpack-videopress-pkg")))},H=({title:e,onChangeTitle:t,description:a,onChangeDescription:n,loading:r=!1,disabled:o=!1})=>{const{hasIncompleteChapters:i}=(0,x.A)(a);return React.createElement(React.Fragment,null,r?React.createElement(s.A,{height:88}):React.createElement(z.Ay,{value:e,label:__("Title","jetpack-videopress-pkg"),name:"title",onChange:t,onEnter:D,disabled:o,size:"large"}),r?React.createElement(s.A,{height:133,className:F.A.input}):React.createElement(React.Fragment,null,React.createElement(z.Ay,{value:a,className:F.A.input,label:__("Description","jetpack-videopress-pkg"),name:"description",onChange:n,onEnter:D,disabled:o,type:"textarea",size:"large",rows:8}),React.createElement("div",{className:F.A["chapters-help-container"]},i?React.createElement(S.A,{className:F.A["incomplete-chapters-notice"]}):React.createElement("div",{className:F.A["learn-more"]},React.createElement(k.A,null)))))},q=()=>{const{guid:e,id:t,duration:a,posterImage:n,filename:r,uploadDate:i,url:h,width:v,height:f,title:A,description:k,rating:S,privacySetting:x,allowDownload:z,displayEmbed:O,isPrivate:D,isFetchingPlaybackToken:q,hasChanges:G,updating:$,updated:J,deleted:W,isFetching:K,isDeleting:Z,handleSaveChanges:Y,handleDelete:X,setTitle:Q,setDescription:ee,setRating:te,setPrivacySetting:ae,setAllowDownload:ne,setDisplayEmbed:re,processing:oe,useVideoAsThumbnail:ie,selectedTime:se,handleConfirmFrame:ce,handleCloseSelectFrame:le,handleOpenSelectFrame:de,handleVideoFrameSelected:ue,frameSelectorIsOpen:pe,selectPosterImageFromLibrary:me,posterImageSource:he,libraryAttachment:ge,isUpdatingPoster:ve}=(0,T.A)(),{canPerformAction:fe}=(0,j.J)(),ye=__("There are unsaved changes. Are you sure you want to exit?","jetpack-videopress-pkg");(0,N.A)({shouldPrevent:G&&!J&&!W&&fe,message:ye});const be=(0,E.Zp)(),{page:we}=(0,V.mo)();(0,w.useEffect)((()=>{if(!0===W){be(we>1?`/?page=${we}`:"/")}}),[W]),fe||be("/");let Ee=n;"video"===he&&ie?Ee=React.createElement(_.L,{src:h,currentTime:se}):"upload"===he&&(Ee=ge.url);const Ae=K||q,ke=Z||$,Re=`[videopress ${e}${v?` w=${v}`:""}${f?` h=${f}`:""}]`;return React.createElement(React.Fragment,null,pe&&React.createElement(M.A,{handleCloseSelectFrame:le,url:h,handleVideoFrameSelected:ue,selectedTime:se,handleConfirmFrame:ce}),React.createElement(c.A,{moduleName:__("Jetpack VideoPress","jetpack-videopress-pkg"),header:React.createElement(React.Fragment,null,React.createElement("div",{id:"jp-admin-notices",className:F.A["jetpack-videopress-jitm-card"]}),React.createElement(B,null),React.createElement(I,{onSaveChanges:Y,onDelete:X,saveDisabled:!G,disabled:ke||Ae,videoId:t})),useInternalLinks:(0,p.pg)()},React.createElement(l.A,null,React.createElement(d.A,{horizontalSpacing:6,horizontalGap:10},React.createElement(u.A,{sm:4,md:8,lg:7},React.createElement(H,{title:A??"",onChangeTitle:Q,description:k??"",onChangeDescription:ee,loading:Ae,disabled:ke})),React.createElement(u.A,{sm:4,md:8,lg:{start:9,end:12}},React.createElement(L.Ay,{thumbnail:Ee,loading:Ae,processing:oe||ve,deleting:Z,updating:$,duration:a,editable:!0,onSelectFromVideo:de,onUploadImage:me}),React.createElement(U.A,{filename:r??"",uploadDate:i??"",shortcode:Re??"",loading:Ae,guid:e,isPrivate:D}),React.createElement("div",{className:F.A["side-fields"]},Ae?React.createElement(s.A,{height:40,className:(0,b.A)(F.A.field)}):React.createElement(m.SelectControl,{className:F.A.field,value:x,label:__("Privacy","jetpack-videopress-pkg"),onChange:e=>ae(e),disabled:ke,prefix:React.createElement("div",{className:F.A["privacy-icon"]},React.createElement(g.A,{icon:x===P.az&&C.A||x===P.o0&&R.A||x===P.CR&&y.A})),options:[{label:__("Site default","jetpack-videopress-pkg"),value:P.CR},{label:__("Public","jetpack-videopress-pkg"),value:P.az},{label:__("Private","jetpack-videopress-pkg"),value:P.o0}],__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),Ae?React.createElement(s.A,{height:40,className:(0,b.A)(F.A.field)}):React.createElement(React.Fragment,null,React.createElement(o.Ay,{className:(0,b.A)(F.A.field,F.A.checkboxTitle)},__("Share","jetpack-videopress-pkg")),React.createElement(m.CheckboxControl,{checked:O,disabled:ke,label:__("Display share menu and allow viewers to copy a link or embed this video","jetpack-videopress-pkg"),onChange:e=>re(e?1:0),__nextHasNoMarginBottom:!0})),Ae?React.createElement(s.A,{height:40,className:(0,b.A)(F.A.field)}):React.createElement(React.Fragment,null,React.createElement(o.Ay,{className:(0,b.A)(F.A.field,F.A.checkboxTitle)},__("Download","jetpack-videopress-pkg")),React.createElement(m.CheckboxControl,{checked:z,disabled:ke,label:__("Display download option and allow viewers to download this video","jetpack-videopress-pkg"),onChange:e=>ne(e?1:0),__nextHasNoMarginBottom:!0})),ke||Ae?React.createElement(s.A,{height:40,className:(0,b.A)(F.A.field)}):React.createElement(m.RadioControl,{className:(0,b.A)(F.A.field,F.A.rating),label:__("Rating","jetpack-videopress-pkg"),selected:S,options:[{label:__("G","jetpack-videopress-pkg"),value:P.ko},{label:__("PG-13","jetpack-videopress-pkg"),value:P.z7},{label:__("R","jetpack-videopress-pkg"),value:P.iL}],onChange:te})))))))}},2267:(e,t,a)=>{"use strict";a.d(t,{A:()=>A});var n=a(9384),r=a(7143),o=a(7723),i=a(6941),s=a.n(i),c=a(1609),l=a(9539),d=a(2185),u=a(1370),p=a(8634),m=a(6287),h=a(8962),g=a(9799),v=a(6302),f=a(2066),y=a(8326),b=a(7750),w=a(7882);const __=o.__,E=s()("videopress:use-edit-details"),A=()=>{const e=(0,l.Zp)(),t=(0,r.useDispatch)(m.ax),{isRegistered:a}=(0,n.useConnection)();a||e("/");const{videoId:o}=(0,l.g)(),i=Number(o),{data:s,isFetching:A,processing:k,isDeleting:R,updateVideoPrivacy:C,isUpdatingPoster:S}=(0,w.A)(Number(i),!0),{playbackToken:_,isFetchingPlaybackToken:x}=(0,y.A)(s),[P,j]=(0,c.useState)(null),[N,V]=(0,c.useState)(null),[z,U]=(0,c.useState)(null),[O,L]=(0,c.useState)(!1),[M,F]=(0,c.useState)(!1),[T,D]=(0,c.useState)(!1),[I,B]=(0,c.useState)({title:s?.title,description:s?.description,privacySetting:h.zG[s?.privacySetting],rating:s?.rating,allowDownload:s?.allowDownload,displayEmbed:s?.displayEmbed}),{selectedTime:H,setVideoFrameMs:q,updatePosterImageFromFrame:G,selectAttachmentFromLibrary:$,updatePosterImageFromLibrary:J,...W}=(0,b.A)({video:s}),{metaChanged:K,handleMetaUpdate:Z,hasFieldChanged:Y,hasPrivacySettingChanged:X,...Q}=(({videoId:e,formData:t,video:a,updateData:n})=>{const r=(0,d.A)(e),o=e=>void 0===e||""===e,i=e=>{const n=t?.[e],r=a?.[e],i=n!==r;return!(o(n)&&o(r))&&i},s=["title","description","rating","allowDownload","displayEmbed"].some((e=>i(e)));return{setTitle:e=>{n({title:e})},setDescription:e=>{n({description:e})},setPrivacySetting:e=>{n({privacySetting:e})},setRating:e=>{n({rating:e})},setAllowDownload:e=>{n({allowDownload:e})},setDisplayEmbed:e=>{n({displayEmbed:e})},handleMetaUpdate:()=>new Promise(((e,a)=>{s?r(t).then(e).catch(a):e(null)})),metaChanged:s,hasFieldChanged:i,hasPrivacySettingChanged:()=>{const e=t?.privacySetting,n=a?.privacySetting,r=e!==h.zG[n];return!(o(e)&&o(n))&&r}}})({videoId:i,video:s,formData:I,updateData:e=>{B((t=>({...t,...e})))}});(0,c.useEffect)((()=>{null!=H&&U("video")}),[H]);const ee=K||null!=H||P!==N||X(),te=async()=>{if(Y("description")){const e=(0,g.Ay)(I.description);if(I.description.length&&(0,f.A)(e)){E("Autogenerated chapters detected. Processing...");let e=!1;try{const t=await(0,u.X)({guid:s.guid,isPrivate:s.isPrivate}),a=t?.tracks?.chapters?.en?.src,n="string"==typeof a;if(n){const t=`https://videos.files.wordpress.com/${s.guid}/${a}`;e=await(0,p.Cr)(t,{guid:s.guid,isPrivate:s.isPrivate}),E("Chapter %o detected. Overwritable: %o",a,e?"yes":"no")}else E("Allow overwrite chapter: File does not exist");if(!n||e){const e={label:__("English (auto-generated)","jetpack-videopress-pkg"),srcLang:"en",kind:"chapters",tmpFile:(0,v.Ay)(I.description)};return E("Autogenerated track: %o",e),(0,p.n)(e,s.guid)}}catch(e){E("Error on chapters processing",e)}}}return null},ae=A&&void 0===I?.title&&void 0===I?.description;return(0,c.useEffect)((()=>{let e=!0;return!ae&&e&&B({title:s?.title,description:s?.description,privacySetting:h.zG[s?.privacySetting],rating:s?.rating,allowDownload:s?.allowDownload,displayEmbed:s?.displayEmbed}),()=>{e=!1}}),[ae]),{playbackToken:_,isFetchingPlaybackToken:x,...s,...I,hasChanges:ee,posterImageSource:z,libraryAttachment:P,selectPosterImageFromLibrary:async()=>{const e=await $();e&&(j(e),U("upload"))},handleSaveChanges:()=>{L(!0);const e=[Z(),te()];"video"===z?e.push(G()):"upload"===z&&e.push(J(P.id)),Y("privacySetting")&&e.push(C(I.privacySetting)),Promise.allSettled(e).then((()=>{const e={...s,...I};delete e.posterImage,delete e.privacySetting,q(null),V(P),L(!1),t?.setVideo(e),F(!0)}))},handleDelete:()=>{D(!0)},isFetching:A,processing:k,isDeleting:R,updating:O,updated:M,deleted:T,selectedTime:H,...Q,...W,isUpdatingPoster:S}}},8575:(e,t,a)=>{"use strict";a.d(t,{X:()=>v});var n=a(9384),r=a(6427),o=a(7723),i=a(3751),s=a(9783),c=a(3883),l=a(1113),d=a(3022),u=a(1609),p=a.n(u),m=a(4870);const __=o.__,h=e=>{switch(e){case"error":case"warning":default:return i.A;case"info":return s.A;case"success":return c.A}};function g({status:e="error",isDismissible:t=!1,className:a,children:n,actions:o,onRemove:i}){const s=(0,d.A)(a,m.A.notice,m.A[`is-${e}`]);return p().createElement(r.Notice,{status:e,isDismissible:t,onRemove:i,className:s,actions:o},p().createElement(l.A,{icon:h(e),className:m.A.icon}),p().createElement("div",{className:m.A.message},n))}const v=()=>{const{adminUri:e,registrationNonce:t}=window.jetpackVideoPressInitialState,{hasConnectedOwner:a,handleRegisterSite:r}=(0,n.useConnection)({redirectUri:e,from:"jetpack-videopress",registrationNonce:t});return a?null:p().createElement(g,{addConnectUserLink:!0,actions:[{label:__("Connect your user account to fix this","jetpack-videopress-pkg"),onClick:r,variant:"link",noDefaultClasses:!0}]},__("Some actions need a user connection to WordPress.com to be able to work","jetpack-videopress-pkg"))}},5333:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>y,DO:()=>f});var n=a(6072),r=a.n(n),o=a(7425),i=a(8478),s=a(6427),c=a(9491),l=a(7723),d=a(1113),u=a(1249),p=a(3022),m=a(1609),h=a(5341);const __=l.__,g=({className:e,disabled:t=!1,loading:a=!1,icon:n=null,endAdornment:o=null,onChange:i,onEnter:c,size:l="small",...d})=>{const u=(0,m.useCallback)((e=>{null!=i&&i(e.currentTarget.value)}),[i]),g=(0,m.useCallback)((e=>{null!=c&&["Enter","NumpadEnter"].includes(e.code)&&c(e.currentTarget.value)}),[c]),v={className:(0,p.A)(h.A.input,{[h.A["with-icon"]]:null!=n}),onChange:u,onKeyUp:g,disabled:t,"aria-disabled":t},f="textarea"===d?.type;return React.createElement("div",{className:(0,p.A)(e,h.A["input-wrapper"],{[h.A.disabled]:t,[h.A.large]:"large"===l,[h.A["is-textarea"]]:f})},f?React.createElement("textarea",r()({},d,v)):React.createElement(React.Fragment,null,a||n?React.createElement("div",{className:(0,p.A)(h.A["icon-wrapper"],{[h.A.loader]:a})},a?React.createElement(s.Spinner,null):n):null,React.createElement("input",r()({},d,v,{value:d.value})),o))},v=({name:e,label:t,className:a,size:n="small",...i})=>t?React.createElement("div",{className:a},React.createElement(o.Ay,{component:"label",variant:"small"===n?"body-small":"body",htmlFor:e,mb:1,className:h.A.label},t),React.createElement(g,r()({name:e,size:n},i))):React.createElement(g,r()({className:a,size:n},i)),f=({placeholder:e=__("Search your library","jetpack-videopress-pkg"),onSearch:t,wait:a=500,...n})=>{const o=(0,c.useDebounce)(t,a),s=(0,m.useCallback)((e=>{n.onEnter?.(e),t(e)}),[n.onEnter,t]),l=(0,m.useCallback)((e=>{n.onChange?.(e),o(e)}),[n.onChange]),g=(0,m.useCallback)((()=>{n.onChange?.(""),t("")}),[n.onChange]);return React.createElement(v,r()({},n,{icon:React.createElement(i.WI,{size:24}),placeholder:e,type:"text",onEnter:s,onChange:l,endAdornment:React.createElement(React.Fragment,null,Boolean(n.value)&&React.createElement("div",{className:(0,p.A)(h.A["icon-wrapper"])},React.createElement(d.A,{icon:u.A,onClick:g,className:(0,p.A)(h.A["clear-icon"])})))}))},y=v},8175:(e,t,a)=>{"use strict";a.d(t,{O6:()=>y,XL:()=>f});var n=a(6072),r=a.n(n),o=a(1112),i=a(7425),s=a(1113),c=a(8888),l=a(9115),d=a(3022),u=a(8728),p=a(957),m=a(3003);const h=(e,t)=>[...Array(t)].map(((t,a)=>a+e)),g=()=>React.createElement(o.A,{size:"small",className:(0,d.A)(m.A.button),variant:"tertiary",disabled:!0,"aria-disabled":!0},React.createElement(i.Ay,null,"...")),v=({className:e,currentPage:t=1,perPage:a,total:n,minColumns:r=7,disabled:i,onChangePage:u})=>{if(!n||!a)return null;const p=Math.ceil(n/a);if(t>p)return u(p),null;if(t<1)return u(1),null;const v=({page:e})=>{const a=e===t;return React.createElement(o.A,{size:"small",className:(0,d.A)(m.A.button,a?m.A.selected:null),variant:a?"primary":"tertiary",disabled:i,"aria-disabled":i,onClick:()=>u(e)},e)};let f,y=Math.max(r,7);if(y=y%2==0?y+1:y,p<=y)f=h(1,p).map((e=>React.createElement(v,{page:e,key:e})));else if(t<y-2)f=React.createElement(React.Fragment,null,h(1,y-2).map((e=>React.createElement(v,{page:e,key:e}))),React.createElement(g,null),React.createElement(v,{page:p}));else if(t>p-y+3)f=React.createElement(React.Fragment,null,React.createElement(v,{page:1}),React.createElement(g,null),h(p-y+3,y-2).map((e=>React.createElement(v,{page:e,key:e}))));else{const e=(y-5)/2;f=React.createElement(React.Fragment,null,h(1,e).map((e=>React.createElement(v,{page:e,key:e}))),React.createElement(g,null),h(t-1,3).map((e=>React.createElement(v,{page:e,key:e}))),React.createElement(g,null),h(p-e+1,e).map((e=>React.createElement(v,{page:e,key:e}))))}return React.createElement("div",{className:(0,d.A)(e,m.A.wrapper)},React.createElement(o.A,{size:"small",className:(0,d.A)(m.A.navigation,m.A.button),variant:"tertiary",disabled:i||1===t,"aria-disabled":i||1===t,onClick:()=>u(Math.max(1,t-1))},React.createElement(s.A,{icon:c.A})),f,React.createElement(o.A,{size:"small",className:(0,d.A)(m.A.navigation,m.A.button),variant:"tertiary",disabled:i||t===p,"aria-disabled":i||t===p,onClick:()=>u(Math.min(p,t+1))},React.createElement(s.A,{icon:l.A})))},f=e=>{const t=(0,u.o)(),{page:a,itemsPerPage:n,total:o,isFetching:i}=(0,p.Ay)();return o<=n?React.createElement("div",{className:(0,d.A)(e.className,m.A["pagination-placeholder"])}):React.createElement(v,r()({},e,{perPage:n,onChangePage:e=>{e>1?t.setParam("page",e):t.deleteParam("page"),t.update()},currentPage:a,total:o,disabled:i||e.disabled}))},y=e=>{const{setPage:t,page:a,itemsPerPage:n,total:o,isFetching:i}=(0,p.Ds)();return o<n?React.createElement("div",{className:(0,d.A)(e.className,m.A["pagination-placeholder"])}):React.createElement(v,r()({},e,{perPage:n,onChangePage:t,currentPage:a,total:o,disabled:i||e.disabled}))}},2301:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var n=a(9245),r=a(489),o=a(1112),i=a(9384),s=a(7723),c=a(1609),l=a(7520);const __=s.__,d=({onRedirecting:e})=>{const{siteSuffix:t,adminUri:a,registrationNonce:d}=window.jetpackVideoPressInitialState,{siteProduct:u,productPrice:p}=(0,l.j)(),{yearly:m}=p,{handleRegisterSite:h,userIsConnecting:g}=(0,i.useConnection)({redirectUri:a,from:"jetpack-videopress",registrationNonce:d}),[v,f]=(0,c.useState)(!1),{run:y,hasCheckoutStarted:b}=(0,i.useProductCheckoutWorkflow)({siteSuffix:t,productSlug:m?.slug,redirectUrl:a}),w=u.features.map((e=>({name:e})));return React.createElement(n.Ay,{title:u.description,items:w},React.createElement(n.N0,{primary:!0},React.createElement(n.i7,null,React.createElement(r.A,{offPrice:m?.discount?m.salePriceByMonth:null,price:m.priceByMonth,promoLabel:m?.discount?(0,s.sprintf)(/* translators: placeholder is the number of videos */
__("%1$s%% off","jetpack-videopress-pkg"),m.discount):null,legend:__("/month, billed yearly","jetpack-videopress-pkg"),currency:m.currency}),React.createElement(o.A,{onClick:()=>{e?.(),y()},isLoading:b,fullWidth:!0,disabled:v||b||g},__("Get VideoPress","jetpack-videopress-pkg"))),React.createElement(n.eY,{isIncluded:!0}),React.createElement(n.eY,{isIncluded:!0}),React.createElement(n.eY,{isIncluded:!0}),React.createElement(n.eY,{isIncluded:!0})),React.createElement(n.N0,null,React.createElement(n.i7,null,React.createElement(r.A,{price:0,legend:"",currency:m.currency,hidePriceFraction:!0}),React.createElement(o.A,{fullWidth:!0,variant:"secondary",onClick:()=>{f(!0),h(),e?.()},isLoading:g||v,disabled:g||v||b},__("Start for free","jetpack-videopress-pkg"))),React.createElement(n.eY,{isIncluded:!1,label:React.createElement("strong",null,__("Upload one video","jetpack-videopress-pkg"))}),React.createElement(n.eY,{isIncluded:!0}),React.createElement(n.eY,{isIncluded:!1}),React.createElement(n.eY,{isIncluded:!1})))}},7663:(e,t,a)=>{"use strict";a.d(t,{A:()=>p});var n=a(1330),r=a(7425),o=a(7143),i=a(7723),s=a(3832),c=a(6287),l=a(7882),d=a(957),u=a(662);const __=i.__,p=({id:e,position:t=null,anchor:a=null})=>{const i=(0,o.useDispatch)(c.ax),{data:p}=(0,l.A)(Number(e)),{firstUploadedVideoId:m,firstVideoProcessed:h,dismissedFirstVideoPopover:g}=(0,d.Ay)(),v=Number(m)===Number(e)&&h&&!g,f=()=>i.dismissFirstVideoPopover(),y=window.jetpackVideoPressInitialState?.contentNonce??"",b=(0,s.addQueryArgs)("post-new.php",{videopress_guid:p.guid,_wpnonce:y});return v&&React.createElement(n.A,{title:__("Publish your new video","jetpack-videopress-pkg"),buttonContent:__("Add video to post","jetpack-videopress-pkg"),buttonHref:b,buttonExternalLink:!0,anchor:a,onClose:f,onClick:f,noArrow:!1,className:u.A["action-popover"],position:t},React.createElement(r.Ay,null,__("Now that your video has been uploaded to VideoPress, it's time to show it to the world.","jetpack-videopress-pkg")))}},8031:(e,t,a)=>{"use strict";a.d(t,{Aq:()=>r,Xb:()=>n});const n="atomic",r="jetpack"},3322:(e,t,a)=>{"use strict";a.d(t,{M:()=>p});var n=a(5918),r=a(8509),o=a(7425),i=a(7723),s=a(9072),c=a(9177),l=a(1755),d=a(8031);const __=i.__,u=({videoPressVideosPrivateForSite:e,siteIsPrivate:t,siteType:a,onPrivacyChange:i})=>{const{canPerformAction:c}=(0,s.J)(),u=t&&a===d.Xb,p=!c||u,m=u?__("You cannot change this setting because your site is private. You can only choose the video privacy default on public sites.","jetpack-videopress-pkg"):null;return React.createElement(n.A,{horizontalSpacing:0,horizontalGap:0},React.createElement(r.A,null,React.createElement(o.Ay,{variant:"headline-small",mb:1},__("Settings","jetpack-videopress-pkg"))),React.createElement(r.A,{sm:12,md:12,lg:12},React.createElement(l.R0,{for:"settings-site-privacy",label:__("Video Privacy: Restrict views to members of this site","jetpack-videopress-pkg"),onChange:i,checked:e,disabled:p,disabledReason:m})))},p=()=>{const{settings:e,onUpdate:t}=(0,c.T)(),{videoPressVideosPrivateForSite:a,siteIsPrivate:n,siteType:r}=e;return React.createElement(u,{videoPressVideosPrivateForSite:a,siteIsPrivate:n,siteType:r,onPrivacyChange:e=>{t({videoPressVideosPrivateForSite:e})}})}},1497:(e,t,a)=>{"use strict";a.d(t,{A:()=>E});var n=a(6072),r=a.n(n),o=a(442),i=a(3924),s=a(7425),c=a(1330),l=a(1112),d=a(7143),u=a(7723),p=a(1113),m=a(8248),h=a(4969),g=a(6673),v=a(3022),f=a(1609),y=a(6287),b=a(5419),w=a(8727);const __=u.__,E=({title:e,id:t})=>{const{dismissErroredVideo:a}=(0,d.useDispatch)(y.ax),n=!e,[u,E]=(0,f.useState)(null),[A]=(0,o.A)("sm"),[k,R]=(0,f.useState)(!1),[C,S]=(0,f.useState)(!1),_=()=>a(t),x=()=>S(!0),P=(0,i.A)("jetpack-videopress-dashboard-troubleshoot"),j=()=>S(!1);return React.createElement(React.Fragment,null,React.createElement("div",r()({className:(0,v.A)(w.A["video-card__wrapper"],{[w.A["is-blank"]]:n,[w.A.disabled]:A})},A&&{onClick:()=>R((e=>!e))}),!A&&React.createElement("div",{className:w.A["video-card__background"]}),React.createElement(b.Ay,{className:w.A["video-card__thumbnail"],ref:E,hasError:!0}),React.createElement("div",{className:w.A["video-card__title-section"]},A&&React.createElement("div",{className:w.A.chevron},k&&React.createElement(p.A,{icon:m.A}),!k&&React.createElement(p.A,{icon:h.A})),React.createElement(s.hE,{className:w.A["video-card__title"],mb:0,size:"small"},e)),C&&React.createElement(c.A,{title:__("Error","jetpack-videopress-pkg"),buttonContent:__("Visit the docs","jetpack-videopress-pkg"),buttonHref:P,buttonExternalLink:!0,anchor:u,onClose:j,onClick:j,noArrow:!1,className:w.A["action-popover"]},React.createElement(s.Ay,null,__("There's been an error uploading your video. Try uploading the video again, if the error persists, visit our documentation to troubleshoot the issue or contact support.","jetpack-videopress-pkg"))),!A&&React.createElement("div",{className:(0,v.A)(w.A["video-card__quick-actions-section"],w.A["is-blank"])},React.createElement(l.A,{variant:"primary",size:"small",onClick:x},__("Upload Error!","jetpack-videopress-pkg")),React.createElement(l.A,{size:"small",variant:"tertiary",icon:g.A,onClick:_}))),A&&k&&React.createElement("div",{className:(0,v.A)(w.A["video-card__quick-actions-section"],w.A.small)},React.createElement(l.A,{variant:"primary",size:"small",onClick:x},__("Upload Error!","jetpack-videopress-pkg")),React.createElement(l.A,{size:"small",variant:"tertiary",icon:g.A,onClick:_})))}},6411:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>C});var n=a(6072),r=a.n(n),o=a(1112),i=a(356),s=a(442),c=a(1876),l=a(7425),d=a(7723),u=a(1113),p=a(8248),m=a(4969),h=a(7267),g=a(3022),v=a(1609),f=a(9072),y=a(7882),b=a(7663),w=a(8596),E=a(5419),A=a(8727);const __=d.__,k=({id:e,onVideoDetailsClick:t,className:a})=>{const{canPerformAction:n}=(0,f.J)();return React.createElement("div",{className:(0,g.A)(A.A["video-card__quick-actions-section"],a)},React.createElement(o.A,{variant:"primary",size:"small",onClick:t,className:A.A["video-card__quick-actions__edit-button"],disabled:!n},__("Edit video details","jetpack-videopress-pkg")),e&&React.createElement(w.W,{videoId:e}))},R=({title:e,id:t,duration:a,plays:n,thumbnail:o,editable:f,showQuickActions:y=!0,loading:w=!1,isUpdatingPoster:R=!1,uploading:C=!1,processing:S=!1,uploadProgress:_,onVideoDetailsClick:x})=>{const P=!(e||a||n||o||w),j=void 0!==n,N=j?(0,d.sprintf)(/* translators: placeholder is a number of plays */
__("%s plays","jetpack-videopress-pkg"),(0,i.A)(n)):"",[V,z]=(0,v.useState)(null),[U]=(0,s.A)("sm"),[O,L]=(0,v.useState)(!1),M=w||C;return React.createElement(React.Fragment,null,React.createElement("div",r()({className:(0,g.A)(A.A["video-card__wrapper"],{[A.A["is-blank"]]:P,[A.A.disabled]:U||M})},U&&!M&&{onClick:()=>L((e=>!e))}),!U&&React.createElement("div",{className:A.A["video-card__background"]}),React.createElement(E.Ay,{className:A.A["video-card__thumbnail"],thumbnail:o,loading:w||R,uploading:C,processing:S,duration:w?null:a,editable:!w&&f,uploadProgress:_,ref:z}),React.createElement("div",{className:A.A["video-card__title-section"]},U&&!M&&React.createElement("div",{className:A.A.chevron},O&&React.createElement(u.A,{icon:p.A}),!O&&React.createElement(u.A,{icon:m.A})),w?React.createElement(c.A,{width:"60%",height:30}):React.createElement(l.hE,{className:A.A["video-card__title"],mb:0,size:"small"},e),w?React.createElement(c.A,{width:96,height:24}):React.createElement(React.Fragment,null,j&&React.createElement(l.Ay,{weight:"regular",size:"small",component:"div",className:A.A["video-card__video-plays-counter"]},React.createElement(u.A,{icon:h.A}),N))),React.createElement(b.A,{id:t,anchor:V}),y&&!U&&React.createElement(k,{id:t,onVideoDetailsClick:x,className:(0,g.A)({[A.A["is-blank"]]:w})})),y&&U&&O&&React.createElement(k,{id:t,onVideoDetailsClick:x,className:A.A.small}))},C=({id:e,...t})=>{const{isDeleting:a,uploading:n,processing:o,isUpdatingPoster:i,data:s,uploadProgress:c}=(0,y.A)(e),l=(a||t?.loading)&&!n&&!o,d=t?.editable&&!a&&!n&&!o;return React.createElement(R,r()({id:e},t,{loading:l,uploading:n,isUpdatingPoster:i,processing:o,editable:d,privacySetting:s.privacySetting,uploadProgress:c}))}},3287:(e,t,a)=>{"use strict";a.d(t,{A:()=>v});var n=a(1112),r=a(723),o=a(6427),i=a(7723),s=a(1597),c=a(1679),l=a(6307),d=a(6673),u=a(3832),p=a(1609),m=a(7882),h=a(3378),g=a(9907);const __=i.__,v=({disabled:e=!1,videoId:t,onDelete:a})=>{const[i,v]=(0,p.useState)(!1),{data:{guid:f,url:y},deleteVideo:b}=(0,m.A)(t),w=window.jetpackVideoPressInitialState?.contentNonce??"",E=(0,u.addQueryArgs)("post-new.php",{videopress_guid:f,_wpnonce:w}),A=(0,p.useCallback)((async()=>{v(!1),await b(),a()}),[b,a]);return React.createElement(React.Fragment,null,React.createElement(o.Dropdown,{placement:"bottom center",renderToggle:({isOpen:t,onToggle:a})=>React.createElement(n.A,{variant:"tertiary",disabled:e,icon:s.A,onClick:a,"aria-expanded":t}),renderContent:({onClose:t})=>React.createElement(r.Ay,null,React.createElement("div",{className:g.A.dropdown},React.createElement(n.A,{weight:"regular",fullWidth:!0,variant:"tertiary",icon:c.A,href:E,target:"_blank",disabled:e,onClick:t},__("Add to new post","jetpack-videopress-pkg")),React.createElement(n.A,{weight:"regular",fullWidth:!0,variant:"tertiary",icon:l.A,href:y,target:"_blank",disabled:e,onClick:t},__("Download file","jetpack-videopress-pkg")),React.createElement("hr",{className:g.A.separator}),React.createElement(n.A,{weight:"regular",fullWidth:!0,variant:"tertiary",icon:d.A,className:g.A.delete,disabled:e,onClick:()=>{v(!0),t()}},__("Delete video","jetpack-videopress-pkg"))))}),i&&React.createElement(h.A,{onClose:()=>v(!1),onDelete:A}))}},4565:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var n=a(7425),r=a(1876),o=a(8443),i=a(7723),s=a(9689),c=a(6955),l=a(7722);const __=i.__,d=({filename:e,uploadDate:t,shortcode:a,loading:i=!1,guid:d,isPrivate:u})=>{const p=!!t?.length&&(0,o.gmdateI18n)("F j, Y",t),m=(0,s.rB)(d,u);return React.createElement("div",{className:l.A.details},React.createElement("div",null,React.createElement(n.Ay,{variant:"body-small"},__("Link to video","jetpack-videopress-pkg")),i?React.createElement(r.A,{height:36}):React.createElement(c.A,{value:m})),React.createElement("div",null,React.createElement(n.Ay,{variant:"body-small"},__("WordPress shortcode","jetpack-videopress-pkg")),i?React.createElement(r.A,{height:36}):React.createElement(c.A,{value:a})),React.createElement("div",null,React.createElement(n.Ay,{variant:"body-small"},__("File name","jetpack-videopress-pkg")),i?React.createElement(r.A,{height:24}):React.createElement(n.Ay,{className:l.A.filename},e)),React.createElement("div",null,React.createElement(n.Ay,{variant:"body-small"},__("Upload date","jetpack-videopress-pkg")),i?React.createElement(r.A,{height:24}):React.createElement(n.Ay,null,p)))}},1755:(e,t,a)=>{"use strict";a.d(t,{Fr:()=>A,M2:()=>S,R0:()=>R});var n=a(6072),r=a.n(n),o=a(1112),i=a(7425),s=a(442),c=a(5918),l=a(8509),d=a(6427),u=a(7723),p=a(1113),m=a(9783),h=a(3022),g=a(6639),v=a(8962),f=a(8728),y=a(2807),b=a(957),w=a(8876),E=a(6455);const __=u.__,A=e=>{const{isActive:t,...a}=e;return React.createElement(o.A,r()({variant:t?"primary":"secondary",className:(0,h.A)(E.A["filter-button"],{[E.A["is-active"]]:t}),icon:g.A,weight:"regular"},a),__("Filters","jetpack-videopress-pkg"))},k=e=>React.createElement(d.Tooltip,{position:"middle center",text:e.message},React.createElement("span",{className:E.A["title-adornment"]},React.createElement(p.A,{icon:m.A}))),R=e=>React.createElement(w.A,{id:e.for,htmlFor:e.for,className:E.A.checkbox,onChange:e.onChange,checked:e.checked,disabled:e.disabled},React.createElement(i.Ay,{variant:"body-small"},e.label),e.disabledReason&&React.createElement(k,{message:e.disabledReason})),C=e=>{const[t]=(0,s.A)("sm"),a=(t,a)=>!0===e?.filter?.[t]?.[a];return React.createElement("div",{className:(0,h.A)(E.A["filters-section"],e.className)},React.createElement(c.A,{horizontalSpacing:t?2:4,horizontalGap:2},React.createElement(l.A,{sm:4,md:4,lg:4},React.createElement(i.Ay,{variant:"body-extra-small-bold",weight:"bold"},__("Uploader","jetpack-videopress-pkg")),e.uploaders.map((t=>React.createElement(R,{key:t.id,label:t.name,for:`uploader-${t.id}`,onChange:a=>e.onChange?.(v.uX,t.id,a),checked:a(v.uX,t.id)})))),React.createElement(l.A,{sm:4,md:4,lg:4},React.createElement(i.Ay,{variant:"body-extra-small-bold",weight:"bold"},__("Privacy","jetpack-videopress-pkg")),React.createElement(R,{for:"filter-public",label:__("Public","jetpack-videopress-pkg"),onChange:t=>e.onChange?.(v.AV,v.zG.indexOf(v.az),t),checked:a(v.AV,v.zG.indexOf(v.az))}),React.createElement(R,{for:"filter-private",label:__("Private","jetpack-videopress-pkg"),onChange:t=>e.onChange?.(v.AV,v.zG.indexOf(v.o0),t),checked:a(v.AV,v.zG.indexOf(v.o0))})),React.createElement(l.A,{sm:4,md:4,lg:4},React.createElement(i.Ay,{variant:"body-extra-small-bold",weight:"bold"},__("Rating","jetpack-videopress-pkg")),React.createElement(R,{for:"filter-g",label:__("G","jetpack-videopress-pkg"),onChange:t=>e.onChange?.(v.VI,v.ko,t),checked:a(v.VI,v.ko)}),React.createElement(R,{for:"filter-pg-13",label:__("PG-13","jetpack-videopress-pkg"),onChange:t=>e.onChange?.(v.VI,v.z7,t),checked:a(v.VI,v.z7)}),React.createElement(R,{for:"filter-r",label:__("R","jetpack-videopress-pkg"),onChange:t=>e.onChange?.(v.VI,v.iL,t),checked:a(v.VI,v.iL)}))))},S=e=>{const{setFilter:t,filter:a}=(0,b.Ay)(),n=(0,f.o)(),{items:o}=(0,y.A)();return React.createElement(C,r()({},e,{onChange:(...e)=>{n.deleteParam("page"),n.update(),t(...e)},uploaders:o,filter:a}))}},6145:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var n=a(5918),r=a(8509),o=a(6411),i=a(1497),s=a(4761);const c=({videos:e,count:t=6,onVideoDetailsClick:a,loading:c})=>{const l=e.slice(0,t),d=(t,a)=>()=>{a?.(e[t])};return React.createElement("div",{className:s.A.wrapper},React.createElement(n.A,{fluid:!0,horizontalSpacing:0,horizontalGap:0},l.map(((e,t)=>React.createElement(r.A,{key:e?.guid??e?.id,sm:4,md:4,lg:4},e.error?React.createElement(i.A,{id:e?.id,title:e.title}):React.createElement(o.Ay,{id:e?.id,title:e.title,thumbnail:e?.posterImage,duration:e.duration,plays:e.plays,onVideoDetailsClick:d(t,a),loading:c}))))))}},3793:(e,t,a)=>{"use strict";a.d(t,{A:()=>b,R:()=>y});var n=a(442),r=a(7425),o=a(6427),i=a(7723),s=a(1113),c=a(9783),l=a(3751),d=a(3022),u=a(1609),p=a(8962),m=a(7520),h=a(957),g=(a(8876),a(9983)),v=a(8533),f=a(6217);const __=i.__,y=({videos:e,showActionButton:t=!0,showQuickActions:a=!1,uploading:i=!1,onActionClick:v})=>{const[y,b]=(0,u.useState)([]),[w]=(0,n.A)("sm"),E=!1,{hasVideoPressPurchase:A}=(0,m.j)(),{uploadedVideoCount:k,isFetching:R}=(0,h.Ay)(),C=k>0||R||i?.length>0,S=t=>()=>{v?.(e[t])},_=e=>{if(e?.isUploadedToVideoPress)return React.createElement(o.Tooltip,{position:"top center",text:__("Video already uploaded to VideoPress","jetpack-videopress-pkg")},React.createElement("div",{className:f.A["title-adornment"]},React.createElement(s.A,{icon:c.A})));if(null!=e?.readError){const t=__("Video cannot be read","jetpack-videopress-pkg"),a=__("Video has an unsupported file type","jetpack-videopress-pkg");return React.createElement(o.Tooltip,{position:"top center",text:e?.readError===p.cl?a:t},React.createElement("div",{className:(0,d.A)(f.A["title-adornment"],f.A.error)},React.createElement(s.A,{icon:l.A})))}return null};return React.createElement("div",{className:f.A.list},React.createElement("div",{className:f.A.header},React.createElement("div",{className:f.A["title-wrapper"]},E,React.createElement(r.Ay,null,__("Title","jetpack-videopress-pkg"))),!w&&React.createElement("div",{className:f.A["data-wrapper"]},React.createElement(g.Uz,{privacy:"",duration:"",plays:"",upload:__("Upload date","jetpack-videopress-pkg")}))),e.map(((e,n)=>e?.id?React.createElement(g.TX,{key:`local-video-${e.id}`,id:e.id,title:e.title,showActionButton:t,showQuickActions:a,showCheckbox:E,uploadDate:e.uploadDate,onActionClick:S(n),actionButtonLabel:__("Upload to VideoPress","jetpack-videopress-pkg"),disabled:e?.isUploadedToVideoPress||null!=e?.readError,disableActionButton:C&&!A||i,titleAdornment:_(e)}):null)))},b=({videos:e,hidePrivacy:t=!1,hideDuration:a=!1,hidePlays:o=!1,showActionButton:i=!0,showQuickActions:s=!0,loading:c=!1,onVideoDetailsClick:l})=>{const[d,m]=(0,u.useState)([]),[h]=(0,n.A)("sm"),y=!1,b=(t,a)=>()=>{a?.(e[t])};return React.createElement("div",{className:f.A.list},React.createElement("div",{className:f.A.header},React.createElement("div",{className:f.A["title-wrapper"]},y,React.createElement(r.Ay,null,__("Title","jetpack-videopress-pkg"))),!h&&React.createElement("div",{className:f.A["data-wrapper"]},React.createElement(g.Uz,{privacy:t?null:__("Privacy","jetpack-videopress-pkg"),duration:a?null:__("Duration","jetpack-videopress-pkg"),plays:o?null:__("Plays","jetpack-videopress-pkg"),upload:__("Upload date","jetpack-videopress-pkg")}))),e.map(((e,n)=>{const r=p.zG[e.privacySetting]===p.o0;return e.error?React.createElement(v.A,{key:e?.guid??e?.id,id:e?.id,title:e?.title}):React.createElement(g.Ay,{key:e?.guid??e?.id,id:e?.id,checked:d.includes(n),title:e.title,thumbnail:e?.posterImage,duration:a?null:e.duration,plays:o?null:e.plays,isPrivate:t?null:r,uploadDate:e.uploadDate,showQuickActions:!e?.uploading&&s,showActionButton:!e?.uploading&&i,showCheckbox:y,className:f.A.row,onActionClick:b(n,l),loading:c,onSelect:e=>m((t=>e?[...t,n]:t.filter((e=>e!==n))))})})))}},8596:(e,t,a)=>{"use strict";a.d(t,{W:()=>V});var n=a(6072),r=a.n(n),o=a(723),i=a(7425),s=a(1112),c=a(6427),l=a(7723),d=a(5938),u=a(4066),p=a(6673),m=a(3022),h=a(1609),g=a(7726),v=a(7121),f=a(8962),y=a(6791),b=a(9072),w=a(8326),E=a(7750),A=a(7882),k=a(3378),R=a(5419),C=a(4231),S=a(9284);const __=l.__,_=({showPopover:e=!1,isAnchorFocused:t=!1,anchor:a,children:n=null})=>{if((0,h.useEffect)((()=>{e&&!t&&a?.querySelector(".components-popover")?.focus()}),[e]),!a||!e)return null;const s={anchor:a,offset:15};return React.createElement(c.Popover,r()({position:"top center",noArrow:!0,focusOnMount:!1},s),React.createElement(o.Ay,null,React.createElement(i.Ay,{variant:"body-small",className:S.A.popover},n)))},x=({icon:e,children:t,className:a,...n})=>{const{setAnchor:o,setIsFocused:i,setIsHovering:c,anchor:l,isFocused:d,showPopover:u}=(0,y.V)();return React.createElement("div",{ref:o,className:a},React.createElement(s.A,r()({size:"small",variant:"tertiary",icon:e,onMouseEnter:()=>c(!0),onMouseLeave:()=>c(!1),onFocus:()=>i(!0),onBlur:()=>i(!1),disabled:n.disabled},n)),React.createElement(_,{showPopover:u,anchor:l,isAnchorFocused:d},t))},P=({description:e,onUpdate:t,isUpdatingPoster:a,disabled:n})=>{const{setAnchor:r,setIsFocused:i,setIsHovering:l,setShowPopover:u,anchor:p,isFocused:m,showPopover:h}=(0,y.V)();return React.createElement(c.Dropdown,{placement:"bottom left",renderToggle:({isOpen:t,onToggle:a})=>React.createElement("div",{ref:r},React.createElement(s.A,{size:"small",variant:"tertiary",icon:d.A,onClick:()=>{u(!1),a()},"aria-expanded":t,onMouseEnter:()=>l(!0),onMouseLeave:()=>l(!1),onFocus:()=>i(!0),onBlur:()=>i(!1),disabled:n}),React.createElement(_,{showPopover:h&&!t,anchor:p,isAnchorFocused:m},e)),renderContent:({onClose:e})=>React.createElement(o.Ay,null,React.createElement(R.sy,{isUpdatingPoster:a,onClose:e,onUseDefaultThumbnail:()=>t("default"),onSelectFromVideo:()=>t("select-from-video"),onUploadImage:()=>t("upload-image")}))})},j=({description:e,privacySetting:t,isUpdatingPrivacy:a,onUpdate:n,disabled:r})=>{const{setAnchor:i,setIsFocused:l,setIsHovering:d,setShowPopover:p,anchor:m,isFocused:h,showPopover:b}=(0,y.V)();let w=u.A;return f.zG[t]===f.o0?w=g.A:f.zG[t]===f.az&&(w=v.A),React.createElement(c.Dropdown,{placement:"bottom left",renderToggle:({isOpen:t,onToggle:n})=>React.createElement("div",{ref:i},React.createElement(s.A,{size:"small",variant:"tertiary",icon:w,onClick:()=>{p(!1),n()},"aria-expanded":t,onMouseEnter:()=>d(!0),onMouseLeave:()=>d(!1),onFocus:()=>l(!0),onBlur:()=>l(!1),disabled:r||a}),React.createElement(_,{showPopover:b&&!t,anchor:m,isAnchorFocused:h},e)),renderContent:({onClose:e})=>React.createElement(o.Ay,null,React.createElement("div",{className:S.A["dropdown-content"]},React.createElement(s.A,{weight:"regular",fullWidth:!0,variant:"tertiary",icon:u.A,onClick:()=>{e(),n("site-default")},disabled:f.zG[t]===f.CR},__("Site default","jetpack-videopress-pkg")),React.createElement(s.A,{weight:"regular",fullWidth:!0,variant:"tertiary",icon:v.A,onClick:()=>{e(),n("public")},disabled:f.zG[t]===f.az},__("Public","jetpack-videopress-pkg")),React.createElement(s.A,{weight:"regular",fullWidth:!0,variant:"tertiary",icon:g.A,onClick:()=>{e(),n("private")},disabled:f.zG[t]===f.o0},__("Private","jetpack-videopress-pkg"))))})},N=({className:e,privacySetting:t,isUpdatingPrivacy:a,isUpdatingPoster:n,onUpdateVideoThumbnail:r,onUpdateVideoPrivacy:o,onDeleteVideo:i})=>{const{canPerformAction:s}=(0,b.J)();return React.createElement("div",{className:(0,m.A)(S.A.actions,e)},React.createElement(P,{onUpdate:r,description:__("Update thumbnail","jetpack-videopress-pkg"),isUpdatingPoster:n,disabled:!s}),React.createElement(j,{onUpdate:o,privacySetting:t,isUpdatingPrivacy:a,description:__("Update privacy","jetpack-videopress-pkg"),disabled:!s}),React.createElement(x,{icon:p.A,className:S.A.trash,onClick:i,disabled:!s},__("Delete video","jetpack-videopress-pkg")))},V=e=>{const{videoId:t}=e;if(!Number.isFinite(t))return null;const{data:a,updateVideoPrivacy:n,deleteVideo:o,isUpdatingPrivacy:i,isUpdatingPoster:s}=(0,A.A)(t),{isFetchingPlaybackToken:c}=(0,w.A)(a),[l,d]=(0,h.useState)(!1),{frameSelectorIsOpen:u,handleCloseSelectFrame:p,handleOpenSelectFrame:m,handleVideoFrameSelected:g,selectedTime:v,handleConfirmFrame:f,updatePosterImageFromFrame:y,selectAndUpdatePosterImageFromLibrary:b}=(0,E.A)({video:a});if((0,h.useEffect)((()=>{null!=v&&y()}),[v]),l)return React.createElement(k.A,{onClose:()=>d(!1),onDelete:()=>{d(!1),o()}});if(u)return React.createElement(React.Fragment,null,React.createElement(C.A,{handleCloseSelectFrame:p,url:a.url,handleVideoFrameSelected:g,selectedTime:v,handleConfirmFrame:f}));const{privacySetting:R}=a;return React.createElement(N,r()({},e,{onUpdateVideoPrivacy:n,onUpdateVideoThumbnail:async e=>{switch(e){case"select-from-video":return m();case"upload-image":return b()}},onDeleteVideo:()=>d(!0),privacySetting:R,isUpdatingPrivacy:i||c,isUpdatingPoster:s}))}},8533:(e,t,a)=>{"use strict";a.d(t,{A:()=>w});var n=a(442),r=a(3924),o=a(7425),i=a(1112),s=a(1330),c=a(7143),l=a(8443),d=a(7723),u=a(1113),p=a(8248),m=a(4969),h=a(6673),g=a(3022),v=a(1609),f=a(6287),y=a(5419),b=a(299);const __=d.__,w=({id:e,className:t="",title:a})=>{const w=(0,v.useRef)(null),E=(0,v.useRef)(null),[A]=(0,n.A)("sm"),[k,R]=(0,v.useState)(!1),[C,S]=(0,v.useState)(!1),[_,x]=(0,v.useState)(null),[P,j]=(0,v.useState)(!1),{dismissErroredVideo:N}=(0,c.useDispatch)(f.ax),V=(0,l.dateI18n)("M j, Y",new Date,null),z=!A&&w?.current?.offsetWidth<w?.current?.scrollWidth,U=!A||A&&C,O=(0,d.sprintf)(/* translators: 1 Video title, 2 Video duration, 3 Video upload date */
__("Video Upload Error: Video upload, titled %1$s, failed. Please try again or visit the troubleshooting docs at %2$s.","jetpack-videopress-pkg"),a,(0,r.A)("jetpack-videopress-dashboard-troubleshoot")),L=(0,r.A)("jetpack-videopress-dashboard-troubleshoot"),M=e=>"Space"===e||"Enter"===e,F=()=>{j(!0)},T=()=>{j(!1)},D=e=>t=>{t.stopPropagation(),e?.(t)},I=e=>{e.target!==E.current&&E?.current?.click()},B=()=>N(e);return React.createElement("div",{role:"button",tabIndex:0,onKeyDown:A?null:e=>{M(e?.code)&&R(!0)},onKeyUp:A?null:e=>{M(e?.code)&&(R(!1),I(e))},onClick:A?null:I,"aria-label":O,className:(0,g.A)(b.A["video-row"],{[b.A.pressed]:k,[b.A["hover-disabled"]]:A},t),ref:x},React.createElement("div",{className:(0,g.A)(b.A["video-data-wrapper"],{[b.A.small]:A})},React.createElement("div",{className:(0,g.A)(b.A["info-wrapper"],{[b.A.small]:A}),onClick:A?e=>{A?S((e=>!e)):I(e)}:null,role:"presentation"},React.createElement("div",{className:b.A.poster},React.createElement(y.Ay,{isRow:!0,hasError:!0})),React.createElement("div",{className:b.A["title-wrapper"]},z&&React.createElement(o.Ay,{variant:"body-extra-small",className:b.A.label,component:"span"},a),React.createElement(o.Ay,{variant:"title-small",className:(0,g.A)(b.A.title),ref:w},a),A&&React.createElement(o.Ay,{component:"div"},V)),A&&React.createElement(u.A,{icon:C?p.A:m.A,size:45})),U&&React.createElement("div",{className:(0,g.A)(b.A["meta-wrapper"],{[b.A.small]:A})},!A&&React.createElement("div",{className:b.A.actions},React.createElement(i.A,{size:"small",onClick:D(F)},__("Upload Error!","jetpack-videopress-pkg")),React.createElement(i.A,{size:"small",variant:"tertiary",icon:h.A,onClick:B})),A&&React.createElement("div",{className:b.A["mobile-actions"]},React.createElement(i.A,{size:"small",onClick:D(F)},__("Upload Error!","jetpack-videopress-pkg")),React.createElement(i.A,{size:"small",variant:"tertiary",icon:h.A,onClick:B})))),P&&React.createElement(s.A,{title:__("Error","jetpack-videopress-pkg"),buttonContent:__("Visit the docs","jetpack-videopress-pkg"),buttonHref:L,buttonExternalLink:!0,anchor:_,onClose:T,onClick:T,noArrow:!1,className:b.A["action-popover"],position:"top center"},React.createElement(o.Ay,null,__("There's been an error uploading your video. Try uploading the video again, if the error persists, visit our documentation to troubleshoot the issue or contact support.","jetpack-videopress-pkg"))))}},9983:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>x,TX:()=>_,Uz:()=>k.A});var n=a(6072),r=a.n(n),o=a(442),i=a(1112),s=a(7425),c=a(1876),l=a(8443),d=a(7723),u=a(1113),p=a(8248),m=a(4969),h=a(3022),g=a(1609),v=a(7726),f=a(9072),y=a(7882),b=a(8876),w=a(7663),E=a(8596),A=a(5419),k=a(822),R=a(299);const __=d.__,C=({duration:e,uploadDate:t,plays:a,isPrivate:n,loading:r=!1})=>{const[i]=(0,o.A)("sm"),s=__("Duration","jetpack-videopress-pkg"),c=__("Plays","jetpack-videopress-pkg"),l=__("Privacy","jetpack-videopress-pkg"),d=__("Private","jetpack-videopress-pkg"),p=__("Public","jetpack-videopress-pkg"),m=i?React.createElement(React.Fragment,null,React.createElement("span",null,l),React.createElement("span",null,n?d:p)):n&&React.createElement(u.A,{icon:v.A}),g=i&&e?React.createElement(React.Fragment,null,React.createElement("span",null,s),React.createElement("span",null,e)):e,f=i&&Number.isFinite(a)?React.createElement(React.Fragment,null,React.createElement("span",null,c),React.createElement("span",null,a)):a,y=i?null:t;return React.createElement(k.A,{privacy:"boolean"==typeof n?m:null,duration:g,plays:f,upload:y,loading:r,className:(0,h.A)({[R.A["mobile-stats"]]:i})})},S=({id:e,className:t="",checked:a=!1,title:n,titleAdornment:r=null,thumbnail:v,showThumbnail:y=!1,duration:k,uploadDate:S,plays:_,isPrivate:x,onActionClick:P,onSelect:j,showActionButton:N=!0,showQuickActions:V=!0,showCheckbox:z=!0,loading:U=!1,uploading:O=!1,processing:L=!1,isUpdatingPoster:M=!1,actionButtonLabel:F=__("Edit video details","jetpack-videopress-pkg"),disableActionButton:T=!1,disabled:D=!1,uploadProgress:I,isLocalVideo:B=!1})=>{const H=(0,g.useRef)(null),q=(0,g.useRef)(null),{canPerformAction:G}=(0,f.J)(),[$]=(0,o.A)("sm"),[J,W]=(0,g.useState)(!1),[K,Z]=(0,g.useState)(!1),[Y,X]=(0,g.useState)(null),Q=(e=>{if(e){const t=Math.floor(e/6e4),a=Math.floor(e%6e4/1e3);return`${t}:${a<10?"0":""}${a}`}})(k),ee=(0,l.dateI18n)("M j, Y",S,null),te=!$&&H?.current?.offsetWidth<H?.current?.scrollWidth,ae=(N||V)&&!U&&!D,ne=!U&&(!$||$&&K),re=$&&ae&&!U&&(N||Boolean(k)||Number.isFinite(_)||"boolean"==typeof x),oe=$||U||D,ie=e=>"Space"===e||"Enter"===e,se=(0,d.sprintf)(/* translators: 1 Video title, 2 Video duration, 3 Video upload date */
__("Video: %1$s, Duration: %2$s, Upload Date: %3$s. Click to edit details.","jetpack-videopress-pkg"),n,Q,ee),ce=React.createElement(i.A,{size:"small",onClick:(le=P,e=>{e.stopPropagation(),le?.(e)}),disabled:!G||T},F);var le;const de=e=>{e.target!==q.current&&q?.current?.click()};return(0,g.useEffect)((()=>{D&&Z(!1)}),[D]),React.createElement("div",{role:"button",tabIndex:0,onKeyDown:$?null:e=>{ie(e?.code)&&W(!0)},onKeyUp:$?null:e=>{ie(e?.code)&&(W(!1),de(e))},"aria-label":se,className:(0,h.A)(R.A["video-row"],{[R.A.pressed]:J,[R.A.disabled]:D,[R.A["hover-disabled"]]:oe},t),ref:X},z&&React.createElement("div",{className:(0,h.A)({[R.A["checkbox-wrapper-small"]]:$})},React.createElement(b.A,{ref:q,checked:a&&!U,tabIndex:-1,onChange:j,disabled:U})),React.createElement("div",{className:(0,h.A)(R.A["video-data-wrapper"],{[R.A.small]:$})},React.createElement("div",{className:(0,h.A)(R.A["info-wrapper"],{[R.A.small]:$}),onClick:$&&!U?e=>{re?Z((e=>!e)):de(e)}:null,role:"presentation"},y&&React.createElement("div",{className:R.A.poster},React.createElement(A.Ay,{thumbnail:v,loading:U||M,uploading:O,processing:L,blankIconSize:28,uploadProgress:I,isRow:!0})),React.createElement("div",{className:R.A["title-wrapper"]},te&&React.createElement(s.Ay,{variant:"body-extra-small",className:R.A.label,component:"span"},n),U?React.createElement(c.A,{height:30}):React.createElement(s.Ay,{variant:"title-small",className:(0,h.A)(R.A.title,{[R.A.disabled]:D}),ref:H},n,r),$&&React.createElement(React.Fragment,null,U?React.createElement(c.A,{height:20,width:"80%"}):React.createElement(s.Ay,{component:"div"},ee))),re&&React.createElement(u.A,{icon:K?p.A:m.A,size:45})),ne&&React.createElement("div",{className:(0,h.A)(R.A["meta-wrapper"],{[R.A.small]:$})},!$&&ae&&React.createElement("div",{className:R.A.actions},N&&ce,V&&e&&React.createElement(E.W,{videoId:e})),React.createElement(C,{duration:Q,uploadDate:ee,plays:_,isPrivate:x,loading:U}),$&&ae&&React.createElement("div",{className:R.A["mobile-actions"]},N&&ce,V&&e&&React.createElement(E.W,{videoId:e})))),!B&&React.createElement(w.A,{id:e,anchor:Y,position:"top center"}))},_=e=>React.createElement(S,r()({isLocalVideo:!0},e)),x=({id:e,...t})=>{const{isDeleting:a,uploading:n,processing:o,isUpdatingPoster:i,data:s,uploadProgress:c}=(0,y.A)(e),l=(a||t?.loading)&&!n&&!o;return React.createElement(S,r()({id:e},t,{loading:l,uploading:n,isUpdatingPoster:i,processing:o,showThumbnail:!0,privacySetting:s.privacySetting,uploadProgress:c}))}},822:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var n=a(442),r=a(1876),o=a(7425),i=a(3022),s=a(299);const c=({privacy:e,duration:t,plays:a,upload:c,loading:l=!1,className:d})=>{const[u]=(0,n.A)("sm");return React.createElement("div",{className:(0,i.A)(d,s.A.stats,{[s.A.small]:u})},l?React.createElement(React.Fragment,null,React.createElement(r.A,{height:24}),React.createElement(r.A,{height:24}),React.createElement(r.A,{height:24}),React.createElement(r.A,{height:24,className:s.A.upload})):React.createElement(React.Fragment,null,Boolean(e)&&React.createElement(o.Ay,{"aria-disabled":u?"false":"true",component:"div"},e),null!=t&&React.createElement(o.Ay,{component:"div"},t),null!=a&&React.createElement(o.Ay,{component:"div"},a),Boolean(c)&&React.createElement(o.Ay,{className:s.A.upload,component:"div"},c)))}},7812:(e,t,a)=>{"use strict";a.d(t,{i:()=>v});var n=a(6072),r=a.n(n),o=a(7425),i=a(2989),s=a(7723),c=a(3022),l=a(7578),d=a(7520),u=a(9177),p=a(957),m=a(8031),h=a(7364);const __=s.__,g=({className:e,progressBarClassName:t,total:a,used:n})=>{if(!a||null==n)return null;const r=n/a,d=`${(100*r).toFixed()}%`,u=(0,l.O)(a,{base:10});return React.createElement("div",{className:(0,c.A)(e)},React.createElement(o.Ay,{className:(0,c.A)(h.A["percentage-description"])},(0,s.sprintf)(/* translators: %1$s is the storage percentage, from 0% to 100%, %2$s is the total storage. */
__("%1$s of %2$s of cloud video storage","jetpack-videopress-pkg"),d,u)),React.createElement(i.A,{className:(0,c.A)(h.A["progress-bar"],t),progress:r}))},v=e=>{const{storageUsed:t,uploadedVideoCount:a}=(0,p.Ay)(),{features:n}=(0,d.j)(),{settings:o}=(0,u.T)(),{siteType:i}=o;return i===m.Xb||n?.isVideoPressUnlimitedSupported?null:a?t?React.createElement(g,r()({},e,{used:t,total:1e12})):React.createElement(g,r()({},e,{used:0,total:1})):null}},4231:(e,t,a)=>{"use strict";a.d(t,{A:()=>p});var n=a(442),r=a(723),o=a(1112),i=a(6427),s=a(7723),c=a(3022),l=a(1609),d=a(6720),u=a(7059);const __=s.__,p=({url:e,selectedTime:t,handleCloseSelectFrame:a,handleVideoFrameSelected:s,handleConfirmFrame:p})=>{const[m,h]=(0,l.useState)(null),[g]=(0,n.A)("sm");return React.createElement(i.Modal,{title:__("Select thumbnail from video","jetpack-videopress-pkg"),onRequestClose:a,isDismissible:!1},React.createElement(r.Ay,{targetDom:m},React.createElement("div",{ref:h,className:(0,c.A)(u.A.selector,{[u.A["is-small"]]:g})},React.createElement(d.A,{src:e,onVideoFrameSelected:s,initialCurrentTime:t}),React.createElement("div",{className:u.A.actions},React.createElement(o.A,{variant:"secondary",onClick:a},__("Close","jetpack-videopress-pkg")),React.createElement(o.A,{variant:"primary",onClick:p},__("Select this frame","jetpack-videopress-pkg"))))))}},5419:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>x,sy:()=>A});var n=a(1112),r=a(723),o=a(2989),i=a(7425),s=a(442),c=a(1876),l=a(6427),d=a(8443),u=a(7723),p=a(5938),m=a(1679),h=a(7534),g=a(2687),v=a(1113),f=a(3751),y=a(8015),b=a(3022),w=a(1609),E=a(7959);const __=u.__,A=({onUseDefaultThumbnail:e,onSelectFromVideo:t,onUploadImage:a,onClose:r,isUpdatingPoster:o=!1})=>React.createElement(React.Fragment,null,React.createElement(n.A,{className:E.A.disabled,weight:"regular",fullWidth:!0,variant:"tertiary",icon:p.A,onClick:()=>{r(),e?.()}},__("Use default thumbnail","jetpack-videopress-pkg")),React.createElement(n.A,{weight:"regular",fullWidth:!0,variant:"tertiary",icon:m.A,onClick:()=>{r(),t?.()}},__("Select from video","jetpack-videopress-pkg")),React.createElement(n.A,{weight:"regular",fullWidth:!0,variant:"tertiary",icon:h.A,disabled:o,onClick:()=>{r(),a?.()}},__("Upload image","jetpack-videopress-pkg"))),k=({onUseDefaultThumbnail:e,onSelectFromVideo:t,onUploadImage:a,busy:o=!1})=>React.createElement("div",{className:E.A["video-thumbnail-edit"]},React.createElement(l.Dropdown,{placement:"bottom left",renderToggle:({isOpen:e,onToggle:t})=>React.createElement(n.A,{variant:"secondary",className:E.A["thumbnail__edit-button"],icon:g.A,disabled:o,onClick:t,"aria-expanded":e}),renderContent:({onClose:n})=>React.createElement(r.Ay,null,React.createElement(A,{onClose:n,onUseDefaultThumbnail:e,onSelectFromVideo:t,onUploadImage:a}))})),R=({uploadProgress:e=0,isRow:t=!1})=>{const a=__("Completing upload","jetpack-videopress-pkg"),n=__("Completing","jetpack-videopress-pkg"),r=t?n:a,s=`${Math.floor(100*e)}%`,c=(0,u.sprintf)(/* translators: placeholder is the upload percentage */
__("Uploading %s","jetpack-videopress-pkg"),s),l=1===e?r:c;return React.createElement("div",{className:(0,b.A)(E.A["custom-thumbnail"],{[E.A["is-row"]]:t})},React.createElement(o.A,{className:E.A["progress-bar"],size:"small",progress:e}),React.createElement(i.Ay,{variant:t?"body-extra-small":"body",className:E.A["upload-text"]},l))},C=({isRow:e=!1})=>React.createElement("div",{className:E.A["custom-thumbnail"]},React.createElement(i.Ay,{variant:e?"body-extra-small":"body",className:E.A.pulse},__("Processing","jetpack-videopress-pkg"))),S=({isRow:e})=>React.createElement("div",{className:(0,b.A)(E.A["thumbnail-blank"],E.A["thumbnail-error"])},React.createElement(v.A,{icon:f.A,size:e?48:96})),_=(0,w.forwardRef)((({className:e,thumbnail:t,duration:a,editable:n,blankIconSize:r=96,loading:o=!1,uploading:l=!1,processing:u=!1,deleting:p=!1,updating:m=!1,onUseDefaultThumbnail:h,onSelectFromVideo:g,onUploadImage:f,uploadProgress:w,isRow:A=!1,hasError:_=!1},x)=>{const[P]=(0,s.A)("sm"),j=o||l||p||m;let N=t;return N=o?React.createElement(c.A,null):N,N=l?React.createElement(R,{isRow:A,uploadProgress:w}):N,N=u?React.createElement(C,{isRow:A}):N,N=_?React.createElement(S,{isRow:A}):N,N="string"==typeof N&&""!==N?React.createElement("img",{src:N,alt:__("Video thumbnail","jetpack-videopress-pkg")}):N,N=N||React.createElement("div",{className:E.A["thumbnail-blank"]},React.createElement(v.A,{icon:y.A,size:r})),React.createElement("div",{className:(0,b.A)(e,E.A.thumbnail,{[E.A["is-small"]]:P}),ref:x},Boolean(N)&&n&&React.createElement(k,{onUseDefaultThumbnail:h,onSelectFromVideo:g,onUploadImage:f,busy:j}),Number.isFinite(a)&&React.createElement("div",{className:E.A["video-thumbnail-duration"]},React.createElement(i.Ay,{variant:"body-small",component:"div"},a>=36e5?(0,d.gmdateI18n)("H:i:s",new Date(a)):(0,d.gmdateI18n)("i:s",new Date(a)))),React.createElement("div",{className:E.A["thumbnail-placeholder"]},N))}));_.displayName="VideoThumbnail";const x=_},9668:(e,t,a)=>{"use strict";a.d(t,{A:()=>h});var n=a(442),r=a(7425),o=a(1112),i=a(7723),s=a(1113),c=a(7711),l=a(3022),d=a(7770),u=a(7520),p=a(3463),m=a(4900);const __=i.__,h=({className:e,onSelectFiles:t})=>{const[a]=(0,n.A)("sm"),{inputRef:i,handleFileInputChangeEvent:h}=(0,p.A)({onSelectFiles:t}),{hasVideoPressPurchase:g}=(0,u.j)();return React.createElement("div",{className:(0,l.A)(m.A.wrapper,e,{[m.A.small]:a})},React.createElement("input",{ref:i,type:"file",accept:d.Zd,className:(0,l.A)(m.A["file-input"]),onChange:h,multiple:g}),React.createElement(s.A,{icon:c.A,size:32,className:(0,l.A)(m.A.icon)}),React.createElement(r.Ay,{variant:"title-small"},__("Drag and drop your video here","jetpack-videopress-pkg")),React.createElement(o.A,{size:"small",variant:"secondary",className:(0,l.A)(m.A.button),onClick:()=>{i.current.click()}},__("Select file to upload","jetpack-videopress-pkg")))}},6791:(e,t,a)=>{"use strict";a.d(t,{V:()=>r});var n=a(1609);const r=()=>{const[e,t]=(0,n.useState)(null),[a,r]=(0,n.useState)(!1),[o,i]=(0,n.useState)(!1),[s,c]=(0,n.useState)(!1);return(0,n.useEffect)((()=>{r(!(!o&&!s))}),[o,s]),{setAnchor:t,setIsFocused:c,setIsHovering:i,setShowPopover:r,anchor:e,isFocused:s,isHovering:o,showPopover:a}}},6764:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n=a(372),r=a(9384),o=a(1609);const i=({pageViewEventName:e,pageViewEventProperties:t={}})=>{const{isUserConnected:a,isRegistered:i,userConnectionData:s}=(0,r.useConnection)(),{blogId:c}=s?.currentUser||{},{login:l,ID:d}=s?.currentUser?.wpcomUser||{},{tracks:u}=n.A,{recordEvent:p}=u,m=(0,o.useCallback)((async(e,t)=>{p(e,t)}),[p]),h=(0,o.useCallback)(((e,t,a=()=>({}))=>(a="function"==typeof t?t:a,t="function"==typeof t?{}:t,c&&(t.blog_id=c),()=>m(e,t).then(a))),[m,c]);return(0,o.useEffect)((()=>{a&&d&&l&&n.A.initialize(d,l)}),[a,d,l]),(0,o.useEffect)((()=>{i&&e&&(c&&(t.blog_id=c),p(e,t))}),[c]),{recordEvent:m,recordEventHandler:h}}},9072:(e,t,a)=>{"use strict";a.d(t,{J:()=>r});var n=a(9384);const r=()=>{const{isRegistered:e,hasConnectedOwner:t,isUserConnected:a}=(0,n.useConnection)();return{isRegistered:e,hasConnectedOwner:t,isUserConnected:a,canPerformAction:e&&t&&a}}},7520:(e,t,a)=>{"use strict";a.d(t,{j:()=>d});var n=a(7143),r=a(6287),o=a(4523);const{paidFeatures:i={},siteProductData:s={},productData:c={},productPrice:l={}}=window&&window.jetpackVideoPressInitialState?window.jetpackVideoPressInitialState:{},d=()=>{const e=(0,o.x)(s.pricing_for_ui,!0),t=(0,o.x)(c.introductory_offer,!0),a={...(0,o.x)(c,!0),introductoryOffer:t},{purchases:d,isFetchingPurchases:u}=(0,n.useSelect)((e=>({purchases:e(r.ax).getPurchases(),isFetchingPurchases:e(r.ax).isFetchingPurchases()})),[]),p=d.map((e=>(0,o.x)(e,!0)));const m=["jetpack_videopress_bi_yearly","jetpack_videopress","jetpack_videopress_monthly","jetpack_complete_bi_yearly","jetpack_complete","jetpack_complete_monthly","jetpack_business","jetpack_business_monthly","jetpack_personal","jetpack_personal_monthly","jetpack_premium","jetpack_premium_monthly","videopress","videopress-pro","wp_p2_plus_monthly","bundle_pro","value_bundle","value_bundle_monthly","value_bundle-2y","value_bundle-3y","pro-plan","pro-plan-monthly","pro-plan-2y","business-bundle","business-bundle-monthly","business-bundle-2y","business-bundle-3y","wp_com_hundred_year_bundle_centennially","wp_bundle_migration_trial_monthly","wp_bundle_hosting_trial_monthly","ecommerce-bundle","ecommerce-bundle-monthly","ecommerce-bundle-2y","ecommerce-bundle-3y","ecommerce-trial-bundle-monthly","wooexpress-small-bundle-yearly","wooexpress-small-bundle-monthly","wooexpress-medium-bundle-yearly","wooexpress-medium-bundle-monthly"].some((e=>{return t=e,p.some((e=>e.productSlug===t));var t}));return{features:i,siteProduct:{...(0,o.x)({...s},!0),pricingForUi:e},product:a,productPrice:l,purchases:p,hasVideoPressPurchase:m,isFetchingPurchases:u}}},8326:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(7143),r=a(8962);function o(e){const t=e.needsPlaybackToken,a=(0,n.useSelect)((a=>t?a(r.ax).getPlaybackToken(e.guid):null),[e.guid]),o=(0,n.useSelect)((e=>!!t&&e(r.ax).isFetchingPlaybackToken()),[e.guid]);return{playbackToken:a?.token,isFetchingPlaybackToken:o}}},7750:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(7143),r=a(7723),o=a(1609),i=a(6287);const __=r.__,s=({video:e})=>{const[t,a]=(0,o.useState)(null),[r,s]=(0,o.useState)(null),[c,l]=(0,o.useState)(!1),d=(0,n.useDispatch)(i.ax),u=()=>new Promise((e=>{const t=window.wp.media({title:__("Select Thumbnail","jetpack-videopress-pkg"),multiple:!1,library:{type:"image"},button:{text:__("Use this image as thumbnail","jetpack-videopress-pkg")}});t.on("select",(function(){const a=t?.state()?.get("selection")?.first()?.toJSON();e({id:a?.id,url:a?.url})})),t.on("close",(function(){setTimeout((()=>{e(null)}),0)})),t.open()})),p=async t=>{if(null!=t)return d?.updateVideoPoster(e.id,e.guid,{poster_attachment_id:t})};return{handleConfirmFrame:()=>{a(r),l(!1)},handleCloseSelectFrame:()=>l(!1),handleOpenSelectFrame:()=>l(!0),handleVideoFrameSelected:e=>{s(e)},useVideoAsThumbnail:null!==t,selectedTime:Number.isFinite(t)?t/1e3:null,frameSelectorIsOpen:c,updatePosterImageFromFrame:()=>{if(Number.isFinite(t))return d?.updateVideoPoster(e.id,e.guid,{at_time:t,is_millisec:!0})},selectAttachmentFromLibrary:u,updatePosterImageFromLibrary:p,selectAndUpdatePosterImageFromLibrary:async()=>{const e=await u();if(e)return p(e.id)},setVideoFrameMs:a}}},8728:(e,t,a)=>{"use strict";a.d(t,{o:()=>r});var n=a(9539);const r=()=>{const e=(0,n.zy)(),t=(0,n.Zp)(),a=new URLSearchParams(e.search);return{getParam:(e,t=null)=>a.has(e)?a.get(e):t,setParam:(e,t=null)=>{a.set(e,t)},deleteParam:e=>{a.delete(e)},update:()=>{const n="?"+a.toString();n!==e.search&&t({search:n})},reset:()=>{""!==e.search&&t({pathname:e.pathname,search:""},{replace:!0})}}}},3463:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(1609),r=a(7770);const o=({canDrop:e=!1,dropElement:t,onSelectFiles:a}={})=>{const[o,i]=(0,n.useState)(!1),s=(0,n.useRef)(null);let c=[];const l=e=>Array.from(e).filter((e=>r.SK.some((t=>e.name.toLocaleLowerCase().endsWith(t))))),d=(0,n.useCallback)((e=>{const t=l(e.currentTarget.files);a(t)}),[a]),u=e=>{e.preventDefault(),e.stopPropagation(),c.length>0&&(c.forEach((e=>clearTimeout(e))),c=[]),i(!0)},p=()=>{const e=setTimeout((()=>{i(!1);const t=c.findIndex((t=>t===e));t>-1&&c.splice(t,1)}),100);c.push(e)},m=(0,n.useCallback)((t=>{if(i(!1),t.preventDefault(),t.stopPropagation(),c.forEach((e=>clearTimeout(e))),c=[],!e)return;const n=l(t.dataTransfer.files);a(n)}),[e,a]);return(0,n.useEffect)((()=>{e||i(!1)}),[e]),t&&(0,n.useEffect)((()=>(t.addEventListener("drop",m),t.addEventListener("dragover",u),t.addEventListener("dragleave",p),()=>{t.removeEventListener("drop",m),t.removeEventListener("dragover",u),t.removeEventListener("dragleave",p)})),[m,u,p]),{isDraggingOver:o,inputRef:s,setIsDraggingOver:i,handleFileInputChangeEvent:d,handleDragOverEvent:u,handleDragLeaveEvent:p,handleDropEvent:m,filterVideoFiles:l}}},4951:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var n=a(1609);const r=({shouldPrevent:e=!1,message:t})=>{(0,n.useEffect)((()=>{if(!e)return;const a=e=>{e.preventDefault(),e.returnValue=t};return window.addEventListener("beforeunload",a),()=>{window.removeEventListener("beforeunload",a)}}),[e])}},2807:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(7143),r=a(8962);function o(){return{items:(0,n.useSelect)((e=>e(r.ax).getUsers())),...(0,n.useSelect)((e=>e(r.ax).getUsersPagination()))}}},7882:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n=a(7143),r=a(6287),o=a(8962);function i(e,t=!1){const a=(0,n.useDispatch)(r.ax),i=(0,n.useSelect)((a=>a(r.ax).getVideo(e,t)),[e]),s=(0,n.useSelect)((t=>t(r.ax).getVideoStateMetadata(e)),[e]),c=(0,n.useSelect)((e=>e(r.ax).getIsFetching()),[e]),l=null===i?.posterImage&&!i?.finished;return{data:{privacySetting:o.zG.indexOf(o.az),...i},...s,processing:l,isFetching:c,setVideo:e=>a.setVideo(e),deleteVideo:()=>a.deleteVideo(e),updateVideoPrivacy:t=>a.updateVideoPrivacy(e,o.zG.findIndex((e=>e===t)))}}},9177:(e,t,a)=>{"use strict";a.d(t,{T:()=>i});var n=a(7143),r=a(6287),o=a(8031);const i=()=>{const e=(0,n.useDispatch)(r.ax),t=(0,n.useSelect)((e=>e(r.ax).getVideoPressSettings()),[]);return{settings:{videoPressVideosPrivateForSite:t?.videoPressVideosPrivateForSite??!1,siteIsPrivate:t?.siteIsPrivate??!1,siteType:t?.siteType??o.Aq},onUpdate:t=>e.updateVideoPressSettings(t)}}},957:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>o,Ds:()=>i,mo:()=>s});var n=a(7143),r=a(8962);function o(){const e=(0,n.useSelect)((e=>e(r.ax).getVideos())),t=(0,n.useSelect)((e=>e(r.ax).getUploadingVideos())),a=t.length>0,o=(0,n.useSelect)((e=>e(r.ax).getUploadedVideoCount())),i=(0,n.useSelect)((e=>e(r.ax).getIsFetching())),s=(0,n.useSelect)((e=>e(r.ax).getIsFetchingUploadedVideoCount())),c=(0,n.useSelect)((e=>e(r.ax).getFirstUploadedVideoId())),l=(0,n.useSelect)((e=>e(r.ax).getFirstVideoProcessed())),d=(0,n.useSelect)((e=>e(r.ax).getDismissedFirstVideoPopover())),u=(0,n.useSelect)((e=>e(r.ax).getVideosQuery()||{})),p=(0,n.useSelect)((e=>e(r.ax).getPagination())),m=(0,n.useSelect)((e=>e(r.ax).getStorageUsed()),[]);return{items:e,uploading:t,isUploading:a,search:"",filter:(0,n.useSelect)((e=>e(r.ax).getVideosFilter())),uploadedVideoCount:o,isFetching:i,isFetchingUploadedVideoCount:s,firstUploadedVideoId:c,firstVideoProcessed:l,dismissedFirstVideoPopover:d,...u,...p,...m,uploadErrors:(0,n.useSelect)((e=>e(r.ax).getUploadErrorVideos())),setPage:e=>(0,n.dispatch)(r.ax).setVideosQuery({page:e}),setSearch:e=>(0,n.dispatch)(r.ax).setVideosQuery({search:e}),setFilter:(0,n.dispatch)(r.ax).setVideosFilter}}const i=()=>({items:(0,n.useSelect)((e=>e(r.ax).getLocalVideos())),uploadedLocalVideoCount:(0,n.useSelect)((e=>e(r.ax).getUploadedLocalVideoCount())),isFetching:(0,n.useSelect)((e=>e(r.ax).getIsFetchingLocalVideos())),...(0,n.useSelect)((e=>e(r.ax).getLocalVideosQuery()||{})),...(0,n.useSelect)((e=>e(r.ax).getLocalPagination())),setPage:e=>(0,n.dispatch)(r.ax).setLocalVideosQuery({page:e})}),s=()=>(0,n.useSelect)((e=>e(r.ax).getVideosQuery()||{}))},6592:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(6427),r=a(7723);const __=r.__;function o({isOpen:e,onClose:t}){return e?React.createElement(n.Modal,{title:__("Chapters in VideoPress","jetpack-videopress-pkg"),isDismissible:!1,className:"learn-how-modal",onRequestClose:t},React.createElement("p",null,__("Chapters are a great way to split up longer videos and organize them into different sections.","jetpack-videopress-pkg")),React.createElement("p",null,__("They allow your visitors to see what each section is about and skip to their favorite parts.","jetpack-videopress-pkg")),React.createElement("p",{className:"learn-how-modal__heading"},__("How to add Chapters to your VideoPress videos","jetpack-videopress-pkg")),React.createElement("ol",null,React.createElement("li",null,__("In the Description, add a list of timestamps and titles.","jetpack-videopress-pkg")),React.createElement("li",null,__("Make sure that the first timestamp starts with 00:00.","jetpack-videopress-pkg")),React.createElement("li",null,__("Add at least three chapters entries and as many as you need.","jetpack-videopress-pkg")),React.createElement("li",null,__("Add your chapters entries in consecutive order, with at least 10-second intervals between each.","jetpack-videopress-pkg"))),React.createElement("p",{className:"learn-how-modal__heading"},__("Example","jetpack-videopress-pkg")),React.createElement("p",null,__("00:00 Intro","jetpack-videopress-pkg")),React.createElement("p",null,__("00:24 Mountains arise","jetpack-videopress-pkg")),React.createElement("p",null,__("02:38 Coming back home","jetpack-videopress-pkg")),React.createElement("p",null,__("03:04 Credits","jetpack-videopress-pkg")),React.createElement("div",{className:"learn-how-modal__buttons"},React.createElement(n.Button,{className:"learn-how-modal__button",onClick:t,variant:"primary"},__("Got it, thanks","jetpack-videopress-pkg")))):null}},6151:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var n=a(1112),r=a(6087),o=a(7723),i=a(1609),s=a(6592);const __=o.__,c=()=>{const[e,t]=(0,i.useState)(!1);return React.createElement(React.Fragment,null,(0,r.createInterpolateElement)(__("Did you know you can now add Chapters to your videos? <link>Learn how</link>","jetpack-videopress-pkg"),{link:React.createElement(n.A,{variant:"link",size:"small",onClick:()=>t(!0)})}),React.createElement(s.A,{onClose:()=>t(!1),isOpen:e}))}},7726:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var n=a(6427);const r=React.createElement(n.SVG,{width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},React.createElement(n.Path,{d:"M8.45414 21.2068L16.4541 3.70678L15.5446 3.29102L13.6676 7.39695C13.1429 7.3024 12.587 7.24988 11.9996 7.24988C9.00203 7.24988 6.82613 8.61747 5.41986 9.95678C4.71735 10.6259 4.19984 11.2937 3.85693 11.7955C3.6851 12.047 3.55604 12.2583 3.46859 12.4095C3.42483 12.4851 3.39141 12.5458 3.36817 12.5891C3.35655 12.6107 3.34747 12.6281 3.34091 12.6407L3.33296 12.6562L3.3304 12.6613L3.3291 12.6639C3.3291 12.6639 3.32878 12.6645 3.9996 12.9999C4.67042 13.3353 4.67016 13.3358 4.67016 13.3358L4.67297 13.3304C4.67621 13.3241 4.68183 13.3134 4.68987 13.2984C4.70594 13.2685 4.73159 13.2217 4.76694 13.1607C4.83769 13.0384 4.94691 12.8591 5.0954 12.6418C5.39312 12.2061 5.84435 11.6239 6.45434 11.043C7.67307 9.88229 9.49718 8.74988 11.9996 8.74988C12.353 8.74988 12.693 8.77247 13.0196 8.81455L12.8834 9.11243C12.6011 9.03897 12.3049 8.99988 11.9996 8.99988C10.0666 8.99988 8.49962 10.5669 8.49962 12.4999C8.49962 13.6966 9.1002 14.753 10.0163 15.3842L7.54467 20.791L8.45414 21.2068Z"}),React.createElement(n.Path,{d:"M15.7168 9.69537C16.4332 10.0877 17.041 10.563 17.5451 11.0431C18.1551 11.624 18.6064 12.2062 18.9041 12.6419C19.0526 12.8592 19.1618 13.0385 19.2325 13.1608C19.2679 13.2218 19.2935 13.2686 19.3096 13.2985L19.3211 13.3202L19.3265 13.3305L19.3293 13.3359C19.3293 13.3359 19.329 13.3354 19.9999 13C20.6707 12.6646 20.67 12.6632 20.67 12.6632L20.6691 12.6614L20.6665 12.6563L20.6586 12.6408C20.652 12.6282 20.6429 12.6108 20.6313 12.5892C20.6081 12.5459 20.5746 12.4852 20.5309 12.4096C20.4434 12.2584 20.3144 12.0471 20.1425 11.7956C19.7996 11.2938 19.2821 10.626 18.5796 9.95691C17.9765 9.38256 17.2319 8.803 16.3417 8.32834L15.7168 9.69537Z"}),React.createElement(n.Path,{d:"M12.8867 15.8868C14.3904 15.4942 15.5002 14.1267 15.5002 12.5C15.5002 11.9453 15.3711 11.4207 15.1414 10.9546L12.8867 15.8868Z"}))},6639:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var n=a(6427);const r=React.createElement(n.SVG,{width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},React.createElement(n.Rect,{x:"5",y:"7",width:"14",height:"1.5"}),React.createElement(n.Rect,{x:"7",y:"11.25",width:"10",height:"1.5"}),React.createElement(n.Rect,{x:"9",y:"15.5",width:"6",height:"1.5"}))},7975:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var n=a(6427);const r=React.createElement(n.SVG,{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},React.createElement(n.Path,{d:"M4.75725 2.075C4.60279 1.97745 4.41041 1.97489 4.25365 2.06832C4.09689 2.16174 4 2.3367 4 2.52632V21.4737C4 21.6633 4.09689 21.8383 4.25365 21.9317C4.41041 22.0251 4.60279 22.0226 4.75725 21.925L19.7573 12.4513C19.9079 12.3562 20 12.1849 20 12C20 11.8151 19.9079 11.6438 19.7573 11.5487L4.75725 2.075Z",fill:"white"}))},7121:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var n=a(6427);const r=React.createElement(n.SVG,{width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},React.createElement(n.Path,{d:"m3.999 13 .67.336.003-.005a2.42 2.42 0 0 1 .094-.17c.07-.122.18-.302.328-.52a10.1 10.1 0 0 1 1.36-1.598c1.218-1.16 3.042-2.293 5.545-2.293 2.502 0 4.326 1.132 5.545 2.293.61.581 1.061 1.163 1.359 1.599a8.29 8.29 0 0 1 .405.657l.017.032.003.005.67-.336.671-.336-.001-.003-.003-.005-.008-.015a9.752 9.752 0 0 0-.516-.845c-.343-.502-.86-1.17-1.563-1.84-1.406-1.338-3.582-2.706-6.58-2.706-2.997 0-5.173 1.368-6.58 2.707-.702.669-1.22 1.337-1.562 1.839a9.77 9.77 0 0 0-.516.845l-.008.015-.003.005v.003l.67.336Zm8 3a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7Z"}))},2843:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(1112),r=a(6427),o=a(6087),i=a(7723),s=a(1609),c=a(6592);const __=i.__,l=({className:e})=>{const[t,a]=(0,s.useState)(!1);return React.createElement(React.Fragment,null,React.createElement(r.Notice,{status:"warning",className:e,isDismissible:!1},(0,o.createInterpolateElement)(__("It seems there are some chapters, but they are incomplete. Check out the <link>format</link> and try again.","jetpack-videopress-pkg"),{link:React.createElement(n.A,{variant:"link",size:"small",onClick:()=>a(!0)})})),React.createElement(c.A,{onClose:()=>a(!1),isOpen:t}))}},6720:(e,t,a)=>{"use strict";a.d(t,{A:()=>d,L:()=>l});var n=a(6427),r=a(6087),o=a(1113),i=a(3022),s=a(7975),c=a(8766);const l=({src:e,setMaxDuration:t=null,currentTime:a})=>{const o=(0,r.useRef)(null),[i,s]=(0,r.useState)(!0);(0,r.useEffect)((()=>{o.current.src=e}),[e]),(0,r.useEffect)((()=>{o.current&&Number.isFinite(a)&&(o.current.currentTime=a)}),[a]);return React.createElement("div",{className:c.A["video-player-wrapper"]},i&&React.createElement("div",{className:c.A["video-player-spinner-wrapper"]},React.createElement(n.Spinner,{className:c.A.spinner})),React.createElement("video",{onLoadedData:()=>s(!1),ref:o,muted:!0,className:c.A.video,onDurationChange:e=>{const n=e.target.duration;if(t?.(n),o.current){const e=Number.isFinite(a)?a:n/2;o.current.currentTime=e}}}))},d=({src:e="",onVideoFrameSelected:t,className:a="",initialCurrentTime:d=null})=>{const[u,p]=(0,r.useState)(0),[m,h]=(0,r.useState)(Number.isFinite(d)?d:null);return React.createElement("div",{className:(0,i.A)(c.A.container,a)},React.createElement(o.A,{className:c.A["play-icon"],icon:s.A}),React.createElement(l,{src:e,setMaxDuration:p,currentTime:m}),React.createElement(n.RangeControl,{className:c.A.range,min:0,step:.1,initialPosition:m,max:u,showTooltip:!1,withInputField:!1,onChange:e=>{h(e),t?.(1e3*e)},__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}))}},2439:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(9491),r=a(1609),o=a(9799),i=a(2066);const s=e=>{const[t,a]=(0,r.useState)(!1),s=(0,r.useCallback)((()=>{const t=(0,o.Ay)(e);0===t.length?a(!1):a(!(0,i.A)(t))}),[e]),c=(0,n.useDebounce)(s,3e3);return(0,r.useEffect)((()=>{c()}),[e]),(0,r.useEffect)(s,[]),{hasIncompleteChapters:t}}},2185:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(1455),r=a.n(n);const o=e=>{const t={allowDownload:"allow_download",displayEmbed:"display_embed"};return a=>new Promise(((n,o)=>{const i=(s=Object.assign({id:e},a),Object.keys(s).reduce(((e,a)=>(e[t[a]||a]=s[a],e)),{}));var s;r()({path:"/wpcom/v2/videopress/meta",method:"POST",data:i}).then((e=>{"success"!==e?.code&&o()})).catch((e=>o(e))).finally((()=>{n()}))}))}},560:(e,t,a)=>{"use strict";a.d(t,{NP:()=>i});var n=a(1455),r=a.n(n),o=(a(6087),a(7723));a(781);const __=o.__,i=e=>{const t=`videopress/v1/upload/${e}`;return new Promise(((a,n)=>{r()({path:t,method:"POST"}).then((t=>{"uploading"===t.status||"new"===t.status||"resume"===t.status?i(e).then(a).catch(n):"complete"===t.status?a({guid:t.uploaded_details.guid,id:t.uploaded_details.media_id,src:t.uploaded_details.upload_src}):"error"===t.status?n({data:{message:t.error}}):n({data:{message:__("Unexpected error uploading video.","jetpack-videopress-pkg")}})})).catch((e=>{n({data:{message:e?.message}})}))}))}},1370:(e,t,a)=>{"use strict";a.d(t,{X:()=>u});var n=a(1455),r=a.n(n),o=a(6087),i=a(6941),s=a.n(i),c=a(8025);const l=o.Platform.isNative,d=s()("videopress:lib:fetch-video-item");async function u({guid:e,isPrivate:t,token:a=null,skipRatingControl:n=!1,retries:o=0}){try{const o=n?{}:{birth_day:"1",birth_month:"1",birth_year:"2000"};let i;t&&!a&&(i=await(0,c.A)("playback",{guid:e})),(a||i?.token)&&(o.metadata_token=a||i.token);const s=Object.keys(o).length?`?${new URLSearchParams(o).toString()}`:"",d=l?{path:`/rest/v1.1/videos/${e}${s}`}:{url:`https://public-api.wordpress.com/rest/v1.1/videos/${e}${s}`};return await r()({...d,credentials:"omit",global:!0})}catch(t){d("updating retry from",o,"to",o+1);const a=o+1;if(a>2)throw d("Too many attempts to get video. Aborting."),new Error(t?.message??t);if("auth"===t?.error)return d("Authentication error. Reattempt %o",a+"/3"),u({guid:e,isPrivate:!0,token:null,skipRatingControl:n,retries:a});if("Please supply the birthdate parameters."===t?.message)return d("Rating error. Reattempt %o",a+"/3"),u({guid:e,isPrivate:!0,token:null,skipRatingControl:!1,retries:a});throw new Error(t?.message??t)}}},8025:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(6941),r=a.n(n),o=a(5468);const i=r()("videopress:get-media-token");const s=async function(e,t={}){const{id:a=0,guid:n=0,flushToken:r}=t,s=`vpc-${e}-${a}-${n}`,c=window?.videopressAjax?.context||"main";let l;const d=localStorage.getItem(s);if(r)i("(%s) Flushing %o token",c,s),localStorage.removeItem(s);else try{if(d){if(l=await JSON.parse(d),l&&l.expire>Date.now())return i("(%s) Providing %o token from the store",c,s),l.data;i("(%s) Token %o expired. Clean.",c,s),localStorage.removeItem(s)}}catch{i("Invalid token in the localStore")}const u=await function(e,t={}){const{id:a=0,guid:n,subscriptionPlanId:r=0,adminAjaxAPI:i,filename:s}=t;return new Promise((function(t,c){const l=i||window.videopressAjax?.ajaxUrl||window?.ajaxurl||"/wp-admin/admin-ajax.php";if(!o.p.includes(e))return c("Invalid scope");const d={action:"videopress-get-playback-jwt"};switch(e){case"upload":d.action="videopress-get-upload-token",s&&(d.filename=s);break;case"upload-jwt":d.action="videopress-get-upload-jwt";break;case"playback":d.action="videopress-get-playback-jwt",d.guid=n,d.post_id=String(a),d.subscription_plan_id=r}fetch(l,{method:"POST",credentials:"same-origin",body:new URLSearchParams(d)}).then((e=>{if(!e.ok)throw new Error("Network response was not ok");return e.json()})).then((a=>{if(!a.success)throw new Error("Token is not achievable");switch(e){case"upload":case"upload-jwt":t({token:a.data.upload_token,blogId:a.data.upload_blog_id,url:a.data.upload_action_url});break;case"playback":t({token:a.data.jwt})}})).catch((()=>{console.warn("Token is not achievable"),t({token:null})}))}))}(e,t);return"playback"===e&&u?.token&&(i("(%s) Storing %o token",c,s),localStorage.setItem(s,JSON.stringify({data:u,expire:Date.now()+864e5}))),i("(%s) Providing %o token from request/response",c,s),u}},5468:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});const n=["upload","playback","upload-jwt"]},5778:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(6941),r=a.n(n),o=a(781),i=a(8025);const s=r()("videopress:resumable-file-uploader"),c={},l=({file:e,tokenData:t,onProgress:a,onSuccess:n,onError:r})=>{const l=new o._O(e,{onError:r,onProgress:a,endpoint:t.url,removeFingerprintOnSuccess:!0,overridePatchMethod:!1,chunkSize:1e7,metadata:{filename:e.name,filetype:e.type},retryDelays:[0,1e3,3e3,5e3,1e4],onShouldRetry:function(e){return 400!==(e.originalResponse?e.originalResponse.getStatus():0)||(s("cleanup retry due to 400 error"),localStorage.removeItem(l._urlStorageKey),!1)},onBeforeRequest:async function(e){const a=e._method;if(["HEAD","OPTIONS"].indexOf(a)>=0&&(e._method="GET",e.setHeader("X-HTTP-Method-Override",a)),["DELETE","PUT","PATCH"].indexOf(a)>=0&&(e._method="POST",e.setHeader("X-HTTP-Method-Override",a)),e._xhr.open(e._method,e._url,!0),Object.keys(e._headers).forEach((function(t){e.setHeader(t,e._headers[t])})),"POST"===a){if(!!!t.token)throw"should never happen";e.setHeader("x-videopress-upload-token",t.token)}if(["OPTIONS","GET","HEAD","DELETE","PUT","PATCH"].indexOf(a)>=0){const t=(n=e._url,new URL(n).pathname.split("/").pop());if(c[t])e.setHeader("x-videopress-upload-token",c[t]);else if("HEAD"===a){const a=await(0,i.A)("upload-jwt");a?.token&&(c[t]=a.token,e.setHeader("x-videopress-upload-token",a.token))}}var n},onAfterResponse:async function(t,a){if(a.getStatus()>=400)return void s("upload error");const r=a.getHeader("x-videopress-upload-guid"),o=a.getHeader("x-videopress-upload-media-id"),i=a.getHeader("x-videopress-upload-src-url");if(r&&o&&i)return void(n&&n({id:Number(o),guid:r,src:i},e));const l={"x-videopress-upload-key-token":"token","x-videopress-upload-key":"key"},d={};Object.keys(l).forEach((function(e){const t=a.getHeader(e);t&&(d[l[e]]=t)})),d.key&&d.token&&(c[d.key]=d.token)}});return l.findPreviousUploads().then((function(e){e.length&&l.resumeFromPreviousUpload(e[0]),l.start()})),l}},9689:(e,t,a)=>{"use strict";a.d(t,{rB:()=>n});a(1455),a(3832);function n(e,t){return t?`https://video.wordpress.com/v/${e}`:`https://videopress.com/v/${e}`}},8634:(e,t,a)=>{"use strict";a.d(t,{Cr:()=>u,n:()=>p});var n=a(1455),r=a.n(n),o=a(6941),i=a.n(o),s=a(8025);const{siteType:c=""}=window?.videoPressEditorState||{},l="simple"!==c,d=i()("videopress:tracks:lib:video-tracks");async function u(e,t){if(!e)return!1;let a,n="";t.isPrivate&&(a=await(0,s.A)("playback",{guid:t.guid}),n="?"+new URLSearchParams({metadata_token:a?.token}).toString());let r=await fetch(e+n);if(!r.ok&&403===r.status&&t.guid&&(a=await(0,s.A)("playback",{guid:t.guid}),n="?"+new URLSearchParams({metadata_token:a?.token}).toString(),r=await fetch(e+n)),!r.ok)return!1;const o=await r.text();return/videopress-chapters-auto-generated/.test(o)}const p=(e,t)=>{const{kind:a,srcLang:n,label:o,tmpFile:i}=e;return l?function(e,t){return d("using jetpack api fetch"),new Promise((function(a,n){const{kind:r,srcLang:o,label:i,tmpFile:c}=e;(0,s.A)("upload",{filename:c.name}).then((({token:e,blogId:s})=>{const l=new FormData;l.append("kind",r),l.append("srclang",o),l.append("label",i),l.append("vtt",c),fetch(`https://public-api.wordpress.com/rest/v1.1/videos/${t}/tracks`,{headers:{Authorization:`X_UPLOAD_TOKEN token="${e}" blog_id="${s}"`},method:"POST",body:l}).then((e=>{try{const t=e.json();return d("data",e),d("json",t),a(t)}catch(e){return d("error",e),n(e)}})).catch(n)}))}))}({kind:a,srcLang:n,label:o,tmpFile:i},t):(d("using wpcom api fetch"),new Promise((function(e,s){return r()({method:"POST",path:`/videos/${t}/tracks`,apiNamespace:"rest/v1.1",global:!0,parse:!1,formData:[["kind",a],["srclang",n],["label",o],["vtt",i]]}).then((t=>{try{const a=t.json();return d("data",t),d("json",a),e(a)}catch(e){return d("error",e),s(e)}})).catch(s)})))}},9596:(e,t,a)=>{"use strict";a.d(t,{A:()=>y});var n=a(1455),r=a.n(n),o=a(7723),i=a(3832),s=a(6941),c=a.n(s),l=a(560),d=a(8025),u=a(5778),p=a(3356),m=a(8962),h=a(8306),g=a(4603);const __=o.__,v=c()("videopress:actions"),f=async e=>{const t=await r()({path:(0,i.addQueryArgs)(`${m.$s}/${e?.id}`)}),a=(0,h.ac)(t);return null!==a?.posterImage&&""!==a?.posterImage?Promise.resolve(a):new Promise(((t,a)=>{setTimeout((()=>{f(e).then(t).catch(a)}),2e3)}))},y={setIsFetchingVideos:e=>({type:m.QK,isFetching:e}),setFetchVideosError:e=>({type:m.Ms,error:e}),setVideosQuery:e=>({type:m.pz,query:e}),setVideosPagination:e=>({type:m.Ni,pagination:e}),setVideosFilter:(e,t,a)=>({type:m.B1,filter:e,value:t,isActive:a}),setVideos:e=>({type:m.c$,videos:e}),dismissFirstVideoPopover:()=>({type:m.IX}),setLocalVideos:e=>({type:m.UB,videos:e}),setIsFetchingLocalVideos:e=>({type:m.k8,isFetching:e}),setLocalVideosQuery:e=>({type:m.DE,query:e}),setLocalVideosPagination:e=>({type:m.DX,pagination:e}),setVideosStorageUsed:e=>({type:m.qd,used:e}),setVideo:(e,t=!1)=>({type:m.Rd,video:e,addAtEnd:t}),setIsFetchingUploadedVideoCount:e=>({type:m.u7,isFetchingUploadedVideoCount:e}),setUploadedVideoCount:e=>({type:m.E9,uploadedVideoCount:e}),setVideoPrivacy:({id:e,privacySetting:t})=>({type:m.wx,id:e,privacySetting:t}),updateVideoPrivacy:(e,t)=>async({dispatch:a,select:n,resolveSelect:o})=>{const i=Number(t);if(isNaN(i))throw new Error(`Invalid privacy level: '${t}'`);if(0>i||i>=m.zG.length)throw new Error(`Invalid privacy level: '${t}'`);if(1===t){const t=await n.getVideo(e);await o.getPlaybackToken(t?.guid)}a.setVideoPrivacy({id:e,privacySetting:i});try{const t=await r()({path:m.bT,method:"POST",data:{id:e,privacy_setting:i}});if(200!==t?.data)return;const{videoPressVideosPrivateForSite:o}=n.getVideoPressSettings();return a.updateVideoIsPrivate(e,(0,g.D)(i,o)),a({type:m.d$,id:e,privacySetting:i})}catch(e){console.error(e)}},removeVideo:e=>({type:m.H9,id:e}),deleteVideo:e=>async({dispatch:t,select:a})=>{t.removeVideo(e);let n={type:m.WX,id:e,hasBeenDeleted:!1,video:{}};try{const t=await r()({path:`${m.$s}/${e}`,method:"DELETE",data:{id:e,force:!0}});t?.deleted&&(n={...n,hasBeenDeleted:!0,video:t?.previous})}catch(e){console.error(e)}finally{t(n)}a.getProcessedAllVideosBeingRemoved()&&(t({type:m.Aq}),t({type:m.kb}))},uploadVideo:e=>async({dispatch:t})=>{const a=(0,p.A)();v("Uploading video"),t({type:m.NX,id:a,title:e?.name});const n=await(0,d.A)("upload-jwt");(0,u.A)({tokenData:n,file:e,onError:e=>{v("Upload error",e);const n=e?.originalResponse?.getHeader("x-videopress-upload-error")||__("Upload error","jetpack-videopress-pkg");t({type:m.SH,id:a,error:n})},onProgress:(e,n)=>{t({type:m.TZ,id:a,bytesSent:e,bytesTotal:n})},onSuccess:async e=>{v("Video uploaded",e),t({type:m.TI,id:a,data:e});const n=await f(e);t({type:m.Ko,video:n})}})},uploadVideoFromLibrary:e=>async({dispatch:t})=>{const a=(0,p.A)();t({type:m.NX,id:a,title:e?.title});const n=await(0,l.NP)(e?.id);t({type:m.K,id:e?.id}),t({type:m.TI,id:a,data:n});const r=await f(n);t({type:m.Ko,video:r})},setIsFetchingPurchases:e=>({type:m.en,isFetching:e}),setPurchases:e=>({type:m.GY,purchases:e}),updateVideoPoster:(e,t,a)=>async({dispatch:n,select:o,resolveSelect:i})=>{const s=`${m.v2}/${t}/poster`,c=await o.getVideo(e),l=async()=>{if(!c.needsPlaybackToken)return null;const e=await i.getPlaybackToken(c.guid);return e?.token},d=(e,t)=>e&&t?`${e}?metadata_token=${t}`:e,u=()=>{setTimeout((async()=>{try{const t=await r()({path:s,method:"GET"});if(t?.data?.generating)u();else{const a=await l(),o=t?.data?.poster;n({type:m.OU,id:e,poster:d(o,a)}),r()({path:m.bT,method:"POST",data:{id:e,poster:o}})}}catch(e){console.error(e)}}),2e3)};try{n({type:m.N1,id:e});const t=c.duration-a.at_time;t<50&&(a.at_time-=t+50);const o=await r()({method:"POST",path:s,data:a});if(o?.data?.generating)return void u();const i=await l(),p=d(o?.data?.poster,i);return n({type:m.OU,id:e,poster:p})}catch(e){console.error(e)}},setUsers:e=>({type:m.q2,users:e}),setUsersPagination:e=>({type:m.NS,pagination:e}),setIsFetchingPlaybackToken:e=>({type:m.$N,isFetching:e}),setPlaybackToken:e=>({type:m.h7,playbackToken:e}),expirePlaybackToken:e=>({type:m.kp,guid:e}),setVideoPressSettings:e=>({type:m.Cj,videoPressSettings:e}),updateVideoPressSettings:e=>async({dispatch:t})=>{if(!e)return;const a={force:!0};"boolean"==typeof e.videoPressVideosPrivateForSite&&(a.videopress_videos_private_for_site=e.videoPressVideosPrivateForSite);try{t.setVideoPressSettings(e);return await r()({path:m.pC,method:"PUT",data:a})}catch(e){console.error(e)}},updateVideoIsPrivate:(e,t)=>({type:m.LF,id:e,isPrivate:t}),dismissErroredVideo:e=>({type:m.RZ,id:e})}},8962:(e,t,a)=>{"use strict";a.d(t,{$N:()=>D,$s:()=>o,AV:()=>ie,Aq:()=>V,B1:()=>C,CR:()=>X,Cj:()=>J,DE:()=>A,DX:()=>k,Dv:()=>d,E9:()=>f,GY:()=>T,H9:()=>j,IX:()=>b,K:()=>R,Ko:()=>L,LF:()=>W,Ms:()=>p,N1:()=>G,NS:()=>q,NX:()=>z,Ni:()=>h,OU:()=>$,QK:()=>u,RZ:()=>K,Rd:()=>S,SH:()=>U,TC:()=>c,TI:()=>O,TZ:()=>M,UB:()=>w,VI:()=>oe,WX:()=>P,ax:()=>n,az:()=>Z,bT:()=>s,c$:()=>g,cl:()=>ee,d$:()=>x,en:()=>F,h7:()=>I,iL:()=>ne,k8:()=>E,kb:()=>N,ko:()=>te,kp:()=>B,o0:()=>Y,p9:()=>r,pC:()=>l,pz:()=>m,q2:()=>H,qd:()=>v,u7:()=>y,uX:()=>re,v2:()=>i,wx:()=>_,z7:()=>ae,zG:()=>Q});const n="videopress/media",r="wp/v2/users",o="wp/v2/media",i="wpcom/v2/videopress",s="wpcom/v2/videopress/meta",c="wpcom/v2/videopress/playback-jwt",l="videopress/v1/settings",d="videopress/v1/site",u="SET_IS_FETCHING_VIDEOS",p="SET_VIDEOS_FETCH_ERROR",m="SET_VIDEOS_QUERY",h="SET_VIDEOS_PAGINATION",g="SET_VIDEOS",v="SET_VIDEOS_STORAGE_USED",f="SET_UPLOADED_VIDEO_COUNT",y="SET_IS_FETCHING_UPLOADED_VIDEO_COUNT",b="DISMISS_FIRST_VIDEO_POPOVER",w="SET_LOCAL_VIDEOS",E="SET_IS_FETCHING_LOCAL_VIDEOS",A="SET_LOCAL_VIDEOS_QUERY",k="SET_LOCAL_VIDEOS_PAGINATION",R="SET_LOCAL_VIDEO_UPLOADED",C="SET_VIDEOS_FILTER",S="SET_VIDEO",_="SET_VIDEO_PRIVACY",x="UPDATE_VIDEO_PRIVACY",P="DELETE_VIDEO",j="REMOVE_VIDEO",N="FLUSH_DELETED_VIDEOS",V="UPDATE_PAGINATION_AFTER_DELETE",z="SET_VIDEO_UPLOADING",U="SET_VIDEO_UPLOADING_ERROR",O="SET_VIDEO_PROCESSING",L="SET_VIDEO_UPLOADED",M="SET_VIDEO_UPLOAD_PROGRESS",F="SET_IS_FETCHING_PURCHASES",T="SET_PURCHASES",D="SET_IS_FETCHING_PLAYBACK_TOKEN",I="SET_PLAYBACK_TOKEN",B="EXPIRE_PLAYBACK_TOKEN",H="SET_USERS",q="SET_USERS_PAGINATION",G="SET_UPDATING_VIDEO_POSTER",$="UPDATE_VIDEO_POSTER",J="SET_VIDEOPRESS_SETTINGS",W="UPDATE_VIDEO_IS_PRIVATE",K="DISMISS_ERRORED_VIDEO",Z="public",Y="private",X="site-default",Q=[Z,Y,X],ee=2,te="G",ae="PG-13",ne="R-17",re="uploader",oe="rating",ie="privacy"},6287:(e,t,a)=>{"use strict";a.d(t,{ax:()=>i.ax,k$:()=>h});var n=a(6941),r=a.n(n),o=a(9596),i=a(8962),s=a(2354),c=a(3132),l=a(6225),d=a(1105);const u=r()("videopress/media:state"),p=window.jetpackVideoPressInitialState?.initialState||{videos:{}},m=window.location.hash.split("?");function h(){u("Initializing %o store",i.ax),d.A.mayBeInit(i.ax,{__experimentalUseThunks:!0,reducer:s.A,actions:o.A,selectors:l.Ay,resolvers:c.A,initialState:p})}"#/"===m?.[0]&&m?.[1]&&"page=1"!==m?.[1]&&(p.videos.isFetching=!0)},2354:(e,t,a)=>{"use strict";a.d(t,{A:()=>s,S:()=>i});var n=a(7143),r=a(3832),o=a(8962);function i(){return{order:"desc",orderBy:"date",itemsPerPage:6,page:1,type:"video/videopress"}}const s=(0,n.combineReducers)({videos:(e,t)=>{switch(t.type){case o.QK:return{...e,isFetching:t.isFetching};case o.Ms:{const{error:a}=t;return{...e,isFetching:!1,error:a}}case o.pz:return{...e,query:{...e.query,...t.query},_meta:{...e._meta,relyOnInitialState:!1}};case o.Ni:return{...e,pagination:{...e.pagination,...t.pagination},_meta:{...e._meta,relyOnInitialState:!1}};case o.B1:{const{filter:a,value:n,isActive:r}=t;return{...e,filter:{...e.filter,[a]:{...e.filter?.[a]||{},[n]:r}},_meta:{...e._meta,relyOnInitialState:!1}}}case o.c$:{const{videos:a}=t;return{...e,items:a,isFetching:!1}}case o.Rd:{const{video:a,addAtEnd:n=!1}=t,r=[...e.items??[]],o=r.findIndex((e=>e.id===a.id));return-1===o?n?r.push(a):r.unshift(a):r[o]={...r[o],...a},{...e,isFetching:!1,items:r}}case o.wx:{const{id:a,privacySetting:n}=t,r=[...e.items??[]],o=r.findIndex((e=>e.id===a));if(o<0)return e;const i=r[o].privacySetting;r[o]={...r[o],privacySetting:n};const s={...e._meta?.items??[]},c=s[a]??{};return{...e,items:r,_meta:{...e._meta,items:{...s,[a]:{...c,isUpdatingPrivacy:!0,hasBeenUpdatedPrivacy:!1,prevPrivacySetting:i}}}}}case o.d$:{const{id:a}=t,n={...e._meta?.items??[]};if(!n?.[a])return e;const r=n[a]??{};return{...e,_meta:{...e._meta,items:{...n,[a]:{...r,isUpdatingPrivacy:!1,hasBeenUpdatedPrivacy:!0,prevPrivacySetting:null}}}}}case o.LF:{const{id:a,isPrivate:n}=t,r=[...e.items??[]],o=r.findIndex((e=>e.id===a));return o<0?e:(r[o]={...r[o],isPrivate:n},{...e,items:r})}case o.H9:{const{id:a}=t,{items:n=[]}=e;if(n.findIndex((e=>e.id===a))<0)return e;const r={...e._meta?.items??[]},o=r[a]??{};return{...e,_meta:{...e._meta,videosBeingRemoved:[{id:a,processed:!1,deleted:!1},...e._meta.videosBeingRemoved??[]],items:{...r,[a]:{...o,isDeleting:!0}}}}}case o.WX:{const{id:a,hasBeenDeleted:n,video:r}=t,o=e?._meta?.items||[],i=o[a]||{},s=[...e._meta.videosBeingRemoved??[]],c=s.findIndex((e=>e.id===a));if(!i||c<0)return e;s[c].processed=!0,s[c].deleted=n;const l=0===s.filter((e=>!e.processed)).length;let d=e.uploadedVideoCount??0;if(l){d-=s.filter((e=>e.deleted)).length}return{...e,uploadedVideoCount:d,_meta:{...e._meta,videosBeingRemoved:s,processedAllVideosBeingRemoved:l,items:{...o,[a]:{...i,hasBeenDeleted:n,deletedVideo:r}}}}}case o.kb:return{...e,_meta:{...e._meta,videosBeingRemoved:[],relyOnInitialState:!1}};case o.Aq:{const{items:t=[],query:a={},pagination:n={},_meta:r={}}=e,{videosBeingRemoved:o=[]}=r,i=o.filter((e=>e.deleted)).length,s=t?.length===i,c=a?.page??1,l=n?.totalPages??1,d=n?.total,u=s&&c>1?c-1:c,p=s&&l>1?l-1:l;return{...e,query:{...a,page:u},pagination:{...n,total:d-1,totalPages:p}}}case o.qd:return{...e,storageUsed:t.used};case o.u7:return{...e,isFetchingUploadedVideoCount:t.isFetchingUploadedVideoCount};case o.E9:return{...e,uploadedVideoCount:t.uploadedVideoCount,isFetchingUploadedVideoCount:!1};case o.NX:{const{id:a,title:n}=t,o=e?._meta||{},i=o?.items||{},s=(0,r.cleanForSlug)(n);return{...e,_meta:{...o,items:{...i,[a]:{title:s,uploading:!0}}}}}case o.SH:{const{id:a,error:n}=t,r=e?._meta||{},o=r?.items||{};return{...e,_meta:{...r,items:{...o,[a]:{...o[a],uploading:!1,error:n}}}}}case o.RZ:{const{id:a}=t,n=e?._meta||{},r=n?.items||{};return delete r[a],{...e,_meta:{...n,items:{...r}}}}case o.TI:{const{id:a,data:n}=t,o=e?.query??{order:"desc",orderBy:"date",itemsPerPage:6,page:1,type:"video/videopress"},i={...e.pagination},s=[...e?.items??[]],c=e?._meta||{},l=Object.assign({},c?.items||{}),d=n?.src?.split("/")?.slice(-1)?.[0]||l[a]?.title||"",u=(0,r.cleanForSlug)(d);let p=e?.uploadedVideoCount??0,m=e?.firstUploadedVideoId??null,h=e?.firstVideoProcessed??!1,g=e?.dismissedFirstVideoPopover??!1;return 0===p&&(m=n.id,h=!1,g=!1),1!==o?.page||o?.search||(p=(e?.uploadedVideoCount??0)+1,i.total=p,i.totalPages=Math.ceil(p/o?.itemsPerPage),s.unshift({id:n.id,guid:n.guid,url:n.src,title:u,posterImage:null,finished:!1})),delete l[a],{...e,items:s,uploadedVideoCount:p,firstUploadedVideoId:m,firstVideoProcessed:h,dismissedFirstVideoPopover:g,pagination:i,_meta:{...c,items:l}}}case o.Ko:{const{video:a}=t,n=[...e?.items??[]],r=n.findIndex((e=>e.id===a.id)),o=e?.firstUploadedVideoId??null;let i=e?.firstVideoProcessed??null;return a.id===o&&(i=!0),-1===r?{...e,firstVideoProcessed:i}:(n[r]=a,{...e,firstVideoProcessed:i,items:n})}case o.N1:{const{id:a}=t,n=e?._meta||{},r=n?.items||{},o=r[a]||{};return{...e,_meta:{...n,items:{...r,[a]:{...o,isUpdatingPoster:!0}}}}}case o.OU:{const{id:a,poster:n}=t,r=[...e.items??[]],o=e?._meta||{},i=o?.items||{},s=r.findIndex((e=>e.id===a));return s>=0&&(r[s]={...r[s],posterImage:n}),{...e,items:r,_meta:{...o,items:{...i,[a]:{isUpdatingPoster:!1}}}}}case o.TZ:{const{id:a,bytesSent:n,bytesTotal:r}=t,o=e?._meta||{},i=o?.items||{},s=i[a]||{},c=r>0?n/r:0;return{...e,_meta:{...o,items:{...i,[a]:{...s,uploadProgress:c}}}}}case o.IX:return{...e,dismissedFirstVideoPopover:!0,firstUploadedVideoId:null};default:return e}},localVideos:(e,t)=>{switch(t.type){case o.UB:{const{videos:a}=t;return{...e,items:a,isFetching:!1}}case o.k8:return{...e,isFetching:t.isFetching};case o.DE:return{...e,query:{...e.query,...t.query},_meta:{...e._meta,relyOnInitialState:!1}};case o.DX:return{...e,pagination:{...e.pagination,...t.pagination},_meta:{...e._meta,relyOnInitialState:!1}};case o.K:{const{id:a}=t,n=[...e?.items??[]],r=n.findIndex((e=>e.id===a));return-1===r?e:(n[r]={...n[r],isUploadedToVideoPress:!0},{...e,items:n,isFetching:!1})}}return e},purchases:(e,t)=>{switch(t.type){case o.en:return{...e,isFetching:t.isFetching};case o.GY:return{...e,items:t.purchases,isFetching:!1};default:return e}},users:(e,t)=>{switch(t.type){case o.q2:return{...e,items:t.users};case o.NS:return{...e,pagination:{...e?.pagination||{},...t.pagination}};default:return e}},playbackTokens:(e,t)=>{switch(t.type){case o.$N:return{...e,isFetching:t.isFetching};case o.h7:{const{playbackToken:a}=t,n=[...e.items??[]],r=n.findIndex((e=>e.guid===a.guid));return-1===r?n.unshift(a):n[r]={...n[r],...a},{...e,items:n,isFetching:!1}}case o.kp:{const{guid:a}=t,n=[...e.items??[]],r=n.findIndex((e=>e.guid===a));return r>-1&&n.splice(r,1),{...e,items:n,isFetching:!1}}default:return e}},siteSettings:(e,t)=>{if(t.type===o.Cj){const{videoPressSettings:a}=t;return{...e,...a}}return e}})},3132:(e,t,a)=>{"use strict";a.d(t,{A:()=>u});var n=a(1455),r=a.n(n),o=a(3832),i=a(8962),s=a(2354),c=a(8306);const{apiRoot:l}=window?.jetpackVideoPressInitialState||{};async function d(e,t,a){if(!e.needsPlaybackToken)return e;let n=await t.getPlaybackToken(e.guid);if(n){Number(n.issueTime)+864e5<Date.now()&&(await a.expirePlaybackToken(e.guid),n=await t.getPlaybackToken(e.guid))}return/metadata_token=/.test(e.posterImage)||(e.posterImage+=`?metadata_token=${n.token}`),/metadata_token=/.test(e.thumbnail)||(e.thumbnail+=`?metadata_token=${n.token}`),/metadata_token=/.test(e.url)||(e.url+=`?metadata_token=${n.token}`),e}const u={getStorageUsed:{isFulfilled:e=>e?.videos?._meta?.relyOnInitialState,fulfill:()=>async({dispatch:e})=>{try{const t=await r()({path:i.Dv});if(!t?.options?.videopress_storage_used)return;const a=t.options.videopress_storage_used?Math.round(1e3*Number(t.options.videopress_storage_used)*1e3):0;e.setVideosStorageUsed(a)}catch(e){console.error(e)}}},getUploadedVideoCount:{isFulfilled:e=>e?.videos?._meta?.relyOnInitialState,fulfill:()=>async({dispatch:e})=>{const t={per_page:1,media_type:"video",mime_type:"video/videopress"};e.setIsFetchingUploadedVideoCount(!0);try{const a=await fetch((0,o.addQueryArgs)(`${l}${i.$s}`,t)),n=Number(a.headers.get("X-WP-Total"));return e.setUploadedVideoCount(n),n}catch(e){console.error(e)}},shouldInvalidate:e=>e.type===i.TI||e.type===i.kb},getVideos:{isFulfilled:e=>e?.videos?._meta?.relyOnInitialState,fulfill:()=>async({dispatch:e,select:t,resolveSelect:a})=>{e.setIsFetchingVideos(!0);let n=t.getVideosQuery();n||(n=(0,s.S)(),e.setVideosQuery(n));const r={order:n.order,orderby:n.orderBy,page:n.page,per_page:n.itemsPerPage,media_type:"video",mime_type:"video/videopress"};"string"==typeof n.search&&n.search.length>0&&(r.search=n.search);const u=t.getVideosFilter(),p=Object.keys(u?.rating||{}).filter((e=>u.rating[e])).join(",");p?.length&&(r.videopress_rating=p);const m=Object.keys(u?.privacy||{}).filter((e=>u.privacy[e])).join(",");m?.length&&(r.videopress_privacy_setting=m);const h=Object.keys(u?.uploader||{}).filter((e=>u.uploader[e])).join(",");h?.length&&(r.author=h);try{const t=await fetch((0,o.addQueryArgs)(`${l}${i.$s}`,r)),n=Number(t.headers.get("X-WP-Total")),s=Number(t.headers.get("X-WP-TotalPages"));e.setVideosPagination({total:n,totalPages:s});const u=await t.json(),p=await Promise.all((0,c.HO)(u).map((async t=>await d(t,a,e))));return e.setVideos(p),u}catch(e){console.error(e)}},shouldInvalidate:({type:e})=>e===i.pz||e===i.kb||e===i.B1},getVideo:{isFulfilled:(e,t)=>{if(!t||"string"==typeof t)return!0;const a=(e.videos.items??[]).find((({id:e})=>e===t));if(a&&a.needsPlaybackToken){return!!(e?.playbackTokens?.items||[]).find((e=>e?.guid===a.guid))}return a},fulfill:(e,t=!1)=>async({dispatch:a,resolveSelect:n})=>{a.setIsFetchingVideos(!0);try{const s=await r()({path:(0,o.addQueryArgs)(`${i.$s}/${e}`)}),l=await d((0,c.ac)(s),n,a);return a.setVideo(l,t),s}catch(e){console.error(e)}}},getLocalVideos:{isFulfilled:e=>e?.localVideos?._meta?.relyOnInitialState,fulfill:()=>async({dispatch:e,select:t})=>{let a=t.getLocalVideosQuery();e.setIsFetchingLocalVideos(!0),a||(a=(0,s.S)(),e.setVideosQuery(a));const n={order:a.order,orderby:a.orderBy,page:a.page,per_page:a.itemsPerPage,media_type:"video",no_videopress:!0};"string"==typeof a.search&&a.search.length>0&&(n.search=a.search);try{const t=await fetch((0,o.addQueryArgs)(`${l}${i.$s}`,n)),a=Number(t.headers.get("X-WP-Total")),r=Number(t.headers.get("X-WP-TotalPages"));e.setLocalVideosPagination({total:a,totalPages:r});const s=await t.json();return e.setLocalVideos((0,c.Kk)(s)),s}catch(e){console.error(e)}},shouldInvalidate:e=>e.type===i.DE},getUsers:{isFulfilled:e=>e?.users?._meta?.relyOnInitialState,fulfill:()=>async({dispatch:e})=>{e.setIsFetchingLocalVideos(!0);try{const t=await fetch(`${l}${i.p9}`),a=Number(t.headers.get("X-WP-Total")),n=Number(t.headers.get("X-WP-TotalPages"));e.setUsersPagination({total:a,totalPages:n});const r=await t.json();if(!r?.length)return;return e.setUsers(r.map((e=>({id:e.id,name:e.name,slug:e.slug,description:e.description,link:e.link,avatar:e.avatar_urls})))),r}catch(e){console.error(e)}}},getPlaybackToken:{isFulfilled:(e,t)=>{const a=e?.playbackTokens?.items??[];return a?.some((e=>e?.guid===t))},fulfill:e=>async({dispatch:t})=>{t.setIsFetchingPlaybackToken(!0);try{const a=await r()({path:(0,o.addQueryArgs)(`${i.TC}/${e}`),method:"POST"}),n={guid:e,token:a.playback_token,issueTime:Date.now()};return t.setPlaybackToken(n),n}catch(e){console.error(e)}},shouldInvalidate:(e,t)=>e.type===i.kp&&e.guid===t},getVideoPressSettings:{isFulfilled:e=>void 0!==e?.siteSettings,fulfill:()=>async({dispatch:e})=>{try{const{videopress_videos_private_for_site:t}=await r()({path:(0,o.addQueryArgs)(`${i.pC}`),method:"GET"}),a={videoPressVideosPrivateForSite:t};return e.setVideoPressSettings(a),a}catch(e){console.error(e)}}}}},6225:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>r});const n=e=>e?.videos?.items||[],r={getVideos:n,getUploadingVideos:e=>{const t=e?.videos?._meta?.items||{};return Object.keys(t||{}).map((e=>({...t[e],id:e}))).filter((e=>e.uploading))},getVideosQuery:e=>e?.videos?.query,getPagination:e=>e?.videos?.pagination,getVideosFilter:e=>e?.videos?.filter,getUploadedVideoCount:e=>e?.videos?.uploadedVideoCount,getIsFetching:e=>e?.videos?.isFetching,getIsFetchingUploadedVideoCount:e=>e?.videos?.isFetchingUploadedVideoCount,getStorageUsed:e=>({storageUsed:e?.videos?.storageUsed}),getFirstUploadedVideoId:e=>e?.videos?.firstUploadedVideoId,getFirstVideoProcessed:e=>e?.videos?.firstVideoProcessed,getDismissedFirstVideoPopover:e=>e?.videos?.dismissedFirstVideoPopover,getProcessedAllVideosBeingRemoved:e=>e?.videos?._meta?.processedAllVideosBeingRemoved,getLocalVideos:e=>e?.localVideos?.items||[],getIsFetchingLocalVideos:e=>e?.localVideos?.isFetching,getLocalVideosQuery:e=>e?.localVideos?.query,getLocalPagination:e=>e?.localVideos?.pagination,getUploadedLocalVideoCount:e=>e?.localVideos?.uploadedVideoCount,getVideo:(e,t)=>n(e).find((({id:e})=>e===t)),getVideoStateMetadata:(e,t)=>(e?.videos?._meta?.items||{})[t]||{},getUsers:e=>e?.users?.items||[],getUsersPagination:e=>e?.users?.pagination,getPurchases:e=>e?.purchases?.items||[],isFetchingPurchases:e=>e?.purchases?.isFetching,getPlaybackToken:(e,t)=>(e?.playbackTokens?.items||[]).find((e=>e?.guid===t))||{},isFetchingPlaybackToken:e=>e?.playbackTokens?.isFetching,getVideoPressSettings:e=>e?.siteSettings,getUploadErrorVideos:e=>{const t=e?.videos?._meta?.items||{};return Object.keys(t||{}).map((e=>({...t[e],id:e}))).filter((e=>!!e.error))}}},1105:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(7143);class r{static store=null;static mayBeInit(e,t){null===r.store&&(r.store=(0,n.createReduxStore)(e,t),(0,n.register)(r.store))}}const o=r},8306:(e,t,a)=>{"use strict";a.d(t,{HO:()=>r,Kk:()=>i,ac:()=>n});const n=e=>{const{media_details:t,id:a,jetpack_videopress:n,jetpack_videopress_guid:r}=e,{videopress:o,width:i,height:s}=t,{title:c,description:l,caption:d,rating:u,allow_download:p,display_embed:m,privacy_setting:h,needs_playback_token:g,is_private:v}=n,{original:f,poster:y,upload_date:b,duration:w,file_url_base:E,finished:A,files:k={dvd:{original_img:""}}}=o||{},{dvd:R}=k,C=R?.original_img?`${E.https}${R.original_img}`:void 0,S=f?.split("/").slice(-1)[0];return{id:a,guid:r,title:c,description:l,caption:d,url:f,uploadDate:b,duration:w,isPrivate:v,posterImage:y,allowDownload:p,displayEmbed:m,rating:u,privacySetting:h,needsPlaybackToken:g,width:i,height:s,poster:{src:y},thumbnail:C,finished:A,filename:S}},r=e=>e?.map?.(n),o=e=>{const{media_details:t,id:a,jetpack_videopress:n,source_url:r,date:o}=e,{width:i,height:s,length:c}=t,{title:l,description:d,caption:u}=n;return{id:a,title:l,description:d,caption:u,width:i,height:s,url:r,uploadDate:o,duration:c}},i=e=>e.map(o)},4603:(e,t,a)=>{"use strict";a.d(t,{D:()=>r});var n=a(8962);const r=(e,t)=>n.zG[e]!==n.az&&(n.zG[e]===n.o0||t)},4523:(e,t,a)=>{"use strict";function n(e){return e.replace(/([-_][a-z])/gi,(e=>e.toUpperCase().replace("_","")))}function r(e,t=!1){const a=Object.assign({},e);for(const e in a)Object.hasOwn(a,e)&&-1!==e.indexOf("_")&&(a[n(e)]=a[e],t&&delete a[e]);return a}a.d(t,{x:()=>r})},3356:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n=()=>{const e=new Uint32Array(10);crypto.getRandomValues(e);return`${performance.now().toString(36)}-${Array.from(e).map((e=>e.toString(36))).join("")}`}},9799:(e,t,a)=>{"use strict";function n(e){if(!e)return[];return e.split("\n").map((e=>function(e){const t=/(?<timeBlock>\(?(?<time>\d{1,2}:\d{2}:\d{2}|\d{1,2}:\d{2})\)?)/.exec(e);if(null==t||null==t.groups)return null;const{groups:{timeBlock:a,time:n}}=t,r=e.indexOf(a),o=(r<(e.length-a.length)/2?e.substring(r+a.length,e.length):e.substring(0,r)).trim().replace(/(\s-$)|(^-\s)/,""),i=n.split(":");return 1===i[0].length&&(i[0]=`0${i[0]}`),2===i.length&&i.unshift("00"),{startAt:i.join(":"),title:o}}(e))).filter((e=>null!==e)).sort(((e,t)=>e.startAt.localeCompare(t.startAt)))}a.d(t,{$k:()=>n,Ay:()=>n})},6302:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>i});var n=a(9799);function r(e){const t=Math.floor(e/36e5);let a=e-36e5*t;const n=Math.floor(a/6e4);a-=6e4*n;return[t,n,Math.floor(a/1e3)].map((e=>e<10?`0${e}`:e)).join(":")}const o="videopress-chapters-auto-generated";function i(e,t){const a=function(e,t=3599999e3){const a=(0,n.$k)(e);if(0===a.length)return null;let i="WEBVTT\n";i+="\n",i+="NOTE\n",i+=`${o}\n`,i+="This file was auto-generated based on Video description.\n",i+="For more information, see https://jetpack.com/support/jetpack-videopress/jetpack-videopress-customizing-your-videos/#adding-subtitles-captions-or-chapters-within-a-video\n";let s=1;for(const[e,n]of a.entries()){const o=0===e?"000":"001",c=e<a.length-1?a[e+1].startAt:r(t);i+=`\n${s++}\n${n.startAt}.${o} --\x3e ${c}.000\n${n.title}\n`}return i}(e,t);return a?new File([a],"chapters.vtt",{type:"text/vtt"}):null}},2066:(e,t,a)=>{"use strict";function n(e){const t=e.split(":");return 3600*parseInt(t[0])+60*parseInt(t[1])+parseInt(t[2])}function r(e){if(!e||0===e.length)return!1;if("00:00:00"!==e[0].startAt)return!1;if(e.length<3)return!1;if(e.some((e=>!e.title)))return!1;for(let t=0;t<e.length-1;t++){const a=e[t];if(n(e[t+1].startAt)-n(a.startAt)<10)return!1}return!0}a.d(t,{A:()=>r})},7770:(e,t,a)=>{"use strict";a.d(t,{SK:()=>r,Zd:()=>o});const n=window.jetpackVideoPressInitialState?.allowedVideoExtensions||{},r=Object.keys(n).filter((e=>"videopress"!==e)),o=r.map((e=>`.${e}`)).join(",")},1016:(e,t,a)=>{"use strict";a.d(t,{A:()=>p});var n=a(4085),r=a(5217),o=a(3569),i=a(615);function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function c(){c=function(){return t};var e,t={},a=Object.prototype,n=a.hasOwnProperty,r=Object.defineProperty||function(e,t,a){e[t]=a.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",d=o.toStringTag||"@@toStringTag";function u(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,a){return e[t]=a}}function p(e,t,a,n){var o=t&&t.prototype instanceof b?t:b,i=Object.create(o.prototype),s=new V(n||[]);return r(i,"_invoke",{value:x(e,a,s)}),i}function m(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(e){return{type:"throw",arg:e}}}t.wrap=p;var h="suspendedStart",g="suspendedYield",v="executing",f="completed",y={};function b(){}function w(){}function E(){}var A={};u(A,i,(function(){return this}));var k=Object.getPrototypeOf,R=k&&k(k(z([])));R&&R!==a&&n.call(R,i)&&(A=R);var C=E.prototype=b.prototype=Object.create(A);function S(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){function a(r,o,i,c){var l=m(e[r],e,o);if("throw"!==l.type){var d=l.arg,u=d.value;return u&&"object"==s(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){a("next",e,i,c)}),(function(e){a("throw",e,i,c)})):t.resolve(u).then((function(e){d.value=e,i(d)}),(function(e){return a("throw",e,i,c)}))}c(l.arg)}var o;r(this,"_invoke",{value:function(e,n){function r(){return new t((function(t,r){a(e,n,t,r)}))}return o=o?o.then(r,r):r()}})}function x(t,a,n){var r=h;return function(o,i){if(r===v)throw Error("Generator is already running");if(r===f){if("throw"===o)throw i;return{value:e,done:!0}}for(n.method=o,n.arg=i;;){var s=n.delegate;if(s){var c=P(s,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(r===h)throw r=f,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=v;var l=m(t,a,n);if("normal"===l.type){if(r=n.done?f:g,l.arg===y)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(r=f,n.method="throw",n.arg=l.arg)}}}function P(t,a){var n=a.method,r=t.iterator[n];if(r===e)return a.delegate=null,"throw"===n&&t.iterator.return&&(a.method="return",a.arg=e,P(t,a),"throw"===a.method)||"return"!==n&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var o=m(r,t.iterator,a.arg);if("throw"===o.type)return a.method="throw",a.arg=o.arg,a.delegate=null,y;var i=o.arg;return i?i.done?(a[t.resultName]=i.value,a.next=t.nextLoc,"return"!==a.method&&(a.method="next",a.arg=e),a.delegate=null,y):i:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,y)}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function V(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function z(t){if(t||""===t){var a=t[i];if(a)return a.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,o=function a(){for(;++r<t.length;)if(n.call(t,r))return a.value=t[r],a.done=!1,a;return a.value=e,a.done=!0,a};return o.next=o}}throw new TypeError(s(t)+" is not iterable")}return w.prototype=E,r(C,"constructor",{value:E,configurable:!0}),r(E,"constructor",{value:w,configurable:!0}),w.displayName=u(E,d,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,E):(e.__proto__=E,u(e,d,"GeneratorFunction")),e.prototype=Object.create(C),e},t.awrap=function(e){return{__await:e}},S(_.prototype),u(_.prototype,l,(function(){return this})),t.AsyncIterator=_,t.async=function(e,a,n,r,o){void 0===o&&(o=Promise);var i=new _(p(e,a,n,r),o);return t.isGeneratorFunction(a)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},S(C),u(C,d,"Generator"),u(C,i,(function(){return this})),u(C,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),a=[];for(var n in t)a.push(n);return a.reverse(),function e(){for(;a.length;){var n=a.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=z,V.prototype={constructor:V,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var a in this)"t"===a.charAt(0)&&n.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var a=this;function r(n,r){return s.type="throw",s.arg=t,a.next=n,r&&(a.method="next",a.arg=e),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var a=this.tryEntries.length-1;a>=0;--a){var r=this.tryEntries[a];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),N(a),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var n=a.completion;if("throw"===n.type){var r=n.arg;N(a)}return r}}throw Error("illegal catch attempt")},delegateYield:function(t,a,n){return this.delegate={iterator:z(t),resultName:a,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}function l(e,t,a,n,r,o,i){try{var s=e[o](i),c=s.value}catch(e){return void a(e)}s.done?t(c):Promise.resolve(c).then(n,r)}function d(e,t){for(var a=0;a<t.length;a++){var n=t[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,u(n.key),n)}}function u(e){var t=function(e,t){if("object"!=s(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var n=a.call(e,t||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==s(t)?t:t+""}var p=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)},t=[{key:"openFile",value:(s=c().mark((function e(t,a){var s;return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(0,n.A)()||!t||void 0===t.uri){e.next=11;break}return e.prev=1,e.next=4,(0,r.A)(t.uri);case 4:return s=e.sent,e.abrupt("return",new o.A(s));case 8:throw e.prev=8,e.t0=e.catch(1),new Error("tus: cannot fetch `file.uri` as Blob, make sure the uri is correct and accessible. ".concat(e.t0));case 11:if("function"!=typeof t.slice||void 0===t.size){e.next=13;break}return e.abrupt("return",Promise.resolve(new o.A(t)));case 13:if("function"!=typeof t.read){e.next=18;break}if(a=Number(a),Number.isFinite(a)){e.next=17;break}return e.abrupt("return",Promise.reject(new Error("cannot create source for stream without a finite value for the `chunkSize` option")));case 17:return e.abrupt("return",Promise.resolve(new i.A(t,a)));case 18:return e.abrupt("return",Promise.reject(new Error("source object may only be an instance of File, Blob, or Reader in this environment")));case 19:case"end":return e.stop()}}),e,null,[[1,8]])})),u=function(){var e=this,t=arguments;return new Promise((function(a,n){var r=s.apply(e,t);function o(e){l(r,a,n,o,i,"next",e)}function i(e){l(r,a,n,o,i,"throw",e)}o(void 0)}))},function(_x,e){return u.apply(this,arguments)})}],t&&d(e.prototype,t),a&&d(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,a,s,u}()},3765:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var n=a(4085);function r(e,t){return(0,n.A)()?Promise.resolve(function(e,t){var a=e.exif?function(e){var t=0;if(0===e.length)return t;for(var a=0;a<e.length;a++){t=(t<<5)-t+e.charCodeAt(a),t&=t}return t}(JSON.stringify(e.exif)):"noexif";return["tus-rn",e.name||"noname",e.size||"nosize",a,t.endpoint].join("/")}(e,t)):Promise.resolve(["tus-br",e.name,e.type,e.size,e.lastModified,t.endpoint].join("-"))}},5369:(e,t,a)=>{"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var a=0;a<t.length;a++){var n=t[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,s(n.key),n)}}function i(e,t,a){return t&&o(e.prototype,t),a&&o(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e}function s(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var r=a.call(e,t||"default");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:t+""}a.d(t,{A:()=>c});var c=function(){return i((function e(){r(this,e)}),[{key:"createRequest",value:function(e,t){return new l(e,t)}},{key:"getName",value:function(){return"XHRHttpStack"}}])}(),l=function(){return i((function e(t,a){r(this,e),this._xhr=new XMLHttpRequest,this._xhr.open(t,a,!0),this._method=t,this._url=a,this._headers={}}),[{key:"getMethod",value:function(){return this._method}},{key:"getURL",value:function(){return this._url}},{key:"setHeader",value:function(e,t){this._xhr.setRequestHeader(e,t),this._headers[e]=t}},{key:"getHeader",value:function(e){return this._headers[e]}},{key:"setProgressHandler",value:function(e){"upload"in this._xhr&&(this._xhr.upload.onprogress=function(t){t.lengthComputable&&e(t.loaded)})}},{key:"send",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return new Promise((function(a,n){e._xhr.onload=function(){a(new d(e._xhr))},e._xhr.onerror=function(e){n(e)},e._xhr.send(t)}))}},{key:"abort",value:function(){return this._xhr.abort(),Promise.resolve()}},{key:"getUnderlyingObject",value:function(){return this._xhr}}])}(),d=function(){return i((function e(t){r(this,e),this._xhr=t}),[{key:"getStatus",value:function(){return this._xhr.status}},{key:"getHeader",value:function(e){return this._xhr.getResponseHeader(e)}},{key:"getBody",value:function(){return this._xhr.responseText}},{key:"getUnderlyingObject",value:function(){return this._xhr}}])}()},781:(e,t,a)=>{"use strict";a.d(t,{_O:()=>w});var n=a(2494),r=a(5082),o=a(1016),i=a(3765),s=a(5369),c=a(851);function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function d(e,t){for(var a=0;a<t.length;a++){var n=t[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,y(n.key),n)}}function u(e,t,a){return t=m(t),function(e,t){if(t&&("object"===l(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,p()?Reflect.construct(t,a||[],m(e).constructor):t.apply(e,a))}function p(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(p=function(){return!!e})()}function m(e){return m=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},m(e)}function h(e,t){return h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},h(e,t)}function g(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function v(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?g(Object(a),!0).forEach((function(t){f(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):g(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function f(e,t,a){return(t=y(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function y(e){var t=function(e,t){if("object"!=l(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var n=a.call(e,t||"default");if("object"!=l(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==l(t)?t:t+""}var b=v(v({},r.A.defaultOptions),{},{httpStack:new s.A,fileReader:new o.A,urlStorage:c.o?new c.Y:new n.A,fingerprint:i.A}),w=function(e){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),u(this,t,[e,a=v(v({},b),a)])}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&h(e,t)}(t,e),a=t,o=[{key:"terminate",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t=v(v({},b),t),r.A.terminate(e,t)}}],(n=null)&&d(a.prototype,n),o&&d(a,o),Object.defineProperty(a,"prototype",{writable:!1}),a;var a,n,o}(r.A);"function"==typeof XMLHttpRequest&&"function"==typeof Blob&&Blob.prototype.slice},4085:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n=function(){return"undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase()}},3569:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var n=a(3716),r=a(9427);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function i(e,t){for(var a=0;a<t.length;a++){var n=t[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,s(n.key),n)}}function s(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var n=a.call(e,t||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:t+""}var c=function(){return e=function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._file=t,this.size=t.size},(t=[{key:"slice",value:function(e,t){if((0,n.A)())return(0,r.A)(this._file.slice(e,t));var a=this._file.slice(e,t),o=t>=this.size;return Promise.resolve({value:a,done:o})}},{key:"close",value:function(){}}])&&i(e.prototype,t),a&&i(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,a}()},615:(e,t,a)=>{"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function r(e,t){for(var a=0;a<t.length;a++){var n=t[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o(n.key),n)}}function o(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var r=a.call(e,t||"default");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:t+""}function i(e){return void 0===e?0:void 0!==e.size?e.size:e.length}a.d(t,{A:()=>s});var s=function(){return e=function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._buffer=void 0,this._bufferOffset=0,this._reader=t,this._done=!1},(t=[{key:"slice",value:function(e,t){return e<this._bufferOffset?Promise.reject(new Error("Requested data is before the reader's current offset")):this._readUntilEnoughDataOrDone(e,t)}},{key:"_readUntilEnoughDataOrDone",value:function(e,t){var a=this,n=t<=this._bufferOffset+i(this._buffer);if(this._done||n){var r=this._getDataFromBuffer(e,t),o=null==r&&this._done;return Promise.resolve({value:r,done:o})}return this._reader.read().then((function(n){var r=n.value;return n.done?a._done=!0:void 0===a._buffer?a._buffer=r:a._buffer=function(e,t){if(e.concat)return e.concat(t);if(e instanceof Blob)return new Blob([e,t],{type:e.type});if(e.set){var a=new e.constructor(e.length+t.length);return a.set(e),a.set(t,e.length),a}throw new Error("Unknown data type")}(a._buffer,r),a._readUntilEnoughDataOrDone(e,t)}))}},{key:"_getDataFromBuffer",value:function(e,t){e>this._bufferOffset&&(this._buffer=this._buffer.slice(e-this._bufferOffset),this._bufferOffset=e);var a=0===i(this._buffer);return this._done&&a?null:this._buffer.slice(0,t-e)}},{key:"close",value:function(){this._reader.cancel&&this._reader.cancel()}}])&&r(e.prototype,t),a&&r(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,a}()},3716:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});const n=function(){return"undefined"!=typeof window&&(void 0!==window.PhoneGap||void 0!==window.Cordova||void 0!==window.cordova)}},9427:(e,t,a)=>{"use strict";function n(e){return new Promise((function(t,a){var n=new FileReader;n.onload=function(){var e=new Uint8Array(n.result);t({value:e})},n.onerror=function(e){a(e)},n.readAsArrayBuffer(e)}))}a.d(t,{A:()=>n})},5217:(e,t,a)=>{"use strict";function n(e){return new Promise((function(t,a){var n=new XMLHttpRequest;n.responseType="blob",n.onload=function(){var e=n.response;t(e)},n.onerror=function(e){a(e)},n.open("GET",e),n.send()}))}a.d(t,{A:()=>n})},851:(e,t,a)=>{"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function r(e,t){for(var a=0;a<t.length;a++){var n=t[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o(n.key),n)}}function o(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var r=a.call(e,t||"default");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:t+""}a.d(t,{Y:()=>d,o:()=>l});var i=!1;try{i="localStorage"in window;var s="tusSupport",c=localStorage.getItem(s);localStorage.setItem(s,c),null===c&&localStorage.removeItem(s)}catch(e){if(e.code!==e.SECURITY_ERR&&e.code!==e.QUOTA_EXCEEDED_ERR)throw e;i=!1}var l=i,d=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)},t=[{key:"findAllUploads",value:function(){var e=this._findEntries("tus::");return Promise.resolve(e)}},{key:"findUploadsByFingerprint",value:function(e){var t=this._findEntries("tus::".concat(e,"::"));return Promise.resolve(t)}},{key:"removeUpload",value:function(e){return localStorage.removeItem(e),Promise.resolve()}},{key:"addUpload",value:function(e,t){var a=Math.round(1e12*Math.random()),n="tus::".concat(e,"::").concat(a);return localStorage.setItem(n,JSON.stringify(t)),Promise.resolve(n)}},{key:"_findEntries",value:function(e){for(var t=[],a=0;a<localStorage.length;a++){var n=localStorage.key(a);if(0===n.indexOf(e))try{var r=JSON.parse(localStorage.getItem(n));r.urlStorageKey=n,t.push(r)}catch(e){}}return t}}],t&&r(e.prototype,t),a&&r(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,a}()},3004:(e,t,a)=>{"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function r(e,t){for(var a=0;a<t.length;a++){var n=t[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o(n.key),n)}}function o(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var r=a.call(e,t||"default");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:t+""}function i(e,t,a){return t=d(t),function(e,t){if(t&&("object"===n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,c()?Reflect.construct(t,a||[],d(e).constructor):t.apply(e,a))}function s(e){var t="function"==typeof Map?new Map:void 0;return s=function(e){if(null===e||!function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,a)}function a(){return function(e,t,a){if(c())return Reflect.construct.apply(null,arguments);var n=[null];n.push.apply(n,t);var r=new(e.bind.apply(e,n));return a&&l(r,a.prototype),r}(e,arguments,d(this).constructor)}return a.prototype=Object.create(e.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),l(a,e)},s(e)}function c(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(c=function(){return!!e})()}function l(e,t){return l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},l(e,t)}function d(e){return d=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},d(e)}a.d(t,{A:()=>u});const u=function(e){function t(e){var a,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),(a=i(this,t,[e])).originalRequest=r,a.originalResponse=o,a.causingError=n,null!=n&&(e+=", caused by ".concat(n.toString())),null!=r){var s=r.getHeader("X-Request-ID")||"n/a",c=r.getMethod(),l=r.getURL(),d=o?o.getStatus():"n/a",u=o?o.getBody()||"":"n/a";e+=", originated from request (method: ".concat(c,", url: ").concat(l,", response code: ").concat(d,", response text: ").concat(u,", request id: ").concat(s,")")}return a.message=e,a}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&l(e,t)}(t,e),a=t,n&&r(a.prototype,n),o&&r(a,o),Object.defineProperty(a,"prototype",{writable:!1}),a;var a,n,o}(s(Error))},2380:(e,t,a)=>{"use strict";a.d(t,{R:()=>r});var n=!1;function r(e){n&&console.log(e)}},2494:(e,t,a)=>{"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function r(e,t){for(var a=0;a<t.length;a++){var n=t[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o(n.key),n)}}function o(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var r=a.call(e,t||"default");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:t+""}a.d(t,{A:()=>i});var i=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)},(t=[{key:"listAllUploads",value:function(){return Promise.resolve([])}},{key:"findUploadsByFingerprint",value:function(e){return Promise.resolve([])}},{key:"removeUpload",value:function(e){return Promise.resolve()}},{key:"addUpload",value:function(e,t){return Promise.resolve(null)}}])&&r(e.prototype,t),a&&r(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,a}()},5082:(e,t,a)=>{"use strict";a.d(t,{A:()=>V});var n=a(9535),r=a(6580),o=a.n(r),i=a(3004),s=a(2380),c=a(7291);function l(){l=function(){return t};var e,t={},a=Object.prototype,n=a.hasOwnProperty,r=Object.defineProperty||function(e,t,a){e[t]=a.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function d(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,a){return e[t]=a}}function u(e,t,a,n){var o=t&&t.prototype instanceof b?t:b,i=Object.create(o.prototype),s=new V(n||[]);return r(i,"_invoke",{value:x(e,a,s)}),i}function m(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var h="suspendedStart",g="suspendedYield",v="executing",f="completed",y={};function b(){}function w(){}function E(){}var A={};d(A,i,(function(){return this}));var k=Object.getPrototypeOf,R=k&&k(k(z([])));R&&R!==a&&n.call(R,i)&&(A=R);var C=E.prototype=b.prototype=Object.create(A);function S(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){function a(r,o,i,s){var c=m(e[r],e,o);if("throw"!==c.type){var l=c.arg,d=l.value;return d&&"object"==p(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){a("next",e,i,s)}),(function(e){a("throw",e,i,s)})):t.resolve(d).then((function(e){l.value=e,i(l)}),(function(e){return a("throw",e,i,s)}))}s(c.arg)}var o;r(this,"_invoke",{value:function(e,n){function r(){return new t((function(t,r){a(e,n,t,r)}))}return o=o?o.then(r,r):r()}})}function x(t,a,n){var r=h;return function(o,i){if(r===v)throw Error("Generator is already running");if(r===f){if("throw"===o)throw i;return{value:e,done:!0}}for(n.method=o,n.arg=i;;){var s=n.delegate;if(s){var c=P(s,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(r===h)throw r=f,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=v;var l=m(t,a,n);if("normal"===l.type){if(r=n.done?f:g,l.arg===y)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(r=f,n.method="throw",n.arg=l.arg)}}}function P(t,a){var n=a.method,r=t.iterator[n];if(r===e)return a.delegate=null,"throw"===n&&t.iterator.return&&(a.method="return",a.arg=e,P(t,a),"throw"===a.method)||"return"!==n&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var o=m(r,t.iterator,a.arg);if("throw"===o.type)return a.method="throw",a.arg=o.arg,a.delegate=null,y;var i=o.arg;return i?i.done?(a[t.resultName]=i.value,a.next=t.nextLoc,"return"!==a.method&&(a.method="next",a.arg=e),a.delegate=null,y):i:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,y)}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function V(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function z(t){if(t||""===t){var a=t[i];if(a)return a.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,o=function a(){for(;++r<t.length;)if(n.call(t,r))return a.value=t[r],a.done=!1,a;return a.value=e,a.done=!0,a};return o.next=o}}throw new TypeError(p(t)+" is not iterable")}return w.prototype=E,r(C,"constructor",{value:E,configurable:!0}),r(E,"constructor",{value:w,configurable:!0}),w.displayName=d(E,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,E):(e.__proto__=E,d(e,c,"GeneratorFunction")),e.prototype=Object.create(C),e},t.awrap=function(e){return{__await:e}},S(_.prototype),d(_.prototype,s,(function(){return this})),t.AsyncIterator=_,t.async=function(e,a,n,r,o){void 0===o&&(o=Promise);var i=new _(u(e,a,n,r),o);return t.isGeneratorFunction(a)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},S(C),d(C,c,"Generator"),d(C,i,(function(){return this})),d(C,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),a=[];for(var n in t)a.push(n);return a.reverse(),function e(){for(;a.length;){var n=a.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=z,V.prototype={constructor:V,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var a in this)"t"===a.charAt(0)&&n.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var a=this;function r(n,r){return s.type="throw",s.arg=t,a.next=n,r&&(a.method="next",a.arg=e),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var a=this.tryEntries.length-1;a>=0;--a){var r=this.tryEntries[a];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),N(a),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var n=a.completion;if("throw"===n.type){var r=n.arg;N(a)}return r}}throw Error("illegal catch attempt")},delegateYield:function(t,a,n){return this.delegate={iterator:z(t),resultName:a,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}function d(e,t,a,n,r,o,i){try{var s=e[o](i),c=s.value}catch(e){return void a(e)}s.done?t(c):Promise.resolve(c).then(n,r)}function u(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var n,r,o,i,s=[],c=!0,l=!1;try{if(o=(a=a.call(e)).next,0===t){if(Object(a)!==a)return;c=!1}else for(;!(c=(n=o.call(a)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(e){l=!0,r=e}finally{try{if(!c&&null!=a.return&&(i=a.return(),Object(i)!==i))return}finally{if(l)throw r}}return s}}(e,t)||m(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}function m(e,t){if(e){if("string"==typeof e)return h(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?h(e,t):void 0}}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=new Array(t);a<t;a++)n[a]=e[a];return n}function g(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function v(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?g(Object(a),!0).forEach((function(t){f(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):g(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function f(e,t,a){return(t=b(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function y(e,t){for(var a=0;a<t.length;a++){var n=t[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,b(n.key),n)}}function b(e){var t=function(e,t){if("object"!=p(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var n=a.call(e,t||"default");if("object"!=p(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==p(t)?t:t+""}var w="tus-v1",E="ietf-draft-03",A={endpoint:null,uploadUrl:null,metadata:{},metadataForPartialUploads:{},fingerprint:null,uploadSize:null,onProgress:null,onChunkComplete:null,onSuccess:null,onError:null,onUploadUrlAvailable:null,overridePatchMethod:!1,headers:{},addRequestId:!1,onBeforeRequest:null,onAfterResponse:null,onShouldRetry:j,chunkSize:Number.POSITIVE_INFINITY,retryDelays:[0,1e3,3e3,5e3],parallelUploads:1,parallelUploadBoundaries:null,storeFingerprintForResuming:!0,removeFingerprintOnSuccess:!1,uploadLengthDeferred:!1,uploadDataDuringCreation:!1,urlStorage:null,fileReader:null,httpStack:null,protocol:w},k=function(){function e(t,a){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),"resume"in a&&console.log("tus: The `resume` option has been removed in tus-js-client v2. Please use the URL storage API instead."),this.options=a,this.options.chunkSize=Number(this.options.chunkSize),this._urlStorage=this.options.urlStorage,this.file=t,this.url=null,this._req=null,this._fingerprint=null,this._urlStorageKey=null,this._offset=null,this._aborted=!1,this._size=null,this._source=null,this._retryAttempt=0,this._retryTimeout=null,this._offsetBeforeRetry=0,this._parallelUploads=null,this._parallelUploadUrls=null}return t=e,a=[{key:"findPreviousUploads",value:function(){var e=this;return this.options.fingerprint(this.file,this.options).then((function(t){return e._urlStorage.findUploadsByFingerprint(t)}))}},{key:"resumeFromPreviousUpload",value:function(e){this.url=e.uploadUrl||null,this._parallelUploadUrls=e.parallelUploadUrls||null,this._urlStorageKey=e.urlStorageKey}},{key:"start",value:function(){var e=this,t=this.file;if(t)if([w,E].includes(this.options.protocol))if(this.options.endpoint||this.options.uploadUrl||this.url){var a=this.options.retryDelays;if(null==a||"[object Array]"===Object.prototype.toString.call(a)){if(this.options.parallelUploads>1)for(var n=0,r=["uploadUrl","uploadSize","uploadLengthDeferred"];n<r.length;n++){var o=r[n];if(this.options[o])return void this._emitError(new Error("tus: cannot use the ".concat(o," option when parallelUploads is enabled")))}if(this.options.parallelUploadBoundaries){if(this.options.parallelUploads<=1)return void this._emitError(new Error("tus: cannot use the `parallelUploadBoundaries` option when `parallelUploads` is disabled"));if(this.options.parallelUploads!==this.options.parallelUploadBoundaries.length)return void this._emitError(new Error("tus: the `parallelUploadBoundaries` must have the same length as the value of `parallelUploads`"))}this.options.fingerprint(t,this.options).then((function(a){return null==a?(0,s.R)("No fingerprint was calculated meaning that the upload cannot be stored in the URL storage."):(0,s.R)("Calculated fingerprint: ".concat(a)),e._fingerprint=a,e._source?e._source:e.options.fileReader.openFile(t,e.options.chunkSize)})).then((function(t){if(e._source=t,e.options.uploadLengthDeferred)e._size=null;else if(null!=e.options.uploadSize){if(e._size=Number(e.options.uploadSize),Number.isNaN(e._size))return void e._emitError(new Error("tus: cannot convert `uploadSize` option into a number"))}else if(e._size=e._source.size,null==e._size)return void e._emitError(new Error("tus: cannot automatically derive upload's size from input. Specify it manually using the `uploadSize` option or use the `uploadLengthDeferred` option"));e.options.parallelUploads>1||null!=e._parallelUploadUrls?e._startParallelUpload():e._startSingleUpload()})).catch((function(t){e._emitError(t)}))}else this._emitError(new Error("tus: the `retryDelays` option must either be an array or null"))}else this._emitError(new Error("tus: neither an endpoint or an upload URL is provided"));else this._emitError(new Error("tus: unsupported protocol ".concat(this.options.protocol)));else this._emitError(new Error("tus: no file or stream to upload provided"))}},{key:"_startParallelUpload",value:function(){var t,a=this,n=this._size,r=0;this._parallelUploads=[];var o=null!=this._parallelUploadUrls?this._parallelUploadUrls.length:this.options.parallelUploads,i=null!==(t=this.options.parallelUploadBoundaries)&&void 0!==t?t:function(e,t){for(var a=Math.floor(e/t),n=[],r=0;r<t;r++)n.push({start:a*r,end:a*(r+1)});return n[t-1].end=e,n}(this._source.size,o);this._parallelUploadUrls&&i.forEach((function(e,t){e.uploadUrl=a._parallelUploadUrls[t]||null})),this._parallelUploadUrls=new Array(i.length);var c,l=i.map((function(t,o){var s=0;return a._source.slice(t.start,t.end).then((function(c){var l=c.value;return new Promise((function(c,d){var u=v(v({},a.options),{},{uploadUrl:t.uploadUrl||null,storeFingerprintForResuming:!1,removeFingerprintOnSuccess:!1,parallelUploads:1,parallelUploadBoundaries:null,metadata:a.options.metadataForPartialUploads,headers:v(v({},a.options.headers),{},{"Upload-Concat":"partial"}),onSuccess:c,onError:d,onProgress:function(e){r=r-s+e,s=e,a._emitProgress(r,n)},onUploadUrlAvailable:function(){a._parallelUploadUrls[o]=p.url,a._parallelUploadUrls.filter((function(e){return Boolean(e)})).length===i.length&&a._saveUploadInUrlStorage()}}),p=new e(l,u);p.start(),a._parallelUploads.push(p)}))}))}));Promise.all(l).then((function(){(c=a._openRequest("POST",a.options.endpoint)).setHeader("Upload-Concat","final;".concat(a._parallelUploadUrls.join(" ")));var e=R(a.options.metadata);return""!==e&&c.setHeader("Upload-Metadata",e),a._sendRequest(c,null)})).then((function(e){if(C(e.getStatus(),200)){var t=e.getHeader("Location");null!=t?(a.url=N(a.options.endpoint,t),(0,s.R)("Created upload at ".concat(a.url)),a._emitSuccess(e)):a._emitHttpError(c,e,"tus: invalid or missing Location header")}else a._emitHttpError(c,e,"tus: unexpected response while creating upload")})).catch((function(e){a._emitError(e)}))}},{key:"_startSingleUpload",value:function(){return this._aborted=!1,null!=this.url?((0,s.R)("Resuming upload from previous URL: ".concat(this.url)),void this._resumeUpload()):null!=this.options.uploadUrl?((0,s.R)("Resuming upload from provided URL: ".concat(this.options.uploadUrl)),this.url=this.options.uploadUrl,void this._resumeUpload()):((0,s.R)("Creating a new upload"),void this._createUpload())}},{key:"abort",value:function(t){var a=this;if(null!=this._parallelUploads){var n,r=function(e,t){var a="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!a){if(Array.isArray(e)||(a=m(e))||t&&e&&"number"==typeof e.length){a&&(e=a);var n=0,r=function(){};return{s:r,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,s=!1;return{s:function(){a=a.call(e)},n:function(){var e=a.next();return i=e.done,e},e:function(e){s=!0,o=e},f:function(){try{i||null==a.return||a.return()}finally{if(s)throw o}}}}(this._parallelUploads);try{for(r.s();!(n=r.n()).done;)n.value.abort(t)}catch(e){r.e(e)}finally{r.f()}}return null!==this._req&&this._req.abort(),this._aborted=!0,null!=this._retryTimeout&&(clearTimeout(this._retryTimeout),this._retryTimeout=null),t&&null!=this.url?e.terminate(this.url,this.options).then((function(){return a._removeFromUrlStorage()})):Promise.resolve()}},{key:"_emitHttpError",value:function(e,t,a,n){this._emitError(new i.A(a,n,e,t))}},{key:"_emitError",value:function(e){var t=this;if(!this._aborted){if(null!=this.options.retryDelays&&(null!=this._offset&&this._offset>this._offsetBeforeRetry&&(this._retryAttempt=0),P(e,this._retryAttempt,this.options))){var a=this.options.retryDelays[this._retryAttempt++];return this._offsetBeforeRetry=this._offset,void(this._retryTimeout=setTimeout((function(){t.start()}),a))}if("function"!=typeof this.options.onError)throw e;this.options.onError(e)}}},{key:"_emitSuccess",value:function(e){this.options.removeFingerprintOnSuccess&&this._removeFromUrlStorage(),"function"==typeof this.options.onSuccess&&this.options.onSuccess({lastResponse:e})}},{key:"_emitProgress",value:function(e,t){"function"==typeof this.options.onProgress&&this.options.onProgress(e,t)}},{key:"_emitChunkComplete",value:function(e,t,a){"function"==typeof this.options.onChunkComplete&&this.options.onChunkComplete(e,t,a)}},{key:"_createUpload",value:function(){var e=this;if(this.options.endpoint){var t=this._openRequest("POST",this.options.endpoint);this.options.uploadLengthDeferred?t.setHeader("Upload-Defer-Length","1"):t.setHeader("Upload-Length","".concat(this._size));var a,n=R(this.options.metadata);""!==n&&t.setHeader("Upload-Metadata",n),this.options.uploadDataDuringCreation&&!this.options.uploadLengthDeferred?(this._offset=0,a=this._addChunkToRequest(t)):(this.options.protocol===E&&t.setHeader("Upload-Complete","?0"),a=this._sendRequest(t,null)),a.then((function(a){if(C(a.getStatus(),200)){var n=a.getHeader("Location");if(null!=n){if(e.url=N(e.options.endpoint,n),(0,s.R)("Created upload at ".concat(e.url)),"function"==typeof e.options.onUploadUrlAvailable&&e.options.onUploadUrlAvailable(),0===e._size)return e._emitSuccess(a),void e._source.close();e._saveUploadInUrlStorage().then((function(){e.options.uploadDataDuringCreation?e._handleUploadResponse(t,a):(e._offset=0,e._performUpload())}))}else e._emitHttpError(t,a,"tus: invalid or missing Location header")}else e._emitHttpError(t,a,"tus: unexpected response while creating upload")})).catch((function(a){e._emitHttpError(t,null,"tus: failed to create upload",a)}))}else this._emitError(new Error("tus: unable to create upload because no endpoint is provided"))}},{key:"_resumeUpload",value:function(){var e=this,t=this._openRequest("HEAD",this.url);this._sendRequest(t,null).then((function(a){var n=a.getStatus();if(!C(n,200))return 423===n?void e._emitHttpError(t,a,"tus: upload is currently locked; retry later"):(C(n,400)&&e._removeFromUrlStorage(),e.options.endpoint?(e.url=null,void e._createUpload()):void e._emitHttpError(t,a,"tus: unable to resume upload (new upload cannot be created without an endpoint)"));var r=Number.parseInt(a.getHeader("Upload-Offset"),10);if(Number.isNaN(r))e._emitHttpError(t,a,"tus: invalid or missing offset value");else{var o=Number.parseInt(a.getHeader("Upload-Length"),10);!Number.isNaN(o)||e.options.uploadLengthDeferred||e.options.protocol!==w?("function"==typeof e.options.onUploadUrlAvailable&&e.options.onUploadUrlAvailable(),e._saveUploadInUrlStorage().then((function(){if(r===o)return e._emitProgress(o,o),void e._emitSuccess(a);e._offset=r,e._performUpload()}))):e._emitHttpError(t,a,"tus: invalid or missing length value")}})).catch((function(a){e._emitHttpError(t,null,"tus: failed to resume upload",a)}))}},{key:"_performUpload",value:function(){var e,t=this;this._aborted||(this.options.overridePatchMethod?(e=this._openRequest("POST",this.url)).setHeader("X-HTTP-Method-Override","PATCH"):e=this._openRequest("PATCH",this.url),e.setHeader("Upload-Offset","".concat(this._offset)),this._addChunkToRequest(e).then((function(a){C(a.getStatus(),200)?t._handleUploadResponse(e,a):t._emitHttpError(e,a,"tus: unexpected response while uploading chunk")})).catch((function(a){t._aborted||t._emitHttpError(e,null,"tus: failed to upload chunk at offset ".concat(t._offset),a)})))}},{key:"_addChunkToRequest",value:function(e){var t=this,a=this._offset,n=this._offset+this.options.chunkSize;return e.setProgressHandler((function(e){t._emitProgress(a+e,t._size)})),e.setHeader("Content-Type","application/offset+octet-stream"),(n===Number.POSITIVE_INFINITY||n>this._size)&&!this.options.uploadLengthDeferred&&(n=this._size),this._source.slice(a,n).then((function(a){var n=a.value,r=a.done,o=null!=n&&n.size?n.size:0;t.options.uploadLengthDeferred&&r&&(t._size=t._offset+o,e.setHeader("Upload-Length","".concat(t._size)));var i=t._offset+o;return!t.options.uploadLengthDeferred&&r&&i!==t._size?Promise.reject(new Error("upload was configured with a size of ".concat(t._size," bytes, but the source is done after ").concat(i," bytes"))):null===n?t._sendRequest(e):(t.options.protocol===E&&e.setHeader("Upload-Complete",r?"?1":"?0"),t._emitProgress(t._offset,t._size),t._sendRequest(e,n))}))}},{key:"_handleUploadResponse",value:function(e,t){var a=Number.parseInt(t.getHeader("Upload-Offset"),10);if(Number.isNaN(a))this._emitHttpError(e,t,"tus: invalid or missing offset value");else{if(this._emitProgress(a,this._size),this._emitChunkComplete(a-this._offset,a,this._size),this._offset=a,a===this._size)return this._emitSuccess(t),void this._source.close();this._performUpload()}}},{key:"_openRequest",value:function(e,t){var a=S(e,t,this.options);return this._req=a,a}},{key:"_removeFromUrlStorage",value:function(){var e=this;this._urlStorageKey&&(this._urlStorage.removeUpload(this._urlStorageKey).catch((function(t){e._emitError(t)})),this._urlStorageKey=null)}},{key:"_saveUploadInUrlStorage",value:function(){var e=this;if(!this.options.storeFingerprintForResuming||!this._fingerprint||null!==this._urlStorageKey)return Promise.resolve();var t={size:this._size,metadata:this.options.metadata,creationTime:(new Date).toString()};return this._parallelUploads?t.parallelUploadUrls=this._parallelUploadUrls:t.uploadUrl=this.url,this._urlStorage.addUpload(this._fingerprint,t).then((function(t){e._urlStorageKey=t}))}},{key:"_sendRequest",value:function(e){return _(e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,this.options)}}],n=[{key:"terminate",value:function(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=S("DELETE",t,a);return _(n,null,a).then((function(e){if(204!==e.getStatus())throw new i.A("tus: unexpected response while terminating upload",null,n,e)})).catch((function(r){if(r instanceof i.A||(r=new i.A("tus: failed to terminate upload",r,n,null)),!P(r,0,a))throw r;var o=a.retryDelays[0],s=a.retryDelays.slice(1),c=v(v({},a),{},{retryDelays:s});return new Promise((function(e){return setTimeout(e,o)})).then((function(){return e.terminate(t,c)}))}))}}],a&&y(t.prototype,a),n&&y(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,a,n}();function R(e){return Object.entries(e).map((function(e){var t=u(e,2),a=t[0],r=t[1];return"".concat(a," ").concat(n.o4.encode(String(r)))})).join(",")}function C(e,t){return e>=t&&e<t+100}function S(e,t,a){var n=a.httpStack.createRequest(e,t);a.protocol===E?n.setHeader("Upload-Draft-Interop-Version","5"):n.setHeader("Tus-Resumable","1.0.0");for(var r=a.headers||{},o=0,i=Object.entries(r);o<i.length;o++){var s=u(i[o],2),l=s[0],d=s[1];n.setHeader(l,d)}if(a.addRequestId){var p=(0,c.A)();n.setHeader("X-Request-ID",p)}return n}function _(_x,e,t){return x.apply(this,arguments)}function x(){var e;return e=l().mark((function e(t,a,n){var r;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("function"!=typeof n.onBeforeRequest){e.next=3;break}return e.next=3,n.onBeforeRequest(t);case 3:return e.next=5,t.send(a);case 5:if(r=e.sent,"function"!=typeof n.onAfterResponse){e.next=9;break}return e.next=9,n.onAfterResponse(t,r);case 9:return e.abrupt("return",r);case 10:case"end":return e.stop()}}),e)})),x=function(){var t=this,a=arguments;return new Promise((function(n,r){var o=e.apply(t,a);function i(e){d(o,n,r,i,s,"next",e)}function s(e){d(o,n,r,i,s,"throw",e)}i(void 0)}))},x.apply(this,arguments)}function P(e,t,a){return!(null==a.retryDelays||t>=a.retryDelays.length||null==e.originalRequest)&&(a&&"function"==typeof a.onShouldRetry?a.onShouldRetry(e,t,a):j(e))}function j(e){var t,a=e.originalResponse?e.originalResponse.getStatus():0;return(!C(a,400)||409===a||423===a)&&(t=!0,"undefined"!=typeof navigator&&!1===navigator.onLine&&(t=!1),t)}function N(e,t){return new(o())(t,e).toString()}k.defaultOptions=A;const V=k},7291:(e,t,a)=>{"use strict";function n(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}a.d(t,{A:()=>n})},6580:(e,t,a)=>{"use strict";var n=a(6811),r=a(4588),o=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,i=/[\n\r\t]/g,s=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,c=/:\d+$/,l=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,d=/^[a-zA-Z]:/;function u(e){return(e||"").toString().replace(o,"")}var p=[["#","hash"],["?","query"],function(e,t){return g(t.protocol)?e.replace(/\\/g,"/"):e},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d*)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],m={hash:1,query:1};function h(e){var t,a=("undefined"!=typeof window||"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).location||{},n={},r=typeof(e=e||a);if("blob:"===e.protocol)n=new f(unescape(e.pathname),{});else if("string"===r)for(t in n=new f(e,{}),m)delete n[t];else if("object"===r){for(t in e)t in m||(n[t]=e[t]);void 0===n.slashes&&(n.slashes=s.test(e.href))}return n}function g(e){return"file:"===e||"ftp:"===e||"http:"===e||"https:"===e||"ws:"===e||"wss:"===e}function v(e,t){e=(e=u(e)).replace(i,""),t=t||{};var a,n=l.exec(e),r=n[1]?n[1].toLowerCase():"",o=!!n[2],s=!!n[3],c=0;return o?s?(a=n[2]+n[3]+n[4],c=n[2].length+n[3].length):(a=n[2]+n[4],c=n[2].length):s?(a=n[3]+n[4],c=n[3].length):a=n[4],"file:"===r?c>=2&&(a=a.slice(2)):g(r)?a=n[4]:r?o&&(a=a.slice(2)):c>=2&&g(t.protocol)&&(a=n[4]),{protocol:r,slashes:o||g(r),slashesCount:c,rest:a}}function f(e,t,a){if(e=(e=u(e)).replace(i,""),!(this instanceof f))return new f(e,t,a);var o,s,c,l,m,y,b=p.slice(),w=typeof t,E=this,A=0;for("object"!==w&&"string"!==w&&(a=t,t=null),a&&"function"!=typeof a&&(a=r.parse),o=!(s=v(e||"",t=h(t))).protocol&&!s.slashes,E.slashes=s.slashes||o&&t.slashes,E.protocol=s.protocol||t.protocol||"",e=s.rest,("file:"===s.protocol&&(2!==s.slashesCount||d.test(e))||!s.slashes&&(s.protocol||s.slashesCount<2||!g(E.protocol)))&&(b[3]=[/(.*)/,"pathname"]);A<b.length;A++)"function"!=typeof(l=b[A])?(c=l[0],y=l[1],c!=c?E[y]=e:"string"==typeof c?~(m="@"===c?e.lastIndexOf(c):e.indexOf(c))&&("number"==typeof l[2]?(E[y]=e.slice(0,m),e=e.slice(m+l[2])):(E[y]=e.slice(m),e=e.slice(0,m))):(m=c.exec(e))&&(E[y]=m[1],e=e.slice(0,m.index)),E[y]=E[y]||o&&l[3]&&t[y]||"",l[4]&&(E[y]=E[y].toLowerCase())):e=l(e,E);a&&(E.query=a(E.query)),o&&t.slashes&&"/"!==E.pathname.charAt(0)&&(""!==E.pathname||""!==t.pathname)&&(E.pathname=function(e,t){if(""===e)return t;for(var a=(t||"/").split("/").slice(0,-1).concat(e.split("/")),n=a.length,r=a[n-1],o=!1,i=0;n--;)"."===a[n]?a.splice(n,1):".."===a[n]?(a.splice(n,1),i++):i&&(0===n&&(o=!0),a.splice(n,1),i--);return o&&a.unshift(""),"."!==r&&".."!==r||a.push(""),a.join("/")}(E.pathname,t.pathname)),"/"!==E.pathname.charAt(0)&&g(E.protocol)&&(E.pathname="/"+E.pathname),n(E.port,E.protocol)||(E.host=E.hostname,E.port=""),E.username=E.password="",E.auth&&(~(m=E.auth.indexOf(":"))?(E.username=E.auth.slice(0,m),E.username=encodeURIComponent(decodeURIComponent(E.username)),E.password=E.auth.slice(m+1),E.password=encodeURIComponent(decodeURIComponent(E.password))):E.username=encodeURIComponent(decodeURIComponent(E.auth)),E.auth=E.password?E.username+":"+E.password:E.username),E.origin="file:"!==E.protocol&&g(E.protocol)&&E.host?E.protocol+"//"+E.host:"null",E.href=E.toString()}f.prototype={set:function(e,t,a){var o=this;switch(e){case"query":"string"==typeof t&&t.length&&(t=(a||r.parse)(t)),o[e]=t;break;case"port":o[e]=t,n(t,o.protocol)?t&&(o.host=o.hostname+":"+t):(o.host=o.hostname,o[e]="");break;case"hostname":o[e]=t,o.port&&(t+=":"+o.port),o.host=t;break;case"host":o[e]=t,c.test(t)?(t=t.split(":"),o.port=t.pop(),o.hostname=t.join(":")):(o.hostname=t,o.port="");break;case"protocol":o.protocol=t.toLowerCase(),o.slashes=!a;break;case"pathname":case"hash":if(t){var i="pathname"===e?"/":"#";o[e]=t.charAt(0)!==i?i+t:t}else o[e]=t;break;case"username":case"password":o[e]=encodeURIComponent(t);break;case"auth":var s=t.indexOf(":");~s?(o.username=t.slice(0,s),o.username=encodeURIComponent(decodeURIComponent(o.username)),o.password=t.slice(s+1),o.password=encodeURIComponent(decodeURIComponent(o.password))):o.username=encodeURIComponent(decodeURIComponent(t))}for(var l=0;l<p.length;l++){var d=p[l];d[4]&&(o[d[1]]=o[d[1]].toLowerCase())}return o.auth=o.password?o.username+":"+o.password:o.username,o.origin="file:"!==o.protocol&&g(o.protocol)&&o.host?o.protocol+"//"+o.host:"null",o.href=o.toString(),o},toString:function(e){e&&"function"==typeof e||(e=r.stringify);var t,a=this,n=a.host,o=a.protocol;o&&":"!==o.charAt(o.length-1)&&(o+=":");var i=o+(a.protocol&&a.slashes||g(a.protocol)?"//":"");return a.username?(i+=a.username,a.password&&(i+=":"+a.password),i+="@"):a.password?(i+=":"+a.password,i+="@"):"file:"!==a.protocol&&g(a.protocol)&&!n&&"/"!==a.pathname&&(i+="@"),(":"===n[n.length-1]||c.test(a.hostname)&&!a.port)&&(n+=":"),i+=n+a.pathname,(t="object"==typeof a.query?e(a.query):a.query)&&(i+="?"!==t.charAt(0)?"?"+t:t),a.hash&&(i+=a.hash),i}},f.extractProtocol=v,f.location=h,f.trimLeft=u,f.qs=r,e.exports=f},3018:e=>{"use strict";e.exports={consumer_slug:"jetpack-videopress"}},9384:e=>{"use strict";e.exports=window.JetpackConnection},7999:e=>{"use strict";e.exports=window.JetpackScriptDataModule},1609:e=>{"use strict";e.exports=window.React},5795:e=>{"use strict";e.exports=window.ReactDOM},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},8468:e=>{"use strict";e.exports=window.lodash},1455:e=>{"use strict";e.exports=window.wp.apiFetch},6427:e=>{"use strict";e.exports=window.wp.components},9491:e=>{"use strict";e.exports=window.wp.compose},7143:e=>{"use strict";e.exports=window.wp.data},8443:e=>{"use strict";e.exports=window.wp.date},8490:e=>{"use strict";e.exports=window.wp.domReady},6087:e=>{"use strict";e.exports=window.wp.element},2619:e=>{"use strict";e.exports=window.wp.hooks},7723:e=>{"use strict";e.exports=window.wp.i18n},2279:e=>{"use strict";e.exports=window.wp.plugins},5573:e=>{"use strict";e.exports=window.wp.primitives},3832:e=>{"use strict";e.exports=window.wp.url},6072:e=>{function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},3022:(e,t,a)=>{"use strict";function n(e){var t,a,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(a=n(e[t]))&&(r&&(r+=" "),r+=a)}else for(a in e)e[a]&&(r&&(r+=" "),r+=a);return r}a.d(t,{A:()=>r});const r=function(){for(var e,t,a=0,r="",o=arguments.length;a<o;a++)(e=arguments[a])&&(t=n(e))&&(r&&(r+=" "),r+=t);return r}},7578:(e,t,a)=>{"use strict";a.d(t,{O:()=>S});const n="array",r="bit",o="bits",i="byte",s="bytes",c="",l="exponent",d="function",u="iec",p="Invalid number",m="Invalid rounding method",h="jedec",g="object",v=".",f="round",y="s",b="si",w="kbit",E="kB",A=" ",k="string",R="0",C={symbol:{iec:{bits:["bit","Kibit","Mibit","Gibit","Tibit","Pibit","Eibit","Zibit","Yibit"],bytes:["B","KiB","MiB","GiB","TiB","PiB","EiB","ZiB","YiB"]},jedec:{bits:["bit","Kbit","Mbit","Gbit","Tbit","Pbit","Ebit","Zbit","Ybit"],bytes:["B","KB","MB","GB","TB","PB","EB","ZB","YB"]}},fullform:{iec:["","kibi","mebi","gibi","tebi","pebi","exbi","zebi","yobi"],jedec:["","kilo","mega","giga","tera","peta","exa","zetta","yotta"]}};function S(e,{bits:t=!1,pad:a=!1,base:S=-1,round:_=2,locale:x=c,localeOptions:P={},separator:j=c,spacer:N=A,symbols:V={},standard:z=c,output:U=k,fullform:O=!1,fullforms:L=[],exponent:M=-1,roundingMethod:F=f,precision:T=0}={}){let D=M,I=Number(e),B=[],H=0,q=c;z===b?(S=10,z=h):z===u||z===h?S=2:2===S?z=u:(S=10,z=h);const G=10===S?1e3:1024,$=!0===O,J=I<0,W=Math[F];if("bigint"!=typeof e&&isNaN(e))throw new TypeError(p);if(typeof W!==d)throw new TypeError(m);if(J&&(I=-I),(-1===D||isNaN(D))&&(D=Math.floor(Math.log(I)/Math.log(G)),D<0&&(D=0)),D>8&&(T>0&&(T+=8-D),D=8),U===l)return D;if(0===I)B[0]=0,q=B[1]=C.symbol[z][t?o:s][D];else{H=I/(2===S?Math.pow(2,10*D):Math.pow(1e3,D)),t&&(H*=8,H>=G&&D<8&&(H/=G,D++));const e=Math.pow(10,D>0?_:0);B[0]=W(H*e)/e,B[0]===G&&D<8&&-1===M&&(B[0]=1,D++),q=B[1]=10===S&&1===D?t?w:E:C.symbol[z][t?o:s][D]}if(J&&(B[0]=-B[0]),T>0&&(B[0]=B[0].toPrecision(T)),B[1]=V[B[1]]||B[1],!0===x?B[0]=B[0].toLocaleString():x.length>0?B[0]=B[0].toLocaleString(x,P):j.length>0&&(B[0]=B[0].toString().replace(v,j)),a&&_>0){const e=B[0].toString(),t=j||(e.match(/(\D)/g)||[]).pop()||v,a=e.toString().split(t),n=a[1]||c,r=n.length,o=_-r;B[0]=`${a[0]}${t}${n.padEnd(r+o,R)}`}return $&&(B[1]=L[D]?L[D]:C.fullform[z][D]+(t?r:i)+(1===B[0]?c:y)),U===n?B:U===g?{value:B[0],symbol:B[1],exponent:D,unit:q}:B.join(N)}},9535:(e,t,a)=>{"use strict";a.d(t,{o4:()=>F});const n="3.7.7",r=n,o="function"==typeof Buffer,i="function"==typeof TextDecoder?new TextDecoder:void 0,s="function"==typeof TextEncoder?new TextEncoder:void 0,c=Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="),l=(e=>{let t={};return e.forEach(((e,a)=>t[e]=a)),t})(c),d=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,u=String.fromCharCode.bind(String),p="function"==typeof Uint8Array.from?Uint8Array.from.bind(Uint8Array):e=>new Uint8Array(Array.prototype.slice.call(e,0)),m=e=>e.replace(/=/g,"").replace(/[+\/]/g,(e=>"+"==e?"-":"_")),h=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),g=e=>{let t,a,n,r,o="";const i=e.length%3;for(let i=0;i<e.length;){if((a=e.charCodeAt(i++))>255||(n=e.charCodeAt(i++))>255||(r=e.charCodeAt(i++))>255)throw new TypeError("invalid character found");t=a<<16|n<<8|r,o+=c[t>>18&63]+c[t>>12&63]+c[t>>6&63]+c[63&t]}return i?o.slice(0,i-3)+"===".substring(i):o},v="function"==typeof btoa?e=>btoa(e):o?e=>Buffer.from(e,"binary").toString("base64"):g,f=o?e=>Buffer.from(e).toString("base64"):e=>{let t=[];for(let a=0,n=e.length;a<n;a+=4096)t.push(u.apply(null,e.subarray(a,a+4096)));return v(t.join(""))},y=(e,t=!1)=>t?m(f(e)):f(e),b=e=>{if(e.length<2)return(t=e.charCodeAt(0))<128?e:t<2048?u(192|t>>>6)+u(128|63&t):u(224|t>>>12&15)+u(128|t>>>6&63)+u(128|63&t);var t=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return u(240|t>>>18&7)+u(128|t>>>12&63)+u(128|t>>>6&63)+u(128|63&t)},w=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,E=e=>e.replace(w,b),A=o?e=>Buffer.from(e,"utf8").toString("base64"):s?e=>f(s.encode(e)):e=>v(E(e)),k=(e,t=!1)=>t?m(A(e)):A(e),R=e=>k(e,!0),C=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,S=e=>{switch(e.length){case 4:var t=((7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3))-65536;return u(55296+(t>>>10))+u(56320+(1023&t));case 3:return u((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return u((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},_=e=>e.replace(C,S),x=e=>{if(e=e.replace(/\s+/g,""),!d.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(3&e.length));let t,a,n,r="";for(let o=0;o<e.length;)t=l[e.charAt(o++)]<<18|l[e.charAt(o++)]<<12|(a=l[e.charAt(o++)])<<6|(n=l[e.charAt(o++)]),r+=64===a?u(t>>16&255):64===n?u(t>>16&255,t>>8&255):u(t>>16&255,t>>8&255,255&t);return r},P="function"==typeof atob?e=>atob(h(e)):o?e=>Buffer.from(e,"base64").toString("binary"):x,j=o?e=>p(Buffer.from(e,"base64")):e=>p(P(e).split("").map((e=>e.charCodeAt(0)))),N=e=>j(z(e)),V=o?e=>Buffer.from(e,"base64").toString("utf8"):i?e=>i.decode(j(e)):e=>_(P(e)),z=e=>h(e.replace(/[-_]/g,(e=>"-"==e?"+":"/"))),U=e=>V(z(e)),O=e=>({value:e,enumerable:!1,writable:!0,configurable:!0}),L=function(){const e=(e,t)=>Object.defineProperty(String.prototype,e,O(t));e("fromBase64",(function(){return U(this)})),e("toBase64",(function(e){return k(this,e)})),e("toBase64URI",(function(){return k(this,!0)})),e("toBase64URL",(function(){return k(this,!0)})),e("toUint8Array",(function(){return N(this)}))},M=function(){const e=(e,t)=>Object.defineProperty(Uint8Array.prototype,e,O(t));e("toBase64",(function(e){return y(this,e)})),e("toBase64URI",(function(){return y(this,!0)})),e("toBase64URL",(function(){return y(this,!0)}))},F={version:n,VERSION:r,atob:P,atobPolyfill:x,btoa:v,btoaPolyfill:g,fromBase64:U,toBase64:k,encode:k,encodeURI:R,encodeURL:R,utob:E,btou:_,decode:U,isValid:e=>{if("string"!=typeof e)return!1;const t=e.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(t)||!/[^\s0-9a-zA-Z\-_]/.test(t)},fromUint8Array:y,toUint8Array:N,extendString:L,extendUint8Array:M,extendBuiltins:()=>{L(),M()}}},8377:e=>{"use strict";e.exports=JSON.parse('{"T":{"White":"#fff","Black":"#000","Gray 0":"#f6f7f7","Gray 5":"#dcdcde","Gray 10":"#c3c4c7","Gray 20":"#a7aaad","Gray 30":"#8c8f94","Gray 40":"#787c82","Gray 50":"#646970","Gray 60":"#50575e","Gray 70":"#3c434a","Gray 80":"#2c3338","Gray 90":"#1d2327","Gray 100":"#101517","Gray":"#646970","Blue 0":"#fbfcfe","Blue 5":"#f7f8fe","Blue 10":"#d6ddf9","Blue 20":"#adbaf3","Blue 30":"#7b90ff","Blue 40":"#546ff3","Blue 50":"#3858e9","Blue 60":"#2a46ce","Blue 70":"#1d35b4","Blue 80":"#1f3286","Blue 90":"#14215a","Blue 100":"#0a112d","Blue":"#3858e9","WordPress Blue 0":"#fbfcfe","WordPress Blue 5":"#f7f8fe","WordPress Blue 10":"#d6ddf9","WordPress Blue 20":"#adbaf3","WordPress Blue 30":"#7b90ff","WordPress Blue 40":"#546ff3","WordPress Blue 50":"#3858e9","WordPress Blue 60":"#2a46ce","WordPress Blue 70":"#1d35b4","WordPress Blue 80":"#1f3286","WordPress Blue 90":"#14215a","WordPress Blue 100":"#0a112d","WordPress Blue":"#3858e9","Purple 0":"#f2e9ed","Purple 5":"#ebcee0","Purple 10":"#e3afd5","Purple 20":"#d48fc8","Purple 30":"#c475bd","Purple 40":"#b35eb1","Purple 50":"#984a9c","Purple 60":"#7c3982","Purple 70":"#662c6e","Purple 80":"#4d2054","Purple 90":"#35163b","Purple 100":"#1e0c21","Purple":"#984a9c","Pink 0":"#f5e9ed","Pink 5":"#f2ceda","Pink 10":"#f7a8c3","Pink 20":"#f283aa","Pink 30":"#eb6594","Pink 40":"#e34c84","Pink 50":"#c9356e","Pink 60":"#ab235a","Pink 70":"#8c1749","Pink 80":"#700f3b","Pink 90":"#4f092a","Pink 100":"#260415","Pink":"#c9356e","Red 0":"#f7ebec","Red 5":"#facfd2","Red 10":"#ffabaf","Red 20":"#ff8085","Red 30":"#f86368","Red 40":"#e65054","Red 50":"#d63638","Red 60":"#b32d2e","Red 70":"#8a2424","Red 80":"#691c1c","Red 90":"#451313","Red 100":"#240a0a","Red":"#d63638","Orange 0":"#f5ece6","Orange 5":"#f7dcc6","Orange 10":"#ffbf86","Orange 20":"#faa754","Orange 30":"#e68b28","Orange 40":"#d67709","Orange 50":"#b26200","Orange 60":"#8a4d00","Orange 70":"#704000","Orange 80":"#543100","Orange 90":"#361f00","Orange 100":"#1f1200","Orange":"#b26200","Yellow 0":"#f5f1e1","Yellow 5":"#f5e6b3","Yellow 10":"#f2d76b","Yellow 20":"#f0c930","Yellow 30":"#deb100","Yellow 40":"#c08c00","Yellow 50":"#9d6e00","Yellow 60":"#7d5600","Yellow 70":"#674600","Yellow 80":"#4f3500","Yellow 90":"#320","Yellow 100":"#1c1300","Yellow":"#9d6e00","Green 0":"#e6f2e8","Green 5":"#b8e6bf","Green 10":"#68de86","Green 20":"#1ed15a","Green 30":"#00ba37","Green 40":"#00a32a","Green 50":"#008a20","Green 60":"#007017","Green 70":"#005c12","Green 80":"#00450c","Green 90":"#003008","Green 100":"#001c05","Green":"#008a20","Celadon 0":"#e4f2ed","Celadon 5":"#a7e8d3","Celadon 10":"#66deb9","Celadon 20":"#31cc9f","Celadon 30":"#09b585","Celadon 40":"#009e73","Celadon 50":"#008763","Celadon 60":"#007053","Celadon 70":"#005c44","Celadon 80":"#004533","Celadon 90":"#003024","Celadon 100":"#001c15","Celadon":"#008763","Automattic Blue 0":"#ebf4fa","Automattic Blue 5":"#c4e2f5","Automattic Blue 10":"#88ccf2","Automattic Blue 20":"#5ab7e8","Automattic Blue 30":"#24a3e0","Automattic Blue 40":"#1490c7","Automattic Blue 50":"#0277a8","Automattic Blue 60":"#036085","Automattic Blue 70":"#02506e","Automattic Blue 80":"#02384d","Automattic Blue 90":"#022836","Automattic Blue 100":"#021b24","Automattic Blue":"#24a3e0","Simplenote Blue 0":"#e9ecf5","Simplenote Blue 5":"#ced9f2","Simplenote Blue 10":"#abc1f5","Simplenote Blue 20":"#84a4f0","Simplenote Blue 30":"#618df2","Simplenote Blue 40":"#4678eb","Simplenote Blue 50":"#3361cc","Simplenote Blue 60":"#1d4fc4","Simplenote Blue 70":"#113ead","Simplenote Blue 80":"#0d2f85","Simplenote Blue 90":"#09205c","Simplenote Blue 100":"#05102e","Simplenote Blue":"#3361cc","WooCommerce Purple 0":"#f2edff","WooCommerce Purple 5":"#e1d7ff","WooCommerce Purple 10":"#d1c1ff","WooCommerce Purple 20":"#b999ff","WooCommerce Purple 30":"#a77eff","WooCommerce Purple 40":"#873eff","WooCommerce Purple 50":"#720eec","WooCommerce Purple 60":"#6108ce","WooCommerce Purple 70":"#5007aa","WooCommerce Purple 80":"#3c087e","WooCommerce Purple 90":"#2c045d","WooCommerce Purple 100":"#1f0342","WooCommerce Purple":"#720eec","Jetpack Green 0":"#f0f2eb","Jetpack Green 5":"#d0e6b8","Jetpack Green 10":"#9dd977","Jetpack Green 20":"#64ca43","Jetpack Green 30":"#2fb41f","Jetpack Green 40":"#069e08","Jetpack Green 50":"#008710","Jetpack Green 60":"#007117","Jetpack Green 70":"#005b18","Jetpack Green 80":"#004515","Jetpack Green 90":"#003010","Jetpack Green 100":"#001c09","Jetpack Green":"#069e08"}}')}},t={};function a(n){var r=t[n];if(void 0!==r)return r.exports;var o=t[n]={exports:{}};return e[n](o,o.exports,a),o.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=a(723),t=a(6087),n=a(7723),r=a(1609),o=a(9539),i=a(28),s=a(6287),c=a(3536),l=a(2784),d=a(4951),u=a(957);a(4237);const __=n.__;function p(){const e=(0,o.zy)();return(0,r.useEffect)((()=>{setTimeout((()=>{window.scrollTo(0,0)}),0)}),[e]),null}(0,s.k$)();const m=()=>{const{isUploading:t}=(0,u.Ay)();return(0,d.A)({shouldPrevent:t,message:__("Leaving will cancel the upload. Are you sure you want to exit?","jetpack-videopress-pkg")}),React.createElement(e.Ay,null,React.createElement(i.I9,null,React.createElement(p,null),React.createElement(o.BV,null,React.createElement(o.qh,{path:"/",element:React.createElement(c.A,null)}),React.createElement(o.qh,{path:"/video/:videoId/edit",element:React.createElement(l.A,null)}))))};!function(){const e=document.getElementById("jetpack-videopress-root");null!==e&&t.createRoot(e).render(React.createElement(m,null))}()})()})();