"use strict";const WPFormsPrintEntryPage=window.WPFormsPrintEntryPage||function(e,t,n){const a={vars:{},cache:{getCookie:function(){return JSON.parse(wpCookies.get("wpforms_entry_print"))||{}},saveCookie:function(e,t){var o=a.cache.getCookie();o[e]=t,wpCookies.set("wpforms_entry_print",JSON.stringify(o))}},init:function(){n(t).on("load",function(){"function"==typeof n.ready.then?n.ready.then(a.load):a.load()})},load:function(){a.vars={$body:n("body"),$page:n(".print-preview"),$modeToggles:n(".toggle-mode"),$richTextFields:n(".wpforms-entry-field-value-richtext"),printButtonSelector:".print",closeButtonSelector:".close-window",settingsButtonSelector:".button-settings",settingsMenuSelector:".actions",toggleModeSelector:".toggle-mode",toggleSelector:".switch",activeClass:"active"},a.vars.$modeToggles.each(a.presetSettingsValues),a.vars.$richTextFields.each(a.loadRichText),a.bindEvents()},bindEvents:function(){n(e).on("click",a.vars.printButtonSelector,a.print).on("click",a.vars.closeButtonSelector,a.close).on("click",a.vars.settingsButtonSelector,a.toggleSettings).on("click",a.vars.toggleModeSelector,a.toggleMode)},toggleMode:function(){var e=n(this),t=e.data("mode"),e=e.find(a.vars.toggleSelector),o=!e.hasClass(a.vars.activeClass);e.toggleClass(a.vars.activeClass),"compact"===t&&a.disableMode("maintain-layout"),"maintain-layout"===t&&a.disableMode("compact"),a.vars.$page.toggleClass("wpforms-preview-mode-"+t),a.cache.saveCookie(t,o)},disableMode:function(e){n(a.vars.toggleModeSelector+'[data-mode="'+e+'"]').find(a.vars.toggleSelector).removeClass(a.vars.activeClass),a.vars.$page.removeClass(a.prepareModeClass(e)),a.cache.saveCookie(e,!1)},presetSettingsValues:function(){var e=n(this),t=e.data("mode"),e=e.find(a.vars.toggleSelector),o=a.cache.getCookie();Object.prototype.hasOwnProperty.call(o,t)&&o[t]&&(e.addClass(a.vars.activeClass),a.vars.$page.addClass(a.prepareModeClass(t)))},prepareModeClass:function(e){return"wpforms-preview-mode-"+e},print:function(e){e.preventDefault(),t.print()},close:function(e){e.preventDefault(),t.close()},loadRichText:function(){const e=this;var t=n(this);t.on("load",function(){a.iframeStyles(e),a.modifyRichTextLinks(e),a.updateRichTextTableClasses(e),a.updateRichTextIframeSize(e)}),t.attr("src",t.data("src"))},updateRichTextIframeSize:function(e){var t;e&&e.contentWindow&&(t=e.contentWindow.document.documentElement||!1)&&(t=t.querySelector(".mce-content-body").scrollHeight,e.style.height=t+"px")},modifyRichTextLinks:function(e){n(e).contents().find("a").attr({target:"_blank",rel:"noopener"})},updateRichTextTableClasses(e){n(e).contents().find("table").addClass("mce-item-table")},iframeStyles:function(e){var e=e.contentWindow.document,t=e.querySelector("head"),e=e.createElement("style"),o=a.vars.$body.css("font-family"),n=a.vars.$body.css("font-size"),s=a.vars.$body.css("line-height");e.setAttribute("type","text/css"),e.innerHTML="body.mce-content-body {\tmargin: 0 !important;\tbackground-color: transparent !important;\tfont-family: "+o+";\tfont-size: "+n+";\tline-height: "+s+";}*:first-child {\tmargin-top: 0}*:last-child {\tmargin-bottom: 0}ul, ol {\tpadding-inline-start: 30px;}li {\tlist-style-position: outside;}pre {\twhite-space: pre !important;\toverflow-x: auto !important;}a,img {\tdisplay: inline-block;}",t.appendChild(e)},toggleSettings:function(e){e.preventDefault(),n(this).toggleClass(a.vars.activeClass),n(a.vars.settingsMenuSelector).toggleClass(a.vars.activeClass)}};return a}(document,window,jQuery);WPFormsPrintEntryPage.init();