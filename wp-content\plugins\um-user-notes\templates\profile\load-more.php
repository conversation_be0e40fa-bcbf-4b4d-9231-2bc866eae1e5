<?php
/**
 * Template for the load more
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-notes/load-more.php
 *
 * @see      https://docs.ultimatemember.com/article/1516-templates-map
 * @package  um_ext\um_user_notes\templates
 * @version  1.1.1
 *
 * @var int $per_page
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

$per_page = isset( $per_page ) ? $per_page : UM()->Notes()->get_per_page( true );
?>

<div class="um-clear"><br /></div>

<p class="um_notes_load_more_holder">
	<a href="#" id="um-notes-load-more-btn" data-per_page="<?php echo esc_attr( $per_page ); ?>" data-page="1"
		data-profile="<?php echo esc_attr( um_profile_id() ); ?>"
		data-nonce="<?php echo esc_attr( wp_create_nonce( 'um_user_notes_load_more' ) ); ?>">
		<?php UM()->Notes()->load_more_text(); ?>
	</a>
</p>
