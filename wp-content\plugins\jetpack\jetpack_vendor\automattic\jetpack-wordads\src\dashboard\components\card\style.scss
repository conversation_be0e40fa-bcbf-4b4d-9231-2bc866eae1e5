@use 'sass:color';
@import 'scss/layout';
@import 'scss/typography';
@import 'scss/calypso-colors';
@import 'scss/functions/rem';

$title: #f6f7f7;
$meta: #a7aaad;
$border: #dcdcde;
$section-border: #dcdcde;

/* Card */

.dops-card {
	display: block;
	position: relative;
	margin: 0 auto 10px auto;
	padding: 16px;
	box-sizing: border-box;
	background: $white;
	box-shadow:
		0 0 0 1px $light-gray-700,
		0 1px 1px 1px  rgba(0,0,0,.04);

	@include clear-fix;

	@include breakpoint( ">480px" ) {
		margin-bottom: 16px;
		padding: 24px;
	}

	// Compact Card
	&.is-compact {
		margin-bottom: 1px;

		@include breakpoint( ">480px" ) {
			margin-bottom: 1px;
			padding: 16px 24px;
		}
	}

	&.is-card-link {
		padding-right: 48px;
	}
}

h2.dops-card-title {
	font-size: $font-title-small;
}

// Clickable Card
.dops-card__link-indicator {
	color: color.adjust( $gray, $lightness: 20% );
	display: block;
	height: 100%;
	position: absolute;
		top: 0;
		right: 16px;
}

a.dops-card:hover {

	.dops-card__link-indicator {
		color: color.adjust( $gray, $lightness: 10% );
	}
}

a.dops-card:focus {
	outline: 0;

	.dops-card__link-indicator {
		color: $link-highlight;
	}
}
