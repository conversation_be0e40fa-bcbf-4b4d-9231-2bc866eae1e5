<?php
/**
 * Initialize shortcodes functionality.
 *
 * @package um_ext\um_user_notes\core
 */

namespace um_ext\um_user_notes\core;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Shortcodes
 *
 * @package um_ext\um_user_notes\core
 */
class Shortcodes {


	/**
	 * Shortcodes constructor.
	 */
	public function __construct() {

		// Display the "Add note" template.
		if ( ! shortcode_exists( 'um_user_notes_add' ) ) {
			add_shortcode( 'um_user_notes_add', array( $this, 'um_user_notes_add' ) );
		}

		// Display the "View notes" template.
		if ( ! shortcode_exists( 'um_user_notes_view' ) ) {
			add_shortcode( 'um_user_notes_view', array( $this, 'um_user_notes_view' ) );
		}

		add_shortcode( 'um_user_notes_single_view', array( $this, 'single_note' ) );
	}

	/**
	 * Display the "Add note" template.
	 *
	 * @return string
	 */
	public function um_user_notes_add() {
		if ( ! is_user_logged_in() ) {
			return '';
		}

		$temp_user = null;
		if ( get_current_user_id() !== um_user( 'ID' ) ) {
			$temp_user = um_user( 'ID' );
			um_fetch_user( get_current_user_id() );
		}

		if ( um_user( 'disable_notes' ) ) {
			if ( $temp_user ) {
				um_fetch_user( $temp_user );
			}
			return '';
		}

		UM()->Notes()->enqueue()->enqueue_scripts();
		$output = UM()->get_template( 'profile/add.php', um_user_notes_plugin );

		if ( $temp_user ) {
			um_fetch_user( $temp_user );
		}
		return '<div class="um">' . $output . '</div>';
	}

	/**
	 * Display the "View notes" template.
	 *
	 * @var array $atts
	 *
	 * @return string
	 */
	public function um_user_notes_view( $atts = array() ) {
		UM()->Notes()->enqueue()->enqueue_scripts();

		$args = shortcode_atts(
			array(
				'user_id'  => get_current_user_id(),
				'per_page' => UM()->Notes()->get_per_page( true ),
				'view'     => 'grid',
			),
			$atts,
			'um_user_notes_view'
		);

		$profile_id = absint( $args['user_id'] );

		$temp_user = null;
		if ( absint( um_user( 'ID' ) ) !== $profile_id ) {
			$temp_user = um_user( 'ID' );
			um_fetch_user( $profile_id );
		}
		if ( um_user( 'disable_notes' ) ) {
			if ( $temp_user ) {
				um_fetch_user( $temp_user );
			}
			return '';
		}

		$per_page     = absint( $args['per_page'] );
		$total        = UM()->Notes()->um_notes_get_total( $profile_id, $per_page );
		$latest_notes = UM()->Notes()->um_notes_get_latest( $profile_id, $per_page );

		$t_args = compact( 'latest_notes', 'total', 'per_page' );

		if ( 'grid' === $args['view'] ) {
			$output = UM()->get_template( 'grid.php', um_user_notes_plugin, $t_args );
		} else {
			$output = UM()->get_template( 'list.php', um_user_notes_plugin, $t_args );
		}

		return $output;
	}

	/**
	 * @param array $atts
	 *
	 * @return string
	 */
	public function single_note( $atts = array() ) {
		$args = shortcode_atts(
			array(
				'note_id' => null,
			),
			$atts,
			'um_user_notes_single_view'
		);

		if ( empty( $args['note_id'] ) ) {
			return '';
		}

		$post_id = absint( $args['note_id'] );
		$note    = get_post( $post_id );
		if ( empty( $note ) ) {
			return esc_html__( 'Invalid Note ID.', 'um-user-notes' );
		}

		$temp_user = null;
		if ( get_current_user_id() !== $note->post_author ) {
			$temp_user = um_user( 'ID' );
			um_fetch_user( $note->post_author );
		}
		if ( um_user( 'disable_notes' ) ) {
			if ( $temp_user ) {
				um_fetch_user( $temp_user );
			}
			return esc_html__( 'Invalid Note ID.', 'um-user-notes' );
		}

		UM()->Notes()->enqueue()->enqueue_scripts();

		$author_profile = um_user_profile_url( $note->post_author );
		$album_link     = add_query_arg( array( 'profiletab' => 'notes' ), $author_profile );

		$img = false;
		if ( has_post_thumbnail( $post_id ) ) {
			$image = wp_get_attachment_image_src( get_post_thumbnail_id( $post_id ), 'full' );
			$img   = $image[0];
		}

		$hide = ! UM()->Notes()->can_view( $post_id );

		$post_content = apply_filters( 'um_notes_oembed', $note->post_content );
		/** This filter is already documented in core. wp-includes/post-template.php */
		$post_content = apply_filters( 'the_content', $post_content );
		$post_content = str_replace( ']]>', ']]&gt;', $post_content );

		return UM()->get_template(
			'profile/single-note.php',
			um_user_notes_plugin,
			array(
				'note_id'      => $post_id,
				'title'        => esc_attr( $note->post_title ),
				'content'      => $post_content,
				'image'        => $img,
				'avatar'       => get_avatar( um_user( 'ID' ), 15 ),
				'profile_link' => um_user_profile_url(),
				'author_name'  => um_user( 'display_name' ),
				'post_date'    => get_the_date( '', $post_id ),
				'back_link'    => $album_link,
				'hide'         => $hide,
			)
		);
	}
}
