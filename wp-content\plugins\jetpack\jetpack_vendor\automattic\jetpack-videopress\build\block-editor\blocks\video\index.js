/*! For license information please see index.js.LICENSE.txt */
(()=>{var e={1113:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var o=r(6087);const n=(0,o.forwardRef)((function({icon:e,size:t=24,...r},n){return(0,o.cloneElement)(e,{width:t,height:t,...r,ref:n})}))},7913:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(5573),n=r(790);const a=(0,n.jsx)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)(o.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M6 5.5h12a.5.5 0 0 1 .5.5v12a.5.5 0 0 1-.5.5H6a.5.5 0 0 1-.5-.5V6a.5.5 0 0 1 .5-.5ZM4 6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6Zm4 10h2v-1.5H8V16Zm5 0h-2v-1.5h2V16Zm1 0h2v-1.5h-2V16Z"})})},3751:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(5573),n=r(790);const a=(0,n.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(o.Path,{d:"M12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4ZM12.75 8V13H11.25V8H12.75ZM12.75 14.5V16H11.25V14.5H12.75Z"})})},3512:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(5573),n=r(790);const a=(0,n.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(o.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})})},5938:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(5573),n=r(790);const a=(0,n.jsx)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)(o.Path,{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM5 4.5h14c.3 0 .5.2.5.5v8.4l-3-2.9c-.3-.3-.8-.3-1 0L11.9 14 9 12c-.3-.2-.6-.2-.8 0l-3.6 2.6V5c-.1-.3.1-.5.4-.5zm14 15H5c-.3 0-.5-.2-.5-.5v-2.4l4.1-3 3 1.9c.3.2.7.2.9-.1L16 12l3.5 3.4V19c0 .3-.2.5-.5.5z"})})},5855:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(5573),n=r(790);const a=(0,n.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(o.Path,{d:"M17.031 4.703 15.576 4l-1.56 3H14v.03l-2.324 4.47H9.5V13h1.396l-1.502 2.889h-.95a3.694 3.694 0 0 1 0-7.389H10V7H8.444a5.194 5.194 0 1 0 0 10.389h.17L7.5 19.53l1.416.719L15.049 8.5h.507a3.694 3.694 0 0 1 0 7.39H14v1.5h1.556a5.194 5.194 0 0 0 .273-10.383l1.202-2.304Z"})})},7326:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(5573),n=r(790);const a=(0,n.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(o.Path,{d:"M18.5 15v3.5H13V6.7l4.5 4.1 1-1.1-6.2-5.8-5.8 5.8 1 1.1 4-4v11.7h-6V15H4v5h16v-5z"})})},6941:(e,t,r)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const r="color: "+this.color;t.splice(1,0,r,"color: inherit");let o=0,n=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(o++,"%c"===e&&(n=o))})),t.splice(n,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=r(3212)(t);const{formatters:o}=e.exports;o.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},3212:(e,t,r)=>{e.exports=function(e){function t(e){let r,n,a,i=null;function s(...e){if(!s.enabled)return;const o=s,n=Number(new Date),a=n-(r||n);o.diff=a,o.prev=r,o.curr=n,r=n,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((r,n)=>{if("%%"===r)return"%";i++;const a=t.formatters[n];if("function"==typeof a){const t=e[i];r=a.call(o,t),e.splice(i,1),i--}return r})),t.formatArgs.call(o,e);(o.log||t.log).apply(o,e)}return s.namespace=e,s.useColors=t.useColors(),s.color=t.selectColor(e),s.extend=o,s.destroy=t.destroy,Object.defineProperty(s,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==i?i:(n!==t.namespaces&&(n=t.namespaces,a=t.enabled(e)),a),set:e=>{i=e}}),"function"==typeof t.init&&t.init(s),s}function o(e,r){const o=t(this.namespace+(void 0===r?":":r)+e);return o.log=this.log,o}function n(e,t){let r=0,o=0,n=-1,a=0;for(;r<e.length;)if(o<t.length&&(t[o]===e[r]||"*"===t[o]))"*"===t[o]?(n=o,a=r,o++):(r++,o++);else{if(-1===n)return!1;o=n+1,a++,r=a}for(;o<t.length&&"*"===t[o];)o++;return o===t.length}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names,...t.skips.map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const r=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of r)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const r of t.skips)if(n(e,r))return!1;for(const r of t.names)if(n(e,r))return!0;return!1},t.humanize=r(7378),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((r=>{t[r]=e[r]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t),r|=0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}},7560:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});const o={button:"zI5tJ_qhWE6Oe6Lk75GY","is-icon-button":"tuBt2DLqimiImoqVzPqo",small:"Na39I683LAaSA99REg14",normal:"ipS7tKy9GntCS4R3vekF",icon:"paGLQwtPEaJmtArCcmyK",regular:"lZAo6_oGfclXOO9CC6Rd","full-width":"xJDOiJxTt0R_wSl8Ipz_",loading:"q_tVWqMjl39RcY6WtQA6","external-icon":"CDuBjJp_8jxzx5j6Nept"}},2604:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});const o={"help-message":"MVuHtQ6MxijxzLoKaiAD"}},941:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});const o={"timestamp-control__controls-wrapper":"rcGUZgpe4CGD8uYmerUD","timestamp-range-control":"iZjYt7tDXuSRYGSpgL1y","timestamp-input-wrapper":"dx9xZVwTj45XWtgjLwf2","timestamp-control-input":"Uge3H06ePl7rnHIbnUgo","is-disabled":"AmMha5bHq6c3gHoP7k28","timestamp-control-divider":"aN3nKQFl5a3qMbO8CaVg"}},8766:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});const o={container:"ianknO6MB_ng2EieMQdE","play-icon":"no33D8j7N4nzdafpETZy","video-player-wrapper":"VFzg5w6i04b05mkee2SA","video-player-spinner-wrapper":"PHuqvGT1Z0IVZgjAtUZw",spinner:"xwBn5mUH0mhSBqas1dog",video:"k5rpcKdHL3HKU39CrUhe",range:"sgykjydBL76DT5yDw_ka"}},7378:e=>{var t=1e3,r=60*t,o=60*r,n=24*o,a=7*n,i=365.25*n;function s(e,t,r,o){var n=t>=1.5*r;return Math.round(e/r)+" "+o+(n?"s":"")}e.exports=function(e,c){c=c||{};var l=typeof e;if("string"===l&&e.length>0)return function(e){if((e=String(e)).length>100)return;var s=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!s)return;var c=parseFloat(s[1]);switch((s[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return c*i;case"weeks":case"week":case"w":return c*a;case"days":case"day":case"d":return c*n;case"hours":case"hour":case"hrs":case"hr":case"h":return c*o;case"minutes":case"minute":case"mins":case"min":case"m":return c*r;case"seconds":case"second":case"secs":case"sec":case"s":return c*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}(e);if("number"===l&&isFinite(e))return c.long?function(e){var a=Math.abs(e);if(a>=n)return s(e,a,n,"day");if(a>=o)return s(e,a,o,"hour");if(a>=r)return s(e,a,r,"minute");if(a>=t)return s(e,a,t,"second");return e+" ms"}(e):function(e){var a=Math.abs(e);if(a>=n)return Math.round(e/n)+"d";if(a>=o)return Math.round(e/o)+"h";if(a>=r)return Math.round(e/r)+"m";if(a>=t)return Math.round(e/t)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},4588:(e,t)=>{"use strict";var r=Object.prototype.hasOwnProperty;function o(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch(e){return null}}function n(e){try{return encodeURIComponent(e)}catch(e){return null}}t.stringify=function(e,t){t=t||"";var o,a,i=[];for(a in"string"!=typeof t&&(t="?"),e)if(r.call(e,a)){if((o=e[a])||null!=o&&!isNaN(o)||(o=""),a=n(a),o=n(o),null===a||null===o)continue;i.push(a+"="+o)}return i.length?t+i.join("&"):""},t.parse=function(e){for(var t,r=/([^=?#&]+)=?([^&]*)/g,n={};t=r.exec(e);){var a=o(t[1]),i=o(t[2]);null===a||null===i||a in n||(n[a]=i)}return n}},6811:e=>{"use strict";e.exports=function(e,t){if(t=t.split(":")[0],!(e=+e))return!1;switch(t){case"http":case"ws":return 80!==e;case"https":case"wss":return 443!==e;case"ftp":return 21!==e;case"gopher":return 70!==e;case"file":return!1}return 0!==e}},372:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var o=r(6941);const n=r.n(o)()("dops:analytics");let a,i;window._tkq=window._tkq||[],window.ga=window.ga||function(){(window.ga.q=window.ga.q||[]).push(arguments)},window.ga.l=+new Date;const s={initialize:function(e,t,r){s.setUser(e,t),s.setSuperProps(r),s.identifyUser()},setGoogleAnalyticsEnabled:function(e,t=null){this.googleAnalyticsEnabled=e,this.googleAnalyticsKey=t},setMcAnalyticsEnabled:function(e){this.mcAnalyticsEnabled=e},setUser:function(e,t){i={ID:e,username:t}},setSuperProps:function(e){a=e},assignSuperProps:function(e){a=Object.assign(a||{},e)},mc:{bumpStat:function(e,t){const r=function(e,t){let r="";if("object"==typeof e){for(const t in e)r+="&x_"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);n("Bumping stats %o",e)}else r="&x_"+encodeURIComponent(e)+"="+encodeURIComponent(t),n('Bumping stat "%s" in group "%s"',t,e);return r}(e,t);s.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom-no-pv"+r+"&t="+Math.random())},bumpStatWithPageView:function(e,t){const r=function(e,t){let r="";if("object"==typeof e){for(const t in e)r+="&"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);n("Built stats %o",e)}else r="&"+encodeURIComponent(e)+"="+encodeURIComponent(t),n('Built stat "%s" in group "%s"',t,e);return r}(e,t);s.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom"+r+"&t="+Math.random())}},pageView:{record:function(e,t){s.tracks.recordPageView(e),s.ga.recordPageView(e,t)}},purchase:{record:function(e,t,r,o,n,a,i){s.ga.recordPurchase(e,t,r,o,n,a,i)}},tracks:{recordEvent:function(e,t){t=t||{},0===e.indexOf("akismet_")||0===e.indexOf("jetpack_")?(a&&(n("- Super Props: %o",a),t=Object.assign(t,a)),n('Record event "%s" called with props %s',e,JSON.stringify(t)),window._tkq.push(["recordEvent",e,t])):n('- Event name must be prefixed by "akismet_" or "jetpack_"')},recordJetpackClick:function(e){const t="object"==typeof e?e:{target:e};s.tracks.recordEvent("jetpack_wpa_click",t)},recordPageView:function(e){s.tracks.recordEvent("akismet_page_view",{path:e})},setOptOut:function(e){n("Pushing setOptOut: %o",e),window._tkq.push(["setOptOut",e])}},ga:{initialized:!1,initialize:function(){let e={};s.ga.initialized||(i&&(e={userId:"u-"+i.ID}),window.ga("create",this.googleAnalyticsKey,"auto",e),s.ga.initialized=!0)},recordPageView:function(e,t){s.ga.initialize(),n("Recording Page View ~ [URL: "+e+"] [Title: "+t+"]"),this.googleAnalyticsEnabled&&(window.ga("set","page",e),window.ga("send",{hitType:"pageview",page:e,title:t}))},recordEvent:function(e,t,r,o){s.ga.initialize();let a="Recording Event ~ [Category: "+e+"] [Action: "+t+"]";void 0!==r&&(a+=" [Option Label: "+r+"]"),void 0!==o&&(a+=" [Option Value: "+o+"]"),n(a),this.googleAnalyticsEnabled&&window.ga("send","event",e,t,r,o)},recordPurchase:function(e,t,r,o,n,a,i){window.ga("require","ecommerce"),window.ga("ecommerce:addTransaction",{id:e,revenue:o,currency:i}),window.ga("ecommerce:addItem",{id:e,name:t,sku:r,price:n,quantity:a}),window.ga("ecommerce:send")}},identifyUser:function(){i&&window._tkq.push(["identifyUser",i.ID,i.username])},setProperties:function(e){window._tkq.push(["setProperties",e])},clearedIdentity:function(){window._tkq.push(["clearIdentity"])}},c=s},1112:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var o=r(6072),n=r.n(o),a=r(6427),i=r(7723),s=r(1113),c=r(3512),l=r(3022),u=r(1609),p=r.n(u),d=r(7560);const __=i.__,f=(0,u.forwardRef)(((e,t)=>{const{children:r,variant:o="primary",size:i="normal",weight:u="bold",icon:f,iconSize:m,disabled:h,isDestructive:v,isLoading:g,isExternalLink:y,className:b,text:k,fullWidth:w,..._}=e,E=(0,l.A)(d.A.button,b,{[d.A.normal]:"normal"===i,[d.A.small]:"small"===i,[d.A.icon]:Boolean(f),[d.A.loading]:g,[d.A.regular]:"regular"===u,[d.A["full-width"]]:w,[d.A["is-icon-button"]]:Boolean(f)&&!r});_.ref=t;const R="normal"===i?20:16,C=y&&p().createElement(p().Fragment,null,p().createElement(s.A,{size:R,icon:c.A,className:d.A["external-icon"]}),p().createElement(a.VisuallyHidden,{as:"span"},/* translators: accessibility text */
__("(opens in a new tab)","jetpack-videopress-pkg"))),S=y?"_blank":void 0,P=r?.[0]&&null!==r[0]&&"components-tooltip"!==r?.[0]?.props?.className;return p().createElement(a.Button,n()({target:S,variant:o,className:(0,l.A)(E,{"has-text":!!f&&P}),icon:y?void 0:f,iconSize:m,disabled:h,"aria-disabled":h,isDestructive:v,text:k},_),g&&p().createElement(a.Spinner,null),p().createElement("span",null,r),C)}));f.displayName="Button";const m=f},3924:(e,t,r)=>{"use strict";function o(e,t={}){const r={};let o;if("undefined"!=typeof window&&(o=window?.JP_CONNECTION_INITIAL_STATE?.calypsoEnv),0===e.search("https://")){const t=new URL(e);e=`https://${t.host}${t.pathname}`,r.url=encodeURIComponent(e)}else r.source=encodeURIComponent(e);for(const e in t)r[e]=encodeURIComponent(t[e]);!Object.keys(r).includes("site")&&"undefined"!=typeof jetpack_redirects&&Object.hasOwn(jetpack_redirects,"currentSiteRawUrl")&&(r.site=jetpack_redirects.currentBlogID??jetpack_redirects.currentSiteRawUrl),o&&(r.calypso_env=o);return"https://jetpack.com/redirect/?"+Object.keys(r).map((e=>e+"="+r[e])).join("&")}r.d(t,{A:()=>o})},5985:(e,t,r)=>{"use strict";r.d(t,{Sy:()=>o.Sy,d9:()=>o.d9,st:()=>n.A});r(2810),r(4815),r(1409);var o=r(2634),n=(r(2034),r(5595),r(3265),r(3489));r(7119),r(8406),r(6923),r(335),r(8290),r(9061),r(5929),r(5765)},5765:(e,t,r)=>{"use strict";r(8490)},2810:(e,t,r)=>{"use strict";r(8377).T["Jetpack Green 40"]},335:(e,t,r)=>{"use strict";r(6087)},4815:(e,t,r)=>{"use strict";r(7999)},3489:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var o=r(372),n=r(9384),a=r(6087);const{tracks:i}=o.A,{recordEvent:s}=i,c=({pageViewEventName:e=null,pageViewNamespace:t="jetpack",pageViewSuffix:r="page_view",pageViewEventProperties:c={}}={})=>{const[l,u]=(0,a.useState)(!1),{isUserConnected:p,isRegistered:d,userConnectionData:f={}}=(0,n.useConnection)(),{wpcomUser:{login:m,ID:h}={},blogId:v}=f.currentUser||{},g=(0,a.useCallback)((async(e,t={})=>{p&&h&&m&&s(e,t)}),[p,h,m]);return(0,a.useEffect)((()=>{p&&h&&m&&v&&o.A.initialize(h,m,{blog_id:v})}),[v,h,m,p]),(0,a.useEffect)((()=>{const o=e?`${t}_${e}_${r}`:null;d&&o&&(l||(g(o,c),u(!0)))}),[l,t,e,r,d,c,g]),{recordEvent:g,tracks:i}}},7119:(e,t,r)=>{"use strict";r(7143),r(6087),r(8468)},6923:(e,t,r)=>{"use strict";r(7143),r(6087),r(8290)},8406:(e,t,r)=>{"use strict";r(6087)},5929:(e,t,r)=>{"use strict";r(7143),r(2619),r(3265),r(7119)},9520:(e,t,r)=>{"use strict";var o=r(6941),n=r.n(o);window,n()("shared-extension-utils:connection")},9061:(e,t,r)=>{"use strict";r(9520)},7105:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>p,E9:()=>u});var o=r(7143),n=r(2634),a=r(4478),i=r(8290);const s="SET_JETPACK_MODULES";function c(e){return u({isLoading:e})}function l(e,t){return{type:"SET_MODULE_UPDATING",name:e,isUpdating:t}}function u(e){return{type:s,options:e}}const p={updateJetpackModuleStatus:function*(e){try{yield l(e.name,!0),yield(0,a.sB)(e);const t=yield(0,a.wz)();return yield u({data:t}),!0}catch{const e=(0,o.select)(i.F).getJetpackModules();return yield u(e),!1}finally{yield l(e.name,!1)}},setJetpackModules:u,fetchModules:function*(){if((0,n.Sy)())return!0;try{yield c(!0);const e=yield(0,a.wz)();return yield u({data:e}),!0}catch{const e=(0,o.select)(i.F).getJetpackModules();return yield u(e),!1}finally{yield c(!1)}}}},4478:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>l,sB:()=>c,wz:()=>s});var o=r(1455),n=r.n(o);const a="FETCH_JETPACK_MODULES",i="UPDATE_JETPACK_MODULE_STATUS",s=()=>({type:a}),c=e=>({type:i,settings:e}),l={[a]:function(){return n()({path:"/jetpack/v4/module/all",method:"GET"})},[i]:function({settings:e}){return n()({path:`/jetpack/v4/module/${e.name}/active`,method:"POST",data:{active:e.active}})}}},8290:(e,t,r)=>{"use strict";r.d(t,{F:()=>l});var o=r(7143),n=r(7105),a=r(4478),i=r(8862),s=r(2701),c=r(1640);const l="jetpack-modules",u=(0,o.createReduxStore)(l,{reducer:i.A,actions:n.Ay,controls:a.Ay,resolvers:s.A,selectors:c.A});(0,o.register)(u);const p=window?.Initial_State?.getModules||window?.Jetpack_Editor_Initial_State?.modules||null;null!==p&&(0,o.dispatch)(l).setJetpackModules({data:{...p}})},8862:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const o={isLoading:!1,isUpdating:{},data:{}},n=(e=o,t)=>{switch(t.type){case"SET_JETPACK_MODULES":return{...e,...t.options};case"SET_MODULE_UPDATING":return{...e,isUpdating:{...e.isUpdating,[t.name]:t.isUpdating}}}return e}},2701:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(7105),n=r(4478);const a={getJetpackModules:function*(){try{const e=yield(0,n.wz)();if(e)return(0,o.E9)({data:e})}catch(e){console.error(e)}}}},1640:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var o=r(2634);const n={getJetpackModules:e=>e.data,isModuleActive:(e,t)=>(0,o.Sy)()||(e?.data?.[t]?.activated??!1),areModulesLoading:e=>e.isLoading??!1,isModuleUpdating:(e,t)=>e?.isUpdating?.[t]??!1}},3265:(e,t,r)=>{"use strict";var o=r(7723);r(3832),r(8468),r(4815);const __=o.__;__("Upgrade your plan to use video covers","jetpack-videopress-pkg"),__("Upgrade your plan to upload audio","jetpack-videopress-pkg")},2034:(e,t,r)=>{"use strict";r(2279)},1409:(e,t,r)=>{"use strict";r(7999)},2634:(e,t,r)=>{"use strict";function o(){return"object"==typeof window&&"string"==typeof window._currentSiteType?window._currentSiteType:null}function n(){return"simple"===o()}function a(){return"atomic"===o()}r.d(t,{Sy:()=>n,d9:()=>a})},5595:(e,t,r)=>{"use strict";r(6072),r(9491)},9079:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var o=r(6427),n=r(7723),a=r(9152);const __=n.__;function i({onConnect:e,isModuleActive:t,isConnected:r,isConnecting:n}){if(r&&t)return null;const i=r&&!t;let s=__("Connect Jetpack","jetpack-videopress-pkg");n&&(s=__("Redirecting…","jetpack-videopress-pkg"));let c=__("Activate VideoPress","jetpack-videopress-pkg");n&&(c=__("Activating…","jetpack-videopress-pkg"));const l=__("Connect your account to continue using VideoPress","jetpack-videopress-pkg"),u=__("Enable Jetpack module to continue using VideoPress","jetpack-videopress-pkg");return React.createElement(a.A,{action:React.createElement(o.Button,{variant:"primary",onClick:e,disabled:n,isBusy:n},i?c:s),icon:""},i?u:l)}},9152:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var o=r(6427),n=r(3751),a=r(1113);function i({icon:e=n.A,action:t,children:r,isLoading:i}){return React.createElement("div",{className:"block-banner"},e&&React.createElement(a.A,{icon:e}),React.createElement("div",{className:"block-banner__content"},r),i&&React.createElement(o.Spinner,null),t&&React.createElement("div",{className:"block-banner__action"},t))}},8226:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var o=r(4715),n=r(6427),a=r(9491),i=r(6087),s=r(7723),c=r(8770),l=r(8894);const __=s.__;function u({clientId:e,attributes:t,setAttributes:r}){const{useAverageColor:s,seekbarColor:u,seekbarLoadingColor:p,seekbarPlayedColor:d}=t,f={seekbarPlayedColor:d,seekbarLoadingColor:p,seekbarColor:u},[m,h]=(0,i.useState)(f),v=(0,a.useDebounce)((e=>{r(e)}),2e3),g=(0,i.useCallback)((e=>{h((t=>({...t,...e}))),v(e)}),[]),y=(0,i.useCallback)((()=>{h({}),r({useAverageColor:!0,seekbarColor:"",seekbarLoadingColor:"",seekbarPlayedColor:""})}),[]);return React.createElement(n.__experimentalToolsPanelItem,{className:"videopress-playback-bar-colors-panel-item",hasValue:()=>!s,label:__("Dynamic color","jetpack-videopress-pkg"),resetAllFilter:y,isShownByDefault:!0,panelId:e,onDeselect:y},React.createElement(n.PanelRow,{className:"videopress-color-panel__title"},__("Playback bar colors","jetpack-videopress-pkg")),React.createElement(n.ToggleControl,{label:__("Dynamic color","jetpack-videopress-pkg"),help:React.createElement(React.Fragment,null,__("Playback bar colors adapt to the video as it plays.","jetpack-videopress-pkg"),React.createElement("img",{className:"videopress-dynamic-color-example",src:(0,c.A)(l),alt:__("Dynamic colors example","jetpack-videopress-pkg")})),onChange:e=>r({useAverageColor:e}),checked:s,__nextHasNoMarginBottom:!0}),!s&&React.createElement(o.PanelColorSettings,{className:"videopress-color-panel",opened:!s,showTitle:!1,colorSettings:[{label:__("Main","jetpack-videopress-pkg"),showTitle:!0,value:m.seekbarColor,onChange:e=>g({seekbarColor:e})},{label:__("Loaded","jetpack-videopress-pkg"),showTitle:!0,value:m.seekbarLoadingColor,onChange:e=>g({seekbarLoadingColor:e})},{label:__("Progress","jetpack-videopress-pkg"),showTitle:!0,value:m.seekbarPlayedColor,onChange:e=>g({seekbarPlayedColor:e})}]}))}},917:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var o=r(6427),n=r(6087),a=r(7723),i=r(6151),s=r(2843),c=r(2439);const __=a.__,l=31;function u({filename:e,chapter:t,isAutoGeneratedChapter:r,attributes:a,setAttributes:u,isRequestingVideoData:p,updateError:d,videoBelongToSite:f}){const{title:m,description:h}=a,{hasIncompleteChapters:v}=(0,c.A)(h),g=h?.length?h.split("\n").map((e=>Math.ceil(e.length/l)||1)).reduce(((e,t)=>e+t),0):4,y=Math.min(12,Math.max(g,4)),b=v?null:React.createElement(i.A,null),k=!!t&&!r;return React.createElement(o.PanelBody,{title:__("Details","jetpack-videopress-pkg")},!f&&React.createElement(o.Notice,{status:"warning",isDismissible:!1,className:"not-belong-to-site-notice"},__("This video is not owned by this site. You can still embed it and customize the player, but you won’t be able to edit the video.","jetpack-videopress-pkg")),React.createElement(o.TextControl,{label:__("Title","jetpack-videopress-pkg"),value:m,placeholder:e?.length?`${e} video`:__("Video title","jetpack-videopress-pkg"),onChange:e=>u({title:e}),disabled:p||!!d||!f,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),React.createElement(o.TextareaControl,{label:__("Description","jetpack-videopress-pkg"),value:h,placeholder:__("Video description","jetpack-videopress-pkg"),onChange:e=>u({description:e}),rows:y,disabled:p||!!d||!f,help:b,__nextHasNoMarginBottom:!0}),!k&&v&&React.createElement(s.A,{className:"incomplete-chapters-notice"}),k&&React.createElement(o.Notice,{status:"success",className:"learn-how-notice",isDismissible:!1},React.createElement("p",{className:"learn-how-notice__message"},(0,n.createInterpolateElement)(__("You already have chapter information on an attached VTT file, so adding chapters to the description will not change the original ones. <link>Learn more</link>","jetpack-videopress-pkg"),{link:React.createElement(o.ExternalLink,{href:"https://jetpack.com/support/jetpack-videopress/jetpack-videopress-customizing-your-videos/#adding-subtitles-captions-or-chapters-within-a-video"})}))),!!d&&React.createElement(o.Notice,{status:"error",className:"details-panel__error",isDismissible:!1},__("Error updating the video details.","jetpack-videopress-pkg")))}},6592:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(6427),n=r(7723);const __=n.__;function a({isOpen:e,onClose:t}){return e?React.createElement(o.Modal,{title:__("Chapters in VideoPress","jetpack-videopress-pkg"),isDismissible:!1,className:"learn-how-modal",onRequestClose:t},React.createElement("p",null,__("Chapters are a great way to split up longer videos and organize them into different sections.","jetpack-videopress-pkg")),React.createElement("p",null,__("They allow your visitors to see what each section is about and skip to their favorite parts.","jetpack-videopress-pkg")),React.createElement("p",{className:"learn-how-modal__heading"},__("How to add Chapters to your VideoPress videos","jetpack-videopress-pkg")),React.createElement("ol",null,React.createElement("li",null,__("In the Description, add a list of timestamps and titles.","jetpack-videopress-pkg")),React.createElement("li",null,__("Make sure that the first timestamp starts with 00:00.","jetpack-videopress-pkg")),React.createElement("li",null,__("Add at least three chapters entries and as many as you need.","jetpack-videopress-pkg")),React.createElement("li",null,__("Add your chapters entries in consecutive order, with at least 10-second intervals between each.","jetpack-videopress-pkg"))),React.createElement("p",{className:"learn-how-modal__heading"},__("Example","jetpack-videopress-pkg")),React.createElement("p",null,__("00:00 Intro","jetpack-videopress-pkg")),React.createElement("p",null,__("00:24 Mountains arise","jetpack-videopress-pkg")),React.createElement("p",null,__("02:38 Coming back home","jetpack-videopress-pkg")),React.createElement("p",null,__("03:04 Credits","jetpack-videopress-pkg")),React.createElement("div",{className:"learn-how-modal__buttons"},React.createElement(o.Button,{className:"learn-how-modal__button",onClick:t,variant:"primary"},__("Got it, thanks","jetpack-videopress-pkg")))):null}},3534:(e,t,r)=>{"use strict";r.d(t,{K:()=>n,a:()=>a});var o=r(6427);const n=React.createElement(o.SVG,{width:"29",height:"21",viewBox:"0 0 29 21",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},React.createElement(o.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M2.79037 0.59375C4.0363 0.59375 5.13102 1.41658 5.47215 2.60947L8.8452 14.4044C8.8486 14.4164 8.85411 14.4273 8.86124 14.4368L12.8572 0.59375H15.0927H21.2721C25.6033 0.59375 28.5066 3.39892 28.5066 7.64565C28.5066 11.9411 25.5272 14.6196 21.0818 14.6196H18.1499H14.3719L13.6379 16.8813C12.9796 18.9095 11.0827 20.2839 8.94152 20.2839C6.80035 20.2839 4.90341 18.9095 4.24517 16.8813L0.137069 4.22276C-0.444671 2.43022 0.898038 0.59375 2.79037 0.59375ZM15.7374 10.4119H20.0156C21.8718 10.4119 22.9856 9.35018 22.9856 7.64565C22.9856 5.93137 21.8718 4.91839 20.0156 4.91839H17.5202L15.7374 10.4119Z"})),a=React.createElement(o.SVG,{width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},React.createElement(o.Path,{d:"M7 15.5H17V17H7V15.5Z"}),React.createElement(o.Path,{d:"M17 12.5H7V14H17V12.5Z"}),React.createElement(o.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M18.7 3H5.3C4 3 3 4 3 5.3V18.7C3 20 4 21 5.3 21H18.7C20 21 21 20 21 18.7V5.3C21 4 20 3 18.7 3ZM19.5 18.7C19.5 19.1 19.1 19.5 18.7 19.5H5.3C4.9 19.5 4.5 19.1 4.5 18.7V5.3C4.5 4.9 4.9 4.5 5.3 4.5H18.7C19.1 4.5 19.5 4.9 19.5 5.3V18.7Z"}))},396:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var o=r(6427),n=r(6087),a=r(7723),i=r(2604);const __=a.__;function s({attributes:e,setAttributes:t}){const{autoplay:r,loop:a,muted:s,controls:c,playsinline:l,preload:u,posterData:p}=e,d=p?.previewOnHover,f=(0,n.useCallback)(((e,r)=>o=>{t({[e]:r??o})}),[t]),m=()=>d?React.createElement("span",{className:i.A["help-message"]},__("Autoplay is turned off as the preview on hover is active.","jetpack-videopress-pkg")):React.createElement(React.Fragment,null,React.createElement("span",{className:i.A["help-message"]},__("Start playing the video as soon as the page loads.","jetpack-videopress-pkg")),r&&React.createElement("span",{className:i.A["help-message"]},__("Note: Autoplaying videos may cause usability issues for some visitors.","jetpack-videopress-pkg")));return React.createElement(o.PanelBody,{title:__("Playback","jetpack-videopress-pkg")},React.createElement(o.ToggleControl,{label:__("Autoplay","jetpack-videopress-pkg"),onChange:f("autoplay"),checked:r&&!d,disabled:d,help:React.createElement(m,null),__nextHasNoMarginBottom:!0}),React.createElement(o.ToggleControl,{label:__("Loop","jetpack-videopress-pkg"),onChange:f("loop"),checked:a,help:__("Restarts the video when it reaches the end.","jetpack-videopress-pkg"),__nextHasNoMarginBottom:!0}),React.createElement(o.ToggleControl,{label:__("Muted","jetpack-videopress-pkg"),onChange:f("muted"),checked:s,__nextHasNoMarginBottom:!0}),React.createElement(o.ToggleControl,{label:__("Show Controls","jetpack-videopress-pkg"),onChange:f("controls"),checked:c,help:__("Display the video playback controls.","jetpack-videopress-pkg"),__nextHasNoMarginBottom:!0}),React.createElement(o.ToggleControl,{label:__("Play Inline","jetpack-videopress-pkg"),onChange:f("playsinline"),checked:l,help:__("Play the video inline instead of full-screen on mobile devices.","jetpack-videopress-pkg"),__nextHasNoMarginBottom:!0}),React.createElement(o.ToggleControl,{label:__("Preload Metadata","jetpack-videopress-pkg"),onChange:f("preload","metadata"===u?"none":"metadata"),checked:"metadata"===u,help:__("Preload the video metadata when the page is loaded.","jetpack-videopress-pkg"),__nextHasNoMarginBottom:!0}),(0,n.createInterpolateElement)(__("Send us your <a>VideoPress feedback</a>","jetpack-videopress-pkg"),{a:React.createElement(o.ExternalLink,{href:"https://automattic.survey.fm/videopress-feedback"})}))}},7137:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var o=r(4715),n=r(6427),a=r(6087),i=r(7723),s=r(4954);const __=i.__,c=[];if(window.videopressAjax){const e=new Blob([`var videopressAjax = ${JSON.stringify({...window.videopressAjax,context:"sandbox"})};`],{type:"text/javascript"});c.push(URL.createObjectURL(e),window.videopressAjax.bridgeUrl)}function l({showCaption:e,html:t,isSelected:r,attributes:i,setAttributes:l,preview:u,isRequestingEmbedPreview:p}){const d=(0,a.useRef)(),f=(0,a.useRef)(),{maxWidth:m,caption:h,videoRatio:v,align:g}=i,[y,b]=(0,a.useState)(400);function k(){b(f.current.offsetWidth*v/100+12)}const[w,_]=(0,a.useState)(!1);(0,a.useEffect)((()=>{f?.current&&(u.html?setTimeout((()=>{b("auto")}),250):v&&(k(),setTimeout((()=>{k()}),0),_(!1)))}),[f,v,u]),(0,a.useEffect)((()=>{_(!!t)}),[t]);const E=(0,a.useCallback)((e=>{const{data:t}=e||{},{event:r}=t;"videopress_loading_state"===r&&_("loaded"===t?.state)}),[]);(0,a.useEffect)((()=>{const e=(0,s.Q)(f);if(e&&!p)return e.addEventListener("message",E),()=>e?.removeEventListener("message",E)}),[f,p]);const{atTime:R,previewOnHover:C,previewAtTime:S,previewLoopDuration:P,type:A}=i.posterData;let x;x="video-frame"===A&&C?S:R,(0,s.A)(f,p,{initialTimePosition:x,autoplay:i.autoplay,wrapperElement:d?.current,previewOnHover:C?{atTime:S,duration:P}:void 0}),(0,a.useEffect)((()=>{p&&k()}),[w,p]);const j=(0,a.useCallback)(((e,t,r)=>{let o=getComputedStyle(r).width;if(null!==r.parentElement){o===getComputedStyle(r.parentElement).width&&(o="100%")}b("auto"),l({maxWidth:o})}),[l]),T={},U=(0,a.useCallback)((e=>{e&&!h&&e.focus()}),[h]);"auto"!==y&&(T.height=y||200,T.paddingBottom=y?12:0);let N={marginRight:"auto"};"center"===g&&(N={...N,marginLeft:"auto"});return React.createElement("figure",{ref:d,className:"jetpack-videopress-player"},React.createElement(n.ResizableBox,{enable:{top:!1,bottom:!1,left:!1,right:!0},maxWidth:"100%",size:{width:m,height:"auto"},style:N,onResizeStop:j,onResizeStart:()=>b("auto")},!r&&React.createElement("div",{className:"jetpack-videopress-player__overlay"}),React.createElement("div",{className:"jetpack-videopress-player__wrapper",ref:f,style:T},React.createElement(React.Fragment,null,!p&&React.createElement(n.SandBox,{html:t,scripts:c,styles:["\n\t\tbody {\n\t\t\tline-height: 0;\n\t\t}\n\t"]}),!w&&React.createElement("div",{className:"jetpack-videopress-player__loading"},__("Loading…","jetpack-videopress-pkg"))))),e&&(!o.RichText.isEmpty(h)||r)&&React.createElement(o.RichText,{identifier:"caption",tagName:"figcaption","aria-label":__("Video caption text","jetpack-videopress-pkg"),placeholder:__("Add caption","jetpack-videopress-pkg"),value:h,onChange:e=>l({caption:e}),inlineToolbar:!0,ref:U}))}window?.videoPressEditorState?.playerBridgeUrl&&c.push(window.videoPressEditorState.playerBridgeUrl)},4749:(e,t,r)=>{"use strict";r.d(t,{A:()=>p,z:()=>u});var o=r(4715),n=r(6427),a=r(6087),i=r(7723),s=r(5938),c=r(5855),l=r(9069);const __=i.__;function u({poster:e,className:t}){const r=(0,a.createInterpolateElement)(__("No custom Poster image selected.<help>You can upload or select an image from your media library to override the default video image.</help>","jetpack-videopress-pkg"),{help:React.createElement("p",{className:"poster-panel-control__help"})}),o=(0,a.createInterpolateElement)(__("You are currently overriding the default Poster image.<help>Remove it if you want to use the default image generated by VideoPress.</help>","jetpack-videopress-pkg"),{help:React.createElement("p",{className:"poster-panel-control__help"})});return React.createElement("div",{className:t},e?o:r)}function p({attributes:e,setAttributes:t,clientId:r}){const{poster:a}=e;return React.createElement(n.Dropdown,{contentClassName:"dropdown-content",renderToggle:({isOpen:e,onToggle:t})=>React.createElement(n.ToolbarButton,{label:__("Poster image","jetpack-videopress-pkg"),showTooltip:!0,"aria-expanded":e,"aria-haspopup":"true",onClick:t,icon:s.A}),renderContent:({onClose:e})=>{const p=`video-block__poster-image-description-${r}`;return React.createElement(React.Fragment,null,React.createElement(n.NavigableMenu,{className:"poster-image-block-control__wrapper"},React.createElement(o.MediaUploadCheck,null,React.createElement(o.MediaUpload,{title:__("Select Poster Image","jetpack-videopress-pkg"),onSelect:r=>{(e=>{t({poster:e.url})})(r),e()},allowedTypes:l.o,render:({open:e})=>React.createElement(n.MenuItem,{icon:s.A,onClick:e,"aria-describedby":p},__("Open Media Library","jetpack-videopress-pkg"),React.createElement("p",{id:p,hidden:!0},a?(0,i.sprintf)(/* translators: Placeholder is an image URL. */
__("The current poster image url is %s","jetpack-videopress-pkg"),a):__("There is no poster image currently selected","jetpack-videopress-pkg")))})),React.createElement(u,{poster:a,className:"current-media"}),!!a&&React.createElement(n.MenuItem,{className:"poster-image-block-control__remove-button",variant:"tertiary",isDestructive:!0,onClick:()=>{t({poster:""}),e()},icon:c.A},__("Remove and use default","jetpack-videopress-pkg"))))}})}},8864:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>S,ZC:()=>k});var o=r(4715),n=r(6427),a=r(6087),i=r(7723),s=r(5938),c=r(5855),l=r(3022),u=r(3685),p=r(9689),d=r(6302),f=r(4967),m=r(4954),h=r(9069),v=r(4749);const __=i.__,g=3e3,y=1e4,b=5e3,k=()=>!!window?.Jetpack_Editor_Initial_State?.available_blocks?.["v6-video-frame-poster"],w=[];if(window.videopressAjax){const e=new Blob([`var videopressAjax = ${JSON.stringify({...window.videopressAjax,context:"sandbox"})};`],{type:"text/javascript"});w.push(URL.createObjectURL(e),window.videopressAjax.bridgeUrl)}function _({clientId:e,attributes:t,setAttributes:r}){const c=`video-block__poster-image-description-${e}`,{poster:l}=t,u=(0,a.useCallback)((e=>{r({poster:e.url,posterData:{...t.posterData,type:"media-library",id:e.id,url:e.url}})}),[t]),p=__("Select Poster Image","jetpack-videopress-pkg"),d=__("Replace Poster Image","jetpack-videopress-pkg"),f=(0,a.useRef)(null),m=Number(t?.videoRatio)/100||9/16,[v,g]=(0,a.useState)(140);return(0,a.useEffect)((()=>{if(!l||!f?.current)return;const{current:e}=f,t=e?.offsetWidth;t&&g(t*m)}),[l,f,m]),React.createElement(n.Dropdown,{contentClassName:"poster-panel__dropdown",placement:"top left",renderToggle:({isOpen:e,onToggle:t})=>React.createElement(n.Button,{ref:f,style:{backgroundImage:l?`url(${l})`:void 0,height:v,minHeight:v},className:"poster-panel__button "+(l?"has-poster":""),variant:"secondary",onClick:t,"aria-expanded":e},React.createElement("span",null,l?d:p)),renderContent:({onClose:e})=>React.createElement(n.NavigableMenu,{className:"block-editor-media-replace-flow__media-upload-menu"},React.createElement(o.MediaUploadCheck,null,React.createElement(o.MediaUpload,{title:__("Select Poster Image","jetpack-videopress-pkg"),onSelect:t=>{u(t),e()},allowedTypes:h.o,render:({open:e})=>React.createElement(n.MenuItem,{icon:s.A,onClick:e,"aria-describedby":c},__("Open Media Library","jetpack-videopress-pkg"),React.createElement("p",{id:c,hidden:!0},l?(0,i.sprintf)(/* translators: Placeholder is an image URL. */
__("The current poster image url is %s","jetpack-videopress-pkg"),l):__("There is no poster image currently selected","jetpack-videopress-pkg")))})))})}window?.videoPressEditorState?.playerBridgeUrl&&w.push(window.videoPressEditorState.playerBridgeUrl);const E=e=>{const t=e?.current?.querySelector("iframe.components-sandbox");return t?.contentWindow};function R({guid:e,isGeneratingPoster:t,atTime:r=.1,onVideoFrameSelect:o,duration:i}){const[s,c]=(0,a.useState)(r),d=(0,a.useRef)(null),h=(0,p.ep)(e,{autoplay:!0,muted:!0,controls:!1,loop:!1}),{preview:v={html:null},isRequestingEmbedPreview:g}=(0,f.g)(h),{html:y}=v,{playerIsReady:b,pause:k}=(0,m.A)(d,g,{initialTimePosition:r});(0,a.useEffect)((()=>{b&&k()}),[b,k]);const _=(0,a.useCallback)((e=>{const t=E(d);t?.postMessage({event:"videopress_action_set_currenttime",currentTime:e/1e3}),o(e)}),[E,o]);return React.createElement("div",{className:"poster-panel__frame-picker"},React.createElement("div",{ref:d,className:(0,l.A)("poster-panel__frame-picker__sandbox-wrapper",{"is-player-ready":b,"is-generating-poster":t})},(!b||t)&&React.createElement(n.Spinner,null),React.createElement(n.SandBox,{html:y,scripts:w})),t&&React.createElement(n.Notice,{status:"info",className:"poster-panel__notice",isDismissible:!1},__("Generating video poster image. It may take a few seconds.","jetpack-videopress-pkg")),React.createElement(u.Ay,{label:__("Video frame","jetpack-videopress-pkg"),help:__("Select the frame you want to use as poster image","jetpack-videopress-pkg"),disabled:g||t,max:i,value:s,wait:250,fineAdjustment:50,onChange:c,onDebounceChange:_}))}function C({previewOnHover:e=!1,previewAtTime:t=0,loopDuration:r=b,videoDuration:o,onPreviewOnHoverChange:s,onPreviewAtTimeChange:c,onLoopDurationChange:l}){const p=!o,f=Math.max(o-g,0),[m,h]=(0,a.useState)(Math.min(y,o-t)),v=Math.floor(m/10)/100,k=(0,a.createInterpolateElement)((0,i.sprintf)(/* translators: placeholder is video duration */
__("Video duration: <em>%s</em>.","jetpack-videopress-pkg"),(0,d.ZO)(o)),{em:React.createElement("em",null)}),w=(0,a.createInterpolateElement)((0,i.sprintf)(/* translators: placeholders are the minimum and maximum lapse duration for the previewOnHover, in seconds */
__("Minimum: <em>%1$ss</em>. Maximum: <em>%2$ss</em>.","jetpack-videopress-pkg"),Math.min(g/1e3,v),v),{em:React.createElement("em",null)}),_=0===f,E=m<=g,R=(0,a.useCallback)((e=>{c(e);const t=Math.min(y,o-e);h(t),r>t&&l(t)}),[c,o,r,l]);return React.createElement(React.Fragment,null,React.createElement(n.ToggleControl,{className:"poster-panel__preview-toggle",label:__("Video preview on hover","jetpack-videopress-pkg"),checked:e,onChange:s,disabled:!e&&p,__nextHasNoMarginBottom:!0}),e&&React.createElement(React.Fragment,null,React.createElement(u.Ay,{label:__("Starting point","jetpack-videopress-pkg"),max:f,fineAdjustment:50,value:t,onDebounceChange:R,wait:300,disabled:p||_,help:k}),React.createElement(u.Ay,{max:m,min:g,fineAdjustment:50,label:__("Loop duration","jetpack-videopress-pkg"),value:r,onDebounceChange:l,wait:300,help:w,disabled:p||E,marksEvery:1e3})))}function S({attributes:e,setAttributes:t,isGeneratingPoster:r,videoBelongToSite:o}){const{poster:i,posterData:s}=e,u=e?.duration,p="video-frame"===s?.type,d=s?.previewOnHover||!1,f=s?.previewAtTime??s?.atTime??0;let m=s?.previewLoopDuration??b;m>u-f&&(m=u-f);const h=(0,a.useCallback)((r=>{t({posterData:{...e.posterData,type:r?"video-frame":"media-library"},poster:r?"":e.posterData.url||""})}),[e]),g=(0,a.useCallback)((r=>{let o={...e.posterData,previewOnHover:r};r&&(o={previewAtTime:f,previewLoopDuration:m,...o}),t({posterData:o,controls:!r&&e.controls})}),[e]),y=(0,a.useCallback)((r=>{t({posterData:{...e.posterData,previewAtTime:r}})}),[e]),w=(0,a.useCallback)((r=>{let o=f;f+r>u&&(o=u-r),t({posterData:{...e.posterData,previewLoopDuration:r,previewAtTime:o}})}),[e]),E=k()?__("Poster and preview","jetpack-videopress-pkg"):__("Poster","jetpack-videopress-pkg");return React.createElement(n.PanelBody,{title:E,className:"poster-panel",initialOpen:!1},React.createElement(n.ToggleControl,{label:__("Pick from video frame","jetpack-videopress-pkg"),checked:p&&o,onChange:h,disabled:!o,__nextHasNoMarginBottom:!0}),React.createElement("div",{className:(0,l.A)("poster-panel__frame-wrapper",{"is-selected":p})},React.createElement(R,{isGeneratingPoster:r,guid:e?.guid,atTime:s?.atTime,duration:u,onVideoFrameSelect:r=>{t({posterData:{...e.posterData,type:"video-frame",atTime:r},poster:""})}})),React.createElement("div",{className:(0,l.A)("poster-panel__image-wrapper",{"is-selected":!p})},React.createElement(_,{attributes:e,setAttributes:t}),React.createElement(v.z,{poster:i,className:"poster-panel-card"}),i&&React.createElement(n.MenuItem,{onClick:()=>{t({poster:"",posterData:{...e.posterData,url:""}})},icon:c.A,isDestructive:!0,variant:"tertiary"},__("Remove and use default","jetpack-videopress-pkg"))),k()&&React.createElement(C,{previewOnHover:d,previewAtTime:f,loopDuration:m,videoDuration:u,onPreviewOnHoverChange:g,onPreviewAtTimeChange:y,onLoopDurationChange:w}))}},9573:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var o=r(3829);function n({attributes:e,setAttributes:t,privateEnabledForSite:r,videoBelongToSite:n}){return React.createElement(o.A,{attributes:e,setAttributes:t,privateEnabledForSite:r,videoBelongToSite:n})}},3829:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var o=r(6427),n=r(7723),a=r(8962);const __=n.__,_x=n._x;function i({attributes:e,setAttributes:t,privateEnabledForSite:r,videoBelongToSite:n}){const{privacySetting:i,rating:s,allowDownload:c,displayEmbed:l}=e,u={private:_x("Site Default (Private)","VideoPress privacy setting","jetpack-videopress-pkg"),public:_x("Site Default (Public)","VideoPress privacy setting","jetpack-videopress-pkg")},p={value:String(a.zG.indexOf(a.CR)),label:r?u.private:u.public},d={value:String(a.zG.indexOf(a.az)),label:_x("Public","VideoPress privacy setting","jetpack-videopress-pkg")},f={value:String(a.zG.indexOf(a.o0)),label:_x("Private","VideoPress privacy setting","jetpack-videopress-pkg")};return React.createElement(o.PanelBody,{title:__("Privacy and rating","jetpack-videopress-pkg"),initialOpen:!1},React.createElement(o.SelectControl,{label:_x("Rating","The age rating for this video.","jetpack-videopress-pkg"),value:s??"",options:[{label:_x("G",'Video rating for "General Audiences".',"jetpack-videopress-pkg"),value:a.ko},{label:_x("PG-13",'Video rating for "Parental Guidance", unsuitable for children under 13.',"jetpack-videopress-pkg"),value:a.z7},{label:_x("R",'Video rating for "Restricted", not recommended for children under 17.',"jetpack-videopress-pkg"),value:a.iL}],onChange:e=>{t({rating:e})},disabled:!n,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),React.createElement(o.SelectControl,{label:__("Privacy","jetpack-videopress-pkg"),onChange:e=>{const o={};o.isPrivate=e!==p.value?e===f.value:r,o.privacySetting=Number(e),t(o)},value:String(i),options:[p,d,f],disabled:!n,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),React.createElement(o.ToggleControl,{label:__("Allow download","jetpack-videopress-pkg"),checked:c,onChange:e=>{t({allowDownload:e})},disabled:!n,__nextHasNoMarginBottom:!0}),React.createElement(o.ToggleControl,{label:__("Show video sharing menu","jetpack-videopress-pkg"),checked:l,onChange:e=>{t({displayEmbed:e})},help:__("Gives viewers the option to share the video link and HTML embed code","jetpack-videopress-pkg"),disabled:!n,__nextHasNoMarginBottom:!0}))}},9454:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(4715),n=r(9069);const a=({attributes:e,onUploadFileStart:t,onSelectVideoFromLibrary:r,onSelectURL:a})=>{const i=e.isPrivate?"https://video.wordpress.com/v":"https://videopress.com/v";return React.createElement(o.MediaReplaceFlow,{mediaId:e.id,handleUpload:!1,accept:"video/*",allowedTypes:n.N,onSelect:function(e){e?.[0]?.name&&e?.[0]?.size&&e?.[0]?.type?t(e[0]):r(e)},mediaURL:`${i}/${e.guid}`,onSelectURL:a})}},4554:(e,t,r)=>{"use strict";r.d(t,{A:()=>y});var o=r(6427),n=r(3582),a=r(7143),i=r(6087),s=r(7723),c=r(7326),l=r(6941),u=r.n(l),p=r(9689),d=r(8634),f=r(3534),m=r(3348);const __=s.__,h=u()("videopress:tracks:tracks-control");function v({track:e,guid:t,onDelete:r}){const[n,a]=(0,i.useState)(!1),{kind:s,label:c,srcLang:l}=e;return React.createElement("div",{className:"video-tracks-control__track-item "+(n?"is-deleting":"")},React.createElement("div",{className:"video-tracks-control__track-item-label"},React.createElement("strong",null,c),React.createElement("span",{className:"video-tracks-control__track-item-kind"},s,l?.length?` [${l}]`:"")),React.createElement(o.Button,{variant:"link",isDestructive:!0,onClick:()=>{a(!0),(0,d.Dr)(e,t).then((()=>{a(!1),r?.(e)}))},disabled:n},__("Delete","jetpack-videopress-pkg")))}function g({tracks:e,guid:t,onTrackListUpdate:r}){if(!e?.length)return React.createElement(o.MenuGroup,null,React.createElement("div",{className:"video-tracks-control__track_list__no-tracks"},__("Tracks can be subtitles, captions, chapters, or descriptions. They help make your content more accessible to a wider range of users.","jetpack-videopress-pkg")));const n=(0,i.useCallback)((t=>{const o=[...e].filter((e=>e!==t));r(o)}),[e]);return React.createElement(o.MenuGroup,{className:"video-tracks-control__track_list",label:__("Text tracks","jetpack-videopress-pkg")},e.map(((e,r)=>React.createElement(v,{key:`${e.kind}-${r}`,track:e,guid:t,onDelete:n}))))}function y({attributes:e,setAttributes:t}){const{tracks:r,guid:s}=e,[l,u]=(0,i.useState)(!1),[v,y]=(0,i.useState)(""),b=(0,a.useDispatch)(n.store).invalidateResolution,k=(0,p.ep)(s,e),w=(0,i.useCallback)((e=>{(0,d.n)(e,s).then((o=>{if(o?.error)return h("catch at regular response",o),void y(`Track error: ${o?.message||o.error}`);const n={...e,src:o};delete n.tmpFile;const a=r.findIndex((e=>e.kind===n.kind&&e.srcLang===n.srcLang)),i=[...r];a>-1?(h("new track already exists, replacing it"),i[a]=n):(h("new track does not exist, adding it"),i.push(n)),t({tracks:i}),u(!1),b("getEmbedPreview",[k])})).catch((e=>{h("catch at catch"),y(`Track error: ${e?.message||e.error}`)}))}),[r]),_=(0,i.useCallback)((e=>{t({tracks:e}),b("getEmbedPreview",[k])}),[r]),E=(0,i.useCallback)((()=>{y(""),u(!0)}),[]);return React.createElement(o.ToolbarDropdownMenu,{icon:f.a,label:__("Text tracks","jetpack-videopress-pkg"),popoverProps:{variant:"toolbar"}},(()=>l?React.createElement(m.A,{onCancel:()=>{u(!1)},onSave:w,tracks:r,errorMessage:v}):React.createElement(React.Fragment,null,React.createElement(g,{tracks:r,guid:s,onTrackListUpdate:_}),React.createElement(o.MenuGroup,{label:__("Add tracks","jetpack-videopress-pkg"),className:"video-tracks-control"},React.createElement(o.MenuItem,{icon:c.A,onClick:E},__("Upload track","jetpack-videopress-pkg"))))))}},3348:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var o=r(4715),n=r(6427),a=r(7143),i=r(6087),s=r(7723),c=r(6941),l=r.n(c);const __=s.__,u="subtitles",p=".vtt,text/vtt",d=[{label:__("Subtitles","jetpack-videopress-pkg"),value:"subtitles"},{label:__("Captions","jetpack-videopress-pkg"),value:"captions"},{label:__("Descriptions","jetpack-videopress-pkg"),value:"descriptions"},{label:__("Chapters","jetpack-videopress-pkg"),value:"chapters"},{label:__("Metadata","jetpack-videopress-pkg"),value:"metadata"}],f=l()("videopress:tracks:track-form");function m({onCancel:e,onSave:t,tracks:r,errorMessage:c}){const[l,m]=(0,i.useState)(!1),[h,v]=(0,i.useState)(!1),[g,y]=(0,i.useState)(""),[b,k]=(0,i.useState)(!1),[w,_]=(0,i.useState)({kind:u,srcLang:"",label:"",tmpFile:null});f("props.errorMessage",c);const E=(0,i.useCallback)(((e,t)=>{f("updateTrack",e,t),"tmpFile"===e&&y(""),_((r=>({...r,[e]:t})))}),[w]);(0,i.useEffect)((()=>{const e=r.some((e=>e.srcLang===w.srcLang&&e.kind===w.kind));v(e)}),[w,r]),(0,i.useEffect)((()=>{y(c),c&&m(!1)}),[c]);const R=w.tmpFile?.name,C=(0,a.useSelect)((e=>e(o.store).getSettings().mediaUpload),[]),S=(0,i.useCallback)((()=>{m(!0),y(""),t(w)}),[w]),P=(0,i.useCallback)((e=>{if(E("srcLang",e),e?.length>5)return y(__("Language must be five characters or less.","jetpack-videopress-pkg"));y(c||"")}),[c]);if(!C)return null;const A=(0,s.sprintf)(/* translators: %s: The allowed file types to be uploaded as a video text track." */
__("Add a new text track to the video. Allowed formats: %s","jetpack-videopress-pkg"),p);return f("error",g),React.createElement(n.MenuGroup,{className:"video-tracks-control__track-form",label:__("Upload track","jetpack-videopress-pkg")},React.createElement("div",{className:"video-tracks-control__track-form-container"},React.createElement("div",{className:"video-tracks-control__track-form-upload-file"},React.createElement("div",{className:"video-tracks-control__track-form-upload-file-label"},React.createElement("span",null,__("File","jetpack-videopress-pkg"),":"),R&&React.createElement("strong",null,R),React.createElement(o.MediaUploadCheck,null,React.createElement(n.FormFileUpload,{onChange:e=>{const t=e.target.files;t?.length&&E("tmpFile",t[0])},accept:p,render:({openFileDialog:e})=>React.createElement(n.Button,{variant:"link",onClick:()=>{e()}},__("Select track","jetpack-videopress-pkg")),__next40pxDefaultSize:!0}))),React.createElement("div",{className:"video-tracks-control__track-form-upload-file-help"},A)),React.createElement("div",{className:"video-tracks-control__track-form-label-language"},React.createElement(n.TextControl,{onChange:e=>E("label",e),label:__("Label","jetpack-videopress-pkg"),value:w.label,help:__("Title of track","jetpack-videopress-pkg"),disabled:l,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),React.createElement(n.TextControl,{className:"video-tracks-control__track-form-language-tag",label:__("Source language","jetpack-videopress-pkg"),value:w.srcLang,onChange:P,help:__("Language (en, fr, etc.)","jetpack-videopress-pkg"),disabled:l,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0})),React.createElement(n.SelectControl,{options:d,value:w.kind,label:/* translators: %s: The kind of video text track e.g: "Subtitles, Captions" */
__("Kind","jetpack-videopress-pkg"),onChange:e=>E("kind",e),disabled:l,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),g&&React.createElement(n.Notice,{status:"error",isDismissible:!1},g),React.createElement("div",{className:"video-tracks-control__track-form-buttons-container "+(h?" track-exists":"")},h&&React.createElement(n.ToggleControl,{className:"video-tracks-control__track-form-toggle",label:__("Track exists. Replace?","jetpack-videopress-pkg"),checked:b,onChange:k,__nextHasNoMarginBottom:!0}),React.createElement(n.Button,{isBusy:l,variant:"secondary",disabled:!w.tmpFile||l||h&&!b||!!g,onClick:S},__("Save","jetpack-videopress-pkg")),React.createElement(n.Button,{variant:"link",onClick:e},__("Cancel","jetpack-videopress-pkg")))))}},3339:(e,t,r)=>{"use strict";r.d(t,{A:()=>b});var o=r(3924),n=r(1455),a=r.n(n),i=r(4715),s=r(6427),c=r(6087),l=r(7723),u=r(7430),p=r(560),d=r(9448),f=r(9689),m=r(9069),h=r(3898),v=r(3534),g=r(6466),y=r(4217);const __=l.__,b=(0,s.withNotices)((({attributes:e,setAttributes:t,noticeUI:r,noticeOperations:n,handleDoneUpload:l,fileToUpload:b,isReplacing:k,onReplaceCancel:w,isActive:_})=>{const[E,R]=(0,c.useState)(!1),[C,S]=(0,c.useState)(!1),[P,A]=(0,c.useState)(!1),[x,j]=(0,c.useState)(!1),T=(0,d.xI)();(0,c.useEffect)((()=>{b&&G(b)}),[b]);const[U,N]=(0,c.useState)(null),[O,B]=(0,c.useState)([]),D=(0,c.useCallback)((function(...e){B(e)}),[]),[L,I]=(0,c.useState)(null),F=(0,c.useCallback)((function(e){if(e?.originalResponse)try{const t=e?.originalResponse?.getBody?.(),r=JSON.parse(t);return void I(r)}catch{}I(e)}),[]),{uploadHandler:M,resumeHandler:H,error:z}=(0,u.A)({onError:F,onProgress:D,onSuccess:S});function V(e,t){const{guid:r,url:o}=(0,f.kK)(e),n=__("Invalid VideoPress URL","jetpack-videopress-pkg");if(r){const e=(0,f.qh)(o);l({...e,guid:r,id:t})}else{const r=(0,f.up)(e);r?(0,f.i2)(r).then((e=>{e?l({...e,id:t}):I({data:{message:n}})})):I({data:{message:n}})}}const G=e=>{L&&F(null),N(e),D(0,e.size),A(!0),M(e)},q=()=>{if(!H)return;H[E?"start":"abort"](),R(!E)},$=function(){H.abort(),w()};if("owner_not_connected"===z?.code){const e=(0,c.createInterpolateElement)(__("<connectLink>Connect</connectLink> your site to use the <moreAboutVideoPressLink>VideoPress</moreAboutVideoPressLink> video block.","jetpack-videopress-pkg"),{connectLink:React.createElement("a",{href:z?.data?.connect_url,rel:"noreferrer noopener"}),moreAboutVideoPressLink:React.createElement(s.ExternalLink,{href:(0,o.A)("jetpack-videopress")})});return React.createElement(h.P,{errorMessage:e},React.createElement(s.Button,{key:"videopress-connect-user",variant:"primary",href:z?.data?.connect_url},__("Connect","jetpack-videopress-pkg")))}if(L){const e=()=>{G(U)},t=()=>{N(null),D([]),F(null),A(!1)};return React.createElement(g.A,{onRetry:e,onCancel:t,errorData:L})}if(P){const r=O[0]/O[1]*100;return React.createElement(y.A,{attributes:e,setAttributes:t,file:U,progress:r,paused:E,uploadedVideoData:C,onPauseOrResume:q,onReplaceCancel:$,isReplacing:k,onDone:l,supportPauseOrResume:!!H})}if(x)return React.createElement(h.P,{disableInstructions:!0},React.createElement("div",{className:"loading-wrapper"},React.createElement(s.Spinner,null),React.createElement("span",null,__("Loading…","jetpack-videopress-pkg"))));if(!_){const e=__("Connect your WordPress.com account to enable high-quality, ad-free video.","jetpack-videopress-pkg"),t=__("Activate the VideoPress module to enable high-quality, ad-free video.","jetpack-videopress-pkg");return React.createElement(h.P,{disableInstructions:!0,className:"disabled"},React.createElement("span",null,T?t:e))}return React.createElement(i.MediaPlaceholder,{handleUpload:!1,className:"is-videopress-placeholder",icon:React.createElement(i.BlockIcon,{icon:v.K}),labels:{title:__("VideoPress","jetpack-videopress-pkg"),instructions:__("Embed a video from your media library or upload a new one with VideoPress.","jetpack-videopress-pkg")},onSelect:function(e){if(e=e?.[0]?e[0]:e,e?.name&&e?.size&&e?.type)G(e);else if(e.videopress_guid){V(Array.isArray(e.videopress_guid)?e.videopress_guid[0]:e.videopress_guid,e?.id)}else{if(e.id){const t=`videopress/v1/upload/${e.id}`;return j(!0),void a()({path:t,method:"GET"}).then((t=>{var r;j(!1),"new"===t.status||"resume"===t.status?(N(e),D(t.file_size,t.file_size),A(!0),r=e.id,(0,p.NP)(r).then((e=>{S(e)})).catch((e=>{I(e)}))):"uploaded"===t.status?V(t.uploaded_video_guid):I({data:{message:t.message?t.message:__("Error selecting video. Please try again.","jetpack-videopress-pkg")}})})).catch((e=>{j(!1),I({data:{message:e.message}})}))}I({data:{message:__("Please select a video from Library or upload a new one","jetpack-videopress-pkg")}})}},onSelectURL:V,accept:"video/*",allowedTypes:m.N,value:e,notices:r,onError:function(e){n.removeAllNotices(),n.createErrorNotice(e)}})}))},9863:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var o=r(4715),n=r(6427),a=r(6087),i=r(7723),s=r(1113),c=r(3022),l=r(7975),u=r(6720);const __=i.__,p=["image"],d=({videoPosterImageUrl:e})=>React.createElement("div",{className:"uploading-editor__poster-image"},e?React.createElement("img",{src:e,alt:"Poster"}):React.createElement("span",null,"No Poster Selected")),f=({file:e,videoPosterImageData:t,onVideoFrameSelected:r})=>{const o=Boolean(t?.url),n=(0,a.useRef)(e?.url??URL.createObjectURL(e));return React.createElement("div",{className:(0,c.A)("uploading-editor__poster-container")},React.createElement(u.A,{src:n?.current,onVideoFrameSelected:r,className:(0,c.A)({"uploading-editor__hide":o})}),o&&React.createElement(React.Fragment,null,React.createElement(d,{videoPosterImageUrl:t?.url}),React.createElement(s.A,{className:"uploading-editor__play-icon",icon:l.A})))},m=({hasPoster:e,onSelectPoster:t,onRemovePoster:r})=>e?React.createElement(o.MediaUpload,{title:__("Select Poster Image","jetpack-videopress-pkg"),onSelect:t,allowedTypes:p,render:({open:e})=>React.createElement("div",{className:"uploading-editor__poster-buttons"},React.createElement(n.Button,{onClick:r,variant:"secondary",isDestructive:!0},__("Remove Poster Image","jetpack-videopress-pkg")),React.createElement(n.Button,{variant:"secondary",onClick:e},__("Select Poster Image","jetpack-videopress-pkg")))}):React.createElement("span",{className:"uploading-editor__scrubber-help"},__("This is how the video will look. Use the slider to choose a poster image or change it from the block settings.","jetpack-videopress-pkg")),h=e=>{const{file:t,onSelectPoster:r,onRemovePoster:o,videoPosterImageData:a,onVideoFrameSelected:i}=e;return React.createElement("div",{className:"uploading-editor"},React.createElement(n.BaseControl,{__nextHasNoMarginBottom:!0},React.createElement(n.BaseControl.VisualLabel,null,__("Video poster (optional)","jetpack-videopress-pkg")),React.createElement(f,{file:t,videoPosterImageData:a,onVideoFrameSelected:i}),React.createElement(m,{hasPoster:Boolean(a),onSelectPoster:r,onRemovePoster:o})))}},6466:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var o=r(6427),n=r(7723),a=r(3898);const __=n.__,i=({errorData:e,onRetry:t,onCancel:r})=>{const n=(e=>{if(!e)return"";let t=e?.data?.message||__("Failed to upload your video. Please try again.","jetpack-videopress-pkg");return"Invalid Mime"===t&&(t=React.createElement(React.Fragment,null,__("The format of the video you uploaded is not supported.","jetpack-videopress-pkg")," ",React.createElement(o.ExternalLink,{href:"https://wordpress.com/support/videopress/recommended-video-settings/",target:"_blank",rel:"noreferrer"},__("Check the recommended video settings.","jetpack-videopress-pkg")))),t})(e);return React.createElement(a.P,{errorMessage:n,onNoticeRemove:r},React.createElement("div",{className:"videopress-uploader-progress__error-actions"},React.createElement(o.Button,{variant:"primary",onClick:t},__("Try again","jetpack-videopress-pkg")),React.createElement(o.Button,{variant:"secondary",onClick:r},__("Cancel","jetpack-videopress-pkg"))))}},4217:(e,t,r)=>{"use strict";r.d(t,{A:()=>w});var o=r(1455),n=r.n(o),a=r(6427),i=r(9491),s=r(6087),c=r(9877),l=r(7723),u=r(6941),p=r.n(u),d=r(7578),f=r(2185),m=r(295),h=r(9697),v=r(9689),g=r(3898),y=r(9863);const __=l.__,b=p()("videopress:block:uploader"),k=({setAttributes:e,videoData:t,onDone:r})=>{const[o,a]=(0,s.useState)(!1),[c,l]=(0,s.useState)(null),[u,p]=(0,s.useState)(null),{title:d}=t,v=t?.guid,g=(0,h.A)(v),y=(0,m.A)(v),b=(0,f.A)(t?.id),k=({data:t})=>{t?.generating?setTimeout((()=>{new Promise(((e,t)=>{y(v).then((t=>e(t))).catch((()=>{n()({path:`/videos/${v}/poster`,apiNamespace:"rest/v1.1",global:!0,method:"GET"}).then((t=>e(t))).catch((e=>t(e)))}))})).then((e=>k(e)))}),2e3):t?.poster&&e({poster:t?.poster})},w=(0,i.useDebounce)((e=>{var t;t=e,new Promise(((e,r)=>{g(t).then((t=>{k(t),e()})).catch((()=>{n()({path:`/videos/${v}/poster`,apiNamespace:"rest/v1.1",method:"POST",global:!0,data:t}).then((()=>{e()})).catch((e=>{r(e)}))}))}))}),1e3);return(0,s.useEffect)((()=>{if(v)return u?w({poster_attachment_id:u?.id}):void(null!=c&&w({at_time:c,is_millisec:!0}))}),[u,c,v]),[e=>{l(e),p(null)},e=>{p(e)},()=>{p(null)},()=>{a(!0);const e=[];d&&e.push(b({title:d})),Promise.allSettled(e).then((()=>{a(!1),r(t)}))},u,o]},w=({attributes:e,setAttributes:t,progress:r,file:o,paused:n,uploadedVideoData:i,onPauseOrResume:u,onDone:p,supportPauseOrResume:f,isReplacing:m,onReplaceCancel:h})=>{const[w,_,E,R,C,S]=k({setAttributes:t,videoData:{...i,title:e.title},onDone:p}),[P,A]=(0,s.useState)(!0);(0,s.useEffect)((()=>{i&&!S&&P&&(b("Waiting for some time before enabling the DONE button..."),setTimeout((()=>{b("Done, enabling the DONE button now..."),A(!1)}),2500))}),[i,S]);const x=Math.round(r),j={width:`${x}%`},T=__("Resume","jetpack-videopress-pkg"),U=__("Pause","jetpack-videopress-pkg"),N=o?.filesizeHumanReadable??(0,d.O)(o?.size),{title:O}=e,B=(0,v.T3)((0,c.escapeHTML)(o?.name));return React.createElement(g.P,{disableInstructions:!0},React.createElement(a.TextControl,{label:__("Video title","jetpack-videopress-pkg"),className:"uploading-editor__title",onChange:e=>t({title:e}),value:O,placeholder:B,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),React.createElement(y.A,{file:o,onSelectPoster:_,onRemovePoster:E,onVideoFrameSelected:w,videoPosterImageData:C}),React.createElement("div",{className:"videopress-uploader-progress"},x<100?React.createElement(React.Fragment,null,React.createElement("div",{className:"videopress-uploader-progress__file-info"},React.createElement("div",{className:"videopress-uploader-progress__progress"},React.createElement("div",{className:"videopress-uploader-progress__progress-loaded",style:j})),React.createElement("div",{className:"videopress-upload__percent-complete"},(0,l.sprintf)(/* translators: Placeholder is an upload progress percenatage number, from 0-100. */
__("Uploading (%1$s%%)","jetpack-videopress-pkg"),x)),React.createElement("div",{className:"videopress-uploader-progress__file-size"},N)),m&&React.createElement("div",{className:"videopress-uploader-progress__actions"},React.createElement(a.Button,{onClick:h,variant:"tertiary",isDestructive:!0},__("Cancel","jetpack-videopress-pkg"))),React.createElement("div",{className:"videopress-uploader-progress__actions"},x<100&&React.createElement(a.Button,{variant:"tertiary",onClick:u,disabled:!f},n?T:U))):React.createElement(React.Fragment,null,P?React.createElement("span",null,__("Finishing up …","jetpack-videopress-pkg")," 🎬"):React.createElement("span",null,__("Upload Complete!","jetpack-videopress-pkg")," 🎉"),React.createElement(a.Button,{variant:"primary",onClick:R,disabled:P,isBusy:P},__("Done","jetpack-videopress-pkg")))))}},9069:(e,t,r)=>{"use strict";r.d(t,{N:()=>o,o:()=>n});const o=["video"],n=["image"]},5980:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var o=r(6072),n=r.n(o),a=r(4715),i=r(3022),s=r(9689),c=r(8864);const l=[{attributes:{autoplay:{type:"boolean"},title:{type:"string"},description:{type:"string"},caption:{type:"string",source:"html",selector:"figcaption"},controls:{type:"boolean",default:!0},loop:{type:"boolean"},maxWidth:{type:"string",default:"100%"},muted:{type:"boolean"},playsinline:{type:"boolean"},preload:{type:"string",default:"metadata"},seekbarPlayedColor:{type:"string",default:""},seekbarLoadingColor:{type:"string",default:""},seekbarColor:{type:"string",default:""},useAverageColor:{type:"boolean",default:!0},id:{type:"number"},guid:{type:"string"},src:{type:"string"},cacheHtml:{type:"string",default:""},poster:{type:"string"},posterData:{type:"object",default:{}},videoRatio:{type:"number"},tracks:{type:"array",items:{type:"object"},default:[]},privacySetting:{type:"number",default:1},allowDownload:{type:"boolean",default:!0},displayEmbed:{type:"boolean",default:!0},rating:{type:"string"},isPrivate:{type:"boolean"},isExample:{type:"boolean",default:!1},duration:{type:"number"}},supports:{align:!0,anchor:!0},save:function({attributes:e}){const{align:t,autoplay:r,caption:o,loop:l,muted:u,controls:p,playsinline:d,preload:f,useAverageColor:m,seekbarColor:h,seekbarLoadingColor:v,seekbarPlayedColor:g,guid:y,maxWidth:b,poster:k,posterData:w}=e,_=a.useBlockProps.save({className:(0,i.A)("wp-block-jetpack-videopress","jetpack-videopress-player",{[`align${t}`]:t})}),E=(0,c.ZC)(),R=E?r||w.previewOnHover:r,C=E?u||w.previewOnHover:u,S=(0,s.ep)(y,{autoplay:R,controls:p,loop:l,muted:C,playsinline:d,preload:f,seekbarColor:h,seekbarLoadingColor:v,seekbarPlayedColor:g,useAverageColor:m,poster:k}),P={};return b&&b.length>0&&"100%"!==b&&(P.maxWidth=b,P.margin="auto"),React.createElement("figure",n()({},_,{style:P}),S&&React.createElement("div",{className:"jetpack-videopress-player__wrapper"},`\n${S}\n`),!a.RichText.isEmpty(o)&&React.createElement(a.RichText.Content,{tagName:"figcaption",value:o}))}}]},3898:(e,t,r)=>{"use strict";r.d(t,{A:()=>G,P:()=>V});var o=r(6072),n=r.n(o),a=r(5985),i=r(3162),s=r(4715),c=r(4997),l=r(6427),u=r(3582),p=r(7143),d=r(6087),f=r(7723),m=r(7913),h=r(3022),v=r(6941),g=r.n(v),y=r(9448),b=r(9689),k=r(4967),w=r(5007),_=r(8228),E=r(9079),R=r(8226),C=r(917),S=r(3534),P=r(396),A=r(7137),x=r(4749),j=r(8864),T=r(9573),U=r(9454),N=r(4554),O=r(3339),B=r(1200);const __=f.__,D=g()("videopress:video:edit"),{myJetpackConnectUrl:L,jetpackVideoPressSettingUrl:I}=window?.videoPressEditorState||{},F=(0,y.ie)(),M=(0,y.b5)(),H=(0,y.Lv)(),z=10,V=(0,l.withNotices)((function({children:e,errorMessage:t,noticeUI:r,noticeOperations:o,instructions:n=B.h_,disableInstructions:a,className:i}){return(0,d.useEffect)((()=>{t&&(o.removeAllNotices(),o.createErrorNotice(t))}),[t,o]),React.createElement(l.Placeholder,{icon:React.createElement(s.BlockIcon,{icon:S.K}),label:B.DD,instructions:a?null:n,notices:r,className:i},e)}));function G({attributes:e,setAttributes:t,isSelected:r,clientId:o}){const{autoplay:f,loop:v,muted:g,controls:S,playsinline:B,preload:G,useAverageColor:q,seekbarColor:$,seekbarLoadingColor:J,seekbarPlayedColor:W,guid:Z,cacheHtml:K,poster:Y,posterData:X,align:Q,videoRatio:ee,tracks:te,src:re,caption:oe,isExample:ne}=e,ae=(0,b.ep)(Z,{autoplay:f||X.previewOnHover,controls:S,loop:v,muted:g||X.previewOnHover,playsinline:B,preload:G,seekbarColor:$,seekbarLoadingColor:J,seekbarPlayedColor:W,useAverageColor:q,poster:Y}),[ie,se]=(0,d.useState)(!1),ce=(0,y.xI)(),{tracks:le}=(0,a.st)(),ue=te?.filter((e=>"chapters"===e.kind))?.[0],[pe,de]=(0,d.useState)(!!oe),{replaceBlock:fe,__unstableMarkNextChangeAsNotPersistent:me}=(0,p.useDispatch)(s.store),{videoData:he,isRequestingVideoData:ve,error:ge,isOverwriteChapterAllowed:ye,isGeneratingPoster:be,videoBelongToSite:ke}=(0,w.n)(e,t),{filename:we,private_enabled_for_site:_e}=he,{preview:Ee,isRequestingEmbedPreview:Re}=(0,k.g)(ae),{html:Ce,width:Se,height:Pe}=Ee;(0,d.useEffect)((()=>{Ce&&Ce!==K&&(me(),t({cacheHtml:Ce}))}),[Ce,K,t]);const Ae=Ce||K;(0,d.useEffect)((()=>{if(!Se||!Pe)return;const e=Pe/Se*100;e!==ee&&(me(),t({videoRatio:e}))}),[ee,Se,Pe,t]);const xe=(0,p.useDispatch)(u.store).invalidateResolution,je=(0,d.useCallback)((()=>{xe("getEmbedPreview",[ae])}),[ae,xe]),[Te,Ue]=(0,d.useState)(0),Ne=(0,d.useRef)();function Oe(){Ne?.current&&(Ne.current=clearInterval(Ne.current))}(0,d.useEffect)((()=>Te>=z?(D("Generating preview ➡ attempts number reached out 😪",Te),Oe()):ae?Re?(D("Generating preview ➡ Requesting… ⌛"),Oe()):Ee.html?(D("Generating preview ➡ Preview achieved 🎉 %o",Ee),Oe()):Ne?.current?void D("Generating preview ➡ Process already requested ⌛"):(Ne.current=setTimeout((()=>{if(Ee.html)return D("Generating preview ➡ Preview already achieved 🎉 %o",Ee),void Ue(0);Ue((e=>e+1)),D("Generating preview ➡ Not achieved so far. Start attempt %o 🔥",Te+1),je()}),2e3),Oe):(D("Generating preview ➡ No URL Provided 👋🏻"),Oe())),[Te,Ne,je,Ee,ae,Re]);const{className:Be,...De}=(0,s.useBlockProps)({className:"wp-block-jetpack-videopress"}),[Le,Ie]=(0,d.useState)(!Z),[Fe,Me]=(0,d.useState)(null);(0,d.useEffect)((()=>{if(!re)return;if(!(0,i.isBlobURL)(re))return;const e=(0,i.getBlobByURL)(re);e&&(0,_.A)(e)&&(me(),t({src:void 0}),Ie(!0),Me(e))}),[re]);const[He,ze]=(0,d.useState)({isReplacing:!1,prevAttrs:{}}),Ve=()=>{t(He.prevAttrs),ze({isReplacing:!1,prevAttrs:{}}),Ie(!1)};if(ne)return React.createElement("img",{style:{width:"100%",height:"auto",backgroundSize:"cover"},className:"wp-block-jetpack-videopress__example",src:re,alt:oe});if(Le){const r=r=>{if(Ie(!1),He.isReplacing){const t={...e,...r};return delete t.poster,ze({isReplacing:!1,prevAttrs:{}}),void fe(o,(0,c.createBlock)("videopress/video",t))}me(),t(r)};return React.createElement("div",n()({},De,{className:Be}),React.createElement(React.Fragment,null,React.createElement(E.A,{isConnected:ce,isModuleActive:H||F,isConnecting:ie,onConnect:()=>{if(se(!0),!ce)return le.recordEvent("jetpack_editor_connect_banner_click",{block:"VideoPress"}),window.location.href=L;le.recordEvent("jetpack_editor_activate_banner_click",{block:"VideoPress"}),window.location.href=I}}),React.createElement(O.A,{setAttributes:t,attributes:e,handleDoneUpload:r,fileToUpload:Fe,isReplacing:He?.isReplacing,onReplaceCancel:Ve,isActive:M})))}if((Re||!Ee.html)&&Te>0&&Te<z)return React.createElement("div",n()({},De,{className:Be}),React.createElement(V,{disableInstructions:!0},React.createElement("div",{className:"loading-wrapper"},React.createElement(l.Spinner,null),__("Generating preview…","jetpack-videopress-pkg"),React.createElement("strong",null," ",Te))));if(Te>=z&&!Ee.html)return React.createElement("div",n()({},De,{className:Be}),React.createElement(V,{errorMessage:__("Impossible to get a video preview after ten attempts.","jetpack-videopress-pkg"),onNoticeRemove:xe},React.createElement("div",{className:"videopress-uploader__error-actions"},React.createElement(l.Button,{variant:"primary",onClick:xe},__("Try again","jetpack-videopress-pkg")),React.createElement(l.Button,{variant:"secondary",onClick:()=>{t({src:void 0,id:void 0,guid:void 0})}},__("Cancel","jetpack-videopress-pkg")))));const Ge=__("Remove caption","jetpack-videopress-pkg"),qe=__("Add caption","jetpack-videopress-pkg");return React.createElement("div",n()({},De,{className:(0,h.A)(Be,{[`align${Q}`]:Q,"is-updating-preview":!Ce})}),React.createElement(s.BlockControls,{group:"block"},React.createElement(l.ToolbarButton,{onClick:()=>{de(!pe),pe&&oe&&t({caption:void 0})},icon:m.A,"aria-pressed":pe,label:pe?Ge:qe}),React.createElement(x.A,{attributes:e,setAttributes:t,clientId:o}),React.createElement(N.A,{attributes:e,setAttributes:t})),React.createElement(s.BlockControls,{group:"other"},React.createElement(U.A,{setAttributes:t,attributes:e,onUploadFileStart:r=>{ze({isReplacing:!0,prevAttrs:e}),Ie(!0),t({id:null,guid:null,cacheHtml:"",videoRatio:null}),Me(r)},onSelectVideoFromLibrary:e=>{const r=Array.isArray(e.videopress_guid)?e.videopress_guid[0]:e.videopress_guid;r?t({guid:r,id:e.id,src:e.url,title:e.title,description:e.description}):D("No media guid provided")},onSelectURL:e=>{const{guid:r,url:o}=(0,b.kK)(e);r?t({guid:r,src:o}):D("Invalid URL. No video GUID  provided")}})),React.createElement(s.InspectorControls,null,React.createElement(C.A,{filename:we,chapter:ue,isAutoGeneratedChapter:ye,updateError:ge,isRequestingVideoData:ve,videoBelongToSite:ke,attributes:e,setAttributes:t}),React.createElement(P.A,{attributes:e,setAttributes:t,isRequestingVideoData:ve}),React.createElement(j.Ay,{clientId:o,attributes:e,setAttributes:t,isGeneratingPoster:be,videoBelongToSite:ke}),React.createElement(T.A,{attributes:e,setAttributes:t,isRequestingVideoData:ve,privateEnabledForSite:_e,videoBelongToSite:ke})),React.createElement(s.InspectorControls,{group:"color"},React.createElement(R.A,{clientId:o,attributes:e,setAttributes:t,isRequestingVideoData:ve})),React.createElement(E.A,{isModuleActive:H||F,isConnected:ce,isConnecting:ie,onConnect:()=>{if(se(!0),!ce)return le.recordEvent("jetpack_editor_connect_banner_click",{block:"VideoPress"}),window.location.href=L;le.recordEvent("jetpack_editor_activate_banner_click",{block:"VideoPress"}),window.location.href=I}}),React.createElement(A.A,{showCaption:pe,html:Ae,isRequestingEmbedPreview:Re,attributes:e,setAttributes:t,isSelected:r,preview:Ee}))}},1200:(e,t,r)=>{"use strict";r.d(t,{DD:()=>d,h_:()=>f});var o=r(4997),n=r(8770),a=r(5015),i=r(3534),s=r(5980),c=r(3898),l=r(4356),u=r(4071);r(2130);const{UU:p,DD:d,h_:f,uK:m}=a;(0,o.registerBlockType)(p,{edit:c.A,title:d,save:()=>null,icon:i.K,attributes:m,example:{attributes:{src:(0,n.A)(u),isExample:!0}},transforms:l.A,deprecated:s.A})},4356:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var o=r(3162),n=r(4715),a=r(4997),i=r(7143),s=r(3022),c=r(9689),l=r(8228);const u={from:[{type:"files",isMatch:e=>!(!e||!e.length)&&e.some(l.A),priority:8,transform:e=>{const t=(0,a.getBlockTransforms)("from");return e.flatMap((e=>{if((0,l.A)(e))return(0,a.createBlock)("videopress/video",{src:(0,o.createBlobURL)(e)});const r=(0,a.findTransform)(t,(t=>"files"===t.type&&t.isMatch([e])));return r?r.transform([e]):[]}))}},{type:"block",blocks:["core/embed"],isMatch:e=>"videopress"===e.providerNameSlug&&(0,c.QW)(e?.url),transform:e=>{const{url:t,providerNameSlug:r}=e,o=(0,c.QW)(t);if(!("videopress"===r&&!!o))return(0,a.createBlock)("core/embed",e);return e.className=e.className?.replace(/(wp-embed-aspect-\d+-\d+)|(wp-has-aspect-ratio)/g,"").trim(),(0,a.createBlock)("videopress/video",{guid:o,src:t})}},{type:"raw",isMatch:e=>{const{textContent:t}=e;return!!t&&(0,c.eS)(t.trim())},transform:e=>{const{textContent:t}=e;if(!t)return!1;const r=t.trim(),o=(0,c.QW)(r);if(!o)return!1;const n=(0,c.qh)(r);return(0,a.createBlock)("videopress/video",{guid:o,...n})}}],to:[{type:"block",blocks:["core/embed"],isMatch:e=>e?.src||e?.guid,transform:e=>{const{guid:t,src:r,className:o}=e,{url:l}=(0,c.kK)(t);if(!(r||l))return(0,a.createBlock)("core/embed");const{updateBlockAttributes:u}=(0,i.dispatch)(n.store),{getBlockAttributes:p}=(0,i.select)(n.store),d=(0,a.createBlock)("core/embed",{allowResponsive:!0,providerNameSlug:"videopress",responsive:!0,type:"video",url:l}),{clientId:f}=d;return setTimeout((()=>{const{className:e}=p(f)||{},t=(0,s.A)(o,e);u(f,{className:t})}),100),d}}]}},2130:(e,t,r)=>{"use strict";var o=r(4997),n=r(2619);(0,n.addFilter)("blocks.registerBlockType","videopress/core-embed/handle-representation",((e,t)=>("core/embed"!==t||(e=>{"variations"in e&&"object"==typeof e.variations&&(0,o.getBlockType)("videopress/video")&&e.variations.some((e=>"videopress"===e.name&&(e.scope=[],!0)))})(e),e)))},4967:(e,t,r)=>{"use strict";r.d(t,{g:()=>i});var o=r(3582),n=r(7143);const a={html:null,width:null,height:null,thumbnail_height:null,thumbnail_width:null,title:null,version:"1.0",type:"video",provider_name:"VideoPress",provider_url:"https://videopress.com"},i=e=>(0,n.useSelect)((t=>e?{preview:t(o.store).getEmbedPreview(e)||a,isRequestingEmbedPreview:t(o.store).isRequestingEmbedPreview(e)||!1}:{preview:a,isRequestingEmbedPreview:!1}),[e])},5007:(e,t,r)=>{"use strict";r.d(t,{n:()=>C});var o=r(4715),n=r(9491),a=r(3582),i=r(7143),s=r(3656),c=r(6087),l=r(7723),u=r(6941),p=r.n(u),d=r(9689),f=r(8634),m=r(4523),h=r(9799),v=r(6302),g=r(2066),y=r(3531),b=r(7871),k=r(4745);const __=l.__,w=p()("videopress:video:use-sync-media"),_=["post_id","title","description","privacy_setting","rating","allow_download","display_embed","is_private","duration"],E={privacy_setting:"privacySetting",allow_download:"allowDownload",display_embed:"displayEmbed",is_private:"isPrivate",post_id:"id"},R=["title","privacy_setting","is_private","allow_download","display_embed"];function C(e,t){const{id:r,guid:l,isPrivate:u}=e,{videoData:p,isRequestingVideoData:C,videoBelongToSite:S}=(0,y.A)({id:r,guid:l,skipRatingControl:!0,maybeIsPrivate:u}),[P,A]=(0,c.useState)(!1),x=(0,i.useSelect)((e=>e(s.store).isSavingPost()),[]),j=(0,n.usePrevious)(x),T=(0,i.useDispatch)(a.store).invalidateResolution,{__unstableMarkNextChangeAsNotPersistent:U}=(0,i.useDispatch)(o.store),[N,O]=(0,c.useState)({}),[B,D]=(0,c.useState)(null),L=(0,c.useCallback)((e=>{O((t=>({...t,...e})))}),[]);(0,c.useEffect)((()=>{if(C)return;if(!p||0===Object.keys(p).filter((e=>_.includes(e))).length)return;const r={},o=_.reduce(((t,o)=>{if(void 0===p[o])return t;let n=p[o];"privacy_setting"===o&&(n=Number(n)),t[o]=n;const a=E[o]||(0,m.s)(o);return n!==e[a]&&(w("%o is out of sync. Updating %o attr from %o to %o ",o,a,e[a],n),r[a]=n),t}),{});if(L(o),w("Initial state: ",o),!Object.keys(o).length)return;const[n,a]=function(e,t){if(!e?.tracks)return[[],!1];const r=[];let o=!1;return Object.keys(e.tracks).forEach((n=>{for(const a in e.tracks[n]){const i=e.tracks[n][a];t.tracks.find((e=>e.kind===n&&e.srcLang===a&&e.src===i.src&&e.label===i.label))||(w("Track %o is out of sync. Set tracks attr",i.src),o=!0),r.push({src:i.src,kind:n,srcLang:a,label:i.label})}})),t.tracks.forEach((t=>{const r=e.tracks[t.kind]?.[t.srcLang];r&&r.src===t.src&&r.label===t.label||(w("Block track %o is out of sync and will be removed",t.src),o=!0)})),[r,o]}(p,e);a&&(r.tracks=n),Object.keys(r).length&&(w("Updating attributes: ",r),U(),t(r))}),[p,C]);const I=e.tracks.filter((e=>"chapters"===e.kind&&"en"===e.srcLang))[0]?.src;(0,c.useEffect)((()=>{(async()=>{if(I){const e="https://videos.files.wordpress.com/"+l+"/"+I,t=await(0,f.Cr)(e,{guid:l,isPrivate:u});w("Chapter %o detected. Overwritable: %o",I,t?"yes":"no"),A(t)}else w("Allow overwrite chapter: File does not exist"),A(!0)})()}),[I]);const F=(0,b.A)(r),M=!(!j||x),{isGeneratingPoster:H}=(0,k.u)(e);return(0,c.useEffect)((()=>{if(!M)return;if(w("%o Post has been just saved. Syncing...",e?.guid),!e?.id)return void w("%o No media ID found. Impossible to sync. Bail early",e?.guid);const r=_.reduce(((t,r)=>{const o=E[r]||r,n=N[r],a=e[o];return N[r]!==e[o]&&(w("Field to sync %o: %o => %o: %o",r,n,o,a),t[r]=e[o]),t}),{});if(!Object.keys(r).length)return w("No data to sync. Bail early");w("Syncing data: ",r),F(r).then((()=>{if(L(r),r.privacy_setting){const e=2!==r.privacy_setting?1===r.privacy_setting:p.private_enabled_for_site;w("Updating isPrivate attribute: %o",e),t({isPrivate:e})}const o=(0,h.Ay)(r?.description);if(P&&e?.guid&&r?.description?.length&&(0,g.A)(o)){w("Autogenerated chapter detected. Processing...");const o={label:__("English (auto-generated)","jetpack-videopress-pkg"),srcLang:"en",kind:"chapters",tmpFile:(0,v.Ay)(r.description)};w("Autogenerated track: %o",o),(0,f.n)(o,e.guid).then((r=>{const n=e.tracks.findIndex((e=>"chapters"===e.kind&&"en"===e.srcLang)),a={...o,src:r},i=[...e.tracks];n>-1?(w("Updating %o auto-generated track",a.src),i[n]=a):(w("Adding auto-generated %o track",a.src),i.push(a)),t({tracks:i});const s=(0,d.ep)(e.guid,e);T("getEmbedPreview",[s])}))}else{const t=Object.keys(r).filter((e=>R.includes(e)));if(t?.length){w("Invalidate resolution because of %o",t.join(", "));const r=(0,d.ep)(e.guid,e);T("getEmbedPreview",[r])}}})).catch((t=>{w("%o Error while syncing data: %o",e?.guid,t),D(t)}))}),[M,F,L,e,N,T,_]),{forceInitialState:L,videoData:p,isRequestingVideoData:C,videoBelongToSite:S,error:B,isOverwriteChapterAllowed:P,isGeneratingPoster:H}}},7871:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(1455),n=r.n(o);function a(e){return t=>new Promise(((r,o)=>{n()({path:"/wpcom/v2/videopress/meta",method:"POST",data:{id:e,...t}}).then((e=>{if(200!==e?.data)return o(e);r(e)})).catch(o)}))}},3531:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var o=r(1455),n=r.n(o),a=r(6087),i=r(8537),s=r(6941),c=r.n(s),l=r(9448),u=r(1370);const p=a.Platform.isNative,d=c()("videopress:video:use-video-data"),f=(0,l.xI)();function m({id:e,guid:t,skipRatingControl:r=!1,maybeIsPrivate:o=!1}){const[s,c]=(0,a.useState)({}),[l,m]=(0,a.useState)(!1),[h,v]=(0,a.useState)(!0);return(0,a.useEffect)((()=>{f||p?t&&(m(!0),async function(e=null){try{let a;for(let n=0;n<5;n++){if(a=await(0,u.X)({guid:t,isPrivate:o,token:e,skipRatingControl:r}),a.duration){d(`video duration available: ${a.duration}, retried ${n} times`,a);break}d(`video duration not yet available, retrying (${n+1})`,a),await new Promise((e=>setTimeout(e,1500)))}m(!1);const s=a.original?.split("/")?.at(-1);c({duration:a.duration,allow_download:a.allow_download,post_id:a.post_id,guid:a.guid,title:(0,i.decodeEntities)(a.title),description:(0,i.decodeEntities)(a.description),display_embed:a.display_embed,privacy_setting:a.privacy_setting,rating:a.rating,filename:s,tracks:a.tracks,is_private:a.is_private,private_enabled_for_site:a.private_enabled_for_site});try{const e=await n()({path:`/wpcom/v2/videopress/${t}/check-ownership/${a.post_id}`,method:"GET"});v("boolean"==typeof e?.["video-belong-to-site"]?e["video-belong-to-site"]:!!e?.body?.["video-belong-to-site"])}catch(e){d("Error checking if video belongs to site",e)}}catch(e){throw m(!1),new Error(e?.message??e)}}()):d("User is not connected")}),[e,t]),{videoData:s,isRequestingVideoData:l,videoBelongToSite:h}}},4954:(e,t,r)=>{"use strict";r.d(t,{A:()=>l,Q:()=>c});var o=r(9491),n=r(6941),a=r.n(n),i=r(1609);const s=a()("videopress:use-video-player"),c=e=>{const t=e?.current?.querySelector("iframe.components-sandbox");return t?.contentWindow},l=(e,t,{initialTimePosition:r,wrapperElement:n,previewOnHover:a})=>{const[l,u]=(0,i.useState)(!1),p=(0,i.useRef)("not-rendered");function d(e){const{data:t={},source:o}=e,{event:n}=e?.data||{};if("videopress_loading_state"===n&&"loaded"===t.state&&(s("state: loaded"),p.current="loaded"),"videopress_playing"===n&&"loaded"===p.current&&(p.current="first-play",s("state: first-play detected"),a&&(s("pause video"),o.postMessage({event:"videopress_action_pause"},{targetOrigin:"*"}),void 0!==r&&(s("set position at time %o ",r),o.postMessage({event:"videopress_action_set_currenttime",currentTime:r/1e3},{targetOrigin:"*"}))),u(!0),p.current="ready"),"videopress_timeupdate"===n&&a){const e=t.currentTimeMs,r=a.atTime,n=a.atTime+a.duration;(e<r||e>n)&&o.postMessage({event:"videopress_action_set_currenttime",currentTime:r/1e3},{targetOrigin:"*"})}}const f=!!a,m=(0,o.usePrevious)(f),h=f&&!m,v=c(e);(0,i.useEffect)((()=>{const r=c(e);if(r&&!t)return s("player is ready to listen events"),r.addEventListener("message",d),()=>{r.removeEventListener("message",d)}}),[e,t,h,a]);const g=(0,i.useCallback)((()=>{v&&l&&v.postMessage({event:"videopress_action_play"},"*")}),[e,l,v]),y=(0,i.useCallback)((()=>{v&&l&&v.postMessage({event:"videopress_action_pause"},"*")}),[e,l,v]);return(0,i.useEffect)((()=>{if(n&&f)return n.addEventListener("mouseenter",g),n.addEventListener("mouseleave",y),()=>{n.removeEventListener("mouseenter",g),n.removeEventListener("mouseleave",y)}}),[f,n,l]),(0,i.useEffect)((()=>{l&&a&&v&&v.postMessage({event:"videopress_action_set_currenttime",currentTime:a.atTime/1e3},{targetOrigin:"*"})}),[a?.atTime,l,v]),(0,i.useEffect)((()=>{l&&a&&v&&v.postMessage({event:"videopress_action_set_currenttime",currentTime:(a.atTime+a.duration)/1e3},{targetOrigin:"*"})}),[a?.duration,l,v]),{playerIsReady:l,play:g,pause:y}}},4745:(e,t,r)=>{"use strict";r.d(t,{u:()=>f});var o=r(9491),n=r(3582),a=r(7143),i=r(3656),s=r(6087),c=r(6941),l=r.n(c),u=r(1649),p=r(9689);const d=l()("videopress:video:use-sync-media");function f(e){const t=(0,a.useSelect)((e=>e(i.store).isSavingPost()),[]),r=!(!(0,o.usePrevious)(t)||t),c=(0,a.useDispatch)(n.store).invalidateResolution,l=(0,s.useRef)(),[f,m]=(0,s.useState)(!1);return(0,s.useEffect)((()=>{r&&l.current?"video-frame"===e?.posterData?.type&&e?.posterData?.atTime!==l.current?.posterData?.atTime&&(d("(*) %o Poster image needs to be generated %s => %s",e?.guid,l.current?.posterData?.atTime,e?.posterData?.atTime),l.current=e,(0,u.TE)(e?.guid,e.posterData.atTime),d("(*) %o Requesting poster image generation",e?.guid),m(!0)):l.current||(l.current=e)}),[r]),(0,s.useEffect)((()=>{f&&(async()=>{if(await(0,u.qb)(e?.guid)){d("(*) %o Poster image has been generated",e?.guid),m(!1);const t=(0,p.ep)(e.guid,e);c("getEmbedPreview",[t])}})()}),[f]),{isGeneratingPoster:f}}},8770:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(3832);const{imagesURLBase:n=""}=window?.videoPressEditorState||{};function a(e){const t=(0,o.getFilename)(e);return n?`${n}${t}`:e}},8228:(e,t,r)=>{"use strict";function o(e){return!!e?.type&&e.type.startsWith("video/")}r.d(t,{A:()=>o})},6151:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var o=r(1112),n=r(6087),a=r(7723),i=r(1609),s=r(6592);const __=a.__,c=()=>{const[e,t]=(0,i.useState)(!1);return React.createElement(React.Fragment,null,(0,n.createInterpolateElement)(__("Did you know you can now add Chapters to your videos? <link>Learn how</link>","jetpack-videopress-pkg"),{link:React.createElement(o.A,{variant:"link",size:"small",onClick:()=>t(!0)})}),React.createElement(s.A,{onClose:()=>t(!1),isOpen:e}))}},7975:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var o=r(6427);const n=React.createElement(o.SVG,{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},React.createElement(o.Path,{d:"M4.75725 2.075C4.60279 1.97745 4.41041 1.97489 4.25365 2.06832C4.09689 2.16174 4 2.3367 4 2.52632V21.4737C4 21.6633 4.09689 21.8383 4.25365 21.9317C4.41041 22.0251 4.60279 22.0226 4.75725 21.925L19.7573 12.4513C19.9079 12.3562 20 12.1849 20 12C20 11.8151 19.9079 11.6438 19.7573 11.5487L4.75725 2.075Z",fill:"white"}))},2843:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var o=r(1112),n=r(6427),a=r(6087),i=r(7723),s=r(1609),c=r(6592);const __=i.__,l=({className:e})=>{const[t,r]=(0,s.useState)(!1);return React.createElement(React.Fragment,null,React.createElement(n.Notice,{status:"warning",className:e,isDismissible:!1},(0,a.createInterpolateElement)(__("It seems there are some chapters, but they are incomplete. Check out the <link>format</link> and try again.","jetpack-videopress-pkg"),{link:React.createElement(o.A,{variant:"link",size:"small",onClick:()=>r(!0)})})),React.createElement(c.A,{onClose:()=>r(!1),isOpen:t}))}},3685:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>g});var o=r(6072),n=r.n(o),a=r(6427),i=r(9491),s=r(6087),c=r(3022),l=r(3239),u=r(941);const p=a.useBaseControlProps||function(e){const{help:t,id:r,...o}=e,n=(0,i.useInstanceId)(a.BaseControl,"wp-components-base-control",r),s="string"==typeof t?"aria-describedby":"aria-details";return{baseControlProps:{id:n,help:t,...o},controlProps:{id:n,...t?{[s]:`${n}__help`}:{}}}},d=e=>{if(a.__experimentalNumberControl)return React.createElement(a.__experimentalNumberControl,e);const t={...e};return["spinControls","isPressEnterToChange","isDragEnabled","isShiftStepEnabled","__unstableStateReducer"].forEach((e=>delete t[e])),React.createElement(a.TextControl,t)},f=({char:e=":"})=>React.createElement("span",{className:u.A["timestamp-control-divider"]},e),m=e=>(t,r)=>{const o={...t};return"COMMIT"!==r.type&&"PRESS_UP"!==r.type&&"PRESS_DOWN"!==r.type&&"CHANGE"!==r.type||void 0!==o.value&&(o.value=o.value.toString().padStart(e,"0")),o};function h(e,t,r){e>r&&(e=r);const o=Number.isNaN(e),n=o||void 0===t?0:Math.floor(e%1e3/Number("1e"+(3-t)));return{hh:o?0:Math.floor(e/36e5%24),mm:o?0:Math.floor(e/6e4%60),ss:o?0:Math.floor(e/1e3%60),decimal:n}}const v=({onChange:e,disabled:t,value:r,max:o,autoHideTimeInput:n=!0,decimalPlaces:a})=>{const i={value:h(r,a,o)},s=o>36e5,l=o>6e4,p=t=>n=>{if("string"!=typeof n||isNaN(parseInt(n,10))||(n=parseInt(n,10)),"hh"===t&&n>99||("mm"===t||"ss"===t)&&n>59||"decimal"===t&&n>Number(`1e${a}`)-1)return;if("string"==typeof n)return;i.value={...h(r,a,o),[t]:n};const s=i.value.decimal?i.value.decimal*Number("1e"+(3-a)):0;e?.(1e3*(3600*i.value.hh+60*i.value.mm+i.value.ss)+s)};return React.createElement("div",{className:(0,c.A)(u.A["timestamp-input-wrapper"],{[u.A["is-disabled"]]:t})},(s||!n)&&React.createElement(React.Fragment,null,React.createElement(d,{className:u.A["timestamp-control-input"],disabled:t,min:0,max:99,step:1,hideLabelFromVision:!0,spinControls:"none",placeholder:"00",isPressEnterToChange:!0,isDragEnabled:!1,isShiftStepEnabled:!1,__unstableStateReducer:m(2),value:String(i.value.hh).padStart(2,"0"),onChange:p("hh"),__next40pxDefaultSize:!0}),React.createElement(f,null)),(l||!n)&&React.createElement(React.Fragment,null,React.createElement(d,{className:u.A["timestamp-control-input"],disabled:t,min:0,max:59,step:1,hideLabelFromVision:!0,spinControls:"none",placeholder:"00",isPressEnterToChange:!0,isDragEnabled:!1,isShiftStepEnabled:!1,__unstableStateReducer:m(2),value:String(i.value.mm).padStart(2,"0"),onChange:p("mm"),__next40pxDefaultSize:!0}),React.createElement(f,null)),React.createElement(d,{className:u.A["timestamp-control-input"],disabled:t,min:0,max:59,step:1,hideLabelFromVision:!0,spinControls:"none",placeholder:"00",isPressEnterToChange:!0,isDragEnabled:!1,isShiftStepEnabled:!1,__unstableStateReducer:m(2),value:String(i.value.ss).padStart(2,"0"),onChange:p("ss"),__next40pxDefaultSize:!0}),a&&React.createElement(React.Fragment,null,React.createElement(f,{char:"."}),React.createElement(d,{className:u.A["timestamp-control-input"],style:{"--input-width":12*a+"px"},disabled:t,min:0,max:Number("9".repeat(a)),step:1,hideLabelFromVision:!0,spinControls:"none",placeholder:"0".repeat(a),isPressEnterToChange:!0,isDragEnabled:!1,isShiftStepEnabled:!1,__unstableStateReducer:m(a),value:String(i.value.decimal).padStart(a,"0"),onChange:p("decimal"),__next40pxDefaultSize:!0})))},g=e=>{const{disabled:t=!1,min:r=0,max:o=Number.MAX_SAFE_INTEGER,value:i,onChange:c,onDebounceChange:f,wait:m=1e3,fineAdjustment:h=50,autoHideTimeInput:g=!0,decimalPlaces:y,marksEvery:b,renderTooltip:k}=e,w=(0,s.useRef)(),[_,E]=(0,s.useState)(i);(0,s.useEffect)((()=>{E(i)}),[i]);const{baseControlProps:R}=p?.(e)||{},C=(0,s.useCallback)((e=>{clearTimeout(w?.current),e>o&&(e=o),e<r&&(e=r),E(e),c?.(e),w.current=setTimeout(f?.bind(null,e),m)}),[f,c,o,r,m]),S=[];if(b)for(let e=r;e<=o;e+=b)S.push({value:e,label:null});const P="function"==typeof k?k:e=>(0,l.f)(e);return React.createElement(a.BaseControl,n()({},R,{__nextHasNoMarginBottom:!0}),React.createElement("div",{className:u.A["timestamp-control__controls-wrapper"]},d&&React.createElement(v,{disabled:t,max:o,value:_,onChange:C,autoHideTimeInput:g,decimalPlaces:y}),React.createElement(a.RangeControl,n()({disabled:t,className:u.A["timestamp-range-control"],min:r,step:h,initialPosition:_,value:_,max:o,withInputField:!1,onChange:C,marks:b?S:void 0,renderTooltipContent:P},!1===k?{showTooltip:!1}:{},{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}))))}},6720:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var o=r(6427),n=r(6087),a=r(1113),i=r(3022),s=r(7975),c=r(8766);const l=({src:e,setMaxDuration:t=null,currentTime:r})=>{const a=(0,n.useRef)(null),[i,s]=(0,n.useState)(!0);(0,n.useEffect)((()=>{a.current.src=e}),[e]),(0,n.useEffect)((()=>{a.current&&Number.isFinite(r)&&(a.current.currentTime=r)}),[r]);return React.createElement("div",{className:c.A["video-player-wrapper"]},i&&React.createElement("div",{className:c.A["video-player-spinner-wrapper"]},React.createElement(o.Spinner,{className:c.A.spinner})),React.createElement("video",{onLoadedData:()=>s(!1),ref:a,muted:!0,className:c.A.video,onDurationChange:e=>{const o=e.target.duration;if(t?.(o),a.current){const e=Number.isFinite(r)?r:o/2;a.current.currentTime=e}}}))},u=({src:e="",onVideoFrameSelected:t,className:r="",initialCurrentTime:u=null})=>{const[p,d]=(0,n.useState)(0),[f,m]=(0,n.useState)(Number.isFinite(u)?u:null);return React.createElement("div",{className:(0,i.A)(c.A.container,r)},React.createElement(a.A,{className:c.A["play-icon"],icon:s.A}),React.createElement(l,{src:e,setMaxDuration:d,currentTime:f}),React.createElement(o.RangeControl,{className:c.A.range,min:0,step:.1,initialPosition:f,max:p,showTooltip:!1,withInputField:!1,onChange:e=>{m(e),t?.(1e3*e)},__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}))}},2439:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var o=r(9491),n=r(1609),a=r(9799),i=r(2066);const s=e=>{const[t,r]=(0,n.useState)(!1),s=(0,n.useCallback)((()=>{const t=(0,a.Ay)(e);0===t.length?r(!1):r(!(0,i.A)(t))}),[e]),c=(0,o.useDebounce)(s,3e3);return(0,n.useEffect)((()=>{c()}),[e]),(0,n.useEffect)(s,[]),{hasIncompleteChapters:t}}},2185:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(1455),n=r.n(o);const a=e=>{const t={allowDownload:"allow_download",displayEmbed:"display_embed"};return r=>new Promise(((o,a)=>{const i=(s=Object.assign({id:e},r),Object.keys(s).reduce(((e,r)=>(e[t[r]||r]=s[r],e)),{}));var s;n()({path:"/wpcom/v2/videopress/meta",method:"POST",data:i}).then((e=>{"success"!==e?.code&&a()})).catch((e=>a(e))).finally((()=>{o()}))}))}},295:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(1455),n=r.n(o);const a=e=>()=>new Promise((function(t,r){n()({path:`/wpcom/v2/videopress/${e}/poster`,method:"GET"}).then((function(e){t(e)})).catch((function(e){r(e)}))}))},9697:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(1455),n=r.n(o);const a=e=>function(t){return new Promise((function(r,o){n()({path:`/wpcom/v2/videopress/${e}/poster`,method:"POST",data:t}).then((function(e){r(e)})).catch((function(e){o(e)}))}))}},7430:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var o=r(6087),n=r(6941),a=r.n(n),i=r(8025),s=r(5778);const c=a()("videopress:use-resumable-uploader"),l=({onProgress:e,onSuccess:t,onError:r})=>{const[n,a]=(0,o.useState)({bytesSent:0,bytesTotal:0,percent:0,status:"idle"}),[l,u]=(0,o.useState)(),[p,d]=(0,o.useState)(null),[f,m]=(0,o.useState)();async function h(o){const c=await(0,i.A)("upload-jwt");if(!c.token)return r("No token provided");"idle"===n.status&&a((e=>({...e,status:"uploading"})));let l=!1;const p=(0,s.A)({file:o,tokenData:c,onProgress:(t,r)=>{if(l)return;const o=Math.round(t/r*100);a({bytesSent:t,bytesTotal:r,percent:o,status:"uploading"}),e(t,r)},onSuccess:e=>{l=!0,a((e=>({...e,status:"done"}))),u(e),t(e)},onError:e=>{a((e=>({...e,status:"error"}))),d(e),r(e)}});m({start:()=>{a((e=>({...e,status:"uploading"}))),p.start()},abort:()=>{a((e=>({...e,status:"aborted"}))),p.abort()}})}return{onUploadHandler:function(e){const t=e.target.files[0];t?h(t):c("No file selected. Bail early")},uploadHandler:h,resumeHandler:f,uploadingData:n,media:l,error:p}}},560:(e,t,r)=>{"use strict";r.d(t,{NP:()=>i});var o=r(1455),n=r.n(o),a=(r(6087),r(7723));r(781);const __=a.__,i=e=>{const t=`videopress/v1/upload/${e}`;return new Promise(((r,o)=>{n()({path:t,method:"POST"}).then((t=>{"uploading"===t.status||"new"===t.status||"resume"===t.status?i(e).then(r).catch(o):"complete"===t.status?r({guid:t.uploaded_details.guid,id:t.uploaded_details.media_id,src:t.uploaded_details.upload_src}):"error"===t.status?o({data:{message:t.error}}):o({data:{message:__("Unexpected error uploading video.","jetpack-videopress-pkg")}})})).catch((e=>{o({data:{message:e?.message}})}))}))}},9448:(e,t,r)=>{"use strict";r.d(t,{Lv:()=>l,b5:()=>u,ie:()=>p,xI:()=>c});var o=r(5985),n=r(6941),a=r.n(n);const i=window?.JP_CONNECTION_INITIAL_STATE,s=a()("videopress:connection");function c(){return(0,o.Sy)()?(s("Simple site connected ✅"),!0):(0,o.d9)()?(s("Atomic site connected ✅"),!0):i?.connectionStatus?.isUserConnected?(s("Jetpack user is connected ✅"),!0):(s("User is not connected ❌"),!1)}function l(){return"1"===window?.videoPressEditorState?.isVideoPressModuleActive}function u(){return!!c()&&(l()||p())}function p(){return"1"===window?.videoPressEditorState?.isStandaloneActive}},1370:(e,t,r)=>{"use strict";r.d(t,{X:()=>p});var o=r(1455),n=r.n(o),a=r(6087),i=r(6941),s=r.n(i),c=r(8025);const l=a.Platform.isNative,u=s()("videopress:lib:fetch-video-item");async function p({guid:e,isPrivate:t,token:r=null,skipRatingControl:o=!1,retries:a=0}){try{const a=o?{}:{birth_day:"1",birth_month:"1",birth_year:"2000"};let i;t&&!r&&(i=await(0,c.A)("playback",{guid:e})),(r||i?.token)&&(a.metadata_token=r||i.token);const s=Object.keys(a).length?`?${new URLSearchParams(a).toString()}`:"",u=l?{path:`/rest/v1.1/videos/${e}${s}`}:{url:`https://public-api.wordpress.com/rest/v1.1/videos/${e}${s}`};return await n()({...u,credentials:"omit",global:!0})}catch(t){u("updating retry from",a,"to",a+1);const r=a+1;if(r>2)throw u("Too many attempts to get video. Aborting."),new Error(t?.message??t);if("auth"===t?.error)return u("Authentication error. Reattempt %o",r+"/3"),p({guid:e,isPrivate:!0,token:null,skipRatingControl:o,retries:r});if("Please supply the birthdate parameters."===t?.message)return u("Rating error. Reattempt %o",r+"/3"),p({guid:e,isPrivate:!0,token:null,skipRatingControl:!1,retries:r});throw new Error(t?.message??t)}}},8025:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var o=r(6941),n=r.n(o),a=r(5468);const i=n()("videopress:get-media-token");const s=async function(e,t={}){const{id:r=0,guid:o=0,flushToken:n}=t,s=`vpc-${e}-${r}-${o}`,c=window?.videopressAjax?.context||"main";let l;const u=localStorage.getItem(s);if(n)i("(%s) Flushing %o token",c,s),localStorage.removeItem(s);else try{if(u){if(l=await JSON.parse(u),l&&l.expire>Date.now())return i("(%s) Providing %o token from the store",c,s),l.data;i("(%s) Token %o expired. Clean.",c,s),localStorage.removeItem(s)}}catch{i("Invalid token in the localStore")}const p=await function(e,t={}){const{id:r=0,guid:o,subscriptionPlanId:n=0,adminAjaxAPI:i,filename:s}=t;return new Promise((function(t,c){const l=i||window.videopressAjax?.ajaxUrl||window?.ajaxurl||"/wp-admin/admin-ajax.php";if(!a.p.includes(e))return c("Invalid scope");const u={action:"videopress-get-playback-jwt"};switch(e){case"upload":u.action="videopress-get-upload-token",s&&(u.filename=s);break;case"upload-jwt":u.action="videopress-get-upload-jwt";break;case"playback":u.action="videopress-get-playback-jwt",u.guid=o,u.post_id=String(r),u.subscription_plan_id=n}fetch(l,{method:"POST",credentials:"same-origin",body:new URLSearchParams(u)}).then((e=>{if(!e.ok)throw new Error("Network response was not ok");return e.json()})).then((r=>{if(!r.success)throw new Error("Token is not achievable");switch(e){case"upload":case"upload-jwt":t({token:r.data.upload_token,blogId:r.data.upload_blog_id,url:r.data.upload_action_url});break;case"playback":t({token:r.data.jwt})}})).catch((()=>{console.warn("Token is not achievable"),t({token:null})}))}))}(e,t);return"playback"===e&&p?.token&&(i("(%s) Storing %o token",c,s),localStorage.setItem(s,JSON.stringify({data:p,expire:Date.now()+864e5}))),i("(%s) Providing %o token from request/response",c,s),p}},5468:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});const o=["upload","playback","upload-jwt"]},1649:(e,t,r)=>{"use strict";r.d(t,{TE:()=>i,qb:()=>c});var o=r(5985),n=r(1455),a=r.n(n);const i=function(e,t){const r={at_time:t,is_millisec:!0};return(0,o.Sy)()?a()({path:`/videos/${e}/poster`,apiNamespace:"rest/v1.1",method:"POST",global:!0,data:r}):a()({path:`/wpcom/v2/videopress/${e}/poster`,method:"POST",data:r})},s=async function(e){const t=await function(e){return a()({path:`/wpcom/v2/videopress/${e}/poster`,method:"GET"})}(e);return!t.data?.generating};async function c(e,{wait:t=3e3,attemps:r=10,initialWait:o=!0}={}){for(o&&await new Promise((e=>setTimeout(e,t)));!await s(e);){if(0==r--)throw new Error("Poster generation timed out");await new Promise((e=>setTimeout(e,t)))}return!0}},5778:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var o=r(6941),n=r.n(o),a=r(781),i=r(8025);const s=n()("videopress:resumable-file-uploader"),c={},l=({file:e,tokenData:t,onProgress:r,onSuccess:o,onError:n})=>{const l=new a._O(e,{onError:n,onProgress:r,endpoint:t.url,removeFingerprintOnSuccess:!0,overridePatchMethod:!1,chunkSize:1e7,metadata:{filename:e.name,filetype:e.type},retryDelays:[0,1e3,3e3,5e3,1e4],onShouldRetry:function(e){return 400!==(e.originalResponse?e.originalResponse.getStatus():0)||(s("cleanup retry due to 400 error"),localStorage.removeItem(l._urlStorageKey),!1)},onBeforeRequest:async function(e){const r=e._method;if(["HEAD","OPTIONS"].indexOf(r)>=0&&(e._method="GET",e.setHeader("X-HTTP-Method-Override",r)),["DELETE","PUT","PATCH"].indexOf(r)>=0&&(e._method="POST",e.setHeader("X-HTTP-Method-Override",r)),e._xhr.open(e._method,e._url,!0),Object.keys(e._headers).forEach((function(t){e.setHeader(t,e._headers[t])})),"POST"===r){if(!!!t.token)throw"should never happen";e.setHeader("x-videopress-upload-token",t.token)}if(["OPTIONS","GET","HEAD","DELETE","PUT","PATCH"].indexOf(r)>=0){const t=(o=e._url,new URL(o).pathname.split("/").pop());if(c[t])e.setHeader("x-videopress-upload-token",c[t]);else if("HEAD"===r){const r=await(0,i.A)("upload-jwt");r?.token&&(c[t]=r.token,e.setHeader("x-videopress-upload-token",r.token))}}var o},onAfterResponse:async function(t,r){if(r.getStatus()>=400)return void s("upload error");const n=r.getHeader("x-videopress-upload-guid"),a=r.getHeader("x-videopress-upload-media-id"),i=r.getHeader("x-videopress-upload-src-url");if(n&&a&&i)return void(o&&o({id:Number(a),guid:n,src:i},e));const l={"x-videopress-upload-key-token":"token","x-videopress-upload-key":"key"},u={};Object.keys(l).forEach((function(e){const t=r.getHeader(e);t&&(u[l[e]]=t)})),u.key&&u.token&&(c[u.key]=u.token)}});return l.findPreviousUploads().then((function(e){e.length&&l.resumeFromPreviousUpload(e[0]),l.start()})),l}},9689:(e,t,r)=>{"use strict";r.d(t,{QW:()=>l,T3:()=>d,eS:()=>m,ep:()=>c,i2:()=>p,kK:()=>u,qh:()=>h,up:()=>f});var o=r(1455),n=r.n(o),a=r(3832);const i=["autoPlay","cover","controls","hd","loop","muted","persistVolume","playsinline","posterUrl","preloadContent","sbc","sbpc","sblc","resizeToParent","useAverageColor"],s={autoPlay:"autoplay",cover:"cover",controls:"controls",hd:"hd",loop:"loop",muted:"muted",persistVolume:"persistVolume",playsinline:"playsinline",posterUrl:"poster",preloadContent:"preload",sbc:"seekbarColor",sbpc:"seekbarPlayedColor",sblc:"seekbarLoadingColor",resizeToParent:"resizeToParent",useAverageColor:"useAverageColor"},c=(e,{autoplay:t,controls:r,loop:o,muted:n,playsinline:i,poster:s,preload:c,seekbarColor:l,seekbarPlayedColor:u,seekbarLoadingColor:p,useAverageColor:d})=>{if(!e)return null;const f={resizeToParent:!0,cover:!0,...t&&{autoPlay:!0},...!r&&{controls:!1},...o&&{loop:!0},...n&&{muted:!0,persistVolume:!1},...i&&{playsinline:!0},...s&&{posterUrl:s},...""!==c&&{preloadContent:c},...""!==l&&{sbc:l},...""!==u&&{sbpc:u},...""!==p&&{sblc:p},...d&&{useAverageColor:!0}};return(0,a.addQueryArgs)(`https://videopress.com/v/${e}`,f)},l=e=>{if(!e||"string"!=typeof e)return null;const t=e.match(/^https?:\/\/(?<host>video(?:\.word|s\.files\.word)?press\.com)(?:\/v|\/embed)?\/(?<guid>[a-zA-Z\d]{8})/);return t?.groups?.guid?t.groups.guid:null};function u(e,t){const r=function(e){const t=e.match(/^[a-zA-Z\d]{8}$/);return!!t&&t[0]}(e);if(r)return t?{url:c(e,t),guid:e}:{url:`https://videopress.com/v/${e}`,guid:e};const o=l(e);return o?{url:e,guid:o}:{}}async function p(e,t={}){try{const r=(await n()({path:`/wp/v2/media?mime_type=video&search=${encodeURIComponent(e)}`})).find((e=>e.jetpack_videopress_guid));return r?.jetpack_videopress_guid?{url:c(r.jetpack_videopress_guid,t),guid:r.jetpack_videopress_guid}:null}catch{return null}}const d=e=>e.replace(/\.[^/.]+$/,"");function f(e){try{const t=new URL(e).pathname.split("/");return t[t.length-1]||""}catch{return""}}function m(e){return/^https?:\/\/(?:(?:v(?:ideo)?\.wordpress\.com|videopress\.com)\/(?:v|embed)|v\.wordpress\.com)\/([a-z\d]{8})(\/|\b)/i.test(e)}function h(e){let t;try{t=new URLSearchParams(new URL(e).search)}catch{return{}}return i.reduce(((e,r)=>{const o=t.get(r);if(null!==o){e[s[r]]=["autoPlay","cover","controls","hd","loop","muted","persistVolume","playsinline","resizeToParent","useAverageColor"].includes(r)?(e=>"1"===e||"true"===e||"0"!==e&&"false"!==e&&null)(o):o}return e}),{})}},8634:(e,t,r)=>{"use strict";r.d(t,{Cr:()=>p,Dr:()=>f,n:()=>d});var o=r(1455),n=r.n(o),a=r(6941),i=r.n(a),s=r(8025);const{siteType:c=""}=window?.videoPressEditorState||{},l="simple"!==c,u=i()("videopress:tracks:lib:video-tracks");async function p(e,t){if(!e)return!1;let r,o="";t.isPrivate&&(r=await(0,s.A)("playback",{guid:t.guid}),o="?"+new URLSearchParams({metadata_token:r?.token}).toString());let n=await fetch(e+o);if(!n.ok&&403===n.status&&t.guid&&(r=await(0,s.A)("playback",{guid:t.guid}),o="?"+new URLSearchParams({metadata_token:r?.token}).toString(),n=await fetch(e+o)),!n.ok)return!1;const a=await n.text();return/videopress-chapters-auto-generated/.test(a)}const d=(e,t)=>{const{kind:r,srcLang:o,label:a,tmpFile:i}=e;return l?function(e,t){return u("using jetpack api fetch"),new Promise((function(r,o){const{kind:n,srcLang:a,label:i,tmpFile:c}=e;(0,s.A)("upload",{filename:c.name}).then((({token:e,blogId:s})=>{const l=new FormData;l.append("kind",n),l.append("srclang",a),l.append("label",i),l.append("vtt",c),fetch(`https://public-api.wordpress.com/rest/v1.1/videos/${t}/tracks`,{headers:{Authorization:`X_UPLOAD_TOKEN token="${e}" blog_id="${s}"`},method:"POST",body:l}).then((e=>{try{const t=e.json();return u("data",e),u("json",t),r(t)}catch(e){return u("error",e),o(e)}})).catch(o)}))}))}({kind:r,srcLang:o,label:a,tmpFile:i},t):(u("using wpcom api fetch"),new Promise((function(e,s){return n()({method:"POST",path:`/videos/${t}/tracks`,apiNamespace:"rest/v1.1",global:!0,parse:!1,formData:[["kind",r],["srclang",o],["label",a],["vtt",i]]}).then((t=>{try{const r=t.json();return u("data",t),u("json",r),e(r)}catch(e){return u("error",e),s(e)}})).catch(s)})))},f=(e,t)=>{const{kind:r,srcLang:o}=e;if(l)return function({kind:e,srcLang:t},r){return new Promise((function(o,n){(0,s.A)("upload").then((({token:a,blogId:i})=>{const s=new FormData;s.append("kind",e),s.append("srclang",t),fetch(`https://public-api.wordpress.com/rest/v1.1/videos/${r}/tracks/delete`,{headers:{Authorization:`X_UPLOAD_TOKEN token="${a}" blog_id="${i}"`},method:"POST",body:s}).then((e=>{try{return o(e.json())}catch(e){return n(e)}})).catch(n)}))}))}({kind:r,srcLang:o},t);const a={method:"POST",path:`/videos/${t}/tracks/delete`,apiNamespace:"rest/v1.1",global:!0,parse:!1,formData:[["kind",r],["srclang",o]]};return n()(a)}},8962:(e,t,r)=>{"use strict";r.d(t,{CR:()=>a,az:()=>o,iL:()=>l,ko:()=>s,o0:()=>n,z7:()=>c,zG:()=>i});const o="public",n="private",a="site-default",i=[o,n,a],s="G",c="PG-13",l="R-17"},4523:(e,t,r)=>{"use strict";function o(e){return e.replace(/([-_][a-z])/gi,(e=>e.toUpperCase().replace("_","")))}r.d(t,{s:()=>o})},3239:(e,t,r)=>{"use strict";function o(e){const t=Math.floor(e/36e5),r=Math.floor(e/6e4)%60,o=Math.floor(e/1e3)%60,n=Math.floor(e/10)%100;return[t>0?t.toString().padStart(2,"0")+":":"",t>0||r>0?r.toString().padStart(2,"0")+":":"",o.toString().padStart(2,"0"),"."+n.toString().padStart(2,"0")].join("")}r.d(t,{f:()=>o})},9799:(e,t,r)=>{"use strict";function o(e){if(!e)return[];return e.split("\n").map((e=>function(e){const t=/(?<timeBlock>\(?(?<time>\d{1,2}:\d{2}:\d{2}|\d{1,2}:\d{2})\)?)/.exec(e);if(null==t||null==t.groups)return null;const{groups:{timeBlock:r,time:o}}=t,n=e.indexOf(r),a=(n<(e.length-r.length)/2?e.substring(n+r.length,e.length):e.substring(0,n)).trim().replace(/(\s-$)|(^-\s)/,""),i=o.split(":");return 1===i[0].length&&(i[0]=`0${i[0]}`),2===i.length&&i.unshift("00"),{startAt:i.join(":"),title:a}}(e))).filter((e=>null!==e)).sort(((e,t)=>e.startAt.localeCompare(t.startAt)))}r.d(t,{$k:()=>o,Ay:()=>o})},6302:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>i,ZO:()=>n});var o=r(9799);function n(e){const t=Math.floor(e/36e5);let r=e-36e5*t;const o=Math.floor(r/6e4);r-=6e4*o;return[t,o,Math.floor(r/1e3)].map((e=>e<10?`0${e}`:e)).join(":")}const a="videopress-chapters-auto-generated";function i(e,t){const r=function(e,t=3599999e3){const r=(0,o.$k)(e);if(0===r.length)return null;let i="WEBVTT\n";i+="\n",i+="NOTE\n",i+=`${a}\n`,i+="This file was auto-generated based on Video description.\n",i+="For more information, see https://jetpack.com/support/jetpack-videopress/jetpack-videopress-customizing-your-videos/#adding-subtitles-captions-or-chapters-within-a-video\n";let s=1;for(const[e,o]of r.entries()){const a=0===e?"000":"001",c=e<r.length-1?r[e+1].startAt:n(t);i+=`\n${s++}\n${o.startAt}.${a} --\x3e ${c}.000\n${o.title}\n`}return i}(e,t);return r?new File([r],"chapters.vtt",{type:"text/vtt"}):null}},2066:(e,t,r)=>{"use strict";function o(e){const t=e.split(":");return 3600*parseInt(t[0])+60*parseInt(t[1])+parseInt(t[2])}function n(e){if(!e||0===e.length)return!1;if("00:00:00"!==e[0].startAt)return!1;if(e.length<3)return!1;if(e.some((e=>!e.title)))return!1;for(let t=0;t<e.length-1;t++){const r=e[t];if(o(e[t+1].startAt)-o(r.startAt)<10)return!1}return!0}r.d(t,{A:()=>n})},1016:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var o=r(4085),n=r(5217),a=r(3569),i=r(615);function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function c(){c=function(){return t};var e,t={},r=Object.prototype,o=r.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function p(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{p({},"")}catch(e){p=function(e,t,r){return e[t]=r}}function d(e,t,r,o){var a=t&&t.prototype instanceof b?t:b,i=Object.create(a.prototype),s=new U(o||[]);return n(i,"_invoke",{value:A(e,r,s)}),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var m="suspendedStart",h="suspendedYield",v="executing",g="completed",y={};function b(){}function k(){}function w(){}var _={};p(_,i,(function(){return this}));var E=Object.getPrototypeOf,R=E&&E(E(N([])));R&&R!==r&&o.call(R,i)&&(_=R);var C=w.prototype=b.prototype=Object.create(_);function S(e){["next","throw","return"].forEach((function(t){p(e,t,(function(e){return this._invoke(t,e)}))}))}function P(e,t){function r(n,a,i,c){var l=f(e[n],e,a);if("throw"!==l.type){var u=l.arg,p=u.value;return p&&"object"==s(p)&&o.call(p,"__await")?t.resolve(p.__await).then((function(e){r("next",e,i,c)}),(function(e){r("throw",e,i,c)})):t.resolve(p).then((function(e){u.value=e,i(u)}),(function(e){return r("throw",e,i,c)}))}c(l.arg)}var a;n(this,"_invoke",{value:function(e,o){function n(){return new t((function(t,n){r(e,o,t,n)}))}return a=a?a.then(n,n):n()}})}function A(t,r,o){var n=m;return function(a,i){if(n===v)throw Error("Generator is already running");if(n===g){if("throw"===a)throw i;return{value:e,done:!0}}for(o.method=a,o.arg=i;;){var s=o.delegate;if(s){var c=x(s,o);if(c){if(c===y)continue;return c}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(n===m)throw n=g,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);n=v;var l=f(t,r,o);if("normal"===l.type){if(n=o.done?g:h,l.arg===y)continue;return{value:l.arg,done:o.done}}"throw"===l.type&&(n=g,o.method="throw",o.arg=l.arg)}}}function x(t,r){var o=r.method,n=t.iterator[o];if(n===e)return r.delegate=null,"throw"===o&&t.iterator.return&&(r.method="return",r.arg=e,x(t,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),y;var a=f(n,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,y;var i=a.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function T(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function U(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function N(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,a=function r(){for(;++n<t.length;)if(o.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}throw new TypeError(s(t)+" is not iterable")}return k.prototype=w,n(C,"constructor",{value:w,configurable:!0}),n(w,"constructor",{value:k,configurable:!0}),k.displayName=p(w,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===k||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,p(e,u,"GeneratorFunction")),e.prototype=Object.create(C),e},t.awrap=function(e){return{__await:e}},S(P.prototype),p(P.prototype,l,(function(){return this})),t.AsyncIterator=P,t.async=function(e,r,o,n,a){void 0===a&&(a=Promise);var i=new P(d(e,r,o,n),a);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},S(C),p(C,u,"Generator"),p(C,i,(function(){return this})),p(C,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var o in t)r.push(o);return r.reverse(),function e(){for(;r.length;){var o=r.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},t.values=N,U.prototype={constructor:U,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(T),!t)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(o,n){return s.type="throw",s.arg=t,r.next=o,n&&(r.method="next",r.arg=e),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],s=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=o.call(i,"catchLoc"),l=o.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,y):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),T(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var o=r.completion;if("throw"===o.type){var n=o.arg;T(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,r,o){return this.delegate={iterator:N(t),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=e),y}},t}function l(e,t,r,o,n,a,i){try{var s=e[a](i),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(o,n)}function u(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,p(o.key),o)}}function p(e){var t=function(e,t){if("object"!=s(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=s(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==s(t)?t:t+""}var d=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)},t=[{key:"openFile",value:(s=c().mark((function e(t,r){var s;return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(0,o.A)()||!t||void 0===t.uri){e.next=11;break}return e.prev=1,e.next=4,(0,n.A)(t.uri);case 4:return s=e.sent,e.abrupt("return",new a.A(s));case 8:throw e.prev=8,e.t0=e.catch(1),new Error("tus: cannot fetch `file.uri` as Blob, make sure the uri is correct and accessible. ".concat(e.t0));case 11:if("function"!=typeof t.slice||void 0===t.size){e.next=13;break}return e.abrupt("return",Promise.resolve(new a.A(t)));case 13:if("function"!=typeof t.read){e.next=18;break}if(r=Number(r),Number.isFinite(r)){e.next=17;break}return e.abrupt("return",Promise.reject(new Error("cannot create source for stream without a finite value for the `chunkSize` option")));case 17:return e.abrupt("return",Promise.resolve(new i.A(t,r)));case 18:return e.abrupt("return",Promise.reject(new Error("source object may only be an instance of File, Blob, or Reader in this environment")));case 19:case"end":return e.stop()}}),e,null,[[1,8]])})),p=function(){var e=this,t=arguments;return new Promise((function(r,o){var n=s.apply(e,t);function a(e){l(n,r,o,a,i,"next",e)}function i(e){l(n,r,o,a,i,"throw",e)}a(void 0)}))},function(_x,e){return p.apply(this,arguments)})}],t&&u(e.prototype,t),r&&u(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,r,s,p}()},3765:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var o=r(4085);function n(e,t){return(0,o.A)()?Promise.resolve(function(e,t){var r=e.exif?function(e){var t=0;if(0===e.length)return t;for(var r=0;r<e.length;r++){t=(t<<5)-t+e.charCodeAt(r),t&=t}return t}(JSON.stringify(e.exif)):"noexif";return["tus-rn",e.name||"noname",e.size||"nosize",r,t.endpoint].join("/")}(e,t)):Promise.resolve(["tus-br",e.name,e.type,e.size,e.lastModified,t.endpoint].join("-"))}},5369:(e,t,r)=>{"use strict";function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,s(o.key),o)}}function i(e,t,r){return t&&a(e.prototype,t),r&&a(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function s(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:t+""}r.d(t,{A:()=>c});var c=function(){return i((function e(){n(this,e)}),[{key:"createRequest",value:function(e,t){return new l(e,t)}},{key:"getName",value:function(){return"XHRHttpStack"}}])}(),l=function(){return i((function e(t,r){n(this,e),this._xhr=new XMLHttpRequest,this._xhr.open(t,r,!0),this._method=t,this._url=r,this._headers={}}),[{key:"getMethod",value:function(){return this._method}},{key:"getURL",value:function(){return this._url}},{key:"setHeader",value:function(e,t){this._xhr.setRequestHeader(e,t),this._headers[e]=t}},{key:"getHeader",value:function(e){return this._headers[e]}},{key:"setProgressHandler",value:function(e){"upload"in this._xhr&&(this._xhr.upload.onprogress=function(t){t.lengthComputable&&e(t.loaded)})}},{key:"send",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return new Promise((function(r,o){e._xhr.onload=function(){r(new u(e._xhr))},e._xhr.onerror=function(e){o(e)},e._xhr.send(t)}))}},{key:"abort",value:function(){return this._xhr.abort(),Promise.resolve()}},{key:"getUnderlyingObject",value:function(){return this._xhr}}])}(),u=function(){return i((function e(t){n(this,e),this._xhr=t}),[{key:"getStatus",value:function(){return this._xhr.status}},{key:"getHeader",value:function(e){return this._xhr.getResponseHeader(e)}},{key:"getBody",value:function(){return this._xhr.responseText}},{key:"getUnderlyingObject",value:function(){return this._xhr}}])}()},781:(e,t,r)=>{"use strict";r.d(t,{_O:()=>k});var o=r(2494),n=r(5082),a=r(1016),i=r(3765),s=r(5369),c=r(851);function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function u(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,y(o.key),o)}}function p(e,t,r){return t=f(t),function(e,t){if(t&&("object"===l(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,d()?Reflect.construct(t,r||[],f(e).constructor):t.apply(e,r))}function d(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(d=function(){return!!e})()}function f(e){return f=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},f(e)}function m(e,t){return m=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},m(e,t)}function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,o)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach((function(t){g(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function g(e,t,r){return(t=y(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y(e){var t=function(e,t){if("object"!=l(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=l(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==l(t)?t:t+""}var b=v(v({},n.A.defaultOptions),{},{httpStack:new s.A,fileReader:new a.A,urlStorage:c.o?new c.Y:new o.A,fingerprint:i.A}),k=function(e){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),p(this,t,[e,r=v(v({},b),r)])}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&m(e,t)}(t,e),r=t,a=[{key:"terminate",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t=v(v({},b),t),n.A.terminate(e,t)}}],(o=null)&&u(r.prototype,o),a&&u(r,a),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,a}(n.A);"function"==typeof XMLHttpRequest&&"function"==typeof Blob&&Blob.prototype.slice},4085:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});const o=function(){return"undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase()}},3569:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var o=r(3716),n=r(9427);function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function i(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,s(o.key),o)}}function s(e){var t=function(e,t){if("object"!=a(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=a(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==a(t)?t:t+""}var c=function(){return e=function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._file=t,this.size=t.size},(t=[{key:"slice",value:function(e,t){if((0,o.A)())return(0,n.A)(this._file.slice(e,t));var r=this._file.slice(e,t),a=t>=this.size;return Promise.resolve({value:r,done:a})}},{key:"close",value:function(){}}])&&i(e.prototype,t),r&&i(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,r}()},615:(e,t,r)=>{"use strict";function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function n(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,a(o.key),o)}}function a(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:t+""}function i(e){return void 0===e?0:void 0!==e.size?e.size:e.length}r.d(t,{A:()=>s});var s=function(){return e=function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._buffer=void 0,this._bufferOffset=0,this._reader=t,this._done=!1},(t=[{key:"slice",value:function(e,t){return e<this._bufferOffset?Promise.reject(new Error("Requested data is before the reader's current offset")):this._readUntilEnoughDataOrDone(e,t)}},{key:"_readUntilEnoughDataOrDone",value:function(e,t){var r=this,o=t<=this._bufferOffset+i(this._buffer);if(this._done||o){var n=this._getDataFromBuffer(e,t),a=null==n&&this._done;return Promise.resolve({value:n,done:a})}return this._reader.read().then((function(o){var n=o.value;return o.done?r._done=!0:void 0===r._buffer?r._buffer=n:r._buffer=function(e,t){if(e.concat)return e.concat(t);if(e instanceof Blob)return new Blob([e,t],{type:e.type});if(e.set){var r=new e.constructor(e.length+t.length);return r.set(e),r.set(t,e.length),r}throw new Error("Unknown data type")}(r._buffer,n),r._readUntilEnoughDataOrDone(e,t)}))}},{key:"_getDataFromBuffer",value:function(e,t){e>this._bufferOffset&&(this._buffer=this._buffer.slice(e-this._bufferOffset),this._bufferOffset=e);var r=0===i(this._buffer);return this._done&&r?null:this._buffer.slice(0,t-e)}},{key:"close",value:function(){this._reader.cancel&&this._reader.cancel()}}])&&n(e.prototype,t),r&&n(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,r}()},3716:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});const o=function(){return"undefined"!=typeof window&&(void 0!==window.PhoneGap||void 0!==window.Cordova||void 0!==window.cordova)}},9427:(e,t,r)=>{"use strict";function o(e){return new Promise((function(t,r){var o=new FileReader;o.onload=function(){var e=new Uint8Array(o.result);t({value:e})},o.onerror=function(e){r(e)},o.readAsArrayBuffer(e)}))}r.d(t,{A:()=>o})},5217:(e,t,r)=>{"use strict";function o(e){return new Promise((function(t,r){var o=new XMLHttpRequest;o.responseType="blob",o.onload=function(){var e=o.response;t(e)},o.onerror=function(e){r(e)},o.open("GET",e),o.send()}))}r.d(t,{A:()=>o})},851:(e,t,r)=>{"use strict";function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function n(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,a(o.key),o)}}function a(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:t+""}r.d(t,{Y:()=>u,o:()=>l});var i=!1;try{i="localStorage"in window;var s="tusSupport",c=localStorage.getItem(s);localStorage.setItem(s,c),null===c&&localStorage.removeItem(s)}catch(e){if(e.code!==e.SECURITY_ERR&&e.code!==e.QUOTA_EXCEEDED_ERR)throw e;i=!1}var l=i,u=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)},t=[{key:"findAllUploads",value:function(){var e=this._findEntries("tus::");return Promise.resolve(e)}},{key:"findUploadsByFingerprint",value:function(e){var t=this._findEntries("tus::".concat(e,"::"));return Promise.resolve(t)}},{key:"removeUpload",value:function(e){return localStorage.removeItem(e),Promise.resolve()}},{key:"addUpload",value:function(e,t){var r=Math.round(1e12*Math.random()),o="tus::".concat(e,"::").concat(r);return localStorage.setItem(o,JSON.stringify(t)),Promise.resolve(o)}},{key:"_findEntries",value:function(e){for(var t=[],r=0;r<localStorage.length;r++){var o=localStorage.key(r);if(0===o.indexOf(e))try{var n=JSON.parse(localStorage.getItem(o));n.urlStorageKey=o,t.push(n)}catch(e){}}return t}}],t&&n(e.prototype,t),r&&n(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,r}()},3004:(e,t,r)=>{"use strict";function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function n(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,a(o.key),o)}}function a(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:t+""}function i(e,t,r){return t=u(t),function(e,t){if(t&&("object"===o(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,c()?Reflect.construct(t,r||[],u(e).constructor):t.apply(e,r))}function s(e){var t="function"==typeof Map?new Map:void 0;return s=function(e){if(null===e||!function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return function(e,t,r){if(c())return Reflect.construct.apply(null,arguments);var o=[null];o.push.apply(o,t);var n=new(e.bind.apply(e,o));return r&&l(n,r.prototype),n}(e,arguments,u(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),l(r,e)},s(e)}function c(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(c=function(){return!!e})()}function l(e,t){return l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},l(e,t)}function u(e){return u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},u(e)}r.d(t,{A:()=>p});const p=function(e){function t(e){var r,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),(r=i(this,t,[e])).originalRequest=n,r.originalResponse=a,r.causingError=o,null!=o&&(e+=", caused by ".concat(o.toString())),null!=n){var s=n.getHeader("X-Request-ID")||"n/a",c=n.getMethod(),l=n.getURL(),u=a?a.getStatus():"n/a",p=a?a.getBody()||"":"n/a";e+=", originated from request (method: ".concat(c,", url: ").concat(l,", response code: ").concat(u,", response text: ").concat(p,", request id: ").concat(s,")")}return r.message=e,r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&l(e,t)}(t,e),r=t,o&&n(r.prototype,o),a&&n(r,a),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,a}(s(Error))},2380:(e,t,r)=>{"use strict";r.d(t,{R:()=>n});var o=!1;function n(e){o&&console.log(e)}},2494:(e,t,r)=>{"use strict";function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function n(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,a(o.key),o)}}function a(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:t+""}r.d(t,{A:()=>i});var i=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)},(t=[{key:"listAllUploads",value:function(){return Promise.resolve([])}},{key:"findUploadsByFingerprint",value:function(e){return Promise.resolve([])}},{key:"removeUpload",value:function(e){return Promise.resolve()}},{key:"addUpload",value:function(e,t){return Promise.resolve(null)}}])&&n(e.prototype,t),r&&n(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,r}()},5082:(e,t,r)=>{"use strict";r.d(t,{A:()=>U});var o=r(9535),n=r(6580),a=r.n(n),i=r(3004),s=r(2380),c=r(7291);function l(){l=function(){return t};var e,t={},r=Object.prototype,o=r.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function p(e,t,r,o){var a=t&&t.prototype instanceof b?t:b,i=Object.create(a.prototype),s=new U(o||[]);return n(i,"_invoke",{value:A(e,r,s)}),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=p;var m="suspendedStart",h="suspendedYield",v="executing",g="completed",y={};function b(){}function k(){}function w(){}var _={};u(_,i,(function(){return this}));var E=Object.getPrototypeOf,R=E&&E(E(N([])));R&&R!==r&&o.call(R,i)&&(_=R);var C=w.prototype=b.prototype=Object.create(_);function S(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function P(e,t){function r(n,a,i,s){var c=f(e[n],e,a);if("throw"!==c.type){var l=c.arg,u=l.value;return u&&"object"==d(u)&&o.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,i,s)}),(function(e){r("throw",e,i,s)})):t.resolve(u).then((function(e){l.value=e,i(l)}),(function(e){return r("throw",e,i,s)}))}s(c.arg)}var a;n(this,"_invoke",{value:function(e,o){function n(){return new t((function(t,n){r(e,o,t,n)}))}return a=a?a.then(n,n):n()}})}function A(t,r,o){var n=m;return function(a,i){if(n===v)throw Error("Generator is already running");if(n===g){if("throw"===a)throw i;return{value:e,done:!0}}for(o.method=a,o.arg=i;;){var s=o.delegate;if(s){var c=x(s,o);if(c){if(c===y)continue;return c}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(n===m)throw n=g,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);n=v;var l=f(t,r,o);if("normal"===l.type){if(n=o.done?g:h,l.arg===y)continue;return{value:l.arg,done:o.done}}"throw"===l.type&&(n=g,o.method="throw",o.arg=l.arg)}}}function x(t,r){var o=r.method,n=t.iterator[o];if(n===e)return r.delegate=null,"throw"===o&&t.iterator.return&&(r.method="return",r.arg=e,x(t,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),y;var a=f(n,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,y;var i=a.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function T(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function U(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function N(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,a=function r(){for(;++n<t.length;)if(o.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}throw new TypeError(d(t)+" is not iterable")}return k.prototype=w,n(C,"constructor",{value:w,configurable:!0}),n(w,"constructor",{value:k,configurable:!0}),k.displayName=u(w,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===k||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,u(e,c,"GeneratorFunction")),e.prototype=Object.create(C),e},t.awrap=function(e){return{__await:e}},S(P.prototype),u(P.prototype,s,(function(){return this})),t.AsyncIterator=P,t.async=function(e,r,o,n,a){void 0===a&&(a=Promise);var i=new P(p(e,r,o,n),a);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},S(C),u(C,c,"Generator"),u(C,i,(function(){return this})),u(C,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var o in t)r.push(o);return r.reverse(),function e(){for(;r.length;){var o=r.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},t.values=N,U.prototype={constructor:U,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(T),!t)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(o,n){return s.type="throw",s.arg=t,r.next=o,n&&(r.method="next",r.arg=e),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],s=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=o.call(i,"catchLoc"),l=o.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,y):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),T(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var o=r.completion;if("throw"===o.type){var n=o.arg;T(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,r,o){return this.delegate={iterator:N(t),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=e),y}},t}function u(e,t,r,o,n,a,i){try{var s=e[a](i),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(o,n)}function p(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var o,n,a,i,s=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(o=a.call(r)).done)&&(s.push(o.value),s.length!==t);c=!0);}catch(e){l=!0,n=e}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(l)throw n}}return s}}(e,t)||f(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function f(e,t){if(e){if("string"==typeof e)return m(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?m(e,t):void 0}}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,o=new Array(t);r<t;r++)o[r]=e[r];return o}function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,o)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach((function(t){g(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function g(e,t,r){return(t=b(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,b(o.key),o)}}function b(e){var t=function(e,t){if("object"!=d(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=d(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==d(t)?t:t+""}var k="tus-v1",w="ietf-draft-03",_={endpoint:null,uploadUrl:null,metadata:{},metadataForPartialUploads:{},fingerprint:null,uploadSize:null,onProgress:null,onChunkComplete:null,onSuccess:null,onError:null,onUploadUrlAvailable:null,overridePatchMethod:!1,headers:{},addRequestId:!1,onBeforeRequest:null,onAfterResponse:null,onShouldRetry:j,chunkSize:Number.POSITIVE_INFINITY,retryDelays:[0,1e3,3e3,5e3],parallelUploads:1,parallelUploadBoundaries:null,storeFingerprintForResuming:!0,removeFingerprintOnSuccess:!1,uploadLengthDeferred:!1,uploadDataDuringCreation:!1,urlStorage:null,fileReader:null,httpStack:null,protocol:k},E=function(){function e(t,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),"resume"in r&&console.log("tus: The `resume` option has been removed in tus-js-client v2. Please use the URL storage API instead."),this.options=r,this.options.chunkSize=Number(this.options.chunkSize),this._urlStorage=this.options.urlStorage,this.file=t,this.url=null,this._req=null,this._fingerprint=null,this._urlStorageKey=null,this._offset=null,this._aborted=!1,this._size=null,this._source=null,this._retryAttempt=0,this._retryTimeout=null,this._offsetBeforeRetry=0,this._parallelUploads=null,this._parallelUploadUrls=null}return t=e,r=[{key:"findPreviousUploads",value:function(){var e=this;return this.options.fingerprint(this.file,this.options).then((function(t){return e._urlStorage.findUploadsByFingerprint(t)}))}},{key:"resumeFromPreviousUpload",value:function(e){this.url=e.uploadUrl||null,this._parallelUploadUrls=e.parallelUploadUrls||null,this._urlStorageKey=e.urlStorageKey}},{key:"start",value:function(){var e=this,t=this.file;if(t)if([k,w].includes(this.options.protocol))if(this.options.endpoint||this.options.uploadUrl||this.url){var r=this.options.retryDelays;if(null==r||"[object Array]"===Object.prototype.toString.call(r)){if(this.options.parallelUploads>1)for(var o=0,n=["uploadUrl","uploadSize","uploadLengthDeferred"];o<n.length;o++){var a=n[o];if(this.options[a])return void this._emitError(new Error("tus: cannot use the ".concat(a," option when parallelUploads is enabled")))}if(this.options.parallelUploadBoundaries){if(this.options.parallelUploads<=1)return void this._emitError(new Error("tus: cannot use the `parallelUploadBoundaries` option when `parallelUploads` is disabled"));if(this.options.parallelUploads!==this.options.parallelUploadBoundaries.length)return void this._emitError(new Error("tus: the `parallelUploadBoundaries` must have the same length as the value of `parallelUploads`"))}this.options.fingerprint(t,this.options).then((function(r){return null==r?(0,s.R)("No fingerprint was calculated meaning that the upload cannot be stored in the URL storage."):(0,s.R)("Calculated fingerprint: ".concat(r)),e._fingerprint=r,e._source?e._source:e.options.fileReader.openFile(t,e.options.chunkSize)})).then((function(t){if(e._source=t,e.options.uploadLengthDeferred)e._size=null;else if(null!=e.options.uploadSize){if(e._size=Number(e.options.uploadSize),Number.isNaN(e._size))return void e._emitError(new Error("tus: cannot convert `uploadSize` option into a number"))}else if(e._size=e._source.size,null==e._size)return void e._emitError(new Error("tus: cannot automatically derive upload's size from input. Specify it manually using the `uploadSize` option or use the `uploadLengthDeferred` option"));e.options.parallelUploads>1||null!=e._parallelUploadUrls?e._startParallelUpload():e._startSingleUpload()})).catch((function(t){e._emitError(t)}))}else this._emitError(new Error("tus: the `retryDelays` option must either be an array or null"))}else this._emitError(new Error("tus: neither an endpoint or an upload URL is provided"));else this._emitError(new Error("tus: unsupported protocol ".concat(this.options.protocol)));else this._emitError(new Error("tus: no file or stream to upload provided"))}},{key:"_startParallelUpload",value:function(){var t,r=this,o=this._size,n=0;this._parallelUploads=[];var a=null!=this._parallelUploadUrls?this._parallelUploadUrls.length:this.options.parallelUploads,i=null!==(t=this.options.parallelUploadBoundaries)&&void 0!==t?t:function(e,t){for(var r=Math.floor(e/t),o=[],n=0;n<t;n++)o.push({start:r*n,end:r*(n+1)});return o[t-1].end=e,o}(this._source.size,a);this._parallelUploadUrls&&i.forEach((function(e,t){e.uploadUrl=r._parallelUploadUrls[t]||null})),this._parallelUploadUrls=new Array(i.length);var c,l=i.map((function(t,a){var s=0;return r._source.slice(t.start,t.end).then((function(c){var l=c.value;return new Promise((function(c,u){var p=v(v({},r.options),{},{uploadUrl:t.uploadUrl||null,storeFingerprintForResuming:!1,removeFingerprintOnSuccess:!1,parallelUploads:1,parallelUploadBoundaries:null,metadata:r.options.metadataForPartialUploads,headers:v(v({},r.options.headers),{},{"Upload-Concat":"partial"}),onSuccess:c,onError:u,onProgress:function(e){n=n-s+e,s=e,r._emitProgress(n,o)},onUploadUrlAvailable:function(){r._parallelUploadUrls[a]=d.url,r._parallelUploadUrls.filter((function(e){return Boolean(e)})).length===i.length&&r._saveUploadInUrlStorage()}}),d=new e(l,p);d.start(),r._parallelUploads.push(d)}))}))}));Promise.all(l).then((function(){(c=r._openRequest("POST",r.options.endpoint)).setHeader("Upload-Concat","final;".concat(r._parallelUploadUrls.join(" ")));var e=R(r.options.metadata);return""!==e&&c.setHeader("Upload-Metadata",e),r._sendRequest(c,null)})).then((function(e){if(C(e.getStatus(),200)){var t=e.getHeader("Location");null!=t?(r.url=T(r.options.endpoint,t),(0,s.R)("Created upload at ".concat(r.url)),r._emitSuccess(e)):r._emitHttpError(c,e,"tus: invalid or missing Location header")}else r._emitHttpError(c,e,"tus: unexpected response while creating upload")})).catch((function(e){r._emitError(e)}))}},{key:"_startSingleUpload",value:function(){return this._aborted=!1,null!=this.url?((0,s.R)("Resuming upload from previous URL: ".concat(this.url)),void this._resumeUpload()):null!=this.options.uploadUrl?((0,s.R)("Resuming upload from provided URL: ".concat(this.options.uploadUrl)),this.url=this.options.uploadUrl,void this._resumeUpload()):((0,s.R)("Creating a new upload"),void this._createUpload())}},{key:"abort",value:function(t){var r=this;if(null!=this._parallelUploads){var o,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=f(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var o=0,n=function(){};return{s:n,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){s=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(s)throw a}}}}(this._parallelUploads);try{for(n.s();!(o=n.n()).done;)o.value.abort(t)}catch(e){n.e(e)}finally{n.f()}}return null!==this._req&&this._req.abort(),this._aborted=!0,null!=this._retryTimeout&&(clearTimeout(this._retryTimeout),this._retryTimeout=null),t&&null!=this.url?e.terminate(this.url,this.options).then((function(){return r._removeFromUrlStorage()})):Promise.resolve()}},{key:"_emitHttpError",value:function(e,t,r,o){this._emitError(new i.A(r,o,e,t))}},{key:"_emitError",value:function(e){var t=this;if(!this._aborted){if(null!=this.options.retryDelays&&(null!=this._offset&&this._offset>this._offsetBeforeRetry&&(this._retryAttempt=0),x(e,this._retryAttempt,this.options))){var r=this.options.retryDelays[this._retryAttempt++];return this._offsetBeforeRetry=this._offset,void(this._retryTimeout=setTimeout((function(){t.start()}),r))}if("function"!=typeof this.options.onError)throw e;this.options.onError(e)}}},{key:"_emitSuccess",value:function(e){this.options.removeFingerprintOnSuccess&&this._removeFromUrlStorage(),"function"==typeof this.options.onSuccess&&this.options.onSuccess({lastResponse:e})}},{key:"_emitProgress",value:function(e,t){"function"==typeof this.options.onProgress&&this.options.onProgress(e,t)}},{key:"_emitChunkComplete",value:function(e,t,r){"function"==typeof this.options.onChunkComplete&&this.options.onChunkComplete(e,t,r)}},{key:"_createUpload",value:function(){var e=this;if(this.options.endpoint){var t=this._openRequest("POST",this.options.endpoint);this.options.uploadLengthDeferred?t.setHeader("Upload-Defer-Length","1"):t.setHeader("Upload-Length","".concat(this._size));var r,o=R(this.options.metadata);""!==o&&t.setHeader("Upload-Metadata",o),this.options.uploadDataDuringCreation&&!this.options.uploadLengthDeferred?(this._offset=0,r=this._addChunkToRequest(t)):(this.options.protocol===w&&t.setHeader("Upload-Complete","?0"),r=this._sendRequest(t,null)),r.then((function(r){if(C(r.getStatus(),200)){var o=r.getHeader("Location");if(null!=o){if(e.url=T(e.options.endpoint,o),(0,s.R)("Created upload at ".concat(e.url)),"function"==typeof e.options.onUploadUrlAvailable&&e.options.onUploadUrlAvailable(),0===e._size)return e._emitSuccess(r),void e._source.close();e._saveUploadInUrlStorage().then((function(){e.options.uploadDataDuringCreation?e._handleUploadResponse(t,r):(e._offset=0,e._performUpload())}))}else e._emitHttpError(t,r,"tus: invalid or missing Location header")}else e._emitHttpError(t,r,"tus: unexpected response while creating upload")})).catch((function(r){e._emitHttpError(t,null,"tus: failed to create upload",r)}))}else this._emitError(new Error("tus: unable to create upload because no endpoint is provided"))}},{key:"_resumeUpload",value:function(){var e=this,t=this._openRequest("HEAD",this.url);this._sendRequest(t,null).then((function(r){var o=r.getStatus();if(!C(o,200))return 423===o?void e._emitHttpError(t,r,"tus: upload is currently locked; retry later"):(C(o,400)&&e._removeFromUrlStorage(),e.options.endpoint?(e.url=null,void e._createUpload()):void e._emitHttpError(t,r,"tus: unable to resume upload (new upload cannot be created without an endpoint)"));var n=Number.parseInt(r.getHeader("Upload-Offset"),10);if(Number.isNaN(n))e._emitHttpError(t,r,"tus: invalid or missing offset value");else{var a=Number.parseInt(r.getHeader("Upload-Length"),10);!Number.isNaN(a)||e.options.uploadLengthDeferred||e.options.protocol!==k?("function"==typeof e.options.onUploadUrlAvailable&&e.options.onUploadUrlAvailable(),e._saveUploadInUrlStorage().then((function(){if(n===a)return e._emitProgress(a,a),void e._emitSuccess(r);e._offset=n,e._performUpload()}))):e._emitHttpError(t,r,"tus: invalid or missing length value")}})).catch((function(r){e._emitHttpError(t,null,"tus: failed to resume upload",r)}))}},{key:"_performUpload",value:function(){var e,t=this;this._aborted||(this.options.overridePatchMethod?(e=this._openRequest("POST",this.url)).setHeader("X-HTTP-Method-Override","PATCH"):e=this._openRequest("PATCH",this.url),e.setHeader("Upload-Offset","".concat(this._offset)),this._addChunkToRequest(e).then((function(r){C(r.getStatus(),200)?t._handleUploadResponse(e,r):t._emitHttpError(e,r,"tus: unexpected response while uploading chunk")})).catch((function(r){t._aborted||t._emitHttpError(e,null,"tus: failed to upload chunk at offset ".concat(t._offset),r)})))}},{key:"_addChunkToRequest",value:function(e){var t=this,r=this._offset,o=this._offset+this.options.chunkSize;return e.setProgressHandler((function(e){t._emitProgress(r+e,t._size)})),e.setHeader("Content-Type","application/offset+octet-stream"),(o===Number.POSITIVE_INFINITY||o>this._size)&&!this.options.uploadLengthDeferred&&(o=this._size),this._source.slice(r,o).then((function(r){var o=r.value,n=r.done,a=null!=o&&o.size?o.size:0;t.options.uploadLengthDeferred&&n&&(t._size=t._offset+a,e.setHeader("Upload-Length","".concat(t._size)));var i=t._offset+a;return!t.options.uploadLengthDeferred&&n&&i!==t._size?Promise.reject(new Error("upload was configured with a size of ".concat(t._size," bytes, but the source is done after ").concat(i," bytes"))):null===o?t._sendRequest(e):(t.options.protocol===w&&e.setHeader("Upload-Complete",n?"?1":"?0"),t._emitProgress(t._offset,t._size),t._sendRequest(e,o))}))}},{key:"_handleUploadResponse",value:function(e,t){var r=Number.parseInt(t.getHeader("Upload-Offset"),10);if(Number.isNaN(r))this._emitHttpError(e,t,"tus: invalid or missing offset value");else{if(this._emitProgress(r,this._size),this._emitChunkComplete(r-this._offset,r,this._size),this._offset=r,r===this._size)return this._emitSuccess(t),void this._source.close();this._performUpload()}}},{key:"_openRequest",value:function(e,t){var r=S(e,t,this.options);return this._req=r,r}},{key:"_removeFromUrlStorage",value:function(){var e=this;this._urlStorageKey&&(this._urlStorage.removeUpload(this._urlStorageKey).catch((function(t){e._emitError(t)})),this._urlStorageKey=null)}},{key:"_saveUploadInUrlStorage",value:function(){var e=this;if(!this.options.storeFingerprintForResuming||!this._fingerprint||null!==this._urlStorageKey)return Promise.resolve();var t={size:this._size,metadata:this.options.metadata,creationTime:(new Date).toString()};return this._parallelUploads?t.parallelUploadUrls=this._parallelUploadUrls:t.uploadUrl=this.url,this._urlStorage.addUpload(this._fingerprint,t).then((function(t){e._urlStorageKey=t}))}},{key:"_sendRequest",value:function(e){return P(e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,this.options)}}],o=[{key:"terminate",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=S("DELETE",t,r);return P(o,null,r).then((function(e){if(204!==e.getStatus())throw new i.A("tus: unexpected response while terminating upload",null,o,e)})).catch((function(n){if(n instanceof i.A||(n=new i.A("tus: failed to terminate upload",n,o,null)),!x(n,0,r))throw n;var a=r.retryDelays[0],s=r.retryDelays.slice(1),c=v(v({},r),{},{retryDelays:s});return new Promise((function(e){return setTimeout(e,a)})).then((function(){return e.terminate(t,c)}))}))}}],r&&y(t.prototype,r),o&&y(t,o),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r,o}();function R(e){return Object.entries(e).map((function(e){var t=p(e,2),r=t[0],n=t[1];return"".concat(r," ").concat(o.o4.encode(String(n)))})).join(",")}function C(e,t){return e>=t&&e<t+100}function S(e,t,r){var o=r.httpStack.createRequest(e,t);r.protocol===w?o.setHeader("Upload-Draft-Interop-Version","5"):o.setHeader("Tus-Resumable","1.0.0");for(var n=r.headers||{},a=0,i=Object.entries(n);a<i.length;a++){var s=p(i[a],2),l=s[0],u=s[1];o.setHeader(l,u)}if(r.addRequestId){var d=(0,c.A)();o.setHeader("X-Request-ID",d)}return o}function P(_x,e,t){return A.apply(this,arguments)}function A(){var e;return e=l().mark((function e(t,r,o){var n;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("function"!=typeof o.onBeforeRequest){e.next=3;break}return e.next=3,o.onBeforeRequest(t);case 3:return e.next=5,t.send(r);case 5:if(n=e.sent,"function"!=typeof o.onAfterResponse){e.next=9;break}return e.next=9,o.onAfterResponse(t,n);case 9:return e.abrupt("return",n);case 10:case"end":return e.stop()}}),e)})),A=function(){var t=this,r=arguments;return new Promise((function(o,n){var a=e.apply(t,r);function i(e){u(a,o,n,i,s,"next",e)}function s(e){u(a,o,n,i,s,"throw",e)}i(void 0)}))},A.apply(this,arguments)}function x(e,t,r){return!(null==r.retryDelays||t>=r.retryDelays.length||null==e.originalRequest)&&(r&&"function"==typeof r.onShouldRetry?r.onShouldRetry(e,t,r):j(e))}function j(e){var t,r=e.originalResponse?e.originalResponse.getStatus():0;return(!C(r,400)||409===r||423===r)&&(t=!0,"undefined"!=typeof navigator&&!1===navigator.onLine&&(t=!1),t)}function T(e,t){return new(a())(t,e).toString()}E.defaultOptions=_;const U=E},7291:(e,t,r)=>{"use strict";function o(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}r.d(t,{A:()=>o})},6580:(e,t,r)=>{"use strict";var o=r(6811),n=r(4588),a=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,i=/[\n\r\t]/g,s=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,c=/:\d+$/,l=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,u=/^[a-zA-Z]:/;function p(e){return(e||"").toString().replace(a,"")}var d=[["#","hash"],["?","query"],function(e,t){return h(t.protocol)?e.replace(/\\/g,"/"):e},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d*)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],f={hash:1,query:1};function m(e){var t,r=("undefined"!=typeof window||"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).location||{},o={},n=typeof(e=e||r);if("blob:"===e.protocol)o=new g(unescape(e.pathname),{});else if("string"===n)for(t in o=new g(e,{}),f)delete o[t];else if("object"===n){for(t in e)t in f||(o[t]=e[t]);void 0===o.slashes&&(o.slashes=s.test(e.href))}return o}function h(e){return"file:"===e||"ftp:"===e||"http:"===e||"https:"===e||"ws:"===e||"wss:"===e}function v(e,t){e=(e=p(e)).replace(i,""),t=t||{};var r,o=l.exec(e),n=o[1]?o[1].toLowerCase():"",a=!!o[2],s=!!o[3],c=0;return a?s?(r=o[2]+o[3]+o[4],c=o[2].length+o[3].length):(r=o[2]+o[4],c=o[2].length):s?(r=o[3]+o[4],c=o[3].length):r=o[4],"file:"===n?c>=2&&(r=r.slice(2)):h(n)?r=o[4]:n?a&&(r=r.slice(2)):c>=2&&h(t.protocol)&&(r=o[4]),{protocol:n,slashes:a||h(n),slashesCount:c,rest:r}}function g(e,t,r){if(e=(e=p(e)).replace(i,""),!(this instanceof g))return new g(e,t,r);var a,s,c,l,f,y,b=d.slice(),k=typeof t,w=this,_=0;for("object"!==k&&"string"!==k&&(r=t,t=null),r&&"function"!=typeof r&&(r=n.parse),a=!(s=v(e||"",t=m(t))).protocol&&!s.slashes,w.slashes=s.slashes||a&&t.slashes,w.protocol=s.protocol||t.protocol||"",e=s.rest,("file:"===s.protocol&&(2!==s.slashesCount||u.test(e))||!s.slashes&&(s.protocol||s.slashesCount<2||!h(w.protocol)))&&(b[3]=[/(.*)/,"pathname"]);_<b.length;_++)"function"!=typeof(l=b[_])?(c=l[0],y=l[1],c!=c?w[y]=e:"string"==typeof c?~(f="@"===c?e.lastIndexOf(c):e.indexOf(c))&&("number"==typeof l[2]?(w[y]=e.slice(0,f),e=e.slice(f+l[2])):(w[y]=e.slice(f),e=e.slice(0,f))):(f=c.exec(e))&&(w[y]=f[1],e=e.slice(0,f.index)),w[y]=w[y]||a&&l[3]&&t[y]||"",l[4]&&(w[y]=w[y].toLowerCase())):e=l(e,w);r&&(w.query=r(w.query)),a&&t.slashes&&"/"!==w.pathname.charAt(0)&&(""!==w.pathname||""!==t.pathname)&&(w.pathname=function(e,t){if(""===e)return t;for(var r=(t||"/").split("/").slice(0,-1).concat(e.split("/")),o=r.length,n=r[o-1],a=!1,i=0;o--;)"."===r[o]?r.splice(o,1):".."===r[o]?(r.splice(o,1),i++):i&&(0===o&&(a=!0),r.splice(o,1),i--);return a&&r.unshift(""),"."!==n&&".."!==n||r.push(""),r.join("/")}(w.pathname,t.pathname)),"/"!==w.pathname.charAt(0)&&h(w.protocol)&&(w.pathname="/"+w.pathname),o(w.port,w.protocol)||(w.host=w.hostname,w.port=""),w.username=w.password="",w.auth&&(~(f=w.auth.indexOf(":"))?(w.username=w.auth.slice(0,f),w.username=encodeURIComponent(decodeURIComponent(w.username)),w.password=w.auth.slice(f+1),w.password=encodeURIComponent(decodeURIComponent(w.password))):w.username=encodeURIComponent(decodeURIComponent(w.auth)),w.auth=w.password?w.username+":"+w.password:w.username),w.origin="file:"!==w.protocol&&h(w.protocol)&&w.host?w.protocol+"//"+w.host:"null",w.href=w.toString()}g.prototype={set:function(e,t,r){var a=this;switch(e){case"query":"string"==typeof t&&t.length&&(t=(r||n.parse)(t)),a[e]=t;break;case"port":a[e]=t,o(t,a.protocol)?t&&(a.host=a.hostname+":"+t):(a.host=a.hostname,a[e]="");break;case"hostname":a[e]=t,a.port&&(t+=":"+a.port),a.host=t;break;case"host":a[e]=t,c.test(t)?(t=t.split(":"),a.port=t.pop(),a.hostname=t.join(":")):(a.hostname=t,a.port="");break;case"protocol":a.protocol=t.toLowerCase(),a.slashes=!r;break;case"pathname":case"hash":if(t){var i="pathname"===e?"/":"#";a[e]=t.charAt(0)!==i?i+t:t}else a[e]=t;break;case"username":case"password":a[e]=encodeURIComponent(t);break;case"auth":var s=t.indexOf(":");~s?(a.username=t.slice(0,s),a.username=encodeURIComponent(decodeURIComponent(a.username)),a.password=t.slice(s+1),a.password=encodeURIComponent(decodeURIComponent(a.password))):a.username=encodeURIComponent(decodeURIComponent(t))}for(var l=0;l<d.length;l++){var u=d[l];u[4]&&(a[u[1]]=a[u[1]].toLowerCase())}return a.auth=a.password?a.username+":"+a.password:a.username,a.origin="file:"!==a.protocol&&h(a.protocol)&&a.host?a.protocol+"//"+a.host:"null",a.href=a.toString(),a},toString:function(e){e&&"function"==typeof e||(e=n.stringify);var t,r=this,o=r.host,a=r.protocol;a&&":"!==a.charAt(a.length-1)&&(a+=":");var i=a+(r.protocol&&r.slashes||h(r.protocol)?"//":"");return r.username?(i+=r.username,r.password&&(i+=":"+r.password),i+="@"):r.password?(i+=":"+r.password,i+="@"):"file:"!==r.protocol&&h(r.protocol)&&!o&&"/"!==r.pathname&&(i+="@"),(":"===o[o.length-1]||c.test(r.hostname)&&!r.port)&&(o+=":"),i+=o+r.pathname,(t="object"==typeof r.query?e(r.query):r.query)&&(i+="?"!==t.charAt(0)?"?"+t:t),r.hash&&(i+=r.hash),i}},g.extractProtocol=v,g.location=m,g.trimLeft=p,g.qs=n,e.exports=g},8894:(e,t,r)=>{"use strict";e.exports=r.p+"images/dynamic-colors-34cf384d91fb0fd3a9d5.png"},4071:(e,t,r)=>{"use strict";e.exports=r.p+"images/videopress-block-example-image-d7dd9fae6ff49181bfca.jpg"},9384:e=>{"use strict";e.exports=window.JetpackConnection},7999:e=>{"use strict";e.exports=window.JetpackScriptDataModule},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},8468:e=>{"use strict";e.exports=window.lodash},1455:e=>{"use strict";e.exports=window.wp.apiFetch},3162:e=>{"use strict";e.exports=window.wp.blob},4715:e=>{"use strict";e.exports=window.wp.blockEditor},4997:e=>{"use strict";e.exports=window.wp.blocks},6427:e=>{"use strict";e.exports=window.wp.components},9491:e=>{"use strict";e.exports=window.wp.compose},3582:e=>{"use strict";e.exports=window.wp.coreData},7143:e=>{"use strict";e.exports=window.wp.data},8490:e=>{"use strict";e.exports=window.wp.domReady},3656:e=>{"use strict";e.exports=window.wp.editor},6087:e=>{"use strict";e.exports=window.wp.element},9877:e=>{"use strict";e.exports=window.wp.escapeHtml},2619:e=>{"use strict";e.exports=window.wp.hooks},8537:e=>{"use strict";e.exports=window.wp.htmlEntities},7723:e=>{"use strict";e.exports=window.wp.i18n},2279:e=>{"use strict";e.exports=window.wp.plugins},5573:e=>{"use strict";e.exports=window.wp.primitives},3832:e=>{"use strict";e.exports=window.wp.url},6072:e=>{function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)({}).hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},3022:(e,t,r)=>{"use strict";function o(e){var t,r,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(r=o(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}r.d(t,{A:()=>n});const n=function(){for(var e,t,r=0,n="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=o(e))&&(n&&(n+=" "),n+=t);return n}},7578:(e,t,r)=>{"use strict";r.d(t,{O:()=>S});const o="array",n="bit",a="bits",i="byte",s="bytes",c="",l="exponent",u="function",p="iec",d="Invalid number",f="Invalid rounding method",m="jedec",h="object",v=".",g="round",y="s",b="si",k="kbit",w="kB",_=" ",E="string",R="0",C={symbol:{iec:{bits:["bit","Kibit","Mibit","Gibit","Tibit","Pibit","Eibit","Zibit","Yibit"],bytes:["B","KiB","MiB","GiB","TiB","PiB","EiB","ZiB","YiB"]},jedec:{bits:["bit","Kbit","Mbit","Gbit","Tbit","Pbit","Ebit","Zbit","Ybit"],bytes:["B","KB","MB","GB","TB","PB","EB","ZB","YB"]}},fullform:{iec:["","kibi","mebi","gibi","tebi","pebi","exbi","zebi","yobi"],jedec:["","kilo","mega","giga","tera","peta","exa","zetta","yotta"]}};function S(e,{bits:t=!1,pad:r=!1,base:S=-1,round:P=2,locale:A=c,localeOptions:x={},separator:j=c,spacer:T=_,symbols:U={},standard:N=c,output:O=E,fullform:B=!1,fullforms:D=[],exponent:L=-1,roundingMethod:I=g,precision:F=0}={}){let M=L,H=Number(e),z=[],V=0,G=c;N===b?(S=10,N=m):N===p||N===m?S=2:2===S?N=p:(S=10,N=m);const q=10===S?1e3:1024,$=!0===B,J=H<0,W=Math[I];if("bigint"!=typeof e&&isNaN(e))throw new TypeError(d);if(typeof W!==u)throw new TypeError(f);if(J&&(H=-H),(-1===M||isNaN(M))&&(M=Math.floor(Math.log(H)/Math.log(q)),M<0&&(M=0)),M>8&&(F>0&&(F+=8-M),M=8),O===l)return M;if(0===H)z[0]=0,G=z[1]=C.symbol[N][t?a:s][M];else{V=H/(2===S?Math.pow(2,10*M):Math.pow(1e3,M)),t&&(V*=8,V>=q&&M<8&&(V/=q,M++));const e=Math.pow(10,M>0?P:0);z[0]=W(V*e)/e,z[0]===q&&M<8&&-1===L&&(z[0]=1,M++),G=z[1]=10===S&&1===M?t?k:w:C.symbol[N][t?a:s][M]}if(J&&(z[0]=-z[0]),F>0&&(z[0]=z[0].toPrecision(F)),z[1]=U[z[1]]||z[1],!0===A?z[0]=z[0].toLocaleString():A.length>0?z[0]=z[0].toLocaleString(A,x):j.length>0&&(z[0]=z[0].toString().replace(v,j)),r&&P>0){const e=z[0].toString(),t=j||(e.match(/(\D)/g)||[]).pop()||v,r=e.toString().split(t),o=r[1]||c,n=o.length,a=P-n;z[0]=`${r[0]}${t}${o.padEnd(n+a,R)}`}return $&&(z[1]=D[M]?D[M]:C.fullform[N][M]+(t?n:i)+(1===z[0]?c:y)),O===o?z:O===h?{value:z[0],symbol:z[1],exponent:M,unit:G}:z.join(T)}},9535:(e,t,r)=>{"use strict";r.d(t,{o4:()=>I});const o="3.7.7",n=o,a="function"==typeof Buffer,i="function"==typeof TextDecoder?new TextDecoder:void 0,s="function"==typeof TextEncoder?new TextEncoder:void 0,c=Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="),l=(e=>{let t={};return e.forEach(((e,r)=>t[e]=r)),t})(c),u=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,p=String.fromCharCode.bind(String),d="function"==typeof Uint8Array.from?Uint8Array.from.bind(Uint8Array):e=>new Uint8Array(Array.prototype.slice.call(e,0)),f=e=>e.replace(/=/g,"").replace(/[+\/]/g,(e=>"+"==e?"-":"_")),m=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),h=e=>{let t,r,o,n,a="";const i=e.length%3;for(let i=0;i<e.length;){if((r=e.charCodeAt(i++))>255||(o=e.charCodeAt(i++))>255||(n=e.charCodeAt(i++))>255)throw new TypeError("invalid character found");t=r<<16|o<<8|n,a+=c[t>>18&63]+c[t>>12&63]+c[t>>6&63]+c[63&t]}return i?a.slice(0,i-3)+"===".substring(i):a},v="function"==typeof btoa?e=>btoa(e):a?e=>Buffer.from(e,"binary").toString("base64"):h,g=a?e=>Buffer.from(e).toString("base64"):e=>{let t=[];for(let r=0,o=e.length;r<o;r+=4096)t.push(p.apply(null,e.subarray(r,r+4096)));return v(t.join(""))},y=(e,t=!1)=>t?f(g(e)):g(e),b=e=>{if(e.length<2)return(t=e.charCodeAt(0))<128?e:t<2048?p(192|t>>>6)+p(128|63&t):p(224|t>>>12&15)+p(128|t>>>6&63)+p(128|63&t);var t=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return p(240|t>>>18&7)+p(128|t>>>12&63)+p(128|t>>>6&63)+p(128|63&t)},k=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,w=e=>e.replace(k,b),_=a?e=>Buffer.from(e,"utf8").toString("base64"):s?e=>g(s.encode(e)):e=>v(w(e)),E=(e,t=!1)=>t?f(_(e)):_(e),R=e=>E(e,!0),C=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,S=e=>{switch(e.length){case 4:var t=((7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3))-65536;return p(55296+(t>>>10))+p(56320+(1023&t));case 3:return p((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return p((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},P=e=>e.replace(C,S),A=e=>{if(e=e.replace(/\s+/g,""),!u.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(3&e.length));let t,r,o,n="";for(let a=0;a<e.length;)t=l[e.charAt(a++)]<<18|l[e.charAt(a++)]<<12|(r=l[e.charAt(a++)])<<6|(o=l[e.charAt(a++)]),n+=64===r?p(t>>16&255):64===o?p(t>>16&255,t>>8&255):p(t>>16&255,t>>8&255,255&t);return n},x="function"==typeof atob?e=>atob(m(e)):a?e=>Buffer.from(e,"base64").toString("binary"):A,j=a?e=>d(Buffer.from(e,"base64")):e=>d(x(e).split("").map((e=>e.charCodeAt(0)))),T=e=>j(N(e)),U=a?e=>Buffer.from(e,"base64").toString("utf8"):i?e=>i.decode(j(e)):e=>P(x(e)),N=e=>m(e.replace(/[-_]/g,(e=>"-"==e?"+":"/"))),O=e=>U(N(e)),B=e=>({value:e,enumerable:!1,writable:!0,configurable:!0}),D=function(){const e=(e,t)=>Object.defineProperty(String.prototype,e,B(t));e("fromBase64",(function(){return O(this)})),e("toBase64",(function(e){return E(this,e)})),e("toBase64URI",(function(){return E(this,!0)})),e("toBase64URL",(function(){return E(this,!0)})),e("toUint8Array",(function(){return T(this)}))},L=function(){const e=(e,t)=>Object.defineProperty(Uint8Array.prototype,e,B(t));e("toBase64",(function(e){return y(this,e)})),e("toBase64URI",(function(){return y(this,!0)})),e("toBase64URL",(function(){return y(this,!0)}))},I={version:o,VERSION:n,atob:x,atobPolyfill:A,btoa:v,btoaPolyfill:h,fromBase64:O,toBase64:E,encode:E,encodeURI:R,encodeURL:R,utob:w,btou:P,decode:O,isValid:e=>{if("string"!=typeof e)return!1;const t=e.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(t)||!/[^\s0-9a-zA-Z\-_]/.test(t)},fromUint8Array:y,toUint8Array:T,extendString:D,extendUint8Array:L,extendBuiltins:()=>{D(),L()}}},8377:e=>{"use strict";e.exports=JSON.parse('{"T":{"White":"#fff","Black":"#000","Gray 0":"#f6f7f7","Gray 5":"#dcdcde","Gray 10":"#c3c4c7","Gray 20":"#a7aaad","Gray 30":"#8c8f94","Gray 40":"#787c82","Gray 50":"#646970","Gray 60":"#50575e","Gray 70":"#3c434a","Gray 80":"#2c3338","Gray 90":"#1d2327","Gray 100":"#101517","Gray":"#646970","Blue 0":"#fbfcfe","Blue 5":"#f7f8fe","Blue 10":"#d6ddf9","Blue 20":"#adbaf3","Blue 30":"#7b90ff","Blue 40":"#546ff3","Blue 50":"#3858e9","Blue 60":"#2a46ce","Blue 70":"#1d35b4","Blue 80":"#1f3286","Blue 90":"#14215a","Blue 100":"#0a112d","Blue":"#3858e9","WordPress Blue 0":"#fbfcfe","WordPress Blue 5":"#f7f8fe","WordPress Blue 10":"#d6ddf9","WordPress Blue 20":"#adbaf3","WordPress Blue 30":"#7b90ff","WordPress Blue 40":"#546ff3","WordPress Blue 50":"#3858e9","WordPress Blue 60":"#2a46ce","WordPress Blue 70":"#1d35b4","WordPress Blue 80":"#1f3286","WordPress Blue 90":"#14215a","WordPress Blue 100":"#0a112d","WordPress Blue":"#3858e9","Purple 0":"#f2e9ed","Purple 5":"#ebcee0","Purple 10":"#e3afd5","Purple 20":"#d48fc8","Purple 30":"#c475bd","Purple 40":"#b35eb1","Purple 50":"#984a9c","Purple 60":"#7c3982","Purple 70":"#662c6e","Purple 80":"#4d2054","Purple 90":"#35163b","Purple 100":"#1e0c21","Purple":"#984a9c","Pink 0":"#f5e9ed","Pink 5":"#f2ceda","Pink 10":"#f7a8c3","Pink 20":"#f283aa","Pink 30":"#eb6594","Pink 40":"#e34c84","Pink 50":"#c9356e","Pink 60":"#ab235a","Pink 70":"#8c1749","Pink 80":"#700f3b","Pink 90":"#4f092a","Pink 100":"#260415","Pink":"#c9356e","Red 0":"#f7ebec","Red 5":"#facfd2","Red 10":"#ffabaf","Red 20":"#ff8085","Red 30":"#f86368","Red 40":"#e65054","Red 50":"#d63638","Red 60":"#b32d2e","Red 70":"#8a2424","Red 80":"#691c1c","Red 90":"#451313","Red 100":"#240a0a","Red":"#d63638","Orange 0":"#f5ece6","Orange 5":"#f7dcc6","Orange 10":"#ffbf86","Orange 20":"#faa754","Orange 30":"#e68b28","Orange 40":"#d67709","Orange 50":"#b26200","Orange 60":"#8a4d00","Orange 70":"#704000","Orange 80":"#543100","Orange 90":"#361f00","Orange 100":"#1f1200","Orange":"#b26200","Yellow 0":"#f5f1e1","Yellow 5":"#f5e6b3","Yellow 10":"#f2d76b","Yellow 20":"#f0c930","Yellow 30":"#deb100","Yellow 40":"#c08c00","Yellow 50":"#9d6e00","Yellow 60":"#7d5600","Yellow 70":"#674600","Yellow 80":"#4f3500","Yellow 90":"#320","Yellow 100":"#1c1300","Yellow":"#9d6e00","Green 0":"#e6f2e8","Green 5":"#b8e6bf","Green 10":"#68de86","Green 20":"#1ed15a","Green 30":"#00ba37","Green 40":"#00a32a","Green 50":"#008a20","Green 60":"#007017","Green 70":"#005c12","Green 80":"#00450c","Green 90":"#003008","Green 100":"#001c05","Green":"#008a20","Celadon 0":"#e4f2ed","Celadon 5":"#a7e8d3","Celadon 10":"#66deb9","Celadon 20":"#31cc9f","Celadon 30":"#09b585","Celadon 40":"#009e73","Celadon 50":"#008763","Celadon 60":"#007053","Celadon 70":"#005c44","Celadon 80":"#004533","Celadon 90":"#003024","Celadon 100":"#001c15","Celadon":"#008763","Automattic Blue 0":"#ebf4fa","Automattic Blue 5":"#c4e2f5","Automattic Blue 10":"#88ccf2","Automattic Blue 20":"#5ab7e8","Automattic Blue 30":"#24a3e0","Automattic Blue 40":"#1490c7","Automattic Blue 50":"#0277a8","Automattic Blue 60":"#036085","Automattic Blue 70":"#02506e","Automattic Blue 80":"#02384d","Automattic Blue 90":"#022836","Automattic Blue 100":"#021b24","Automattic Blue":"#24a3e0","Simplenote Blue 0":"#e9ecf5","Simplenote Blue 5":"#ced9f2","Simplenote Blue 10":"#abc1f5","Simplenote Blue 20":"#84a4f0","Simplenote Blue 30":"#618df2","Simplenote Blue 40":"#4678eb","Simplenote Blue 50":"#3361cc","Simplenote Blue 60":"#1d4fc4","Simplenote Blue 70":"#113ead","Simplenote Blue 80":"#0d2f85","Simplenote Blue 90":"#09205c","Simplenote Blue 100":"#05102e","Simplenote Blue":"#3361cc","WooCommerce Purple 0":"#f2edff","WooCommerce Purple 5":"#e1d7ff","WooCommerce Purple 10":"#d1c1ff","WooCommerce Purple 20":"#b999ff","WooCommerce Purple 30":"#a77eff","WooCommerce Purple 40":"#873eff","WooCommerce Purple 50":"#720eec","WooCommerce Purple 60":"#6108ce","WooCommerce Purple 70":"#5007aa","WooCommerce Purple 80":"#3c087e","WooCommerce Purple 90":"#2c045d","WooCommerce Purple 100":"#1f0342","WooCommerce Purple":"#720eec","Jetpack Green 0":"#f0f2eb","Jetpack Green 5":"#d0e6b8","Jetpack Green 10":"#9dd977","Jetpack Green 20":"#64ca43","Jetpack Green 30":"#2fb41f","Jetpack Green 40":"#069e08","Jetpack Green 50":"#008710","Jetpack Green 60":"#007117","Jetpack Green 70":"#005b18","Jetpack Green 80":"#004515","Jetpack Green 90":"#003010","Jetpack Green 100":"#001c09","Jetpack Green":"#069e08"}}')},5015:e=>{"use strict";e.exports=JSON.parse('{"UU":"videopress/video","DD":"VideoPress","h_":"Embed a video from your media library or upload a new one with VideoPress.","uK":{"autoplay":{"type":"boolean"},"anchor":{"type":"string"},"title":{"type":"string"},"description":{"type":"string"},"caption":{"type":"string"},"controls":{"type":"boolean","default":true},"loop":{"type":"boolean"},"maxWidth":{"type":"string","default":"100%"},"muted":{"type":"boolean"},"playsinline":{"type":"boolean"},"preload":{"type":"string","default":"metadata"},"seekbarPlayedColor":{"type":"string","default":""},"seekbarLoadingColor":{"type":"string","default":""},"seekbarColor":{"type":"string","default":""},"useAverageColor":{"type":"boolean","default":true},"id":{"type":"number"},"guid":{"type":"string"},"src":{"type":"string"},"cacheHtml":{"type":"string","default":""},"poster":{"type":"string"},"posterData":{"type":"object","default":{}},"videoRatio":{"type":"number"},"tracks":{"type":"array","items":{"type":"object"},"default":[]},"privacySetting":{"type":"number","default":1},"allowDownload":{"type":"boolean","default":true},"displayEmbed":{"type":"boolean","default":true},"rating":{"type":"string"},"isPrivate":{"type":"boolean"},"isExample":{"type":"boolean","default":false},"duration":{"type":"number"}}}')}},t={};function r(o){var n=t[o];if(void 0!==n)return n.exports;var a=t[o]={exports:{}};return e[o](a,a.exports,r),a.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var o in t)r.o(t,o)&&!r.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e;r.g.importScripts&&(e=r.g.location+"");var t=r.g.document;if(!e&&t&&(t.currentScript&&"SCRIPT"===t.currentScript.tagName.toUpperCase()&&(e=t.currentScript.src),!e)){var o=t.getElementsByTagName("script");if(o.length)for(var n=o.length-1;n>-1&&(!e||!/^http(s?):/.test(e));)e=o[n--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),r.p=e+"../../../"})();r(1200)})();