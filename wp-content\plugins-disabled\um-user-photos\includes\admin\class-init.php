<?php
namespace um_ext\um_user_photos\admin;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Init
 *
 * @package um_ext\um_user_photos\admin
 */
class Init {

	/**
	 * Create classes' instances where __construct isn't empty for hooks init
	 */
	public function includes() {
		$this->menu();
		$this->metabox();
		$this->posts();
		$this->settings();
	}

	/**
	 * @return Menu
	 */
	public function menu() {
		if ( empty( UM()->classes['um_ext\um_user_photos\admin\menu'] ) ) {
			UM()->classes['um_ext\um_user_photos\admin\menu'] = new Menu();
		}
		return UM()->classes['um_ext\um_user_photos\admin\menu'];
	}

	/**
	 * @return Metabox
	 */
	public function metabox() {
		if ( empty( UM()->classes['um_ext\um_user_photos\admin\metabox'] ) ) {
			UM()->classes['um_ext\um_user_photos\admin\metabox'] = new Metabox();
		}
		return UM()->classes['um_ext\um_user_photos\admin\metabox'];
	}

	/**
	 * @return Posts
	 */
	public function posts() {
		if ( empty( UM()->classes['um_ext\um_user_photos\admin\posts'] ) ) {
			UM()->classes['um_ext\um_user_photos\admin\posts'] = new Posts();
		}
		return UM()->classes['um_ext\um_user_photos\admin\posts'];
	}

	/**
	 * @return Settings
	 */
	public function settings() {
		if ( empty( UM()->classes['um_ext\um_user_photos\admin\settings'] ) ) {
			UM()->classes['um_ext\um_user_photos\admin\settings'] = new Settings();
		}
		return UM()->classes['um_ext\um_user_photos\admin\settings'];
	}
}
