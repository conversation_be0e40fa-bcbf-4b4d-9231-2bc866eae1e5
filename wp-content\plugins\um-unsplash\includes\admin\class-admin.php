<?php
namespace um_ext\um_unsplash\admin;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! class_exists( 'um_ext\um_unsplash\admin\Admin' ) ) {

	/**
	 * Class Admin
	 * @package um_ext\um_unsplash\admin
	 */
	class Admin {

		/**
		 * Admin constructor.
		 */
		public function __construct() {
			add_filter( 'um_settings_structure', array( &$this, 'extend_settings' ) );

			add_filter( 'um_override_templates_scan_files', array( &$this, 'um_unsplash_extend_scan_files' ), 10, 1 );
			add_filter( 'um_override_templates_get_template_path__um-unsplash', array( &$this, 'um_unsplash_get_path_template' ), 10, 2 );
		}

		/**
		 * Additional Settings for Photos
		 *
		 * @param array $settings
		 *
		 * @return array
		 */
		public function extend_settings( $settings ) {
			$settings['licenses']['fields'][] = array(
				'id'        => 'um_unsplash_license_key',
				'label'     => __( 'Unsplash License Key', 'um-unsplash' ),
				'item_name' => 'Unsplash',
				'author'    => 'ultimatemember',
				'version'   => um_unsplash_version,
			);

			$settings['extensions']['sections']['um-unsplash'] = array(
				'title'  => __( 'Unsplash', 'um-unsplash' ),
				'fields' => array(
					array(
						'id'          => 'unsplash_no_of_photos',
						'type'        => 'text',
						'placeholder' => '1 to 30',
						'label'       => __( 'No. of photos to display', 'um-unsplash' ),
						'size'        => 'medium',
					),
					array(
						'id'          => 'unsplash_default_keyword',
						'type'        => 'text',
						'placeholder' => __( 'Example : Light', 'um-unsplash' ),
						'label'       => __( 'Default keyword', 'um-unsplash' ),
						'size'        => 'medium',
						'default'     => 'light',
					),
				),
			);

			return $settings;
		}

		/**
		 * Scan templates from extensionю
		 *
		 * @param array $scan_files
		 *
		 * @return array
		 */
		public function um_unsplash_extend_scan_files( $scan_files ) {
			$extension_files['um-unsplash'] = UM()->admin_settings()->scan_template_files( um_unsplash_path . '/templates/' );
			$scan_files                     = array_merge( $scan_files, $extension_files );

			return $scan_files;
		}

		/**
		 * Get template paths.
		 *
		 * @param array  $located
		 * @param string $file
		 *
		 * @return array
		 */
		public function um_unsplash_get_path_template( $located, $file ) {
			if ( file_exists( get_stylesheet_directory() . '/ultimate-member/um-unsplash/' . $file ) ) {
				$located = array(
					'theme' => get_stylesheet_directory() . '/ultimate-member/um-unsplash/' . $file,
					'core'  => um_unsplash_path . 'templates/' . $file,
				);
			}

			return $located;
		}
	}
}
