<?php
/**
 * @var array $object Ultimate Member role.
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

$role_data = $object['data'];
?>

<div class="um-admin-metabox">
	<?php
	UM()->admin_forms(
		array(
			'class'     => 'um-role-photos um-half-column',
			'prefix_id' => 'role',
			'fields'    => array(
				array(
					'id'             => '_um_enable_user_photos',
					'type'           => 'checkbox',
					'default'        => 0,
					'checkbox_label' => __( 'Enable User Photos', 'ultimate-member' ),
					'label'          => __( 'User Photos feature', 'um-user-photos' ),
					'description'    => __( 'Allows this role to have user photos feature', 'um-user-photos' ),
					'value'          => isset( $role_data['_um_enable_user_photos'] ) ? $role_data['_um_enable_user_photos'] : 0,
				),
				array(
					'id'          => '_um_limit_user_photos',
					'type'        => 'number',
					'default'     => 0,
					'label'       => __( 'User gallery limit', 'um-user-photos' ),
					'description' => __( 'The number of photos that the user can add. 0 or empty for unlimited upload', 'um-user-photos' ),
					'value'       => isset( $role_data['_um_limit_user_photos'] ) ? $role_data['_um_limit_user_photos'] : 0,
					'conditional' => array( '_um_enable_user_photos', '=', '1' ),
					'size'        => 'small',
				),
			),
		)
	)->render_form();
	?>
	<div class="clear"></div>
</div>
