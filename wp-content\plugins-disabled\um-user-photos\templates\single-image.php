<?php
/**
 * Template for the UM User Photos. The "Album" block
 *
 * Call: UM()->User_Photos()->ajax()->um_user_photos_load_more()
 * Call: UM()->User_Photos()->ajax()->get_single_album_view()
 * Page: "Profile", tab "Photos"
 * Parent template: photos.php
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/single-image.php
 * @var bool   $is_my_profile
 * @var int    $photo_id
 * @var string $thumbnail_image
 * @var string $img_link
 * @var array  $full_image
 * @var string $caption
 * @var string $img_title
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! $is_my_profile ) {
	?>
	<div class="um-user-photos-image-block um-up-cell">
		<?php if ( $img_link && esc_url( $img_link ) ) : ?>
			<div class="um-user-photos-image-block-buttons">
				<a href="<?php echo esc_url( $img_link ); ?>" title="<?php esc_attr_e( 'Related link', 'um-user-photos' ); ?>" target="_blank"><i class="um-faicon-link"></i></a>
			</div>
		<?php endif; ?>
		<div class="um-user-photos-image">
			<a data-caption="<?php echo esc_attr( $caption ); ?>" title="<?php echo esc_attr( $img_title ); ?>" href="<?php echo esc_url( $full_image[0] ); ?>" class="um-user-photos-image" data-id="<?php echo esc_attr( $photo_id ); ?>" data-wpnonce="<?php echo esc_attr( wp_create_nonce( 'um_user_photos_get_comment_section' ) ); ?>" data-umaction="open_modal">
				<img src="<?php echo esc_url( $thumbnail_image[0] ); ?>" alt="<?php echo esc_attr( $img_title ); ?>" />
			</a>
		</div>
	</div>
<?php } else { ?>
	<div class="um-user-photos-image-block um-user-photos-image-block-editable um-up-cell">
		<div class="um-user-photos-image-block-buttons">
			<?php if ( $img_link && esc_url( $img_link ) ) : ?>
				<a href="<?php echo esc_url( $img_link ); ?>" title="<?php esc_attr_e( 'Related link', 'um-user-photos' ); ?>" target="_blank"><i class="um-faicon-link"></i></a>
			<?php endif; ?>
			<a href="javascript:void(0);"
				data-modal_title="<?php esc_attr_e( 'Edit Image', 'um-user-photos' ); ?>"
				class="um-user-photos-edit-image"
				title="<?php esc_attr_e( 'Edit Image', 'um-user-photos' ); ?>"
				data-nonce="<?php echo esc_attr( wp_create_nonce( 'um_edit_image_modal' . $photo_id ) ); ?>"
				data-photo-id="<?php echo esc_attr( $photo_id ); ?>">
				<i class="um-faicon-pencil"></i>
			</a>
		</div>
		<div class="um-user-photos-image">
			<a data-caption="<?php echo esc_attr( $caption ); ?>"
			   data-id="<?php echo esc_attr( $photo_id ); ?>"
			   title="<?php echo esc_attr( $img_title ); ?>"
			   href="<?php echo esc_url( $full_image[0] ); ?>"
			   class="um-user-photos-image"
			   data-wpnonce="<?php echo esc_attr( wp_create_nonce( 'um_user_photos_get_comment_section' ) ); ?>"
			   data-umaction="open_modal">
				<img src="<?php echo esc_url( $thumbnail_image[0] ); ?>" alt="<?php echo esc_attr( $img_title ); ?>" />
			</a>
		</div>
	</div>
	<?php
}
