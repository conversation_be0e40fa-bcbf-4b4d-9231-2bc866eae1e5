.wpforms-conditional-block .wpforms-toggle-control {
  margin-bottom: 0;
}

.wpforms-conditional-block button {
  cursor: pointer;
}

.wpforms-conditional-block h4, .wpforms-conditional-block h5 {
  margin: 0 0 20px;
  font-size: 14px;
  font-style: italic;
  font-weight: 400;
  line-height: 17px;
}

.wpforms-conditional-block h5 {
  margin: 10px 0 20px;
}

.wpforms-conditional-block h4 select {
  display: inline-block;
  vertical-align: baseline;
  box-shadow: none;
  margin-inline-end: 10px !important;
  min-width: 81px;
  max-width: 81px;
}

.wpforms-conditional-block table {
  width: 100%;
  max-width: 1000px;
  padding: 0;
  margin: 0;
  border-collapse: collapse;
  border-spacing: 0;
  font-size: 13px;
}

.wpforms-conditional-block table td {
  padding: 0 10px 10px 0;
}

.wpforms-conditional-block .field {
  width: 35%;
  max-width: 35%;
}

.wpforms-conditional-block .operator {
  width: calc( 30% - 76px);
  min-width: 70px;
  max-width: 140px;
}

.wpforms-conditional-block .value {
  width: 35%;
  max-width: 250px;
}

.wpforms-conditional-block .value :disabled {
  background-color: #f1f1f1;
  cursor: not-allowed;
}

.wpforms-conditional-block .actions {
  width: 76px;
  min-width: 76px;
  max-width: 76px;
  padding-right: 0;
  display: flex;
  justify-content: space-between;
}

.wpforms-conditional-block .wpforms-conditional-row input,
.wpforms-conditional-block .wpforms-conditional-row select {
  width: 100% !important;
  max-width: 100%;
  margin: 0;
  text-overflow: ellipsis;
}

.wpforms-conditional-block .wpforms-conditional-rule-delete {
  background-color: transparent;
  border: none;
  color: #d63638;
  font-size: 16px;
  padding: 0 2px;
  height: 32px;
  vertical-align: middle;
}

.wpforms-conditional-block .wpforms-conditional-rule-delete:hover {
  color: #b32d2e;
}

.wpforms-panel-content .wpforms-conditional-block > .wpforms-panel-field {
  max-width: 100%;
}

.wpforms-panel-content .wpforms-conditional-block h4 select {
  min-width: 132px;
}

.wpforms-builder-settings-block .wpforms-conditional-groups {
  padding: 0 20px 20px 20px;
}

@media screen and (max-width: 960px) {
  .wpforms-builder-settings-block .wpforms-conditional-block .wpforms-conditional-row td {
    display: block;
    width: 100%;
    max-width: 100%;
  }
}

.rtl .wpforms-conditional-block table td {
  padding: 0 0 10px 10px;
}

.rtl .wpforms-conditional-block table td.actions {
  padding: 0;
}
