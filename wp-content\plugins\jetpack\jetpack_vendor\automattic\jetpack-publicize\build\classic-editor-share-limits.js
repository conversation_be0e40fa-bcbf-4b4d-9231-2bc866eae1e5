(()=>{"use strict";var e={428:e=>{e.exports=window.jQuery}},t={};function i(n){var r=t[n];if(void 0!==r)return r.exports;var s=t[n]={exports:{}};return e[n](s,s.exports,i),s.exports}i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var n=i(428);i.n(n)()((function(e){const t=window.jetpackSocialClassicEditorInitialState??{};if(!t||t.sharesRemaining>t.numberOfConnections)return;const i=e("#publicize-form").find('input[type="checkbox"]');if(0===t.sharesRemaining)return void i.each((function(){e(this).parent().addClass("wpas-disabled"),e(this).prop("disabled",!0)}));const n=e("#publicize-form");n.click((function(r){const s=e(r.target);if(!s.is("input")||s.is(":disabled"))return;const o=n.find('input[type="checkbox"]:checked').length>=t.sharesRemaining;i.each((function(){this.id!==s.attr("id")&&(this.checked||(e(this).parent().toggleClass("wpas-disabled",o),e(this).prop("disabled",o)))}))}))}))})();