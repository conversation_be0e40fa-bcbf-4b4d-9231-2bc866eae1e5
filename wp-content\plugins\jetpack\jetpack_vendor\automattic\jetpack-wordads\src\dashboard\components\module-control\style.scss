@import 'scss/variables';

$color-button-background: $black;
$color-button-text: $white;
$color-button-background-disabled: #dcdcde;
$color-button-text-disabled: #a7aaad;
$toggle-dot-radius: 0.9375em;
$toggle-dot-padding: 0.1875em;
$toggle-height: 1.5em;
$toggle-width: 3em;

.jp-form-wordads-settings-group {
	width: 100%;

	.dops-card {
		box-shadow: none;
		padding: 0;
		padding-top: 4em;
	}

	.form-toggle__label {
		margin: 0;
	}
}

.jp-form-wordads-settings-group__toggle {

	&.is-instant-search {
		margin-top: 4em;
	}

	.jp-form-wordads-settings-group__toggle-container {
		display: flex;

		@include for-tablet-down {
			justify-content: center;
		}
	}
}

.form-toggle__label-content {
	font-size: 1.5em;
	line-height: 1.167;
	font-weight: 600;

	span {
		font-weight: 400;
	}
}

.jp-form-wordads-settings-group__toggle-description {
	margin-top: 1em;
}

p.jp-form-wordads-settings-group__toggle-explanation {
	line-height: 1.5;
	font-size: 1em;
	font-weight: 400;
	margin-bottom: 0;
	margin-top: 0;
}

.jp-form-wordads-settings-group-buttons {
	margin-top: 1.5em;
}

.jp-form-wordads-settings-group-buttons__button {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 2.5em;
	padding: 0.5em 1.5em;
	text-align: center;

	border-color: $color-button-background;
	font-size: 1em;

	&.is-customize-search {
		color: $color-button-text;
		background-color: $color-button-background;
	}

	&:disabled,
	&[disabled] {
		background-color: $color-button-background-disabled;
		border-color: $color-button-background-disabled;
		color: $color-button-text-disabled;
		cursor: not-allowed;
	}

	&.is-widgets-editor {
		color: $color-button-background;
		background: transparent;

		&:disabled,
		&[disabled] {
			color: $color-button-text-disabled;
			background: transparent;
		}
	}
}

.form-toggle.is-wordads-admin.is-compact {

	+ .form-toggle__switch {
		border-radius: calc( #{$toggle-height}/ 2 );
		width: $toggle-width;
		height: $toggle-height;

		&::before,
		&::after {
			width: $toggle-dot-radius;
			height: $toggle-dot-radius;
			background-color: $black;
		}
		background: $white;
		border: 2px solid $black;

		&:focus {
			box-shadow: 0 0 0 2px $blue-medium;
		}
	}

	&:checked {

		+ .form-toggle__switch {
			background: $color-plan;
			border-color: $color-plan;

			&::after {
				left: $toggle-height;
				background-color: $white;
			}
		}
	}

	&.is-toggling + .form-toggle__switch::before,
	&.is-toggling + .form-toggle__switch::after {
		left: $toggle-height;
	}

	&.is-toggling:checked + .form-toggle__switch::before,
	&.is-toggling:checked + .form-toggle__switch::after {
		left: 0;
	}
}

.jp-wordads-dashboard-cut {
	position: relative;
	display: block;
	margin: 2em 0;
	padding: 1em 4em 1em 1.5em;
	border: 2px solid $jp-green-primary;
	border-radius: $jp-border-radius;
	text-decoration: none;

	span {
		display: block;

		&:last-of-type {
			font-weight: 600;
		}
	}

	&:hover,
	&:focus {

		span:last-of-type {
			text-decoration: underline;
			text-decoration-thickness: $jp-underline-thickness;
		}

		&::after {
			transform: translateY( -50% ) translateX( 8px );
		}
	}

	&::after {
		content: '→';
		position: absolute;
		top: 50%;
		right: 1.5em;
		font-size: 1.5em;
		font-weight: 600;
		color: $jp-green-primary;
		transform: translateY( -50% );
		transition: transform 0.15s ease-out;
	}
}

.jp-wordads-dashboard-row .form-toggle__switch-container.sm-col-span-1 {
	display: flex;
	justify-content: center;

	padding-top: 4px;
}
