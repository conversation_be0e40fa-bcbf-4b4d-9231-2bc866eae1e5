<?php
/**
 * Template for the User Photos
 *
 * Page: "Account", tab "My Photos"
 * Caller: UM()->User_Photos()->account()->um_account_content_hook_um_user_photos()
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/account.php
 * @var int  $user_id
 * @var int  $count_user_photos
 * @var bool $download_my_photos_notice
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
// @todo make ZIP file prepare handler. After that we can show the download link. This download link will be expired in 12 hours.
$nonce_download = esc_attr( wp_create_nonce( 'um_user_photos_download_all' ) );
$nonce_delete   = esc_attr( wp_create_nonce( 'um_user_photos_delete_all' ) );
?>

<div class="um_user_photos_account">
	<?php
	echo wp_kses(
		UM()->frontend()::layouts()::link(
			esc_html__( 'Download my photos', 'um-user-photos' ),
			array(
				'id'     => 'um_user_photos_download_all',
				'type'   => 'button',
				'design' => 'primary',
				'size'   => 'm',
				'url'    => admin_url( "admin-ajax.php?action=download_my_photos&_wpnonce=$nonce_download" ),
			)
		),
		UM()->get_allowed_html( 'templates' )
	);
	if ( $download_my_photos_notice ) {
		?>
		<div class="um-field-error">
			<span class="um-field-arrow"><i class="um-faicon-caret-up"></i></span><?php echo esc_html( $download_my_photos_notice ); ?>
		</div>
		<?php
	}
	?>
	<div class="um-user-photos-delete-wrap">
		<?php
		$loader = UM()->frontend()::layouts()::ajax_loader( 'm', array( 'classes' => array( 'um-user-photos-loader', 'um-display-none' ) ) );
		$button = UM()->frontend()::layouts()::button(
			__( 'Delete all my albums & photos', 'um-user-photos' ),
			array(
				'id'     => 'um_user_photos_delete_all',
				'type'   => 'button',
				'design' => 'primary-destructive',
				'size'   => 'm',
				'data'   => array(
					'nonce'         => $nonce_delete,
					'redirect'      => um_get_predefined_page_url( 'account' ),
					'alert_message' => __( 'Are you sure to delete all your albums & photos?', 'um-user-photos' ),
				),
			)
		);
		echo wp_kses( $button . $loader, UM()->get_allowed_html( 'templates' ) );
		?>
	</div>
</div>
