<?php

declare (strict_types=1);
namespace WPForms\Vendor\Square\Models;

use stdClass;
/**
 * Searches [Event]($m/Event)s for your application.
 */
class SearchEventsRequest implements \JsonSerializable
{
    /**
     * @var string|null
     */
    private $cursor;
    /**
     * @var int|null
     */
    private $limit;
    /**
     * @var SearchEventsQuery|null
     */
    private $query;
    /**
     * Returns Cursor.
     * A pagination cursor returned by a previous call to this endpoint. Provide this cursor to retrieve
     * the next set of events for your original query.
     *
     * For more information, see [Pagination](https://developer.squareup.com/docs/build-basics/common-api-
     * patterns/pagination).
     */
    public function getCursor() : ?string
    {
        return $this->cursor;
    }
    /**
     * Sets Cursor.
     * A pagination cursor returned by a previous call to this endpoint. Provide this cursor to retrieve
     * the next set of events for your original query.
     *
     * For more information, see [Pagination](https://developer.squareup.com/docs/build-basics/common-api-
     * patterns/pagination).
     *
     * @maps cursor
     */
    public function setCursor(?string $cursor) : void
    {
        $this->cursor = $cursor;
    }
    /**
     * Returns Limit.
     * The maximum number of events to return in a single page. The response might contain fewer events.
     * The default value is 100, which is also the maximum allowed value.
     *
     * For more information, see [Pagination](https://developer.squareup.com/docs/build-basics/common-api-
     * patterns/pagination).
     *
     * Default: 100
     */
    public function getLimit() : ?int
    {
        return $this->limit;
    }
    /**
     * Sets Limit.
     * The maximum number of events to return in a single page. The response might contain fewer events.
     * The default value is 100, which is also the maximum allowed value.
     *
     * For more information, see [Pagination](https://developer.squareup.com/docs/build-basics/common-api-
     * patterns/pagination).
     *
     * Default: 100
     *
     * @maps limit
     */
    public function setLimit(?int $limit) : void
    {
        $this->limit = $limit;
    }
    /**
     * Returns Query.
     * Contains query criteria for the search.
     */
    public function getQuery() : ?SearchEventsQuery
    {
        return $this->query;
    }
    /**
     * Sets Query.
     * Contains query criteria for the search.
     *
     * @maps query
     */
    public function setQuery(?SearchEventsQuery $query) : void
    {
        $this->query = $query;
    }
    /**
     * Encode this object to JSON
     *
     * @param bool $asArrayWhenEmpty Whether to serialize this model as an array whenever no fields
     *        are set. (default: false)
     *
     * @return array|stdClass
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize(bool $asArrayWhenEmpty = \false)
    {
        $json = [];
        if (isset($this->cursor)) {
            $json['cursor'] = $this->cursor;
        }
        if (isset($this->limit)) {
            $json['limit'] = $this->limit;
        }
        if (isset($this->query)) {
            $json['query'] = $this->query;
        }
        $json = \array_filter($json, function ($val) {
            return $val !== null;
        });
        return !$asArrayWhenEmpty && empty($json) ? new stdClass() : $json;
    }
}
