@echo off
echo ===== FIXING VIRTUALHOST ORDER ISSUE =====
echo.

echo Checking administrator privileges...
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click on the batch file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)
echo Administrator privileges confirmed.
echo.

echo 1. Backing up current VirtualHost configuration...
copy "C:\xampp\apache\conf\extra\httpd-vhosts.conf" "C:\xampp\apache\conf\extra\httpd-vhosts.conf.backup2" > nul
echo Backup created: httpd-vhosts.conf.backup2

echo.
echo 2. Creating new VirtualHost configuration with correct order...
(
echo # Virtual Hosts
echo #
echo # Required modules: mod_log_config
echo.
echo # If you want to maintain multiple domains/hostnames on your
echo # machine you can setup VirtualHost containers for them. Most configurations
echo # use only name-based virtual hosts so the server doesn't need to worry about
echo # IP addresses. This is indicated by the asterisks in the directives below.
echo #
echo # Please see the documentation at 
echo # ^<URL:http://httpd.apache.org/docs/2.4/vhosts/^>
echo # for further details before you try to setup virtual hosts.
echo #
echo # You may use the command line option '-S' to verify your virtual host
echo # configuration.
echo.
echo #
echo # Use name-based virtual hosting.
echo #
echo ##NameVirtualHost *:80
echo #
echo # VirtualHost example:
echo # Almost any Apache directive may go into a VirtualHost container.
echo # The first VirtualHost section is used for all requests that do not
echo # match a ##ServerName or ##ServerAlias in any ^<VirtualHost^> block.
echo #
echo ##^<VirtualHost *:80^>
echo     ##ServerAdmin <EMAIL>
echo     ##DocumentRoot "C:/xampp/htdocs/dummy-host.example.com"
echo     ##ServerName dummy-host.example.com
echo     ##ServerAlias www.dummy-host.example.com
echo     ##ErrorLog "logs/dummy-host.example.com-error.log"
echo     ##CustomLog "logs/dummy-host.example.com-access.log" common
echo ##^</VirtualHost^>
echo.
echo ##^<VirtualHost *:80^>
echo     ##ServerAdmin <EMAIL>
echo     ##DocumentRoot "C:/xampp/htdocs/dummy-host2.example.com"
echo     ##ServerName dummy-host2.example.com
echo     ##ErrorLog "logs/dummy-host2.example.com-error.log"
echo     ##CustomLog "logs/dummy-host2.example.com-access.log" common
echo ##^</VirtualHost^>
echo.
echo # Default VirtualHost for localhost - MUST BE FIRST
echo ^<VirtualHost *:80^>
echo     DocumentRoot "C:/xampp/htdocs"
echo     ServerName localhost
echo     ServerAlias 127.0.0.1
echo ^</VirtualHost^>
echo.
echo ^<VirtualHost *:80^>
echo     ServerName like_excel.local
echo     DocumentRoot "C:/xampp/htdocs/like_excel/public"
echo     ^<Directory "C:/xampp/htdocs/like_excel/public"^>
echo         AllowOverride All
echo         Require all granted
echo     ^</Directory^>
echo ^</VirtualHost^>
echo.
echo ^<VirtualHost *:80^>
echo     ServerName rakib.local
echo     DocumentRoot "C:/xampp/htdocs/arakib_app/public"
echo     ^<Directory "C:/xampp/htdocs/arakib_app/public"^>
echo         AllowOverride All
echo         Require all granted
echo     ^</Directory^>
echo ^</VirtualHost^>
echo.
echo ^<VirtualHost *:80^>
echo     ServerName wp.local
echo     DocumentRoot "C:/xampp/htdocs/wordpress"
echo     ^<Directory "C:/xampp/htdocs/wordpress"^>
echo         AllowOverride All
echo         Require all granted
echo     ^</Directory^>
echo ^</VirtualHost^>
echo.
echo ^<VirtualHost *:80^>
echo     ServerName raezg.local
echo     ServerAlias www.raezg.local
echo     DocumentRoot "C:/xampp/htdocs/raezg"
echo     ErrorLog "logs/raezg.local-error.log"
echo     CustomLog "logs/raezg.local-access.log" common
echo     ^<Directory "C:/xampp/htdocs/raezg"^>
echo         Options Indexes FollowSymLinks
echo         AllowOverride All
echo         Require all granted
echo     ^</Directory^>
echo ^</VirtualHost^>
echo.
) > C:\xampp\apache\conf\extra\httpd-vhosts.conf

echo New VirtualHost configuration created with localhost as default.

echo.
echo 3. Testing Apache configuration...
C:\xampp\apache\bin\httpd.exe -t
if %errorlevel% neq 0 (
    echo ERROR: Apache configuration test failed!
    echo Restoring backup...
    copy "C:\xampp\apache\conf\extra\httpd-vhosts.conf.backup2" "C:\xampp\apache\conf\extra\httpd-vhosts.conf" > nul
    echo Configuration restored. Please check the Apache error log.
    pause
    exit /b 1
) else (
    echo Apache configuration test passed!
)

echo.
echo 4. Restarting Apache...
echo Stopping Apache processes...
taskkill /F /IM httpd.exe /T > nul 2>&1
timeout /t 3 > nul

echo Starting Apache...
net start Apache2.4 > nul 2>&1
if %errorlevel% neq 0 (
    net start Apache > nul 2>&1
    if %errorlevel% neq 0 (
        echo Failed to start Apache service. Starting manually...
        start /B C:\xampp\apache\bin\httpd.exe
        timeout /t 3 > nul
    )
)

echo.
echo 5. Verifying new VirtualHost order...
C:\xampp\apache\bin\httpd.exe -S | findstr "default server\|namevhost"

echo.
echo ===== VIRTUALHOST ORDER FIX COMPLETE =====
echo.
echo Now try accessing http://raezg.local/ in your browser.
echo.
pause
