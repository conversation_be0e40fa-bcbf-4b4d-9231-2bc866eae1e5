(()=>{var e={6941:(e,n,t)=>{n.formatArgs=function(n){if(n[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+n[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const t="color: "+this.color;n.splice(1,0,t,"color: inherit");let r=0,s=0;n[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(r++,"%c"===e&&(s=r))})),n.splice(s,0,t)},n.save=function(e){try{e?n.storage.setItem("debug",e):n.storage.removeItem("debug")}catch(e){}},n.load=function(){let e;try{e=n.storage.getItem("debug")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},n.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},n.storage=function(){try{return localStorage}catch(e){}}(),n.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),n.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],n.log=console.debug||console.log||(()=>{}),e.exports=t(3212)(n);const{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},3212:(e,n,t)=>{e.exports=function(e){function n(e){let t,s,o,a=null;function i(...e){if(!i.enabled)return;const r=i,s=Number(new Date),o=s-(t||s);r.diff=o,r.prev=t,r.curr=s,t=s,e[0]=n.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let a=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((t,s)=>{if("%%"===t)return"%";a++;const o=n.formatters[s];if("function"==typeof o){const n=e[a];t=o.call(r,n),e.splice(a,1),a--}return t})),n.formatArgs.call(r,e);(r.log||n.log).apply(r,e)}return i.namespace=e,i.useColors=n.useColors(),i.color=n.selectColor(e),i.extend=r,i.destroy=n.destroy,Object.defineProperty(i,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==a?a:(s!==n.namespaces&&(s=n.namespaces,o=n.enabled(e)),o),set:e=>{a=e}}),"function"==typeof n.init&&n.init(i),i}function r(e,t){const r=n(this.namespace+(void 0===t?":":t)+e);return r.log=this.log,r}function s(e,n){let t=0,r=0,s=-1,o=0;for(;t<e.length;)if(r<n.length&&(n[r]===e[t]||"*"===n[r]))"*"===n[r]?(s=r,o=t,r++):(t++,r++);else{if(-1===s)return!1;r=s+1,o++,t=o}for(;r<n.length&&"*"===n[r];)r++;return r===n.length}return n.debug=n,n.default=n,n.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},n.disable=function(){const e=[...n.names,...n.skips.map((e=>"-"+e))].join(",");return n.enable(""),e},n.enable=function(e){n.save(e),n.namespaces=e,n.names=[],n.skips=[];const t=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of t)"-"===e[0]?n.skips.push(e.slice(1)):n.names.push(e)},n.enabled=function(e){for(const t of n.skips)if(s(e,t))return!1;for(const t of n.names)if(s(e,t))return!0;return!1},n.humanize=t(7378),n.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((t=>{n[t]=e[t]})),n.names=[],n.skips=[],n.formatters={},n.selectColor=function(e){let t=0;for(let n=0;n<e.length;n++)t=(t<<5)-t+e.charCodeAt(n),t|=0;return n.colors[Math.abs(t)%n.colors.length]},n.enable(n.load()),n}},7378:e=>{var n=1e3,t=60*n,r=60*t,s=24*r,o=7*s,a=365.25*s;function i(e,n,t,r){var s=n>=1.5*t;return Math.round(e/t)+" "+r+(s?"s":"")}e.exports=function(e,c){c=c||{};var u=typeof e;if("string"===u&&e.length>0)return function(e){if((e=String(e)).length>100)return;var i=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!i)return;var c=parseFloat(i[1]);switch((i[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return c*a;case"weeks":case"week":case"w":return c*o;case"days":case"day":case"d":return c*s;case"hours":case"hour":case"hrs":case"hr":case"h":return c*r;case"minutes":case"minute":case"mins":case"min":case"m":return c*t;case"seconds":case"second":case"secs":case"sec":case"s":return c*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}(e);if("number"===u&&isFinite(e))return c.long?function(e){var o=Math.abs(e);if(o>=s)return i(e,o,s,"day");if(o>=r)return i(e,o,r,"hour");if(o>=t)return i(e,o,t,"minute");if(o>=n)return i(e,o,n,"second");return e+" ms"}(e):function(e){var o=Math.abs(e);if(o>=s)return Math.round(e/s)+"d";if(o>=r)return Math.round(e/r)+"h";if(o>=t)return Math.round(e/t)+"m";if(o>=n)return Math.round(e/n)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}}},n={};function t(r){var s=n[r];if(void 0!==s)return s.exports;var o=n[r]={exports:{}};return e[r](o,o.exports,t),o.exports}t.n=e=>{var n=e&&e.__esModule?()=>e.default:()=>e;return t.d(n,{a:n}),n},t.d=(e,n)=>{for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},t.o=(e,n)=>Object.prototype.hasOwnProperty.call(e,n),(()=>{"use strict";var e=t(6941);const n=t.n(e)()("videopress:player-bridge"),r=["videopress_playing","videopress_pause","videopress_seeking","videopress_resize","videopress_volumechange","videopress_ended","videopress_timeupdate","videopress_durationchange","videopress_progress","videopress_loading_state","videopress_toggle_fullscreen"],s=["videopress_action_play","videopress_action_pause","videopress_action_set_currenttime","videopress_action_set_volume"];async function o(e){const{data:t={event:null}}=e||{},{event:o}=t;if(r.includes(o)){-1!==["https://videopress.com","https://video.wordpress.com"].indexOf(e.origin)&&(n("broadcast %o event: %o",o,t),window.top.postMessage(e.data,"*"))}if(s.includes(o)){const e=document.querySelector("iframe"),r=e?.contentWindow;if(!r)return;n("emit %o event - %o",o,t),r.postMessage(t,"*")}}window.addEventListener("message",o)})()})();