.choices {
  position: relative;
  margin-bottom: 24px;
}

.choices ::-webkit-input-placeholder {
  color: #999999;
}

.choices ::-moz-placeholder {
  color: #999999;
  opacity: 1;
}

.choices ::placeholder {
  color: #999999;
}

.choices:focus {
  outline: none;
}

.choices:last-child {
  margin-bottom: 0;
}

.choices.is-disabled .choices__inner,
.choices.is-disabled .choices__input {
  background-color: #bbbbbb;
  cursor: not-allowed;
  user-select: none;
}

.choices [hidden] {
  display: none !important;
}

.choices * {
  box-sizing: border-box;
}

.choices.is-open .choices__inner {
  border-radius: 4px 4px 0 0;
}

.choices.is-open.is-flipped .choices__inner {
  border-radius: 0 0 4px 4px;
}

.choices[data-type*="select-one"] {
  cursor: pointer;
}

.choices[data-type*="select-one"] .choices__inner {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding-top: 0 !important;
}

.choices[data-type*="select-one"] input.choices__input {
  display: block;
  width: calc(100% - 20px) !important;
  margin: 10px !important;
  padding: 7px 12px !important;
  box-sizing: border-box !important;
  border: 1px solid #8c8f94 !important;
  border-radius: 4px !important;
  background-color: #fff;
}

.choices[data-type*="select-one"] input.choices__input:focus {
  border: 1px solid #056aab !important;
  box-shadow: 0 0 0 1px #056aab !important;
  outline: none !important;
}

.choices[data-type*="select-one"] .choices__button {
  background-image: url("../../images/cross-inverse.svg");
  padding: 0;
  background-size: 8px;
  position: absolute;
  top: 50%;
  right: 0;
  margin-top: -10px;
  margin-right: 25px;
  height: 20px;
  width: 20px;
  border-radius: 10em;
  opacity: .5;
}

.choices[data-type*="select-one"] .choices__button:hover, .choices[data-type*="select-one"] .choices__button:focus {
  opacity: 1;
}

.choices[data-type*="select-one"] .choices__button:focus {
  box-shadow: 0 0 0 2px #036aab;
}

.choices[data-type*="select-one"] .choices__item[data-value=''] .choices__button {
  display: none;
}

.choices[data-type*="select-one"]:after {
  content: "";
  height: 0;
  width: 0;
  border-style: solid;
  border-color: currentColor transparent transparent transparent;
  border-width: 5px;
  position: absolute;
  inset-inline-end: 11.5px;
  top: 50%;
  margin-top: -2.5px;
  pointer-events: none;
}

.choices[data-type*="select-one"].is-open:after {
  border-color: transparent transparent currentColor transparent;
  margin-top: -7.5px;
}

.choices[data-type*="select-one"][dir="rtl"]:after {
  left: 11.5px;
  right: auto;
}

.choices[data-type*="select-one"][dir="rtl"] .choices__button {
  right: auto;
  left: 0;
  margin-left: 25px;
  margin-right: 0;
}

.choices[data-type*="select-multiple"] .choices__inner {
  padding-right: 24px;
}

.choices[data-type*="select-multiple"] .choices__inner .choices__input {
  padding: 0 4px !important;
  max-width: 100%;
  background-color: transparent;
  line-height: 22px;
}

.choices[data-type*="select-multiple"]:after {
  content: "";
  height: 0;
  width: 0;
  border-style: solid;
  border-color: currentColor transparent transparent transparent;
  border-width: 5px;
  position: absolute;
  inset-inline-end: 11.5px;
  top: 50%;
  margin-top: -1.5px;
  pointer-events: none;
}

.choices[data-type*="select-multiple"].is-open:after {
  border-color: transparent transparent currentColor transparent;
  margin-top: -7.5px;
}

.choices[data-type*="select-multiple"] .choices__inner,
.choices[data-type*="text"] .choices__inner {
  cursor: text;
}

.choices[data-type*="select-multiple"] .choices__button,
.choices[data-type*="text"] .choices__button {
  position: absolute;
  display: inline-block;
  vertical-align: baseline;
  margin-top: 0;
  margin-bottom: 0;
  margin-inline-start: 5px;
  padding: 0;
  background-color: transparent;
  background-image: url("../../images/cross.svg");
  background-size: 12px;
  background-position: center center;
  background-repeat: no-repeat;
  width: 12px;
  height: 12px;
  line-height: 1;
  opacity: .75;
  border-radius: 0;
  inset-inline-end: 4px;
}

.choices[data-type*="select-multiple"] .choices__button:hover, .choices[data-type*="select-multiple"] .choices__button:focus,
.choices[data-type*="text"] .choices__button:hover,
.choices[data-type*="text"] .choices__button:focus {
  opacity: 1;
}

.choices__inner {
  width: 100%;
  background-color: #ffffff;
  padding: 4px 6px 0;
  border: 1px solid #8c8f94;
  overflow: hidden;
  border-radius: 4px;
}

.choices__list {
  margin: 0;
  padding-left: 0;
  list-style: none;
}

.choices__list--single {
  display: inline-block;
  vertical-align: baseline;
  width: 100%;
  padding: 0 16px 0 4px;
  font-size: 0.875em;
}

.choices__list--single .choices__item {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 20px;
  white-space: nowrap;
  color: #2c3338;
}

.choices__list--single .choices__item[data-value=''] {
  padding-right: 0;
}

.choices__list--multiple {
  display: inline;
  height: auto;
  overflow: auto;
}

.choices__list--multiple .choices__item {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  align-items: center;
  border-radius: 2px;
  padding-block: 4px;
  padding-inline: 7px 20px;
  font-size: .75em;
  line-height: 1;
  font-weight: 400;
  margin: 0 6px 4px 0;
  background-color: #036aab;
  border: 1px solid #036aab;
  color: #ffffff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100% - 10px);
}

.choices__list--multiple .choices__item.is-highlighted {
  background-color: #036aab;
}

.is-disabled .choices__list--multiple .choices__item {
  background-color: #bbbbbb;
  border: 1px solid #bbbbbb;
}

.choices__list--dropdown {
  display: none;
  z-index: 101;
  position: absolute;
  width: 100%;
  background-color: #ffffff;
  border: 1px solid #8c8f94;
  top: 100%;
  margin-top: -1px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  overflow: hidden;
  overflow-wrap: break-word;
}

.choices__list--dropdown.is-active {
  display: block;
}

.choices__list--dropdown .choices__list {
  position: relative;
  max-height: 300px;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  will-change: scroll-position;
}

.choices__list--dropdown .choices__item {
  position: relative;
  vertical-align: top;
  padding: 10px;
  font-size: .875em;
}

.choices__list--dropdown .choices__item--selectable.is-highlighted {
  background-color: #f6f6f6;
}

.choices__list--dropdown .choices__item--selectable.is-highlighted:after {
  opacity: .5;
}

.choices__list--dropdown .choices__placeholder {
  display: none;
}

.is-flipped .choices__list--dropdown {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: -1px;
  border-radius: 4px 4px 0 0;
}

.choices__item {
  cursor: default;
}

.choices__item--selectable {
  cursor: pointer;
}

.choices__item--disabled {
  cursor: not-allowed;
  user-select: none;
  opacity: .5;
}

.choices__heading {
  font-weight: 600;
  font-size: .75em;
  text-transform: uppercase;
  padding: 10px;
  border-top: 1px solid #b4b6b9;
  border-bottom: 1px solid #b4b6b9;
  color: #a6a6a6;
}

.choices__group[data-value="hidden"] > .choices__heading {
  display: none;
}

.choices__button {
  text-indent: -9999px;
  -webkit-appearance: none;
  appearance: none;
  border: 0;
  background-color: transparent;
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
}

.choices__button:focus {
  outline: none;
}

.choices__input {
  display: inline-block;
  background-color: transparent;
  margin: 0 0 2px 0 !important;
  border: 0 !important;
  border-radius: 0 !important;
  min-height: 20px !important;
  padding: 2px 4px !important;
  height: auto !important;
  min-width: 1ch;
  width: 1ch;
  vertical-align: middle;
}

.choices__input::-webkit-search-cancel-button {
  display: none;
}

.choices__input--hidden {
  clip: rect(1px, 1px, 1px, 1px) !important;
  clip-path: inset(50%) !important;
  height: 1px !important;
  margin: -1px !important;
  overflow: hidden !important;
  padding: 0 !important;
  position: absolute !important;
  width: 1px !important;
  min-width: auto !important;
  word-wrap: normal !important;
}

.choices .choices__inner input.choices__input:focus {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}

.choices__placeholder {
  opacity: .5;
}

#wpforms-admin-form-embed-wizard .choices.is-open.is-flipped .choices__inner {
  border-radius: 4px 4px 0 0;
}

#wpforms-admin-form-embed-wizard .is-flipped .choices__list--dropdown {
  border-radius: inherit;
}

#wpforms-admin-form-embed-wizard .choices[data-type*="select-one"]:after {
  border: none;
  background: #ffffff url(data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M5%206l5%205%205-5%202%201-7%207-7-7%202-1z%22%20fill%3D%22%23777%22%2F%3E%3C%2Fsvg%3E) no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  width: 16px;
  height: 16px;
  top: 13px;
  right: 8px;
  margin-top: 0;
}

#wpforms-admin-form-embed-wizard .choices[data-type*="select-one"].is-flipped:after {
  transform: rotate(180deg);
}

body.rtl .choices[data-type*="select-multiple"] .choices__inner {
  padding-right: 4px;
  padding-left: 24px;
}

body.rtl .choices__list--single {
  padding-right: 4px;
  padding-left: 16px;
}

body.rtl .choices__list--multiple .choices__item {
  margin-right: 0;
  margin-left: 3.75px;
}

body.rtl .choices__list--dropdown .choices__item {
  text-align: right;
}

body.rtl .choices__input {
  padding-right: 2px !important;
  padding-left: 0 !important;
}

body.rtl .choices[data-type*="select-multiple"] .choices__button, body.rtl .choices[data-type*="text"] .choices__button {
  margin-inline-end: 0;
  border-left: none;
}

@media (min-width: 640px) {
  body.rtl .choices__list--dropdown .choices__item--selectable {
    text-align: right;
    padding-left: 100px;
    padding-right: 10px;
  }
  body.rtl .choices__list--dropdown .choices__item--selectable:after {
    right: auto;
    left: 10px;
  }
}

.wpforms-builder-provider .choices.is-flipped .choices__inner {
  border-radius: 0 0 4px 4px !important;
}

.wpforms-builder-provider .choices.is-flipped .choices__list--dropdown.is-active {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.wpforms-builder-provider .choices.is-open .choices__inner {
  border: 1px solid #036aab;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  background: #ffffff url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTMiIGhlaWdodD0iNyIgdmlld0JveD0iMCAwIDEzIDciIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xMi4wMDM0IDYuMDAxNzJDMTIuMDAzNCA2LjA5Nzk0IDExLjk1NTMgNi4yMDYxOSAxMS44ODMyIDYuMjc4MzVMMTEuMjgxOCA2Ljg3OTczQzExLjIwOTYgNi45NTE4OSAxMS4xMDE0IDcgMTEuMDA1MiA3QzEwLjkwODkgNyAxMC44MDA3IDYuOTUxODkgMTAuNzI4NSA2Ljg3OTczTDYuMDAxNzIgMi4xNTI5MkwxLjI3NDkxIDYuODc5NzNDMS4yMDI3NSA2Ljk1MTg5IDEuMDk0NSA3IDAuOTk4MjgyIDdDMC44OTAwMzQgNyAwLjc5MzgxNCA2Ljk1MTg5IDAuNzIxNjUgNi44Nzk3M0wwLjEyMDI3NSA2LjI3ODM1QzAuMDQ4MTA5OSA2LjIwNjE5IDAgNi4wOTc5NCAwIDYuMDAxNzJDMCA1LjkwNTUgMC4wNDgxMDk5IDUuNzk3MjUgMC4xMjAyNzUgNS43MjUwOUw1LjcyNTA5IDAuMTIwMjc1QzUuNzk3MjUgMC4wNDgxMDk1IDUuOTA1NSAwIDYuMDAxNzIgMEM2LjA5Nzk0IDAgNi4yMDYxOSAwLjA0ODEwOTUgNi4yNzgzNSAwLjEyMDI3NUwxMS44ODMyIDUuNzI1MDlDMTEuOTU1MyA1Ljc5NzI1IDEyLjAwMzQgNS45MDU1IDEyLjAwMzQgNi4wMDE3MloiIGZpbGw9IiM3Nzc3NzciLz4KPC9zdmc+Cg==") no-repeat right 5px top 55%;
}

.wpforms-builder-provider .choices.is-focused .choices__inner {
  border-color: #056aab;
  box-shadow: 0 0 0 1px #056aab;
}

.wpforms-builder-provider .choices .choices__inner {
  background: #ffffff url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M5%206l5%205%205-5%202%201-7%207-7-7%202-1z%22%20fill%3D%22%23777%22%2F%3E%3C%2Fsvg%3E") no-repeat right 5px top 55%;
  background-size: 16px 16px;
}

.wpforms-builder-provider .choices .choices__inner .choices__list--multiple {
  display: inline !important;
}

.wpforms-builder-provider .choices .choices__inner:has(.wpforms-error) {
  border: 1px solid #d63638;
}

.wpforms-builder-provider .choices.is-open:has(.wpforms-error) .choices__inner,
.wpforms-builder-provider .choices.is-open:has(.wpforms-error) .choices__list--dropdown, .wpforms-builder-provider .choices.is-focused:has(.wpforms-error) .choices__inner,
.wpforms-builder-provider .choices.is-focused:has(.wpforms-error) .choices__list--dropdown {
  border-color: #d63638;
  box-shadow: 0 0 0 1px #d63638;
}

.wpforms-builder-provider .choices.is-open:has(.wpforms-error) .choices__list--dropdown, .wpforms-builder-provider .choices.is-focused:has(.wpforms-error) .choices__list--dropdown {
  box-shadow: 0 1px 0 1px #d63638;
}

.wpforms-builder-provider .choices.is-open.is-flipped:has(.wpforms-error) .choices__list--dropdown, .wpforms-builder-provider .choices.is-focused.is-flipped:has(.wpforms-error) .choices__list--dropdown {
  box-shadow: 0 -1px 0 1px #d63638;
}

.wpforms-builder-provider .choices[data-type*="select-one"]:has(.wpforms-error) input.choices__input {
  border: 1px solid #d63638 !important;
}

.wpforms-builder-provider .choices[data-type*="select-one"]:has(.wpforms-error) input.choices__input:focus {
  box-shadow: 0 0 0 1px #d63638 !important;
}

.wpforms-builder-provider .choices .choices__list--dropdown {
  transform: translate3d(0, 0, 0);
}

.wpforms-builder-provider .choices .choices__list--dropdown .choices__item {
  color: #777777;
}

.wpforms-builder-provider .choices[data-type*="text"] .choices__button,
.wpforms-builder-provider .choices[data-type*="select-multiple"] .choices__button {
  background-image: url("../../images/cross.svg");
  background-size: 12px;
  width: 12px;
  border: none;
}

.wpforms-builder-provider .choices[data-type*="text"]:after, .wpforms-builder-provider .choices[data-type*="select-one"]:after, .wpforms-builder-provider .choices[data-type*="select-multiple"]:after {
  content: none;
}

.wpforms-builder-provider .choices[data-type*="select-one"] input.choices__input {
  padding: 5px 12px !important;
}

.wpforms-builder-provider .choices[data-type*="select-one"] .choices__inner .choices__item {
  font-size: 14px;
  margin-bottom: 0;
  margin-top: 2px;
}

.wpforms-panel-fields .choices[data-type*="select-multiple"]:after,
.wpforms-panel-fields .choices[data-type*="select-one"]:after {
  content: none;
}

.wpforms-panel-fields .choices__inner {
  background: #ffffff url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M5%206l5%205%205-5%202%201-7%207-7-7%202-1z%22%20fill%3D%22%23777%22%2F%3E%3C%2Fsvg%3E") no-repeat right 5px top 55%;
  background-size: 16px 16px;
}

.rtl .wpforms-panel-fields .choices__inner {
  background-position: left 5px top 55%;
}

.wpforms-panel-fields .choices__list--single {
  font-size: inherit;
}

.wpforms-panel-fields .choices-list {
  margin-bottom: 0;
}

.wpforms-panel-fields .choices-list li {
  border-bottom: 1px solid #ced7e0;
  margin: 0;
  padding: 10px 0;
}

.wpforms-panel-fields .choices-list li:first-of-type {
  padding-top: 1px;
}

.wpforms-panel-fields .choices-list li:last-of-type {
  border: 0;
  padding-bottom: 1px;
}

.wpforms-panel-fields .choices-list li i {
  display: inline-block;
  font-size: 16px;
  margin-block: 0;
  margin-inline-end: 0;
  margin-inline-start: 10px;
}

.wpforms-panel-fields .choices-list li input[type=text] {
  display: inline-block;
  margin-block: 0;
  margin-inline-end: 0;
  margin-inline-start: 10px;
  width: calc(100% - 102px);
}

.wpforms-panel-fields .choices-list li input[type=text].value {
  display: none;
  margin-inline-end: 48px;
  margin-inline-start: 54px;
  margin-top: 10px;
}

.wpforms-panel-fields .choices-list .move i {
  color: #86919e;
  font-size: 20px;
  position: relative;
  top: 1px;
}

.wpforms-panel-fields .choices-list .move:hover {
  cursor: pointer;
}

.wpforms-panel-fields .choices-list .add:hover {
  color: #215d8f;
}

.wpforms-panel-fields .choices-list .remove {
  color: #d63638;
}

.wpforms-panel-fields .choices-list .remove:hover {
  color: #b32d2e;
}

.wpforms-panel-fields .wpforms-field-option-row-choices .show-values li input[type=text].value {
  display: block;
}

.wpforms-panel-fields .wpforms-field-option-row-choices .wpforms-alert {
  margin-bottom: 4px;
}

.wpforms-panel-fields .wpforms-field-option-payment-multiple li input[type=text],
.wpforms-panel-fields .wpforms-field-option-payment-checkbox li input[type=text],
.wpforms-panel-fields .wpforms-field-option-payment-select li input[type=text] {
  width: calc(100% - 185px);
}

.wpforms-panel-fields .wpforms-field-option-payment-multiple li input[type=text].value,
.wpforms-panel-fields .wpforms-field-option-payment-checkbox li input[type=text].value,
.wpforms-panel-fields .wpforms-field-option-payment-select li input[type=text].value {
  display: inline-block;
  margin-block: 0;
  margin-inline-end: 0;
  margin-inline-start: 10px;
  width: 73px;
}

.wpforms-panel-fields ul.primary-input {
  margin-bottom: 0;
}

.wpforms-panel-fields .wpforms-list-2-columns ul,
.wpforms-panel-fields .wpforms-list-3-columns ul {
  display: flex;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
  justify-content: flex-start;
  margin-bottom: -15px;
}

.wpforms-panel-fields .wpforms-list-2-columns ul li {
  display: block;
  margin-right: 15px;
  margin-bottom: 15px;
  width: calc( 50% - 7.5px);
}

.wpforms-panel-fields .wpforms-list-2-columns ul li:nth-child(2n) {
  margin-right: 0;
  padding-right: 0;
}

.wpforms-panel-fields .wpforms-list-3-columns ul li {
  display: block;
  margin-right: 15px;
  margin-bottom: 15px;
  width: calc( 100% / 3 - 10px);
}

.wpforms-panel-fields .wpforms-list-3-columns ul li:nth-child(3n+3) {
  margin-right: 0;
  padding-right: 0;
}

.wpforms-panel-fields .wpforms-list-inline ul {
  margin-bottom: -15px;
}

.wpforms-panel-fields .wpforms-list-inline ul li {
  display: inline-block;
  margin-right: 15px;
  margin-bottom: 15px;
  max-width: calc( 50% - 15px);
}

.wpforms-panel-fields .wpforms-field.wpforms-field-checkbox .primary-input li, .wpforms-panel-fields .wpforms-field.wpforms-field-radio .primary-input li, .wpforms-panel-fields .wpforms-field.wpforms-field-payment-checkbox .primary-input li, .wpforms-panel-fields .wpforms-field.wpforms-field-payment-multiple .primary-input li {
  word-break: break-word;
  line-height: 20px;
}

.wpforms-panel-fields .wpforms-field.wpforms-field-select .choices[data-type*="select-multiple"] .choices__inner {
  padding-top: 8px !important;
  padding-bottom: 4px;
}

.wpforms-panel-fields .wpforms-field.wpforms-field-select .choices[data-type*="select-multiple"] .choices__inner .choices__list--multiple:empty + .choices__input {
  min-width: 100% !important;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.wpforms-panel-fields .wpforms-field.wpforms-field-select .choices.is-disabled .choices__list--multiple .choices__item {
  grid-template-columns: initial;
}

.wpforms-admin-page .choices:after {
  content: "\f347";
  position: absolute;
  top: calc( 50% - 6px);
  inset-inline-end: 6px;
  font-family: dashicons, sans-serif;
  color: #50575e;
  border: none;
  width: 16px;
  height: 16px;
  line-height: 1;
  z-index: 2;
}

.wpforms-admin-page .choices.is-open:after {
  margin-top: -1px;
  transform: rotate(180deg);
}

.wpforms-admin-page .choices[data-type*="select-multiple"] .choices__inner {
  cursor: pointer;
}

.wpforms-admin-page .is-focused .choices__inner,
.wpforms-admin-page .is-open .choices__inner {
  border-color: #056aab;
  box-shadow: 0 0 0 1px #056aab;
  outline: none;
}

.wpforms-admin-page .is-flipped.is-open .choices__inner {
  border-color: #056aab;
  box-shadow: 0 0 0 1px #056aab;
  outline: none;
}

.wpforms-admin-page .is-open .choices__list--dropdown {
  border-color: #056aab;
  border-top-color: #72757b;
  border-bottom: 0;
  box-shadow: 0 1px 0 1px #056aab;
}

.wpforms-admin-page .is-open.is-flipped .choices__list--dropdown {
  border-top: 0;
  border-bottom: 1px solid #72757b;
  box-shadow: 0 -1px 0 1px #056aab;
}

.choices__inner {
  min-height: 36px;
  padding-top: 6px;
  line-height: 1;
}

div.wpforms-container.wpforms-edit-entry-container .wpforms-form .choices .choices__inner .choices__list--multiple .choices__item {
  line-height: 1;
}

.wpforms-admin-page:not(.wpforms_page_wpforms-entries) .choices[data-type*="select-multiple"][aria-expanded="false"] .choices__inner {
  max-height: 36px;
}

.wpforms-admin-page:not(.wpforms_page_wpforms-entries) .choices[data-type*="select-multiple"][aria-expanded="false"] .choices__inner .choices__list {
  overflow: hidden;
  display: block;
  max-height: 24px;
}

.wpforms-admin-page:not(.wpforms_page_wpforms-entries) .choices[data-type*="select-multiple"][aria-expanded="false"].choices__show-more:before {
  position: absolute;
  content: '\f11c';
  font-family: dashicons, sans-serif;
  top: 7px;
  height: 22px;
  line-height: 22px;
  inset-inline-end: 28px;
  text-align: center;
  font-size: 14px;
  color: #a7aaad;
  box-sizing: border-box;
  pointer-events: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.wpforms-admin-page:not(.wpforms_page_wpforms-entries) .choices[data-type*="select-multiple"] .choices__inner {
  padding-inline-end: 40px;
}

.wpforms-admin-page#wpforms-builder .choices[data-type*="select-multiple"][aria-expanded="false"] .choices__inner {
  max-height: 32px;
}

.wpforms-admin-page#wpforms-builder .choices[data-type*="select-multiple"][aria-expanded="false"].choices__show-more:before {
  top: 5px;
}

.wpforms-panel-fields .wpforms-panel-sidebar .choices-list.show-images .wpforms-image-upload {
  display: block;
}

.wpforms-panel-fields .wpforms-panel-sidebar .choices-list .wpforms-image-upload {
  display: none;
}

.wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-option-row-choices .wpforms-image-upload {
  margin: 10px 48px 0 54px;
}

.wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-option-row-choices .wpforms-image-upload .preview {
  background-color: #ffffff;
  border-radius: 4px;
  display: block;
  padding: 10px;
  position: relative;
  width: 100%;
}

.wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-option-row-choices .wpforms-image-upload .preview:empty {
  padding: 0;
}

.wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-option-row-choices .wpforms-image-upload .preview a {
  color: #d63638;
  display: block;
  position: absolute;
  right: 10px;
  top: 10px;
}

.wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-option-row-choices .wpforms-image-upload .preview a:hover {
  color: #b32d2e;
}

.wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-option-row-choices .wpforms-image-upload .preview a i {
  margin: 0;
}

.wpforms-panel-fields .wpforms-panel-sidebar .wpforms-field-option-row-choices .wpforms-image-upload .preview img {
  display: block;
  margin: 0 auto;
  max-height: 100px;
  max-width: 100%;
}

.wpforms-panel-fields .wpforms-field-checkbox.wpforms-list-inline .primary-input li,
.wpforms-panel-fields .wpforms-field-radio.wpforms-list-inline .primary-input li,
.wpforms-panel-fields .wpforms-field-payment-multiple.wpforms-list-inline .primary-input li,
.wpforms-panel-fields .wpforms-field-payment-checkbox.wpforms-list-inline .primary-input li {
  vertical-align: top;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices {
  font-size: 0;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices input,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices input,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices input,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices input {
  display: none;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices img,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices img,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices img,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices img {
  max-width: 250px;
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices .wpforms-image-choices-label,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices .wpforms-image-choices-label,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices .wpforms-image-choices-label,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices .wpforms-image-choices-label {
  font-size: 14px;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices li,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices li,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices li,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices li {
  margin-bottom: 15px;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices li > label,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices li > label,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices li > label,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices li > label {
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices-modern li.wpforms-selected label,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices-modern li.wpforms-selected label,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices-modern li.wpforms-selected label,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices-modern li.wpforms-selected label {
  border-color: #ffffff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices-modern li.wpforms-selected .wpforms-image-choices-image:after,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices-modern li.wpforms-selected .wpforms-image-choices-image:after,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices-modern li.wpforms-selected .wpforms-image-choices-image:after,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices-modern li.wpforms-selected .wpforms-image-choices-image:after {
  opacity: 1;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices-modern li.wpforms-selected .wpforms-image-choices-label,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices-modern li.wpforms-selected .wpforms-image-choices-label,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices-modern li.wpforms-selected .wpforms-image-choices-label,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices-modern li.wpforms-selected .wpforms-image-choices-label {
  font-weight: 600;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices-modern img,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices-modern img,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices-modern img,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices-modern img {
  display: inline;
  margin: 0 auto;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices-modern label,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices-modern label,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices-modern label,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices-modern label {
  background-color: #ffffff;
  border: 1px solid transparent;
  border-radius: 4px;
  display: inline-block;
  margin: 0 auto;
  padding: 20px;
  text-align: center;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices-modern .wpforms-image-choices-image,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices-modern .wpforms-image-choices-image,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices-modern .wpforms-image-choices-image,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices-modern .wpforms-image-choices-image {
  display: block;
  position: relative;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices-modern .wpforms-image-choices-image:after,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices-modern .wpforms-image-choices-image:after,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices-modern .wpforms-image-choices-image:after,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices-modern .wpforms-image-choices-image:after {
  background: url("../../images/builder/check-circle.svg") no-repeat;
  background-size: 32px 32px;
  border-radius: 50%;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.25);
  content: "";
  height: 32px;
  left: 50%;
  margin: -16px 0 0 -16px;
  opacity: 0;
  position: absolute;
  top: 50%;
  width: 32px;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices-modern .wpforms-image-choices-label,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices-modern .wpforms-image-choices-label,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices-modern .wpforms-image-choices-label,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices-modern .wpforms-image-choices-label {
  display: block;
  margin-top: 8px;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices-classic li.wpforms-selected label,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices-classic li.wpforms-selected label,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices-classic li.wpforms-selected label,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices-classic li.wpforms-selected label {
  border-color: #777777;
  border-width: 2px;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices-classic img,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices-classic img,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices-classic img,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices-classic img {
  display: inline;
  margin: 0 auto;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices-classic label,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices-classic label,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices-classic label,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices-classic label {
  background-color: #ffffff;
  border: 1px solid #ffffff;
  display: inline-block;
  margin: 0 auto;
  padding: 10px;
  text-align: center;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices-classic label:hover,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices-classic label:hover,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices-classic label:hover,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices-classic label:hover {
  border-color: #777777;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices-classic .wpforms-image-choices-image,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices-classic .wpforms-image-choices-image,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices-classic .wpforms-image-choices-image,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices-classic .wpforms-image-choices-image {
  display: block;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices-classic .wpforms-image-choices-label,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices-classic .wpforms-image-choices-label,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices-classic .wpforms-image-choices-label,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices-classic .wpforms-image-choices-label {
  display: block;
  margin-top: 8px;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices-none img,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices-none img,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices-none img,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices-none img {
  display: inline;
  margin: 0;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices-none label,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices-none label,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices-none label,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices-none label {
  display: inline;
  line-height: 1;
  margin: 0;
  padding: 0;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices-none label input,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices-none label input,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices-none label input,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices-none label input {
  display: inline-block !important;
  margin-bottom: 5px;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices-none .wpforms-image-choices-image,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices-none .wpforms-image-choices-image,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices-none .wpforms-image-choices-image,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices-none .wpforms-image-choices-image {
  display: block;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-image-choices-none .wpforms-image-choices-label,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-image-choices-none .wpforms-image-choices-label,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-image-choices-none .wpforms-image-choices-label,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-image-choices-none .wpforms-image-choices-label {
  margin-top: 8px;
}

.wpforms-panel-fields .wpforms-panel-sidebar .choices-list.show-icons .wpforms-icon-select {
  display: flex;
}

.wpforms-panel-fields .wpforms-panel-sidebar .choices-list .wpforms-icon-select {
  display: none;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  position: relative;
  margin: 10px 48px 0 54px;
  padding: 10px;
  border-radius: 4px;
  background: #ffffff;
  color: #0399ed;
  font-size: 14px;
  line-height: 14px;
  cursor: pointer;
}

.wpforms-panel-fields .wpforms-panel-sidebar .choices-list .wpforms-icon-select:hover .fa-edit {
  color: #444444;
}

.wpforms-panel-fields .wpforms-panel-sidebar .choices-list .wpforms-icon-select span {
  color: #b0b6bd;
}

.wpforms-panel-fields .wpforms-panel-sidebar .choices-list .fa-edit {
  position: absolute;
  top: 10px;
  inset-inline-end: 10px;
  border: none;
  background: transparent;
  color: #86919e;
}

.wpforms-panel-fields .wpforms-panel-sidebar .choices-list .ic-fa-brands,
.wpforms-panel-fields .wpforms-panel-sidebar .choices-list .ic-fa-regular,
.wpforms-panel-fields .wpforms-panel-sidebar .choices-list .ic-fa-solid {
  margin-left: 0;
  font-size: var(--wpforms-icon-choices-size-medium);
  line-height: var(--wpforms-icon-choices-size-medium);
  color: var(--wpforms-icon-choices-color);
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices {
  margin-bottom: -15px;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices li,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices li,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices li,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices li {
  min-width: 120px;
  max-width: 100%;
  margin-bottom: 15px;
  text-align: center;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices label,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices label,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices label,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices label {
  cursor: pointer;
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices input,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices input,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices input,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices input {
  display: none;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices .wpforms-icon-choices-label,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices .wpforms-icon-choices-label,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices .wpforms-icon-choices-label,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices .wpforms-icon-choices-label {
  font-size: 14px;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices .wpforms-icon-choices-icon,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices .wpforms-icon-choices-icon,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices .wpforms-icon-choices-icon,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices .wpforms-icon-choices-icon {
  display: block;
  margin-bottom: 15px;
  color: #0399ed;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices .wpforms-icon-choices-icon i,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices .wpforms-icon-choices-icon i,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices .wpforms-icon-choices-icon i,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices .wpforms-icon-choices-icon i {
  color: var(--wpforms-icon-choices-color);
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices-small .wpforms-icon-choices-icon i,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices-small .wpforms-icon-choices-icon i,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices-small .wpforms-icon-choices-icon i,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices-small .wpforms-icon-choices-icon i {
  font-size: var(--wpforms-icon-choices-size-small);
  line-height: var(--wpforms-icon-choices-size-small);
  width: calc( var(--wpforms-icon-choices-size-small) * 1.25);
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices-medium .wpforms-icon-choices-icon i,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices-medium .wpforms-icon-choices-icon i,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices-medium .wpforms-icon-choices-icon i,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices-medium .wpforms-icon-choices-icon i {
  font-size: var(--wpforms-icon-choices-size-medium);
  line-height: var(--wpforms-icon-choices-size-medium);
  width: calc( var(--wpforms-icon-choices-size-medium) * 1.25);
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices-large .wpforms-icon-choices-icon i,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices-large .wpforms-icon-choices-icon i,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices-large .wpforms-icon-choices-icon i,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices-large .wpforms-icon-choices-icon i {
  font-size: var(--wpforms-icon-choices-size-large);
  line-height: var(--wpforms-icon-choices-size-large);
  width: calc( var(--wpforms-icon-choices-size-large) * 1.25);
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-default, .wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-modern,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices.wpforms-icon-choices-default,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices.wpforms-icon-choices-modern,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices.wpforms-icon-choices-default,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices.wpforms-icon-choices-modern,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-default,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-modern {
  margin: 0 1px -16px 1px;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-default li, .wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices.wpforms-icon-choices-default li,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices.wpforms-icon-choices-default li,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-default li,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  margin-bottom: 17px;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-default li .wpforms-icon-choices-icon,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices.wpforms-icon-choices-default li .wpforms-icon-choices-icon,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices.wpforms-icon-choices-default li .wpforms-icon-choices-icon,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-default li .wpforms-icon-choices-icon {
  padding: 15px 20px 45px 20px;
  background-color: #ffffff;
  border-radius: 6px;
  box-shadow: 0 0 0 1px #cccccc;
  position: relative;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-default li .wpforms-icon-choices-icon .wpforms-icon-choices-icon-bg,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices.wpforms-icon-choices-default li .wpforms-icon-choices-icon .wpforms-icon-choices-icon-bg,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices.wpforms-icon-choices-default li .wpforms-icon-choices-icon .wpforms-icon-choices-icon-bg,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-default li .wpforms-icon-choices-icon .wpforms-icon-choices-icon-bg {
  display: block;
  position: absolute;
  border-radius: 6px;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon .wpforms-icon-choices-icon-bg,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon .wpforms-icon-choices-icon-bg,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon .wpforms-icon-choices-icon-bg,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon .wpforms-icon-choices-icon-bg {
  background-color: var(--wpforms-icon-choices-color);
  opacity: .1;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  padding: 20px 20px 15px 20px;
  box-shadow: 0 0 0 1px #cccccc;
  border-radius: 6px;
  background-color: #ffffff;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-modern li.wpforms-selected,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices.wpforms-icon-choices-modern li.wpforms-selected,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices.wpforms-icon-choices-modern li.wpforms-selected,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-modern li.wpforms-selected {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color), 0 2px 10px rgba(0, 0, 0, 0.15);
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-classic li,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices.wpforms-icon-choices-classic li,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices.wpforms-icon-choices-classic li,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-classic li {
  padding: 20px 20px 15px 20px;
  background-color: #ffffff;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-classic li.wpforms-selected,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices.wpforms-icon-choices-classic li.wpforms-selected,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices.wpforms-icon-choices-classic li.wpforms-selected,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-classic li.wpforms-selected {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-none li,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices.wpforms-icon-choices-none li,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices.wpforms-icon-choices-none li,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-none li {
  text-align: initial;
}

.wpforms-panel-fields .wpforms-field-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-none input,
.wpforms-panel-fields .wpforms-field-radio ul.wpforms-icon-choices.wpforms-icon-choices-none input,
.wpforms-panel-fields .wpforms-field-payment-multiple ul.wpforms-icon-choices.wpforms-icon-choices-none input,
.wpforms-panel-fields .wpforms-field-payment-checkbox ul.wpforms-icon-choices.wpforms-icon-choices-none input {
  display: inline-block !important;
}

.wpforms-panel-fields .wpforms-field-checkbox.wpforms-list-2-columns ul.wpforms-icon-choices li:nth-child(2n),
.wpforms-panel-fields .wpforms-field-radio.wpforms-list-2-columns ul.wpforms-icon-choices li:nth-child(2n),
.wpforms-panel-fields .wpforms-field-payment-multiple.wpforms-list-2-columns ul.wpforms-icon-choices li:nth-child(2n),
.wpforms-panel-fields .wpforms-field-payment-checkbox.wpforms-list-2-columns ul.wpforms-icon-choices li:nth-child(2n) {
  margin-right: 0 !important;
}

.wpforms-panel-fields .wpforms-field-checkbox.wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li, .wpforms-panel-fields .wpforms-field-checkbox.wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-panel-fields .wpforms-field-radio.wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li,
.wpforms-panel-fields .wpforms-field-radio.wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-panel-fields .wpforms-field-payment-multiple.wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li,
.wpforms-panel-fields .wpforms-field-payment-multiple.wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-panel-fields .wpforms-field-payment-checkbox.wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li,
.wpforms-panel-fields .wpforms-field-payment-checkbox.wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  margin-right: 17px;
  width: calc( 100% / 2 - 8.5px);
}

.wpforms-panel-fields .wpforms-field-checkbox.wpforms-list-3-columns ul.wpforms-icon-choices li:nth-child(3n),
.wpforms-panel-fields .wpforms-field-radio.wpforms-list-3-columns ul.wpforms-icon-choices li:nth-child(3n),
.wpforms-panel-fields .wpforms-field-payment-multiple.wpforms-list-3-columns ul.wpforms-icon-choices li:nth-child(3n),
.wpforms-panel-fields .wpforms-field-payment-checkbox.wpforms-list-3-columns ul.wpforms-icon-choices li:nth-child(3n) {
  margin-right: 0 !important;
}

.wpforms-panel-fields .wpforms-field-checkbox.wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li, .wpforms-panel-fields .wpforms-field-checkbox.wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-panel-fields .wpforms-field-radio.wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li,
.wpforms-panel-fields .wpforms-field-radio.wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-panel-fields .wpforms-field-payment-multiple.wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li,
.wpforms-panel-fields .wpforms-field-payment-multiple.wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-panel-fields .wpforms-field-payment-checkbox.wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li,
.wpforms-panel-fields .wpforms-field-payment-checkbox.wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  margin-right: 17px;
  width: calc( 100% / 3 - 11.33333px);
}

.wpforms-panel-fields .wpforms-field-checkbox.wpforms-list-inline ul.wpforms-icon-choices,
.wpforms-panel-fields .wpforms-field-radio.wpforms-list-inline ul.wpforms-icon-choices,
.wpforms-panel-fields .wpforms-field-payment-multiple.wpforms-list-inline ul.wpforms-icon-choices,
.wpforms-panel-fields .wpforms-field-payment-checkbox.wpforms-list-inline ul.wpforms-icon-choices {
  display: flex;
  flex-wrap: wrap;
}

.wpforms-panel-fields .wpforms-field-checkbox.wpforms-list-inline ul.wpforms-icon-choices.wpforms-icon-choices-default li, .wpforms-panel-fields .wpforms-field-checkbox.wpforms-list-inline ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-panel-fields .wpforms-field-radio.wpforms-list-inline ul.wpforms-icon-choices.wpforms-icon-choices-default li,
.wpforms-panel-fields .wpforms-field-radio.wpforms-list-inline ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-panel-fields .wpforms-field-payment-multiple.wpforms-list-inline ul.wpforms-icon-choices.wpforms-icon-choices-default li,
.wpforms-panel-fields .wpforms-field-payment-multiple.wpforms-list-inline ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-panel-fields .wpforms-field-payment-checkbox.wpforms-list-inline ul.wpforms-icon-choices.wpforms-icon-choices-default li,
.wpforms-panel-fields .wpforms-field-payment-checkbox.wpforms-list-inline ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  margin-right: 17px;
}

.wpforms-panel-fields .wpforms-field-checkbox .description:not(:empty),
.wpforms-panel-fields .wpforms-field-radio .description:not(:empty),
.wpforms-panel-fields .wpforms-field-payment-multiple .description:not(:empty),
.wpforms-panel-fields .wpforms-field-payment-checkbox .description:not(:empty) {
  margin-top: 15px;
}

.wpforms-panel-fields .wpforms-field-checkbox .wpforms-icon-choices-default .wpforms-icon-choices-icon:after,
.wpforms-panel-fields .wpforms-field-payment-checkbox .wpforms-icon-choices-default .wpforms-icon-choices-icon:after {
  content: "";
  box-sizing: border-box;
  position: absolute;
  bottom: 15px;
  left: calc( 50% - 8px);
  display: block;
  width: 16px;
  height: 16px;
  background-color: #ffffff;
  box-shadow: 0 0 0 1px #cccccc;
  border-radius: 3px;
  margin: 15px auto 0;
}

.wpforms-panel-fields .wpforms-field-checkbox .wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon:after,
.wpforms-panel-fields .wpforms-field-payment-checkbox .wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon:after {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-panel-fields .wpforms-field-checkbox .wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon:before,
.wpforms-panel-fields .wpforms-field-payment-checkbox .wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon:before {
  content: "";
  box-sizing: border-box;
  position: absolute;
  z-index: 1;
  left: calc( 50% - 6px);
  bottom: 23px;
  display: block;
  width: 6px;
  height: 10px;
  border-style: solid;
  border-color: var(--wpforms-icon-choices-color);
  border-width: 0 2px 2px 0;
  transform-origin: bottom left;
  transform: rotate(45deg);
}

.wpforms-panel-fields .wpforms-field-radio .wpforms-icon-choices-default .wpforms-icon-choices-icon:after,
.wpforms-panel-fields .wpforms-field-payment-multiple .wpforms-icon-choices-default .wpforms-icon-choices-icon:after {
  content: "";
  box-sizing: border-box;
  position: absolute;
  bottom: 15px;
  left: calc( 50% - 8px);
  display: block;
  width: 16px;
  height: 16px;
  background-color: #ffffff;
  box-shadow: 0 0 0 1px #cccccc;
  border-radius: 50%;
  margin: 15px auto 0;
}

.wpforms-panel-fields .wpforms-field-radio .wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon:after,
.wpforms-panel-fields .wpforms-field-payment-multiple .wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon:after {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-panel-fields .wpforms-field-radio .wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon:before,
.wpforms-panel-fields .wpforms-field-payment-multiple .wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon:before {
  content: "";
  position: absolute;
  z-index: 1;
  bottom: 19px;
  left: calc( 50% - 4px);
  display: block;
  width: 8px;
  height: 8px;
  background-color: var(--wpforms-icon-choices-color);
  border-radius: 50%;
}

.wpforms-panel-fields .wpforms-field .choices[data-type*="select-multiple"] .choices__input {
  width: fit-content;
  vertical-align: top;
  font-size: 16px;
}

.wpforms-panel-fields .wpforms-field .choices[data-type*="select-one"] .choices__inner {
  display: block;
}

.wpforms-panel-fields .wpforms-field .choices[data-type*="select-one"] .choices__list--single {
  padding-left: 3px;
}

.wpforms-panel-fields .wpforms-field .choices__inner {
  min-height: 40px;
  padding-left: 7px;
  padding-top: 6px !important;
}

.wpforms-panel-fields .wpforms-field .choices__inner input.choices__input {
  height: auto;
  padding-left: 3px !important;
}

.wpforms-panel-fields .wpforms-field .choices__inner,
.wpforms-panel-fields .wpforms-field .choices__input {
  background-color: #ffffff;
  cursor: pointer;
  user-select: none;
}

.wpforms-panel-fields .wpforms-field .choices__list:not(.choices__list--multiple) .choices__item.choices__placeholder, .wpforms-panel-fields .wpforms-field .choices__list:not(.choices__list--multiple) .choices__item.choices__item--selectable {
  line-height: 18px;
  margin: 4px 0;
  cursor: pointer;
  color: #999999;
}

.wpforms-panel-fields .wpforms-field-select.size-small .choices,
.wpforms-panel-fields .wpforms-field-payment-select.size-small .choices {
  width: 25%;
}

.wpforms-panel-fields .wpforms-field-select .choices, .wpforms-panel-fields .wpforms-field-select.size-medium .choices,
.wpforms-panel-fields .wpforms-field-payment-select .choices,
.wpforms-panel-fields .wpforms-field-payment-select.size-medium .choices {
  min-width: 250px;
  width: 60%;
}

.wpforms-panel-fields .wpforms-field-select.size-large .choices,
.wpforms-panel-fields .wpforms-field-payment-select.size-large .choices {
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-payment-select.payment-quantity-enabled.size-small .primary-input,
.wpforms-panel-fields .wpforms-field-payment-select.payment-quantity-enabled.size-small .choices {
  width: calc( 25% - 85px) !important;
  min-width: 165px;
}

.wpforms-panel-fields .wpforms-field-payment-select.payment-quantity-enabled.size-large .primary-input,
.wpforms-panel-fields .wpforms-field-payment-select.payment-quantity-enabled.size-large .choices {
  width: calc( 100% - 85px) !important;
}

.wpforms-panel-fields .wpforms-field-payment-select.payment-quantity-enabled .primary-input,
.wpforms-panel-fields .wpforms-field-payment-select.payment-quantity-enabled .choices {
  float: inline-start;
  width: calc( 60% - 85px);
  min-width: 180px;
}

.wpforms-panel-fields .wpforms-field-payment-select.payment-quantity-enabled .description {
  padding-top: 5px;
  margin: 0;
}

@-moz-document url-prefix() {
  .wpforms-panel-fields .choices__item.choices__placeholder, .wpforms-panel-fields .choices__item.choices__item--selectable {
    margin-right: 6px;
  }
}

.wpforms-panel-fields .wpforms-field-option-name .format-selected-simple .wpforms-field-option-row-first,
.wpforms-panel-fields .wpforms-field-option-name .format-selected-simple .wpforms-field-option-row-middle,
.wpforms-panel-fields .wpforms-field-option-name .format-selected-simple .wpforms-field-option-row-last {
  display: none;
}

.wpforms-panel-fields .wpforms-field-option-name .format-selected-first-last .wpforms-field-option-row-simple,
.wpforms-panel-fields .wpforms-field-option-name .format-selected-first-last .wpforms-field-option-row-middle {
  display: none;
}

.wpforms-panel-fields .wpforms-field-option-name .format-selected-first-middle-last .wpforms-field-option-row-simple {
  display: none;
}

.wpforms-panel-fields .wpforms-field-name .format-selected input[type=text] {
  width: 100%;
  min-width: initial;
}

.wpforms-panel-fields .wpforms-field-name.size-small .format-selected {
  width: 25%;
}

.wpforms-panel-fields .wpforms-field-name .format-selected,
.wpforms-panel-fields .wpforms-field-name.size-medium .format-selected {
  width: 60%;
  min-width: 250px;
}

.wpforms-panel-fields .wpforms-field-name.size-large .format-selected {
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-name .format-selected-first-last .wpforms-simple,
.wpforms-panel-fields .wpforms-field-name .format-selected-first-last .wpforms-middle-name,
.wpforms-panel-fields .wpforms-field-name .format-selected-first-middle-last .wpforms-simple,
.wpforms-panel-fields .wpforms-field-name .format-selected-simple .wpforms-first-name,
.wpforms-panel-fields .wpforms-field-name .format-selected-simple .wpforms-middle-name,
.wpforms-panel-fields .wpforms-field-name .format-selected-simple .wpforms-last-name {
  display: none;
}

.wpforms-panel-fields .wpforms-field-name .format-selected-first-last .wpforms-first-name {
  float: left;
  width: calc( 50% - 10px);
}

.wpforms-panel-fields .wpforms-field-name .format-selected-first-last .wpforms-last-name {
  float: right;
  width: calc( 50% - 10px);
}

.wpforms-panel-fields .wpforms-field-name .format-selected-first-middle-last .wpforms-first-name {
  float: left;
  margin-right: 20px;
  width: calc( 40% - 20px);
}

.wpforms-panel-fields .wpforms-field-name .format-selected-first-middle-last .wpforms-middle-name {
  float: left;
  width: 20%;
}

.wpforms-panel-fields .wpforms-field-name .format-selected-first-middle-last .wpforms-last-name {
  float: right;
  width: calc( 40% - 20px);
}

.wpforms-panel-fields .wpforms-field-option .wpforms-field-option-row-allowlist,
.wpforms-panel-fields .wpforms-field-option .wpforms-field-option-row-denylist {
  display: none;
}

.wpforms-panel-fields .wpforms-field-option.wpforms-filter-allowlist .wpforms-field-option-row-allowlist,
.wpforms-panel-fields .wpforms-field-option.wpforms-filter-denylist .wpforms-field-option-row-denylist {
  display: block;
  margin-top: -10px;
}

.wpforms-panel-fields .wpforms-field .wpforms-confirm input[type=email],
.wpforms-panel-fields .wpforms-field .wpforms-confirm input[type=password] {
  width: 100%;
  min-width: initial;
}

.wpforms-panel-fields .wpforms-field.size-large .wpforms-confirm {
  width: 100%;
}

.wpforms-panel-fields .wpforms-field .wpforms-confirm,
.wpforms-panel-fields .wpforms-field.size-medium .wpforms-confirm {
  width: 60%;
  min-width: 250px;
}

.wpforms-panel-fields .wpforms-field.size-small .wpforms-confirm {
  width: 25%;
}

.wpforms-panel-fields .wpforms-confirm-disabled .wpforms-confirm-confirmation,
.wpforms-panel-fields .wpforms-confirm-disabled .wpforms-confirm-primary .wpforms-sub-label {
  display: none;
}

.wpforms-panel-fields .wpforms-confirm-enabled .wpforms-confirm-primary {
  float: left;
  width: calc( 50% - 10px);
}

.wpforms-panel-fields .wpforms-confirm-enabled .wpforms-confirm-confirmation {
  float: right;
  width: calc( 50% - 10px);
}

.wpforms-panel-fields .wpforms-field-entry-preview .wpforms-entry-preview-notice {
  padding: 20px;
  background: #fef8ee;
  border: 1px solid #f0b849;
  border-radius: 4px;
  word-break: break-word;
}

.wpforms-panel-fields .wpforms-field-entry-preview .wpforms-field-duplicate {
  display: none;
}

.wpforms-panel-fields .wpforms-field-entry-preview .wpforms-alert {
  margin-bottom: 0;
}

#wpforms-panel-fields .wpforms-field-option-gdpr-checkbox .wpforms-field-option-row-choices .wpforms-help-tooltip,
#wpforms-panel-fields .wpforms-field-option-gdpr-checkbox .wpforms-field-option-row-choices .toggle-bulk-add-display,
#wpforms-panel-fields .wpforms-field-option-gdpr-checkbox .wpforms-field-option-row-choices .add,
#wpforms-panel-fields .wpforms-field-option-gdpr-checkbox .wpforms-field-option-row-choices .remove,
#wpforms-panel-fields .wpforms-field-option-gdpr-checkbox .wpforms-field-option-row-choices .move,
#wpforms-panel-fields .wpforms-field-option-gdpr-checkbox .wpforms-field-option-row-choices .default {
  display: none;
}

#wpforms-panel-fields .wpforms-field-option-gdpr-checkbox .wpforms-field-option-row-choices li input[type=text] {
  width: calc( 100% - 2px);
  margin: 0;
}

.wpforms-panel-fields .wpforms-field-recaptcha {
  background-color: #f6f6f6;
  border-start-start-radius: 25px;
  border-end-start-radius: 25px;
  display: none;
  position: absolute;
  inset-inline-end: 30px;
  top: 49px;
  padding-inline-start: 13px;
  padding-inline-end: 30px;
}

.wpforms-panel-fields .wpforms-field-recaptcha-wrap {
  align-items: center;
  display: flex;
  height: 50px;
}

.wpforms-panel-fields .wpforms-field-recaptcha-wrap-l {
  margin-inline-end: 10px;
  margin-top: 4px;
}

.wpforms-panel-fields .wpforms-field-recaptcha-wrap-r {
  flex-grow: 1;
}

.wpforms-panel-fields .wpforms-field-hcaptcha-icon,
.wpforms-panel-fields .wpforms-field-recaptcha-icon,
.wpforms-panel-fields .wpforms-field-turnstile-icon {
  width: 28px;
  height: auto;
}

.wpforms-panel-fields .wpforms-field-hcaptcha-title,
.wpforms-panel-fields .wpforms-field-recaptcha-title,
.wpforms-panel-fields .wpforms-field-turnstile-title {
  font-weight: 600;
  line-height: 1;
  margin: 0;
  font-size: 13px;
}

.wpforms-panel-fields .wpforms-field-recaptcha-desc {
  color: #777777;
  line-height: 15px;
  margin: 0;
  overflow: hidden;
  white-space: nowrap;
}

.wpforms-panel-fields .wpforms-field-recaptcha-desc-txt,
.wpforms-panel-fields .wpforms-field-recaptcha-desc-icon {
  vertical-align: middle;
  font-size: 12px;
}

.wpforms-panel-fields .wpforms-field-recaptcha-desc-icon {
  color: #0399ed;
  width: 14px;
  margin-inline-start: 5px;
}

.wpforms-panel-fields .wpforms-field-recaptcha-desc-icon path {
  fill: currentColor;
}

.wpforms-panel-fields .wpforms-field-recaptcha-icon,
.wpforms-panel-fields .wpforms-field-recaptcha-title,
.wpforms-panel-fields .wpforms-field-turnstile-icon,
.wpforms-panel-fields .wpforms-field-turnstile-title {
  display: none;
}

.wpforms-panel-fields .wpforms-field-recaptcha.is-recaptcha .wpforms-field-hcaptcha-icon,
.wpforms-panel-fields .wpforms-field-recaptcha.is-recaptcha .wpforms-field-hcaptcha-title,
.wpforms-panel-fields .wpforms-field-recaptcha.is-recaptcha .wpforms-field-turnstile-icon,
.wpforms-panel-fields .wpforms-field-recaptcha.is-recaptcha .wpforms-field-turnstile-title {
  display: none;
}

.wpforms-panel-fields .wpforms-field-recaptcha.is-recaptcha .wpforms-field-recaptcha-icon,
.wpforms-panel-fields .wpforms-field-recaptcha.is-recaptcha .wpforms-field-recaptcha-title {
  display: block;
}

.wpforms-panel-fields .wpforms-field-recaptcha.is-turnstile .wpforms-field-turnstile-icon,
.wpforms-panel-fields .wpforms-field-recaptcha.is-turnstile .wpforms-field-turnstile-title {
  display: block;
}

.wpforms-panel-fields .wpforms-field-recaptcha.is-turnstile .wpforms-field-turnstile-icon {
  margin-bottom: 3px;
}

.wpforms-panel-fields .wpforms-field-recaptcha.is-turnstile .wpforms-field-recaptcha-icon,
.wpforms-panel-fields .wpforms-field-recaptcha.is-turnstile .wpforms-field-recaptcha-title,
.wpforms-panel-fields .wpforms-field-recaptcha.is-turnstile .wpforms-field-hcaptcha-icon,
.wpforms-panel-fields .wpforms-field-recaptcha.is-turnstile .wpforms-field-hcaptcha-title {
  display: none;
}

.wpforms-panel-fields .wpforms-field-option-row-questions li input[type=text] {
  display: inline-block;
  width: 82%;
  margin: 0 0 10px 0;
}

.wpforms-panel-fields .wpforms-field-option-row-questions li input[type=text].value {
  display: block;
}

.wpforms-panel-fields .wpforms-field-option-row-questions li input[type=text]:last-of-type {
  margin: 0;
}

.wpforms-panel-fields .wpforms-field-captcha .format-selected-math input[type=text] {
  display: inline-block;
  width: 70px;
}

.wpforms-panel-fields .wpforms-field-captcha .format-selected-qa .wpforms-question {
  margin: 0 0 5px 0;
}

.wpforms-panel-fields .wpforms-field-captcha .format-selected-math .wpforms-question,
.wpforms-panel-fields .wpforms-field-captcha .format-selected-qa .wpforms-equation {
  display: none;
}

.wpforms-panel-fields .wpforms-field-option-row-min_max .wpforms-input-row {
  display: flex;
}

.wpforms-panel-fields .wpforms-field-option-row-min_max .wpforms-input-row .minimum {
  margin-right: 10px;
  width: 50%;
}

.wpforms-panel-fields .wpforms-field-option-row-min_max .wpforms-input-row .maximum {
  width: 50%;
}

.wpforms-panel-fields .wpforms-field-option-row-min_max .wpforms-input-row input {
  width: calc( 100% - 2px);
}

.wpforms-panel-fields .wpforms-field-option-row-default_value .wpforms-alert {
  margin-top: 20px;
}

.wpforms-panel-fields .wpforms-field-number-slider input[type=range] {
  background: #ffffff;
  border: 1px solid #cccccc;
  border-radius: 2em;
  height: 10px;
  margin: 10px 0;
  outline: none;
  padding: 0;
  appearance: none;
  -webkit-appearance: none;
}

.wpforms-panel-fields .wpforms-field-number-slider input[type=range]::-ms-track {
  color: transparent;
}

.wpforms-panel-fields .wpforms-field-number-slider input[type=range]::-webkit-slider-thumb {
  background: #999999;
  border-radius: 100%;
  height: 17px;
  width: 17px;
  appearance: none;
  -webkit-appearance: none;
}

.wpforms-panel-fields .wpforms-field-number-slider input[type=range]::-moz-range-thumb, .wpforms-panel-fields .wpforms-field-number-slider input[type=range]::-ms-thumb {
  background: #999999;
  border-radius: 100%;
  height: 17px;
  width: 17px;
}

.wpforms-panel-fields .wpforms-field-number-slider .wpforms-number-slider-hint {
  color: #777777;
  display: block;
  font-size: 14px;
}

.wpforms-field-option-internal-information .wpforms-field-option-row-description textarea,
.wpforms-field-option-internal-information .wpforms-field-option-row-expanded-description textarea {
  height: 160px;
  vertical-align: top;
}

.wpforms-field-option-internal-information .note {
  margin-top: 10px;
}

.wpforms-panel-fields .wpforms-field.wpforms-field-internal-information {
  padding-bottom: 0;
}

.wpforms-panel-fields .wpforms-field.internal-information-editable .internal-information-wrap {
  margin-inline-end: 55px;
  margin-bottom: 15px;
}

.wpforms-panel-fields .wpforms-field.internal-information-not-editable:hover > .wpforms-field-duplicate {
  display: none;
}

.wpforms-panel-fields .wpforms-field.internal-information-not-editable {
  border: none !important;
  margin-bottom: 15px;
}

.wpforms-panel-fields .wpforms-field.internal-information-not-editable * {
  cursor: default;
}

.wpforms-panel-fields .wpforms-field.internal-information-not-editable a {
  cursor: pointer;
}

.wpforms-panel-fields .wpforms-field.internal-information-not-editable a * {
  cursor: pointer;
}

.wpforms-panel-fields .wpforms-field.internal-information-not-editable.active {
  background-color: #ffffff;
}

.wpforms-panel-fields .wpforms-field.internal-information-not-editable .wpforms-field-helper {
  display: none;
}

.wpforms-panel-fields .wpforms-field.internal-information-not-editable .wpforms-field-delete {
  top: 30px;
  inset-inline-end: 30px;
  color: #777777;
  font-size: 24px;
  width: 24px;
  height: 24px;
  opacity: 0.5;
  text-decoration: none;
  transition-duration: 0.05s;
}

.wpforms-panel-fields .wpforms-field.internal-information-not-editable .wpforms-field-delete:hover {
  color: inherit;
  opacity: 1;
}

.wpforms-panel-fields .wpforms-field.internal-information-not-editable .wpforms-field-delete i {
  font-family: dashicons;
  display: inline-block;
  font-size: 1em;
  line-height: 1;
  font-weight: 400;
  font-style: normal;
  speak: none;
  text-rendering: auto;
  vertical-align: top;
  text-align: center;
  color: inherit;
}

.wpforms-panel-fields .wpforms-field.internal-information-not-editable .wpforms-field-delete i::before {
  content: '\f335';
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap {
  min-height: 62px;
  background-color: #fdfaf2;
  border: 1px solid rgba(0, 0, 0, 0.07);
  border-radius: 6px;
  padding-top: 20px;
  padding-inline-end: 40px;
  padding-inline-start: 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.07);
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-lightbulb {
  float: inline-start;
  width: 14px;
  padding-top: 1px;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content {
  margin-inline-start: 30px;
  color: #444444;
  font-size: 15px;
  line-height: 22px;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .wpforms-field-internal-information-row-heading .label-title {
  font-size: 17px;
  line-height: 22px;
  margin-bottom: 10px;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .wpforms-field-internal-information-row-description :last-child,
.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .wpforms-field-internal-information-row-expanded-description :last-child {
  margin-bottom: 20px;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .wpforms-field-internal-information-row-description .description, .wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .wpforms-field-internal-information-row-expanded-description .expanded-description {
  font-size: 15px;
  line-height: 22px;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .wpforms-field-internal-information-row-description .description p, .wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .wpforms-field-internal-information-row-expanded-description .expanded-description p {
  line-height: 22px;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .wpforms-field-internal-information-row-description .description {
  color: #444444;
  clear: none;
  margin-top: 0;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content hr {
  border: none;
  border-top: 1px solid #ebe9e1;
  margin-top: 19px;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .wpforms-field-internal-information-row-expanded-description {
  margin-top: 15px;
  display: none;
  border-top: 1px solid #ebe9e1;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .wpforms-field-internal-information-row-expanded-description .expanded-description p {
  margin-bottom: 0;
  margin-top: 17px;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .expanded-description .wpforms-field-internal-information-checkbox-wrap, .wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .description .wpforms-field-internal-information-checkbox-wrap {
  clear: both;
  margin-top: 17px;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .expanded-description .wpforms-field-internal-information-checkbox-wrap .wpforms-field-internal-information-checkbox-input, .wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .description .wpforms-field-internal-information-checkbox-wrap .wpforms-field-internal-information-checkbox-input {
  float: inline-start;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .expanded-description .wpforms-field-internal-information-checkbox-wrap input[type=checkbox], .wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .description .wpforms-field-internal-information-checkbox-wrap input[type=checkbox] {
  margin-bottom: 0;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .expanded-description .wpforms-field-internal-information-checkbox-wrap .wpforms-field-internal-information-checkbox-label, .wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .description .wpforms-field-internal-information-checkbox-wrap .wpforms-field-internal-information-checkbox-label {
  margin-inline-start: 30px;
  position: relative;
  top: -2px;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .expanded-description a, .wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .description a {
  color: #444;
  text-decoration: underline;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .expanded-description a:hover, .wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .expanded-description a:focus, .wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .description a:hover, .wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .description a:focus {
  text-decoration: none;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .expanded-description ol, .wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .description ol {
  list-style-type: revert;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .expanded-description ul, .wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .description ul {
  list-style-type: revert;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .expanded-description ul, .wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .expanded-description ol, .wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .description ul, .wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .description ol {
  margin-block: 1em;
  margin-inline: 0;
  padding-inline-start: 1em;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .wpforms-field-internal-information-row-cta-button {
  clear: both;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .wpforms-field-internal-information-row-cta-button a {
  display: inline-block;
  background-color: #e6a700;
  color: white;
  padding: 9px 14px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 600;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .wpforms-field-internal-information-row-cta-button a:hover {
  background-color: #d79500;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .wpforms-field-internal-information-row-cta-button .cta-button {
  margin-bottom: 19px;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .wpforms-field-internal-information-row-cta-button .cta-button.cta-expand-description {
  padding-top: 20px;
  border-top: 1px solid #ebe9e1;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .wpforms-field-internal-information-row-cta-button .cta-expand-description a .icon {
  padding-inline-start: 8px;
  position: relative;
  top: -1px;
  cursor: pointer;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .wpforms-field-internal-information-row-cta-button .cta-expand-description a .icon * {
  cursor: pointer;
}

.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .wpforms-field-internal-information-row-cta-button .cta-expand-description.not-expanded span.icon.expanded,
.wpforms-panel-fields .wpforms-field .internal-information-wrap .internal-information-content .wpforms-field-internal-information-row-cta-button .cta-expand-description.expanded span.icon.not-expanded {
  display: none;
}

.internal-information-field-user-mode {
  padding: 20px 20px 0 20px;
}

.wpforms-field-internal-information .internal-information-lightbulb svg path {
  fill: #e6a700;
}

.wpforms-field-internal-information .wpforms-field-delete svg path {
  fill: #777777;
}

.wpforms-field-internal-information .cta-button .icon.expanded svg,
.wpforms-field-internal-information .cta-button .icon.not-expanded svg {
  width: 10px;
  height: 7px;
}

.wpforms-field-internal-information .cta-button .icon.expanded svg path,
.wpforms-field-internal-information .cta-button .icon.not-expanded svg path {
  fill: currentColor;
}

.wpforms-panel-fields .wpforms-field-address.size-small .wpforms-address-scheme {
  width: 25%;
}

.wpforms-panel-fields .wpforms-field-address .wpforms-address-scheme, .wpforms-panel-fields .wpforms-field-address.size-medium .wpforms-address-scheme {
  width: 60%;
  min-width: 250px;
}

.wpforms-panel-fields .wpforms-field-address.size-large .wpforms-address-scheme {
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-address .wpforms-hide {
  display: none;
}

.wpforms-panel-fields .wpforms-field-address .wpforms-address-scheme input[type=text],
.wpforms-panel-fields .wpforms-field-address .wpforms-address-scheme select {
  width: 100%;
  min-width: initial;
}

.wpforms-panel-sidebar .wpforms-field-options.wpforms-tab-content.wpforms-content-editor-expanded {
  overflow: visible !important;
}

.wpforms-panel-sidebar .wpforms-field-option.wpforms-field-has-tinymce.wpforms-content-editor-expanded .wpforms-expandable-editor {
  width: 786px;
  position: fixed;
  z-index: 99;
}

.wpforms-panel-sidebar .wpforms-field-option.wpforms-field-has-tinymce.wpforms-content-editor-expanded .wpforms-expandable-editor .wp-core-ui.wp-editor-wrap {
  filter: drop-shadow(0px 2px 15px rgba(0, 0, 0, 0.07));
}

.rtl .wpforms-panel-sidebar .wp-media-buttons .button {
  margin-right: 0;
}

.wpforms-panel-sidebar .mce-rtl .mce-flow-layout .mce-flow-layout-item > div {
  text-align: right;
}

.wpforms-panel-sidebar-closed .wpforms-field-option.wpforms-content-editor-expanded .wpforms-expandable-editor {
  display: none;
}

.wpforms-field-has-tinymce .wpforms-field-option-group .wpforms-field-option-group-inner {
  padding-bottom: 5px;
}

.wpforms-field-has-tinymce .mce-toolbar-grp {
  height: 40px;
  overflow: hidden;
  padding-left: 2px;
}

.wpforms-field-has-tinymce .mce-toolbar .mce-btn-group .mce-btn.mce-listbox {
  border-radius: 2px;
  border-color: #b0b6bd;
}

.wpforms-field-has-tinymce .mce-toolbar .mce-widget.mce-btn {
  margin-bottom: 5px;
}

.wpforms-field-has-tinymce .mce-toolbar .mce-ico {
  color: #777777;
}

.wpforms-field-has-tinymce .wpforms-field-option-row-content {
  margin-bottom: 10px;
}

.wpforms-field-has-tinymce .wpforms-field-option-row-content .quicktags-toolbar {
  border: 1px solid #b0b6bd;
  border-bottom: none;
  border-start-start-radius: 4px;
  background-color: #f8f8f8;
  height: 37px;
  overflow: hidden;
}

.wpforms-field-has-tinymce .wpforms-field-option-row-content textarea.wp-editor-area {
  border: solid 1px #b0b6bd;
  border-start-start-radius: 0;
  border-start-end-radius: 0;
  border-end-start-radius: 4px;
  border-end-end-radius: 4px;
  width: 100%;
  margin-top: 0;
}

.wpforms-field-has-tinymce .wpforms-field-option-row-content textarea.wp-editor-area:focus {
  border-color: #b0b6bd;
  box-shadow: none;
}

.wpforms-field-has-tinymce .wpforms-field-content-action-buttons {
  display: flex;
  justify-content: space-between;
}

.wpforms-field-has-tinymce .wpforms-field-content-action-buttons button {
  height: 32px;
  font-size: 14px;
  line-height: 17px;
  padding: 0 9px;
  border-radius: 4px;
}

.wpforms-field-has-tinymce .wpforms-field-content-action-buttons button svg {
  position: relative;
  top: 2px;
  margin-right: 4px;
}

.wpforms-field-has-tinymce .wpforms-field-content-action-buttons button .wpforms-expand-button-label {
  padding-left: 4px;
}

.wpforms-field-has-tinymce .wpforms-field-content-action-buttons .update-preview {
  display: none;
  background-color: #036aab;
  color: #ffffff;
  box-shadow: none;
  border-color: #036aab;
}

.wpforms-field-has-tinymce .wpforms-field-content-action-buttons .update-preview:hover {
  background-color: #215d8f;
  border-color: #215d8f;
  color: #ffffff;
}

.wpforms-field-has-tinymce .wpforms-field-content-action-buttons .expand-editor {
  margin-inline-start: auto;
  color: #036aab;
  box-shadow: none;
  border-color: #036aab;
  background: rgba(255, 255, 255, 0.5);
}

.wpforms-field-has-tinymce .wpforms-field-content-action-buttons .expand-editor:hover {
  color: #024068;
  border-color: #024068;
  background: rgba(255, 255, 255, 0.5);
}

.wpforms-field-has-tinymce .wpforms-field-content-action-buttons .expand-editor svg {
  width: 14px;
  height: 14px;
}

.wpforms-field-has-tinymce .wpforms-field-content-action-buttons .expand-editor path {
  fill: currentColor;
}

.wpforms-field-has-tinymce .wpforms-field-content-action-buttons .expand-editor svg.collapse {
  display: none;
}

.wpforms-field-has-tinymce .wpforms-field-content-action-buttons .expand-editor svg.expand {
  display: inline-block;
}

.wpforms-field-has-tinymce .wpforms-field-content-action-buttons .expand-editor.wpforms-content-editor-expanded svg.collapse {
  display: inline-block;
}

.wpforms-field-has-tinymce .wpforms-field-content-action-buttons .expand-editor.wpforms-content-editor-expanded svg.expand {
  display: none;
}

.wpforms-field-has-tinymce .wp-media-buttons {
  padding-bottom: 6px;
}

.wpforms-field-has-tinymce .wp-media-buttons button.insert-media {
  color: #036aab;
  border-color: currentColor;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.5);
  vertical-align: top;
  box-shadow: none;
  min-height: 30px;
  font-size: 14px;
  padding: 6px 10px 4px 6px;
  margin-bottom: 6px;
  line-height: 17px;
  height: unset;
}

.wpforms-field-has-tinymce .wp-media-buttons button.insert-media:hover {
  color: #024068;
}

.wpforms-field-has-tinymce .wp-media-buttons button.insert-media span.wp-media-buttons-icon {
  width: 20px;
  height: 20px;
  line-height: 1;
  vertical-align: middle;
}

.wpforms-field-has-tinymce .wp-media-buttons button:active {
  position: initial;
  margin-top: 0px;
  margin-bottom: 6px;
  top: 0px;
}

.wpforms-field-has-tinymce .wp-media-buttons > *:not(.insert-media) {
  display: none !important;
}

.wpforms-field-has-tinymce .wp-editor-tabs button {
  border-start-end-radius: 4px;
  border-start-start-radius: 4px;
  border-color: #b0b6bd;
  border-bottom-color: #f8f8f8;
  background: #f8f8f8;
  color: #86919e;
  position: relative;
}

.wpforms-field-has-tinymce .wp-editor-tabs button:before, .wpforms-field-has-tinymce .wp-editor-tabs button:after {
  content: '';
  position: absolute;
  bottom: -1px;
  display: block;
  width: 1px;
  height: 50%;
  background-color: #b0b6bd;
}

.wpforms-field-has-tinymce .wp-editor-tabs button:before {
  left: -1px;
}

.wpforms-field-has-tinymce .wp-editor-tabs button:after {
  right: -1px;
}

.wpforms-field-has-tinymce .tmce-active .wp-editor-tabs button.switch-html, .wpforms-field-has-tinymce .html-active .wp-editor-tabs button.switch-tmce {
  border-bottom-color: #b0b6bd;
  background-color: #eeeeee;
}

.wpforms-field-has-tinymce .html-active .wp-editor-container {
  border: none;
}

.wpforms-field-has-tinymce .wp-editor-wrap.tmce-initialized > .wp-editor-container {
  border-width: 0;
}

.wpforms-field-has-tinymce .wp-editor-container, .wpforms-field-has-tinymce .mce-panel, .wpforms-field-has-tinymce .mce-container-body {
  border-radius: 4px;
  border-color: #b0b6bd;
  background: #f8f8f8;
}

.wpforms-field-has-tinymce .wp-editor-container.mce-statusbar, .wpforms-field-has-tinymce .mce-panel.mce-statusbar, .wpforms-field-has-tinymce .mce-container-body.mce-statusbar {
  border-start-start-radius: 0;
}

.wpforms-field-has-tinymce .wp-editor-container iframe, .wpforms-field-has-tinymce .mce-panel iframe, .wpforms-field-has-tinymce .mce-container-body iframe {
  max-height: 57vh;
}

.wpforms-field-has-tinymce .mce-first > .mce-container-body, .wpforms-field-has-tinymce .mce-first > .mce-container-body > .mce-toolbar-grp {
  border-radius: 0;
  border-start-end-radius: 4px;
}

.wpforms-field-has-tinymce .mce-tinymce > .mce-container-body.mce-stack-layout {
  border-radius: 4px;
}

.wpforms-field-has-tinymce .wp-core-ui.wp-editor-wrap.tinymce-active > .mce-tinymce.mce-container.mce-panel > .mce-stack-layout {
  border: solid 1px #b0b6bd;
}

.wpforms-field-has-tinymce .mce-statusbar, .wpforms-field-has-tinymce .mce-statusbar div {
  background-color: #ffffff;
  border-start-end-radius: 0;
  border-start-start-radius: 0;
  border-end-end-radius: 4px;
  border-end-start-radius: 4px;
}

.wpforms-field-has-tinymce .mce-top-part::before {
  box-shadow: 0 0 2px #b0b6bd !important;
}

.wpforms-field-has-tinymce .wp-core-ui .ed_button.button {
  box-shadow: none;
}

.wpforms-field-has-tinymce .wp-core-ui .ed_button.button:hover {
  border-color: #86919e;
}

.wpforms-field-has-tinymce .wp-editor-tabs {
  position: absolute;
  bottom: 0;
  inset-inline-end: 0;
}

.wpforms-panel-sidebar:not(.wpforms-content-editor-expanded) .wpforms-expandable-editor .quicktags-toolbar .ed_button:nth-last-child(-n+3) {
  display: none;
}

.tmce-initialized .mce-top-part.mce-container.mce-stack-layout-item.mce-first {
  border: 1px solid #b0b6bd !important;
  border-radius: 0;
  border-start-start-radius: 4px;
  border-bottom-width: 1px !important;
}

.tmce-initialized .mce-top-part.mce-container.mce-stack-layout-item.mce-first {
  border-bottom: none !important;
}

.tmce-initialized .mce-edit-area.mce-container.mce-panel.mce-stack-layout-item {
  border: 1px solid #b0b6bd !important;
  border-top: none !important;
  border-bottom: none !important;
  border-radius: 0;
}

.tmce-initialized .mce-statusbar.mce-container.mce-panel.mce-stack-layout-item.mce-last {
  border: 1px solid #b0b6bd !important;
  border-start-end-radius: 0;
  border-start-start-radius: 0;
  border-end-end-radius: 4px;
  border-end-start-radius: 4px;
  border-top: none;
}

.media-modal p.description {
  line-height: 1.5;
}

.media-modal select {
  appearance: revert;
  background-color: white;
  background-image: none;
  padding: 5px;
}

.media-modal fieldset {
  border: none;
  padding: 0;
  margin: 0;
}

.media-modal fieldset legend.legend-inline {
  margin-left: -2%;
}

.media-modal fieldset .setting.align .button-group {
  margin-top: 3px !important;
}

.media-modal .attachments-browser .media-toolbar-secondary > .media-button.media-button-backToLibrary {
  margin-right: 10px;
}

.wpforms_page_wpforms-builder .mce-colorbutton-grid .mce-colorbtn-trans div {
  line-height: 14px;
}

.wpforms_page_wpforms-builder #wp-link .query-results {
  position: static;
  margin: auto;
}

.wpforms_page_wpforms-builder #wp-link .link-target {
  margin-top: 5px;
}

.wpforms-panel-fields .wpforms-field-option-date-time .format-selected-time .wpforms-field-option-row-date {
  display: none;
}

.wpforms-panel-fields .wpforms-field-option-date-time .format-selected-date .wpforms-field-option-row-time {
  display: none;
}

.wpforms-panel-fields .wpforms-field-option-date-time.wpforms-date-type-dropdown option.datepicker-only {
  display: none;
}

.wpforms-panel-fields .wpforms-field-option-date-time.wpforms-date-type-dropdown .wpforms-field-option-row-date .placeholder {
  display: none;
}

.wpforms-panel-fields .wpforms-field-date-time .format-selected input[type=text] {
  width: 100% !important;
  min-width: initial;
}

.wpforms-panel-fields .wpforms-field-date-time.size-large .format-selected {
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-date-time.size-medium .format-selected,
.wpforms-panel-fields .wpforms-field-date-time .format-selected {
  width: 60%;
  min-width: 250px;
}

.wpforms-panel-fields .wpforms-field-date-time.size-small .format-selected {
  width: 25%;
  flex-direction: column;
}

.wpforms-panel-fields .wpforms-field-date-time.size-small .format-selected.format-selected-date-time .wpforms-date-type-datepicker,
.wpforms-panel-fields .wpforms-field-date-time.size-small .format-selected.format-selected-date-time .wpforms-time {
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-date-time.size-small .format-selected .wpforms-date-type-dropdown + .wpforms-time {
  min-width: 100%;
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-date-time .format-selected-date .wpforms-time,
.wpforms-panel-fields .wpforms-field-date-time .format-selected-date .wpforms-sub-label {
  display: none;
}

.wpforms-panel-fields .wpforms-field-date-time .format-selected-time .wpforms-date,
.wpforms-panel-fields .wpforms-field-date-time .format-selected-time .wpforms-sub-label {
  display: none;
}

.wpforms-panel-fields .wpforms-field-date-time .format-selected-date-time {
  display: flex;
  flex-direction: row;
  gap: 10px 20px;
}

.wpforms-panel-fields .wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker {
  width: 50%;
}

.wpforms-panel-fields .wpforms-field-date-time .format-selected-date-time .wpforms-date-type-datepicker + .wpforms-time {
  width: 50%;
}

.wpforms-panel-fields .wpforms-field-date-time .wpforms-date-dropdown {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  column-gap: 10px;
  min-width: 60%;
}

.wpforms-panel-fields .wpforms-field-date-time .wpforms-date-dropdown select {
  padding-inline: 6px;
  width: calc( 100% / 3 - 20px / 3) !important;
  min-width: initial !important;
}

.wpforms-panel-fields .wpforms-field-date-time .wpforms-date-type-dropdown {
  flex-grow: 1;
}

.wpforms-panel-fields .wpforms-field-date-time .wpforms-date-type-dropdown .wpforms-date-datepicker {
  display: none;
}

.wpforms-panel-fields .wpforms-field-date-time .wpforms-date-type-dropdown + .wpforms-time {
  flex: 1;
  max-width: calc( 40% - 20px);
}

.wpforms-panel-fields .wpforms-field-date-time .wpforms-date-type-datepicker .wpforms-date-dropdown {
  display: none;
}

@media screen and (max-width: 1140px) {
  .wpforms-panel-fields .wpforms-field-date-time select {
    padding-left: 10px;
    padding-right: 10px;
    color: transparent !important;
  }
}

.wpforms-panel-fields .wpforms-field-divider {
  padding-top: 20px;
  min-height: 82px;
  margin-top: 40px;
  /* Display top border over the divider. */
  /* Hide top border on the first divider and on hover. */
}

.wpforms-panel-fields .wpforms-field-divider:first-child {
  margin-top: 0;
}

.wpforms-panel-fields .wpforms-field-divider::before {
  content: '';
  display: block;
  height: 1px;
  background-color: #dddddd;
  position: absolute;
  top: 0;
  left: 15px;
  right: 15px;
}

.wpforms-panel-fields .wpforms-field-divider:hover::before, .wpforms-panel-fields .wpforms-field-divider:focus::before, .wpforms-panel-fields .wpforms-field-divider.active::before, .wpforms-panel-fields .wpforms-field-divider:first-child::before {
  display: none;
}

.wpforms-panel-fields .wpforms-field-divider label.label-title {
  font-size: 18px;
  margin-top: 10px;
}

.wpforms-panel-fields .wpforms-field-divider .description {
  font-size: 14px;
}

.wpforms-panel-fields .wpforms-field-file-upload .wpforms-hide {
  display: none !important;
}

.wpforms-panel-fields .wpforms-field-file-upload .wpforms-file-upload-builder-modern {
  background: #f8f8f8;
  border: 1px dashed #cccccc;
  border-radius: 4px;
  color: #777777;
  line-height: 1.5;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.wpforms-panel-fields .wpforms-field-file-upload .wpforms-file-upload-builder-modern svg {
  height: 40px;
  margin-bottom: 10px;
  opacity: .5;
  width: 40px;
}

.wpforms-panel-fields .wpforms-field-file-upload .wpforms-file-upload-builder-modern .modern-hint {
  color: rgba(0, 0, 0, 0.35);
}

.wpforms-panel-fields .wpforms-field-file-upload.active .wpforms-file-upload-builder-modern {
  background: #ffffff;
}

.wpforms-panel-fields .wpforms-field-file-upload .wpforms-file-upload-builder-classic input[type=file] {
  font-size: 14px;
  line-height: 16px;
  padding-top: 8px;
}

.wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-field-option-row-protection_password {
  position: relative;
}

.wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-file-upload-password-clean {
  position: absolute;
  inset-inline-end: 0;
  top: 7px;
  padding: 0;
  border: none;
  background-color: transparent;
  margin: 0;
  color: #a7aaad;
  cursor: pointer;
}

.wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-file-upload-password-clean:hover {
  color: #d63638;
}

.wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-file-upload-password-clean i {
  font-size: 16px;
}

.wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-field-option-row-protection_password_label {
  margin-bottom: 5px;
}

.wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-field-option-row-protection_password_columns .wpforms-field-options-columns {
  gap: 10px;
}

.wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-field-option-row-protection_password_columns .wpforms-field-option-row-protection_password,
.wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-field-option-row-protection_password_columns .wpforms-field-option-row-protection_password_confirm {
  width: 100%;
  margin-bottom: 0;
}

.wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-field-option-row-protection_password_columns .wpforms-field-option-row-protection_password input,
.wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-field-option-row-protection_password_columns .wpforms-field-option-row-protection_password_confirm input {
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-field-option-row-protection_password_columns .wpforms-field-option-row-protection_password input {
  padding-inline-end: 30px;
}

.wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-error {
  color: #d63638;
}

.wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-error-message {
  position: absolute;
  font-size: 12px;
  margin: 2px 0 0 1px;
}

.wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-file-upload-user-roles-select + .choices__list .choices__item[data-value="administrator"] {
  background-color: #86919e;
  border-color: #86919e;
}

.wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-file-upload-user-roles-select + .choices__list .choices__item[data-value="administrator"] button, .wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-file-upload-user-roles-select + .choices__list .choices__item[data-value="administrator"]:hover {
  cursor: not-allowed;
}

.wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-file-upload-user-roles-select + .choices__list .choices__item[data-value="administrator"] button:hover {
  opacity: .75;
}

.wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-field-option-row-user_roles_restrictions .choices__inner .choices__input {
  display: none;
}

.wpforms-panel-fields .wpforms-field-option-file-upload .sub-label {
  display: block;
}

.wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-field-option-row-access_restrictions[post-submissions-disabled="1"] {
  opacity: .5;
}

.wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-field-option-row-access_restrictions[post-submissions-disabled="1"], .wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-field-option-row-access_restrictions[post-submissions-disabled="1"] * {
  cursor: not-allowed;
}

.wpforms-panel-fields .wpforms-field-option-file-upload .wpforms-field-option-row-access_restrictions[post-submissions-disabled="1"] i {
  pointer-events: none;
}

.wpforms-panel-fields .wpforms-field-html .label-title .text {
  margin: 0 0 15px 0;
}

.wpforms-panel-fields .wpforms-field-html .label-title .text:empty {
  margin: 0;
}

.wpforms-panel-fields .wpforms-field-html .label-title .grey {
  color: #777777;
}

.wpforms-panel-fields .wpforms-field-pagebreak .wpforms-pagebreak-buttons {
  overflow: hidden;
  text-align: center;
}

.wpforms-panel-fields .wpforms-field-pagebreak .wpforms-pagebreak-buttons.wpforms-pagebreak-buttons-left {
  text-align: left;
}

.wpforms-panel-fields .wpforms-field-pagebreak .wpforms-pagebreak-buttons.wpforms-pagebreak-buttons-left .wpforms-pagebreak-button {
  margin: 0 20px 0 0;
}

.wpforms-panel-fields .wpforms-field-pagebreak .wpforms-pagebreak-buttons.wpforms-pagebreak-buttons-right {
  text-align: right;
}

.wpforms-panel-fields .wpforms-field-pagebreak .wpforms-pagebreak-buttons.wpforms-pagebreak-buttons-right .wpforms-pagebreak-button {
  margin: 0 0 0 20px;
}

.wpforms-panel-fields .wpforms-field-pagebreak .wpforms-pagebreak-buttons.wpforms-pagebreak-buttons-split .wpforms-pagebreak-prev {
  float: left;
  margin: 0;
}

.wpforms-panel-fields .wpforms-field-pagebreak .wpforms-pagebreak-buttons.wpforms-pagebreak-buttons-split .wpforms-pagebreak-next {
  float: right;
  margin: 0;
}

.wpforms-panel-fields .wpforms-field-pagebreak .wpforms-pagebreak-button {
  background: #999999;
  border: none;
  border-radius: 4px;
  color: #ffffff;
  cursor: pointer;
  display: inline-block;
  font-size: 17px;
  font-weight: 600;
  line-height: 21px;
  margin: 0 10px;
  min-width: 85px;
  padding: 10px 15px;
}

.wpforms-panel-fields .wpforms-field-pagebreak .wpforms-pagebreak-button.wpforms-hidden {
  display: none;
}

.wpforms-panel-fields .wpforms-field-pagebreak .wpforms-pagebreak-divider {
  height: 30px;
  position: relative;
  text-align: center;
}

.wpforms-panel-fields .wpforms-field-pagebreak .line {
  border-top: 1px dashed #cccccc;
  display: block;
  left: 0;
  position: absolute;
  top: 50%;
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-pagebreak.active .pagebreak-label {
  background-color: #f8f8f8;
}

.wpforms-panel-fields .wpforms-field-pagebreak .pagebreak-label {
  background-color: #ffffff;
  display: inline-block;
  font-size: 16px;
  font-weight: 600;
  padding: 5px 20px;
  position: relative;
  z-index: 10;
}

.wpforms-panel-fields .wpforms-field-pagebreak .pagebreak-label .wpforms-badge {
  margin-inline-start: 15px;
  vertical-align: top;
  margin: -2px 0 0 10px;
}

.wpforms-panel-fields .wpforms-field-pagebreak .wpforms-pagebreak-title {
  color: #777777;
  font-weight: 400;
}

.wpforms-panel-fields .wpforms-field-pagebreak .wpforms-pagebreak-title:not(:empty):after {
  content: ')';
}

.wpforms-panel-fields .wpforms-field-pagebreak .wpforms-pagebreak-title:not(:empty):before {
  content: '(';
}

.wpforms-panel-fields .wpforms-field-pagebreak.wpforms-pagebreak-normal {
  border: none;
  margin: 0 -15px 20px -15px;
  padding: 0;
}

.wpforms-panel-fields .wpforms-field-pagebreak.wpforms-pagebreak-normal .wpforms-pagebreak-divider {
  background-color: #626262;
  height: 60px;
  padding-top: 16px;
}

.wpforms-panel-fields .wpforms-field-pagebreak.wpforms-pagebreak-normal .pagebreak-label {
  background-color: #626262;
  color: #eeeeee;
  font-weight: 400;
}

.wpforms-panel-fields .wpforms-field-pagebreak.wpforms-pagebreak-normal .wpforms-pagebreak-title {
  color: #d6d6d6;
}

.wpforms-panel-fields .wpforms-field-pagebreak.wpforms-pagebreak-normal .wpforms-pagebreak-buttons {
  border: 1px solid #ffffff;
  border-radius: 6px;
  margin: 0 15px 5px 15px;
  padding: 15px;
}

.wpforms-panel-fields .wpforms-field-pagebreak.wpforms-pagebreak-normal:hover, .wpforms-panel-fields .wpforms-field-pagebreak.wpforms-pagebreak-normal.active {
  border: none;
}

.wpforms-panel-fields .wpforms-field-pagebreak.wpforms-pagebreak-normal:hover .wpforms-pagebreak-divider,
.wpforms-panel-fields .wpforms-field-pagebreak.wpforms-pagebreak-normal:hover .pagebreak-label, .wpforms-panel-fields .wpforms-field-pagebreak.wpforms-pagebreak-normal.active .wpforms-pagebreak-divider,
.wpforms-panel-fields .wpforms-field-pagebreak.wpforms-pagebreak-normal.active .pagebreak-label {
  background-color: #6d6d6d;
}

.wpforms-panel-fields .wpforms-field-pagebreak.wpforms-pagebreak-normal:hover .wpforms-pagebreak-buttons {
  border: 1px dashed #cccccc;
}

.wpforms-panel-fields .wpforms-field-pagebreak.wpforms-pagebreak-normal.active {
  background-color: #ffffff;
}

.wpforms-panel-fields .wpforms-field-pagebreak.wpforms-pagebreak-normal.active .wpforms-pagebreak-buttons {
  background-color: #f8f8f8;
  border: 1px dashed #cccccc;
}

.wpforms-panel-fields .wpforms-field-pagebreak.wpforms-pagebreak-normal .wpforms-field-delete {
  inset-inline-end: 30px;
}

.wpforms-panel-fields .wpforms-field-pagebreak.wpforms-pagebreak-normal .wpforms-field-helper {
  bottom: 66px;
  inset-inline-end: 16px;
}

.wpforms-panel-fields .wpforms-field-pagebreak.wpforms-pagebreak-normal .wpforms-badge {
  background-color: #444444;
  color: #999999;
}

.wpforms-panel-fields .wpforms-field-pagebreak.wpforms-pagebreak-bottom .wpforms-pagebreak-buttons {
  margin: 0;
}

.wpforms-panel-fields .wpforms-field-phone-input-container {
  position: relative;
}

.wpforms-panel-fields .wpforms-field-phone-input-container[data-format="smart"] .wpforms-field-phone-country-container {
  display: flex;
}

.wpforms-panel-fields .wpforms-field-phone-input-container[data-format="smart"] input.primary-input {
  padding-inline-start: 52px;
}

.wpforms-panel-fields .wpforms-field-phone-country-container {
  position: absolute;
  height: 100%;
  top: 0;
  padding: 0 6px 0 8px;
  align-items: center;
  gap: 6px;
  display: none;
}

.wpforms-panel-fields .wpforms-field-phone-flag {
  height: 11px;
  width: 20px;
  box-shadow: 0 0 1px 0 #888;
  background: url("../../images/phone/us-flag.png") no-repeat;
  background-size: 100%;
}

.wpforms-panel-fields .wpforms-field-phone-arrow {
  border-inline: calc( 5px / 2) solid transparent;
  border-top: 4px solid #555;
}

.wpforms-field.wpforms-field-content {
  padding: 12px 15px;
}

.wpforms-field-content-preview {
  min-height: 16px;
  width: 95%;
  color: #444444;
  word-break: break-word;
  font-size: 16px;
  line-height: 22px;
}

.wpforms-field-content-preview .wpforms-field-content-preview-end {
  clear: both;
}

.wpforms-field-content-preview h1, .wpforms-field-content-preview h2, .wpforms-field-content-preview h3, .wpforms-field-content-preview h4, .wpforms-field-content-preview h5, .wpforms-field-content-preview h6 {
  margin: 20px 0;
  padding: 0;
  clear: unset;
}

.wpforms-field-content-preview h1:first-child, .wpforms-field-content-preview h2:first-child, .wpforms-field-content-preview h3:first-child, .wpforms-field-content-preview h4:first-child, .wpforms-field-content-preview h5:first-child, .wpforms-field-content-preview h6:first-child {
  margin-top: 0;
}

.wpforms-field-content-preview h1 {
  font-size: 32px;
  line-height: 40px;
}

.wpforms-field-content-preview h2 {
  font-size: 28px;
  line-height: 36px;
}

.wpforms-field-content-preview h3 {
  font-size: 24px;
  line-height: 32px;
}

.wpforms-field-content-preview h4 {
  font-size: 20px;
  line-height: 28px;
}

.wpforms-field-content-preview h5 {
  font-size: 18px;
  line-height: 26px;
}

.wpforms-field-content-preview h6 {
  font-size: 16px;
  line-height: 24px;
  text-transform: uppercase;
}

.wpforms-field-content-preview p, .wpforms-field-content-preview blockquote, .wpforms-field-content-preview pre, .wpforms-field-content-preview table {
  margin: 0 0 20px 0;
}

.wpforms-field-content-preview li {
  margin: 0 0 10px 0;
}

.wpforms-field-content-preview a {
  text-decoration: underline;
}

.wpforms-field-content-preview a:hover {
  text-decoration: none;
}

.wpforms-field-content-preview code, .wpforms-field-content-preview pre {
  font-family: monospace;
  overflow: auto;
}

.wpforms-field-content-preview del {
  text-decoration: line-through;
}

.wpforms-field-content-preview ins {
  text-decoration: underline;
}

.wpforms-field-content-preview small {
  font-size: smaller;
}

.wpforms-field-content-preview dt {
  margin: 5px 0;
}

.wpforms-field-content-preview dd {
  margin-left: 25px;
}

.wpforms-field-content-preview abbr, .wpforms-field-content-preview acronym {
  text-decoration: underline dotted;
}

.wpforms-field-content-preview ul {
  list-style: disc outside none !important;
  padding-inline-start: 29px !important;
  margin-bottom: 20px !important;
}

.wpforms-field-content-preview ul ul {
  list-style-type: circle !important;
  margin-top: 10px !important;
  margin-bottom: 0 !important;
}

.wpforms-field-content-preview ul ul ul {
  list-style-type: square !important;
}

.wpforms-field-content-preview ul ol {
  margin-top: 10px;
  margin-bottom: 0;
}

.wpforms-field-content-preview ul li {
  list-style: inherit !important;
  margin-bottom: 10px !important;
}

.wpforms-field-content-preview ol {
  list-style: decimal outside none;
  padding-inline-start: 29px;
  margin-bottom: 20px;
}

.wpforms-field-content-preview ol ol {
  margin-top: 10px;
  margin-bottom: 0;
}

.wpforms-field-content-preview ol ul {
  margin-top: 10px !important;
  margin-bottom: 0 !important;
}

.wpforms-field-content-preview ol li {
  list-style: inherit;
}

.wpforms-field-content-preview blockquote {
  border-left: 4px solid rgba(0, 0, 0, 0.15);
  padding-left: 20px;
}

.wpforms-field-content-preview blockquote:before, .wpforms-field-content-preview blockquote:after {
  display: none;
}

.wpforms-field-content-preview table {
  width: 100%;
  border-collapse: collapse;
  word-break: normal;
}

.wpforms-field-content-preview table th, .wpforms-field-content-preview table td {
  padding: 0.5em;
  border: 1px solid;
}

.wpforms-field-content-preview sup, .wpforms-field-content-preview sub {
  font-size: smaller;
  line-height: calc( 100% + 11px);
}

.wpforms-field-content-preview sup {
  vertical-align: super;
}

.wpforms-field-content-preview sub {
  vertical-align: sub;
}

.wpforms-field-content-preview h1, .wpforms-field-content-preview h2, .wpforms-field-content-preview h3, .wpforms-field-content-preview h4, .wpforms-field-content-preview h5, .wpforms-field-content-preview h6 {
  margin: 10px 0;
  font-weight: 600;
}

.wpforms-field-content-preview p {
  line-height: inherit;
}

.wpforms-field-content-preview img {
  vertical-align: top;
}

.wpforms-field-content-preview img {
  max-width: 100%;
  height: auto;
}

.wpforms-field-content-preview .alignleft {
  float: left;
  margin: 0 30px 20px 0;
}

.wpforms-field-content-preview .alignright {
  float: right;
  margin: 0 0 20px 30px;
}

.wpforms-field-content-preview .aligncenter {
  display: block;
  clear: both;
  text-align: center;
  margin: 0 auto 20px;
}

.wpforms-field-content-preview .alignnone {
  display: block;
  clear: both;
  margin: 0 0 20px 0;
}

.wpforms-field-content-preview .wp-caption-dt,
.wpforms-field-content-preview .wp-caption-dd {
  margin: 0;
}

.wpforms-field-content-preview .wp-caption {
  position: relative;
  left: auto;
  right: auto;
  transform: none;
  max-width: 100%;
}

.wpforms-field-content-preview .wp-caption .wp-caption-text,
.wpforms-field-content-preview .wp-caption .wp-caption-dd {
  text-align: center;
  font-size: 14px;
  margin-top: 0.5em;
}

.wpforms-field-content-preview > :nth-last-child(2) {
  margin-bottom: 0;
}

.size-medium .wpforms-field-content-preview, .wpforms-field-content-preview {
  width: 60%;
}

.size-small .wpforms-field-content-preview {
  width: 25%;
}

.size-large .wpforms-field-content-preview {
  width: 95%;
}

.wpforms-field .wpforms-layout-column .wpforms-field .wpforms-field-content-preview > *:first-child,
.wpforms-field.wpforms-field-drag-to-column .wpforms-field-content-preview > *:first-child {
  padding-right: 60px;
}

.wpforms-panel-fields .wpforms-field-credit-card .format-selected input[type=text],
.wpforms-panel-fields .wpforms-field-credit-card .format-selected select {
  width: 100%;
  min-width: initial;
}

.wpforms-panel-fields .wpforms-field-credit-card.size-large .format-selected {
  width: 100%;
}

.wpforms-panel-fields .wpforms-field-credit-card .format-selected, .wpforms-panel-fields .wpforms-field-credit-card.size-medium .format-selected {
  width: 60%;
  min-width: 250px;
}

.wpforms-panel-fields .wpforms-field-credit-card.size-small .format-selected {
  width: 25%;
}

.wpforms-panel-fields .wpforms-field-credit-card .wpforms-sub-label {
  margin: 0 0 5px 1px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.wpforms-panel-fields .wpforms-field-credit-card .wpforms-credit-card-cardnumber {
  margin-right: 115px;
}

.wpforms-panel-fields .wpforms-field-credit-card .wpforms-credit-card-cardcvc {
  position: absolute;
  right: 0;
  top: 0;
  width: 92px;
}

.wpforms-panel-fields .wpforms-field-credit-card .wpforms-credit-card-cardname {
  margin-right: 172px;
}

.wpforms-panel-fields .wpforms-field-credit-card .wpforms-credit-card-expiration {
  position: absolute;
  right: 0;
  top: 0;
  width: 150px;
}

.wpforms-panel-fields .wpforms-field-credit-card .wpforms-credit-card-expiration span {
  float: left;
  padding-top: 8px;
  text-align: center;
  width: 16%;
}

.wpforms-panel-fields .wpforms-field-credit-card .wpforms-credit-card-cardmonth,
.wpforms-panel-fields .wpforms-field-credit-card .wpforms-credit-card-cardyear {
  display: block;
  float: left;
  width: 42%;
}

.wpforms-panel-fields .wpforms-field-payment-single.payment-quantity-enabled .format-selected-single {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.wpforms-panel-fields .wpforms-field-payment-single.payment-quantity-enabled .format-selected-single p.item-price {
  float: left;
  line-height: 30px;
  width: calc( 60% - 85px);
}

.wpforms-panel-fields .wpforms-field-payment-single.payment-quantity-enabled .format-selected-single select.quantity-input {
  height: 30px;
  min-height: 30px;
  font-size: 14px;
}

.wpforms-panel-fields .wpforms-field-payment-single.payment-quantity-enabled .format-selected-single .description {
  padding-top: 5px;
  margin: 0;
  flex-basis: 100%;
}

.wpforms-panel-fields .wpforms-field-payment-single.payment-quantity-enabled.size-small p.item-price {
  width: calc( 25% - 85px);
  min-width: 165px;
}

.wpforms-panel-fields .wpforms-field-payment-single.payment-quantity-enabled.size-large p.item-price {
  width: calc( 100% - 85px);
}

.wpforms-panel-fields .wpforms-field-payment-single .single-item-user-defined-block {
  display: flex;
}

.wpforms-panel-fields .wpforms-field-payment-single .format-selected-single .primary-input,
.wpforms-panel-fields .wpforms-field-payment-single .format-selected-single .item-min-price,
.wpforms-panel-fields .wpforms-field-payment-single .format-selected-single .item-price-hidden,
.wpforms-panel-fields .wpforms-field-payment-single .format-selected-single .item-price-hidden-note,
.wpforms-panel-fields .wpforms-field-payment-single .format-selected-single .fa-exclamation-triangle {
  display: none;
}

.wpforms-panel-fields .wpforms-field-payment-single .format-selected-hidden .primary-input,
.wpforms-panel-fields .wpforms-field-payment-single .format-selected-hidden .item-min-price,
.wpforms-panel-fields .wpforms-field-payment-single .format-selected-hidden .fa-exclamation-triangle {
  display: none;
}

.wpforms-panel-fields .wpforms-field-payment-single .format-selected-hidden .item-price-hidden,
.wpforms-panel-fields .wpforms-field-payment-single .format-selected-hidden .item-price-hidden-note {
  display: block;
}

.wpforms-panel-fields .wpforms-field-payment-single .format-selected-user .primary-input,
.wpforms-panel-fields .wpforms-field-payment-single .format-selected-user .item-min-price {
  display: block;
}

.wpforms-panel-fields .wpforms-field-payment-single .format-selected-user .item-min-price {
  clear: both;
  color: #777777;
  font-size: 14px;
  margin: 5px 0 0 0;
  display: block;
}

.wpforms-panel-fields .wpforms-field-payment-single .format-selected-user .item-price,
.wpforms-panel-fields .wpforms-field-payment-single .format-selected-user .item-price-hidden,
.wpforms-panel-fields .wpforms-field-payment-single .format-selected-user .item-price-hidden-note {
  display: none;
}

.wpforms-panel-fields .wpforms-field-payment-single .format-selected-user .fa-exclamation-triangle {
  font-size: 16px;
  color: #ffb900;
  padding-left: 20px;
  line-height: 40px;
}

.wpforms-panel-fields .wpforms-field-payment-single .item-price-hidden-note {
  color: #999999;
  font-size: 13px;
  margin: 0;
}

.wpforms-panel-fields .wpforms-field-payment-single .item-price {
  margin-bottom: 0;
}

.wpforms-panel-fields .wpforms-field-option-payment-single .wpforms-item-minimum-price-alert {
  margin-top: 20px;
}

.wpforms-panel-fields .wpforms-layout-column .min-price-warning .single-item-user-defined-block {
  margin-right: 35px;
}

.wpforms-order-summary-container {
  display: block;
  max-width: 60%;
}

.wpforms-order-summary-container * {
  word-break: break-word;
  box-sizing: border-box;
}

.wpforms-order-summary-container table.wpforms-order-summary-preview {
  width: 100%;
  table-layout: fixed;
}

.wpforms-order-summary-container table.wpforms-order-summary-preview tr td, .wpforms-order-summary-container table.wpforms-order-summary-preview tr th {
  text-align: center;
}

.wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: left;
}

.rtl .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: right;
}

.wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
  width: 8ch;
}

.wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

@media (max-width: 600px) {
  .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
    width: 4ch;
  }
  .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
    display: inline;
  }
  .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full, .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full {
    display: none;
  }
}

.wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  width: 6ch;
  text-align: right;
}

.rtl .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  text-align: left;
}

.wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: left;
}

.rtl .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: right;
}

table.wpforms-order-summary-preview {
  border-radius: 4px;
  border: 1px solid #e2e2e2;
  border-collapse: separate;
}

table.wpforms-order-summary-preview tr td {
  border-top: 1px solid #e2e2e2;
  border-bottom: none;
  border-left: none;
  border-right: none;
}

table.wpforms-order-summary-preview tr th {
  font-weight: 400;
  border: none;
}

table.wpforms-order-summary-preview tr td, table.wpforms-order-summary-preview tr th {
  padding: 9px 0;
  line-height: 20px;
  background: none;
}

table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-wrap: balance;
  padding-left: 10px;
}

.rtl table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  padding-right: 10px;
  padding-left: 0;
}

table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  padding-right: 10px;
}

.rtl table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  padding-left: 10px;
  padding-right: 0;
}

table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  padding-left: 10px;
}

.rtl table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  padding-right: 10px;
  padding-left: 0;
}

table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td {
  font-weight: 700;
}

.size-large > .wpforms-order-summary-container,
.wpforms-field-large > .wpforms-order-summary-container {
  max-width: 100%;
}

.size-medium > .wpforms-order-summary-container,
.wpforms-field-medium > .wpforms-order-summary-container {
  max-width: 60%;
}

.wpforms-field-payment-total .wpforms-order-summary-container {
  display: none;
}

.wpforms-field-payment-total.wpforms-summary-enabled .wpforms-order-summary-container {
  display: block;
}

.wpforms-field-payment-total.wpforms-summary-enabled .wpforms-total-amount {
  display: none;
}

.wpforms-order-summary-container table.wpforms-order-summary-preview {
  border-color: #cccccc;
}

.wpforms-order-summary-container table.wpforms-order-summary-preview tr th, .wpforms-order-summary-container table.wpforms-order-summary-preview tr td {
  padding-top: 8px;
  padding-bottom: 8px;
}

.wpforms-order-summary-container table.wpforms-order-summary-preview tr td {
  color: #777777;
  border-color: #cccccc;
}

.wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td,
.wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td {
  color: #444444;
  font-weight: 600;
}

.wpforms-field-option-richtext .wpforms-field-option-row-media_controls.wpforms-hide {
  display: none;
}

.wpforms-panel-fields .wpforms-field-richtext label.label-title {
  min-height: 20px;
}

.wpforms-panel-fields .wpforms-field-richtext .wpforms-richtext-wrap {
  min-width: 250px;
}

.wpforms-panel-fields .wpforms-field-richtext .wpforms-richtext-wrap .wp-editor-container {
  border-radius: 4px 0 4px 4px;
  border-color: #cccccc;
  overflow: hidden;
}

.wpforms-panel-fields .wpforms-field-richtext .wpforms-richtext-wrap .wp-switch-editor {
  border-radius: 4px 4px 0 0;
  margin: 0 0 0 5px;
  border-color: #cccccc;
  pointer-events: none;
  color: rgba(80, 87, 94, 0.5);
}

.wpforms-panel-fields .wpforms-field-richtext .wpforms-richtext-wrap .wp-switch-editor.switch-tmce {
  background-color: #f5f5f5;
  border-bottom-color: transparent;
}

.wpforms-panel-fields .wpforms-field-richtext .wpforms-richtext-wrap textarea {
  width: 100%;
  border: none;
  padding: 0;
  margin: 0;
  resize: none;
  cursor: pointer;
  border-radius: 0;
}

.wpforms-panel-fields .wpforms-field-richtext .wpforms-richtext-wrap .mce-statusbar {
  border-radius: 0 0 4px 4px;
  height: 21px;
  background-color: #ffffff;
  border-color: #cccccc;
}

.wpforms-panel-fields .wpforms-field-richtext .wpforms-richtext-wrap .mce-statusbar .mce-i-resize {
  float: right;
}

.wpforms-panel-fields .wpforms-field-richtext .wpforms-richtext-wrap .mce-container-body {
  background-color: #f5f5f5;
}

.wpforms-panel-fields .wpforms-field-richtext .wpforms-richtext-wrap .mce-toolbar-grp {
  height: 43px;
  display: block;
  background-image: url("../../images/richtext/tinymce-toolbar-full.png");
  background-repeat: no-repeat;
  background-position: left center;
  background-size: auto 38px;
  border-color: #cccccc;
  opacity: .5;
}

.wpforms-panel-fields .wpforms-field-richtext .wpforms-richtext-wrap .mce-toolbar-grp.wpforms-field-richtext-media-enabled {
  background-image: url("../../images/richtext/tinymce-toolbar-full-mb.png");
}

.wpforms-panel-fields .wpforms-field-richtext .wpforms-richtext-wrap .mce-toolbar-grp.wpforms-field-richtext-toolbar-basic {
  background-image: url("../../images/richtext/tinymce-toolbar-basic.png");
}

.wpforms-panel-fields .wpforms-field-richtext .wpforms-richtext-wrap .mce-toolbar-grp.wpforms-field-richtext-toolbar-basic.wpforms-field-richtext-media-enabled {
  background-image: url("../../images/richtext/tinymce-toolbar-basic-mb.png");
}

.wpforms-field-option-row-choices ul.wpforms-ai-choices li:last-of-type {
  border-bottom: 1px solid #ced7e0;
  padding-bottom: 10px;
}

.wpforms-field-option-row-ai_modal_button {
  margin-top: -10px;
}

.wpforms-ai-modal-button {
  padding: 6px 10px;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.wpforms-ai-modal-button:before {
  content: '';
  display: inline-block;
  vertical-align: middle;
  width: 16px;
  height: 16px;
  background-image: url("../../images/integrations/ai/ai-feature.svg");
  background-size: 16px 16px;
  margin-inline-end: 8px;
  opacity: .85;
}

.wpforms-ai-modal-button:hover:before {
  opacity: 1;
}

.wpforms-panel-fields .wpforms-field.wpforms-field-payment-coupon .wpforms-field-payment-coupon-wrapper {
  position: relative;
  display: flex;
  max-width: 60%;
}

.wpforms-panel-fields .wpforms-field.wpforms-field-payment-coupon .wpforms-field-payment-coupon-wrapper .fa-exclamation-triangle {
  font-size: 16px;
  color: #ffb900;
  position: absolute;
  top: calc( 50% - 8px);
  inset-inline-end: -36px;
}

.wpforms-panel-fields .wpforms-field.wpforms-field-payment-coupon .wpforms-field-payment-coupon-wrapper input[type=text].wpforms-field-payment-coupon-input {
  cursor: pointer;
  width: auto;
  flex: 1;
  /* The layout field compatibility. */
  min-width: 80px !important;
}

.wpforms-panel-fields .wpforms-field.wpforms-field-payment-coupon .wpforms-field-payment-coupon-wrapper .wpforms-field-payment-coupon-button {
  font-size: 15px;
  line-height: 18px;
  font-weight: 500;
  cursor: pointer;
  border: 0;
  margin-inline-start: 20px;
  background-color: #999;
  color: #fff;
  padding: 10px 15px;
  border-radius: 4px;
  white-space: nowrap;
  max-width: 250px;
  text-overflow: ellipsis;
  overflow: hidden;
}

#wpforms-add-fields-payment-coupon.wpforms-add-fields-button-disabled {
  background-color: #036aab;
  cursor: no-drop;
}

.wpforms-field-option-row-allowed_coupons .wpforms-alert {
  margin-top: 20px;
}

.wpforms-field-option-row-allowed_coupons .choices:not(.is-focused) .choices__inner {
  border: 1px solid #b0b6bd;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column .wpforms-field-payment-coupon .wpforms-field-payment-coupon-wrapper,
.wpforms-panel-fields .wpforms-field.wpforms-field-payment-coupon.wpforms-field-drag-to-column .wpforms-field-payment-coupon-wrapper {
  max-width: calc(100% - 46px);
  flex-wrap: wrap;
  justify-content: flex-end;
  margin-bottom: -15px;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column .wpforms-field-payment-coupon .wpforms-field-payment-coupon-wrapper .wpforms-field-payment-coupon-input,
.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column .wpforms-field-payment-coupon .wpforms-field-payment-coupon-wrapper .wpforms-field-payment-coupon-button,
.wpforms-panel-fields .wpforms-field.wpforms-field-payment-coupon.wpforms-field-drag-to-column .wpforms-field-payment-coupon-wrapper .wpforms-field-payment-coupon-input,
.wpforms-panel-fields .wpforms-field.wpforms-field-payment-coupon.wpforms-field-drag-to-column .wpforms-field-payment-coupon-wrapper .wpforms-field-payment-coupon-button {
  margin-bottom: 15px;
}

@media screen and (max-width: 1280px) {
  .wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column .wpforms-field-payment-coupon .wpforms-field-payment-coupon-wrapper,
  .wpforms-panel-fields .wpforms-field.wpforms-field-payment-coupon.wpforms-field-drag-to-column .wpforms-field-payment-coupon-wrapper {
    max-width: calc(100% - 30px);
    margin-bottom: -15px;
  }
  .wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column .wpforms-field-payment-coupon .wpforms-field-payment-coupon-wrapper .wpforms-field-payment-coupon-input,
  .wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column .wpforms-field-payment-coupon .wpforms-field-payment-coupon-wrapper .wpforms-field-payment-coupon-button,
  .wpforms-panel-fields .wpforms-field.wpforms-field-payment-coupon.wpforms-field-drag-to-column .wpforms-field-payment-coupon-wrapper .wpforms-field-payment-coupon-input,
  .wpforms-panel-fields .wpforms-field.wpforms-field-payment-coupon.wpforms-field-drag-to-column .wpforms-field-payment-coupon-wrapper .wpforms-field-payment-coupon-button {
    margin-bottom: 15px;
  }
  .wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column .wpforms-field-payment-coupon .wpforms-field-payment-coupon-wrapper .wpforms-field-payment-coupon-button,
  .wpforms-panel-fields .wpforms-field.wpforms-field-payment-coupon.wpforms-field-drag-to-column .wpforms-field-payment-coupon-wrapper .wpforms-field-payment-coupon-button {
    margin-inline-start: 10px;
  }
  .wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column .wpforms-field-payment-coupon .wpforms-field-payment-coupon-wrapper .fa-exclamation-triangle,
  .wpforms-panel-fields .wpforms-field.wpforms-field-payment-coupon.wpforms-field-drag-to-column .wpforms-field-payment-coupon-wrapper .fa-exclamation-triangle {
    inset-inline-end: -15px;
  }
  .wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column .wpforms-field-payment-coupon input[type=text].wpforms-field-payment-coupon-input,
  .wpforms-panel-fields .wpforms-field.wpforms-field-payment-coupon.wpforms-field-drag-to-column input[type=text].wpforms-field-payment-coupon-input {
    min-width: 100% !important;
  }
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field-payment-coupon .wpforms-field-payment-coupon-wrapper,
.wpforms-panel-fields .wpforms-field.wpforms-field-payment-coupon.wpforms-field-drag-to-column.wpforms-layout-column-100 .wpforms-field-payment-coupon-wrapper {
  max-width: 60%;
}

@media screen and (max-width: 1280px) {
  .wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field-payment-coupon .wpforms-field-payment-coupon-wrapper,
  .wpforms-panel-fields .wpforms-field.wpforms-field-payment-coupon.wpforms-field-drag-to-column.wpforms-layout-column-100 .wpforms-field-payment-coupon-wrapper {
    max-width: 60%;
  }
}

.wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.wpforms-field-payment-coupon .wpforms-field-payment-coupon-wrapper input[type=text].wpforms-field-payment-coupon-input {
  min-width: 80px !important;
}

table.wpforms-order-summary-preview .wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price {
  color: #d63638;
}

.wpforms-panel-fields .wpforms-field-signature.size-medium .wpforms-signature-wrap {
  width: 60%;
}

.wpforms-panel-fields .wpforms-field-signature.size-small .wpforms-signature-wrap {
  width: 25%;
}

.wpforms-panel-fields .wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field .wpforms-signature-wrap,
.wpforms-panel-fields .wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field .wpforms-signature-wrap,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-repeater .wpforms-signature-wrap,
.wpforms-panel-fields .wpforms-field.wpforms-field-drag-to-layout:not(.wpforms-field-drag-to-column-100) .wpforms-signature-wrap {
  width: 100%;
  min-width: 100%;
}

.wpforms-panel-fields .wpforms-field .wpforms-signature-wrap {
  border: 1px solid #cccccc;
  border-radius: 4px;
  position: relative;
  height: 130px;
  background-color: #ffffff;
  min-width: 250px;
}

.wpforms-panel-fields .wpforms-field .wpforms-signature-wrap:before {
  content: "";
  left: 20px;
  right: 20px;
  bottom: 30px;
  border-top: 1px dashed #cccccc;
  position: absolute;
}

.wpforms-panel-fields .wpforms-field-likert_scale table.single-row.modern tbody tr td:first-of-type {
  border-radius: 0;
  border-start-start-radius: 5px;
  border-end-end-radius: 5px;
}

.wpforms-panel-fields .wpforms-field-likert_scale table.modern th {
  font-size: 14px;
  font-weight: 400;
  padding: 8px 5px;
  text-align: center;
  vertical-align: bottom;
  white-space: normal;
  word-break: break-word;
}

.wpforms-panel-fields .wpforms-field-likert_scale table.modern tbody tr:nth-child(odd) th,
.wpforms-panel-fields .wpforms-field-likert_scale table.modern tbody tr:nth-child(odd) td {
  background-color: #eeeeee;
}

.wpforms-panel-fields .wpforms-field-likert_scale table.modern tbody tr th {
  font-size: 16px;
  text-align: start;
  vertical-align: middle;
  padding-inline-start: 10px;
  border-radius: 0;
  border-start-start-radius: 5px;
  border-end-start-radius: 5px;
  line-height: 1.2;
  word-break: break-word;
}

.wpforms-panel-fields .wpforms-field-likert_scale table.modern tbody tr td {
  padding: 10px 5px;
  vertical-align: middle;
  text-align: center;
  position: relative;
}

.wpforms-panel-fields .wpforms-field-likert_scale table.modern tbody tr td:last-child {
  border-radius: 0;
  border-end-end-radius: 5px;
  border-start-end-radius: 5px;
}

.wpforms-panel-fields .wpforms-field-likert_scale table.modern tbody tr td label {
  display: block;
  width: 100%;
  height: 20px;
  position: relative;
  cursor: pointer;
}

.wpforms-panel-fields .wpforms-field-likert_scale table.modern tbody tr td label:hover:after {
  border: 1px solid #777777;
  box-shadow: 0 0 0 1px #777777;
}

.wpforms-panel-fields .wpforms-field-likert_scale table.modern tbody tr td label:after {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  margin: 0 0 0 -10px;
  width: 20px;
  height: 20px;
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border-radius: 50%;
  box-sizing: border-box;
}

.wpforms-panel-fields .wpforms-field-likert_scale table.modern tbody tr td input[type=radio].wpforms-error + label:after,
.wpforms-panel-fields .wpforms-field-likert_scale table.modern tbody tr td input[type=checkbox].wpforms-error + label:after {
  border: 1px solid red;
}

.wpforms-panel-fields .wpforms-field-likert_scale table.modern tbody tr td input[type=radio]:checked + label:after,
.wpforms-panel-fields .wpforms-field-likert_scale table.modern tbody tr td input[type=checkbox]:checked + label:after {
  background-color: green;
  border: 0;
}

.wpforms-panel-fields .wpforms-field-likert_scale table.modern tbody tr td input[type=radio]:checked + label:before,
.wpforms-panel-fields .wpforms-field-likert_scale table.modern tbody tr td input[type=checkbox]:checked + label:before {
  content: "";
  position: absolute;
  top: 2px;
  left: 50%;
  margin: 0 0 0 -8px;
  width: 16px;
  height: 16px;
  background-image: url("../images/check.svg");
  background-size: contain;
  z-index: 1;
  border: 0;
}

.wpforms-panel-fields .wpforms-field-likert_scale table.classic {
  border: 1px solid #cccccc;
}

.wpforms-panel-fields .wpforms-field-likert_scale table.classic th {
  background-color: #eeeeee;
  font-size: 14px;
  font-weight: 400;
  padding: 10px 5px;
  text-align: center;
  vertical-align: bottom;
  white-space: normal;
  word-break: break-word;
  border-inline-start: 1px solid #cccccc;
}

.wpforms-panel-fields .wpforms-field-likert_scale table.classic th:first-of-type {
  border-inline-start: 0;
}

.wpforms-panel-fields .wpforms-field-likert_scale table.classic tbody tr:nth-child(odd) th,
.wpforms-panel-fields .wpforms-field-likert_scale table.classic tbody tr:nth-child(odd) td {
  background-color: #f8f8f8;
}

.wpforms-panel-fields .wpforms-field-likert_scale table.classic tbody tr th {
  background-color: #ffffff;
  font-size: 15px;
  font-weight: 600;
  text-align: start;
  vertical-align: middle;
  padding-inline-start: 10px;
  border-top: 1px solid #cccccc;
  position: relative;
  line-height: 1.2;
  word-break: break-word;
}

.wpforms-panel-fields .wpforms-field-likert_scale table.classic tbody tr td {
  background-color: #ffffff;
  padding: 10px 5px;
  vertical-align: middle;
  text-align: center;
  border: 1px solid #cccccc;
  position: relative;
}

.wpforms-likert-scale-mobile-label {
  display: none;
}

@media only screen and (max-width: 600px) {
  form.wpforms-form .wpforms-field-likert_scale table {
    display: block;
  }
  form.wpforms-form .wpforms-field-likert_scale table thead {
    display: block;
  }
  form.wpforms-form .wpforms-field-likert_scale table thead tr {
    display: none;
  }
  form.wpforms-form .wpforms-field-likert_scale table tbody {
    display: block;
  }
  form.wpforms-form .wpforms-field-likert_scale table tbody tr {
    display: block;
    border: 1px solid #ccc !important;
  }
  form.wpforms-form .wpforms-field-likert_scale table tbody tr th {
    display: block;
  }
  form.wpforms-form .wpforms-field-likert_scale table tbody tr td {
    display: block;
    border: none !important;
    border-bottom: 1px solid #eee !important;
    padding: 10px 20px !important;
  }
  form.wpforms-form .wpforms-field-likert_scale table tbody tr td:last-child {
    border-radius: 0 !important;
    border-bottom: none !important;
  }
  form.wpforms-form .wpforms-field-likert_scale table tbody tr td label {
    display: none;
  }
  form.wpforms-form .wpforms-field-likert_scale table.classic .wpforms-likert-scale-option {
    display: block;
  }
  form.wpforms-form .wpforms-field-likert_scale table.modern tbody tr th {
    font-weight: bold;
    border-radius: 0;
  }
  form.wpforms-form .wpforms-field-likert_scale table.modern tbody tr td label {
    margin-inline-start: 20px;
    width: 20px;
  }
  form.wpforms-form .wpforms-field-likert_scale .wpforms-likert-scale-mobile-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  form.wpforms-form .wpforms-field-likert_scale .wpforms-likert-scale-mobile-label {
    display: block;
    text-align: start !important;
  }
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.modern th {
  font-size: 14px;
  font-weight: 400;
  padding: 10px 0;
  text-align: center;
  vertical-align: bottom;
  -webkit-border-before: revert;
  border-block-start: revert;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.modern th .not-likely {
  float: left;
  text-align: left;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.modern th .extremely-likely {
  float: right;
  text-align: right;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.modern tbody tr td {
  background-color: #ffffff;
  padding: 0;
  vertical-align: middle;
  text-align: center;
  border: 1px solid #cccccc;
  border-left: 0;
  width: 9.090909091%;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.modern tbody tr td:last-child {
  border-radius: 0 5px 5px 0;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.modern tbody tr td:first-of-type {
  border-radius: 5px 0 0 5px;
  border-left: 1px solid #cccccc;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.modern tbody tr td label {
  display: block;
  width: 100%;
  height: 40px;
  font-weight: 600;
  font-size: 16px;
  line-height: 40px;
  position: relative;
  cursor: pointer;
  color: #444444;
  border: 0;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.modern tbody tr td label:hover {
  background-color: #f8f8f8;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.modern tbody tr td label:hover:after {
  box-shadow: 0 0 0 2px #777777;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.modern tbody tr td label:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.modern tbody tr td input[type=radio].wpforms-error + label:after {
  box-shadow: 0 0 0 1px #cc0000;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.modern tbody tr td input[type=radio].wpforms-error + label:hover:after {
  box-shadow: 0 0 0 2px #cc0000;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.modern tbody tr td input[type=radio]:checked + label {
  background-color: green;
  color: #ffffff;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.modern tbody tr td input[type=radio]:checked + label:after {
  border: none;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.classic {
  border: 1px solid #cccccc;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.classic th {
  background-color: #eeeeee;
  font-size: 14px;
  font-weight: 400;
  padding: 10px;
  text-align: center;
  vertical-align: bottom;
  -webkit-border-before: revert;
  border-block-start: revert;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.classic th .not-likely {
  float: left;
  text-align: left;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.classic th .extremely-likely {
  float: right;
  text-align: right;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.classic tbody tr td {
  background-color: #ffffff;
  padding: 10px 5px;
  vertical-align: middle;
  text-align: center;
  border-top: 1px solid #cccccc;
  position: relative;
  width: 9.090909091%;
  color: #444444;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.classic tbody tr td input[type=radio],
.wpforms-panel-fields .wpforms-field-net_promoter_score table.classic tbody tr td label {
  display: block;
  margin: 0 auto;
  text-align: center;
}


.wpforms-panel-fields .wpforms-field-net_promoter_score table.classic tbody tr td label {
  margin-top: 6px;
}

.rtl .wpforms-panel-fields .wpforms-field-net_promoter_score table.modern thead th .not-likely, .wpforms-panel-fields .wpforms-field-net_promoter_score .rtl table.modern thead th .not-likely, .rtl .wpforms-panel-fields .wpforms-field-net_promoter_score table.classic thead th .not-likely, .wpforms-panel-fields .wpforms-field-net_promoter_score .rtl table.classic thead th .not-likely {
  float: right;
  text-align: right;
}

.rtl .wpforms-panel-fields .wpforms-field-net_promoter_score table.modern thead th .extremely-likely, .wpforms-panel-fields .wpforms-field-net_promoter_score .rtl table.modern thead th .extremely-likely, .rtl .wpforms-panel-fields .wpforms-field-net_promoter_score table.classic thead th .extremely-likely, .wpforms-panel-fields .wpforms-field-net_promoter_score .rtl table.classic thead th .extremely-likely {
  float: left;
  text-align: left;
}

.rtl .wpforms-panel-fields .wpforms-field-net_promoter_score table.modern tbody tr td:first-of-type, .wpforms-panel-fields .wpforms-field-net_promoter_score .rtl table.modern tbody tr td:first-of-type, .rtl .wpforms-panel-fields .wpforms-field-net_promoter_score table.classic tbody tr td:first-of-type, .wpforms-panel-fields .wpforms-field-net_promoter_score .rtl table.classic tbody tr td:first-of-type {
  border-radius: 0 5px 5px 0;
  border-right: 1px solid #cccccc;
  border-left: 0;
}

.rtl .wpforms-panel-fields .wpforms-field-net_promoter_score table.modern tbody tr td:last-child, .wpforms-panel-fields .wpforms-field-net_promoter_score .rtl table.modern tbody tr td:last-child, .rtl .wpforms-panel-fields .wpforms-field-net_promoter_score table.classic tbody tr td:last-child, .wpforms-panel-fields .wpforms-field-net_promoter_score .rtl table.classic tbody tr td:last-child {
  border-radius: 5px 0 0 5px;
  border-left: 1px solid #cccccc;
}

.wpforms-panel-fields .wpforms-field-option-likert_scale .wpforms-field-option-row-columns .choices-list,
.wpforms-panel-fields .wpforms-field-option-likert_scale .wpforms-field-option-row-rows .choices-list {
  margin-bottom: 0;
}

.wpforms-panel-fields .wpforms-field-option-likert_scale .wpforms-field-option-row-columns .choices-list li,
.wpforms-panel-fields .wpforms-field-option-likert_scale .wpforms-field-option-row-rows .choices-list li {
  border-bottom: 1px solid #ced7e0;
  overflow: visible;
  padding: 10px 0;
  margin: 0;
}

.wpforms-panel-fields .wpforms-field-option-likert_scale .wpforms-field-option-row-columns .choices-list li:first-of-type,
.wpforms-panel-fields .wpforms-field-option-likert_scale .wpforms-field-option-row-rows .choices-list li:first-of-type {
  padding-top: 1px;
}

.wpforms-panel-fields .wpforms-field-option-likert_scale .wpforms-field-option-row-columns .choices-list li:last-of-type,
.wpforms-panel-fields .wpforms-field-option-likert_scale .wpforms-field-option-row-rows .choices-list li:last-of-type {
  padding-bottom: 1px;
  border-bottom: 0;
}

.wpforms-panel-fields .wpforms-field-option-likert_scale .wpforms-field-option-row-columns .choices-list li i,
.wpforms-panel-fields .wpforms-field-option-likert_scale .wpforms-field-option-row-rows .choices-list li i {
  font-size: 16px;
  display: inline-block;
  margin-block: 0;
  margin-inline-end: 0;
  margin-inline-start: 10px;
}

.wpforms-panel-fields .wpforms-field-option-likert_scale .wpforms-field-option-row-columns .choices-list li .move:hover,
.wpforms-panel-fields .wpforms-field-option-likert_scale .wpforms-field-option-row-rows .choices-list li .move:hover {
  cursor: pointer;
}

.wpforms-panel-fields .wpforms-field-option-likert_scale .wpforms-field-option-row-columns .choices-list li .move i,
.wpforms-panel-fields .wpforms-field-option-likert_scale .wpforms-field-option-row-rows .choices-list li .move i {
  margin: 0;
  font-size: 20px;
}

.wpforms-panel-fields .wpforms-field-option-likert_scale .wpforms-field-option-row-columns .choices-list li .add i,
.wpforms-panel-fields .wpforms-field-option-likert_scale .wpforms-field-option-row-rows .choices-list li .add i {
  margin-inline-start: 10px;
}

.wpforms-panel-fields .wpforms-field-option-likert_scale .wpforms-field-option-row-columns .choices-list li input[type=text],
.wpforms-panel-fields .wpforms-field-option-likert_scale .wpforms-field-option-row-rows .choices-list li input[type=text] {
  display: inline-block;
  width: calc( 100% - 75px);
}

.wpforms-panel-fields .wpforms-field-likert_scale table {
  width: 100%;
  min-width: 250px;
  border-collapse: collapse;
}

.wpforms-panel-fields .wpforms-field-likert_scale table *,
.wpforms-panel-fields .wpforms-field-likert_scale table *::before,
.wpforms-panel-fields .wpforms-field-likert_scale table *::after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.wpforms-panel-fields .wpforms-field-likert_scale table.modern input {
  display: none !important;
}

.wpforms-panel-fields .wpforms-field-likert_scale table.modern label:hover:after {
  border: 1px solid #cccccc !important;
  box-shadow: none !important;
}

.wpforms-panel-fields .wpforms-field-likert_scale.size-medium table {
  max-width: 65%;
}

.wpforms-panel-fields .wpforms-field-likert_scale.size-small table {
  max-width: 25%;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table {
  width: 100%;
  min-width: 250px;
  border-collapse: initial;
  border-spacing: 0;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table *,
.wpforms-panel-fields .wpforms-field-net_promoter_score table *::before,
.wpforms-panel-fields .wpforms-field-net_promoter_score table *::after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.modern input {
  display: none !important;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.modern label:hover {
  background-color: initial !important;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score table.modern label:hover:after {
  box-shadow: none !important;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score.size-medium table {
  max-width: 65%;
}

.wpforms-panel-fields .wpforms-field-net_promoter_score.size-small table {
  max-width: 25%;
}

.wpforms-panel-fields .wpforms-field-drag-to-column:not(.wpforms-layout-column-100).wpforms-field-net_promoter_score table,
.wpforms-panel-fields .wpforms-field-drag-to-column:not(.wpforms-layout-column-100).wpforms-field-likert_scale table,
.wpforms-panel-fields .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field-net_promoter_score table,
.wpforms-panel-fields .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field-likert_scale table {
  max-width: 100%;
}
