<?php
namespace um_ext\um_unsplash\core;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Unsplash_Ajax
 * @package um_ext\um_unsplash\core
 */
class Unsplash_Ajax {

	/**
	 * Unsplash_Ajax constructor.
	 */
	public function __construct() {
		add_action( 'wp_ajax_um_unsplash_update', array( $this, 'um_unsplash_save_cover_photo' ) );
	}

	/**
	 * Saves unsplash photo for cover image.
	 */
	public function um_unsplash_save_cover_photo() {
		check_ajax_referer( 'um_unsplash_save_photo' );

		if ( empty( $_POST['profile'] ) ) {
			wp_send_json_error( __( 'Invalid user ID', 'um-unsplash' ) );
		}

		if ( empty( $_POST['photo_id'] ) ) {
			wp_send_json_error( __( 'Invalid photo ID', 'um-unsplash' ) );
		}

		$user_id            = absint( $_POST['profile'] );
		$photo_author       = isset( $_POST['photo_author'] ) ? sanitize_text_field( wp_unslash( $_POST['photo_author'] ) ) : '';
		$photo_author_url   = isset( $_POST['photo_author_url'] ) ? esc_url_raw( wp_unslash( $_POST['photo_author_url'] ) ) : '';
		$photo_download_url = isset( $_POST['photo_download_url'] ) ? esc_url_raw( wp_unslash( $_POST['photo_download_url'] ) ) : '';
		$photo_id           = sanitize_key( $_POST['photo_id'] );
		$image              = isset( $_POST['unsplash_img'] ) ? esc_url_raw( wp_unslash( $_POST['unsplash_img'] ) ) : '';
		$image              = str_replace( 'q=85', 'q=100', $image );

		do_action( 'um_before_unsplash_cover_update', $user_id );

		if ( ! get_user_meta( $user_id, 'cover_photo', true ) ) {
			add_user_meta( $user_id, 'cover_photo', 'cover_photo.jpg' );
		} else {
			UM()->files()->delete_core_user_photo( $user_id, 'cover_photo' );
		}

		update_user_meta( $user_id, '_um_unsplash_cover', $image );
		update_user_meta( $user_id, '_um_unsplash_photo_author', $photo_author );
		update_user_meta( $user_id, '_um_unsplash_photo_author_url', $photo_author_url );
		update_user_meta( $user_id, '_um_unsplash_photo_download_url', $photo_download_url );
		update_user_meta( $user_id, '_um_unsplash_photo_id', $photo_id );

		do_action( 'after_unsplash_cover_update', $user_id, $photo_download_url );

		um_fetch_user( $user_id );

		$data = array(
			'image'    => str_replace( '&amp;', '&', $image ),
			'img_html' => um_user( 'cover_photo' ),
		);

		wp_send_json_success( $data );
	}
}
