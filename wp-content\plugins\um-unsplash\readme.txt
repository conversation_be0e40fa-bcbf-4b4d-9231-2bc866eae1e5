=== Ultimate Member - Unsplash ===
Author URI: https://ultimatemember.com
Plugin URI: https://ultimatemember.com/extensions/unsplash/
Contributors: nsinelnikov
Tags: private-content, user-profile
Requires at least: 5.5
Tested up to: 6.7
Stable tag: 2.1.4
License: GNU Version 2 or Any Later Version
License URI: http://www.gnu.org/licenses/gpl-3.0.txt
Requires UM core at least: 2.6.9

Allow users to select a profile cover photo from Unsplash from their profile.

== Description ==

Allow users to select a profile cover photo from Unsplash from their profile.

= Features of the plugin include: =

* When users click to add a cover photo they can upload their own cover photo or search for a cover photo from Unsplash
* When a user chooses to select a photo from Unsplash a modal will appear including some pre-loaded photos
* Pre-loaded photos are based on a search term you enter into admin settings
* Users can then search and select a photo to use as their cover photo

= Important: =

* Use of this extension requires creating a developer account and app on Unsplash website to get an Unsplash API key
* Production level rate limits require application and approval by Unsplash team. We cannot guarantee your app will be approved.
* Cover photos will show attribution on the top right of the cover photo to meet one the requirements for the app to be approved for production access.

Read about all of the plugin's features at [Ultimate Member - Unsplash](https://ultimatemember.com/extensions/unsplash/)

= Documentation & Support =

Got a problem or need help with Ultimate Member? Head over to our [documentation](https://docs.ultimatemember.com/article/1483-unsplash-setup) and perform a search of the knowledge base. If you can’t find a solution to your issue then you can create a topic on the [support forum](https://wordpress.org/support/plugin/ultimate-member).

== Installation ==

1. Activate the plugin
2. That's it. Go to Ultimate Member > Settings > Extensions > Unsplash to customize plugin options
3. For more details, please visit the official [Documentation](https://docs.ultimatemember.com/article/1483-unsplash-setup) page.

== Changelog ==

Version 2.1.4

* Fixed: Cover photo value for getting progress in Profile Completeness extension
* Fixed: "Load textdomain just in time" issue
* Tweak: Enhancements for WPCS support

Version 2.1.3

* Added: Hook `um_before_unsplash_cover_update` based on user request

Version 2.1.2

* Fixed: Displaying photo attributes

Version 2.1.1

* Fixed: Case when extension isn't active based on dependency, but we can provide the license key field

Version 2.1.0

* Tweak: Enhancements for security. Sanitizing, escapes, WPCS support

Version 2.0.9

* Tweak: Template overwrite versioning

Version 2.0.8

* Fixed: Extension settings structure

Version 2.0.7

* Tweak: Added `_wp_http_referer` argument to UM Proxy requests for getting proper license URLs

Version 2.0.6

* Tweak: Сhanged requests to UM Proxy

Version 2.0.5

* Tweak: WordPress 5.7 compatibility

Version 2.0.4

* Added: Requests via Unsplash proxy
* Added: *.pot translations file

Version 2.0.3

* Added: Compatibility with Profile Completeness

Version 2.0.1

* Added: Displaying download link and user profile url

Version 2.0

* Initial release
