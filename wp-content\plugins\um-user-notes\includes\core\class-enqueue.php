<?php
namespace um_ext\um_user_notes\core;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Enqueue
 * @package um_ext\um_user_notes\core
 */
class Enqueue {

	/**
	 * Enqueue constructor.
	 */
	public function __construct() {
		add_action( 'wp_enqueue_scripts', array( $this, 'wp_enqueue_scripts' ) );
	}

	/**
	 * Enqueue CSS/JS
	 */
	public function wp_enqueue_scripts() {
		$suffix = UM()->frontend()->enqueue()::get_suffix();
		wp_register_script( 'um-user-notes', um_user_notes_url . 'assets/js/um-user-notes' . $suffix . '.js', array( 'jquery', 'wp-util', 'wp-i18n', 'masonry' ), um_user_notes_version, true );

		wp_set_script_translations( 'um-user-notes', 'um-user-notes' );

		wp_register_style( 'um-user-notes', um_user_notes_url . 'assets/css/um-user-notes' . $suffix . '.css', array(), um_user_notes_version );
	}

	/**
	 * Enqueue Notes scripts
	 */
	public function enqueue_scripts() {
		wp_enqueue_script( 'um-user-notes' );
		wp_enqueue_style( 'um-user-notes' );

		wp_enqueue_editor();

		add_action( 'wp_footer', array( UM()->Notes()->profile(), 'add_modal' ), 999999 );
	}

	/**
	 * Enqueue Masonry
	 */
	public function enqueue_masonry_scripts() {
		wp_enqueue_script( 'masonry' );
	}
}
