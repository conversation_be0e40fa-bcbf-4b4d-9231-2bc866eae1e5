jQuery(document).ready(function(t){t(document.body).on("click",".um-user-bookmarks-add-button",function(o){o.preventDefault();var e=t(this),r=e.attr("data-post"),a=e.attr("data-profile"),s=e.attr("data-nonce");r&&("1"!=e.attr("data-folders")?wp.ajax.send("um_bookmarks_modal_content",{data:{bookmark_post:r,_wpnonce:s},beforeSend:function(){e.append('<i class="um-user-bookmarks-ajax-loading"></i>').attr("disabled",!0).css("cursor","wait")},success:function(o){e.css("cursor","pointer").removeAttr("disabled").find(".um-user-bookmarks-ajax-loading").remove(),t(".um-user-bookmarks-modal").show().find(".um-user-bookmarks-modal-content").html(o)},error:function(o){console.log(o)}}):wp.ajax.send("um_bookmarks_add",{data:{post_id:r,_um_user_bookmarks_folder:"default",_wpnonce:s},beforeSend:function(){e.append('<i class="um-user-bookmarks-ajax-loading"></i>').attr("disabled",!0).css("cursor","wait")},success:function(o){t('.um-user-bookmarks-button[data-post="'+r+'"]').closest(".um-clear").html(o)},error:function(o){console.log(o)}})),a&&(wp.ajax.send("um_bookmarks_add_profile",{data:{profile_id:a,_wpnonce:s},beforeSend:function(){e.append('<i class="um-user-bookmarks-ajax-loading"></i>').attr("disabled",!0).css("cursor","wait")},success:function(o){t('.um-user-bookmarks-button[data-profile="'+a+'"]').closest(".um-clear").html(o)},error:function(o){console.log(o)}}),o.stopPropagation())}),t(document.body).on("click",".um-user-bookmarks-remove-button",function(o){o.preventDefault();var e,r=t(this),a=r.data("nonce"),s=r.data("post"),n=r.attr("data-profile");s&&(e=wp.hooks.applyFilters("um_bookmarks_remove_button_args",{bookmark_post:s,return_button:!0,_nonce:a}),wp.ajax.send("um_bookmarks_remove",{data:e,beforeSend:function(){r.append('<i class="um-user-bookmarks-ajax-loading"></i>').attr("disabled",!0).css("cursor","wait")},success:function(o){t('.um-user-bookmarks-button[data-post="'+s+'"]').closest(".um-clear").html(o)},error:function(o){console.log(o)}})),n&&(e=wp.hooks.applyFilters("um_bookmarks_remove_button_args",{bookmark_profile:n,return_button:!0,_nonce:a}),wp.ajax.send("um_bookmarks_remove_profile",{data:e,beforeSend:function(){r.append('<i class="um-user-bookmarks-ajax-loading"></i>').attr("disabled",!0).css("cursor","wait")},success:function(o){t(".um-user-bookmarks-remove-profile").length?t('.um-bookmarks-profile[data-profile="'+n+'"]').remove():t('.um-user-bookmarks-button[data-profile="'+n+'"]').closest(".um-clear").html(o)},error:function(o){console.log(o)}}),o.stopPropagation())}),t(document.body).on("change",".um_user_bookmarks_old_folder-radio",function(){var o=t(this),e=o.parents("form"),r=e.find('[name="post_id"]').val(),e=wp.hooks.applyFilters("um_bookmarks_add_button_args",e.serialize());wp.ajax.send({data:e,beforeSend:function(){o.closest("form").addClass("um-disabled").attr("disabled",!0)},success:function(o){t(".um-user-bookmarks-modal").hide(),t('.um-user-bookmarks-button[data-post="'+r+'"]').closest(".um-clear").html(o)},error:function(o){console.log(o)}})}),t(document.body).on("click",".um_user_bookmarks_create_folder_btn",function(o){o.preventDefault();var e,r,a,s=t(this),n=t(".um-user-bookmarks-modal");s.hasClass("busy")||(o=(e=s.parents("#form-um-new-folder-bookmark")).find('[name="_um_user_bookmarks_folder"]'),e.find(".error").removeClass("error"),e.find(".error-message").hide(),""===t.trim(o.val())?(o.addClass("error"),o.parent("td").find(".error-message").show()):(r=e.find('[name="post_id"]').val(),a=s.html(),s.addClass("busy").html("..."),e.find(".form-response").html(""),o=wp.hooks.applyFilters("um_bookmarks_add_button_args",e.serialize()),wp.ajax.send({data:o,success:function(o){n.find("form").remove(),n.find(".um-user-bookmarks-modal-content").find(".um-user-bookmarks-modal-heading").remove(),n.find(".um-user-bookmarks-modal-content").append('<h1 style="text-align:center;"><i class="um-faicon-check"></i> '+wp.i18n.__("Successful","um-user-bookmarks")+"</h1>"),setTimeout(function(){t(".um-user-bookmarks-modal").hide()},1e3),t('.um-user-bookmarks-button[data-post="'+r+'"]').closest(".um-clear").html(o)},error:function(o){console.log(o),e.find(".form-response").html(o),s.removeClass("busy").html(a)}})))}),t(document.body).on("click",".um-user-bookmarks-profile-remove-link",function(){var e=t(this).css("cursor","wait"),o=e.data("nonce"),r=e.data("id");wp.ajax.send("um_bookmarks_remove",{data:{bookmark_post:r,_nonce:o},beforeSend:function(){e.append('<i class="um-user-bookmarks-ajax-loading"></i>').attr("disabled",!0).css("cursor","wait")},success:function(o){e.parents(".um-user-bookmarked-item").remove()},error:function(o){console.log(o)}})}),t(document.body).on("click",".um-user-bookmarks-cancel-btn",function(){var o='<a href="javascript:void(0);" class="um-user-bookmarks-cancel-btn">&times;</a>'+wp.i18n.__("Loading..","um-user-bookmarks");t("body").find(".um-user-bookmarks-modal").hide().find(".um-user-bookmarks-modal-content").html(o)}),t(document.body).on("click","#um-bookmarks-profile-add-folder",function(){t(this).parents(".um-user-bookmarks-profile-add-folder-holder").find("form").toggleClass("show"),t(this).find("i.icon").toggleClass("um-faicon-angle-down um-faicon-angle-up")}),t(document.body).on("click",".um_user_bookmarks_profile_create_folder_btn",function(o){o.preventDefault();var e,r,a=t(this);a.hasClass("busy")||(o=(e=a.parents("#um-user-bookmarks-profile-add-folder-form")).find('[name="_um_user_bookmarks_folder"]'),e.find(".error").removeClass("error"),e.find(".error-message").hide(),""===t.trim(o.val())?(o.addClass("error"),o.parent(".um_bookmarks_td").find(".error-message").show()):(r=a.html(),a.addClass("busy").html("..."),e.find(".form-response").html(""),wp.ajax.send({data:e.serialize(),success:function(o){location.reload()},error:function(o){console.log(o),e.find(".form-response").html(o),a.removeClass("busy").html(r)}})))}),t(document.body).on("click",".um-user-bookmarks-folder, .um-user-bookmarks-folder-back",function(){var o=t(this),e=o.data("nonce"),r=o.data("profile"),a=o.data("folder_key"),s=o.parents(".um-profile-body");s.html('<div style="text-align:center;"><p class="um-user-bookmarks-ajax-loading"></p></div>'),wp.ajax.send("um_bookmarks_view_folder",{data:{key:a,profile_id:r,_nonce:e},success:function(o){s.html(o)},error:function(o){console.log(o)}})}),t(document.body).on("click",".um-user-bookmarks-folder-edit",function(){var o=t(this),e=o.data("folder_key"),r=o.data("profile"),a=o.data("nonce"),s=o.parents(".um-profile-body");s.html('<div style="text-align:center;"><p class="um-user-bookmarks-ajax-loading"></p></div>'),wp.ajax.send("um_bookmarks_view_edit_folder",{data:{key:e,user:r,_nonce:a},success:function(o){s.html(o)},error:function(o){console.log(o)}})}),t(document.body).on("click",".um-user-bookmarks-folder-delete",function(){var e=t(this),r=e.parents(".um-profile-body"),o=e.attr("data-alert_text");confirm(o)&&wp.ajax.send("um_bookmarks_delete_folder",{data:{key:e.data("folder_key"),_nonce:e.data("nonce")},success:function(o){r.html('<div style="text-align:center;"><p class="um-user-bookmarks-ajax-loading"></p></div>'),wp.ajax.send("um_bookmarks_get_folder_view",{data:{profile_id:e.data("profile"),_nonce:e.data("callback-nonce")},success:function(o){r.html(o)},error:function(o){console.log(o)}})},error:function(o){console.log(o)}})}),t(document.body).on("click",".um-user-bookmarks-back-btn",function(){var o=t(this),e=o.data("profile"),r=o.data("nonce"),a=o.parents(".um-profile-body");a.html('<div style="text-align:center;"><p class="um-user-bookmarks-ajax-loading"></p></div>'),wp.ajax.send("um_bookmarks_get_folder_view",{data:{profile_id:e,_nonce:r},success:function(o){a.html(o)},error:function(o){console.log(o)}})}),t(document.body).on("click",".um_user_bookmarks_action_folder_update",function(o){o.preventDefault();var e,o=t(this),r=o.parents("form.um-user-bookmarks-edit-folder-form"),a=r.find('[name="folder_title"]');r.find(".error-message").hide(),""===t.trim(a.val())?a.parent("p").find(".error-message").show():(e=o.parents(".um-profile-body"),wp.ajax.send({data:r.serialize(),success:function(o){e.find(".um-user-bookmarks-folder-back").data("folder_key",o.slug).trigger("click")},error:function(o){console.log(o),r.find(".form-response").html(o)}}))}),t(document.body).on("click",".um-user-bookmarks-dropdown-hide",function(){return UM.dropdown.hideAll(),!1})});