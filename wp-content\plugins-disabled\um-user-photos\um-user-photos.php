<?php
/**
 * Plugin Name: Ultimate Member - User Photos
 * Plugin URI: http://ultimatemember.com/extensions/user-photos
 * Description: Let users add albums and photos
 * Version: 2.2.0
 * Author: Ultimate Member
 * Author URI: http://ultimatemember.com/
 * Text Domain: um-user-photos
 * Domain Path: /languages
 * Requires at least: 6.2
 * Requires PHP: 5.6
 * Requires Plugins: ultimate-member
 * UM version: 2.9.0
 *
 * @package UM_User_Photos
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

require_once ABSPATH . 'wp-admin/includes/plugin.php';

$plugin_data = get_plugin_data( __FILE__, true, false );

define( 'um_user_photos_url', plugin_dir_url( __FILE__ ) );
define( 'um_user_photos_path', plugin_dir_path( __FILE__ ) );
define( 'um_user_photos_plugin', plugin_basename( __FILE__ ) );
define( 'um_user_photos_extension', $plugin_data['Name'] );
define( 'um_user_photos_version', $plugin_data['Version'] );
define( 'um_user_photos_textdomain', 'um-user-photos' );
define( 'um_user_photos_requires', '2.9.0' );
// phpcs:enable Generic.NamingConventions.UpperCaseConstantName

define( 'UM_USER_PHOTOS_URL', plugin_dir_url( __FILE__ ) );
define( 'UM_USER_PHOTOS_PATH', plugin_dir_path( __FILE__ ) );
define( 'UM_USER_PHOTOS_PLUGIN', plugin_basename( __FILE__ ) );
define( 'UM_USER_PHOTOS_EXTENSION', $plugin_data['Name'] );
define( 'UM_USER_PHOTOS_VERSION', $plugin_data['Version'] );
define( 'UM_USER_PHOTOS_TEXTDOMAIN', 'um-user-photos' );
define( 'UM_USER_PHOTOS_REQUIRES', '2.9.0' );

function um_user_photos_plugins_loaded() {
	$locale = '' !== get_locale() ? get_locale() : 'en_US';
	load_textdomain( UM_USER_PHOTOS_TEXTDOMAIN, WP_LANG_DIR . '/plugins/' . UM_USER_PHOTOS_TEXTDOMAIN . '-' . $locale . '.mo' );
	load_plugin_textdomain( UM_USER_PHOTOS_TEXTDOMAIN, false, dirname( plugin_basename( __FILE__ ) ) . '/languages/' );
}
add_action( 'plugins_loaded', 'um_user_photos_plugins_loaded', 0 );

add_action( 'plugins_loaded', 'um_user_photos_check_dependencies', -20 );

if ( ! function_exists( 'um_user_photos_check_dependencies' ) ) {
	function um_user_photos_check_dependencies() {
		if ( ! defined( 'UM_PATH' ) || ! file_exists( UM_PATH . 'includes/class-dependencies.php' ) ) {
			//UM is not installed
			function um_user_photos_dependencies() {
				// translators: %s is the User Photos extension name.
				echo '<div class="error"><p>' . sprintf( __( 'The <strong>%s</strong> extension requires the Ultimate Member plugin to be activated to work properly. You can download it <a href="https://wordpress.org/plugins/ultimate-member">here</a>', 'um-user-photos' ), UM_USER_PHOTOS_EXTENSION ) . '</p></div>';
			}

			add_action( 'admin_notices', 'um_user_photos_dependencies' );
		} else {

			if ( ! function_exists( 'UM' ) ) {
				require_once um_path . 'includes/class-dependencies.php';
				$is_um_active = um\is_um_active();
			} else {
				$is_um_active = UM()->dependencies()->ultimatemember_active_check();
			}

			if ( ! $is_um_active ) {
				//UM is not active
				function um_user_photos_dependencies() {
					// translators: %s is the User Photos extension name.
					echo '<div class="error"><p>' . sprintf( __( 'The <strong>%s</strong> extension requires the Ultimate Member plugin to be activated to work properly. You can download it <a href="https://wordpress.org/plugins/ultimate-member">here</a>', 'um-user-photos' ), UM_USER_PHOTOS_EXTENSION ) . '</p></div>';
				}

				add_action( 'admin_notices', 'um_user_photos_dependencies' );

			} elseif ( true !== UM()->dependencies()->compare_versions( UM_USER_PHOTOS_REQUIRES, UM_USER_PHOTOS_VERSION, 'user-photos', UM_USER_PHOTOS_EXTENSION ) ) {
				//UM old version is active
				function um_user_photos_dependencies() {
					echo '<div class="error"><p>' . UM()->dependencies()->compare_versions( UM_USER_PHOTOS_REQUIRES, UM_USER_PHOTOS_VERSION, 'user-photos', UM_USER_PHOTOS_EXTENSION ) . '</p></div>';
				}

				add_action( 'admin_notices', 'um_user_photos_dependencies' );

				function um_user_photos_extend_license_settings( $settings ) {
					$settings['licenses']['fields'][] = array(
						'id'        => 'um_user_photos_license_key',
						'label'     => __( 'User Photos License Key', 'um-user-photos' ),
						'item_name' => 'User Photos',
						'author'    => 'ultimatemember',
						'version'   => UM_USER_PHOTOS_VERSION,
					);

					return $settings;
				}
				add_filter( 'um_settings_structure', 'um_user_photos_extend_license_settings' );
			} else {
				require_once UM_USER_PHOTOS_PATH . 'includes/class-um-user-photos.php';
			}
		}
	}
}


register_activation_hook( UM_USER_PHOTOS_PLUGIN, 'um_user_photos_activation_hook' );
function um_user_photos_activation_hook() {
	// First install.
	$version_old = get_option( 'um_user_photos_latest_version' );
	$version     = get_option( 'um_user_photos_version' );
	if ( ! $version && ! $version_old ) {
		update_option( 'um_user_photos_last_version_upgrade', UM_USER_PHOTOS_VERSION );
	}

	if ( UM_USER_PHOTOS_VERSION !== $version ) {
		update_option( 'um_user_photos_version', UM_USER_PHOTOS_VERSION );
	}

	// Run setup.
	if ( ! class_exists( 'um_ext\um_user_photos\common\Setup' ) ) {
		require_once UM_USER_PHOTOS_PATH . 'includes/common/class-setup.php';
	}

	$user_photos_setup = new um_ext\um_user_photos\common\Setup();
	$user_photos_setup->run_setup();
}
