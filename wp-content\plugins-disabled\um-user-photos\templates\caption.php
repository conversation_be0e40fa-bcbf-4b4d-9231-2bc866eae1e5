<?php
/**
 * Template for the UM User Photos. The image caption and comments block.
 *
 * Call: UM()->User_Photos()->ajax()->um_user_photos_get_comment_section()
 * Page: "Profile", tab "Photos", the image popup
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/caption.php
 * @var WP_Post $image
 * @var int     $image_id
 * @var string  $image_link
 * @var array   $likes
 * @var int     $likes_count
 * @var bool    $disable_comments
 * @var array   $comments
 * @var int     $comment_count
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
?>

<div class="um-user-photos-widget" id="postid-<?php echo esc_attr( $image_id ); ?>">

	<div class="um-user-photos-head">
		<div class="um-user-photos-right">
			<?php if ( $image_link && esc_url( $image_link ) ) : ?>
				<div class="um-user-photos-link">
					<a href="<?php echo esc_url( $image_link ); ?>" title="<?php esc_attr_e( 'Related link', 'um-user-photos' ); ?>" target="_blank"><?php esc_html_e( 'Related link', 'um-user-photos' ); ?> <i class="um-faicon-link"></i></a>
				</div>
			<?php endif; ?>
		</div>

		<div class="um-user-photos-left um-user-photos-author">
			<div class="um-user-photos-ava">
				<a href="<?php echo esc_url( um_user_profile_url() ); ?>">
					<?php echo get_avatar( $image->post_author, 80 ); ?>
				</a>
			</div>
			<div class="um-user-photos-author-meta">
				<div class="um-user-photos-author-url">
					<a href="<?php echo esc_url( um_user_profile_url() ); ?>" class="um-link">
						<?php echo wp_kses( um_user( 'display_name', 'html' ), UM()->get_allowed_html( 'templates' ) ); ?>
					</a>
				</div>
				<span class="um-user-photos-metadata">
					<?php
					$unix_published_date = get_post_datetime( $image_id, 'date', 'gmt' );
					$post_date           = wp_date( get_option( 'date_format', 'F j, Y' ) . ' ' . get_option( 'time_format', 'g:i a' ), $unix_published_date->getTimestamp() );
					?>
					<a  class="um-user-photos-metadata-date" href="#" title="<?php echo esc_attr( $post_date ); ?>">
						<?php echo esc_html( UM()->datetime()->time_diff( $unix_published_date->getTimestamp() ) ); ?>
					</a>
				</span>
			</div>
		</div>

		<div class="um-clear"></div>
	</div>

	<div class="um-user-photos-body">
		<div class="um-user-photos-bodyinner">
			<div class="um-user-photos-bodyinner-txt">
				<?php echo wp_kses( wp_get_attachment_caption( $image_id ), UM()->get_allowed_html( 'templates' ) ); ?>
			</div>

			<div class="um-user-photos-bodyinner-photo"></div>
			<div class="um-user-photos-bodyinner-video"></div>
		</div>

		<div class="um-user-photos-disp">
			<div class="um-user-photos-left">
				<?php
				$likes_class = 0 < $likes_count ? 'um-user-photos-show-likes' : '';
				?>
				<div class="um-user-photos-disp-likes">
					<a data-template="modal/photo-likes"
						data-wpnonce="<?php echo esc_attr( wp_create_nonce( 'um_user_photos_show_likes' ) ); ?>"
						data-modal_title="<?php esc_attr_e( 'Likes', 'um-user-photos' ); ?>"
						href="javascript:void(0);"
						class="<?php echo esc_attr( $likes_class ); ?> um-link"
						data-id="<?php echo esc_attr( $image_id ); ?>">
						<span class="um-user-photos-post-likes"><?php echo esc_html( $likes_count ); ?></span>
						<span class="um-user-photos-disp-span"><?php esc_html_e( 'Likes', 'um-user-photos' ); ?></span>
					</a>
				</div>
				<?php if ( ! $disable_comments ) { ?>
					<div class="um-user-photos-disp-comments">
						<a href="javascript:void(0);" class="um-link">
							<span class="um-user-photos-post-comments"><?php echo esc_html( $comment_count ); ?></span>
							<span class="um-user-photos-disp-span"><?php esc_html_e( 'Comments', 'um-user-photos' ); ?></span>
						</a>
					</div>
				<?php } ?>
			</div>
			<div class="um-clear"></div>
		</div>

		<div class="um-clear"></div>
	</div>

	<?php
	if ( is_user_logged_in() ) {
		$user_id     = get_current_user_id();
		$has_liked   = in_array( $user_id, $likes, true );
		$likenonce   = wp_create_nonce( 'um_user_photos_like_photo' );
		$unlikenonce = wp_create_nonce( 'um_user_photos_unlike_photo' );

		$like_class  = 'um-user-photos-like';
		$like_class .= $has_liked ? ' active' : '';
		?>
		<div class="um-user-photos-foot status" id="photoid-<?php echo esc_attr( $image_id ); ?>">

			<div class="um-user-photos-left um-user-photos-actions">
				<div class="<?php echo esc_attr( $like_class ); ?>" data-like_text="<?php esc_html_e( 'Like', 'um-user-photos' ); ?>" data-unlike_text="<?php esc_html_e( 'Unlike', 'um-user-photos' ); ?>">
					<a href="javascript:void(0);" data-likenonce="<?php echo esc_attr( $likenonce ); ?>" data-unlikenonce="<?php echo esc_attr( $unlikenonce ); ?>">
						<?php if ( ! $has_liked ) { ?>
							<i class="um-faicon-thumbs-up"></i>
							<span class=""><?php esc_html_e( 'Like', 'um-user-photos' ); ?></span>
						<?php } else { ?>
							<i class="um-faicon-thumbs-up um-effect-pop um-active-color"></i>
							<span class=""><?php esc_html_e( 'Unlike', 'um-user-photos' ); ?></span>
						<?php } ?>
					</a>
				</div>

				<?php if ( ! $disable_comments && UM()->User_Photos()->common()->user()->can_comment( $image_id ) ) { ?>
					<div class="um-user-photos-comment">
						<a href="javascript:void(0);">
							<i class="um-faicon-comment"></i>
							<span class=""><?php esc_html_e( 'Comment', 'um-user-photos' ); ?></span>
						</a>
					</div>
				<?php } ?>
			</div>

			<div class="um-clear"></div>
		</div>
	<?php } ?>

	<?php if ( ! $disable_comments ) { ?>
		<div class="um-user-photos-comments">
			<?php
			UM()->get_template(
				'comment-form.php',
				UM_USER_PHOTOS_PLUGIN,
				array(
					'image_id' => $image_id,
				),
				true
			);

			UM()->get_template(
				'comments.php',
				UM_USER_PHOTOS_PLUGIN,
				array(
					'image_id'      => $image_id,
					'comments'      => $comments,
				),
				true
			);
			?>
		</div>
	<?php } ?>
</div>
