@echo off
echo ===== RAEZG.LOCAL DIAGNOSTIC TOOL =====
echo.

echo 1. Checking if Apache is running...
tasklist /FI "IMAGENAME eq httpd.exe" | findstr httpd.exe > nul
if %errorlevel% equ 0 (
    echo ✓ Apache is running
    tasklist /FI "IMAGENAME eq httpd.exe"
) else (
    echo ✗ Apache is NOT running
)

echo.
echo 2. Checking port 80...
netstat -ano | findstr :80
echo.

echo 3. Checking hosts file entries...
findstr /C:"raezg.local" C:\Windows\System32\drivers\etc\hosts
if %errorlevel% equ 0 (
    echo ✓ raezg.local found in hosts file
) else (
    echo ✗ raezg.local NOT found in hosts file
)

echo.
echo 4. Checking VirtualHost configuration...
findstr /C:"raezg.local" C:\xampp\apache\conf\extra\httpd-vhosts.conf
if %errorlevel% equ 0 (
    echo ✓ raezg.local VirtualHost found
) else (
    echo ✗ raezg.local VirtualHost NOT found
)

echo.
echo 5. Checking if VirtualHosts are enabled...
findstr /C:"Include conf/extra/httpd-vhosts.conf" C:\xampp\apache\conf\httpd.conf
if %errorlevel% equ 0 (
    echo ✓ VirtualHosts are enabled
) else (
    echo ✗ VirtualHosts are NOT enabled
)

echo.
echo 6. Testing Apache configuration...
C:\xampp\apache\bin\httpd.exe -t
if %errorlevel% equ 0 (
    echo ✓ Apache configuration is valid
) else (
    echo ✗ Apache configuration has errors
)

echo.
echo 7. Checking raezg directory...
if exist "C:\xampp\htdocs\raezg" (
    echo ✓ raezg directory exists
    dir "C:\xampp\htdocs\raezg" /B
) else (
    echo ✗ raezg directory does NOT exist
)

echo.
echo 8. Testing DNS resolution...
nslookup raezg.local
echo.

echo 9. Recent Apache errors (last 10 lines)...
powershell -Command "Get-Content 'C:\xampp\apache\logs\error.log' -Tail 10"

echo.
echo 10. Testing HTTP connection...
echo Testing localhost...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost' -TimeoutSec 5; Write-Host 'localhost: HTTP' $response.StatusCode } catch { Write-Host 'localhost: Failed -' $_.Exception.Message }"

echo Testing raezg.local...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://raezg.local' -TimeoutSec 5; Write-Host 'raezg.local: HTTP' $response.StatusCode } catch { Write-Host 'raezg.local: Failed -' $_.Exception.Message }"

echo.
echo ===== DIAGNOSTIC COMPLETE =====
echo.
pause
