<?php
return ['x-generator'=>'GlotPress/4.0.1','translation-revision-date'=>'2023-06-04 17:58:10+0000','plural-forms'=>'nplurals=2; plural=n != 1;','project-id-version'=>'Plugins - WP Mail SMTP by WPForms &#8211; The Most Popular SMTP and Email Log Plugin - Stable (latest release)','language'=>'en_GB','messages'=>['WP Core (%s)'=>'WP Core (%s)','Primary'=>'Primary','Friendly reminder, your Primary Connection will be used for all emails that do not match the conditions above.'=>'Friendly reminder, your Primary Connection will be used for all emails that do not match the conditions above.','Initiator'=>'Initiator','Contact Emails (SMTP.com)'=>'Contact Emails (SMTP.com)','Add New Group'=>'Add New Group','or'=>'or','Is'=>'Is','And'=>'And','Order'=>'Order','Contains'=>'Contains','Arrow Down'=>'Arrow Down','Arrow Up'=>'Arrow Up','if the following conditions are met...'=>'if the following conditions are met...','WooCommerce Emails (SendLayer)'=>'WooCommerce Emails (SendLayer)','Send with'=>'Send with','Enable Smart Routing'=>'Enable Smart Routing','Light bulb icon'=>'Light bulb icon','Once you add an <a href="%s">additional connection</a>, you can select it here.'=>'Once you add an <a href="%s">additional connection</a>, you can select it here.','Don’t worry about losing emails. Add an additional connection, then set it as your Backup Connection. Emails that fail to send with the Primary Connection will be sent via the selected Backup Connection. <a href="%s" target="_blank" rel="noopener noreferrer">Upgrade to WP Mail SMTP Pro!</a>'=>'Don’t worry about losing emails. Add an additional connection, then set it as your Backup Connection. Emails that fail to send with the Primary Connection will be sent via the selected Backup Connection. <a href="%s" target="_blank" rel="noopener noreferrer">Upgrade to WP Mail SMTP Pro!</a>','Primary Connection'=>'Primary Connection','Create advanced routing rules'=>'Create advanced routing rules','Use mailers for different purposes'=>'Use mailers for different purposes','Set a Backup Connection'=>'Set a Backup Connection','With additional connections you can...'=>'With additional connections you can...','Smart Routing'=>'Smart Routing','Backup Connection'=>'Backup Connection','Additional Connections'=>'Additional Connections','There was an error while processing the authentication request. The state key is invalid. Please try again.'=>'There was an error while processing the authentication request. The state key is invalid. Please try again.','Remove OAuth Connection'=>'Remove OAuth Connection','It looks like we can\'t remove OAuth connection.'=>'It looks like we can\'t remove OAuth connection.','create the missing DB tables by clicking on this link'=>'create the missing DB tables by clicking on this link','Go to WP Mail SMTP settings page.'=>'Go to WP Mail SMTP settings page.','Some DB Tables are still missing.'=>'Some DB tables are still missing.','Missing DB tables were created successfully.'=>'Missing DB tables were created successfully.','Unknown.'=>'Unknown.','%d Day'=>'%d Day' . "\0" . '%d Days','1 Year'=>'1 Year','6 Months'=>'6 Months','3 Months'=>'3 Months','1 Month'=>'1 Month','1 Week'=>'1 Week','Debug events older than the selected period will be permanently deleted from the database.'=>'Debug events older than the selected period will be permanently deleted from the database.','Forever'=>'Forever','Events Retention Period'=>'Events Retention Period','WP Mail SMTP is using custom database tables for some of its features. In order to work properly, the custom tables should be created, and it seems they are missing. Please try to <a href="%1$s" target="_self" aria-label="%2$s" rel="noopener noreferrer">%3$s</a>. If this issue persists, please contact our support.'=>'WP Mail SMTP is using custom database tables for some of its features. In order to work properly, the custom tables should be created, and it seems they are missing. Please try to <a href="%1$s" target="_self" aria-label="%2$s" rel="noopener noreferrer">%3$s</a>. If this issue persists, please contact our support.','[%1$d] %2$s called at [%3$s:%4$s]'=>'[%1$d] %2$s called at [%3$s:%4$s]','Backtrace:'=>'Backtrace:','Track when a link in an email is clicked'=>'Track when a link in an email is clicked','Complete Email Reports'=>'Complete Email Reports','See the delivery status, track opens and clicks, and create deliverability graphs.'=>'See the delivery status, track opens and clicks, and create deliverability graphs.','Get notifications via email, SMS, Slack, or webhook when emails fail to send.'=>'Get notifications via email, SMS, Slack, or webhook when emails fail to send.','Instant Email Alerts'=>'Instant Email Alerts','This option must be enabled if you\'d like to be able to resend emails. Please be aware that all email content will be stored in your WordPress database. This may include sensitive data, passwords, and personal details.'=>'This option must be enabled if you\'d like to be able to resend emails. Please be aware that all email content will be stored in your WordPress database. This may include sensitive data, passwords, and personal details.','Enable these powerful logging features for more control of your WordPress emails.'=>'Enable these powerful logging features for more control of your WordPress emails.','See which links were clicked in emails sent from your WordPress site. Click tracking works with emails that are sent in HTML format.'=>'See which links were clicked in emails sent from your WordPress site. Click tracking works with emails that are sent in HTML format.','See which emails were opened by the recipients. Email open tracking works with emails that are sent in HTML format.'=>'See which emails were opened by the recipients. Email open tracking works with emails that are sent in HTML format.','Track when an email is opened'=>'Track when an email is opened','All file attachments sent from your site will be saved to the WordPress Uploads folder. Please note that this may reduce available disk space on your server.'=>'All file attachments sent from your site will be saved to the WordPress uploads folder. Please note that this may reduce available disk space on your server.','Save file attachments sent from WordPress'=>'Save file attachments sent from WordPress','Store the content for all sent emails'=>'Store the content for all sent emails','Configure Email Logs'=>'Configure Email Logs','%1$sFollow this link%2$s to get a Private API Key from Mailgun.'=>'%1$sFollow this link%2$s to get a Private API Key from Mailgun.','Let’s see how many emails you’ve sent with WP Mail SMTP.'=>'Let’s see how many emails you’ve sent with WP Mail SMTP.','Current function path: %s'=>'Current function path: %s','It looks like it\'s overwritten in the "wp-config.php" file. Please reach out to your hosting provider on how to disable or remove the "wp_mail" function overwrite to prevent conflicts with WP Mail SMTP.'=>'It looks like it\'s overwritten in the "wp-config.php" file. Please reach out to your hosting provider on how to disable or remove the "wp_mail" function overwrite to prevent conflicts with WP Mail SMTP.','It looks like the "%s" must-use plugin is overwriting the "wp_mail" function. Please reach out to your hosting provider on how to disable or remove the "wp_mail" function overwrite to prevent conflicts with WP Mail SMTP.'=>'It looks like the "%s" must-use plugin is overwriting the "wp_mail" function. Please reach out to your hosting provider on how to disable or remove the "wp_mail" function overwrite to prevent conflicts with WP Mail SMTP.','It looks like the "%s" plugin is overwriting the "wp_mail" function. Please reach out to the plugin developer on how to disable or remove the "wp_mail" function overwrite to prevent conflicts with WP Mail SMTP.'=>'It looks like the "%s" plugin is overwriting the "wp_mail" function. Please reach out to the plugin developer on how to disable or remove the "wp_mail" function overwrite to prevent conflicts with WP Mail SMTP.','WP Mail SMTP has detected incorrect "wp_mail" function location. Usually, this means that emails will not be sent successfully!'=>'WP Mail SMTP has detected incorrect "wp_mail" function location. Usually, this means that emails will not be sent successfully!','Or deactivate "SMTP" module in Branda > Emails > SMTP.'=>'Or deactivate "SMTP" module in Branda > Emails > SMTP.','Webhook Alerts'=>'Webhook Alerts','Paste in the webhook URL you’d like to use to receive alerts when email sending fails. Read our documentation on setting up webhook alerts.'=>'Paste in the webhook URL you’d like to use to receive alerts when email sending fails. Read our documentation on setting up webhook alerts.','Webhook'=>'Webhook','To Phone Number'=>'To Phone Number','From Phone Number'=>'From Phone Number','Twilio Auth Token'=>'Twilio Auth Token','Twilio Account ID'=>'Twilio Account ID','SMS via Twilio Alerts'=>'SMS via Twilio Alerts','To receive SMS alerts, you’ll need a Twilio account. Read our documentation to learn how to set up Twilio SMS, then enter your connection details below.'=>'To receive SMS alerts, you’ll need a Twilio account. Read our documentation to learn how to set up Twilio SMS, then enter your connection details below.','SMS via Twilio'=>'SMS via Twilio','Webhook URL'=>'Webhook URL','Slack Alerts'=>'Slack Alerts','Paste in the Slack webhook URL you’d like to use to receive alerts when email sending fails. Read our documentation on setting up Slack alerts.'=>'Paste in the Slack webhook URL you’d like to use to receive alerts when email sending fails. Read our documentation on setting up Slack alerts.','Slack'=>'Slack','Email Alerts'=>'Email Alerts','Enter the email addresses (3 max) you’d like to use to receive alerts when email sending fails. Read our documentation on setting up email alerts.'=>'Enter the email addresses (3 max) you’d like to use to receive alerts when email sending fails. Read our documentation on setting up email alerts.','Alerts'=>'Alerts','There was an error while processing the authentication request. The authorization code is missing. Please try again.'=>'There was an error while processing the authentication request. The authorisation code is missing. Please try again.','There was an error while processing the authentication request. The nonce is invalid. Please try again.'=>'There was an error while processing the authentication request. The nonce is invalid. Please try again.','Follow this link to get an API Key from SendLayer: %s.'=>'Follow this link to get an API Key from SendLayer: %s.','SendLayer'=>'SendLayer','<strong><a href="%1$s" target="_blank" rel="noopener noreferrer">SendLayer</a> is our #1 recommended mailer.</strong> Its affordable pricing and simple setup make it the perfect choice for WordPress sites. SendLayer will authenticate your outgoing emails to make sure they always hit customers’ inboxes, and it has detailed documentation to help you authorize your domain.<br><br>You can send hundreds of emails for free when you sign up for a trial.<br><br>To get started, read our <a href="%2$s" target="_blank" rel="noopener noreferrer">SendLayer documentation</a>.'=>'<strong><a href="%1$s" target="_blank" rel="noopener noreferrer">SendLayer</a> is our #1 recommended mailer.</strong> Its affordable pricing and simple setup make it the perfect choice for WordPress sites. SendLayer will authenticate your outgoing emails to make sure they always hit customers’ inboxes, and it has detailed documentation to help you authorise your domain.<br><br>You can send hundreds of emails for free when you sign up for a trial.<br><br>To get started, read our <a href="%2$s" target="_blank" rel="noopener noreferrer">SendLayer documentation</a>.','Verify your domain Region is correct.'=>'Verify your domain Region is correct.','Verify your <a href="%1$s" target="_blank" rel="noopener noreferrer">Domain Name</a> is correct.'=>'Verify your <a href="%1$s" target="_blank" rel="noopener noreferrer">Domain Name</a> is correct.','Hide Announcements'=>'Hide Announcements','WPForms'=>'WPForms','Return Path indicates where non-delivery receipts - or bounce messages - are to be sent.'=>'Return Path indicates where non-delivery receipts - or bounce messages - are to be sent.','Save Changes'=>'Save Changes','Settings'=>'Settings','Authentication'=>'Authentication','From Email'=>'From Email','From Name'=>'From Name','SMTP Port'=>'SMTP Port','Send a Test Email'=>'Send a Test Email','Encryption'=>'Encryption','SMTP Host'=>'SMTP Host','Return Path'=>'Return Path','Mailer'=>'Mailer','WP Mail SMTP'=>'WP Mail SMTP','Email'=>'Email']];