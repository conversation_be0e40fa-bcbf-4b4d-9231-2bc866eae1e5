{"translation-revision-date": "2024-10-02 16:14:23+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "Toggle to use the global query context that is set with the current template, such as variations of the product catalog or search. Disable to customize the filtering independently.": ["Toggle to use the global query context that is set with the current template, such as variations of the product catalogue or search. Disable to customise the filtering independently."], "An error has prevented the block from being updated.": ["An error has prevented the block from being updated."], "%d term": ["%d term", "%d terms"], "%1$s, has %2$d term": ["%1$s, has %2$d term", "%1$s, has %2$d terms"], "%1$s, has %2$d product": ["%1$s, has %2$d product", "%1$s, has %2$d products"], "Give us your feedback.": ["Give us your feedback."], "Feedback?": ["Feedback?"], "Loading…": ["Loading…"], "Advanced Filters": ["Advanced Filters"], "Product Summary": ["Product summary"], "Display the title of a product.": ["Display the title of a product."], "Display a short description about a product.": ["Display a short description about a product."], "The following error was returned": ["The following error was returned"], "The following error was returned from the API": ["The following error was returned from the API"], "Search results updated.": ["Search results updated."], "%d item selected": ["%d item selected", "%d items selected"], "Search for items": ["Search for items"], "No results for %s": ["No results for %s"], "No items found.": ["No items found."], "Clear all selected items": ["Clear all selected items"], "Clear all": ["Clear all"], "Remove %s": ["Remove %s"], "%d attribute selected": ["%d attribute selected", "%d attributes selected"], "All selected attributes": ["All selected attributes"], "Any selected attributes": ["Any selected attributes"], "Clear all product attributes": ["Clear all product attributes"], "Pick at least two attributes to use this setting.": ["Pick at least two attributes to use this setting."], "Product attribute search results updated.": ["Product attribute search results updated."], "Search for product attributes": ["Search for product attributes"], "Your store doesn't have any product attributes.": ["Your store doesn't have any product attributes."], "Hand-picked Products": ["Hand-picked Products"], "Display products matching": ["Display products matching"], "Product Title": ["Product Title"], "Product Attributes": ["Product Attributes"], "Related products": ["Related products"], "%d product": ["%d product", "%d products"], "Stock status": ["Stock status"], "Attributes": ["Attributes"]}}, "comment": {"reference": "assets/client/blocks/product-query.js"}}