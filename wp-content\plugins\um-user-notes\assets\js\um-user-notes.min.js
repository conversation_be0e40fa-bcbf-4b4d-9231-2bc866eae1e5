wp.hooks.addAction("um_window_resize","um_user_notes_frontend",function(){jQuery(".um-notes-grid").length&&jQuery(".um-notes-grid").imagesLoaded(function(){jQuery(".um-notes-grid").masonry("reloadItems"),jQuery(".um-notes-grid").masonry("layout")})}),jQuery(document).ready(function(i){i(document.body).on("click","#um_user_notes_download",function(){var e=i(".um-note-content").data("id"),t=i(this).data("nonce");wp.ajax.send("um_notes_download",{data:{_nonce:t,id:e},success:function(e){console.log(e),window.open(e)},error:function(e){console.log(e)}})}),i(document.body).on("click","#um_user_notes_print",function(){var e=i(".um-note-content").html(),t=window.open("","","width=600,height=600");t.document.open(),t.document.write("<html><head><style>img{max-width:100%;}</style></head><body>"+e+"</body></html>"),t.document.close(),t.onload=function(){t.print()}}),i(".um-notes-grid").length&&i(".um-notes-grid").masonry({itemSelector:".note-block"}),i.fn.um_serializeFiles=function(){var e=i(this),o=new FormData,t=e.serializeArray();return i.each(e.find('input[type="file"]'),function(e,n){i.each(i(n)[0].files,function(e,t){o.append(n.name,t)})}),i.each(t,function(e,t){o.append(t.name,t.value)}),o},i(document.body).on("change","#um_notes_image_control",function(e){var t=i(this).parents(".um_notes_image_label"),n=new FileReader;n.onload=function(e){e=e.target.result;t.css("background-image",'url("'+e+'")'),t.css("background-size","cover")},this.files.length&&(n.readAsDataURL(this.files[0]),i("#um_notes_clear_image").css("display","block"),t.find(".um_notes_image_label_text").html(t.find("> span").data("edit_photo")))}),i(document.body).on("click","#um_notes_add_btn",function(e){e.preventDefault();var n,o,a=i(this);a.hasClass("busy")||(tinyMCE.triggerSave(),n=a.html(),o=i("form#um-user-notes-add"),a.html('<i class="um-user-notes-ajax-loading"></i>').addClass("busy"),wp.ajax.send({data:o.um_serializeFiles(),cache:!1,contentType:!1,processData:!1,success:function(e){a.html(n).removeClass("busy"),o.trigger("reset"),i("label.um_notes_image_label").css("background-image","none"),i("#um_notes_clear_image").hide();var t=o.find(".form-response");t.html('<p style="background:green;padding:10px;color:#fff;">'+e.display+"</p>"),t.find(".um_note_read_more").trigger("click")},error:function(e){console.log(e),a.html(n).removeClass("busy"),o.find(".form-response").html('<div style="background:red;padding:10px;color:#fff;">'+e+"</div><br/>")}}))}),i(document.body).on("click","#um_notes_back_btn",function(e){e.preventDefault();var t,n,o,e=i(this);e.hasClass("busy")||(e.addClass("busy").html('<i class="um-user-notes-ajax-loading"></i>'),n=(t=i("body")).find(".um-notes-modal"),(o=n.find(".um_notes_modal_content")).html('<h1 style="margin-top:100px;text-align:center"><i class="um-user-notes-ajax-loading"></i></h1>'),n.css("display","block"),t.addClass("um_notes_overlay"),wp.ajax.send("um_notes_view",{data:{note:e.data("note_id"),_nonce:e.data("nonce")},success:function(e){o.html(e)},error:function(e){console.log(e)}}))}),i(document.body).on("click","#um_notes_update_btn",function(e){e.preventDefault();var t,e=i(this);e.hasClass("busy")||(tinyMCE.triggerSave(),e.addClass("busy").html('<i class="um-user-notes-ajax-loading"></i>'),t=i("form#um-user-notes-edit"),wp.ajax.send("um_notes_update",{data:t.um_serializeFiles(),cache:!1,contentType:!1,processData:!1,success:function(e){t.find(".form-response").html('<p style="background:green;padding:10px;color:#fff;">'+wp.i18n.__("Note successfully updated.","um-user-notes")+"</p>"),setTimeout(function(){i("#um_notes_back_btn").trigger("click")},2e3),i(".um-notes-modal").addClass("updated")},error:function(e){console.log(e),t.find(".form-response").html('<p style="background:red;padding:10px;color:#fff;">'+e+"</p>")}}))}),i(document.body).on("click",".um_note_read_more",function(e){e.preventDefault();var e=i(this),t=i("body"),n=t.find(".um-notes-modal"),o=n.find(".um_notes_modal_content");o.html('<h1 style="margin-top:100px;text-align:center"><i class="um-user-notes-ajax-loading"></i></h1>'),n.css("display","block"),t.addClass("um_notes_overlay"),wp.ajax.send("um_notes_view",{data:{note:e.data("note_id"),_nonce:e.data("nonce")},success:function(e){o.html(e)},error:function(e){o.html(e)}})}),i(document.body).on("click","#um_notes_modal_close",function(e){e.preventDefault();var e=i(this),t=e.parents(".um-notes-modal"),n=t.find(".um_notes_modal_content");t.hasClass("updated")?location.reload():""!==(e=e.data("close"))&&window.location.href!==e?document.location.href=e:(n.html(""),t.css("display","none"),wp.editor.remove("note_content"),i("body").removeClass("um_notes_overlay"))}).on("click","div.um-notes-modal",function(e){e=jQuery(e.target);e.is("div.um-notes-modal")&&e.find("#um_notes_modal_close").trigger("click")}),i(document.body).on("click",".um_notes_delete_note",function(e){e.preventDefault();var t,n,e=i(this);e.html();e.hasClass("busy")||(t=e.attr("data-id"),n=e.attr("data-nonce"),confirm(wp.i18n.__("Want to delete note?","um-user-notes"))&&(e.addClass("busy"),e.html('<i class="um-user-notes-ajax-loading"></i>'),wp.ajax.send("um_notes_delete",{data:{post_id:t,_nonce:n},success:function(e){location.reload()},error:function(e){console.log(e)}})))}),i(document.body).on("click",".um_notes_edit_note",function(e){e.preventDefault();var t,n,e=i(this);wp.editor.remove("note_content_edit"),e.hasClass("busy")||(wp.editor.remove("note_content"),e.addClass("busy").html('<i class="um-user-notes-ajax-loading"></i>'),t=e.attr("data-id"),e=e.attr("data-nonce"),(n=i("body").find(".um-notes-modal").find(".um_notes_modal_content")).html('<h1 style="margin-top:100px;text-align:center"><i class="um-user-notes-ajax-loading large"></i></h1>'),wp.ajax.send("um_notes_edit",{data:{post_id:t,_nonce:e},success:function(e){n.html(e),wp.editor.initialize("note_content_edit",{tinymce:{wpautop:!0,plugins:"charmap colorpicker compat3x directionality fullscreen hr image lists media paste tabfocus textcolor wordpress wpautoresize wpdialogs wpeditimage wpemoji wpgallery wplink wptextpattern wpview",toolbar1:"formatselect bold italic | bullist numlist | blockquote | alignleft aligncenter alignright | link unlink | wp_more | spellchecker | fullscreen | wp_adv",toolbar2:"strikethrough | hr | forecolor | pastetext | removeformat | charmap | outdent | indent | undo | redo | wp_help"},quicktags:!0})},error:function(e){console.log(e)}}))}),i(document.body).on("click","#um_notes_clear_image",function(e){e.preventDefault();var e=i(this),t=(e.attr("data-id"),e.attr("data-mode"),e.parents("form"));i(".um_notes_image_label").css("background-image","none"),i(".um_notes_image_label .um_notes_image_label_text").html(i(".um_notes_image_label > span").data("add_photo")),i("#um_notes_image_control").val(null),t.find('[name="thumbnail_id"]').val(""),e.css("display","none")}),i(document.body).on("click","#um-notes-load-more-btn",function(e){e.preventDefault();var t=i(this),e=t.data("per_page"),n=t.data("profile"),o=t.data("nonce"),a=t.data("page"),s=t.html();t.html('<i class="um-user-notes-ajax-loading"></i>'),wp.ajax.send("um_notes_load_more",{data:{per_page:e,offset:e*a,profile:n,_nonce:o},success:function(e){!0!==e.loadmore&&t.hide(),"empty"!==e.html&&(i("body").find(".um-notes-holder").append(e.html),t.data("page",parseInt(a)+1).html(s)),i(".um-notes-grid").length&&i(".um-notes-grid").imagesLoaded(function(){i(".um-notes-grid").masonry("reloadItems"),i(".um-notes-grid").masonry("layout")})},error:function(e){console.log(e)}})})});