<?php
/**
 * Template for the UM User Photos. The comment edit form.
 *
 * Page: "Profile", tab "Photos", the image popup
 * Call: UM()->User_Photos()->ajax()->get_um_user_photos_comment_edit()
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/modal/edit-comment.php
 * @var object $comment
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
?>

<div class="um-form">
	<form method="post" action="update_um_user_photos_comment">

		<div class="um-galley-form-response"></div>

		<div class="um-field">
			<textarea class="um-user-photos-comment-textarea" name="comment_text" placeholder="<?php esc_attr_e( 'Write a comment...', 'um-user-photos' ); ?>"><?php echo esc_textarea( $comment->comment_content ); ?></textarea>
		</div>

		<div class="um-field um-user-photos-modal-footer text-right">

			<button type="button" id="um-user-photos-comment-update-btn" data-wpnonce="<?php echo esc_attr( wp_create_nonce( 'um_user_photos_comment_update' ) ); ?>" class="um-modal-btn" data-commentid="<?php echo esc_attr( $comment->comment_ID ); ?>" ><?php esc_html_e( 'Update', 'um-user-photos' ); ?></button>

			<a href="javascript:void(0);" class="um-modal-btn alt um-user-photos-modal-close-link"><?php esc_html_e( 'Cancel', 'um-user-photos' ); ?></a>
		</div>

		<?php wp_nonce_field( 'um_edit_comment' ); ?>
	</form>
</div>
