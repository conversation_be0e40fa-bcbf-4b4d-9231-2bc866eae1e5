﻿=== Ultimate Member - User Notes ===
Author URI: https://ultimatemember.com/
Plugin URI: https://ultimatemember.com/extensions/user-notes/
Contributors: ultimatemember, champsupertramp, nsinelnikov
Donate link:
Tags: private messaging, email, user, community
Requires at least: 5.5
Tested up to: 6.7
Stable tag: 1.1.6
License: GNU Version 2 or Any Later Version
License URI: http://www.gnu.org/licenses/gpl-3.0.txt
Requires UM core at least: 2.7.0

Allow users to create public and private notes from their profile.

== Description ==

Allow users to create public and private notes from their profile.

= Key Features: =

* Users can create private and public notes
* Notes appear on a user's profile in a separate tab
* Users can save notes as drafts
* Users can edit and delete their notes
* Integrated with social activity extension to show in social activity when a user creates a new note
* Integrated with friends extension to create notes only for friends

Read about all of the plugin's features at [Ultimate Member - User Notes](https://ultimatemember.com/extensions/user-notes/)

= Documentation & Support =

Got a problem or need help with Ultimate Member - User Notes? Head over to our [documentation](https://docs.ultimatemember.com/category/1478-user-notes) and perform a search of the knowledge base.

== Installation ==

1. Activate the plugin
2. That's it. Go to Ultimate Member > Settings > Extensions > User Notes to customize plugin options
3. For more details, please visit the official [Documentation](https://docs.ultimatemember.com/article/1479-how-to-setup-user-notes-extension) page.

== Changelog ==

= 1.1.6: November 15, 2024 =

* Fixed: "Load textdomain just in time" issue

= 1.1.5: October 01, 2024 =

* Fixed: Break lines in the downloaded note

= 1.1.4: May 22, 2024 =

* Fixed: Multiple paragraphs merged into a single paragraph
* Fixed: Displaying shortcodes when user hasn't capabilities for notes
* Tweak: Added Ultimate Member as required plugins

* Templates required update:

  - profile/single-note.php
  - profile/view.php

= 1.1.3: February 21, 2024 =

* Fixed: Allowed protocols while PDF file generated

= 1.1.2: December 20, 2023 =

* Fixed: Note privacy settings with Followers, Following and Friends users. Performance for Activity wall loading.
* Fixed: Note ID visibility only for Administrator user.
* Fixed: Remove the direct link to user note.

= 1.1.1: December 11, 2023 =

* Added: List layout for the displaying notes
* Added: Shortcode `um_user_notes_view` attributes user_id, per_page, view
* Added: Masonry.JS to the grid layout when displaying notes
* Added: Functionality for download notes in the *.pdf format
* Added: Functionality for print notes
* Fixed: Note's privacy settings with Followers and Following users. User Profile page, Activity wall posts
* Fixed: Note's privacy settings with Friends users. User Profile page, Activity wall posts
* Tweak: Added CPT as UM registered post type for define proper UM screen on wp-admin
* Tweak: `um-admin-clear` CSS class. It duplicates WordPress native `clear`. Using WordPress native instead
* Tweak: Enhancements related to WPCS
* Deprecated: `get_notes_content()` function. Overwritten by the PHP templates inside shortcodes

= 1.1.0: October 11, 2023 =

* Updated: Dependencies versions based on the recent changes for `UM()->frontend()->enqueue()::get_suffix();`
* Fixed: Case when extension isn't active based on dependency, but we can provide the license key field

= 1.0.9: August 23, 2023 =

* Added: Shortcodes [um_user_notes_add], [um_user_notes_view]
* Fixed: Redirect on modal close and added the `um` class to modal wrapper
* Fixed: Nonce actions handlers

= 1.0.8: June 14, 2023 =

* Tweak: Template overwrite versioning

= 1.0.7: August 17, 2022 =

* Fixed: Activity posts visibility for different visibility option for the Note
* Fixed: Issue of links not rendering on note content (embed links)

= 1.0.6: February 9, 2022 =

* Fixed: Extension settings structure

= 1.0.5: December 20, 2021 =

* Fixed: redirect on modal closing

* Templates required update:
  - profile/modal.php

= 1.0.4: March 29, 2021 =

* Fixed: Notes tab visibility if it disabled in profile menu
* Fixed: "Disable notes feature?" capability
* Fixed: The note's editor
* Tweak: better performance of the filtering posts on the Activity wall

= 1.0.3: December 8, 2020 =

* Fixed: Social Activity query when UM:Friends extension is inactive

= 1.0.2: October 26, 2020 =

* Fixed: Dependencies with UM:Friends extension

= 1.0.1: October 22, 2020 =

* Added: oEmbed content in the notes
* Fixed: Note image duplicates in the Social Activity wall
* Fixed: Changed loading footer priority
* Fixed: User role settings save
* Fixed: Social activity template
* Fixed: Note's draft status and privacy for the social activity posts
* Fixed: Using HTML in the note's content

= 1.0.0: September 1, 2020 =

* Initial release
