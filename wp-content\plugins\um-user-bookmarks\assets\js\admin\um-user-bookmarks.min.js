wp.hooks.addAction("um_same_page_upgrade","um_user_bookmarks",function(_){var o,s;"sync_user_bookmarks_disable_folders"===_&&(o=0,s=1,um_add_same_page_log(_,wp.i18n.__("Getting bookmarks metadata","um-user-bookmarks")),jQuery.ajax({url:wp.ajax.settings.url,type:"POST",dataType:"json",data:{action:"um_same_page_update",cb_func:"um_get_bookmarks_metadata",nonce:um_admin_scripts.nonce},success:function(a){void 0!==a.data.count?(um_add_same_page_log(_,wp.i18n.__("There are ","um-user-bookmarks")+a.data.count+wp.i18n.__(" bookmarks metadata rows...","um-user-bookmarks")),um_add_same_page_log(_,wp.i18n.__("Start bookmarks metadata upgrading...","um-user-bookmarks")),o=Math.ceil(a.data.count/500),function e(){s<=o?jQuery.ajax({url:wp.ajax.settings.url,type:"POST",dataType:"json",data:{action:"um_same_page_update",cb_func:"um_update_bookmarks_metadata_single",page:s,nonce:um_admin_scripts.nonce},success:function(a){void 0!==a.data?(um_add_same_page_log(_,a.data.message),s++,e()):um_same_page_wrong_ajax(_)},error:function(){um_same_page_something_wrong(_)}}):jQuery("#submit").trigger("click")}()):um_same_page_wrong_ajax(_)},error:function(){um_same_page_something_wrong(_)}}))},10);