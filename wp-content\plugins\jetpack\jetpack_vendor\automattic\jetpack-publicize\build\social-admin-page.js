/*! For license information please see social-admin-page.js.LICENSE.txt */
(()=>{var e={51113:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var c=n(86087);const a=(0,c.forwardRef)((function({icon:e,size:t=24,...n},a){return(0,c.cloneElement)(e,{width:t,height:t,...n,ref:a})}))},71797:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var c=n(5573),a=n(10790);const i=(0,a.jsx)(c.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(c.<PERSON>,{d:"m14.5 6.5-1 1 3.7 3.7H4v1.6h13.2l-3.7 3.7 1 1 5.6-5.5z"})})},23751:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var c=n(5573),a=n(10790);const i=(0,a.jsx)(c.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(c.Path,{d:"M12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4ZM12.75 8V13H11.25V8H12.75ZM12.75 14.5V16H11.25V14.5H12.75Z"})})},83883:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var c=n(5573),a=n(10790);const i=(0,a.jsx)(c.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(c.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})})},64969:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var c=n(5573),a=n(10790);const i=(0,a.jsx)(c.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)(c.Path,{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"})})},98248:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var c=n(5573),a=n(10790);const i=(0,a.jsx)(c.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)(c.Path,{d:"M6.5 12.4L12 8l5.5 4.4-.9 1.2L12 10l-4.5 3.6-1-1.2z"})})},31249:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var c=n(5573),a=n(10790);const i=(0,a.jsx)(c.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(c.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})})},53512:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var c=n(5573),a=n(10790);const i=(0,a.jsx)(c.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(c.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})})},19783:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var c=n(5573),a=n(10790);const i=(0,a.jsx)(c.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)(c.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M5.5 12a6.5 6.5 0 1 0 13 0 6.5 6.5 0 0 0-13 0ZM12 4a8 8 0 1 0 0 16 8 8 0 0 0 0-16Zm.75 4v1.5h-1.5V8h1.5Zm0 8v-5h-1.5v5h1.5Z"})})},41496:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var c=n(5573),a=n(10790);const i=(0,a.jsx)(c.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(c.Path,{fillRule:"evenodd",d:"M17.375 15.656A6.47 6.47 0 0018.5 12a6.47 6.47 0 00-.943-3.374l-1.262.813c.448.749.705 1.625.705 2.561a4.977 4.977 0 01-.887 2.844l1.262.813zm-1.951 1.87l-.813-1.261A4.976 4.976 0 0112 17c-.958 0-1.852-.27-2.613-.736l-.812 1.261A6.47 6.47 0 0012 18.5a6.47 6.47 0 003.424-.974zm-8.8-1.87A6.47 6.47 0 015.5 12c0-1.235.344-2.39.943-3.373l1.261.812A4.977 4.977 0 007 12c0 1.056.328 2.036.887 2.843l-1.262.813zm2.581-7.803A4.977 4.977 0 0112 7c1.035 0 1.996.314 2.794.853l.812-1.262A6.47 6.47 0 0012 5.5a6.47 6.47 0 00-3.607 1.092l.812 1.261zM12 20a8 8 0 100-16 8 8 0 000 16zm0-4.5a3.5 3.5 0 100-7 3.5 3.5 0 000 7z",clipRule:"evenodd"})})},45459:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var c=n(5573),a=n(10790);const i=(0,a.jsx)(c.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(c.Path,{d:"M11 12.5V17.5H12.5V12.5H17.5V11H12.5V6H11V11H6V12.5H11Z"})})},30:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var c=n(5573),a=n(10790);const i=(0,a.jsx)(c.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)(c.Path,{d:"M18 5.5H6a.5.5 0 0 0-.5.5v12a.5.5 0 0 0 .5.5h12a.5.5 0 0 0 .5-.5V6a.5.5 0 0 0-.5-.5ZM6 4h12a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2Zm1 5h1.5v1.5H7V9Zm1.5 4.5H7V15h1.5v-1.5ZM10 9h7v1.5h-7V9Zm7 4.5h-7V15h7v-1.5Z"})})},77136:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var c=n(5573),a=n(10790);const i=(0,a.jsx)(c.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(c.Path,{d:"M11.776 4.454a.25.25 0 01.448 0l2.069 4.192a.25.25 0 00.188.137l4.626.672a.25.25 0 01.139.426l-3.348 3.263a.25.25 0 00-.072.222l.79 4.607a.25.25 0 01-.362.263l-4.138-2.175a.25.25 0 00-.232 0l-4.138 2.175a.25.25 0 01-.363-.263l.79-4.607a.25.25 0 00-.071-.222L4.754 9.881a.25.25 0 01.139-.426l4.626-.672a.25.25 0 00.188-.137l2.069-4.192z"})})},46941:(e,t,n)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const n="color: "+this.color;t.splice(1,0,n,"color: inherit");let c=0,a=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(c++,"%c"===e&&(a=c))})),t.splice(a,0,n)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=n(53212)(t);const{formatters:c}=e.exports;c.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},53212:(e,t,n)=>{e.exports=function(e){function t(e){let n,a,i,s=null;function o(...e){if(!o.enabled)return;const c=o,a=Number(new Date),i=a-(n||a);c.diff=i,c.prev=n,c.curr=a,n=a,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let s=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((n,a)=>{if("%%"===n)return"%";s++;const i=t.formatters[a];if("function"==typeof i){const t=e[s];n=i.call(c,t),e.splice(s,1),s--}return n})),t.formatArgs.call(c,e);(c.log||t.log).apply(c,e)}return o.namespace=e,o.useColors=t.useColors(),o.color=t.selectColor(e),o.extend=c,o.destroy=t.destroy,Object.defineProperty(o,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(a!==t.namespaces&&(a=t.namespaces,i=t.enabled(e)),i),set:e=>{s=e}}),"function"==typeof t.init&&t.init(o),o}function c(e,n){const c=t(this.namespace+(void 0===n?":":n)+e);return c.log=this.log,c}function a(e,t){let n=0,c=0,a=-1,i=0;for(;n<e.length;)if(c<t.length&&(t[c]===e[n]||"*"===t[c]))"*"===t[c]?(a=c,i=n,c++):(n++,c++);else{if(-1===a)return!1;c=a+1,i++,n=i}for(;c<t.length&&"*"===t[c];)c++;return c===t.length}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names,...t.skips.map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const n=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of n)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const n of t.skips)if(a(e,n))return!1;for(const n of t.names)if(a(e,n))return!0;return!1},t.humanize=n(44997),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((n=>{t[n]=e[n]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let n=0;for(let t=0;t<e.length;t++)n=(n<<5)-n+e.charCodeAt(t),n|=0;return t.colors[Math.abs(n)%t.colors.length]},t.enable(t.load()),t}},2467:e=>{"use strict";var t,n="object"==typeof Reflect?Reflect:null,c=n&&"function"==typeof n.apply?n.apply:function(e,t,n){return Function.prototype.apply.call(e,t,n)};t=n&&"function"==typeof n.ownKeys?n.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var a=Number.isNaN||function(e){return e!=e};function i(){i.init.call(this)}e.exports=i,e.exports.once=function(e,t){return new Promise((function(n,c){function a(n){e.removeListener(t,i),c(n)}function i(){"function"==typeof e.removeListener&&e.removeListener("error",a),n([].slice.call(arguments))}h(e,t,i,{once:!0}),"error"!==t&&function(e,t,n){"function"==typeof e.on&&h(e,"error",t,n)}(e,a,{once:!0})}))},i.EventEmitter=i,i.prototype._events=void 0,i.prototype._eventsCount=0,i.prototype._maxListeners=void 0;var s=10;function o(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function r(e){return void 0===e._maxListeners?i.defaultMaxListeners:e._maxListeners}function l(e,t,n,c){var a,i,s,l;if(o(n),void 0===(i=e._events)?(i=e._events=Object.create(null),e._eventsCount=0):(void 0!==i.newListener&&(e.emit("newListener",t,n.listener?n.listener:n),i=e._events),s=i[t]),void 0===s)s=i[t]=n,++e._eventsCount;else if("function"==typeof s?s=i[t]=c?[n,s]:[s,n]:c?s.unshift(n):s.push(n),(a=r(e))>0&&s.length>a&&!s.warned){s.warned=!0;var p=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");p.name="MaxListenersExceededWarning",p.emitter=e,p.type=t,p.count=s.length,l=p,console&&console.warn&&console.warn(l)}return e}function p(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function m(e,t,n){var c={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},a=p.bind(c);return a.listener=n,c.wrapFn=a,a}function u(e,t,n){var c=e._events;if(void 0===c)return[];var a=c[t];return void 0===a?[]:"function"==typeof a?n?[a.listener||a]:[a]:n?function(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}(a):g(a,a.length)}function d(e){var t=this._events;if(void 0!==t){var n=t[e];if("function"==typeof n)return 1;if(void 0!==n)return n.length}return 0}function g(e,t){for(var n=new Array(t),c=0;c<t;++c)n[c]=e[c];return n}function h(e,t,n,c){if("function"==typeof e.on)c.once?e.once(t,n):e.on(t,n);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function a(i){c.once&&e.removeEventListener(t,a),n(i)}))}}Object.defineProperty(i,"defaultMaxListeners",{enumerable:!0,get:function(){return s},set:function(e){if("number"!=typeof e||e<0||a(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");s=e}}),i.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},i.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||a(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},i.prototype.getMaxListeners=function(){return r(this)},i.prototype.emit=function(e){for(var t=[],n=1;n<arguments.length;n++)t.push(arguments[n]);var a="error"===e,i=this._events;if(void 0!==i)a=a&&void 0===i.error;else if(!a)return!1;if(a){var s;if(t.length>0&&(s=t[0]),s instanceof Error)throw s;var o=new Error("Unhandled error."+(s?" ("+s.message+")":""));throw o.context=s,o}var r=i[e];if(void 0===r)return!1;if("function"==typeof r)c(r,this,t);else{var l=r.length,p=g(r,l);for(n=0;n<l;++n)c(p[n],this,t)}return!0},i.prototype.addListener=function(e,t){return l(this,e,t,!1)},i.prototype.on=i.prototype.addListener,i.prototype.prependListener=function(e,t){return l(this,e,t,!0)},i.prototype.once=function(e,t){return o(t),this.on(e,m(this,e,t)),this},i.prototype.prependOnceListener=function(e,t){return o(t),this.prependListener(e,m(this,e,t)),this},i.prototype.removeListener=function(e,t){var n,c,a,i,s;if(o(t),void 0===(c=this._events))return this;if(void 0===(n=c[e]))return this;if(n===t||n.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete c[e],c.removeListener&&this.emit("removeListener",e,n.listener||t));else if("function"!=typeof n){for(a=-1,i=n.length-1;i>=0;i--)if(n[i]===t||n[i].listener===t){s=n[i].listener,a=i;break}if(a<0)return this;0===a?n.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(n,a),1===n.length&&(c[e]=n[0]),void 0!==c.removeListener&&this.emit("removeListener",e,s||t)}return this},i.prototype.off=i.prototype.removeListener,i.prototype.removeAllListeners=function(e){var t,n,c;if(void 0===(n=this._events))return this;if(void 0===n.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete n[e]),this;if(0===arguments.length){var a,i=Object.keys(n);for(c=0;c<i.length;++c)"removeListener"!==(a=i[c])&&this.removeAllListeners(a);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=n[e]))this.removeListener(e,t);else if(void 0!==t)for(c=t.length-1;c>=0;c--)this.removeListener(e,t[c]);return this},i.prototype.listeners=function(e){return u(this,e,!0)},i.prototype.rawListeners=function(e){return u(this,e,!1)},i.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):d.call(e,t)},i.prototype.listenerCount=d,i.prototype.eventNames=function(){return this._eventsCount>0?t(this._events):[]}},71631:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={"admin-page":"sexr0jUxC1jVixdKiDnC",background:"vKQ11sLeAM45M04P1ccj","admin-page-header":"iWGAhN9gOB48g0jEO1OQ","sandbox-domain-badge":"JOYmuxQjG4FArIIUxJfA"}},43496:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={section:"cAbGtJDGgLubucBnz7vM"}},91842:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={"section-hero":"vMa4i_Dza2t5Zi_Bw9Nf"}},65486:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={container:"demNsMJjhi7BLY7xhjU5","icon-wrapper":"QiUjdjJSkqh6nH7YMG5A","is-error":"Q080AHcq29J2fc68Hhk5",icon:"hYWbIwhppukXmGnsiT9H","is-warning":"JjHuxWly0HI9C60gorbq","is-info":"Cm8ZFHi3mngl4cj9Gatx","is-success":"ytGBsU015p3LGwOPwFDx"}},77560:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={button:"zI5tJ_qhWE6Oe6Lk75GY","is-icon-button":"tuBt2DLqimiImoqVzPqo",small:"Na39I683LAaSA99REg14",normal:"ipS7tKy9GntCS4R3vekF",icon:"paGLQwtPEaJmtArCcmyK",regular:"lZAo6_oGfclXOO9CC6Rd","full-width":"xJDOiJxTt0R_wSl8Ipz_",loading:"q_tVWqMjl39RcY6WtQA6","external-icon":"CDuBjJp_8jxzx5j6Nept"}},29245:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={cut:"msOlyh2T7D6uhbM6AROg",icon:"cPN7USVqSBpxUswfDtUZ",cta:"EmnJAyEzzn1QpA8HtypY",iconContainer:"vV7YZikAz0oHYsuvtxMq",description:"T1YaMupeZmBIpXZHY9EZ"}},57448:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={"one-section-style":"Gv5Luzad_e0BQaGSPdvq",primary:"EZSX4CwXdPsrNRfhBEGk",secondary:"OCm4Aq9srXhRMHqkqg1I","is-viewport-small":"yrFREta4NGFUulXcnM4k"}},78888:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={"global-notices":"jT9Kt2ZTvxPKDQ1pa7sN"}},4459:()=>{},84813:()=>{},47842:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={"star-icon":"cuoSlhSNrqf1dozY22Xb",jetpack:"lAIiifeLMmZAPlQ9n9ZR","checkmark-icon":"JLquNpQVlysAamuh5lJO",socialIcon:"cbOwD8Y4tFjwimmtchQI",bluesky:"aLWBKY0yRghEk7tNCgK3",facebook:"aHOlEBGD5EA8NKRw3xTw",twitter:"af4Y_zItXvLAOEoSDPSv",linkedin:"f68aqF3XSD1OBvXR1get",tumblr:"xFI0dt3UiXRlRQdqPWkx",google:"q7JEoyymveP6kF747M43",mastodon:"DKOBOTVmTLbh26gUH_73",nextdoor:"n5XodNsuMfMAAvqHFmbw",instagram:"cL3m0xBYTYhIKI7lCqDB",whatsapp:"fftumuc_lJ6v0tq4UMVR",threads:"inzgC27qxdt7hSdhTWRI"}},9128:()=>{},59053:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={sm:"(max-width: 599px)",md:"(min-width: 600px) and (max-width: 959px)",lg:"(min-width: 960px)"}},71157:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={sm:"(max-width: 599px)",md:"(min-width: 600px) and (max-width: 959px)",lg:"(min-width: 960px)",smCols:"4",mdCols:"8",lgCols:"12","col-sm-1":"RuVLl3q4lxTQa3wbhBJB","col-sm-1-start":"f9LZTRG4MMK42rS89afW","col-sm-1-end":"bHe_zKxjjpUwHw_MdYE1","col-sm-2":"QZbNrOqE2aNSn50xVhpU","col-sm-2-start":"ev7W3z7zVYPeHAlYqZjf","col-sm-2-end":"NJWd1m_e7lOiPYru2ZMP","col-sm-3":"Xc6nt1Qc1DI0Z2A3gt1r","col-sm-3-start":"UIcN_GXiPRoIsin8Kohg","col-sm-3-end":"GRKCyqb5LufCSCgykKFc","col-sm-4":"i_qTq8gqhhC3vIUepVRB","col-sm-4-start":"G3qaZ3Jpbvam_1XvGxgc","col-sm-4-end":"VRCNYKZtO9zukEwmgP1y","col-md-1":"tRm008K_WJL79WoNZTNL","col-md-1-start":"l5T2P_bgKts4tdaRkS1d","col-md-1-end":"zOCxfLZpF6BlgC7a_Yq1","col-md-2":"F80DdgVn0m5OpvtSQWka","col-md-2-start":"oI1c7JYfiJtMQHbhngtU","col-md-2-end":"pMQtA_4jh1_1lVknqEP5","col-md-3":"VenqMpdgyKQVUNNQcfqd","col-md-3-start":"seNYL99uoczf9V4MxBxT","col-md-3-end":"YKfF1HFhI9KygA5l3b2J","col-md-4":"yAi0Cv1xDWkoqsaUhvhR","col-md-4-start":"ubhnyZOnkgxNhh6XtVWv","col-md-4-end":"RGOPGQbWMJ9Ei5oFxS7X","col-md-5":"Sz1E2aWbX483ijdi6yge","col-md-5-start":"tku6_bRYrX9tMbgYGmIl","col-md-5-end":"b5JHttOhSEcI1WBlqAjk","col-md-6":"FboSx5MoKTAWbxXyYlCw","col-md-6-start":"Jhs8yEEmodG30edbJvag","col-md-6-end":"IpzbbKVqEqPcfIGkXkwt","col-md-7":"mhCPwfAZ4Kmm_empzJAq","col-md-7-start":"x034ilrJF7rO9UJB2rI1","col-md-7-end":"Wt8t2e16viRrOJ1lLA5v","col-md-8":"S6pIrEy9AMLKx9bgh_Ae","col-md-8-start":"kEfI4tGyuWfHTlRnvIab","col-md-8-end":"PUzX4RRsKq1dnsz3gebS","col-lg-1":"X_pdcLJikd8LS_YAdJlB","col-lg-1-start":"tl936d14Huby4khYp05X","col-lg-1-end":"hnge0LnR69d3NXEtEE1t","col-lg-2":"fj0NUMuyZQcPNgKcjp5Z","col-lg-2-start":"R2ncBX7a2NigdYCcV1OX","col-lg-2-end":"t8vMSDVYno9k9itRwnXb","col-lg-3":"wsDuEN2GqHx6qzo8dUdk","col-lg-3-start":"cIEVPUweWtLBy3xaXnMx","col-lg-3-end":"fajUWBwu1m2B479j3jmz","col-lg-4":"YR0c7fQTgMkDdWzwSyLp","col-lg-4-start":"xlwp8BmplxkKNMI7gamo","col-lg-4-end":"_C4O1w9DUqx1m3gPf8aA","col-lg-5":"Z54F1hAErckAIrKlxnXW","col-lg-5-start":"ezSDWkRHmKSxDJXxuiOH","col-lg-5-end":"T0ChoeAjGJjkkNrYhD4g","col-lg-6":"qtMoMPF6yHvGJnWHSsde","col-lg-6-start":"gdoywN5VPiWERfIBqkph","col-lg-6-end":"wUev_VH5uf_pwFFlbnAU","col-lg-7":"egIPDFJsOpownTClq9XP","col-lg-7-start":"yGhp9yoAW7k0kQik9AB7","col-lg-7-end":"SJ43U9mR5wUg5V2qBeQA","col-lg-8":"cTuyHfMwSUJxN_HdIEgd","col-lg-8-start":"smCr8DaIagcumdvdldiK","col-lg-8-end":"T03NHzQJvzwL6wAfIiTL","col-lg-9":"pMvxM3RJGjqyNdf9qg1Y","col-lg-9-start":"iIVpNRwEnQ_JI5gpp9EN","col-lg-9-end":"ZbQ4u4vGSX5rJOje4uGL","col-lg-10":"gKb5wuIDAlKGbrjK2vxy","col-lg-10-start":"Z7pINdImE2WJiYnZBTqm","col-lg-10-end":"ZTxp6qpvwurMdOnLLSz1","col-lg-11":"NnQTlbfnxPDR6cQ7rygg","col-lg-11-start":"O137wZd6Yl0olSA9PsXR","col-lg-11-end":"zf2OJtQ2MPz6SDoh6CB0","col-lg-12":"U3H6UHW6HqRt9hdzVg3O","col-lg-12-start":"zynnNeS_ZBTxABcVpUQH","col-lg-12-end":"vI8tltFZtFUNAy9Iag9s"}},82498:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={sm:"(max-width: 599px)",md:"(min-width: 600px) and (max-width: 959px)",lg:"(min-width: 960px)",container:"SqdhUZkXCRuIpErj1B3z",fluid:"OZC_9a1LhpWF9dv15Gdh"}},53128:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={container:"p4qz2tkq0p9hxucJ6Qk2",table:"lbNDyXioOwvyvbALtCBm","is-viewport-large":"s2Lsn4kbm6BrS3DSndRB",card:"cLaNK_XcbTGlRQ4Tp43Q","is-primary":"CYt1X0eH1icRjhtJ28jx",header:"DAkZc1P9A3K12fjEliMg",item:"WUBuYABl8nymjs9NnCEL","last-feature":"ANtCFeb41NhA8PA3H7ZN",value:"Ql2gy_148yW8Vw5vhaKD",icon:"EAQrAnQEW1z1BfdY5gbC","icon-check":"JDSTlLoOC_4aUoH2oNM2","icon-cross":"zNdQRJ1w7BvaQOYyqzHK",popover:"lr7vbX95SKtoe7DarJcZ","popover-icon":"KRrGp2xdkeBOxLZeuQ6X",tos:"H_ZJiRVJg0LiMXPGOcmt","tos-container":"x21z_DixObRDsDaWotP1"}},20184:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={wrapper:"_8KSnqvLTWZxA3pOHJ_4","is-card":"eHRue5JnliVwnpFj02QC","card-header":"CsSIwCSOu7xPJBneLTxv","card-container":"HdlkIQj607CnZRJO1Jnb","product-bundle-icon":"Z1tO5mGmNssD0vfiaKJP","product-bundle-icons":"ZkroujjLoalBMpu5FYg5","product-icon":"Cy52r6jtBvpjD17AVGW5","plus-icon":"Zdhe_V4nBb2DA2uv1wlk",features:"fgLpNd__JyphNEMd0dp1",check:"Bb1J_iNXK19CYf3Df4PW","product-has-required-plan":"rK_SkBOsecwHTfrhVbOA","add-button":"C0fzq8OhjvcgY9zAYM7_"}},9921:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={container:"dovianZYLKhnbnh9I06o","price-container":"lljtQMhW7lq5tE5SDJEf","promo-label":"NubApIV1vQCRUNprfm6b",price:"dhFQXpZfMwVI8vuYHnwC","is-not-off-price":"eD7hzxFmdtG_MgmBtl_k",footer:"C64ZjjUAqJC1T2Sa7apS",legend:"UpZDGew6Ay1hPoP6eI7b",symbol:"TDiiPbuW1Z0_05u_pvcK"}},85335:()=>{},66392:()=>{},22073:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={reset:"WQVtrU6q0L1Igcj7wCrQ","headline-medium":"UujoBFTnQNY2cWU2SIsH","headline-small":"TeGO5V_thHw5lDAm1_2M","headline-small-regular":"WolQzb2MsSgiNmLtc7_j","title-medium":"hUB0JT8p1T2Hw28N6qC8","title-medium-semi-bold":"gKZWDv5chz3_O3Syp74H","title-small":"zY2No8Ga4b8shbOQGhnv",body:"tIj0D1t8Cc892ikmgFPZ","body-small":"KdcN0BnOaVeVhyLRKqhS","body-extra-small":"dso3Rh3tl3Xv1GumBktz","body-extra-small-bold":"mQ1UlbN9u4Mg9byO8m7v",label:"PItlW5vRExLnTj4a8eLE","m-0":"TwRpPlktzxhmFVeua7P5","mx-0":"zVfqx7gyb3o9mxfGynn1","my-0":"iSHVzNiB9iVleGljaQxy","mt-0":"xqDIp6cNVr_E6RXaiPyD","mr-0":"S8EwaXk1kyPizt6x4WH2","mb-0":"ODX5Vr1TARoLFkDDFooD","ml-0":"cphJ8dCpfimnky7P2FHg","m-1":"PFgIhNxIyiSuNvQjAIYj","mx-1":"M2jKmUzDxvJjjVEPU3zn","my-1":"io15gAh8tMTNbSEfwJKk","mt-1":"rcTN5uw9xIEeMEGL3Xi_","mr-1":"CQSkybjq2TcRM1Xo9COV","mb-1":"hfqOWgq6_MEGdFE82eOY","ml-1":"I8MxZQYTbuu595yfesWA","m-2":"kQkc6rmdpvLKPkyoJtVQ","mx-2":"j6vFPxWuu4Jan2ldoxpp","my-2":"hqr39dC4H_AbactPAkCG","mt-2":"c3dQnMi16C6J6Ecy4283","mr-2":"YNZmHOuRo6hU7zzKfPdP","mb-2":"Db8lbak1_wunpPk8NwKU","ml-2":"ftsYE5J9hLzquQ0tA5dY","m-3":"Det4MHzLUW7EeDnafPzq","mx-3":"h_8EEAztC29Vve1datb5","my-3":"YXIXJ0h1k47u6hzK8KcM","mt-3":"soADBBkcIKCBXzCTuV9_","mr-3":"zSX59ziEaEWGjnpZa4uV","mb-3":"yrVTnq_WBMbejg89c2ZQ","ml-3":"UKtHPJnI2cXBWtPDm5hM","m-4":"guexok_Tqd5Tf52hRlbT","mx-4":"oS1E2KfTBZkJ3F0tN7T6","my-4":"DN1OhhXi6AoBgEdDSbGd","mt-4":"ot2kkMcYHv53hLZ4LSn0","mr-4":"A1krOZZhlQ6Sp8Cy4bly","mb-4":"pkDbXXXL32237M0hokEh","ml-4":"XXv4kDTGvEnQeuGKOPU3","m-5":"yGqHk1a57gaISwkXwXe6","mx-5":"X8cghM358X3DkXLc9aNK","my-5":"GdfSmGwHlFnN2S6xBn1f","mt-5":"yqeuzwyGQ7zG0avrGqi_","mr-5":"g9emeCkuHvYhveiJbfXO","mb-5":"Lvk3dqcyHbZ07QCRlrUQ","ml-5":"r3yQECDQ9qX0XZzXlVAg","m-6":"aQhlPwht2Cz1X_63Miw0","mx-6":"JyHb0vK3wJgpblL9s5j8","my-6":"cY2gULL1lAv6WPNIRuf3","mt-6":"NBWQ9Lwhh_fnry3lg_p7","mr-6":"yIOniNe5E40C8fWvBm5V","mb-6":"t30usboNSyqfQWIwHvT3","ml-6":"Nm_TyFkYCMhOoghoToKJ","m-7":"C4qJKoBXpgKtpmrqtEKB","mx-7":"S93Srbu6NQ_PBr7DmTiD","my-7":"fJj8k6gGJDks3crUZxOS","mt-7":"cW6D6djs7Ppm7fD7TeoV","mr-7":"DuCnqNfcxcP3Z__Yo5Ro","mb-7":"im8407m2fw5vOg7O2zsw","ml-7":"G0fbeBgvz2sh3uTP9gNl","m-8":"kvW3sBCxRxUqz1jrVMJl","mx-8":"tOjEqjLONQdkiYx_XRnw","my-8":"op5hFSx318zgxsoZZNLN","mt-8":"c9WfNHP6TFKWIfLxv52J","mr-8":"sBA75QqcqRwwYSHJh2wc","mb-8":"GpL6idrXmSOM6jB8Ohsf","ml-8":"HbtWJoQwpgGycz8dGzeT","p-0":"uxX3khU88VQ_Ah49Ejsa","px-0":"KX0FhpBKwKzs9fOUdbNz","py-0":"PfK8vKDyN32dnimlzYjz","pt-0":"emxLHRjQuJsImnPbQIzE","pr-0":"kJ8WzlpTVgdViXt8ukP9","pb-0":"tg_UIUI11VBzrTAn2AzJ","pl-0":"uczvl8kaz84oPQJ2DB2R","p-1":"o7UHPcdVK3lt7q3lqV4o","px-1":"IDqEOxvDoYrFYxELPmtX","py-1":"DdywPW2qSYlu2pt8tpO2","pt-1":"npy3hw4A5QSkDicb2CJJ","pr-1":"LgbptTApNY5NwLQvEFAt","pb-1":"WZQy2SZuZso59bUsXXyl","pl-1":"o331apInxNunbYB3SfPE","p-2":"fMPIyD9Vqki1Lrc_yJnG","px-2":"i2pMcTcdrr10IQoiSm_L","py-2":"eA702gn32kwptiI1obXH","pt-2":"o9bGieUKcYc8o0Ij9oZX","pr-2":"SwZcFez1RDqWsOFjB5iG","pb-2":"eHpLc_idmuEqeqCTvqkN","pl-2":"vU39i2B4P1fUTMB2l6Vo","p-3":"JHWNzBnE29awhdu5BEh1","px-3":"X72lGbb56L3KFzC2xQ9N","py-3":"BzfNhRG8wXdCEB5ocQ6e","pt-3":"srV0KSDC83a2fiimSMMQ","pr-3":"lUWfkmbQjCskhcNwkyCm","pb-3":"Ts0dIlc3aTSL7V4cIHis","pl-3":"CzlqQXXhX6MvorArFZ8B","p-4":"TqMPkQtR_DdZuKb5vBoV","px-4":"a7UrjhI69Vetlcj9ZVzz","py-4":"StEhBzGs2Gi5dDEkjhAv","pt-4":"FGneZfZyvYrt1dG0zcnm","pr-4":"APEH216rpdlJWgD2fHc8","pb-4":"oGwXC3ohCic9XnAj6x69","pl-4":"U6gnT9y42ViPNOcNzBwb","p-5":"IpdRLBwnHqbqFrixgbYC","px-5":"HgNeXvkBa9o3bQ5fvFZm","py-5":"tJtFZM3XfPG9v9TSDfN1","pt-5":"PdifHW45QeXYfK568uD8","pr-5":"mbLkWTTZ0Za_BBbFZ5b2","pb-5":"vVWpZpLlWrkTt0hMk8XU","pl-5":"RxfaJj5a1Nt6IavEo5Zl","p-6":"SppJULDGdnOGcjZNCYBy","px-6":"palY2nLwdoyooPUm9Hhk","py-6":"WYw1JvZC0ppLdvSAPhr_","pt-6":"YEEJ9b90ueQaPfiU8aeN","pr-6":"QE0ssnsKvWJMqlhPbY5u","pb-6":"n8yA3jHlMRyLd5UIfoND","pl-6":"tXHmxYnHzbwtfxEaG51n","p-7":"kBTsPKkO_3g_tLkj77Um","px-7":"RyhrFx6Y1FGDrGAAyaxm","py-7":"CBwRpB0bDN3iEdQPPMJO","pt-7":"vQVSq6SvWKbOMu6r4H6b","pr-7":"oBy5__aEADMsH46mrgFX","pb-7":"KVEXoJqf1s92j0JMdNmN","pl-7":"ZMXGNrNaKW3k_3TLz0Fq","p-8":"tuiR9PhkHXhGyEgzRZRI","px-8":"U7454qyWkQNa2iaSJziu","py-8":"VLYIv2GVocjuN93e8HC8","pt-8":"X1rm9DQ1zLGLfogja5Gn","pr-8":"JS7G6kAuqJo5GIuF8S5t","pb-8":"Y8F9ga1TDCMbM1lj4gUz","pl-8":"AJuyNGrI63BOWql719H8"}},25196:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={global:"_fUXxnSp5pagKBp9gSN7"}},27251:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={card:"jhusgrsLY49cJaH3R8DR",column:"hja6ASdvgiTiDpu35hyP",offer:"kwjWvvgSYXYVt_vtgipm","terms-of-service":"_A4eGKpTFq5cLDF6C93A",sidebar:"SXZOE_EsVMplJjAVtjQv",background:"XLiCCgm0YLBwYL4TH5d2",illustration:"dinLNIrYem2NUdWjnsp0"}},9390:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={cards:"Ux30N4q2szel9cyj5fKQ",card:"YurU7LB5pUTdr7Qw59HT",label:"E5iSwLwknPShXCYDmFLY",spinner:"UYwW4SkhLNQIU26RAP5d",value:"xp4e8qBQ6yMXnZHCLbY4"}},71007:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={container:"Jm72EPiCksSeDWktxFnO",title:"CQX0CmmUSdV3If6j15Qk",actions:"EwRylEoMeQjRNauj5Xvm",action:"Isj9riIvtLB53feZUgA1",column:"o9Z0Pe2X9iM1pe62dDVA","connection-error-col":"l9WQedGtCkj94SJY93fr","bar-wrapper":"I5WDjmKxlsc6YSWC59ho"}},28520:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={column:"OHg6EyDRkE8E_clRoDdP","is-viewport-medium":"fixpv983gJsRKWFeiv4U","is-viewport-large":"yqRU_h9OmsT4CfCMfyip",title:"QHGs8pCnhGzgpkr3Aayd",number:"FjmzkjyPTZqT9vbGMYA7"}},54867:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={header:"iqOI22aUn8jsHT_ynl2G",logo:"jjkYD6i7KWH31V1Jcxyx"}},11476:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={notice:"zNXdzgqAFvg0VKki2y1k",spinner:"U1Wid7ecTQ2RlhWcDv2A"}},65067:()=>{"use strict"},32221:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={column:"qnoWjQx10q42eq2Q0iLs","viewport-gt-small":"VartjcEKVbDT6dvTyczG","viewport-gt-medium":"ESZn71j47MqxUXwvqdSY",title:"FNBO40s_MPeYpuUBwEZw",text:"CO4jWZzbR76SS7kznn9l",icon:"Mv1tCkJB2wkM2bWPRALQ"}},59333:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={link:"W_i5PvBD9eEFoP3_dhXW","is-viewport-medium":"qVFZTEqt4TzX14ClF3pb",icon:"TxM25RTZYO2qU78fdDWe"}},6600:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={text:"lcV7CGqglGAK5iB1ATiE",button:"j4UGQC83yOdC0cMVf5cQ"}},86949:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={text:"FwxYqPGiotzYfm7MqXxB",button:"midlKyW1rzJmvo4ve6oQ","connection-management":"eIWMVpVwirkIsoIlE7CN",cut:"BurimaXcjsCBV1qoLMkw",small:"M3MylzxcRVINTgxIPggO",learn:"VxprYNM_qBrY6za1yJl3"}},21904:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={text:"NciOCN697Pq2u2PZLRKL",button:"nZQlKs9kQ3erhtZcIoeE","notes-options-wrapper":"oyE3qBsrOuQXALjKsv4a"}},38745:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={column:"EQTNl7VnmXY3FE5Om5Er",notoggle:"Umz0yyUnjZ8j0p7T50t8",title:"hzF9d0gvBeBNbH4zYmFm",toggle:"s27cviAOakM_CCjxXVUK",beta:"G6iHpTPD7VA0dTpli45i"}},45027:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={text:"XEt8QD8_z8gqt4fFMB_S"}},85605:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={wrapper:"O5Go3eBOBqWlXy7fX0su","connection-name":"fgQ4OdnJ1svMwDnOKu2H","profile-link":"ArQoWnNoP4okGpEbxF6b",confirmDialog:"QsEfjoVyfc0gVACXTvUM","mark-shared-wrap":"eCH_xkiqbqL0UkJjU_1p","connection-panel":"J2Rz6Qb6mWPyJDF0yoqw",description:"kigMrUb5_HJk7XhSh7Um","connection-list":"fXYYdRkn3LXtN9YH3354","connection-list-item":"LaK9P7Nfi4ZBH0NhtbSs","connection-item":"tI3FtttoJPuF9AJzCAMv","connection-item-name":"msoMjBB_4Uq5qpRgobsH","learn-more":"RjMLptH6v03AX5ABWBp3","connection-name-wrapper":"z5Do97zRnujyW3Oj0pPJ"}},18973:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={confirmation:"udAWaza1u34TkmZZUPq4","header-text":"JsO4jFxFAKjG6Go62bZF",form:"SeoSfJ6eII9O0PiDkl5g","accounts-list":"M6POQFFbhswwolN6D9VN","account-input":"Sk_WQxS97yTokR_rM3Ci","account-label":"CRjatFVv3dQMjG9_Vmjj","submit-wrap":"re3HrL6GJa59uqDgx771","profile-pic":"jPB4ApiYyEswu6AKKJqk","account-info":"JhIphWt8e2X_ee0l7KEZ"}},73842:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={modal:"Mu_jAq5ZcYDM1kmBbxcs","connect-form":"foAsC5GlXOWEzACnxT_P",small:"_7PKKGx44v2H9dJbUltFw","manual-share":"IGOk1gMf5SKkfOKyF7Xc"}},93794:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={notice:"ptn8jt555LOvMXWaoLxs","notice--default":"IVzciZv5S5E8XgR5EsLp","notice--highlight":"OpbDsm3F65Z4K5Bz43cz","notice--warning":"dlrSwB0tyuf1TYcypDec","notice--error":"hkpqil8E11PObwu7TVU2",dismiss:"IArBTmNL2K4du5WPBUhg",actions:"T7E2eJJN4qjwPpv3mcjV"}},42475:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={services:"SbpyhN5jnda8H868J3rX","service-list-item":"ONeewrk3aNC1gYJebq7K","service-item":"RoTxOeGFqAwk3pXUZpvF","service-item-info":"AjlYaYB_xi9ciXXCqHIJ","service-basics":"kdYli5nS3b46T57Ih5TQ",heading:"ci8T5eNe1rggYILce596",badges:"W6mPdl5wGQlXfp1hYSK0",badge:"QbcyaAVqz3U_2xlIOZCw",title:"fXvUC1jZNUXR7RSrIylI",description:"d22_aeSA1etqpnckP_Mr",actions:"ddhaNmGMqOySRjR_TtqJ","learn-more":"nnSF_IK4vYviTlCOzPrg","service-panel":"u17AiTny2Jbh35JFAUYw","active-connection":"MyXskAb4aXwO7zitR97z","broken-connection-alert":"hMntEpO6BV5B11lyAitv","service-connection-list":"Y_4JpLfOnXVZKvEs179H","example-wrapper":"jdahJvXCrCw0hA5NP3D3",small:"LLeYGX7Owy6gmpNW88VV","service-connection":"EdjyE9tVLH6lWMoB9z3M","connection-details":"EQQHwkVXRn0caD6J7LqV","profile-pic":"vvlyiVm67wUWF9NhpMA4",disconnect:"zqX6xk8KCGK1_qbYKqY5","connect-form-wrapper":"U_Z9JvOsEQ6BtkH9P1IQ","connect-form":"fNeO_MZFp7cPkgtCdkAF","connect-button":"zK8sijAMFNs2m6WRAoi4","fields-wrapper":"UVvtbZph1hXruamo6ZXw",input:"bzrYZZHzlTKn71KsfFCP","fields-item":"rFJU1Nr26kL8C6HZax0G",error:"XIX7fVqeFgDdGkOv2_hr","mark-shared-wrap":"zLobzkXnF6jHxNvkfz5_","error-text":"IiaedU1hr9K0GZ5PeQBd"}},88375:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={footer:"WJ5nPw0EHj7eoNZB06TW"}},52002:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c={templates:"m9360_cMWKFHTNk9xpNz",template:"j4EvSJfKIJLGEIAETiEY","template--active":"dQb3Zpl0puk4V6ezZslo"}},44997:e=>{var t=1e3,n=60*t,c=60*n,a=24*c,i=7*a,s=365.25*a;function o(e,t,n,c){var a=t>=1.5*n;return Math.round(e/n)+" "+c+(a?"s":"")}e.exports=function(e,r){r=r||{};var l=typeof e;if("string"===l&&e.length>0)return function(e){if((e=String(e)).length>100)return;var o=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!o)return;var r=parseFloat(o[1]);switch((o[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return r*s;case"weeks":case"week":case"w":return r*i;case"days":case"day":case"d":return r*a;case"hours":case"hour":case"hrs":case"hr":case"h":return r*c;case"minutes":case"minute":case"mins":case"min":case"m":return r*n;case"seconds":case"second":case"secs":case"sec":case"s":return r*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}(e);if("number"===l&&isFinite(e))return r.long?function(e){var i=Math.abs(e);if(i>=a)return o(e,i,a,"day");if(i>=c)return o(e,i,c,"hour");if(i>=n)return o(e,i,n,"minute");if(i>=t)return o(e,i,t,"second");return e+" ms"}(e):function(e){var i=Math.abs(e);if(i>=a)return Math.round(e/a)+"d";if(i>=c)return Math.round(e/c)+"h";if(i>=n)return Math.round(e/n)+"m";if(i>=t)return Math.round(e/t)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},15762:(e,t,n)=>{"use strict";var c=n(53761);function a(){}function i(){}i.resetWarningCache=a,e.exports=function(){function e(e,t,n,a,i,s){if(s!==c){var o=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:a};return n.PropTypes=n,n}},28120:(e,t,n)=>{e.exports=n(15762)()},53761:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},90372:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var c=n(46941);const a=n.n(c)()("dops:analytics");let i,s;window._tkq=window._tkq||[],window.ga=window.ga||function(){(window.ga.q=window.ga.q||[]).push(arguments)},window.ga.l=+new Date;const o={initialize:function(e,t,n){o.setUser(e,t),o.setSuperProps(n),o.identifyUser()},setGoogleAnalyticsEnabled:function(e,t=null){this.googleAnalyticsEnabled=e,this.googleAnalyticsKey=t},setMcAnalyticsEnabled:function(e){this.mcAnalyticsEnabled=e},setUser:function(e,t){s={ID:e,username:t}},setSuperProps:function(e){i=e},assignSuperProps:function(e){i=Object.assign(i||{},e)},mc:{bumpStat:function(e,t){const n=function(e,t){let n="";if("object"==typeof e){for(const t in e)n+="&x_"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);a("Bumping stats %o",e)}else n="&x_"+encodeURIComponent(e)+"="+encodeURIComponent(t),a('Bumping stat "%s" in group "%s"',t,e);return n}(e,t);o.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom-no-pv"+n+"&t="+Math.random())},bumpStatWithPageView:function(e,t){const n=function(e,t){let n="";if("object"==typeof e){for(const t in e)n+="&"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);a("Built stats %o",e)}else n="&"+encodeURIComponent(e)+"="+encodeURIComponent(t),a('Built stat "%s" in group "%s"',t,e);return n}(e,t);o.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom"+n+"&t="+Math.random())}},pageView:{record:function(e,t){o.tracks.recordPageView(e),o.ga.recordPageView(e,t)}},purchase:{record:function(e,t,n,c,a,i,s){o.ga.recordPurchase(e,t,n,c,a,i,s)}},tracks:{recordEvent:function(e,t){t=t||{},0===e.indexOf("akismet_")||0===e.indexOf("jetpack_")?(i&&(a("- Super Props: %o",i),t=Object.assign(t,i)),a('Record event "%s" called with props %s',e,JSON.stringify(t)),window._tkq.push(["recordEvent",e,t])):a('- Event name must be prefixed by "akismet_" or "jetpack_"')},recordJetpackClick:function(e){const t="object"==typeof e?e:{target:e};o.tracks.recordEvent("jetpack_wpa_click",t)},recordPageView:function(e){o.tracks.recordEvent("akismet_page_view",{path:e})},setOptOut:function(e){a("Pushing setOptOut: %o",e),window._tkq.push(["setOptOut",e])}},ga:{initialized:!1,initialize:function(){let e={};o.ga.initialized||(s&&(e={userId:"u-"+s.ID}),window.ga("create",this.googleAnalyticsKey,"auto",e),o.ga.initialized=!0)},recordPageView:function(e,t){o.ga.initialize(),a("Recording Page View ~ [URL: "+e+"] [Title: "+t+"]"),this.googleAnalyticsEnabled&&(window.ga("set","page",e),window.ga("send",{hitType:"pageview",page:e,title:t}))},recordEvent:function(e,t,n,c){o.ga.initialize();let i="Recording Event ~ [Category: "+e+"] [Action: "+t+"]";void 0!==n&&(i+=" [Option Label: "+n+"]"),void 0!==c&&(i+=" [Option Value: "+c+"]"),a(i),this.googleAnalyticsEnabled&&window.ga("send","event",e,t,n,c)},recordPurchase:function(e,t,n,c,a,i,s){window.ga("require","ecommerce"),window.ga("ecommerce:addTransaction",{id:e,revenue:c,currency:s}),window.ga("ecommerce:addItem",{id:e,name:t,sku:n,price:a,quantity:i}),window.ga("ecommerce:send")}},identifyUser:function(){s&&window._tkq.push(["identifyUser",s.ID,s.username])},setProperties:function(e){window._tkq.push(["setProperties",e])},clearedIdentity:function(){window._tkq.push(["clearIdentity"])}},r=o},5932:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>m});var c=n(56439),a=n(93832);function i(e){class t extends Error{constructor(...t){super(...t),this.name=e}}return t}const s=i("JsonParseError"),o=i("JsonParseAfterRedirectError"),r=i("Api404Error"),l=i("Api404AfterRedirectError"),p=i("FetchNetworkError");const m=new function(e,t){let n=e,i=e,s={"X-WP-Nonce":t},o={credentials:"same-origin",headers:s},r={method:"post",credentials:"same-origin",headers:Object.assign({},s,{"Content-type":"application/json"})},l=function(e){const t=e.split("?"),n=t.length>1?t[1]:"",c=n.length?n.split("&"):[];return c.push("_cacheBuster="+(new Date).getTime()),t[0]+"?"+c.join("&")};const p={setApiRoot(e){n=e},setWpcomOriginApiUrl(e){i=e},setApiNonce(e){s={"X-WP-Nonce":e},o={credentials:"same-origin",headers:s},r={method:"post",credentials:"same-origin",headers:Object.assign({},s,{"Content-type":"application/json"})}},setCacheBusterCallback:e=>{l=e},registerSite:(e,t,a)=>{const i={};return(0,c.jetpackConfigHas)("consumer_slug")&&(i.plugin_slug=(0,c.jetpackConfigGet)("consumer_slug")),null!==t&&(i.redirect_uri=t),a&&(i.from=a),g(`${n}jetpack/v4/connection/register`,r,{body:JSON.stringify(i)}).then(u).then(d)},fetchAuthorizationUrl:e=>m((0,a.addQueryArgs)(`${n}jetpack/v4/connection/authorize_url`,{no_iframe:"1",redirect_uri:e}),o).then(u).then(d),fetchSiteConnectionData:()=>m(`${n}jetpack/v4/connection/data`,o).then(d),fetchSiteConnectionStatus:()=>m(`${n}jetpack/v4/connection`,o).then(d),fetchSiteConnectionTest:()=>m(`${n}jetpack/v4/connection/test`,o).then(u).then(d),fetchUserConnectionData:()=>m(`${n}jetpack/v4/connection/data`,o).then(d),fetchUserTrackingSettings:()=>m(`${n}jetpack/v4/tracking/settings`,o).then(u).then(d),updateUserTrackingSettings:e=>g(`${n}jetpack/v4/tracking/settings`,r,{body:JSON.stringify(e)}).then(u).then(d),disconnectSite:()=>g(`${n}jetpack/v4/connection`,r,{body:JSON.stringify({isActive:!1})}).then(u).then(d),fetchConnectUrl:()=>m(`${n}jetpack/v4/connection/url`,o).then(u).then(d),unlinkUser:(e=!1,t={})=>{const c={linked:!1,force:!!e};return t.disconnectAllUsers&&(c["disconnect-all-users"]=!0),g(`${n}jetpack/v4/connection/user`,r,{body:JSON.stringify(c)}).then(u).then(d)},reconnect:()=>g(`${n}jetpack/v4/connection/reconnect`,r).then(u).then(d),fetchConnectedPlugins:()=>m(`${n}jetpack/v4/connection/plugins`,o).then(u).then(d),setHasSeenWCConnectionModal:()=>g(`${n}jetpack/v4/seen-wc-connection-modal`,r).then(u).then(d),fetchModules:()=>m(`${n}jetpack/v4/module/all`,o).then(u).then(d),fetchModule:e=>m(`${n}jetpack/v4/module/${e}`,o).then(u).then(d),activateModule:e=>g(`${n}jetpack/v4/module/${e}/active`,r,{body:JSON.stringify({active:!0})}).then(u).then(d),deactivateModule:e=>g(`${n}jetpack/v4/module/${e}/active`,r,{body:JSON.stringify({active:!1})}),updateModuleOptions:(e,t)=>g(`${n}jetpack/v4/module/${e}`,r,{body:JSON.stringify(t)}).then(u).then(d),updateSettings:e=>g(`${n}jetpack/v4/settings`,r,{body:JSON.stringify(e)}).then(u).then(d),getProtectCount:()=>m(`${n}jetpack/v4/module/protect/data`,o).then(u).then(d),resetOptions:e=>g(`${n}jetpack/v4/options/${e}`,r,{body:JSON.stringify({reset:!0})}).then(u).then(d),activateVaultPress:()=>g(`${n}jetpack/v4/plugins`,r,{body:JSON.stringify({slug:"vaultpress",status:"active"})}).then(u).then(d),getVaultPressData:()=>m(`${n}jetpack/v4/module/vaultpress/data`,o).then(u).then(d),installPlugin:(e,t)=>{const c={slug:e,status:"active"};return t&&(c.source=t),g(`${n}jetpack/v4/plugins`,r,{body:JSON.stringify(c)}).then(u).then(d)},activateAkismet:()=>g(`${n}jetpack/v4/plugins`,r,{body:JSON.stringify({slug:"akismet",status:"active"})}).then(u).then(d),getAkismetData:()=>m(`${n}jetpack/v4/module/akismet/data`,o).then(u).then(d),checkAkismetKey:()=>m(`${n}jetpack/v4/module/akismet/key/check`,o).then(u).then(d),checkAkismetKeyTyped:e=>g(`${n}jetpack/v4/module/akismet/key/check`,r,{body:JSON.stringify({api_key:e})}).then(u).then(d),getFeatureTypeStatus:e=>m(`${n}jetpack/v4/feature/${e}`,o).then(u).then(d),fetchStatsData:e=>m(function(e){let t=`${n}jetpack/v4/module/stats/data`;-1!==t.indexOf("?")?t+=`&range=${encodeURIComponent(e)}`:t+=`?range=${encodeURIComponent(e)}`;return t}(e),o).then(u).then(d).then(v),getPluginUpdates:()=>m(`${n}jetpack/v4/updates/plugins`,o).then(u).then(d),getPlans:()=>m(`${n}jetpack/v4/plans`,o).then(u).then(d),fetchSettings:()=>m(`${n}jetpack/v4/settings`,o).then(u).then(d),updateSetting:e=>g(`${n}jetpack/v4/settings`,r,{body:JSON.stringify(e)}).then(u).then(d),fetchSiteData:()=>m(`${n}jetpack/v4/site`,o).then(u).then(d).then((e=>JSON.parse(e.data))),fetchSiteFeatures:()=>m(`${n}jetpack/v4/site/features`,o).then(u).then(d).then((e=>JSON.parse(e.data))),fetchSiteProducts:()=>m(`${n}jetpack/v4/site/products`,o).then(u).then(d),fetchSitePurchases:()=>m(`${n}jetpack/v4/site/purchases`,o).then(u).then(d).then((e=>JSON.parse(e.data))),fetchSiteBenefits:()=>m(`${n}jetpack/v4/site/benefits`,o).then(u).then(d).then((e=>JSON.parse(e.data))),fetchSiteDiscount:()=>m(`${n}jetpack/v4/site/discount`,o).then(u).then(d).then((e=>e.data)),fetchSetupQuestionnaire:()=>m(`${n}jetpack/v4/setup/questionnaire`,o).then(u).then(d),fetchRecommendationsData:()=>m(`${n}jetpack/v4/recommendations/data`,o).then(u).then(d),fetchRecommendationsProductSuggestions:()=>m(`${n}jetpack/v4/recommendations/product-suggestions`,o).then(u).then(d),fetchRecommendationsUpsell:()=>m(`${n}jetpack/v4/recommendations/upsell`,o).then(u).then(d),fetchRecommendationsConditional:()=>m(`${n}jetpack/v4/recommendations/conditional`,o).then(u).then(d),saveRecommendationsData:e=>g(`${n}jetpack/v4/recommendations/data`,r,{body:JSON.stringify({data:e})}).then(u),fetchProducts:()=>m(`${n}jetpack/v4/products`,o).then(u).then(d),fetchRewindStatus:()=>m(`${n}jetpack/v4/rewind`,o).then(u).then(d).then((e=>JSON.parse(e.data))),fetchScanStatus:()=>m(`${n}jetpack/v4/scan`,o).then(u).then(d).then((e=>JSON.parse(e.data))),dismissJetpackNotice:e=>g(`${n}jetpack/v4/notice/${e}`,r,{body:JSON.stringify({dismissed:!0})}).then(u).then(d),fetchPluginsData:()=>m(`${n}jetpack/v4/plugins`,o).then(u).then(d),fetchIntroOffers:()=>m(`${n}jetpack/v4/intro-offers`,o).then(u).then(d),fetchVerifySiteGoogleStatus:e=>m(null!==e?`${n}jetpack/v4/verify-site/google/${e}`:`${n}jetpack/v4/verify-site/google`,o).then(u).then(d),verifySiteGoogle:e=>g(`${n}jetpack/v4/verify-site/google`,r,{body:JSON.stringify({keyring_id:e})}).then(u).then(d),submitSurvey:e=>g(`${n}jetpack/v4/marketing/survey`,r,{body:JSON.stringify(e)}).then(u).then(d),saveSetupQuestionnaire:e=>g(`${n}jetpack/v4/setup/questionnaire`,r,{body:JSON.stringify(e)}).then(u).then(d),updateLicensingError:e=>g(`${n}jetpack/v4/licensing/error`,r,{body:JSON.stringify(e)}).then(u).then(d),updateLicenseKey:e=>g(`${n}jetpack/v4/licensing/set-license`,r,{body:JSON.stringify({license:e})}).then(u).then(d),getUserLicensesCounts:()=>m(`${n}jetpack/v4/licensing/user/counts`,o).then(u).then(d),getUserLicenses:()=>m(`${n}jetpack/v4/licensing/user/licenses`,o).then(u).then(d),updateLicensingActivationNoticeDismiss:e=>g(`${n}jetpack/v4/licensing/user/activation-notice-dismiss`,r,{body:JSON.stringify({last_detached_count:e})}).then(u).then(d),updateRecommendationsStep:e=>g(`${n}jetpack/v4/recommendations/step`,r,{body:JSON.stringify({step:e})}).then(u),confirmIDCSafeMode:()=>g(`${n}jetpack/v4/identity-crisis/confirm-safe-mode`,r).then(u),startIDCFresh:e=>g(`${n}jetpack/v4/identity-crisis/start-fresh`,r,{body:JSON.stringify({redirect_uri:e})}).then(u).then(d),migrateIDC:()=>g(`${n}jetpack/v4/identity-crisis/migrate`,r).then(u),attachLicenses:e=>g(`${n}jetpack/v4/licensing/attach-licenses`,r,{body:JSON.stringify({licenses:e})}).then(u).then(d),fetchSearchPlanInfo:()=>m(`${i}jetpack/v4/search/plan`,o).then(u).then(d),fetchSearchSettings:()=>m(`${i}jetpack/v4/search/settings`,o).then(u).then(d),updateSearchSettings:e=>g(`${i}jetpack/v4/search/settings`,r,{body:JSON.stringify(e)}).then(u).then(d),fetchSearchStats:()=>m(`${i}jetpack/v4/search/stats`,o).then(u).then(d),fetchWafSettings:()=>m(`${n}jetpack/v4/waf`,o).then(u).then(d),updateWafSettings:e=>g(`${n}jetpack/v4/waf`,r,{body:JSON.stringify(e)}).then(u).then(d),fetchWordAdsSettings:()=>m(`${n}jetpack/v4/wordads/settings`,o).then(u).then(d),updateWordAdsSettings:e=>g(`${n}jetpack/v4/wordads/settings`,r,{body:JSON.stringify(e)}),fetchSearchPricing:()=>m(`${i}jetpack/v4/search/pricing`,o).then(u).then(d),fetchMigrationStatus:()=>m(`${n}jetpack/v4/migration/status`,o).then(u).then(d),fetchBackupUndoEvent:()=>m(`${n}jetpack/v4/site/backup/undo-event`,o).then(u).then(d),fetchBackupPreflightStatus:()=>m(`${n}jetpack/v4/site/backup/preflight`,o).then(u).then(d)};function m(e,t){return fetch(l(e),t)}function g(e,t,n){return fetch(e,Object.assign({},t,n)).catch(h)}function v(e){return e.general&&void 0===e.general.response||e.week&&void 0===e.week.response||e.month&&void 0===e.month.response?e:{}}Object.assign(this,p)};function u(e){return e.status>=200&&e.status<300?e:404===e.status?new Promise((()=>{throw e.redirected?new l(e.redirected):new r})):e.json().catch((e=>g(e))).then((t=>{const n=new Error(`${t.message} (Status ${e.status})`);throw n.response=t,n.name="ApiError",n}))}function d(e){return e.json().catch((t=>g(t,e.redirected,e.url)))}function g(e,t,n){throw t?new o(n):new s}function h(){throw new p}},42947:(e,t,n)=>{"use strict";n.d(t,{A:()=>u});var c=n(5932),a=n(27723),i=n(13022),s=n(51609),o=n(38250),r=n(67142),l=n(28509),p=n(75918),m=n(71631);const __=a.__,u=({children:e,moduleName:t=__("Jetpack","jetpack-publicize-pkg"),moduleNameHref:n,showHeader:u=!0,showFooter:d=!0,useInternalLinks:g=!1,showBackground:h=!0,sandboxedDomain:v="",apiRoot:f="",apiNonce:b="",optionalMenuItems:k,header:y})=>{(0,s.useEffect)((()=>{c.Ay.setApiRoot(f),c.Ay.setApiNonce(b)}),[f,b]);const E=(0,i.A)(m.A["admin-page"],{[m.A.background]:h}),w=(0,s.useCallback)((async()=>{try{const e=await c.Ay.fetchSiteConnectionTest();window.alert(e.message)}catch(e){window.alert((0,a.sprintf)(/* translators: placeholder is an error message. */
__("There was an error testing Jetpack. Error: %s","jetpack-publicize-pkg"),e.message))}}),[]);return React.createElement("div",{className:E},u&&React.createElement(p.A,{horizontalSpacing:5},React.createElement(l.A,{className:(0,i.A)(m.A["admin-page-header"],"jp-admin-page-header")},y||React.createElement(r.A,null),v&&React.createElement("code",{className:m.A["sandbox-domain-badge"],onClick:w,onKeyDown:w,role:"button",tabIndex:0,title:`Sandboxing via ${v}. Click to test connection.`},"API Sandboxed"))),React.createElement(p.A,{fluid:!0,horizontalSpacing:0},React.createElement(l.A,null,e)),d&&React.createElement(p.A,{horizontalSpacing:5},React.createElement(l.A,null,React.createElement(o.A,{moduleName:t,moduleNameHref:n,menu:k,useInternalLinks:g}))))}},95640:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var c=n(51609),a=n.n(c),i=n(43496);const s=({children:e})=>a().createElement("div",{className:i.A.section},e)},90766:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var c=n(51609),a=n.n(c),i=n(91842);const s=({children:e})=>a().createElement("div",{className:i.A["section-hero"]},e)},42266:(e,t,n)=>{"use strict";n.d(t,{A:()=>u});var c=n(23751),a=n(19783),i=n(83883),s=n(51113),o=n(13022),r=n(51609),l=n.n(r),p=n(65486);const m=e=>{switch(e){case"error":case"warning":default:return c.A;case"info":return a.A;case"success":return i.A}},u=({level:e="warning",children:t,showIcon:n=!0,className:c})=>{const a=(0,o.A)(p.A.container,p.A[`is-${e}`],c);return l().createElement("div",{className:a},n&&l().createElement("div",{className:p.A["icon-wrapper"]},l().createElement(s.A,{icon:m(e),className:p.A.icon})),l().createElement("div",null,t))}},48907:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var c=n(96072),a=n.n(c),i=n(27723),s=n(13022),o=n(51609),r=n.n(o);const __=i.__,l=({title:e=__("An Automattic Airline","jetpack-publicize-pkg"),height:t=7,className:n,...c})=>r().createElement("svg",a()({role:"img",x:"0",y:"0",viewBox:"0 0 935 38.2",enableBackground:"new 0 0 935 38.2","aria-labelledby":"jp-automattic-byline-logo-title",height:t,className:(0,s.A)("jp-automattic-byline-logo",n)},c),r().createElement("desc",{id:"jp-automattic-byline-logo-title"},e),r().createElement("path",{d:"M317.1 38.2c-12.6 0-20.7-9.1-20.7-18.5v-1.2c0-9.6 8.2-18.5 20.7-18.5 12.6 0 20.8 8.9 20.8 18.5v1.2C337.9 29.1 329.7 38.2 317.1 38.2zM331.2 18.6c0-6.9-5-13-14.1-13s-14 6.1-14 13v0.9c0 6.9 5 13.1 14 13.1s14.1-6.2 14.1-13.1V18.6zM175 36.8l-4.7-8.8h-20.9l-4.5 8.8h-7L157 1.3h5.5L182 36.8H175zM159.7 8.2L152 23.1h15.7L159.7 8.2zM212.4 38.2c-12.7 0-18.7-6.9-18.7-16.2V1.3h6.6v20.9c0 6.6 4.3 10.5 12.5 10.5 8.4 0 11.9-3.9 11.9-10.5V1.3h6.7V22C231.4 30.8 225.8 38.2 212.4 38.2zM268.6 6.8v30h-6.7v-30h-15.5V1.3h37.7v5.5H268.6zM397.3 36.8V8.7l-1.8 3.1 -14.9 25h-3.3l-14.7-25 -1.8-3.1v28.1h-6.5V1.3h9.2l14 24.4 1.7 3 1.7-3 13.9-24.4h9.1v35.5H397.3zM454.4 36.8l-4.7-8.8h-20.9l-4.5 8.8h-7l19.2-35.5h5.5l19.5 35.5H454.4zM439.1 8.2l-7.7 14.9h15.7L439.1 8.2zM488.4 6.8v30h-6.7v-30h-15.5V1.3h37.7v5.5H488.4zM537.3 6.8v30h-6.7v-30h-15.5V1.3h37.7v5.5H537.3zM569.3 36.8V4.6c2.7 0 3.7-1.4 3.7-3.4h2.8v35.5L569.3 36.8 569.3 36.8zM628 11.3c-3.2-2.9-7.9-5.7-14.2-5.7 -9.5 0-14.8 6.5-14.8 13.3v0.7c0 6.7 5.4 13 15.3 13 5.9 0 10.8-2.8 13.9-5.7l4 4.2c-3.9 3.8-10.5 7.1-18.3 7.1 -13.4 0-21.6-8.7-21.6-18.3v-1.2c0-9.6 8.9-18.7 21.9-18.7 7.5 0 14.3 3.1 18 7.1L628 11.3zM321.5 12.4c1.2 0.8 1.5 2.4 0.8 3.6l-6.1 9.4c-0.8 1.2-2.4 1.6-3.6 0.8l0 0c-1.2-0.8-1.5-2.4-0.8-3.6l6.1-9.4C318.7 11.9 320.3 11.6 321.5 12.4L321.5 12.4z"}),r().createElement("path",{d:"M37.5 36.7l-4.7-8.9H11.7l-4.6 8.9H0L19.4 0.8H25l19.7 35.9H37.5zM22 7.8l-7.8 15.1h15.9L22 7.8zM82.8 36.7l-23.3-24 -2.3-2.5v26.6h-6.7v-36H57l22.6 24 2.3 2.6V0.8h6.7v35.9H82.8z"}),r().createElement("path",{d:"M719.9 37l-4.8-8.9H694l-4.6 8.9h-7.1l19.5-36h5.6l19.8 36H719.9zM704.4 8l-7.8 15.1h15.9L704.4 8zM733 37V1h6.8v36H733zM781 37c-1.8 0-2.6-2.5-2.9-5.8l-0.2-3.7c-0.2-3.6-1.7-5.1-8.4-5.1h-12.8V37H750V1h19.6c10.8 0 15.7 4.3 15.7 9.9 0 3.9-2 7.7-9 9 7 0.5 8.5 3.7 8.6 7.9l0.1 3c0.1 2.5 0.5 4.3 2.2 6.1V37H781zM778.5 11.8c0-2.6-2.1-5.1-7.9-5.1h-13.8v10.8h14.4c5 0 7.3-2.4 7.3-5.2V11.8zM794.8 37V1h6.8v30.4h28.2V37H794.8zM836.7 37V1h6.8v36H836.7zM886.2 37l-23.4-24.1 -2.3-2.5V37h-6.8V1h6.5l22.7 24.1 2.3 2.6V1h6.8v36H886.2zM902.3 37V1H935v5.6h-26v9.2h20v5.5h-20v10.1h26V37H902.3z"}))},51112:(e,t,n)=>{"use strict";n.d(t,{A:()=>g});var c=n(96072),a=n.n(c),i=n(56427),s=n(27723),o=n(51113),r=n(53512),l=n(13022),p=n(51609),m=n.n(p),u=n(77560);const __=s.__,d=(0,p.forwardRef)(((e,t)=>{const{children:n,variant:c="primary",size:s="normal",weight:p="bold",icon:d,iconSize:g,disabled:h,isDestructive:v,isLoading:f,isExternalLink:b,className:k,text:y,fullWidth:E,...w}=e,R=(0,l.A)(u.A.button,k,{[u.A.normal]:"normal"===s,[u.A.small]:"small"===s,[u.A.icon]:Boolean(d),[u.A.loading]:f,[u.A.regular]:"regular"===p,[u.A["full-width"]]:E,[u.A["is-icon-button"]]:Boolean(d)&&!n});w.ref=t;const A="normal"===s?20:16,C=b&&m().createElement(m().Fragment,null,m().createElement(o.A,{size:A,icon:r.A,className:u.A["external-icon"]}),m().createElement(i.VisuallyHidden,{as:"span"},/* translators: accessibility text */
__("(opens in a new tab)","jetpack-publicize-pkg"))),z=b?"_blank":void 0,S=n?.[0]&&null!==n[0]&&"components-tooltip"!==n?.[0]?.props?.className;return m().createElement(i.Button,a()({target:z,variant:c,className:(0,l.A)(R,{"has-text":!!d&&S}),icon:b?void 0:d,iconSize:g,disabled:h,"aria-disabled":h,isDestructive:v,text:y},w),f&&m().createElement(i.Spinner,null),m().createElement("span",null,n),C)}));d.displayName="Button";const g=d},94437:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var c=n(51113),a=n(71797),i=n(13022),s=n(40597),o=n(47425),r=n(29245);const l=({description:e,cta:t,onClick:n,href:l,openInNewTab:p=!1,className:m,tooltipText:u=""})=>{const d=void 0!==l?"a":"button",g="a"===d?{href:l,...p&&{target:"_blank"}}:{onClick:n};return React.createElement("div",{className:(0,i.A)(r.A.cut,m)},React.createElement("div",null,React.createElement("div",null,React.createElement(o.Ay,{className:r.A.description},e),u&&React.createElement(s.A,{className:r.A.iconContainer,iconSize:16,offset:4},React.createElement(o.Ay,{variant:"body-small"},u))),React.createElement("div",null,React.createElement(d,g,React.createElement(o.Ay,{className:r.A.cta},t)))),React.createElement(c.A,{icon:a.A,className:r.A.icon,size:30}))}},7064:(e,t,n)=>{"use strict";n.d(t,{A:()=>u});var c=n(96072),a=n.n(c),i=n(13022),s=n(51609),o=n.n(s),r=n(28509),l=n(75918),p=n(60442),m=n(57448);const u=({primary:e,secondary:t,isTwoSections:n=!1,...c})=>{const[s,u]=(0,p.A)(["sm","lg"],[null,"<"]),d=!n&&s,g=(0,i.A)({[m.A["one-section-style"]]:!n,[m.A["is-viewport-small"]]:s});return o().createElement(l.A,a()({className:g,horizontalSpacing:0,horizontalGap:0,fluid:!1},c),!d&&o().createElement(o().Fragment,null,o().createElement(r.A,{sm:4,md:u?4:5,lg:7,className:m.A.primary},e),o().createElement(r.A,{sm:4,md:u?4:3,lg:5,className:m.A.secondary},t)),d&&o().createElement(r.A,null,e))}},59244:(e,t,n)=>{"use strict";n.d(t,{D:()=>s});var c=n(56427),a=n(78888),i=n(63406);function s({maxVisibleNotices:e=3}){const{getNotices:t,removeNotice:n}=(0,i.I)(),s=t().filter((({type:e})=>"snackbar"===e)).slice(-e);return React.createElement(c.SnackbarList,{notices:s,className:a.A["global-notices"],onRemove:n})}},63406:(e,t,n)=>{"use strict";n.d(t,{I:()=>i});var c=n(47143),a=n(692);function i(){const e=(0,c.useDispatch)(a.store),t=(0,c.useSelect)((e=>e(a.store).getNotices()),[]);return{...e,createNotice:(t,n,c)=>e.createNotice(t,n,{type:"snackbar",...c}),createErrorNotice:(t,n)=>e.createErrorNotice(t,{type:"snackbar",...n}),createInfoNotice:(t,n)=>e.createInfoNotice(t,{type:"snackbar",...n}),createSuccessNotice:(t,n)=>e.createSuccessNotice(t,{type:"snackbar",...n}),createWarningNotice:(t,n)=>e.createWarningNotice(t,{type:"snackbar",...n}),getNotices:()=>t}}},11883:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var c=n(27723),a=n(13022),i=n(51609);n(4459);const __=c.__;class s extends i.Component{static defaultProps={"aria-hidden":"false",focusable:"true"};needsOffset(e,t){return["gridicons-arrow-left","gridicons-arrow-right","gridicons-calendar","gridicons-cart","gridicons-folder","gridicons-help-outline","gridicons-info","gridicons-info-outline","gridicons-posts","gridicons-star-outline","gridicons-star"].indexOf(e)>=0&&t%18==0}getSVGDescription(e){if("description"in this.props)return this.props.description;switch(e){default:return"";case"gridicons-audio":return __("Has audio.","jetpack-publicize-pkg");case"gridicons-arrow-left":return __("Arrow left","jetpack-publicize-pkg");case"gridicons-arrow-right":return __("Arrow right","jetpack-publicize-pkg");case"gridicons-calendar":return __("Is an event.","jetpack-publicize-pkg");case"gridicons-cart":return __("Is a product.","jetpack-publicize-pkg");case"chevron-down":return __("Show filters","jetpack-publicize-pkg");case"gridicons-comment":return __("Matching comment.","jetpack-publicize-pkg");case"gridicons-cross":return __("Close.","jetpack-publicize-pkg");case"gridicons-filter":return __("Toggle search filters.","jetpack-publicize-pkg");case"gridicons-folder":return __("Category","jetpack-publicize-pkg");case"gridicons-help-outline":return __("Help","jetpack-publicize-pkg");case"gridicons-info":case"gridicons-info-outline":return __("Information.","jetpack-publicize-pkg");case"gridicons-image-multiple":return __("Has multiple images.","jetpack-publicize-pkg");case"gridicons-image":return __("Has an image.","jetpack-publicize-pkg");case"gridicons-page":return __("Page","jetpack-publicize-pkg");case"gridicons-post":return __("Post","jetpack-publicize-pkg");case"gridicons-jetpack-search":case"gridicons-search":return __("Magnifying Glass","jetpack-publicize-pkg");case"gridicons-tag":return __("Tag","jetpack-publicize-pkg");case"gridicons-video":return __("Has a video.","jetpack-publicize-pkg")}}renderIcon(e){switch(e){default:return null;case"gridicons-audio":return React.createElement("g",null,React.createElement("path",{d:"M8 4v10.184C7.686 14.072 7.353 14 7 14c-1.657 0-3 1.343-3 3s1.343 3 3 3 3-1.343 3-3V7h7v4.184c-.314-.112-.647-.184-1-.184-1.657 0-3 1.343-3 3s1.343 3 3 3 3-1.343 3-3V4H8z"}));case"gridicons-arrow-left":return React.createElement("g",null,React.createElement("path",{d:"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"}));case"gridicons-arrow-right":return React.createElement("g",null,React.createElement("path",{d:"M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8-8-8z"}));case"gridicons-block":return React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zM4 12c0-4.418 3.582-8 8-8 1.848 0 3.545.633 4.9 1.686L5.686 16.9C4.633 15.545 4 13.848 4 12zm8 8c-1.848 0-3.546-.633-4.9-1.686L18.314 7.1C19.367 8.455 20 10.152 20 12c0 4.418-3.582 8-8 8z"}));case"gridicons-calendar":return React.createElement("g",null,React.createElement("path",{d:"M19 4h-1V2h-2v2H8V2H6v2H5c-1.105 0-2 .896-2 2v13c0 1.104.895 2 2 2h14c1.104 0 2-.896 2-2V6c0-1.104-.896-2-2-2zm0 15H5V8h14v11z"}));case"gridicons-cart":return React.createElement("g",null,React.createElement("path",{d:"M9 20c0 1.1-.9 2-2 2s-1.99-.9-1.99-2S5.9 18 7 18s2 .9 2 2zm8-2c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2zm.396-5c.937 0 1.75-.65 1.952-1.566L21 5H7V4c0-1.105-.895-2-2-2H3v2h2v11c0 1.105.895 2 2 2h12c0-1.105-.895-2-2-2H7v-2h10.396z"}));case"gridicons-checkmark":return React.createElement("g",null,React.createElement("path",{d:"M11 17.768l-4.884-4.884 1.768-1.768L11 14.232l8.658-8.658C17.823 3.39 15.075 2 12 2 6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10c0-1.528-.353-2.97-.966-4.266L11 17.768z"}));case"gridicons-chevron-left":return React.createElement("g",null,React.createElement("path",{d:"M16.443 7.41L15.0399 6L9.06934 12L15.0399 18L16.443 16.59L11.8855 12L16.443 7.41Z"}));case"gridicons-chevron-right":return React.createElement("g",null,React.createElement("path",{d:"M10.2366 6L8.8335 7.41L13.391 12L8.8335 16.59L10.2366 18L16.2072 12L10.2366 6Z"}));case"gridicons-chevron-down":return React.createElement("g",null,React.createElement("path",{d:"M20 9l-8 8-8-8 1.414-1.414L12 14.172l6.586-6.586"}));case"gridicons-comment":return React.createElement("g",null,React.createElement("path",{d:"M3 6v9c0 1.105.895 2 2 2h9v5l5.325-3.804c1.05-.75 1.675-1.963 1.675-3.254V6c0-1.105-.895-2-2-2H5c-1.105 0-2 .895-2 2z"}));case"gridicons-computer":return React.createElement("g",null,React.createElement("path",{d:"M20 2H4c-1.104 0-2 .896-2 2v12c0 1.104.896 2 2 2h6v2H7v2h10v-2h-3v-2h6c1.104 0 2-.896 2-2V4c0-1.104-.896-2-2-2zm0 14H4V4h16v12z"}));case"gridicons-cross":return React.createElement("g",null,React.createElement("path",{d:"M18.36 19.78L12 13.41l-6.36 6.37-1.42-1.42L10.59 12 4.22 5.64l1.42-1.42L12 10.59l6.36-6.36 1.41 1.41L13.41 12l6.36 6.36z"}));case"gridicons-filter":return React.createElement("g",null,React.createElement("path",{d:"M10 19h4v-2h-4v2zm-4-6h12v-2H6v2zM3 5v2h18V5H3z"}));case"gridicons-folder":return React.createElement("g",null,React.createElement("path",{d:"M18 19H6c-1.1 0-2-.9-2-2V7c0-1.1.9-2 2-2h3c1.1 0 2 .9 2 2h7c1.1 0 2 .9 2 2v8c0 1.1-.9 2-2 2z"}));case"gridicons-help-outline":return React.createElement("g",null,React.createElement("path",{d:"M12 4c4.41 0 8 3.59 8 8s-3.59 8-8 8-8-3.59-8-8 3.59-8 8-8m0-2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 13h-2v2h2v-2zm-1.962-2v-.528c0-.4.082-.74.246-1.017.163-.276.454-.546.87-.808.333-.21.572-.397.717-.565.146-.168.22-.36.22-.577 0-.172-.078-.308-.234-.41-.156-.1-.358-.15-.608-.15-.62 0-1.34.22-2.168.658l-.854-1.67c1.02-.58 2.084-.872 3.194-.872.913 0 1.63.202 2.15.603.52.4.78.948.78 1.64 0 .495-.116.924-.347 1.287-.23.362-.6.705-1.11 1.03-.43.278-.7.48-.807.61-.108.13-.163.282-.163.458V13h-1.885z"}));case"gridicons-image":return React.createElement("g",null,React.createElement("path",{d:"M13 9.5c0-.828.672-1.5 1.5-1.5s1.5.672 1.5 1.5-.672 1.5-1.5 1.5-1.5-.672-1.5-1.5zM22 6v12c0 1.105-.895 2-2 2H4c-1.105 0-2-.895-2-2V6c0-1.105.895-2 2-2h16c1.105 0 2 .895 2 2zm-2 0H4v7.444L8 9l5.895 6.55 1.587-1.85c.798-.932 2.24-.932 3.037 0L20 15.426V6z"}));case"gridicons-image-multiple":return React.createElement("g",null,React.createElement("path",{d:"M15 7.5c0-.828.672-1.5 1.5-1.5s1.5.672 1.5 1.5S17.328 9 16.5 9 15 8.328 15 7.5zM4 20h14c0 1.105-.895 2-2 2H4c-1.1 0-2-.9-2-2V8c0-1.105.895-2 2-2v14zM22 4v12c0 1.105-.895 2-2 2H8c-1.105 0-2-.895-2-2V4c0-1.105.895-2 2-2h12c1.105 0 2 .895 2 2zM8 4v6.333L11 7l4.855 5.395.656-.73c.796-.886 2.183-.886 2.977 0l.513.57V4H8z"}));case"gridicons-info":return React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"}));case"gridicons-info-outline":return React.createElement("g",null,React.createElement("path",{d:"M13 9h-2V7h2v2zm0 2h-2v6h2v-6zm-1-7c-4.411 0-8 3.589-8 8s3.589 8 8 8 8-3.589 8-8-3.589-8-8-8m0-2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2z"}));case"gridicons-jetpack-search":return React.createElement("g",null,React.createElement("path",{d:"M0 9.257C0 4.15 4.151 0 9.257 0c5.105 0 9.256 4.151 9.256 9.257a9.218 9.218 0 01-2.251 6.045l.034.033h1.053L24 22.01l-1.986 1.989-6.664-6.662v-1.055l-.033-.033a9.218 9.218 0 01-6.06 2.264C4.15 18.513 0 14.362 0 9.257zm4.169 1.537h4.61V1.82l-4.61 8.973zm5.547-3.092v8.974l4.61-8.974h-4.61z"}));case"gridicons-phone":return React.createElement("g",null,React.createElement("path",{d:"M16 2H8c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h8c1.104 0 2-.896 2-2V4c0-1.104-.896-2-2-2zm-3 19h-2v-1h2v1zm3-2H8V5h8v14z"}));case"gridicons-pages":return React.createElement("g",null,React.createElement("path",{d:"M16 8H8V6h8v2zm0 2H8v2h8v-2zm4-6v12l-6 6H6c-1.105 0-2-.895-2-2V4c0-1.105.895-2 2-2h12c1.105 0 2 .895 2 2zm-2 10V4H6v16h6v-4c0-1.105.895-2 2-2h4z"}));case"gridicons-posts":return React.createElement("g",null,React.createElement("path",{d:"M16 19H3v-2h13v2zm5-10H3v2h18V9zM3 5v2h11V5H3zm14 0v2h4V5h-4zm-6 8v2h10v-2H11zm-8 0v2h5v-2H3z"}));case"gridicons-search":return React.createElement("g",null,React.createElement("path",{d:"M21 19l-5.154-5.154C16.574 12.742 17 11.42 17 10c0-3.866-3.134-7-7-7s-7 3.134-7 7 3.134 7 7 7c1.42 0 2.742-.426 3.846-1.154L19 21l2-2zM5 10c0-2.757 2.243-5 5-5s5 2.243 5 5-2.243 5-5 5-5-2.243-5-5z"}));case"gridicons-star-outline":return React.createElement("g",null,React.createElement("path",{d:"M12 6.308l1.176 3.167.347.936.997.042 3.374.14-2.647 2.09-.784.62.27.963.91 3.25-2.813-1.872-.83-.553-.83.552-2.814 1.87.91-3.248.27-.962-.783-.62-2.648-2.092 3.374-.14.996-.04.347-.936L12 6.308M12 2L9.418 8.953 2 9.257l5.822 4.602L5.82 21 12 16.89 18.18 21l-2.002-7.14L22 9.256l-7.418-.305L12 2z"}));case"gridicons-star":return React.createElement("g",null,React.createElement("path",{d:"M12 2l2.582 6.953L22 9.257l-5.822 4.602L18.18 21 12 16.89 5.82 21l2.002-7.14L2 9.256l7.418-.304"}));case"gridicons-tag":return React.createElement("g",null,React.createElement("path",{d:"M20 2.007h-7.087c-.53 0-1.04.21-1.414.586L2.592 11.5c-.78.78-.78 2.046 0 2.827l7.086 7.086c.78.78 2.046.78 2.827 0l8.906-8.906c.376-.374.587-.883.587-1.413V4.007c0-1.105-.895-2-2-2zM17.007 9c-1.105 0-2-.895-2-2s.895-2 2-2 2 .895 2 2-.895 2-2 2z"}));case"gridicons-video":return React.createElement("g",null,React.createElement("path",{d:"M20 4v2h-2V4H6v2H4V4c-1.105 0-2 .895-2 2v12c0 1.105.895 2 2 2v-2h2v2h12v-2h2v2c1.105 0 2-.895 2-2V6c0-1.105-.895-2-2-2zM6 16H4v-3h2v3zm0-5H4V8h2v3zm4 4V9l4.5 3-4.5 3zm10 1h-2v-3h2v3zm0-5h-2V8h2v3z"}));case"gridicons-lock":return React.createElement(React.Fragment,null,React.createElement("g",{id:"lock"},React.createElement("path",{d:"M18,8h-1V7c0-2.757-2.243-5-5-5S7,4.243,7,7v1H6c-1.105,0-2,0.895-2,2v10c0,1.105,0.895,2,2,2h12c1.105,0,2-0.895,2-2V10 C20,8.895,19.105,8,18,8z M9,7c0-1.654,1.346-3,3-3s3,1.346,3,3v1H9V7z M13,15.723V18h-2v-2.277c-0.595-0.346-1-0.984-1-1.723 c0-1.105,0.895-2,2-2s2,0.895,2,2C14,14.738,13.595,15.376,13,15.723z"})),React.createElement("g",{id:"Layer_1"}));case"gridicons-external":return React.createElement("g",null,React.createElement("path",{d:"M19 13v6c0 1.105-.895 2-2 2H5c-1.105 0-2-.895-2-2V7c0-1.105.895-2 2-2h6v2H5v12h12v-6h2zM13 3v2h4.586l-7.793 7.793 1.414 1.414L19 6.414V11h2V3h-8z"}))}}render(){const{size:e=24,className:t=""}=this.props,n=this.props.height||e,c=this.props.width||e,i=this.props.style||{height:n,width:c},s="gridicons-"+this.props.icon,o=(0,a.A)("gridicon",s,t,{"needs-offset":this.needsOffset(s,e)}),r=this.getSVGDescription(s);return React.createElement("svg",{className:o,focusable:this.props.focusable,height:n,onClick:this.props.onClick,style:i,viewBox:"0 0 24 24",width:c,xmlns:"http://www.w3.org/2000/svg","aria-hidden":this.props["aria-hidden"]},r?React.createElement("desc",null,r):null,this.renderIcon(s))}}const o=s},40597:(e,t,n)=>{"use strict";n.d(t,{A:()=>p});var c=n(56427),a=n(13022),i=n(51609),s=n.n(i),o=n(51112),r=n(11883);n(84813);const l=e=>({"top-end":"top left",top:"top center","top-start":"top right","bottom-end":"bottom left",bottom:"bottom center","bottom-start":"bottom right"}[e]),p=({className:e="",iconClassName:t="",placement:n="bottom-end",animate:p=!0,iconCode:m="info-outline",iconSize:u=18,offset:d=10,title:g,children:h,popoverAnchorStyle:v="icon",forceShow:f=!1,hoverShow:b=!1,wide:k=!1,inline:y=!0,shift:E=!1})=>{const[w,R]=(0,i.useState)(!1),[A,C]=(0,i.useState)(null),z=(0,i.useCallback)((()=>R(!1)),[R]),S=(0,i.useCallback)((e=>{e.preventDefault(),R(!w)}),[w,R]),_={position:l(n),placement:n,animate:p,noArrow:!1,resize:!1,flip:!1,offset:d,focusOnMount:"container",onClose:z,className:"icon-tooltip-container",inline:y,shift:E},j="wrapper"===v,M=(0,a.A)("icon-tooltip-wrapper",e),x={left:j?0:-(62-u/2)+"px"},N=j&&f,L=(0,i.useCallback)((()=>{b&&(A&&(clearTimeout(A),C(null)),R(!0))}),[b,A]),P=(0,i.useCallback)((()=>{if(b){const e=setTimeout((()=>{R(!1),C(null)}),100);C(e)}}),[b]);return s().createElement("div",{className:M,"data-testid":"icon-tooltip_wrapper",onMouseEnter:L,onMouseLeave:P},!j&&s().createElement(o.A,{variant:"link",onMouseDown:S},s().createElement(r.A,{className:t,icon:m,size:u})),s().createElement("div",{className:(0,a.A)("icon-tooltip-helper",{"is-wide":k}),style:x},(N||w)&&s().createElement(c.Popover,_,s().createElement("div",null,g&&s().createElement("div",{className:"icon-tooltip-title"},g),s().createElement("div",{className:"icon-tooltip-content"},h)))))}},78478:(e,t,n)=>{"use strict";n.d(t,{M5:()=>g,Nr:()=>m,Wy:()=>d,b6:()=>p});var c=n(96072),a=n.n(c),i=n(56427),s=n(13022),o=n(84705),r=n(47842);const l=({className:e,size:t=24,viewBox:n="0 0 24 24",opacity:c=1,color:o="#2C3338",children:l})=>{const p={className:(0,s.A)(r.A.iconWrapper,e),width:t,height:t,viewBox:n,opacity:c,fill:void 0};return o&&(p.fill=o),React.createElement(i.SVG,a()({},p,{fillRule:"evenodd",clipRule:"evenodd",xmlns:"http://www.w3.org/2000/svg"}),React.createElement(i.G,{opacity:c},l))},p=({opacity:e=1,size:t,color:n})=>React.createElement(l,{size:t,opacity:e,color:n},React.createElement(i.Path,{d:"M15.5 3.97809V18.0219L7.5 15.5977V20H6V15.1431L3.27498 14.3173C2.22086 13.9979 1.5 13.0262 1.5 11.9248V10.0752C1.5 8.97375 2.22087 8.00207 3.27498 7.68264L15.5 3.97809ZM14 16L7.5 14.0303L7.5 7.96969L14 5.99999V16ZM6 8.42423L6 13.5757L3.70999 12.8818C3.28835 12.754 3 12.3654 3 11.9248V10.0752C3 9.63462 3.28835 9.24595 3.70999 9.11818L6 8.42423ZM17.5 11.75H21.5V10.25H17.5V11.75ZM21.5 16L17.5 15V13.5L21.5 14.5V16ZM17.5 8.5L21.5 7.5V6L17.5 7V8.5Z"})),m=({size:e,className:t=r.A["checkmark-icon"],color:n})=>React.createElement(l,{className:t,size:e,color:n},React.createElement(i.Path,{d:"M11 17.768l-4.884-4.884 1.768-1.768L11 14.232l8.658-8.658C17.823 3.39 15.075 2 12 2 6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10c0-1.528-.353-2.97-.966-4.266L11 17.768z"})),u={...{"anti-spam":({opacity:e=1,size:t,color:n})=>React.createElement(l,{size:t,opacity:e,color:n},React.createElement(i.Path,{d:"M13.2,4.7l4.7,12.8c0.4,1.1,1,1.5,2.1,1.6c0.1,0,0.1,0,0.1,0l0.1,0.1l0.1,0.1c0,0.1,0,0.1,0,0.2c0,0.1,0,0.1,0,0.1 s0,0.1-0.1,0.1c-0.1,0-0.1,0.1-0.1,0.1s-0.1,0-0.2,0h-5.1c-0.1,0-0.1,0-0.2,0c-0.1,0-0.1-0.1-0.1-0.1l-0.1-0.1c0-0.1,0-0.1,0-0.1 c0-0.1,0-0.1,0-0.2s0-0.1,0.1-0.1l0.1-0.1c0,0,0.1,0,0.2,0c0.5,0,1.1-0.2,1.1-0.8c0-0.3-0.1-0.5-0.2-0.8l-1.1-3.1 c-0.1-0.2-0.1-0.2-0.2-0.2h-4.3c-0.7,0-1.5,0-1.9,0.9l-1.1,2.4C7.1,17.6,7,17.8,7,18.1c0,0.8,1,0.9,1.6,0.9c0.1,0,0.1,0,0.2,0 L8.8,19l0.1,0.1c0,0.1,0,0.1,0,0.2c0,0.1,0,0.1,0,0.1s-0.1,0.1-0.1,0.1l-0.1,0.1c-0.1,0-0.1,0-0.2,0H4.1c-0.1,0-0.1,0-0.1,0 c-0.1,0-0.1-0.1-0.1-0.1l-0.1-0.1c0-0.1,0-0.1,0-0.1c0-0.1,0-0.1,0-0.2s0-0.1,0.1-0.1L4,19c0,0,0.1,0,0.1,0C5.2,19,5.5,18.5,6,17.5 l5.4-12.4c0.2-0.5,0.8-1,1.3-1C13,4.2,13.1,4.4,13.2,4.7z M9.1,13.1c0,0.1-0.1,0.1-0.1,0.2c0,0.1,0.1,0.1,0.1,0.1h4.4 c0.3,0,0.4-0.1,0.4-0.3c0-0.1,0-0.2-0.1-0.3l-1.2-3.5c-0.3-0.8-0.8-1.9-0.8-2.7c0-0.1,0-0.1-0.1-0.1c0,0-0.1,0-0.1,0.1 c-0.1,0.6-0.4,1.2-0.7,1.7L9.1,13.1z"}),React.createElement(i.Path,{d:"M13.2,4.7l4.7,12.8c0.4,1.1,1,1.5,2.1,1.6c0.1,0,0.1,0,0.1,0l0.1,0.1l0.1,0.1c0,0.1,0,0.1,0,0.2c0,0.1,0,0.1,0,0.1 s0,0.1-0.1,0.1c-0.1,0-0.1,0.1-0.1,0.1s-0.1,0-0.2,0h-5.1c-0.1,0-0.1,0-0.2,0c-0.1,0-0.1-0.1-0.1-0.1l-0.1-0.1c0-0.1,0-0.1,0-0.1 c0-0.1,0-0.1,0-0.2s0-0.1,0.1-0.1l0.1-0.1c0,0,0.1,0,0.2,0c0.5,0,1.1-0.2,1.1-0.8c0-0.3-0.1-0.5-0.2-0.8l-1.1-3.1 c-0.1-0.2-0.1-0.2-0.2-0.2h-4.3c-0.7,0-1.5,0-1.9,0.9l-1.1,2.4C7.1,17.6,7,17.8,7,18.1c0,0.8,1,0.9,1.6,0.9c0.1,0,0.1,0,0.2,0 L8.8,19l0.1,0.1c0,0.1,0,0.1,0,0.2c0,0.1,0,0.1,0,0.1s-0.1,0.1-0.1,0.1l-0.1,0.1c-0.1,0-0.1,0-0.2,0H4.1c-0.1,0-0.1,0-0.1,0 c-0.1,0-0.1-0.1-0.1-0.1l-0.1-0.1c0-0.1,0-0.1,0-0.1c0-0.1,0-0.1,0-0.2s0-0.1,0.1-0.1L4,19c0,0,0.1,0,0.1,0C5.2,19,5.5,18.5,6,17.5 l5.4-12.4c0.2-0.5,0.8-1,1.3-1C13,4.2,13.1,4.4,13.2,4.7z M9.1,13.1c0,0.1-0.1,0.1-0.1,0.2c0,0.1,0.1,0.1,0.1,0.1h4.4 c0.3,0,0.4-0.1,0.4-0.3c0-0.1,0-0.2-0.1-0.3l-1.2-3.5c-0.3-0.8-0.8-1.9-0.8-2.7c0-0.1,0-0.1-0.1-0.1c0,0-0.1,0-0.1,0.1 c-0.1,0.6-0.4,1.2-0.7,1.7L9.1,13.1z"}),React.createElement(i.Path,{d:"M21.6,12.5c0,0.6-0.3,1-0.9,1c-0.6,0-0.8-0.3-0.8-0.8c0-0.6,0.4-1,0.9-1C21.3,11.7,21.6,12.1,21.6,12.5z"}),React.createElement(i.Path,{d:"M4.1,12.5c0,0.6-0.3,1-0.9,1s-0.8-0.3-0.8-0.8c0-0.6,0.4-1,0.9-1S4.1,12.1,4.1,12.5z"})),backup:({opacity:e=1,size:t,color:n})=>React.createElement(l,{size:t,opacity:e,color:n},React.createElement(i.Path,{d:"M2.1,5.8c0-0.1,0-0.1,0-0.2c0-0.2,0.1-0.5,0.1-0.7c0.1-0.4,0.4-0.6,0.7-0.8l8.3-2.9c0.1-0.1,0.3-0.1,0.4-0.1l0.5,0.1 l8.3,2.9c0.3,0.2,0.5,0.4,0.7,0.7c0.2,0.2,0.2,0.4,0.2,0.7c0,0.1,0,0.1,0,0.2v0.1c-0.1,0.5-0.2,0.9-0.3,1.4 c-0.2,0.4-0.3,1.2-0.7,2.2c-0.3,1-0.7,2.1-1.1,3.1c-0.5,1-1,2.1-1.6,3.3s-1.4,2.3-2.2,3.5c-0.9,1.1-1.8,2.2-2.8,3.1 c-0.2,0.2-0.5,0.4-0.9,0.4c-0.3,0-0.6-0.1-0.9-0.4c-1.2-1.1-2.4-2.4-3.5-4c-1-1.6-1.9-3-2.5-4.3c-0.6-1.3-1.1-2.7-1.6-4 C2.8,8.7,2.5,7.6,2.3,7C2.3,6.5,2.1,6.1,2.1,5.8z M2.9,5.9c0,0.2,0.1,0.4,0.1,0.8C3.1,7,3.2,7.5,3.5,8.2C3.7,9,3.9,9.7,4.2,10.6 c0.3,0.7,0.7,1.7,1.1,2.7c0.4,1,1,2,1.5,2.9c0.5,1,1.2,1.9,1.9,2.9c0.8,1,1.6,1.9,2.4,2.6c0.2,0.2,0.4,0.2,0.5,0.2 c0.2,0,0.4-0.1,0.5-0.2c1.2-1,2.2-2.3,3.2-3.8c1-1.5,1.8-2.8,2.3-4c0.6-1.3,1.1-2.5,1.5-3.9c0.4-1.3,0.7-2.2,0.9-2.8 c0.1-0.5,0.2-1,0.3-1.3c0-0.1,0-0.1,0-0.1c0-0.2,0-0.3-0.1-0.4C20.3,5.2,20.2,5.1,20,5L12,2.1c0,0-0.1,0-0.2,0s-0.1,0-0.1,0h-0.2 l-8,2.8C3.2,5,3.1,5.2,3,5.3C2.9,5.5,2.9,5.6,2.9,5.8C2.9,5.8,2.9,5.8,2.9,5.9z M5.9,6.7h3l2.8,7l2.8-7h3c-0.1,0.1-0.2,0.5-0.3,0.8 C17,7.8,17,8.2,16.8,8.4c-0.1,0.3-0.2,0.5-0.4,0.8c0,0.1-0.1,0.1-0.1,0.1s-0.1,0.1-0.2,0.1c-0.1,0-0.1,0-0.1,0 c-0.1,0-0.2,0.1-0.2,0.2c0,0-0.1,0.1-0.1,0.1s-0.1,0.1-0.1,0.1c0,0,0,0.1-0.1,0.2c0,0.1-0.1,0.1-0.1,0.1l-0.4,1.1 c-1.3,3.3-2.1,5.2-2.3,5.8h-2.2l-1-2.4c-0.1-0.3-0.3-0.8-0.5-1.3c-0.1-0.3-0.3-0.8-0.5-1.3L8,10.8c-0.1-0.1-0.1-0.2-0.1-0.4 C7.8,10.2,7.7,10,7.7,9.8C7.6,9.7,7.5,9.5,7.4,9.4C7.3,9.3,7.3,9.3,7.3,9.3c-0.1,0-0.2,0-0.2,0s-0.1,0-0.1,0 C6.6,8.5,6.3,7.6,5.9,6.7z"})),boost:({opacity:e=1,size:t,color:n})=>React.createElement(l,{size:t,opacity:e,color:n},React.createElement(i.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M4.19505 16.2545C4.47368 16.561 4.94802 16.5836 5.25451 16.3049L10.2595 11.7549L14.2842 15.2765L19 10.5607V13.75H20.5V9.5V8.75239V8.7476V8H19.7529H19.7471H19H14.75V9.5H17.9393L14.2158 13.2235L10.2405 9.74507L4.2455 15.195C3.93901 15.4737 3.91642 15.948 4.19505 16.2545Z"})),crm:({opacity:e=1,size:t,color:n})=>React.createElement(l,{size:t,opacity:e,color:n},React.createElement(i.Path,{d:"M15.5 9.5a1 1 0 1 0 0-2 1 1 0 0 0 0 2Zm0 1.5a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Zm-2.25 6v-2a2.75 2.75 0 0 0-2.75-2.75h-4A2.75 2.75 0 0 0 3.75 15v2h1.5v-2c0-.69.56-1.25 1.25-1.25h4c.69 0 1.25.56 1.25 1.25v2h1.5Zm7-2v2h-1.5v-2c0-.69-.56-1.25-1.25-1.25H15v-1.5h2.5A2.75 2.75 0 0 1 20.25 15ZM9.5 8.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm1.5 0a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0Z"})),extras:({opacity:e=1,size:t,color:n})=>React.createElement(l,{size:t,opacity:e,color:n},React.createElement(i.Path,{d:"M18.5 5.5V8H20V5.5h2.5V4H20V1.5h-1.5V4H16v1.5h2.5ZM12 4H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-6h-1.5v6a.5.5 0 0 1-.5.5H6a.5.5 0 0 1-.5-.5V6a.5.5 0 0 1 .5-.5h6V4Z"})),protect:({opacity:e=1,size:t,className:n,color:c})=>React.createElement(l,{className:n,size:t,opacity:e,color:c},React.createElement(i.Path,{d:"M12 3.17627L18.75 6.24445V10.8183C18.75 14.7173 16.2458 18.4089 12.7147 19.5735C12.2507 19.7265 11.7493 19.7265 11.2853 19.5735C7.75416 18.4089 5.25 14.7173 5.25 10.8183V6.24445L12 3.17627ZM6.75 7.21032V10.8183C6.75 14.1312 8.89514 17.2057 11.7551 18.149C11.914 18.2014 12.086 18.2014 12.2449 18.149C15.1049 17.2057 17.25 14.1312 17.25 10.8183V7.21032L12 4.82396L6.75 7.21032Z"}),React.createElement(i.Path,{d:"M15.5291 10.0315L11.1818 14.358L8.47095 11.66L9.52907 10.5968L11.1818 12.2417L14.4709 8.96826L15.5291 10.0315Z"})),scan:({opacity:e=1,size:t,color:n})=>React.createElement(l,{size:t,opacity:e,color:n},React.createElement(i.Path,{d:"m12 3.176 6.75 3.068v4.574c0 3.9-2.504 7.59-6.035 8.755a2.283 2.283 0 0 1-1.43 0c-3.53-1.164-6.035-4.856-6.035-8.755V6.244L12 3.176ZM6.75 7.21v3.608c0 3.313 2.145 6.388 5.005 7.33.159.053.331.053.49 0 2.86-.942 5.005-4.017 5.005-7.33V7.21L12 4.824 6.75 7.21Z"})),search:({opacity:e=1,size:t,color:n})=>React.createElement(l,{size:t,opacity:e,color:n},React.createElement(i.Path,{d:"M17.5 11.5a4 4 0 1 1-8 0 4 4 0 0 1 8 0Zm1.5 0a5.5 5.5 0 0 1-9.142 4.121l-3.364 2.943-.988-1.128 3.373-2.952A5.5 5.5 0 1 1 19 11.5Z"})),social:p,star:({size:e,className:t=r.A["star-icon"],color:n})=>React.createElement(l,{className:t,size:e,color:n},React.createElement(i.Path,{d:"M12 2l2.582 6.953L22 9.257l-5.822 4.602L18.18 21 12 16.89 5.82 21l2.002-7.14L2 9.256l7.418-.304"})),videopress:({opacity:e=1,size:t,color:n})=>React.createElement(l,{size:t,opacity:e,color:n},React.createElement(i.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M4.3,6.2c0.8,0,1.6,0.6,1.8,1.4l2.3,7.9c0,0,0,0,0,0l2.7-9.3h1.5h4.2c2.9,0,4.9,1.9,4.9,4.7c0,2.9-2,4.7-5,4.7 h-2h-2.5l-0.5,1.5c-0.4,1.4-1.7,2.3-3.2,2.3c-1.4,0-2.7-0.9-3.2-2.3L2.5,8.7C2.1,7.4,3,6.2,4.3,6.2z M13,12.8h2.9c1.3,0,2-0.7,2-1.9 c0-1.2-0.8-1.8-2-1.8h-1.7L13,12.8z"})),jetpack:({size:e,className:t=r.A.jetpack,color:n})=>React.createElement(l,{className:t,size:e,color:n,viewBox:"0 0 32 32"},React.createElement(i.Path,{className:"jetpack-logo__icon-circle",d:"M16,0C7.2,0,0,7.2,0,16s7.2,16,16,16s16-7.2,16-16S24.8,0,16,0z"}),React.createElement(i.Polygon,{fill:"#fff",points:"15,19 7,19 15,3"}),React.createElement(i.Polygon,{fill:"#fff",points:"17,29 17,13 25,13"})),share:({size:e=16,className:t,color:n})=>React.createElement(l,{className:t,size:e,color:n,viewBox:"0 0 16 16"},React.createElement(i.Path,{fill:"#161722",fillRule:"evenodd",d:"M8.3 4.66C3.85 5.308.727 9.75.034 13.69l-.02.117c-.137.842.809 1.232 1.446.68 2.013-1.745 3.648-2.475 5.318-2.719a10.482 10.482 0 011.524-.103v2.792c0 .694.82 1.041 1.3.55l6.176-6.307a.79.79 0 00.012-1.088L9.614 1.004C9.14.496 8.301.84 8.301 1.542v3.117zm1.525-1.175v1.85a.773.773 0 01-.654.77l-.655.096c-2.133.311-3.987 1.732-5.295 3.672-.472.7-.854 1.44-1.143 2.18a12.32 12.32 0 011.675-.972c1.58-.75 3.048-.972 4.548-.972h.762a.77.77 0 01.762.779v1.69l4.347-4.44-4.347-4.653z",clipRule:"evenodd"})),ai:({size:e=24,color:t="#069e08"})=>React.createElement(l,{color:t,size:e,viewBox:"0 0 32 32"},React.createElement(i.Path,{className:"spark-first",d:"M9.33301 5.33325L10.4644 8.20188L13.333 9.33325L10.4644 10.4646L9.33301 13.3333L8.20164 10.4646L5.33301 9.33325L8.20164 8.20188L9.33301 5.33325Z"}),React.createElement(i.Path,{className:"spark-second",d:"M21.3333 5.33333L22.8418 9.15817L26.6667 10.6667L22.8418 12.1752L21.3333 16L19.8248 12.1752L16 10.6667L19.8248 9.15817L21.3333 5.33333Z"}),React.createElement(i.Path,{className:"spark-third",d:"M14.6667 13.3333L16.5523 18.1144L21.3333 20L16.5523 21.8856L14.6667 26.6667L12.781 21.8856L8 20L12.781 18.1144L14.6667 13.3333Z"})),stats:({opacity:e=1,size:t,color:n})=>React.createElement(l,{size:t,opacity:e,color:n},React.createElement(i.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M11.25 5H12.75V20H11.25V5ZM6 10H7.5V20H6V10ZM18 14H16.5V20H18V14Z"}))}};function d(e){return u[e]?u[e]:null}const g=({serviceName:e,className:t,iconSize:n})=>React.createElement(o.d6,{className:(0,s.A)(r.A.socialIcon,r.A[e],t),icon:e,size:n||24})},38250:(e,t,n)=>{"use strict";n.d(t,{A:()=>b});var c=n(96072),a=n.n(c),i=n(27723),s=n(51113),o=n(53512),r=n(13022),l=n(51609),p=n.n(l),m=n(3924),u=n(61069),d=n(48907),g=(n(9128),n(67142)),h=n(60442);const __=i.__,_x=i._x,v=()=>p().createElement(g.A,{logoColor:"#000",showText:!1,height:16,"aria-hidden":"true"}),f=()=>p().createElement(p().Fragment,null,p().createElement(s.A,{icon:o.A,size:16}),p().createElement("span",{className:"jp-dashboard-footer__accessible-external-link"},/* translators: accessibility text */
__("(opens in a new tab)","jetpack-publicize-pkg"))),b=({moduleName:e=__("Jetpack","jetpack-publicize-pkg"),className:t,moduleNameHref:n="https://jetpack.com",menu:c,useInternalLinks:i,onAboutClick:s,onPrivacyClick:o,onTermsClick:l,...g})=>{const[b]=(0,h.A)("sm","<="),[k]=(0,h.A)("md","<="),[y]=(0,h.A)("lg",">"),E=(0,u.A)();let w=[{label:_x("About","Link to learn more about Jetpack.","jetpack-publicize-pkg"),title:__("About Jetpack","jetpack-publicize-pkg"),href:i?new URL("admin.php?page=jetpack_about",E).href:(0,m.A)("jetpack-about"),target:i?"_self":"_blank",onClick:s},{label:_x("Privacy","Shorthand for Privacy Policy.","jetpack-publicize-pkg"),title:__("Automattic's Privacy Policy","jetpack-publicize-pkg"),href:i?new URL("admin.php?page=jetpack#/privacy",E).href:(0,m.A)("a8c-privacy"),target:i?"_self":"_blank",onClick:o},{label:_x("Terms","Shorthand for Terms of Service.","jetpack-publicize-pkg"),title:__("WordPress.com Terms of Service","jetpack-publicize-pkg"),href:(0,m.A)("wpcom-tos"),target:"_blank",onClick:l}];c&&(w=[...w,...c]);const R=p().createElement(p().Fragment,null,p().createElement(v,null),e);return p().createElement("footer",a()({className:(0,r.A)("jp-dashboard-footer",{"is-sm":b,"is-md":k,"is-lg":y},t),"aria-label":__("Jetpack","jetpack-publicize-pkg"),role:"contentinfo"},g),p().createElement("ul",null,p().createElement("li",{className:"jp-dashboard-footer__jp-item"},n?p().createElement("a",{href:n},R):R),w.map((e=>{const t="button"===e.role,n=!t&&"_blank"===e.target;return p().createElement("li",{key:e.label},p().createElement("a",{href:e.href,title:e.title,target:e.target,onClick:e.onClick,onKeyDown:e.onKeyDown,className:(0,r.A)("jp-dashboard-footer__menu-item",{"is-external":n}),role:e.role,rel:n?"noopener noreferrer":void 0,tabIndex:t?0:void 0},e.label,n&&p().createElement(f,null)))})),p().createElement("li",{className:"jp-dashboard-footer__a8c-item"},p().createElement("a",{href:i?new URL("admin.php?page=jetpack_about",E).href:(0,m.A)("a8c-about"),"aria-label":__("An Automattic Airline","jetpack-publicize-pkg")},p().createElement(d.A,{"aria-hidden":"true"})))))}},67142:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var c=n(96072),a=n.n(c),i=n(27723),s=n(13022),o=n(51609),r=n.n(o);const __=i.__,l=({logoColor:e="#069e08",showText:t=!0,className:n,height:c=32,...i})=>{const o=t?"0 0 118 32":"0 0 32 32";return r().createElement("svg",a()({xmlns:"http://www.w3.org/2000/svg",x:"0px",y:"0px",viewBox:o,className:(0,s.A)("jetpack-logo",n),"aria-labelledby":"jetpack-logo-title",height:c},i,{role:"img"}),r().createElement("title",{id:"jetpack-logo-title"},__("Jetpack Logo","jetpack-publicize-pkg")),r().createElement("path",{fill:e,d:"M16,0C7.2,0,0,7.2,0,16s7.2,16,16,16s16-7.2,16-16S24.8,0,16,0z M15,19H7l8-16V19z M17,29V13h8L17,29z"}),t&&r().createElement(r().Fragment,null,r().createElement("path",{d:"M41.3,26.6c-0.5-0.7-0.9-1.4-1.3-2.1c2.3-1.4,3-2.5,3-4.6V8h-3V6h6v13.4C46,22.8,45,24.8,41.3,26.6z"}),r().createElement("path",{d:"M65,18.4c0,1.1,0.8,1.3,1.4,1.3c0.5,0,2-0.2,2.6-0.4v2.1c-0.9,0.3-2.5,0.5-3.7,0.5c-1.5,0-3.2-0.5-3.2-3.1V12H60v-2h2.1V7.1 H65V10h4v2h-4V18.4z"}),r().createElement("path",{d:"M71,10h3v1.3c1.1-0.8,1.9-1.3,3.3-1.3c2.5,0,4.5,1.8,4.5,5.6s-2.2,6.3-5.8,6.3c-0.9,0-1.3-0.1-2-0.3V28h-3V10z M76.5,12.3 c-0.8,0-1.6,0.4-2.5,1.2v5.9c0.6,0.1,0.9,0.2,1.8,0.2c2,0,3.2-1.3,3.2-3.9C79,13.4,78.1,12.3,76.5,12.3z"}),r().createElement("path",{d:"M93,22h-3v-1.5c-0.9,0.7-1.9,1.5-3.5,1.5c-1.5,0-3.1-1.1-3.1-3.2c0-2.9,2.5-3.4,4.2-3.7l2.4-0.3v-0.3c0-1.5-0.5-2.3-2-2.3 c-0.7,0-2.3,0.5-3.7,1.1L84,11c1.2-0.4,3-1,4.4-1c2.7,0,4.6,1.4,4.6,4.7L93,22z M90,16.4l-2.2,0.4c-0.7,0.1-1.4,0.5-1.4,1.6 c0,0.9,0.5,1.4,1.3,1.4s1.5-0.5,2.3-1V16.4z"}),r().createElement("path",{d:"M104.5,21.3c-1.1,0.4-2.2,0.6-3.5,0.6c-4.2,0-5.9-2.4-5.9-5.9c0-3.7,2.3-6,6.1-6c1.4,0,2.3,0.2,3.2,0.5V13 c-0.8-0.3-2-0.6-3.2-0.6c-1.7,0-3.2,0.9-3.2,3.6c0,2.9,1.5,3.8,3.3,3.8c0.9,0,1.9-0.2,3.2-0.7V21.3z"}),r().createElement("path",{d:"M110,15.2c0.2-0.3,0.2-0.8,3.8-5.2h3.7l-4.6,5.7l5,6.3h-3.7l-4.2-5.8V22h-3V6h3V15.2z"}),r().createElement("path",{d:"M58.5,21.3c-1.5,0.5-2.7,0.6-4.2,0.6c-3.6,0-5.8-1.8-5.8-6c0-3.1,1.9-5.9,5.5-5.9s4.9,2.5,4.9,4.9c0,0.8,0,1.5-0.1,2h-7.3 c0.1,2.5,1.5,2.8,3.6,2.8c1.1,0,2.2-0.3,3.4-0.7C58.5,19,58.5,21.3,58.5,21.3z M56,15c0-1.4-0.5-2.9-2-2.9c-1.4,0-2.3,1.3-2.4,2.9 C51.6,15,56,15,56,15z"})))}},28509:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var c=n(13022),a=n(51609),i=n(71157);const s=Number(i.A.smCols),o=Number(i.A.mdCols),r=Number(i.A.lgCols),l=e=>{const{children:t,tagName:n="div",className:l}=e,p=Math.min(s,"number"==typeof e.sm?e.sm:s),m=Math.min(s,"object"==typeof e.sm?e.sm.start:0),u=Math.min(s,"object"==typeof e.sm?e.sm.end:0),d=Math.min(o,"number"==typeof e.md?e.md:o),g=Math.min(o,"object"==typeof e.md?e.md.start:0),h=Math.min(o,"object"==typeof e.md?e.md.end:0),v=Math.min(r,"number"==typeof e.lg?e.lg:r),f=Math.min(r,"object"==typeof e.lg?e.lg.start:0),b=Math.min(r,"object"==typeof e.lg?e.lg.end:0),k=(0,c.A)(l,{[i.A[`col-sm-${p}`]]:!(m&&u),[i.A[`col-sm-${m}-start`]]:m>0,[i.A[`col-sm-${u}-end`]]:u>0,[i.A[`col-md-${d}`]]:!(g&&h),[i.A[`col-md-${g}-start`]]:g>0,[i.A[`col-md-${h}-end`]]:h>0,[i.A[`col-lg-${v}`]]:!(f&&b),[i.A[`col-lg-${f}-start`]]:f>0,[i.A[`col-lg-${b}-end`]]:b>0});return(0,a.createElement)(n,{className:k},t)}},75918:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var c=n(13022),a=n(51609),i=n(82498);const s=({children:e,fluid:t=!1,tagName:n="div",className:s,horizontalGap:o=1,horizontalSpacing:r=1},l)=>{const p=(0,a.useMemo)((()=>{const e=`calc( var(--horizontal-spacing) * ${r} )`;return{paddingTop:e,paddingBottom:e,rowGap:`calc( var(--horizontal-spacing) * ${o} )`}}),[o,r]),m=(0,c.A)(s,i.A.container,{[i.A.fluid]:t});return(0,a.createElement)(n,{className:m,style:p,ref:l},e)},o=(0,a.forwardRef)(s)},60442:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var c=n(29491),a=n(59053);const i=["sm","md","lg"],s=(e,t)=>{const n=Array.isArray(e)?e:[e],s=Array.isArray(t)?t:[t],[o,r,l]=i,p={sm:(0,c.useMediaQuery)(a.A[o]),md:(0,c.useMediaQuery)(a.A[r]),lg:(0,c.useMediaQuery)(a.A[l])};return n.map(((e,t)=>{const n=s[t];return n?((e,t,n)=>{const c=i.indexOf(e),a=c+1,s=t.includes("=");let o=[];return t.startsWith("<")&&(o=i.slice(0,s?a:c)),t.startsWith(">")&&(o=i.slice(s?c:a)),o?.length?o.some((e=>n[e])):n[e]})(e,n,p):p[e]}))}},79245:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>w,N0:()=>E,eY:()=>k,i7:()=>y});var c=n(27723),a=n(51113),i=n(83883),s=n(31249),o=n(13022),r=n(51609),l=n.n(r),p=n(40597),m=n(60442),u=n(85879),d=n(47425),g=n(53128);const __=c.__,h=__("Included","jetpack-publicize-pkg"),v=__("Not included","jetpack-publicize-pkg"),f=__("Coming soon","jetpack-publicize-pkg"),b=(0,r.createContext)(void 0),k=({isIncluded:e=!1,isComingSoon:t=!1,index:n=0,label:u=null,tooltipInfo:k,tooltipTitle:y,tooltipClassName:E=""})=>{const[w]=(0,m.A)("lg"),R=(0,r.useContext)(b)[n],A=t||e,C=R.name,z=R.tooltipInfo,S=R.tooltipTitle,_=k||!w&&z,j=((e,t,n)=>e?{lg:f,
// translators: Name of the current feature
default:(0,c.sprintf)(__("%s coming soon","jetpack-publicize-pkg"),n)}:{lg:t?h:v,default:t?n:(0,c.sprintf)(/* translators: Name of the current feature */
__("%s not included","jetpack-publicize-pkg"),n)})(t,e,C),M=w?j.lg:j.default;return l().createElement("div",{className:(0,o.A)(g.A.item,g.A.value)},l().createElement(a.A,{className:(0,o.A)(g.A.icon,A?g.A["icon-check"]:g.A["icon-cross"]),size:32,icon:A?i.A:s.A}),l().createElement(d.Ay,{variant:"body-small"},u||M),_&&l().createElement(p.A,{title:y||S,iconClassName:g.A["popover-icon"],className:(0,o.A)(g.A.popover,E),placement:"bottom-end",iconSize:14,offset:4,wide:Boolean(y&&k)},l().createElement(d.Ay,{variant:"body-small",component:"div"},k||z)))},y=({children:e})=>l().createElement("div",{className:g.A.header},e),E=({primary:e=!1,children:t})=>{let n=0;return l().createElement("div",{className:(0,o.A)(g.A.card,{[g.A["is-primary"]]:e})},r.Children.map(t,(e=>{const t=e;return t.type===k?(n++,(0,r.cloneElement)(t,{index:n-1})):t})))},w=({title:e,items:t,children:n,showIntroOfferDisclaimer:c=!1})=>{const[a]=(0,m.A)("lg");return l().createElement(b.Provider,{value:t},l().createElement("div",{className:(0,o.A)(g.A.container,{[g.A["is-viewport-large"]]:a}),style:{"--rows":t.length+1,"--columns":r.Children.toArray(n).length+1}},l().createElement("div",{className:g.A.table},l().createElement(d.Ay,{variant:"headline-small"},e),a&&t.map(((e,n)=>l().createElement("div",{className:(0,o.A)(g.A.item,{[g.A["last-feature"]]:n===t.length-1}),key:n},l().createElement(d.Ay,{variant:"body-small"},l().createElement("strong",null,e.name)),e.tooltipInfo&&l().createElement(p.A,{title:e.tooltipTitle,iconClassName:g.A["popover-icon"],className:g.A.popover,placement:e.tooltipPlacement?e.tooltipPlacement:"bottom-end",iconSize:14,offset:4,wide:Boolean(e.tooltipTitle&&e.tooltipInfo)},l().createElement(d.Ay,{variant:"body-small"},e.tooltipInfo))))),n)),l().createElement("div",{className:g.A["tos-container"]},l().createElement("div",{className:g.A.tos},c&&l().createElement(d.Ay,{variant:"body-small"},__("Reduced pricing is a limited offer for the first year and renews at regular price.","jetpack-publicize-pkg")),l().createElement(u.A,{multipleButtons:!0}))))}},28539:(e,t,n)=>{"use strict";n.d(t,{l:()=>l});var c=n(51113),a=n(45459),i=n(51609),s=n.n(i),o=n(78478),r=n(20184);const l=({products:e,icon:t,size:n=24})=>{if(t){const e=(0,o.Wy)(t);return s().createElement("div",{className:r.A["product-bundle-icons"]},s().createElement(e,{size:n}))}return s().createElement("div",{className:r.A["product-bundle-icons"]},e.map(((t,l)=>{const p=(0,o.Wy)(t),m=p||(()=>null);return s().createElement(i.Fragment,{key:l},s().createElement(m,{size:n}),l!==e.length-1&&s().createElement(c.A,{className:r.A["plus-icon"],key:"icon-plugs"+(2*l+1),icon:a.A,size:16}))})))}},75304:(e,t,n)=>{"use strict";n.d(t,{A:()=>f});var c=n(96072),a=n.n(c),i=n(27723),s=n(51113),o=n(83883),r=n(13022),l=n(42266),p=n(51112),m=n(78478),u=n(489),d=n(47425),g=n(28539),h=n(22268),v=n(20184);const __=i.__,f=({addProductUrl:e,buttonDisclaimer:t,buttonText:n="",className:c,description:f,error:b="",features:k,hasRequiredPlan:y,icon:E,isBundle:w=!1,isCard:R,isLoading:A,onAdd:C,pricing:z={},slug:S,subTitle:_="",supportedProducts:j,title:M=""})=>{const{isFree:x,price:N,currency:L,offPrice:P}=z,B=!x&&!y,I=(0,i.sprintf)(/* translators: placeholder is product name. */
__("Add %s","jetpack-publicize-pkg"),M);return React.createElement("div",{className:(0,r.A)(v.A.wrapper,c,{[v.A["is-bundle-card"]]:w,[v.A["is-card"]]:R||w})},w&&React.createElement(h.z,null),React.createElement("div",{className:v.A["card-container"]},React.createElement(g.l,{icon:E,products:j?.length?j:[S],size:32}),React.createElement(d.H3,null,M),_&&React.createElement(d.hE,{mb:3},_),f&&React.createElement(d.Ay,{mb:3},f),React.createElement("ul",{className:v.A.features},k.map(((e,t)=>React.createElement(d.Ay,{component:"li",key:`feature-${t}`,variant:"body"},React.createElement(s.A,{icon:o.A,size:24,className:v.A.check}),e)))),B&&React.createElement(u.A,{price:N,offPrice:P,currency:L}),x&&React.createElement(d.H3,null,__("Free","jetpack-publicize-pkg")),React.createElement(l.A,{level:"error",showIcon:!!b},b),t,(!w||w&&!y)&&React.createElement(p.A,a()({onClick:e?null:C,isLoading:A,disabled:A,variant:A||!w?"primary":"secondary",className:v.A["add-button"]},e?{href:e}:{}),n||I),w&&y&&React.createElement("div",{className:v.A["product-has-required-plan"]},React.createElement(m.Nr,{size:36}),React.createElement(d.Ay,null,__("Active on your site","jetpack-publicize-pkg")))))}},22268:(e,t,n)=>{"use strict";n.d(t,{z:()=>r});var c=n(27723),a=n(51113),i=n(77136),s=n(47425),o=n(20184);const __=c.__,r=({title:e=__("Popular upgrade","jetpack-publicize-pkg")})=>React.createElement("div",{className:o.A["card-header"]},React.createElement(a.A,{icon:i.A,className:o.A["product-bundle-icon"],size:24}),React.createElement(s.Ay,{variant:"label"},e))},489:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var c=n(27723),a=n(13022),i=n(47425),s=n(72746),o=n(9921);const __=c.__,r=({price:e,offPrice:t,currency:n="",showNotOffPrice:c=!0,hideDiscountLabel:r=!0,promoLabel:l="",legend:p=__("/month, paid yearly","jetpack-publicize-pkg"),isNotConvenientPrice:m=!1,hidePriceFraction:u=!1,children:d})=>{if(null==e&&null==t||!n)return null;c=c&&null!=t;const g="number"==typeof e&&"number"==typeof t?Math.floor((e-t)/e*100):0,h=!r&&g&&g>0?g+__("% off","jetpack-publicize-pkg"):null;return React.createElement(React.Fragment,null,React.createElement("div",{className:o.A.container},React.createElement("div",{className:(0,a.A)(o.A["price-container"],"product-price_container")},React.createElement(s.g,{value:t??e,currency:n,isOff:!m,hidePriceFraction:u}),c&&React.createElement(s.g,{value:e,currency:n,isOff:!1,hidePriceFraction:u}),h&&React.createElement(i.Ay,{className:(0,a.A)(o.A["promo-label"],"product-price_promo_label")},h))),React.createElement("div",{className:o.A.footer},d||React.createElement(i.Ay,{className:(0,a.A)(o.A.legend,"product-price_legend")},p),l&&React.createElement(i.Ay,{className:(0,a.A)(o.A["promo-label"],"product-price_promo_label")},l)))}},72746:(e,t,n)=>{"use strict";n.d(t,{g:()=>o});var c=n(4567),a=n(13022),i=n(47425),s=n(9921);const o=({value:e,currency:t,isOff:n,hidePriceFraction:o})=>{const r=(0,a.A)(s.A.price,"product-price_price",{[s.A["is-not-off-price"]]:!n}),{symbol:l,integer:p,fraction:m}=(0,c.vA)(e,t),u=!o||!m.endsWith("00");return React.createElement(i.Ay,{className:r,variant:"headline-medium",component:"p"},React.createElement(i.Ay,{className:s.A.symbol,component:"sup",variant:"title-medium"},l),p,u&&React.createElement(i.Ay,{component:"sup",variant:"body-small","data-testid":"PriceFraction"},React.createElement("strong",null,m)))}},56461:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var c=n(28120),a=n.n(c),i=n(51609),s=n.n(i);n(85335);const o=({color:e="#FFFFFF",className:t="",size:n=20})=>{const c=t+" jp-components-spinner",a={width:n,height:n,fontSize:n,borderTopColor:e},i={borderTopColor:e,borderRightColor:e};return s().createElement("div",{className:c},s().createElement("div",{className:"jp-components-spinner__outer",style:a},s().createElement("div",{className:"jp-components-spinner__inner",style:i})))};o.propTypes={color:a().string,className:a().string,size:a().number};const r=o},85879:(e,t,n)=>{"use strict";n.d(t,{A:()=>h});var c=n(96072),a=n.n(c),i=n(56427),s=n(86087),o=n(27723),r=n(13022),l=n(3924),p=n(47425);n(66392);const __=o.__,m=({multipleButtonsLabels:e})=>Array.isArray(e)&&e.length>1?(0,s.createInterpolateElement)((0,o.sprintf)(/* translators: %1$s is button label 1 and %2$s is button label 2 */
__("By clicking <strong>%1$s</strong> or <strong>%2$s</strong>, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-publicize-pkg"),e[0],e[1]),{strong:React.createElement("strong",null),tosLink:React.createElement(g,{slug:"wpcom-tos"}),shareDetailsLink:React.createElement(g,{slug:"jetpack-support-what-data-does-jetpack-sync"})}):(0,s.createInterpolateElement)(__("By clicking the buttons above, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-publicize-pkg"),{tosLink:React.createElement(g,{slug:"wpcom-tos"}),shareDetailsLink:React.createElement(g,{slug:"jetpack-support-what-data-does-jetpack-sync"})}),u=({agreeButtonLabel:e})=>(0,s.createInterpolateElement)((0,o.sprintf)(/* translators: %s is a button label */
__("By clicking <strong>%s</strong>, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-publicize-pkg"),e),{strong:React.createElement("strong",null),tosLink:React.createElement(g,{slug:"wpcom-tos"}),shareDetailsLink:React.createElement(g,{slug:"jetpack-support-what-data-does-jetpack-sync"})}),d=()=>(0,s.createInterpolateElement)(__("By continuing you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site’s data</shareDetailsLink> with us. We’ll check if that email is linked to an existing WordPress.com account or create a new one instantly.","jetpack-publicize-pkg"),{tosLink:React.createElement(g,{slug:"wpcom-tos"}),shareDetailsLink:React.createElement(g,{slug:"jetpack-support-what-data-does-jetpack-sync"})}),g=({slug:e,children:t})=>React.createElement(i.ExternalLink,{className:"terms-of-service__link",href:(0,l.A)(e)},t),h=({className:e,multipleButtons:t,agreeButtonLabel:n,isTextOnly:c,...i})=>React.createElement(p.Ay,a()({className:(0,r.A)(e,"terms-of-service")},i),c?React.createElement(d,null):t?React.createElement(m,{multipleButtonsLabels:t}):React.createElement(u,{agreeButtonLabel:n}))},10110:(e,t,n)=>{"use strict";n.d(t,{Q:()=>c,Z:()=>a});const c={"headline-medium":"h1","headline-small":"h2","headline-small-regular":"h2","title-medium":"h3","title-medium-semi-bold":"h3","title-small":"h4",body:"p","body-small":"p","body-extra-small":"p","body-extra-small-bold":"p",label:"p"},a=["mt","mr","mb","ml","mx","my","m","pt","pr","pb","pl","px","py","p"]},47425:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>m,H3:()=>u,hE:()=>d});var c=n(96072),a=n.n(c),i=n(13022),s=n(51609),o=n.n(s),r=n(10110),l=n(22073);const p=(0,s.forwardRef)((({variant:e="body",children:t,component:n,className:c,...p},m)=>{const u=n||r.Q[e]||"span",d=(0,s.useMemo)((()=>r.Z.reduce(((e,t)=>(void 0!==p[t]&&(e+=l.A[`${t}-${p[t]}`]+" ",delete p[t]),e)),"")),[p]);return o().createElement(u,a()({className:(0,i.A)(l.A.reset,l.A[e],c,d)},p,{ref:m}),t)}));p.displayName="Text";const m=p,u=({children:e,weight:t="bold",...n})=>{const c="headline-small"+("bold"===t?"":`-${t}`);return o().createElement(p,a()({variant:c,mb:3},n),e)},d=({children:e,size:t="medium",...n})=>o().createElement(p,a()({variant:`title-${t}`,mb:1},n),e)},50723:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>u});var c=n(51609),a=n.n(c),i=n(25196);const s={"--font-headline-medium":"48px","--font-headline-small":"36px","--font-title-medium":"24px","--font-title-small":"20px","--font-body":"16px","--font-body-small":"14px","--font-body-extra-small":"12px","--font-title-large":"var(--font-headline-small)","--font-label":"var(--font-body-extra-small)"},o={"--jp-black":"#000000","--jp-black-80":"#2c3338","--jp-white":"#ffffff","--jp-white-off":"#f9f9f6","--jp-gray":"#dcdcde","--jp-gray-0":"#F6F7F7","--jp-gray-5":"var(--jp-gray)","--jp-gray-10":"#C3C4C7","--jp-gray-20":"#A7AAAD","--jp-gray-40":"#787C82","--jp-gray-50":"#646970","--jp-gray-60":"#50575E","--jp-gray-70":"#3C434A","--jp-gray-80":"#2C3338","--jp-gray-90":"#1d2327","--jp-gray-off":"#e2e2df","--jp-red-0":"#F7EBEC","--jp-red-5":"#FACFD2","--jp-red-40":"#E65054","--jp-red-50":"#D63638","--jp-red-60":"#B32D2E","--jp-red-70":"#8A2424","--jp-red-80":"#691C1C","--jp-red":"#d63639","--jp-yellow-5":"#F5E6B3","--jp-yellow-10":"#F2CF75","--jp-yellow-20":"#F0C930","--jp-yellow-30":"#DEB100","--jp-yellow-40":"#C08C00","--jp-yellow-50":"#9D6E00","--jp-yellow-60":"#7D5600","--jp-blue-20":"#68B3E8","--jp-blue-40":"#1689DB","--jp-pink":"#C9356E","--jp-green-0":"#f0f2eb","--jp-green-5":"#d0e6b8","--jp-green-10":"#9dd977","--jp-green-20":"#64ca43","--jp-green-30":"#2fb41f","--jp-green-40":"#069e08","--jp-green-50":"#008710","--jp-green-60":"#007117","--jp-green-70":"#005b18","--jp-green-80":"#004515","--jp-green-90":"#003010","--jp-green-100":"#001c09","--jp-green":"#069e08","--jp-green-primary":"var( --jp-green-40 )","--jp-green-secondary":"var( --jp-green-30 )"},r={"--jp-border-radius":"4px","--jp-menu-border-height":"1px","--jp-underline-thickness":"2px"},l={"--spacing-base":"8px"},p={},m=(e,t,n)=>{const c={...s,...o,...r,...l};for(const t in c)e.style.setProperty(t,c[t]);n&&e.classList.add(i.A.global),t&&(p[t]={provided:!0,root:e})},u=({children:e=null,targetDom:t,id:n,withGlobalStyles:i=!0})=>{const s=(0,c.useRef)(),o=p?.[n]?.provided;return(0,c.useLayoutEffect)((()=>{if(!o)return t?m(t,n,i):void(s?.current&&m(s.current,n,i))}),[t,s,o,n,i]),t?a().createElement(a().Fragment,null,e):a().createElement("div",{ref:s},e)}},51437:(e,t,n)=>{"use strict";n.d(t,{Y:()=>a});var c=n(38443);const a=()=>{const{l10n:{locale:e}}=(0,c.getSettings)();if(e)return(e=>{const t=e.match(/^([a-z]{2,3})(_[a-z]{2}|_[a-z][a-z0-9]{4,7})?(?:_.*)?$/i);return t?`${t[1]}${t[2]?t[2]:""}`.replace("_","-"):"en-US"})(e);return window?.window?.navigator?.language??"en-US"}},61069:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var c=n(97999);function a(){return(0,c.getScriptData)()?.site?.admin_url||window.Initial_State?.adminUrl||window.Jetpack_Editor_Initial_State?.adminUrl||window?.myJetpackInitialState?.adminUrl||null}},3924:(e,t,n)=>{"use strict";function c(e,t={}){const n={};let c;if("undefined"!=typeof window&&(c=window?.JP_CONNECTION_INITIAL_STATE?.calypsoEnv),0===e.search("https://")){const t=new URL(e);e=`https://${t.host}${t.pathname}`,n.url=encodeURIComponent(e)}else n.source=encodeURIComponent(e);for(const e in t)n[e]=encodeURIComponent(t[e]);!Object.keys(n).includes("site")&&"undefined"!=typeof jetpack_redirects&&Object.hasOwn(jetpack_redirects,"currentSiteRawUrl")&&(n.site=jetpack_redirects.currentBlogID??jetpack_redirects.currentSiteRawUrl),c&&(n.calypso_env=c);return"https://jetpack.com/redirect/?"+Object.keys(n).map((e=>e+"="+n[e])).join("&")}n.d(t,{A:()=>c})},56439:(e,t,n)=>{let c={};try{c=n(44109)}catch{console.error("jetpackConfig is missing in your webpack config file. See @automattic/jetpack-config"),c={missingConfig:!0}}const a=e=>Object.hasOwn(c,e);e.exports={jetpackConfigHas:a,jetpackConfigGet:e=>{if(!a(e))throw'This app requires the "'+e+'" Jetpack Config to be defined in your webpack configuration file. See details in @automattic/jetpack-config package docs.';return c[e]}}},10966:(e,t,n)=>{"use strict";n.d(t,{A:()=>d});var c=n(7064),a=n(75304),i=n(85879),s=n(39384),o=n(27723),r=n(51609),l=n.n(r),p=n(51459),m=n(33010),u=n(27251);const __=o.__,_x=o._x,d=()=>{const{userIsConnecting:e,siteIsRegistering:t,handleRegisterSite:n,registrationError:o}=(0,s.useConnection)({from:"jetpack-social",redirectUri:"admin.php?page=jetpack-social"}),r=__("Get Started","jetpack-publicize-pkg");return l().createElement(c.A,{className:u.A.card,primary:l().createElement("div",{className:u.A.column},l().createElement(a.A,{className:u.A.offer,slug:"jetpack-social",title:_x("Jetpack Social","Plugin name","jetpack-publicize-pkg"),subTitle:__("Share your posts with your social media network and increase your site’s traffic","jetpack-publicize-pkg"),features:[__("Share to Facebook, Instagram, LinkedIn, Mastodon, Tumblr, Threads, Bluesky, and Nextdoor","jetpack-publicize-pkg"),__("Post to multiple channels at once","jetpack-publicize-pkg"),__("Manage all of your channels from a single hub","jetpack-publicize-pkg")],isCard:!1,isBundle:!1,onAdd:n,buttonText:r,icon:"social",isLoading:t||e,buttonDisclaimer:l().createElement(i.A,{className:u.A["terms-of-service"],agreeButtonLabel:r}),error:o?__("An error occurred. Please try again.","jetpack-publicize-pkg"):null})),secondary:l().createElement("div",{className:u.A.sidebar},l().createElement("img",{className:u.A.background,src:p,alt:""}),l().createElement("img",{className:u.A.illustration,src:m,alt:""}))})}},47893:(e,t,n)=>{"use strict";var c=n(50723),a=n(86087),i=n(51609),s=n.n(i),o=n(18424);!function(){const e=document.getElementById("jetpack-social-root");if(null===e)return;const t=s().createElement(c.Ay,{targetDom:document.body},s().createElement(o.g,null));a.createRoot(e).render(t)}()},10218:(e,t,n)=>{"use strict";n.d(t,{A:()=>k});var c=n(51437),a=n(75918),i=n(28509),s=n(47425),o=n(51112),r=n(78478),l=n(39384),p=n(97999),m=n(47143),u=n(27723),d=n(51113),g=n(30),h=n(31963),v=n(70407),f=n(36759),b=n(71007);const __=u.__,k=()=>{const{hasConnections:e,isModuleEnabled:t,totalSharesCount:n,sharedPostsCount:u,isShareLimitEnabled:k}=(0,m.useSelect)((e=>{const t=e(h.M_);return{hasConnections:t.getConnections().length>0,isModuleEnabled:t.getSocialModuleSettings().publicize,totalSharesCount:t.getTotalSharesCount(),sharedPostsCount:t.getSharedPostsCount(),isShareLimitEnabled:t.isShareLimitEnabled()}})),{urls:y,feature_flags:E}=(0,v.nE)(),w=E.useAdminUiV1,{hasConnectionError:R}=(0,l.useConnectionErrorNotice)(),A=Intl.NumberFormat((0,c.Y)(),{notation:"compact",compactDisplay:"short"}),{openConnectionsModal:C}=(0,m.useDispatch)(h.M_);return React.createElement(React.Fragment,null,React.createElement(a.A,{horizontalSpacing:0},R&&React.createElement(i.A,{className:b.A["connection-error-col"]},React.createElement(l.ConnectionError,null)),React.createElement(i.A,null,React.createElement("div",{id:"jp-admin-notices",className:"jetpack-social-jitm-card"}))),React.createElement(a.A,{horizontalSpacing:3,horizontalGap:3,className:b.A.container},React.createElement(i.A,{sm:4,md:4,lg:5},React.createElement(s.H3,{mt:2},__("Write once, post everywhere","jetpack-publicize-pkg")),React.createElement("div",{className:b.A.actions},t&&!e&&React.createElement(React.Fragment,null,w?React.createElement(o.A,{onClick:C},__("Connect accounts","jetpack-publicize-pkg")):React.createElement(o.A,{href:y.connectionsManagementPage,isExternalLink:!0},__("Connect accounts","jetpack-publicize-pkg"))),React.createElement(o.A,{href:(0,p.getAdminUrl)("post-new.php"),variant:e?"primary":"secondary"},__("Write a post","jetpack-publicize-pkg")))),k?React.createElement(i.A,{sm:4,md:4,lg:{start:7,end:12}},React.createElement(f.A,{stats:[{icon:React.createElement(r.b6,null),label:__("Total shares past 30 days","jetpack-publicize-pkg"),value:A.format(n)},{icon:React.createElement(d.A,{icon:g.A}),label:__("Posted this month","jetpack-publicize-pkg"),value:A.format(u)}]})):null))}},36759:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var c=n(96072),a=n.n(c),i=n(47425),s=n(56461),o=n(9390);const r=({icon:e,label:t,value:n,link:c,loading:a=!1})=>React.createElement("div",{className:o.A.card},e&&e,React.createElement(i.Ay,{className:o.A.label},c?React.createElement("a",{href:c},t):t),a?React.createElement(s.A,{color:"#000",size:24,className:o.A.spinner}):React.createElement(i.Ay,{className:o.A.value,variant:"headline-small"},n)),l=({stats:e})=>React.createElement("div",{className:o.A.cards},e.map((e=>React.createElement(r,a()({},e,{key:e.label})))))},18424:(e,t,n)=>{"use strict";n.d(t,{g:()=>z});var c=n(42947),a=n(75918),i=n(28509),s=n(59244),o=n(90766),r=n(95640),l=n(39384),p=n(97999),m=n(85985),u=n(47143),d=n(86087),g=n(31963),h=n(70407),v=n(10966),f=n(10218),b=n(51733),k=n(87674),y=(n(65067),n(29617)),E=n(53196),w=n(48973),R=n(65534),A=n(40101),C=n(36064);const z=()=>{const e=(0,p.isSimpleSite)(),t=(0,p.isJetpackSelfHostedSite)(),{isUserConnected:n,isRegistered:z}=(0,l.useConnection)(),S=!(e||z&&n),[_,j]=(0,d.useState)(!1),M=(0,d.useCallback)((()=>j(!0)),[]),{isModuleEnabled:x,showPricingPage:N,isUpdatingJetpackSettings:L}=(0,u.useSelect)((e=>{const t=e(g.M_);return{isModuleEnabled:t.getSocialModuleSettings().publicize,showPricingPage:t.getSocialSettings().showPricingPage,isUpdatingJetpackSettings:t.isSavingSocialModuleSettings()}}),[]),{social:P,jetpack:B}=(0,h.nE)().plugin_info,I=P.version?`Jetpack Social ${P.version}`:`Jetpack ${B.version}`,D=(0,p.currentUserCan)("manage_options");return S?React.createElement(c.A,{moduleName:I,showHeader:!1,showBackground:!1,useInternalLinks:(0,m.pg)()},React.createElement(a.A,{horizontalSpacing:3,horizontalGap:3},React.createElement(i.A,null,React.createElement(v.A,null)))):React.createElement(c.A,{moduleName:I,header:React.createElement(k.A,null),showFooter:t,useInternalLinks:(0,m.pg)()},React.createElement(s.D,null),t&&!(0,h.pq)()&&N&&!_?React.createElement(o.A,null,React.createElement(a.A,{horizontalSpacing:3,horizontalGap:3},React.createElement(i.A,null,React.createElement(y.A,{onDismiss:M})))):React.createElement(React.Fragment,null,React.createElement(o.A,null,React.createElement(f.A,null)),React.createElement(r.A,null,React.createElement(R.A,null),D&&React.createElement(React.Fragment,null,x&&React.createElement(C.A,null),P.version&&x&&React.createElement(A.A,{disabled:L}),x&&(0,p.siteHasFeature)(h.qT.IMAGE_GENERATOR)&&React.createElement(w.A,{disabled:L}))),React.createElement(o.A,null,React.createElement(b.A,null)),React.createElement(r.A,null,React.createElement(E.A,null))))}},51733:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var c=n(60442),a=n(75918),i=n(47425),s=n(27723),o=n(13022),r=n(28520);const __=s.__,l=()=>{const[e]=(0,c.A)("lg"),[t]=(0,c.A)("md",">="),n={[r.A["is-viewport-large"]]:e,[r.A["is-viewport-medium"]]:t};return React.createElement(a.A,{className:(0,o.A)(n),horizontalSpacing:7,horizontalGap:3},React.createElement("div",{className:r.A.column},React.createElement(i.Ay,{variant:"title-medium",className:r.A.title},__("Did you know?","jetpack-publicize-pkg")),React.createElement(i.Ay,{variant:"headline-small-regular",component:"span",className:r.A.number},"40x"),React.createElement(i.Ay,null,__("Visual content is 40 times more likely to get shared on social media than any other type. Remember to include an image.","jetpack-publicize-pkg")),React.createElement(i.Ay,{variant:"headline-small-regular",component:"span",className:r.A.number},"10x"),React.createElement(i.Ay,null,__("By publishing at least once per week, you’ll be ahead of 99% of all other sites. Promoting that weekly content on social media may grow your audience by 10x in a few short months.","jetpack-publicize-pkg"))))}},87674:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var c=n(97999),a=n(86087),i=n(27723),s=n(70407),o=n(55999),r=n(54867);const __=i.__,l=()=>{const e=(0,c.isJetpackSelfHostedSite)();return React.createElement("div",{className:r.A.header},React.createElement("span",{className:r.A.logo},React.createElement(o.A,null)),!(0,s.pq)()&&e&&React.createElement("p",null,(0,a.createInterpolateElement)(__("Already have an existing plan or license key? <a>Click here to get started</a>","jetpack-publicize-pkg"),{a:React.createElement("a",{href:(0,c.getMyJetpackUrl)("#/add-license")})})))}},55999:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});const c=({height:e=40})=>React.createElement("svg",{fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 225 40",height:e},React.createElement("path",{d:"M157.947 12.986c0 .569.115 1.041.347 1.417.231.367.54.67.925.912.396.24.849.438 1.36.593.52.154 1.06.298 1.619.433.753.183 1.49.386 2.213.608.733.221 1.384.53 1.953.925a4.352 4.352 0 0 1 1.373 1.519c.357.627.536 1.432.536 2.415 0 1.003-.188 1.87-.564 2.603a4.878 4.878 0 0 1-1.548 1.793c-.656.463-1.441.805-2.357 1.027-.916.232-1.919.347-3.008.347-.502 0-1.017-.033-1.548-.1-.52-.059-1.022-.14-1.504-.247a11.65 11.65 0 0 1-1.287-.361 4.923 4.923 0 0 1-.911-.405V24.31c.347.174.738.333 1.171.477.434.145.883.27 1.345.376.463.097.931.174 1.403.232.473.057.916.086 1.331.086.675 0 1.316-.058 1.923-.173a5.033 5.033 0 0 0 1.62-.579c.463-.27.829-.636 1.099-1.099.27-.472.405-1.06.405-1.764 0-.579-.121-1.056-.362-1.432a2.777 2.777 0 0 0-.94-.925 5.43 5.43 0 0 0-1.374-.608 27.818 27.818 0 0 0-1.619-.434 39.125 39.125 0 0 1-2.213-.578 7.22 7.22 0 0 1-1.923-.911 4.478 4.478 0 0 1-1.36-1.49c-.337-.607-.506-1.383-.506-2.328 0-.984.173-1.827.521-2.531a4.906 4.906 0 0 1 1.417-1.765 6.006 6.006 0 0 1 2.097-1.026 9.545 9.545 0 0 1 2.56-.333c1.031 0 1.991.101 2.878.304a13.37 13.37 0 0 1 2.458.766v2.184a16.7 16.7 0 0 0-2.487-.824 11.733 11.733 0 0 0-2.734-.333c-.732 0-1.369.082-1.909.246-.54.154-.988.38-1.345.68-.356.289-.621.646-.795 1.07-.174.414-.26.877-.26 1.388ZM181.965 20.362c0-.849-.106-1.6-.319-2.256-.202-.666-.491-1.225-.867-1.678a3.516 3.516 0 0 0-1.316-1.041 3.883 3.883 0 0 0-1.678-.362c-.607 0-1.167.12-1.677.362a3.635 3.635 0 0 0-1.331 1.041c-.366.453-.656 1.012-.868 1.678-.202.655-.303 1.407-.303 2.256 0 .839.101 1.59.303 2.256.212.655.506 1.21.882 1.663.376.453.82.8 1.331 1.041.511.232 1.07.347 1.678.347.607 0 1.161-.115 1.663-.347a3.72 3.72 0 0 0 1.316-1.041c.376-.453.665-1.008.867-1.663.213-.665.319-1.417.319-2.256Zm2.342 0c0 1.08-.154 2.063-.462 2.95-.309.887-.748 1.649-1.316 2.285a5.781 5.781 0 0 1-2.039 1.46c-.801.348-1.697.521-2.69.521-1.022 0-1.938-.173-2.748-.52a5.921 5.921 0 0 1-2.054-1.461c-.559-.636-.988-1.398-1.287-2.285-.299-.887-.448-1.87-.448-2.95 0-1.09.154-2.078.463-2.965.308-.887.742-1.649 1.301-2.285a5.826 5.826 0 0 1 2.054-1.475c.81-.347 1.711-.52 2.704-.52 1.012 0 1.924.173 2.733.52a5.66 5.66 0 0 1 2.054 1.475c.559.636.988 1.398 1.287 2.285.299.887.448 1.875.448 2.965ZM193.603 13.116c.665 0 1.302.073 1.909.217.608.135 1.128.328 1.562.579v1.865a12.518 12.518 0 0 0-1.678-.52 7.045 7.045 0 0 0-1.561-.174c-.56 0-1.114.092-1.664.275a3.823 3.823 0 0 0-1.475.911c-.434.415-.786.969-1.055 1.663-.261.685-.391 1.538-.391 2.56 0 .742.096 1.427.289 2.054.203.626.497 1.171.882 1.634.396.453.887.81 1.476 1.07.588.25 1.267.376 2.039.376.559 0 1.123-.058 1.692-.174a10.074 10.074 0 0 0 1.663-.52v1.865a3.898 3.898 0 0 1-.651.29c-.26.096-.549.178-.868.245a7.877 7.877 0 0 1-1.012.174 7.981 7.981 0 0 1-1.07.072c-.926 0-1.803-.14-2.632-.419a5.89 5.89 0 0 1-2.155-1.287c-.617-.579-1.104-1.311-1.461-2.198-.356-.887-.535-1.938-.535-3.153 0-.906.092-1.721.275-2.444.193-.723.453-1.36.781-1.91a5.928 5.928 0 0 1 1.142-1.402 6.524 6.524 0 0 1 1.403-.94 6.15 6.15 0 0 1 1.548-.535 7.253 7.253 0 0 1 1.547-.174ZM200.686 27.217v-13.74h2.242v13.74h-2.242Zm-.058-16.935V7.78h2.358v2.502h-2.358ZM215.318 25.698h-.058a4.682 4.682 0 0 1-.694.694 5.502 5.502 0 0 1-.969.608 6.271 6.271 0 0 1-1.2.42 5.84 5.84 0 0 1-1.389.158 5.354 5.354 0 0 1-1.836-.303 4.042 4.042 0 0 1-1.432-.854 4.057 4.057 0 0 1-.926-1.402c-.221-.55-.332-1.172-.332-1.866 0-.685.12-1.297.361-1.837.251-.54.598-.998 1.042-1.374a4.68 4.68 0 0 1 1.59-.867 6.704 6.704 0 0 1 2.069-.304c.713.01 1.373.072 1.981.188.607.116 1.147.26 1.62.434h.057v-.955a6.15 6.15 0 0 0-.072-.997 2.541 2.541 0 0 0-.246-.767c-.25-.463-.65-.844-1.2-1.143-.55-.308-1.292-.462-2.227-.462-.694 0-1.35.067-1.967.202-.607.125-1.215.304-1.822.535v-1.88c.231-.116.501-.222.81-.318.318-.106.65-.193.997-.26a9.388 9.388 0 0 1 1.1-.174c.385-.038.771-.058 1.157-.058 1.388 0 2.521.246 3.398.738.887.492 1.523 1.142 1.909 1.952.145.309.246.651.304 1.027.067.366.101.771.101 1.215v9.169h-1.88l-.246-1.519Zm-.116-4.512a17.448 17.448 0 0 0-1.475-.332 11.13 11.13 0 0 0-1.88-.174c-.954 0-1.701.202-2.241.607-.54.405-.81 1.027-.81 1.866 0 .424.067.795.202 1.114.135.318.318.583.55.795.241.202.52.357.839.463.327.096.675.144 1.041.144.482 0 .93-.067 1.345-.202a5.82 5.82 0 0 0 1.099-.506c.328-.193.603-.386.824-.579.232-.193.4-.342.506-.448v-2.748ZM221.794 27.217V6.276h2.241v20.94h-2.241Z",fill:"#000"}),React.createElement("path",{d:"M20 40c11.047 0 20.002-8.955 20.002-20C40.002 8.952 31.047 0 20 0 8.955 0 0 8.955 0 20c0 11.047 8.955 20 20 20Z",fill:"#069E08"}),React.createElement("path",{d:"M20.993 16.642V36.03l10-19.39h-10ZM18.97 23.321V3.97L9.008 23.321h9.962Z",fill:"#fff"}),React.createElement("path",{d:"M51.642 33.206c-.574-.878-1.107-1.755-1.642-2.595 2.825-1.718 3.779-3.09 3.779-5.687v-15h-3.32V7.061h7.061v17.1c0 4.35-1.259 6.793-5.879 9.044ZM81.223 22.978c0 1.45 1.031 1.602 1.719 1.602.687 0 1.68-.23 2.442-.457v2.671c-1.07.344-2.176.61-3.703.61-1.832 0-3.97-.686-3.97-3.893v-7.863h-1.945v-2.71h1.946V8.93h3.511v4.008h4.427v2.71h-4.427v7.329ZM88.552 34.542V12.9h3.359v1.298c1.336-1.031 2.825-1.68 4.657-1.68 3.168 0 5.687 2.214 5.687 6.985 0 4.734-2.747 7.863-7.29 7.863-1.106 0-1.985-.152-2.901-.343v7.48h-3.512v.04ZM95.65 15.42c-1.031 0-2.328.496-3.55 1.565v7.367c.762.153 1.565.267 2.633.267 2.48 0 3.894-1.566 3.894-4.848 0-3.015-1.031-4.351-2.977-4.351ZM116.071 27.061h-3.282v-1.565h-.077c-1.145.878-2.558 1.832-4.657 1.832-1.832 0-3.817-1.336-3.817-4.047 0-3.625 3.091-4.313 5.267-4.618l3.091-.419v-.419c0-1.91-.763-2.52-2.558-2.52-.878 0-2.938.267-4.618.954l-.305-2.824c1.527-.535 3.626-.915 5.382-.915 3.435 0 5.649 1.374 5.649 5.458v9.083h-.075Zm-3.512-6.602-2.902.457c-.878.114-1.793.649-1.793 1.946 0 1.145.649 1.794 1.602 1.794 1.031 0 2.138-.61 3.091-1.298v-2.9h.002ZM130.575 26.604c-1.45.496-2.747.8-4.39.8-5.267 0-7.367-3.015-7.367-7.403 0-4.618 2.902-7.481 7.595-7.481 1.755 0 2.825.305 4.008.687v2.977c-1.031-.382-2.519-.801-3.969-.801-2.138 0-3.97 1.145-3.97 4.427 0 3.626 1.832 4.733 4.161 4.733 1.106 0 2.328-.23 3.969-.878v2.939h-.037ZM137.216 19.008c.306-.343.535-.687 4.962-6.07h4.58l-5.726 6.72 6.259 7.442h-4.58l-5.458-6.719V27.1h-3.51V7.062h3.512v11.946h-.039ZM73.13 26.603c-1.831.574-3.397.802-5.229.802-4.504 0-7.29-2.252-7.29-7.52 0-3.855 2.367-7.367 6.908-7.367 4.504 0 6.07 3.13 6.07 6.107 0 .992-.078 1.527-.115 2.099h-9.083c.077 3.09 1.832 3.816 4.465 3.816 1.45 0 2.748-.343 4.238-.878v2.939h.037v.002Zm-3.204-8.206c0-1.718-.573-3.207-2.443-3.207-1.755 0-2.824 1.259-3.054 3.207h5.497Z",fill:"#000"}))},29617:(e,t,n)=>{"use strict";n.d(t,{A:()=>f});var c=n(60442),a=n(79245),i=n(489),s=n(51112),o=n(3924),r=n(97999),l=n(56427),p=n(47143),m=n(27723),u=n(51609),d=n(71763),g=n(31963),h=n(51371),v=n(11476);const __=m.__,_x=m._x,f=({onDismiss:e})=>{const[t]=(0,d.A)(),n=(0,r.getScriptData)().site.wpcom.blog_id,m=(0,r.getScriptData)().site.suffix,{setShowPricingPage:f,updateSocialModuleSettings:b}=(0,p.useDispatch)(g.M_),[k]=(0,c.A)("lg"),y=(0,p.useSelect)((e=>e(g.M_).isSavingSocialModuleSettings()),[]),{is_publicize_enabled:E}=(0,h.nE)(),w=(0,u.useCallback)((async()=>{if(E||await b({publicize:!0}),f(!1),!E)return window.location.reload();e()}),[b,f,e,E]);return React.createElement(a.Ay,{showIntroOfferDisclaimer:!0,title:__("Write once, post everywhere","jetpack-publicize-pkg"),items:[{name:__("Priority support","jetpack-publicize-pkg")},{name:__("Schedule posting","jetpack-publicize-pkg")},{name:__("Share to Facebook, Instagram, LinkedIn, Mastodon, Tumblr, Threads, Bluesky, and Nextdoor","jetpack-publicize-pkg")},{name:__("Customize publications","jetpack-publicize-pkg")},{name:__("Recycle content","jetpack-publicize-pkg"),tooltipInfo:__("Repurpose, reuse or republish already published content.","jetpack-publicize-pkg")},{name:__("Upload custom images with your posts","jetpack-publicize-pkg")},{name:__("Upload videos with your posts","jetpack-publicize-pkg")},{name:__("Automatically generate images for posts","jetpack-publicize-pkg"),tooltipInfo:__("Automatically create custom images, saving you hours of tedious work.","jetpack-publicize-pkg")},{name:__("Multi-image sharing","jetpack-publicize-pkg"),tooltipTitle:__("Coming soon","jetpack-publicize-pkg"),tooltipInfo:__("Share multiple images at once on social media platforms.","jetpack-publicize-pkg")}]},React.createElement(a.N0,{primary:!0},React.createElement(a.i7,null,t?.v1?React.createElement(i.A,{price:t?.v1?.price,offPrice:t?.v1?.introOffer,legend:__("per month for the first year, then billed yearly","jetpack-publicize-pkg"),currency:t?.currencyCode,hidePriceFraction:!0}):React.createElement(l.Spinner,{className:v.A.spinner}),React.createElement(s.A,{href:(0,o.A)("jetpack-social-v1-plan-plugin-admin-page",{site:n?n.toString():m,query:"redirect_to=admin.php?page=jetpack-social"}),fullWidth:!0},__("Get Social","jetpack-publicize-pkg"))),React.createElement(a.eY,{isIncluded:!0}),React.createElement(a.eY,{isIncluded:!0}),React.createElement(a.eY,{isIncluded:!0}),React.createElement(a.eY,{isIncluded:!0}),React.createElement(a.eY,{isIncluded:!0}),React.createElement(a.eY,{isIncluded:!0}),React.createElement(a.eY,{isIncluded:!0}),React.createElement(a.eY,{isIncluded:!0}),React.createElement(a.eY,{isIncluded:!1,isComingSoon:!0})),React.createElement(a.N0,null,React.createElement(a.i7,null,React.createElement(i.A,{price:0,legend:"",currency:t?.currencyCode||"USD",hidePriceFraction:!0}),React.createElement(s.A,{fullWidth:!0,variant:"secondary",onClick:w,className:k&&v.A.button,disabled:y},y?__("Please wait…","jetpack-publicize-pkg"):_x("Start for free","Pricing page CTA for Social admin page","jetpack-publicize-pkg"))),React.createElement(a.eY,{isIncluded:!1}),React.createElement(a.eY,{isIncluded:!0}),React.createElement(a.eY,{isIncluded:!0}),React.createElement(a.eY,{isIncluded:!0}),React.createElement(a.eY,{isIncluded:!0}),React.createElement(a.eY,{isIncluded:!1}),React.createElement(a.eY,{isIncluded:!1}),React.createElement(a.eY,{isIncluded:!1}),React.createElement(a.eY,{isIncluded:!1})))}},84132:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var c=n(60442),a=n(47425),i=n(13022),s=n(32221);const o=({icon:e,title:t,children:n})=>{const[o,r]=(0,c.A)(["sm","md"],[">",">"]),l=(0,i.A)(s.A.column,{[s.A["viewport-gt-small"]]:o,[s.A["viewport-gt-medium"]]:r});return React.createElement("div",{className:l},React.createElement("div",{className:s.A.icon},e),React.createElement(a.Ay,{className:s.A.title,variant:"title-medium"},t),React.createElement("div",{className:s.A.text},n))}},53196:(e,t,n)=>{"use strict";n.d(t,{A:()=>v});var c=n(60442),a=n(75918),i=n(47425),s=n(3924),o=n(97999),r=n(56427),l=n(27723),p=n(51113),m=n(41496),u=n(13022),d=n(70407),g=n(84132),h=n(59333);const __=l.__,v=()=>{const[e]=(0,c.A)("md",">=");if(!(0,d.pq)())return null;const t=(0,o.isJetpackSelfHostedSite)();return React.createElement(a.A,{horizontalSpacing:7,horizontalGap:3,className:(0,u.A)({[h.A["is-viewport-medium"]]:e})},React.createElement(g.A,{icon:React.createElement(p.A,{icon:m.A,size:30,className:h.A.icon}),title:__("World-class support","jetpack-publicize-pkg")},React.createElement(i.Ay,null,__("Do you need any help? Get in touch with our world-class support with a high-priority support ticket and get a solution faster.","jetpack-publicize-pkg")),React.createElement(i.Ay,{className:h.A.link},React.createElement(r.ExternalLink,{href:t?(0,s.A)("jetpack-contact-support"):(0,s.A)("wpcom-contact-support")},__("Contact Support","jetpack-publicize-pkg")))))}},48973:(e,t,n)=>{"use strict";n.d(t,{A:()=>h});var c=n(60442),a=n(51112),i=n(47425),s=n(47143),o=n(86087),r=n(27723),l=n(51609),p=n.n(l),m=n(31963),u=n(12226),d=n(68814),g=n(6600);const __=r.__,h=({disabled:e})=>{const{isEnabled:t,isUpdating:n,defaultTemplate:r}=(0,s.useSelect)((e=>{const t=e(m.M_).getSocialSettings().socialImageGenerator;return{isEnabled:t.enabled,defaultTemplate:t.template,isUpdating:e(m.M_).isSavingSiteSettings()}}),[]),{updateSocialImageGeneratorConfig:l}=(0,s.useDispatch)(m.M_),h=(0,o.useCallback)((()=>{l({enabled:!t})}),[t,l]),v=(0,o.useCallback)((e=>{l({template:e})}),[l]),[f]=(0,c.A)("sm"),b=(0,o.useCallback)((({open:e})=>p().createElement(a.A,{fullWidth:f,className:g.A.button,variant:"secondary",disabled:n||!t,onClick:e},__("Change default template","jetpack-publicize-pkg"))),[t,f,n]);return p().createElement(d.A,{title:__("Enable Social Image Generator","jetpack-publicize-pkg"),disabled:n||e,checked:t,onChange:h},p().createElement(i.Ay,{className:g.A.text},__("When enabled, Social Image Generator will automatically generate social images for your posts. You can use the button below to choose a default template for new posts. This feature is only supported in the block editor.","jetpack-publicize-pkg")),p().createElement(u.A,{value:r,onSelect:v,render:b}))}},65534:(e,t,n)=>{"use strict";n.d(t,{A:()=>E});var c=n(60442),a=n(51112),i=n(47425),s=n(3924),o=n(94437),r=n(97999),l=n(56427),p=n(47143),m=n(27723),u=n(13022),d=n(51609),g=n.n(d),h=n(31963),v=n(70407),f=n(48625),b=n(26637),k=n(68814),y=n(86949);const __=m.__,_x=m._x,E=()=>{const{isModuleEnabled:e,isUpdating:t}=(0,p.useSelect)((e=>{const t=e(h.M_);return{isModuleEnabled:t.getSocialModuleSettings().publicize,isUpdating:t.isSavingSocialModuleSettings()}}),[]),{wpcom:n,host:m,suffix:E}=(0,r.getScriptData)().site,w="wpcom"===m,{urls:R,feature_flags:A}=(0,v.nE)(),C=A.useAdminUiV1,{updateSocialModuleSettings:z}=(0,p.useDispatch)(h.M_),S=(0,d.useCallback)((async()=>{const t={publicize:!e};await z(t),t.publicize&&!(0,v.nE)().is_publicize_enabled&&window.location.reload()}),[e,z]),[_]=(0,c.A)("sm"),j=!(0,f.E)();return g().createElement(k.A,{hideToggle:j,title:__("Automatically share your posts to social networks","jetpack-publicize-pkg"),disabled:t,checked:e,onChange:S},g().createElement(i.Ay,{className:y.A.text},j?__("Connect your social media accounts and send a post’s featured image and content to the selected channels with a single click when the post is published.","jetpack-publicize-pkg"):_x("When enabled, you’ll be able to connect your social media accounts and send a post’s featured image and content to the selected channels with a single click when the post is published.","Description of the feature that the toggle enables","jetpack-publicize-pkg")," ",g().createElement(l.ExternalLink,{href:w?(0,s.A)("wpcom-social-plugin-publicize-support-admin-page"):(0,s.A)("social-plugin-publicize-support-admin-page"),className:y.A.learn},__("Learn more","jetpack-publicize-pkg"))),(0,r.isWpcomPlatformSite)()||(0,v.pq)()?null:g().createElement(o.A,{className:(0,u.A)(y.A.cut,{[y.A.small]:_}),description:__("Unlock advanced sharing options","jetpack-publicize-pkg"),cta:__("Power up Jetpack Social","jetpack-publicize-pkg"),href:(0,s.A)("jetpack-social-admin-page-upsell",{site:`${n.blog_id??E}`,query:"redirect_to=admin.php?page=jetpack-social"}),tooltipText:__("Share custom images and videos that capture attention, use our powerful Social Image Generator to create stunning visuals, and access priority support for expert help whenever you need it.","jetpack-publicize-pkg")}),C?e?g().createElement(b.A,{className:y.A["connection-management"],disabled:t}):null:R.connectionsManagementPage?g().createElement(a.A,{fullWidth:_,className:y.A.button,variant:"secondary",isExternalLink:!0,href:R.connectionsManagementPage,disabled:t||!e,target:"_blank"},__("Manage social media connections","jetpack-publicize-pkg")):null)}},40101:(e,t,n)=>{"use strict";n.d(t,{A:()=>f});var c=n(60442),a=n(47425),i=n(51112),s=n(97999),o=n(56427),r=n(47143),l=n(86087),p=n(27723),m=n(51609),u=n.n(m),d=n(31963),g=n(68814),h=n(21904);const __=p.__,v=async(e,t)=>{t?.(!0),document.body.style.cursor="wait",await e(),t?.(!1),document.body.style.cursor="auto"},f=({disabled:e})=>{const{isEnabled:t,notesConfig:n,isUpdating:p}=(0,r.useSelect)((e=>{const t=e(d.M_);return{isEnabled:t.getSocialSettings().socialNotes.enabled,notesConfig:t.getSocialSettings().socialNotes.config,isUpdating:t.isSavingSiteSettings()}}),[]),f=(0,s.getAdminUrl)("post-new.php?post_type=jetpack-social-note"),[b,k]=(0,m.useState)(!1),[y,E]=(0,m.useState)(!1),[w]=(0,c.A)("sm"),{toggleSocialNotes:R,updateSocialNotesConfig:A}=(0,r.useDispatch)(d.M_),C=(0,l.useCallback)((async()=>{v((()=>R(!t)))}),[t,R]),z=(0,l.useCallback)((e=>{v((()=>A({...n,append_link:e})),k)}),[n,A]),S=(0,l.useCallback)((e=>{v((()=>A({...n,link_format:e})),E)}),[n,A]),_=n.append_link??!0;return u().createElement(g.A,{title:__("Enable Social Notes","jetpack-publicize-pkg"),beta:!0,disabled:p||e,checked:t,onChange:C},!t&&u().createElement("style",null,"#adminmenu #menu-posts-jetpack-social-note { display: none; }"),u().createElement(a.Ay,{className:h.A.text},__("Do you want to quickly share what's on your mind? Turn on Social Notes to effortlessly jot down and share quick notes without the need for titles or formatting, enabling swift and spontaneous communication with your followers.","jetpack-publicize-pkg")),u().createElement(i.A,{className:h.A.button,fullWidth:w,variant:"secondary",disabled:p||!t,href:f},__("Create a note","jetpack-publicize-pkg")),t?u().createElement("div",{className:h.A["notes-options-wrapper"]},u().createElement(o.ToggleControl,{label:__("Append post link","jetpack-publicize-pkg"),checked:_,disabled:b||y||p,className:h.A.toggle,onChange:z,help:__("Whether to append the post link when sharing a note.","jetpack-publicize-pkg"),__nextHasNoMarginBottom:!0}),_?u().createElement(o.SelectControl,{label:__("Link format","jetpack-publicize-pkg"),value:n.link_format??"full_url",onChange:S,disabled:y||p||b,options:[{label:__("Full URL","jetpack-publicize-pkg"),value:"full_url"},{label:__("Shortlink","jetpack-publicize-pkg"),value:"shortlink"},{label:__("Permashortcitation","jetpack-publicize-pkg"),value:"permashortcitation"}],help:u().createElement("span",null,__("Format of the link to use when sharing a note.","jetpack-publicize-pkg")," ",u().createElement(o.ExternalLink,{href:"https://jetpack.com/redirect/?source=jetpack-social-notes-link-format"},__("Learn more","jetpack-publicize-pkg"))),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}):null):null)}},68814:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var c=n(75918),a=n(47425),i=n(56427),s=n(51609),o=n.n(s),r=n(38745);const l=({title:e,beta:t,onChange:n,checked:s,disabled:l,children:p,hideToggle:m})=>o().createElement(c.A,{horizontalSpacing:7,horizontalGap:3},o().createElement("div",{className:`${r.A.column} ${m?r.A.notoggle:""}`},!m&&o().createElement(i.ToggleControl,{label:"",className:r.A.toggle,disabled:l,checked:s,onChange:n,__nextHasNoMarginBottom:!0}),o().createElement(a.Ay,{className:r.A.title,variant:"title-medium"},e,t&&o().createElement("div",{className:r.A.beta},"Beta")),p))},36064:(e,t,n)=>{"use strict";n.d(t,{A:()=>u});var c=n(47425),a=n(47143),i=n(86087),s=n(27723),o=n(51609),r=n.n(o),l=n(31963),p=n(68814),m=n(45027);const __=s.__,u=({disabled:e})=>{const{isEnabled:t,isUpdating:n}=(0,a.useSelect)((e=>({isEnabled:e(l.M_).getSocialSettings().utmSettings.enabled,isUpdating:e(l.M_).isSavingSiteSettings()})),[]),{updateUtmSettings:s}=(0,a.useDispatch)(l.M_),o=(0,i.useCallback)((()=>{s({enabled:!t})}),[t,s]);return r().createElement(p.A,{title:__("Append UTM parameters to shared URLs","jetpack-publicize-pkg"),disabled:n||e,checked:t,onChange:o},r().createElement(c.Ay,{className:m.A.text},__("UTM parameters are tags added to links to help track where website visitors come from, improving our understanding of how content is shared. Don't worry, it doesn't change the experience or the link destination!","jetpack-publicize-pkg")))}},12141:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var c=n(78478),a=n(86087),i=n(13022),s=n(28120),o=n.n(s);const r=e=>{const{checked:t,serviceName:n,label:s,onClick:o,profilePicture:r}=e,[l,p]=(0,a.useState)(!!r),m=(0,a.useCallback)((()=>p(!1)),[]),u=(0,a.useCallback)((e=>{13===e.keyCode&&o()}),[o]);return React.createElement("div",{onClick:o,onKeyDown:u,role:"switch","aria-checked":t,tabIndex:"0",className:(0,i.A)("components-connection-icon",{"components-connection-icon__picture":l})},l&&React.createElement("img",{src:r,alt:s,onError:m}),React.createElement(c.M5,{alt:s,serviceName:"instagram-business"===n?"instagram":"twitter"===n?"x":n,className:"jetpack-publicize-gutenberg-social-icon"}))};r.propTypes={serviceName:o().string,label:o().string,profilePicture:o().string};const l=r},55488:(e,t,n)=>{"use strict";n.d(t,{T:()=>y});var c=n(51112),a=n(40597),i=n(47425),s=n(56427),o=n(47143),r=n(27723),l=n(51113),p=n(98248),m=n(64969),u=n(51609),d=n(31963),g=n(12141),h=n(27817),v=n(86158),f=n(43119),b=n(52683),k=n(85605);const __=r.__,_x=r._x;function y({connection:e,service:t,canMarkAsShared:n}){const[r,y]=(0,u.useReducer)((e=>!e),!1),E=(0,o.useSelect)((t=>t(d.M_).canUserManageConnection(e)),[e]);return React.createElement(React.Fragment,null,React.createElement("div",{className:k.A["connection-item"]},React.createElement(g.A,{serviceName:e.service_name,label:e.display_name,profilePicture:e.profile_picture}),React.createElement("div",{className:k.A["connection-name-wrapper"]},React.createElement("div",{className:k.A["connection-item-name"]},React.createElement(h.s,{connection:e})),React.createElement(v.v,{connection:e,service:t})),React.createElement(c.A,{size:"small",className:k.A["learn-more"],variant:"tertiary",onClick:y,"aria-label":r?__("Close panel","jetpack-publicize-pkg"):_x("Open panel","Accessibility label","jetpack-publicize-pkg")},React.createElement(l.A,{className:k.A.chevron,icon:r?p.A:m.A}))),React.createElement(s.Panel,{className:k.A["connection-panel"]},React.createElement(s.PanelBody,{opened:r,onToggle:y},n&&React.createElement("div",{className:k.A["mark-shared-wrap"]},React.createElement(b.j,{connection:e}),React.createElement(a.A,null,__("If enabled, the connection will be available to all administrators, editors, and authors.","jetpack-publicize-pkg"))),E?React.createElement(f.V,{connection:e}):React.createElement(i.Ay,{className:k.A.description},__("This connection is added by a site administrator.","jetpack-publicize-pkg")))))}},27817:(e,t,n)=>{"use strict";n.d(t,{s:()=>r});var c=n(56427),a=n(47143),i=n(27723),s=n(31963),o=n(85605);const __=i.__;function r({connection:e}){const t=(0,a.useSelect)((t=>t(s.M_).getUpdatingConnections().includes(e.connection_id)),[e.connection_id]);return React.createElement("div",{className:o.A["connection-name"]},e.profile_link?React.createElement(c.ExternalLink,{className:o.A["profile-link"],href:e.profile_link},e.display_name):React.createElement("span",{className:o.A["profile-link"]},e.display_name),t?React.createElement(c.Spinner,{color:"black","aria-label":__("Updating account","jetpack-publicize-pkg")}):null)}},86158:(e,t,n)=>{"use strict";n.d(t,{v:()=>m});var c=n(3924),a=n(56427),i=n(47143),s=n(86087),o=n(27723),r=n(31963),l=n(43119),p=n(76046);const __=o.__,_x=o._x;function m({connection:e,service:t}){const n=(0,i.useSelect)((t=>t(r.M_).getServicesBy("status","unsupported").some((({id:t})=>t===e.service_name))),[e]);return"broken"===e.status||"must_reauth"===e.status||n?React.createElement("div",null,React.createElement("span",{className:"description"},(m=n,u=e.status,m?(0,s.createInterpolateElement)((0,o.sprintf)("%1$s %2$s",__("This platform is no longer supported.","jetpack-publicize-pkg"),__("You can use our <link>Manual Sharing</link> feature instead.","jetpack-publicize-pkg")),{link:React.createElement(a.ExternalLink,{href:(0,c.A)("jetpack-social-manual-sharing-help")})}):"broken"===u?_x("There is an issue with this connection.","This notice is shown when a social media connection is broken.","jetpack-publicize-pkg"):_x("To keep sharing with this connection, please reconnect it.","This notice is shown when a social media connection needs to be reconnected.","jetpack-publicize-pkg")))," ",!n&&t?React.createElement(p.C,{connection:e,service:t}):React.createElement(l.V,{connection:e,variant:"link",isDestructive:!1})):null;var m,u}},43119:(e,t,n)=>{"use strict";n.d(t,{V:()=>p});var c=n(51112),a=n(56427),i=n(47143),s=n(86087),o=n(27723),r=n(31963),l=n(85605);const __=o.__,_x=o._x;function p({connection:e,variant:t="secondary",isDestructive:n=!0,buttonClassName:p}){const[m,u]=(0,s.useReducer)((e=>!e),!1),{deleteConnectionById:d}=(0,i.useDispatch)(r.M_),{isDisconnecting:g,canManageConnection:h}=(0,i.useSelect)((t=>{const{getDeletingConnections:n,canUserManageConnection:c}=t(r.M_);return{isDisconnecting:n().includes(e.connection_id),canManageConnection:c(e)}}),[e]),v=(0,s.useCallback)((async()=>{u(),await d({connectionId:e.connection_id})}),[e.connection_id,d]);return h?React.createElement(React.Fragment,null,React.createElement(a.__experimentalConfirmDialog,{className:l.A.confirmDialog,isOpen:m,onConfirm:v,onCancel:u,cancelButtonText:__("Cancel","jetpack-publicize-pkg"),confirmButtonText:__("Yes","jetpack-publicize-pkg")},(0,s.createInterpolateElement)((0,o.sprintf)(
// translators: %s: The name of the connection the user is disconnecting.
__("Are you sure you want to disconnect <strong>%s</strong>?","jetpack-publicize-pkg"),e.display_name),{strong:React.createElement("strong",null)})),React.createElement(c.A,{size:"small",onClick:u,disabled:g,variant:t,isDestructive:n,className:p},g?__("Disconnecting…","jetpack-publicize-pkg"):_x("Disconnect","Disconnect a social media account","jetpack-publicize-pkg"))):null}},26637:(e,t,n)=>{"use strict";n.d(t,{A:()=>v});var c=n(51112),a=n(56427),i=n(47143),s=n(86087),o=n(27723),r=n(13022),l=n(50001),p=n(21259),m=n(31963),u=n(48222),d=n(48712),g=n(55488),h=n(85605);const __=o.__,v=({className:e=null,disabled:t=!1})=>{const{refresh:n}=(0,l.A)(),{connections:o,deletingConnections:v,updatingConnections:f}=(0,i.useSelect)((e=>{const{getConnections:t,getDeletingConnections:n,getUpdatingConnections:c}=e(m.M_);return{connections:t(),deletingConnections:n(),updatingConnections:c()}}),[]);o.sort(((e,t)=>e.service_name===t.service_name?e.connection_id.localeCompare(t.connection_id):e.service_name.localeCompare(t.service_name))),(0,s.useEffect)((()=>{n()}),[n]);const b=(0,d.h)(),{openConnectionsModal:k}=(0,i.useDispatch)(m.M_),y=(0,p._)();return React.createElement("div",{className:(0,r.A)(h.A.wrapper,e),inert:t?"true":void 0},o.length?React.createElement(React.Fragment,null,React.createElement("h3",null,__("Connected accounts","jetpack-publicize-pkg")),React.createElement("ul",{className:h.A["connection-list"]},o.map((e=>{const t=f.includes(e.connection_id)||v.includes(e.connection_id);return React.createElement("li",{className:h.A["connection-list-item"],key:e.connection_id},React.createElement(a.Disabled,{isDisabled:t},React.createElement(g.T,{connection:e,service:b(e.service_name),canMarkAsShared:y})))})))):null,React.createElement(u._,null),React.createElement(c.A,{variant:o.length?"secondary":"primary",onClick:k},__("Connect an account","jetpack-publicize-pkg")))}},52683:(e,t,n)=>{"use strict";n.d(t,{j:()=>r});var c=n(56427),a=n(47143),i=n(86087),s=n(27723),o=n(31963);const __=s.__;function r({connection:e}){const{updateConnectionById:t}=(0,a.useDispatch)(o.M_),{isUpdating:n}=(0,a.useSelect)((t=>{const{getUpdatingConnections:n}=t(o.M_);return{isUpdating:n().includes(e.connection_id)}}),[e.connection_id]),s=(0,i.useCallback)((n=>{t(e.connection_id,{shared:n})}),[e.connection_id,t]);return React.createElement(c.CheckboxControl,{checked:e.shared??!1,onChange:s,disabled:n||"broken"===e.status,label:__("Mark the connection as shared","jetpack-publicize-pkg"),__nextHasNoMarginBottom:!0})}},76046:(e,t,n)=>{"use strict";n.d(t,{C:()=>l});var c=n(51112),a=n(47143),i=n(86087),s=n(27723),o=n(31963),r=n(70745);const __=s.__,_x=s._x;function l({connection:e,service:t,variant:n="link"}){const{deleteConnectionById:s,setKeyringResult:l,openConnectionsModal:p,setReconnectingAccount:m}=(0,a.useDispatch)(o.M_),{isDisconnecting:u,canManageConnection:d}=(0,a.useSelect)((t=>{const{getDeletingConnections:n,canUserManageConnection:c}=t(o.M_);return{isDisconnecting:n().includes(e.connection_id),canManageConnection:c(e)}}),[e]),g=(0,i.useCallback)((e=>{l(e),e?.ID&&p()}),[p,l]),h=(0,r.P)({service:t,onConfirm:g}),v=(0,i.useCallback)((async()=>{if(!await s({connectionId:e.connection_id,showSuccessNotice:!1}))return;await m(e);const n=new FormData;"mastodon"===t.id&&n.set("instance",e.external_handle),"bluesky"===t.id?p():h(n)}),[e,s,p,h,t.id,m]);return d?React.createElement(c.A,{size:"small",onClick:v,disabled:u,variant:n},u?__("Disconnecting…","jetpack-publicize-pkg"):_x("Reconnect","Reconnect a social media account","jetpack-publicize-pkg")):null}},59493:(e,t,n)=>{"use strict";n.d(t,{O:()=>h});var c=n(63406),a=n(3924),i=n(51112),s=n(56427),o=n(47143),r=n(86087),l=n(27723),p=n(51475),m=n(31963),u=n(39630),d=n(18973);const __=l.__,_x=l._x;function g({label:e,profile_picture:t}){return React.createElement("div",{className:d.A["account-info"]},t?React.createElement("img",{className:d.A["profile-pic"],src:t,alt:e}):null,React.createElement("span",null,e))}function h({keyringResult:e,onComplete:t,canMarkAsShared:n}){const l=(0,u.o)(),{existingConnections:h,reconnectingAccount:v}=(0,o.useSelect)((e=>{const t=e(m.M_);return{existingConnections:t.getConnections(),reconnectingAccount:t.getReconnectingAccount()}}),[]),{createErrorNotice:f}=(0,c.I)(),b=l.find((t=>t.id===e.service)),k=(0,r.useCallback)((e=>h.some((t=>t.service_name===b?.id&&t.external_id===e))),[h,b.id]),y=(0,r.useMemo)((()=>{const t=[],n=[];if(!b)return{connected:t,not_connected:n};const c=[];if(b.supports.additional_users_only||c.push({label:e.external_display||e.external_name,value:e.external_ID,profile_picture:e.external_profile_picture}),b.supports.additional_users&&e.additional_external_users?.length)for(const t of e.additional_external_users)c.push({label:t.external_name,value:t.external_ID,profile_picture:t.external_profile_picture});for(const e of c)k(e.value)?t.push(e):n.push(e);return{connected:t,not_connected:n}}),[k,e,b]),{createConnection:E,setReconnectingAccount:w}=(0,o.useDispatch)(m.M_),R=(0,r.useCallback)((async n=>{n.preventDefault(),n.stopPropagation();const c=n.target,a=new FormData(c),i=a.get("external_user_ID");if(!i)return void f(__("Please select an account to connect.","jetpack-publicize-pkg"));const s={external_user_ID:b.supports.additional_users?i:void 0,keyring_connection_ID:e.ID,shared:"1"===a.get("shared")||void 0},o=y.not_connected.find((e=>e.value===i));v&&w(void 0),E(s,{display_name:o?.label,profile_picture:o?.profile_picture,service_name:b.id,external_id:i.toString()}),t()}),[E,v,w,f,e.ID,t,b.supports,b.id,y.not_connected]);return React.createElement("section",{className:d.A.confirmation},y.not_connected.length?React.createElement("div",null,React.createElement("p",{className:d.A["header-text"]},__("Select the account you'd like to connect. All your new blog posts will be automatically shared to this account. You'll be able to change this option in the editor sidebar when you're writing a post.","jetpack-publicize-pkg")),e?.show_linkedin_warning&&React.createElement(p.A,{type:"warning"},React.createElement("p",null,__("We could not retrieve which company pages you have access to. This is a known issue with the LinkedIn API. If you would like to connect a company page, please retry after 5 minutes.","jetpack-publicize-pkg")," ",React.createElement(s.ExternalLink,{key:"linkedin-api-documentaion",href:(0,a.A)("jetpack-linkedin-permissions-warning")},__("Learn more","jetpack-publicize-pkg")))),React.createElement("form",{className:d.A.form,onSubmit:R,id:"connection-confirmation-form"},React.createElement("div",{className:d.A["accounts-list"]},y.not_connected.map(((e,t)=>{const n=v?v.service_name===b?.id&&v.external_id===e.value:0===t;return React.createElement("label",{key:e.value,htmlFor:`external_user_ID__${e.value}`,className:d.A["account-label"],"aria-required":!0},React.createElement("input",{type:"radio",id:`external_user_ID__${e.value}`,name:"external_user_ID",value:e.value,defaultChecked:n,className:d.A["account-input"],required:!0}),React.createElement(g,{label:e.label,profile_picture:e.profile_picture}))}))),n?React.createElement(s.BaseControl,{__nextHasNoMarginBottom:!0,id:"mark-connection-as-shared",help:`${__("If enabled, the connection will be available to all administrators, editors, and authors.","jetpack-publicize-pkg")} ${__("You can change this later.","jetpack-publicize-pkg")}`},React.createElement(s.__experimentalHStack,{justify:"flex-start",spacing:3},React.createElement("span",null,React.createElement("input",{type:"checkbox",id:"mark-connection-as-shared",name:"shared",value:"1"})),React.createElement(s.FlexBlock,{as:"label",htmlFor:"mark-connection-as-shared"},__("Mark the connection as shared","jetpack-publicize-pkg")))):null,React.createElement("input",{type:"hidden",name:"keyring_connection_ID",value:e.ID}))):React.createElement("p",{className:d.A["header-text"]},y.connected.length?_x("No more accounts/pages found.","Message shown when there are no connections found to connect","jetpack-publicize-pkg"):__("No accounts/pages found.","jetpack-publicize-pkg")),y.connected.length?React.createElement("section",null,React.createElement("h3",null,__("Already connected","jetpack-publicize-pkg")),React.createElement("ul",null,y.connected.map(((e,t)=>React.createElement("li",{key:e.label+t},React.createElement(g,{label:e.label,profile_picture:e.profile_picture})))))):null,React.createElement("div",{className:d.A["submit-wrap"]},React.createElement(i.A,{variant:"secondary",onClick:t},__("Cancel","jetpack-publicize-pkg")),y.not_connected.length?React.createElement(i.A,{form:"connection-confirmation-form",type:"submit"},__("Confirm","jetpack-publicize-pkg")):null))}},48222:(e,t,n)=>{"use strict";n.d(t,{_:()=>b});var c=n(60442),a=n(47425),i=n(3924),s=n(50723),o=n(56427),r=n(47143),l=n(86087),p=n(27723),m=n(13022),u=n(21259),d=n(31963),g=n(78362),h=n(59493),v=n(73842);const __=p.__,_x=p._x,f=()=>{const{keyringResult:e}=(0,r.useSelect)((e=>{const{getKeyringResult:t}=e(d.M_);return{keyringResult:t()}}),[]),{setKeyringResult:t,closeConnectionsModal:n,setReconnectingAccount:s}=(0,r.useDispatch)(d.M_),[p]=(0,c.A)("sm"),f=(0,l.useCallback)((()=>{t(null),s(void 0),n()}),[n,t,s]),b=Boolean(e?.ID),k=b?__("Connection confirmation","jetpack-publicize-pkg"):_x("Manage Jetpack Social connections","","jetpack-publicize-pkg"),y=(0,u._)();return React.createElement(o.Modal,{className:(0,m.A)(v.A.modal,{[v.A.small]:p}),onRequestClose:f,title:k},b?React.createElement(h.O,{keyringResult:e,onComplete:f,canMarkAsShared:y}):React.createElement(React.Fragment,null,React.createElement(g.b,null),React.createElement("div",{className:v.A["manual-share"]},React.createElement("em",null,React.createElement(a.Ay,null,__("Want to share to other networks? Use our Manual Sharing feature from the editor.","jetpack-publicize-pkg")," ",React.createElement(o.ExternalLink,{href:(0,i.A)("jetpack-social-manual-sharing-help")},__("Learn more","jetpack-publicize-pkg")))))))};function b(){const e=(0,r.useSelect)((e=>e(d.M_).isConnectionsModalOpen()),[]);return React.createElement(s.Ay,{targetDom:document.body},e?React.createElement(f,null):null)}},51475:(e,t,n)=>{"use strict";n.d(t,{A:()=>u});var c=n(56427),a=n(27723),i=n(51113),s=n(31249),o=n(13022),r=n(28120),l=n.n(r),p=n(93794);const __=a.__,m=({children:e,type:t="default",actions:n=[],onDismiss:a})=>{const r=(0,o.A)(p.A.notice,p.A[`notice--${t}`]);return React.createElement("div",{className:r},React.createElement("div",null," ",e," "),a&&React.createElement("button",{className:p.A.dismiss,onClick:a},React.createElement(c.VisuallyHidden,null,__("Dismiss notice","jetpack-publicize-pkg")),React.createElement(i.A,{icon:s.A})),n&&n.length>0&&React.createElement("div",{className:p.A.actions},n.map((e=>e))))};m.propTypes={children:l().node.isRequired,type:l().oneOf(["default","highlight","warning","error"]),actions:l().arrayOf(l().element),onDismiss:l().func};const u=m},53774:(e,t,n)=>{"use strict";n.d(t,{$:()=>u});var c=n(51112),a=n(47143),i=n(86087),s=n(27723),o=n(13022),r=n(31963),l=n(75018),p=n(42475),m=n(70745);const __=s.__,_x=s._x;function u({service:e,isSmall:t,onSubmit:n,displayInputs:s,hasConnections:u,buttonLabel:d}){const{setKeyringResult:g}=(0,a.useDispatch)(r.M_),{isConnectionsModalOpen:h}=(0,a.useSelect)((e=>e(r.M_)),[]),[v,f]=(0,i.useState)(!1),b=(0,a.useSelect)((e=>e(r.M_).isFetchingServicesList()),[]),k=(0,i.useCallback)((e=>{h()&&g(e)}),[g,h]),y=(0,m.P)({service:e,onConfirm:k}),E=(0,i.useCallback)((async e=>{if(e.preventDefault(),e.stopPropagation(),n)return n();f(!0);const t=new FormData(e.target);await y(t)}),[n,y]);return React.createElement("form",{className:(0,o.A)(p.A["connect-form"],{[p.A.small]:t}),onSubmit:E},s?React.createElement("div",{className:(0,o.A)(p.A["fields-wrapper"],p.A.input)},React.createElement(l.t,{service:e})):null,React.createElement("div",{className:p.A["fields-wrapper"]},React.createElement("div",{className:p.A["fields-item"]},React.createElement(c.A,{variant:u?"secondary":"primary",type:"submit",className:p.A["connect-button"],disabled:b},d||(b&&v?__("Connecting…","jetpack-publicize-pkg"):u?_x("Connect more","","jetpack-publicize-pkg"):__("Connect","jetpack-publicize-pkg"))))))}},75018:(e,t,n)=>{"use strict";n.d(t,{t:()=>m});var c=n(42266),a=n(56427),i=n(47143),s=n(86087),o=n(27723),r=n(13022),l=n(31963),p=n(42475);const __=o.__,_x=o._x;function m({service:e}){const t=(0,s.useId)(),[n,m]=(0,s.useState)(null),u=(0,i.useSelect)((e=>e(l.M_).getReconnectingAccount()),[]),d=(0,s.useCallback)((e=>{if(e.endsWith(".bsky.social")){if(e.replace(".bsky.social","").includes("."))return m((0,o.sprintf)(/* translators: %s is the handle suffix like .bsky.social */
__('Bluesky usernames cannot contain dots. If you are using a custom domain, enter it without "%s"',"jetpack-publicize-pkg"),".bsky.social")),!1}return m(null),!0}),[]),g=(0,s.useCallback)((e=>{d(e.target.value)}),[d]);return"mastodon"===e.id?React.createElement("div",{className:p.A["fields-item"]},React.createElement("label",{htmlFor:`${t}-handle`},_x("Handle","The handle of a social media account.","jetpack-publicize-pkg")),React.createElement("input",{id:`${t}-handle`,required:!0,type:"text",name:"instance",autoComplete:"off",autoCapitalize:"off",autoCorrect:"off",spellCheck:"false","aria-label":__("Mastodon handle","jetpack-publicize-pkg"),"aria-describedby":`${t}-handle-description`,placeholder:"@<EMAIL>"}),React.createElement("p",{className:"description",id:`${t}-handle-description`},__("You can find the handle in your Mastodon profile.","jetpack-publicize-pkg"))):"bluesky"===e.id?React.createElement(React.Fragment,null,React.createElement("div",{className:p.A["fields-item"]},React.createElement("label",{htmlFor:`${t}-handle`},_x("Handle","The handle of a social media account.","jetpack-publicize-pkg")),React.createElement("input",{id:`${t}-handle`,required:!0,type:"text",name:"handle",defaultValue:"bluesky"===u?.service_name?u?.external_handle:void 0,autoComplete:"off",autoCapitalize:"off",autoCorrect:"off",spellCheck:"false","aria-label":__("Bluesky handle","jetpack-publicize-pkg"),"aria-describedby":`${t}-handle-description`,placeholder:"username.bsky.social",onChange:g,className:n?p.A.error:void 0}),React.createElement("p",{className:(0,r.A)("description",n&&p.A["error-text"]),id:`${t}-handle-description`},n||React.createElement(React.Fragment,null,__("You can find the handle in your Bluesky profile.","jetpack-publicize-pkg")," ",(0,s.createInterpolateElement)((0,o.sprintf)(/* translators: %s is the bluesky handle suffix like .bsky.social */
__("This can either be %s or just the domain name if you are using a custom domain.","jetpack-publicize-pkg"),"<strong>username.bsky.social</strong>"),{strong:React.createElement("strong",null)})))),React.createElement("div",{className:p.A["fields-item"]},React.createElement("label",{htmlFor:`${t}-password`},__("App password","jetpack-publicize-pkg")),React.createElement("input",{id:`${t}-password`,required:!0,type:"password",name:"app_password",autoComplete:"off",autoCapitalize:"off",autoCorrect:"off",spellCheck:"false","aria-label":__("App password","jetpack-publicize-pkg"),"aria-describedby":`${t}-password-description`,placeholder:"xxxx-xxxx-xxxx-xxxx"}),React.createElement("p",{className:"description",id:`${t}-password-description`},(0,s.createInterpolateElement)(__("App password is needed to safely connect your account. App password is different from your account password. You can <link>generate it in Bluesky</link>.","jetpack-publicize-pkg"),{link:React.createElement(a.ExternalLink,{href:"https://bsky.app/settings/app-passwords"})})),"bluesky"===u?.service_name&&React.createElement(c.A,{level:"error",showIcon:!1},__("Please provide an app password to fix the connection.","jetpack-publicize-pkg")))):null}},77088:(e,t,n)=>{"use strict";n.d(t,{e:()=>d});var c=n(40597),a=n(47425),i=n(47143),s=n(27723),o=n(31963),r=n(27817),l=n(86158),p=n(43119),m=n(52683),u=n(42475);const __=s.__,d=({connection:e,service:t,canMarkAsShared:n})=>{const s=(0,i.useSelect)((t=>t(o.M_).canUserManageConnection(e)),[e]);return React.createElement("div",{className:u.A["service-connection"]},React.createElement("div",null,e.profile_picture?React.createElement("img",{className:u.A["profile-pic"],src:e.profile_picture,alt:e.display_name}):React.createElement(t.icon,{iconSize:40})),React.createElement("div",{className:u.A["connection-details"]},React.createElement(r.s,{connection:e}),"broken"!==(d=e).status&&"must_reauth"!==d.status||!s?n?React.createElement("div",{className:u.A["mark-shared-wrap"]},React.createElement(m.j,{connection:d}),React.createElement(c.A,{placement:"top",inline:!1,shift:!0},__("If enabled, the connection will be available to all administrators, editors, and authors.","jetpack-publicize-pkg"))):s?null:React.createElement(React.Fragment,null,React.createElement(a.Ay,{className:u.A.description},__("This connection is added by a site administrator.","jetpack-publicize-pkg")),"broken"===d.status?React.createElement(l.v,{connection:d,service:t}):null):React.createElement(l.v,{connection:d,service:t})),React.createElement("div",{className:u.A["connection-actions"]},React.createElement(p.V,{connection:e,isDestructive:!1,variant:"tertiary",buttonClassName:u.A.disconnect})));var d}},46985:(e,t,n)=>{"use strict";n.d(t,{x:()=>m});var c=n(60442),a=n(56427),i=n(47143),s=n(13022),o=n(21259),r=n(31963),l=n(77088),p=n(42475);function m({service:e,serviceConnections:t}){const[n]=(0,c.A)("sm"),{deletingConnections:m,updatingConnections:u}=(0,i.useSelect)((e=>{const{getDeletingConnections:t,getUpdatingConnections:n}=e(r.M_);return{deletingConnections:t(),updatingConnections:n()}}),[]),d=(0,o._)();return t.length?React.createElement("ul",{className:p.A["service-connection-list"]},t.map((t=>{const n=u.includes(t.connection_id)||m.includes(t.connection_id);return React.createElement("li",{key:t.connection_id},React.createElement(a.Disabled,{isDisabled:n},React.createElement(l.e,{connection:t,service:e,canMarkAsShared:d})))}))):React.createElement("div",{className:(0,s.A)(p.A["example-wrapper"],{[p.A.small]:n})},e.examples.map(((t,n)=>React.createElement("div",{key:e.id+n,className:p.A.example},React.createElement(t,null)))))}},59008:(e,t,n)=>{"use strict";n.d(t,{j:()=>f});var c=n(60442),a=n(51112),i=n(56427),s=n(47143),o=n(86087),r=n(27723),l=n(51113),p=n(98248),m=n(64969),u=n(31963),d=n(53774),g=n(46985),h=n(21159),v=n(42475);const __=r.__,_x=r._x;function f({service:e,serviceConnections:t,isPanelDefaultOpen:n}){const[r]=(0,c.A)("sm"),[f,b]=(0,o.useReducer)((e=>!e),n),k=(0,o.useRef)(null);(0,o.useEffect)((()=>{n&&k.current?.scrollIntoView({block:"center",behavior:"smooth"})}),[]);const y=f&&e.needsCustomInputs,E=t.filter((({status:e})=>"broken"===e)),w=t.filter((({status:e})=>"must_reauth"===e)),R=(0,s.useSelect)((e=>{const{canUserManageConnection:t}=e(u.M_);return E.some(t)}),[E]),A=y||R&&f,C=E.length>1?_x("Fix connections","Fix the social media connections","jetpack-publicize-pkg"):_x("Fix connection","Fix social media connection","jetpack-publicize-pkg");return React.createElement("div",{className:v.A["service-item"]},React.createElement("div",{className:v.A["service-item-info"]},React.createElement("div",null,React.createElement(e.icon,{iconSize:r?36:48})),React.createElement("div",{className:v.A["service-basics"]},React.createElement("div",{className:v.A.heading},React.createElement("span",{className:v.A.title},e.label),e.badges?.length?React.createElement("div",{className:v.A.badges},e.badges.map((({text:e,style:t},n)=>React.createElement("span",{key:n,className:v.A.badge,style:t},e)))):null),r||t.length?null:React.createElement("span",{className:v.A.description},e.description),React.createElement(h.E,{serviceConnections:t,brokenConnections:E,reauthConnections:w})),React.createElement("div",{className:v.A.actions},A?null:React.createElement(d.$,{service:e,isSmall:r,onSubmit:R||e.needsCustomInputs?b:void 0,hasConnections:t.length>0,buttonLabel:R?C:void 0}),React.createElement(a.A,{size:"small",className:v.A["learn-more"],variant:"tertiary",onClick:b,"aria-label":__("Learn more","jetpack-publicize-pkg")},React.createElement(l.A,{className:v.A.chevron,icon:f?p.A:m.A})))),React.createElement(i.Panel,{className:v.A["service-panel"],ref:k},React.createElement(i.PanelBody,{opened:f,onToggle:b},React.createElement(g.x,{service:e,serviceConnections:t}),e.needsCustomInputs&&!R?React.createElement("div",{className:v.A["connect-form-wrapper"]},React.createElement(d.$,{service:e,displayInputs:!0,isSmall:!1,buttonLabel:__("Connect","jetpack-publicize-pkg")})):null)))}},21159:(e,t,n)=>{"use strict";n.d(t,{E:()=>r});var c=n(42266),a=n(47143),i=n(27723),s=n(31963),o=n(42475);const __=i.__,_n=i._n;function r({serviceConnections:e,brokenConnections:t,reauthConnections:n}){const r=(0,a.useSelect)((e=>t.some(e(s.M_).canUserManageConnection)||n.some(e(s.M_).canUserManageConnection)),[t,n]);if(!e.length)return null;if(t.length||n.length){let e;return e=t.length?r?__("Please fix the broken connections or disconnect them to create more connections.","jetpack-publicize-pkg"):_n("Broken connection","Broken connections",t.length,"jetpack-publicize-pkg"):r?__("Reconnect to continue sharing.","jetpack-publicize-pkg"):_n("Expiring connection","Expiring connections",n.length,"jetpack-publicize-pkg"),React.createElement(c.A,{level:r?"error":"warning",showIcon:!1,className:o.A["broken-connection-alert"]},e)}return React.createElement("span",{className:o.A["active-connection"]},e.length>1?(0,i.sprintf)(
// translators: %d: Number of connections
__("%d connections","jetpack-publicize-pkg"),e.length):__("Connected","jetpack-publicize-pkg"))}},78362:(e,t,n)=>{"use strict";n.d(t,{b:()=>l});var c=n(47143),a=n(86087),i=n(31963),s=n(59008),o=n(42475),r=n(39630);function l(){const e=(0,r.o)(),t=(0,c.useSelect)((e=>e(i.M_).getConnections()),[]),n=(0,a.useMemo)((()=>t.reduce(((e,t)=>(e[t.service_name]||(e[t.service_name]=[]),e[t.service_name].push(t),e)),{})),[t]),l=(0,c.useSelect)((e=>e(i.M_).getReconnectingAccount()),[]);return React.createElement("ul",{className:o.A.services},e.map((e=>React.createElement("li",{key:e.id,className:o.A["service-list-item"]},React.createElement(s.j,{service:e,serviceConnections:n[e.id]||[],isPanelDefaultOpen:l?.service_name===e.id})))))}},70745:(e,t,n)=>{"use strict";n.d(t,{P:()=>p});var c=n(63406),a=n(47143),i=n(86087),s=n(27723),o=n(31963),r=n(70407);const __=s.__,l=e=>/^@?\b([A-Z0-9_]+)@([A-Z0-9.-]+\.[A-Z]{2,})$/gi.test(e);function p({service:e,onConfirm:t}){const{createErrorNotice:n}=(0,c.I)(),s=(0,a.useSelect)((e=>e(o.M_).isMastodonAccountAlreadyConnected),[]),p=(0,a.useSelect)((e=>e(o.M_).isBlueskyAccountAlreadyConnected),[]),{refreshServicesList:m}=(0,a.useDispatch)(o.M_),{getService:u}=(0,a.useSelect)((e=>e(o.M_)),[]);return(0,i.useCallback)((async c=>{let a=e.url;if(!a){await m();do{await new Promise((e=>setTimeout(e,100))),a=u(e.id)?.url}while(!a)}const i=new URL(a);switch(e.id){case"mastodon":{const e=c.get("instance").toString().trim();if(!l(e))return void n(__("Invalid Mastodon username","jetpack-publicize-pkg"));if(s?.(e))return void n(__("This Mastodon account is already connected","jetpack-publicize-pkg"));i.searchParams.set("instance",e);break}case"bluesky":{const e=(c.get("handle")?.toString()||"").trim().replace(/^@/,"");if(!function(e){const t=e.split(".").filter(Boolean);return!(t.length<2)&&t.every((e=>/^[a-z0-9_-]+$/i.test(e)))}(e))return void n(__("Invalid Bluesky handle","jetpack-publicize-pkg"));if(p?.(e))return void n(__("This Bluesky account is already connected","jetpack-publicize-pkg"));i.searchParams.set("handle",e),i.searchParams.set("app_password",(c.get("app_password")?.toString()||"").trim());break}}(0,r.x9)(i.toString(),t)}),[n,u,p,s,t,m,e])}},48712:(e,t,n)=>{"use strict";n.d(t,{h:()=>i});var c=n(51609),a=n(39630);function i(){const e=(0,a.o)(),t=(0,c.useMemo)((()=>e.reduce(((e,t)=>(e[t.id]=t,e)),{})),[e]);return(0,c.useCallback)((e=>t[e]),[t])}},39630:(e,t,n)=>{"use strict";n.d(t,{o:()=>f});var c=n(96072),a=n.n(c),i=n(78478),s=n(56427),o=n(47143),r=n(86087),l=n(27723),p=n(30081),m=n(26818),u=n(43443),d=n(11326),g=n(65398),h=n(9791),v=n(31963);const __=l.__;function f(){const e=(0,o.useSelect)((e=>e(v.M_).getServicesList()),[]),t=(0,r.useMemo)((()=>e.reduce(((e,t)=>({...e,[t.id]:t})),{})),[e]),n={text:__("New","jetpack-publicize-pkg"),style:{background:"#e9eff5",color:"#0675C4"}};return[{...t.facebook,icon:e=>React.createElement(i.M5,a()({serviceName:"facebook"},e)),description:__("Share to your pages","jetpack-publicize-pkg"),examples:[()=>React.createElement(React.Fragment,null,(0,r.createInterpolateElement)(__("<strong>Connect</strong> to automatically share posts on your Facebook page.","jetpack-publicize-pkg"),{strong:React.createElement("strong",null)})),()=>React.createElement("img",{src:p,alt:__("Add Facebook connection","jetpack-publicize-pkg")})]},{...t["instagram-business"],icon:e=>React.createElement(i.M5,a()({serviceName:"instagram"},e)),description:__("Share to your Instagram Business account.","jetpack-publicize-pkg"),examples:[()=>React.createElement(React.Fragment,null,__("Drive engagement and save time by automatically sharing images to Instagram when you publish blog posts.","jetpack-publicize-pkg"),React.createElement("div",{className:"instagram-business__requirements"},React.createElement("h4",null,__("Requirements for connecting Instagram:","jetpack-publicize-pkg")),React.createElement("ol",null,React.createElement("li",null,__("You must have an Instagram Business account.","jetpack-publicize-pkg")),React.createElement("li",null,__("Your Instagram Business account must be linked to a Facebook page.","jetpack-publicize-pkg")))),(0,r.createInterpolateElement)(__("<i>When you click “connect” you'll be asked to <strong>log into Facebook</strong>. If your Instagram Business account isn't listed, ensure it's linked to a Facebook page.</i>","jetpack-publicize-pkg"),{strong:React.createElement("strong",null),i:React.createElement("em",null)}),React.createElement("br",null),React.createElement("br",null),React.createElement(s.ExternalLink,{className:"instagram-business__help-link",href:"https://jetpack.com/redirect/?source=jetpack-social-instagram-business-help"},__("Learn how to convert & link your Instagram account.","jetpack-publicize-pkg"))),()=>React.createElement("img",{src:m,alt:__("Add Instagram photo","jetpack-publicize-pkg")})]},{...t.threads,icon:e=>React.createElement(i.M5,a()({serviceName:"threads"},e)),description:__("Share posts to your Threads feed.","jetpack-publicize-pkg"),examples:[()=>React.createElement(React.Fragment,null,__("Increase your presence in social media by sharing your posts automatically to Threads.","jetpack-publicize-pkg")),()=>React.createElement("img",{src:g,alt:__("Add Threads connection","jetpack-publicize-pkg")})]},{...t.bluesky,needsCustomInputs:!0,icon:e=>React.createElement(i.M5,a()({serviceName:"bluesky"},e)),badges:[n],description:__("Share with your network.","jetpack-publicize-pkg"),examples:[()=>React.createElement(React.Fragment,null,__("To share to Bluesky please enter your Bluesky handle and app password below, then click connect.","jetpack-publicize-pkg"))]},{...t.linkedin,icon:e=>React.createElement(i.M5,a()({serviceName:"linkedin"},e)),description:__("Share with your LinkedIn community.","jetpack-publicize-pkg"),examples:[()=>React.createElement(React.Fragment,null,(0,r.createInterpolateElement)(__("<strong>Connect</strong> to automatically share posts with your LinkedIn connections.","jetpack-publicize-pkg"),{strong:React.createElement("strong",null)})),()=>React.createElement("img",{src:u,alt:__("Add LinkedIn connection","jetpack-publicize-pkg")})]},{...t.nextdoor,icon:e=>React.createElement(i.M5,a()({serviceName:"nextdoor"},e)),description:__("Share on communities","jetpack-publicize-pkg"),examples:[()=>React.createElement(React.Fragment,null,(0,r.createInterpolateElement)(__("<strong>Connect</strong> with friends, neighbors, and local businesses by automatically sharing your posts to Nextdoor.","jetpack-publicize-pkg"),{strong:React.createElement("strong",null)})),()=>React.createElement("img",{src:d,alt:__("Add Instagram photo","jetpack-publicize-pkg")})]},{...t.tumblr,icon:e=>React.createElement(i.M5,a()({serviceName:"tumblr-alt"},e)),description:__("Share to your Tumblr blog.","jetpack-publicize-pkg"),examples:[()=>React.createElement(React.Fragment,null,(0,r.createInterpolateElement)(__("<strong>Connect</strong> to automatically share posts to your Tumblr blog.","jetpack-publicize-pkg"),{strong:React.createElement("strong",null)})),()=>React.createElement("img",{src:h,alt:__("Add Tumblr connection","jetpack-publicize-pkg")})]},{...t.mastodon,needsCustomInputs:!0,icon:e=>React.createElement(i.M5,a()({serviceName:"mastodon"},e)),description:__("Share with your network.","jetpack-publicize-pkg"),examples:[()=>React.createElement(React.Fragment,null,__("To share to Mastodon please enter your Mastodon username below, then click connect.","jetpack-publicize-pkg"))]}].filter((e=>Boolean(e.id)))}},12226:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var c=n(50723),a=n(56427),i=n(86087),s=n(27723),o=n(81299),r=n(88375);const __=s.__,l=({onSelect:e,render:t,value:n=null})=>{const[s,l]=(0,i.useState)(!1),[p,m]=(0,i.useState)(n),u=(0,i.useCallback)((()=>l(!0)),[l]),d=(0,i.useCallback)((()=>{l(!1)}),[l]),g=(0,i.useCallback)((()=>{e(p),l(!1)}),[e,l,p]);return React.createElement(c.Ay,{targetDom:document.body},t({open:u}),s&&React.createElement(a.Modal,{onRequestClose:d,title:__("Pick a Template","jetpack-publicize-pkg")},React.createElement(o.A,{value:p,onTemplateSelected:m}),React.createElement("div",{className:r.A.footer},React.createElement(a.Button,{variant:"tertiary",onClick:d},__("Cancel","jetpack-publicize-pkg")),React.createElement(a.Button,{variant:"primary",onClick:g},__("Save","jetpack-publicize-pkg")))))}},81299:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var c=n(86087),a=n(27723),i=n(13022),s=n(52002),o=n(10936);const __=a.__,r=({value:e=null,onTemplateSelected:t=null})=>{const n=(0,c.useCallback)((e=>{const n=e.target.id;t?.(n)}),[t]);return React.createElement("div",{className:s.A.templates},o.A.map((t=>React.createElement("button",{onClick:n,id:t.name,key:t.name,className:(0,i.A)(s.A.template,{[s.A["template--active"]]:t.name===e})},React.createElement("img",{src:t.image,alt:t.label}),React.createElement("span",{className:"screen-reader-text"},(0,a.sprintf)(/* translators: %s is the name of the template */
__("Pick the %s template","jetpack-publicize-pkg"),t.label))))))}},10936:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var c=n(26365),a=n(45329),i=n(9509);const s=[{name:"highway",label:"Highway",image:n(22417)},{name:"dois",label:"Dois",image:c},{name:"edge",label:"Edge",image:a},{name:"fullscreen",label:"Fullscreen",image:i}]},2204:(e,t,n)=>{"use strict";const c="video/mp4",a="video/mov",i="video/videopress",s=["image/jpeg","image/jpg","image/png"],o=s.concat(["image/gif","image/bmp"]),r=["video/3g2","video/3gp","video/3gpp","video/asf","video/avi","video/dat","video/divx","video/dv","video/f4v","video/flv","video/gif","video/m2ts","video/m4v","video/mkv","video/mod","video/mov","video/mp4","video/mpe","video/mpeg","video/mpeg4","video/mpg","video/mts","video/nsv","video/ogm","video/ogv","video/qt","video/tod","video/ts","video/vob","video/wmv"],l=s.concat(["image/gif","image/heic","image/heif","image/webp","image/avif"]),p=["video/webm","video/quicktime","video/ogg"],m=s.concat(["image/gif","image/jpe","image/tif","image/tiff","image/webp"]),u=[a,"video/avi","video/mpg","video/mpeg","video/m4v"];s.concat([c,i,a]),s.concat([c,i]),o.concat([i,...r]),s.concat([c,a,i]),s.concat([c,i]),l.concat([...p,c,i]),m.concat([...u,c,i]),s.concat([c,i,a,"video/webm","video/mpeg"]),new Set([...s,...o,...l,...r,...p,"image/png","image/jpeg","image/jpg","image/heic","image/heif","image/webp"])},71763:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var c=n(1455),a=n.n(c),i=n(93832),s=n(51609);const o=e=>({price:e.full_price/12,introOffer:e.introductory_offer?e.introductory_offer.cost_per_interval/12:null}),r=e=>({currencyCode:e.currency_code||"USD",v1:o(e)});function l(){const[e,t]=(0,s.useState)(null),n=(0,s.useCallback)((async()=>{try{const e=await a()({path:(0,i.addQueryArgs)("/my-jetpack/v1/site/products",{products:"social"})}),n=e?.social?.pricing_for_ui;n&&t(r(n))}catch{t(null)}}),[]);return(0,s.useEffect)((()=>{n()}),[n]),[e]}},50001:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var c=n(47143),a=n(51609),i=n(31963);function s(){const{refreshConnectionTestResults:e,toggleConnectionById:t}=(0,c.useDispatch)(i.M_),n=(0,c.useSelect)((e=>{const t=e(i.M_),n=t.getConnections(),c=t.getEnabledConnections(),a=t.getDisabledConnections();return{connections:n,hasConnections:n.length>0,hasEnabledConnections:c.length>0,disabledConnections:a,enabledConnections:c}}),[]),s=(0,a.useMemo)((()=>n.disabledConnections.map((e=>e.connection_id))),[n.disabledConnections]);return(0,a.useMemo)((()=>({...n,skippedConnections:s,toggleById:t,refresh:e})),[n,e,s,t])}},21259:(e,t,n)=>{"use strict";n.d(t,{_:()=>s});var c=n(97999),a=n(3582),i=n(47143);function s(){return(0,i.useSelect)((e=>{const{getUser:t}=e(a.store),{current_user:n}=(0,c.getScriptData)().user;return Boolean(t(n.id)?.capabilities?.edit_others_posts)}),[])}},38716:(e,t,n)=>{"use strict";n.r(t),n.d(t,{abortRefreshConnectionsRequest:()=>k,abortRequest:()=>b,addConnection:()=>d,closeConnectionsModal:()=>P,createAbortController:()=>v,createConnection:()=>S,deleteConnection:()=>R,deleteConnectionById:()=>C,deletingConnection:()=>A,mergeConnections:()=>h,openConnectionsModal:()=>L,refreshConnectionTestResults:()=>y,removeAbortControllers:()=>f,setConnections:()=>m,setKeyringResult:()=>u,setReconnectingAccount:()=>M,syncConnectionsToPostMeta:()=>E,toggleConnection:()=>g,toggleConnectionById:()=>w,toggleConnectionsModal:()=>N,updateConnection:()=>_,updateConnectionById:()=>x,updatingConnection:()=>j});var c=n(692),a=n(1455),i=n.n(a),s=n(47143),o=n(43656),r=n(27723),l=n(51371),p=n(54118);const __=r.__;function m(e){return{type:p.dA,connections:e}}function u(e){return{type:p.BN,keyringResult:e}}function d(e){return{type:p.Bc,connection:e}}function g(e){return{type:p.af,connectionId:e}}function h(e){return function({dispatch:t,select:n}){const c=n.getConnections(),a=[],i={enabled:!0};for(const t of e){const e={...i,...c.find((e=>e.connection_id===t.connection_id)),...t};a.push(e)}t(m(a))}}function v(e,t){return{type:p.WB,requestType:t,abortController:e}}function f(e){return{type:p.Wp,requestType:e}}function b(e){return function({dispatch:t,select:n}){const c=n.getAbortControllers(e);for(const e of c)e.abort();t(f(e))}}function k(){return b(p.rz)}function y(e=!1){return async function({dispatch:t,select:n}){try{const c=(0,l.nE)().api_paths.refreshConnections;for(;n.getUpdatingConnections().length>0||n.getDeletingConnections().length>0;)await new Promise((e=>setTimeout(e,100)));const a=new AbortController;t(v(a,p.rz));t(h(await i()({path:c,signal:a.signal}))),e&&t(E())}catch(n){"AbortError"===n.name&&t(y(e))}}}function E(){return function({registry:e,select:t}){const n=t.getConnections();return e.dispatch(o.store).editPost({jetpack_publicize_connections:n})}}function w(e,t=!0){return function({dispatch:n}){n(g(e)),t&&n(E())}}function R(e){return{type:p.nC,connectionId:e}}function A(e,t=!0){return{type:p.WH,connectionId:e,deleting:t}}function C({connectionId:e,showSuccessNotice:t=!0}){return async function({registry:n,dispatch:a}){const{createErrorNotice:r,createSuccessNotice:l}=(0,s.dispatch)(c.store);try{const c=`/wpcom/v2/publicize/connections/${e}`;return a(k()),a(A(e)),await i()({method:"DELETE",path:c}),a(R(e)),t&&l(__("Account disconnected successfully.","jetpack-publicize-pkg"),{type:"snackbar",isDismissible:!0}),n.select(o.store).getCurrentPostId()&&a(E()),!0}catch(e){let t=__("Error disconnecting account.","jetpack-publicize-pkg");"object"==typeof e&&"message"in e&&e.message&&(t=`${t} ${e.message}`),r(t,{type:"snackbar",isDismissible:!0})}finally{a(A(e,!1))}return!1}}let z=1;function S(e,t={}){return async function({registry:n,dispatch:a}){const{createErrorNotice:l,createSuccessNotice:p}=(0,s.dispatch)(c.store),m="new-"+ ++z;try{const c="/wpcom/v2/publicize/connections/";a(d({connection_id:m,...t})),a(k()),a(j(m));const s=await i()({method:"POST",path:c,data:e});s&&(a(_(m,{...s,enabled:!0})),p((0,r.sprintf)(/* translators: %s is the name of the social media platform e.g. "Facebook" */
__("%s account connected successfully.","jetpack-publicize-pkg"),s.service_label),{type:"snackbar",isDismissible:!0}),n.select(o.store).getCurrentPostId()&&a(E()))}catch(e){let t=__("Error connecting account.","jetpack-publicize-pkg");"object"==typeof e&&"message"in e&&e.message&&(t=`${t} ${e.message}`),l(t,{type:"snackbar",isDismissible:!0})}finally{a(j(m,!1)),a(R(m))}}}function _(e,t){return{type:p.dw,connectionId:e,data:t}}function j(e,t=!0){return{type:p._6,connectionId:e,updating:t}}function M(e){return{type:p.Nv,reconnectingAccount:e}}function x(e,t){return async function({dispatch:n,select:a}){const{createErrorNotice:o,createSuccessNotice:r}=(0,s.dispatch)(c.store),l=a.getConnectionById(e);try{const c=`/wpcom/v2/publicize/connections/${e}`;n(k()),n(_(e,t)),n(j(e));await i()({method:"POST",path:c,data:t})&&r(__("Account updated successfully.","jetpack-publicize-pkg"),{type:"snackbar",isDismissible:!0})}catch(t){let c=__("Error updating account.","jetpack-publicize-pkg");"object"==typeof t&&"message"in t&&t.message&&(c=`${c} ${t.message}`),n(_(e,l)),o(c,{type:"snackbar",isDismissible:!0})}finally{n(j(e,!1))}}}function N(e){return{type:p.xW,isOpen:e}}function L(){return N(!0)}function P(){return N(!1)}},54118:(e,t,n)=>{"use strict";n.d(t,{BN:()=>m,Bc:()=>a,M6:()=>k,Mc:()=>y,Nv:()=>p,WB:()=>d,WH:()=>o,Wp:()=>g,_6:()=>l,_y:()=>b,aF:()=>E,af:()=>i,b5:()=>h,dA:()=>c,dw:()=>r,fz:()=>f,nC:()=>s,rz:()=>v,xW:()=>u});const c="SET_CONNECTIONS",a="ADD_CONNECTION",i="TOGGLE_CONNECTION",s="DELETE_CONNECTION",o="DELETING_CONNECTION",r="UPDATE_CONNECTION",l="UPDATING_CONNECTION",p="SET_RECONNECTING_ACCOUNT",m="SET_KEYRING_RESULT",u="TOGGLE_CONNECTIONS_MODAL",d="ADD_ABORT_CONTROLLER",g="REMOVE_ABORT_CONTROLLERS",h="DEFAULT",v="REFRESH_CONNECTIONS",f="FETCH_POST_SHARE_STATUS",b="RECEIVE_POST_SHARE_STATUS",k="TOGGLE_SHARE_STATUS_MODAL",y="TOGGLE_SHARE_POST_MODAL",E="POLLING_FOR_POST_SHARE_STATUS"},62491:(e,t,n)=>{"use strict";n.d(t,{A:()=>u});var c=n(38716),a=n(8773),i=n(83577),s=n(3819),o=n(21131),r=n(72451),l=n(64236),p=n(63491),m=n(95660);const u={...r,...o,...c,...l,...n(83117),...m,...a,...p,...s,...i}},8773:(e,t,n)=>{"use strict";n.r(t),n.d(t,{setShowPricingPage:()=>i});var c=n(3582),a=n(75238);function i(e){return async function({registry:t}){const{saveSite:n}=t.dispatch(c.store);await n({[a.HN]:e})}}},83577:(e,t,n)=>{"use strict";n.r(t),n.d(t,{createScheduledShare:()=>s,deleteScheduledShare:()=>o});var c=n(3582),a=n(27723),i=n(692);const __=a.__;function s(e){return async function({registry:t}){const{saveEntityRecord:n}=t.dispatch(c.store),{getLastEntitySaveError:a}=t.select(c.store),{createErrorNotice:s,createSuccessNotice:o}=t.dispatch(i.store),r=await n("wpcom/v2","publicize/scheduled-actions",e);if(r)o(__("Post scheduled successfully.","jetpack-publicize-pkg"),{type:"snackbar",id:"social-scheduled-share"});else{const e=a("wpcom/v2","publicize/scheduled-actions");let t=__("There was an error scheduling the post.","jetpack-publicize-pkg");e?.message&&(t+=" "+e.message),s(t,{type:"snackbar",id:"social-scheduled-share"})}return r}}function o(e){return async function({registry:t}){const{deleteEntityRecord:n}=t.dispatch(c.store),{getLastEntityDeleteError:a}=t.select(c.store),{createErrorNotice:s}=t.dispatch(i.store);if(!await n("wpcom/v2","publicize/scheduled-actions",e)){const t=a("wpcom/v2","publicize/scheduled-actions",e);let n=__("There was an error deleting the item.","jetpack-publicize-pkg");t?.message&&(n+=" "+t.message),s(n,{type:"snackbar"})}}}},3819:(e,t,n)=>{"use strict";n.r(t),n.d(t,{refreshServicesList:()=>a});var c=n(3582);function a(){return async function({registry:e}){e.dispatch(c.store).invalidateResolution("getEntityRecords",["wpcom/v2","publicize/services"])}}},21131:(e,t,n)=>{"use strict";n.r(t),n.d(t,{closeSharePostModal:()=>s,openSharePostModal:()=>i,toggleSharePostModal:()=>a});var c=n(54118);function a(e){return{type:c.Mc,isOpen:e}}function i(){return a(!0)}function s(){return a(!1)}},72451:(e,t,n)=>{"use strict";n.r(t),n.d(t,{closeShareStatusModal:()=>l,defaultIsRequestComplete:()=>p,fetchPostShareStatus:()=>i,openShareStatusModal:()=>r,pollForPostShareStatus:()=>g,pollingForPostShareStatus:()=>d,receivePostShareStaus:()=>s,toggleShareStatusModal:()=>o});var c=n(43656),a=n(54118);function i(e,t=!0){return{type:a.fz,postId:e,loading:t}}function s(e,t){return{type:a._y,shareStatus:e,postId:t}}function o(e){return{type:a.M6,isOpen:e}}function r(){return o(!0)}function l(){return o(!1)}const p=({lastTimestamp:e,postShareStatus:t})=>e?t.shares.some((t=>t.timestamp>e)):t.shares.length>0,m=6e4,u=3e3;function d(e,t=!0){return{type:a.aF,postId:e,polling:t}}function g({pollingInterval:e=u,postId:t,isRequestComplete:n=p,timeout:a=m}={}){return async function({dispatch:i,select:s,registry:o}){const r=Date.now(),l=t||o.select(c.store).getCurrentPostId(),p=s.getPostShareStatus(l).shares[0]?.timestamp||0;let m=!1,u=!1;i(d(l));do{s.getPostShareStatus(l).loading||i.invalidateResolution("getPostShareStatus",[l]),await new Promise((t=>setTimeout(t,e))),m=n({lastTimestamp:p,postShareStatus:s.getPostShareStatus(l)}),u=Date.now()-r>a}while(!m&&!u);i(d(l,!1))}}},64236:(e,t,n)=>{"use strict";n.r(t),n.d(t,{updateSocialImageGeneratorConfig:()=>i});var c=n(3582),a=n(75238);function i(e){return async function({registry:t}){const{saveSite:n}=t.dispatch(c.store);await n({[a.Am]:e})}}},63491:(e,t,n)=>{"use strict";n.r(t),n.d(t,{updateSocialModuleSettings:()=>i});var c=n(3582),a=n(70407);function i(e){return async function({registry:t}){const{socialToggleBase:n}=(0,a.nE)().api_paths,{saveEntityRecord:i}=t.dispatch(c.store);await i("jetpack/v4",n,e)}}},95660:(e,t,n)=>{"use strict";n.r(t),n.d(t,{toggleSocialNotes:()=>i,updateSocialNotesConfig:()=>s});var c=n(3582),a=n(75238);function i(e){return async function({registry:t}){const{saveSite:n}=t.dispatch(c.store);await n({[a.cK]:e})}}function s(e){return async function({registry:t}){const{saveSite:n}=t.dispatch(c.store);await n({[a.LQ]:e})}}},83117:(e,t,n)=>{"use strict";n.r(t),n.d(t,{updateUtmSettings:()=>i});var c=n(3582),a=n(75238);function i(e){return async function({registry:t}){const{saveSite:n}=t.dispatch(c.store);await n({[a.bn]:e})}}},75238:(e,t,n)=>{"use strict";n.d(t,{Am:()=>c,HN:()=>o,LQ:()=>s,Ml:()=>r,bn:()=>a,cK:()=>i});const c="jetpack_social_image_generator_settings",a="jetpack_social_utm_settings",i="jetpack-social-note",s="jetpack_social_notes_config",o="jetpack-social_show_pricing_page",r=[]},14967:(e,t,n)=>{"use strict";n.d(t,{g:()=>o});var c=n(3582),a=n(47143),i=n(27723),s=n(70407);const __=i.__;async function o(){const{addEntities:e,receiveEntityRecords:t,finishResolution:n}=(0,a.dispatch)(c.store),i=(0,s.nE)()?.api_paths?.socialToggleBase;(0,a.select)(c.store).getEntitiesConfig("jetpack/v4").some((({name:e})=>e===i))||(await e([{kind:"jetpack/v4",name:i,baseURL:`/jetpack/v4/${i}`,label:__("Social Settings","jetpack-publicize-pkg")}]),await t("jetpack/v4",i,{publicize:(0,s.nE)()?.is_publicize_enabled},!0),await n("getEntityRecord",["jetpack/v4",i]));const o=(0,a.select)(c.store).getEntitiesConfig("wpcom/v2");o.some((({name:e})=>"publicize/services"===e))||(await e([{kind:"wpcom/v2",name:"publicize/services",baseURL:"/wpcom/v2/publicize/services",label:__("Publicize services","jetpack-publicize-pkg")}]),await t("wpcom/v2","publicize/services",(0,s.nE)()?.supported_services,!0),await n("getEntityRecords",["wpcom/v2","publicize/services"])),o.some((({name:e})=>"publicize/shares-data"===e))||await e([{kind:"wpcom/v2",name:"publicize/shares-data",baseURL:"/wpcom/v2/publicize/shares-data",label:__("Publicize shares data","jetpack-publicize-pkg")}]),o.some((({name:e})=>"publicize/scheduled-actions"===e))||await e([{kind:"wpcom/v2",name:"publicize/scheduled-actions",baseURL:"/wpcom/v2/publicize/scheduled-actions",label:__("Publicize scheduled actions","jetpack-publicize-pkg")}])}},31963:(e,t,n)=>{"use strict";n.d(t,{M_:()=>m});var c=n(47143),a=n(51371),i=n(62491),s=n(14967),o=n(708),r=n(75772),l=n(62146);const p={reducer:o.A,actions:i.A,selectors:l.A,resolvers:r.Ay,initialState:(0,a.nE)()?.store_initial_state},m=(0,c.createReduxStore)("jetpack-social-plugin",p);(0,c.register)(m),(0,s.g)()},62015:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var c=n(54118);const a=(e={connections:[]},t)=>{switch(t.type){case c.xW:return{...e,isConnectionsModalOpen:t.isOpen};case c.Bc:return{...e,connections:[...e.connections,t.connection]};case c.dA:return{...e,connections:t.connections};case c.nC:return{...e,connections:e.connections.filter((({connection_id:e})=>e!==t.connectionId))};case c.WH:{const n=new Set(e.deletingConnections);return t.deleting?n.add(t.connectionId):n.delete(t.connectionId),{...e,deletingConnections:[...n]}}case c.Nv:return{...e,reconnectingAccount:t.reconnectingAccount};case c.dw:return{...e,connections:e.connections.map((e=>e.connection_id===t.connectionId?{...e,...t.data}:e))};case c._6:{const n=new Set(e.updatingConnections);return t.updating?n.add(t.connectionId):n.delete(t.connectionId),{...e,updatingConnections:[...n]}}case c.WB:{const n=t.requestType||c.b5;return{...e,abortControllers:{...e.abortControllers,[n]:[...e.abortControllers?.[n]||[],t.abortController]}}}case c.Wp:{const n=t.requestType||c.b5;return{...e,abortControllers:{...e.abortControllers,[n]:[]}}}case c.BN:return{...e,keyringResult:t.keyringResult};case c.af:return{...e,connections:e.connections.map((e=>e.connection_id===t.connectionId?{...e,enabled:!e.enabled}:e))}}return e}},708:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var c=n(47143),a=n(62015),i=n(46450),s=n(53202);const o=(0,c.combineReducers)({connectionData:a.A,shareStatus:s.q,sharePost:i.a})},46450:(e,t,n)=>{"use strict";n.d(t,{a:()=>a});var c=n(54118);function a(e={},t){return t.type===c.Mc?{...e,isModalOpen:t.isOpen}:e}},53202:(e,t,n)=>{"use strict";n.d(t,{q:()=>a});var c=n(54118);function a(e={},t){switch(t.type){case c.fz:return{...e,[t.postId]:{shares:[],...e?.[t.postId],loading:t.loading??!0}};case c.aF:return{...e,[t.postId]:{shares:[],...e?.[t.postId],polling:t.polling??!0}};case c._y:return{...e,[t.postId]:{...e?.[t.postId],...t.shareStatus,loading:!1}};case c.M6:return{...e,isModalOpen:t.isOpen}}return e}},75772:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>m});var c=n(97999),a=n(1455),i=n.n(a),s=n(43656),o=n(27844),r=n(59061),l=n(38716),p=n(72451);const m={getConnections:function(){return function({dispatch:e,registry:t}){const n=t.select(s.store);if(!n.getCurrentPostId())return;const c=n.getEditedPostAttribute("jetpack_publicize_connections");Array.isArray(c)?e((0,l.setConnections)(c||[])):console.error("Invalid connections data received from the post meta.",c)}},getPostShareStatus:function(e){return async({dispatch:t,registry:n})=>{const a=e||n.select(s.store).getCurrentPostId();if((0,c.siteHasFeature)(o.q.SHARE_STATUS))try{t((0,p.fetchPostShareStatus)(a));let e=await i()({path:`/wpcom/v2/publicize/share-status?post_id=${a}`});e=(0,r.xA)(e),t((0,p.receivePostShareStaus)(e,a))}catch{t((0,p.fetchPostShareStatus)(a,!1))}}}}},98789:(e,t,n)=>{"use strict";n.r(t),n.d(t,{canUserManageConnection:()=>C,getAbortControllers:()=>y,getBrokenConnections:()=>p,getConnectionById:()=>l,getConnections:()=>r,getConnectionsByService:()=>m,getDeletingConnections:()=>f,getDisabledConnections:()=>v,getEnabledConnections:()=>h,getFailedConnections:()=>d,getKeyringResult:()=>R,getMustReauthConnections:()=>g,getReconnectingAccount:()=>k,getUpdatingConnections:()=>b,hasConnections:()=>u,isBlueskyAccountAlreadyConnected:()=>w,isConnectionsModalOpen:()=>A,isMastodonAccountAlreadyConnected:()=>E});var c=n(97999),a=n(3582),i=n(47143),s=n(54118),o=n(75238);function r(e){return e.connectionData?.connections??o.Ml}function l(e,t){return r(e).find((e=>e.connection_id===t))}const p=(0,i.createSelector)((e=>r(e).filter((e=>"broken"===e.status))),(e=>[e.connectionData?.connections])),m=(0,i.createSelector)(((e,t)=>r(e).filter((({service_name:e})=>e===t))),(e=>[e.connectionData?.connections]));function u(e){return r(e).length>0}const d=(0,i.createSelector)((e=>r(e).filter((e=>"broken"===e.status))),(e=>[e.connectionData?.connections])),g=(0,i.createSelector)((e=>r(e).filter((e=>"must_reauth"===e.status)).map((e=>e.service_name))),(e=>[e.connectionData?.connections])),h=(0,i.createSelector)((e=>r(e).filter((e=>e.enabled))),(e=>[e.connectionData?.connections])),v=(0,i.createSelector)((e=>r(e).filter((e=>!e.enabled))),(e=>[e.connectionData?.connections]));function f(e){return e.connectionData?.deletingConnections??o.Ml}function b(e){return e.connectionData?.updatingConnections??o.Ml}function k(e){return e.connectionData?.reconnectingAccount}function y(e,t=s.b5){return e.connectionData?.abortControllers?.[t]??o.Ml}function E(e,t){return m(e,"mastodon").some((e=>e.external_handle===t))}function w(e,t){return m(e,"bluesky").some((e=>e.external_handle===t))}function R(e){return e.connectionData?.keyringResult}function A(e){return e.connectionData?.isConnectionsModalOpen??!1}const C=(0,i.createRegistrySelector)((e=>(t,n)=>{const i="string"==typeof n?l(t,n):n,{current_user:s}=(0,c.getScriptData)().user;if(s.wpcom?.ID===i.wpcom_user_id)return!0;const{getUser:o}=e(a.store);return o(s.id)?.capabilities?.edit_others_posts??!1}))},62146:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var c=n(98789),a=n(74998),i=n(96140),s=n(17320),o=n(49424),r=n(77357);const l={...c,...o,...s,...n(69356),...n(30513),...i,...r,...a}},74998:(e,t,n)=>{"use strict";n.r(t),n.d(t,{getScheduledSharesForPost:()=>s,isDeletingScheduledShare:()=>l,isFetchingScheduledSharesForPost:()=>r,isSavingScheduledShare:()=>o});var c=n(3582),a=n(47143),i=n(75238);const s=(0,a.createRegistrySelector)((e=>(t,n)=>e(c.store).getEntityRecords("wpcom/v2","publicize/scheduled-actions",{post_id:n})??i.Ml)),o=(0,a.createRegistrySelector)((e=>()=>e(c.store).isSavingEntityRecord("wpcom/v2","publicize/scheduled-actions",void 0))),r=(0,a.createRegistrySelector)((e=>(t,n)=>{const{isResolving:a}=e(c.store);return a("getEntityRecords",["wpcom/v2","publicize/scheduled-actions",{post_id:n}])})),l=(0,a.createRegistrySelector)((e=>(t,n)=>e(c.store).isDeletingEntityRecord("wpcom/v2","publicize/scheduled-actions",n)))},96140:(e,t,n)=>{"use strict";n.r(t),n.d(t,{getService:()=>o,getServicesBy:()=>l,getServicesList:()=>s,isFetchingServicesList:()=>r});var c=n(3582),a=n(47143),i=n(75238);const s=(0,a.createRegistrySelector)((e=>()=>e(c.store).getEntityRecords("wpcom/v2","publicize/services")??i.Ml));function o(e,t){return s().find((e=>e.id===t))}const r=(0,a.createRegistrySelector)((e=>()=>{const{isResolving:t}=e(c.store);return t("getEntityRecords",["wpcom/v2","publicize/services"])}));function l(e,t,n){return s().filter((e=>e[t]===n))}},17320:(e,t,n)=>{"use strict";function c(e){return e.sharePost?.isModalOpen??!1}n.r(t),n.d(t,{isSharePostModalOpen:()=>c})},49424:(e,t,n)=>{"use strict";n.r(t),n.d(t,{getPostShareStatus:()=>s,isShareStatusModalOpen:()=>o});var c=n(47143),a=n(43656),i=n(75238);const s=(0,c.createRegistrySelector)((e=>(t,n)=>{const c=n||e(a.store).getCurrentPostId();return t.shareStatus?.[c]??{shares:i.Ml}}));function o(e){return e.shareStatus?.isModalOpen??!1}},77357:(e,t,n)=>{"use strict";n.r(t),n.d(t,{getSharedPostsCount:()=>l,getTotalSharesCount:()=>r,isShareLimitEnabled:()=>p});var c=n(3582),a=n(47143),i=n(70407);const s={},o=e=>e(c.store).getEntityRecord("wpcom/v2","publicize/shares-data")||(0,i.nE)().shares_data||s,r=(0,a.createRegistrySelector)((e=>()=>{const t=o(e),n=(e=>e?.publicized_count??0)(t)+(e=>e?.to_be_publicized_count??0)(t);return Math.max(n,0)})),l=(0,a.createRegistrySelector)((e=>()=>{const t=o(e);return t?.shared_posts_count??0})),p=(0,a.createRegistrySelector)((e=>()=>{const t=o(e);return t?.is_share_limit_enabled??!1}))},69356:(e,t,n)=>{"use strict";n.r(t),n.d(t,{getSocialModuleSettings:()=>s,isSavingSocialModuleSettings:()=>o});var c=n(3582),a=n(47143),i=n(70407);const s=(0,a.createRegistrySelector)((e=>()=>{const{api_paths:t,is_publicize_enabled:n}=(0,i.nE)();return e(c.store).getEntityRecord("jetpack/v4",t.socialToggleBase)??{publicize:n}})),o=(0,a.createRegistrySelector)((e=>()=>{const{socialToggleBase:t}=(0,i.nE)().api_paths;return e(c.store).isSavingEntityRecord("jetpack/v4",t,void 0)}))},30513:(e,t,n)=>{"use strict";n.r(t),n.d(t,{getSocialSettings:()=>r,isSavingSiteSettings:()=>o});var c=n(97999),a=n(3582),i=n(47143),s=n(70407);const o=(0,i.createRegistrySelector)((e=>()=>e(a.store).isSavingEntityRecord("root","site",void 0))),r=(0,i.createRegistrySelector)((e=>()=>{const t=(0,c.currentUserCan)("manage_options")?e(a.store).getEntityRecord("root","site"):null,{settings:n}=(0,s.nE)();return t?{showPricingPage:t["jetpack-social_show_pricing_page"]??n.showPricingPage,socialImageGenerator:{...n.socialImageGenerator,...t.jetpack_social_image_generator_settings},utmSettings:{...n.utmSettings,...t.jetpack_social_utm_settings},socialNotes:{enabled:Boolean(t["jetpack-social-note"]),config:{...n.socialNotes.config,...t.jetpack_social_notes_config}}}:n}))},27844:(e,t,n)=>{"use strict";n.d(t,{q:()=>c});const c={ENHANCED_PUBLISHING:"social-enhanced-publishing",IMAGE_GENERATOR:"social-image-generator",SHARE_STATUS:"social-share-status"}},70407:(e,t,n)=>{"use strict";n.d(t,{nE:()=>a.nE,pq:()=>a.pq,qT:()=>i.q,x9:()=>c.x});n(93530);var c=n(5367),a=n(51371),i=n(27844)},48625:(e,t,n)=>{"use strict";n.d(t,{E:()=>a});var c=n(97999);function a(){return!("wpcom"===(0,c.getScriptData)().site.host)&&(0,c.currentUserCan)("manage_modules")}},5367:(e,t,n)=>{"use strict";n.d(t,{x:()=>a});var c=n(51119);const a=(e,t)=>{const n=new c.A;let a;n.open(e,null,"toolbar=0,location=0,status=0,menubar=0,"+n.getScreenCenterSpecs(780,700)),n.once("close",(()=>{t(a?.ID?a:{})})),n.on("message",(e=>{a=e?.data}))}},51371:(e,t,n)=>{"use strict";n.d(t,{nE:()=>a,pq:()=>i});var c=n(97999);function a(){return(0,c.getScriptData)()?.social}function i(){return(0,c.siteHasFeature)("social-enhanced-publishing")}},59061:(e,t,n)=>{"use strict";function c(e){return e&&"shares"in e&&e.done&&e.shares.sort(((e,t)=>t.timestamp-e.timestamp)),e}n.d(t,{xA:()=>c})},93530:(e,t,n)=>{"use strict";n(2204),n(50001)},85985:(e,t,n)=>{"use strict";n.d(t,{pg:()=>c.A});n(52810),n(54815);var c=n(41409);n(82034),n(65595),n(53265),n(73489),n(47119),n(58406),n(76923),n(30335),n(88290),n(9061),n(25929),n(5765)},5765:(e,t,n)=>{"use strict";n(98490)},52810:(e,t,n)=>{"use strict";n(38377).T["Jetpack Green 40"]},30335:(e,t,n)=>{"use strict";n(86087)},54815:(e,t,n)=>{"use strict";n(97999)},73489:(e,t,n)=>{"use strict";var c=n(90372);n(39384),n(86087);const{tracks:a}=c.A,{recordEvent:i}=a},47119:(e,t,n)=>{"use strict";n(47143),n(86087),n(66087)},76923:(e,t,n)=>{"use strict";n(47143),n(86087),n(88290)},58406:(e,t,n)=>{"use strict";n(86087)},25929:(e,t,n)=>{"use strict";n(47143),n(52619),n(53265),n(47119)},9520:(e,t,n)=>{"use strict";var c=n(46941),a=n.n(c);window,a()("shared-extension-utils:connection")},9061:(e,t,n)=>{"use strict";n(9520)},27105:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>m,E9:()=>p});var c=n(47143),a=n(62634),i=n(94478),s=n(88290);const o="SET_JETPACK_MODULES";function r(e){return p({isLoading:e})}function l(e,t){return{type:"SET_MODULE_UPDATING",name:e,isUpdating:t}}function p(e){return{type:o,options:e}}const m={updateJetpackModuleStatus:function*(e){try{yield l(e.name,!0),yield(0,i.sB)(e);const t=yield(0,i.wz)();return yield p({data:t}),!0}catch{const e=(0,c.select)(s.F).getJetpackModules();return yield p(e),!1}finally{yield l(e.name,!1)}},setJetpackModules:p,fetchModules:function*(){if((0,a.Sy)())return!0;try{yield r(!0);const e=yield(0,i.wz)();return yield p({data:e}),!0}catch{const e=(0,c.select)(s.F).getJetpackModules();return yield p(e),!1}finally{yield r(!1)}}}},94478:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>l,sB:()=>r,wz:()=>o});var c=n(1455),a=n.n(c);const i="FETCH_JETPACK_MODULES",s="UPDATE_JETPACK_MODULE_STATUS",o=()=>({type:i}),r=e=>({type:s,settings:e}),l={[i]:function(){return a()({path:"/jetpack/v4/module/all",method:"GET"})},[s]:function({settings:e}){return a()({path:`/jetpack/v4/module/${e.name}/active`,method:"POST",data:{active:e.active}})}}},88290:(e,t,n)=>{"use strict";n.d(t,{F:()=>l});var c=n(47143),a=n(27105),i=n(94478),s=n(38862),o=n(62701),r=n(31640);const l="jetpack-modules",p=(0,c.createReduxStore)(l,{reducer:s.A,actions:a.Ay,controls:i.Ay,resolvers:o.A,selectors:r.A});(0,c.register)(p);const m=window?.Initial_State?.getModules||window?.Jetpack_Editor_Initial_State?.modules||null;null!==m&&(0,c.dispatch)(l).setJetpackModules({data:{...m}})},38862:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});const c={isLoading:!1,isUpdating:{},data:{}},a=(e=c,t)=>{switch(t.type){case"SET_JETPACK_MODULES":return{...e,...t.options};case"SET_MODULE_UPDATING":return{...e,isUpdating:{...e.isUpdating,[t.name]:t.isUpdating}}}return e}},62701:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var c=n(27105),a=n(94478);const i={getJetpackModules:function*(){try{const e=yield(0,a.wz)();if(e)return(0,c.E9)({data:e})}catch(e){console.error(e)}}}},31640:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var c=n(62634);const a={getJetpackModules:e=>e.data,isModuleActive:(e,t)=>(0,c.Sy)()||(e?.data?.[t]?.activated??!1),areModulesLoading:e=>e.isLoading??!1,isModuleUpdating:(e,t)=>e?.isUpdating?.[t]??!1}},53265:(e,t,n)=>{"use strict";var c=n(27723);n(93832),n(66087),n(54815);const __=c.__;__("Upgrade your plan to use video covers","jetpack-publicize-pkg"),__("Upgrade your plan to upload audio","jetpack-publicize-pkg")},82034:(e,t,n)=>{"use strict";n(92279)},41409:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var c=n(97999);function a(){const{connectedPlugins:e,connectionStatus:t}=(0,c.getScriptData)()?.connection??{};return t?.isActive&&e?.some((({slug:e})=>"jetpack"===e))}},62634:(e,t,n)=>{"use strict";function c(){return"object"==typeof window&&"string"==typeof window._currentSiteType?window._currentSiteType:null}function a(){return"simple"===c()}n.d(t,{Sy:()=>a})},65595:(e,t,n)=>{"use strict";n(96072),n(29491)},84705:(e,t,n)=>{"use strict";n.d(t,{d6:()=>c.d});var c=n(58992);n(91135)},91135:(e,t,n)=>{"use strict";n.d(t,{$:()=>c});const c=[{name:"amazon",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M13.582 8.182c-1.648.185-3.802.308-5.344.984-1.781.769-3.03 2.337-3.03 4.644 0 2.953 1.86 4.429 4.253 4.429 2.02 0 3.125-.477 4.685-2.065.516.747.685 1.109 1.629 1.894a.59.59 0 0 0 .672-.066l.006.006c.567-.505 1.599-1.401 2.18-1.888.231-.188.19-.496.009-.754-.52-.718-1.072-1.303-1.072-2.634V8.305c0-1.876.133-3.599-1.249-4.891C15.23 2.369 13.422 2 12.04 2 9.336 2 6.318 3.01 5.686 6.351c-.068.355.191.542.423.594l2.754.298c.258-.013.445-.266.494-.523.236-1.151 1.2-1.706 2.284-1.706.584 0 1.249.215 1.595.738.398.584.346 1.384.346 2.061zm-.533 5.906c-.451.8-1.169 1.291-1.967 1.291-1.09 0-1.728-.83-1.728-2.061 0-2.42 2.171-2.86 4.227-2.86v.615c.001 1.108.027 2.031-.532 3.015m7.634 5.251C18.329 21.076 14.917 22 11.979 22c-4.118 0-7.826-1.522-10.632-4.057-.22-.199-.024-.471.241-.317 3.027 1.762 6.771 2.823 10.639 2.823 2.608 0 5.476-.541 8.115-1.66.397-.169.73.262.341.55m.653 1.704c-.194.163-.379.076-.293-.139.284-.71.92-2.298.619-2.684s-1.99-.183-2.749-.092c-.23.027-.266-.173-.059-.319 1.348-.946 3.555-.673 3.811-.356.26.32-.066 2.533-1.329 3.59"})))},{name:"behance",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M7.799 5.698c.589 0 1.12.051 1.606.156q.722.155 1.241.507.516.351.804.938c.188.387.281.871.281 1.443q0 .93-.421 1.551-.424.62-1.255 1.014 1.133.328 1.689 1.146.559.822.557 1.975 0 .935-.359 1.612a3.14 3.14 0 0 1-.973 1.114q-.613.432-1.399.637A6.1 6.1 0 0 1 7.963 18H2V5.698zm-.35 4.97q.721 0 1.192-.345.465-.344.463-1.119 0-.43-.152-.707a1.1 1.1 0 0 0-.416-.427 1.7 1.7 0 0 0-.596-.216 3.6 3.6 0 0 0-.697-.06H4.709v2.874zm.151 5.237q.401.001.759-.077c.243-.053.457-.137.637-.261.182-.12.332-.283.441-.491q.164-.31.163-.798-.002-.948-.533-1.357c-.356-.27-.83-.404-1.413-.404H4.709v3.388zm8.562-.041q.552.538 1.583.538.74 0 1.277-.374c.354-.248.571-.514.654-.79h2.155c-.347 1.072-.872 1.838-1.589 2.299-.708.463-1.572.693-2.58.693q-1.05 0-1.899-.337a4 4 0 0 1-1.439-.958 4.4 4.4 0 0 1-.904-1.484 5.4 5.4 0 0 1-.32-1.899q0-1 .329-1.863a4.4 4.4 0 0 1 .933-1.492q.607-.63 1.444-.994a4.6 4.6 0 0 1 1.857-.363q1.131-.001 1.98.44a3.94 3.94 0 0 1 1.389 1.181 4.8 4.8 0 0 1 .783 1.69q.24.947.171 1.983h-6.428c-.001.706.237 1.372.604 1.73m2.811-4.68c-.291-.321-.783-.496-1.384-.496q-.585 0-.973.2a2 2 0 0 0-.621.491 1.8 1.8 0 0 0-.328.628 2.7 2.7 0 0 0-.111.587h3.98c-.058-.625-.271-1.085-.563-1.41m-3.916-3.446h4.985V6.524h-4.985z"})))},{name:"blogger-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19.779 9.904h-.981l-.021.001a1.163 1.163 0 0 1-1.16-1.079l-.001-.013A5.813 5.813 0 0 0 11.803 3H8.871a5.813 5.813 0 0 0-5.813 5.813v6.375a5.813 5.813 0 0 0 5.813 5.813h6.257a5.814 5.814 0 0 0 5.813-5.813l.002-4.121a1.164 1.164 0 0 0-1.164-1.163M8.726 7.713h3.291a1.117 1.117 0 1 1 0 2.234H8.726a1.117 1.117 0 1 1 0-2.234m6.601 8.657H8.72a1.057 1.057 0 1 1 0-2.114h6.607a1.057 1.057 0 1 1 0 2.114"})))},{name:"blogger",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M14.722 14.019a.654.654 0 0 1-.654.654H9.977a.654.654 0 0 1 0-1.308h4.091c.361 0 .654.293.654.654m-4.741-3.321h2.038a.692.692 0 0 0 0-1.384H9.981a.692.692 0 0 0 0 1.384M21 5v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2m-3.456 6.39a.72.72 0 0 0-.72-.72h-.607l-.013.001a.72.72 0 0 1-.718-.668l-.001-.008a3.6 3.6 0 0 0-3.599-3.599H10.07a3.6 3.6 0 0 0-3.599 3.599v3.947a3.6 3.6 0 0 0 3.599 3.599h3.874a3.6 3.6 0 0 0 3.599-3.599z"})))},{name:"bluesky",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M21.2 3.3c-.5-.2-1.4-.5-3.6 1C15.4 6 12.9 9.2 12 11c-.9-1.8-3.4-5-5.7-6.7-2.2-1.6-3-1.3-3.6-1S2 4.6 2 5.1s.3 4.7.5 5.4c.7 2.3 3.1 3.1 5.3 2.8-3.3.5-6.2 1.7-2.4 5.9 4.2 4.3 5.7-.9 6.5-3.6.8 2.7 1.7 7.7 6.4 3.6 3.6-3.6 1-5.4-2.3-5.9 2.2.2 4.6-.5 5.3-2.8.4-.7.7-4.8.7-5.4 0-.5-.1-1.5-.8-1.8"})))},{name:"codepen",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"m22.016 8.84-.007-.037q-.006-.037-.015-.072-.007-.022-.013-.042l-.023-.062-.02-.042a.4.4 0 0 0-.03-.057l-.025-.038-.035-.052-.03-.037q-.021-.026-.043-.045-.015-.018-.035-.035a.4.4 0 0 0-.048-.04l-.037-.03-.015-.012-9.161-6.096a.86.86 0 0 0-.955 0L2.359 8.237l-.015.012-.038.028-.048.04a.638.638 0 0 0-.078.082q-.018.018-.03.037-.018.026-.035.052l-.025.038q-.016.031-.03.059l-.02.041a1 1 0 0 0-.034.106q-.01.034-.016.071-.003.02-.006.037a1 1 0 0 0-.009.114v6.093q0 .056.008.112l.007.038q.006.035.015.072a.2.2 0 0 0 .013.04q.01.032.022.063l.02.04a.4.4 0 0 0 .055.096l.035.052.03.037.042.045.035.035q.023.02.048.04l.038.03.013.01 9.163 6.095a.858.858 0 0 0 .959.004l9.163-6.095.015-.01q.02-.015.037-.03l.048-.04q.02-.017.035-.035.025-.024.043-.045l.03-.037.035-.052.025-.038a.4.4 0 0 0 .03-.058l.02-.04.023-.063c.003-.013.01-.027.013-.04q.009-.037.015-.072l.007-.037q.006-.062.007-.117V8.954a1 1 0 0 0-.008-.114m-9.154-4.376 6.751 4.49-3.016 2.013-3.735-2.492zm-1.724 0v4.009l-3.735 2.494-3.014-2.013zm-7.439 6.098L5.853 12l-2.155 1.438zm7.439 8.974-6.749-4.491 3.015-2.011 3.735 2.492zM12 14.035 8.953 12 12 9.966 15.047 12zm.862 5.501v-4.009l3.735-2.492 3.016 2.011zm7.441-6.098L18.147 12l2.156-1.438z"})))},{name:"deezer",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M20.129 3.662c.222-1.287.548-2.096.909-2.098h.001c.673.002 1.219 2.809 1.219 6.274s-.546 6.274-1.22 6.274c-.276 0-.531-.477-.736-1.276-.324 2.926-.997 4.937-1.776 4.937-.603 0-1.144-1.208-1.507-3.114-.248 3.624-.872 6.195-1.602 6.195-.458 0-.875-1.019-1.184-2.678C13.861 21.6 13.003 24 12.002 24s-1.861-2.399-2.231-5.824c-.307 1.659-.724 2.678-1.184 2.678-.73 0-1.352-2.571-1.602-6.195-.363 1.905-.903 3.114-1.507 3.114-.778 0-1.452-2.011-1.776-4.937-.204.802-.46 1.276-.736 1.276-.674 0-1.22-2.809-1.22-6.274s.546-6.274 1.22-6.274c.362 0 .685.812.91 2.098.357-2.22.94-3.662 1.6-3.662.784 0 1.463 2.04 1.784 5.002.314-2.156.791-3.53 1.325-3.53.749 0 1.385 2.703 1.621 6.474.443-1.933 1.085-3.146 1.795-3.146s1.352 1.214 1.795 3.146c.237-3.771.872-6.474 1.621-6.474.533 0 1.009 1.374 1.325 3.53.321-2.962 1-5.002 1.784-5.002.658 0 1.244 1.443 1.603 3.662M0 7.221c0-1.549.31-2.805.692-2.805s.692 1.256.692 2.805-.31 2.805-.692 2.805S0 8.77 0 7.221m22.616 0c0-1.549.31-2.805.692-2.805S24 5.672 24 7.221s-.31 2.805-.692 2.805-.692-1.256-.692-2.805"})))},{name:"discord",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M20.33 4.523A20 20 0 0 0 15.379 3a14 14 0 0 0-.634 1.289 18.4 18.4 0 0 0-5.495 0A14 14 0 0 0 8.615 3 20 20 0 0 0 3.66 4.527C.527 9.163-.323 13.684.102 18.141a20 20 0 0 0 6.073 3.049 14.7 14.7 0 0 0 1.301-2.097 13 13 0 0 1-2.048-.978q.258-.189.502-.378a14.27 14.27 0 0 0 12.142 0q.247.202.502.378a13 13 0 0 1-2.052.98 14.5 14.5 0 0 0 1.301 2.095 19.9 19.9 0 0 0 6.076-3.047c.498-5.168-.851-9.648-3.568-13.62M8.013 15.4c-1.183 0-2.161-1.074-2.161-2.395S6.796 10.6 8.01 10.6s2.183 1.083 2.163 2.405S9.22 15.4 8.013 15.4m7.974 0c-1.186 0-2.16-1.074-2.16-2.395s.944-2.405 2.16-2.405 2.178 1.083 2.157 2.405-.951 2.395-2.158 2.395"})))},{name:"dribbble",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10m8.434-8.631c-.292-.092-2.644-.794-5.32-.365 1.117 3.07 1.572 5.57 1.659 6.09a8.56 8.56 0 0 0 3.661-5.725m-5.098 6.507c-.127-.749-.623-3.361-1.822-6.477l-.056.019c-4.818 1.679-6.547 5.02-6.701 5.334A8.5 8.5 0 0 0 12 20.555a8.5 8.5 0 0 0 3.336-.679m-9.682-2.152c.193-.331 2.538-4.213 6.943-5.637q.167-.054.337-.102a29 29 0 0 0-.692-1.45c-4.266 1.277-8.405 1.223-8.778 1.216a8.497 8.497 0 0 0 2.19 5.973m-2.015-7.46c.382.005 3.901.02 7.897-1.041a55 55 0 0 0-3.167-4.94 8.57 8.57 0 0 0-4.73 5.981m6.359-6.555a46 46 0 0 1 3.187 5c3.037-1.138 4.323-2.867 4.477-3.085a8.51 8.51 0 0 0-7.664-1.915m8.614 2.903c-.18.243-1.612 2.078-4.77 3.367a27 27 0 0 1 .751 1.678c2.842-.357 5.666.215 5.948.275a8.5 8.5 0 0 0-1.929-5.32"})))},{name:"dropbox",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 6.134 6.069 9.797 2 6.54l5.883-3.843zm-10 6.92 5.883 3.843L12 13.459 6.069 9.797zm10 .405 4.116 3.439L22 13.054l-4.069-3.257zM22 6.54l-5.884-3.843L12 6.134l5.931 3.663zm-9.989 7.66-4.129 3.426-1.767-1.153v1.291l5.896 3.539 5.897-3.539v-1.291l-1.769 1.153z"})))},{name:"eventbrite",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M18.041 3.931 5.959 3A2.96 2.96 0 0 0 3 5.959v12.083A2.96 2.96 0 0 0 5.959 21l12.083-.931C19.699 19.983 21 18.744 21 17.11V6.89c0-1.634-1.259-2.863-2.959-2.959M16.933 8.17c-.082.215-.192.432-.378.551-.188.122-.489.132-.799.132-1.521 0-3.062-.048-4.607-.048q-.23 1.061-.451 2.128c.932-.004 1.873.005 2.81.005.726 0 1.462-.069 1.586.525.04.189-.001.426-.052.615-.105.38-.258.676-.625.783-.185.054-.408.058-.646.058-1.145 0-2.345.017-3.493.02-.169.772-.328 1.553-.489 2.333 1.57-.005 3.067-.041 4.633-.058.627-.007 1.085.194 1.009.85a2.2 2.2 0 0 1-.211.725c-.102.208-.248.376-.488.452-.237.075-.541.064-.862.078-.304.014-.614.008-.924.016-.309.009-.619.022-.919.022-1.253 0-2.429.08-3.683.073-.603-.004-1.014-.249-1.124-.757-.059-.273-.018-.58.036-.841a3543 3543 0 0 1 1.629-7.763c.056-.265.114-.511.225-.714a1.24 1.24 0 0 1 .79-.62c.368-.099.883-.047 1.344-.047.305 0 .612.008.914.016.925.026 1.817.03 2.747.053.304.007.615.016.915.016.621 0 1.17.073 1.245.614.039.288-.051.567-.132.783"})))},{name:"facebook",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.5 2 2 6.5 2 12c0 5 3.7 9.1 8.4 9.9v-7H7.9V12h2.5V9.8c0-2.5 1.5-3.9 3.8-3.9 1.1 0 2.2.2 2.2.2v2.5h-1.3c-1.2 0-1.6.8-1.6 1.6V12h2.8l-.4 2.9h-2.3v7C18.3 21.1 22 17 22 12c0-5.5-4.5-10-10-10"})))},{name:"fediverse",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 743 743"},React.createElement("g",null,React.createElement("path",{d:"M181.131 275.137a68.9 68.9 0 0 1-29.465 29.328l161.758 162.389 38.998-19.764zm213.363 214.187-38.998 19.764 81.963 82.283a68.9 68.9 0 0 1 29.471-29.332zM581.646 339.391l-91.576 46.41 6.752 43.189 103.616-52.513a68.9 68.9 0 0 1-18.792-37.086m-144.738 73.351L220.383 522.477a68.9 68.9 0 0 1 18.795 37.089L443.66 455.934zM367.275 142.438l-104.48 203.97 30.848 30.967 110.623-215.957a68.9 68.9 0 0 1-36.991-18.98M235.621 399.459l-52.922 103.314a68.9 68.9 0 0 1 36.987 18.979l46.781-91.328zM150.768 304.918a68.9 68.9 0 0 1-34.416 7.195 69 69 0 0 1-6.651-.695l30.903 197.662a68.9 68.9 0 0 1 34.416-7.195 69 69 0 0 1 6.646.695zM239.342 560.545c.707 4.589.949 9.239.72 13.877a68.9 68.9 0 0 1-7.267 27.18l197.629 31.712c-.708-4.59-.95-9.24-.723-13.878a68.9 68.9 0 0 1 7.27-27.178zM601.133 377.199l-91.219 178.082a68.9 68.9 0 0 1 36.994 18.983l91.217-178.08a68.9 68.9 0 0 1-36.992-18.985M476.723 125.33a68.9 68.9 0 0 1-29.471 29.332l141.266 141.811a68.9 68.9 0 0 1 29.468-29.332zM347.787 104.631l-178.576 90.498a68.9 68.9 0 0 1 18.793 37.086l178.574-90.502a68.9 68.9 0 0 1-18.791-37.082M446.926 154.826a68.9 68.9 0 0 1-34.983 7.483 69 69 0 0 1-6.029-.633l15.818 101.291 43.163 6.926zm-16 167.028 37.4 239.482a68.9 68.9 0 0 1 33.914-6.943q3.625.206 7.207.791L474.09 328.777zM188.131 232.975c.734 4.66.988 9.383.758 14.095a68.9 68.9 0 0 1-7.16 26.983l101.369 16.281 19.923-38.908zm173.736 27.9-19.926 38.912 239.514 38.467a69 69 0 0 1-.695-13.719 68.9 68.9 0 0 1 7.349-27.324z"}),React.createElement("path",{fillOpacity:".996",d:"M412.284 156.054c34.538 1.882 64.061-24.592 65.943-59.13s-24.592-64.062-59.131-65.943c-34.538-1.882-64.061 24.592-65.943 59.13s24.593 64.062 59.131 65.943M646.144 390.82c34.538 1.881 64.062-24.593 65.943-59.131s-24.592-64.061-59.13-65.943-64.062 24.593-65.943 59.131 24.592 64.061 59.13 65.943M495.086 685.719c34.538 1.881 64.062-24.592 65.943-59.13s-24.592-64.062-59.13-65.943-64.062 24.592-65.943 59.13 24.592 64.062 59.13 65.943M167.866 633.211c34.538 1.882 64.062-24.592 65.943-59.13s-24.592-64.062-59.13-65.943-64.062 24.592-65.943 59.13 24.592 64.062 59.13 65.943M116.692 305.86c34.538 1.882 64.062-24.592 65.943-59.13s-24.592-64.062-59.131-65.943c-34.538-1.881-64.061 24.592-65.943 59.13s24.593 64.062 59.131 65.943"})))},{name:"feed",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M2 8.667V12c5.515 0 10 4.485 10 10h3.333c0-7.363-5.97-13.333-13.333-13.333M2 2v3.333c9.19 0 16.667 7.477 16.667 16.667H22C22 10.955 13.045 2 2 2m2.5 15a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5"})))},{name:"flickr",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M6.5 7c-2.75 0-5 2.25-5 5s2.25 5 5 5 5-2.25 5-5-2.25-5-5-5m11 0c-2.75 0-5 2.25-5 5s2.25 5 5 5 5-2.25 5-5-2.25-5-5-5"})))},{name:"foursquare",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M17.573 2H6.905C5.434 2 5 3.107 5 3.805v16.948c0 .785.422 1.077.66 1.172.238.097.892.177 1.285-.275 0 0 5.035-5.843 5.122-5.93.132-.132.132-.132.262-.132h3.26c1.368 0 1.588-.977 1.732-1.552.078-.318.692-3.428 1.225-6.122l.675-3.368C19.56 2.893 19.14 2 17.573 2m-1.078 5.22c-.053.252-.372.518-.665.518h-4.157c-.467 0-.802.318-.802.787v.508c0 .467.337.798.805.798h3.528c.331 0 .655.362.583.715s-.407 2.102-.448 2.295c-.04.193-.262.523-.655.523h-2.88c-.523 0-.683.068-1.033.503-.35.437-3.505 4.223-3.505 4.223-.032.035-.063.027-.063-.015V4.852c0-.298.26-.648.648-.648h8.562c.315 0 .61.297.528.683z"})))},{name:"ghost",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M10.203 20.997H3.005v-3.599h7.198zm10.792-3.599h-7.193v3.599h7.193zm.003-7.198H3v3.599h17.998zm-7.195-7.197H3.005v3.599h10.798zm7.197 0h-3.599v3.599H21z"})))},{name:"git",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M23.519 10.947 13.053.482a1.543 1.543 0 0 0-2.183 0L8.696 2.656l2.756 2.756a1.83 1.83 0 0 1 1.886.439 1.84 1.84 0 0 1 .436 1.898l2.656 2.657a1.83 1.83 0 0 1 1.899.436 1.837 1.837 0 0 1 0 2.597 1.84 1.84 0 0 1-2.599 0 1.84 1.84 0 0 1-.4-1.998l-2.478-2.477v6.521a1.837 1.837 0 0 1 .485 2.945 1.837 1.837 0 0 1-2.597 0 1.837 1.837 0 0 1 0-2.598 1.8 1.8 0 0 1 .602-.401V8.85a1.8 1.8 0 0 1-.602-.4 1.84 1.84 0 0 1-.395-2.009L7.628 3.723.452 10.898a1.544 1.544 0 0 0 0 2.184l10.467 10.467a1.544 1.544 0 0 0 2.183 0l10.417-10.418a1.546 1.546 0 0 0 0-2.184"})))},{name:"github",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12c0 4.419 2.865 8.166 6.839 9.489.5.09.682-.218.682-.484 0-.236-.009-.866-.014-1.699-2.782.602-3.369-1.34-3.369-1.34-.455-1.157-1.11-1.465-1.11-1.465-.909-.62.069-.608.069-.608 1.004.071 1.532 1.03 1.532 1.03.891 1.529 2.341 1.089 2.91.833.091-.647.349-1.086.635-1.337-2.22-.251-4.555-1.111-4.555-4.943 0-1.091.39-1.984 1.03-2.682-.103-.254-.447-1.27.097-2.646 0 0 .84-.269 2.75 1.025A9.6 9.6 0 0 1 12 6.836c.85.004 1.705.114 2.504.336 1.909-1.294 2.748-1.025 2.748-1.025.546 1.376.202 2.394.1 2.646.64.699 1.026 1.591 1.026 2.682 0 3.841-2.337 4.687-4.565 4.935.359.307.679.917.679 1.852 0 1.335-.012 2.415-.012 2.741 0 .269.18.579.688.481A10 10 0 0 0 22 12c0-5.523-4.477-10-10-10"})))},{name:"google-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m-.05 16c-3.312 0-6-2.688-6-6s2.688-6 6-6c1.62 0 2.976.594 4.014 1.566L14.26 9.222c-.432-.408-1.188-.888-2.31-.888-1.986 0-3.606 1.65-3.606 3.672s1.62 3.672 3.606 3.672c2.298 0 3.144-1.59 3.3-2.532h-3.306v-2.238h5.616c.084.378.15.732.15 1.23 0 3.426-2.298 5.862-5.76 5.862"})))},{name:"google-plus-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M8 11h6.61c.06.35.11.7.11 1.16 0 4-2.68 6.84-6.72 6.84-3.87 0-7-3.13-7-7s3.13-7 7-7c1.89 0 3.47.69 4.69 1.83l-1.9 1.83c-.52-.5-1.43-1.08-2.79-1.08-2.39 0-4.34 1.98-4.34 4.42S5.61 16.42 8 16.42c2.77 0 3.81-1.99 3.97-3.02H8zm15 0h-2V9h-2v2h-2v2h2v2h2v-2h2"})))},{name:"google-plus",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m-1.919 14.05a4.051 4.051 0 0 1 0-8.1c1.094 0 2.009.401 2.709 1.057l-1.15 1.118a2.23 2.23 0 0 0-1.559-.599c-1.341 0-2.434 1.114-2.434 2.479s1.094 2.479 2.434 2.479c1.551 0 2.122-1.073 2.227-1.709h-2.232v-1.511h3.791c.057.255.101.494.101.83.001 2.312-1.55 3.956-3.887 3.956M19 12.75h-1.25V14h-1.5v-1.25H15v-1.5h1.25V10h1.5v1.25H19z"})))},{name:"google",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12.02 10.18v3.73h5.51c-.26 1.57-1.67 4.22-5.5 4.22-3.31 0-6.01-2.75-6.01-6.12s2.7-6.12 6.01-6.12c1.87 0 3.13.8 3.85 1.48l2.84-2.76C16.99 2.99 14.73 2 12.03 2c-5.52 0-10 4.48-10 10s4.48 10 10 10c5.77 0 9.6-4.06 9.6-9.77 0-.83-.11-1.42-.25-2.05z"})))},{name:"instagram",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 4.622c2.403 0 2.688.009 3.637.052.877.04 1.354.187 1.671.31.42.163.72.358 1.035.673s.51.615.673 1.035c.123.317.27.794.31 1.671.043.949.052 1.234.052 3.637s-.009 2.688-.052 3.637c-.04.877-.187 1.354-.31 1.671-.163.42-.358.72-.673 1.035s-.615.51-1.035.673c-.317.123-.794.27-1.671.31-.949.043-1.233.052-3.637.052s-2.688-.009-3.637-.052c-.877-.04-1.354-.187-1.671-.31a2.8 2.8 0 0 1-1.035-.673 2.8 2.8 0 0 1-.673-1.035c-.123-.317-.27-.794-.31-1.671-.043-.949-.052-1.234-.052-3.637s.009-2.688.052-3.637c.04-.877.187-1.354.31-1.671.163-.42.358-.72.673-1.035s.615-.51 1.035-.673c.317-.123.794-.27 1.671-.31.949-.043 1.234-.052 3.637-.052M12 3c-2.444 0-2.751.01-3.711.054-.958.044-1.612.196-2.184.418a4.4 4.4 0 0 0-1.594 1.039c-.5.5-.808 1.002-1.038 1.594-.223.572-.375 1.226-.419 2.184C3.01 9.249 3 9.556 3 12s.01 2.751.054 3.711c.044.958.196 1.612.418 2.185.23.592.538 1.094 1.038 1.594s1.002.808 1.594 1.038c.572.222 1.227.375 2.185.418.96.044 1.267.054 3.711.054s2.751-.01 3.711-.054c.958-.044 1.612-.196 2.185-.418a4.4 4.4 0 0 0 1.594-1.038c.5-.5.808-1.002 1.038-1.594.222-.572.375-1.227.418-2.185.044-.96.054-1.267.054-3.711s-.01-2.751-.054-3.711c-.044-.958-.196-1.612-.418-2.185A4.4 4.4 0 0 0 19.49 4.51c-.5-.5-1.002-.808-1.594-1.038-.572-.222-1.227-.375-2.185-.418C14.751 3.01 14.444 3 12 3m0 4.378a4.622 4.622 0 1 0 0 9.244 4.622 4.622 0 0 0 0-9.244M12 15a3 3 0 1 1 0-6 3 3 0 0 1 0 6m4.804-8.884a1.08 1.08 0 1 0 .001 2.161 1.08 1.08 0 0 0-.001-2.161"})))},{name:"json-feed",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"m8.522 17.424.027.027c1.076-1.076 1.854-.993 3.154.306l2.053 2.053c2.136 2.136 4.131 2.028 6.515-.356l.729-.728-1.548-1.548-.373.373c-1.349 1.349-2.293 1.366-3.585.075l-2.409-2.409c-1.242-1.242-2.475-1.366-3.659-.381l-.232-.232c1.01-1.225.911-2.368-.29-3.568l-2.16-2.162c-1.317-1.317-1.308-2.236.058-3.602l.372-.372-1.54-1.54-.728.729c-2.393 2.393-2.525 4.346-.439 6.433l1.78 1.78c1.3 1.3 1.383 2.095.315 3.163l.008.008a1.384 1.384 0 0 0 1.952 1.951"}),React.createElement("circle",{cx:"13.089",cy:"10.905",r:"1.383"}),React.createElement("circle",{cx:"16.349",cy:"7.644",r:"1.383"}),React.createElement("circle",{cx:"19.61",cy:"4.383",r:"1.383"})))},{name:"line",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M14.255 9.572v3.333c0 .084-.066.15-.15.15h-.534a.16.16 0 0 1-.122-.061l-1.528-2.063v1.978c0 .084-.066.15-.15.15h-.534a.15.15 0 0 1-.15-.15V9.576c0-.084.066-.15.15-.15h.529a.14.14 0 0 1 .122.066l1.528 2.063V9.577c0-.084.066-.15.15-.15h.534a.15.15 0 0 1 .155.145m-3.844-.15h-.534a.15.15 0 0 0-.15.15v3.333c0 .084.066.15.15.15h.534c.084 0 .15-.066.15-.15V9.572c0-.08-.066-.15-.15-.15m-1.289 2.794H7.664V9.572a.15.15 0 0 0-.15-.15H6.98a.15.15 0 0 0-.15.15v3.333q0 .062.042.103a.16.16 0 0 0 .103.042h2.142c.084 0 .15-.066.15-.15v-.534a.15.15 0 0 0-.145-.15m7.945-2.794h-2.142c-.08 0-.15.066-.15.15v3.333c0 .08.066.15.15.15h2.142c.084 0 .15-.066.15-.15v-.534a.15.15 0 0 0-.15-.15h-1.458v-.563h1.458c.084 0 .15-.066.15-.15v-.539a.15.15 0 0 0-.15-.15h-1.458v-.563h1.458c.084 0 .15-.066.15-.15v-.534c-.005-.08-.07-.15-.15-.15M22.5 5.33v13.373c-.005 2.1-1.725 3.802-3.83 3.797H5.297c-2.1-.005-3.802-1.73-3.797-3.83V5.297c.005-2.1 1.73-3.802 3.83-3.797h13.373c2.1.005 3.802 1.725 3.797 3.83m-2.888 5.747c0-3.422-3.431-6.206-7.645-6.206s-7.645 2.784-7.645 6.206c0 3.066 2.719 5.634 6.394 6.122.895.192.792.52.591 1.725-.033.192-.155.755.661.413s4.402-2.592 6.009-4.439c1.106-1.219 1.636-2.452 1.636-3.82"})))},{name:"link",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M17 13H7v-2h10zm1-6h-1c-1.631 0-3.065.792-3.977 2H18c1.103 0 2 .897 2 2v2c0 1.103-.897 2-2 2h-4.977c.913 1.208 2.347 2 3.977 2h1a4 4 0 0 0 4-4v-2a4 4 0 0 0-4-4M2 11v2a4 4 0 0 0 4 4h1c1.63 0 3.065-.792 3.977-2H6c-1.103 0-2-.897-2-2v-2c0-1.103.897-2 2-2h4.977C10.065 7.792 8.631 7 7 7H6a4 4 0 0 0-4 4"})))},{name:"linkedin",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19.7 3H4.3A1.3 1.3 0 0 0 3 4.3v15.4A1.3 1.3 0 0 0 4.3 21h15.4a1.3 1.3 0 0 0 1.3-1.3V4.3A1.3 1.3 0 0 0 19.7 3M8.339 18.338H5.667v-8.59h2.672zM7.004 8.574a1.548 1.548 0 1 1-.002-3.096 1.548 1.548 0 0 1 .002 3.096m11.335 9.764H15.67v-4.177c0-.996-.017-2.278-1.387-2.278-1.389 0-1.601 1.086-1.601 2.206v4.249h-2.667v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.779 3.203 4.092v4.711z"})))},{name:"mail",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M20 4H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2m0 4.236-8 4.882-8-4.882V6h16z"})))},{name:"mastodon",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M11.973 2.352c-2.468.02-4.842.286-6.225.921 0 0-2.742 1.229-2.742 5.415 0 .958-.018 2.105.012 3.32.1 4.094.75 8.128 4.535 9.129 1.745.462 3.244.56 4.45.494 2.19-.122 3.417-.781 3.417-.781l-.072-1.588s-1.565.491-3.32.431c-1.74-.06-3.576-.188-3.858-2.324a4 4 0 0 1-.04-.598s1.709.416 3.874.516c1.324.06 2.563-.076 3.824-.226 2.418-.29 4.524-1.78 4.79-3.141.416-2.144.38-5.232.38-5.232 0-4.186-2.74-5.415-2.74-5.415-1.383-.635-3.76-.9-6.227-.921zM9.18 5.622c1.028 0 1.804.395 2.318 1.185l.502.84.5-.84c.514-.79 1.292-1.186 2.32-1.186.888 0 1.605.313 2.15.922q.795.915.794 2.469v5.068h-2.008V9.16c0-1.037-.438-1.562-1.31-1.562-.966 0-1.448.622-1.448 1.857v2.693h-1.996V9.455c0-1.235-.484-1.857-1.45-1.857-.872 0-1.308.525-1.308 1.562v4.92H6.236V9.012q-.001-1.554.793-2.469c.547-.609 1.263-.922 2.15-.922"})))},{name:"medium-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{fillRule:"nonzero",d:"M7.423 6c3.27 0 5.922 2.686 5.922 6s-2.651 6-5.922 6S1.5 15.313 1.5 12s2.652-6 5.923-6m9.458.351c1.635 0 2.961 2.53 2.961 5.65 0 3.118-1.325 5.648-2.96 5.648S13.92 15.119 13.92 12s1.325-5.649 2.96-5.649m4.577.589c.576 0 1.042 2.265 1.042 5.06s-.466 5.06-1.042 5.06c-.575 0-1.04-2.265-1.04-5.06s.465-5.06 1.04-5.06"})))},{name:"medium",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M3 3v18h18V3zm15 4.26-1 .93a.28.28 0 0 0-.11.27v6.8a.27.27 0 0 0 .11.27l.94.93v.2h-4.75v-.2l1-1c.09-.1.09-.12.09-.27V9.74l-2.71 6.9h-.37L8 9.74v4.62a.67.67 0 0 0 .17.54l1.27 1.54v.2H5.86v-.2l1.27-1.54a.64.64 0 0 0 .17-.54V9a.5.5 0 0 0-.16-.4L6 7.26v-.2h3.52L12.23 13l2.38-5.94H18z"})))},{name:"messenger",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12.026.375C5.462.375.375 5.172.375 11.652c0 3.389 1.393 6.318 3.66 8.341.391.352.311.556.377 2.73a.934.934 0 0 0 1.307.823c2.48-1.092 2.512-1.178 2.933-1.064 7.185 1.977 14.973-2.621 14.973-10.83 0-6.48-5.035-11.277-11.599-11.277m6.996 8.678L15.6 14.47a1.75 1.75 0 0 1-2.527.465l-2.723-2.038a.7.7 0 0 0-.844 0l-3.674 2.786c-.49.372-1.133-.216-.802-.735l3.422-5.417a1.75 1.75 0 0 1 2.527-.465l2.722 2.037a.7.7 0 0 0 .844 0L18.22 8.32c.489-.374 1.132.213.801.732"})))},{name:"microblog",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19.641 17.086c1.294-1.522 2.067-3.438 2.067-5.521 0-4.957-4.371-8.972-9.763-8.972s-9.763 4.015-9.763 8.972 4.371 8.972 9.763 8.972a10.5 10.5 0 0 0 3.486-.59.315.315 0 0 1 .356.112c.816 1.101 2.09 1.876 3.506 2.191a.194.194 0 0 0 .192-.309 3.82 3.82 0 0 1 .162-4.858zm-3.065-6.575-2.514 1.909.912 3.022a.286.286 0 0 1-.437.317l-2.592-1.802-2.592 1.802a.285.285 0 0 1-.436-.317l.912-3.022-2.515-1.909a.285.285 0 0 1 .167-.513l3.155-.066 1.038-2.981a.285.285 0 0 1 .539 0l1.038 2.981 3.155.066a.285.285 0 0 1 .17.513"})))},{name:"nextdoor",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",strokeMiterlimit:"10",viewBox:"0 0 130 130"},React.createElement("g",null,React.createElement("path",{d:"M64.25 3.531c-31.144.337-57.596 24.22-60.469 55.907-3.064 33.799 21.857 63.685 55.657 66.75s63.685-21.857 66.75-55.657-21.857-63.686-55.657-66.75a62 62 0 0 0-6.281-.25m3.938 34.907C82.468 38.438 93.5 48.58 93.5 61.5v27c0 .685-.565 1.25-1.25 1.25H80.906a1.267 1.267 0 0 1-1.25-1.25V63.375c0-5.58-4.309-11.937-11.469-11.937-7.47 0-11.468 6.357-11.468 11.937V88.5c0 .685-.565 1.25-1.25 1.25H44.125c-.68 0-1.219-.57-1.219-1.25V64.156c0-.74-.529-1.364-1.25-1.531-13.13-2.93-15.115-10.285-15.375-21.125-.005-.332.142-.67.375-.906.233-.237.543-.375.875-.375l11.688.062c.66.01 1.187.529 1.218 1.188.13 4.44.438 9.406 4.438 9.406.83 0 1.443-1.179 1.813-1.719 4.41-6.48 12.28-10.718 21.5-10.718"})))},{name:"patreon",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M20 7.408c-.003-2.299-1.746-4.182-3.79-4.862-2.54-.844-5.888-.722-8.312.453-2.939 1.425-3.862 4.545-3.896 7.656-.028 2.559.22 9.297 3.92 9.345 2.75.036 3.159-3.603 4.43-5.356.906-1.247 2.071-1.599 3.506-1.963 2.465-.627 4.146-2.626 4.142-5.273"})))},{name:"pinterest-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12.289 2C6.617 2 3.606 5.648 3.606 9.622c0 1.846 1.025 4.146 2.666 4.878.25.111.381.063.439-.169.044-.175.267-1.029.365-1.428a.37.37 0 0 0-.091-.362c-.54-.63-.975-1.791-.975-2.873 0-2.777 2.194-5.464 5.933-5.464 3.23 0 5.49 2.108 5.49 5.122 0 3.407-1.794 5.768-4.13 5.768-1.291 0-2.257-1.021-1.948-2.277.372-1.495 1.089-3.112 1.089-4.191 0-.967-.542-1.775-1.663-1.775-1.319 0-2.379 1.309-2.379 3.059 0 1.115.394 1.869.394 1.869s-1.302 5.279-1.54 6.261c-.405 1.666.053 4.368.094 4.604.021.126.167.169.25.063.129-.165 1.699-2.419 2.142-4.051.158-.59.817-2.995.817-2.995.43.784 1.681 1.446 3.013 1.446 3.963 0 6.822-3.494 6.822-7.833C20.394 5.112 16.849 2 12.289 2"})))},{name:"pinterest",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12c0 4.236 2.636 7.855 6.356 9.312-.087-.791-.166-2.005.035-2.869.182-.78 1.173-4.971 1.173-4.971s-.299-.599-.299-1.484c0-1.39.806-2.429 1.809-2.429.853 0 1.265.641 1.265 1.409 0 .858-.546 2.141-.828 3.329-.236.996.499 1.807 1.481 1.807 1.777 0 3.144-1.874 3.144-4.579 0-2.394-1.72-4.068-4.177-4.068-2.845 0-4.515 2.134-4.515 4.34 0 .859.331 1.781.744 2.282a.3.3 0 0 1 .069.287c-.077.316-.246.995-.279 1.134-.044.183-.145.222-.334.134-1.249-.581-2.03-2.407-2.03-3.874 0-3.154 2.292-6.051 6.607-6.051 3.469 0 6.165 2.472 6.165 5.775 0 3.446-2.173 6.22-5.189 6.22-1.013 0-1.966-.526-2.292-1.148l-.623 2.377c-.226.869-.835 1.957-1.243 2.622.936.289 1.93.445 2.961.445 5.523 0 10-4.477 10-10S17.523 2 12 2"})))},{name:"pocket",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M21.927 4.194A1.82 1.82 0 0 0 20.222 3H3.839a1.823 1.823 0 0 0-1.813 1.814v6.035l.069 1.2c.29 2.73 1.707 5.115 3.899 6.778l.119.089.025.018a9.9 9.9 0 0 0 3.91 1.727 10.06 10.06 0 0 0 4.049-.014.3.3 0 0 0 .064-.023 9.9 9.9 0 0 0 3.753-1.691l.025-.018q.06-.043.119-.089c2.192-1.664 3.609-4.049 3.898-6.778l.069-1.2V4.814a1.8 1.8 0 0 0-.098-.62m-4.235 6.287-4.704 4.512a1.37 1.37 0 0 1-1.898 0l-4.705-4.512a1.371 1.371 0 1 1 1.898-1.979l3.756 3.601 3.755-3.601a1.372 1.372 0 0 1 1.898 1.979"})))},{name:"polldaddy",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.487 2 2 6.487 2 12c0 5.514 4.487 10 10 10 5.514 0 10-4.486 10-10 0-5.513-4.486-10-10-10m.991 1.68c2.361.084 4.657 1.251 6.197 3.136.283.334.541.693.774 1.067a7.78 7.78 0 0 0-6.094-2.94 7.76 7.76 0 0 0-5.896 2.703q-.008.006-.016.014l-.152.159-.031.032a6.12 6.12 0 0 0-1.633 4.165 6.15 6.15 0 0 0 6.143 6.143c.57 0 1.123-.081 1.649-.227-1.849.839-4.131.747-5.926-.324-1.841-1.089-3.171-3.111-3.433-5.313A7.39 7.39 0 0 1 6.69 6.137C8.294 4.5 10.634 3.563 12.991 3.68m3.373 8.519c-.049-2.024-1.587-3.889-3.544-4.174-1.927-.343-3.917.857-4.451 2.661a3.67 3.67 0 0 0 .2 2.653c.39.8 1.067 1.451 1.894 1.759 1.664.654 3.63-.27 4.173-1.863.593-1.58-.396-3.423-1.94-3.776-1.52-.407-3.161.757-3.204 2.243a2.36 2.36 0 0 0 .753 1.879c.501.476 1.23.667 1.871.529a2.07 2.07 0 0 0 1.469-1.134 1.91 1.91 0 0 0-.087-1.767c-.297-.513-.859-.863-1.429-.881a1.7 1.7 0 0 0-1.437.679 1.53 1.53 0 0 0-.18 1.489q.006.016.016.03c.193.634.774 1.1 1.467 1.117a1.6 1.6 0 0 1-.97-.183c-.466-.244-.809-.747-.893-1.29a1.8 1.8 0 0 1 .499-1.539 2.02 2.02 0 0 1 1.58-.606c.593.04 1.159.35 1.517.859.364.496.51 1.156.383 1.773-.116.62-.529 1.174-1.093 1.514a2.52 2.52 0 0 1-1.914.286c-.65-.161-1.226-.606-1.584-1.206a2.83 2.83 0 0 1-.341-2.031c.143-.7.573-1.321 1.176-1.753 1.193-.883 3.056-.751 4.106.411 1.106 1.1 1.327 3.027.406 4.371-.877 1.376-2.74 2.086-4.374 1.594-1.639-.449-2.913-2.079-3.031-3.853-.07-.884.13-1.797.583-2.577.445-.777 1.155-1.432 1.972-1.862 1.64-.88 3.816-.743 5.349.424 1.251.924 2.083 2.42 2.236 4.009l.001.03c0 2.9-2.359 5.26-5.26 5.26a5.2 5.2 0 0 1-1.947-.376 5 5 0 0 0 2.613-.079 4.96 4.96 0 0 0 2.514-1.751c.618-.828.95-1.861.901-2.869M12 21.113c-5.024 0-9.111-4.087-9.111-9.113 0-4.789 3.713-8.723 8.411-9.081a7 7 0 0 0-.397.06c-2.644.453-5.017 2.106-6.32 4.409-1.309 2.301-1.391 5.19-.3 7.527 1.056 2.34 3.253 4.156 5.776 4.553 2.497.44 5.133-.483 6.787-2.301 1.719-1.797 2.269-4.529 1.486-6.796-.583-1.81-1.976-3.331-3.7-4.046 3.417.594 6.174 3.221 6.174 6.781 0 1.004-.241 2.02-.657 2.966-1.498 2.984-4.586 5.041-8.149 5.041"})))},{name:"print",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M9 16h6v2H9zm13 1h-3v3a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-3H2V9a2 2 0 0 1 2-2h1V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v2h1a2 2 0 0 1 2 2zM7 7h10V5H7zm10 7H7v6h10zm3-3.5a1.5 1.5 0 1 0-3.001.001A1.5 1.5 0 0 0 20 10.5"})))},{name:"quora",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M47.736 16.521c-.41-.81-.898-1.631-1.846-1.631a1 1 0 0 0-.527.107l-.322-.644a2.93 2.93 0 0 1 1.836-.595c1.26 0 1.914.605 2.431 1.397a6.8 6.8 0 0 0 .449-2.675c0-2.773-.869-4.199-2.929-4.199-1.992 0-2.851 1.465-2.851 4.199s.859 4.17 2.851 4.17a4 4 0 0 0 .869-.107zm.498.966a6 6 0 0 1-1.367.185 5.27 5.27 0 0 1-5.263-5.204c0-3.114 2.558-5.233 5.263-5.233s5.282 2.109 5.282 5.233a5.08 5.08 0 0 1-1.992 4.072c.381.566.781.956 1.319.956.595 0 .839-.459.878-.82h.781c.049.488-.195 2.48-2.373 2.48-1.319 0-2.012-.761-2.529-1.66zm5.624-2.646v-3.563c0-.371-.146-.586-.615-.586h-.498v-.956h3.251v5.048c0 .849.459 1.231 1.161 1.231a1.56 1.56 0 0 0 1.465-.839V11.28c0-.371-.146-.586-.615-.586h-.527v-.957h3.28v5.302c0 .527.195.732.8.732h.107v.976l-2.929.468V16.21h-.057a3.12 3.12 0 0 1-2.509 1.152c-1.28 0-2.304-.644-2.304-2.558zm12.059 1.611c1.152 0 1.592-1.005 1.611-3.027.02-1.982-.459-2.929-1.611-2.929-1.005 0-1.641.956-1.641 2.929 0 2.021.625 3.027 1.641 3.027m0 .956a3.906 3.906 0 0 1-3.974-3.974c0-2.334 1.836-3.886 3.974-3.886 2.226 0 4.004 1.582 4.004 3.886a3.867 3.867 0 0 1-4.004 3.974m4.072-.146v-.956h.312c.781 0 .859-.224.859-.908v-4.121c0-.371-.215-.586-.732-.586h-.42v-.955h2.968l.146 1.553h.108c.371-1.113 1.221-1.699 2.051-1.699.693 0 1.221.39 1.221 1.181 0 .547-.264 1.093-1.005 1.093-.664 0-.8-.449-1.358-.449-.488 0-.869.468-.869 1.152v2.783c0 .673.166.908.937.908h.439v.956h-4.658zm9.901-1.093c.956 0 1.338-.898 1.338-1.797v-1.211c-.732.722-2.304.742-2.304 2.021 0 .625.371.986.966.986m1.387 0c-.39.752-1.191 1.26-2.314 1.26-1.309 0-2.148-.732-2.148-1.914 0-2.451 3.417-1.797 4.423-3.427v-.185c0-1.25-.488-1.445-1.035-1.445-1.524 0-.83 1.631-2.226 1.631-.673 0-.937-.371-.937-.859 0-.927 1.093-1.67 3.173-1.67 1.963 0 3.163.537 3.163 2.49v3.114q-.02.742.595.742a1 1 0 0 0 .449-.127l.254.615c-.205.312-.752.869-1.836.869-.908 0-1.465-.42-1.543-1.113h-.01zm-68.554 2.558c-.83-1.641-1.807-3.3-3.711-3.3a2.9 2.9 0 0 0-1.093.215l-.644-1.299a5.66 5.66 0 0 1 3.662-1.211c2.548 0 3.857 1.231 4.892 2.792q.917-2.012.908-5.38c0-5.585-1.748-8.417-5.829-8.417-4.033 0-5.76 2.87-5.76 8.417s1.738 8.397 5.76 8.397a5.9 5.9 0 0 0 1.748-.224zm.996 1.953a9.8 9.8 0 0 1-2.744.371C5.614 21.041.371 16.764.371 10.545.371 4.277 5.614 0 10.965 0c5.448 0 10.642 4.248 10.642 10.545a10.25 10.25 0 0 1-4.013 8.201c.732 1.152 1.563 1.914 2.665 1.914 1.201 0 1.689-.927 1.768-1.66h1.572c.088.966-.4 4.999-4.775 4.999-2.646 0-4.052-1.543-5.106-3.339z"})))},{name:"reddit",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M22 11.816a2.28 2.28 0 0 0-2.277-2.277c-.593 0-1.122.24-1.526.614-1.481-.965-3.455-1.594-5.647-1.69l1.171-3.702 3.18.748a1.88 1.88 0 0 0 1.876 1.862 1.88 1.88 0 0 0 1.877-1.878 1.88 1.88 0 0 0-1.877-1.877c-.769 0-1.431.466-1.72 1.13l-3.508-.826a.386.386 0 0 0-.46.261l-1.35 4.268c-2.316.038-4.411.67-5.97 1.671a2.24 2.24 0 0 0-1.492-.581A2.28 2.28 0 0 0 2 11.816c0 .814.433 1.523 1.078 1.925a4 4 0 0 0-.061.672c0 3.292 4.011 5.97 8.941 5.97s8.941-2.678 8.941-5.97q-.002-.32-.053-.632A2.26 2.26 0 0 0 22 11.816m-3.224-7.422a1.1 1.1 0 1 1-.001 2.199 1.1 1.1 0 0 1 .001-2.199M2.777 11.816c0-.827.672-1.5 1.499-1.5.313 0 .598.103.838.269-.851.676-1.477 1.479-1.812 2.36a1.48 1.48 0 0 1-.525-1.129m9.182 7.79c-4.501 0-8.164-2.329-8.164-5.193S7.457 9.22 11.959 9.22s8.164 2.329 8.164 5.193-3.663 5.193-8.164 5.193m8.677-6.605c-.326-.89-.948-1.701-1.797-2.384.248-.186.55-.301.883-.301.827 0 1.5.673 1.5 1.5.001.483-.23.911-.586 1.185m-11.64 1.703c-.76 0-1.397-.616-1.397-1.376s.637-1.397 1.397-1.397 1.376.637 1.376 1.397-.616 1.376-1.376 1.376m7.405-1.376c0 .76-.616 1.376-1.376 1.376s-1.399-.616-1.399-1.376.639-1.397 1.399-1.397 1.376.637 1.376 1.397m-1.172 3.38a.39.39 0 0 1 0 .55c-.674.674-1.727 1.002-3.219 1.002l-.011-.002-.011.002c-1.492 0-2.544-.328-3.218-1.002a.389.389 0 1 1 .55-.55c.521.521 1.394.775 2.669.775l.011.002.011-.002c1.275 0 2.148-.253 2.669-.775a.387.387 0 0 1 .549 0"})))},{name:"share",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M18 16c-.788 0-1.499.31-2.034.807L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.048 4.118A3 3 0 0 0 15 19a3 3 0 1 0 3-3"})))},{name:"skype",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"m10.113 2.699.1-.02q.05.025.098.051zM2.72 10.223l-.017.103q.025.048.051.095zm18.555 3.548q.009-.053.018-.106-.025-.047-.052-.095zm-7.712 7.428q.049.027.096.053l.105-.017zM22 16.386a5.55 5.55 0 0 1-1.637 3.953 5.55 5.55 0 0 1-3.953 1.637 5.6 5.6 0 0 1-2.75-.725l.105-.017-.202-.035q.049.027.096.053a9.5 9.5 0 0 1-1.654.147 9.4 9.4 0 0 1-3.676-.743 9.4 9.4 0 0 1-3.002-2.023 9.4 9.4 0 0 1-2.023-3.002 9.4 9.4 0 0 1-.743-3.676c0-.546.049-1.093.142-1.628q.025.048.051.095l-.034-.199-.017.103A5.6 5.6 0 0 1 2 7.615c0-1.493.582-2.898 1.637-3.953A5.56 5.56 0 0 1 7.59 2.024c.915 0 1.818.228 2.622.655l-.1.02.199.031q-.049-.026-.098-.051l.004-.001a9.5 9.5 0 0 1 1.788-.169 9.41 9.41 0 0 1 6.678 2.766 9.4 9.4 0 0 1 2.024 3.002 9.4 9.4 0 0 1 .743 3.676c0 .575-.054 1.15-.157 1.712q-.025-.047-.052-.095l.034.201q.009-.053.018-.106c.461.829.707 1.767.707 2.721m-5.183-2.248c0-1.331-.613-2.743-3.033-3.282l-2.209-.49c-.84-.192-1.807-.444-1.807-1.237s.679-1.348 1.903-1.348c2.468 0 2.243 1.696 3.468 1.696.645 0 1.209-.379 1.209-1.031 0-1.521-2.435-2.663-4.5-2.663-2.242 0-4.63.952-4.63 3.488 0 1.221.436 2.521 2.839 3.123l2.984.745c.903.223 1.129.731 1.129 1.189 0 .762-.758 1.507-2.129 1.507-2.679 0-2.307-2.062-3.743-2.062-.645 0-1.113.444-1.113 1.078 0 1.236 1.501 2.886 4.856 2.886 3.195 0 4.776-1.538 4.776-3.599"})))},{name:"sms",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M17.696 4C20.069 4 22 5.973 22 8.398v4.357c0 2.04-1.368 3.783-3.261 4.266v4.427l-5.234-4.295h-7.2C3.93 17.153 2 15.18 2 12.755V8.398C2 5.973 3.931 4 6.304 4zM7.028 8.515c-.98 0-1.66.562-1.66 1.349-.009.497.322.91.985 1.178l.39.142c.242.097.305.171.305.297 0 .162-.131.251-.442.251s-.76-.135-1.004-.284l-.112.046-.215.868c.359.258.832.364 1.33.364 1.104 0 1.764-.523 1.764-1.333-.008-.574-.305-.956-.954-1.216l-.393-.146c-.266-.108-.341-.181-.341-.287 0-.152.131-.243.387-.243.274 0 .587.093.808.214l.109-.047.214-.837c-.315-.224-.741-.316-1.171-.316m10.302 0c-.98 0-1.66.562-1.66 1.349-.008.497.322.91.985 1.178l.39.142c.243.097.305.171.305.297 0 .162-.13.251-.442.251-.311 0-.76-.135-1.004-.284l-.112.046-.215.868c.359.258.832.364 1.33.364 1.104 0 1.764-.523 1.764-1.333-.008-.574-.305-.956-.954-1.216l-.393-.146c-.266-.108-.341-.181-.341-.287 0-.152.131-.243.387-.243.274 0 .587.093.808.214l.109-.047.214-.837c-.316-.224-.741-.316-1.171-.316m-3.733 0c-.297 0-.55.066-.78.202l-.144.098a2 2 0 0 0-.264.247l-.078.095-.027-.077c-.15-.34-.55-.565-1.033-.565l-.169.007a1.36 1.36 0 0 0-.896.42l-.08.09-.038-.363-.075-.067H8.994l-.075.079.024.634c.005.2.008.397.008.604v2.652l.075.075h1.178l.075-.075v-2.269q-.002-.168.042-.274c.083-.23.262-.392.496-.392.314 0 .483.267.483.753v2.182l.075.075h1.179l.075-.075v-2.277c0-.097.016-.213.043-.285.077-.224.26-.373.486-.373.33 0 .5.272.5.817v2.118l.074.075h1.179l.075-.075v-2.293c0-1.131-.537-1.763-1.39-1.763Z"})))},{name:"snapchat",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M11.989 1.728c3.221.001 5.904 2.683 5.908 5.912q.002 1.133.067 2.094a.737.737 0 0 0 .902.669l1.009-.237a.6.6 0 0 1 .129-.015c.256 0 .492.175.55.434a.74.74 0 0 1-.479.861l-1.532.618a.823.823 0 0 0-.485.98c1.229 4.543 4.661 4.071 4.661 4.662 0 .743-2.587.848-2.821 1.082s-.01 1.368-.532 1.588a1.1 1.1 0 0 1-.409.056c-.393 0-.95-.077-1.536-.077-.509 0-1.04.058-1.507.273-1.239.572-2.433 1.641-3.914 1.641S9.325 21.2 8.086 20.628c-.467-.216-.998-.273-1.507-.273-.586 0-1.143.077-1.536.077-.17 0-.31-.014-.409-.056-.522-.22-.299-1.354-.532-1.588s-2.821-.337-2.821-1.08c0-.592 3.432-.119 4.661-4.662a.824.824 0 0 0-.486-.98l-1.532-.618a.74.74 0 0 1-.479-.861.56.56 0 0 1 .679-.419l1.009.237q.086.02.169.02a.737.737 0 0 0 .733-.689q.065-.961.067-2.094c.004-3.229 2.666-5.91 5.887-5.912m0-1.281c-.961 0-1.898.194-2.784.574A7.2 7.2 0 0 0 6.93 2.572a7.2 7.2 0 0 0-1.539 2.282A7.1 7.1 0 0 0 4.82 7.64a33 33 0 0 1-.029 1.369l-.375-.088a2 2 0 0 0-.421-.049 1.86 1.86 0 0 0-1.135.389 1.84 1.84 0 0 0-.666 1.049 2.024 2.024 0 0 0 1.271 2.335l1.124.454c-.744 2.285-2.117 2.723-3.041 3.018a5 5 0 0 0-.659.246C.087 16.76 0 17.436 0 17.708c0 .521.247.996.694 1.339.223.17.499.311.844.43.47.162 1.016.265 1.459.347.021.164.053.341.106.518.22.738.684 1.069 1.034 1.217.332.14.676.156.905.156.224 0 .462-.018.713-.036.269-.02.548-.041.823-.041.426 0 .743.051.97.155.311.144.64.337.989.542.972.571 2.073 1.217 3.462 1.217s2.49-.647 3.462-1.217c.349-.205.679-.399.989-.542.226-.105.544-.155.97-.155.275 0 .554.021.823.041.251.019.488.036.713.036.229 0 .573-.016.905-.156.35-.147.814-.478 1.034-1.217.053-.178.084-.354.106-.518.443-.082.989-.185 1.459-.347.345-.119.621-.259.844-.43.448-.342.694-.818.694-1.339 0-.272-.087-.948-.891-1.347a5 5 0 0 0-.659-.246c-.924-.295-2.297-.733-3.041-3.018l1.124-.454a2.025 2.025 0 0 0 1.271-2.335 1.83 1.83 0 0 0-.666-1.049 1.86 1.86 0 0 0-1.556-.34l-.375.088a33 33 0 0 1-.029-1.369 7.1 7.1 0 0 0-.575-2.789c-.365-.853-.886-1.62-1.547-2.282s-1.428-1.182-2.28-1.547a7.1 7.1 0 0 0-2.786-.574"})))},{name:"soundcloud",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M23.587 13.923a3.303 3.303 0 0 1-3.344 3.117h-8.037a.674.674 0 0 1-.667-.67V7.717a.74.74 0 0 1 .444-.705s.739-.512 2.296-.512a5.27 5.27 0 0 1 2.702.742 5.35 5.35 0 0 1 2.516 3.485 3.1 3.1 0 0 1 .852-.116 3.217 3.217 0 0 1 3.237 3.312m-13.05-5.659c.242 2.935.419 5.612 0 8.538a.261.261 0 0 1-.519 0c-.39-2.901-.221-5.628 0-8.538a.26.26 0 0 1 .398-.25.26.26 0 0 1 .12.25zm-1.627 8.541a.273.273 0 0 1-.541 0 32.7 32.7 0 0 1 0-7.533.274.274 0 0 1 .544 0 29.4 29.4 0 0 1-.003 7.533m-1.63-7.788c.264 2.69.384 5.099-.003 7.782a.262.262 0 0 1-.522 0c-.374-2.649-.249-5.127 0-7.782a.264.264 0 0 1 .525 0m-1.631 7.792a.268.268 0 0 1-.532 0 27.6 27.6 0 0 1 0-7.034.27.27 0 1 1 .541 0 25.8 25.8 0 0 1-.01 7.034zm-1.63-5.276c.412 1.824.227 3.435-.015 5.294a.255.255 0 0 1-.504 0c-.22-1.834-.402-3.482-.015-5.295a.268.268 0 0 1 .535 0m-1.626-.277c.378 1.869.254 3.451-.01 5.325-.031.277-.506.28-.531 0-.239-1.846-.352-3.476-.01-5.325a.277.277 0 0 1 .551 0m-1.643.907c.396 1.239.261 2.246-.016 3.517a.258.258 0 0 1-.514 0c-.239-1.246-.336-2.274-.021-3.517a.276.276 0 0 1 .55 0z"})))},{name:"spotify",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m4.586 14.424a.62.62 0 0 1-.857.207c-2.348-1.435-5.304-1.76-8.785-.964a.622.622 0 1 1-.277-1.215c3.809-.871 7.077-.496 9.713 1.115a.623.623 0 0 1 .206.857M17.81 13.7a.78.78 0 0 1-1.072.257c-2.687-1.652-6.785-2.131-9.965-1.166A.779.779 0 1 1 6.32 11.3c3.632-1.102 8.147-.568 11.234 1.328a.78.78 0 0 1 .256 1.072m.105-2.835c-3.223-1.914-8.54-2.09-11.618-1.156a.935.935 0 1 1-.542-1.79c3.532-1.072 9.404-.865 13.115 1.338a.936.936 0 1 1-.955 1.608"})))},{name:"squarespace",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M20.87 9.271a3.86 3.86 0 0 0-5.458 0l-6.141 6.141a.964.964 0 1 0 1.365 1.364l6.14-6.14a1.929 1.929 0 1 1 2.729 2.729l-6.022 6.022a1.93 1.93 0 0 0 2.729 0l4.658-4.658a3.86 3.86 0 0 0 0-5.458m-2.047 2.047a.965.965 0 0 0-1.365 0l-6.14 6.14a1.93 1.93 0 0 1-2.729 0 .964.964 0 1 0-1.364 1.364 3.86 3.86 0 0 0 5.458 0l6.14-6.14a.966.966 0 0 0 0-1.364m-2.047-6.141a3.86 3.86 0 0 0-5.458 0l-6.14 6.14a.964.964 0 1 0 1.364 1.364l6.141-6.14a1.93 1.93 0 0 1 2.729 0 .965.965 0 1 0 1.364-1.364m-2.047 2.047a.964.964 0 0 0-1.364 0l-6.14 6.141a1.929 1.929 0 1 1-2.729-2.729l6.022-6.022a1.93 1.93 0 0 0-2.729 0L3.13 9.271a3.86 3.86 0 0 0 5.458 5.458l6.14-6.141a.963.963 0 0 0 .001-1.364"})))},{name:"stackexchange",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M4 11.606h16v3.272H4zM4 7.377h16v3.272H4zM17.514 3H6.55C5.147 3 4 4.169 4 5.614v.848h16v-.85C20 4.167 18.895 3 17.514 3M4 15.813v.85c0 1.445 1.147 2.614 2.55 2.614h6.799v3.463l3.357-3.463h.744c1.402 0 2.55-1.169 2.55-2.614v-.85z"})))},{name:"stackoverflow",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M18.18 20.103V14.78h1.767v7.09H4v-7.09h1.767v5.323z"}),React.createElement("path",{d:"m7.717 14.275 8.673 1.813.367-1.744-8.673-1.813zm1.147-4.13 8.031 3.74.734-1.606-8.031-3.763zm2.226-3.946 6.815 5.667 1.124-1.354-6.815-5.667zM15.495 2l-1.423 1.055 5.277 7.113 1.423-1.055zM7.533 18.314h8.857v-1.767H7.533z"})))},{name:"stumbleupon",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 4.294a4.47 4.47 0 0 0-4.471 4.471v6.353a1.059 1.059 0 1 1-2.118 0v-2.824H2v2.941a4.471 4.471 0 0 0 8.942 0v-6.47a1.059 1.059 0 1 1 2.118 0v1.294l1.412.647 2-.647V8.765A4.473 4.473 0 0 0 12 4.294m1.059 8.059v2.882a4.471 4.471 0 0 0 8.941 0v-2.824h-3.412v2.824a1.059 1.059 0 1 1-2.118 0v-2.882l-2 .647z"})))},{name:"substack",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19.904 9.182H4.095V7.054h15.81v2.127M4.095 11.109V21L12 16.583 19.905 21v-9.891zM19.905 3H4.095v2.127h15.81z"})))},{name:"telegram",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m3.08 14.757s-.25.625-.936.325l-2.541-1.949-1.63 1.486s-.127.096-.266.036c0 0-.12-.011-.27-.486s-.911-2.972-.911-2.972L6 12.349s-.387-.137-.425-.438c-.037-.3.437-.462.437-.462l10.03-3.934s.824-.362.824.238z"})))},{name:"threads",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 192 192"},React.createElement("g",null,React.createElement("path",{d:"M141.537 88.988a67 67 0 0 0-2.518-1.143c-1.482-27.307-16.403-42.94-41.457-43.1h-.34c-14.986 0-27.449 6.396-35.12 18.036l13.779 9.452c5.73-8.695 14.724-10.548 21.348-10.548h.229c8.249.053 14.474 2.452 18.503 7.129 2.932 3.405 4.893 8.111 5.864 14.05-7.314-1.243-15.224-1.626-23.68-1.14-23.82 1.371-39.134 15.264-38.105 34.568.522 9.792 5.4 18.216 13.735 23.719 7.047 4.652 16.124 6.927 25.557 6.412 12.458-.683 22.231-5.436 29.049-14.127 5.178-6.6 8.453-15.153 9.899-25.93 5.937 3.583 10.337 8.298 12.767 13.966 4.132 9.635 4.373 25.468-8.546 38.376-11.319 11.308-24.925 16.2-45.488 16.351-22.809-.169-40.06-7.484-51.275-21.742C35.236 139.966 29.808 120.682 29.605 96c.203-24.682 5.63-43.966 16.133-57.317C56.954 24.425 74.204 17.11 97.013 16.94c22.975.17 40.526 7.52 52.171 21.847 5.71 7.026 10.015 15.86 12.853 26.162l16.147-4.308c-3.44-12.68-8.853-23.606-16.219-32.668C147.036 9.607 125.202.195 97.07 0h-.113C68.882.194 47.292 9.642 32.788 28.08 19.882 44.485 13.224 67.315 13.001 95.932L13 96v.067c.224 28.617 6.882 51.447 19.788 67.854C47.292 182.358 68.882 191.806 96.957 192h.113c24.96-.173 42.554-6.708 57.048-21.189 18.963-18.945 18.392-42.692 12.142-57.27-4.484-10.454-13.033-18.945-24.723-24.553M98.44 129.507c-10.44.588-21.286-4.098-21.82-14.135-.397-7.442 5.296-15.746 22.461-16.735q2.948-.17 5.79-.169c6.235 0 12.068.606 17.371 1.765-1.978 24.702-13.58 28.713-23.802 29.274"})))},{name:"tiktok-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M5 3a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2zm7.531 3h2.053s-.114 2.635 2.85 2.82v2.04s-1.582.099-2.85-.87l.021 4.207a3.804 3.804 0 1 1-3.802-3.802h.533v2.082a1.73 1.73 0 0 0-1.922.648 1.727 1.727 0 0 0 1.947 2.646 1.73 1.73 0 0 0 1.19-1.642z"})))},{name:"tiktok",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12.22 2h3.42s-.19 4.394 4.75 4.702v3.396s-2.636.166-4.75-1.448l.037 7.012a6.338 6.338 0 1 1-6.34-6.339h.89v3.472a2.882 2.882 0 1 0 2.024 2.752z"})))},{name:"tripadvisor",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M21.01 9.859c.236-1.002.985-2.003.985-2.003l-3.341-.002C16.779 6.643 14.502 6 11.979 6 9.363 6 7 6.659 5.135 7.877L2 7.88s.74.988.98 1.983a4.98 4.98 0 0 0-.977 2.961 5.01 5.01 0 0 0 5.009 5.003 5 5 0 0 0 3.904-1.875l1.065 1.592 1.076-1.606a4.96 4.96 0 0 0 1.838 1.448 4.98 4.98 0 0 0 3.831.151 5.01 5.01 0 0 0 2.963-6.431 5 5 0 0 0-.679-1.247m-13.998 6.96a4 4 0 0 1-3.998-3.995 4 4 0 0 1 3.998-3.997 4 4 0 0 1 3.996 3.997 4 4 0 0 1-3.996 3.995m4.987-4.36A5.007 5.007 0 0 0 7.11 7.821c1.434-.613 3.081-.947 4.867-.947 1.798 0 3.421.324 4.853.966a4.984 4.984 0 0 0-4.831 4.619m6.288 4.134a3.97 3.97 0 0 1-3.058-.122 3.96 3.96 0 0 1-2.075-2.245v-.001a3.97 3.97 0 0 1 .118-3.056 3.97 3.97 0 0 1 2.246-2.077 4.005 4.005 0 0 1 5.135 2.366 4.006 4.006 0 0 1-2.366 5.135"}),React.createElement("path",{d:"M6.949 10.307a2.477 2.477 0 0 0-2.475 2.472 2.48 2.48 0 0 0 2.475 2.474 2.474 2.474 0 0 0 0-4.946m0 4.094a1.626 1.626 0 0 1-1.624-1.623 1.621 1.621 0 1 1 1.624 1.623M16.981 10.307a2.477 2.477 0 0 0-2.474 2.472 2.48 2.48 0 0 0 2.474 2.474 2.476 2.476 0 0 0 2.472-2.474 2.475 2.475 0 0 0-2.472-2.472m0 4.094a1.625 1.625 0 0 1-1.622-1.623 1.622 1.622 0 1 1 1.622 1.623"}),React.createElement("path",{d:"M7.778 12.778a.832.832 0 1 1-1.664.002.832.832 0 0 1 1.664-.002M16.981 11.947a.832.832 0 1 0 .002 1.666.832.832 0 0 0-.002-1.666"})))},{name:"tumblr-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M16.749 17.396c-.357.17-1.041.319-1.551.332-1.539.041-1.837-1.081-1.85-1.896V9.847h3.861v-2.91h-3.847V2.039h-2.817c-.046 0-.127.041-.138.144-.165 1.499-.867 4.13-3.783 5.181v2.484h1.945v6.282c0 2.151 1.587 5.206 5.775 5.135 1.413-.024 2.982-.616 3.329-1.126z"})))},{name:"tumblr",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-5.569 14.265c-2.446.042-3.372-1.742-3.372-2.998v-3.668H8.923v-1.45c1.703-.614 2.113-2.15 2.209-3.025.007-.06.054-.084.081-.084h1.645V8.9h2.246v1.7H12.85v3.495c.008.476.182 1.131 1.081 1.107.298-.008.697-.094.906-.194l.54 1.601c-.205.296-1.121.641-1.946.656"})))},{name:"twitch",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M16.499 8.089h-1.636v4.91h1.636zm-4.499 0h-1.637v4.91H12zM4.228 3.178 3 6.451v13.092h4.499V22h2.456l2.454-2.456h3.681L21 14.636V3.178zm15.136 10.638L16.5 16.681H12l-2.453 2.453V16.68H5.863V4.814h13.501z"})))},{name:"twitter-alt",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M22.23 5.924a8.2 8.2 0 0 1-2.357.646 4.12 4.12 0 0 0 1.804-2.27 8.2 8.2 0 0 1-2.606.996 4.103 4.103 0 0 0-6.991 3.742 11.65 11.65 0 0 1-8.457-4.287 4.1 4.1 0 0 0-.556 2.063 4.1 4.1 0 0 0 1.825 3.415 4.1 4.1 0 0 1-1.859-.513v.052a4.104 4.104 0 0 0 3.292 4.023 4.1 4.1 0 0 1-1.853.07 4.11 4.11 0 0 0 3.833 2.85 8.24 8.24 0 0 1-5.096 1.756 8 8 0 0 1-.979-.057 11.6 11.6 0 0 0 6.29 1.843c7.547 0 11.675-6.252 11.675-11.675q0-.267-.012-.531a8.3 8.3 0 0 0 2.047-2.123"})))},{name:"twitter",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-2.534 6.71q.007.148.007.298c0 3.045-2.318 6.556-6.556 6.556a6.5 6.5 0 0 1-3.532-1.035q.27.032.55.032a4.63 4.63 0 0 0 2.862-.986 2.31 2.31 0 0 1-2.152-1.6 2.3 2.3 0 0 0 1.04-.04 2.306 2.306 0 0 1-1.848-2.259v-.029c.311.173.666.276 1.044.288a2.303 2.303 0 0 1-.713-3.076 6.54 6.54 0 0 0 4.749 2.407 2.305 2.305 0 0 1 3.926-2.101 4.6 4.6 0 0 0 1.463-.559 2.3 2.3 0 0 1-1.013 1.275c.466-.056.91-.18 1.323-.363-.31.461-.7.867-1.15 1.192"})))},{name:"untappd",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"m11 13.299-5.824 8.133c-.298.416-.8.635-1.308.572-.578-.072-1.374-.289-2.195-.879S.392 19.849.139 19.323a1.4 1.4 0 0 1 .122-1.425l5.824-8.133a3.1 3.1 0 0 1 1.062-.927l1.146-.604c.23-.121.436-.283.608-.478.556-.631 2.049-2.284 4.696-4.957l.046-.212a.13.13 0 0 1 .096-.1l.146-.037a.135.135 0 0 0 .101-.141l-.015-.18a.13.13 0 0 1 .125-.142c.176-.005.518.046 1.001.393s.64.656.692.824a.13.13 0 0 1-.095.164l-.175.044a.13.13 0 0 0-.101.141l.012.15a.13.13 0 0 1-.063.123l-.186.112c-1.679 3.369-2.764 5.316-3.183 6.046a2.2 2.2 0 0 0-.257.73l-.205 1.281A3.1 3.1 0 0 1 11 13.3zm12.739 4.598-5.824-8.133a3.1 3.1 0 0 0-1.062-.927l-1.146-.605a2.1 2.1 0 0 1-.608-.478 51 51 0 0 0-.587-.654.09.09 0 0 0-.142.018 97 97 0 0 1-1.745 3.223 1.4 1.4 0 0 0-.171.485 3.5 3.5 0 0 0 0 1.103l.01.064c.075.471.259.918.536 1.305l5.824 8.133c.296.413.79.635 1.294.574a4.76 4.76 0 0 0 2.209-.881 4.76 4.76 0 0 0 1.533-1.802 1.4 1.4 0 0 0-.122-1.425zM8.306 3.366l.175.044a.134.134 0 0 1 .101.141l-.012.15a.13.13 0 0 0 .063.123l.186.112q.465.933.869 1.721c.026.051.091.06.129.019q.655-.703 1.585-1.668a.137.137 0 0 0 .003-.19c-.315-.322-.645-.659-1.002-1.02l-.046-.212a.13.13 0 0 0-.096-.099l-.146-.037a.135.135 0 0 1-.101-.141l.015-.18a.13.13 0 0 0-.123-.142c-.175-.005-.518.045-1.002.393-.483.347-.64.656-.692.824a.13.13 0 0 0 .095.164z"})))},{name:"vimeo",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M22.396 7.164q-.139 3.039-4.245 8.32Q13.907 21 10.97 21q-1.82 0-3.079-3.359l-1.68-6.159q-.934-3.36-2.005-3.36-.234.001-1.634.98l-.978-1.261q1.541-1.353 3.037-2.708 2.056-1.774 3.084-1.869 2.429-.234 2.99 3.321.607 3.836.841 4.769.7 3.181 1.542 3.181.653 0 1.963-2.065 1.307-2.063 1.401-3.142.187-1.781-1.401-1.782-.747.001-1.541.341 1.534-5.024 5.862-4.884 3.21.095 3.024 4.161"})))},{name:"vk",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{fillRule:"evenodd",d:"M1.687 1.687C0 3.374 0 6.09 0 11.52v.96c0 5.431 0 8.146 1.687 9.833S6.09 24 11.52 24h.96c5.431 0 8.146 0 9.833-1.687S24 17.91 24 12.48v-.96c0-5.431 0-8.146-1.687-9.833S17.91 0 12.48 0h-.96C6.09 0 3.374 0 1.687 1.687M4.05 7.3c.13 6.24 3.25 9.99 8.72 9.99h.31v-3.57c2.01.2 3.53 1.67 4.14 3.57h2.84c-.78-2.84-2.83-4.41-4.11-5.01 1.28-.74 3.08-2.54 3.51-4.98h-2.58c-.56 1.98-2.22 3.78-3.8 3.95V7.3H10.5v6.92c-1.6-.4-3.62-2.34-3.71-6.92z"})))},{name:"whatsapp",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"m2.048 22 1.406-5.136a9.9 9.9 0 0 1-1.323-4.955C2.133 6.446 6.579 2 12.042 2a9.85 9.85 0 0 1 7.011 2.906 9.85 9.85 0 0 1 2.9 7.011c-.002 5.464-4.448 9.91-9.91 9.91h-.004a9.9 9.9 0 0 1-4.736-1.206zm5.497-3.172.301.179a8.2 8.2 0 0 0 4.193 1.148h.003c4.54 0 8.235-3.695 8.237-8.237a8.2 8.2 0 0 0-2.41-5.828 8.18 8.18 0 0 0-5.824-2.416c-4.544 0-8.239 3.695-8.241 8.237a8.2 8.2 0 0 0 1.259 4.384l.196.312-.832 3.04zm9.49-4.554c-.062-.103-.227-.165-.475-.289s-1.465-.723-1.692-.806-.392-.124-.557.124-.64.806-.784.971-.289.186-.536.062-1.046-.385-1.991-1.229c-.736-.657-1.233-1.468-1.378-1.715s-.015-.382.109-.505c.111-.111.248-.289.371-.434.124-.145.165-.248.248-.413s.041-.31-.021-.434-.557-1.343-.763-1.839c-.202-.483-.407-.417-.559-.425-.144-.007-.31-.009-.475-.009a.9.9 0 0 0-.66.31c-.226.248-.866.847-.866 2.066s.887 2.396 1.011 2.562 1.746 2.666 4.23 3.739c.591.255 1.052.408 1.412.522.593.189 1.133.162 1.56.098.476-.071 1.465-.599 1.671-1.177.206-.58.206-1.075.145-1.179"})))},{name:"woocommerce",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M19 2H5C3.3 2 2 3.3 2 5v11c0 1.7 1.3 3 3 3h4l6 3-1-3h5c1.7 0 3-1.3 3-3V5c0-1.7-1.3-3-3-3m-1.6 4.5c-.4.8-.8 2.1-1 3.9-.3 1.8-.4 3.1-.3 4.1 0 .3 0 .5-.1.7s-.3.4-.6.4-.6-.1-.9-.4c-1-1-1.8-2.6-2.4-4.6-.7 1.4-1.2 2.4-1.6 3.1-.6 1.2-1.2 1.8-1.6 1.9-.3 0-.5-.2-.8-.7-.5-1.4-1.1-4.2-1.7-8.2 0-.3 0-.5.2-.7.1-.2.4-.3.7-.4.5 0 .9.2.9.8.3 2.3.7 4.2 1.1 5.7l2.4-4.5c.2-.4.4-.6.8-.6q.75 0 .9.9c.3 1.4.6 2.6 1 3.7.3-2.7.8-4.7 1.4-5.9.2-.3.4-.5.7-.5.2 0 .5.1.7.2q.3.3.3.6c0 .3 0 .4-.1.5"})))},{name:"wordpress",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M12.158 12.786 9.46 20.625a9 9 0 0 0 5.526-.144 1 1 0 0 1-.065-.124zM3.009 12a8.99 8.99 0 0 0 5.067 8.092L3.788 8.341A8.95 8.95 0 0 0 3.009 12m15.06-.454c0-1.112-.399-1.881-.741-2.48-.456-.741-.883-1.368-.883-2.109 0-.826.627-1.596 1.51-1.596q.06.002.116.007A8.96 8.96 0 0 0 12 3.009a8.98 8.98 0 0 0-7.512 4.052c.211.007.41.011.579.011.94 0 2.396-.114 2.396-.114.484-.028.541.684.057.741 0 0-.487.057-1.029.085l3.274 9.739 1.968-5.901-1.401-3.838c-.484-.028-.943-.085-.943-.085-.485-.029-.428-.769.057-.741 0 0 1.484.114 2.368.114.94 0 2.397-.114 2.397-.114.485-.028.542.684.057.741 0 0-.488.057-1.029.085l3.249 9.665.897-2.996q.684-1.753.684-2.907m1.82-3.86q.06.428.06.924c0 .912-.171 1.938-.684 3.22l-2.746 7.94a8.98 8.98 0 0 0 4.47-7.771 8.9 8.9 0 0 0-1.1-4.313M12 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10"})))},{name:"x",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M13.982 10.622 20.54 3h-1.554l-5.693 6.618L8.745 3H3.5l6.876 10.007L3.5 21h1.554l6.012-6.989L15.868 21h5.245zm-2.128 2.474-.697-.997-5.543-7.93H8l4.474 6.4.697.996 5.815 8.318h-2.387z"})))},{name:"xanga",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M9 9h6v6H9zM3 9h6V3H3zm12 0h6V3h-6zm0 12h6v-6h-6zM3 21h6v-6H3z"})))},{name:"youtube",svg:React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},React.createElement("g",null,React.createElement("path",{d:"M21.8 8.001s-.195-1.378-.795-1.985c-.76-.797-1.613-.801-2.004-.847-2.799-.202-6.997-.202-6.997-.202h-.009s-4.198 0-6.997.202c-.39.047-1.242.051-2.003.847-.6.607-.795 1.985-.795 1.985S2 9.62 2 11.238v1.517c0 1.618.2 3.237.2 3.237s.195 1.378.795 1.985c.761.797 1.76.771 2.205.855 1.6.153 6.8.201 6.8.201s4.203-.006 7.001-.209c.391-.047 1.243-.051 2.004-.847.6-.607.795-1.985.795-1.985s.2-1.618.2-3.237v-1.517c0-1.618-.2-3.237-.2-3.237M9.935 14.594l-.001-5.62 5.404 2.82z"})))}]},58992:(e,t,n)=>{"use strict";n.d(t,{d:()=>p});var c=n(96072),a=n.n(c),i=n(28120),s=n.n(i),o=n(51609),r=n.n(o),l=n(91135);class p extends o.PureComponent{static defaultProps={size:24};static propTypes={icon:s().string.isRequired,size:s().number,onClick:s().func,className:s().string};render(){const{size:e,onClick:t,icon:n,className:c,...i}=this.props,s=["social-logo","social-logo-"+n,c].filter(Boolean).join(" "),o=l.$.find((e=>e.name===n));if(!o)return r().createElement("svg",a()({height:e,width:e},i));return r().cloneElement(o.svg,{className:s,height:e,width:e,onClick:t,...i})}}},26356:(e,t,n)=>{"use strict";n.d(t,{i:()=>a});const c={AED:{symbol:"د.إ.‏",grouping:",",decimal:".",precision:2},AFN:{symbol:"؋",grouping:",",decimal:".",precision:2},ALL:{symbol:"Lek",grouping:".",decimal:",",precision:2},AMD:{symbol:"֏",grouping:",",decimal:".",precision:2},ANG:{symbol:"ƒ",grouping:",",decimal:".",precision:2},AOA:{symbol:"Kz",grouping:",",decimal:".",precision:2},ARS:{symbol:"$",grouping:".",decimal:",",precision:2},AUD:{symbol:"A$",grouping:",",decimal:".",precision:2},AWG:{symbol:"ƒ",grouping:",",decimal:".",precision:2},AZN:{symbol:"₼",grouping:" ",decimal:",",precision:2},BAM:{symbol:"КМ",grouping:".",decimal:",",precision:2},BBD:{symbol:"Bds$",grouping:",",decimal:".",precision:2},BDT:{symbol:"৳",grouping:",",decimal:".",precision:0},BGN:{symbol:"лв.",grouping:" ",decimal:",",precision:2},BHD:{symbol:"د.ب.‏",grouping:",",decimal:".",precision:3},BIF:{symbol:"FBu",grouping:",",decimal:".",precision:0},BMD:{symbol:"$",grouping:",",decimal:".",precision:2},BND:{symbol:"$",grouping:".",decimal:",",precision:0},BOB:{symbol:"Bs",grouping:".",decimal:",",precision:2},BRL:{symbol:"R$",grouping:".",decimal:",",precision:2},BSD:{symbol:"$",grouping:",",decimal:".",precision:2},BTC:{symbol:"Ƀ",grouping:",",decimal:".",precision:2},BTN:{symbol:"Nu.",grouping:",",decimal:".",precision:1},BWP:{symbol:"P",grouping:",",decimal:".",precision:2},BYR:{symbol:"р.",grouping:" ",decimal:",",precision:2},BZD:{symbol:"BZ$",grouping:",",decimal:".",precision:2},CAD:{symbol:"C$",grouping:",",decimal:".",precision:2},CDF:{symbol:"FC",grouping:",",decimal:".",precision:2},CHF:{symbol:"CHF",grouping:"'",decimal:".",precision:2},CLP:{symbol:"$",grouping:".",decimal:",",precision:2},CNY:{symbol:"¥",grouping:",",decimal:".",precision:2},COP:{symbol:"$",grouping:".",decimal:",",precision:2},CRC:{symbol:"₡",grouping:".",decimal:",",precision:2},CUC:{symbol:"CUC",grouping:",",decimal:".",precision:2},CUP:{symbol:"$MN",grouping:",",decimal:".",precision:2},CVE:{symbol:"$",grouping:",",decimal:".",precision:2},CZK:{symbol:"Kč",grouping:" ",decimal:",",precision:2},DJF:{symbol:"Fdj",grouping:",",decimal:".",precision:0},DKK:{symbol:"kr.",grouping:"",decimal:",",precision:2},DOP:{symbol:"RD$",grouping:",",decimal:".",precision:2},DZD:{symbol:"د.ج.‏",grouping:",",decimal:".",precision:2},EGP:{symbol:"ج.م.‏",grouping:",",decimal:".",precision:2},ERN:{symbol:"Nfk",grouping:",",decimal:".",precision:2},ETB:{symbol:"ETB",grouping:",",decimal:".",precision:2},EUR:{symbol:"€",grouping:".",decimal:",",precision:2},FJD:{symbol:"FJ$",grouping:",",decimal:".",precision:2},FKP:{symbol:"£",grouping:",",decimal:".",precision:2},GBP:{symbol:"£",grouping:",",decimal:".",precision:2},GEL:{symbol:"Lari",grouping:" ",decimal:",",precision:2},GHS:{symbol:"₵",grouping:",",decimal:".",precision:2},GIP:{symbol:"£",grouping:",",decimal:".",precision:2},GMD:{symbol:"D",grouping:",",decimal:".",precision:2},GNF:{symbol:"FG",grouping:",",decimal:".",precision:0},GTQ:{symbol:"Q",grouping:",",decimal:".",precision:2},GYD:{symbol:"G$",grouping:",",decimal:".",precision:2},HKD:{symbol:"HK$",grouping:",",decimal:".",precision:2},HNL:{symbol:"L.",grouping:",",decimal:".",precision:2},HRK:{symbol:"kn",grouping:".",decimal:",",precision:2},HTG:{symbol:"G",grouping:",",decimal:".",precision:2},HUF:{symbol:"Ft",grouping:".",decimal:",",precision:0},IDR:{symbol:"Rp",grouping:".",decimal:",",precision:0},ILS:{symbol:"₪",grouping:",",decimal:".",precision:2},INR:{symbol:"₹",grouping:",",decimal:".",precision:2},IQD:{symbol:"د.ع.‏",grouping:",",decimal:".",precision:2},IRR:{symbol:"﷼",grouping:",",decimal:"/",precision:2},ISK:{symbol:"kr.",grouping:".",decimal:",",precision:0},JMD:{symbol:"J$",grouping:",",decimal:".",precision:2},JOD:{symbol:"د.ا.‏",grouping:",",decimal:".",precision:3},JPY:{symbol:"¥",grouping:",",decimal:".",precision:0},KES:{symbol:"S",grouping:",",decimal:".",precision:2},KGS:{symbol:"сом",grouping:" ",decimal:"-",precision:2},KHR:{symbol:"៛",grouping:",",decimal:".",precision:0},KMF:{symbol:"CF",grouping:",",decimal:".",precision:2},KPW:{symbol:"₩",grouping:",",decimal:".",precision:0},KRW:{symbol:"₩",grouping:",",decimal:".",precision:0},KWD:{symbol:"د.ك.‏",grouping:",",decimal:".",precision:3},KYD:{symbol:"$",grouping:",",decimal:".",precision:2},KZT:{symbol:"₸",grouping:" ",decimal:"-",precision:2},LAK:{symbol:"₭",grouping:",",decimal:".",precision:0},LBP:{symbol:"ل.ل.‏",grouping:",",decimal:".",precision:2},LKR:{symbol:"₨",grouping:",",decimal:".",precision:0},LRD:{symbol:"L$",grouping:",",decimal:".",precision:2},LSL:{symbol:"M",grouping:",",decimal:".",precision:2},LYD:{symbol:"د.ل.‏",grouping:",",decimal:".",precision:3},MAD:{symbol:"د.م.‏",grouping:",",decimal:".",precision:2},MDL:{symbol:"lei",grouping:",",decimal:".",precision:2},MGA:{symbol:"Ar",grouping:",",decimal:".",precision:0},MKD:{symbol:"ден.",grouping:".",decimal:",",precision:2},MMK:{symbol:"K",grouping:",",decimal:".",precision:2},MNT:{symbol:"₮",grouping:" ",decimal:",",precision:2},MOP:{symbol:"MOP$",grouping:",",decimal:".",precision:2},MRO:{symbol:"UM",grouping:",",decimal:".",precision:2},MTL:{symbol:"₤",grouping:",",decimal:".",precision:2},MUR:{symbol:"₨",grouping:",",decimal:".",precision:2},MVR:{symbol:"MVR",grouping:",",decimal:".",precision:1},MWK:{symbol:"MK",grouping:",",decimal:".",precision:2},MXN:{symbol:"MX$",grouping:",",decimal:".",precision:2},MYR:{symbol:"RM",grouping:",",decimal:".",precision:2},MZN:{symbol:"MT",grouping:",",decimal:".",precision:0},NAD:{symbol:"N$",grouping:",",decimal:".",precision:2},NGN:{symbol:"₦",grouping:",",decimal:".",precision:2},NIO:{symbol:"C$",grouping:",",decimal:".",precision:2},NOK:{symbol:"kr",grouping:" ",decimal:",",precision:2},NPR:{symbol:"₨",grouping:",",decimal:".",precision:2},NZD:{symbol:"NZ$",grouping:",",decimal:".",precision:2},OMR:{symbol:"﷼",grouping:",",decimal:".",precision:3},PAB:{symbol:"B/.",grouping:",",decimal:".",precision:2},PEN:{symbol:"S/.",grouping:",",decimal:".",precision:2},PGK:{symbol:"K",grouping:",",decimal:".",precision:2},PHP:{symbol:"₱",grouping:",",decimal:".",precision:2},PKR:{symbol:"₨",grouping:",",decimal:".",precision:2},PLN:{symbol:"zł",grouping:" ",decimal:",",precision:2},PYG:{symbol:"₲",grouping:".",decimal:",",precision:2},QAR:{symbol:"﷼",grouping:",",decimal:".",precision:2},RON:{symbol:"lei",grouping:".",decimal:",",precision:2},RSD:{symbol:"Дин.",grouping:".",decimal:",",precision:2},RUB:{symbol:"₽",grouping:" ",decimal:",",precision:2},RWF:{symbol:"RWF",grouping:" ",decimal:",",precision:2},SAR:{symbol:"﷼",grouping:",",decimal:".",precision:2},SBD:{symbol:"S$",grouping:",",decimal:".",precision:2},SCR:{symbol:"₨",grouping:",",decimal:".",precision:2},SDD:{symbol:"LSd",grouping:",",decimal:".",precision:2},SDG:{symbol:"£‏",grouping:",",decimal:".",precision:2},SEK:{symbol:"kr",grouping:",",decimal:".",precision:2},SGD:{symbol:"S$",grouping:",",decimal:".",precision:2},SHP:{symbol:"£",grouping:",",decimal:".",precision:2},SLL:{symbol:"Le",grouping:",",decimal:".",precision:2},SOS:{symbol:"S",grouping:",",decimal:".",precision:2},SRD:{symbol:"$",grouping:",",decimal:".",precision:2},STD:{symbol:"Db",grouping:",",decimal:".",precision:2},SVC:{symbol:"₡",grouping:",",decimal:".",precision:2},SYP:{symbol:"£",grouping:",",decimal:".",precision:2},SZL:{symbol:"E",grouping:",",decimal:".",precision:2},THB:{symbol:"฿",grouping:",",decimal:".",precision:2},TJS:{symbol:"TJS",grouping:" ",decimal:";",precision:2},TMT:{symbol:"m",grouping:" ",decimal:",",precision:0},TND:{symbol:"د.ت.‏",grouping:",",decimal:".",precision:3},TOP:{symbol:"T$",grouping:",",decimal:".",precision:2},TRY:{symbol:"TL",grouping:".",decimal:",",precision:2},TTD:{symbol:"TT$",grouping:",",decimal:".",precision:2},TVD:{symbol:"$T",grouping:",",decimal:".",precision:2},TWD:{symbol:"NT$",grouping:",",decimal:".",precision:2},TZS:{symbol:"TSh",grouping:",",decimal:".",precision:2},UAH:{symbol:"₴",grouping:" ",decimal:",",precision:2},UGX:{symbol:"USh",grouping:",",decimal:".",precision:2},USD:{symbol:"$",grouping:",",decimal:".",precision:2},UYU:{symbol:"$U",grouping:".",decimal:",",precision:2},UZS:{symbol:"сўм",grouping:" ",decimal:",",precision:2},VEB:{symbol:"Bs.",grouping:",",decimal:".",precision:2},VEF:{symbol:"Bs. F.",grouping:".",decimal:",",precision:2},VND:{symbol:"₫",grouping:".",decimal:",",precision:1},VUV:{symbol:"VT",grouping:",",decimal:".",precision:0},WST:{symbol:"WS$",grouping:",",decimal:".",precision:2},XAF:{symbol:"F",grouping:",",decimal:".",precision:2},XCD:{symbol:"$",grouping:",",decimal:".",precision:2},XOF:{symbol:"F",grouping:" ",decimal:",",precision:2},XPF:{symbol:"F",grouping:",",decimal:".",precision:2},YER:{symbol:"﷼",grouping:",",decimal:".",precision:2},ZAR:{symbol:"R",grouping:" ",decimal:",",precision:2},ZMW:{symbol:"ZK",grouping:",",decimal:".",precision:2},WON:{symbol:"₩",grouping:",",decimal:".",precision:2}};function a(e){return c[e]||{symbol:"$",grouping:",",decimal:".",precision:2}}},4567:(e,t,n)=>{"use strict";n.d(t,{vA:()=>i});var c=n(26356),a=n(18360);function i(e,t,n={}){const i=(0,c.i)(t);if(!i||isNaN(e))return null;const{decimal:s,grouping:o,precision:r,symbol:l}={...i,...n},p=e<0?"-":"",m=Math.abs(e),u=Math.floor(m);return{sign:p,symbol:l,integer:(0,a.A)(m,r,s,o).split(s)[0],fraction:r>0?(0,a.A)(m-u,r,s,o).slice(1):""}}},18360:(e,t,n)=>{"use strict";function c(e,t=0,n=".",c=","){const a=(e+"").replace(/[^0-9+\-Ee.]/g,""),i=isFinite(+a)?+a:0,s=isFinite(+t)?Math.abs(t):0,o=(s?function(e,t){const n=Math.pow(10,t);return""+(Math.round(e*n)/n).toFixed(t)}(i,s):""+Math.round(i)).split(".");return o[0].length>3&&(o[0]=o[0].replace(/\B(?=(?:\d{3})+(?!\d))/g,c)),(o[1]||"").length<s&&(o[1]=o[1]||"",o[1]+=new Array(s-o[1].length+1).join("0")),o.join(n)}n.d(t,{A:()=>c})},51119:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var c,a=n(2467);function i(){this.intervals={},this.monitorInterval=null,this.windowInstance=null,this.onMessage=e=>{e.source===this.windowInstance&&this.emit("message",e.data)}}c=i.prototype,Object.assign(c,a.EventEmitter.prototype),c.emitChange=function(){this.emit("change")},c.off=c.removeListener,i.prototype.open=function(e,t,n){return t=t||Date.now(),this.windowInstance=window.open(e,t,n),this.startMonitoring(t,this.windowInstance),window.addEventListener("message",this.onMessage,!1),this},i.prototype.getScreenCenterSpecs=function(e,t){const n=void 0!==window.screenTop?window.screenTop:window.screenY,c=void 0!==window.screenLeft?window.screenLeft:window.screenX;return["width="+e,"height="+t,"top="+(n+window.innerHeight/2-t/2),"left="+(c+window.innerWidth/2-e/2)].join()},i.prototype.isOpen=function(e){let t=!1;try{t=this.intervals[e]&&this.intervals[e].closed}catch(e){}return!t},i.prototype.checkStatus=function(){for(const e in this.intervals)this.intervals.hasOwnProperty(e)&&!this.isOpen(e)&&(this.emit("close",e),delete this.intervals[e]);0===Object.keys(this.intervals).length&&(clearInterval(this.monitorInterval),delete this.monitorInterval,window.removeEventListener("message",this.onMessage))},i.prototype.startMonitoring=function(e,t){this.monitorInterval||(this.monitorInterval=setInterval(this.checkStatus.bind(this),100)),this.intervals[e]=t};const s=i},30081:(e,t,n)=>{"use strict";e.exports=n.p+"images/connections-facebook-c8a9715c8b6c8707594f.png"},26818:(e,t,n)=>{"use strict";e.exports=n.p+"images/connections-instagram-business-53d4764803db1a7b3cbc.png"},43443:(e,t,n)=>{"use strict";e.exports=n.p+"images/connections-linkedin-553fcb77cb734fc8d08f.png"},11326:(e,t,n)=>{"use strict";e.exports=n.p+"images/connections-nextdoor-6e76141465483081fde8.png"},65398:(e,t,n)=>{"use strict";e.exports=n.p+"images/connections-threads-1c941351fac252724ae7.png"},9791:(e,t,n)=>{"use strict";e.exports=n.p+"images/connections-tumblr-ac23dad7016040416331.png"},51459:(e,t,n)=>{"use strict";e.exports=n.p+"images/background-adca5912a15bcc675a12.svg"},33010:(e,t,n)=>{"use strict";e.exports=n.p+"images/illustration-77dfe189dd76498975de.png"},26365:(e,t,n)=>{"use strict";e.exports=n.p+"images/dois-66b3f018328e8f585646.jpg"},45329:(e,t,n)=>{"use strict";e.exports=n.p+"images/edge-e1adff73f2dc1c1dc64e.jpg"},9509:(e,t,n)=>{"use strict";e.exports=n.p+"images/fullscreen-21ca1ee2bafcb1c8eb55.jpg"},22417:(e,t,n)=>{"use strict";e.exports=n.p+"images/highway-205f94656e2fcc4aeb86.jpg"},44109:e=>{"use strict";e.exports={consumer_slug:"jetpack-social"}},39384:e=>{"use strict";e.exports=window.JetpackConnection},97999:e=>{"use strict";e.exports=window.JetpackScriptDataModule},51609:e=>{"use strict";e.exports=window.React},10790:e=>{"use strict";e.exports=window.ReactJSXRuntime},66087:e=>{"use strict";e.exports=window.lodash},1455:e=>{"use strict";e.exports=window.wp.apiFetch},56427:e=>{"use strict";e.exports=window.wp.components},29491:e=>{"use strict";e.exports=window.wp.compose},3582:e=>{"use strict";e.exports=window.wp.coreData},47143:e=>{"use strict";e.exports=window.wp.data},38443:e=>{"use strict";e.exports=window.wp.date},98490:e=>{"use strict";e.exports=window.wp.domReady},43656:e=>{"use strict";e.exports=window.wp.editor},86087:e=>{"use strict";e.exports=window.wp.element},52619:e=>{"use strict";e.exports=window.wp.hooks},27723:e=>{"use strict";e.exports=window.wp.i18n},692:e=>{"use strict";e.exports=window.wp.notices},92279:e=>{"use strict";e.exports=window.wp.plugins},5573:e=>{"use strict";e.exports=window.wp.primitives},93832:e=>{"use strict";e.exports=window.wp.url},96072:e=>{function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var c in n)({}).hasOwnProperty.call(n,c)&&(e[c]=n[c])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},13022:(e,t,n)=>{"use strict";function c(e){var t,n,a="";if("string"==typeof e||"number"==typeof e)a+=e;else if("object"==typeof e)if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(n=c(e[t]))&&(a&&(a+=" "),a+=n)}else for(n in e)e[n]&&(a&&(a+=" "),a+=n);return a}n.d(t,{A:()=>a});const a=function(){for(var e,t,n=0,a="",i=arguments.length;n<i;n++)(e=arguments[n])&&(t=c(e))&&(a&&(a+=" "),a+=t);return a}},38377:e=>{"use strict";e.exports=JSON.parse('{"T":{"White":"#fff","Black":"#000","Gray 0":"#f6f7f7","Gray 5":"#dcdcde","Gray 10":"#c3c4c7","Gray 20":"#a7aaad","Gray 30":"#8c8f94","Gray 40":"#787c82","Gray 50":"#646970","Gray 60":"#50575e","Gray 70":"#3c434a","Gray 80":"#2c3338","Gray 90":"#1d2327","Gray 100":"#101517","Gray":"#646970","Blue 0":"#fbfcfe","Blue 5":"#f7f8fe","Blue 10":"#d6ddf9","Blue 20":"#adbaf3","Blue 30":"#7b90ff","Blue 40":"#546ff3","Blue 50":"#3858e9","Blue 60":"#2a46ce","Blue 70":"#1d35b4","Blue 80":"#1f3286","Blue 90":"#14215a","Blue 100":"#0a112d","Blue":"#3858e9","WordPress Blue 0":"#fbfcfe","WordPress Blue 5":"#f7f8fe","WordPress Blue 10":"#d6ddf9","WordPress Blue 20":"#adbaf3","WordPress Blue 30":"#7b90ff","WordPress Blue 40":"#546ff3","WordPress Blue 50":"#3858e9","WordPress Blue 60":"#2a46ce","WordPress Blue 70":"#1d35b4","WordPress Blue 80":"#1f3286","WordPress Blue 90":"#14215a","WordPress Blue 100":"#0a112d","WordPress Blue":"#3858e9","Purple 0":"#f2e9ed","Purple 5":"#ebcee0","Purple 10":"#e3afd5","Purple 20":"#d48fc8","Purple 30":"#c475bd","Purple 40":"#b35eb1","Purple 50":"#984a9c","Purple 60":"#7c3982","Purple 70":"#662c6e","Purple 80":"#4d2054","Purple 90":"#35163b","Purple 100":"#1e0c21","Purple":"#984a9c","Pink 0":"#f5e9ed","Pink 5":"#f2ceda","Pink 10":"#f7a8c3","Pink 20":"#f283aa","Pink 30":"#eb6594","Pink 40":"#e34c84","Pink 50":"#c9356e","Pink 60":"#ab235a","Pink 70":"#8c1749","Pink 80":"#700f3b","Pink 90":"#4f092a","Pink 100":"#260415","Pink":"#c9356e","Red 0":"#f7ebec","Red 5":"#facfd2","Red 10":"#ffabaf","Red 20":"#ff8085","Red 30":"#f86368","Red 40":"#e65054","Red 50":"#d63638","Red 60":"#b32d2e","Red 70":"#8a2424","Red 80":"#691c1c","Red 90":"#451313","Red 100":"#240a0a","Red":"#d63638","Orange 0":"#f5ece6","Orange 5":"#f7dcc6","Orange 10":"#ffbf86","Orange 20":"#faa754","Orange 30":"#e68b28","Orange 40":"#d67709","Orange 50":"#b26200","Orange 60":"#8a4d00","Orange 70":"#704000","Orange 80":"#543100","Orange 90":"#361f00","Orange 100":"#1f1200","Orange":"#b26200","Yellow 0":"#f5f1e1","Yellow 5":"#f5e6b3","Yellow 10":"#f2d76b","Yellow 20":"#f0c930","Yellow 30":"#deb100","Yellow 40":"#c08c00","Yellow 50":"#9d6e00","Yellow 60":"#7d5600","Yellow 70":"#674600","Yellow 80":"#4f3500","Yellow 90":"#320","Yellow 100":"#1c1300","Yellow":"#9d6e00","Green 0":"#e6f2e8","Green 5":"#b8e6bf","Green 10":"#68de86","Green 20":"#1ed15a","Green 30":"#00ba37","Green 40":"#00a32a","Green 50":"#008a20","Green 60":"#007017","Green 70":"#005c12","Green 80":"#00450c","Green 90":"#003008","Green 100":"#001c05","Green":"#008a20","Celadon 0":"#e4f2ed","Celadon 5":"#a7e8d3","Celadon 10":"#66deb9","Celadon 20":"#31cc9f","Celadon 30":"#09b585","Celadon 40":"#009e73","Celadon 50":"#008763","Celadon 60":"#007053","Celadon 70":"#005c44","Celadon 80":"#004533","Celadon 90":"#003024","Celadon 100":"#001c15","Celadon":"#008763","Automattic Blue 0":"#ebf4fa","Automattic Blue 5":"#c4e2f5","Automattic Blue 10":"#88ccf2","Automattic Blue 20":"#5ab7e8","Automattic Blue 30":"#24a3e0","Automattic Blue 40":"#1490c7","Automattic Blue 50":"#0277a8","Automattic Blue 60":"#036085","Automattic Blue 70":"#02506e","Automattic Blue 80":"#02384d","Automattic Blue 90":"#022836","Automattic Blue 100":"#021b24","Automattic Blue":"#24a3e0","Simplenote Blue 0":"#e9ecf5","Simplenote Blue 5":"#ced9f2","Simplenote Blue 10":"#abc1f5","Simplenote Blue 20":"#84a4f0","Simplenote Blue 30":"#618df2","Simplenote Blue 40":"#4678eb","Simplenote Blue 50":"#3361cc","Simplenote Blue 60":"#1d4fc4","Simplenote Blue 70":"#113ead","Simplenote Blue 80":"#0d2f85","Simplenote Blue 90":"#09205c","Simplenote Blue 100":"#05102e","Simplenote Blue":"#3361cc","WooCommerce Purple 0":"#f2edff","WooCommerce Purple 5":"#e1d7ff","WooCommerce Purple 10":"#d1c1ff","WooCommerce Purple 20":"#b999ff","WooCommerce Purple 30":"#a77eff","WooCommerce Purple 40":"#873eff","WooCommerce Purple 50":"#720eec","WooCommerce Purple 60":"#6108ce","WooCommerce Purple 70":"#5007aa","WooCommerce Purple 80":"#3c087e","WooCommerce Purple 90":"#2c045d","WooCommerce Purple 100":"#1f0342","WooCommerce Purple":"#720eec","Jetpack Green 0":"#f0f2eb","Jetpack Green 5":"#d0e6b8","Jetpack Green 10":"#9dd977","Jetpack Green 20":"#64ca43","Jetpack Green 30":"#2fb41f","Jetpack Green 40":"#069e08","Jetpack Green 50":"#008710","Jetpack Green 60":"#007117","Jetpack Green 70":"#005b18","Jetpack Green 80":"#004515","Jetpack Green 90":"#003010","Jetpack Green 100":"#001c09","Jetpack Green":"#069e08"}}')}},t={};function n(c){var a=t[c];if(void 0!==a)return a.exports;var i=t[c]={exports:{}};return e[c](i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var c in t)n.o(t,c)&&!n.o(e,c)&&Object.defineProperty(e,c,{enumerable:!0,get:t[c]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;n.g.importScripts&&(e=n.g.location+"");var t=n.g.document;if(!e&&t&&(t.currentScript&&"SCRIPT"===t.currentScript.tagName.toUpperCase()&&(e=t.currentScript.src),!e)){var c=t.getElementsByTagName("script");if(c.length)for(var a=c.length-1;a>-1&&(!e||!/^http(s?):/.test(e));)e=c[a--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),n.p=e})(),(()=>{"use strict";n(47893)})()})();