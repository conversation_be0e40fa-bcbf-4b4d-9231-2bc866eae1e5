<?php

namespace UM_Stripe\Vendor\UM_Stripe\Vendor;

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
use UM_Stripe\Vendor\Symfony\Intl\Collator\Collator as IntlCollator;
use UM_Stripe\Vendor\Symfony\Polyfill\Intl\Icu\Collator as CollatorPolyfill;
if (!\class_exists(CollatorPolyfill::class)) {
    trigger_deprecation('symfony/intl', '5.3', 'Polyfills are deprecated, try running "composer require symfony/polyfill-intl-icu ^1.21" instead.');
    /**
     * Stub implementation for the Collator class of the intl extension.
     *
     * <AUTHOR> <<EMAIL>>
     */
    class Collator extends IntlCollator
    {
    }
    /**
     * Stub implementation for the Collator class of the intl extension.
     *
     * <AUTHOR> <bschuss<PERSON>@gmail.com>
     */
    \class_alias('UM_Stripe\Vendor\Collator', 'Collator', \false);
} else {
    /**
     * Stub implementation for the Collator class of the intl extension.
     *
     * <AUTHOR> <<EMAIL>>
     */
    class Collator extends CollatorPolyfill
    {
    }
    /**
     * Stub implementation for the Collator class of the intl extension.
     *
     * <AUTHOR> Schussek <<EMAIL>>
     */
    \class_alias('UM_Stripe\Vendor\Collator', 'Collator', \false);
}
