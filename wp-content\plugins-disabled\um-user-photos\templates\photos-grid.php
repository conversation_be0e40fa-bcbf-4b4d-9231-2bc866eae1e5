<?php
/**
 * Template for the UM User Photos. The "Photos" block
 *
 * Call: UM()->User_Photos()->ajax()->um_user_photos_load_more()
 * Page: "Profile", tab "Photos"
 * Parent template: photos.php
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/photos-grid.php
 * @var array $photos
 * @var bool  $is_my_profile
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

foreach ( $photos as $photo_id ) {
	$thumbnail_image = wp_get_attachment_image_src( $photo_id, 'gallery_image' );
	if ( ! $thumbnail_image ) {
		continue;
	}
	$full_image = wp_get_attachment_image_src( $photo_id, 'full' );
	$caption    = wp_get_attachment_caption( $photo_id );
	$img_title  = get_the_title( $photo_id );
	$img_link   = get_post_meta( $photo_id, '_link', true );

	$args_t = compact( 'is_my_profile', 'photo_id', 'thumbnail_image', 'img_link', 'full_image', 'caption', 'img_title' );
	UM()->get_template( 'single-image.php', UM_USER_PHOTOS_PLUGIN, $args_t, true );
}
