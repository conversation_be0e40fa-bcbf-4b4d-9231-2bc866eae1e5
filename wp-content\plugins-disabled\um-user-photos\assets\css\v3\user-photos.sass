@import "../../../../ultimate-member/assets/css/selectors"
@import "../../../../ultimate-member/assets/css/mixins"
@import "../../../../ultimate-member/assets/css/box"
@import "../../../../ultimate-member/assets/css/colors"
@import "../../../../ultimate-member/assets/css/typography"

// 1. Gallery (albums grid). User albums list.
.#{$prefix}user-photos-gallery
  +flex(column, flex-start, center, nowrap)
  margin: 0
  padding: 0
  gap: $spacing-xl

// All albums shortcode.
.um.ultimatemember_albums
  .#{$prefix}user-photos-albums
    padding: 0 0 $spacing-lg 0
    +border-box

// 1.1 Gallery (albums grid)
.um
  .#{$prefix}user-photos-albums
    grid-gap: $spacing-lg
    width: 100%
    &.#{$prefix}user-photos-col-1
      grid-template-columns: repeat(1, 1fr)
    &.#{$prefix}user-photos-col-2
      grid-template-columns: repeat(2, 1fr)
    &.#{$prefix}user-photos-col-3
      grid-template-columns: repeat(3, 1fr)
    &.#{$prefix}user-photos-col-4
      grid-template-columns: repeat(4, 1fr)
    &.#{$prefix}user-photos-col-5
      grid-template-columns: repeat(5, 1fr)
    &.#{$prefix}user-photos-col-6
      grid-template-columns: repeat(6, 1fr)
    .#{$prefix}user-photos-album
      +flex( column, flex-start, stretch, nowrap )
      gap: $spacing-md
      +border-box
      overflow: hidden
      padding: 0
      position: relative
      .#{$prefix}user-photos-album-skeleton
        position: absolute
        background: transparent
        top: 0
        bottom: 0
        left: 0
        right: 0
        z-index: 3
        width: 100%
        height: 100%
        +border-box
        display: none
        .#{$prefix}user-photos-album-skeleton-inner
          +flex(column,flex-start,center,nowrap)
          gap: $spacing-md
          width: 100%
          height: 100%
          overflow: hidden
          +border-box
          .#{$prefix}skeleton-photo
            border-radius: $radius-lg
            aspect-ratio: 1 / 1
            width: 100%
          .#{$prefix}skeleton-box-header
            +flex(row,flex-start,center, nowrap)
            gap: $spacing-lg
            width: 100%
            +border-box
            .#{$prefix}skeleton-box-avatar
              width: 32px
              height: 32px
              border-radius: 100%
            .#{$prefix}skeleton-box-header-title
              flex: 1
              height: 28px
          .#{$prefix}skeleton-box-details
            +flex(column,flex-start,stretch, nowrap)
            width: 100%
            gap: 7px
            +border-box
            .#{$prefix}skeleton-box-title
              height: 20px
            .#{$prefix}skeleton-box-photos-count
              height: 15px
      &.#{$prefix}skeleton-mode
        .#{$prefix}user-photos-album-skeleton
          display: block
      .#{$prefix}user-photos-album-block
        transition: all 0.2s linear
        background: $gray-300
        border-radius: $radius-lg
        position: relative
        width: 100%
        height: 100%
        padding-top: 100%
        +flex( column, center, center, nowrap )
        overflow: hidden
        .#{$prefix}image-lazyload-wrapper
          position: absolute
          top: 0
          left: 0
          width: 100%
          height: 100%
        &:hover
          opacity: 0.7
        img
          width: 100%
          height: 100%
          object-fit: cover
      .#{$prefix}user-photos-album-details
        +flex( column, flex-start, flex-start, nowrap )
        margin: 0
        padding: 0
        +border-box
        .#{$prefix}user-photos-album-title
          +typography( text, md, semibold )
          margin: 0
          padding: 0
          +border-box
        .#{$prefix}user-photos-album-photos-count
          +typography( text, xs, regular )
          margin: 0
          padding: 0
          +border-box


// 2. Gallery (all photos grid)
.#{$prefix}user-photos-gallery-photos
  +flex(column, flex-start, center, nowrap)
  margin: 0
  padding: 0
  gap: $spacing-xl


// 3. Single album
.#{$prefix}user-photos-single-album
  +flex(column, flex-start, center, nowrap)
  margin: 0
  padding: 0
  gap: $spacing-xl
  .#{$prefix}user-photos-album-head
    +flex( row, space-between, center, nowrap )
    position: relative
    width: 100%
    margin: 0
    padding: 0
    &.#{$prefix}display-none
      display: none

    .#{$prefix}user-photos-album-title
      margin: 0
      +border-box
      padding: 0 $spacing-9xl
      +typography( header, md, medium )
      color: $gray-900
      position: absolute
      width: 100%
      text-align: center
      display: block
      z-index: -1
      word-break: break-word
      align-self: start


// 4. Photos grid (inside single album or gallery photos)
.um
  .#{$prefix}user-photos-grid
    align-items: center
    justify-content: center
    justify-items: center
    width: 100%
    .#{$prefix}up-cell
      background: $gray-200
      border-radius: $radius-lg
      overflow: hidden
      width: 100%
      height: 100%
      display: flex
      a.#{$prefix}user-photos-image
        display: block
        object-fit: cover
        height: 100%
        width: 100%
        img
          position: relative
          height: 100%
          width: 100%
          aspect-ratio: 1 / 1
          object-fit: cover
          object-position: center
          display: block
    .#{$prefix}user-photos-image-block
      display: block
      padding: 0
      a.#{$prefix}user-photos-image
        > img
          display: block
          transition: all 0.1s linear
        &:hover
          > img
            opacity: 0.8
      position: relative
    .#{$prefix}user-photos-image-block-editable
      position: relative
    .#{$prefix}user-photos-image-block .#{$prefix}user-photos-image-block-buttons,
    .#{$prefix}user-photos-image-block-editable .#{$prefix}user-photos-image-block-buttons
      position: absolute
      z-index: 9
      display: inline-block
      padding: 5px
      right: 0
      opacity: 0
      transition: all 0.2s ease-in
    .#{$prefix}user-photos-image-block:hover > .#{$prefix}user-photos-image-block-buttons,
    .#{$prefix}user-photos-image-block-editable:hover > .#{$prefix}user-photos-image-block-buttons
      opacity: 1
    .#{$prefix}user-photos-image-block .#{$prefix}user-photos-image-block-buttons a,
    .#{$prefix}user-photos-image-block-editable .#{$prefix}user-photos-image-block-buttons a
      color: $um-white
      text-decoration: none
      border: none
      margin: 2px
      background: rgba(0, 0, 0, 0.4)
      border-radius: 50%
      display: inline-block !important
      height: 30px
      width: 30px
      text-align: center
      line-height: 30px
      transition: all 0.2s linear
      padding: 3px !important
      svg
        width: 20px
    .#{$prefix}user-photos-image-block .#{$prefix}user-photos-image-block-buttons a:hover,
    .#{$prefix}user-photos-image-block-editable .#{$prefix}user-photos-image-block-buttons a:hover
      background: rgba(255, 255, 255, 0.8)
      color: $um-black


// 5. Add/edit album forms.
.#{$prefix}user-photos-add-album,
.#{$prefix}user-photos-edit-album-form
  +flex(column, flex-start, center, nowrap)
  row-gap: $spacing-lg
  .#{$prefix}user-photos-form-title
    +typography( header, sm, medium )
    width: 100%
    padding: 0
    margin: 0
    text-align: left
  .#{$prefix}user-photos-album-form
    width: 100%
    .#{$prefix}upload-error
      border-color: $error-600 !important
    .#{$prefix}uploader#um_user_photos_uploader
      .#{$prefix}uploader-filelist
        .#{$prefix}uploader-file
          .#{$prefix}uploader-file-data
            +flex( column, flex-start, center, nowrap )
            .#{$prefix}uploader-file-preview-error
              margin-bottom: $spacing-md
              color: $um-white
              border-radius: $radius-sm
              width: 100%
              text-align: center
              font-size: 16px
              box-sizing: border-box
              padding: $spacing-md
              background: $error-300
            .#{$prefix}uploader-user-photos-data-wrapper
              width: 100%
              +flex ( row, space-between, flex-start, nowrap )
              column-gap: $spacing-xl
              margin: 0 0 $spacing-xl 0
              +border-box
              .#{$prefix}uploader-file-preview
                width: 100px
                height: 100px
                canvas
                  border-radius: $radius-xl
                img
                  width: 100%
                  height: 100%
                  object-fit: cover
                  object-position: center
                  border-radius: $radius-xl
              .#{$prefix}uploader-file-data-header
                +flex ( column, flex-start, flex-start, nowrap)
                row-gap: $spacing-md
                flex: 1
                .#{$prefix}uploader-file-data-header-info
                  +flex ( row, space-between, start, nowrap)
                  column-gap: $spacing-md
                  width: 100%
                  .#{$prefix}uploader-file-name
                    flex: 1
                  .#{$prefix}user-photos-delete-photo
                    padding: $spacing-xxs 0 0 0
                .#{$prefix}progress-bar-wrapper
                  width: 100%
            .#{$prefix}uploader-file-more-info
              .#{$prefix}button-icon
                .#{$prefix}toggle-chevron
                  width: 20px
                  height: 20px
                  position: relative
                  &:after
                    content: ""
                    position: absolute
                    top: 0
                    right: 0
                    mask-size: contain
                    -webkit-mask-size: contain
                    mask-position: center
                    -webkit-mask-position: center
                    mask-repeat: no-repeat
                    -webkit-mask-repeat: no-repeat
                    mask-image: url(../../images/chevron-down.svg)
                    -webkit-mask-image: url(../../images/chevron-down.svg)
                    width: 20px
                    height: 20px
                    background-color: $gray-700
                    text-indent: 500%
                    white-space: nowrap
                    overflow: hidden
              &.#{$prefix}toggle-button-active
                .#{$prefix}button-icon
                  .#{$prefix}toggle-chevron
                    &:after
                      mask-image: url(../../images/chevron-up.svg)
                      -webkit-mask-image: url(../../images/chevron-up.svg)
            .#{$prefix}toggle-block
              width: 100%
              +border-box
              .#{$prefix}toggle-block-inner
                & > .#{$prefix}form-rows
                  margin: $spacing-xl 0 0 0
    .#{$prefix}form-submit
      +flex( row, flex-end, center, nowrap )
      gap: $spacing-md


// 6. Edit photo form.
##{$prefix}user-photos-edit-image
  width: 100%
  .#{$prefix}photo-form-response
    margin-bottom: $spacing-md
    color: $um-white
    border-radius: $radius-sm
    display: none
    width: 100%
    text-align: center
    font-size: 16px
    box-sizing: border-box
    padding: $spacing-md
    p
      margin: 0
    &.error
      display: block
      background: $error-300
    &.success
      display: block
      background: $success-300
  .#{$prefix}form-submit
    +flex( row, flex-end, center, nowrap )
    gap: $spacing-md


// 7. Single photo modal.
.um
  &.um-user-photos-single-photo-modal
    overflow: auto
    .#{$prefix}modal-close
      color: $um-black
      right: 0
      padding: 0
      width: 40px
      height: 40px
      text-align: center
      line-height: 38px
    .#{$prefix}modal-body
      height: 100%
      flex-direction: row
  .#{$prefix}user-photos-widget-image
    position: relative
    flex: 0 0 60%
    +flex( row, center, center, nowrap )
    .#{$prefix}image-lazyload-wrapper
      background: $gray-200
      height: 100%
      +flex( row, center, center, nowrap )
      .#{$prefix}skeleton-box
        border-radius: 0
      .#{$prefix}image-lazyload
        max-width: 100%
        max-height: 100%
        margin: 0 auto
        width: auto
        height: auto
    img
      object-fit: contain
    .#{$prefix}photo-modal-next,
    .#{$prefix}photo-modal-prev
      position: absolute
      z-index: 1
    .#{$prefix}photo-modal-prev
      left: 0
    .#{$prefix}photo-modal-next
      right: 0
    button
      padding: 0 !important
      color: $um-white
      column-gap: 0 !important
    .#{$prefix}user-photos-link
      position: absolute
      bottom: $spacing-xl
      left: 50%
      margin-left: -21px
      a
        column-gap: 0 !important
        padding: $spacing-md !important
        border-radius: $radius-full
        width: 42px
        height: 42px
  .#{$prefix}user-photos-widget
    background: $um-white
    flex: 0 0 40%
    padding: $spacing-xl
    box-sizing: border-box
    row-gap: $spacing-xl
    +flex( column, space-between, stretch, nowrap )
    .#{$prefix}user-photos-modal-skeleton
      position: absolute
      background: $um-white
      top: 0
      bottom: 0
      left: 0
      right: 0
      z-index: 3
      width: 100%
      height: 100%
      +border-box
      display: none
      .#{$prefix}user-photos-modal-skeleton-inner
        +flex(column,flex-start,flex-start,nowrap)
        gap: $spacing-xl
        width: calc( 100% - 16px )
        height: 100%
        overflow: hidden
        padding: $spacing-xl
        +border-box
        .#{$prefix}skeleton-box-header
          +flex(row,flex-start,center, nowrap)
          gap: $spacing-lg
          width: 100%
          +border-box
          .#{$prefix}skeleton-box-avatar
            width: 40px
            height: 40px
            border-radius: 100%
          .#{$prefix}skeleton-box-header-info
            flex: 1
            +flex(column,flex-start,stretch, nowrap)
            gap: 7px
            +border-box
            .#{$prefix}skeleton-box-title
              height: 20px
            .#{$prefix}skeleton-box-time
              height: 15px
        .#{$prefix}skeleton-box-description
          height: 24px
          width: 100%
    &.#{$prefix}skeleton-mode
      position: relative
      .#{$prefix}user-photos-modal-skeleton
        display: block
    .#{$prefix}user-photos-photo-data
      width: 100%
      padding: 0
      margin: 0
      row-gap: $spacing-xl
      +flex( column, start, baseline, nowrap )
      .#{$prefix}small-data
        &.#{$prefix}user-photos-author
          max-width: calc(100% - 30px)
          row-gap: 0
          .#{$prefix}user-display-name
            line-height: var(--um-text-line-height-sm, 20px)
      .#{$prefix}user-photos-image-caption
        +typography( text, sm, regular )
        color: $gray-600
        word-wrap: break-word
      .#{$prefix}user-photos-actions
        +flex( column, flex-start, start, nowrap )
        row-gap: $spacing-xs
        .#{$prefix}user-photos-comments-toggle
          column-gap: $spacing-sm
          padding: 0
          .#{$prefix}button-content
            +flex( row, flex-start, center, nowrap )
            column-gap: $spacing-sm
        .#{$prefix}user-photos-like-wrap
          +flex( row, flex-start, center, nowrap )
          column-gap: $spacing-sm
          height: $spacing-4xl
          .#{$prefix}user-photos-like
            position: relative
            padding: 0
            .#{$prefix}like-icon
              display: block
              width: $icon-size
              height: $icon-size
              position: relative
              &:after
                content: ""
                position: absolute
                top: 0
                right: 0
                mask-size: contain
                -webkit-mask-size: contain
                mask-position: center
                -webkit-mask-position: center
                mask-repeat: no-repeat
                -webkit-mask-repeat: no-repeat
                mask-image: url(../../images/thumb-up.svg)
                -webkit-mask-image: url(../../images/thumb-up.svg)
                width: $icon-size
                height: $icon-size
                background-color: $gray-600
                text-indent: 500%
                white-space: nowrap
                overflow: hidden
            &.active
              .#{$prefix}like-icon
                &:after
                  mask-image: url(../../images/thumb-up-active.svg)
                  -webkit-mask-image: url(../../images/thumb-up-active.svg)
            &:hover
              .#{$prefix}like-icon
                &:after
                  background-color: $gray-700
            &:focus
              .#{$prefix}like-icon
                &:after
                  background-color: $gray-600
            &:disabled
              .#{$prefix}like-icon
                &:after
                  background-color: $gray-400
          .#{$prefix}user-photos-show-likes
            padding: 0
            .#{$prefix}user-photos-post-likes
              +flex( row, flex-start, center, nowrap )
              column-gap: $spacing-sm
    .#{$prefix}user-photos-comments
      flex: 1
      overflow-y: auto
      padding: 0
      margin: 0
      &::-webkit-scrollbar
        width: $spacing-sm
      &::-webkit-scrollbar-track
        -webkit-box-shadow: inset 0 0 4px rgba(0,0,0,0.3)
      &::-webkit-scrollbar-thumb
        background: $gray-300
        outline: 0 solid $gray-300
      .#{$prefix}user-photos-comments-inner
        +flex( column, flex-start, center, nowrap )
        row-gap: $spacing-md
        .#{$prefix}user-photos-comments-loop
          +flex( column, flex-start, stretch, nowrap )
          width: 100%
          row-gap: $spacing-lg
          padding: 0
          margin: 0
          .#{$prefix}user-photos-comment-wrap
            +flex( column, flex-start, flex-start, nowrap )
            position: relative
            row-gap: $spacing-sm
            .#{$prefix}small-data
              max-width: calc( 100% - 64px ) // $spacing-7xl
              .#{$prefix}user-display-name
                +typography( text, sm, semibold )
              .#{$prefix}supporting-text
                +typography( text, xs, regular )
            .#{$prefix}user-photos-comment-actions
              position: absolute
              top: 0
              right: $spacing-xs
              +flex( row, flex-start, center, nowrap )
              column-gap: $spacing-md
              button
                padding: 0
            .#{$prefix}user-photos-comment-info
              +flex( column, flex-start, flex-start, nowrap )
              padding: 0 $spacing-sm 0 52px
              row-gap: $spacing-md
              width: 100%
              +border-box
              .#{$prefix}user-photos-comment-text
                width: 100%
                word-wrap: break-word
                +typography( text, sm, regular )
              .#{$prefix}user-photos-comment-edit-section
                +flex( column, flex-start, stretch, nowrap )
                row-gap: $spacing-md
                width: 100%
                .#{$prefix}user-photos-comment-textarea-action
                  +flex( row, flex-end, center, nowrap )
                  column-gap: $spacing-sm
                textarea
                  +typography( text, sm, regular )
                  resize: vertical
                  width: 100%
              .#{$prefix}user-photos-comment-meta
                +flex( row, flex-start, center, nowrap )
                column-gap: $spacing-xs
                height: $spacing-3xl
                .#{$prefix}user-photos-comment-like
                  padding: 0
                  .#{$prefix}like-icon
                    display: block
                    width: $icon-size
                    height: $icon-size
                    position: relative
                    &:after
                      content: ""
                      position: absolute
                      top: 0
                      right: 0
                      mask-size: contain
                      -webkit-mask-size: contain
                      mask-position: center
                      -webkit-mask-position: center
                      mask-repeat: no-repeat
                      -webkit-mask-repeat: no-repeat
                      mask-image: url(../../images/thumb-up.svg)
                      -webkit-mask-image: url(../../images/thumb-up.svg)
                      width: $icon-size
                      height: $icon-size
                      background-color: $gray-600
                      text-indent: 500%
                      white-space: nowrap
                      overflow: hidden
                  &.active
                    .#{$prefix}like-icon
                      &:after
                        mask-image: url(../../images/thumb-up-active.svg)
                        -webkit-mask-image: url(../../images/thumb-up-active.svg)
                  &:hover
                    .#{$prefix}like-icon
                      &:after
                        background-color: $gray-700
                  &:focus
                    .#{$prefix}like-icon
                      &:after
                        background-color: $gray-600
                  &:disabled
                    .#{$prefix}like-icon
                      &:after
                        background-color: $gray-400
                .#{$prefix}user-photos-show-comment-likes
                  padding: 0
                  .#{$prefix}user-photos-comment-likes
                    +flex( row, flex-start, center, nowrap )
                    column-gap: $spacing-xs

    .#{$prefix}user-photos-comment-form
      +flex( column, flex-start, stretch, nowrap )
      width: 100%
      padding: 0
      margin: 0
      row-gap: $spacing-md
      .#{$prefix}user-photos-comment-box
        +flex( row, flex-start, start, nowrap )
        padding: 0
        margin: 0
        column-gap: $spacing-md
        textarea
          flex: 1
          +typography( text, sm, regular )
      .#{$prefix}form-submit
        +flex( row, flex-end, center, nowrap )
        gap: $spacing-md


// 8. Activity post attachments.
.um
  .um_user_photos_activity_view
    +flex ( row, center, stretch, wrap )
    br
      display: none
    img
      flex-basis: 100px
      max-height: 100px
      object-fit: cover


// 9. Likes modal.
.um
  .#{$prefix}user-photos-like-list-item
    margin: 0
    a
      +flex( row, start, center, nowrap )
    img
      display: inline-block
      border-radius: 50%


// 10. Account

.um
  .um_user_photos_account
    +flex( column, flex-start, flex-start, nowrap )
    gap: $spacing-md
    margin: 0
    padding: 0
    .#{$prefix}user-photos-delete-wrap
      +flex( row, flex-start, center, nowrap )
      gap: $spacing-md
      margin: 0
      padding: 0


// @todo check mobile styles
// @todo check RTL styles
@media (max-width: 768px)
  .um
    &.#{$prefix}modal.#{$prefix}user-photos-single-photo-modal
      overflow-y: scroll
      display: block
      .#{$prefix}modal-close
        right: 0
        font-size: 36px
        padding: 5px
      .#{$prefix}modal-body
        display: block
      .#{$prefix}user-photos-widget-image
        max-height: 100% !important
        img
          display: block
          max-height: 100% !important
        button
          top: 50%
          margin-top: -33px
      .#{$prefix}user-photos-widget
        max-height: 100% !important
    .#{$prefix}user-photos-col-3
      a
        display: block
        text-align: center
        margin-bottom: 20px
      img
        display: inline-block
