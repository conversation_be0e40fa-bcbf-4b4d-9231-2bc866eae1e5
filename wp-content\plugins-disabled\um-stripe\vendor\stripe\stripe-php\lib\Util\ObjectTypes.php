<?php

namespace UM_Stripe\Vendor\Stripe\Util;

class ObjectTypes
{
    /**
     * @var array Mapping from object types to resource classes
     */
    const mapping = [
        \UM_Stripe\Vendor\Stripe\Collection::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Collection::class,
        \UM_Stripe\Vendor\Stripe\Issuing\CardDetails::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Issuing\CardDetails::class,
        \UM_Stripe\Vendor\Stripe\SearchResult::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\SearchResult::class,
        \UM_Stripe\Vendor\Stripe\File::OBJECT_NAME_ALT => \UM_Stripe\Vendor\Stripe\File::class,
        // The beginning of the section generated from our OpenAPI spec
        \UM_Stripe\Vendor\Stripe\Account::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Account::class,
        \UM_Stripe\Vendor\Stripe\AccountLink::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\AccountLink::class,
        \UM_Stripe\Vendor\Stripe\AccountSession::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\AccountSession::class,
        \UM_Stripe\Vendor\Stripe\ApplePayDomain::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\ApplePayDomain::class,
        \UM_Stripe\Vendor\Stripe\Application::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Application::class,
        \UM_Stripe\Vendor\Stripe\ApplicationFee::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\ApplicationFee::class,
        \UM_Stripe\Vendor\Stripe\ApplicationFeeRefund::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\ApplicationFeeRefund::class,
        \UM_Stripe\Vendor\Stripe\Apps\Secret::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Apps\Secret::class,
        \UM_Stripe\Vendor\Stripe\Balance::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Balance::class,
        \UM_Stripe\Vendor\Stripe\BalanceTransaction::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\BalanceTransaction::class,
        \UM_Stripe\Vendor\Stripe\BankAccount::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\BankAccount::class,
        \UM_Stripe\Vendor\Stripe\Billing\Meter::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Billing\Meter::class,
        \UM_Stripe\Vendor\Stripe\Billing\MeterEvent::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Billing\MeterEvent::class,
        \UM_Stripe\Vendor\Stripe\Billing\MeterEventAdjustment::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Billing\MeterEventAdjustment::class,
        \UM_Stripe\Vendor\Stripe\Billing\MeterEventSummary::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Billing\MeterEventSummary::class,
        \UM_Stripe\Vendor\Stripe\BillingPortal\Configuration::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\BillingPortal\Configuration::class,
        \UM_Stripe\Vendor\Stripe\BillingPortal\Session::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\BillingPortal\Session::class,
        \UM_Stripe\Vendor\Stripe\Capability::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Capability::class,
        \UM_Stripe\Vendor\Stripe\Card::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Card::class,
        \UM_Stripe\Vendor\Stripe\CashBalance::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\CashBalance::class,
        \UM_Stripe\Vendor\Stripe\Charge::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Charge::class,
        \UM_Stripe\Vendor\Stripe\Checkout\Session::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Checkout\Session::class,
        \UM_Stripe\Vendor\Stripe\Climate\Order::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Climate\Order::class,
        \UM_Stripe\Vendor\Stripe\Climate\Product::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Climate\Product::class,
        \UM_Stripe\Vendor\Stripe\Climate\Supplier::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Climate\Supplier::class,
        \UM_Stripe\Vendor\Stripe\ConfirmationToken::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\ConfirmationToken::class,
        \UM_Stripe\Vendor\Stripe\ConnectCollectionTransfer::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\ConnectCollectionTransfer::class,
        \UM_Stripe\Vendor\Stripe\CountrySpec::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\CountrySpec::class,
        \UM_Stripe\Vendor\Stripe\Coupon::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Coupon::class,
        \UM_Stripe\Vendor\Stripe\CreditNote::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\CreditNote::class,
        \UM_Stripe\Vendor\Stripe\CreditNoteLineItem::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\CreditNoteLineItem::class,
        \UM_Stripe\Vendor\Stripe\Customer::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Customer::class,
        \UM_Stripe\Vendor\Stripe\CustomerBalanceTransaction::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\CustomerBalanceTransaction::class,
        \UM_Stripe\Vendor\Stripe\CustomerCashBalanceTransaction::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\CustomerCashBalanceTransaction::class,
        \UM_Stripe\Vendor\Stripe\CustomerSession::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\CustomerSession::class,
        \UM_Stripe\Vendor\Stripe\Discount::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Discount::class,
        \UM_Stripe\Vendor\Stripe\Dispute::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Dispute::class,
        \UM_Stripe\Vendor\Stripe\Entitlements\ActiveEntitlement::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Entitlements\ActiveEntitlement::class,
        \UM_Stripe\Vendor\Stripe\Entitlements\Feature::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Entitlements\Feature::class,
        \UM_Stripe\Vendor\Stripe\EphemeralKey::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\EphemeralKey::class,
        \UM_Stripe\Vendor\Stripe\Event::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Event::class,
        \UM_Stripe\Vendor\Stripe\ExchangeRate::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\ExchangeRate::class,
        \UM_Stripe\Vendor\Stripe\File::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\File::class,
        \UM_Stripe\Vendor\Stripe\FileLink::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\FileLink::class,
        \UM_Stripe\Vendor\Stripe\FinancialConnections\Account::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\FinancialConnections\Account::class,
        \UM_Stripe\Vendor\Stripe\FinancialConnections\AccountOwner::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\FinancialConnections\AccountOwner::class,
        \UM_Stripe\Vendor\Stripe\FinancialConnections\AccountOwnership::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\FinancialConnections\AccountOwnership::class,
        \UM_Stripe\Vendor\Stripe\FinancialConnections\Session::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\FinancialConnections\Session::class,
        \UM_Stripe\Vendor\Stripe\FinancialConnections\Transaction::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\FinancialConnections\Transaction::class,
        \UM_Stripe\Vendor\Stripe\Forwarding\Request::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Forwarding\Request::class,
        \UM_Stripe\Vendor\Stripe\FundingInstructions::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\FundingInstructions::class,
        \UM_Stripe\Vendor\Stripe\Identity\VerificationReport::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Identity\VerificationReport::class,
        \UM_Stripe\Vendor\Stripe\Identity\VerificationSession::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Identity\VerificationSession::class,
        \UM_Stripe\Vendor\Stripe\Invoice::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Invoice::class,
        \UM_Stripe\Vendor\Stripe\InvoiceItem::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\InvoiceItem::class,
        \UM_Stripe\Vendor\Stripe\InvoiceLineItem::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\InvoiceLineItem::class,
        \UM_Stripe\Vendor\Stripe\Issuing\Authorization::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Issuing\Authorization::class,
        \UM_Stripe\Vendor\Stripe\Issuing\Card::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Issuing\Card::class,
        \UM_Stripe\Vendor\Stripe\Issuing\Cardholder::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Issuing\Cardholder::class,
        \UM_Stripe\Vendor\Stripe\Issuing\Dispute::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Issuing\Dispute::class,
        \UM_Stripe\Vendor\Stripe\Issuing\PersonalizationDesign::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Issuing\PersonalizationDesign::class,
        \UM_Stripe\Vendor\Stripe\Issuing\PhysicalBundle::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Issuing\PhysicalBundle::class,
        \UM_Stripe\Vendor\Stripe\Issuing\Token::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Issuing\Token::class,
        \UM_Stripe\Vendor\Stripe\Issuing\Transaction::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Issuing\Transaction::class,
        \UM_Stripe\Vendor\Stripe\LineItem::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\LineItem::class,
        \UM_Stripe\Vendor\Stripe\LoginLink::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\LoginLink::class,
        \UM_Stripe\Vendor\Stripe\Mandate::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Mandate::class,
        \UM_Stripe\Vendor\Stripe\PaymentIntent::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\PaymentIntent::class,
        \UM_Stripe\Vendor\Stripe\PaymentLink::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\PaymentLink::class,
        \UM_Stripe\Vendor\Stripe\PaymentMethod::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\PaymentMethod::class,
        \UM_Stripe\Vendor\Stripe\PaymentMethodConfiguration::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\PaymentMethodConfiguration::class,
        \UM_Stripe\Vendor\Stripe\PaymentMethodDomain::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\PaymentMethodDomain::class,
        \UM_Stripe\Vendor\Stripe\Payout::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Payout::class,
        \UM_Stripe\Vendor\Stripe\Person::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Person::class,
        \UM_Stripe\Vendor\Stripe\Plan::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Plan::class,
        \UM_Stripe\Vendor\Stripe\PlatformTaxFee::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\PlatformTaxFee::class,
        \UM_Stripe\Vendor\Stripe\Price::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Price::class,
        \UM_Stripe\Vendor\Stripe\Product::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Product::class,
        \UM_Stripe\Vendor\Stripe\ProductFeature::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\ProductFeature::class,
        \UM_Stripe\Vendor\Stripe\PromotionCode::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\PromotionCode::class,
        \UM_Stripe\Vendor\Stripe\Quote::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Quote::class,
        \UM_Stripe\Vendor\Stripe\Radar\EarlyFraudWarning::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Radar\EarlyFraudWarning::class,
        \UM_Stripe\Vendor\Stripe\Radar\ValueList::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Radar\ValueList::class,
        \UM_Stripe\Vendor\Stripe\Radar\ValueListItem::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Radar\ValueListItem::class,
        \UM_Stripe\Vendor\Stripe\Refund::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Refund::class,
        \UM_Stripe\Vendor\Stripe\Reporting\ReportRun::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Reporting\ReportRun::class,
        \UM_Stripe\Vendor\Stripe\Reporting\ReportType::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Reporting\ReportType::class,
        \UM_Stripe\Vendor\Stripe\ReserveTransaction::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\ReserveTransaction::class,
        \UM_Stripe\Vendor\Stripe\Review::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Review::class,
        \UM_Stripe\Vendor\Stripe\SetupAttempt::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\SetupAttempt::class,
        \UM_Stripe\Vendor\Stripe\SetupIntent::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\SetupIntent::class,
        \UM_Stripe\Vendor\Stripe\ShippingRate::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\ShippingRate::class,
        \UM_Stripe\Vendor\Stripe\Sigma\ScheduledQueryRun::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Sigma\ScheduledQueryRun::class,
        \UM_Stripe\Vendor\Stripe\Source::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Source::class,
        \UM_Stripe\Vendor\Stripe\SourceMandateNotification::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\SourceMandateNotification::class,
        \UM_Stripe\Vendor\Stripe\SourceTransaction::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\SourceTransaction::class,
        \UM_Stripe\Vendor\Stripe\Subscription::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Subscription::class,
        \UM_Stripe\Vendor\Stripe\SubscriptionItem::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\SubscriptionItem::class,
        \UM_Stripe\Vendor\Stripe\SubscriptionSchedule::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\SubscriptionSchedule::class,
        \UM_Stripe\Vendor\Stripe\Tax\Calculation::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Tax\Calculation::class,
        \UM_Stripe\Vendor\Stripe\Tax\CalculationLineItem::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Tax\CalculationLineItem::class,
        \UM_Stripe\Vendor\Stripe\Tax\Registration::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Tax\Registration::class,
        \UM_Stripe\Vendor\Stripe\Tax\Settings::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Tax\Settings::class,
        \UM_Stripe\Vendor\Stripe\Tax\Transaction::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Tax\Transaction::class,
        \UM_Stripe\Vendor\Stripe\Tax\TransactionLineItem::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Tax\TransactionLineItem::class,
        \UM_Stripe\Vendor\Stripe\TaxCode::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\TaxCode::class,
        \UM_Stripe\Vendor\Stripe\TaxDeductedAtSource::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\TaxDeductedAtSource::class,
        \UM_Stripe\Vendor\Stripe\TaxId::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\TaxId::class,
        \UM_Stripe\Vendor\Stripe\TaxRate::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\TaxRate::class,
        \UM_Stripe\Vendor\Stripe\Terminal\Configuration::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Terminal\Configuration::class,
        \UM_Stripe\Vendor\Stripe\Terminal\ConnectionToken::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Terminal\ConnectionToken::class,
        \UM_Stripe\Vendor\Stripe\Terminal\Location::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Terminal\Location::class,
        \UM_Stripe\Vendor\Stripe\Terminal\Reader::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Terminal\Reader::class,
        \UM_Stripe\Vendor\Stripe\TestHelpers\TestClock::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\TestHelpers\TestClock::class,
        \UM_Stripe\Vendor\Stripe\Token::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Token::class,
        \UM_Stripe\Vendor\Stripe\Topup::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Topup::class,
        \UM_Stripe\Vendor\Stripe\Transfer::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Transfer::class,
        \UM_Stripe\Vendor\Stripe\TransferReversal::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\TransferReversal::class,
        \UM_Stripe\Vendor\Stripe\Treasury\CreditReversal::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Treasury\CreditReversal::class,
        \UM_Stripe\Vendor\Stripe\Treasury\DebitReversal::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Treasury\DebitReversal::class,
        \UM_Stripe\Vendor\Stripe\Treasury\FinancialAccount::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Treasury\FinancialAccount::class,
        \UM_Stripe\Vendor\Stripe\Treasury\FinancialAccountFeatures::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Treasury\FinancialAccountFeatures::class,
        \UM_Stripe\Vendor\Stripe\Treasury\InboundTransfer::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Treasury\InboundTransfer::class,
        \UM_Stripe\Vendor\Stripe\Treasury\OutboundPayment::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Treasury\OutboundPayment::class,
        \UM_Stripe\Vendor\Stripe\Treasury\OutboundTransfer::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Treasury\OutboundTransfer::class,
        \UM_Stripe\Vendor\Stripe\Treasury\ReceivedCredit::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Treasury\ReceivedCredit::class,
        \UM_Stripe\Vendor\Stripe\Treasury\ReceivedDebit::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Treasury\ReceivedDebit::class,
        \UM_Stripe\Vendor\Stripe\Treasury\Transaction::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Treasury\Transaction::class,
        \UM_Stripe\Vendor\Stripe\Treasury\TransactionEntry::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\Treasury\TransactionEntry::class,
        \UM_Stripe\Vendor\Stripe\UsageRecord::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\UsageRecord::class,
        \UM_Stripe\Vendor\Stripe\UsageRecordSummary::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\UsageRecordSummary::class,
        \UM_Stripe\Vendor\Stripe\WebhookEndpoint::OBJECT_NAME => \UM_Stripe\Vendor\Stripe\WebhookEndpoint::class,
    ];
}
