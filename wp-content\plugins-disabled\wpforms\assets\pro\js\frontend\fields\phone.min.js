window.WPFormsPhoneField=window.WPFormsPhoneField||function(n,l,o){const r={init(){o(n).on("wpformsReady",r.ready)},ready(){r.loadValidation(),r.loadSmartField(),r.loadSmartFieldUtils(),r.bindSmartField(),o(".wpforms-smart-phone-field").each(function(){r.repairSmartHiddenField(o(this))})},loadValidation(){void 0!==o.fn.validate&&(o.validator.addMethod("us-phone-field",function(t,e){return!t.match(/[^\d()\-+\s]/)&&(this.optional(e)||10===t.replace(/\D/g,"").length)},wpforms_settings.val_phone),o.validator.addMethod("int-phone-field",function(t,e){return!t.match(/[^\d()\-+\s]/)&&(this.optional(e)||0<t.replace(/\D/g,"").length)},wpforms_settings.val_phone),void 0!==l.intlTelInput)&&o.validator.addMethod("smart-phone-field",function(t,e){var n;return!t.match(/[^\d()\-+\s]/)&&(t=l.intlTelInputGlobals?.getInstance(e),n=o(e).triggerHandler("validate"),this.optional(e)||t?.isValidNumberPrecise()||n)},wpforms_settings.val_phone)},loadSmartField(t=null){void 0!==l.intlTelInput&&(r.loadJqueryIntlTelInput(),(t=t?.length?t:o(n)).find(".wpforms-smart-phone-field").each(function(t,e){e=o(e);if(e.parents(".elementor-location-popup").is(":hidden"))return!1;r.initSmartField(e,{})}))},loadSmartFieldUtils(){const t=o(n).find(".wpforms-smart-phone-field");var e;t.length&&((e=l.intlTelInputGlobals.loadUtils(wpforms_settings.wpforms_plugin_url+"assets/pro/lib/intl-tel-input/module.intl-tel-input-utils.min.js"))?e.then(function(){t.each(r.updateSmartFieldHiddenInput)}):t.each(r.updateSmartFieldHiddenInput))},updateSmartFieldHiddenInput(){var t=o(this).data("plugin_intlTelInput");o(t.hiddenInput).val(t.getNumber())},loadJqueryIntlTelInput(){void 0===o.fn.intlTelInput&&o.fn.extend({intlTelInput(i){var t=o(this);if(void 0===i||"object"==typeof i)return t.each(function(){var t,e=o(this);e.data("plugin_intlTelInput")||(t=l.intlTelInput(e.get(0),i),e.data("plugin_intlTelInput",t))});if("string"==typeof i||"_"!==i[0]){const r=i;let n=this;return t.each(function(){var t=o(this),e=t.data("plugin_intlTelInput");"function"==typeof e[r]&&(n=e[r](),"destroy"===i)&&t.removeData("plugin_intlTelInput")}),n}}})},initSmartField(e,t){if("object"!=typeof e.data("plugin_intlTelInput")){t=0<Object.keys(t).length?t:r.getDefaultSmartFieldOptions();const n=e.closest(".wpforms-field-phone").data("field-id"),i=(t.hiddenInput=function(){return{phone:"wpforms[fields]["+n+"]"}},l.intlTelInput(e.get(0),t));e.on("validate",function(){return i.isValidNumber(i.getNumber())}),e.data("plugin_intlTelInput",i),e.attr("name","wpf-temp-wpforms[fields]["+n+"]"),e.addClass("wpforms-input-temp-name"),e.on("blur input",function(){var t=e.data("plugin_intlTelInput");e.siblings('input[type="hidden"]').val(t.getNumber())})}},bindSmartField(){o(".wpforms-form").on("wpformsBeforeFormSubmit",function(){var t=o(this).find(".wpforms-smart-phone-field");t.each(function(){r.repairSmartHiddenField(o(this))}),t.trigger("input")})},repairSmartHiddenField(n){var i=n.closest(".wpforms-field-phone").data("field-id");if(!o('[name="wpforms[fields]['+i+']"]').length){i=n.data("plugin_intlTelInput");let t=n.val(),e={};i&&(e=i.d||i.options||{},t=i.getNumber(),i.destroy()),n.removeData("plugin_intlTelInput"),n.val(t),r.initSmartField(n,e)}},getDefaultSmartFieldOptions(){var t,e={countrySearch:!1,fixDropdownWidth:!1,preferredCountries:["us","gb"],countryListAriaLabel:wpforms_settings.country_list_label};wpforms_settings.gdpr||(e.geoIpLookup=r.currentIpToCountry);let n;if(wpforms_settings.gdpr&&(t=r.mapLanguageToIso(r.getFirstBrowserLanguage()),n=-1<t.indexOf("-")?t.split("-").pop():t),n){let t=l.intlTelInputGlobals.getCountryData();t=t.filter(function(t){return t.iso2===n.toLowerCase()}),n=t.length?n:""}return e.initialCountry=wpforms_settings.gdpr&&n?n.toLowerCase():"auto",e},getFirstBrowserLanguage(){var t=l.navigator,e=["language","browserLanguage","systemLanguage","userLanguage"];let n,i;if(Array.isArray(t.languages))for(n=0;n<t.languages.length;n++)if((i=t.languages[n])&&i.length)return i;for(n=0;n<e.length;n++)if((i=t[e[n]])&&i.length)return i;return""},mapLanguageToIso(t){return{ar:"ar-SA",bg:"bg-BG",ca:"ca-ES",cs:"cs-CZ",da:"da-DK",de:"de-DE",el:"el-GR",en:"en-US",es:"es-ES",fi:"fi-FI",fr:"fr-FR",he:"he-IL",hi:"hi-IN",hr:"hr-HR",hu:"hu-HU",id:"id-ID",it:"it-IT",ja:"ja-JP",ko:"ko-KR",lt:"lt-LT",lv:"lv-LV",ms:"ms-MY",nl:"nl-NL",no:"nb-NO",pl:"pl-PL",pt:"pt-PT",ro:"ro-RO",ru:"ru-RU",sk:"sk-SK",sl:"sl-SI",sr:"sr-RS",sv:"sv-SE",th:"th-TH",tr:"tr-TR",uk:"uk-UA",vi:"vi-VN",zh:"zh-CN"}[t]||t},currentIpToCountry(n){if(wpforms_settings.country)n(wpforms_settings.country);else{const e=function(){o.get("https://ipapi.co/jsonp",function(){},"jsonp").always(function(t){let e=t?.country?t.country:"";e||(t=r.getFirstBrowserLanguage(),e=-1<t.indexOf("-")?t.split("-").pop():""),n(e)})};o.get("https://geo.wpforms.com/v3/geolocate/json").done(function(t){t&&t.country_iso?n(t.country_iso):e()}).fail(function(){e()})}}};return r}(document,window,jQuery),window.WPFormsPhoneField.init();