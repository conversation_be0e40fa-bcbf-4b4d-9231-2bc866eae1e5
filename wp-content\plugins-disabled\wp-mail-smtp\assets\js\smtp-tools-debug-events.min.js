"use strict";var WPMailSmtpDebugEvents=window.WPMailSmtpDebugEvents||function(s){var n={$debugEventsPage:s(".wp-mail-smtp-tab-tools-debug-events"),$dateFlatpickr:s(".wp-mail-smtp-filter-date-selector")},i={init:function(){s(i.ready)},ready:function(){i.initDateRange(),i.events();var e=new URLSearchParams(location.search);e.has("debug_event_id")&&i.openDebugEventPopup(e.get("debug_event_id"))},events:function(){n.$debugEventsPage.on("click","#wp-mail-smtp-reset-filter .reset",i.resetFilter),n.$debugEventsPage.on("click","#wp-mail-smtp-delete-all-debug-events-button",i.deleteAllDebugEvents),n.$debugEventsPage.on("click",".js-wp-mail-smtp-debug-event-preview",i.eventClicked)},initDateRange:function(){var e=wp_mail_smtp_tools_debug_events.lang_code,t={rangeSeparator:" - "};"undefined"!==flatpickr&&Object.prototype.hasOwnProperty.call(flatpickr,"l10ns")&&Object.prototype.hasOwnProperty.call(flatpickr.l10ns,e)&&((t=flatpickr.l10ns[e]).rangeSeparator=" - "),n.$dateFlatpickr.flatpickr({altInput:!0,altFormat:"M j, Y",dateFormat:"Y-m-d",locale:t,mode:"range"})},resetFilter:function(){var e=s(this).parents("form");e.find(s(this).data("scope")).find("input,select").each(function(){var e=s(this);i.isIgnoredForResetInput(e)||i.resetInput(e)}),e.submit()},resetInput:function(e){switch(e.prop("tagName").toLowerCase()){case"input":e.val("");break;case"select":e.val(e.find("option").first().val())}},isIgnoredForResetInput:function(e){return-1!==["submit","hidden"].indexOf((e.attr("type")||"").toLowerCase())&&!e.hasClass("flatpickr-input")},deleteAllDebugEvents:function(e){e.preventDefault();var t=s(e.target);s.confirm({backgroundDismiss:!1,escapeKey:!0,animationBounce:1,closeIcon:!0,type:"orange",icon:i.getModalIcon("exclamation-circle-solid-orange"),title:wp_mail_smtp_tools_debug_events.texts.notice_title,content:wp_mail_smtp_tools_debug_events.texts.delete_all_notice,buttons:{confirm:{text:wp_mail_smtp_tools_debug_events.texts.yes,btnClass:"btn-confirm",keys:["enter"],action:function(){i.executeAllDebugEventsDeletion(t)}},cancel:{text:wp_mail_smtp_tools_debug_events.texts.cancel,btnClass:"btn-cancel"}}})},eventClicked:function(e){e.preventDefault(),i.openDebugEventPopup(s(this).data("event-id"))},openDebugEventPopup:function(e){var e={action:"wp_mail_smtp_debug_event_preview",id:e,nonce:s("#wp-mail-smtp-debug-events-nonce",n.$debugEventsPage).val()},t=s.alert({backgroundDismiss:!0,escapeKey:!0,animationBounce:1,type:"blue",icon:i.getModalIcon("info-circle-blue"),title:!1,content:wp_mail_smtp_tools_debug_events.loader,boxWidth:"550px",buttons:{confirm:{text:wp_mail_smtp_tools_debug_events.texts.close,btnClass:"btn-confirm",keys:["enter"]}},onOpenBefore:function(){this.$contentPane.addClass("no-scroll")}});s.post(ajaxurl,e,function(e){e.success?(t.setTitle(e.data.title),t.setContent(e.data.content)):(t.setIcon(i.getModalIcon("exclamation-circle-regular-red")),t.setType("red"),t.setContent(e.data))}).fail(function(){t.setContent(wp_mail_smtp_tools_debug_events.texts.error_occurred)})},executeAllDebugEventsDeletion:function(o){o.prop("disabled",!0);var e={action:"wp_mail_smtp_delete_all_debug_events",nonce:s("#wp-mail-smtp-debug-events-nonce",n.$debugEventsPage).val()};s.post(ajaxurl,e,function(e){var t,n,a,s=e.data;e.success?(t="check-circle-solid-green",n="green",a=function(){return location.reload(),!1}):(t="exclamation-circle-regular-red",n="red"),i.displayModal(s,t,n,a),o.prop("disabled",!1)}).fail(function(){i.displayModal(wp_mail_smtp_tools_debug_events.texts.error_occurred,"exclamation-circle-regular-red","red"),o.prop("disabled",!1)})},displayModal:function(e,t,n,a){a=a||function(){},s.alert({backgroundDismiss:!0,escapeKey:!0,animationBounce:1,type:n=n||"default",closeIcon:!0,title:!1,icon:t?i.getModalIcon(t):"",content:e,buttons:{confirm:{text:wp_mail_smtp_tools_debug_events.texts.ok,btnClass:"wp-mail-smtp-btn wp-mail-smtp-btn-md",keys:["enter"],action:a}}})},getModalIcon:function(e){return'"></i><img src="'+wp_mail_smtp_tools_debug_events.plugin_url+"/assets/images/font-awesome/"+e+'.svg" style="width: 40px; height: 40px;" alt=""><i class="'}};return i}((document,window,jQuery));WPMailSmtpDebugEvents.init();