const WPFormsEntriesExport=window.WPFormsEntriesExport||function(e,t,n){const r={$form:n("#wpforms-tools-entries-export"),$selectForm:n("#wpforms-tools-entries-export-selectform"),$selectFormSpinner:n("#wpforms-tools-entries-export-selectform-spinner"),$selectFormMsg:n("#wpforms-tools-entries-export-selectform-msg"),$expOptions:n("#wpforms-tools-entries-export-options"),$fieldsCheckboxes:n("#wpforms-tools-entries-export-options-fields-checkboxes"),$paymentFieldsSection:n("#wpforms-tools-entries-export-options-payment-fields"),$paymentFieldsCheckboxes:n("#wpforms-tools-entries-export-options-payment-fields-checkboxes"),$dateSection:n("#wpforms-tools-entries-export-options-date"),$dateFlatpickr:n("#wpforms-tools-entries-export-options-date-flatpickr"),$searchSection:n("#wpforms-tools-entries-export-options-search"),$searchField:n("#wpforms-tools-entries-export-options-search-field"),$submitButton:n("#wpforms-tools-entries-export-submit"),$cancelButton:n("#wpforms-tools-entries-export-cancel"),$processMsg:n("#wpforms-tools-entries-export-process-msg"),$optionFields:n("#wpforms-tools-entries-export-options-type-info"),$selectStatuses:n("#wpforms-tools-entries-export-select-statuses"),$optionStatuses:n("#wpforms-tools-entries-export-options-status"),$clearDateButton:n(".wpforms-clear-datetime-field")},a=wpforms_tools_entries_export.i18n,i={},l={formsCache:{},init(){n(l.ready)},ready(){i.processing=!1,l.initChoices(),l.initDateRange(),l.initFormContainer(),l.initSubmit(),l.events()},events(){r.$selectForm[0].addEventListener("choice",function(e){l.selectFormEvent(e)}),n(e).on("change","#wpforms-tools-entries-export-options .wpforms-toggle-all",function(){var e=n(this),s=e.find("input");e.siblings().find("input").prop("checked",s.prop("checked"))}),n(e).on("change","#wpforms-tools-entries-export-options-fields-checkboxes label, #wpforms-tools-entries-export-options-payment-fields-checkboxes label, #wpforms-tools-entries-export-options-additional-info label",function(){var e,s,o=n(this);o.hasClass("wpforms-toggle-all")||(s=(e=o.parent().find("label").not(".wpforms-toggle-all").find("input")).filter(":checked"),o.siblings(".wpforms-toggle-all").find("input").prop("checked",s.length===e.length))}),n(e).on("csv_file_error",function(e,s){l.displaySubmitMessage(s,"error")}),n(e).on("change","#wpforms-tools-entries-export-options-type-info input",function(){l.switchDynamicColumnsNotice(n(this))}),n(e).on("click",".wpforms-clear-datetime-field",function(e){e.preventDefault(),r.$dateFlatpickr.flatpickr().clear(),r.$dateFlatpickr[0].removeAttribute("value"),n(this).addClass("wpforms-hidden"),l.initDateRange()})},selectFormEvent(e){e.detail.choice.placeholder?r.$expOptions.addClass("hidden"):i.formID!==e.detail.choice.value&&(i.formID=e.detail.choice.value,l.resetChoices(),r.$optionStatuses.removeClass("wpforms-hidden"),void 0===l.formsCache[i.formID]?l.retrieveFormAndRenderFields():(1<l.formsCache[i.formID].statuses.length?l.setChoices(l.formsCache[i.formID].statuses):r.$optionStatuses.addClass("wpforms-hidden"),l.renderFields(l.formsCache[i.formID].fields),l.renderFields(l.formsCache[i.formID].paymentFields,!0),l.handleSearchFields(l.formsCache[i.formID].fields,l.formsCache[i.formID].paymentFields),l.optionsFields(l.formsCache[i.formID].dynamicColumns),l.addDynamicColumnsNotice(l.formsCache[i.formID].dynamicColumnsNotice)))},switchDynamicColumnsNotice(e){"dynamic_columns"===e.val()&&e.parent().find(".wpforms-tools-entries-export-notice-warning").toggleClass("wpforms-hide")},retrieveFormAndRenderFields(){i.ajaxData={action:"wpforms_tools_entries_export_form_data",nonce:wpforms_tools_entries_export.nonce,form:i.formID},r.$selectFormSpinner.removeClass("hidden"),l.displayFormsMessage(""),n.get(ajaxurl,i.ajaxData).done(function(e){e.success?(l.renderFields(e.data.fields),l.renderFields(e.data.payment_fields,!0),l.optionsFields(e.data.dynamic_columns),e.data.dynamic_columns&&l.addDynamicColumnsNotice(e.data.dynamic_columns_notice),l.handleSearchFields(e.data.fields,e.data.payment_fields),l.formsCache[i.formID]={fields:e.data.fields,paymentFields:e.data.payment_fields,dynamicColumns:e.data.dynamic_columns,dynamicColumnsNotice:e.data.dynamic_columns_notice,statuses:e.data.statuses},r.$expOptions.removeClass("hidden"),1<e.data.statuses.length?l.setChoices(e.data.statuses):r.$optionStatuses.addClass("wpforms-hidden")):(l.displayFormsMessage(e.data.error),r.$expOptions.addClass("hidden"))}).fail(function(e,s,o){l.displayFormsMessage(a.error_prefix+"<br>"+o),r.$expOptions.addClass("hidden")}).always(function(){r.$selectFormSpinner.addClass("hidden")})},exportAjaxStep(e){i.processing&&(e=l.getAjaxPostData(e),n.post(ajaxurl,e).done(function(e){var s;clearTimeout(i.timerId),e.success?0===e.data.count?l.displaySubmitMessage(a.prc_2_no_entries):(s=a.prc_3_done,s+="<br>"+a.prc_3_download+', <a href="#" class="wpforms-download-link">'+a.prc_3_click_here+"</a>.",l.displaySubmitMessage(s,"info"),l.triggerDownload(e.data.request_id)):l.displaySubmitMessage(e.data.error,"error")}).fail(function(e,s,o){clearTimeout(i.timerId),l.displaySubmitMessage(a.error_prefix+"<br>"+o,"error")}).always(function(){l.displaySubmitSpinner(!1)}))},getAjaxPostData(e){let s;if("first-step"===e){const o=[];s=r.$form.serializeArray().reduce(function(e,s){return"statuses"===s.name?o.push(s.value):e[s.name]=s.value,e},{}),r.$fieldsCheckboxes.find("input").length<1&&(s.date="",s["search[term]"]=""),s.statuses=o}else s={action:"wpforms_tools_entries_export_step",nonce:wpforms_tools_entries_export.nonce,request_id:e};return s},initSubmit(){r.$submitButton.on("click",function(e){e.preventDefault(),n(this).hasClass("wpforms-btn-spinner-on")||(r.$submitButton.blur(),l.displaySubmitSpinner(!0),l.displaySubmitMessage(""),i.timerId=setTimeout(function(){l.displaySubmitMessage(a.prc_1_filtering+"<br>"+a.prc_1_please_wait,"info")},3e3),l.exportAjaxStep("first-step"))}),r.$cancelButton.on("click",function(e){e.preventDefault(),r.$cancelButton.blur(),l.displaySubmitMessage(""),l.displaySubmitSpinner(!1)})},initFormContainer(){0<wpforms_tools_entries_export.form_id&&(r.$expOptions.removeClass("hidden"),r.$fieldsCheckboxes.find("input").length<1)&&(r.$dateSection.addClass("hidden"),r.$searchSection.addClass("hidden"))},initChoices(){i.Choices=new Choices(r.$selectStatuses[0],{removeItemButton:!0,allowHTML:!1})},resetChoices(){i.Choices.clearInput(),i.Choices.clearStore(),i.Choices.setChoices([],"value","label",!0)},setChoices(e){var s=e.filter(function(e){return"spam"===e.value})[0];s&&i.Choices.setChoices([s],"value","label",!0),e=e.filter(function(e){return"spam"!==e.value}),i.Choices.setValue(e,"value","label",!0)},initDateRange(){var e=wpforms_tools_entries_export.lang_code,s=t.flatpickr;let o={rangeSeparator:" - "};"undefined"!==s&&s.hasOwnProperty("l10ns")&&s.l10ns.hasOwnProperty(e)&&((o=s.l10ns[e]).rangeSeparator=" - "),r.$dateFlatpickr.flatpickr({altInput:!0,altFormat:"M j, Y",dateFormat:"Y-m-d",locale:o,mode:"range",defaultDate:wpforms_tools_entries_export.dates,onChange(e){r.$clearDateButton.toggleClass("wpforms-hidden",2!==e.length)}})},renderFields(n,e=!1){if("object"==typeof n){const i={checkboxes:"",options:""};var s=Object.keys(n),o=(r.$paymentFieldsSection.show(),0===s.length?i.checkboxes="<span>"+a.error_form_empty+"</span>":(i.checkboxes+='<label class="wpforms-toggle-all"><input type="checkbox" checked> '+a.label_select_all+"</label>",s.forEach(function(e){let s='<label><input type="checkbox" name="fields[{index}]" value="{id}" checked> {label}</label>';var o=parseInt(n[e].id,10);s=(s=(s=s.replace("{index}",parseInt(e,10)+"-"+o)).replace("{id}",o)).replace("{label}",n[e].label),i.checkboxes+=s;let t='<option value="{id}">{label}</option>';t=(t=t.replace("{id}",o)).replace("{label}",n[e].label),i.options+=t}),r.$dateSection.removeClass("hidden"),r.$searchSection.removeClass("hidden")),(e?r.$paymentFieldsCheckboxes:r.$fieldsCheckboxes).html(i.checkboxes),e&&0===s.length&&r.$paymentFieldsSection.hide(),e?"payment-fields":"form-fields"),o=r.$searchField.find('optgroup[data-type="'+o+'"]');o.find("option:not(:first-child)").remove(),e&&o.find("option:first-child").toggle(0===s.length),o.append(i.options)}},handleSearchFields(e,s){e=Object.keys(e).length,s=Object.keys(s).length;0===e&&0===s&&(r.$dateSection.addClass("hidden"),r.$searchSection.addClass("hidden"))},optionsFields(e){l.switchDynamicColumns(e),e&&r.$optionFields.find("input[value=dynamic_columns]").prop("checked",!1)},switchDynamicColumns(e){r.$optionFields.find("input[value=dynamic_columns]").parent().toggle(e)},addDynamicColumnsNotice(e){r.$optionFields.find(".wpforms-tools-entries-export-notice-warning").remove(),r.$optionFields.find("input[value=dynamic_columns]").parent().append('<div class="wpforms-tools-entries-export-notice-warning wpforms-hide">'+e+"</div>")},displaySubmitSpinner(e){e?(r.$submitButton.addClass("wpforms-btn-spinner-on"),r.$cancelButton.removeClass("hidden"),i.processing=!0):(r.$submitButton.removeClass("wpforms-btn-spinner-on"),r.$cancelButton.addClass("hidden"),i.processing=!1)},displayFormsMessage(e){r.$selectFormMsg.html("<p>"+e+"</p>"),0<e.length?r.$selectFormMsg.removeClass("wpforms-hidden"):r.$selectFormMsg.addClass("wpforms-hidden")},displaySubmitMessage(e,s=""){s&&"error"===s?r.$processMsg.addClass("wpforms-error"):r.$processMsg.removeClass("wpforms-error"),r.$processMsg.html("<p>"+e+"</p>"),0<e.length?r.$processMsg.removeClass("wpforms-hidden"):r.$processMsg.addClass("wpforms-hidden")},triggerDownload(e){var s=wpforms_tools_entries_export.export_page,s=(s+="&action=wpforms_tools_entries_export_download")+("&nonce="+wpforms_tools_entries_export.nonce)+("&request_id="+e);r.$expOptions.find("iframe").remove(),r.$expOptions.append('<iframe src="'+s+'"></iframe>'),r.$processMsg.find(".wpforms-download-link").attr("href",s)}};return l}(document,window,jQuery);WPFormsEntriesExport.init();