<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="ExpandRulesetReferenceTest" xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/PHPCSStandards/PHP_CodeSniffer/master/phpcs.xsd">

    <!-- This "home path" reference is not going to work, but that's not the point of this test.
         The point is for the code to, at least, _try_ to resolve it. -->
    <rule ref="~/src/Standards/Squiz/Sniffs/Files/"/>

</ruleset>
