<?php
/**
 * Template for the UM User Photos, The "Album header" block
 *
 * Page: "Profile", tab "Photos"
 * Caller: User_Photos_Ajax->get_single_album_view() method
 * @version 2.1.9
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/album-head.php
 * @var object $album
 * @var bool   $is_my_profile
 * @var int    $album_id
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
$disable_title = UM()->options()->get( 'um_user_photos_disable_title' );
$disable_cover = UM()->options()->get( 'um_user_photos_disable_cover' );
?>

<div class="um-user-photos-album-head">
	<div class="col-back">
		<a href="javascript:void(0);"
			class="um-user-photos-back-to-gallery"
			data-nonce="<?php echo esc_attr( wp_create_nonce( 'um_get_gallery' ) ); ?>"
			data-profile="<?php echo esc_attr( $album->post_author ); ?>">
			<span class="um-icon-arrow-left-c"></span> <?php esc_html_e( 'Back', 'um-user-photos' ); ?>
		</a>
	</div>

	<div class="col-title">
		<h2>
		<?php
		if ( ! $disable_title ) {
			echo esc_html( $album->post_title );
		}
		?>
		</h2>
	</div>

	<div class="col-delete">
		<?php if ( $is_my_profile ) { ?>
			<a href="" class="um-user-photos-album-options"><i class="um-faicon-cog"></i></a>
			<div class="um-dropdown">
				<div class="um-dropdown-b">
					<div class="um-dropdown-arr"><i class="um-icon-arrow-up-b"></i></div>
					<ul>
						<li>
							<a href="javascript:void(0);" class="um-user-photos-edit-album"
								data-modal_title="<?php esc_attr_e( 'Edit album', 'um-user-photos' ); ?>"
								title="<?php esc_attr_e( 'Edit album', 'um-user-photos' ); ?>"
								data-nonce="<?php echo esc_attr( wp_create_nonce( 'um_edit_album_modal' . $album_id ) ); ?>"
								data-id="<?php echo esc_attr( $album_id ); ?>">
								<?php esc_html_e( 'Edit album', 'um-user-photos' ); ?>
							</a>
						</li>
						<li>
							<a href="javascript:void(0);"
								class="um-user-photos-delete-album"
								data-title="<?php esc_attr_e( 'Delete album', 'um-user-photos' ); ?>"
								data-confirm="<?php esc_attr_e( 'Are you sure to delete this album?', 'um-user-photos' ); ?>"
								data-nonce="<?php echo esc_attr( wp_create_nonce( 'um_delete_album' . $album_id ) ); ?>"
								data-id="<?php echo esc_attr( $album_id ); ?>">
								<?php esc_html_e( 'Delete album', 'um-user-photos' ); ?>
							</a>
						</li>
						<li><a href="javascript:void(0);" class="um-dropdown-hide"><?php esc_html_e( 'Cancel', 'um-user-photos' ); ?></a></li>
					</ul>
				</div>
			</div>
		<?php } ?>
	</div>
</div>
