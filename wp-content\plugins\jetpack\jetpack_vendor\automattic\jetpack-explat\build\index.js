(()=>{"use strict";var e={792:(e,t,n)=>{n.d(t,{A:()=>s});var r=n(790),i=n(609);const o={isEligible:!0};function s(e){const t=(t,n={})=>{const r={...o,...n},[,s]=(0,i.useReducer)((e=>e+1),0),a=(0,i.useRef)(t);if((0,i.useEffect)((()=>{let n=!0;return r.isEligible&&e.loadExperimentAssignment(t).then((()=>{n&&s()})),()=>{n=!1}}),[t,r.isEligible]),t===a.current||a.current.startsWith("explat_test")||e.config.logError({message:"[ExPlat] useExperiment: experimentName should never change between renders!"}),!r.isEligible)return[!1,null];const m=e.dangerouslyGetMaybeLoadedExperimentAssignment(t);return[!m,m]};return{useExperiment:t,Experiment:({defaultExperience:e,treatmentExperience:n,loadingExperience:i,name:o,options:s})=>{const[a,m]=t(o,s);return a?(0,r.jsx)(r.Fragment,{children:i}):m?.variationName?(0,r.jsx)(r.Fragment,{children:n}):(0,r.jsx)(r.Fragment,{children:e})},ProvideExperimentData:({children:e,name:n,options:r})=>{const[i,o]=t(n,r);return e(i,o)}}}},517:(e,t,n)=>{n.d(t,{kU:()=>c,pg:()=>u});var r=n(689),i=n(808),o=n(738),s=n(762),a=n(626);const m=1e4;Error;function c(e){if("undefined"==typeof window)throw new Error("Running outside of a browser context.");const t={},n=(...t)=>{try{e.logError(...t)}catch(e){}};try{(0,r.bZ)()}catch(e){n({message:e.message,source:"removeExpiredExperimentAssignments-error"})}return{loadExperimentAssignment:async c=>{try{if(!a.Eo(c))throw new Error(`Invalid experimentName: "${c}"`);const n=(0,r.B1)(c);if(n&&i.H2(n))return n;void 0===t[c]&&(t[c]=(t=>s.MC((async()=>{const n=await o.FI(e,t);return(0,r.a2)(n),n})))(c));let u=m;Math.random()>.5&&(u=5e3);const l=await s.BK(t[c](),u);if(!l)throw new Error("Could not fetch ExperimentAssignment");return l}catch(e){n({message:e.message,experimentName:c,source:"loadExperimentAssignment-initialError"})}try{const e=(0,r.B1)(c);if(e)return e;const t=(0,i.fj)(c);return(0,r.a2)(t),t}catch(e){return n({message:e.message,experimentName:c,source:"loadExperimentAssignment-fallbackError"}),(0,i.fj)(c)}},dangerouslyGetExperimentAssignment:t=>{try{if(!a.Eo(t))throw new Error(`Invalid experimentName: ${t}`);const i=(0,r.B1)(t);if(!i)throw new Error("Trying to dangerously get an ExperimentAssignment that hasn't loaded.");return e.isDevelopmentMode&&i&&s.XZ()-i.retrievedTimestamp<1e3&&n({message:"Warning: Trying to dangerously get an ExperimentAssignment too soon after loading it.",experimentName:t,source:"dangerouslyGetExperimentAssignment"}),i}catch(r){return e.isDevelopmentMode&&n({message:r.message,experimentName:t,source:"dangerouslyGetExperimentAssignment-error"}),(0,i.fj)(t)}},dangerouslyGetMaybeLoadedExperimentAssignment:t=>{try{if(!a.Eo(t))throw new Error(`Invalid experimentName: ${t}`);const e=(0,r.B1)(t);return e||null}catch(r){return e.isDevelopmentMode&&n({message:r.message,experimentName:t,source:"dangerouslyGetMaybeLoadedExperimentAssignment-error"}),(0,i.fj)(t)}},config:e}}function u(e){return{loadExperimentAssignment:async t=>(e.logError({message:"Attempting to load ExperimentAssignment in SSR context",experimentName:t}),(0,i.fj)(t)),dangerouslyGetExperimentAssignment:t=>(e.logError({message:"Attempting to dangerously get ExperimentAssignment in SSR context",experimentName:t}),(0,i.fj)(t)),dangerouslyGetMaybeLoadedExperimentAssignment:t=>(e.logError({message:"Attempting to dangerously get ExperimentAssignment in SSR context",experimentName:t}),(0,i.fj)(t)),config:e}}},226:(e,t,n)=>{n.d(t,{k:()=>i});var r=n(517);const i="undefined"==typeof window?r.pg:r.kU},689:(e,t,n)=>{n.d(t,{B1:()=>c,a2:()=>m,bZ:()=>p});var r=n(808),i=n(765),o=n(626);const s="explat-experiment-",a=e=>`${s}-${e}`;function m(e){o.zV(e);const t=c(e.experimentName);if(t&&e.retrievedTimestamp<t.retrievedTimestamp)throw new Error("Trying to store an older experiment assignment than is present in the store, likely a race condition.");i.A.setItem(a(e.experimentName),JSON.stringify(e))}function c(e){const t=i.A.getItem(a(e));if(t)return o.zV(JSON.parse(t))}const u=e=>[...Array(e).keys()];function l(e){return e.startsWith(s)}function d(e){return e.slice(s.length+1)}function p(){u(i.A.length).map((e=>i.A.key(e))).filter(l).map(d).filter((e=>{try{if(r.H2(c(e)))return!1}catch(e){}return!0})).map(a).map((e=>i.A.removeItem(e)))}},808:(e,t,n)=>{n.d(t,{H2:()=>i,fj:()=>s,fn:()=>o});var r=n(762);function i(e){return r.XZ()<e.ttl*r.If+e.retrievedTimestamp}const o=60,s=(e,t=o)=>({experimentName:e,variationName:null,retrievedTimestamp:r.XZ(),ttl:Math.max(o,t),isFallbackExperimentAssignment:!0})},765:(e,t,n)=>{n.d(t,{A:()=>i});let r={_data:{},setItem:function(e,t){this._data[e]=t},getItem:function(e){return this._data.hasOwnProperty(e)?this._data[e]:null},removeItem:function(e){delete this._data[e]},clear:function(){this._data={}},get length(){return Object.keys(this._data).length},key:function(e){const t=Object.keys(this._data);return e in t?t[e]:null}};try{window.localStorage&&(r=window.localStorage)}catch(e){}const i=r},738:(e,t,n)=>{n.d(t,{FI:()=>l});var r=n(808),i=n(765),o=n(762),s=n(626);function a(e){if(function(e){return(0,s.Gv)(e)&&(0,s.Gv)(e.variations)&&"number"==typeof e.ttl&&0<e.ttl}(e))return e;throw new Error("Invalid FetchExperimentAssignmentResponse")}const m="explat-last-anon-id",c="explat-last-anon-id-retrieval-time",u=async e=>{const t=await e();if(t)return i.A.setItem(m,t),i.A.setItem(c,String((0,o.XZ)())),t;const n=i.A.getItem(m),r=i.A.getItem(c);return n&&r&&(0,o.XZ)()-parseInt(r,10)<864e5?n:null};async function l(e,t){const n=(0,o.XZ)(),{variations:i,ttl:m}=a(await e.fetchExperimentAssignment({anonId:await u(e.getAnonId),experimentName:t})),c=Math.max(r.fn,m),l=Object.entries(i).map((([e,t])=>({experimentName:e,variationName:t,retrievedTimestamp:n,ttl:c}))).map(s.zV);if(l.length>1)throw new Error("Received multiple experiment assignments while trying to fetch exactly one.");if(0===l.length)return r.fj(t,c);const d=l[0];if(d.experimentName!==t)throw new Error("Newly fetched ExperimentAssignment's experiment name does not match request.");if(!r.H2(d))throw new Error("Newly fetched experiment isn't alive.");return d}},762:(e,t,n)=>{n.d(t,{BK:()=>s,If:()=>r,MC:()=>a,XZ:()=>o});const r=1e3;let i=Date.now();function o(){const e=Date.now();return i=i<e?e:i+1,i}function s(e,t){return Promise.race([e,new Promise(((e,n)=>setTimeout((()=>n(new Error(`Promise has timed-out after ${t}ms.`))),t)))])}function a(e){let t=null;return()=>(t||(t=e().finally((()=>{t=null}))),t)}},626:(e,t,n)=>{function r(e){return"object"==typeof e&&null!==e}function i(e){return"string"==typeof e&&""!==e&&/^[a-z0-9_]*$/.test(e)}function o(e){if(!function(e){return r(e)&&i(e.experimentName)&&(i(e.variationName)||null===e.variationName)&&"number"==typeof e.retrievedTimestamp&&"number"==typeof e.ttl&&0!==e.ttl}(e))throw new Error("Invalid ExperimentAssignment");return e}n.d(t,{Eo:()=>i,Gv:()=>r,zV:()=>o})},172:(e,t)=>{t.qg=function(e,t){const n=new a,r=e.length;if(r<2)return n;const i=t?.decode||u;let o=0;do{const t=e.indexOf("=",o);if(-1===t)break;const s=e.indexOf(";",o),a=-1===s?r:s;if(t>a){o=e.lastIndexOf(";",t-1)+1;continue}const u=m(e,o,t),l=c(e,t,u),d=e.slice(u,l);if(void 0===n[d]){let r=m(e,t+1,a),o=c(e,a,r);const s=i(e.slice(r,o));n[d]=s}o=a+1}while(o<r);return n};const n=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,r=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,i=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,o=/^[\u0020-\u003A\u003D-\u007E]*$/,s=Object.prototype.toString,a=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function m(e,t,n){do{const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<n);return n}function c(e,t,n){for(;t>n;){const n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return n}function u(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},889:(e,t,n)=>{n.d(t,{Ck:()=>s,wf:()=>o});var r=n(172);let i=null;const o=async()=>{let e=0;return i=new Promise((t=>{const n=()=>{const i=(0,r.qg)(document.cookie).tk_ai||null;"string"!=typeof i||""===i?99<=e?t(null):(e+=1,setTimeout(n,50)):t(i)};n()})),i},s=async()=>await i},222:(e,t,n)=>{n.d(t,{V:()=>m,z:()=>a});var r=n(455),i=n.n(r),o=n(832);const s=(e=!1)=>async({experimentName:t,anonId:n})=>{if(!n)throw new Error("Tracking is disabled, can't fetch experimentAssignment");const r={experiment_name:t,anon_id:n??void 0,as_connected_user:e},s=(0,o.addQueryArgs)("jetpack/v4/explat/assignments",r);return await i()({path:s})},a=s(!1),m=s(!0)},69:(e,t,n)=>{n.d(t,{v:()=>i});var r=n(382);const i=e=>{const t=e=>{r.D&&console.error("[ExPlat] Unable to send error to server:",e)};try{const{message:n,...i}=e,o={message:n,properties:{...i,context:"explat",explat_client:"jetpack"}};if(r.D)console.error("[ExPlat] ",e.message,e);else{const e=new window.FormData;e.append("error",JSON.stringify(o)),window.fetch("https://public-api.wordpress.com/rest/v1.1/js-error",{method:"POST",body:e}).catch(t)}}catch(e){t(e)}}},382:(e,t,n)=>{n.d(t,{D:()=>r});const r=!1},609:e=>{e.exports=window.React},790:e=>{e.exports=window.ReactJSXRuntime},455:e=>{e.exports=window.wp.apiFetch},832:e=>{e.exports=window.wp.url}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var r=n(226),i=n(792),o=n(889),s=n(222),a=n(69),m=n(382);(0,o.wf)().catch((e=>(0,a.v)({message:e.message})));const c=(0,r.k)({fetchExperimentAssignment:s.z,getAnonId:o.Ck,logError:a.v,isDevelopmentMode:m.D}),{loadExperimentAssignment:u,dangerouslyGetExperimentAssignment:l}=c,{useExperiment:d,Experiment:p,ProvideExperimentData:g}=(0,i.A)(c),f=(0,r.k)({fetchExperimentAssignment:s.V,getAnonId:o.Ck,logError:a.v,isDevelopmentMode:m.D}),{loadExperimentAssignment:x,dangerouslyGetExperimentAssignment:E}=f,{useExperiment:h,Experiment:w,ProvideExperimentData:y}=(0,i.A)(f)})();