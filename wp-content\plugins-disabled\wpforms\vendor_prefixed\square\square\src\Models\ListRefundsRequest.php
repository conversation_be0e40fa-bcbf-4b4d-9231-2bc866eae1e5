<?php

declare (strict_types=1);
namespace WPForms\Vendor\Square\Models;

use stdClass;
/**
 * Defines the query parameters that can be included in
 * a request to the [ListRefunds](api-endpoint:Transactions-ListRefunds) endpoint.
 *
 * Deprecated - recommend using [SearchOrders](api-endpoint:Orders-SearchOrders)
 */
class ListRefundsRequest implements \JsonSerializable
{
    /**
     * @var array
     */
    private $beginTime = [];
    /**
     * @var array
     */
    private $endTime = [];
    /**
     * @var string|null
     */
    private $sortOrder;
    /**
     * @var array
     */
    private $cursor = [];
    /**
     * Returns Begin Time.
     * The beginning of the requested reporting period, in RFC 3339 format.
     *
     * See [Date ranges](https://developer.squareup.com/docs/build-basics/working-with-dates) for details
     * on date inclusivity/exclusivity.
     *
     * Default value: The current time minus one year.
     */
    public function getBeginTime() : ?string
    {
        if (\count($this->beginTime) == 0) {
            return null;
        }
        return $this->beginTime['value'];
    }
    /**
     * Sets Begin Time.
     * The beginning of the requested reporting period, in RFC 3339 format.
     *
     * See [Date ranges](https://developer.squareup.com/docs/build-basics/working-with-dates) for details
     * on date inclusivity/exclusivity.
     *
     * Default value: The current time minus one year.
     *
     * @maps begin_time
     */
    public function setBeginTime(?string $beginTime) : void
    {
        $this->beginTime['value'] = $beginTime;
    }
    /**
     * Unsets Begin Time.
     * The beginning of the requested reporting period, in RFC 3339 format.
     *
     * See [Date ranges](https://developer.squareup.com/docs/build-basics/working-with-dates) for details
     * on date inclusivity/exclusivity.
     *
     * Default value: The current time minus one year.
     */
    public function unsetBeginTime() : void
    {
        $this->beginTime = [];
    }
    /**
     * Returns End Time.
     * The end of the requested reporting period, in RFC 3339 format.
     *
     * See [Date ranges](https://developer.squareup.com/docs/build-basics/working-with-dates) for details
     * on date inclusivity/exclusivity.
     *
     * Default value: The current time.
     */
    public function getEndTime() : ?string
    {
        if (\count($this->endTime) == 0) {
            return null;
        }
        return $this->endTime['value'];
    }
    /**
     * Sets End Time.
     * The end of the requested reporting period, in RFC 3339 format.
     *
     * See [Date ranges](https://developer.squareup.com/docs/build-basics/working-with-dates) for details
     * on date inclusivity/exclusivity.
     *
     * Default value: The current time.
     *
     * @maps end_time
     */
    public function setEndTime(?string $endTime) : void
    {
        $this->endTime['value'] = $endTime;
    }
    /**
     * Unsets End Time.
     * The end of the requested reporting period, in RFC 3339 format.
     *
     * See [Date ranges](https://developer.squareup.com/docs/build-basics/working-with-dates) for details
     * on date inclusivity/exclusivity.
     *
     * Default value: The current time.
     */
    public function unsetEndTime() : void
    {
        $this->endTime = [];
    }
    /**
     * Returns Sort Order.
     * The order (e.g., chronological or alphabetical) in which results from a request are returned.
     */
    public function getSortOrder() : ?string
    {
        return $this->sortOrder;
    }
    /**
     * Sets Sort Order.
     * The order (e.g., chronological or alphabetical) in which results from a request are returned.
     *
     * @maps sort_order
     */
    public function setSortOrder(?string $sortOrder) : void
    {
        $this->sortOrder = $sortOrder;
    }
    /**
     * Returns Cursor.
     * A pagination cursor returned by a previous call to this endpoint.
     * Provide this to retrieve the next set of results for your original query.
     *
     * See [Paginating results](https://developer.squareup.com/docs/working-with-apis/pagination) for more
     * information.
     */
    public function getCursor() : ?string
    {
        if (\count($this->cursor) == 0) {
            return null;
        }
        return $this->cursor['value'];
    }
    /**
     * Sets Cursor.
     * A pagination cursor returned by a previous call to this endpoint.
     * Provide this to retrieve the next set of results for your original query.
     *
     * See [Paginating results](https://developer.squareup.com/docs/working-with-apis/pagination) for more
     * information.
     *
     * @maps cursor
     */
    public function setCursor(?string $cursor) : void
    {
        $this->cursor['value'] = $cursor;
    }
    /**
     * Unsets Cursor.
     * A pagination cursor returned by a previous call to this endpoint.
     * Provide this to retrieve the next set of results for your original query.
     *
     * See [Paginating results](https://developer.squareup.com/docs/working-with-apis/pagination) for more
     * information.
     */
    public function unsetCursor() : void
    {
        $this->cursor = [];
    }
    /**
     * Encode this object to JSON
     *
     * @param bool $asArrayWhenEmpty Whether to serialize this model as an array whenever no fields
     *        are set. (default: false)
     *
     * @return array|stdClass
     */
    #[\ReturnTypeWillChange] // @phan-suppress-current-line PhanUndeclaredClassAttribute for (php < 8.1)
    public function jsonSerialize(bool $asArrayWhenEmpty = \false)
    {
        $json = [];
        if (!empty($this->beginTime)) {
            $json['begin_time'] = $this->beginTime['value'];
        }
        if (!empty($this->endTime)) {
            $json['end_time'] = $this->endTime['value'];
        }
        if (isset($this->sortOrder)) {
            $json['sort_order'] = $this->sortOrder;
        }
        if (!empty($this->cursor)) {
            $json['cursor'] = $this->cursor['value'];
        }
        $json = \array_filter($json, function ($val) {
            return $val !== null;
        });
        return !$asArrayWhenEmpty && empty($json) ? new stdClass() : $json;
    }
}
