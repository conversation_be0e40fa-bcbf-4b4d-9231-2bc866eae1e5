# Copyright (C) 2023 Ultimate Member
# This file is distributed under the same license as the Ultimate Member - User Bookmarks plugin.
msgid ""
msgstr ""
"Project-Id-Version: Ultimate Member - User Bookmarks 2.1.5\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/user-bookmarks\n"
"POT-Creation-Date: 2023-12-07T23:45:05+00:00\n"
"PO-Revision-Date: 2023-12-08 14:23+0200\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.0.1\n"
"X-Domain: um-user-bookmarks\n"

#. Plugin Name of the plugin
msgid "Ultimate Member - User Bookmarks"
msgstr ""

#. Plugin URI of the plugin
msgid "https://ultimatemember.com/extensions/user-bookmarks/"
msgstr ""

#. Description of the plugin
msgid "Let users bookmark posts, pages and custom post types"
msgstr ""

#. Author of the plugin
msgid "Ultimate Member"
msgstr ""

#. Author URI of the plugin
msgid "http://ultimatemember.com/"
msgstr ""

#: includes/admin/class-admin.php:71 um-user-bookmarks.php:88
msgid "User Bookmarks License Key"
msgstr ""

#: includes/admin/class-admin.php:83
msgid "Disable folder system for Bookmarks"
msgstr ""

#: includes/admin/class-admin.php:84
msgid ""
"Check this box if you would like to enable the using User Bookmarks "
"functionality without folders."
msgstr ""

#: includes/admin/class-admin.php:89
msgid ""
"We recommend creating a backup of your site before running the update "
"process. Do not exit the page before the update process has complete."
msgstr ""

#: includes/admin/class-admin.php:90
msgid ""
"After clicking the <strong>\"Run\"</strong> button, the update process will "
"start. All information will be displayed in the field below."
msgstr ""

#: includes/admin/class-admin.php:91
msgid ""
"If the update was successful, you will see a corresponding message. "
"Otherwise, contact technical support if the update failed."
msgstr ""

#: includes/admin/class-admin.php:95 includes/admin/class-admin.php:263
msgid "User Bookmarks"
msgstr ""

#: includes/admin/class-admin.php:102
msgid "Bottom"
msgstr ""

#: includes/admin/class-admin.php:103
msgid "Top"
msgstr ""

#: includes/admin/class-admin.php:105
msgid "Bookmark icon position"
msgstr ""

#: includes/admin/class-admin.php:114
msgid "Enable bookmark"
msgstr ""

#: includes/admin/class-admin.php:120
msgid "Enable bookmark on archive pages"
msgstr ""

#: includes/admin/class-admin.php:125
msgid "Enable bookmark on profile pages"
msgstr ""

#: includes/admin/class-admin.php:130
msgid "Profile folders text (Plural)"
msgstr ""

#: includes/admin/class-admin.php:138
msgid "Profile folder text (Singular)"
msgstr ""

#: includes/admin/class-admin.php:146
msgid "Add bookmark button text"
msgstr ""

#: includes/admin/class-admin.php:153
msgid "Remove bookmark button text"
msgstr ""

#: includes/admin/class-admin.php:161
msgid "Folders text (Plural)"
msgstr ""

#: includes/admin/class-admin.php:169
msgid "Folder text (Singular)"
msgstr ""

#: includes/admin/class-admin.php:177
msgid "Bookmarked Icon (css class)"
msgstr ""

#: includes/admin/class-admin.php:184
msgid "Regular Icon (css class)"
msgstr ""

#: includes/admin/class-admin.php:190
msgid "Using Page Builder"
msgstr ""

#: includes/admin/class-admin.php:191
msgid "If it's not checked - show post excerpt without applying shortcodes"
msgstr ""

#: includes/admin/class-admin.php:196
msgid "Create default folder"
msgstr ""

#: includes/admin/class-admin.php:197
msgid ""
"Automatically create the first bookmarks folder for a newly created member"
msgstr ""

#: includes/admin/class-admin.php:203
msgid "Default folder name"
msgstr ""

#: includes/admin/templates/bookmarks/metabox.php:13
msgid "Disable Bookmark"
msgstr ""

#: includes/admin/templates/role/bookmark.php:20
msgid "Enable bookmark feature?"
msgstr ""

#: includes/admin/templates/role/bookmark.php:21
msgid "Can this role have bookmark feature?"
msgstr ""

#: includes/core/class-bookmark-ajax.php:43
#: includes/core/class-bookmark-ajax.php:67
#: includes/core/class-bookmark-ajax.php:153
#: includes/core/class-bookmark-ajax.php:167
#: includes/core/class-bookmark-ajax.php:193
#: includes/core/class-bookmark-ajax.php:212
#: includes/core/class-bookmark-ajax.php:264
#: includes/core/class-bookmark-ajax.php:290
#: includes/core/class-bookmark-ajax.php:345
#: includes/core/class-bookmark-ajax.php:348
#: includes/core/class-bookmark-ajax.php:376
#: includes/core/class-bookmark-ajax.php:379
#: includes/core/class-bookmark-ajax.php:412
#: includes/core/class-bookmark-ajax.php:460
#: includes/core/class-bookmark-ajax.php:474
#: includes/core/class-bookmark-ajax.php:483
msgid "Invalid request"
msgstr ""

#: includes/core/class-bookmark-ajax.php:46
#: includes/core/class-bookmark-ajax.php:70
#: includes/core/class-bookmark-ajax.php:207
#: includes/core/class-bookmark-ajax.php:260
msgid "Invalid post data"
msgstr ""

#: includes/core/class-bookmark-ajax.php:73
#: includes/core/class-bookmark-ajax.php:163
#: includes/core/class-bookmark-ajax.php:202
#: includes/core/class-bookmark-ajax.php:256
#: includes/core/class-bookmark-ajax.php:293
msgid "You have to login"
msgstr ""

#. translators: %s is a folder text
#: includes/core/class-bookmark-ajax.php:77
#: includes/core/class-bookmark-ajax.php:297
#: includes/core/class-bookmark-ajax.php:487
msgid "%s name is required"
msgstr ""

#. translators: %s is a folder text
#: includes/core/class-bookmark-ajax.php:106
#: includes/core/class-bookmark-ajax.php:317
#: includes/core/class-bookmark-ajax.php:500
msgid "%s is already exists"
msgstr ""

#. translators: %s is a folder text
#: includes/core/class-bookmark-ajax.php:124
msgid "%s isn't exists"
msgstr ""

#: includes/core/class-bookmark-ajax.php:171
msgid "Invalid profile data"
msgstr ""

#: includes/core/class-bookmark-ajax.php:218
msgid "Not found in bookmarks"
msgstr ""

#. translators: %s is a folder text
#: includes/core/class-bookmark-ajax.php:336
msgid "Invalid %s"
msgstr ""

#. translators: %s is a folder text
#: includes/core/class-bookmark-ajax.php:358
#: includes/core/class-bookmark-ajax.php:389
#: includes/core/class-bookmark-ajax.php:424
#: includes/core/class-bookmark-ajax.php:496
msgid "%s doesn't exists"
msgstr ""

#: includes/core/class-bookmark-ajax.php:409
#: includes/core/class-bookmark-ajax.php:456
msgid "Invalid request."
msgstr ""

#: includes/core/class-bookmark-ajax.php:539
#: includes/core/class-bookmark-ajax.php:547
#: includes/core/class-bookmark-ajax.php:606
msgid "Default"
msgstr ""

#: includes/core/class-bookmark-ajax.php:574
msgid "Wrong data"
msgstr ""

#. translators: %1$s is a "from" pagination, %2$s is a "to" pagination
#: includes/core/class-bookmark-ajax.php:619
msgid "Bookmarks metadata from %1$s to %2$s was upgraded successfully..."
msgstr ""

#: includes/core/class-bookmark-common.php:314
#: includes/core/class-bookmark-setup.php:44
msgid "My bookmarks"
msgstr ""

#: includes/core/class-bookmark-privacy.php:34
msgid "Everyone"
msgstr ""

#: includes/core/class-bookmark-privacy.php:35
msgid "Only me"
msgstr ""

#: includes/core/class-bookmark-privacy.php:41
#: includes/core/class-bookmark-privacy.php:44
msgid "Who can see user bookmarks tab?"
msgstr ""

#: includes/core/class-bookmark-profile.php:47
msgid "Bookmarks"
msgstr ""

#: includes/core/class-bookmark-profile.php:73
msgid "All"
msgstr ""

#: includes/core/class-bookmark-profile.php:78
#: includes/core/class-bookmark-profile.php:85
msgid "All Posts"
msgstr ""

#: includes/core/class-bookmark-profile.php:105
#: includes/core/class-bookmark-profile.php:120
#: includes/core/class-bookmark-profile.php:135
msgid "You do not have permission to view bookmark"
msgstr ""

#: includes/core/class-bookmark-profile.php:239
#: includes/core/class-bookmark-profile.php:275
#: includes/core/class-bookmark-shortcode.php:139
#: templates/profile/folder-view.php:54 templates/profile/users-view.php:39
msgid "No bookmarks have been added."
msgstr ""

#: includes/core/class-bookmark-setup.php:33
msgid "Bookmark"
msgstr ""

#: includes/core/class-bookmark-setup.php:34
msgid "Remove bookmark"
msgstr ""

#: includes/core/class-bookmark-setup.php:36
#: includes/core/um-user-bookmarks-functions.php:174
msgid "Folders"
msgstr ""

#: includes/core/class-bookmark-setup.php:37
#: includes/core/um-user-bookmarks-functions.php:175
msgid "Folder"
msgstr ""

#: includes/core/class-bookmark-setup.php:38
#: includes/core/um-user-bookmarks-functions.php:176
msgid "Users"
msgstr ""

#: includes/core/class-bookmark-setup.php:39
#: includes/core/um-user-bookmarks-functions.php:177
msgid "User"
msgstr ""

#: templates/modal.php:20 assets/js/um-user-bookmarks.js:241
msgid "Loading.."
msgstr ""

#: templates/profile/edit-folder.php:30
msgid "Edit folder"
msgstr ""

#. translators: %s is a folder text
#: templates/profile/edit-folder.php:41
msgid "%s title"
msgstr ""

#. translators: %s is a folder text
#: templates/profile/edit-folder.php:44
msgid "%s title is required"
msgstr ""

#: templates/profile/edit-folder.php:51
#: templates/profile/folder-view/add-folder.php:43
#: templates/profile/folder-view/folder/folder-info.php:26
#: templates/select-folder.php:68
msgid "Private"
msgstr ""

#: templates/profile/edit-folder.php:57
msgid "Update"
msgstr ""

#. translators: %s is a folder text
#: templates/profile/folder-view/add-folder.php:27
msgid "Add %s"
msgstr ""

#. translators: %s is a folder text
#: templates/profile/folder-view/add-folder.php:36
#: templates/select-folder.php:59
msgid "%s name"
msgstr ""

#. translators: %s is a folder text
#: templates/profile/folder-view/add-folder.php:38
#: templates/select-folder.php:62
msgid "%s name is required."
msgstr ""

#: templates/profile/folder-view/add-folder.php:47
#: templates/select-folder.php:73
msgid "Create"
msgstr ""

#: templates/profile/folder-view/folder.php:34
msgid "saved"
msgstr ""

#: templates/profile/folder-view/folder/folder-info.php:28
msgid "Public"
msgstr ""

#: templates/profile/single-folder.php:54
msgid "Folder is empty"
msgstr ""

#: templates/profile/single-folder/bookmark-item.php:50
#: templates/profile/users-view.php:32
msgid "Remove"
msgstr ""

#: templates/profile/single-folder/dropdown.php:37
msgid "Edit"
msgstr ""

#: templates/profile/single-folder/dropdown.php:41
msgid "Delete folder and its content?"
msgstr ""

#: templates/profile/single-folder/dropdown.php:42
msgid "Delete"
msgstr ""

#: templates/profile/single-folder/dropdown.php:47
msgid "Cancel"
msgstr ""

#. translators: %s is a folder text
#: templates/select-folder.php:26
msgid "Select %s"
msgstr ""

#. translators: %s is the User Bookmarks extension name.
#: um-user-bookmarks.php:53 um-user-bookmarks.php:72
msgid ""
"The <strong>%s</strong> extension requires the Ultimate Member plugin to be "
"activated to work properly. You can download it <a href=\"https://wordpress."
"org/plugins/ultimate-member\">here</a>"
msgstr ""

#: assets/js/admin/um-user-bookmarks.js:9
msgid "Getting bookmarks metadata"
msgstr ""

#: assets/js/admin/um-user-bookmarks.js:21
msgid "There are "
msgstr ""

#: assets/js/admin/um-user-bookmarks.js:21
msgid " bookmarks metadata rows..."
msgstr ""

#: assets/js/admin/um-user-bookmarks.js:22
msgid "Start bookmarks metadata upgrading..."
msgstr ""

#: assets/js/um-user-bookmarks.js:197
msgid "Successful"
msgstr ""
