{"name": "dompdf/dompdf", "type": "library", "description": "DOMPDF is a CSS 2.1 compliant HTML to PDF converter", "homepage": "https://github.com/dompdf/dompdf", "license": "LGPL-2.1", "authors": [{"name": "The Dompdf Community", "homepage": "https://github.com/dompdf/dompdf/blob/master/AUTHORS.md"}], "autoload": {"psr-4": {"Dompdf\\": "src/"}, "classmap": ["lib/"]}, "autoload-dev": {"psr-4": {"Dompdf\\Tests\\": "tests/"}}, "require": {"php": "^7.1 || ^8.0", "ext-dom": "*", "ext-mbstring": "*", "masterminds/html5": "^2.0", "phenx/php-font-lib": ">=0.5.4 <1.0.0", "phenx/php-svg-lib": ">=0.3.3 <1.0.0"}, "require-dev": {"ext-json": "*", "ext-zip": "*", "phpunit/phpunit": "^7.5 || ^8 || ^9", "squizlabs/php_codesniffer": "^3.5", "mockery/mockery": "^1.3"}, "suggest": {"ext-gd": "Needed to process images", "ext-imagick": "Improves image processing performance", "ext-gmagick": "Improves image processing performance", "ext-zlib": "Needed for pdf stream compression"}}