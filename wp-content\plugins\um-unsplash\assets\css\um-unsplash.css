.um-unsplash-modal-overlay {

	background: rgba(0,0,0, 0.85);
	width: 100%;
	height: 100vh;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 999999;
	justify-content: center;
	display:none;
	overflow-y: scroll;

}

body.um_unsplash_overlay {

	max-height: 100vh;
	overflow: hidden;

}

body.um_unsplash_overlay .um-unsplash-modal-overlay {

	display: flex;

}


.um-unsplash-modal {

	-moz-border-radius: 3px;
	-webkit-border-radius: 3px;
	border-radius: 3px;
	z-index: 1999993;
	box-sizing: border-box;
	width: 80%;
	max-width: 730px;
	margin:auto auto;

}


.um-unsplash-modal-body {
	min-height: 3px;
	padding: 20px;
	box-sizing: border-box;
	width: 100%;
	background: #fff;
	-moz-border-radius: 0 0 3px 3px;
	-webkit-border-radius: 0 0 3px 3px;
	border-radius: 0 0 3px 3px;
}



.um-unsplash-modal.loading .um-unsplash-modal-body {

	background: #fff url(../img/loading.gif) no-repeat center;
	min-height: 150px;

}

.um-unsplash-modal div.um {

	margin-bottom: 20px !important;

}

.um-unsplash-modal #um-unsplash-photo-search-field {

	display:inline-block;
	width: auto;

}

.um-unsplash-modal #um-unsplash-photo-search-btn {

	color:#333;
}

.um-unsplash-modal-header {

	-moz-border-radius: 3px 3px 0 0;
	-webkit-border-radius: 3px 3px 0 0;
	border-radius: 3px 3px 0 0;
	height: 44px;
	line-height: 44px;
	color: #fff;
	padding: 0 20px;
	box-sizing: border-box;
	font-size: 17px;
	background-color: #3ba1da;

}

.um-unsplash-modal-footer {

	margin-top: 20px;

}

.um-unsplash-modal-footer #um_unsplash_view_image {

	display:none;

}

.um-unsplash-modal-left {
	float: left;
}

.um-unsplash-modal-right {
	float: right;
}


.unsplash_img_radio {
	display: none;
}

.unsplash_img_radio + label {
	border:2px solid #fff;
	display: inline-block;
	cursor: pointer;
	background-position: 50%;
    background-size: cover;
	width: 85px;
	height:65px;
	border-radius: 8px;
}

.unsplash_img_radio + label:hover {
	border-color:#000;
}


.unsplash_img_radio:checked + label {
	border-color:red;
}


img.croper {
	cursor:pointer;
}


.selected-image {}


@media  (max-height: 400px){

	.um-unsplash-modal {
		-moz-border-radius:0;
		-webkit-border-radius:0;
		border-radius:0;
		width: 100% !important;
		max-width: 100% !important;
		margin: 0 !important;
		left: 0 !important;
		height: 100vh;
	}

}



.um-unsplash-ajax-load-more-holder {
	text-align: center;
}



.um-unsplash-modal-body-ajax-content .selected-image{
	position: relative;
}

.selected-image .um-unsplash-crop-adjustment-buttons {
	position: absolute;
	right: 5px;
    top: 5px;
	width: 30px;
	height: 60px;
	display:block;
	z-index: 50;
}

.selected-image .um-unsplash-crop-adjustment-buttons .button {
	width: 30px;
	height: 30px;
	display: block;
	color: #fff;
	background: rgba(0,0,0,0.6);
	padding:0;
	line-height: 30px;
	text-align: center;
}

.selected-image .um-unsplash-crop-adjustment-buttons .button:focus,
.selected-image .um-unsplash-crop-adjustment-buttons .button:hover {
	text-decoration: none;
	background: rgba(255,255,255,0.6);
	color:#000;
	outline:none;
}


.selected-image .um-unsplash-crop-adjustment-buttons .button.disabled:focus,
.selected-image .um-unsplash-crop-adjustment-buttons .button.disabled:hover,
.selected-image .um-unsplash-crop-adjustment-buttons .button.disabled {
	opacity:0.5;
	color: #fff;
	background: rgba(0,0,0,0.6);
}


.um-unsplash-ajax-load-more-holder .um-unsplash-ajax-load-more {
	text-decoration: none;
}

.um-unsplash-attribution {
    position:absolute;
    right:0;
    top:0;
    font-size:0.6em;
    color:#fff;
    background: rgba(0,0,0,0.2);
    padding: 0 5px;
}

.um-unsplash-attribution a {
    color:#fff;
    font-weight: bold;
}

.um-unsplash-attribution a.download-url {
    margin-left: 5px;
    padding-left: 5px;
    border-left: 1px solid rgba(255,255,255,0.3);
}
