"use strict";var WPMailSMTPDashboardWidget=window.WPMailSMTPDashboardWidget||function(e){var s={$canvas:e("#wp-mail-smtp-dash-widget-chart"),$settingsBtn:e("#wp-mail-smtp-dash-widget-settings-button"),$dismissBtn:e(".wp-mail-smtp-dash-widget-dismiss-chart-upgrade"),$summaryReportEmailBlock:e(".wp-mail-smtp-dash-widget-summary-report-email-block"),$summaryReportEmailDismissBtn:e(".wp-mail-smtp-dash-widget-summary-report-email-dismiss"),$summaryReportEmailEnableInput:e("#wp-mail-smtp-dash-widget-summary-report-email-enable"),$emailAlertsDismissBtn:e("#wp-mail-smtp-dash-widget-dismiss-email-alert-block")},n={instance:null,settings:{type:"line",data:{labels:[],datasets:[{label:"",data:[],backgroundColor:"rgba(34, 113, 177, 0.15)",borderColor:"rgba(34, 113, 177, 1)",borderWidth:2,pointRadius:4,pointBorderWidth:1,pointBackgroundColor:"rgba(255, 255, 255, 1)"}]},options:{maintainAspectRatio:!1,scales:{xAxes:[{type:"time",time:{unit:"day",tooltipFormat:"MMM D"},distribution:"series",ticks:{beginAtZero:!0,source:"labels",padding:10,minRotation:25,maxRotation:25,callback:function(t,a,i){var e=Math.floor(i.length/7);return e<1||(i.length-a-1)%e==0?t:void 0}}}],yAxes:[{ticks:{beginAtZero:!0,maxTicksLimit:6,padding:20,callback:function(t){if(Math.floor(t)===t)return t}}}]},elements:{line:{tension:0}},animation:{duration:0},hover:{animationDuration:0},legend:{display:!1},tooltips:{displayColors:!1},responsiveAnimationDuration:0}},init:function(){var t;s.$canvas.length&&(t=s.$canvas[0].getContext("2d"),n.instance=new WPMailSMTPChart(t,n.settings),n.updateWithDummyData(),n.instance.update())},updateWithDummyData:function(){for(var t,a=moment().startOf("day"),i=[55,45,34,45,32,55,65],e=1;e<=7;e++)t=a.clone().subtract(e,"days"),n.settings.data.labels.push(t),n.settings.data.datasets[0].data.push({t:t,y:i[e-1]})}},a={chart:n,init:function(){e(a.ready)},ready:function(){s.$settingsBtn.on("click",function(t){e(this).toggleClass("open"),e(this).siblings(".wp-mail-smtp-dash-widget-settings-menu").fadeToggle(200)}),s.$dismissBtn.on("click",function(t){t.preventDefault(),a.saveWidgetMeta("hide_graph",1),e(this).closest(".wp-mail-smtp-dash-widget-chart-block-container").remove(),e("#wp-mail-smtp-dash-widget-upgrade-footer").show()}),s.$summaryReportEmailDismissBtn.on("click",function(t){t.preventDefault(),a.saveWidgetMeta("hide_summary_report_email_block",1),s.$summaryReportEmailBlock.slideUp()}),s.$summaryReportEmailEnableInput.on("change",function(t){t.preventDefault();var a=e(this),i=a.next("i");a.hide(),i.show();t={_wpnonce:wp_mail_smtp_dashboard_widget.nonce,action:"wp_mail_smtp_"+wp_mail_smtp_dashboard_widget.slug+"_enable_summary_report_email"};e.post(ajaxurl,t).done(function(){s.$summaryReportEmailBlock.find(".wp-mail-smtp-dash-widget-summary-report-email-block-setting").addClass("hidden"),s.$summaryReportEmailBlock.find(".wp-mail-smtp-dash-widget-summary-report-email-block-applied").removeClass("hidden")}).fail(function(){a.show(),i.hide()})}),s.$emailAlertsDismissBtn.on("click",function(t){t.preventDefault(),e("#wp-mail-smtp-dash-widget-email-alerts-education").remove(),a.saveWidgetMeta("hide_email_alerts_banner",1)}),n.init(),a.removeOverlay(s.$canvas)},saveWidgetMeta:function(t,a){a={_wpnonce:wp_mail_smtp_dashboard_widget.nonce,action:"wp_mail_smtp_"+wp_mail_smtp_dashboard_widget.slug+"_save_widget_meta",meta:t,value:a};e.post(ajaxurl,a)},removeOverlay:function(t){t.siblings(".wp-mail-smtp-dash-widget-overlay").remove()}};return a}((document,window,jQuery));WPMailSMTPDashboardWidget.init();