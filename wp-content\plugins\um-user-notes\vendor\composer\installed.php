<?php return array(
    'root' => array(
        'name' => 'ultimatemember/um-user-notes',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => '50633501940bf070704e77b14b9672c24c236d59',
        'type' => 'composer-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'dompdf/dompdf' => array(
            'pretty_version' => 'v2.0.3',
            'version' => '2.0.3.0',
            'reference' => 'e8d2d5e37e8b0b30f0732a011295ab80680d7e85',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dompdf/dompdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'masterminds/html5' => array(
            'pretty_version' => '2.8.1',
            'version' => '2.8.1.0',
            'reference' => 'f47dcf3c70c584de14f21143c55d9939631bc6cf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../masterminds/html5',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phenx/php-font-lib' => array(
            'pretty_version' => '0.5.4',
            'version' => '0.5.4.0',
            'reference' => 'dd448ad1ce34c63d09baccd05415e361300c35b4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phenx/php-font-lib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phenx/php-svg-lib' => array(
            'pretty_version' => '0.5.0',
            'version' => '0.5.0.0',
            'reference' => '76876c6cf3080bcb6f249d7d59705108166a6685',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phenx/php-svg-lib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sabberworm/php-css-parser' => array(
            'pretty_version' => '8.4.0',
            'version' => '8.4.0.0',
            'reference' => 'e41d2140031d533348b2192a83f02d8dd8a71d30',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sabberworm/php-css-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ultimatemember/um-user-notes' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '50633501940bf070704e77b14b9672c24c236d59',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
