.et-db.et-fb #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form div.wpforms-field-richtext .quicktags-toolbar,
.et-db.et-fb #et-boc .et-l .et_pb_module .wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar {
  height: 39px;
  display: block;
  background: #f6f7f7 url("../../../../images/richtext/tinymce-toolbar-full.png") no-repeat left center;
  background-size: auto 34px;
}

.et-db.et-fb #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-toolbar-basic .quicktags-toolbar,
.et-db.et-fb #et-boc .et-l .et_pb_module .wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-toolbar-basic .quicktags-toolbar {
  background-image: url("../../../../images/richtext/tinymce-toolbar-basic.png");
}

.et-db.et-fb #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled.wpforms-field-richtext-toolbar-basic .quicktags-toolbar,
.et-db.et-fb #et-boc .et-l .et_pb_module .wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled.wpforms-field-richtext-toolbar-basic .quicktags-toolbar {
  background-image: url("../../../../images/richtext/tinymce-toolbar-basic-mb.png");
}

.et-db.et-fb #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled .quicktags-toolbar,
.et-db.et-fb #et-boc .et-l .et_pb_module .wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled .quicktags-toolbar {
  background-image: url("../../../../images/richtext/tinymce-toolbar-full-mb.png");
}

.et-db.et-fb #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button,
.et-db.et-fb #et-boc .et-l .et_pb_module .wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button {
  pointer-events: none;
  min-height: auto;
}

.et-db.et-fb #et-boc .et-l .et_pb_module .wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button {
  min-height: 29px;
}

.et-db.et-fb #et-boc .et-l .et_pb_module .wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar,
.et-db.et-fb #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form div.wpforms-field-richtext .quicktags-toolbar {
  position: relative;
  background-color: whitesmoke;
  border: 1px solid #cccccc;
}

.et-db.et-fb #et-boc .et-l .et_pb_module .wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar:before,
.et-db.et-fb #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form div.wpforms-field-richtext .quicktags-toolbar:before {
  cursor: not-allowed;
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.5);
}

.et-db.et-fb #et-boc .et-l .et_pb_module .wpforms-container .wpforms-form div.wpforms-field-richtext .html-active button.switch-html:before,
.et-db.et-fb #et-boc .et-l .et_pb_module .wpforms-container .wpforms-form div.wpforms-field-richtext .tmce-active button.switch-tmce:before,
.et-db.et-fb #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form div.wpforms-field-richtext .html-active button.switch-html:before,
.et-db.et-fb #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form div.wpforms-field-richtext .tmce-active button.switch-tmce:before {
  bottom: -1px;
}

.et-db.et-fb #et-boc .et-l .et_pb_module .wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button,
.et-db.et-fb #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button {
  cursor: not-allowed;
  pointer-events: none;
  position: relative;
  box-sizing: initial;
}

.et-db.et-fb #et-boc .et-l .et_pb_module .wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button:before,
.et-db.et-fb #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button:before {
  cursor: not-allowed;
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.5);
}

.et-db.et-fb #et-boc .et-l .et_pb_module .wpforms-container .wpforms-form div.wpforms-field-richtext textarea,
.et-db.et-fb #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form div.wpforms-field-richtext textarea {
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.et-db.et-fb .media-modal .media-frame-menu,
.et-db.et-fb .media-modal .media-frame-menu-heading {
  display: block;
}

.et-db.et-fb .media-modal .media-modal-content .media-frame-title,
.et-db.et-fb .media-modal .media-modal-content .media-frame-router,
.et-db.et-fb .media-modal .media-modal-content .media-frame-content {
  left: 200px;
}
