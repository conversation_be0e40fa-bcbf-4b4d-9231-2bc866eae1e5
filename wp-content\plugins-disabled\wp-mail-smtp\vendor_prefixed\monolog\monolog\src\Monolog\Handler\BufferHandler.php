<?php

declare (strict_types=1);
/*
 * This file is part of the Monolog package.
 *
 * (c) <PERSON><PERSON> <j.bog<PERSON><PERSON>@seld.be>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace WPMailSMTP\Vendor\Monolog\Handler;

use WPMailSMTP\Vendor\Monolog\Logger;
use WPMailSMTP\Vendor\Monolog\ResettableInterface;
use WPMailSMTP\Vendor\Monolog\Formatter\FormatterInterface;
/**
 * Buffers all records until closing the handler and then pass them as batch.
 *
 * This is useful for a MailHandler to send only one mail per request instead of
 * sending one per log message.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @phpstan-import-type Record from \Monolog\Logger
 */
class BufferHandler extends \WPMailSMTP\Vendor\Monolog\Handler\AbstractHandler implements \WPMailSMTP\Vendor\Monolog\Handler\ProcessableHandlerInterface, \WPMailSMTP\Vendor\Monolog\Handler\FormattableHandlerInterface
{
    use ProcessableHandlerTrait;
    /** @var HandlerInterface */
    protected $handler;
    /** @var int */
    protected $bufferSize = 0;
    /** @var int */
    protected $bufferLimit;
    /** @var bool */
    protected $flushOnOverflow;
    /** @var Record[] */
    protected $buffer = [];
    /** @var bool */
    protected $initialized = \false;
    /**
     * @param HandlerInterface $handler         Handler.
     * @param int              $bufferLimit     How many entries should be buffered at most, beyond that the oldest items are removed from the buffer.
     * @param bool             $flushOnOverflow If true, the buffer is flushed when the max size has been reached, by default oldest entries are discarded
     */
    public function __construct(\WPMailSMTP\Vendor\Monolog\Handler\HandlerInterface $handler, int $bufferLimit = 0, $level = \WPMailSMTP\Vendor\Monolog\Logger::DEBUG, bool $bubble = \true, bool $flushOnOverflow = \false)
    {
        parent::__construct($level, $bubble);
        $this->handler = $handler;
        $this->bufferLimit = $bufferLimit;
        $this->flushOnOverflow = $flushOnOverflow;
    }
    /**
     * {@inheritDoc}
     */
    public function handle(array $record) : bool
    {
        if ($record['level'] < $this->level) {
            return \false;
        }
        if (!$this->initialized) {
            // __destructor() doesn't get called on Fatal errors
            \register_shutdown_function([$this, 'close']);
            $this->initialized = \true;
        }
        if ($this->bufferLimit > 0 && $this->bufferSize === $this->bufferLimit) {
            if ($this->flushOnOverflow) {
                $this->flush();
            } else {
                \array_shift($this->buffer);
                $this->bufferSize--;
            }
        }
        if ($this->processors) {
            /** @var Record $record */
            $record = $this->processRecord($record);
        }
        $this->buffer[] = $record;
        $this->bufferSize++;
        return \false === $this->bubble;
    }
    public function flush() : void
    {
        if ($this->bufferSize === 0) {
            return;
        }
        $this->handler->handleBatch($this->buffer);
        $this->clear();
    }
    public function __destruct()
    {
        // suppress the parent behavior since we already have register_shutdown_function()
        // to call close(), and the reference contained there will prevent this from being
        // GC'd until the end of the request
    }
    /**
     * {@inheritDoc}
     */
    public function close() : void
    {
        $this->flush();
        $this->handler->close();
    }
    /**
     * Clears the buffer without flushing any messages down to the wrapped handler.
     */
    public function clear() : void
    {
        $this->bufferSize = 0;
        $this->buffer = [];
    }
    public function reset()
    {
        $this->flush();
        parent::reset();
        $this->resetProcessors();
        if ($this->handler instanceof \WPMailSMTP\Vendor\Monolog\ResettableInterface) {
            $this->handler->reset();
        }
    }
    /**
     * {@inheritDoc}
     */
    public function setFormatter(\WPMailSMTP\Vendor\Monolog\Formatter\FormatterInterface $formatter) : \WPMailSMTP\Vendor\Monolog\Handler\HandlerInterface
    {
        if ($this->handler instanceof \WPMailSMTP\Vendor\Monolog\Handler\FormattableHandlerInterface) {
            $this->handler->setFormatter($formatter);
            return $this;
        }
        throw new \UnexpectedValueException('The nested handler of type ' . \get_class($this->handler) . ' does not support formatters.');
    }
    /**
     * {@inheritDoc}
     */
    public function getFormatter() : \WPMailSMTP\Vendor\Monolog\Formatter\FormatterInterface
    {
        if ($this->handler instanceof \WPMailSMTP\Vendor\Monolog\Handler\FormattableHandlerInterface) {
            return $this->handler->getFormatter();
        }
        throw new \UnexpectedValueException('The nested handler of type ' . \get_class($this->handler) . ' does not support formatters.');
    }
}
