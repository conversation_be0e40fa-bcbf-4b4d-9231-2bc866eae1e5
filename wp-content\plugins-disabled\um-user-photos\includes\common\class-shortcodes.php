<?php
namespace um_ext\um_user_photos\common;

use WP_Query;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Shortcodes
 *
 * @package um_ext\um_user_photos\common
 */
class Shortcodes {

	/**
	 * @var bool
	 */
	public $is_profile = false;

	/**
	 * Shortcodes constructor.
	 */
	public function __construct() {
		add_shortcode( 'ultimatemember_gallery', array( $this, 'get_gallery_content' ) );
		add_shortcode( 'ultimatemember_gallery_photos', array( $this, 'gallery_photos_content' ) );
		add_shortcode( 'ultimatemember_albums', array( $this, 'get_albums_content' ) );
	}

	/**
	 * Display common "Albums" block
	 *
	 * @param array $atts
	 *
	 * @return string
	 */
	public function get_albums_content( $atts = array() ) {
		$atts_def = array(
			'column'   => 2,
			'page'     => 1,
			'per_page' => UM()->User_Photos()->common()->gallery()->albums_per_page,
		);

		/**
		 * There is possible to use 'shortcode_atts_ultimatemember_albums' filter for getting customized $atts.
		 * @link https://developer.wordpress.org/reference/hooks/shortcode_atts_shortcode/
		 */
		$args = shortcode_atts( $atts_def, $atts, 'ultimatemember_albums' );

		$albums = UM()->User_Photos()->common()->query()->get_albums( $args );
		if ( empty( $albums ) || ! $albums->have_posts() ) {
			return '';
		}

		$sizes         = UM()->options()->get( 'photo_thumb_sizes' );
		$args['size']  = isset( $sizes[2] ) ? absint( $sizes[2] ) : 190;
		$args['pages'] = (int) ceil( $albums->found_posts / $args['per_page'] );

		$args_t = compact( 'albums', 'args' );
		$output = UM()->get_template( 'v3/albums.php', UM_USER_PHOTOS_PLUGIN, $args_t );

		wp_enqueue_script( 'um-user-photos' );
		wp_enqueue_style( 'um-user-photos' );

		return $output;
	}

	/**
	 * Display the user "Albums" by user ID
	 *
	 * @param array $atts
	 *
	 * @return string
	 */
	public function get_gallery_content( $atts = array() ) {
		/** There is possible to use 'shortcode_atts_ultimatemember_gallery' filter for getting customized $atts. This filter is documented in wp-includes/shortcodes.php "shortcode_atts_{$shortcode}" */
		$atts = shortcode_atts(
			array(
				'user_id'  => um_profile_id(),
				'per_page' => UM()->User_Photos()->common()->gallery()->albums_per_page,
			),
			$atts,
			'ultimatemember_gallery'
		);

		$user_id           = absint( $atts['user_id'] );
		$user_role         = UM()->roles()->get_priority_user_role( $user_id );
		$user_role_data    = UM()->roles()->role_data( $user_role );
		$album_limit_error = false;

		if ( empty( $user_role_data['enable_user_photos'] ) ) {
			return '';
		}

		$is_my_profile = is_user_logged_in() && get_current_user_id() === $user_id;
		if ( ! $is_my_profile && ! um_can_view_profile( $user_id ) ) {
			return '';
		}

		if ( ! UM()->is_ajax() ) {
			wp_enqueue_script( 'um-user-photos' );
			wp_enqueue_style( 'um-user-photos' );
		}

		// phpcs:disable WordPress.Security.NonceVerification
		if ( ! empty( $_GET['gallery_action'] ) && 'add_album' === sanitize_key( $_GET['gallery_action'] ) ) {
			$count_user_photos = UM()->User_Photos()->common()->user()->photos_count( $user_id );
			$limit_user_photos = UM()->User_Photos()->common()->user()->photos_limit( $user_id );
			$limit_per_albums  = UM()->options()->get( 'um_user_photos_album_limit' );
			$limit_per_albums  = ! empty( $limit_per_albums ) ? $limit_per_albums : '';
			$is_profile_tab    = $this->is_profile;

			$wrapper_classes = array( 'um-user-photos-add-album' );
			if ( ! $is_profile_tab ) {
				$wrapper_classes[] = 'um';
			}

			$disable_title = UM()->options()->get( 'um_user_photos_disable_title' );
			$disable_cover = UM()->options()->get( 'um_user_photos_disable_cover' );

			$enable_upload = true;
			if ( false !== $limit_user_photos && absint( $count_user_photos ) >= absint( $limit_user_photos ) ) {
				$enable_upload = false;
			}

			$args_t = compact( 'count_user_photos', 'limit_user_photos', 'limit_per_albums', 'wrapper_classes', 'disable_title', 'disable_cover', 'enable_upload', 'album_limit_error' );
			return UM()->get_template( 'v3/album-add.php', UM_USER_PHOTOS_PLUGIN, $args_t );
		}

		if ( ! empty( $_GET['album_id'] ) ) {
			// Then load the necessary album static by the link.
			$album_id = absint( $_GET['album_id'] );
			if ( empty( $album_id ) ) {
				return '';
			}

			if ( ! UM()->User_Photos()->common()->user()->can_view_album( $album_id ) ) {
				return '';
			}

			$album = get_post( $album_id );

			if ( ! empty( $_GET['gallery_action'] ) && 'edit_album' === sanitize_key( $_GET['gallery_action'] ) ) {
				if ( ! UM()->User_Photos()->common()->user()->can_edit_album( $album_id ) ) {
					return '';
				}

				$privacy        = get_post_meta( $album_id, '_privacy', true );
				$is_profile_tab = $this->is_profile;

				$wrapper_classes = array( 'um-user-photos-edit-album-form' );
				if ( ! $is_profile_tab ) {
					$wrapper_classes[] = 'um';
				}

				$photos                 = get_post_meta( $album->ID, '_photos', true );
				$disable_title          = UM()->options()->get( 'um_user_photos_disable_title' );
				$disable_cover          = UM()->options()->get( 'um_user_photos_disable_cover' );
				$disable_comment_option = UM()->options()->get( 'um_user_photos_disable_comments' );

				$user_id           = get_current_user_id();
				$count_user_photos = UM()->User_Photos()->common()->user()->photos_count( $user_id );
				$limit_user_photos = UM()->User_Photos()->common()->user()->photos_limit( $user_id );

				$limit_per_albums   = UM()->options()->get( 'um_user_photos_album_limit' );
				$limit_per_albums   = ! empty( $limit_per_albums ) ? $limit_per_albums : '';
				$album_photos_array = get_post_meta( $album->ID, '_photos', true );
				$album_photos_array = ! empty( $album_photos_array ) && is_array( $album_photos_array ) ? $album_photos_array : array();
				$count_per_album    = count( $album_photos_array );

				$enable_upload = true;
				if ( false !== $limit_user_photos && absint( $count_user_photos ) >= absint( $limit_user_photos ) ) {
					$enable_upload = false;
				}

				if ( ! empty( $limit_per_albums ) && absint( $count_per_album ) >= absint( $limit_per_albums ) ) {
					$enable_upload     = false;
					$album_limit_error = true;
				}

				$uploaded_photos = array();
				if ( ! empty( $photos ) && is_array( $photos ) ) {
					foreach ( $photos as $photo_id ) {
						$image = wp_get_attachment_image_src( $photo_id );
						if ( ! $image ) {
							continue;
						}

						$image_data     = get_post( $photo_id );
						$image_guid     = $image_data->guid;
						$last_slash_pos = strrpos( $image_guid, '/' );
						$image_filename = substr( $image_guid, $last_slash_pos + 1 );

						$disable_comment = get_post_meta( $image_data->ID, '_disable_comment', true );
						$cover_photo     = get_post_meta( $album->ID, '_thumbnail_id', true );

						$args = array(
							'photo_id'     => $photo_id,
							'preview_url'  => $image[0],
							'title'        => $image_data->post_title,
							'filename'     => $image_filename,
							'caption'      => $image_data->post_excerpt,
							'related_link' => $image_data->_link,
						);
						if ( ! $disable_comment_option ) {
							$args['disable_comment'] = ! empty( $disable_comment );
						}
						if ( ! $disable_cover ) {
							$args['cover_photo'] = absint( $cover_photo ) === absint( $image_data->ID );
						}
						$uploaded_photos[] = $args;
					}
				}

				$args_t = compact( 'count_user_photos', 'limit_user_photos', 'limit_per_albums', 'count_per_album', 'album', 'privacy', 'disable_title', 'wrapper_classes', 'enable_upload', 'uploaded_photos', 'album_limit_error' );
				return UM()->get_template( 'v3/album-edit.php', UM_USER_PHOTOS_PLUGIN, $args_t );
			}

			$photos = get_post_meta( $album_id, '_photos', true );
			if ( empty( $photos ) ) {
				$photos = array();
			}
			$count_photos = count( $photos );
			$per_page     = UM()->User_Photos()->common()->gallery()->photos_per_page;
			$photos       = array_slice( $photos, 0, $per_page );

			$is_my_profile = is_user_logged_in() && get_current_user_id() === absint( $album->post_author );
			$count         = count( $photos );
			$disable_title = UM()->options()->get( 'um_user_photos_disable_title' );
			$columns       = UM()->options()->get( 'um_user_photos_images_column' );
			if ( empty( $columns ) ) {
				$columns = 3;
			}

			$is_profile_tab = $this->is_profile;

			$args_t = compact( 'album', 'album_id', 'columns', 'count', 'is_my_profile', 'photos', 'is_profile_tab', 'count_photos', 'disable_title', 'album_limit_error' );
			return UM()->get_template( 'v3/single-album.php', UM_USER_PHOTOS_PLUGIN, $args_t );
		}

		$per_page = $atts['per_page'];

		$args = array(
			'post_type'      => 'um_user_photos',
			'author__in'     => array( $user_id ),
			'post_status'    => 'publish',
			'paged'          => 1,
			'posts_per_page' => $per_page,
		);

		$args = apply_filters( 'um_user_photo_query_args', $args, $user_id );

		$albums_query = new WP_Query( $args );

		$albums = $albums_query->posts;
		$total  = $albums_query->found_posts;

		$last_id = 0;
		if ( $albums_query->posts ) {
			$last_post = end( $albums_query->posts );
			$last_id   = $last_post->ID;
		}

		$columns = UM()->options()->get( 'um_user_photos_albums_column' );
		if ( empty( $columns ) ) {
			$columns = 2;
		}

		$default       = UM_USER_PHOTOS_URL . 'assets/images/dummy_album_cover.png';
		$disable_title = UM()->options()->get( 'um_user_photos_disable_title' );

		$is_profile_tab = $this->is_profile;

		$args_t = compact( 'albums', 'total', 'columns', 'default', 'disable_title', 'is_my_profile', 'user_id', 'is_profile_tab', 'per_page', 'last_id' );
		// phpcs:enable WordPress.Security.NonceVerification

		return UM()->get_template( 'v3/gallery.php', UM_USER_PHOTOS_PLUGIN, $args_t );
	}

	/**
	 * Display the user all "Photos" through the albums by user ID
	 *
	 * @param array $atts
	 * @return string
	 */
	public function gallery_photos_content( $atts = array() ) {
		/** There is possible to use 'shortcode_atts_ultimatemember_gallery_photos' filter for getting customized $atts. This filter is documented in wp-includes/shortcodes.php "shortcode_atts_{$shortcode}" */
		$atts = shortcode_atts(
			array(
				'user_id' => um_profile_id(),
			),
			$atts,
			'ultimatemember_gallery_photos'
		);

		// phpcs:ignore WordPress.Security.NonceVerification
		$user_id        = absint( $atts['user_id'] );
		$user_role      = UM()->roles()->get_priority_user_role( $user_id );
		$user_role_data = UM()->roles()->role_data( $user_role );

		if ( empty( $user_role_data['enable_user_photos'] ) ) {
			return '';
		}

		$is_my_profile = is_user_logged_in() && get_current_user_id() === $user_id;
		if ( ! $is_my_profile && ! um_can_view_profile( $user_id ) ) {
			return '';
		}

		$columns = UM()->options()->get( 'um_user_photos_images_column' );
		if ( empty( $columns ) ) {
			$columns = 3;
		}

		$per_page = UM()->User_Photos()->common()->gallery()->photos_per_page;

		$query_args = array(
			'post_type'      => 'attachment',
			'author__in'     => array( $user_id ),
			'post_status'    => 'inherit',
			'post_mime_type' => 'image',
			'posts_per_page' => $per_page,
			'meta_query'     => array(
				array(
					'key'     => '_part_of_gallery',
					'value'   => 'yes',
					'compare' => '=',
				),
			),
			'orderby'        => 'ID',
		);

		$args = array(
			'post_type'      => 'um_user_photos',
			'author__in'     => array( $user_id ),
			'posts_per_page' => -1,
			'post_status'    => 'publish',
			'fields'         => 'ids',
		);
		$args = apply_filters( 'um_user_photo_query_args', $args, $user_id );

		$albums = new WP_Query( $args );

		$visible_photos = array();
		if ( ! $is_my_profile ) {
			if ( ! empty( $albums->posts ) ) {
				foreach ( $albums->posts as $album_id ) {
					$photos_query_args  = array(
						'post_type'      => 'attachment',
						'author__in'     => array( $user_id ),
						'post_status'    => 'inherit',
						'post_mime_type' => 'image',
						'posts_per_page' => -1,
						'meta_query'     => array(
							array(
								'key'     => '_part_of_gallery',
								'value'   => 'yes',
								'compare' => '=',
							),
						),
						'orderby'        => 'ID',
						'post_parent'    => $album_id,
						'fields'         => 'ids',
					);
					$album_photos_query = new WP_Query( $photos_query_args );
					$photos_result      = $album_photos_query->get_posts();

					if ( ! empty( $photos_result ) ) {
						$visible_photos[] = $photos_result;
					}
				}

				$visible_photos = array_merge( ...$visible_photos );
				$visible_photos = array_unique( $visible_photos );
			}

			if ( ! empty( $visible_photos ) ) {
				$query_args['post__in'] = $visible_photos;
			}
		}

		$total  = 0;
		$photos = array();
		if ( $is_my_profile || ! empty( $visible_photos ) ) {
			// Disable posts query filter by the taxonomy 'language'. Integration with the plugin 'Polylang'.
			add_action( 'pre_get_posts', array( UM()->User_Photos()->common()->query(), 'remove_language_filter' ), 9 );

			$latest_photos = new WP_Query( $query_args );

			// Return back language filter
			remove_action( 'pre_get_posts', array( UM()->User_Photos()->common()->query(), 'remove_language_filter' ), 9 );

			wp_enqueue_script( 'um-user-photos' );
			wp_enqueue_style( 'um-user-photos' );

			$total = $latest_photos->found_posts;

			foreach ( $latest_photos->posts as $photo ) {
				$photos[] = $photo->ID;
			}
		}

		$is_profile_tab = $this->is_profile;

		$args_t = compact( 'columns', 'total', 'is_my_profile', 'per_page', 'photos', 'user_id', 'is_profile_tab' );
		return UM()->get_template( 'v3/gallery-photos.php', UM_USER_PHOTOS_PLUGIN, $args_t );
	}
}
