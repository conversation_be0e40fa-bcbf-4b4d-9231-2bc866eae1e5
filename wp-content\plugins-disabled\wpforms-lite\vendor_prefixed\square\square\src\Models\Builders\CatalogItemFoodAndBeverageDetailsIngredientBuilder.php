<?php

declare (strict_types=1);
namespace WPForms\Vendor\Square\Models\Builders;

use WPForms\Vendor\Core\Utils\CoreHelper;
use WPForms\Vendor\Square\Models\CatalogItemFoodAndBeverageDetailsIngredient;
/**
 * Builder for model CatalogItemFoodAndBeverageDetailsIngredient
 *
 * @see CatalogItemFoodAndBeverageDetailsIngredient
 */
class CatalogItemFoodAndBeverageDetailsIngredientBuilder
{
    /**
     * @var CatalogItemFoodAndBeverageDetailsIngredient
     */
    private $instance;
    private function __construct(CatalogItemFoodAndBeverageDetailsIngredient $instance)
    {
        $this->instance = $instance;
    }
    /**
     * Initializes a new Catalog Item Food And Beverage Details Ingredient Builder object.
     */
    public static function init() : self
    {
        return new self(new CatalogItemFoodAndBeverageDetailsIngredient());
    }
    /**
     * Sets type field.
     *
     * @param string|null $value
     */
    public function type(?string $value) : self
    {
        $this->instance->setType($value);
        return $this;
    }
    /**
     * Sets standard name field.
     *
     * @param string|null $value
     */
    public function standardName(?string $value) : self
    {
        $this->instance->setStandardName($value);
        return $this;
    }
    /**
     * Sets custom name field.
     *
     * @param string|null $value
     */
    public function customName(?string $value) : self
    {
        $this->instance->setCustomName($value);
        return $this;
    }
    /**
     * Unsets custom name field.
     */
    public function unsetCustomName() : self
    {
        $this->instance->unsetCustomName();
        return $this;
    }
    /**
     * Initializes a new Catalog Item Food And Beverage Details Ingredient object.
     */
    public function build() : CatalogItemFoodAndBeverageDetailsIngredient
    {
        return CoreHelper::clone($this->instance);
    }
}
