jQuery(document).ready( function($) {
	$(document.body).on('click', '.um-user-photos-new-album', function (e) {
		e.preventDefault();
		var target = $('body').find('[data-scope="um-user-photos-modal"]');
		var modal_title = $(this).data('modal_title');
		var nonce = $(this).data('nonce');
		var modal_content = '<div class="text-center"><div class="um-user-photos-ajax-loading"></div></div>';
		var modal_content_div = target.find('.um-user-photos-modal-content');
		modal_content_div.html(modal_content);

		target.show();

		wp.ajax.send(
			'um_user_photos_new_album_modal',
			{
				data: {
					image_id: $(this).data('photo-id'),
					_wpnonce: nonce
				},
				success: function( response ) {
					modal_content_div.html( response );
					target.find('.um-user-photos-modal-title').text(modal_title);
				},
				error: function( data ) {
					console.log( data );
				}
			}
		);
	});

	$(document.body).on('click', '.um-user-photos-edit-album', function (e) {
		e.preventDefault();
		let target = $('body').find('[data-scope="um-user-photos-modal"]');
		let modal_title = $(this).data('modal_title');
		let album = $(this).data('id');
		let nonce = $(this).data('nonce');
		let modal_content = '<div class="text-center"><div class="um-user-photos-ajax-loading"></div></div>';
		let modal_content_div = target.find('.um-user-photos-modal-content');
		modal_content_div.html(modal_content);

		target.show();

		wp.ajax.send(
			'um_user_photos_edit_album_modal',
			{
				data: {
					album_id: album,
					_wpnonce: nonce
				},
				success: function( response ) {
					modal_content_div.html( response );
					target.find('.um-user-photos-modal-title').text(modal_title);
					$( "#um-user-photos-sortable" ).sortable();
				},
				error: function( data ) {
					console.log( data );
				}
			}
		);
	});

	$(document.body).on('click', '.um-user-photos-delete-album', function (e) {
		e.preventDefault();

		let deleteLink = $(this);

		$.um_confirm({
			title   : deleteLink.attr('title'),
			message : deleteLink.data('confirm'),
			onYes: function() {
				//var button = $(this);
				//var form = button.parents('form');
				let albumID = deleteLink.data('id');
				let nonce = deleteLink.data('nonce');

				//button.html('<i class="um-user-photos-ajax-loading"></i>').attr('disabled', true);

				// var response_div = form.find('.um-galley-form-response');
				// response_div.removeClass('success').removeClass('error');
				// response_div.html('');

				wp.ajax.send(
					'um_user_photos_delete_album',
					{
						data: {
							id: albumID,
							_wpnonce: nonce
						},
						success: function() {
							let newUrl = new URL( window.location.href );
							newUrl.searchParams.delete( 'album_id' );
							window.location.assign( newUrl );
						},
						error: function( data ) {
							console.log( data );
							//button.attr( 'disabled', false );
							//response_div.html( '<span>Error:' + data + '</span>' ).addClass( 'error' );
						}
					}
				);
			},
			object:this
		});
	});

	$(document.body).on('click', '.um-user-photos-edit-image', function (e) {
		e.preventDefault();
		var target = $('body').find('[data-scope="um-user-photos-modal"]');
		var modal_title = $(this).data('modal_title');
		var nonce = $(this).data('nonce');
		var modal_content = '<div class="text-center"><div class="um-user-photos-ajax-loading"></div></div>';
		var modal_content_div = target.find('.um-user-photos-modal-content');
		modal_content_div.html(modal_content);

		target.show();

		wp.ajax.send(
			'um_user_photos_edit_photo_modal',
			{
				data: {
					image_id: $(this).data('photo-id'),
					_wpnonce: nonce
				},
				success: function( response ) {
					modal_content_div.html( response );
					target.find('.um-user-photos-modal-title').text(modal_title);
				},
				error: function( data ) {
					console.log( data );
				}
			}
		);
	});

	// back to gallery
	$(document.body).on('click', '.um-user-photos-back-to-gallery', function (e) {
		e.preventDefault();

		let box = $(this).parents('.um-user-photos-albums');
		let userID = $(this).data('profile');
		let nonce = $(this).data('nonce');

		box.html('<div class="text-center"><div class="um-user-photos-ajax-loading"></div></div>');
		wp.ajax.send(
			'um_user_photos_get_gallery',
			{
				data: {
					user_id: userID,
					_wpnonce: nonce
				},
				success: function( response ) {
					box.html( response );

					let newUrl = new URL( window.location.href );
					newUrl.searchParams.delete( 'album_id' );
					history.pushState( null, '', newUrl );
				},
				error: function( data ) {
					console.log( data );
				}
			}
		);
	});
});

(function ($) {
	$(document.body).on('click', '.um-user-photos-modal-close-link', function (e) {
		e.preventDefault();
		um_photos_close_modal();
	});

	$(document.body).on( 'click', '.um-user-photos-modal', function ( e ) {
		if ( ! $( e.target ).closest( '.um-user-photos-modal-body' ).length ) {
			um_photos_close_modal();
		}
	});

	function um_photos_close_modal() {
		var modal = $( '.um-user-photos-modal' );
		modal.hide();
		modal.find( '.um-user-photos-modal-title' ).text( '' );
		modal.find( '.um-user-photos-modal-content' ).html( '' );
	}

	// add/edit album
	$(document).on('click', '.um-galley-modal-update', function (e) {
		e.preventDefault();
		var button = $(this);
		var btn_init = button.text();
		var form = button.parents('form');
		var limit = form.attr('data-limit');
		var count = form.attr('data-count');
		var album_limit = $('.um-user-photos-modal-form').attr('data-album_limit');
		var album_count = $('.um-user-photos-modal-form').attr('data-count_album');
		var max_upload = $('.um-user-photos-modal-form').attr('data-max_upload');
		var max_size = $('.um-user-photos-modal-form').attr('data-max_size');
		var imgs_size = $('#um-user-photos-input-album-images').attr('data-imgs_size');
		var imgs = $('#um-user-photos-images-uploaded img').length;
		var cover_size = 0;

		if ( $('#um-user-photos-input-album-cover.uploaded').length > 0 ) {
			cover_size = $('#um-user-photos-input-album-cover.uploaded').attr('data-size');
		}

		if ( parseInt( imgs ) > parseInt( max_upload ) || ( $('#um-user-photos-input-album-cover.uploaded').length > 0 && parseInt( imgs ) > ( parseInt( max_upload ) - 1 ) ) ) {
			$('.um-user-photos-max-upload-error').show();
		} else if ( parseInt( cover_size ) + parseInt( imgs_size ) > parseInt( max_size ) ) {
			$('.um-user-photos-max-size-upload-error').show();
		} else if ( '' !== limit && parseInt( imgs ) + parseInt( count ) > parseInt( limit ) ) {
			$('.um-user-photos-error').show();
		} else if ( '' !== album_limit && parseInt( imgs ) + parseInt( album_count ) > parseInt( album_limit ) ) {
			$('.um-user-photos-error').show();
		} else {
			$('.um-user-photos-error').hide();

			var action = form.attr('action');
			var real_form = form[0];
			var formData = new FormData(real_form);
			button.html('<i class="um-user-photos-ajax-loading"></i>');
			button.attr('disabled', true);

			var response_div = form.find('.um-galley-form-response');
			response_div.removeClass('success').removeClass('error');
			response_div.html('');

			$.ajax({
				type: 'POST',
				url: action,
				data: formData,
				cache: false,
				contentType: false,
				processData: false,
				success: function (data) {
					if ( data.success ) {
						location.reload();
					} else {
						button.html(btn_init);
						button.removeAttr('disabled');
						try {
							response_div.addClass('error');
							let html = '<ul><li>' + data.data + '</li></ul>';
							response_div.html(html);
						} catch (e) {
							if (data.data) {
								response_div.html('<span>Error:' + data.data + '</span>').addClass('error');
							}
						}
					}
				},
				error: function (data) {
					button.html(btn_init);
					button.attr('disabled', false);
					console.log("error");
					console.log(data);
				}
			});
		}
	});

	window.album_load = false;
	$(window).on('load', function (){
		var params = new URLSearchParams( window.location.search );

		if ( params.has('album_id') && ! params.has('photo_id') ) {
			window.album_load = true;
			var album_id = params.get('album_id');

			var album = $('.um-user-photos-album-block[data-id=' + album_id + ']');
			um_get_album( album, album_id );
		}
	});

	$(window).on('popstate', function(event) {
		var params = new URLSearchParams( window.location.search );

		if ( ! params.has('album_id') && $('.um-user-photos-single-album').length > 0 ) {
			$('.um-user-photos-back-to-gallery').trigger('click');
		} else if ( params.has('album_id') && $('.um-user-photos-single-album').length === 0  ) {
			$('.um-user-photos-album-block[data-id=' + params.get('album_id') + ']').trigger('click');
		}
	});

	/* album ajax view */
	$(document.body).on('click', '.um-user-photos-album-block', function (e) {
		e.preventDefault();
		var album_id = $(this).data('id');
		um_get_album( $(this), album_id );

		var params = new URLSearchParams( window.location.search );
		if ( ! params.has('album_id') ) {
			var newUrl = new URL( window.location.href );
			newUrl.searchParams.set( 'album_id', album_id );
			history.pushState( {albumId: album_id}, '', newUrl );
		}
	});

	function um_get_album( album, album_id ) {
		let box = album.parents('.um-user-photos-albums');
		box.html('<div class="text-center"><div class="um-user-photos-ajax-loading"></div></div>');

		let nonce = album.data('nonce');

		wp.ajax.send(
			'um_user_photos_get_single_album_view',
			{
				data: {
					id: album_id,
					_wpnonce: nonce
				},
				success: function( response ) {
					box.html( response );
				},
				error: function( data ) {
					console.log( data );
				}
			}
		);

		box.siblings('.um-user-photos-add').remove();
		window.album_load = false;
	}

	// edit image
	$(document.body).on('click', '#um-user-photos-image-update-btn', function (e) {
		e.preventDefault();

		var button = $(this);
		var btn_init = button.text();
		var formObj = button.parents('form');
		button.html('<i class="um-user-photos-ajax-loading"></i>');
		button.attr('disabled', true);

		var response_div = formObj.find('.um-galley-form-response');
		response_div.removeClass('success').removeClass('error');
		response_div.html('');

		let formObjData = UM.common.form.vanillaSerialize( 'um_user_photos_edit_image' );

		wp.ajax.send(
			'um_user_photos_image_update',
			{
				data: formObjData,
				success: function( response ) {
					button.html( btn_init );
					button.attr( 'disabled', false );
					response_div.addClass( 'success' );
					let html = '<ul><li>' + response + '</li></ul>';
					response_div.html( html );

					setTimeout( function () {
						response_div.parents( '.um-user-photos-modal' ).hide();
					}, 1000 );
					// close modal after success response
				},
				error: function( response ) {
					button.html( btn_init );
					button.attr( 'disabled', false );
					response_div.addClass( 'error' );
					let html = '<ul><li>' + response + '</li></ul>';
					response_div.html( html );

					setTimeout( function () {
						response_div.parents( '.um-user-photos-modal' ).hide();
					}, 2000 );
					// close modal after success response
				}
			}
		);
	});

	$( document ).on( 'click', '.um-user-photos-delete-photo-album', function ( e ) {
		e.preventDefault();
		e.stopPropagation();

		$( this ).hide();

		let $editAlbumForm = $( '#um-user-photos-form-edit-album' );

		var confirmation_text = $( this ).data( 'confirmation' );
		var target            = $( this ).data( 'delete_photo' );
		var id                = $( this ).data( 'id' );
		var nonce             = $( this ).data( 'wpnonce' );
		var limit             = $editAlbumForm.attr( 'data-limit' );
		var count             = $editAlbumForm.attr( 'data-count' );
		var album_limit       = $editAlbumForm.attr( 'data-album_limit' );
		var album_count       = $editAlbumForm.attr( 'data-count_album' );
		var response_div      = $( this ).parents( '#um-user-photos-form-edit-album' ).find( '.um-galley-form-response' );

		if ( confirm( confirmation_text ) ) {
			wp.ajax.send(
				'um_delete_album_photo',
				{
					data: {
						image_id: id,
						_wpnonce: nonce
					},
					success: function( response ) {
						$( target ).remove();
						if ( '' !== limit ) {
							var new_count       = count - 1;
							var new_album_count = album_count - 1;
							var imgs            = $( '#um-user-photos-images-uploaded img' ).length;
							$editAlbumForm.data( 'count', new_count ).data( 'count_album', new_album_count );
							if ( parseInt( new_album_count ) > parseInt( new_count ) ) {
								if ( parseInt( imgs ) + parseInt( new_count ) < parseInt( limit ) ) {
									$( '#um-user-photos-form-edit-album .um-modal-btn' ).show();
									$( '#um-user-photos-form-edit-album .um-user-photos-error' ).hide();
									$( '#um-user-photos-input-album-images, .um-galley-modal-update' ).removeAttr( 'disabled' );
								}
							} else {
								if ( '' !== album_limit && parseInt( imgs ) + parseInt( new_album_count ) < parseInt( album_limit ) ) {
									$( '#um-user-photos-form-edit-album .um-modal-btn' ).show();
									$( '#um-user-photos-form-edit-album .um-user-photos-error' ).hide();
									$( '#um-user-photos-input-album-images, .um-galley-modal-update' ).removeAttr( 'disabled' );
								}
							}
						}
					},
					error: function( response ) {
						console.log( response );
						response_div.html( '<span>Error:' + response + '</span>' ).addClass( 'error' );
					}
				}
			);
		} else {
			$( this ).show();
		}
	});

	// Photos grid pagination
	$(document).on('click', '#um-user-photos-toggle-view-photos-load-more', function (e) {
		e.preventDefault();
		var btn = $(this);
		var btn_parent = btn.parents('.um-load-more');
		var btn_init = btn.text();
		var url = btn.attr('data-href');
		var profile_id = btn.attr('data-profile');
		var data_per_page = btn.attr('data-per_page');
		var data_current_page = btn.attr('data-current_page');
		let next_page = parseInt(data_current_page) + 1;
		var count = btn.attr('data-count');
		var parent = btn.parents('.um-user-photos-albums');
		var photo_holder = parent.find('.photos-container .um-user-photos-single-album');
		var nonce = jQuery(this).attr('data-wpnonce');

		btn.attr('data-current_page', next_page );

		btn.text( wp.i18n.__( 'Loading', 'um-user-photos' ) );
		btn.attr('disabled', true);
		parent.css('opacity', '0.5');

		//photo_holder.append('<p>Display response</p>');
		wp.ajax.send(
			'um_user_photos_load_more',
			{
				data: {
					profile: profile_id,
					per_page: data_per_page,
					page: next_page,
					_wpnonce: nonce
				},
				success: function(response) {
					btn.text(btn_init);
					btn.attr('disabled', false);
					parent.css('opacity', '1');

					if ( '' === response ) {
						btn_parent.remove();
					} else {
						photo_holder.append(response);
						var imgs = photo_holder.find('.um-user-photos-image-block').length;
						if ( parseInt(count) <= parseInt(imgs) ) {
							btn_parent.remove();
						}
						parent.attr( 'data-count', imgs );
					}
				},
				error: function( data ) {
					console.log( data );
				}
			}
		);
	});


	$(document).on('change', '#um-user-photos-input-album-cover', function (e) {
		var target_element = $(this).parents('h1.album-poster-holder');
		var reader = new FileReader();
		reader.onload = function (e) {
			var bg = e.target.result;
			target_element.css('background-image', 'url("' + bg + '")');
			target_element.css('background-size', 'contain');
		};
		reader.readAsDataURL(this.files[0]);
		$('#um-user-photos-input-album-cover').addClass('uploaded').attr('data-size', this.files[0].size);
	});


	$( document ).on( 'change', '#um-user-photos-input-album-images', function ( e ) {
		$( '.um-user-photos-max-upload-error' ).hide();
		$( '.um-user-photos-max-size-upload-error' ).hide();

		var target_form    = $( this ).parents( 'form' );
		var target_element = $( this ).parents( 'form' ).find( '#um-user-photos-images-uploaded' );
		var files          = e.target.files;

		var imgs_size = 0;
		target_element.html( '' );
		var i = 0;
		for ( i; i < files.length; i++ ) {
			var file = files[i];
			//Only pics
			if ( ! file.type.match( 'image' ) ) {
				continue;
			}

			var picReader = new FileReader();
			picReader.addEventListener( 'load', function ( e ) {
				var picFile = e.target;
				target_element.append( '<span><img src="' + picFile.result + '" data-index=""/></span>' );
			});
			//Read the image
			picReader.readAsDataURL( file );

			imgs_size = imgs_size + file.size;
		}

		var count       = target_form.attr( 'data-count' );
		var limit       = target_form.attr( 'data-limit' );
		var album_limit = target_form.attr( 'data-album_limit' );
		var album_count = target_form.attr( 'data-count_album' );
		if ( '' !== limit && '' !== album_limit ) {
			if ( i + parseInt( count ) > parseInt( limit ) || i + parseInt( album_count ) > parseInt( album_limit ) ) {
				um_photos_disable_upload();
			} else {
				um_photos_enable_upload();
			}
		} else {
			if ( '' !== limit ) {
				if ( i + parseInt( count ) > parseInt( limit ) ) {
					um_photos_disable_upload();
				} else {
					um_photos_enable_upload();
				}
			}
			if ( '' !== album_limit ) {
				if ( i + parseInt( album_count ) > parseInt( album_limit ) ) {
					um_photos_disable_upload();
				} else {
					um_photos_enable_upload();
				}
			}
		}

		picReader.addEventListener( 'loadend', function ( e ) {
			if ( 0 === $( '.um-user-photos-album-photos' ).length && $( '#um-user-photos-cover-image' ).length > 0 ) {
				var cover_text = target_element.attr( 'data-covertext' );
				target_element.find( 'span' ).first().addClass( 'um-user-photos-cover' ).attr( 'data-covertext', cover_text );
			}
		});

		$( '.um-user-photos-modal-form h6' ).show();
		$( this ).attr( 'data-imgs_size', imgs_size );
	});

	$( document.body ).on( 'click', '#um-user-photos-images-uploaded span', function ( e ) {
		e.preventDefault();
		if ( $( '#um-user-photos-cover-image' ).length > 0 ) {
			var index = $(this).index();
			var cover_text = $('#um-user-photos-images-uploaded').attr('data-covertext');
			$('.um-user-photos-album-photos .um-user-photos-photo').removeClass('um-user-photos-cover');
			$('#um-user-photos-images-uploaded span').removeClass('um-user-photos-cover');
			$(this).addClass('um-user-photos-cover').attr('data-covertext', cover_text);
			$('#um-user-photos-cover-image').val(index);
			$('#um-user-photos-cover-image-id').val('');
		}
	});

	$( document.body ).on( 'click', '.um-user-photos-album-photos .um-user-photos-photo', function ( e ) {
		e.preventDefault();
		if ( $( '#um-user-photos-cover-image' ).length > 0 ) {
			$('.um-user-photos-album-photos .um-user-photos-photo').removeClass('um-user-photos-cover');
			$('#um-user-photos-images-uploaded span').removeClass('um-user-photos-cover');
			$(this).addClass('um-user-photos-cover')
			$('#um-user-photos-cover-image').val('');
			var id = $(this).find('input').val();
			$('#um-user-photos-cover-image-id').val(id);
		}
	});

	function um_photos_disable_upload() {
		$('.um-user-photos-error').show();
		$('.um-galley-modal-update').attr('disabled', 'disabled');
	}


	function um_photos_enable_upload() {
		$('.um-user-photos-error').hide();
		$('.um-galley-modal-update').removeAttr('disabled', 'disabled');
	}

	$(document).on('change', '#um-user-photos-input-album-cover', function (e) {
		e.preventDefault();
		var field = $(this);
		var form = field.parents('form');
		var max_size = form.attr('data-max_size');
		var max_size_error_msg = form.attr('data-max_size_error');
		var footer = form.find('.um-user-photos-modal-footer');
		var response_div = form.find('.um-galley-form-response');

		response_div.html('').removeClass('error');
		footer.show();

		var allowed_size = max_size / 1000000;

		if (field[0]['files']) {
			var size = field[0]['files'][0]['size'];
			if (size >= max_size) {
				var name = field[0]['files'][0]['name'];
				var err_txt = name + ' ' + max_size_error_msg + ' ' + allowed_size + ' MB';
				field.trigger('reset');
				response_div.html('<span>' + err_txt + '</span>').addClass('error');
				footer.hide();
				return false;
			}
		}
	});


	$(document).on('change', '#um-user-photos-input-album-images', function (e) {
		e.preventDefault();
		var field = $(this);
		var form = field.parents('form');
		var max_size = form.attr('data-max_size');
		var max_size_error_msg = form.attr('data-max_size_error');
		var footer = form.find('.um-user-photos-modal-footer');
		var response_div = form.find('.um-galley-form-response');

		response_div.html('').removeClass('error');
		footer.show();

		var allowed_size = max_size / 1000000;

		if (field[0]['files']) {

			var error = false;
			var error_html = '';
			var img_size = 0;
			var img_name = '';
			var files = field[0]['files'];
			var err_txt = '';

			for (var i = 0, f; f = files[i]; i++) {
				//console.log(field[0]['files'][i]);
				img_size = field[0]['files'][i]['size'];
				img_name = field[0]['files'][i]['name'];
				if (img_size >= max_size) {
					error = true;
					err_txt = img_name + ' ' + max_size_error_msg + ' ' + allowed_size + ' MB';
					error_html += '<p style="margin:0;">' + err_txt + '</p>';
				}
			}

			if (error) {
				response_div.html('<span>' + err_txt + '</span>').addClass('error');
				footer.hide();
				return false;
			}
		}
	});


	$(document).on('click', '.um-user-photos-album-options', function (e) {
		e.preventDefault();
		var $menu = $(this).next('.um-dropdown');
		return $menu.length && UM.dropdown.show( $menu );
	});


	$(document).on('click', '.um-dropdown-hide', function (e) {
		e.preventDefault();
		UM.dropdown.hideAll();
	});


	/* Albums grid pagination */
	$(document).on('click', '[data-um-pagi-action] [data-page]', function (e) {
		e.preventDefault();

		var $container = jQuery(e.currentTarget).closest('[data-um-pagi-action]');
		var action = $container.data('um-pagi-action');
		var nonce = $container.find('.um-pagi').data('wpnonce');

		wp.ajax.send(
			'um_user_photos_get_albums_content',
			{
				data: {
					page: jQuery( e.currentTarget ).data( 'page' ),
					per_page: $container.data( 'um-pagi-per_page' ),
					column: $container.data( 'um-pagi-column' ),
					_wpnonce: nonce,
				},
				success: function (response) {
					$container.html( jQuery( response ) );
					$container.find( '.um.ultimatemember_albums' ).unwrap();
					$( document ).trigger( 'resize' );
				},
				error: function( data ) {
					console.log( data );
				}
			}
		);
	});

})(jQuery);
