<?php

declare (strict_types=1);
/*
 * This file is part of the Monolog package.
 *
 * (c) <PERSON><PERSON> <j.bog<PERSON><PERSON>@seld.be>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace WPMailSMTP\Vendor\Monolog\Processor;

use WPMailSMTP\Vendor\Monolog\ResettableInterface;
/**
 * Adds a unique identifier into records
 *
 * <AUTHOR> <<EMAIL>>
 */
class UidProcessor implements \WPMailSMTP\Vendor\Monolog\Processor\ProcessorInterface, \WPMailSMTP\Vendor\Monolog\ResettableInterface
{
    /** @var string */
    private $uid;
    public function __construct(int $length = 7)
    {
        if ($length > 32 || $length < 1) {
            throw new \InvalidArgumentException('The uid length must be an integer between 1 and 32');
        }
        $this->uid = $this->generateUid($length);
    }
    /**
     * {@inheritDoc}
     */
    public function __invoke(array $record) : array
    {
        $record['extra']['uid'] = $this->uid;
        return $record;
    }
    public function getUid() : string
    {
        return $this->uid;
    }
    public function reset()
    {
        $this->uid = $this->generateUid(\strlen($this->uid));
    }
    private function generateUid(int $length) : string
    {
        return \substr(\bin2hex(\random_bytes((int) \ceil($length / 2))), 0, $length);
    }
}
