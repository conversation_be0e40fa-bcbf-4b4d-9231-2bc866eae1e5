var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.FieldLayout=WPForms.Admin.Builder.FieldLayout||function(n){let p={};const m={init(){n(m.ready)},ready(){m.setup(),m.initLabels(),m.events(),m.rowDisplayHeightBalance(n(".wpforms-layout-display-rows, .wpforms-layout-display-blocks"))},setup(){p={$builder:n("#wpforms-builder"),$fieldOptions:n("#wpforms-field-options"),$sortableFieldsWrap:n("#wpforms-panel-fields .wpforms-field-wrap")}},events(){p.$builder.on("change",".wpforms-field-option-row-preset input",m.presetChange).on("change",".wpforms-field-option-row-display select",m.displayChange).on("mouseenter",".wpforms-field-layout-columns .wpforms-field",m.subfieldMouseEnter).on("mouseleave",".wpforms-field-layout-columns .wpforms-field",m.subfieldMouseLeave).on("wpformsFieldAdd",m.fieldAdd).on("wpformsBeforeFieldAddToDOM",m.beforeFieldAddToDOM).on("wpformsBeforeFieldAddOnClick",m.beforeFieldAddOnClick).on("wpformsBeforeFieldDelete",m.beforeFieldDelete).on("wpformsBeforeFieldDeleteAlert",m.adjustDeleteFieldAlert).on("wpformsFieldOptionTabToggle",m.fieldOptionsUpdate).on("wpformsFieldMoveRejected",m.fieldMoveRejected).on("wpformsBeforeFieldDuplicate",m.beforeFieldDuplicate).on("wpformsFieldDuplicated",m.fieldDuplicated).on("wpformsFieldDelete",m.handleFieldDelete).on("wpformsFieldAdd wpformsFieldChoiceAdd wpformsFieldChoiceDelete wpformsFieldDynamicChoiceToggle wpformsFieldLayoutChangeDisplay",m.handleFieldOperations).on("wpformsFieldMoveRejected",m.handleFieldMoveRejected).on("wpformsFieldMove",m.handleFieldMove).on("wpformsFieldDragOver wpformsFieldDragChange",m.handleFieldDrag).on("change",".wpforms-field-option-row-size select",m.handleFieldSizeChange)},isLayoutBasedField(e){return["layout","repeater"].includes(e)},handleFieldDelete(e,l,o,i){0!==i.length&&i.hasClass("wpforms-layout-display-rows")&&m.rowDisplayHeightBalance(i)},handleFieldSizeChange(){var e=n(this).parent().data("field-id"),e=n("#wpforms-field-"+e).parents(".wpforms-layout-display-rows, .wpforms-layout-display-blocks");e.length&&m.rowDisplayHeightBalance(e)},handleFieldOperations(e,l){l=n("#wpforms-field-"+l).find(".wpforms-layout-display-rows, .wpforms-layout-display-blocks");l.length&&m.rowDisplayHeightBalance(l)},handleFieldMoveRejected(e,l){l=l.prev(".wpforms-field, .wpforms-alert").data("field-id"),l=n("#wpforms-field-"+l).find(".wpforms-layout-display-rows, .wpforms-layout-display-blocks");l.length&&m.rowDisplayHeightBalance(l)},handleFieldMove(e,l){l=l.item.first().parents(".wpforms-layout-display-rows, .wpforms-layout-display-blocks");l.length&&m.rowDisplayHeightBalance(l)},handleFieldDrag(e,l,o){o.hasClass("wpforms-layout-column")&&(o=o.parents(".wpforms-layout-display-rows, .wpforms-layout-display-blocks")).length&&m.rowDisplayHeightBalance(o)},displayChange(){var e=n(this),l=e.val(),o=e.closest(".wpforms-field-option-row-display").data("field-id"),i=n("#wpforms-field-"+o).find(".wpforms-field-layout-columns");e.closest(".wpforms-field-option-row-display").parent().find(".wpforms-field-option-row-preset").toggleClass("wpforms-layout-display-rows","rows"===l),i.toggleClass("wpforms-layout-display-rows","rows"===l).toggleClass("wpforms-layout-display-columns","columns"===l).find(".wpforms-field").css("margin-bottom","columns"===l?5:""),p.$builder.trigger("wpformsFieldLayoutChangeDisplay",[o])},rowDisplayHeightBalance(e){e.each(function(){var e=n(this);const o=[];e.find(".wpforms-field, .wpforms-field-drag-placeholder").each(function(){var e=n(this),l=e.index();o[l]=Math.max(o[l]||0,e.outerHeight())}),e.find(".wpforms-field, .wpforms-field-drag-placeholder").each(function(){var e=n(this);e.css("margin-bottom",o[e.index()]-e.outerHeight()+5)})}),p.$builder.trigger("wpformsLayoutAfterHeightBalance",{$rows:e})},presetChange(e){const l=n(this),o=l.val(),i=l.closest(".wpforms-field-option-row-preset"),t=i.data("field-id"),d=n("#wpforms-field-"+t),s=d.find(".wpforms-field-layout-columns"),r=[],a=(s.find(".wpforms-layout-column").each(function(e){r[e]=n(this).find(".wpforms-field").detach()}),m.getFieldColumnsData(t)),f=o.split("-").map(function(e,l){return{width_preset:e,fields:a[l]?a[l].fields:[]}});if(m.updateFieldColumnsData(t,f),s.html(m.generatePreviewColumns(f)),s.find(".wpforms-layout-column").each(function(e){var l=n(this);l.append(r[e]),WPForms.Admin.Builder.DragFields.initSortableContainer(l)}),f.length<a.length){let l=n([]);for(let e=f.length;e<a.length;e++)l=l.add(r[e]);l.css("margin-bottom",""),d.after(l)}m.rowDisplayHeightBalance(s),m.reorderLayoutFieldsOptions(d),p.$builder.trigger("wpformsLayoutAfterPresetChange",{fieldId:t,preset:o,newColumnsData:f,oldColumnsData:a})},generatePreviewColumns(e){if(!e?.length)return"";const l=wp.template("wpforms-layout-field-column-plus-placeholder-template")();return e.map(function(e){return`<div class="wpforms-layout-column wpforms-layout-column-${e.width_preset}">
							${l}
						</div>`}).join("")},getFieldColumnsData(e){e=n(`#wpforms-field-option-${e}-columns-json`).val();let l;try{l=JSON.parse(e)}catch(e){l=[]}return l},columnsHasFieldID(e,l){return 0<m.getFieldColumnsData(e).filter(function(e){return e.fields&&e.fields.includes(l)}).length},updateFieldColumnsData(e,l){var o=n(`#wpforms-field-option-${e}-columns-json`),i=o.val(),t=JSON.stringify(l);o.val(t),i!==t&&p.$builder.trigger("wpformsLayoutColumnsDataUpdated",{fieldId:e,data:l}),p.$builder.trigger("wpformsLayoutAfterUpdateColumnsData",{fieldId:e,data:l})},beforeFieldAddToDOM(e,l,o,i,t){t&&t.length&&t.hasClass("wpforms-layout-column")&&(e.skipAddFieldToBaseLevel=!0,m.fieldAddToColumn(o,i,l.position,t))},fieldAdd(e,l,o){var i=n("#wpforms-field-option-"+l).prev(),t=i.find(".wpforms-field-option-hidden-type").val();m.isLayoutBasedField(t)&&(t=i.find(".wpforms-field-option-hidden-id").val(),m.reorderLayoutFieldsOptions(n("#wpforms-field-"+t))),m.isLayoutBasedField(o)&&p.$builder.find(`#wpforms-field-${l} .wpforms-layout-column`).each(function(){WPForms.Admin.Builder.DragFields.initSortableContainer(n(this))})},beforeFieldAddOnClick(e,l,o){var i=p.$sortableFieldsWrap.find(".wpforms-fields-sortable-default");i.length&&!m.isFieldAllowedInColum(l,i)&&(e.preventDefault(),m.fieldMoveRejected(e,o,null,null))},beforeFieldDelete(e,l,o){m.isLayoutBasedField(o)?m.getFieldColumnsData(l).forEach(function(e){Array.isArray(e.fields)&&e.fields.forEach(function(e){WPFormsBuilder.fieldDeleteById(e)})}):(m.removeFieldFromColumns(l),n("#wpforms-field-"+l).closest(".wpforms-field").removeClass("wpforms-field-child-hovered"))},adjustDeleteFieldAlert(e,l,o){m.isLayoutBasedField(o)&&(e.preventDefault(),e=(wpforms_builder[o]?.delete_confirm?wpforms_builder[o]:wpforms_builder.layout).delete_confirm,n.confirm({title:!1,content:e,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action(){WPFormsBuilder.fieldDeleteById(l.id)}},cancel:{text:wpforms_builder.cancel}}}))},fieldOptionsUpdate(e,l){m.fieldLegacyLayoutSelectorUpdate(l),m.fieldSizeOptionUpdate(l)},fieldLegacyLayoutSelectorUpdate(e){var l,e=n(`#wpforms-field-option-row-${e}-css .layout-selector-display`);e.find(".wpforms-alert-layout").length||(l=n(`
				<div class="wpforms-alert-warning wpforms-alert-layout wpforms-alert wpforms-alert-nomargin">
					<h4>${wpforms_builder.layout.legacy_layout_notice_title}</h4>
					<p>${wpforms_builder.layout.legacy_layout_notice_text}</p>
				</div>
			`),e.append(l),e.find(".heading, .layouts").addClass("wpforms-hidden-strict"))},fieldSizeOptionUpdate(l){var o=n("#wpforms-field-"+l),i=o.data("field-type"),t=o.closest(".wpforms-layout-column"),d=0<t.length,s=t.closest(".wpforms-field").data("field-type")??"layout";if(!(-1<["textarea","richtext"].indexOf(i))){i=n(`#wpforms-field-option-row-${l}-size`);let e=i.find(".wpforms-notice-field-size");l=t.hasClass("wpforms-layout-column-100")&&o.closest(".wpforms-field-layout").length,t=(e.remove(),e=n(`
				<label class="sub-label wpforms-notice-field-size" title="${wpforms_builder[s].size_notice_tooltip}">
					${wpforms_builder[s].size_notice_text}
				</label>
			`),i.append(e),i.find("select")),o=(d?t.attr("title",wpforms_builder[s].size_notice_tooltip):t.attr("title",""),d&&!l);t.toggleClass("wpforms-disabled",o),e.toggleClass("wpforms-hidden",!o)}},receiveFieldToColumn(e,l,o){m.removeFieldFromColumns(e),o&&o.hasClass("wpforms-layout-column")&&(m.positionFieldInColumn(e,l,o),m.fieldOptionsUpdate(null,e),p.$builder.trigger("wpformsLayoutAfterReceiveFieldToColumn",{fieldId:e,position:l,column:o}))},removeFieldFromColumns(o){o=Number(o),p.$builder.find(".wpforms-field").each(function(){var e=n(this);if(m.isLayoutBasedField(e.data("field-type"))){var e=Number(e.data("field-id")),l=m.getFieldColumnsData(e);for(let e=0;e<l.length;e++)Array.isArray(l[e].fields)&&(l[e].fields=l[e].fields.filter(function(e){return Number(e)!==o}));m.updateFieldColumnsData(e,l)}})},positionFieldInColumn(l,e,o){var i,t,d;o&&o.hasClass("wpforms-layout-column")&&(i=o.closest(".wpforms-field").data("field-id"),o=o.index(),t=m.getFieldColumnsData(i))&&t[o]&&((d=t[o]).fields=Array.isArray(d.fields)?d.fields:[],l=Number(l),d.fields=d.fields.filter(function(e){return Number(e)!==l}),d.fields.splice(e,0,l),t[o]=d,m.updateFieldColumnsData(i,t))},duplicateLayoutField(e){var l=n("#wpforms-field-"+e);if(m.isLayoutBasedField(l.data("field-type"))){const o=m.getFieldColumnsData(e),i=WPFormsBuilder.fieldDuplicateRoutine(e,!0),t=n("#wpforms-field-"+i),d=n("#wpforms-field-option-"+i),s=t.find(".wpforms-layout-column"),r=JSON.parse(JSON.stringify(o)),a=n(`#wpforms-field-option-${e} .wpforms-field-option-row-preset input:checked`).val();d.find(`#wpforms-field-option-${i}-preset-`+a).prop("checked",!0),t.find(".wpforms-layout-column .wpforms-field").remove(),t.find(".wpforms-fields-sortable-default").removeClass("wpforms-fields-sortable-default"),o.forEach(function(e,i){if(r[i].fields=[],Array.isArray(e.fields)){const t=s.eq(i);e.fields.forEach(function(e){var l,o=n("#wpforms-field-"+e);o.length&&o.find("> .wpforms-field-duplicate").length&&(o=WPFormsBuilder.fieldDuplicateRoutine(e,!1),e=n("#wpforms-field-"+o).detach().removeClass("active"),l=n("#wpforms-field-option-"+o),t.append(e),l.hide(),r[i].fields.push(o))})}}),m.updateFieldColumnsData(i,r),m.reorderLayoutFieldsOptions(t),t.trigger("click"),WPFormsUtils.triggerEvent(p.$builder,"wpformsFieldDuplicated",[e,l,i,t])}},subfieldMouseEnter(e){n(this).closest(".wpforms-field-layout-columns").closest(".wpforms-field").addClass("wpforms-field-child-hovered")},subfieldMouseLeave(e){n(this).closest(".wpforms-field-layout-columns").closest(".wpforms-field").removeClass("wpforms-field-child-hovered")},initLabels(){n(".wpforms-field-option-layout .wpforms-field-option-row-label input").trigger("input")},fieldAddToColumn(e,l,o,i){var t=i.find(".wpforms-field"),t=("bottom"===o&&(o=t.length),t.eq(o)),d=t.data("field-id"),t=(t.length?t.before(e):i.append(e),n("#wpforms-field-option-"+d));t.length?t.before(l):p.$fieldOptions.append(l),m.receiveFieldToColumn(e.data("field-id"),o,i),m.reorderLayoutFieldsOptions(i.closest(".wpforms-field-layout, .wpforms-field-repeater"))},fieldMoveRejected(e,l,o,i){var t=l.data("field-type"),t=(t?n("#wpforms-add-fields-"+t):l).text(),t={title:wpforms_builder.heads_up,content:wpforms_builder.layout.not_allowed_alert_text.replace(/%s/g,`<strong>${t}</strong>`),type:"red"},t=wp.hooks.applyFilters("wpforms.LayoutField.fieldMoveRejectedModalOptions",t,l,o,i);n.confirm({title:t.title,content:t.content,icon:"fa fa-exclamation-circle",type:t.type,buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})},reorderLayoutFieldsOptions(e){if(e?.length&&m.isLayoutBasedField(e.data("field-type"))){const l=e.data("field-id"),t=m.getFieldColumnsData(l);let i=n("#wpforms-field-option-"+l);t.forEach(function(e,l){if(Array.isArray(e.fields)){const o=e.fields.slice();e.fields.forEach(function(e){let l=n("#wpforms-field-option-"+e);l.length?(l=l.detach(),i.after(l),i=l):-1!==(e=o.indexOf(e))&&o.splice(e,1)}),e.fields=o,t[l]=e}}),m.updateFieldColumnsData(l,t)}},isFieldAllowedInColum(e,l){var o=wpforms_builder.layout.not_allowed_fields.indexOf(e)<0;return wp.hooks.applyFilters("wpforms.LayoutField.isFieldAllowedInColumn",o,e,l)},beforeFieldDuplicate(e,l,o){m.isLayoutBasedField(o.data("field-type"))&&(e.preventDefault(),m.duplicateLayoutField(l),WPFormsBuilder.increaseNextFieldIdAjaxRequest())},fieldDuplicated(e,l,o,i,t){m.isLayoutBasedField(o.data("field-type"))||m.positionFieldInColumn(i,t.index()-1,t.parent())}};return m}((document,window,jQuery)),WPForms.Admin.Builder.FieldLayout.init();