<?php
namespace um_ext\um_user_bookmarks\core;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Bookmark_Ajax
 * @package um_ext\um_user_bookmarks\core
 */
class Bookmark_Ajax {

	/**
	 * Bookmark_Ajax constructor.
	 */
	public function __construct() {
		add_action( 'wp_ajax_um_bookmarks_modal_content', array( $this, 'load_modal_content' ) );
		add_action( 'wp_ajax_um_bookmarks_add', array( $this, 'add_bookmark' ) );
		add_action( 'wp_ajax_um_bookmarks_add_profile', array( $this, 'add_profile_bookmark' ) );
		add_action( 'wp_ajax_um_bookmarks_remove', array( $this, 'remove_bookmark' ) );
		add_action( 'wp_ajax_um_bookmarks_remove_profile', array( $this, 'remove_profile_bookmark' ) );
		add_action( 'wp_ajax_um_bookmarks_folder_add', array( $this, 'add_folder' ) );
		add_action( 'wp_ajax_um_bookmarks_view_folder', array( $this, 'view_folder' ) );
		add_action( 'wp_ajax_nopriv_um_bookmarks_view_folder', array( $this, 'view_folder' ) );
		add_action( 'wp_ajax_um_bookmarks_view_edit_folder', array( $this, 'view_edit_folder' ) );
		add_action( 'wp_ajax_um_bookmarks_delete_folder', array( $this, 'delete_folder' ) );

		add_action( 'wp_ajax_um_bookmarks_get_folder_view', array( $this, 'get_folders_list' ) );
		add_action( 'wp_ajax_nopriv_um_bookmarks_get_folder_view', array( $this, 'get_folders_list' ) );

		add_action( 'wp_ajax_um_bookmarks_update_folder', array( $this, 'update_folder' ) );

		add_action( 'um_same_page_update_ajax_action', array( $this, 'same_page_update' ) );
	}


	/**
	 * Returns folder option for new bookmark
	 */
	public function load_modal_content() {
		if ( empty( $_POST['_wpnonce'] ) || ! wp_verify_nonce( $_POST['_wpnonce'] , 'um_user_bookmarks_new_bookmark' ) ) {
			wp_send_json_error( __( 'Invalid request', 'um-user-bookmarks' ) );
		}
		if ( empty( $_POST['bookmark_post'] ) ) {
			wp_send_json_error( __( 'Invalid post data', 'um-user-bookmarks' ) );
		}

		$user_id = get_current_user_id();
		$post_id = absint( $_POST['bookmark_post'] );
		$bookmarks = get_user_meta( $user_id, '_um_user_bookmarks' , true );

		$content = UM()->get_template( 'select-folder.php', um_user_bookmarks_plugin, array(
			'bookmarks' => $bookmarks,
			'post_id'   => $post_id
		) );

		wp_send_json_success( $content );
	}


	/**
	 * Add New bookmark
	 */
	public function add_bookmark() {
		if ( empty( $_POST['_wpnonce'] ) || ! wp_verify_nonce( $_POST['_wpnonce'] , 'um_user_bookmarks_new_bookmark' ) ) {
			wp_send_json_error( __( 'Invalid request', 'um-user-bookmarks' ) );
		}
		if ( empty( $_POST['post_id'] ) ) {
			wp_send_json_error( __( 'Invalid post data', 'um-user-bookmarks' ) );
		}
		if ( ! is_user_logged_in() ) {
			wp_send_json_error( __( 'You have to login', 'um-user-bookmarks' ) );
		}
		if ( empty( $_POST['_um_user_bookmarks_folder'] ) ) {
			// translators: %s is a folder text
			wp_send_json_error( sprintf( __( '%s name is required', 'um-user-bookmarks' ), UM()->User_Bookmarks()->get_folder_text() ) );
		}

		// sanitize input data
		$is_new      = ! empty( $_POST['is_new'] );
		$is_private  = ! empty( $_POST['is_private'] );
		$post_id     = absint( $_POST['post_id'] );
		$user_id     = get_current_user_id();
		$folder_name = sanitize_text_field( $_POST['_um_user_bookmarks_folder'] );

		if( UM()->options()->get( 'um_user_bookmarks_disable_folders' )){
			$this->add_default_folder( $user_id );
		}

		if ( preg_match( "/^[\p{Latin}\d\-_ ]+$/i", $folder_name ) ) {
			$folder_slug = sanitize_title( $folder_name );
		} else {
			$folder_slug = $user_id . '_' . substr( md5( $folder_name ), 0, 8 );
		}

		$bookmark          = array();
		$previous_bookmark = get_user_meta( $user_id, '_um_user_bookmarks', true );
		if ( ! empty( $previous_bookmark ) && is_array( $previous_bookmark ) ) {
			$bookmark = $previous_bookmark;
		}

		if ( $is_new ) {
			if ( isset( $bookmark[ $folder_slug ] ) ) {
				// translators: %s is a folder text
				wp_send_json_error( sprintf( __( '%s is already exists', 'um-user-bookmarks' ), UM()->User_Bookmarks()->get_folder_text() ) );
			}

			$access = $is_private ? 'private' : 'public';

			$bookmark[ $folder_slug ] = array(
				'title'     => $folder_name,
				'type'      => $access,
				'bookmarks' => array(
					$post_id => array(
						'url' => get_the_permalink( $post_id ),
					),
				),
			);

		} else {
			if ( ! isset( $bookmark[ $folder_slug ] ) ) {
				// translators: %s is a folder text
				wp_send_json_error( sprintf( __( '%s isn\'t exists', 'um-user-bookmarks' ), UM()->User_Bookmarks()->get_folder_text() ) );
			}

			$bookmark[ $folder_slug ]['bookmarks'][ $post_id ] = array(
				'url' => get_the_permalink( $post_id ),
			);
		}

		$bookmark = apply_filters( 'um_user_bookmarks_data', $bookmark );

		if ( $bookmark && is_array( $bookmark ) ) {
			update_user_meta( $user_id, '_um_user_bookmarks', $bookmark );

			$post_users = array();

			$old_post_users = get_post_meta( $post_id, '_um_in_users_bookmarks', true );
			if ( ! empty( $old_post_users ) && is_array( $old_post_users ) ) {
				$post_users = $old_post_users;
			}

			if ( ! in_array( $user_id, $post_users ) ) {
				$post_users[] = $user_id;
			}

			update_post_meta( $post_id, '_um_in_users_bookmarks', $post_users );

			wp_send_json_success( trim( UM()->User_Bookmarks()->get_button( 'remove', $post_id ) ) );
		}

		wp_send_json_error( __( 'Invalid request', 'um-user-bookmarks' ) );
	}


	/**
	 * Add New profile bookmark
	 *
	 */
	public function add_profile_bookmark() {
		if ( ! is_user_logged_in() ) {
			wp_send_json_error( __( 'You have to login', 'um-user-bookmarks' ) );
		}
		// phpcs:disable WordPress.Security.NonceVerification
		if ( empty( $_POST['_wpnonce'] ) || ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_bookmarks_new_bookmark' ) ) {
			wp_send_json_error( __( 'Invalid request', 'um-user-bookmarks' ) );
		}

		if ( empty( $_POST['profile_id'] ) && empty( $_POST['profile_id'] ) ) {
			wp_send_json_error( __( 'Invalid profile data', 'um-user-bookmarks' ) );
		}

		if ( ! empty( $_POST['profile_id'] ) ) {
			$bookmark          = array();
			$profile_id        = absint( $_POST['profile_id'] );
			$current_user_id   = get_current_user_id();
			$previous_bookmark = get_user_meta( $current_user_id, '_um_user_bookmarks_profiles', true );
			if ( ! empty( $previous_bookmark ) && is_array( $previous_bookmark ) ) {
				$bookmark = $previous_bookmark;
			}

			$profile_slug            = strtolower( UM()->user()->get_profile_slug( $profile_id ) );
			$bookmark[ $profile_id ] = UM()->permalinks()->profile_permalink( $profile_slug );

			$bookmark = apply_filters( 'um_user_bookmarks_profiles_data', $bookmark );
			update_user_meta( $current_user_id, '_um_user_bookmarks_profiles', $bookmark );

			wp_send_json_success( trim( UM()->User_Bookmarks()->get_button( 'remove', null, true, $profile_id ) ) );
		}
		// phpcs:enable WordPress.Security.NonceVerification

		wp_send_json_error( __( 'Invalid request', 'um-user-bookmarks' ) );
	}


	/**
	 * Remove post from user bookmarks
	 */
	public function remove_bookmark() {
		if ( ! is_user_logged_in() ) {
			wp_send_json_error( __( 'You have to login', 'um-user-bookmarks' ) );
		}
		$user_id = get_current_user_id();

		if ( empty( $_POST['bookmark_post'] ) ) {
			wp_send_json_error( __( 'Invalid post data', 'um-user-bookmarks' ) );
		}
		$post_id = absint( $_POST['bookmark_post'] );

		if ( empty( $_POST['_nonce'] ) || ! wp_verify_nonce( $_POST['_nonce'], 'um_user_bookmarks_remove_' . $post_id ) ) {
			wp_send_json_error( __( 'Invalid request', 'um-user-bookmarks' ) );
		}

		do_action( 'um_before_bookmark_remove', $post_id );

		if ( ! UM()->User_Bookmarks()->is_bookmarked( $user_id, $post_id ) ) {
			wp_send_json_error( __( 'Not found in bookmarks', 'um-user-bookmarks' ) );
		}
		$user_bookmarks = get_user_meta( $user_id, '_um_user_bookmarks', true );

		foreach ( $user_bookmarks as $key => $value ) {
			if ( ! empty( $value['bookmarks'][ $post_id ] ) ) {
				unset( $user_bookmarks[ $key ]['bookmarks'][ $post_id ] );
				break;
			}
		}

		update_user_meta( $user_id, '_um_user_bookmarks', $user_bookmarks );

		$post_users = get_post_meta( $post_id, '_um_in_users_bookmarks', true );
		$post_users = empty( $post_users ) ? array() : $post_users;

		if ( $k = array_search( $user_id, $post_users ) ) {
			unset( $post_users[ $k ] );
			$post_users = array_unique( $post_users );
		}

		update_post_meta( $post_id, '_um_in_users_bookmarks', $post_users );

		do_action( 'um_after_bookmark_removed', $post_id );

		if ( ! empty( $_POST['return_button'] ) ) {
			wp_send_json_success( trim( UM()->User_Bookmarks()->get_button( 'add', $post_id ) ) );
		}

		wp_send_json_success();
	}


	/**
	 * Remove profile from user bookmarks
	 */
	public function remove_profile_bookmark() {
		if ( ! is_user_logged_in() ) {
			wp_send_json_error( __( 'You have to login', 'um-user-bookmarks' ) );
		}
		// phpcs:disable WordPress.Security.NonceVerification
		if ( empty( $_POST['bookmark_profile'] ) ) {
			wp_send_json_error( __( 'Invalid post data', 'um-user-bookmarks' ) );
		}
		$profile_id = absint( $_POST['bookmark_profile'] );
		if ( empty( $_POST['_nonce'] ) || ! wp_verify_nonce( $_POST['_nonce'], 'um_user_bookmarks_remove_' . $profile_id ) ) {
			wp_send_json_error( __( 'Invalid request', 'um-user-bookmarks' ) );
		}
		$current_user_id = get_current_user_id();
		$bookmarks       = get_user_meta( $current_user_id, '_um_user_bookmarks_profiles', true );

		if ( ! empty( $bookmarks ) ) {
			unset( $bookmarks[ $profile_id ] );

			update_user_meta( $current_user_id, '_um_user_bookmarks_profiles', $bookmarks );
			do_action( 'um_after_bookmark_removed_profile', $profile_id );

			if ( ! empty( $_REQUEST['return_button'] ) ) {
				wp_send_json_success( trim( UM()->User_Bookmarks()->get_button( 'add', null, true, $profile_id ) ) );
			}
		}
		// phpcs:enable WordPress.Security.NonceVerification

		wp_send_json_success();
	}


	/**
	 * Add New bookmark folder
	 */
	public function add_folder() {
		if ( empty( $_POST['_wpnonce'] ) || ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_bookmarks_new_bookmark_folder' ) ) {
			wp_send_json_error( __( 'Invalid request', 'um-user-bookmarks' ) );
		}
		if ( ! is_user_logged_in() ) {
			wp_send_json_error( __( 'You have to login', 'um-user-bookmarks' ) );
		}
		if ( empty( $_POST['_um_user_bookmarks_folder'] ) ) {
			// translators: %s is a folder text
			wp_send_json_error( sprintf( __( '%s name is required', 'um-user-bookmarks' ), UM()->User_Bookmarks()->get_folder_text() ) );
		}

		// sanitize input data
		$is_private  = ! empty( $_POST['is_private'] );
		$user_id     = get_current_user_id();
		$folder_name = sanitize_text_field( $_POST['_um_user_bookmarks_folder'] );

		if ( preg_match( "/^[\p{Latin}\d\-_ ]+$/i", $folder_name ) ) {
			$folder_slug = sanitize_title( $folder_name );
		} else {
			$folder_slug = $user_id . '_' . substr( md5( $folder_name ), 0, 8 );
		}

		$bookmark = array();
		if ( $previous_bookmark = get_user_meta( $user_id, '_um_user_bookmarks', true ) ) {
			$bookmark = $previous_bookmark;
		}
		if ( isset( $bookmark[ $folder_slug ] ) ) {
			// translators: %s is a folder text
			wp_send_json_error( sprintf( __( '%s is already exists', 'um-user-bookmarks' ), UM()->User_Bookmarks()->get_folder_text() ) );
		}

		$access = $is_private ? 'private' : 'public';

		$bookmark[ $folder_slug ] = array(
			'title'     => $folder_name,
			'type'      => $access,
			'bookmarks' => array(),
		);

		$bookmark = apply_filters( 'um_user_bookmarks_data', $bookmark );

		if ( $bookmark ) {
			update_user_meta( $user_id, '_um_user_bookmarks', $bookmark );
			wp_send_json_success();
		}

		// translators: %s is a folder text
		wp_send_json_error( sprintf( __( 'Invalid %s', 'um-user-bookmarks' ), UM()->User_Bookmarks()->get_folder_text() ) );
	}


	/**
	 * View Folder
	 */
	public function view_folder() {
		if ( empty( $_POST['_nonce'] ) || ! wp_verify_nonce( $_POST['_nonce'], 'um_user_bookmarks_folder_view' ) ) {
			wp_send_json_error( __( 'Invalid request', 'um-user-bookmarks' ) );
		}
		if ( empty( $_POST['key'] ) || empty( $_POST['profile_id'] ) ) {
			wp_send_json_error( __( 'Invalid request', 'um-user-bookmarks' ) );
		}

		// sanitize input data
		$key  = sanitize_key( $_POST['key'] );
		$user = absint( $_POST['profile_id'] );

		$bookmarks = get_user_meta( $user, '_um_user_bookmarks', true );
		if ( ! isset( $bookmarks[ $key ] ) ) {
			// translators: %s is a folder text
			wp_send_json_error( sprintf( __( '%s doesn\'t exists', 'um-user-bookmarks' ), UM()->User_Bookmarks()->get_folder_text() ) );
		}

		$data = array(
			'key'   => $key,
			'title' => $bookmarks[ $key ]['title'],
			'user'  => $user,
		);

		wp_send_json_success( UM()->get_template( 'profile/single-folder.php', um_user_bookmarks_plugin, $data ) );
	}


	/**
	 * View Edit Folder form
	 */
	public function view_edit_folder() {
		if ( empty( $_POST['_nonce'] ) || ! wp_verify_nonce( $_POST['_nonce'], 'um_user_bookmarks_folder_edit' ) ) {
			wp_send_json_error( __( 'Invalid request', 'um-user-bookmarks' ) );
		}
		if ( ! isset( $_POST['key'] ) || ! isset( $_POST['user'] ) ) {
			wp_send_json_error( __( 'Invalid request', 'um-user-bookmarks' ) );
		}

		$key  = sanitize_key( $_POST['key'] );
		$user = absint( $_POST['user'] );

		$bookmarks = get_user_meta( $user, '_um_user_bookmarks',true );

		if ( ! isset( $bookmarks[ $key ] ) ) {
			// translators: %s is a folder text
			wp_send_json_error( sprintf( __( '%s doesn\'t exists', 'um-user-bookmarks' ), UM()->User_Bookmarks()->get_folder_text() ) );
		}

		$folder = $bookmarks[ $key ];
		$folder['title'] = sanitize_text_field( $folder['title'] );

		wp_send_json_success( UM()->get_template( 'profile/edit-folder.php', um_user_bookmarks_plugin, array(
			'key'     => $key,
			'user'    => $user,
			'private' => ( $folder['type'] == 'private' ) ? true : false,
			'folder'  => $folder,
		) ) );
	}


	/**
	 * Delete bookmark folder & its data
	 */
	public function delete_folder() {
		if ( empty( $_POST['_nonce'] ) || ! wp_verify_nonce( $_POST['_nonce'], 'um_user_bookmarks_folder_delete' ) ) {
			wp_send_json_error( __( 'Invalid request.', 'um-user-bookmarks' ) );
		}
		if ( empty( $_POST['key'] ) ) {
			wp_send_json_error( __( 'Invalid request', 'um-user-bookmarks' ) );
		}

		// sanitize input data
		$key = sanitize_key( $_POST['key'] );

		do_action( 'um_user_bookmarks_before_folder_delete', $key );

		$user_id   = get_current_user_id();
		$bookmarks = get_user_meta( $user_id , '_um_user_bookmarks', true );
		if ( empty( $bookmarks ) || ! isset( $bookmarks[ $key ] ) ) {
			// translators: %s is a folder text
			wp_send_json_error( sprintf( __( '%s doesn\'t exists', 'um-user-bookmarks' ), UM()->User_Bookmarks()->get_folder_text() ) );
		}

		if ( ! empty( $bookmarks[ $key ]['bookmarks'] ) ) {
			foreach ( array_keys( $bookmarks[ $key ]['bookmarks'] ) as $post_id ) {
				$post_users = get_post_meta( $post_id, '_um_in_users_bookmarks', true );
				$post_users = empty( $post_users ) ? array() : $post_users;

				if ( $k = array_search( $user_id, $post_users ) ) {
					unset( $post_users[ $k ] );
					$post_users = array_unique( $post_users );
				}

				update_post_meta( $post_id, '_um_in_users_bookmarks', $post_users );
			}
		}

		unset( $bookmarks[ $key ] );

		update_user_meta( $user_id, '_um_user_bookmarks', $bookmarks );

		do_action( 'um_user_bookmarks_after_folder_delete', $key );

		wp_send_json_success();
	}


	/**
	 * Returns folder view
	 */
	public function get_folders_list() {
		if ( ! wp_verify_nonce( $_POST['_nonce'], 'um_user_bookmarks_back' ) ) {
			wp_send_json_error( __( 'Invalid request.', 'um-user-bookmarks' ) );
		}

		if ( ! isset( $_POST['profile_id'] ) ) {
			wp_send_json_error( __( 'Invalid request', 'um-user-bookmarks' ) );
		}

		$profile_id = absint( $_POST['profile_id'] );

		wp_send_json_success( UM()->User_Bookmarks()->profile()->get_user_profile_bookmarks( $profile_id ) );
	}


	/**
	 * Update bookmark folder details
	 */
	public function update_folder() {
		if ( empty( $_POST['_wpnonce'] ) || ! wp_verify_nonce( $_POST['_wpnonce'], 'um_user_bookmarks_update_folder' ) ) {
			wp_send_json_error( __( 'Invalid request', 'um-user-bookmarks' ) );
		}

		// sanitize input data
		$folder_slug = isset( $_POST['folder_key'] ) ? sanitize_title( $_POST['folder_key'] ) : '';
		$folder_name = isset( $_POST['folder_title'] ) ? sanitize_text_field( $_POST['folder_title'] ) : '';
		$is_private  = ! empty( $_POST['is_private'] );

		if ( empty( $folder_slug ) ) {
			wp_send_json_error( __( 'Invalid request', 'um-user-bookmarks' ) );
		}
		if ( empty( $folder_name ) ) {
			// translators: %s is a folder text
			wp_send_json_error( sprintf( __( '%s name is required', 'um-user-bookmarks' ), UM()->User_Bookmarks()->get_folder_text() ) );
		}
		$new_folder_slug = sanitize_title( $folder_name );

		$user_id        = get_current_user_id();
		$user_bookmarks = get_user_meta( $user_id , '_um_user_bookmarks', true );

		if ( ! isset( $user_bookmarks[ $folder_slug ] ) ) {
			// translators: %s is a folder text
			wp_send_json_error( sprintf( __( '%s doesn\'t exists', 'um-user-bookmarks' ), UM()->User_Bookmarks()->get_folder_text() ) );
		}
		if ( $new_folder_slug != $folder_slug && isset( $user_bookmarks[ $new_folder_slug ] ) ) {
			// translators: %s is a folder text
			wp_send_json_error( sprintf( __( '%s is already exists', 'um-user-bookmarks' ), UM()->User_Bookmarks()->get_folder_text() ) );
		}

		$type = $is_private ? 'private' : 'public';

		do_action( 'um_user_bookmarks_before_folder_update', $folder_slug );

		if ( $new_folder_slug != $folder_slug ) {
			$user_bookmarks[ $new_folder_slug ] = $user_bookmarks[ $folder_slug ];
			$user_bookmarks[ $new_folder_slug ]['title'] = $folder_name;
			$user_bookmarks[ $new_folder_slug ]['type'] = $type;
			unset( $user_bookmarks[ $folder_slug ] );
		} else {
			$user_bookmarks[ $folder_slug ]['title'] = $folder_name;
			$user_bookmarks[ $folder_slug ]['type'] = $type;
		}

		update_user_meta( $user_id, '_um_user_bookmarks', $user_bookmarks );

		do_action( 'um_user_bookmarks_after_folder_update', $folder_slug );

		wp_send_json_success( array( 'slug' => $new_folder_slug ) );
	}


	/**
	 * Default folder for folderless bookmarks
	 *
	 * @param null|int $user_id
	 */
	public function add_default_folder( $user_id = null ) {
		if ( ! $user_id ) {
			$user_id = get_current_user_id();
		}

		$previous_bookmark = get_user_meta( $user_id, '_um_user_bookmarks', true );
		if ( empty( $previous_bookmark ) || ! is_array( $previous_bookmark ) ) {
			$previous_bookmark            = array();
			$previous_bookmark['default'] = array(
				'title'     => __( 'Default', 'um-user-bookmarks' ),
				'type'      => 'public',
				'bookmarks' => array(),
			);
		}

		if ( ! isset( $previous_bookmark['default'] ) ) {
			$previous_bookmark['default'] = array(
				'title'     => __( 'Default', 'um-user-bookmarks' ),
				'type'      => 'public',
				'bookmarks' => array(),
			);
		}

		if ( is_array( $previous_bookmark ) && ! empty( $previous_bookmark ) ) {
			update_user_meta( $user_id, '_um_user_bookmarks', $previous_bookmark );
		}
	}


	public function same_page_update( $cb_func ) {
		if ( 'um_get_bookmarks_metadata' === $cb_func ) {
			global $wpdb;

			$count = $wpdb->get_var(
				"SELECT COUNT(*)
				FROM {$wpdb->usermeta}
				WHERE meta_key = '_um_user_bookmarks'"
			);

			update_option( 'um_user_bookmarks_disable_folders_update', time() );

			wp_send_json_success( array( 'count' => $count ) );
		} elseif ( 'um_update_bookmarks_metadata_single' === $cb_func ) {
			if ( empty( $_POST['page'] ) ) {
				wp_send_json_error( __( 'Wrong data', 'um-user-bookmarks' ) );
			}

			$per_page = 500;

			global $wpdb;
			$metadata = $wpdb->get_results(
				$wpdb->prepare(
					"SELECT *
						FROM {$wpdb->usermeta}
						WHERE meta_key = '_um_user_bookmarks'
						LIMIT %d, %d",
					( absint( $_POST['page'] ) - 1 ) * $per_page,
					$per_page
				),
				ARRAY_A
			);

			foreach ( $metadata as $metarow ) {
				$meta_value = maybe_unserialize( $metarow['meta_value'] );
				if ( empty( $meta_value ) ) {
					$this->add_default_folder( $metarow['user_id'] );
				} else {
					$all_bookmarks = array();
					foreach ( $meta_value as $slug => $folder_data ) {
						if ( array_key_exists( 'bookmarks', $folder_data ) && is_array( $folder_data['bookmarks'] ) ) {
							$all_bookmarks += $folder_data['bookmarks'];
						}
					}

					$structure = array(
						'default' => array(
							'title'     => __( 'Default', 'um-user-bookmarks' ),
							'type'      => 'public',
							'bookmarks' => $all_bookmarks,
						),
					);
					update_user_meta( $metarow['user_id'], $metarow['meta_key'], $structure );
				}
			}

			$from = ( absint( $_POST['page'] ) * $per_page ) - $per_page + 1;
			$to   = absint( $_POST['page'] ) * $per_page;

			// translators: %1$s is a "from" pagination, %2$s is a "to" pagination
			wp_send_json_success( array( 'message' => sprintf( __( 'Bookmarks metadata from %1$s to %2$s was upgraded successfully...', 'um-user-bookmarks' ), $from, $to ) ) );
		}
	}
}
