<?php
/**
 * Template for the users bookmarks
 *
 * Used:   Profile page > Bookmarks tab
 * Parent: profile/users-view.php
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-bookmarks/profile/users-view.php
 *
 * @see      https://docs.ultimatemember.com/article/1516-templates-map
 * @package  um_ext\um_user_bookmarks\templates
 * @version  2.1.3
 *
 * @var  array  $user_bookmarks
 * @var  int    $profile_id
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! empty( $user_bookmarks ) ) {
	foreach ( $user_bookmarks as $user_id => $url ) {
		?>
		<div class="um-bookmarks-profile" data-profile="<?php echo esc_attr( $user_id ); ?>">
			<a href="<?php echo esc_url( $url ); ?>"><?php echo get_avatar( $user_id, 40 ); ?></a>
			<a href="<?php echo esc_url( $url ); ?>"><?php echo wp_kses( um_user( 'display_name', 'html' ), UM()->get_allowed_html( 'templates' ) ); ?></a>
			<?php if ( um_profile_id() === get_current_user_id() ) { ?>
				<br>
				<a href="javascript:void(0);" class="um-user-bookmarks-button um-user-bookmarks-remove-button um-user-bookmarks-remove-profile"
				data-profile="<?php echo esc_attr( $user_id ); ?>"
				data-nonce="<?php echo esc_attr( wp_create_nonce( 'um_user_bookmarks_remove_' . $user_id ) ); ?>">
					<?php esc_html_e( 'Remove', 'um-user-bookmarks' ); ?>
				</a>
			<?php } ?>
		</div>
		<?php
	}
} else {
	esc_html_e( 'No bookmarks have been added.', 'um-user-bookmarks' );
}
