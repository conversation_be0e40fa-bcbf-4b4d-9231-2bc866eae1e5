<?php
/**
 * Template for the UM User Photos, the "New Album" modal content
 *
 * Page: "Profile", tab "Photos"
 * Caller: User_Photos_Ajax->load_new_album_modal() method
 * @version 2.1.9
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/modal/add-album.php
 * @var int        $count_user_photos
 * @var int        $limit_user_photos
 * @var int|string $limit_per_albums
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
?>

<div class="um-form">
	<form method="post" action="<?php echo esc_url( admin_url( 'admin-ajax.php?action=create_um_user_photos_album' ) ); ?>" enctype="multipart/form-data" class="um-user-photos-modal-form"  data-max_size_error="<?php esc_html_e( 'is too large. File should be less than ', 'um-user-photos' ); ?>" data-max_size="<?php echo esc_attr( wp_max_upload_size() ); ?>" data-limit="<?php echo esc_attr( $limit_user_photos ); ?>" data-count="<?php echo esc_attr( $count_user_photos ); ?>" data-count_album="0" data-album_limit="<?php echo esc_attr( $limit_per_albums ); ?>" data-max_upload="<?php echo esc_attr( ini_get( 'max_file_uploads' ) ); ?>">

		<div class="um-galley-form-response"></div>

		<?php if ( ! UM()->options()->get( 'um_user_photos_disable_title' ) ) { ?>
			<div class="um-field">
				<input type="text" name="title" placeholder="<?php esc_html_e( 'Album title', 'um-user-photos' ); ?>" required/>
			</div>
		<?php } ?>

		<div class="um-field">
			<?php if ( ! UM()->options()->get( 'um_user_photos_disable_cover' ) ) { ?>
				<input id="um-user-photos-cover-image" name="album_cover" type="hidden" value="0">
			<?php } ?>
			<div id="um-user-photos-images-uploaded" data-covertext="<?php echo esc_html__( 'Cover photo', 'um-user-photos' ); ?>"></div>
			<div class="um-clearfix"></div>
		</div>
		<div class="um-user-photos-error" <?php echo ( false !== $limit_user_photos && (int) $count_user_photos >= (int) $limit_user_photos ) ? '' : 'style="display:none;"'; ?>><?php esc_html_e( 'You cannot upload more photos, you have reached the limit of uploads. Delete old photos to add new ones.', 'um-user-photos' ); ?></div>
		<div class="um-user-photos-max-upload-error">
			<?php
				// translators: %s: User's display name.
				echo esc_html( sprintf( __( 'You cannot upload more than %s photos at one time. Please upload less photos or increase the max_file_uploads parameter in your PHP' ), ini_get( 'max_file_uploads' ) ) );
			?>
		</div>
		<div class="um-user-photos-max-size-upload-error">
			<?php
			// translators: %1$s: files size.
			echo esc_html( sprintf( __( 'You cannot upload more than %s photos size at one time. Please upload less photos or increase the post_max_size parameter in your PHP' ), ini_get( 'post_max_size' ) ) );
			?>
		</div>
		<div class="um-field um-user-photos-modal-footer text-right">
			<?php $privacy_options = apply_filters(
				'um_user_photos_privacy_options_dropdown',
				array(
					'everyone' => __( 'Everyone', 'um-user-photos' ),
					'only_me'  => __( 'Only me', 'um-user-photos' ),
				)
			); ?>

			<select class="um-form-field um-select2" name="user_photos_privacy">
				<?php foreach ( $privacy_options as $key => $label ) { ?>
					<option value="<?php echo esc_attr( $key ); ?>" <?php selected( 'everyone', $key ); ?>><?php echo esc_html( $label ); ?></option>
				<?php } ?>
			</select>
			<button type="button" class="um-modal-btn um-galley-modal-update"><?php esc_html_e( 'Publish', 'um-user-photos' ); ?></button>
			<label class="um-modal-btn alt" <?php echo ( false === $limit_user_photos || (int) $count_user_photos < (int) $limit_user_photos ) ? '' : 'style="display:none;"'; ?>>
				<i class="um-icon-plus"></i>
				<?php esc_html_e( 'Select photos', 'um-user-photos' ); ?>
				<input id="um-user-photos-input-album-images" style="display:none;" type="file" name="album_images[]" accept="image/*" multiple <?php echo ( false === $limit_user_photos || (int) $count_user_photos < (int) $limit_user_photos ) ? '' : 'disabled'; ?> />
			</label>
			<a href="javascript:void(0);" class="um-modal-btn alt um-user-photos-modal-close-link"><?php esc_html_e( 'Cancel', 'um-user-photos' ); ?></a>
		</div>
		<?php wp_nonce_field( 'um_add_album' ); ?>
	</form>
</div>
