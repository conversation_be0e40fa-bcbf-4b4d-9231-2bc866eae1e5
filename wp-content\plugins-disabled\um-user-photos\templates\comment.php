<?php
/**
 * Template for the UM User Photos. The comment block
 *
 * Call:  UM()->User_Photos()->ajax()->um_user_photos_post_comment()
 * Page: "Profile", tab "Photos", the image popup
 * Parent template: comments.php
 * @version 2.2.0
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-user-photos/comment.php
 * @var int        $user_id
 * @var int        $id
 * @var int        $image_id
 * @var string     $content
 * @var WP_Comment $photo_comment
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

$comment_date     = get_comment_time( get_option( 'date_format', 'F j, Y' ) . ' ' . get_option( 'time_format', 'g:i a' ), false, true, $id );
$comment_date_gmt = get_comment_time( get_option( 'date_format', 'F j, Y' ) . ' ' . get_option( 'time_format', 'g:i a' ), true, true, $id );

um_fetch_user( $user_id );
$avatar = get_avatar( um_user( 'ID' ), 80 );

$has_liked = false;
$likes     = UM()->User_Photos()->common()->photo()->get_comment_likes( $id );
if ( ! $likes ) {
	$likes = array();
}

if ( in_array( get_current_user_id(), $likes, true ) ) {
	$has_liked = true;
}
$likes_count = count( $likes );
?>
<div class="um-user-photos-commentwrap" data-comment_id="<?php echo esc_attr( $id ); ?>">
	<div class="um-user-photos-commentl" id="commentid-<?php echo esc_attr( $id ); ?>">

		<a href="javascript:void(0);" class="um-user-photos-comment-hide um-tip-s">
			<i class="um-icon-close-round"></i>
		</a>

		<div class="um-user-photos-comment-avatar hidden-0">
			<a href="<?php echo esc_url( um_user_profile_url() ); ?>"><?php echo wp_kses( $avatar, UM()->get_allowed_html( 'templates' ) ); ?></a>
		</div>

		<div class="um-user-photos-comment-hidden hidden-0">
			<?php esc_html_e( 'Comment hidden.', 'um-user-photos' ); ?>&nbsp;
			<a href="javascript:void(0);" class="um-link"><?php esc_html_e( 'Show this comment', 'um-user-photos' ); ?></a>.
		</div>

		<div class="um-user-photos-comment-info hidden-0">

			<div class="um-user-photos-comment-data">
				<span class="um-user-photos-comment-author-link">
					<a href="<?php echo esc_url( um_user_profile_url() ); ?>" class="um-link">
						<strong><?php echo esc_html( um_user( 'display_name' ) ); ?></strong>
					</a>
				</span>
				<span class="um-user-photos-comment-text"><?php echo wp_kses( $content, UM()->get_allowed_html( 'templates' ) ); ?></span>
			</div>

			<!-- comment meta-->
			<div class="um-user-photos-comment-meta">
				<?php
				if ( is_user_logged_in() ) {
					$likenonce   = wp_create_nonce( 'um_user_photos_comment_like' );
					$unlikenonce = wp_create_nonce( 'um_user_photos_comment_unlike' );
					if ( $has_liked ) {
						?>
						<span><a data-likenonce="<?php echo esc_attr( $likenonce ); ?>" data-unlikenonce="<?php echo esc_attr( $unlikenonce ); ?>" data-id="<?php echo esc_attr( $id ); ?>" href="javascript:void(0);" class="um-link um-user-photos-comment-like active" data-like_text="<?php esc_html_e( 'Like', 'um-user-photos' ); ?>" data-unlike_text="<?php esc_html_e( 'Unlike', 'um-user-photos' ); ?>"><?php esc_html_e( 'Unlike', 'um-user-photos' ); ?></a></span>
					<?php } else { ?>
						<span><a data-likenonce="<?php echo esc_attr( $likenonce ); ?>" data-unlikenonce="<?php echo esc_attr( $unlikenonce ); ?>" data-id="<?php echo esc_attr( $id ); ?>" href="javascript:void(0);" class="um-link um-user-photos-comment-like" data-like_text="<?php esc_html_e( 'Like', 'um-user-photos' ); ?>" data-unlike_text="<?php esc_html_e( 'Unlike', 'um-user-photos' ); ?>"><?php esc_html_e( 'Like', 'um-user-photos' ); ?></a></span>
					<?php } ?>

					<span class="um-user-photos-comment-likes count-<?php echo esc_attr( count( $likes ) ); ?>">
						<a href="javascript:void(0);" data-id="<?php echo esc_attr( $id ); ?>" data-modal_title="<?php esc_attr_e( 'Likes', 'um-user-photos' ); ?>" data-wpnonce="<?php echo esc_attr( wp_create_nonce( 'um_user_photos_get_comment_likes' ) ); ?>" >
							<i class="um-faicon-thumbs-up"></i>

							<ins class="um-user-photos-ajaxdata-commentlikes">
							<?php echo esc_html( count( $likes ) ); ?>
							</ins>
						</a>
					</span>
				<?php } ?>

				<span>
					<a href="javascript:void(0);" class="um-user-photos-comment-permalink" title="<?php echo esc_attr( $comment_date ); ?>">
						<?php echo esc_html( UM()->datetime()->time_diff( strtotime( $comment_date_gmt ) ) ); ?>
					</a>
				</span>

				<?php if ( is_user_logged_in() && UM()->User_Photos()->common()->user()->can_edit_comment( $id, get_current_user_id() ) ) { ?>
					<span class="um-user-photos-editc"><a href="javascript:void(0);"><i class="um-icon-edit"></i></a>
						<span class="um-user-photos-editc-d">
							<?php if ( UM()->User_Photos()->common()->user()->is_comment_author( $id, get_current_user_id() ) ) { ?>
								<a href="javascript:void(0);"
									class="edit"
									data-wpnonce="<?php echo esc_attr( wp_create_nonce( 'um_user_photos_comment_edit' ) ); ?>"
									data-modal_title="<?php esc_html_e( 'Edit comment', 'um-user-photos' ); ?>"
									data-template="modal/edit-comment"
									data-commentid="<?php echo esc_attr( $id ); ?>">
									<?php esc_html_e( 'Edit', 'um-user-photos' ); ?>
								</a>
							<?php } ?>

							<a href="javascript:void(0);" class="delete" data-wpnonce="<?php echo esc_attr( wp_create_nonce( 'get_um_user_photos_comment_delete' ) ); ?>" data-modal_title="<?php esc_html_e( 'Delete comment', 'um-user-photos' ); ?>" data-commentid="<?php echo esc_attr( $id ); ?>" data-msg="<?php esc_html_e( 'Are you sure you want to delete this comment?', 'um-user-photos' ); ?>"><?php esc_html_e( 'Delete', 'um-user-photos' ); ?></a>
						</span>
					</span>
				<?php } ?>
			</div>
			<!-- comment meta-->
		</div>
	</div>
</div>
