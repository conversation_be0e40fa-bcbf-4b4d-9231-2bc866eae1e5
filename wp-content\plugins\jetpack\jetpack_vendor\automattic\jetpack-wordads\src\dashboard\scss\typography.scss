// ==========================================================================
// TYPOGRAPHY
// ==========================================================================

$normal: 400; // Change these values when using custom fonts
$bold: 700; // For example, bold could change to 400;

// Typefaces
$monospace: Monaco, Consolas, 'courier new', "Andale Mono", monospace;
$serif: Georgia, "Times New Roman", Times, serif;
$sans: Helvetica, Arial, sans-serif;
$open-sans: 'Open Sans', $sans; // 300 400 400i 600 700 700i 800
$merriweather: 'Merriweather', $serif; // 900 700
$calluna: "calluna-1","calluna-2", Georgia, "Times New Roman", Times, serif;
$helvetica: "Helvetica Neue", $sans;
$helvetica-ultralight: HelveticaNeue-UltraLight, 'Helvetica Neue UltraLight', 'Helvetica Neue', $sans;
$helvetica-light: HelveticaNeue-Light, 'Helvetica Neue Light', 'Helvetica Neue', $sans;
$arial: Arial, Helvetica, Sans-serif;
$gill-sans: "Gill Sans", "Gill Sans MT", "Trebuchet MS", $sans;

// Icons
$genericons: 'Genericons';
$dashicons: 'dashicons';
$noticons: 'Noticons';
$automatticons: 'automatticons';

// NOTE:
// If there are exceptions to these stacks,
// please mark them with a //typography-exception comment
// so we can easily search for them later.

// Typography size variables

$font-headline-large: rem( 54px );
$font-headline-medium: rem( 48px );
$font-headline-small: rem( 36px );
$font-title-large: rem( 32px );
$font-title-medium: rem( 24px );
$font-title-small: rem( 20px );
$font-body: rem( 16px );
$font-body-small: rem( 14px );
$font-body-extra-small: rem( 12px );
$font-code: rem( 15px );
