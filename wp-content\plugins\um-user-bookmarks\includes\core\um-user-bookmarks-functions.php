<?php if ( ! defined( 'ABSPATH' ) ) {
	exit;
}


if ( ! class_exists( 'UM_User_Bookmarks_Functions' ) ) {


	/**
	 * Class UM_User_Bookmarks_Functions
	 */
	class UM_User_Bookmarks_Functions {


		/**
		 * UM_User_Bookmarks_Functions constructor.
		 */
		public function __construct() {
		}


		/**
		 * Check if bookmark is disabled for post
		 *
		 * @param $post_id
		 *
		 * @return bool
		 */
		public function is_post_disabled( $post_id ) {
			$post_settings = get_post_meta( $post_id, '_um_user_bookmarks', true );

			if ( ! empty( $post_settings ) ) {
				if ( ! empty( $post_settings['disable_bookmark'] ) ) {
					return true;
				}
			}

			return false;
		}


		/**
		 * Check if post is saved in user bookmarks
		 *
		 * @param int $user_id
		 * @param int $post_id
		 *
		 * @return bool
		 */
		public function is_bookmarked( $user_id, $post_id = null, $is_profile = false ) {
			if ( $is_profile ) {
				$bookmarks = get_user_meta( get_current_user_id(), '_um_user_bookmarks_profiles', true );
				if ( ! empty( $bookmarks ) && array_key_exists( $user_id, $bookmarks ) ) {
					return true;
				}
			} else {
				$user_bookmarks = get_user_meta( $user_id, '_um_user_bookmarks', true );

				if ( ! empty( $user_bookmarks ) ) {

					foreach ( $user_bookmarks as $key => $value ) {

						if ( ! empty( $value['bookmarks'] ) && isset( $value['bookmarks'][ $post_id ] ) ) {

							return true;

						}
					}
				}
			}

			return false;
		}


		/**
		 * Retrives Add / Remove Bookmark Button for post type single page
		 *
		 * @param $button_type
		 * @param null $post_id
		 *
		 * @return false|string
		 */
		public function get_button( $button_type, $post_id = null, $is_profile = false, $profile_id = null ) {
			if ( ! $is_profile && ! $post_id ) {
				global $post;
				$post_id = $post->ID;
			}

			ob_start();

			$args = array();
			if ( $is_profile ) {
				if ( ! empty( $profile_id ) ) {
					$args['profile_id'] = $profile_id;
				} else {
					$args['profile_id'] = um_profile_id();
				}
			} else {
				$args['post_id'] = $post_id;
			}
			if ( 'add' === $button_type ) {
				$args['icon'] = UM()->options()->get( 'um_user_bookmarks_regular_icon' );
				$args['text'] = UM()->options()->get( 'um_user_bookmarks_add_text' );

				$button_args = apply_filters( 'um_bookmarks_add_button_args', $args );

				// add bookmark button.
				UM()->get_template( 'buttons/add.php', um_user_bookmarks_plugin, $button_args, true );

			} elseif ( 'remove' === $button_type ) {
				$args['icon'] = UM()->options()->get( 'um_user_bookmarks_bookmarked_icon' );
				$args['text'] = UM()->options()->get( 'um_user_bookmarks_remove_text' );

				$button_args = apply_filters( 'um_bookmarks_remove_button_args', $args );

				// remove bookmark button.
				UM()->get_template( 'buttons/remove.php', um_user_bookmarks_plugin, $button_args, true );
			}

			$content = ob_get_clean();
			return trim( $content );
		}


		/**
		 * Check if user can view bookmakrs
		 *
		 * @param null|int $profile_id
		 *
		 * @return bool
		 */
		public function user_can_view_bookmark( $profile_id = null ) {
			if ( ! $profile_id ) {
				return false;
			}

			$profile_id = absint( $profile_id );

			$user_id = get_current_user_id();
			if ( ! um_user( 'enable_bookmark' ) ) {
				return false;
			}

			$privacy = get_user_meta( $profile_id, 'um_bookmark_privacy', true );
			if ( 'everyone' === $privacy || $profile_id === $user_id ) {
				return true;
			}

			if ( 'only_me' === $privacy ) {
				return false;
			}

			$custom_privacy = apply_filters( 'um_user_bookmarks_custom_privacy', true, $privacy, $user_id );
			return $custom_privacy;
		}


		/**
		 * Returns text from admin settings
		 *
		 * @param bool $is_plural
		 *
		 * @return string
		 */
		public function get_folder_text( $is_plural = false, $is_profile = false ) {
			if ( $is_profile ) {
				$key = $is_plural ? 'um_user_bookmarks_profile_folders_text' : 'um_user_bookmarks_profile_folder_text';
			} else {
				$key = $is_plural ? 'um_user_bookmarks_folders_text' : 'um_user_bookmarks_folder_text';
			}

			$default_map = array(
				'um_user_bookmarks_folders_text'         => __( 'Folders', 'um-user-bookmarks' ),
				'um_user_bookmarks_folder_text'          => __( 'Folder', 'um-user-bookmarks' ),
				'um_user_bookmarks_profile_folders_text' => __( 'Users', 'um-user-bookmarks' ),
				'um_user_bookmarks_profile_folder_text'  => __( 'User', 'um-user-bookmarks' ),
			);

			$option_value = UM()->options()->get( $key );
			if ( empty( $option_value ) ) {
				$option_value = $default_map[ $key ];
			}

			return $option_value;
		}
	}
}
