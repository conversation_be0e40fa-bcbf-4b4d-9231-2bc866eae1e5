jQuery((function(t){if("undefined"!=typeof jetpack_empty_spam_button_parameters){const e=t("<div/>").addClass("jetpack-empty-spam-container"),a=t("<a />").addClass("button-secondary").addClass("jetpack-empty-spam").attr("href","#").attr("data-progress-label",jetpack_empty_spam_button_parameters.progress_label).attr("data-success-url",jetpack_empty_spam_button_parameters.success_url).attr("data-failure-url",jetpack_empty_spam_button_parameters.failure_url).attr("data-spam-feedbacks-count",jetpack_empty_spam_button_parameters.spam_count).attr("data-nonce",jetpack_empty_spam_button_parameters.nonce).text(jetpack_empty_spam_button_parameters.label);e.append(a);const n=t("<span />").addClass("jetpack-empty-spam-spinner");e.append(n),t(".tablenav.top .actions, .tablenav.bottom .actions").not(".bulkactions").append(e)}function e(a,n){const s=t("#jetpack-check-feedback-spam").data("nonce-name"),o=t("#"+s).attr("value"),c=t("#jetpack-check-feedback-spam").data("failure-url"),r={action:"grunion_recheck_queue",offset:a,limit:n};r[s]=o,t.post(ajaxurl,r).fail((function(){window.location.href=c})).done((function(t){t.processed<n?window.location.reload():e(a+n,n)}))}t(document).on("click","#jetpack-check-feedback-spam:not(.button-disabled)",(function(a){a.preventDefault(),t("#jetpack-check-feedback-spam:not(.button-disabled)").addClass("button-disabled"),t(".jetpack-check-feedback-spam-spinner").addClass("spinner").show(),e(0,100)}));let a=0,n=0;function s(){const e=t(".jetpack-empty-spam"),o=e.data("nonce"),c=Math.round(n/a*1e3)/10;e.text(e.data("progress-label").replace("%1$s",c).replace("%%","%")),t.post(ajaxurl,{action:"jetpack_delete_spam_feedbacks",nonce:o}).fail((function(){window.location.href=e.data("failure-url")})).done((function(t){n+=t.data.counts.deleted,t.data.counts.deleted<t.data.counts.limit?window.location.href=e.data("success-url"):s()}))}t(document).on("click",".jetpack-empty-spam",(function(e){e.preventDefault(),t(this).hasClass("button-disabled")||(t(".jetpack-empty-spam").addClass("button-disabled").addClass("emptying"),t(".jetpack-empty-spam-spinner").addClass("spinner").addClass("is-active"),t(".jetpack-empty-spam").text(t(".jetpack-empty-spam").data("progress-label").replace("%1$s","0").replace("%%","%")),a=parseInt(t(this).data("spam-feedbacks-count"),10),s())})),t(document).ready((function(){function e(e,a,n){t.post(ajaxurl,{action:"grunion_ajax_spam",post_id:e,make_it:a,sub_menu:jQuery(".subsubsub .current").attr("href"),_ajax_nonce:window.__grunionPostStatusNonce},(function(a){t("#post-"+e).css({backgroundColor:n}).fadeOut(350,(function(){t(this).remove(),t(".subsubsub").html(a)}))}))}t("tr.type-feedback .row-actions a").click((function(a){a.preventDefault();const n=t(a.target).closest("tr.type-feedback").attr("id").match(/^post-(\d+)/);if(!n)return;const s=parseInt(n[1],10);t(a.target).parent().hasClass("spam")&&(a.preventDefault(),e(s,"spam","#FF7979")),t(a.target).parent().hasClass("trash")&&(a.preventDefault(),e(s,"trash","#FF7979")),t(a.target).parent().hasClass("unspam")&&(a.preventDefault(),e(s,"ham","#59C859")),t(a.target).parent().hasClass("untrash")&&(a.preventDefault(),e(s,"publish","#59C859")),t(a.target).parent().hasClass("delete")&&(a.preventDefault(),e(s,"delete","#FF7979"))}))})),t(document).on("click","#jetpack-form-responses-connect",(function(e){const a=t(this),n=a.data("nonce-name"),s=t("#"+n).attr("value"),o=a.attr("href");if(e.preventDefault(),a.attr("disabled","disabled"),a.text(window.exportParameters&&window.exportParameters.waitingConnection||"Waiting for connection..."),"undefined"!=typeof analytics&&"undefined"!=typeof jetpack_forms_tracking&&jetpack_forms_tracking?.tracksUserData){const t=jetpack_forms_tracking.tracksUserData;analytics.initialize(t.userid,t.username),analytics.tracks.recordEvent("jetpack_forms_upsell_googledrive_click",{screen:"form-responses-classic"})}!function({name:e,value:a}){let n=!1,s=null;const o=setInterval((function(){n||t.post(ajaxurl,{action:"grunion_gdrive_connection",[e]:a},(function(e){e&&e.connection&&e.html&&(clearInterval(o),n=!0,s=t(e.html),t("#jetpack-form-responses-connect").replaceWith(s))})).fail((function(){clearInterval(o)}))}),5e3)}({name:n,value:s}),window.open(o,"_blank")})),t(document).on("click","#jetpack-export-feedback-gdrive",(function(e){e.preventDefault();const a=t(e.target),n=a.data("nonce-name"),s=t("#"+n).attr("value"),o=window.location.search.match(/[?&]m=(\d+)/),c=window.location.search.match(/[?&]jetpack_form_parent_id=(\d+)/),r=[];t('#posts-filter .check-column input[type="checkbox"]:checked').each((function(){r.push(parseInt(t(this).attr("value"),10))}));const p=window.exportParameters&&window.exportParameters.exportError||"There was an error exporting your results";a.attr("disabled","disabled"),t.post(ajaxurl,{action:"grunion_export_to_gdrive",year:o?o[1].substr(0,4):"",month:o?o[1].substr(4,2):"",post:c?parseInt(c[1],10):"all",selected:r,[n]:s},(function(t,e){"success"===e&&t.data&&t.data.sheet_link&&window.open(t.data.sheet_link,"_blank")})).fail((function(){window.alert(p)})).always((function(){a.removeAttr("disabled")}))})),t(document).on("click","#jetpack-export-feedback-csv",(function(e){e.preventDefault();const a=t(e.target).data("nonce-name"),n=t("#"+a).attr("value"),s=window.location.search.match(/[?&]m=(\d+)/),o=window.location.search.match(/[?&]jetpack_form_parent_id=(\d+)/),c=[];t('#posts-filter .check-column input[type="checkbox"]:checked').each((function(){c.push(parseInt(t(this).attr("value"),10))})),t.post(ajaxurl,{action:"feedback_export",year:s?s[1].substr(0,4):"",month:s?s[1].substr(4,2):"",post:o?parseInt(o[1],10):"all",selected:c,[a]:n},(function(t,e,a){const n=new Blob([t],{type:"application/octetstream"}),s=document.createElement("a");s.href=window.URL.createObjectURL(n);var o=a.getResponseHeader("content-disposition");s.download=o.split("filename=")[1]||"Jetpack Form Responses.csv",document.body.appendChild(s),s.click(),document.body.removeChild(s),window.URL.revokeObjectURL(s.href)}))})),t(document).on("click","#export-modal-opener",(function(e){const a=t(this);e.preventDefault(),window.tb_show(a.html(),a.attr("href"))}))}));