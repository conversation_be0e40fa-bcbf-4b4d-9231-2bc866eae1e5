jQuery(document).ready(function(n){n(document.body).on("click","#um_user_photos_delete_all",function(e){e.preventDefault();let o=n(this);var t;o.hasClass("inactive")||(e=o.data("nonce"),t=o.data("alert_message"),confirm(t)&&(o.addClass("inactive").text(wp.i18n.__("Processing...Please wait.","um-user-photos")),wp.ajax.send("um_user_photos_flush_albums",{data:{_wpnonce:e},success:function(e){o.parent("p").append('<font style="color:green;display:block;text-align:center;">Deleted Successfully !</font>'),o.remove(),n("#um_user_photos_download_all").remove()},error:function(e){console.log(e)}})))})});