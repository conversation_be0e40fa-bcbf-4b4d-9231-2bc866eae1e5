<?php
namespace um_ext\um_user_photos\common;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class User
 *
 * @package um_ext\um_user_photos\common
 */
class User {

	/**
	 * User constructor.
	 */
	public function __construct() {
	}

	public function hooks() {
		// Delete all user photos on user delete.
		add_action( 'um_delete_user', array( $this, 'delete_photos' ) );
	}

	/**
	 * Delete all user photos.
	 *
	 * @since 2.1.1
	 *
	 * @param int $user_id User ID.
	 */
	public function delete_photos( $user_id ) {
		$user_photos = get_posts(
			array(
				'fields'      => 'ids',
				'author'      => $user_id,
				'post_type'   => 'um_user_photos',
				'numberposts' => -1,
			)
		);

		foreach ( $user_photos as $post_id ) {
			wp_delete_post( $post_id, true );
		}
	}

	/**
	 * @return array
	 */
	public function get_blocked_users() {
		static $blocked_users = null;

		if ( ! is_null( $blocked_users ) ) {
			return $blocked_users;
		}

		if ( current_user_can( 'manage_options' ) ) {
			$blocked_users = array();
		} else {
			$all_users = get_users(
				array(
					'fields' => 'ids',
				)
			);

			foreach ( $all_users as $user_id ) {
				if ( ! um_can_view_profile( $user_id ) ) {
					$blocked_users[] = absint( $user_id );
				}
			}
		}

		return $blocked_users;
	}

	/**
	 * @param int $user_id
	 *
	 * @return int|bool
	 */
	public function photos_limit( $user_id ) {
		$role      = UM()->roles()->get_priority_user_role( $user_id );
		$role_data = UM()->roles()->role_data( $role );

		return ! empty( $role_data['limit_user_photos'] ) ? absint( $role_data['limit_user_photos'] ) : false;
	}

	/**
	 * Get user photos count.
	 * @param int $user_id
	 *
	 * @return int
	 */
	public function photos_count( $user_id ) {
		$albums = get_posts(
			array(
				'numberposts' => -1,
				'meta_key'    => '_photos',
				'post_type'   => 'um_user_photos',
				'author'      => $user_id,
				'fields'      => 'ids',
			)
		);

		$count = 0;
		foreach ( $albums as $album_id ) {
			$album_photos_array = get_post_meta( $album_id, '_photos', true );
			$album_photos_array = ! empty( $album_photos_array ) && is_array( $album_photos_array ) ? $album_photos_array : array();

			$count += count( $album_photos_array );
		}

		return $count;
	}

	/**
	 * @param int      $post_id
	 * @param null|int $user_id
	 *
	 * @return bool
	 */
	public function is_post_author( $post_id, $user_id = null ) {
		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		if ( empty( $user_id ) ) {
			return false;
		}

		$author_id = get_post_field( 'post_author', $post_id );
		if ( ! $author_id ) {
			return false;
		}

		$is_author = absint( $author_id ) === absint( $user_id );
		return apply_filters( 'um_user_photos_is_post_author', $is_author, $post_id, $user_id );
	}

	/**
	 * @since 2.2.0
	 *
	 * @param int $comment_id
	 * @param int $user_id
	 *
	 * @return bool
	 */
	public function is_comment_author( $comment_id, $user_id = null ) {
		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		if ( empty( $user_id ) ) {
			return false;
		}

		$comment = get_comment( $comment_id );
		if ( empty( $comment ) ) {
			return false;
		}

		$is_author = absint( $comment->user_id ) === absint( $user_id );
		return apply_filters( 'um_user_photos_is_comment_author', $is_author, $comment_id, $user_id );
	}

	public function can_add_album( $user_id = null ) {
		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		$can_add = false;

		$user_role      = UM()->roles()->get_priority_user_role( $user_id );
		$user_role_data = UM()->roles()->role_data( $user_role );
		if ( ! empty( $user_role_data['enable_user_photos'] ) ) {
			$can_add = true;
		}

		return apply_filters( 'um_user_photos_can_add_album', $can_add, $user_id );
	}

	/**
	 * @param int      $post_id
	 * @param null|int $user_id
	 *
	 * @return bool
	 */
	public function can_view_album( $post_id, $user_id = null ) {
		// return `false` if doesn't exist.
		if ( ! UM()->User_Photos()->common()->album()->exists( $post_id ) ) {
			return false;
		}

		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		$can_view = false;
		$album    = get_post( $post_id );

		$user_role      = UM()->roles()->get_priority_user_role( $album->post_author );
		$user_role_data = UM()->roles()->role_data( $user_role );
		if ( ! empty( $user_role_data['enable_user_photos'] ) ) {
			$is_author = $this->is_post_author( $post_id, $user_id );

			if ( $is_author ) {
				$can_view = true;
			} elseif ( ! empty( $album->post_author ) && um_can_view_profile( $album->post_author ) && UM()->User_Photos()->common()->album()->is_published( $post_id ) ) {
				$privacy = get_post_meta( $post_id, '_privacy', true );
				if ( 'everyone' === $privacy ) {
					$can_view = true;
				}
			}
		}

		return apply_filters( 'um_user_photos_can_view_album', $can_view, $post_id, $user_id );
	}

	/**
	 * Only album author can edit his album.
	 * @param int      $post_id
	 * @param null|int $user_id
	 *
	 * @return bool
	 */
	public function can_edit_album( $post_id, $user_id = null ) {
		// return `false` if doesn't exist.
		if ( ! UM()->User_Photos()->common()->album()->exists( $post_id ) ) {
			return false;
		}

		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		$can_edit       = false;
		$album          = get_post( $post_id );
		$user_role      = UM()->roles()->get_priority_user_role( $album->post_author );
		$user_role_data = UM()->roles()->role_data( $user_role );
		if ( ! empty( $user_role_data['enable_user_photos'] ) ) {
			$can_edit = $this->is_post_author( $post_id, $user_id );
		}
		return apply_filters( 'um_user_photos_can_edit_album', $can_edit, $post_id, $user_id );
	}

	/**
	 * Only album author can delete his album.
	 * @param int      $post_id
	 * @param null|int $user_id
	 *
	 * @return bool
	 */
	public function can_delete_album( $post_id, $user_id = null ) {
		// return `false` if doesn't exist.
		if ( ! UM()->User_Photos()->common()->album()->exists( $post_id ) ) {
			return false;
		}

		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		$can_delete     = false;
		$album          = get_post( $post_id );
		$user_role      = UM()->roles()->get_priority_user_role( $album->post_author );
		$user_role_data = UM()->roles()->role_data( $user_role );
		if ( ! empty( $user_role_data['enable_user_photos'] ) ) {
			$can_delete = $this->is_post_author( $post_id, $user_id );
		}
		return apply_filters( 'um_user_photos_can_delete_album', $can_delete, $post_id, $user_id );
	}

	/**
	 * @param int      $post_id
	 * @param null|int $user_id
	 *
	 * @return bool
	 */
	public function can_view_photo( $post_id, $user_id = null ) {
		// return `false` if doesn't exist.
		if ( ! UM()->User_Photos()->common()->photo()->exists( $post_id ) ) {
			return false;
		}

		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		$can_view = false;
		$photo    = get_post( $post_id );

		$user_role      = UM()->roles()->get_priority_user_role( $photo->post_author );
		$user_role_data = UM()->roles()->role_data( $user_role );
		if ( ! empty( $user_role_data['enable_user_photos'] ) ) {
			$is_author = $this->is_post_author( $post_id, $user_id );

			if ( $is_author ) {
				$can_view = true;
			} elseif ( ! empty( $photo->post_author ) && um_can_view_profile( $photo->post_author ) && UM()->User_Photos()->common()->photo()->is_published( $post_id ) ) {
				$album_id = get_post_field( 'post_parent', $post_id );
				if ( ! empty( $album_id ) ) {
					$can_view = $this->can_view_album( $album_id, $user_id );
				}
			}
		}

		return apply_filters( 'um_user_photos_can_view_photo', $can_view, $post_id, $user_id );
	}

	/**
	 * Only album author can edit his photo.
	 * @param int      $post_id
	 * @param null|int $user_id
	 *
	 * @return bool
	 */
	public function can_edit_photo( $post_id, $user_id = null ) {
		// return `false` if doesn't exist.
		if ( ! UM()->User_Photos()->common()->photo()->exists( $post_id ) ) {
			return false;
		}

		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		$can_edit       = false;
		$photo          = get_post( $post_id );
		$user_role      = UM()->roles()->get_priority_user_role( $photo->post_author );
		$user_role_data = UM()->roles()->role_data( $user_role );
		if ( ! empty( $user_role_data['enable_user_photos'] ) ) {
			$can_edit = $this->is_post_author( $post_id, $user_id );
		}
		return apply_filters( 'um_user_photos_can_edit_photo', $can_edit, $post_id, $user_id );
	}

	/**
	 * Only album author can delete his album.
	 * @param int      $post_id
	 * @param null|int $user_id
	 *
	 * @return bool
	 */
	public function can_delete_photo( $post_id, $user_id = null ) {
		// return `false` if doesn't exist.
		if ( ! UM()->User_Photos()->common()->photo()->exists( $post_id ) ) {
			return false;
		}

		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		$can_delete     = false;
		$photo          = get_post( $post_id );
		$user_role      = UM()->roles()->get_priority_user_role( $photo->post_author );
		$user_role_data = UM()->roles()->role_data( $user_role );
		if ( ! empty( $user_role_data['enable_user_photos'] ) ) {
			$can_delete = $this->is_post_author( $post_id, $user_id );
		}
		return apply_filters( 'um_user_photos_can_delete_photo', $can_delete, $post_id, $user_id );
	}

	/**
	 * @since 2.2.0
	 *
	 * @param int $comment_id
	 * @param int $user_id
	 *
	 * @return bool
	 */
	public function can_view_comment( $comment_id, $user_id = null ) {
		// return `false` if doesn't exist.
		if ( ! UM()->User_Photos()->common()->photo()->comment_exists( $comment_id ) ) {
			return false;
		}

		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		$can_view = false;

		$disabled_comments_likes = UM()->options()->get( 'um_user_photos_disable_comments' );
		if ( ! $disabled_comments_likes ) {
			$comment = get_comment( $comment_id );

			$user_role      = UM()->roles()->get_priority_user_role( $comment->user_id );
			$user_role_data = UM()->roles()->role_data( $user_role );

			if ( ! empty( $user_role_data['enable_user_photos'] ) ) {
				$is_author = $this->is_comment_author( $comment_id, $user_id );
				if ( $is_author ) {
					$can_view = true;
				} elseif ( ! empty( $comment->user_id ) && um_can_view_profile( $comment->user_id ) && UM()->User_Photos()->common()->photo()->is_comment_approved( $comment ) ) {
					$can_view_photo = $this->can_view_photo( $comment->comment_post_ID );
					if ( $can_view_photo ) {
						$can_view = true;
					}
				}
			}
		}

		return apply_filters( 'um_user_photos_can_view_comment', $can_view, $comment_id, $user_id );
	}

	/**
	 * @since 2.2.0
	 *
	 * @return bool
	 */
	public function can_comment( $post_id, $user_id = null ) {
		// return `false` if doesn't exist.
		if ( ! UM()->User_Photos()->common()->photo()->exists( $post_id ) ) {
			return false;
		}

		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		$disabled_comments_likes = UM()->options()->get( 'um_user_photos_disable_comments' );
		if ( $disabled_comments_likes ) {
			$can_comment = false;
		} else {
			$can_comment = UM()->User_Photos()->common()->user()->can_view_photo( $post_id, $user_id );
		}

		return apply_filters( 'um_user_photos_can_comment', $can_comment, $post_id, $user_id );
	}

	/**
	 * @since 2.2.0
	 *
	 * @param int $comment_id
	 * @param int $user_id
	 *
	 * @return bool
	 */
	public function can_edit_comment( $comment_id, $user_id = null ) {
		// return `false` if doesn't exist.
		if ( ! UM()->User_Photos()->common()->photo()->comment_exists( $comment_id ) ) {
			return false;
		}

		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		$disabled_comments_likes = UM()->options()->get( 'um_user_photos_disable_comments' );
		if ( $disabled_comments_likes ) {
			$can_edit = false;
		} else {
			$can_edit = $this->is_comment_author( $comment_id, $user_id );
			if ( $can_edit ) {
				$user_role      = UM()->roles()->get_priority_user_role( $user_id );
				$user_role_data = UM()->roles()->role_data( $user_role );
				if ( empty( $user_role_data['enable_user_photos'] ) ) {
					$can_edit = false;
				}
			}
		}
		return apply_filters( 'um_user_photos_can_edit_comment', $can_edit, $comment_id, $user_id );
	}

	/**
	 * @since 2.2.0
	 *
	 * @param int $comment_id
	 * @param int $user_id
	 *
	 * @return bool
	 */
	public function can_delete_comment( $comment_id, $user_id = null ) {
		// return `false` if doesn't exist.
		if ( ! UM()->User_Photos()->common()->photo()->comment_exists( $comment_id ) ) {
			return false;
		}

		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		$disabled_comments_likes = UM()->options()->get( 'um_user_photos_disable_comments' );
		if ( $disabled_comments_likes ) {
			$can_delete = false;
		} else {
			$can_delete = $this->is_comment_author( $comment_id, $user_id );
			if ( $can_delete ) {
				$user_role      = UM()->roles()->get_priority_user_role( $user_id );
				$user_role_data = UM()->roles()->role_data( $user_role );
				if ( empty( $user_role_data['enable_user_photos'] ) ) {
					$can_delete = false;
				}
			}
		}

		return apply_filters( 'um_user_photos_can_delete_comment', $can_delete, $comment_id, $user_id );
	}

	/**
	 * @since 2.2.0
	 *
	 * @return bool
	 */
	public function can_view_photo_likes( $post_id, $user_id = null ) {
		// return `false` if doesn't exist.
		if ( ! UM()->User_Photos()->common()->photo()->exists( $post_id ) ) {
			return false;
		}

		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		$disabled_comments_likes = UM()->options()->get( 'um_user_photos_disable_comments' );
		if ( $disabled_comments_likes ) {
			$can_view_likes = false;
		} else {
			$can_view_likes = $this->can_view_photo( $post_id, $user_id );
		}

		return apply_filters( 'um_user_photos_can_view_photo_likes', $can_view_likes, $post_id, $user_id );
	}

	/**
	 * @since 2.2.0
	 *
	 * @return bool
	 */
	public function can_like_photo( $post_id, $user_id = null ) {
		// return `false` if doesn't exist.
		if ( ! UM()->User_Photos()->common()->photo()->exists( $post_id ) ) {
			return false;
		}

		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		$disabled_comments_likes = UM()->options()->get( 'um_user_photos_disable_comments' );
		if ( $disabled_comments_likes ) {
			$can_like = false;
		} else {
			$likes = get_post_meta( $post_id, '_liked', true );
			if ( empty( $likes ) ) {
				$likes = array();
			}
			if ( in_array( $user_id, $likes, true ) ) {
				$can_like = false;
			} else {
				$can_like = $this->can_view_photo( $post_id, $user_id );
			}
		}

		return apply_filters( 'um_user_photos_can_like_photo', $can_like, $post_id, $user_id );
	}

	/**
	 * @since 2.2.0
	 *
	 * @return bool
	 */
	public function can_unlike_photo( $post_id, $user_id = null ) {
		// return `false` if doesn't exist.
		if ( ! UM()->User_Photos()->common()->photo()->exists( $post_id ) ) {
			return false;
		}

		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		$disabled_comments_likes = UM()->options()->get( 'um_user_photos_disable_comments' );
		if ( $disabled_comments_likes ) {
			$can_unlike = false;
		} else {
			$likes = get_post_meta( $post_id, '_liked', true );
			if ( empty( $likes ) ) {
				$likes = array();
			}
			if ( ! in_array( $user_id, $likes, true ) ) {
				$can_unlike = false;
			} else {
				$can_unlike = $this->can_view_photo( $post_id, $user_id );
			}
		}

		return apply_filters( 'um_user_photos_can_unlike_photo', $can_unlike, $post_id, $user_id );
	}

	/**
	 * @since 2.2.0
	 *
	 * @return bool
	 */
	public function can_view_comment_likes( $comment_id, $user_id = null ) {
		// return `false` if doesn't exist.
		if ( ! UM()->User_Photos()->common()->photo()->comment_exists( $comment_id ) ) {
			return false;
		}

		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		$disabled_comments_likes = UM()->options()->get( 'um_user_photos_disable_comments' );
		if ( $disabled_comments_likes ) {
			$can_view_likes = false;
		} else {
			$can_view_likes = $this->can_view_comment( $comment_id, $user_id );
		}

		return apply_filters( 'um_user_photos_can_view_comment_likes', $can_view_likes, $comment_id, $user_id );
	}

	/**
	 * @since 2.2.0
	 *
	 * @return bool
	 */
	public function can_like_comment( $comment_id, $user_id = null ) {
		// return `false` if doesn't exist.
		if ( ! UM()->User_Photos()->common()->photo()->comment_exists( $comment_id ) ) {
			return false;
		}

		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		$disabled_comments_likes = UM()->options()->get( 'um_user_photos_disable_comments' );
		if ( $disabled_comments_likes ) {
			$can_like = false;
		} else {
			$likes = get_comment_meta( $comment_id, '_likes', true );
			if ( empty( $likes ) ) {
				$likes = array();
			}
			if ( in_array( $user_id, $likes, true ) ) {
				$can_like = false;
			} else {
				$can_like = $this->can_view_comment( $comment_id, $user_id );
			}
		}

		return apply_filters( 'um_user_photos_can_like_comment', $can_like, $comment_id, $user_id );
	}

	/**
	 * @since 2.2.0
	 *
	 * @return bool
	 */
	public function can_unlike_comment( $comment_id, $user_id = null ) {
		// return `false` if doesn't exist.
		if ( ! UM()->User_Photos()->common()->photo()->comment_exists( $comment_id ) ) {
			return false;
		}

		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		$disabled_comments_likes = UM()->options()->get( 'um_user_photos_disable_comments' );
		if ( $disabled_comments_likes ) {
			$can_unlike = false;
		} else {
			$likes = get_comment_meta( $comment_id, '_likes', true );
			if ( empty( $likes ) ) {
				$likes = array();
			}
			if ( ! in_array( $user_id, $likes, true ) ) {
				$can_unlike = false;
			} else {
				$can_unlike = $this->can_view_comment( $comment_id, $user_id );
			}
		}

		return apply_filters( 'um_user_photos_can_unlike_comment', $can_unlike, $comment_id, $user_id );
	}
}
