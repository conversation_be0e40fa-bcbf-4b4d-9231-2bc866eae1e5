<?php
namespace um_ext\um_user_photos\ajax;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class Uploader
 *
 * @package um_ext\um_user_photos\ajax
 */
class Uploader {

	/**
	 * Uploader constructor.
	 */
	public function __construct() {
		add_filter( 'um_upload_handlers', array( $this, 'um_upload_handlers' ) );
		add_filter( 'um_upload_mimes', array( &$this, 'extends_upload_mimes' ), 10, 2 );
		add_action( 'um_upload_file_validation', array( $this, 'upload_validation' ), 10, 2 );
	}

	/**
	 * Extend handlers for upload
	 *
	 * @return string[]
	 */
	public function um_upload_handlers( $handlers ) {
		$handlers[] = 'um-user-photos-upload';
		return $handlers;
	}

	public function extends_upload_mimes( $mimes, $handler ) {
		if ( 'um-user-photos-upload' === $handler ) {
			// Check the avatar file format.
			$mimes = UM()->User_Photos()->common()->uploader()->allowed_mime_types;
		}

		return $mimes;
	}

	public function upload_validation( &$error, $handler ) {
		if ( ! empty( $error ) ) {
			return;
		}

		if ( 'um-user-photos-upload' === $handler ) {
			if ( ! wp_verify_nonce( sanitize_key( $_REQUEST['nonce'] ), 'um_upload_' . $handler ) ) {
				// This nonce is not valid.
				$error = __( 'Invalid nonce.', 'um-user-photos' );
			}

			if ( empty( $error ) ) {
				if ( isset( $_REQUEST['album_id'] ) ) {
					$album_id = absint( $_REQUEST['album_id'] );
					if ( ! UM()->User_Photos()->common()->user()->can_edit_album( $album_id ) ) {
						$error = __( 'You are not authorized for edit this album.', 'um-user-photos' );
					}
				} elseif ( ! UM()->User_Photos()->common()->user()->can_add_album() ) {
					$error = __( 'You are not authorized for adding album.', 'um-user-photos' );
				}
			}
			// Don't need to validate the photos count and user/album limits in this place. Doing later on album add/edit form submission.
		}
	}
}
