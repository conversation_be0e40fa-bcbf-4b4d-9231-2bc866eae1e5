!function(p,f,m,g){var s,a,n,i,o,b,r,t;function e(t){var e=t.currentTarget,n=t.data?t.data.options:{},o=t.data?t.data.items:[],s="",i=0;t.preventDefault(),t.stopPropagation(),(s=m(e).attr("data-umfancybox")?m(e).data("umfancybox"):s)?i=(o=o.length?o.filter('[data-umfancybox="'+s+'"]'):m("[data-umfancybox="+s+"]")).index(e):o=[e],m.umfancybox.open(o,n,i)}m&&(s={speed:330,loop:!0,opacity:"auto",margin:[44,0],gutter:30,infobar:!0,buttons:!0,slideShow:!0,fullScreen:!0,thumbs:!0,closeBtn:!0,smallBtn:"auto",image:{preload:"auto",protect:!1},baseClass:"",slideClass:"",baseTpl:'<div class="umfancybox-container" role="dialog" tabindex="-1"><div class="umfancybox-bg"></div><div class="umfancybox-controls"><div class="umfancybox-infobar"><button data-umfancybox-previous class="umfancybox-button umfancybox-button--left" title="Previous"></button><div class="umfancybox-infobar__body"><span class="js-umfancybox-index"></span>&nbsp;/&nbsp;<span class="js-umfancybox-count"></span></div><button data-umfancybox-next class="umfancybox-button umfancybox-button--right" title="Next"></button></div><div class="umfancybox-buttons"><button data-umfancybox-close class="umfancybox-button umfancybox-button--close" title="Close (Esc)"></button></div></div><div class="umfancybox-slider-wrap"><div class="umfancybox-slider"></div></div><div class="umfancybox-caption-wrap"><div class="umfancybox-caption"></div></div></div>',spinnerTpl:'<div class="umfancybox-loading"></div>',errorTpl:'<div class="umfancybox-error"><p>The requested content cannot be loaded. <br /> Please try again later.<p></div>',closeTpl:'<button data-umfancybox-close class="umfancybox-close-small"></button>',parentEl:"body",touch:!0,keyboard:!0,focus:!0,closeClickOutside:!0,beforeLoad:m.noop,afterLoad:m.noop,beforeMove:m.noop,afterMove:m.noop,onComplete:m.noop,onInit:m.noop,beforeClose:m.noop,afterClose:m.noop,onActivate:m.noop,onDeactivate:m.noop},a=m(p),n=m(f),i=0,o=function(t){return t&&t.hasOwnProperty&&t instanceof m},b=p.requestAnimationFrame||p.webkitRequestAnimationFrame||p.mozRequestAnimationFrame||function(t){p.setTimeout(t,1e3/60)},m.extend((r=function(t,e,n){var o=this;o.opts=m.extend(!0,{index:n},s,e||{}),o.id=o.opts.id||++i,o.group=[],o.currIndex=parseInt(o.opts.index,10)||0,o.prevIndex=null,o.prevPos=null,o.currPos=0,o.firstRun=null,o.createGroup(t),o.group.length&&(o.$lastFocus=m(f.activeElement).blur(),o.slides={},o.init(t))}).prototype,{init:function(){var t,e=this;e.scrollTop=n.scrollTop(),e.scrollLeft=n.scrollLeft(),m.umfancybox.getInstance()||(t=m("body").width(),m("html").addClass("umfancybox-enabled"),m.umfancybox.isTouch)||1<(t=m("body").width()-t)&&m('<style id="umfancybox-noscroll" type="text/css">').html(".compensate-for-scrollbar, .umfancybox-enabled body { margin-right: "+t+"px; }").appendTo("head"),t=m(e.opts.baseTpl).attr("id","umfancybox-container-"+e.id).data("FancyBox",e).addClass(e.opts.baseClass).hide().prependTo(e.opts.parentEl),e.$refs={container:t,bg:t.find(".umfancybox-bg"),controls:t.find(".umfancybox-controls"),buttons:t.find(".umfancybox-buttons"),slider_wrap:t.find(".umfancybox-slider-wrap"),slider:t.find(".umfancybox-slider"),caption:t.find(".umfancybox-caption")},e.trigger("onInit"),e.activate(),e.current||e.jumpTo(e.currIndex)},createGroup:function(t){var c=this,t=m.makeArray(t);m.each(t,function(t,e){var n,o,s,i={},a={},r=[];m.isPlainObject(e)?a=(i=e).opts||{}:"object"===m.type(e)&&m(e).length?(a="options"in(r=(n=m(e)).data())?r.options:{},a="object"===m.type(a)?a:{},i.type=("type"in r?r:a).type,i.src="src"in r?r.src:a.src||n.attr("href"),a.width=("width"in r?r:a).width,a.height=("height"in r?r:a).height,a.thumb=("thumb"in r?r:a).thumb,a.selector=("selector"in r?r:a).selector,"srcset"in r&&(a.image={srcset:r.srcset}),a.$orig=n):i={type:"html",content:e+""},i.opts=m.extend(!0,{},c.opts,a),o=i.type,s=i.src||"",o||(i.content?o="html":s.match(/(^data:image\/[a-z0-9+\/=]*,)|(\.(jp(e|g|eg)|gif|png|bmp|webp|svg|ico)((\?|#).*)?$)/i)?o="image":s.match(/\.(pdf)((\?|#).*)?$/i)?o="pdf":"#"===s.charAt(0)&&(o="inline"),i.type=o),i.index=c.group.length,i.opts.$orig&&!i.opts.$orig.length&&delete i.opts.$orig,!i.opts.$thumb&&i.opts.$orig&&(i.opts.$thumb=i.opts.$orig.find("img:first")),i.opts.$thumb&&!i.opts.$thumb.length&&delete i.opts.$thumb,"function"===m.type(i.opts.caption)?i.opts.caption=i.opts.caption.apply(e,[c,i]):"caption"in r?i.opts.caption=r.caption:a.$orig&&(i.opts.caption=n.attr("title")),i.opts.caption=i.opts.caption===g?"":i.opts.caption+"","auto"==i.opts.smallBtn&&(i.opts.smallBtn=!1),i.opts.modal&&m.extend(!0,i.opts,{infobar:0,buttons:0,keyboard:0,slideShow:0,fullScreen:0,closeClickOutside:0}),c.group.push(i)})},addEvents:function(){var o=this;o.removeEvents(),o.$refs.container.on("click.umfb-close","[data-umfancybox-close]",function(t){t.stopPropagation(),t.preventDefault(),o.close(t)}).on("click.umfb-previous","[data-umfancybox-previous]",function(t){t.stopPropagation(),t.preventDefault(),o.previous()}).on("click.umfb-next","[data-umfancybox-next]",function(t){t.stopPropagation(),t.preventDefault(),o.next()}),m(p).on("orientationchange.umfb resize.umfb",function(t){b(function(){t&&t.originalEvent&&"resize"===t.originalEvent.type?o.update():(o.$refs.slider_wrap.hide(),b(function(){o.$refs.slider_wrap.show(),o.update()}))})}),n.on("focusin.umfb",function(t){var e=m.umfancybox?m.umfancybox.getInstance():null;!e||m(t.target).hasClass("umfancybox-container")||m.contains(e.$refs.container[0],t.target)||(t.stopPropagation(),e.trigger("focus"),a.scrollTop(o.scrollTop).scrollLeft(o.scrollLeft))}),n.on("keydown.umfb",function(t){var e=o.current,n=t.keyCode||t.which;if(e&&e.opts.keyboard&&!m(t.target).is("input")&&!m(t.target).is("textarea"))if(8===n||27===n)t.preventDefault(),o.close(t);else switch(n){case 37:case 38:t.preventDefault(),o.previous();break;case 39:case 40:t.preventDefault(),o.next();break;case 80:case 32:t.preventDefault(),o.UmSlideShow&&(t.preventDefault(),o.UmSlideShow.toggle());break;case 70:o.FullScreen&&(t.preventDefault(),o.FullScreen.toggle());break;case 71:o.Thumbs&&(t.preventDefault(),o.Thumbs.toggle())}})},removeEvents:function(){a.off("scroll.umfb resize.umfb orientationchange.umfb"),n.off("keydown.umfb focusin.umfb click.umfb-close"),this.$refs.container.off("click.umfb-close click.umfb-previous click.umfb-next")},previous:function(t){this.jumpTo(this.currIndex-1,t)},next:function(t){this.jumpTo(this.currIndex+1,t)},jumpTo:function(t,e){var n,o=this,s=o.firstRun=null===o.firstRun,i=n=t=parseInt(t,10),a=!!o.current&&o.current.opts.loop;if(!o.isAnimating&&(i!=o.currIndex||s)){if(1<o.group.length&&a)i=(i%=o.group.length)<0?o.group.length+i:i,2==o.group.length?n=t-o.currIndex+o.currPos:(n=i-o.currIndex+o.currPos,Math.abs(o.currPos-(n+o.group.length))<Math.abs(o.currPos-n)?n+=o.group.length:Math.abs(o.currPos-(n-o.group.length))<Math.abs(o.currPos-n)&&(n-=o.group.length));else if(!o.group[i])return void o.update(!1,!1,e);o.current&&(o.current.$slide.removeClass("umfancybox-slide--current umfancybox-slide--complete"),o.updateSlide(o.current,!0)),o.prevIndex=o.currIndex,o.prevPos=o.currPos,o.currIndex=i,o.currPos=n,o.current=o.createSlide(n),1<o.group.length&&((o.opts.loop||0<=n-1)&&o.createSlide(n-1),o.opts.loop||n+1<o.group.length)&&o.createSlide(n+1),o.current.isMoved=!1,o.current.isComplete=!1,e=parseInt(e===g?1.5*o.current.opts.speed:e,10),o.trigger("beforeMove"),o.updateControls(),s&&(o.current.$slide.addClass("umfancybox-slide--current"),o.$refs.container.show(),b(function(){o.$refs.bg.css("transition-duration",o.current.opts.speed+"ms"),o.$refs.container.addClass("umfancybox-container--ready")})),o.update(!0,!1,s?0:e,function(){o.afterMove()}),o.loadSlide(o.current),s&&o.current.$ghost||o.preload()}},createSlide:function(t){var e,n,o=this,s=t%o.group.length;if(s=s<0?o.group.length+s:s,!o.slides[t]&&o.group[s]){if(o.opts.loop&&2<o.group.length)for(var i in o.slides)if(o.slides[i].index===s)return(n=o.slides[i]).pos=t,o.slides[t]=n,delete o.slides[i],o.updateSlide(n),n;e=m('<div class="umfancybox-slide"></div>').appendTo(o.$refs.slider),o.slides[t]=m.extend(!0,{},o.group[s],{pos:t,$slide:e,isMoved:!1,isLoaded:!1})}return o.slides[t]},zoomInOut:function(e,t,n){var o,s,i,a=this,r=a.current,c=r.$placeholder,l=r.opts.opacity,u=r.opts.$thumb,h=u?u.offset():0,d=r.$slide.offset();return!!(c&&r.isMoved&&h&&(i=u,0<(i=(i="function"==typeof m&&i instanceof m?i[0]:i).getBoundingClientRect()).bottom)&&0<i.right&&i.left<(p.innerWidth||f.documentElement.clientWidth)&&i.top<(p.innerHeight||f.documentElement.clientHeight))&&!("In"===e&&!a.firstRun||(m.umfancybox.stop(c),a.isAnimating=!0,i={top:h.top-d.top+parseFloat(u.css("border-top-width")||0),left:h.left-d.left+parseFloat(u.css("border-left-width")||0),width:u.width(),height:u.height(),scaleX:1,scaleY:1},"auto"==l&&(l=.1<Math.abs(r.width/r.height-i.width/i.height)),"In"===e?(o=i,(s=a.getFitPos(r)).scaleX=s.width/o.width,s.scaleY=s.height/o.height,l&&(o.opacity=.1,s.opacity=1)):(o=m.umfancybox.getTranslate(c),s=i,r.$ghost&&(r.$ghost.show(),r.$image)&&r.$image.remove(),o.scaleX=o.width/s.width,o.scaleY=o.height/s.height,o.width=s.width,o.height=s.height,l&&(s.opacity=0)),a.updateCursor(s.width,s.height),delete s.width,delete s.height,m.umfancybox.setTranslate(c,o),c.show(),a.trigger("beforeZoom"+e),c.css("transition","all "+t+"ms"),m.umfancybox.setTranslate(c,s),setTimeout(function(){var t;c.css("transition","none"),(t=m.umfancybox.getTranslate(c)).scaleX=1,t.scaleY=1,m.umfancybox.setTranslate(c,t),a.trigger("afterZoom"+e),n.apply(a),a.isAnimating=!1},t),0))},canPan:function(){var t=this.current,e=t.$placeholder,n=!1;return e&&(n=this.getFitPos(t),n=1<Math.abs(e.width()-n.width)||1<Math.abs(e.height()-n.height)),n},isScaledDown:function(){var t=this.current,e=t.$placeholder,n=!1;return n=e?(n=m.umfancybox.getTranslate(e)).width<t.width||n.height<t.height:n},scaleToActual:function(t,e,n){var o,s,i,a,r,c=this,l=c.current,u=l.$placeholder,h=parseInt(l.$slide.width(),10),d=parseInt(l.$slide.height(),10),p=l.width,f=l.height;u&&(c.isAnimating=!0,t=t===g?.5*h:t,e=e===g?.5*d:e,a=p/(o=m.umfancybox.getTranslate(u)).width,r=f/o.height,s=.5*h-.5*p,i=.5*d-.5*f,h<p&&(s=0<(s=o.left*a-(t*a-t))?0:s)<h-p&&(s=h-p),d<f&&(i=0<(i=o.top*r-(e*r-e))?0:i)<d-f&&(i=d-f),c.updateCursor(p,f),m.umfancybox.animate(u,null,{top:i,left:s,scaleX:a,scaleY:r},n||l.opts.speed,function(){c.isAnimating=!1}))},scaleToFit:function(t){var e,n=this,o=n.current,s=o.$placeholder;s&&(n.isAnimating=!0,e=n.getFitPos(o),n.updateCursor(e.width,e.height),m.umfancybox.animate(s,null,{top:e.top,left:e.left,scaleX:e.width/s.width(),scaleY:e.height/s.height()},t||o.opts.speed,function(){n.isAnimating=!1}))},getFitPos:function(t){var e,n=t.$placeholder||t.$content,o=t.width,s=t.height,i=t.opts.margin;return!(!n||!n.length||!o&&!s)&&(2==(i="number"===m.type(i)?[i,i]:i).length&&(i=[i[0],i[1],i[0],i[1]]),a.width()<800&&(i=[0,0,0,0]),n=parseInt(t.$slide.width(),10)-(i[1]+i[3]),t=parseInt(t.$slide.height(),10)-(i[0]+i[2]),e=Math.min(1,n/o,t/s),o=Math.floor(e*o),e=Math.floor(e*s),{top:Math.floor(.5*(t-e))+i[0],left:Math.floor(.5*(n-o))+i[3],width:o,height:e})},update:function(t,n,e,o){var s,i=this;!0!==i.isAnimating&&i.current&&(s=i.current.pos*Math.floor(i.current.$slide.width())*-1-i.current.pos*i.current.opts.gutter,e=parseInt(e,10)||0,m.umfancybox.stop(i.$refs.slider),!1===t?i.updateSlide(i.current,n):m.each(i.slides,function(t,e){i.updateSlide(e,n)}),e?m.umfancybox.animate(i.$refs.slider,null,{top:0,left:s},e,function(){i.current.isMoved=!0,"function"===m.type(o)&&o.apply(i)}):(m.umfancybox.setTranslate(i.$refs.slider,{top:0,left:s}),i.current.isMoved=!0,"function"===m.type(o)&&o.apply(i)))},updateSlide:function(t,e){var n,o=this,s=t.$placeholder;(t=t||o.current)&&!o.isClosing&&((n=t.pos*Math.floor(t.$slide.width())+t.pos*t.opts.gutter)!==t.leftPos&&(m.umfancybox.setTranslate(t.$slide,{top:0,left:n}),t.leftPos=n),!1!==e&&s&&(m.umfancybox.setTranslate(s,o.getFitPos(t)),t.pos===o.currPos)&&o.updateCursor(),t.$slide.trigger("refresh"),o.trigger("onUpdate",t))},updateCursor:function(t,e){var n=this,o=n.$refs.container.removeClass("umfancybox-controls--canzoomIn umfancybox-controls--canzoomOut umfancybox-controls--canGrab");!n.isClosing&&n.opts.touch&&((t!==g&&e!==g?t<n.current.width&&e<n.current.height:n.isScaledDown())?o.addClass("umfancybox-controls--canzoomIn"):n.group.length<2?o.addClass("umfancybox-controls--canzoomOut"):o.addClass("umfancybox-controls--canGrab"))},loadSlide:function(t){var e;if(t&&!t.isLoaded&&!t.isLoading)return t.isLoading=!0,this.trigger("beforeLoad",t),e=t.type,t.$slide.off("refresh").trigger("onReset").addClass("umfancybox-slide--"+(e||"unknown")).addClass(t.opts.slideClass),this.setImage(t),!0},setImage:function(t){var e,n,o,s,i=this,a=t.opts.image.srcset;if(t.isLoaded&&!t.hasError)i.afterLoad(t);else{if(a){o=p.devicePixelRatio||1,s=p.innerWidth*o,(n=a.split(",").map(function(t){var o={};return t.trim().split(/\s+/).forEach(function(t,e){var n=parseInt(t.substring(0,t.length-1),10);if(0===e)return o.url=t;n&&(o.value=n,o.postfix=t[t.length-1])}),o})).sort(function(t,e){return t.value-e.value});for(var r=0;r<n.length;r++){var c=n[r];if("w"===c.postfix&&c.value>=s||"x"===c.postfix&&c.value>=o){e=c;break}}(e=!e&&n.length?n[n.length-1]:e)&&(t.src=e.url,t.width)&&t.height&&"w"==e.postfix&&(t.height=t.width/t.height*e.value,t.width=e.value)}t.$placeholder=m('<div class="umfancybox-placeholder"></div>').hide().appendTo(t.$slide),!1!==t.opts.preload&&t.opts.width&&t.opts.height&&(t.opts.thumb||t.opts.$thumb)?(t.width=t.opts.width,t.height=t.opts.height,t.$ghost=m("<img />").one("load error",function(){i.isClosing||(m("<img/>")[0].src=t.src,i.revealImage(t,function(){i.setBigImage(t),i.firstRun&&t.index===i.currIndex&&i.preload()}))}).addClass("umfancybox-image").appendTo(t.$placeholder).attr("src",t.opts.thumb||t.opts.$thumb.attr("src"))):i.setBigImage(t)}},setBigImage:function(t){var e=this,n=m("<img />");t.$image=n.one("error",function(){e.setError(t)}).one("load",function(){clearTimeout(t.timouts),t.timouts=null,e.isClosing||(t.width=this.naturalWidth,t.height=this.naturalHeight,t.opts.image.srcset&&n.attr("sizes","100vw").attr("srcset",t.opts.image.srcset),e.afterLoad(t),t.$ghost&&(t.timouts=setTimeout(function(){t.$ghost.hide()},350)))}).addClass("umfancybox-image").attr("src",t.src).appendTo(t.$placeholder),n[0].complete?n.trigger("load"):n[0].error?n.trigger("error"):t.timouts=setTimeout(function(){n[0].complete||t.hasError||e.showLoading(t)},150),t.opts.image.protect&&m('<div class="umfancybox-spaceball"></div>').appendTo(t.$placeholder).on("contextmenu.umfb",function(t){return 2==t.button&&t.preventDefault(),!0})},revealImage:function(t,e){var n=this;e=e||m.noop,"image"!==t.type||t.hasError||!0===t.isRevealed?e.apply(n):(t.isRevealed=!0,t.pos===n.currPos&&n.zoomInOut("In",t.opts.speed,e)||(t.$ghost&&!t.isLoaded&&n.updateSlide(t,!0),t.pos===n.currPos?m.umfancybox.animate(t.$placeholder,{opacity:0},{opacity:1},300,e):t.$placeholder.show(),e.apply(n)))},setContent:function(e,n){this.isClosing||(this.hideLoading(e),e.$slide.empty(),o(n)&&n.parent().length?(n.data("placeholder")&&n.parents(".umfancybox-slide").trigger("onReset"),n.data({placeholder:m("<div></div>").hide().insertAfter(n)}).css("display","inline-block")):("string"===m.type(n)&&3===(n=m("<div>").append(n).contents())[0].nodeType&&(n=m("<div>").html(n)),e.opts.selector&&(n=m("<div>").html(n).find(e.opts.selector))),e.$slide.one("onReset",function(){var t=o(n)?n.data("placeholder"):0;t&&(n.hide().replaceAll(t),n.data("placeholder",null)),e.hasError||(m(this).empty(),e.isLoaded=!1)}),e.$content=m(n).appendTo(e.$slide),!0===e.opts.smallBtn&&e.$content.find(".umfancybox-close-small").remove().end().eq(0).append(e.opts.closeTpl),this.afterLoad(e))},setError:function(t){t.hasError=!0,this.setContent(t,t.opts.errorTpl)},showLoading:function(t){(t=t||this.current)&&!t.$spinner&&(t.$spinner=m(this.opts.spinnerTpl).appendTo(t.$slide))},hideLoading:function(t){(t=t||this.current)&&t.$spinner&&(t.$spinner.remove(),delete t.$spinner)},afterMove:function(){var n=this,t=n.current,o={};t&&(t.$slide.siblings().trigger("onReset"),m.each(n.slides,function(t,e){e.pos>=n.currPos-1&&e.pos<=n.currPos+1?o[e.pos]=e:e&&e.$slide.remove()}),n.slides=o,n.trigger("afterMove"),t.isLoaded)&&n.complete()},afterLoad:function(t){var e=this;e.isClosing||(t.isLoading=!1,t.isLoaded=!0,e.trigger("afterLoad",t),e.hideLoading(t),t.$ghost||e.updateSlide(t,!0),t.index===e.currIndex&&t.isMoved?e.complete():t.$ghost||e.revealImage(t))},complete:function(){var t=this,e=t.current;t.revealImage(e,function(){e.isComplete=!0,e.$slide.addClass("umfancybox-slide--complete"),t.updateCursor(),t.trigger("onComplete")})},preload:function(){var t,e,n=this;n.group.length<2||(t=n.slides[n.currPos+1],e=n.slides[n.currPos-1],t&&"image"===t.type&&n.loadSlide(t),e&&"image"===e.type&&n.loadSlide(e))},focus:function(){var t=this.current,e=t&&t.isComplete?t.$slide.find('button,:input,[tabindex],a:not(".disabled")').filter(":visible:first"):null;(e=e&&e.length?e:this.$refs.container).trigger("focus"),this.$refs.slider_wrap.scrollLeft(0),t&&t.$slide.scrollTop(0)},activate:function(){var e=this;m(".umfancybox-container").each(function(){var t=m(this).data("FancyBox");t&&t.uid!==e.uid&&!t.isClosing&&t.trigger("onDeactivate")}),e.current&&(0<e.$refs.container.index()&&e.$refs.container.prependTo(f.body),e.updateControls()),e.trigger("onActivate"),e.addEvents()},close:function(t){var e=this,n=e.current,o=n.opts.speed,s=m.proxy(function(){e.cleanUp(t)},this);if(e.isAnimating||e.isClosing)return!1;!1===e.trigger("beforeClose",t)?(m.umfancybox.stop(e.$refs.slider),b(function(){e.update(!0,!0,150)})):(e.isClosing=!0,n.timouts&&clearTimeout(n.timouts),!0!==t&&m.umfancybox.stop(e.$refs.slider),e.$refs.container.removeClass("umfancybox-container--active").addClass("umfancybox-container--closing"),n.$slide.removeClass("umfancybox-slide--complete").siblings().remove(),n.isMoved||n.$slide.css("overflow","visible"),e.removeEvents(),e.hideLoading(n),e.hideControls(),e.updateCursor(),e.$refs.bg.css("transition-duration",o+"ms"),this.$refs.container.removeClass("umfancybox-container--ready"),!0===t?setTimeout(s,o):e.zoomInOut("Out",o,s)||m.umfancybox.animate(e.$refs.container,null,{opacity:0},o,"easeInSine",s))},cleanUp:function(t){var e=this;e.$refs.slider.children().trigger("onReset"),e.$refs.container.empty().remove(),e.trigger("afterClose",t),e.current=null,(t=m.umfancybox.getInstance())?t.activate():(m("html").removeClass("umfancybox-enabled"),m("body").removeAttr("style"),a.scrollTop(e.scrollTop).scrollLeft(e.scrollLeft),m("#umfancybox-noscroll").remove()),e.$lastFocus&&e.$lastFocus.trigger("focus")},trigger:function(t,e){var n,o=Array.prototype.slice.call(arguments,1),e=e&&e.opts?e:this.current;if(e?o.unshift(e):e=this,o.unshift(this),!1===(n=m.isFunction(e.opts[t])?e.opts[t].apply(e,o):n))return n;("afterClose"===t?m(f):this.$refs.container).trigger(t+".umfb",o)},toggleControls:function(t){this.isHiddenControls?this.updateControls(t):this.hideControls()},hideControls:function(){this.isHiddenControls=!0,this.$refs.container.removeClass("umfancybox-show-controls"),this.$refs.container.removeClass("umfancybox-show-caption")},updateControls:function(t){var e=this,n=e.$refs.container,o=e.$refs.caption,s=e.current,i=s.index,a=s.opts,r=a.caption;this.isHiddenControls&&!0!==t||(this.isHiddenControls=!1,n.addClass("umfancybox-show-controls").toggleClass("umfancybox-show-infobar",!!a.infobar&&1<e.group.length).toggleClass("umfancybox-show-buttons",!!a.buttons).toggleClass("umfancybox-is-modal",!!a.modal),m(".umfancybox-button--left",n).toggleClass("umfancybox-button--disabled",!a.loop&&i<=0),m(".umfancybox-button--right",n).toggleClass("umfancybox-button--disabled",!a.loop&&i>=e.group.length-1),m(".umfancybox-button--play",n).toggle(!!(a.slideShow&&1<e.group.length)),m(".umfancybox-button--close",n).toggle(!!a.closeBtn),m(".js-umfancybox-count",n).html(e.group.length),m(".js-umfancybox-index",n).html(i+1),s.$slide.trigger("refresh"),o&&o.empty(),r&&r.length?(o.html(r),this.$refs.container.addClass("umfancybox-show-caption "),e.$caption=o):this.$refs.container.removeClass("umfancybox-show-caption"))}}),m.umfancybox={version:"3.0.47",defaults:s,getInstance:function(t){var e=m('.umfancybox-container:not(".umfancybox-container--closing"):first').data("FancyBox"),n=Array.prototype.slice.call(arguments,1);return e instanceof r&&("string"===m.type(t)?e[t].apply(e,n):"function"===m.type(t)&&t.apply(e,n),e)},open:function(t,e,n){return new r(t,e,n)},close:function(t){var e=this.getInstance();e&&(e.close(),!0===t)&&this.close()},isTouch:f.createTouch!==g&&/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent),use3d:(t=f.createElement("div"),p.getComputedStyle(t).getPropertyValue("transform")&&!(f.documentMode&&f.documentMode<=11)),getTranslate:function(t){var e,n;return!(!t||!t.length)&&(e=t.get(0).getBoundingClientRect(),{top:(n=(n=(n=t.eq(0).css("transform"))&&-1!==n.indexOf("matrix")?(n=(n=n.split("(")[1]).split(")")[0]).split(","):[]).length?(n=10<n.length?[n[13],n[12],n[0],n[5]]:[n[5],n[4],n[0],n[3]]).map(parseFloat):[0,0,1,1])[0],left:n[1],scaleX:n[2],scaleY:n[3],opacity:parseFloat(t.css("opacity")),width:e.width,height:e.height})},setTranslate:function(t,e){var n="",o={};if(t&&e)return e.left===g&&e.top===g||(n=(e.left===g?t.position().top:e.left)+"px, "+(e.top===g?t.position():e).top+"px",n=this.use3d?"translate3d("+n+", 0px)":"translate("+n+")"),(n=e.scaleX!==g&&e.scaleY!==g?(n.length?n+" ":"")+"scale("+e.scaleX+", "+e.scaleY+")":n).length&&(o.transform=n),e.opacity!==g&&(o.opacity=e.opacity),e.width!==g&&(o.width=e.width),e.height!==g&&(o.height=e.height),t.css(o)},easing:{easeOutCubic:function(t,e,n,o){return n*((t=t/o-1)*t*t+1)+e},easeInCubic:function(t,e,n,o){return n*(t/=o)*t*t+e},easeOutSine:function(t,e,n,o){return n*Math.sin(t/o*(Math.PI/2))+e},easeInSine:function(t,e,n,o){return-n*Math.cos(t/o*(Math.PI/2))+n+e}},stop:function(t){t.removeData("animateID")},animate:function(n,o,s,i,a,t){var r,c,l,u=this,h=null,d=0,p=function(){s.scaleX!==g&&s.scaleY!==g&&o&&o.width!==g&&o.height!==g&&(s.width=o.width*s.scaleX,s.height=o.height*s.scaleY,s.scaleX=1,s.scaleY=1),u.setTranslate(n,s),t()},f=function(t){if(r=[],c=0,n.length&&n.data("animateID")===l)if(t=t||Date.now(),h&&(c=t-h),h=t,i<=(d+=c))p();else{for(var e in s)s.hasOwnProperty(e)&&o[e]!==g&&(o[e]==s[e]?r[e]=s[e]:r[e]=u.easing[a](d,o[e],s[e]-o[e],i));u.setTranslate(n,r),b(f)}};u.animateID=l=u.animateID===g?1:u.animateID+1,n.data("animateID",l),t===g&&"function"==m.type(a)&&(t=a,a=g),a=a||"easeOutCubic",t=t||m.noop,o?this.setTranslate(n,o):o=this.getTranslate(n),i?(n.show(),b(f)):p()}},m.fn.umfancybox=function(t){return this.off("click.umfb-start").on("click.umfb-start",{items:this,options:t||{}},e),this},m(f).on("click.umfb-start","[data-umfancybox]",e))}(window,document,window.jQuery),function(p){function f(n,t,e){if(n)return"object"===p.type(e=e||"")&&(e=p.param(e,!0)),p.each(t,function(t,e){n=n.replace("$"+t,e||"")}),e.length&&(n+=(0<n.indexOf("?")?"&":"?")+e),n}p(document).on("onInit.umfb",function(t,e){p.each(e.group,function(t,s){var i,a,r,c,l,u,h=s.src||"",d=!1;s.type||(p.each(media,function(t,e){if(a=h.match(e.matcher),l={},u=t,a){if(d=e.type,e.paramPlace&&a[e.paramPlace]){c=(c="?"==(c=a[e.paramPlace])[0]?c.substring(1):c).split("&");for(var n=0;n<c.length;++n){var o=c[n].split("=",2);2==o.length&&(l[o[0]]=decodeURIComponent(o[1].replace(/\+/g," ")))}}return r=p.extend(!0,{},e.params,s.opts[t],l),h="function"===p.type(e.url)?e.url.call(this,a,r,s):f(e.url,a,r),i="function"===p.type(e.thumb)?e.thumb.call(this,a,r,s):f(e.thumb,a),"vimeo"===u&&(h=h.replace("&%23","#")),!1}}),d&&(s.src=h,s.type=d,s.opts.thumb||s.opts.$thumb&&s.opts.$thumb.length||(s.opts.thumb=i)))})})}(window.jQuery),function(u,t,h){function r(t){var e,n=[];for(e in t=(t=t.originalEvent||t||u.e).touches&&t.touches.length?t.touches:t.changedTouches&&t.changedTouches.length?t.changedTouches:[t])t[e].pageX?n.push({x:t[e].pageX,y:t[e].pageY}):t[e].clientX&&n.push({x:t[e].clientX,y:t[e].clientY});return n}function d(t,e,n){return e&&t?"x"===n?t.x-e.x:"y"===n?t.y-e.y:Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2)):0}function i(t){return t.is("a")||t.is("button")||t.is("input")||t.is("select")||t.is("textarea")||h.isFunction(t.get(0).onclick)}function a(t){for(var e,n,o,s=!1;(e=t.get(0),o=n=o=n=void 0,n=u.getComputedStyle(e)["overflow-y"],o=u.getComputedStyle(e)["overflow-x"],n=("scroll"===n||"auto"===n)&&e.scrollHeight>e.clientHeight,o=("scroll"===o||"auto"===o)&&e.scrollWidth>e.clientWidth,!(s=n||o))&&((t=t.parent()).length&&!t.hasClass("umfancybox-slider")&&!t.is("body")););return s}function n(t){var e=this;e.instance=t,e.$wrap=t.$refs.slider_wrap,e.$slider=t.$refs.slider,e.$container=t.$refs.container,e.destroy(),e.$wrap.on("touchstart.umfb mousedown.umfb",h.proxy(e,"ontouchstart"))}var p=u.requestAnimationFrame||u.webkitRequestAnimationFrame||u.mozRequestAnimationFrame||function(t){u.setTimeout(t,1e3/60)};n.prototype.destroy=function(){this.$wrap.off("touchstart.umfb mousedown.umfb touchmove.umfb mousemove.umfb touchend.umfb touchcancel.umfb mouseup.umfb mouseleave.umfb")},n.prototype.ontouchstart=function(t){var e=this,n=h(t.target),o=e.instance.current,s=o.$content||o.$placeholder;return e.startPoints=r(t),e.$target=n,e.$content=s,e.canvasWidth=Math.round(o.$slide[0].clientWidth),e.canvasHeight=Math.round(o.$slide[0].clientHeight),(e.startEvent=t).originalEvent.clientX>e.canvasWidth+o.$slide.offset().left||(i(n)||i(n.parent())||a(n)?void 0:o.opts.touch?void(t.originalEvent&&2==t.originalEvent.button||(t.stopPropagation(),t.preventDefault(),!o)||e.instance.isAnimating||e.instance.isClosing||!e.startPoints||1<e.startPoints.length&&!o.isMoved||(e.$wrap.off("touchmove.umfb mousemove.umfb",h.proxy(e,"ontouchmove")),e.$wrap.off("touchend.umfb touchcancel.umfb mouseup.umfb mouseleave.umfb",h.proxy(e,"ontouchend")),e.$wrap.on("touchend.umfb touchcancel.umfb mouseup.umfb mouseleave.umfb",h.proxy(e,"ontouchend")),e.$wrap.on("touchmove.umfb mousemove.umfb",h.proxy(e,"ontouchmove")),e.startTime=(new Date).getTime(),e.distanceX=e.distanceY=e.distance=0,e.canTap=!1,e.isPanning=!1,e.isSwiping=!1,e.isZooming=!1,e.sliderStartPos=h.umfancybox.getTranslate(e.$slider),e.contentStartPos=h.umfancybox.getTranslate(e.$content),e.contentLastPos=null,1!==e.startPoints.length||e.isZooming||(e.canTap=o.isMoved,"image"===o.type&&(e.contentStartPos.width>e.canvasWidth+1||e.contentStartPos.height>e.canvasHeight+1)?(h.umfancybox.stop(e.$content),e.isPanning=!0):(h.umfancybox.stop(e.$slider),e.isSwiping=!0),e.$container.addClass("umfancybox-controls--isGrabbing")),2===e.startPoints.length&&o.isMoved&&!o.hasError&&"image"===o.type&&(o.isLoaded||o.$ghost)&&(e.isZooming=!0,e.isSwiping=!1,e.isPanning=!1,h.umfancybox.stop(e.$content),e.centerPointStartX=.5*(e.startPoints[0].x+e.startPoints[1].x)-h(u).scrollLeft(),e.centerPointStartY=.5*(e.startPoints[0].y+e.startPoints[1].y)-h(u).scrollTop(),e.percentageOfImageAtPinchPointX=(e.centerPointStartX-e.contentStartPos.left)/e.contentStartPos.width,e.percentageOfImageAtPinchPointY=(e.centerPointStartY-e.contentStartPos.top)/e.contentStartPos.height,e.startDistanceBetweenFingers=d(e.startPoints[0],e.startPoints[1])))):(e.endPoints=e.startPoints,e.ontap()))},n.prototype.ontouchmove=function(t){var e=this;t.preventDefault(),e.newPoints=r(t),e.newPoints&&e.newPoints.length&&(e.distanceX=d(e.newPoints[0],e.startPoints[0],"x"),e.distanceY=d(e.newPoints[0],e.startPoints[0],"y"),e.distance=d(e.newPoints[0],e.startPoints[0]),0<e.distance)&&(e.isSwiping?e.onSwipe():e.isPanning?e.onPan():e.isZooming&&e.onZoom())},n.prototype.onSwipe=function(){var t,e=this,n=e.isSwiping,o=e.sliderStartPos.left;!0===n?10<Math.abs(e.distance)&&(e.instance.group.length<2?e.isSwiping="y":!e.instance.current.isMoved||!1===e.instance.opts.touch.vertical||"auto"===e.instance.opts.touch.vertical&&800<h(u).width()?e.isSwiping="x":(t=Math.abs(180*Math.atan2(e.distanceY,e.distanceX)/Math.PI),e.isSwiping=45<t&&t<135?"y":"x"),e.canTap=!1,e.instance.current.isMoved=!1,e.startPoints=e.newPoints):("x"==n&&(!e.instance.current.opts.loop&&0===e.instance.current.index&&0<e.distanceX?o+=Math.pow(e.distanceX,.8):!e.instance.current.opts.loop&&e.instance.current.index===e.instance.group.length-1&&e.distanceX<0?o-=Math.pow(-e.distanceX,.8):o+=e.distanceX),e.sliderLastPos={top:"x"==n?0:e.sliderStartPos.top+e.distanceY,left:o},p(function(){h.umfancybox.setTranslate(e.$slider,e.sliderLastPos)}))},n.prototype.onPan=function(){var t,e,n=this;n.canTap=!1,e=n.contentStartPos.width>n.canvasWidth?n.contentStartPos.left+n.distanceX:n.contentStartPos.left,t=n.contentStartPos.top+n.distanceY,(e=n.limitMovement(e,t,n.contentStartPos.width,n.contentStartPos.height)).scaleX=n.contentStartPos.scaleX,e.scaleY=n.contentStartPos.scaleY,n.contentLastPos=e,p(function(){h.umfancybox.setTranslate(n.$content,n.contentLastPos)})},n.prototype.limitMovement=function(t,e,n,o){var s=this,i=s.canvasWidth,a=s.canvasHeight,r=s.contentStartPos.left,c=s.contentStartPos.top,l=s.distanceX,s=s.distanceY,u=Math.max(0,.5*i-.5*n),h=Math.max(0,.5*a-.5*o),d=Math.min(i-n,.5*i-.5*n),p=Math.min(a-o,.5*a-.5*o);return i<n&&(0<l&&u<t&&(t=u-1+Math.pow(-u+r+l,.8)||0),l<0)&&t<d&&(t=d+1-Math.pow(d-r-l,.8)||0),{top:e=a<o&&(0<s&&h<e&&(e=h-1+Math.pow(-h+c+s,.8)||0),s<0)&&e<p?p+1-Math.pow(p-c-s,.8)||0:e,left:t}},n.prototype.limitPosition=function(t,e,n,o){var s=this.canvasWidth,i=this.canvasHeight;return t=s<n?(t=0<t?0:t)<s-n?s-n:t:Math.max(0,s/2-n/2),{top:e=i<o?(e=0<e?0:e)<i-o?i-o:e:Math.max(0,i/2-o/2),left:t}},n.prototype.onZoom=function(){var t=this,e=t.contentStartPos.width,n=t.contentStartPos.height,o=t.contentStartPos.left,s=t.contentStartPos.top,i=d(t.newPoints[0],t.newPoints[1])/t.startDistanceBetweenFingers,a=Math.floor(e*i),r=Math.floor(n*i),e=(e-a)*t.percentageOfImageAtPinchPointX,n=(n-r)*t.percentageOfImageAtPinchPointY,c=(t.newPoints[0].x+t.newPoints[1].x)/2-h(u).scrollLeft(),l=(t.newPoints[0].y+t.newPoints[1].y)/2-h(u).scrollTop(),c=c-t.centerPointStartX,s={top:s+(n+(l-t.centerPointStartY)),left:o+(e+c),scaleX:t.contentStartPos.scaleX*i,scaleY:t.contentStartPos.scaleY*i};t.canTap=!1,t.newWidth=a,t.newHeight=r,t.contentLastPos=s,p(function(){h.umfancybox.setTranslate(t.$content,t.contentLastPos)})},n.prototype.ontouchend=function(t){var e=this,n=e.instance.current,o=Math.max((new Date).getTime()-e.startTime,1),s=e.isSwiping,i=e.isPanning,a=e.isZooming;if(e.endPoints=r(t),e.$container.removeClass("umfancybox-controls--isGrabbing"),e.$wrap.off("touchmove.umfb mousemove.umfb",h.proxy(this,"ontouchmove")),e.$wrap.off("touchend.umfb touchcancel.umfb mouseup.umfb mouseleave.umfb",h.proxy(this,"ontouchend")),e.isSwiping=!1,e.isPanning=!1,e.isZooming=!1,e.canTap)return e.ontap();e.velocityX=e.distanceX/o*.5,e.velocityY=e.distanceY/o*.5,e.speed=n.opts.speed||330,e.speedX=Math.max(.75*e.speed,Math.min(1.5*e.speed,1/Math.abs(e.velocityX)*e.speed)),e.speedY=Math.max(.75*e.speed,Math.min(1.5*e.speed,1/Math.abs(e.velocityY)*e.speed)),i?e.endPanning():a?e.endZooming():e.endSwiping(s)},n.prototype.endSwiping=function(t){var e=this;"y"==t&&50<Math.abs(e.distanceY)?(h.umfancybox.animate(e.$slider,null,{top:e.sliderStartPos.top+e.distanceY+150*e.velocityY,left:e.sliderStartPos.left,opacity:0},e.speedY),e.instance.close(!0)):"x"==t&&50<e.distanceX?e.instance.previous(e.speedX):"x"==t&&e.distanceX<-50?e.instance.next(e.speedX):e.instance.update(!1,!0,150)},n.prototype.endPanning=function(){var t,e,n=this;n.contentLastPos&&(e=n.contentLastPos.left+n.velocityX*n.speed*2,t=n.contentLastPos.top+n.velocityY*n.speed*2,(e=n.limitPosition(e,t,n.contentStartPos.width,n.contentStartPos.height)).width=n.contentStartPos.width,e.height=n.contentStartPos.height,h.umfancybox.animate(n.$content,null,e,n.speed,"easeOutSine"))},n.prototype.endZooming=function(){var t,e,n=this,o=n.instance.current,s=n.newWidth,i=n.newHeight;n.contentLastPos&&(t=n.contentLastPos.left,e=n.contentLastPos.top,h.umfancybox.setTranslate(n.$content,{top:e,left:t,width:s,height:i,scaleX:1,scaleY:1}),s<n.canvasWidth&&i<n.canvasHeight?n.instance.scaleToFit(150):s>o.width||i>o.height?n.instance.scaleToActual(n.centerPointStartX,n.centerPointStartY,150):(o=n.limitPosition(t,e,s,i),h.umfancybox.animate(n.$content,null,o,n.speed,"easeOutSine")))},n.prototype.ontap=function(){var t=this,e=t.instance,n=e.current,o=t.endPoints[0].x,s=t.endPoints[0].y;if(o-=t.$wrap.offset().left,s-=t.$wrap.offset().top,e.UmSlideShow&&e.UmSlideShow.isActive&&e.UmSlideShow.stop(),!h.umfancybox.isTouch)return n.opts.closeClickOutside&&t.$target.is(".umfancybox-slide")?void e.close(t.startEvent):void("image"==n.type&&n.isMoved&&(e.canPan()?e.scaleToFit():e.isScaledDown()?e.scaleToActual(o,s):e.group.length<2&&e.close(t.startEvent)));if(t.tapped){if(clearTimeout(t.tapped),t.tapped=null,50<Math.abs(o-t.x)||50<Math.abs(s-t.y)||!n.isMoved)return this;"image"==n.type&&(n.isLoaded||n.$ghost)&&(e.canPan()?e.scaleToFit():e.isScaledDown()&&e.scaleToActual(o,s))}else t.x=o,t.y=s,t.tapped=setTimeout(function(){t.tapped=null,e.toggleControls(!0)},300);return this},h(t).on("onActivate.umfb",function(t,e){e&&!e.Guestures&&(e.Guestures=new n(e))}),h(t).on("beforeClose.umfb",function(t,e){e&&e.Guestures&&e.Guestures.destroy()})}(window,document,window.jQuery),function(t,e){function n(t){this.instance=t,this.init()}e.extend(n.prototype,{timer:null,isActive:!1,$button:null,speed:3e3,init:function(){var t=this;t.$button=e('<button data-umfancybox-play class="umfancybox-button umfancybox-button--play" title="Slideshow (P)"></button>').appendTo(t.instance.$refs.buttons),t.instance.$refs.container.on("click","[data-umfancybox-play]",function(){t.toggle()})},set:function(){var t=this;t.instance&&t.instance.current&&(t.instance.current.opts.loop||t.instance.currIndex<t.instance.group.length-1)?t.timer=setTimeout(function(){t.instance.next()},t.instance.current.opts.slideShow.speed||t.speed):t.stop()},clear:function(){clearTimeout(this.timer),this.timer=null},start:function(){var t=this;t.stop(),t.instance&&t.instance.current&&(t.instance.current.opts.loop||t.instance.currIndex<t.instance.group.length-1)&&(t.instance.$refs.container.on({"beforeLoad.umfb.player":e.proxy(t,"clear"),"onComplete.umfb.player":e.proxy(t,"set")}),t.isActive=!0,t.instance.current.isComplete&&t.set(),t.instance.$refs.container.trigger("onPlayStart"),t.$button.addClass("umfancybox-button--pause"))},stop:function(){this.clear(),this.instance.$refs.container.trigger("onPlayEnd").off(".player"),this.$button.removeClass("umfancybox-button--pause"),this.isActive=!1},toggle:function(){this.isActive?this.stop():this.start()}}),e(t).on("onInit.umfb",function(t,e){e&&1<e.group.length&&e.opts.slideShow&&!e.UmSlideShow&&(e.UmSlideShow=new n(e))}),e(t).on("beforeClose.umfb onDeactivate.umfb",function(t,e){e&&e.UmSlideShow&&e.UmSlideShow.stop()})}(document,window.jQuery),function(i,o){var s,e=function(){for(var t,e,n=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],o={},s=0;s<n.length;s++)if((t=n[s])&&t[1]in i){for(e=0;e<t.length;e++)o[n[0][e]]=t[e];return o}return!1}();e&&(s={request:function(t){(t=t||i.documentElement)[e.requestFullscreen](t.ALLOW_KEYBOARD_INPUT)},exit:function(){i[e.exitFullscreen]()},toggle:function(t){this.isFullscreen()?this.exit():this.request(t)},isFullscreen:function(){return Boolean(i[e.fullscreenElement])},enabled:function(){return Boolean(i[e.fullscreenEnabled])}},o(i).on({"onInit.umfb":function(t,e){var n;e&&e.opts.fullScreen&&!e.FullScreen&&(n=e.$refs.container,e.$refs.button_fs=o('<button data-umfancybox-fullscreen class="umfancybox-button umfancybox-button--fullscreen" title="Full screen (F)"></button>').appendTo(e.$refs.buttons),n.on("click.umfb-fullscreen","[data-umfancybox-fullscreen]",function(t){t.stopPropagation(),t.preventDefault(),s.toggle(n[0])}),!0===e.opts.fullScreen.requestOnStart)&&s.request(n[0])},"beforeMove.umfb":function(t,e){e&&e.$refs.button_fs&&e.$refs.button_fs.toggle(!!e.current.opts.fullScreen)},"beforeClose.umfb":function(){s.exit()}}),o(i).on(e.fullscreenchange,function(){var t=o.umfancybox.getInstance(),e=t?t.current.$placeholder:null;e&&(e.css("transition","none"),t.isAnimating=!1,t.update(!0,!0,0))}))}(document,window.jQuery),function(t,a){function s(t){this.instance=t,this.init()}a.extend(s.prototype,{$button:null,$grid:null,$list:null,isVisible:!1,init:function(){var e=this;e.$button=a('<button data-umfancybox-thumbs class="umfancybox-button umfancybox-button--thumbs" title="Thumbnails (G)"></button>').appendTo(this.instance.$refs.buttons).on("touchend click",function(t){t.stopPropagation(),t.preventDefault(),e.toggle()})},create:function(){var n,o,t=this.instance;this.$grid=a('<div class="umfancybox-thumbs"></div>').appendTo(t.$refs.container),n="<ul>",a.each(t.group,function(t,e){(o=(o=e.opts.thumb||(e.opts.$thumb?e.opts.$thumb.attr("src"):null))||"image"!==e.type?o:e.src)&&o.length&&(n+='<li data-index="'+t+'"  tabindex="0" class="umfancybox-thumbs-loading"><img data-src="'+o+'" /></li>')}),n+="</ul>",this.$list=a(n).appendTo(this.$grid).on("click touchstart","li",function(){t.jumpTo(a(this).data("index"))}),this.$list.find("img").hide().one("load",function(){var t=a(this).parent().removeClass("umfancybox-thumbs-loading"),e=t.outerWidth(),t=t.outerHeight(),n=this.naturalWidth||this.width,o=this.naturalHeight||this.height,s=n/e,i=o/t;1<=s&&1<=i&&(i<s?(n/=i,o=t):(n=e,o/=s)),a(this).css({width:Math.floor(n),height:Math.floor(o),"margin-top":Math.min(0,Math.floor(.3*t-.3*o)),"margin-left":Math.min(0,Math.floor(.5*e-.5*n))}).show()}).each(function(){this.src=a(this).data("src")})},focus:function(){this.instance.current&&this.$list.children().removeClass("umfancybox-thumbs-active").filter('[data-index="'+this.instance.current.index+'"]').addClass("umfancybox-thumbs-active").trigger("focus")},close:function(){this.$grid.hide()},update:function(){this.instance.$refs.container.toggleClass("umfancybox-container--thumbs",this.isVisible),this.isVisible?(this.$grid||this.create(),this.$grid.show(),this.trigger("focus")):this.$grid&&this.$grid.hide(),this.instance.update()},hide:function(){this.isVisible=!1,this.update()},show:function(){this.isVisible=!0,this.update()},toggle:function(){this.isVisible?this.hide():this.show()}}),a(t).on("onInit.umfb",function(t,e){var n=e.group[0],o=e.group[1];e.opts.thumbs&&!e.Thumbs&&1<e.group.length&&("image"==n.type||n.opts.thumb||n.opts.$thumb)&&("image"==o.type||o.opts.thumb||o.opts.$thumb)&&(e.Thumbs=new s(e))}),a(t).on("beforeMove.umfb",function(t,e,n){var o=e&&e.Thumbs;o&&(n.modal?(o.$button.hide(),o.hide()):(!0===e.opts.thumbs.showOnStart&&e.firstRun&&o.show(),o.$button.show(),o.isVisible&&o.trigger("focus")))}),a(t).on("beforeClose.umfb",function(t,e){e&&e.Thumbs&&(e.Thumbs.isVisible&&!1!==e.opts.thumbs.hideOnClosing&&e.Thumbs.close(),e.Thumbs=null)})}(document,window.jQuery),function(s,i,n){n.escapeSelector||(n.escapeSelector=function(t){return(t+"").replace(/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g,function(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t})});var a=null;function r(){var t=i.location.hash.substr(1),e=t.split("-"),n=1<e.length&&/^\+?\d+$/.test(e[e.length-1])&&parseInt(e.pop(-1),10)||1;return{hash:t,index:n=n<1?1:n,gallery:e.join("-")}}function e(t){var e;""!==t.gallery&&((e=n("[data-umfancybox='"+n.escapeSelector(t.gallery)+"']").eq(t.index-1)).length?e:n("#"+n.escapeSelector(t.gallery))).trigger("click")}function c(t){return!!t&&((t=(t.current||t).opts).$orig?t.$orig.data("umfancybox"):t.hash||"")}n(function(){setTimeout(function(){!1!==n.umfancybox.defaults.hash&&(n(i).on("hashchange.umfb",function(){var t=r();n.umfancybox.getInstance()?a&&a!==t.gallery+"-"+t.index&&(a=null,n.umfancybox.close()):""!==t.gallery&&e(t)}),n(s).on({"onInit.umfb":function(t,e){var n=r(),o=c(e);o&&n.gallery&&o==n.gallery&&(e.currIndex=n.index-1)},"beforeMove.umfb":function(t,e,n){var o=c(e);o&&""!==o&&(i.location.hash.indexOf(o)<0&&(e.opts.origHash=i.location.hash),a=o+(1<e.group.length?"-"+(n.index+1):""),"pushState"in history?history.pushState("",s.title,i.location.pathname+i.location.search+"#"+a):i.location.hash=a)},"beforeClose.umfb":function(t,e,n){var o=c(e),e=e&&e.opts.origHash?e.opts.origHash:"";o&&""!==o&&("pushState"in history?history.pushState("",s.title,i.location.pathname+i.location.search+e):i.location.hash=e),a=null}}),e(r()))},50)})}(document,window,window.jQuery);