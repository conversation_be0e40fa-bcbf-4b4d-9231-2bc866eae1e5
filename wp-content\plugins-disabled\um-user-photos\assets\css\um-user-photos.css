.text-center {
	text-align: center;
}

.text-right {
	text-align: right;
}

.um-clearfix {
	clear: both;
}

.um-user-photos-add {
	padding-bottom:10px;
}

#um-user-photos-mode {
	text-align:center;
	line-height: 30px;
	background: #fff;
	margin:0;
	padding:0;
	margin-bottom: 5px;
}

#um-user-photos-mode a {
	color:#333;
	text-decoration: none;
	border: none;
	border-bottom:1px solid transparent;
}

#um-user-photos-mode a.active {
	color:#449fdf;
	border-bottom:1px solid #449fdf;
	line-height: 30px;
	display: inline-block;
}

.um-user-photos-album-head {
	border-bottom: 1px solid #ccc;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	margin-bottom: 10px;
	padding-bottom: 10px;

}

.um-user-photos-album-head a {
	text-decoration:none;
	border:none;
}

.um-user-photos-album-head .col-back {
	float: left;
}

.um-user-photos-album-head .col-title {
	text-align: center;
	max-width: 100%;
	overflow: hidden;
}

.um-user-photos-album-head .col-title .um-user-photos-edit-album-link {
	padding:15px;
}

.um-user-photos-album-head .col-title .um-user-photos-edit-album-link:hover {
	color: #3ba1da;
}

.um-user-photos-album-head .col-delete {
	text-align: right;
	position: relative;
}

.um-user-photos-album-head .col-delete .um-user-photos-album-options {
	text-decoration: none !important;
	border:none;
	font-size:20px;
}

.um-user-photos-album-head .col-delete .um-dropdown {
	top: 35px;
	right:-7px;
	display: none;
	min-width:150px;
	text-align: center;
	left: auto;
}

.um-user-photos-album-head .col-delete .um-dropdown.active {
	display: block;
}

.um-user-photos-album-head .col-delete .um-dropdown .um-dropdown-arr {
	top:-17px;
	left: auto;
	right:2px;
}

.um-user-photos-album-head .col-delete a {
	color: #333;
}

.um-user-photos-album {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	flex-wrap: nowrap;
	height: 100%;
	align-items: stretch;
}


/* image hover */
.um-user-photos-image-block a.um-user-photos-image > img {
	transition: all 0.1s linear;
}

.um-user-photos-image-block a.um-user-photos-image:hover > img {
	opacity: 0.8;
}

.um-user-photos-image-block,
.um-user-photos-image-block-editable {
	position:relative;
}

.um-user-photos-image-block .um-user-photos-image-block-buttons,
.um-user-photos-image-block-editable .um-user-photos-image-block-buttons {
	position: absolute;
	z-index: 50;
	display: inline-block;
	padding:5px;
	right:0;
	opacity:0;
	transition: all 0.2s ease-in;
}

.um-user-photos-image-block:hover>.um-user-photos-image-block-buttons,
.um-user-photos-image-block-editable:hover>.um-user-photos-image-block-buttons {
	opacity: 1;
}

.um-user-photos-image-block .um-user-photos-image-block-buttons a,
.um-user-photos-image-block-editable .um-user-photos-image-block-buttons a {
	color:#fff;
	text-decoration: none;
	border:none;
	margin:2px;
	background:rgba(0,0,0,0.4);
	border-radius: 50%;
	display: inline-block;
	height: 30px;
	width: 30px;
	text-align: center;
	line-height: 30px;
	transition: all 0.2s linear;
}

.um-user-photos-image-block .um-user-photos-image-block-buttons a:hover,
.um-user-photos-image-block-editable .um-user-photos-image-block-buttons a:hover {
	background:rgba(255,255,255,0.8);
	color:#000;
}


.um-user-photos-modal {
	position:fixed;
	top:0;
	left:0;
	width:100%;
	height:100%;
	background:rgba(0,0,0,0.8);
	z-index:1000;
	display: none;
	overflow-y: scroll;
}

.um-user-photos-modal .um-user-photos-modal-body {
	background: #fff;
	width:95%;
	max-width:700px;
	margin-top:50px;
	margin-left:auto;
	margin-right:auto;
	min-height: 80px;
	display: block;
}

.um-user-photos-modal .um-user-photos-modal-body .um-user-photos-modal-head {
	background: transparent;
	padding: 10px;
	position: relative;
	text-align: left;
}

.um-user-photos-modal .um-user-photos-modal-body .um-user-photos-modal-head .um-user-photos-modal-title {
	color:#333;
	font-weight: 800;
}

.um-user-photos-modal .um-user-photos-modal-body .um-user-photos-modal-head .um-user-photos-modal-close{
	position: absolute;
	right:0;
	top:0;
	font-size:24px;
}

.um-user-photos-modal .um-user-photos-modal-body .um-user-photos-modal-head .um-user-photos-modal-close .um-user-photos-modal-close-link {
	text-decoration: none;
	border:none;
	color:#fff;
	padding:10px;
}

.um-user-photos-modal .um-user-photos-modal-body .um-user-photos-modal-head .um-user-photos-modal-close .um-user-photos-modal-close-link:hover {
	color:#333;
}

.um-user-photos-modal .um-user-photos-modal-body .um-user-photos-modal-content {
	padding: 20px 10px;
	opacity: 1 !important;
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
}

.um-user-photos-modal .um-user-photos-modal-body .um-user-photos-modal-content .um-form {
	max-width:97%;
}


.um-user-photos-modal .um-user-photos-modal-body .um-user-photos-modal-content .um-form button {
	border:none;
}

.um-user-photos-modal .um-user-photos-modal-body #um-user-photos-images-uploaded {
	margin-top: 20px;
	display: grid;
	grid-template-columns: repeat(5, 1fr);
	grid-gap: 10px;
}

.um-user-photos-modal .um-user-photos-modal-body #um-user-photos-images-uploaded span {
	display: inline-block;
	position: relative;
	cursor: pointer;
	height: 70px;
	border: 4px solid #fff;
}

.um-user-photos-modal .um-user-photos-modal-body #um-user-photos-images-uploaded span img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	object-position: center;
	border: 1px solid #000;
}

.um-user-photos-modal .um-user-photos-modal-body #um-user-photos-images-uploaded .um-user-photos-cover {
	border: 4px solid #000;
}

.um-user-photos-modal .um-user-photos-modal-body #um-user-photos-images-uploaded .um-user-photos-cover img {
	/*border: none;*/
}

.um-user-photos-modal .um-user-photos-modal-body #um-user-photos-images-uploaded .um-user-photos-cover:before {
	content: attr(data-covertext);
	display: block;
	position: absolute;
	top: -17px;
	left: -4px;
	font-size: 10px;
	background: #000;
	color: #fff;
	font-weight: 700;
	width: 100%;
	text-align: center;
	border: 4px solid #000;
	padding: 0;
	border-top: 0;
	border-bottom: 0;
}

.um-user-photos-toggle {
	display: none;
	height: 0;
	transition: all 0.2s linear;
	background:#fff;
}

.um-user-photos-toggle.toggle-visible {
	height:auto;
	display: block;
	padding:10px;
	margin:10px 0;
	background:#eee;
}



/*album block*/
.um-user-photos-albums-wrap {
	direction: ltr;
	display: grid;
	grid-gap: 10px;
	grid-template-columns: repeat(3, 1fr);
	align-content: center;
	align-items: flex-start;
}
.um-user-photos-albums-wrap.um-user-photos-col-1 {
	grid-template-columns: repeat(1, 1fr);
}
.um-user-photos-albums-wrap.um-user-photos-col-2 {
	grid-template-columns: repeat(2, 1fr);
}
.um-user-photos-albums-wrap.um-user-photos-col-3 {
	grid-template-columns: repeat(3, 1fr);
}
.um-user-photos-albums-wrap.um-user-photos-col-4 {
	grid-template-columns: repeat(4, 1fr);
}
.um-user-photos-albums-wrap.um-user-photos-col-5 {
	grid-template-columns: repeat(5, 1fr);
}
.um-user-photos-albums-wrap.um-user-photos-col-6 {
	grid-template-columns: repeat(6, 1fr);
}

.um-user-photos-album {
	overflow: hidden;
}

.um-user-photos-album .um-user-photos-album-block {
	position:relative;
}

.um-user-photos-album .um-user-photos-album-block  .album-overlay {
	background:rgba(0,0,0,0.5);
	position:absolute;
	top:0;
	left:0;
	height:100%;
	width:100%;
	display:block;
	transition: all 0.2s linear;
	background: transparent;
}

.um-user-photos-album .um-user-photos-album-block  .album-overlay .album-title {
	position:absolute;
	top:0;
	left:0;
	color:#fff;
	text-align:center;
	display:block;
	max-width:100%;
	padding:10px;
	margin:0;
	text-shadow: 0 0 5px #2B2B2B;
}

.um-user-photos-album  .album-title {
	margin-top: 4px;
	margin-bottom: 0;
	text-align: left;
	padding-left: 5px;
	word-break: break-all;
	line-height: 1.2;
	max-height: 60px;
}

.um-user-photos-albums .um-user-photos-album-block:hover > .album-overlay {
	background: rgba(255,255,255,0.3);
}

.um-user-photos-albums .um-user-photos-album-block img {
	max-width: 99%;
	padding:2px;
	clear: both;
}


.um-user-photos-modal-form .album-poster-holder {
	margin:0;
	background-repeat: no-repeat;
	background-position: center center;
}
.um-user-photos-modal-form .album-poster-holder:before {
	display: none;
}

.um-user-photos-modal-form .album-poster-label {
	background:rgba(0,0,0,0.5);
	padding:40px 0;
	display:block;
	border:none;
	color:#fff;
	cursor:pointer;
}

.um-user-photos-modal-form h6 {
	display: none;
	padding: 20px 0 0;
	margin: 0;
}

.um-user-photos-modal-form .album-poster-label span {
	font-size:13px;
}

.um-user-photos-modal-form .album-poster-label span {
	font-size:13px;
}

.um-user-photos-modal-form .um-user-photos-album-photos {
	clear: both;
	margin-top: 10px;
	display: grid;
	grid-template-columns: repeat(5, 1fr);
	grid-gap: 10px;
}

.um-user-photos-modal-form .um-user-photos-album-photos .um-user-photos-photo {
	position: relative;
	cursor: pointer;
	height: 70px;
	border: 4px solid #fff;
}

.um-user-photos-modal-form .um-user-photos-album-photos .um-user-photos-photo.um-user-photos-cover {
	border: 4px solid #000;
}

.um-user-photos-modal-form .um-user-photos-album-photos .um-user-photos-photo.um-user-photos-cover:before {
	content: attr(data-covertext);
	display: block;
	position: absolute;
	top: -17px;
	left: -4px;
	font-size: 10px;
	background: #000;
	color: #fff;
	font-weight: 700;
	width: 100%;
	text-align: center;
	border: 4px solid #000;
	padding: 0;
	border-top: 0;
	border-bottom: 0;
}

.um-user-photos-modal-form .um-user-photos-album-photos .um-user-photos-photo img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	object-position: center;
	border: 1px solid #000;
}

.um-user-photos-modal-form .um-user-photos-album-photos .um-user-photos-photo .um-user-photos-delete-photo-album {
	position:absolute;
	top:0;
	right:0;
	z-index:3333;
	color:#fff;
	margin:0;
	display:inline-block;
	height:20px;
	width:20px;
	line-height:20px;
	text-align:center;
	text-shadow: 0 0 5px #000;
	border:none;
}

.um-user-photos-modal-form .um-user-photos-album-photos .um-user-photos-photo .um-user-photos-delete-photo-album:hover {
	color:#eee;
}

.um-user-photos-modal-form .um-user-photos-error,
.um-user-photos-modal-form .um-user-photos-max-upload-error,
.um-user-photos-modal-form .um-user-photos-max-size-upload-error {
	display: none;
	padding:10px;
	margin-bottom:10px;
	color:#fff;
	border-radius: 5px;
	background:#b50606;
}

.um-user-photos-modal-form .um-user-photos-error {
	display: block;
}

.um-user-photos-modal-content form .um-galley-form-response.error,
.um-user-photos-modal-content form .um-galley-form-response.success{
	padding:10px;
	margin-bottom:10px;
	color:#fff;
	border-radius: 5px;
}

.um-user-photos-modal-content form .um-galley-form-response.error ul,
.um-user-photos-modal-content form .um-galley-form-response.success ul {
	padding:0;
	margin:0;
	list-style: none;
}

.um-user-photos-modal-content form .um-galley-form-response.error {
	background:#b50606;
}

.um-user-photos-modal-content form .um-galley-form-response.success {
	background:#04945f;
}

@-webkit-keyframes um-user-photos-ajax-spinning {
	0% {
		-webkit-transform: rotate(0);
		transform: rotate(0)
	}

	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg)
	}
}

@keyframes um-user-photos-ajax-spinning {
	0% {
		-webkit-transform: rotate(0);
		transform: rotate(0)
	}

	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg)
	}
}

.um-spin {
	-webkit-animation: um-user-photos-ajax-spinning 1.1s infinite linear;
	animation: um-user-photos-ajax-spinning 1.1s infinite linear;
	-ms-transform: translateZ(0);
	transform: translateZ(0);
}

div.um-user-photos-ajax-loading {
	color: #c6c6c6 !important;
	-webkit-transition: .1s opacity!important;
	-moz-transition: .1s opacity!important;
	-ms-transition: .1s opacity!important;
	-o-transition: .1s opacity!important;
	transition: .1s opacity!important;
	-webkit-animation: um-user-photos-ajax-spinning 1.1s infinite linear;
	animation: um-user-photos-ajax-spinning 1.1s infinite linear;
	border-top: .2em solid rgba(198, 198, 198, 0.4);
	border-right: .2em solid rgba(198, 198, 198, 0.4);
	border-bottom: .2em solid rgba(198, 198, 198, 0.4);
	border-left: .2em solid #a29f9f;
	font-size: 1.75em;
	filter: alpha(opacity=0);
	-ms-transform: translateZ(0);
	transform: translateZ(0);
	border-radius: 50%;
	display: inline-block;
	width: 2.5em;
	height: 2.5em;
	margin: 0;
	outline: 0;
	padding: 0;
	vertical-align: baseline;
}

i.um-user-photos-ajax-loading {
	color: #ffffff !important;
	-webkit-transition: .1s opacity!important;
	-moz-transition: .1s opacity!important;
	-ms-transition: .1s opacity!important;
	-o-transition: .1s opacity!important;
	transition: .1s opacity!important;
	-webkit-animation: um-user-photos-ajax-spinning 1.1s infinite linear;
	animation: um-user-photos-ajax-spinning 1.1s infinite linear;
	border-top: .1em solid rgba(255, 255, 255,0.5);
	border-right: .1em solid rgba(255,255,255,0.5);
	border-bottom: .1em solid rgba(255,255,255,0.5);
	font-size: .85em;
	filter: alpha(opacity=0);
	-ms-transform: translateZ(0);
	transform: translateZ(0);
	border-radius: 50%;
	display: inline-block;
	width: 1em;
	height: 1em;
	margin: 0;
	outline: 0;
	padding: 0;
	vertical-align: baseline
}

.um-account .um-account-tab-um_user_photos .um_user_photos_account .um-button {
	margin:10px 0;
}

.um-account .um-account-tab-um_user_photos .um_user_photos_account .um-button.danger {
	background:#c50707;
}

.um-account .um-account-tab-um_user_photos .um_user_photos_account .um-button.danger:hover {
	background:#a70606;
}

.um-account .um-account-tab-um_user_photos .um_user_photos_account .um-button.inactive:hover,
.um-account .um-account-tab-um_user_photos .um_user_photos_account .um-button.inactive {
	background:#ccc;
	opacity: 0.7;
	cursor: wait;
}

.um-user-photos-modal-footer button.um-modal-btn:disabled{
  cursor: not-allowed !important;
  background:#98d2f3;
}

.um-user-photos-modal-footer button.um-modal-btn[disabled]:hover{
	line-height: 34px !important;
	height: 34px;
	display: inline-block;
	-moz-border-radius: 3px;
	-webkit-border-radius: 3px;
	border-radius: 3px;
	padding: 0 20px;
	text-align: center;
}

.um-user-photos-modal-footer label.um-modal-btn {
	margin-bottom: 0 !important;
}

/* Shortcode: [ultimatemember_albums] */

.ultimatemember_albums .um-user-photos-album {
	border: 1px solid #ddd;
	box-sizing: border-box;
	margin: 0px;
	padding: 0px;
	width: 100%;
}
.ultimatemember_albums .um-user-photos-album-link {
	display: block;
	float: none;
	margin: 0px;
	padding: 0px;
	position: relative;
	text-decoration: none;
	transition: all 0.1s linear;
	width: 100%;
}
.ultimatemember_albums .um-user-photos-album-link p.album-title {
	background-color: rgba(0,0,0,0.4);
	color: #ffffff;
	margin: 0px;
	padding: 1em;
	position: absolute;
	text-shadow: 0px 0px 5px #000000;
}
.ultimatemember_albums .um-user-photos-album-link img {
	max-width: 100%;
	padding: 0px;
}
.ultimatemember_albums .um-member-photo,
.ultimatemember_albums .um-member-card {
	font-weight: 700;
	padding: 0px 0px 1rem 0px;
	text-align: center;
}
.ultimatemember_albums .um-member-photo a img {
	background: #fff;
	border: 5px solid #fff;
	-moz-border-radius: 50%;
	-webkit-border-radius: 50%;
	border-radius: 50%;
	display: inline;
	float: none;
	height: 90px;
	margin-bottom: -45px;
	opacity: 1;
	top: -35px;
	position: relative;
	width: 90px;
}


/* albums pagination */
.um .um-pagi{
	text-align: center;
}
.um .um-pagi .pagi{
	color: #666;
	cursor: pointer;
	display: inline-block;
	font-size: 15px;
	font-weight: normal;
	height: 34px;
	line-height: 34px;
	padding: 0 14px;
	transition: all .2s linear;
	width: auto;
}
.um .um-pagi .pagi.current {
	background-color: #3ba1da;
	color: #fff;
	cursor: default;
}
.um .um-pagi .pagi.disabled{
	cursor: default;
	opacity: 0.4;
}
.um .um-pagi .pagi.pagi-arrow i:before {
	font-size: 20px;
	vertical-align: middle !important;
	height: 34px;
	line-height: 34px;
	top: -2px;
	position: relative;
}


/* albums grid */
.um .grid-row {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	align-items: stretch;
	justify-content: space-between;
	gap: 10px;
}
.um .grid-row .grid-item,
.um .grid-row.grid-row-1 .grid-item{
	flex-basis: 100%;
	flex-grow: 1;
}
.um .grid-row.grid-row-2 .grid-item{
	flex-basis: 50%;
	flex-basis: calc(50% - 10px);
}
.um .grid-row.grid-row-3 .grid-item{
	flex-basis: 33.33%;
	flex-basis: calc(33.33% - 10px);
}
.um .grid-row.grid-row-4 .grid-item{
	flex-basis: 25%;
	flex-basis: calc(25% - 10px);
}


/* activity grid */
.um .um_user_photos_activity_view{
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	align-items: stretch;
	justify-content: center
}
.um .um_user_photos_activity_view br{
	display: none;
}
.um .um_user_photos_activity_view img{
	flex-basis: 100px;
	max-height: 100px;
	object-fit: cover;
}


/* gallery grid */
.um-user-photos-albums .um-up-grid{
	direction: ltr;
	display: grid;
	grid-gap: 10px;
	grid-template-columns: repeat(3, 1fr);
	align-content: center;
	align-items: center;
	justify-content: center;
	justify-items: center;
}
.um-user-photos-albums .um-up-grid-col-1 {
	grid-template-columns: repeat(1, 1fr);
}
.um-user-photos-albums .um-up-grid-col-2 {
	grid-template-columns: repeat(2, 1fr);
}
.um-user-photos-albums .um-up-grid-col-3 {
	grid-template-columns: repeat(3, 1fr);
}
.um-user-photos-albums .um-up-grid-col-4 {
	grid-template-columns: repeat(4, 1fr);
}
.um-user-photos-albums .um-up-grid-col-5 {
	grid-template-columns: repeat(5, 1fr);
}
.um-user-photos-albums .um-up-grid-col-6 {
	grid-template-columns: repeat(6, 1fr);
}
.um-user-photos-albums .um-up-cell {
	background: rgba(234, 234, 234, 0.5);
	border-radius: 10px;
	overflow: hidden;
	width: 100%;
	height: 100%;
	display: flex;
}
.um-user-photos-albums .um-up-cell a.um-user-photos-image {
	display: block;
	object-fit: cover;
	height: 100%;
	width: 100%;
}
.um-user-photos-albums .um-up-cell a.um-user-photos-image img {
	position: relative;
	object-fit: contain;
	height: 100%;
	width: 100%;
}
