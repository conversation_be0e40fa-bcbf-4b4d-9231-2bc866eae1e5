const gulp = require('gulp'),
	concat = require('gulp-concat'),
	uglify = require('gulp-uglify'),
	cleanCSS = require('gulp-clean-css'),
	rename = require('gulp-rename'),
	replace = require( 'gulp-replace' ),
	clean = require( 'gulp-clean' );


/**
 * Import and Concat Javascript
 */
function pluginJS(done) {

	gulp.src(['./node_modules/breakpoints-js/dist/breakpoints.min.js'])
		.pipe(concat('breakpoints.min.js'))
		.pipe(uglify())
		.pipe(gulp.dest('./assets/js/'))
		
	gulp.src(['./assets/js/stripe.js'])
		.pipe(concat('stripe.min.js'))
		.pipe(uglify())
		.pipe(gulp.dest('./assets/js/'))

	gulp.src(['./node_modules/@fortawesome/fontawesome-free/js/all.js'])
		.pipe(concat('stripe-fontawesome-regular.js'))
		.pipe(uglify())
		.pipe(gulp.dest('./assets/js/'))		
	gulp.src(['./node_modules/@fortawesome/fontawesome-free/js/all.js'])
		.pipe(concat('stripe-fontawesome-regular.min.js'))
		.pipe(uglify())
		.pipe(gulp.dest('./assets/js/'))	

	gulp.src(['./assets/js/admin/settings.js'])
		.pipe(concat('settings.min.js'))
		.pipe(uglify())
		.pipe(gulp.dest('./assets/js/admin'))

	gulp.src(['./assets/js/admin/blocks.js'])
		.pipe(concat('blocks.min.js'))
		.pipe(uglify())
		.pipe(gulp.dest('./assets/js/admin'))

	gulp.src(['./node_modules/select2/dist/js/select2.js', './assets/js/admin/user-fields.js'])
		.pipe(concat('user-fields.min.js'))
		.pipe(uglify())
		.pipe(gulp.dest('./assets/js/admin'))


	return done();
}
gulp.task('js', pluginJS);

/**
 * Import Modules
 */
function pluginModules(done) {

	var sources = [
		'./node_modules/select2/src/scss/**/*.*',
	]
	gulp.src(sources).pipe(gulp.dest('./assets/vendor/select2/'));

	gulp.src(['./node_modules/credit-card-logos/img/*.svg']).pipe(gulp.dest('./assets/images/cc/'));

	return done();
}
gulp.task('modules', pluginModules);


function pluginCSS() {

	var cssMinifyAdminProfile = ['./assets/css/admin/profile.css', './assets/css/libs/select2/**/*.css', '!./assets/css/**/*.min.css'];

	gulp.src(cssMinifyAdminProfile)
		.pipe(rename("profile.min.css"))
		.pipe(cleanCSS())
		.pipe(gulp.dest('./assets/css/admin'));

	var cssMinifyAdmin = ['./assets/css/admin/admin.css', '!./assets/css/**/*.min.css'];

	gulp.src(cssMinifyAdmin)
		.pipe(rename("admin.min.css"))
		.pipe(cleanCSS())
		.pipe(gulp.dest('./assets/css/admin/'));

	var cssMinifyFront = ['./assets/css/stripe.css', '!./assets/css/**/*.min.css'];
	
	return gulp.src(cssMinifyFront)
		.pipe(rename("stripe.min.css"))
		.pipe(cleanCSS())
		.pipe(gulp.dest('./assets/css/'));

}
gulp.task('minify-css', pluginCSS);

/**
 * Clean up Vendor & Move prefixed vendors back to the /vendor/
 */
gulp.task( 'composer:delete_prefixed_vendor_libraries', function () {
	
	gulp.src([
			'vendor_prefixed/berlindb/**/*.php',
		],
		)
		.pipe(gulp.dest('vendor/berlindb/'));

	gulp.src([
			'vendor_prefixed/stripe/stripe-php/data/ca-certificates.crt',
		],
		)
		.pipe(gulp.dest('vendor/stripe/stripe-php/data'));
	
	gulp.src([
			'vendor_prefixed/symfony/**/*.php',
		],
		)
		.pipe(gulp.dest('vendor/symfony'));

	return gulp.src([
			'vendor_prefixed/stripe/stripe-php/**/*.php',
		],
		)
		.pipe(gulp.dest('vendor/stripe/stripe-php'));
	  
} );

/**
 *  Create Vendor Prefixed Folder & Stripe Certifications folder.
 */
gulp.task( 'composer:create_vendor_prefixed_folder', function () {
	gulp.src( '*.*', { read: false } )
		.pipe( gulp.dest( './vendor/stripe/data' ) )

	return gulp.src( '*.*', { read: false } )
		.pipe( gulp.dest( './vendor_prefixed' ) );
} );
/**
 * Watch
 */
gulp.task('watch', function (done) {
	gulp.watch(['./assets/js/**/*.js', '!./assets/js/**/*.min.js'], gulp.series(pluginJS));
	return done();
});

/**
 * Build Release
 */
gulp.task('release', gulp.series(pluginJS, pluginModules, pluginCSS));
